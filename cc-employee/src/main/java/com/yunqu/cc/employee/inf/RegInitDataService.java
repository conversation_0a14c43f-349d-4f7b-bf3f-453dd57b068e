package com.yunqu.cc.employee.inf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.BaseRegInitDataService;
import com.yq.busi.common.util.NoticeHelper;
import com.yunqu.cc.employee.base.CommonLogger;
import com.yunqu.cc.employee.base.Constants;
import com.yunqu.cc.employee.base.QueryFactory;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;


/**
 * <AUTHOR>
 * @date :2019年9月24日下午3:55:40
 * 提供本模块需要初始化的数据，如数据字典、定时任务等
 * 提供的数据由相关管理模块通过服务类接口采集
 *
 */
public class RegInitDataService extends BaseRegInitDataService{

	public JSONArray getNoticeInfo() {
		return NoticeHelper.getNoticeInfo(Constants.APP_NAME);
	}
	
	/**
	 * 获取该模块里的定时任务
	 * @return
	 */
	public JSONArray getInitJobs() {
		JSONArray jobArray = new JSONArray();

		
		return jobArray;
	}
	
	
	/**
	 * 获取本模块所需的数据字段集合
	 * @return
	 */
	public JSONArray getInitDict() {
		//模块所需数据字典
		JSONArray dictArray = new JSONArray();

		// 积分规则，字典值：YG_POINTS_RULE_TYPE，01-加分，02-减分，03-无
		JSONObject dictGroup1 = createDictGroup("YG_POINTS_RULE_TYPE","积分规则");
		JSONArray dictGroupArray1 = new JSONArray();
        dictGroupArray1.add(createDict("01","加分",1));
        dictGroupArray1.add(createDict("02","减分",2));
        dictGroupArray1.add(createDict("03","无",3));
        dictGroup1.put("DICTS", dictGroupArray1);
        dictArray.add(dictGroup1);

		// 招聘渠道，字典值：YG_RECRUITMENT_CHANNEL，01-网络招聘，02-校园招聘，03-猎头招聘，04-内部招聘，05-其他
		JSONObject dictGroup2 = createDictGroup("YG_RECRUITMENT_CHANNEL","招聘渠道");
        JSONArray dictGroupArray2 = new JSONArray();
        dictGroupArray2.add(createDict("01","网络招聘",1));
        dictGroupArray2.add(createDict("02","校园招聘",2));
        dictGroupArray2.add(createDict("03","猎头招聘",3));
        dictGroupArray2.add(createDict("04","内部招聘",4));
        dictGroupArray2.add(createDict("05","其他",5));
        dictGroup2.put("DICTS", dictGroupArray2);
        dictArray.add(dictGroup2);

        // 员工状态，字典值：YG_EMP_STATUS，01-在职，02-离职，03-休假，04-其他
        JSONObject dictGroup3 = createDictGroup("YG_EMP_STATUS","员工状态");
        JSONArray dictGroupArray3 = new JSONArray();
        dictGroupArray3.add(createDict("01","在职",1));
        dictGroupArray3.add(createDict("02","离职",2));
        dictGroupArray3.add(createDict("03","休假",3));
        dictGroupArray3.add(createDict("04","其他",4));
        dictGroup3.put("DICTS", dictGroupArray3);
        dictArray.add(dictGroup3);

        // 招聘批次，字典值：YG_RECRUITMENT_BATCH ，01-2024年春招，02-2024年秋招
        JSONObject dictGroup4 = createDictGroup("YG_RECRUITMENT_BATCH","招聘批次");
        JSONArray dictGroupArray4 = new JSONArray();
        dictGroupArray4.add(createDict("01","2024年春招",1));
        dictGroupArray4.add(createDict("02","2024年秋招",2));
        dictGroupArray4.add(createDict("03","2025年春招",3));
        dictGroupArray4.add(createDict("04","2025年秋招",4));
        dictGroupArray4.add(createDict("05","其他",5));
        dictGroup4.put("DICTS", dictGroupArray4);
        dictArray.add(dictGroup4);

        // 学历，字典值：YG_EDUCATION，01-博士，02-硕士，03-本科，04-大专，05-中专，06-高中，07-初中，08-小学，09-其他
        JSONObject dictGroup5 = createDictGroup("YG_EDUCATION","学历");
        JSONArray dictGroupArray5 = new JSONArray();
        dictGroupArray5.add(createDict("01","博士",1));
        dictGroupArray5.add(createDict("02","硕士",2));
        dictGroupArray5.add(createDict("03","本科",3));
        dictGroupArray5.add(createDict("04","大专",4));
        dictGroupArray5.add(createDict("05","中专",5));
        dictGroupArray5.add(createDict("06","高中",6));
        dictGroupArray5.add(createDict("07","初中",7));
        dictGroupArray5.add(createDict("08","小学",8));
        dictGroupArray5.add(createDict("09","其他",9));
        dictGroup5.put("DICTS", dictGroupArray5);

		// 收货方式，字典值：YG_DELIVERY_METHOD,01-邮寄-02-自提
		JSONObject dictGroup6 = createDictGroup("YG_DELIVERY_METHOD","收货方式");
        JSONArray dictGroupArray6 = new JSONArray();
        dictGroupArray6.add(createDict("01","邮寄",1));
        dictGroupArray6.add(createDict("02","自提",2));
        dictGroup6.put("DICTS", dictGroupArray6);
        dictArray.add(dictGroup6);

		// 订单状态，字典值：YG_ORDER_STATUS：01-待核销，02-已核销，03-待发货，04-已发货，05-已拒绝
		JSONObject dictGroup7 = createDictGroup("YG_ORDER_STATUS","订单状态");
        JSONArray dictGroupArray7 = new JSONArray();
        dictGroupArray7.add(createDict("01","待核销",1));
        dictGroupArray7.add(createDict("02","已核销",2));
        dictGroupArray7.add(createDict("03","待发货",3));
        dictGroupArray7.add(createDict("04","已发货",4));
        dictGroupArray7.add(createDict("05","已拒绝",5));
        dictGroup7.put("DICTS", dictGroupArray7);
        dictArray.add(dictGroup7);

		// '经历类型，字典值：YG_EXPERIENCE_TYPE，01-学习经历，02-工作经历，03-培训经历',
		JSONObject dictGroup8 = createDictGroup("YG_EXPERIENCE_TYPE","经历类型");
        JSONArray dictGroupArray8 = new JSONArray();
        dictGroupArray8.add(createDict("01","学习经历",1));
        dictGroupArray8.add(createDict("02","工作经历",2));
        dictGroupArray8.add(createDict("03","培训经历",3));
        dictGroup8.put("DICTS", dictGroupArray8);
        dictArray.add(dictGroup8);


		// 指标类型
		JSONObject dictGroup9 = createDictGroup("YG_METRICS_TYPE","指标类型");
		JSONArray dictGroupArray9 = new JSONArray();
        dictGroupArray9.add(createDict("0","内置指标",1));
        dictGroupArray9.add(createDict("1","自定义指标",2));
        dictGroup9.put("DICTS", dictGroupArray9);
		dictArray.add(dictGroup9);

		// 指标来源
		JSONObject dictGroup10 = createDictGroup("YG_METRICS_SOURCE","指标来源");
		JSONArray dictGroupArray10 = new JSONArray();
        dictGroupArray10.add(createDict("1","数据库",1));
        dictGroupArray10.add(createDict("2","Redis",2));
        dictGroupArray10.add(createDict("3","内置程序计算",3));
        dictGroup10.put("DICTS", dictGroupArray10);
		dictArray.add(dictGroup10);

        // '员工分享经验类别，字典值：YG_EXP_TYPE'
        JSONObject dictGroup11 = createDictGroup("YG_EXP_TYPE","经验类别");
        JSONArray dictGroupArray11 = new JSONArray();
        dictGroupArray11.add(createDict("01","技术文档",1));
        dictGroupArray11.add(createDict("02","操作指南",2));
        dictGroupArray11.add(createDict("03","工作心得",3));
        dictGroupArray11.add(createDict("04","其他",4));
        dictGroup11.put("DICTS", dictGroupArray11);
        dictArray.add(dictGroup11);

		// 模型类型字典，根据注释生成：1-服务之星评选模型（内置） 2-质量之星评选模型（内置）3-接待王评选模型（内置）4-员工能力图模型（内置）
		JSONObject dictGroup12 = createDictGroup("YG_MODEL_TYPE", "模型类型");
		JSONArray dictGroupArray12 = new JSONArray();
		dictGroupArray12.add(createDict("1", "服务之星评选模型", 1));
		dictGroupArray12.add(createDict("2", "质量之星评选模型", 2));
		dictGroupArray12.add(createDict("3", "接待王评选模型", 3));
		dictGroupArray12.add(createDict("4", "员工能力图模型", 4));
		dictGroup12.put("DICTS", dictGroupArray12);
		dictArray.add(dictGroup12);


		return dictArray;
	}
	

	@Override
	public String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	public Logger getLogger() {
		return CommonLogger.logger;
	}

	@Override
	public EasyQuery getEasyQuery() {
		return QueryFactory.getWriteQuery();
	}

	

}
