<!DOCTYPE html>
<html>
<head>
    <title>职业管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css?v=1.3" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <style>
        .vue-box {
            padding: 0;
            height: 100%;
            box-sizing: border-box;
        }
        .dev-plan-form {
            padding: 20px;
        }
        .dev-plan-item {
            border: 1px solid #EBEEF5;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            cursor: move;
            transition: all 0.3s;
            background: #fff;
            position: relative;
        }
        .dev-plan-item:hover {
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        .dev-plan-item .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .dev-plan-item .drag-handle {
            color: #909399;
            margin-right: 10px;
        }
        .sortable-ghost {
            opacity: 0.5;
            background: #c8ebfb !important;
            border: 2px dashed #409EFF;
        }
        .sortable-drag {
            background: #fff;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="professionTable" class="flex yq-table-page" v-loading="loading" :element-loading-text="getI18nValue('加载中...')" v-cloak>
        <div class="yq-card">
            <div class="card-header">
                <div class="head-title">{{ getI18nValue('职业管理') }}</div>
                <div class="yq-table-control">
                    <el-button type="primary" size="small" @click="handleAdd">
                        <i class="el-icon-plus"></i>{{ getI18nValue('新增') }}
                    </el-button>
                </div>
            </div>
            <div class="card-content">
                <senior-search :show.sync="moreSearch">
                    <el-form class="search-form" :inline="false" :model="searchForm" ref="searchForm" size="small" label-width="120px">
                        <el-form-item :label="getI18nValue('职业名称')" prop="professionName">
                            <el-input v-model="searchForm.professionName" :placeholder="getI18nValue('请输入')" clearable></el-input>
                        </el-form-item>
                        <el-form-item class="btns" label-width="0px">
                            <el-button type="primary" plain size="small" icon="el-icon-refresh" @click="handleReset">{{getI18nValue('重置')}}</el-button>
                            <el-button type="primary" size="small" icon="el-icon-search" @click="getList(1)">{{getI18nValue('搜索')}}</el-button>
                        </el-form-item>
                    </el-form>
                </senior-search>

                <div class="yq-table">
                    <el-table stripe :data="tableData.data" height="100%" fit ref="table" style="width: 100%">
                        <el-table-column :label="getI18nValue('序号')" type="index" width="90px" fixed>
                            <template slot-scope="scope">
                                {{ (tableData.pageIndex - 1) * tableData.pageSize + scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="professionName" :label="getI18nValue('职业名称')" show-overflow-tooltip min-width="180"></el-table-column>
                        <el-table-column prop="createTime" :label="getI18nValue('创建时间')" min-width="180"></el-table-column>
                        <el-table-column prop="createAcc" :label="getI18nValue('创建人')" min-width="120"></el-table-column>
                        <el-table-column :label="getI18nValue('操作')" min-width="280" fixed="right">
                            <template slot-scope="scope">
                                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">{{getI18nValue('修改')}}</el-link>
                                <el-link @click="handleDevPlan(scope.row)" type="success" :underline="false" style="margin: 0 10px;">{{getI18nValue('发展规划')}}</el-link>
                                <el-link @click="handleDel(scope.row)" type="danger" :underline="false">{{getI18nValue('删除')}}</el-link>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
                        :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[15, 30, 50, 100]"
                        layout="total, prev, pager, next, jumper,sizes" :total="tableData.totalRow">
                    </el-pagination>
                </div>
            </div>
        </div>

        <el-drawer custom-class="yq-drawer" :title="form.id ? getI18nValue('修改职业') : getI18nValue('新增职业')" :visible.sync="drawer"
            direction="rtl" size="35%" :wrapper-closable="false" @close="closeDrawer">
            <div class="drawer-content yq-drawer-content">
                <el-form label-width="150px" ref="ruleForm" :model="form" :rules="rules">
                    <el-form-item :label="getI18nValue('职业名称')" prop="professionName">
                        <el-input :placeholder="getI18nValue('请输入')" v-model="form.professionName"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="drawer-footer yq-drawer-footer">
                <el-button type="primary" plain @click="drawer=false">{{ getI18nValue('取消') }}</el-button>
                <el-button type="primary" :loading="isSave" @click="handleSubmit" style="margin-left: 16px">{{getI18nValue('确定')}}</el-button>
            </div>
        </el-drawer>

        <!-- 发展规划抽屉 -->
        <el-drawer custom-class="yq-drawer" :title="getI18nValue('职业发展规划')" :visible.sync="devPlanDrawer"
            direction="rtl" size="50%" :wrapper-closable="false" @close="closeDevPlanDrawer">
            <div class="drawer-content yq-drawer-content">
                <div class="dev-plan-form">
                    <div class="current-profession" style="margin-bottom: 20px;">
                        <span style="font-weight: bold;">{{getI18nValue('当前职业')}}: </span>
                        <span>{{currentProfession.professionName}}</span>
                    </div>

                    <el-form :model="devPlanForm" label-width="120px" style="margin-bottom: 20px;">
                        <el-form-item :label="getI18nValue('规划名称')" required>
                            <el-input v-model="devPlanForm.planName" :placeholder="getI18nValue('请输入规划名称')"></el-input>
                        </el-form-item>
                    </el-form>
                    
                    <div class="dev-plan-list">
                        <div v-for="(item, index) in sortedDevPlanList" :key="item.id" class="dev-plan-item">
                            <div class="item-header">
                                <div style="display: flex; align-items: center;">
                                    <i class="el-icon-rank drag-handle"></i>
                                    <span>{{getI18nValue('发展等级')}} {{index + 1}}</span>
                                </div>
                                <el-button type="text" @click="removePlanItem(index)" icon="el-icon-delete"></el-button>
                            </div>
                            <el-form :model="item" label-width="120px">
                                <el-form-item :label="getI18nValue('岗位')" required>
                                    <el-select v-model="item.positionId" :placeholder="getI18nValue('请选择岗位')">
                                        <el-option
                                            v-for="pos in positionList"
                                            :key="pos.id"
                                            :label="pos.positionName"
                                            :value="pos.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <el-button type="primary" plain icon="el-icon-plus" @click="addPlanItem">
                            {{getI18nValue('添加岗位')}}
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="drawer-footer yq-drawer-footer">
                <el-button type="primary" plain @click="devPlanDrawer=false">{{ getI18nValue('取消') }}</el-button>
                <el-button type="primary" :loading="isSavePlan" @click="saveDevPlan" style="margin-left: 16px">{{getI18nValue('确定')}}</el-button>
            </div>
        </el-drawer>
    </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>

<script>
    var appPage = new Vue({
        el: "#professionTable",
        data: function () {
            return {
                visible: false,
                loading: false,
                isSave: false,
                isSavePlan: false,
                moreSearch: false,
                drawer: false,
                devPlanDrawer: false,
                searchForm: {
                    pageType: "3",
                    professionName: ''
                },
                form: {
                    id: "",
                    professionName: ''
                },
                rules: {
                    professionName: [
                        { required: true, message: getI18nValue("请输入职业名称"), trigger: "change" }
                    ]
                },
                tableData: {
                    pageIndex: 1,
                    pageSize: 15,
                    totalRow: 0,
                    data: []
                },
                // 发展规划相关数据
                currentProfession: {},
                devPlanForm: {
                    planName: ''
                },
                devPlanList: [],
                positionList: [],
                sortable: null,
            };
        },
        computed: {
            sortedDevPlanList: {
                get() {
                    return this.devPlanList.map((item, index) => ({
                        ...item,
                        planLevel: index + 1
                    }));
                }
            }
        },
        methods: {
            onPageChange: function (page) {
                this.tableData.pageIndex = page;
                this.getList();
            },
            onPageSizeChange: function (size) {
                this.tableData.pageSize = size;
                this.getList();
            },
            handleEdit: function (data) {
                this.drawer = true;
                this.form = {
                    id: data.id,
                    professionName: data.professionName
                };
            },
            closeDrawer: function () {
                this.form.id = "";
                this.$refs["ruleForm"].resetFields();
            },
            handleReset() {
                this.$refs.searchForm.resetFields();
            },
            handleSubmit: function () {
                var _this = this;
                _this.$refs["ruleForm"].validate(function (valid) {
                    if (valid) {
                        _this.save();
                    }
                });
            },
            handleDel: function (data) {
                var _this = this;
                var query = {
                    id: data.id
                };
                this.$confirm(getI18nValue("是否删除该职业?"), getI18nValue("提示"), {
                    confirmButtonText: getI18nValue("确定"),
                    cancelButtonText: getI18nValue("取消"),
                    type: "warning"
                }).then(function () {
                    _this.delApi(query);
                }).catch(function () { });
            },
            handleAdd() {
                this.drawer = true;
                this.form = {
                    id: "",
                    professionName: ""
                };
            },
            getList: function (page) {
                this.tableData.pageIndex = page || this.tableData.pageIndex;
                const data = {
                    pageIndex: this.tableData.pageIndex,
                    pageSize: this.tableData.pageSize,
                    ...this.searchForm
                }
                yq.remoteCall("/cc-employee/webcall?action=ProfessionDao.getProfessionList", data, (res) => {
                    if (res.state == 1) {
                        this.tableData.data = res.data;
                        this.tableData.totalRow = res.totalRow;
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            },
            save: function () {
                var _this = this;
                _this.isSave = true;

                var query = {
                    ..._this.form
                };

                yq.remoteCall("/cc-employee/servlet/professionServlet?action=editProfession", query, (data) => {
                    yq.msg({
                        message: data.msg,
                        type: data.state == 1 ? "success" : "error"
                    });
                    if (data.state == 1) {
                        _this.drawer = false;
                        _this.getList(1);
                    }
                });
                _this.isSave = false;
            },
            delApi: function (data) {
                var _this = this;
                yq.remoteCall("/cc-employee/servlet/professionServlet?action=delProfession", data, function (res) {
                    yq.msg({
                        message: res.msg,
                        type: res.state == 1 ? "success" : "error"
                    });
                    if (res.state == 1) {
                        _this.getList(1);
                    }
                });
            },
            handleDevPlan: function(row) {
                this.currentProfession = row;
                this.devPlanDrawer = true;
                this.getPositionList();
                this.getDevPlanList();
                this.$nextTick(() => {
                    this.initSortable();
                });
            },
            initSortable: function() {
                const el = document.querySelector('.dev-plan-list');
                this.sortable = new Sortable(el, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    dragClass: 'sortable-drag',
                    onEnd: (evt) => {
                        const newIndex = evt.newIndex;
                        const oldIndex = evt.oldIndex;
                        
                        if (newIndex === oldIndex) return;
                        
                        // 更新数组顺序
                        const itemToMove = this.devPlanList.splice(oldIndex, 1)[0];
                        this.devPlanList.splice(newIndex, 0, itemToMove);
                        
                        // 强制更新视图
                        this.$nextTick(() => {
                            this.$forceUpdate();
                        });
                    }
                });
            },
            closeDevPlanDrawer: function() {
                this.devPlanDrawer = false;
                this.currentProfession = {};
                this.devPlanForm.planName = '';
                this.devPlanList = [];
                if (this.sortable) {
                    this.sortable.destroy();
                    this.sortable = null;
                }
            },
            getPositionList: function() {
                yq.remoteCall("/cc-employee/webcall?action=PositionDao.getPositionAll", {}, (res) => {
                    if (res.state == 1) {
                        this.positionList = res.data;
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            },
            getDevPlanList: function() {
                const params = {
                    professionId: this.currentProfession.id
                };
                yq.remoteCall("/cc-employee/webcall?action=DevPlanDao.getDevPlanList", params, (res) => {
                    if (res.state == 1) {
                        if (res.data && res.data.length > 0) {
                            this.devPlanForm.planName = res.data[0].planName;
                            this.devPlanList = res.data.map(item => ({
                                id: item.id || 'temp_' + Date.now(),
                                positionId: item.positionId,
                                planLevel: item.planLevel
                            }));
                        }
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            },
            addPlanItem: function() {
                const newLevel = this.devPlanList.length + 1;
                this.devPlanList.push({
                    id: 'temp_' + Date.now(),
                    positionId: '',
                    planLevel: newLevel
                });
            },
            removePlanItem: function(index) {
                this.devPlanList.splice(index, 1);
            },
            saveDevPlan: function() {
                if (!this.validateDevPlan()) {
                    return;
                }

                this.isSavePlan = true;
                const params = {
                    professionId: this.currentProfession.id,
                    planName: this.devPlanForm.planName,
                    planItems: this.sortedDevPlanList
                };

                yq.remoteCall("/cc-employee/servlet/devPlanServlet?action=editDevPlan", params, (res) => {
                    this.isSavePlan = false;
                    if (res.state == 1) {
                        this.$message.success(res.msg || getI18nValue('保存成功'));
                        this.devPlanDrawer = false;
                    } else {
                        this.$message.error(res.msg || getI18nValue('保存失败'));
                    }
                });
            },
            validateDevPlan: function() {
                if (!this.devPlanForm.planName) {
                    this.$message.warning(getI18nValue('请输入规划名称'));
                    return false;
                }

                if (this.devPlanList.length === 0) {
                    this.$message.warning(getI18nValue('请至少添加一个岗位'));
                    return false;
                }

                for (let item of this.devPlanList) {
                    if (!item.positionId) {
                        this.$message.warning(getI18nValue('请选择岗位'));
                        return false;
                    }
                }
                return true;
            }
        },
        mounted: function () {
            this.getList();
        }
    });
</script>

</html> 