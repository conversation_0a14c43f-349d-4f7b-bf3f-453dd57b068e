package com.yq.orderext.model;

import java.io.Serializable;

public class ProcessInfoModel extends OrderextBaseModel implements Serializable {
	private static final long serialVersionUID = 1L;

	private String id;
	private String name;	// 流程名称
	private String flowKey;	// 流程key
	private String type;	// 流程类型
	private String status;	// 流程状态
	private int version;	// 流程版本号
	private String xml;		// xml文件内容
	private String img;		// 图片文件内容（base64）
	private String bakup;	// 备注信息
	private String createTime;
	private String createUser;
	private String createDeptId;
	private String updateTime;
	private String updateUser;
	private String updateDeptId;
	private String entId;
	private String busiOrderId;
	private String fileType;	// 文件类型 1-xml 2-jpg 3-josn
	private String pcSrhFormKey; // pc端查看表单
	private String json;	//JSON文件内容
	private String skipUrl;
	private String icons;
	private String deploymentId;
	private String processDefineId;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getFlowKey() {
		return flowKey;
	}
	public void setFlowKey(String flowKey) {
		this.flowKey = flowKey;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public int getVersion() {
		return version;
	}
	public void setVersion(int version) {
		this.version = version;
	}
	public String getXml() {
		return xml;
	}
	public void setXml(String xml) {
		this.xml = xml;
	}
	public String getImg() {
		return img;
	}
	public void setImg(String img) {
		this.img = img;
	}
	public String getBakup() {
		return bakup;
	}
	public void setBakup(String bakup) {
		this.bakup = bakup;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	public String getCreateDeptId() {
		return createDeptId;
	}
	public void setCreateDeptId(String createDeptId) {
		this.createDeptId = createDeptId;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	public String getUpdateDeptId() {
		return updateDeptId;
	}
	public void setUpdateDeptId(String updateDeptId) {
		this.updateDeptId = updateDeptId;
	}
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	public String getBusiOrderId() {
		return busiOrderId;
	}
	public void setBusiOrderId(String busiOrderId) {
		this.busiOrderId = busiOrderId;
	}
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	public String getPcSrhFormKey() {
		return pcSrhFormKey;
	}
	public void setPcSrhFormKey(String pcSrhFormKey) {
		this.pcSrhFormKey = pcSrhFormKey;
	}
	public String getJson() {
		return json;
	}
	public void setJson(String json) {
		this.json = json;
	}
	public String getSkipUrl() {
		return skipUrl;
	}
	public void setSkipUrl(String skipUrl) {
		this.skipUrl = skipUrl;
	}
	public String getIcons() {
		return icons;
	}
	public void setIcons(String icons) {
		this.icons = icons;
	}
	public String getDeploymentId() {
		return deploymentId;
	}
	public void setDeploymentId(String deploymentId) {
		this.deploymentId = deploymentId;
	}
	public String getProcessDefineId() {
		return processDefineId;
	}
	public void setProcessDefineId(String processDefineId) {
		this.processDefineId = processDefineId;
	}
	
}
