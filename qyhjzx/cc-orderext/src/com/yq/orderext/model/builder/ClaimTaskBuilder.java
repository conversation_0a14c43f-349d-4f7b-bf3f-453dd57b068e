package com.yq.orderext.model.builder;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.DateUtil;
import com.yq.orderext.model.BaseOrderModel;
import com.yq.orderext.model.OrderFollowModel;

public class ClaimTaskBuilder extends BaseOrderBuilder<ClaimTaskBuilder> {

	// 工单id
	private String orderId;
	// 创建人账号
	private String createAcc;
	// 业务订购id
	private String busiOrderId;
	// 企业id
	private String epCode;
	// 创建部门编号
	private String createDeptCode;
	// 创建人名称
	private String createName;
	// 跟进内容
	private String content;
	// 任务id
	private String taskId;
	// 转派人员
	private String transferAssignee;
	// 是否分派
	private String isAssign;
	// 是否自动分派
	private boolean isAutoAssign;
	
	public BaseOrderModel buildBaseOrderModel() {
		BaseOrderModel result = new BaseOrderModel();
		result.setId(orderId);
		if (StringUtils.isNotBlank(transferAssignee)) {
			result.setDealAcc(transferAssignee);
		} else {
			result.setDealAcc(createAcc);
		}
		result.setEpCode(epCode);
		result.setBusiOrderId(busiOrderId);
		// result.setIsRead(DictConstants.DICT_SY_YN_Y);
		setCommonVar(result);
		return result;
	}
	
	public OrderFollowModel buildFollowModel() {
		OrderFollowModel result = new OrderFollowModel();
		result.setCreateAcc(createAcc);
		result.setCreateDeptCode(createDeptCode);
		result.setCreateName(createName);
		result.setAssignee(createAcc);
		result.setCreateTime(DateUtil.getCurrentDateStr());
		result.setEpCode(epCode);
		result.setBusiOrderId(busiOrderId);
		result.setContent(content);
		result.setOrderId(orderId);
		result.setTaskId(taskId);
		result.setType(OrderFollowModel.TYPE_DO);
		setCommonVar(result);
		return result;
	}
	
	public String getTransferAssignee() {
		return this.transferAssignee;
	}

	@Override
	void handleFlowVar(Map<String, Object> flowVar) {}
	
	public ClaimTaskBuilder setOrderId(String orderId) {
		this.orderId = orderId;
		return this;
	}
	public ClaimTaskBuilder setCreateAcc(String createAcc) {
		this.createAcc = createAcc;
		return this;
	}
	public ClaimTaskBuilder setBusiOrderId(String busiOrderId) {
		this.busiOrderId = busiOrderId;
		return this;
	}
	public ClaimTaskBuilder setEpCode(String epCode) {
		this.epCode = epCode;
		return this;
	}
	public ClaimTaskBuilder setCreateDeptCode(String createDeptCode) {
		this.createDeptCode = createDeptCode;
		return this;
	}
	public ClaimTaskBuilder setCreateName(String createName) {
		this.createName = createName;
		return this;
	}
	public ClaimTaskBuilder setContent(String content) {
		this.content = content;
		return this;
	}

	public ClaimTaskBuilder setTaskId(String taskId) {
		this.taskId = taskId;
		return this;
	}

	public ClaimTaskBuilder setTransferAssignee(String transferAssignee) {
		this.transferAssignee = transferAssignee;
		return this;
	}

	public String getIsAssign() {
		return this.isAssign;
	}

	public ClaimTaskBuilder setIsAssign(String isAssign) {
		this.isAssign = isAssign;
		return this;
	}

	public boolean isAutoAssign() {
		return isAutoAssign;
	}

	public ClaimTaskBuilder setAutoAssign(boolean isAutoAssign) {
		this.isAutoAssign = isAutoAssign;
		return this;
	}

}
