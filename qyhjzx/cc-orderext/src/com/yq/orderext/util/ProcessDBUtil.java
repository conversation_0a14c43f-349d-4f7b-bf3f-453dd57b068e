package com.yq.orderext.util;

import java.sql.Connection;

import javax.sql.DataSource;

import org.apache.commons.lang.StringUtils;
import org.easitline.common.core.EasyPool;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyQuery;

import com.yq.orderext.base.Constants;
import com.yq.orderext.base.OrderextLogger;

/**
 * @Title:	数据库操作工具类(保证一个线程拿到同一个链接)
 * @Description:	数据库操作工具类(保证一个线程拿到同一个链接),可应用于跨函数、跨类进行事务处理 
 * @Copyright:	Copyright (c) 2013
 * @Company:	云趣科技
 * <AUTHOR>
 * @version	V1.0  
 * @Date	2019年3月26日 下午5:24:37 
 */

public class ProcessDBUtil {
	private static ThreadLocal<Connection> connThreadMgr = new ThreadLocal<>();
	
	public static DataSource getDataSource(String appName, String dataSourceName) {
		try {
			return EasyPool.getInstance().getDatasource(AppContext.getContext(appName).getDatasourceName(dataSourceName));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
			return null;
		}
	}
	
	public static void setConnection(Connection conn) {
		connThreadMgr.set(conn);
	}
	
	public static Connection openConnection(String appName, String dataSrouceName) {
		Connection conn = null;
		conn = AppContext.getContext(appName).getConnection(dataSrouceName);
		connThreadMgr.set(conn);
		return conn;
	}
	
	public static Connection getConnection() {
		return getConnection(null, null);
	}
	
	public static Connection getConnection(String appName, String dataSrouceName) {
		Connection conn = connThreadMgr.get();
		if (conn == null && StringUtils.isNotBlank(appName) && StringUtils.isNotBlank(dataSrouceName)) {
			conn = openConnection(appName, dataSrouceName);
		}
		return conn;
	}
	
	public static void closeConnection(Connection conn) {
		try {
			if (conn!=null) {
				conn.close();
				connThreadMgr.remove();
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
		}
	}
	
	public static void closeConnection() {
		Connection conn = getConnection();
		connThreadMgr.set(null);
		closeConnection(conn);
	}
	
	public static void roolback(EasyQuery query) {
		try {
			if (query!=null) {
				query.roolback();
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
		}
	}
	public static void roolback(Connection conn) {
		try {
			if (conn!=null) {
				conn.rollback();
				conn.setAutoCommit(true);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
		}
	}
	
	public static void begin(Connection conn) {
		try {
			if (conn!=null) {
				conn.setAutoCommit(false);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
		}
	}
	
	public static void commit(Connection conn) {
		try {
			if (conn!=null) {
				conn.commit();
				conn.setAutoCommit(true);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			OrderextLogger.logger.error(e.getMessage(), e);
		}
	}
	
	public static EasyQuery getQuery() {
		Connection conn = getConnection();
		if (conn == null) {
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.PROCESS_DS);
		}
		return EasyQuery.getQuery(conn);
	}
}
