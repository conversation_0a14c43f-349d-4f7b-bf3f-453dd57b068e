package com.yunqu.alarm.listener;

import com.yunqu.alarm.base.Constants;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;

@WebListener
public class AppServiceListener extends ServiceContextListener {
    @Override
    protected List<ServiceResource> serviceResourceCatalog() {
        List<ServiceResource> list = new ArrayList<ServiceResource>();

        ServiceResource  resource1 = new ServiceResource();
        resource1.appName     = Constants.APP_NAME; //服务所在的WAR应用名
        resource1.className   =  "com.yunqu.alarm.service.CdrCallNotifyService";//服务实现类，类必须实现IService接口
        resource1.description =  "CDR话单通知处理服务，用于通话异常监控实时更新数据";//服务描述
        resource1.serviceId   =  "BATCHCALL_CDR_EXT_CALLFAIL";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
        resource1.serviceName =  "CDR话单通知处理服务";//服务名称
        list.add(resource1);
        return list;
    }
}
