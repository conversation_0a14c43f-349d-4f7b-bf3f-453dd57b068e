package com.yunqu.ccportal.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.StatDaoContext;

/**
 * 现场监控
 * <AUTHOR>
 *
 */
@WebObject(name="MonitorDao")
public class MonitorDao extends StatDaoContext{
	
	/**
	 * 企业监控列表
	 * @return
	 */
	@WebControl(name="entList",type=Types.LIST)
	public JSONObject entList(){
		//获取企业的所有技能组SKILL_GROUP_ID,SKILL_GROUP_NAME
		EasySQL sql = this.getEasySQL("select * from "+getTableName("CC_SKILL_GROUP")+" where 1=1 ");
		sql.append(this.getBusiOrderId()," and BUSI_ORDER_ID = ? " );
		sql.append(this.getEntId(), " and ENT_ID = ? ",false);
		sql.append(this.getSkillRes("SKILL_GROUP_ID",null));
		sql.append(" order by IDX_ORDER");
		JSONObject skill = this.queryForList(sql.getSQL(), sql.getParams(),null);
		if(skill!=null){
			JSONArray groupList = skill.getJSONArray("data");
			JSONObject skillGroup = null;
			EasyCache cache=CacheManager.getMemcache();
			JSONArray resultList = new JSONArray();
			for (int i = 0; i < groupList.size(); i++) {
				skillGroup = groupList.getJSONObject(i);//技能组对象
				if(skillGroup.getIntValue("BUSI_TYPE") == 1){
					continue;
					
				}
				JSONObject skillGroupAgent  = cache.get("ICCS_SKILLGROUP_"+skillGroup.getString("SKILL_GROUP_ID"));//技能组监控对象
				
				if(skillGroupAgent != null){
					skillGroupAgent.put("skillGroupName",skillGroup.get("SKILL_GROUP_NAME"));//技能组名称，todo:由于缓存中的数据是乱码，这里直接读数据库的
					skillGroupAgent.put("agentCount",skillGroup.get("AGENT_COUNT"));//坐席人数，todo:缓存中没有这个字段
					skillGroup.putAll(skillGroupAgent);
				}else{
					skillGroup.put("skillGroupName",skillGroup.get("SKILL_GROUP_NAME"));//技能组名称
					skillGroup.put("agentCount",skillGroup.get("AGENT_COUNT"));//坐席人数
					skillGroup.put("logonAgentCount",0);
					skillGroup.put("talkAgentCount",0);
					skillGroup.put("idleAgentCount",0);
					skillGroup.put("busyAgentCount",0);
					skillGroup.put("alertAgentCount",0);
					skillGroup.put("workNotReadyAgentCount",0);
					skillGroup.put("queueCallCount",0);
					skillGroup.put("aveQueueLen",0);
					skillGroup.put("abandonQueueCount",0);
					skillGroup.put("abandonAgentCount",0);
				}
				resultList.add(skillGroup);
			}
			skill.put("data", resultList);//技能组的统计
		}
		return skill;
	}
	
	/**
	 * 技能组总体话务情况监控
	 * @return
	 */
	@WebControl(name="entRecord",type=Types.RECORD)
	public JSONObject entRecord(){
		//获取企业的监控对象
		JSONObject jsonObject  = CacheManager.getMemcache().get("ICCS_ENT_"+this.getEntId());
		if(jsonObject == null){
			jsonObject=new JSONObject();
			jsonObject.put("entId", "1");
			jsonObject.put("entName", "企业名称");
			jsonObject.put("entCode", 0);
			jsonObject.put("logonAgentCount", "0");
			jsonObject.put("idleAgentCount", 0);
			jsonObject.put("busyAgentCount", 0);
			jsonObject.put("talkAgentCount", 0);
			jsonObject.put("workNotReadyAgentCount", 0);
			jsonObject.put("alertAgentCount", 0);
			jsonObject.put("updateTime", "");
		}
		return getJsonResult(jsonObject);
	}
	

	/**
	 * 业务员状态监控列表
	 * @return
	 */
	@WebControl(name="agentList",type=Types.LIST)
	public JSONObject list(){
		long thisTime = System.currentTimeMillis()/1000;
		EasySQL sql= this.getEasySQL("select t1.WORK_MODE,t1.AGENT_ID ,t1.IP_ADDR,t1.AGENT_NAME,t1.AGENT_STATE,t1.CUST_PHONE,t1.CALL_TYPE,t1.CALL_TIME,t1.IVR_TIME,t1.AGENT_TIME,t1.UPDATE_TIME,t1.PING_TIME,t1.LOGON_FLAG");
		sql.append(", ("+thisTime+" - t1.STATE_TIME ) as STATE_TIME from");
		sql.append(getStatTableName("CC_RPT_AGENT_MONITOR t1"));
		sql.append(",CC_USER T2,").append(getTableName("CC_SKILL_GROUP_USER T3"));
		sql.append("where t1.LOGON_FLAG = 1");
		sql.append("and T1.AGENT_ID = T2.USER_ACCT and T2.USER_ID = T3.USER_ID");
		sql.append(this.getEntId(), " and t1.ENT_ID = ? " , "-1");
		
		if("all".equals(param.getString("groupId"))){		//获取全部技能组座席信息
			sql.append(this.getUserResSql("T2.USER_ID"));
		}else{
			sql.append(getBusiOrderId(),"and t3.BUSI_ORDER_ID = ?");
			sql.append(param.getString("groupId"), " and t3.SKILL_GROUP_ID = ? ", false);
		}
		return this.queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 业务员监控状态汇总
	 * @return
	 */
	@WebControl(name="agentRecord",type=Types.RECORD)
	public  JSONObject agentRecord(){
		
		JSONObject jsonObject  = null;
		if("all".equals(param.getString("groupId"))){
			jsonObject  = CacheManager.getMemcache().get("ICCS_ENT_"+this.getEntId());
			if(jsonObject != null){
				jsonObject.put("queueCallCount", '-');
				jsonObject.put("aveQueueLen", '-');
				jsonObject.put("maxQueueLen", '-');
				jsonObject.put("abandonQueueCount", '-');
				jsonObject.put("abandonAgentCount", '-');
			}
		}else{
			jsonObject  = CacheManager.getMemcache().get("ICCS_SKILLGROUP_"+param.getString("groupId"));
		}
		if(jsonObject==null){
			jsonObject=new JSONObject();
			jsonObject.put("logonAgentCount", "0");
			jsonObject.put("idleAgentCount", 0);
			jsonObject.put("busyAgentCount", 0);
			jsonObject.put("busyAgentCount", 0);
			jsonObject.put("talkAgentCount", 0);
			jsonObject.put("workNotReadyAgentCount", 0);
			jsonObject.put("alertAgentCount", 0);
			jsonObject.put("queueCallCount", 0);
			jsonObject.put("aveQueueLen", 0);
			jsonObject.put("maxQueueLen", 0);
			jsonObject.put("abandonQueueCount", 0);
			jsonObject.put("abandonAgentCount", 0);
			jsonObject.put("updateTime", 0);
		}
		return getJsonResult(jsonObject);
	}
	
	/**
	 * 获取状态汇总统计
	 * @return
	 */
	@WebControl(name="agentStateCount",type=Types.RECORD)
	public JSONObject agentStateCount(){
		//从缓存取得统计数据
		String key = "all".equals(param.getString("groupId"))?"ICCS_ENT_" + this.getEntId():"ICCS_SKILLGROUP_" + param.getString("groupId");
		//取统计数据
		JSONObject jsonObject = CacheManager.getMemcache().get(key);
		
		if (jsonObject == null) {
			jsonObject = new JSONObject();
			jsonObject.put("logonAgentCount", 0);
			jsonObject.put("idleAgentCount", 0);
			jsonObject.put("busyAgentCount", 0);
			jsonObject.put("busyAgentCount", 0);
			jsonObject.put("talkAgentCount", 0);
			jsonObject.put("workNotReadyAgentCount", 0);
			jsonObject.put("alertAgentCount", 0);
			jsonObject.put("queueCallCount", 0);
			jsonObject.put("aveQueueLen", 0);
			jsonObject.put("maxQueueLen", 0);
			jsonObject.put("abandonQueueCount", 0);
			jsonObject.put("abandonAgentCount", 0);
			jsonObject.put("creatTime", EasyDate.getCurrentDateString());
		}else {
			if("all".equals(param.getString("groupId"))) {
				jsonObject.put("queueCallCount", 0);
				jsonObject.put("aveQueueLen", 0);
				jsonObject.put("maxQueueLen", 0);
				jsonObject.put("abandonQueueCount", 0);
				jsonObject.put("abandonAgentCount", 0);
			}
			jsonObject.put("creatTime", EasyDate.getCurrentDateString());
		}
		return getJsonResult(jsonObject);
	}
}
