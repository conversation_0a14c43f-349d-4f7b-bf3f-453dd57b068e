
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>坐席状态同步 · ccbar jssdk话务能力接口文档</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="http://www.yunqu-info.com/">
        <meta name="identifier" content="false" scheme="ISBN">
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-splitter/splitter.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-prism/prism.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-back-to-top-button/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="event-notify.html" />
    
    
    <link rel="prev" href="event.html" />
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                        <b>1.1.</b>
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="rule.html">
            
                <a href="rule.html">
            
                    
                        <b>1.2.</b>
                    
                    通讯协议和规则
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="layout.html">
            
                <a href="layout.html">
            
                    
                        <b>1.3.</b>
                    
                    开发流程
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="data-.html">
            
                <a href="data-.html">
            
                    
                        <b>1.4.</b>
                    
                    基于data-*的界面绑定
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="initv2.html">
            
                <a href="initv2.html">
            
                    
                        <b>1.5.</b>
                    
                    初始化方法V2.0
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="call.html">
            
                <a href="call.html">
            
                    
                        <b>1.6.</b>
                    
                    话务事件方法
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="cmd-logon.html">
            
                <a href="cmd-logon.html">
            
                    
                        <b>1.6.1.</b>
                    
                    签入
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="cmd-logoff.html">
            
                <a href="cmd-logoff.html">
            
                    
                        <b>1.6.2.</b>
                    
                    签出
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="cmd-makcall.html">
            
                <a href="cmd-makcall.html">
            
                    
                        <b>1.6.3.</b>
                    
                    外呼
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="cmd-answercall.html">
            
                <a href="cmd-answercall.html">
            
                    
                        <b>1.6.4.</b>
                    
                    应答
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.5" data-path="cmd-clearcall.html">
            
                <a href="cmd-clearcall.html">
            
                    
                        <b>1.6.5.</b>
                    
                    挂机
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.6" data-path="cmd-ready.html">
            
                <a href="cmd-ready.html">
            
                    
                        <b>1.6.6.</b>
                    
                    置忙
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.7" data-path="cmd-notready.html">
            
                <a href="cmd-notready.html">
            
                    
                        <b>1.6.7.</b>
                    
                    置闲
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.8" data-path="cmd-holdcall.html">
            
                <a href="cmd-holdcall.html">
            
                    
                        <b>1.6.8.</b>
                    
                    保持
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.9" data-path="cmd-retrievecall.html">
            
                <a href="cmd-retrievecall.html">
            
                    
                        <b>1.6.9.</b>
                    
                    恢复
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.10" data-path="cmd-consultcall.html">
            
                <a href="cmd-consultcall.html">
            
                    
                        <b>1.6.10.</b>
                    
                    咨询
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.11" data-path="cmd-transfercall.html">
            
                <a href="cmd-transfercall.html">
            
                    
                        <b>1.6.11.</b>
                    
                    转移
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.12" data-path="cmd-satisfy.html">
            
                <a href="cmd-satisfy.html">
            
                    
                        <b>1.6.12.</b>
                    
                    转满意度调查
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.13" data-path="cmd-conferencecall.html">
            
                <a href="cmd-conferencecall.html">
            
                    
                        <b>1.6.13.</b>
                    
                    三方
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.14" data-path="cmd-worknotready.html">
            
                <a href="cmd-worknotready.html">
            
                    
                        <b>1.6.14.</b>
                    
                    进入话后整理
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.15" data-path="cmd-workready.html">
            
                <a href="cmd-workready.html">
            
                    
                        <b>1.6.15.</b>
                    
                    结束话后整理
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.16" data-path="cmd-autoanswer.html">
            
                <a href="cmd-autoanswer.html">
            
                    
                        <b>1.6.16.</b>
                    
                    自动应答切换
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.17" data-path="cmd-workmode.html">
            
                <a href="cmd-workmode.html">
            
                    
                        <b>1.6.17.</b>
                    
                    呼叫模式切换
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.18" data-path="cmd-callerlist.html">
            
                <a href="cmd-callerlist.html">
            
                    
                        <b>1.6.18.</b>
                    
                    获取外显列表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.19" data-path="cmd-grouplist.html">
            
                <a href="cmd-grouplist.html">
            
                    
                        <b>1.6.19.</b>
                    
                    获取技能组列表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.20" data-path="cmd-agentlist.html">
            
                <a href="cmd-agentlist.html">
            
                    
                        <b>1.6.20.</b>
                    
                    获取空闲坐席
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="event.html">
            
                <a href="event.html">
            
                    
                        <b>1.7.</b>
                    
                    事件通知
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter active" data-level="1.7.1" data-path="event-agentstate.html">
            
                <a href="event-agentstate.html">
            
                    
                        <b>1.7.1.</b>
                    
                    坐席状态同步
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="event-notify.html">
            
                <a href="event-notify.html">
            
                    
                        <b>1.7.2.</b>
                    
                    消息响应通知
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="event-callevent.html">
            
                <a href="event-callevent.html">
            
                    
                        <b>1.7.3.</b>
                    
                    话务事件通知
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="event-asr.md">
            
                <span>
            
                    
                        <b>1.7.4.</b>
                    
                    转写事件通知
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="agentstate.html">
            
                <a href="agentstate.html">
            
                    
                        <b>1.8.</b>
                    
                    坐席状态列表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.9" data-path="createcause.html">
            
                <a href="createcause.html">
            
                    
                        <b>1.9.</b>
                    
                    呼叫原因(createCause)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.10" data-path="clearCause.html">
            
                <a href="clearCause.html">
            
                    
                        <b>1.10.</b>
                    
                    挂机原因(clearCause)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.11" data-path="errorcode.html">
            
                <a href="errorcode.html">
            
                    
                        <b>1.11.</b>
                    
                    错误代码
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.12" data-path="qa.html">
            
                <a href="qa.html">
            
                    
                        <b>1.12.</b>
                    
                    常见问题
            
                </a>
            

            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >坐席状态同步</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x540C;&#x6B65;">&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x540C;&#x6B65;</h1>
<p>&#x5728;&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x53D1;&#x751F;&#x53D8;&#x66F4;&#x65F6;,&#x901A;&#x8FC7;&#x6DFB;&#x52A0; <font style="color:red">agentStateSync</font>&#x4E8B;&#x4EF6;&#x53EF;&#x4EE5;&#x76D1;&#x542C;&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x540C;&#x6B65;&#x4E8B;&#x4EF6;</p>
<h5 id="&#x8C03;&#x7528;&#x793A;&#x4F8B;">&#x8C03;&#x7528;&#x793A;&#x4F8B;</h5>
<pre class="language-"><code>ccbarEvent.addEvent(&apos;agentStateSync&apos;,handelAgentStateSync);
function handelAgentStateSync(event){
    console.log(event);
}
</code></pre><h5 id="event&#x5BF9;&#x8C61;&#x53C2;&#x6570;&#x8BF4;&#x660E;">event&#x5BF9;&#x8C61;&#x53C2;&#x6570;&#x8BF4;&#x660E;</h5>
<table>
<thead>
<tr>
<th><strong>&#x53C2;&#x6570;&#x540D;</strong></th>
<th><strong>&#x53C2;&#x6570;&#x7C7B;&#x578B;</strong></th>
<th><strong>&#x53C2;&#x6570;&#x8BF4;&#x660E;</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>workMode</td>
<td>String</td>
<td>&#x5F53;&#x524D;&#x5DE5;&#x4F5C;&#x6A21;&#x5F0F;</td>
</tr>
<tr>
<td>stateDesc</td>
<td>String</td>
<td>&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x540D;&#x79F0;</td>
</tr>
<tr>
<td>autoAnswer</td>
<td>String</td>
<td>&#x662F;&#x5426;&#x81EA;&#x52A8;&#x5E94;&#x7B54;</td>
</tr>
<tr>
<td>state</td>
<td>String</td>
<td>&#x5750;&#x5E2D;&#x72B6;&#x6001;</td>
</tr>
<tr>
<td>funcMask</td>
<td>Object</td>
<td>&#x529F;&#x80FD;&#x6309;&#x94AE;&#x72B6;&#x6001;,&#x5B50;&#x9879;true&#x4EE3;&#x8868;&#x53EF;&#x7528;,false&#x4E3A;&#x4E0D;&#x53EF;&#x7528;</td>
</tr>
</tbody>
</table>
<h5 id="event&#x5BF9;&#x8C61;&#x793A;&#x4F8B;">event&#x5BF9;&#x8C61;&#x793A;&#x4F8B;</h5>
<pre class="language-"><code>{
  &quot;workMode&quot;: &quot;inbound&quot;, //&#x5F53;&#x524D;&#x5DE5;&#x4F5C;&#x6A21;&#x5F0F;
  &quot;bizSessionId&quot;: &quot;F536496F6310EB28DF37E00D3D28AF0A&quot;,
  &quot;stateDesc&quot;: &quot;&#x95F2;&quot;,//&#x5750;&#x5E2D;&#x72B6;&#x6001;&#x540D;&#x79F0;
  &quot;notifyContent&quot;: &quot;&quot;,
  &quot;autoAnswer&quot;: &quot;true&quot;,//&#x81EA;&#x52A8;&#x5E94;&#x7B54;&#x72B6;&#x6001;
  &quot;state&quot;: &quot;IDLE&quot;,//&#x5750;&#x5E2D;&#x72B6;&#x6001;
  &quot;resultDesc&quot;: null,
  &quot;funcMask&quot;: { //&#x529F;&#x80FD;&#x6309;&#x94AE;&#x72B6;&#x6001;
    &quot;logon&quot;: false,
    &quot;deleteConf&quot;: false,
    &quot;transfercall&quot;: false,
    &quot;stopInvent&quot;: false,
    &quot;agentnotready&quot;: true,
    &quot;workReady&quot;: false,
    &quot;createConf&quot;: false,
    &quot;consultationcall&quot;: false,
    &quot;holdcall&quot;: false,
    &quot;conferencecall&quot;: false,
    &quot;quitConf&quot;: false,
    &quot;sstransfer&quot;: false,
    &quot;workNotReady&quot;: false,
    &quot;interventcall&quot;: false,
    &quot;clearconnection&quot;: false,
    &quot;stopMonitor&quot;: false,
    &quot;answercall&quot;: false,
    &quot;clearcall&quot;: false,
    &quot;interceptCall&quot;: false,
    &quot;logoff&quot;: true,
    &quot;startMonitor&quot;: true,
    &quot;agentready&quot;: false,
    &quot;unholdcall&quot;: false,
    &quot;listencall&quot;: true,
    &quot;makecall&quot;: true,
    &quot;singleTransferCall&quot;: false,
    &quot;startInvent&quot;: true
  }
}
</code></pre>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="event.html" class="navigation navigation-prev " aria-label="Previous page: 事件通知">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="event-notify.html" class="navigation navigation-next " aria-label="Next page: 消息响应通知">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"坐席状态同步","level":"1.7.1","depth":2,"next":{"title":"消息响应通知","level":"1.7.2","depth":2,"path":"jssdk/event-notify.md","ref":"jssdk/event-notify.md","articles":[]},"previous":{"title":"事件通知","level":"1.7","depth":1,"path":"jssdk/event.md","ref":"jssdk/event.md","articles":[{"title":"坐席状态同步","level":"1.7.1","depth":2,"path":"jssdk/event-agentstate.md","ref":"jssdk/event-agentstate.md","articles":[]},{"title":"消息响应通知","level":"1.7.2","depth":2,"path":"jssdk/event-notify.md","ref":"jssdk/event-notify.md","articles":[]},{"title":"话务事件通知","level":"1.7.3","depth":2,"path":"jssdk/event-callevent.md","ref":"jssdk/event-callevent.md","articles":[]},{"title":"转写事件通知","level":"1.7.4","depth":2,"path":"jssdk/event-asr.md","ref":"jssdk/event-asr.md","articles":[]}]},"dir":"ltr"},"config":{"plugins":["-lunr","splitter","-search","-sharing","search-plus","prism","-highlight","back-to-top-button","-toggle-chapters","copy-code-button"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":true,"progress":true},"splitter":{},"search-plus":{},"prism":{},"back-to-top-button":{},"copy-code-button":{},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"http://www.yunqu-info.com/","pdf":{"pageBreaksBefore":"/","headerTemplate":null,"paperSize":"a4","margin":{"right":62,"left":62,"top":36,"bottom":36},"fontSize":12,"fontFamily":"Arial","footerTemplate":null,"chapterMark":"pagebreak","pageNumbers":false},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"isbn":"false","variables":{},"title":"ccbar jssdk话务能力接口文档","language":"zh-hans","links":{"sharing":{"all":null,"facebook":false,"google":false,"twitter":false,"weibo":true}},"gitbook":"*","description":"话务能力接口文档","extension":null},"file":{"path":"jssdk/event-agentstate.md","mtime":"2019-11-15T03:26:28.261Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2023-09-05T09:32:12.579Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-splitter/splitter.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-back-to-top-button/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-copy-code-button/toggle.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

