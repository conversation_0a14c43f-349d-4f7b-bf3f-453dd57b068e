<html>

<head>
    <meta charset="UTF-8">
    <title>ccbar开发手册</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="icon" type="image/x-icon" href="./favicon.ico">
    <link rel="stylesheet" type="text/css" href="ccbar/ccbar-sdk.css">
    <script src="/easitline-static/js/jquery.min.js"></script>
    <script src='ccbar/ccbar.js'></script>
    <script src='ccbar/md5.js'></script>
    <script src="ccbar/ccbar_sdk.js?t=233333"></script>
    <style>
        .layui-btn.disabled{
      opacity: 0.7;
      cursor: not-allowed;
    }
    .ccbar-phone{z-index: 1000;}
  </style>
    <script type="text/javascript">
    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function iniSDK() {
        var host = $("#host").val();
        var entId = $("#entId").val();
        var loginKey = $("#loginKey").val();
        var productId = $("#productId").val();
        var type = $("#layoutType").val();

        ccbarsdk.init(host, entId, productId, loginKey, function() {
            ccbar_plugin.init('', '', type);
        });
    }

    /*初始化*/
    $(function() {
        var host = location.origin; //云呼平台接入地址，由云呼平台提供
        var entId = GetQueryString('entId'); //企业ID，由云呼平台提供
        var loginKey = GetQueryString('loginKey'); //登录密钥，32位长度，由云呼平台提供
        var productId = GetQueryString('productId'); //企业订购的产品，由云呼平台提供

        $("#host").val(host);
        $("#entId").val(entId);
        $("#loginKey").val(loginKey);
        $("#productId").val(productId);





        // ccbarsdk.registMessageListener('evtAltering',handleMessage);//振铃
        // ccbarsdk.registMessageListener('evtConnected',handleMessage);//开始通话
        // ccbarsdk.registMessageListener('logon',handleLogon);//签入
        // ccbarsdk.registMessageListener('logoff',handleLogoff);//签出
        // ccbarsdk.registMessageListener('evtDisConnected',handleClearcall);//呼叫结束

        //ccbarsdk.makecall('13112341234',''); //外呼


    });
    //处理通话
    function handleMessage(result) {
        console.log(JSON.stringify(result));
    }
    //处理签入
    function handleLogon(result) {
        console.log(JSON.stringify(result));
    }
    //处理签出
    function handleLogoff(result) {
        console.log(JSON.stringify(result));
    }
    //处理呼叫结束
    function handleClearcall(result) {
        console.log(JSON.stringify(result));
    }
    </script>
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css?t=1515376178738" media="all">
    <style>
        .site-demo-title {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
  }
  .site-demo{
    bottom: 60px;
     left: 0;
      padding: 0;    top: 41px;
  }
   .document-p{padding: 10px;}   
  </style>
    　　　
</head>
<style>
    .ccbar-table{width: 100%;}
      .ccbar-table td{padding: 5px 10px;}
      .ccbar-table-th{text-align: right;color: #333;width: 90px}
      .ccbar-label{}
      .ccbar-select,.ccbar-input{display: block;width: 100%;}

  </style>
<script>
    $(function(){
        $("input[name=trantype]").off("click").on("click",function(){
            var v= $(this).val();
            if(v=='1'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=1]").show();
            }else if(v=='4'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=4]").show();
            }else if(v=='3'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=3]").show();
            }
        });
    });
    var testResult = {
        "msg": "<EventServlet> - >> 事件[agentList]消息，处理成功！",
        "data":
        {
            "groups": [
            {
                "SKILL_GROUP_ID": "1",
                "SKILL_GROUP_NAME": "班组1"
            },
            {
                "SKILL_GROUP_ID": "25",
                "SKILL_GROUP_NAME": "班组11"
            },
            {
                "SKILL_GROUP_ID": "6",
                "SKILL_GROUP_NAME": "234234"
            }],
            "agents": [
            {
                "AGENT_ID": "8007@1000",
                "AGENT_NAME": "8007"
            },
            {
                "AGENT_ID": "8006@1000",
                "AGENT_NAME": "8006"
            }]
        },
        "state": 1
    };

  </script>
</head>

<body onunload='checkLeave()'>
    <!-- 通话中功能 -->
    <div class="calling" style="display: none">
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="consultationcall">咨询</a></li>
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="sstransfer">呼叫转移</a></li>
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="holdcall">客户话路保持</a></li>
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="unholdcall">客户话路恢复</a></li>
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="conferencecall">三方通话</a></li>
        <li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="listencall">监听</a></li>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="demoTitle">
        <ul class="layui-tab-title site-demo-title">
            <li class="layui-this">预览</li>
            <li class="">代码说明</li>
            <li>常见问题 Q&A</li>
            <li>常用代码片段</li>
            <li>sipphone测试</li>
        </ul>
        <div class="layui-body layui-tab-content site-demo site-demo-body">
            <div class="layui-tab-item layui-show">
                <div class="" style="padding:20px;">
                    <div id="LAY_preview">
                        <fieldset class="layui-elem-field">
                            <legend>初始化</legend>
                            <div class="layui-field-box">
                                <form action="javascript:;" class="layui-form layui-form-pane">
                                    <div class="layui-form-item">
                                        <div class="layui-inline">
                                            <label class="layui-form-label">接入地址</label>
                                            <div class="layui-input-inline">
                                                <input id="host" value="" type="text" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <label class="layui-form-label">企业ID</label>
                                            <div class="layui-input-inline">
                                                <input id="entId" value="" type="text" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <label class="layui-form-label">登录秘钥</label>
                                            <div class="layui-input-inline">
                                                <input id="loginKey" value="" type="text" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <label class="layui-form-label">企业产品ID</label>
                                            <div class="layui-input-inline">
                                                <input id="productId" value="" type="text" autocomplete="off" class="layui-input">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <label class="layui-form-label">界面</label>
                                            <div class="layui-input-inline">
                                                <select id="layoutType" autocomplete="off" class="layui-input">
                                                    <option value="true" selected="">基本功能默认界面</option>
                                                    <option value="advanced">带进阶功能默认界面</option>
                                                    <option value="false">自定义界面</option>
                                                </select>
                                            </div>
                                        </div>
                                        <button class="layui-btn layui-btn-sm" onclick="iniSDK()">初始化ccbar</button>
                                    </div>
                                </form>
                            </div>
                        </fieldset>
                        <!-- ccbar的容器 -->
                        <div id="ccbar"></div>
                        <hr>
                        <fieldset class="layui-elem-field">
                            <legend>自定义界面</legend>
                            <div class="layui-field-box">
                                <p class="document-p">
                                    自定义面板主要通过data-api进行绑定,实现ccbar的对应功能,具体参考代码说明
                                </p>
                                <div>
                                    <button class="layui-btn layui-btn-primary" data-ccbartype="logon" data-toggle="ccbarlogon">打开ccbar签入面板签入</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="logoff">签出</button>
                                    <div class="layui-input-inline" style="width: 150px">
                                        <input type="text" id="_ccbar_callNumber_js" data-ccbartype="ccbarcallnum" autofocus="" placeholder="外呼号码" class="layui-input">
                                    </div>
                                    <button data-tag="true" onclick="CallControl.makeCall($('#_ccbar_callNumber_js').val())" class="layui-btn layui-btn-primary disabled" data-ccbartype="makecall">外呼</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-toggle="ccbarphone" data-ccbartype="makecall">打开外呼面板</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="answercall">应答</button>
                                    <!-- <button class="layui-btn layui-btn-primary" onClick="ccbarsdk.makecall('')">通过js外呼</button> -->
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="clearcall">挂机</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="agentnotready">置忙</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="agentready">置闲</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="holdcall">保持</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="unholdcall">恢复</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="consultationcall">咨询</button>
                                    <button class="layui-btn layui-btn-primary disabled" data-ccbartype="conferencecall">三方通话</button>
                                </div>
                                <div style="padding: 20px">
                                    <h5>文字标识</h5>
                                    <label>当前状态</label>
                                    <span data-agentinfo='curstatus'>-</span>
                                    <label>主叫</label>
                                    <span data-ccbaragentinfo="caller">-</span>
                                    <label>被叫</label>
                                    <span class="color-main" data-ccbaragentinfo="called">-</span>
                                    <label>计时</label>
                                    <span data-ccbar-text="clock" class="clocktext"></span>
                                    <label>呼叫模式</label>
                                    <span data-curworkmode></span>
                                </div>
                                <div>
                                    <button class="layui-btn layui-btn-primary " onclick="CallControl.workMode('inbound')">呼入模式</button>
                                    <button class="layui-btn layui-btn-primary " onclick="CallControl.workMode('outbound')">呼出模式</button>
                                </div>
                                <!-- 180124新增功能 -->
                                <div style="padding:10px;display: none">
                                    <p style="padding: 5px 0">
                                        <div class="layui-input-inline">
                                            <button class="layui-btn layui-btn-primary disabled" data-ccbartype="sstransfer">呼叫转移</button>
                                        </div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="transfer-called" placeholder="called" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="transfer-caller" placeholder="caller" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <!-- <input type="text" id="transfer-callType" placeholder="callType" autocomplete="off" class="layui-input"> -->
                                            <select name="" id="transfer-callType">
                                                <option value="1" selected>呼叫座席</option>
                                                <option value="2">呼叫IVR</option>
                                                <option value="3">呼叫外线</option>
                                            </select>
                                        </div>
                                    </p>
                                    <p style="padding: 5px 0">
                                        <div class="layui-input-inline">
                                            <button class="layui-btn layui-btn-primary disabled" data-ccbartype="consultationcall">咨询</button></div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="consultation-called" placeholder="called" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="consultation-caller" placeholder="caller" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <!-- <input type="text" id="consultation-callType" placeholder="callType" autocomplete="off" class="layui-input"> -->
                                            <select name="" id="consultation-callType">
                                                <option value="1" selected>呼叫座席</option>
                                                <option value="2">呼叫IVR</option>
                                                <option value="3">呼叫外线</option>
                                            </select>
                                        </div>
                                    </p>
                                    <p style="padding: 5px 0">
                                        <button class="layui-btn layui-btn-primary disabled" data-ccbartype="holdcall">客户话路保持</button>
                                    </p>
                                    <p style="padding: 5px 0">
                                        <button class="layui-btn layui-btn-primary disabled" data-ccbartype="unholdcall">客户话路恢复</button>
                                    </p>
                                    <p style="padding: 5px 0">
                                        <div class="layui-input-inline"><button class="layui-btn layui-btn-primary disabled" data-ccbartype="conferencecall">三方通话</button></div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="conference-called" placeholder="called" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="conference-caller" placeholder="caller" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline">
                                            <!-- <input type="text" id="conference-callType" placeholder="callType" autocomplete="off" class="layui-input"> -->
                                            <select name="" id="conference-callType">
                                                <option value="1" selected>呼叫座席</option>
                                                <option value="2">呼叫IVR</option>
                                                <option value="3">呼叫外线</option>
                                            </select>
                                        </div>
                                    </p>
                                    <p style="padding: 5px 0">
                                        <div class="layui-input-inline">
                                            <button class="layui-btn layui-btn-primary disabled" data-ccbartype="listencall">监听</button></div>
                                        <div class="layui-input-inline">
                                            <input type="text" id="listenCall-listenedAgentId" placeholder="listenedAgentId" autocomplete="off" class="layui-input">
                                        </div>
                                    </p>
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="layui-elem-field">
                            <legend>自定义事件</legend>
                            <div class="layui-field-box">
                                通过ccbarsdk.registMessageListener添加自定义事件,在ccbar触发对应事件时,会触发绑定的方法
                            </div>
                        </fieldset>
                        <!-- 180125 -->
                        <p></p>
                    </div>
                    <div>
                        <h3>最近更新</h3>
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tr>
                                <td>181008</td>
                                <td>添加监控相关接口CallControl.monitor.getSkillGroup(callback)和CallControl.monitor.getAgents(skillGroupId,callback) //仅限定于已经签入的有该功能权限的班长使用</td>
                            </tr>
                            <tr>
                                <td>180926</td>
                                <td>添加DES加密外呼接口 ccbarsdk.cryptMakeCall</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div id="sourceCode" class="layui-tab-item">
                <div style="padding:20px">
                    <blockquote class="layui-elem-quote">
                        <ul>
                            <li>
                                <p>
                                    <a href="#m1">1.初始化</a>
                                </p>
                            </li>
                            <li>
                                <p>
                                    <a href="#m2">2.不需要默认界面时的初始化</a>
                                </p>
                            </li>
                            <li>
                                <p>
                                    <a href="#m3">3.功能按钮列表</a>
                                </p>
                            </li>
                            <li>
                                <p>
                                    <a href="#m4">4.接口API列表</a>
                                </p>
                            </li>
                        </ul>
                    </blockquote>
                    <p id="m1" class="document-p">1.引入ccbar相关css和js,依赖jquery,ccbar相关js和css可以直接取本测试页面的地址,方便后续更新的同步</p>
                    <pre class="layui-code">
&ltlink rel="stylesheet" type="text/css" href="ccbar/ccbar-sdk.css">
&ltscript src="/easitline-static/js/jquery.min.js">&lt/script>
&ltscript src='/yc-ccbar/ccbar/ccbar.js'>&lt/script>
&ltscript src='/yc-ccbar/ccbar/md5.js'>&lt/script> 
&ltscript src="/yc-ccbar/ccbar/ccbar_sdk.js">&lt/script>

        </pre>
                    <p class="document-p">2.在页面放置ccbar的容器div,id为ccbar,自行实现界面时可忽略这步</p>
                    <pre class="layui-code">

&ltdiv id="ccbar">&lt/div> 
</pre>
                    <p class="document-p">3.通过提供的相关信息进行sdk初始化</p>
                    <pre class="layui-code">

var host = 'http://ip:port';//云呼平台接入地址，由云呼平台提供
var entId = '1000';//企业ID，由云呼平台提供
var loginKey = '0HA3B74BJJAJ1KF4XCWXFG867FV0SPTQ';//登录密钥，32位长度，由云呼平台提供
var productId = '002';  //企业订购的产品，由云呼平台提供

/*初始化*/
  $(function(){
  //进行ccbar初始化,必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登录密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true
 false表示不使用默认的界面,需要自己实现,实现方式参见demo和代码说明
   ccbarsdk.init(host,entId,productId,loginKey,function(){
    ccbar_plugin.init('','',type);
  });

      ccbarsdk.registMessageListener('evtAltering',handleAltering);//振铃
      ccbarsdk.registMessageListener('evtConnected',handleMessage);//来电弹屏
      ccbarsdk.registMessageListener('logon',handleLogon);//签入
      ccbarsdk.registMessageListener('logoff',handleLogoff);//签出
      ccbarsdk.registMessageListener('evtDisConnected',handleClearcall);//通话结束

      ccbarsdk.makecall(called,caller,callback); //外呼 被叫号码和外显号码,外显号码通过CallControl.callerList获取,如caller传''则外呼时自动取坐席对应的外显号码
      
      
    });
    //处理来电弹屏
    function handleAltering(result){
      console.log(JSON.stringify(result));
    }
    //处理振铃
    function handleMessage(result){
      console.log(JSON.stringify(result));
    }
    //处理签入
    function handleLogon(result){
      console.log(JSON.stringify(result));
    }
    //处理签出
    function handleLogoff(result){
      console.log(JSON.stringify(result));
    }
    //处理挂机
    function handleClearcall(result){
      console.log(JSON.stringify(result));
    }
            </pre>
                    <p id="m2" class="document-p">
                        若不需要ccbar提供界面,打算通过自定义界面来实现ccbar功能,可以参考如下方法进行配置
                    </p>
                    <p>界面元素通过data-ccbartype绑定</p>
                    <pre class="layui-code">
            <div>

&ltbutton class="layui-btn layui-btn-primary" data-ccbartype="logon" data-toggle="ccbarlogon">打开ccbar签入面板签入&lt/button>
&ltbutton class="layui-btn layui-btn-primary disabled" data-ccbartype="logoff">签出&lt/button>

&ltbutton class="layui-btn layui-btn-primary" disabled data-ccbartype="clearcall">挂机&lt/button>
&ltbutton class="layui-btn layui-btn-primary disabled" data-ccbartype="agentnotready">置忙&lt/button>
&ltbutton class="layui-btn layui-btn-primary disabled" data-ccbartype="agentready">置闲&lt/button>
          </div>
                  
                  
          </pre>
                    <p class="document-p">3-1.初始化的差异,init的时候需要添加第五个参数layout='false'</p>
                    <pre class="layui-code">
ccbarsdk.init(host,entId,productId,loginKey,function(){
                     ccbar_plugin.init('','',type);
                  });
        </pre>
                    <p class="document-p">
                        3-2.对自定义的页面元素进行修改<br>
                        目前,ccbar的功能按钮主要通过data-api进行功能的绑定和控制
                    </p>
                    <pre class="layui-code">

&ltbutton data-ccbartype="logon">签入</button>
&ltbutton data-ccbartype="logoff">签出</button>
          </pre>
                </div>
                <div id="m3" style="padding:20px">
                    <p>按钮通过设置data-ccbartype生效,签入后列表内的按钮状态受ccbar控制,不可用的将被添加disabled样式,可设置的功能按钮列表如下(与funcMask也对应)</p>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>按钮</th>
                                <th>标识</th>
                                <th>示例</th>
                                <th>追加说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>签入</td>
                                <td>logon</td>
                                <td>data-ccbartype="logon"</td>
                                <td>添加data-toggle="ccbarlogon" 可使按钮功能变成打开签入表单的面板</td>
                            </tr>
                            <tr>
                                <td>签出</td>
                                <td>logoff</td>
                                <td>data-ccbartype="logoff"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>外呼</td>
                                <td>makecall</td>
                                <td>data-ccbartype="makecall"</td>
                                <td>添加data-toggle="ccbarphone" 可使按钮功能变成打开外呼面板</td>
                            </tr>
                            <tr>
                                <td>应答</td>
                                <td>answercall</td>
                                <td>data-ccbartype="answercall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>挂机</td>
                                <td>clearcall</td>
                                <td>data-ccbartype="clearcall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>置闲</td>
                                <td>agentready</td>
                                <td>data-ccbartype="agentready"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>置忙</td>
                                <td>agentnotready</td>
                                <td>data-ccbartype="agentnotready"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>进入话后整理</td>
                                <td>workNotReady</td>
                                <td>data-ccbartype="workNotReady"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>完成话后整理</td>
                                <td>workready</td>
                                <td>data-ccbartype="workReady"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>保持</td>
                                <td>holdcall</td>
                                <td>data-ccbartype="holdcall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>恢复</td>
                                <td>unholdcall</td>
                                <td>data-ccbartype="unholdcall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>咨询</td>
                                <td>consultationcall</td>
                                <td>data-ccbartype="consultationcall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>三方通话</td>
                                <td>conferencecall</td>
                                <td>data-ccbartype="conferencecall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>监听</td>
                                <td>listencall</td>
                                <td>data-ccbartype="listencall"</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>呼叫转移</td>
                                <td>sstransfer</td>
                                <td>data-ccbartype="sstransfer"</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="padding: 20px">
                    <!--             <p class="document-p">来电弹屏event对象定义说明：</p>
<pre class="layui-code">
event.callSerialId //呼叫id，全局唯一
event.caller   //主叫号码
event.called   //被叫
event.taskId 主叫号码
event.objId 主叫号码
event.custData.custId  //客户ID
event.custData.custName //客户名称
event.custData.prodList //推荐产品列表
event.custData.tel1 //客户电话1
event.custData.tel2 //客户电话2
event.custData.tel3 //客户电话3
event.custData.tel4 //客户电话4
event.custData.f1 //保留字段1，根据客户资料模板进行定义。
event.custData.f2 //保留字段1，根据客户资料模板进行定义。
event.custData.f3 //保留字段1，根据客户资料模板进行定义。
event.custData.f4 //保留字段1，根据客户资料模板进行定义。
event.custData.f5 //保留字段1，根据客户资料模板进行定义。
event.custData.f6 //保留字段1，根据客户资料模板进行定义。
event.custData.f7 //保留字段1，根据客户资料模板进行定义。
event.custData.f8 //保留字段1，根据客户资料模板进行定义。
event.custData.f9 //保留字段1，根据客户资料模板进行定义。
event.custData.f10 //保留字段1，根据客户资料模板进行定义。
event.custData.f11 //保留字段1，根据客户资料模板进行定义。
event.custData.f12 //保留字段1，根据客户资料模板进行定义。
event.custData.f13 //保留字段1，根据客户资料模板进行定义。
event.custData.f14 //保留字段1，根据客户资料模板进行定义。
event.custData.f15 //保留字段1，根据客户资料模板进行定义。
event.custData.f16 //保留字段1，根据客户资料模板进行定义。
event.custData.f17 //保留字段1，根据客户资料模板进行定义。
event.custData.f18 //保留字段1，根据客户资料模板进行定义。
event.custData.f19 //保留字段1，根据客户资料模板进行定义。
event.custData.f20 //保留字段1，根据客户资料模板进行定义。
</pre>
<p class="document-p">通话结束</p>
<pre class="layui-code">
{
    "callSerialId": "84839025092795960524126",
    "recordFile": "1000/20180116/I101047216_A8007@hnxt_013380278728_90002_5a5dcffa_3bee",
    "beginTime": "2018-01-16 18:12:10",
    "endTime": "2018-01-16 18:12:20",
    "createCause": "8"
}
</pre>
<table class="layui-table">
<thead>
  <tr>
    <th>参数</th>
    <th>说明</th>
  </tr>
</thead>
<tbody>
  <tr>
    <td>callSerialId</td>
    <td>通话id</td>
  </tr>
  <tr>
    <td>recordFile</td>
    <td>录音文件</td>
  </tr>
  <tr>
    <td>beginTime</td>
    <td>开始时间</td>
  </tr>
  <tr>
    <td>endTime</td>
    <td>结束时间</td>
  </tr>
  <tr>
    <td>createCause</td>
    <td>通话类型,8为自动外呼,6为手动外呼</td>
  </tr>
</tbody>
          </table>
           -->
                    <p class="document-p">错误代码列表</p>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>响应消息代码编号</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>000</td>
                                <td>操作成功</td>
                            </tr>
                            <tr>
                                <td>001</td>
                                <td>包文格式错误</td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>无效的操作请求</td>
                            </tr>
                            <tr>
                                <td>100</td>
                                <td>无效的技能组</td>
                            </tr>
                            <tr>
                                <td>101</td>
                                <td>无效的坐席工号</td>
                            </tr>
                            <tr>
                                <td>102</td>
                                <td>无效的坐席密码</td>
                            </tr>
                            <tr>
                                <td>103</td>
                                <td>无效的坐席状态</td>
                            </tr>
                            <tr>
                                <td>104</td>
                                <td>无效的呼叫状态</td>
                            </tr>
                            <tr>
                                <td>105</td>
                                <td>坐席工号已登录</td>
                            </tr>
                            <tr>
                                <td>106</td>
                                <td>话机已被使用</td>
                            </tr>
                            <tr>
                                <td>107</td>
                                <td>外呼主显号码为空</td>
                            </tr>
                            <tr>
                                <td>108</td>
                                <td>无效的话机号码</td>
                            </tr>
                            <tr>
                                <td>109</td>
                                <td>未配置席间转移字冠</td>
                            </tr>
                            <tr>
                                <td>110</td>
                                <td>咨询操作失败</td>
                            </tr>
                            <tr>
                                <td>111</td>
                                <td>三方会议失败</td>
                            </tr>
                            <tr>
                                <td>112</td>
                                <td>转移操作失败</td>
                            </tr>
                            <tr>
                                <td>113</td>
                                <td>话机状态异常</td>
                            </tr>
                            <tr>
                                <td>114</td>
                                <td>外呼操作失败</td>
                            </tr>
                            <tr>
                                <td>999</td>
                                <td>未定义错误</td>
                            </tr>
                        </tbody>
                    </table>
                    <p class="document-p">创建呼叫原因--可通过编码从ccbar_config['createCause'][createCauseCode]取得对应编码的翻译</p>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>响应消息代码编号</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>客户来电(外线呼入)</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>客户来电(IVR转入)</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>席间转移呼入</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>IVR咨询呼入</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>席间咨询呼入</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>呼出</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>预拨号呼出</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>呼叫前转呼入</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>转移呼入</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>席间呼入</td>
                            </tr>
                        </tbody>
                    </table>
                    <p id="m4" class="document-p">4.接口API</p>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>方法</th>
                                <th>参数</th>
                                <th>需要签入</th>
                                <th style="width:150px">说明</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ccbarsdk.init(host,entId,productId,loginKey,callback)</td>
                                <td>
                                    <p>host(String)(必填)云呼平台接入地址</p>
                                    <p>entId(String)(必填)企业ID</p>
                                    <p>productId(String)(必填)登录密钥</p>
                                    <p>loginKey(String)(必填)订购产品</p>
                                    <p>type(String)(可选)是否需要ccbar默认界面,默认为true,不需要时填'false'</p>
                                </td>
                                必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登录密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true
                                <td>否</td>
                                <td>sdk初始化方法</td>
                                <td>ccbarsdk.init(host,entId,productId,loginKey,function(){
                                    ccbar_plugin.init('','',type);
                                    });
                                </td>
                            </tr>
                            <tr>
                                <td>ccbarsdk.makecall(called,caller)</td>
                                <td>
                                    <p>called(String)(必填)外呼号码</p>
                                    <p>caller(String)(必填)外显号码,通过CallControl.callerList获取或填''自动取技能组配置的号码</p>
                                </td>
                                <td>是</td>
                                <td>外呼</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>ccbarsdk.cryptMakeCall(called,caller)</td>
                                <td>
                                    <p>called(String)(必填)通过DES加密过外呼号码</p>
                                    <p>caller(String)(必填)外显号码,通过CallControl.callerList获取或填''自动取技能组配置的号码</p>
                                </td>
                                <td>是</td>
                                <td>外呼</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>ccbarsdk.logon(agentId,token,phone)</td>
                                <td>
                                    <p>1.agentId(String)(必填)坐席工号</p>
                                    <p>2.token(String)(必填)坐席密码,由原密码进行MD5加密后得出 </p>
                                    <p>3.phone(String)(必填)话机号码</p>
                                </td>
                                <td>否</td>
                                <td>签入</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>CallControl.wakeupSIP()</td>
                                <td>
                                </td>
                                <td>是</td>
                                <td>拉起sip话机</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>CallControl.closeSIP()</td>
                                <td>
                                </td>
                                <td>否</td>
                                <td>退出sip话机</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>CallControl.downloadSIP()</td>
                                <td>
                                </td>
                                <td>否</td>
                                <td>下载sip话机</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>ccbarsdk.registMessageListener(listener,callback)</td>
                                <td>
                                    <p>1.listener(String)(必填)监听方法名,可设置'logon','logoff','evtAltering','evtDisConnected','evtConnected',对应签入,签出,振铃,结束通话,通话等,其中振铃.通话和结束通话会传入呼叫相关信息,相信见下表 <a href="#callbackData">事件监听回调对象</a></p>
                                    <p>2.callback(Function)(必填)回调方法,可传入方法名或function对象</p>
                                </td>
                                <td>否</td>
                                <td>注册监听事件</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>CallControl.getStatus()</td>
                                <td>
                                </td>
                                <td>否</td>
                                <td>获取坐席当前状态,返回话机状态为对应状态值,如BUSY,未签入过调用为返回null</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>CallControl.getFunc(type)</td>
                                <td>
                                    <p>1.type:{[string/array]} [功能按钮类型,可出入单个或用数组传入多个,如type=makecall,或type = ['makecall','logout'],或空}</p>
                                </td>
                                <td>否</td>
                                <td>坐席功能检测</td>
                                <td>传入单个类型,返回单个类型对应的值/传入数组时,结果为数组/当不传入时,返回整个funcmask对象,未签入时返回null</td>
                            </tr>
                            <tr>
                                <td>CallControl.agentList(skillGroupId,callback)</td>
                                <td>
                                    <p>/**
                                        * [agentList 获取当前空闲坐席列表]
                                        * 技能组ID传空值时,返回所在坐席技能组的空闲坐席列表,和技能组列表
                                        * 传对应技能组ID时获取该技能组的空闲坐席列表
                                        * @param {String} skillGroupId [技能组ID]
                                        * @param {Function} callback [回调函数]
                                        */</p>
                                </td>
                                <td>是</td>
                                <td>获取技能组和坐席列表</td>
                                <td>
                                    <textarea rows="3" style="width: 100%">
{
    "msg": "操作成功!",
    "data":
    {
        "result":
        {
            "groups": [
            {
                "SKILL_GROUP_ID": "1",
                "SKILL_GROUP_NAME": "班组1"
            }
            ],
            "agents": [
           
            {
                "AGENT_ID": "8012@1000",
                "AGENT_NAME": "8012"
            },
            {
                "AGENT_ID": "8008@1000",
                "AGENT_NAME": "8008"
            }]
        },
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.callerList(callback)</td>
                                <td>
                                    <p>callback[function]发送请求后的回调函数,</p>
                                </td>
                                <td>是</td>
                                <td>获取外显号码列表,外呼时必须有对应的外显号码才允许外呼</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        "result":
        {
            "callers": [ "05975250000", "05985168246", "05996980296"]
        },
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.workMode(workMode,callback)</td>
                                <td>
                                    <p>workMode{String} [inbound呼入 outbound呼出 ]</p>
                                    <p>callback[function]发送请求后的回调函数,</p>
                                </td>
                                <td>是</td>
                                <td>切换呼叫模式</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.autoAnswer(autoFlag,callback)</td>
                                <td>
                                    <p>autoFlag{String} [true 自动应答 false 非自动应答 ]</p>
                                    <p>callback[function]发送请求后的回调函数,</p>
                                </td>
                                <td>是</td>
                                <td>切换自动应答模式</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.workReady(callback)</td>
                                <td>
                                </td>
                                <td>是</td>
                                <td>完成话后整理</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.workNotReady(callback)</td>
                                <td>
                                </td>
                                <td>是</td>
                                <td>在通话中调用,在结束会话的时候进入话后整理</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.holdCall(callback)</td>
                                <td>
                                </td>
                                <td>是</td>
                                <td>保持</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                            <tr>
                                <td>CallControl.unholdCall(callback)</td>
                                <td>
                                </td>
                                <td>是</td>
                                <td>恢复</td>
                                <td>
                                    <p>返回对象</p>
                                    <pre>
{
    "msg": "操作成功!",
    "data":
    {
        
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <fieldset id="callbackData" class="layui-elem-field">
                        <legend>事件监听回调对象</legend>
                        <div class="layui-field-box">
                            <p>
                                对应ccbarsdk.registMessageListener
                            </p>
                            <h4>呼叫事件-振铃-evtAlerting,主要取返回的对象里面的event进行解析</h4>
                            <pre class="layui-code">
"event": {
  "eventId": "altering",//事件
  "agentId": "8012@fjyq",//坐席工号
  "custTempId": null,//客户模板
  "called": "13812341234",//被叫号码
  "callSerialId": "84638703767393401057845",//呼叫序列ID
  "busiId": null,//业务ID
  "entId": "1001",//企业ID
  "createCause": "6",//创建呼叫原因
  "custPhone": "442013812341234",//客户号码
  "areaInfo": null,//区域信息
  "skillId": "7",//技能组ID
  "caller": "05923352191",//主叫号码
  "station": "11559110018012",//分机号
  "eventName": "振铃",//事件名称
  "skillGroupName": "云客服全勤组",//技能组名称
  "busiOrderId": "84746867964469417471091" //业务订购ID
},                  
                </pre>
                            <h4>呼叫事件-通话-evtConnected,主要取返回的对象里面的event进行解析</h4>
                            <pre class="layui-code">
"event": {
  "eventId": "connected", //事件
  "agentId": "8012@fjyq",//坐席工号
  "custTempId": null,//客户模板
  "called": "13812341234",//被叫号码
  "callSerialId": "84638703767393401057845",//呼叫序列ID
  "busiId": null,//业务ID
  "entId": "1001",//企业ID
  "createCause": "6",//创建呼叫原因
  "custPhone": "442013812341234",//客户号码
  "skillId": "7",//技能组ID
  "caller": "05923352191",//主叫
  "station": "11559110018012",//分机号
  "eventName": "接通",//时间名称
  "busiOrderId": "84746867964469417471091" //业务订购ID
},
                </pre>
                            <h4>呼叫事件-呼叫结束-evtDisConnected,主要取返回的对象里面的event进行解析</h4>
                            <pre class="layui-code">

"event": {
  "eventId": "disconnect", //事件
  "agentId": "8012@fjyq", //坐席工号
  "custTempId": null, //客户模板
  "called": "13812341234", //被叫号码
  "callSerialId": "84638703767393401057845", //呼叫序列ID
  "busiId": null, //业务ID
  "entId": "1001", //企业ID
  "createCause": "6", //创建呼叫原因 
  "custPhone": "442013812341234", //客户号码
  "recordId": "5b8f7a9f_160019b5_01fa124c_160A0080",
  "skillId": "7", //技能组ID
  "caller": "05923352191", //主叫
  "createTime": "2018-09-05 14:41:34", //创建时间
  "station": "11559110018012", //分机号
  "eventName": "挂断", //时间名称
  "recordFileName": "1001/20180905/I770016862_A8012@fjyq_05923352191_013812341234_144203_341f", //录音文件名称
  "busiOrderId": "84746867964469417471091", //业务订购ID
  "beginTime": "2018-09-05 14:42:03", //开始通话时间
  "endTime": "2018-09-05 14:42:10" //结束通话时间
}

                </pre>
                        </div>
                    </fieldset>
                </div>
            </div>
            <div class="layui-tab-item">
                <div class="layui-main">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>问题</th>
                                <th>说明</th>
                                <th>解决办法</th>
                                <th>相关接口</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>外呼时话机状态异常</td>
                                <td>话机状态不在线或软话机没有拉起</td>
                                <td>拉起软话机</td>
                                <td> </td>
                            </tr>
                            <tr>
                                <td>话机在线但是接不到呼入的电话</td>
                                <td>话务条不在呼入模式</td>
                                <td>切换到呼入模式</td>
                                <td>CallControl.workMode</td>
                            </tr>
                            <tr>
                                <td>自动应答</td>
                                <td>自动/非自动应答</td>
                                <td>通过接口切换</td>
                                <td>CallControl.autoAnswer</td>
                            </tr>
                        </tbody>
                    </table>
                    <p>常用功能的js实现方式</p>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>功能</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>通过js实现签入</td>
                                <td>
ccbarsdk.logon(agentId,token,phone,function(result){console.log(result)});//坐席工号,MD5加密的密码,话机号码
</td>
                            </tr>
                            <tr>
                                <td>通过js实现外呼</td>
                                <td>ccbarsdk.makecall(called,caller,callback); //被叫号码,外显号码,通过CallControl.callerList获取或填""自动取技能组配置的号码 </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- 代码片段 -->
            <div class="layui-tab-item" style="padding: 20px;">
                <blockquote class="layui-elem-quote">
                    ccbar主要通过jsonp进行异步数据的请求交互,通过界面绑定、js事件绑定提供事件的发送和处理,进一步实现对应的呼叫业务
                    <ol>
                        <li></li>
                    </ol>
                </blockquote>
                <!-- code demo -->
                <div class="layui-collapse" lay-filter="component-panel">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">1. 初始化</h2>
                        <div class="layui-colla-content">
                            1.1 引入ccbar资源
                            <pre class="layui-code">
&ltlink rel="stylesheet" type="text/css" href="/yc-ccbar/ccbar/ccbar-sdk.css">
&ltscript src="/easitline-static/js/jquery.min.js">&lt/script>
&ltscript src='/yc-ccbar/ccbar/ccbar.js'>&lt/script>
&ltscript src='/yc-ccbar/ccbar/md5.js'>&lt/script>
&ltscript src="/yc-ccbar/ccbar/ccbar_sdk.js">&lt/script>                   
                  </pre>
                            1.2 页面放置ccbar容器
                            <pre class="layui-code">

&ltdiv id="ccbar">&lt/div>                   
                  </pre>
                            1.3 执行初始化JS
                            <pre class="layui-code">

$(function(){
  var host = 'http://ip:port';//云呼平台接入地址，由云呼平台提供
  var entId = '1000';//企业ID，由云呼平台提供
  var loginKey = '0HA3B74BJJAJ1KF4XCWXFG867FV0SPTQ';//登录密钥，32位长度，由云呼平台提供
  var productId = '002';  //企业订购的产品，由云呼平台提供
  var type = '';//为空或者true时为默认基础功能界面,advanced为带更多功能的界面,false时只进行初始化,界面自行开发

  //进行ccbar初始化,必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登录密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true,false表示不使用默认的界面,需要自己实现,实现方式参见demo和代码说明
   ccbarsdk.init(host,entId,productId,loginKey,function(){
    ccbar_plugin.init('','',type);
  });
});                  
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">2. 签入</h2>
                        <div class="layui-colla-content">
                            2.1 自定义按钮打开签入面板签入
                            <pre class="layui-code">

&ltbutton  data-ccbartype="logon" data-toggle="ccbarlogon">打开ccbar签入面板签入&lt/button>                  
                  </pre>
                            2.2 JS签入
                            <pre class="layui-code">

ccbarsdk.logon(agentId,token,phone,function(result){console.log(result)});//坐席工号,MD5加密的密码,话机号码
                  </pre>
                            <pre class="layui-code">
返回对象
{
  "msg": "操作成功!",
  "data": {
    "result": {
      "phoneType": "3", //话机类型,3为sip软话机
      "inbound": "on", //具备呼入能力
      "outbound": "on",//具备呼出能力
      "sbcAddr": "yunqumicrosip://E7B8A852A9DA8A76532B81655868CDB66BFC4E10F686A36F3ABF7EDED9E23FBEA43C7309F885A89DB5BB5788E873DF34270E16261714D28E2EC5D272346B43CCE6DD070A91A3535161EF462B53C6941BF543B059DA2507083854AA9CC2105B4C" //sip话机的拉起地址
    },
    "code": "succ",
    "content": "succ"
  },
  "state": 1
}
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">3. 事件绑定</h2>
                        <div class="layui-colla-content">
                            通过ccbarsdk.registMessageListener来进行事件绑定,同一个事件可以绑定多个处理方法,按绑定的顺序进行触发
                            <div class="layui-collapse" lay-filter="component-panel">
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.1 添加振铃事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('evtAltering',handleAltering);
function handleAltering(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
"event": {
  "eventId": "altering",//事件
  "agentId": "8012@fjyq",//坐席工号
  "custTempId": null,//客户模板
  "called": "13812341234",//被叫号码
  "callSerialId": "84638703767393401057845",//呼叫序列ID
  "busiId": null,//业务ID
  "entId": "1001",//企业ID
  "createCause": "6",//创建呼叫原因
  "custPhone": "442013812341234",//客户号码
  "areaInfo": null,//区域信息
  "skillId": "7",//技能组ID
  "caller": "05923352191",//主叫号码
  "station": "11559110018012",//分机号
  "eventName": "振铃",//事件名称
  "skillGroupName": "云客服全勤组",//技能组名称
  "busiOrderId": "84746867964469417471091" //业务订购ID
},                  
                          </pre>
                                    </div>
                                </div>
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.2 添加接通事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('evtConnected',handleConnected);
function handleConnected(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
"event": {
  "eventId": "connected", //事件
  "agentId": "8012@fjyq",//坐席工号
  "custTempId": null,//客户模板
  "called": "13812341234",//被叫号码
  "callSerialId": "84638703767393401057845",//呼叫序列ID
  "busiId": null,//业务ID
  "entId": "1001",//企业ID
  "createCause": "6",//创建呼叫原因
  "custPhone": "442013812341234",//客户号码
  "skillId": "7",//技能组ID
  "caller": "05923352191",//主叫
  "station": "11559110018012",//分机号
  "eventName": "接通",//时间名称
  "busiOrderId": "84746867964469417471091" //业务订购ID
},                  
                          </pre>
                                    </div>
                                </div>
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.3 添加挂断事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('evtDisConnected',handleDisConnected);
function handleDisConnected(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
"event": {
  "eventId": "disconnect", //事件
  "agentId": "8012@fjyq", //坐席工号
  "custTempId": null, //客户模板
  "called": "13812341234", //被叫号码
  "callSerialId": "84638703767393401057845", //呼叫序列ID
  "busiId": null, //业务ID
  "entId": "1001", //企业ID
  "createCause": "6", //创建呼叫原因 
  "custPhone": "442013812341234", //客户号码
  "recordId": "5b8f7a9f_160019b5_01fa124c_160A0080",
  "skillId": "7", //技能组ID
  "caller": "05923352191", //主叫
  "createTime": "2018-09-05 14:41:34", //创建时间
  "station": "11559110018012", //分机号
  "eventName": "挂断", //时间名称
  "recordFileName": "1001/20180905/I770016862_A8012@fjyq_05923352191_013812341234_144203_341f", //录音文件名称
  "busiOrderId": "84746867964469417471091", //业务订购ID
  "beginTime": "2018-09-05 14:42:03", //开始通话时间
  "endTime": "2018-09-05 14:42:10" //结束通话时间
}               
                          </pre>
                                    </div>
                                </div>
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.4 添加签入事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('logon',handleLogon);
function handleLogon(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
{
  "result": "Succ",
  "readyTimeout": "12000",
  "srcMessageId": "respLogin",
  "resultCode": "000",
  "resultDesc": "成功"
}              
                          </pre>
                                    </div>
                                </div>
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.5 添加签出事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('logoff',handleLogoff);
function handleLogoff(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
{
  "result": "Succ",
  "readyTimeout": "12000",
  "srcMessageId": "respLogout",
  "resultCode": "000",
  "resultDesc": "成功"
}              
                          </pre>
                                    </div>
                                </div>
                                <div class="layui-colla-item">
                                    <h2 class="layui-colla-title">3.5 添加坐席状态同步事件</h2>
                                    <div class="layui-colla-content">
                                        <pre class="layui-code">

ccbarsdk.registMessageListener('agentStateSync',handleAgentStateSync);
function handleAgentStateSync(result){console.log(JSON.stringify(result));}
                          </pre>
                                        返回对象
                                        <pre class="layui-code">
{
  "workMode": "inbound", //当前工作模式
  "bizSessionId": "F536496F6310EB28DF37E00D3D28AF0A",
  "stateDesc": "闲",//坐席状态名称
  "notifyContent": "",
  "autoAnswer": "true",//自动应答状态
  "state": "IDLE",//坐席状态
  "resultDesc": null,
  "funcMask": { //功能按钮状态
    "logon": false,
    "deleteConf": false,
    "transfercall": false,
    "stopInvent": false,
    "agentnotready": true,
    "workReady": false,
    "createConf": false,
    "consultationcall": false,
    "holdcall": false,
    "conferencecall": false,
    "quitConf": false,
    "sstransfer": false,
    "workNotReady": false,
    "interventcall": false,
    "clearconnection": false,
    "stopMonitor": false,
    "answercall": false,
    "clearcall": false,
    "interceptCall": false,
    "logoff": true,
    "startMonitor": true,
    "agentready": false,
    "unholdcall": false,
    "listencall": true,
    "makecall": true,
    "singleTransferCall": false,
    "startInvent": true
  }
}              
                          </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">4. 外呼</h2>
                        <div class="layui-colla-content">
                            4.1 普通js外呼
                            <pre class="layui-code">

ccbarsdk.makecall(called,caller,callback); //被叫号码,外显号码,通过CallControl.callerList获取或填""自动取技能组配置的号码 
                  </pre>
                            4.2 DES加密外呼
                            <pre class="layui-code">

ccbarsdk.cryptMakeCall(called,caller,callback); //DES加密被叫号码,外显号码,通过CallControl.callerList获取或填""自动取技能组配置的号码 
                  </pre>
                            <pre class="layui-code">
发起外呼成功
{"msg":"操作成功!","data":{"code":"succ","content":"succ"},"state":1}
                  </pre>
                            DES加密算法(JAVA)
                            <pre class="layui-code">
public class DesUtil {
  
  private static final String Algorithm = "DES"; //定义 加密算法,可用 DES,DESede,Blowfish
  private static byte[] iv = new byte[]{ 0x12, 0x34, 0x56, 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF };
  // DES加密
    public static String EncryptDES(String dataString) {
      try {
        //生成密钥           
        SecretKey deskey = new SecretKeySpec(getKey(), Algorithm);
        IvParameterSpec ivspec = new IvParameterSpec(iv);
        //加密   
        Cipher c1 = Cipher.getInstance("DES/CBC/PKCS5Padding");     
        c1.init(Cipher.ENCRYPT_MODE, deskey,ivspec);     
        return BASE64Util.encode(c1.doFinal(dataString.getBytes()));
    } catch (Exception ex) {
      //ICCSLogger.getLogger().info(ex,ex);
    }
      return "";
      
    }
    
    public static byte[] getKey(){
      return "12345678".getBytes();
    }
  // DES解密
    public static String DecryptDES(String decryptString){
      try {
        byte[] data = BASE64Util.decode(decryptString);
        IvParameterSpec ivspec = new IvParameterSpec(iv);
        //生成密钥   
      SecretKey deskey = new SecretKeySpec(getKey(), Algorithm);   
      //解密     
      Cipher c1 = Cipher.getInstance("DES/CBC/PKCS5Padding");  
      c1.init(Cipher.DECRYPT_MODE, deskey,ivspec);     
      return new String(c1.doFinal(data));
      } catch (Exception ex) {
      //ICCSLogger.getLogger().info(ex,ex);
    }
      return "";
    }
    
    public static void main(String[] args) {
      //明文:helloworld  密文：wR/xa+1PN1E8vkWeDr//Dw==
    //  System.out.println(new String(new byte[]{0x78)));
      //System.out.println(new String(Keys));
      String crpytData = EncryptDES("helloworld");
      System.out.println(crpytData);
      System.out.println(DecryptDES(crpytData));
  }

}

                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">5. 转移</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">
/**
 * [sstransfer 呼叫转移]
 * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
 * @param  {String}   displayNumber [被转移对象的主显号码]
 * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
 * @param  {Object}   userData      [业务私有数据]
 * @param  {Function} callback      [回调函数]
 */                  
CallControl.sstransfer(called, displayNumber, skillId, callType, userData, callback)</pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">6. 咨询</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">
/**
 * [consultation 咨询]
 * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
 * @param  {String}   displayNumber [被转移对象的主显号码]
 * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
 * @param  {Object}   userData      [业务私有数据]
 * @param  {Function} callback      [回调函数]
 */                
CallControl.consultation(called, displayNumber, skillId, callType, userData, callback)</pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">7. 三方</h2>
                        <div class="layui-colla-content">
                            在咨询状态调用
                            <pre class="layui-code">
/**
 * [conference 三方]
 * @param  {Object}   userData      [业务私有数据]
 * @param  {Function} callback      [回调函数]
 */
CallControl.conference(userData, callback);
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">8. 切换自动应答</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">
CallControl.autoAnswer(true,function(result){console.log(result)})

返回对象

{
    "msg": "操作成功!",
    "data":
    {
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}                    
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">9. 切换工作模式</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">

CallControl.workMode('inbound',function(result){console.log(result)}) //inbound呼入,outbound呼出

返回对象
{
    "msg": "操作成功!",
    "data":
    {
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}                 
  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">10. 获取外显号码</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">
CallControl.callerList(function(result){console.log(result)})

返回对象
{
    "msg": "操作成功!",
    "data":
    {
        "result":
        {
            "callers": [ "05975250000", "05985168246", "05996980296"]
        },
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}             
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">11. 获取技能组在线坐席信息</h2>
                        <div class="layui-colla-content">
                            技能组skillGroupId为空时为获取当前坐席所在技能组
                            <pre class="layui-code">
CallControl.agentList(skillGroupId,function(result){console.log(result)})

返回对象
{
    "msg": "操作成功!",
    "data":
    {
        "result":
        {
            "groups": [
            {
                "SKILL_GROUP_ID": "1",
                "SKILL_GROUP_NAME": "班组1"
            }
            ],
            "agents": [
           
            {
                "AGENT_ID": "8012@1000",
                "AGENT_NAME": "8012"
            },
            {
                "AGENT_ID": "8008@1000",
                "AGENT_NAME": "8008"
            }]
        },
        "code": "succ",
        "content": "succ"
    },
    "state": 1
}
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">12. 获取坐席状态和获取功能按钮状态</h2>
                        <div class="layui-colla-content">
                            获取坐席状态,可通过对象 ccbar_config.agentStatus 获取对应翻译
                            <pre class="layui-code">
CallControl.getState();  return null (未签入时) IDLE (闲)
                  </pre>
                            获取功能按钮状态
                            <pre class="layui-code">
CallControl.getFunc() //返回整个funcmask对象,包含所有功能按钮的状态
CallControl.getFunc('makecall') //返回外呼的状态
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">13. 获取监控数据</h2>
                        <div class="layui-colla-content">
                            获取技能组列表
                            <pre class="layui-code">

CallControl.monitor.getSkillGroup(callback)
                  </pre>
                            <pre class="layui-code">
{
  "msg": "操作成功!",
  "data": {
    "result": {
      "groups": {
        "71": {
          "skillGroupId": "71",
          "skillGroupName": "云趣云客服-家用空调22",
          "workReadyFalg": true,
          "workReadyTime": 0
        },
        "42": {
          "skillGroupId": "42",
          "skillGroupName": "云趣云客服",
          "workReadyFalg": true,
          "workReadyTime": 0
        }
      }
    },
    "code": "succ",
    "content": "succ"
  },
  "state": 1
} 
                  </pre>
                            技能组状态
                            <pre class="layui-code">

CallControl.monitor.getAgents(skillGroupId,callback)
                  </pre>
                            <pre class="layui-code">
{
  "msg": "操作成功!",
  "data": {
    "result": {
      "agents": [{
        "agentId": "8007@1000",
        "agentName": "周莉",
        "agentState": "置闲",
        "stateTime": "-35",
        "workModel": "呼出"
      }],
      "group": {
        "talkAgentCount": "0",//通话坐席数
        "workNotReadyAgentCount": "0",//话后整理坐席数
        "entId": "1000",//企业id
        "idleAgentCount": "1",//空闲坐席数
        "aveQueueLen": "0",//平均排队时长
        "queueCallCount": "0",//呼叫排队
        "logonAgentCount": "1",//签入坐席数
        "pdsboundIdleAgentCount": "0",//智能外呼空闲坐席数
        "updateTime": "2018-09-29 14:45:40",//数据更新时间
        "alertAgentCount": "0",//振铃数
        "maxQueueLen": "0",//最大排队时长
        "outboundIdleAgentCount": "1",//呼出空闲坐席数
        "abandonAgentCount": "0",//呼叫中用户挂机数
        "busyAgentCount": "0",//示忙数
        "abandonQueueCount": "0",//排队中用户挂机数
        "skillGroupId": "42",//技能组id
        "inboundIdleAgentCount": "0"//呼入空闲坐席数
      }
    },
    "code": "succ",
    "content": "succ"
  },
  "state": 1
}
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">14. 咨询ivr(如按键确认)</h2>
                        <div class="layui-colla-content">
                          1.在通话状态中发起咨询ivr命令<br>
                          一般用于在通话中,需要客户进行按键确认或输入信息核对身份的业务场景,
                          如在电销场景进行业务二次订购确认/ 银行场景核对身份 /

       <p>可通过以下方法判断坐席状态</p>
       <pre class="layui-code">
        CallControl.getState() == 'TALK' //坐席在通话状态</pre>
       <pre class="layui-code">
        /**
         * [consultIVR description]
         * @param  {[type]}   called   [流程号,即对应ivr字冠,电销任务直接填 '' ]
         * @param  {[type]}   source   [来源,电销任务填写 task ]
         * @param  {[type]}   userData [业务私有参数]
         * @param  {Function} callback [回调方法]
         */
        CallControl.consultIVR(called, source, userData, callback)
                            
                          </pre class="layui-code">
                          2.在发起咨询ivr命令后,坐席进入挂起或放音乐状态,通过监听咨询结束事件,通过返回的自定义字段(具体字段跟ivr侧确认)获取用户输入结果,可以通过修改CallControl.onRespIVR定义的方法来处理,以云电销中二次确认订购作为参考,userSelectKey和ivrSaleResult为ivr返回的自定义字段
                          <pre class="layui-code">
CallControl.onRespIVR = function(result){
  if(result&&result.userData||result.event){
    var data = result.userData || result.event;
    var selectKey=data.userSelectKey||data.userSelectKey||''; //用户按键值
    var ivrSaleResult=data.ivrSaleResult||data.ivrSaleResult; //订购结果

    $("#returnMsg").html("按键值为："+selectKey+" ； 结果："+resultMsg);

  };
}
                          </pre>
                          接口返回数据
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">15. 转移ivr(如转满意度)</h2>
                        <div class="layui-colla-content">
                          转满意度可以在通话过程中,调用转移接口来实现
                          <p>可通过以下方法判断坐席状态</p>
       <pre class="layui-code">
        CallControl.getFunc('sstransfer') == true //单步转可用
        CallControl.getState() == 'TALK' //坐席在通话状态</pre>
                          <pre class="layui-code">
         /**
         * [transfer 呼叫转移]
         * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
         * @param  {String}   displayNumber [被转移对象的主显号码,当呼叫ivr时不需要]
         * @param  {String}  skillId [被转移坐席的技能组id,非必填]
         * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
         * @param  {Object}   userData      [业务私有数据]
         * @param  {Function} callback      [回调函数]
         */
        CallControl.sstransfer(called, displayNumber, skillId, callType, userData, callback)

        进行转满意度时,一般需要在userData传送该通通话的相关信息,以视源的转满意度为例
        CallControl.sstransfer(ivr字冠,'', '', '2', {
          busiOrderId:latestCall.event.busiOrderId, //业务订购id
          callSerialId:latestCall.event.callSerialId,//呼叫流水号
          entId:latestCall.event.entId,//企业id
          command:"respSatisf"
        })
                        </pre>
                        </div>
                    </div>

                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">16. 限制示忙</h2>
                        <div class="layui-colla-content">
                          通过修改CallControl.beforeSetNotReady方法,坐席调用示忙前,先调用方法进行判断返回true的时候示忙,返回false时阻止坐席示忙
                        <pre class="layui-code">
CallControl.beforeSetNotReady = function(){console.log('限制'); return false}
                        </pre>
                        </div>
                    </div>

                </div>
                <h3>自定义界面开发</h3>
                <div class="layui-collapse" lay-filter="component-panel">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">不做任何修改的基础功能demo代码</h2>
                        <div class="layui-colla-content">
                            <pre class="layui-code">

&lthtml>
&lthead>
  &lttitle>ccbar&lt/title>
  &ltmeta charset="utf-8"> 
  &ltmeta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"> 
  &ltmeta name="apple-mobile-web-app-capable" content="yes"> 
  &ltmeta name="apple-mobile-web-app-status-bar-style" content="black"> 
  &ltmeta name="format-detection" content="telephone=no">
  &ltlink rel="stylesheet" type="text/css" href="ccbar/ccbar-sdk.css">
  &ltscript src="/easitline-static/js/jquery.min.js">&lt/script>
  &ltscript src='/yc-ccbar/ccbar/ccbar.js'>&lt/script>
  &ltscript src='/yc-ccbar/ccbar/md5.js'>&lt/script>
  &ltscript src="/yc-ccbar/ccbar/ccbar_sdk.js">&lt/script>
&lt/head>
&ltbody>
  &ltdiv id="ccbar">&lt/div>
  &ltscript>
    $(function(){
      var host = 'http://ip:port';//云呼平台接入地址，由云呼平台提供
      var entId = '1000';//企业ID，由云呼平台提供
      var loginKey = '0HA3B74BJJAJ1KF4XCWXFG867FV0SPTQ';//登录密钥，32位长度，由云呼平台提供
      var productId = '002';  //企业订购的产品，由云呼平台提供
      var type = '';//为空或者true时为默认基础功能界面,advanced为带更多功能的界面,false时只进行初始化,界面自行开发
      //进行ccbar初始化,必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登录密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true,false表示不使用默认的界面,需要自己实现,实现方式参见demo和代码说明
       ccbarsdk.init(host,entId,productId,loginKey,function(){
        ccbar_plugin.init('','',type);
        });
    });  
  &lt/script>
&lt/body>
&lt/html>
                  </pre>
                        </div>
                    </div>
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">简单的自定义界面demo代码</h2>
                        <div class="layui-colla-content">
                            <div>
                                <button data-ccbartype="logon" data-toggle="ccbarlogon">打开ccbar签入面板签入</button>
                                <button data-ccbartype="logoff">签出</button>
                                <button data-toggle="ccbarphone" data-ccbartype="makecall">打开外呼面板</button>
                                <h5>文字标识</h5>
                                <label>当前状态</label>
                                <span data-agentinfo="curstatus">-</span>
                                <label>主叫</label>
                                <span data-ccbaragentinfo="caller">-</span>
                                <label>被叫</label>
                                <span data-ccbaragentinfo="called">-</span>
                                <label>当前状态计时</label>
                                <span data-ccbar-text="clock" class="clocktext"></span>
                                <label>当前呼叫模式</label>
                                <span data-curworkmode=""></span>
                            </div>
                            1.初始化
                            <pre class="layui-code">
var host = 'http://ip:port';//云呼平台接入地址，由云呼平台提供
var entId = '1000';//企业ID，由云呼平台提供
var loginKey = '0HA3B74BJJAJ1KF4XCWXFG867FV0SPTQ';//登录密钥，32位长度，由云呼平台提供
var productId = '002';  //企业订购的产品，由云呼平台提供
var type = 'false';//为空或者true时为默认基础功能界面,advanced为带更多功能的界面,false时只进行初始化,界面自行开发

//进行ccbar初始化,必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登录密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true,false表示不使用默认的界面,需要自己实现,实现方式参见demo和代码说明
 ccbarsdk.init(host,entId,productId,loginKey,function(){
  ccbar_plugin.init('','',type);
});
                  </pre>
                            2.编写界面
                            <pre class="layui-code">
&ltdiv>
  &ltbutton data-ccbartype="logon" data-toggle="ccbarlogon">打开ccbar签入面板签入&lt/button>
  &ltbutton data-ccbartype="logoff">签出&lt/button>
  &ltbutton data-toggle="ccbarphone" data-ccbartype="makecall">打开外呼面板&lt/button>
  &lth5>文字标识&lt/h5>
  &ltlabel>当前状态&lt/label>
  &ltspan data-agentinfo="curstatus">-&lt/span>

  &ltlabel>主叫&lt/label>
  &ltspan data-ccbaragentinfo="caller">-&lt/span>
  &ltlabel>被叫&lt/label>
  &ltspan data-ccbaragentinfo="called">-&lt/span>
  &ltlabel>当前状态计时&lt/label>
  &ltspan data-ccbar-text="clock" class="clocktext">&lt/span>

  &ltlabel>当前呼叫模式&lt/label>
  &ltspan data-curworkmode="">&lt/span>
&lt/div>
                  </pre>
                        </div>
                    </div>
                </div>
                
                <!-- code demo end -->
            </div>
            <!-- sip -->
            <div class="layui-tab-item">
                
                <fieldset class="layui-elem-field">
                  <legend>ccbar-sipphone测试</legend>
                  <div class="layui-field-box">
                      <form action="javascript:;" class="layui-form layui-form-pane">
                          <div class="layui-form-item">
                              <div class="layui-inline">
                                  <label class="layui-form-label">接入地址</label>
                                  <div class="layui-input-inline">
                                      <input id="host1" placeholder="默认地址" value="" type="text" autocomplete="off" class="layui-input">
                                  </div>
                              </div>
                              
                              <div class="layui-inline">
                                  <label class="layui-form-label">企业产品ID</label>
                                  <div class="layui-input-inline">
                                      <input id="productId1" value="" type="text" autocomplete="off" class="layui-input">
                                  </div>
                              </div>
                              <div class="layui-inline">
                                  <label class="layui-form-label">话机号码</label>
                                  <div class="layui-input-inline">
                                      <input id="phoneNum1" value="" type="text" autocomplete="off" class="layui-input">
                                  </div>
                              </div>
                              <button class="layui-btn layui-btn-sm" onclick="sip.login()">签入</button>
                              <button class="layui-btn layui-btn-sm" onclick="sip.logout()">签出</button>
                              <button class="layui-btn layui-btn-sm" onclick="$('#results').html('')">清空结果</button>
                          </div>
                      </form>
                  </div>
              </fieldset>
              <fieldset class="layui-elem-field">
                  <legend>接口结果</legend>
                  <div class="layui-field-box"><div id="results" style="border: 1px solid #ddd"></div></div>
              </fieldset>

              <script>
                $("#host1").val(location.origin);
                var sip = {
                    login: function() {
                        var host = $("#host1").val();
                        ccbarsdk.setting.host = host;
                        var phoneNum = $('#phoneNum1').val();
                        var busiId = $('#productId1').val();
                        if (phoneNum == '') { alert('话机号码为空'); return false; }
                        ccbarRequestSend(CallControl.getContextPath() + '/yc-ccbar/sipphone?action=login',
                         { command: 'login', phoneNum: phoneNum ,busiId:busiId},
                          function(event) {
                            var data = JSON.stringify(event);
                            sip.log(data,'login');
                        }, null, true);
                    },
                    logout: function() {
                        var host = $("#host1").val();
                        ccbarsdk.setting.host = host;
                        var phoneNum = $('#phoneNum1').val();
                        var busiId = $('#productId1').val();

                        if (phoneNum == '') { alert('话机号码为空'); return false; }
                        ccbarRequestSend(CallControl.getContextPath() + '/yc-ccbar/sipphone?action=login',
                         { command: 'logout', phoneNum: phoneNum ,busiId:busiId},
                          function(event) {
                            var data = JSON.stringify(event);
                            sip.log(data,'logout');

                        }, null, true);
                    },
                    log(data,type) {
                        var date = new Date().format('yyyy-MM-dd hh:mm:ss');
                        $('#results').append('<p>' + date + '--'+type+'--' + data + '</p>')
                    }
                }
              </script>
            </div>
            <!-- sip end -->
        </div>
    </div>
    <script src="/easitline-static/lib/layui/layui.all.js?t=1515376178738
" charset="utf-8"></script>
    <script>
    $(function() {
        layui.use('code', function() {
            layui.code({encode:true});
        });
    })
    </script>
</body>

</html>