#v3.0#20210202


/*==============================================================*/
/* Table: CC_RPT_AGENT_INDEX_VAL                                */
/*==============================================================*/
create table CC_RPT_AGENT_INDEX_VAL
(
   AGENT_ID             varchar(30) not null comment '坐席登录账号，如：8001@hnxt',
   DATE_ID              int not null comment '日期，格式：yyyymmdd',
   LAST_INDEX_VAL       text not null comment '最后指标值',
   primary key (AGENT_ID, DATE_ID)
);

alter table CC_RPT_AGENT_INDEX_VAL comment '保持当前坐席的监控指标值';


#v3.0#20201013
alter table    CC_RPT_AGENT_INDEX add   NOTREADY4_TIME       int comment '置忙时间(4) 单位：秒';
alter table    CC_RPT_AGENT_INDEX add   NOTREADY4_COUNT      int comment '置忙次数(4)';
alter table    CC_RPT_AGENT_INDEX add   NOTREADY5_TIME       int comment '置忙时间(5)，单位：秒';
alter table    CC_RPT_AGENT_INDEX add   NOTREADY5_COUNT      int comment '置忙次数(5)';

#v3.0#20201209

alter table  CC_RPT_AGENT_INDEX add   DEPT_CODE            varchar(50) comment '所在部门';
