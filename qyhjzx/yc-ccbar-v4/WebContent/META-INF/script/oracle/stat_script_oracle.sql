#v3.0#20210202


/*==============================================================*/
/* Table: CC_RPT_AGENT_INDEX_VAL                                */
/*==============================================================*/
create table CC_RPT_AGENT_INDEX_VAL 
(
   AGENT_ID             VARCHAR2(30)         not null,
   DATE_ID              INTEGER              not null,
   LAST_INDEX_VAL       CLOB                 not null,
   constraint PK_CC_RPT_AGENT_INDEX_VAL primary key (AGENT_ID, DATE_ID)
);

comment on table CC_RPT_AGENT_INDEX_VAL is
'保持当前坐席的监控指标值';

comment on column CC_RPT_AGENT_INDEX_VAL.AGENT_ID is
'坐席登录账号，如：8001@hnxt';

comment on column CC_RPT_AGENT_INDEX_VAL.DATE_ID is
'日期，格式：yyyymmdd';

comment on column CC_RPT_AGENT_INDEX_VAL.LAST_INDEX_VAL is
'最后指标值';



#v3.0#20201013
alter table    CC_RPT_AGENT_INDEX add   NOTREADY4_TIME       INTEGER ;
alter table    CC_RPT_AGENT_INDEX add   NOTREADY4_COUNT      INTEGER ;
alter table    CC_RPT_AGENT_INDEX add   NOTREADY5_TIME       INTEGER ;
alter table    CC_RPT_AGENT_INDEX add   NOTREADY5_COUNT      INTEGER ;

#v3.0#20201209

alter table  CC_RPT_AGENT_INDEX add   DEPT_CODE            varchar2(50);



