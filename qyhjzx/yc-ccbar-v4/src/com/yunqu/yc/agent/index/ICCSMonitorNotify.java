package com.yunqu.yc.agent.index;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.listener.GlobalContextListener;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.monitor.GroupMonitor;
import com.yunqu.yc.agent.monitor.RedisMonitor;
import com.yunqu.yc.agent.util.CacheUtil;

/**
 * 当ICCS的队列排队人数发生变化的时候通知到CCBAR，然后CCBAR在后台线程执行通知的处理操作。
 * 这里主要处理：respSkillGroupMonitor 消息。
 * 
 * {
	"workNotReadyAgentCount": "0",
	"agentId": "",
	"groupType": "group",
	"aveQueueLen": "0.00",
	"queueUsers": [],
	"logonAgentCount": "0",
	"serialId": "50382743",
	"outboundIdleAgentCount": "0",
	"skillGroupName": "E8AFADE99FB3E68A80E883BDE7BB8431",
	"busyAgentCount": "0",
	"timestamp": "20220209111255",
	"talkAgentCount": "0",
	"queueCallCount30": "0",
	"entId": "1001",
	"idleAgentCount": "0",
	"messageId": "respSkillGroupMonitor",
	"queueCallCount": "0",
	"pdsboundIdleAgentCount": "0",
	"alertAgentCount": "0",
	"maxQueueLen": "0",
	"maxIdleLen": "0",
	"workTimeRatio": "0.00",
	"abandonAgentCount": "0",
	"abandonQueueCount": "0",
	"skillGroupId": "6",
	"inboundIdleAgentCount": "0"
}
 * <AUTHOR>
 *
 */

public class ICCSMonitorNotify implements Runnable{
	
		private static  BlockingQueue<JSONObject> blockingQueue = new LinkedBlockingQueue<JSONObject>();
		
		public static void addNotify(JSONObject  jsonObject){
			try {
				if(blockingQueue.size()>1000){
					CcbarLogger.getLogger().warn("ICCSMonitorNotify queue size over(10000) ,ignore notify->"+jsonObject);
					return ;
				} 
				blockingQueue.add(jsonObject);
			} catch (Exception ex) {
				CcbarLogger.getLogger().warn(ex,ex);
			}
		}
		
		@Override
		public void run() {
			CcbarLogger.getLogger().info("start ICCSMonitorNotify...");
			Set<String> notifySkills = new HashSet<String>();
			long timer = System.currentTimeMillis();
			while(GlobalContextListener.runState){
			    try {
			    	JSONObject  jsonObject  = blockingQueue.poll(500, TimeUnit.MILLISECONDS);
			    	//if(jsonObject!=null)    	CcbarLogger.getLogger().info("ICCSMonitorNotify->"+jsonObject);
			    	if(jsonObject!=null){
			    		notifySkills.add(jsonObject.getString("skillGroupId"));
			    		cacheMonitor(jsonObject);
			    	}else{
			    		if(notifySkills.size()>0){
			    			this.notifyAgent(notifySkills);
			    			timer = System.currentTimeMillis();
			    			notifySkills.clear();
			    			continue;
			    		}
			    	}
			    	
			    	if(System.currentTimeMillis() - timer > 3000){
			    		if(notifySkills.size()>0){
			    			this.notifyAgent(notifySkills);
			    			notifySkills.clear();
			    		}
			    		timer = System.currentTimeMillis();
			    	}
			    	
				} catch (Exception ex) {
					CcbarLogger.getLogger().error(ex,ex);
				}
			}
		}
		
		private void notifyAgent(Set<String> notifySkills){
			try {
				AgentInfos.setSkillgroupChange(notifySkills);
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex,ex);
			}
		}
		
		
		private  void cacheMonitor(JSONObject  jsonObject){
			try {
		    	if(jsonObject!=null){
		    		String skillgroupId = jsonObject.getString("skillGroupId");
		    		jsonObject.remove("messageId");
		    		jsonObject.remove("serialId");
		    		jsonObject.remove("timestamp");
		    		jsonObject.remove("entId");
		    		jsonObject.remove("agentId");
		    		jsonObject.put("timestamp", System.currentTimeMillis());
		    		if(RedisMonitor.isOk()){
			    		CacheUtil.delete("ICCS_SKILLGROUP_"+skillgroupId);
			    		CacheUtil.put("ICCS_SKILLGROUP_"+skillgroupId, jsonObject, 120);
		    		}
		    		GroupMonitor.setGroupMonitor(skillgroupId, jsonObject);
		    	}
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex,ex);
			}
		}
}
