package com.yunqu.yc.agent.base;

public class EventContants {
	
	
	//接收消息事件
	public final static String MSG_EVT_ENTOPEN = "entOpen"; //4.1	企业开通
	public final static String MSG_EVT_ENTQUERY = "entQuery"; // 4.2	企业信息查询
	public final static String MSG_EVT_SKILLGROUPSYNC = "skillGroupSync"; // 4.3	技能组同步接口
	public final static String MSG_EVT_LOGON = "logon"; // 4.4	坐席签入接口
	public final static String MSG_EVT_LOGOFF = "logoff"; // 4.5	坐席签出接口
	public final static String MSG_EVT_MAKECALL = "makecall"; // 4.6	呼出接口
	public final static String MSG_EVT_CLEARCALL = "clearcall"; // 4.7	挂断接口
	public final static String MSG_EVT_STATE = "state"; // 4.8	获取坐席状态接口
	public final static String MSG_EVT_AGENTNOTREADY= "agentnotready"; //4.9	置忙接口
	public final static String MSG_EVT_AGENTREADY = "agentready"; // 4.10	置闲接口
	public final static String MSG_EVT_TRANSFER = "transfer"; // 4.11	转移接口
	public final static String MSG_EVT_CONSULT = "consult"; // 4.12	咨询接口
	public final static String MSG_EVT_RETRIEVE = "retrieve"; // 4.13	找回接口
	public final static String MSG_EVT_CONFERENCE = "conference"; // 4.14	三方接口
	public final static String MSG_EVT_MONITOR = "monitor"; // 4.15	监听接口
	public final static String MSG_EVT_APPMAKECALL= "appMakecall"; // 4.16	点击拨号接口
	
	//通知消息事件
	public final static String NOTIFY_EVT_ENTOPEN= "entOpenNotify"; //5.1	企业开通工单接口
	public final static String NOTIFY_EVT_ENTQUERY= "entQueryNotify"; // 5.2	企业信息查询通知接口
	public final static String NOTIFY_EVT_SKILLGROUPSYNC= "skillGroupSyncNotify"; // 5.3	技能组同步通知接口
	public final static String NOTIFY_EVT_LOGON= "logonNotify"; // 5.4	签入结果通知
	public final static String NOTIFY_EVT_LOGOFF= "logoffNotify"; // 5.5	签出结果通知
	public final static String NOTIFY_EVT_DELIVERED= "deliveredNotify"; // 5.6	外呼振铃通知接口
	public final static String NOTIFY_EVT_ESTABLISHED= "establishedNotify"; // 5.7	通话接通接口
	public final static String NOTIFY_EVT_CLEARCALL= "clearcallNotify"; // 5.8	通话结束通知（
	public final static String NOTIFY_EVT_STATE= "stateNotify"; // 5.9	坐席状态通知
	public final static String NOTIFY_EVT_TRANSFER= "transferNotify"; // 5.10	转移结果通知
	public final static String NOTIFY_EVT_CONSULT= "consultNotify"; // 5.11	咨询结果通知
	public final static String NOTIFY_EVT_RETRIEVE= "retrieveNotify"; // 5.12	找回结果通知
	public final static String NOTIFY_EVT_CONFERENCE= "conferenceNotify"; // 5.13	三方结果通知
	public final static String NOTIFY_EVT_MONITOR= "monitorNotify"; // 5.14	监听结果通知
	public final static String NOTIFY_EVT_CDR= "cdrNotify"; //5.15	通话话单通知
	
}
