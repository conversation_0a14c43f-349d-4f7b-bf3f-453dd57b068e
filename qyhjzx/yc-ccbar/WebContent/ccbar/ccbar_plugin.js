/**
 * ccbar_plugin.CallControl为对外调用方法
 */

var isAutoLogoff = false;
var autoLogoffTime = 120; //空闲自动签出的时间(秒)


var ccbarCallClock = null; //定时器,防止某些窗口一直不关闭
var ccbarCallClockTime = 60*1e3;//定时器超时时间
var _bpoFlag = 0;
var _bpoPrefix ='';
var ccbar_status = {
	funcMask:{},
	agent_state:{},
	callEvent:null,
	current:{
		caller:null,
		called:null,
	}
};
var agentListFromCcbar = [];
var needcallPhobne = null; //拉起话机的回调

function callerphoneNeedLogin(){
	tipsMsg('请签入后再使用');
}

var ccbar = (function() {
	var _action_ = 'click';
	//按钮绑定data事件
	var dataType = 'ccbartype';
	//data属性对应表
	var dataInfo = {
			agentUserName: 'ccbarusername', //用户工号
			agentUserPSW: 'ccbaruserpsw', //用户密码
			agentPhone: 'ccbaruserphone', //座机
			curStatus: 'ccbarcurstatus', //坐席当前状态
			makeCallNum: 'ccbarcallnum', //外呼输入框
		}
		// 暴露的容器
	var _ccbar_ = {};
	_ccbar_Event = null;
	var phoneDialPopup = null;
	var loginPopup = null;
	var isMakeCall = false;

	

	/*状态整理*/

	/**
	 * 链接或按钮添加data-ccbartype属性,值为下面respStatus或customStatus时,表明为相关按钮,
	 * respStatus内的类型,受SDK
	 */

	var status = {
		respStatus: {
			'logon': '登录',
			'logoff': '登出',
			'makecall': '外呼',
			'answercall': '应答',
			'clearcall': '挂机',
			'agentready': '置闲',
			'agentnotready': '置忙',
			'workNotReady':'进入话后整理',
			'workReady':'完成话后整理',
			"clearconnection": '结果指定号码的话路',
            "holdcall": '客户话路保持',
            "unholdcall": '客户话路恢复',
            "consultationcall": '咨询',
            "transfercall": '呼叫转移',
            "conferencecall": '三方会议',
            "listencall": '监听',
            "interventcall": '强插',
            "sstransfer": '单步转',
            "workMode":'工作模式',
            "startMonitor":'开始监听',
            "stopMonitor":'结束监听',
            'startInvent':'cmdInterventCall',//强插
            'stopInvent':'cmdStopInterventCall',//结束强插
		},
		customStatus: {
			'fastmakecall': '快速拨号'
		},
		callStatus: {
			'remote_delivered': '来电振铃',
			'remote_established': '开始通话',
			'callcleared': '呼叫结束',
			'INCALL':'呼入',
			'OUTCALL':'呼出'
		},
		funcMaskStatus: {
			'ready': '闲',
			'notready': '忙',
			'alerting': '振铃中',
			'talking': '通话中',
			'logoff': '未签入',
			'workNotReady': '话后整理',
			'unknow':'未知状态',
			'logout':'未登录',
			'occupy': '预占',
			'talk':'咨询',
			'held':'保持',
			'consulted':'咨询',
			'conferenced':'三方通话',
			'monitored':'监听'
		},
		agentinfo:{

		}
	};

	var curFuncStatus = null;
	
	var agentPhone = null;

	/*私有方法*/

	/**
	 * [customCallcontrol ccbarAPI整合]
	 * @type {Object}
	 */
	var customCallcontrol = {
			/*登录*/
			logon: function() {

				// var j_username_val = $("[data-" + dataInfo.agentUserName + "]").eq(0).val();
				// var j_password_val = $("[data-" + dataInfo.agentUserPSW + "]").eq(0).val();
				var phoneNum_val = '';
				phoneNum_val = $("[data-" + dataInfo.agentPhone + "]").eq(0).val();
				if(agentListFromCcbar.length>1){
					phoneNum_val = $('#ccbar-phone-select').val();
				}
				// if (j_username_val == "" || j_password_val == "" || phoneNum_val == "") {tipsMsg('输入不能为空'); return false};
				if (phoneNum_val == "") {tipsMsg('输入不能为空'); return false};

				var t = $("#ccbarLogin [data-ccbartype='logon']");
				t.addClass('disabled').text('签入中...');
				CallControl.logon(phoneNum_val,function(rsp){
					if(rsp.data.code == 'succ'){
						if(rsp.data.content != 'succ'){
							tipsMsg(rsp.data.content);
						}
						if(rsp.data.readyTimeout){
							autoLogoffTime = rsp.data.readyTimeout;
						}
						$('[data-ccbaragentinfo="phoneNum"]').text(phoneNum_val);
						//自动拉起话机
						if(rsp.data.result.phoneType == '3'){
							if(rsp.data.result.sbcAddr == ''){
								tipsMsg('签入失败，缺少SIP话机的接入地址');
								var t = $("#ccbarLogin [data-ccbartype='logon']");
								t.removeClass('disabled').text('签入');
								return false; //没有sip话机,中止签入流程
							}
						}else{
							needcallPhobne = null;
						}
						needcallPhobne = true;
						agentPhone = rsp.data.agentId;

						callerphone = function(){
							CallControl.wakeupSIP();
						}
						//签入计时器,防止失去响应
						clearTimeout(ccbarCallClock);
						ccbarCallClock = setTimeout(function(){
							var t = $("#ccbarLogin [data-ccbartype='logon']");
							t.removeClass('disabled').text('签入');
						},ccbarCallClockTime);
					}else{
						tipsMsg(rsp.data.content);
						var t = $("#ccbarLogin [data-ccbartype='logon']");
						t.removeClass('disabled').text('签入');
					}
				});
			},
			logonWithoutPhonenum:function(){
				CallControl.logon('',function(rsp){
					if(rsp.data.code == 'succ'){
						if(rsp.data.content != 'succ'){
							tipsMsg(rsp.data.content);
						}
						if(rsp.data.readyTimeout){
							autoLogoffTime = rsp.data.readyTimeout;
						}
						//自动拉起话机
						if(rsp.data.result.phoneType == '3'){
							if(rsp.data.result.sbcAddr == ''){
								tipsMsg('签入失败，缺少SIP话机的接入地址');
								var t = $("#ccbarLogin [data-ccbartype='logon']");
								t.removeClass('disabled').text('签入');
								return false; //没有sip话机,中止签入流程
							}
							//needcallPhobne = true;
							
						}else{
							needcallPhobne = null;
						}
						needcallPhobne = true;
						agentPhone = rsp.data.agentId;
						//CallControl.logon(rsp.data.phoneNum);

						callerphone = function(){
							//CallControl.getActivateSipPhoneUrl(rsp.data.phoneNum,rsp.data.agentPassword,"auto");
							//oprIfm.location.href = rsp.data.result.sbcAddr;//改用本地服务器的拉起链接
							CallControl.wakeupSIP();
						}
						//签入计时器,防止失去响应
						clearTimeout(ccbarCallClock);
						ccbarCallClock = setTimeout(function(){
							var t = $("#ccbarLogin [data-ccbartype='logon']");
							t.removeClass('disabled').text('签入');
						},ccbarCallClockTime);

						
					}else{
						tipsMsg(rsp.data.content);
						var t = $("#ccbarLogin [data-ccbartype='logon']");
						t.removeClass('disabled').text('签入');
					}
				});
			},
			/*登出*/
			logoff: function() {
				CallControl.logoff(agentPhone);

			},
			/*挂机*/
			clearCall: function() {
				CallControl.clearCall();
			},
			/*外呼*/
			callNumber: function() {
				
				var callNumber_val = $("#_ccbar_callNumber").val();
				
				this.makeCall(callNumber_val);
			},
			/*呼叫转移等功能的坐席选择面板*/
			agentSel:function(type){
				$("#ccbar_hidden_sel_type").val(type);
				CallControl.agentList(function(result){
			          if (result.state) {
			            var groups = result.data.result.groups;
			            var agents = result.data.result.agents;
			            var groupsHtml = '<option value="">请选择技能组</option>';
                        for (var i = 0; i < groups.length; i++) {
                          groupsHtml+='<option value="'+groups[i].SKILL_GROUP_ID+'">'+groups[i].SKILL_GROUP_NAME+'</option>'
                        }
                        var agentsHtml = '<option>---</option>';
			            for (var j = 0; j < agents.length; j++) {
			              agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
			            }
			            $("#ccbar_select_groups").html(groupsHtml);
			            $("#ccbar_select_agents").html(agentsHtml);
			            CallControl.callerList(function(result){
					  		var tempOptions = '';
					  		
					  		if(result.data.result.callers){
					  			for (var i = 0; i < result.data.result.callers.length; i++) {
					  				tempOptions += '<option value="'+result.data.result.callers[i]+'">'+result.data.result.callers[i]+'</option>'
					  			}
					  			//$("#ccbar_phoneCallerList").show();

					  		}else{
					  			tipsMsg('无外显号码');
					  		}
					  		if(CallControl.workModeType == 'inbound'){
                                tempOptions += '<option value="'+ccbar_status.current.caller+'">客户-'+ccbar_status.current.caller+'</option>'

                            }
					  		$("#ccbar_select_agents_callers").html(tempOptions);
					  		$("#agentsel").fadeIn();
					  	});
			            
			          }
			    });
			},
			canCall :function(){
				return CallControl.getFunc("makecall")
			},
			makeCall: function(num,userData) {
               if(!CallControl.getFunc('makecall')) {tipsMsg('当前不可外呼!'); return;}
				
                // var reg = /(^((\d{11})|(0)(\d{11})|(\d{7,8})|(\d{4}|\d{3})(\d{7,8}))$)/;
                var reg  = /^\d{3,}$/;
                var reg2 = /(^((1)(\d{2,5})|(9)(\d{2,5}))$)/;//紧急号码或服务电话
                if (new RegExp(reg).test(num)||new RegExp(reg2).test(num)||num.indexOf('@')!=-1 ||String(num).substr(0, 1) == "#") {
                    if(!userData) userData = {}
                    var callType = 2;
                	if(num.indexOf('@')!=-1){callType = 1;}
					//调用busi,num为电话号码,后面的方法为原发起呼叫方法,作为busi的回调执行
					var caller = $("#ccbar_phoneCallerList option:selected").val()||'';
                    

					CallControl.makeCall(num,caller,userData,function(result){
						
						if(result.data.bpoFlag&&result.data.bpoFlag == '1'){
							_bpoFlag = 1
							_bpoPrefix = result.data.bpoPrefix ;
						}else{
							_bpoFlag = 0
							_bpoPrefix = '';
						}

						layer.close(phoneDialPopup);
						tipsMsg('正在呼叫'+num);
						
						if(result.data.content !='succ'){
							tipsMsg(result.data.content);
						}
						
					},callType)

				
				} else {
					
					tipsMsg('请输入正确的电话号码');
					
				}
			},
			setReady: function() {
				CallControl.agentReady();
			},
			setNotReady: function(busytype) {
                CallControl.agentNotReady(busytype);
            },
			//应答
			answercall: function(){
				CallControl.answerCall();
			},
			workNotReady : function() {// 进入话后整理
				CallControl.workNotReady();
			},
			workReady : function() {//完成话后整理
				CallControl.workReady();
			},
			//呼叫转移
            transfer:function(called,displaynum,skillId, callType, userData){
                CallControl.transfer(called,displaynum,skillId, callType, userData)
            },
            //呼叫转移
            sstransfer:function(called, displaynum ,skillId, callType, userData){
                CallControl.transfer(called, displaynum,skillId, callType, userData)
            },
            //咨询
            consultationcall:function(called,displaynum, skillId,callType, userData){
                CallControl.consultation(called,displaynum,skillId, callType, userData)
            },
            //客户话路保持
            holdCall:function(){
                CallControl.holdCall();
            },
            //客户话路恢复
            unholdCall:function(){
                CallControl.unholdCall();
            },
            //三方通话
            conferencecall:function(){
                CallControl.conference();
            },
            //监听
            listenCall:function(listenedAgentId){
                CallControl.listenCall(listenedAgentId);
            },
            workMode:function(type){
                if(CallControl.isLogoned){
                	var typeName = {
                		'inbound':'呼入',
                		'outbound':'呼出',
                		'pdsbound':'智能外呼',
                		'all':'自动',
                	}
                    tipsMsg('切换到'+typeName[type]+'模式');
                	
                    CallControl.workMode(type);
                }
            },
		}
	/**
	 * [callControl 暴露的接口操作]
	 * @type {[object]}
	 */
	_ccbar_.callControl = customCallcontrol;

		/**
		 * @description 同意根据接口操作当前页面功能按钮元素
		 * @param       {[string]}      type   [状态]
		 * @param       {[string]}      status [状态值,1为可用]
		 * @return      {[type]}             [description]
		 */
	var _clockTimeout = null;
	_ccbar_.clock = {
		t: 0,
		lastSend:0,
		reset: function() {
			clearInterval(_clockTimeout);
			// $("#_ccbar_clock").hide();
			this.t = 0;
		},
		start: function() {
			var el = $('[data-ccbar-text="clock"]');
			var s = this;
			$("#_ccbar_clock").show();
			_clockTimeout = setInterval(updateTime, 1000)

			function updateTime(str) {
				s.t++;
				var hours = Math.floor(s.t / 3600);
				var minutes = Math.floor((s.t % 3600) / 60);
				var seconds = s.t % 60;
				hours = hours < 10 ? '0' + hours : hours;
				minutes = minutes < 10 ? '0' + minutes : minutes;
				seconds = seconds < 10 ? '0' + seconds : seconds;
				el.text(hours + ":" + minutes + ":" + seconds);

				/*置闲2分钟后*/
				if(isAutoLogoff && s.t>= autoLogoffTime){
					var state = CallControl.getState();
					if(state=='IDLE' || state == 'ready'){
						var now_t = new Date().getTime()
						var split_t = now_t - s.lastSend;
						if(split_t>5000){
							// layer.alert('坐席置闲已超过2分钟,已自动签出',{title:false})
							alertPopup =  layer.open({
							  type: 1,area:['380px','140px'],
							  icon:7,
							  offset:'70px',
							  title:'提示', content:'<p style="padding:20px;">坐席置闲超过'+autoLogoffTime+'秒,已自动签出</p>',
							  resize:false,btn:['确定']}
							  )

							CallControl.logoff();
							s.lastSend = now_t;	
						}
						// tipsMsg('坐席置闲已超过2分钟,已自动签出');
						
						// t.reset();
					}	
				}
				
			}
		}
	}

	/*私有方法end*/
	//更新拨号信息
	//界面更新
	_ccbar_.updateLayout = function(type, status) {
		var btn = $('[data-' + dataType + '=' + type + ']');
		var flag = status ? false : true;
		btn.prop('disabled', flag);
		if (flag) {
			btn.addClass('disabled');
		} else {
			btn.removeClass('disabled');
		}
	}

	//功能更新
	_ccbar_.funcMaskUpdate = function(obj) {
		var self = this;
		var newobj = typeof obj === "object" ? obj : {};
		var type;
		isMakeCall = obj.makecall;
		for (type in newobj) {
			self.updateLayout(type, newobj[type]);
		}
	}
	//更新坐席信息
	_ccbar_.updateAgentInfo  = function(agentInfo) {
		var self = this;
		var newobj = typeof agentInfo === "object" ? agentInfo : {};
		var type;
		for (type in newobj) {
			updateAgentInfoText(type, newobj[type]);
		}
		var directionType = agentInfo.directionType;
		if (directionType=='OUTCALL') {directionType='呼出'}else if(directionType=='INCALL'){directionType='呼出'}
		$("#_ccbar_clock .calltype").text(directionType);

		function updateAgentInfoText(type, status) {
			var btn = $('[data-ccbaragentinfo=' + type + ']').text(status);
		}
	}
	//坐席当前状态更新
	_ccbar_.agentStatusUpdate = function(agent_state,agent_state_name) {
			
			var sbox = $(".agentinfo");
			var sname = $("[data-agentinfo='curstatus']");

			sname.text(agent_state_name);
			
			switch (agent_state) {
				case 'IDLE':
					sname.css('color', '#88f555');
					break;
				case 'BUSY':
					sname.css('color', '#f5cf55');
					break;
				case 'ALERTING':
					sname.css('color', '#f5cf55');
					break;
				case 'TALK':
					sname.css('color', '#f5cf55');
					break;
				default:
					sname.css('color', '#f4d0d0');

			}

		}
		//绑定事件
	_ccbar_.initBind = function() {
		$("select[name='ccbar_select_groups']").off('change').on('change',function(e){
            var groupId = $(this).val();
            if(groupId=='') {$("#ccbar_select_groups_agent").html(''); return};
            CallControl.agentList(groupId,function(result){
                  if (result.state) {
                    var groups = result.data.result.groups;
                    var agents = result.data.result.agents;
                   /* var groupsHtml = '';
                    for (var i = 0; i < groups.length; i++) {
                      groupsHtml+='<option value="'+groups[i].SKILL_GROUP_ID+'">'+groups[i].SKILL_GROUP_NAME+'</option>'
                    }*/
                    var agentsHtml = '<option value="">系统自动选择</option>';
                    for (var j = 0; j < agents.length; j++) {
                      agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
                    }
                    $("#ccbar_select_groups_agent").html(agentsHtml);
                }
            });
        });
		//绑定坐席选择功能
		$("#agentsel input[name=trantype]").off("click").on("click",function(){
            var v= $(this).val();
            if(v=='1'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=1]").show();

              $('option[data-displaynum="cust"]').show();

            }else if(v=='4'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=4]").show();

              $('option[data-displaynum="cust"]').show();
            }else if(v=='3'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=3]").show();

              $('option[data-displaynum="cust"]').hide();
              $('option[data-displaynum="cust"]:checked').prop("checked",false);
            }
        });

		$("#ccbar_agent_sel").off("click").on("click",function(){
			
			var type = $("#agentsel input[name=trantype]:checked").val();
			var called = '';
			switch (type) {
				//坐席
				case '1':
					called = $("#ccbar_select_agents option:selected").val();
				break;
				//外线
				case '3':
					called = $("#ccbar_select_num").val();
				break;
				//坐席组
				case '4':
					called = $("#ccbar_select_groups option:selected").val();
					
				break;
			}
			if(called == ''){tipsMsg('请选择目标坐席或呼叫对象')}
			var caller = ccbar_status.current.called;
			var eventType = $("#ccbar_hidden_sel_type").val();
			var displaynum = $("#ccbar_select_agents_callers").val();
            var skillId = $("#ccbar_select_groups option:selected").val();

			if(eventType == "") return;
			if(type==4){
                var skillGroupAgent = $("#ccbar_select_groups_agent option:selected").val();
                if(skillGroupAgent!=''){
                    // eventType = 'sstransfer';
                    type = 1;
                    called = skillGroupAgent;
                    customCallcontrol[eventType](called,displaynum,skillId,type,{});
                    return false;
                }
            }
			customCallcontrol[eventType](called,displaynum,type,{});
		});


		//功能按钮绑定
		$(document).on(_action_, '[data-' + dataType + ']', function(event) {
			var t = $(this);
			if (t.hasClass('disabled')) return false;
			 if (t.data('tag')==true) return false;
			var type = t.data(dataType);
			if (type in status.respStatus || type in status.customStatus) {
			consoleLog("点击的ccbar按钮绑定了事件======>"+type);

				switch (type) {
					//快速拨号
					case 'fastmakecall':
						var callNum = t.data('ccbarcallnum');
						$('.number-area .numbers').val(callNum).trigger('blur');
						//customCallcontrol.makeCall(callNum);
						break;
						//外呼
					case 'makecall':
						if (t.data('toggle') && t.data('toggle') == 'ccbarphone') {
							phoneDialPopup =  layer.open({
							  type: 1,area:['340px','390px'],
							  title:false, 
							  resize:false,
							  content: $("#ccbarCustomPhone"), //这里content是一个普通的String
							  end:function(){
							  	$("#ccbarCustomPhone").hide();
							  	$("#ccbar_phoneCallerList").html('')
							  },
							  success:function(){
							  	//设置显示的外呼号码
							  	CallControl.callerList(function(result){
							  		var tempOptions = '';
							  		if(result.data.result.callers){
							  			for (var i = 0; i < result.data.result.callers.length; i++) {
							  				tempOptions += '<option value="'+result.data.result.callers[i]+'">'+result.data.result.callers[i]+'</option>'
							  			}
							  			//$("#ccbar_phoneCallerList").show();

							  		}else{
							  			tipsMsg('无外显号码,不允许呼叫.');
							  		}
							  		$("#ccbar_phoneCallerList").html(tempOptions);
							  	});
							  }
							});
							return;
						} else if (t.data('calltype') && t.data('calltype') == 'fastmakecall') {
							if($('.disabled[data-ccbartype="makecall"]').length>0) return;
							var callNum = t.data('ccbarcallnum');
							customCallcontrol.makeCall(callNum);
						} else {

							customCallcontrol.callNumber();
						}

						break;
						//登出
					case 'logoff':
						customCallcontrol.logoff();
						break;
						//登录
					case 'logon':
						if (t.data('toggle') && t.data('toggle') == 'ccbarlogon') {
							if(agentListFromCcbar.length==1){
								customCallcontrol.logon();
								return;
							}
							if(CallControl.agentLogin == 'auto'){
								customCallcontrol.logonWithoutPhonenum();
								return;
							}
							loginPopup =  layer.open({
							  type: 1,
							  title:false,
							  resize:false, 
							  offset:'70px',
							  area:['330px','140px'],
							  content: $("#ccbarLogin"), //这里content是一个普通的String
							  success:function(){
							  	document.getElementById("_ccbar_agentPhoneInput").focus();
							  },
							  end:function(){
							  	$("#ccbarLogin").hide();
							  }
							});
							return;
						}else{
							//t.addClass('disabled').text('签入中...');
							customCallcontrol.logon();
						}
						
						break;
						//挂机
					case 'clearcall':
						customCallcontrol.clearCall();
						break;
						//应答
					case 'answercall':
						customCallcontrol.answercall();
						break;
					case 'agentnotready':
                        var busytype = $(this).data('busytype')
                        customCallcontrol.setNotReady(busytype);
                        break;
					case 'agentready':
						customCallcontrol.setReady();
						break;
						
					case 'workNotReady':
						customCallcontrol.workNotReady();
						break;
					case 'workReady':
						customCallcontrol.workReady();
						break;

					//呼叫转移
                    case 'sstransfer':
                    	customCallcontrol.agentSel('sstransfer');
                        break;
                     
                    //咨询
                    case 'consultationcall':
                    	customCallcontrol.agentSel('consultationcall');
                        break;
                    //客户话路保持
                    case 'holdcall':
                        customCallcontrol.holdCall();
                        break;
                    //客户话路恢复
                    case 'unholdcall':
                        customCallcontrol.unholdCall();
                        break;
                    //三方通话
                    case 'conferencecall':
                    	// customCallcontrol.agentSel('conferencecall');
                        customCallcontrol.conferencecall();
                        break;
                    //监听
                    case 'listencall':
                    	customCallcontrol.agentSel('listencall');
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
                    case 'workMode':
                    	
                        customCallcontrol.workMode(t.data('type'));
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
                    case 'stopMonitor':
                        CallControl.stopMonitor();
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
                    case 'stopInvent':
                        CallControl.stopInvent();
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
                    case 'startInvent':
                        CallControl.startInvent();
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
                    case 'interventcall':
                        CallControl.interventcall();
                        // customCallcontrol.listenCall(listenedAgentId);
                        break;
				};
			} else {
				consoleLog('未定义的操作类型');
			}
		});

		//自定义绑定
		
		ccbarEvent.addEvent('logon', function(event) {
			if(event.result == 'Fail'){return}
			layer.close(loginPopup);
			if(needcallPhobne){
				callerphone();
			}
			tipsMsg('签入成功');

		});
		ccbarEvent.addEvent('logoff', function(event) {
			if(event.result == 'Fail'){return}
			callerphone = callerphoneNeedLogin;//改写话机事件
			tipsMsg('已签出');
		});
		
	};
	_ccbar_.initLayout = function(type) {
		var loginDIV = '<div id="ccbarLogin" style="display:none"><div class="ccbar-form"><div><div class="ccbar-form-input-group"><span class="ccbar-input-group-addon">话机号码</span><input type="text" id="_ccbar_agentPhoneInput" data-ccbaruserphone placeholder="请输入话机号码..." value="" class="ccbar-form-control"><select id="ccbar-phone-select" class="ccbar-form-control"></select></div><div style="text-align:center;padding-top:10px"><button type="button" style="width:120px" data-ccbartype="logon" data-placement="bottom" class="ccbar-form-submit">签入</button></div></div></div></div>';
		var dial = '<div id="ccbarCustomPhone" class="ccbar-phone" style="display:none"><div class="ccbar-phone-panel ccbar-phone-panel-left"><div class="ccbar-phone-panel-content"><div id="ccbar_agentSel" class="ccbar-form-input-group" style="display: table;padding: 10px 35px;"><span class="input-group-addon">外显号码</span><select name="" id="ccbar_phoneCallerList" class="form-control"></select></div><div id="" class="ccbar-form-input-group" style="display: table;padding: 0 35px 10px;"><span class="input-group-addon">客户号码</span><input data-ccbartype="ccbarcallnum" placeholder="请输入号码..." autofocus="" id="_ccbar_callNumber" class="form-control"></div><ul class="numbers-container clearfix"><li><span data-pushnum="1" class="pusher">1</span></li><li><span data-pushnum="2" class="pusher">2</span></li><li><span data-pushnum="3" class="pusher">3</span></li><li><span data-pushnum="4" class="pusher">4</span></li><li><span data-pushnum="5" class="pusher">5</span></li><li><span data-pushnum="6" class="pusher">6</span></li><li><span data-pushnum="7" class="pusher">7</span></li><li><span data-pushnum="8" class="pusher">8</span></li><li><span data-pushnum="9" class="pusher">9</span></li><li><span data-pushnum="*" class="pusher">*</span></li><li><span data-pushnum="0" class="pusher">0</span></li><li><span data-pushnum="#" class="pusher">#</span></li></ul><div class="call-btn"><div href="javascript:void(0)" data-ccbartype="makecall" class="pusher-makecall"><i class="_ccbar_icon _ccbar_icon-makecall"></i>呼叫</div></div></div></div></div>';
		var kefuBtn = '<li class="dropdown funcbtn"><a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><span class="color-white text-xs block">更多 <b class="caret"></b></span></a><ul class="dropdown-menu dropdown-icon"><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="holdcall">保持</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="unholdcall">恢复</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="consultationcall">咨询</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="conferencecall">三方通话</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="listencall">监听</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="sstransfer">呼叫转移</a></li></ul></li>';
		var extendBtn = '<li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="workNotReady">进入话后处理</a></li><li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="workReady">完成话后处理</a></li><li class="funcbtn"><a href="javascript:void(0)" class="ccbar-hide-disabled disabled" data-ccbartype="stopMonitor">停止监听</a></li><li class="funcbtn"><a href="javascript:void(0)" class="ccbar-hide-disabled disabled" data-ccbartype="stopInvent">停止强插</a></li>';
		var iframe = "<iframe id='oprIfm' name='oprIfm' border='0' frameborder='0' width='0' height='0' style='display:none'></iframe>";
        var agentSel = '<div id="agentsel" class="ccbar_panel_login" style="display:none"><a href="javascript:void(0)" onclick="$(\'#agentsel\').fadeOut()" class="ccbarbtn_close"></a><form class="ccbar-form"><input type="hidden" id="ccbar_hidden_sel_type" /><div style="height: 160px"><div class="ccbar-form-input-group"><span class="input-group-addon">外显号码</span><select name="" id="ccbar_select_agents_callers" class="form-control"></select></div><div class="ccbar-form-input-group"><span class="input-group-addon">类型</span><div class="form-control"><label for="trantype1" class="ccbar-label"><input type="radio" checked id="trantype1" name="trantype" value="1"><span>坐席</span></label><label for="trantype4" class="ccbar-label"><input type="radio" id="trantype4" name="trantype" value="4"><span>坐席组</span></label><label for="trantype3" class="ccbar-label"><input type="radio" id="trantype3" name="trantype" value="3"><span>外线</span></label></div></div><div data-trantype="3" class="ccbar-form-input-group" style="display: none"><span class="input-group-addon">被叫号码</span><input type="text" id="ccbar_select_num" name="groupName" placeholder="请输入被叫号码..." value="" class="form-control"></div><div data-trantype="4" class="ccbar-form-input-group" style="display: none"><span class="input-group-addon">技能组</span><select name="ccbar_select_groups" id="ccbar_select_groups" class="form-control"></select></div><div data-trantype="4" class="ccbar-form-input-group" style="display: none"><span class="input-group-addon">坐席</span><select name="ccbar_select_groups_agent" id="ccbar_select_groups_agent" class="form-control"><option value="">---</option></select></div><div data-trantype="1" class="ccbar-form-input-group"><span class="input-group-addon">坐席</span><select name="" id="ccbar_select_agents" class="form-control"></select></div></div><div style="text-align:center;padding-top:10px"><button type="button" style="width:120px" id="ccbar_agent_sel" data-placement="bottom" class="ccbar_btn_login">选择</button></div></form></div>';
		$("#ccbarLogin").remove();
		$("body").append(loginDIV+dial+iframe+agentSel);


		if(type!='undefined' && type!='false'){
           

            if(CallControl.ccbarMode!=1){
                 $("#ccbar .funcbtn-group").append(extendBtn);
            }else if(CallControl.ccbarMode==1){
            	$("#ccbar .funcbtn-group").html('');
            }

            if(type && type=="advanced"||type =='kefu'||CallControl.ccbarMode==3){
               
                $("#ccbar .funcbtn-group").append(kefuBtn);
            }
        }

		$("#ccbar .agentLogoff").addClass('disabled').removeAttr('style');
		
		$('.ccbar-form input').on("keyup",function(event){
			if(event.keyCode == "13") {//keyCode=13是回车键)
				event.preventDefault()
				$("#ccbarLogin .ccbar-form button").click();
			}
		});
		
	}
	_ccbar_.initPhone = function() {
		var numInput = $('#_ccbar_callNumber');

		/*	Delete */
		$('#ccbarCustomPhone').on('click', '.del-btn', function() {
			var numbers = TrimAll(numInput.val(),'g');
			var numbers2 = numbers.length;
			numInput.val(numbers.substr(0, numbers2 - 1));
			document.getElementById("_ccbar_callNumber").focus();
		});

		$("#ccbarCustomPhone").on('click', '.pusher', function() {
			var n = $(this).data('pushnum');
			var numbers = TrimAll(numInput.val(),'g');
			numInput.val(numbers + n)
			
			document.getElementById("_ccbar_callNumber").focus();
		});

		numInput.on("keyup",function(event){
			if(event.keyCode == "13") {//keyCode=13是回车键)
				$("#ccbarCustomPhone").find(".pusher-makecall").click();
			}
		});

		
	}
	_ccbar_.initSDK = function(ip, port) {
		// 重写相关CTI事件
		
		CallControl.onEventLoginFromOther = onEventLoginFromOther;
		CallControl.onRespAgentState = onRecvAgentStateEvent;
		CallControl.onRespCallEvent = onRecvCallEvent;
		CallControl.onRespNotify = onRecvNotifyEvent;

		CallControl.onEventSessionTimeout = onEventSessionTimeout;
		//启动sdk

		CallControl.start(ip, port);
		consoleLog('SDK启动');
	};
	_ccbar_.init = function(ip, port ,type) {
		CallControl.ccbarInit(function(result){
			_ccbar_.initLayout(type);
			_ccbar_.initBind();
			_ccbar_.initSDK(ip, port);
			ccbar_plugin.clock.start();
			//拨号盘初始化
			_ccbar_.initPhone();
			audio_player.init();
			//重写拉起事件
			callerphone=callerphoneNeedLogin;
			//复写输入框
			resetLogonInput(result);
		});
	};

	function resetLogonInput(result){
		var agentList = result.data.agentPhones;
        agentListFromCcbar = agentList;
        if (agentList.length == 0) {
            $("#ccbar-phone-select").hide();
        } else if (agentList.length == 1) {
            $("#_ccbar_agentPhoneInput").val(agentList[0]);
            $("#ccbar-phone-select").hide();
        } else {
            $("#_ccbar_agentPhoneInput").hide();
            var html = '';
            for (var i = 0; i < agentList.length; i++) {
                html += '<option value="' + agentList[i] + '">' + agentList[i] + '</option>';
            }
            $("#ccbar-phone-select").html(html);
        }
        // //不需要输入或者选择工号
        // if (CallControl.agentLogin == 'auto') {
        //     $("#ccbar-phone-input,#ccbar-phone-select").hide();
        // }

        // if (result.data.voiceAgent == 'off') {
        //     $("#voiceAgent").hide();
        //     $("#voiceSwitch").prop("checked", false);
        // }
        // if (result.data.multiMediaAgent == 'off') {
        //     $("#multiMediaAgent").hide();
        //     $("#mediaIframeBtn").hide();
        //     $("#multiMediaSwitch").prop("checked", false);

        // }
	}

	return _ccbar_;
});
//初始化
var ccbar_plugin = new ccbar();

/*SDK应用方法复写*/


// 收到当前坐席异机登录事件
function onEventLoginFromOther() {
	layer.msg('坐席已签出');
	consoleLog('坐席已签出');
}

function consoleLog(str) {
	if(_ccbarDebug){
		console.log(new Date().format('yyyy-MM-dd hh:mm:ss') + " " + str);
	}
}
Date.prototype.format = function(format) {
	var o = {
		"M+": this.getMonth() + 1, // month
		"d+": this.getDate(), // day
		"h+": this.getHours(), // hour
		"m+": this.getMinutes(), // minute
		"s+": this.getSeconds(), // second
		"q+": Math.floor((this.getMonth() + 3) / 3), // quarter
		"S": this.getMilliseconds() // millisecond
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
			.substr(4 - RegExp.$1.length));
	for (var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
	return format;
};

function jsonpCallback() {

}

/**
 * <AUTHOR>
 * @DateTime    2017-09-14
 * @copyright   [copyright]
 * @license     [license]
 * @version     [version]
 * @description [自定义事件控制器]
 */

function TrimAll(str, is_global) { 
    var result;  
    result = str.replace(/[^\d*#]/g,'');
    /*if (is_global.toLowerCase() == "g") {  
        result = result.replace(/\s/g, "");  
    } */ 
    return result;  
} 

function tipsMsg(msg) {if(layer){layer.msg(msg, {icon:7});}}

var audio_player = {
      audio:null,
      inited:false,
      init:function(){
        audio_player.audio = document.createElement("audio");
        audio_player.audio.controls = true;
        audio_player.inited = true;
      },
      play:function(src,loop) { 
        if(!audio_player.inited) return;
        audio_player.stop();
        audio_player.audio.src = src;
        audio_player.audio.play();
        audio_player.audio.loop = loop ? true:false;
        audio_player.audio.addEventListener('ended', function () {  
        }, false);
      },
      stop:function() {
        if(!audio_player.inited) return;
        audio_player.audio.pause();
      }
}
function callerdownload(){
	CallControl.downloadSIP();
}


//功能事件消息相关
var workNotReadyTimeoutClock = null,_workNotReadyTimeout= null,workNotReadyText = null;
function workNotReadyTimeout(){
    workNotReadyTimeoutClock --;
    var btn = $('[data-ccbartype=workReady]');
    if(!workNotReadyText){
        workNotReadyText = btn.html();
    }
    btn.html(workNotReadyText+'('+workNotReadyTimeoutClock+')')
    if(workNotReadyTimeoutClock>0) {
        _workNotReadyTimeout = setTimeout(workNotReadyTimeout,1000)
    }else{btn.html(workNotReadyText)}
}


// 收到坐席相关事件
function onRecvAgentStateEvent(agentEvent){
	consoleLog('坐席状态同步');
	//处理功能状态变化
	$("[data-ccbartype='workMode']").removeClass("active");
    $("[data-ccbartype='workMode'][data-type='"+agentEvent.workMode+"']").addClass("active");

    var typeName = {'inbound':'呼入','outbound':'呼出','pdsbound':'智能外呼','all':'自动'};
    $("[data-curworkmode]").text(typeName[agentEvent.workMode]);
    ccbar_plugin.clock.reset();
    ccbar_plugin.clock.start();

	ccbar_plugin.agentStatusUpdate(agentEvent.state,agentEvent.stateDesc);
	ccbar_plugin.funcMaskUpdate(agentEvent.funcMask);

	if(agentEvent.state == 'WORKNOTREADY' && CallControl.oldState=='TALK' && CallControl.config.readyTimeout >0){
        workNotReadyTimeoutClock = Number(CallControl.config.readyTimeout/1000)
        workNotReadyTimeout();
    }
	
	//根据状态码进行提示
	if(agentEvent.notifyContent){
        var tips = ccbar_config['error'][agentEvent.notifyContent];
        if(tips== undefined){tips = '错误代码:'+agentEvent.notifyContent}
        tipsMsg(tips)
    }
}

function getPhone(val,realPhone){
	if(val==''){
		return '--';
	}
	var iscrypt=localStorage.getItem("iscrypt");
	if(iscrypt=='true'&&realPhone){
		//return advReplace(realPhone,-4,3,'*');
		 return advReplace(realPhone,3,4,'*');//只显示前3后4，如：156****5954
	}
	if(iscrypt=='false'&&realPhone){
		return realPhone;
	}
	if(val.substring(0,1)=='#'){
		return '******';
	}
	return val;
}

// 收到与呼叫相关事件
function onRecvCallEvent(callEvent){
	consoleLog("接收到呼叫事件====>");
	consoleLog(callEvent);
	clearTimeout(ccbarCallClock);//有呼叫返回了,停止定时器

    ccbarEvent.emitEvent(callEvent.callEventId,callEvent);//触发呼叫事件监听
	
	function updateAgentInfoText(type, status) {
		var btn = $('[data-ccbaragentinfo=' + type + ']').text(status);
	}
	function getTrueNum(num){
		if (_bpoFlag) {
			var n = String(num);
			return n.replace(_bpoPrefix,'');
		}else{
			//return getPhone(num,num);//加密号码
			return num;
		}
	}
	if (callEvent.callEventId == 'evtAltering') {
		consoleLog('callEvent.callEventId=' + callEvent.callEventId + ' 振铃事件');
		//修改呼叫信息
		var _caller = _bpoFlag?getTrueNum(callEvent.event.caller):callEvent.event.caller;
		var _called = _bpoFlag?getTrueNum(callEvent.event.called):callEvent.event.called;
		updateAgentInfoText('caller', _caller);
		updateAgentInfoText('called', _called);
		var num2 =  callEvent.event.createCause=='6'?callEvent.event.called:callEvent.event.caller
        tipsMsg("振铃中:"+num2);
		
	} else if (callEvent.callEventId == 'evtConnected') {
		consoleLog('callEvent.callEventId=' + callEvent.callEventId + ' 开始通话');
		//修改呼叫信息
		var _caller = _bpoFlag?getTrueNum(callEvent.event.caller):callEvent.event.caller;
		var _called = _bpoFlag?getTrueNum(callEvent.event.called):callEvent.event.called;
		updateAgentInfoText('caller', _caller);
		updateAgentInfoText('called', _called);

		ccbar_status.callEventId = callEvent; //缓存当前的callEvent
		ccbar_status.current.caller = _caller;
		ccbar_status.current.called = _called;
		//智能外呼,调用进入话务状态
		if(callEvent.event.createCause == '8'){
			ccbar_plugin.callControl.workNotReady()
		}
		
		if(callEvent.urls&&callEvent.urls.length>0){
			for (var i = 0; i < callEvent.urls.length; i++) {
				var url = callEvent.urls[i].url;
				var prarms ='custInfo='+ JSON.stringify(callEvent.custInfo);
				if (url.indexOf("?") > -1) {
					url = url+'&'+prarms
				}else{
					url = url+'?'+prarms
				}
				callEvent.urls[i].type == '1'?_createPage({url:url,title:callEvent.urls[i].title,id:callEvent.urls[i].id}):window.open(url,callEvent.urls[i].title);
			}
		}
		
	} else if (callEvent.callEventId == 'evtDisConnected') {
		consoleLog('callEvent.callEventId=' + callEvent.callEventId + '   呼叫结束');
		audio_player.play('/yc-ccbar/ccbar/call_end.mp3');
		updateAgentInfoText('caller', '');
		updateAgentInfoText('called', '');
		$('#agentsel').fadeOut();
		if(callEvent.clearCause && callEvent.clearCause!='0'){
            tipsMsg('呼叫失败！')
        }
	}
}
//功能事件消息相关
function onRecvNotifyEvent(Notify){
	var respFuncList = {
		respLogin:'logon',
		respLogout:'logoff',
		respMakeCall:'makecall',
		respAnswerCall:'answercall',
		respClearCall:'clearcall',
		respReady:'setready',
		respNotReady:'setnotready',
		respWorkReady:'workReady',
		respWorkNotReady:'workNotReady',
	}
	if(Notify.result == 'Succ'){
		
		 switch(Notify.srcMessageId){
            //签入
            case 'respLogin' : 
                tipsMsg('签入成功');
                var t = $("#ccbarLogin [data-ccbartype='logon']");
                t.removeClass('disabled').text('签入');
                clearTimeout(ccbarCallClock);
             break;
            case 'respLogout' : CallControl.closeSIP(); break;
            case 'respTransferCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg('发起呼叫转移成功');
                }
                $('#agentsel').fadeOut();
                break;
            case 'respConsultCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg('发起咨询成功');
                }
                $('#agentsel').fadeOut(); 
                
                break;
            case 'respConferenceCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg('发起三方通话成功');
                }
                $('#agentsel').fadeOut();
                break;
        }

	}else{
		switch(Notify.srcMessageId){
			//签入
			case 'respLogin' : 
				var t = $("#ccbarLogin [data-ccbartype='logon']");
				tipsMsg('签入失败：'+Notify.resultDesc);
				t.removeClass('disabled').text('签入');
				clearTimeout(ccbarCallClock);
			 break;
			 default :
				tipsMsg(Notify.resultDesc);
		}
	}
}

function onEventSessionTimeout(data){
	layer.alert(data.content,{
		yes: function(index, layero){
		   location.reload()
		  }
	});
}

ccbarEvent.addEvent('logoff', function(event) { if(event.result == 'Fail'){return}CallControl.isLogoned = false;$("#workMode").hide(); $("[data-ccbartype='workMode']").removeClass("active");});
ccbarEvent.addEvent('logon', function(event) { if(event.result == 'Fail'){return}CallControl.isLogoned = true; $("#workMode").show();});

// ccbarEvent.addEvent('evtAltering', function(event) {
// 	consoleLog('alerting:')
//     if (event.createCause == 8) { CallControl.answerCall(); } 
// });