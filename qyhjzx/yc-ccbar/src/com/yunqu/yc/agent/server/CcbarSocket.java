package com.yunqu.yc.agent.server;


import javax.websocket.*;


import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.alarm.MarsAlarm;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.ICCSLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.model.SkillGroupModel;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.model.RequestDataV1;
import com.yunqu.yc.agent.servlet.AgentEventServlet;
import com.yunqu.yc.agent.util.CacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint("/ccbarws/{username}")  
public class CcbarSocket {
	
	/**
	 * 保持agentId 和  CcbarSocket的对应关系
	 */
	private static Map<String, CcbarSocket> clients = new ConcurrentHashMap<String, CcbarSocket>(); 
	private Session session;
	private String username;
	private String agentId;
	private String cuid;
	private Integer agentSequence = 0;
	private long  heartbeatTime = System.currentTimeMillis();
	private YCUserPrincipal principal;
	private static long collectionTime = System.currentTimeMillis();
	private static Set<String> exceptionList = new HashSet<String>();

	private String entId;
	 
	public static CcbarSocket getSocket(String agentId){
			return clients.get(agentId);
	}
	
	/**
	 * 判断当前连接是否正常
	 * @return
	 */
	public boolean isOk(){
		long timer = System.currentTimeMillis() - this.heartbeatTime;
		if(this.session.isOpen() &&  timer < 30*1000) return true;
		return false;
	}
	 
    @OnOpen
    public void onOpen(@PathParam("username") String username, Session session) throws IOException {  

         this.username = username;  
         this.session = session;  
         YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
         if(principal==null){
        	CcbarLogger.getLogger().warn("[websocket]["+agentId+"] WebSocket.event.onOpen("+username+") fail,cause:principal is null!");  
        	ICCSLogger.getLogger().warn( "[websocket]["+agentId+"] WebSocket.event.onOpen("+username+") fail,cause:principal is null!");  
         	JSONObject result = new JSONObject();
         	result.put("error", "session timeout！");
         	session.getAsyncRemote().sendText(result.toJSONString());
         	session.close();
         	return;
         }
         
        
         
         this.principal = principal;
         this.agentId 	= principal.getLoginAcct();
         this.entId 	= principal.getEntId();
         
         CcbarSocket  _ccbarSocket = clients.get(this.agentId);
         
         //将原来的ws进行关闭处理。
         if(_ccbarSocket != null) _ccbarSocket.distory();
         
         clients.put(this.agentId, this);  
         CcbarLogger.getLogger().info("[websocket]["+agentId+"] WebSocket.event.onOpen("+username+") started!");  
         ICCSLogger.getLogger().info( "[websocket]["+agentId+"] WebSocket.event.onOpen("+username+") started!");  
         
         long socketTime = System.currentTimeMillis();
         
         try {
			Thread.sleep(2*1000);
		 } catch (Exception e) {
			// TODO: handle exception
		 }
         
         AgentModel agentModel = AgentInfos.getAgentInfo(this.agentId);
         
         
         if(this.username.indexOf("-")>-1){
        	 this.cuid = this.username.split("-")[1];
         }
         
         if(StringUtils.isNotBlank(this.cuid)){
        	 if(!this.cuid.equalsIgnoreCase(agentModel.getCuid())){
        		 CcbarLogger.getLogger().info("[websocket]["+agentId+"] 坐席已经在其他的电脑上进行签入，不对当前的Websocket进行处理。");  
        		 ICCSLogger.getLogger().info("[websocket]["+agentId+"] 坐席已经在其他的电脑上进行签入，不对当前的Websocket进行处理。");  
        		 return;
        	 }
         }
         
         String  message = agentModel.getLastAgentStateMessage(socketTime);
         if(StringUtils.isNotBlank(message)){
        	 synchronized (this.session) {
         		this.session.getBasicRemote().sendText(message);  
     		}
         }
    }
         
    
    @OnClose  
    public void onClose() throws IOException {  
    	if(clients.containsKey(this.agentId)){
    		try {
    			long timer = System.currentTimeMillis() - collectionTime;
    			if(timer > 300*1000){
    				exceptionList.clear();
    				timer = System.currentTimeMillis();
    			}
    			JSONObject  websocketInfo = new JSONObject();
        		websocketInfo.put("agentId",this.agentId);
        		websocketInfo.put("time", EasyCalendar.newInstance().getDateTime("-"));
        		exceptionList.add(websocketInfo.toJSONString());
        		if(exceptionList.size()>10){
        			MarsAlarm.websocketAlarm(timer, exceptionList.size(), JSONObject.toJSONString(exceptionList));
        		}
        		
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex,ex);
			}
    		
    		CcbarLogger.getLogger().info("[websocket]["+agentId+"] WebSocket.event.onClose("+username+") websocket通信异常关闭!");  
    		ICCSLogger.getLogger().info( "[websocket]["+agentId+"] WebSocket.event.onClose("+username+") websocket通信异常关闭!");  
    	}else{
    		CcbarLogger.getLogger().info("[websocket]["+agentId+"] WebSocket.event.onClose("+username+") 坐席正常签出关闭!");  
    		ICCSLogger.getLogger().info( "[websocket]["+agentId+"] WebSocket.event.onClose("+username+") 坐席正常签出关闭!");  
    	}
    	if(StringUtils.isBlank(this.agentId)) return;
    	clients.remove(this.agentId);  
    }  
  
    @OnMessage  
    public void onMessage(String message) throws IOException {
    	
    	if(ServerContext.isDebug()){
	    	CcbarLogger.getLogger().info("[websocket]["+agentId+"] WebSocket.event.onMessage("+username+") << "+message);  
	    }
    	//this.sendHeartbeat();  //发送心跳到iccs
    	JSONObject msgObject = JSONObject.parseObject(message);
    	EasyResult result = EasyResult.ok();
    	if (msgObject == null) {
			result = EasyResult.fail("Valid json format,json->"+message);
			try {
				sendMessageToAgent("resp",agentId,result.toJSONString());
			} catch (Exception ex) {
				CcbarLogger.getLogger().error("[websocket]["+agentId+"] resp message error ,casue:" + ex.getMessage(), ex);
			}
		}
    	
    	/**
    	 * 处理ws心跳信息
    	 * {"event":"heartbeat","action":"sendMessage","cmdJson":"{\"msgType\":\"heartbeat\"}"}
    	 */
    	
    	String messageId = StringUtils.trimToEmpty(msgObject.getString("messageId"));
    	
    	String event = StringUtils.trimToEmpty(msgObject.getString("event"));
    	
    	if("heartbeat".equalsIgnoreCase(event)){
    		AgentModel agent = AgentInfos.getAgentInfo(agentId);
			agent.setLastHeartTime();
    		long timer = System.currentTimeMillis() - heartbeatTime;
    		//如果15S没有心跳，则发送心跳。
    		if(timer>20*1000){
    			this.sendHeartbeat();
    		}
    		this.sendWebsocketHeartbeat();
    	}
    	
    	if(!messageId.toLowerCase().startsWith("cmd")) return;
    	
		try {
			result = AgentEventServlet.doEvent(principal.getEntId(), principal.getBusiId(),principal.getBusiOrderId(), message, this.session.getId(), "socket", msgObject);
			sendMessageToAgent("resp",agentId,result.toJSONString());
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("[websocket]["+agentId+"] resp message error ,casue:" + ex.getMessage(), ex);
		}
		
    }  
    
    
    public  void  sendHeartbeat(){
    	try {
			AgentModel agent = AgentInfos.getAgentInfo(agentId);
			//如果非在线状态，不再发送心跳。
			if(!agent.isOnline()) return;
			String key = agent.getSessionId()+"_"+System.currentTimeMillis();
			CcbarLogger.getLogger().info("[websocket]["+agentId+"][heartbeart-thread] sendHeartbeat,CacheUtil.put("+Constants.VOICE_LOGIN_KEY  + agentId+",30) -> "+key);
			CacheUtil.put(Constants.VOICE_LOGIN_KEY  + agentId, key , 30);
			RequestDataV1 requestDataModel = new RequestDataV1();
			requestDataModel.setCommand("cmdHeartBeat");
			requestDataModel.setEntId(agent.getEntId());
			requestDataModel.setAgentId(agentId);
			requestDataModel.setTimestamp(System.currentTimeMillis() + "");
			requestDataModel.setVersion("v1");
			EventFactory.handleMessage(requestDataModel);
		} catch (Exception ex) {
			ICCSLogger.getLogger().error("[websocket]["+agentId+"][heartbeart-thread] sendHeartbeat to iccs error ,casue:" + ex.getMessage(), ex);
			CcbarLogger.getLogger().error("[websocket]["+agentId+"][heartbeart-thread] sendHeartbeat to iccs error ,casue:" + ex.getMessage(), ex);
		}
    	this.heartbeatTime = System.currentTimeMillis();
    }
    
    public static Set<String> getOnlineAgents(){
    	return new HashSet<String>(clients.keySet());
    }
    
    /**
     * 销毁当前的websocket
     */
    public void distory(){
    	CcbarLogger.getLogger().error("[websocket]["+agentId+"] WebSocket.distory() 关闭已经失效的WebSocket... ");  
    	try {
    		this.session.close();
		} catch (IOException ex) {
			CcbarLogger.getLogger().error("[websocket]["+agentId+"] WebSocket.distory() error,cause:"+ex.getMessage(),ex);  
		}
    	try {
    		Thread.sleep(3000);
		} catch (Exception e) {
			// TODO: handle exception
		}
    	clients.remove(this.agentId);
    }

    public String getEntId(){
    	return this.entId;
    }
    
	 public YCUserPrincipal getPrincipal() {
		return principal;
	 }
    
    /**
	 * 获得技能监控信息
	 * @param groups
	 * @return
	 */
	public static JSONObject getMonitorEvent(String agentId){
		
		AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
		if(agentModel.ownerGroups() == null) return null;
		Collection<SkillGroupModel> groups = agentModel.ownerGroups();
		JSONObject monitor = new JSONObject();
		monitor.put("agentId", agentModel.getAgentId());
		monitor.put("phoneNum", agentModel.getPhoneNum());
		monitor.put("bizSessionId", System.currentTimeMillis());
		monitor.put("entId", agentModel.getEntId());
		monitor.put("messageId", "monitor");
		monitor.put("version", "v1");
		monitor.put("sequence", "0");
		try {
			
			//fix by tzc ,20211025 如果坐席在线，并且登录持续时间>30秒，才推送话机状态。防止登录后话机还没有拉起的情况。
			if(agentModel.isOnline() && (System.currentTimeMillis() - agentModel.getOnlineTime() > 30*1000)){				
				//status -- online|offline|ringing|talking    （判断是否online状态，需结合lastActiveTime字段，如果lastActiveTime超时，则是offline状态） 
				if(StringUtils.isNotBlank(agentModel.getPhoneNum())){
					String phoneStatus = StringUtils.trimToEmpty(CacheUtil.hget("ibac_"+agentModel.getPhoneNum(), "status"));
					monitor.put("phoneStatus", phoneStatus);
				}
			}
		} catch (Exception ex) {
		}
		
		int totalQueueCount = 0;
		JSONArray  groupMonitors = new JSONArray();
		
		String groupType = Constants.getMonitorGroupType();
		//if(StringUtils.isBlank(groupType)) groupType = "all";
		
		for (SkillGroupModel skillGroup:groups) {
			//CcbarLogger.getLogger().info("AgentMonitor,group["+groupType+","+skillGroup.getType()+"] -> groupId:"+skillGroup.getSkillGroupId()+",groupName:"+skillGroup.getSkillGroupName());
			JSONObject queueObj  = CacheUtil.get("ICCS_SKILLGROUP_"+skillGroup.getSkillGroupId());//技能组监控对象
			if(queueObj == null){
				Map<String,String> map = CacheUtil.hgetAll("ICCS_OCS_"+skillGroup.getSkillGroupId());
				if(map.size()>0){
					queueObj = JSONObject.parseObject(JSONObject.toJSONString(map));
				}
			}
			if(queueObj == null) continue;
			if(StringUtils.isNotBlank(groupType)){
				if(!groupType.equalsIgnoreCase(skillGroup.getType())) continue;
			}
			queueObj.put("skillGroupName",skillGroup.getSkillGroupName());//技能组名称，todo:由于缓存中的数据是乱码，这里直接读数据库的
			groupMonitors.add(queueObj);
			totalQueueCount = totalQueueCount + queueObj.getIntValue("queueCallCount");
		}
		JSONObject cmddata = new JSONObject();
		cmddata.put("totalQueueCount", totalQueueCount);
		cmddata.put("groupMonitors", groupMonitors);
		monitor.put("cmdata", cmddata);
		return monitor;
	}
    
    
    private boolean  isValidMessage(String message){
    	JSONObject eventObject = JSONObject.parseObject(message);
    	if("agentStateSync".equals(eventObject.getString("messageId"))){
			Integer msgSequence= eventObject.getInteger("sequence");
			if(msgSequence == null) msgSequence = 0;
			if(agentSequence - msgSequence  >  9999999 ) {
				agentSequence = 0;
			}
			if( agentSequence > msgSequence){
				if(ServerContext.isDebug()){
					CcbarLogger.getLogger().info("[DEBUG] agentId["+agentId+"]  msgSequence["+msgSequence+"] less then agentSequence["+agentSequence+"].");
				}
				return false;
			}
			agentSequence = msgSequence;
		}
    	return true;
    }
    
    /**
     * 发送心跳给web
     * @param agentId
     * @throws IOException
     */
    public  void sendMonitor() throws IOException {
    	
    	JSONObject  resp = new JSONObject();
		try {
			JSONObject monitorEvent = getMonitorEvent(this.agentId);
			if(monitorEvent == null) monitorEvent = new JSONObject();
			resp.put("messageId", "monitor");
			sendMessageToAgent("monitor",this.agentId,monitorEvent.toJSONString());
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("<" + this.agentId + "> send monitor to web error ,casue:" + ex.getMessage(), ex);
		}
    }  
    
    
    public  void sendWebsocketHeartbeat() throws IOException {
    	
    	JSONObject  heartbeat = new JSONObject();
		try {
			heartbeat.put("messageId", "heartbeat");
			sendMessageToAgent("heartbeat",this.agentId,heartbeat.toJSONString());
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("<" + this.agentId + "> send heartbeat to websocket error ,casue:" + ex.getMessage(), ex);
		}
    }  
    
    /**
     * 发送消息到坐席
     * @param msgType 消息类型
     * @param agentId	 坐席工号
     * @param message 消息内容，JSON格式
     * @throws Exception
     */
    public static  void sendMessageToAgent(String msgType,String agentId,String message) throws Exception {  
    	
    	JSONObject jsonObject = JSONObject.parseObject(message);
    	String messageId = jsonObject.getString("messageId");
    	String srcMessageId = "";
    	JSONObject cmdJson = jsonObject.getJSONObject("cmddata");
    	if(cmdJson != null){
    		srcMessageId  = cmdJson.getString("srcMessageId");
    	}else{
    		cmdJson = new JSONObject();
    	}
    	if(StringUtils.isBlank(srcMessageId)){
    		srcMessageId =  messageId;
    	}
    	if("heartbeat".equals(msgType)||"monitor".equals(msgType)){
    		if(ServerContext.isDebug()) {
    			CcbarLogger.getLogger().info( "[DEBUG][websocket]["+agentId+"]["+srcMessageId+"] >> "+message );
    		}
    	}else{
    		
    		if("evtAgentState".equalsIgnoreCase(srcMessageId)){
    			CcbarLogger.getLogger().info( "[websocket]["+agentId+"]["+srcMessageId+"]["+cmdJson.getString("state")+"] >> "+message );
        		ICCSLogger.getLogger().info(  "[websocket]["+agentId+"]["+srcMessageId+"]["+cmdJson.getString("state")+"] >> "+message );
    			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
    			agentModel.setLastAgentStateMessage(message);
    		}else{
    			CcbarLogger.getLogger().info( "[websocket]["+agentId+"]["+srcMessageId+"] >> "+message );
        		ICCSLogger.getLogger().info(  "[websocket]["+agentId+"]["+srcMessageId+"] >> "+message );
    		}
    	}
    	long timer = System.currentTimeMillis();
    	try {
    		sendMessage(msgType,agentId,message);
		} catch (Exception ex) {
			CcbarLogger.getLogger().error( "[websocket]["+agentId+"]["+srcMessageId+"] 消息发送失败,原因:"+ex.getMessage()+",msgObj -> "+message,ex);
		}
    	timer = System.currentTimeMillis()-timer;
    	if(timer > 1000l){
    		CcbarLogger.getLogger().warn( "[websocket]["+agentId+"]["+srcMessageId+"] 消息发送耗时[ "+timer+"ms],msgObj -> "+message);
    	}
    	if("respLogout".equalsIgnoreCase(srcMessageId)){
    		CcbarSocket ws = clients.remove(agentId);
    		if(ws!=null){
	    		ws.session.close();
	    		CcbarLogger.getLogger().info( "[websocket]["+agentId+"]["+srcMessageId+"] 接收到ICCS的respLogout，关闭当前坐席websocket!");
    		}
    	}
    }
    
    private static  void sendMessage(String msgType,String agentId,String message) throws Exception {  
    	CcbarSocket  item = null;
    	try {
    		for(int i = 0 ;i< 2;i++){
    			item = clients.get(agentId);
    			if(item == null){
    				ICCSLogger.getLogger().info( "[websocket]["+agentId+"]获取不到该坐席对应的Websocket链接，消息内容->"+message);
        			Thread.sleep(1000l);
        			continue;
        		}
    			break;
    		}
		} catch (Exception ex) {
			// TODO: handle exception
		}
    	if(item == null) {
    		CcbarLogger.getLogger().info( "[websocket]["+agentId+"]发送消息失败，原因：websocket已经断开，消息内容->"+message);
    		return ;
    	}
    	if(!item.isValidMessage(message)) return;
    	if(!item.session.isOpen()){
    		CcbarLogger.getLogger().info( "[websocket]["+agentId+"]["+msgType+"] >> 发送消息失败，当前websocket.isOpen()->false,websocket已关闭.");
    		throw new Exception("websocket is close!");
    	}
    	synchronized (item.session) {
    		item.session.getBasicRemote().sendText(message);  
		}
    }
  
    @OnError  
    public void onError(Session session, Throwable error) { 
    	CcbarLogger.getLogger().info("[websocket]["+agentId+"] WebSocket.event.onError("+username+") "+error.getMessage(),error);  
    	ICCSLogger.getLogger().info( "[websocket]["+agentId+"] WebSocket.event.onError("+username+") "+error.getMessage(),error);  
    }  
  

}