<!DOCTYPE html>
<html>
<head>
    <title i18n-content="详情"></title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="description" content="">

    <link rel="stylesheet" href="js/weui/css/weui.min.css">
    <link rel="stylesheet" href="js/weui/css/jquery-weui.min.css">
    <link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
    <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
</head>

<body ontouchstart>
<div class="layui-col-md12">
    <div class="layui-card">
        <div style="margin-top: 400px;
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: red;" i18n-content="添加成功">
        </div>
    </div>
</div>
<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
<script type="text/javascript" src="js/qt.js"></script>
<script type="text/javascript" src="/cc-qs/static/js/my_i18n.js"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
<script type="text/javascript">
	$(function(){
		//防止页面后退
		history.pushState(null, null, document.URL);
		window.addEventListener('popstate', function () {
			history.pushState(null, null, document.URL);
		});
	});
</script>
</body>
</html>
