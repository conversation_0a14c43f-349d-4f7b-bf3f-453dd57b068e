package com.yunqu.project.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.project.base.AppDaoContext;

@WebObject(name = "noticeOperLog")
public class NoticeOperLogDao extends AppDaoContext {

	@WebControl(name="noticeOperLogList",type=Types.LIST)
	public JSONObject noticeOperLogList(){
		EasySQL easySQL = this.getEasySQL("select * from C_NOTICE_OPER_LOG where 1=1");
		easySQL.append(param.getString("OPER_TYPE"), "and OPER_TYPE = ?");
		easySQL.appendLike(param.getString("AGENT_NAME"), "and AGENT_NAME like ?");
		JSONObject queryForPageList = this.queryForPageList(easySQL.getSQL(), easySQL.getParams(),null);
		return queryForPageList;
	}

}
