<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>草稿箱</title>
	<link href="/easitline-static/lib/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
	<!-- <script type="text/javascript" src="/easitline-static/lib/umeditor/third-party/jquery.min.js"></script> -->
	<script type="text/javascript" src="/easitline-static/lib/umeditor/third-party/template.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="/easitline-static/lib/umeditor/umeditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="/easitline-static/lib/umeditor/umeditor.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/umeditor/lang/zh-cn/zh-cn.js"></script>
	
	<script src="/easitline-static/lib/umeditor/dialogs/link/link.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/image/image.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/video/video.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/map/map.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/formula/formula.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/emotion/emotion.js" type="text/javascript" defer="defer"></script>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="noticeInfo.getNoticeInfo" data-pk="${param.noticeId}" method="post"  autocomplete="off" data-mars-prefix="noticeInfo." >
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <!-- <tr>
			                      <td>收件人：</td>
			                      <td colspan="3">yyy</td>
		                     </tr> -->
		                     <tr>
			                      <td width="60px">收件人：</td>
			                      <td width="200px"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.NOTICE_RECEIVER"/></td>
			                      <td width="60px">抄送：</td>
			                      <td width="200px"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.COPY_PERSON"/></td>
		                     </tr>
		                     <tr>
			                      <td>类型：</td>
			                      <td>
			                      	<select class="form-control input-sm" name="noticeInfo.TYPE_ID">
			                      		<option value="">请选择</option>
			                      		<option value="1">普通公告</option>
			                      	</select>
			                      </td>
			                      <td>发布时间：</td>
			                      <td><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.SEND_TIME"/></td>
			                 </tr>
			                 <tr>
			                      <td>主题：</td>
			                      <td colspan="3"><input type="text" disabled="disabled" class="form-control input-sm" name="noticeInfo.NOTICE_TITLE"/></td>
		                     </tr>
		                     <tr>
		                          <td>内容：</td>
			                      <td colspan="3">
			                          <div id="myEditor" name="noticeInfo.NOTICE_CONTENT" style="height:300px"></div>
			                      </td>
		                     </tr>
	                    </tbody>
	              </table>
	              <div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="">席间交流</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("noticeInfoEdit");
	 window.UMEDITOR_CONFIG.toolbar = [
	                                      'source | undo redo | bold italic underline strikethrough | superscript subscript | forecolor backcolor | removeformat |',
	                                      'insertorderedlist insertunorderedlist | selectall cleardoc paragraph | fontfamily fontsize' ,
	                                      '| justifyleft justifycenter justifyright justifyjustify |',
	                                      'link unlink | emotion',
	                                      '| horizontal print preview fullscreen'
	                                  ];

	    var um = UM.getEditor('myEditor');
	    um.setDisabled('fullscreen');
	$(function() {
		$("#editForm").render();
	});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>