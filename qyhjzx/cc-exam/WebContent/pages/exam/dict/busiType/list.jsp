<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="业务类型管理"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
					
				<div class="form-group">
					<h5>
						<span i18n-content="业务类型管理"></span>
						<span id="titleAndTime"> </span>
					</h5>
					<div class="input-group pull-right">
						<button type="button" class="btn btn-sm btn-success btn-outline" onclick="BusiType.add('','')"><span i18n-content="新增"></span> </button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<table class="layui-table layui-form" id="tree-table" lay-size="sm"></table>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		//var treeTable;
		var BusiType = {
			query : function() {
				$(".layui-card-body").empty();
				$(".layui-card-body").append('<table class="layui-table layui-form" id="tree-table" lay-size="sm"></table>');
				renderTable();
			},
			add : function(pid,pname) {
				popup.layerShow({type:1,title:getI18nValue('新增业务类型'),offset:'20px',area:['530px;','350px']},"${ctxPath}/pages/exam/dict/busiType/edit.jsp",{PID:pid,PNAME:pname});
			},
			edit : function(id,pname) {
				popup.layerShow({type:1,title:getI18nValue('编辑业务类型'),offset:'20px',area:['530px;','350px']},"${ctxPath}/pages/exam/dict/busiType/edit.jsp",{ID:id,PNAME:pname});
			},
			delRecord : function(obj) {
				if(obj) {
					layer.confirm(getI18nValue('是否确定删除当前数据？'),{icon: 3, title:getI18nValue('确定提示'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},  function(index){
						var url = "${ctxPath}/servlet/busiType?action=delRecord";
						ajax.remoteCall(url,{ID:obj},function(result) {
							var state = result.state;
							if(state=='1') {
								layer.msg(result.msg,{icon: 1,time:1200});
								BusiType.query();
							} else {
								layer.alert(result.msg,{icon: 5});
								return;
							}
						});
					});
				}
			}
		}

		layui.config({
			base: '/cc-exam/static/js/',
		});
		var renderTable = function() {
			var name = $("#name").val();
			ajax.daoCall({controls: ['busiType.getList'], params: {NAME:name}}, function (result) {
				var data = result['busiType.getList'] ? result['busiType.getList'].data : {};
				data = formartData(data);
				loadTable(data);
			});
		}
		var loadTable = function(datas) {
			layui.use(['treeTable','layer','code','form'],function(){
				var o = layui.$,
					form = layui.form,
					layer = layui.layer,
					treeTable = layui.treeTable;
				var	re = treeTable.render({
					elem: '#tree-table',
					data:datas,
					icon_key: 'NAME',
					end: function(e){
						form.render();
					},
					cols: [
						{
							key: 'NAME',
							title: getI18nValue('名称'),
							template: function(item){
								return '<span>'+item.NAME+'</span>';
							}
						},
						{
							key: 'REMARK',
							title: getI18nValue('描述'),
							align: 'center',
						},
						{
							title: getI18nValue('操作'),
							align: 'center',
							template: function(item){
								var id = item.ID;
								var name = item.NAME;
								var pname = item.PNAME
								var btn1 = '<a href="javascript:void(0)" onclick="BusiType.add(\''+id+'\',\''+name+'\')">'+getI18nValue("添加")+'</a> ';
								var btn2 = ' <a href="javascript:void(0)" onclick="BusiType.edit(\''+id+'\',\''+pname+'\')">'+getI18nValue("编辑")+'</a> ';
								var btn3 = ' <a href="javascript:void(0)" onclick="BusiType.delRecord(\''+id+'\')">'+getI18nValue("删除")+'</a>';
								if(item.id.length>=9){//第三层后不再添加子层
									return btn2 +"-"+ btn3;
								}
								return btn1 +"-"+ btn2 +"-"+ btn3;
							}
						}
					],
				});
			})
		}

		var formartData = function(data) {
			var array = [];
			if(data) {
				for (var i = 0; i < data.length; i++) {
					var d = data[i];
					d['id'] = d.ID;
					d['pid'] = d.PID;
					array.push(d);
				}
			}
			return array;
		}

		$(function(){
			renderTable();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>