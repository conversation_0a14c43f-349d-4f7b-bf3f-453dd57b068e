package com.yunqu.cc.exam.dao;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.cc.exam.base.AppDaoContext;


@WebObject(name="UserDao")
public class UserDao extends AppDaoContext{

	@WebControl(name="agentList",type=Types.LIST)
	public  JSONObject agentList(){
		EasySQL sql = agentListSql(param, getDbName(), getEntId(), getBusiOrderId());
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		if(result!=null){
			JSONArray dataArray = result.getJSONArray("data");
			if(dataArray!=null){
				for(int i = 0 ;i<dataArray.size(); i++){
					JSONObject data = dataArray.getJSONObject(i);
					data.put("NICK_NAME", "");
					
					JSONObject extConf = data.getJSONObject("EXT_CONF");
					if(extConf!=null && StringUtils.isNotBlank(extConf.getString("NICK_NAME"))){
						data.put("NICK_NAME", extConf.getString("NICK_NAME"));
					}
					
					//获取用户登录状态，在获取用户通知的地方通过心跳方式写入，缓存60s  NOticeServlet.actionForSearchUserNotice
					//获取用户登录状态
					JSONObject j = new JSONObject();
					j.put("userAcc", data.getString("USER_ACCT"));
					j.put("command", "srhUserLoginState");
					JSONObject r = ServiceUtil.invoke("CCBASE_CCUSER_INTERFACE", j);
					String loginState = "N";
					if(r!=null){
						loginState = r.getString("loginState");
					}
					
					data.put("LOGIN_STATE", loginState);
				}
			}
			result.put("data", dataArray);
		}
		return result;
	}
	
	/**
	 * 用户管理的sql
	 * @param param
	 * @param schema
	 * @param entId
	 * @param busiOrderId
	 * @return
	 */
	public EasySQL agentListSql(JSONObject param,String schema, String entId, String busiOrderId) {
		EasySQL sql = new EasySQL("select t1.USER_ID ,t1.AGENT_NAME,t1.MAX_SERVER, t1.ENT_ID ,t1.BUSI_ORDER_ID,t1.USER_STATE,t4.DEPT_NAME,t1.GROUP_LIST,t1.ROLE_LIST,t1.EXT_CONF ");
		sql.append(" ,t2.USERNAME, t2.USER_ACCT,t2.AGENT_PHONE,t2.EMAIL,t2.SSO_ACCT,t2.SEX,t5.LOGIN_LOCK,t2.MOBILE,t2.SALES_CODE,t2.F_PHONE_NUM ");
		sql.append(" ,t2.LOGIN_TIME,t2.LAST_LOGIN_IP,t1.ROLE_ID,t1.PREFIX_NUM,t1.INBOUND,t1.OUTBOUND,'' ROLE_NAME,'' ROLE_TYPE ");
		sql.append("from");
		sql.append(schema+".CC_BUSI_USER t1 ");
		sql.append("left join CC_USER t2 on t1.USER_ID = t2.USER_ID ");
		sql.append("left join "+schema+(".V_CC_DEPT_USER")+" t4 on t1.USER_ID=t4.USER_ID and t4.BUSI_ORDER_ID = t1.BUSI_ORDER_ID");
		sql.append("left join EASI_USER_LOGIN_SECURITY t5 on t5.USER_ACCT = t2.USER_ACCT");
		sql.append(" where 1 = 1 and t2.ADMIN_FLAG = 0  and t2.USER_STATE =0 ");
		
		sql.append(param.getString("userState")," and t1.USER_STATE = ? ");
		sql.appendLike(param.getString("USER_ACCT")," and ( t2.USER_ACCT LIKE ? OR t1.AGENT_NAME LIKE ? OR t1.AGENT_PHONE LIKE ?) " ); //3.1#20210521-1 使用账号查询时会关联出其他企业账号的问题
		sql.append(busiOrderId," and t1.BUSI_ORDER_ID = ? "); 
		sql.append(entId," and t1.ENT_ID = ? ");
		sql.appendLike(param.getString("agentName"), "and t1.AGENT_NAME like ? ");
		sql.appendLike(param.getString("agentPhone"), "and t2.AGENT_PHONE like ? ");
		sql.append(param.getString("lockState"), " AND t2.LOCK_STATE = ? ");
		sql.append(param.getString("roleId"), " AND EXISTS (SELECT 1 FROM "+schema+(".V_CC_USER_ROLE")+" t3 where t3.USER_ID = t1.USER_ID and t3.ROLE_ID=?) ");
		//sql.appendLike(param.getString("agentGroup"), "and t1.GROUP_LIST like ? ");
		//sql.appendLike(param.getString("userAcct"), "and t2.USER_ACCT like ? ");
		String KEY_WORD = param.getString("KEY_WORD");
		if (StringUtils.notBlank(KEY_WORD)) {
			sql.append(" and (");
			sql.appendLike(KEY_WORD,"t1.AGENT_NAME like ?");
			sql.appendLike(KEY_WORD," or t2.USER_ACCT like ?");
			sql.appendLike(KEY_WORD,"or t2.USERNAME like ?");
			sql.appendLike(KEY_WORD,"or t2.AGENT_PHONE like ?");
			sql.append(")");
		}
		
//		sql.appendRLike(param.getString("skillName"), "and t4.DEPT_NAME like ? ");
		sql.appendRLike(param.getString("deptCode"), "and t4.DEPT_CODE like ? ");
		
		sql.appendLike(param.getString("USER_ACCT"), "and t2.USER_ACCT LIKE ? ");
		
		if(StringUtils.isNotBlank(param.getString("userId"))) {
			sql.append("and t1.USER_ID IN (\'"+param.getString("userId").replaceAll(",", "\',\'")+"\')");
		}
		
		String sortType = param.getString("sortType");
		if (StringUtils.isNotBlank(sortType)) {
			sql.appendSort(param.getString("sortName"), sortType, "t2.AGENT_PHONE");
		}
		return sql;
	}
	
}
