<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="全媒体小结分析"></title>
		<style>
		    .blank-page{padding: 20px 40px;overflow-y: hidden;border: solid 1px #cccccc; background-color: #ffffff;margin: 20px auto;box-shadow: -2px -2px 7px #cccccc;width:1200px;margin-bottom: 100px}
        .blank-page .header-title{text-align:center;color:#333;font-size:19px}
        .blank-page .p-title{border-bottom:1px solid #cccccc;font-size:16px;color:#555;margin-top:30px;margin-bottom:15px}
        .blank-page .p-title>span{border-bottom:2px solid #00a0f0;padding:2px 6px}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-type="3">
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId"> 
				<div class="form-group">
					<h5 i18n-content="全媒体小结分析">
					</h5>			
				</div>
			    <hr style="margin: 5px -15px">
				<div class="form-group">
					<div class="input-group">
						<span class="input-group-addon" i18n-content="小结类型"></span>
						<select name="summaryType" id="summaryType" class="form-control" onchange="summaryStatMedia.onChangeSummaryType()">
							<option value="0" i18n-content="二级小结"></option>
							<option value="1"i18n-content="多级小结-通用树"></option>
							<option value="2" i18n-content="多级小结"></option>
						</select>
					</div>
					<div class="input-group">
						<span class="input-group-addon" i18n-content="版本号"></span>
						<select name="version" id="version" class="form-control input-sm" onchange="summaryStatMedia.onChangeVersion()">
						</select>
					</div>
					<div class="input-group">
						<span class="input-group-addon" i18n-content="小结">小结</span>
						<div id="summaryType1">
						<select name="summary" id="summary" class="form-control input-sm" onchange="summaryStatMedia.onChangeSummary()">
							<option value=""i18n-content="请选择"></option>
						</select>
						</div>
						<div id="summaryType2" style="display: none;">
						<select name="summary1" id="summary1" class="form-control" onchange="summaryStatMedia.onChangeSummary2(1)">
							<option value=""i18n-content="请选择"></option>
						</select>
						<select name="summary2" id="summary2" class="form-control" onchange="summaryStatMedia.onChangeSummary2(2)">
							<option value=""i18n-content="请选择"></option>
						</select>
						<select name="summary3" id="summary3" class="form-control" onchange="summaryStatMedia.onChangeSummary2(3)">
							<option value=""i18n-content="请选择"></option>
						</select>
						<select name="summary4" id="summary4" class="form-control">
							<option value=""i18n-content="请选择"></option>
						</select>
						</div>
					</div>
					<div class="input-group input-group-sm">
				 		<span class="input-group-addon" i18n-content="时间">时间</span>
				        <input name="limitTime" autocomplete="off" type="text" class="layui-input" id="limitTime" style="height:30px;"> 
					</div>
					<!-- <div class="input-group input-group-sm">
						<span class="input-group-addon">开始时间</span> 
						<input  name="startTime" onClick="WdatePicker({minDate:'#F{$dp.$D(\'endTime\',{d:-30});}',maxDate : '#F{$dp.$D(\'endTime\')||\'%y-%M-%d\'}',dateFmt: 'yyyy-MM-dd'})" id="startTime" data-mars-reload="false" id="startDate" data-mars-top="true"  data-mars="common.todayDate"  class="form-control input-sm Wdate">
					</div>
					
					<div class="input-group input-group-sm">
						<span class="input-group-addon">结束时间</span> 
						<input  name="endTime" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startTime\')}',maxDate : '#F{$dp.$D(\'startTime\',{d:30})||\'%y-%M-%d\'}',dateFmt: 'yyyy-MM-dd'})" id="endTime" data-mars-reload="false" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\',{d:0});}',maxDate:'#F{$dp.$D(\'startDate\',{d:14});}'})"  data-mars-top="true"  data-mars="common.tomorrowDate" class="form-control input-sm Wdate">
					</div> -->
					<div class="input-group">
						<span class="input-group-addon" i18n-content="渠道">渠道</span>
						<select name="channelId" class="form-control" data-mars="common.queryMediaChannel">
							<option value=""i18n-content="请选择"></option>
			            </select>
					</div>
					<div class="input-group">
						<span class="input-group-addon" i18n-content="技能组"></span>
						<select name="groupId" class="form-control" data-mars="common.queryMediaSkill" data-mars-top="true">
							<option value=""i18n-content="请选择"></option>
						</select>
					</div>
					<div class="input-group" >
						<button type="button" class="btn btn-sm btn-default" onclick="summaryStatMedia.analyse()">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="分析"></span>
						</button>
					</div>
				</div>
			</div>
		</div>
		<div class="ibox-index" id="stat">
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/summary/include/summaryStat.js"></script>
	<script type="text/javascript">
		jQuery.namespace("summaryStatMedia");
		var ctxPath='${ctxPath}';
		var maxVersion='${maxVersion}';
		$(function(){
			$('[data-toggle="tooltip"]').tooltip();
			requreLib.setplugs('layui',function(){
		  	});
			$("#searchForm").render({success:function(){
				layui.use('laydate', function(){
      			  	var laydate = layui.laydate;
      			  	laydate.render({ elem: '#limitTime' ,type: 'date',range: '~',lang:getDateLang()});
      			})
			}});
			$("#limitTime").data("mars","common.limitDate");
			$("#limitTime").render({data:{},success:function(result){
				//渲染版本下拉框并且选中最大版本
				$("#version").data("mars","common.getSummaryVersion");
				$("#version").render({data:{type:"3"},success:function(result){
					$("#version option[value='"+maxVersion+"']").prop("selected",true);
					$("#version option[value='"+maxVersion+"']").text(getI18nValue("当前版本"));
					//渲染小结下拉框
					$("#summary").data("mars","common.getParentSummary");
					$("#summary").render({data:{version:maxVersion,type:"3"},success:function(result){
						summaryStatMedia.analyse();
					}});
				}});
			}});
		});
		summaryStatMedia.onChangeSummaryType = function(){
			var val = $("select[name='summaryType']").val();
			if(val==0){
				$("#summaryType1").show();
				$("#summaryType2").hide();
				$("#version").html("");
				$("#version").data("mars","common.getSummaryVersion");
	    		$("#version").render({data:{type:"3"},success:function(result){
	    			var returnData=result["common.getSummaryVersion"];
	    			var total=returnData.total
	    			$("#version option[value='"+maxVersion+"']").prop("selected",true);
	    			$("#version option[value='"+maxVersion+"']").text(getI18nValue("当前版本"));
	    			//渲染小结下拉框
	    			$("#summary").data("mars","common.getParentSummary");
	    			$("#summary").render({data:{version:maxVersion,type:"3"},success:function(){
	    				summaryStatMedia.analyse();
	    			}});
	    		}});
			}else{
				$("#version").html("<option value=''>"+getI18nValue("多级小结")+"</option>");
				$("#summaryType2").show();
				$("#summaryType1").hide();
				$("#summary1").data("mars","common.getParentSummary");
				$("#summary1").render({data:{version:"",type:"3"},success:function(){
					summaryStatMedia.analyse();
				}});
				$("#summary2").html("<option value=''>"+getI18nValue("请选择")+"</option>");
				$("#summary3").html("<option value=''>"+getI18nValue("请选择")+"</option>");
				$("#summary4").html("<option value=''>"+getI18nValue("请选择")+"</option>");
			}
		}
		summaryStatMedia.onChangeVersion = function(){
			var val = $("select[name='version']").val();
			$("#summary").data("mars","common.getParentSummary");
			$("#summary").render({data:{version:val,type:"3"},success:function(){
				summaryStatMedia.analyse();
			}});
		}
		
		summaryStatMedia.onChangeSummary = function(){
			summaryStatMedia.analyse();
		}
		summaryStatMedia.onChangeSummary2 = function(level){
			var code="";
			if(level==1){
				code=$("#summary1").val();
				if(!code){
					$("#summary2").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					$("#summary3").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					$("#summary4").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					return;
				}
				$("#summary2").data("mars","common.getChildSummary");
				$("#summary2").render({data:{version:"",type:"3",code:code},success:function(){
				}});
			}else if(level==2){
				code=$("#summary2").val();
				if(!code){
					$("#summary3").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					$("#summary4").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					return;
				}
				$("#summary3").data("mars","common.getChildSummary");
				$("#summary3").render({data:{version:"",type:"3",code:code},success:function(){
				}});
			}else{
				code=$("#summary3").val();
				if(!code){
					$("#summary4").html("<option value=''>"+getI18nValue("请选择")+"</option>");
					return;
				}
				$("#summary4").data("mars","common.getChildSummary");
				$("#summary4").render({data:{version:"",type:"3",code:code},success:function(){
				}});
			}
		}

		summaryStatMedia.analyse = function(){
			var version=$("select[name='version']").val();
// 			if(!version){
//				layer.alert(getI8nValue("请选择小结版本"), {
//					icon: 5,
//					closeBtn: 0,
//					title:getI18nValue("提示"),
//					btn:[getI18nValue("确定")]
//				}, function() {
//					layer.closeAll();
//				});
// 				return false;
// 			}
			var limitTime=$("input[name='limitTime']").val();
			if(!limitTime){
				layer.alert(getI18nValue("请选择时间"),{icon: 3, title:getI18nValue('提示'),btn:[getI18nValue('确定'),getI18nValue('取消')] ,offset:'20px'});
				return false;
			}
			var split=limitTime.split("~");
			var startTime=split[0].substring(0,split[0].length-1);
			var endTime=split[1].substring(1,split[1].length);
			if(!startTime || !endTime){
				layer.alert(getI18nValue("请选择正确的时间"),{icon: 3, title:getI18nValue('提示'),btn:[getI18nValue('确定')]});
				return false;
			}
			var timeDiff = new Date(endTime).getTime() - new Date(startTime).getTime();
		    var dayDiff=Math.floor(timeDiff/(24*3600*1000));
		    if(dayDiff<=0 || dayDiff>31){
		    	layer.alert(getI18nValue("时间需要大于一天并且不能超过一个月"),{icon: 3, title:getI18nValue('提示'),btn:[getI18nValue('确定')] ,offset:'20px'});
				return false;
		    }
			$("#stat").html("");
			if(version){
				var summary=$("select[name='summary']").val();
				if(summary){
					var summaryDesc=$("#summary option:checked").text();
					summaryStatMedia.loadEcharts(version,summary,startTime,endTime,dayDiff,summaryDesc);
				}else{
					$("#summary option").each(function(i,obj){
						var summary=$(this).val();
						if(summary){
							var summaryDesc=$(this).text();
							summaryStatMedia.loadEcharts(version,summary,startTime,endTime,dayDiff,summaryDesc,i);
						}
					})
				}
			}else{
				var summary1=$("select[name='summary1']").val();
				if(!summary1){
					summaryStatMedia.loadEcharts(version,"0",startTime,endTime,dayDiff,getI18nValue("根节点"));
					return;
				}
				var summaryDesc1=$("select[name='summary1'] option:checked").text();
				var summary2=$("select[name='summary2']").val();
				if(!summary2){
					//没有选择二级小结，加载选择得一级小结
					summaryStatMedia.loadEcharts(version,summary1,startTime,endTime,dayDiff,summaryDesc1);
					return;
				}
				var summaryDesc2=$("select[name='summary2'] option:checked").text();
				var summary3=$("select[name='summary3']").val();
				if(!summary3){
					//没有选择三级小结，加载选择得二级小结
					summaryStatMedia.loadEcharts(version,summary2,startTime,endTime,dayDiff,summaryDesc2);
					return;
				}
				var summaryDesc3=$("select[name='summary3'] option:checked").text();
				var summary4=$("select[name='summary4']").val();
				if(!summary4){
					//没有选择四级小结，加载选择得三级小结
					summaryStatMedia.loadEcharts(version,summary3,startTime,endTime,dayDiff,summaryDesc3);
					return;
				}
				var summaryDesc4=$("select[name='summary4'] option:checked").text();
				summaryStatMedia.loadEcharts(version,summary4,startTime,endTime,dayDiff,summaryDesc4);
			}
		}
		summaryStatMedia.loadEcharts = function(version,summary,startTime,endTime,dayDiff,summaryDesc,index){
			var html=inithtml(index);
			$("#stat").append(html);
			var timeDiff = new Date(endTime).getTime() - new Date(startTime).getTime();
		    var dayDiff=Math.floor(timeDiff/(24*3600*1000));
			var obj={
				version:version,
				pcode:summary,
				groupId : $("select[name='groupId']").val(),
				channelId : $("select[name='channelId']").val(),
				startTime : startTime,
				endTime : endTime,
				dayDiff : dayDiff,
			}
			ajax.remoteCall(ctxPath+"/servlet/SummaryStat?action=MediaSummaryRate",{data : obj},function(result) {
				var id="summaryRate";
				if(index){
					id+=index;
				}
				initOptionRate(optionRate,result,id,summaryDesc);
			});
			ajax.remoteCall(ctxPath+"/servlet/SummaryStat?action=MediaSummaryTrend",{data : obj},function(result) { 
				var id="summaryTrend";
				if(index){
					id+=index;
				}
				initOptionTrend(optionTrend,result,id);
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>