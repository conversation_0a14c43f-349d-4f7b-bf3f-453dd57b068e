<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>短信黑名单</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name=" " class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId"> 
				<div class="form-group">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 短信黑名单
					</h5>	
					<div class="input-group pull-right">
						<button type="button" class="btn btn-sm btn-success"
							onClick="CustTemp.saveOrUpdate()">
							<span class="glyphicon glyphicon-plus-sign"></span> 录入
						</button>
					</div>			
				</div>
				<div class="form-group">
					<div class="input-group ">
						<span class="input-group-addon">手机号</span> <input type="text"
							name="PHONENUM" class="form-control input-sm" width="" id="PHONENUM"
							style="width: 110px">
					</div>
					<div class="input-group " style="width: 90px">
						<span class="input-group-addon">状态</span><select  class="form-control input-sm" 
						 style="width:140px"  name="STATUS" id="STATUS" > 
							<option value="">请选择</option>
							<option value="1">启用</option>
							<option value="2">禁用</option>
						</select>
					</div>
					<div class="input-group" >
						<button type="button" class="btn btn-sm btn-default"
							onclick="selectList()">
							<span class="glyphicon glyphicon-search"></span> 查询
						</button>
					</div>
			</div>
			
		<hr style="margin: 5px -15px">
		<div class="form-group">
			
			
		</div>
				</div>
		
		<div class="ibox-content">
			<table
				class="table table-auto table-bordered table-hover table-condensed"
				id="tableHead" data-mars="smsBlackList.blackList" >
				<thead>
					<tr>
						<th>序号</th>
						<th>手机号</th>
						<th>创建时间</th>
						<th>创建人</th>
						<th>启动状态</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="dataList">

				</tbody>
			</table>
			<script id="list-template" type="text/x-jsrender">
				{{for  list}}
					<tr>
						<td>{{:#index+1}}</td>   
						<td>{{:PHONENUM}}</td>
						<td>{{:CREATE_TIME}}</td>                                                                                            
						<td>{{:CREATE_USER_NAME}}</td>                                                                                            
						<td>{{dictFUN:startStatus "STATUS"}}</td>                                                                          
                        <td><a href="javascript:CustTemp.saveOrUpdate('{{:ID}}')">修改</a>-<a href="javascript:CustTemp.delData('{{:ID}}')">删除</a> </td>                                                    
					</tr>
				{{/for}}					         
			</script>
			<div class="row paginate">
				<jsp:include page="/pages/common/pagination.jsp" />
			</div>
		</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("CustTemp");
		$(function() {
			$("#searchForm").render();
		});
				function selectList(){
			$("#searchForm").searchData();
		}
		CustTemp.delData = function(tId) {
			 layer.confirm('确认删除?', 
			            
			            {
			              btn: ['确定','取消'] //按钮
			    
			            }, 
			            
			            function(index){
			            	var data = {};
				data.id = tId;
					ajax.remoteCall("/cc-sms/servlet/smsBlackList?action=Delete", data,
						function(result) {
			    				//debugger;
			    					if(result.state == 1){
			    						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
			    						});
			    						selectList();
			    						}else{
			    						layer.alert(result.msg,{icon: 5});
			    					}
			    				}
			    			);
			              return true;
			            },
			            function(index){
			                layer.msg('已取消！', {icon: 1});
			                return false;
			            }
			        );

	}
		CustTemp.saveOrUpdate = function(id) {
			if ('' == id || id == null) {
				popup.layerShow({
					type : 1,
					title : '短信黑名单管理',
					offset : '20px',
					area : [ '350px', '190px' ]
				}, "${ctxPath}/pages/blackList/smsBlackListAdd.jsp", {});
			} else {
				popup.layerShow({
					type : 1,
					title : '短信黑名单管理',
					offset : '20px',
					area : [ '350px', '190px' ]
				}, "${ctxPath}/pages/blackList/smsBlackListUpdate.jsp?id=" + id, {});

			}
		}
		CustTemp.reset=function(){
	    	$("#PHONENUM").val("");
	    	$("#STATUS").val("");

		};
		
		$.views.converters("startStatus", function(val) {
			if (val == "1") {
				return "启用";
			} else {
				return "禁用";
			}

		});
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>