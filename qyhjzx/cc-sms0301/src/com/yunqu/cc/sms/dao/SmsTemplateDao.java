package com.yunqu.cc.sms.dao;

import java.sql.SQLException;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.sms.base.AppDaoContext;
import com.yunqu.cc.sms.base.CommonLogger;
import com.yunqu.cc.sms.base.Constants;

/**
 * Title:短信模板管理
 * Description:获取短信模板相关信息
 * Company:云趣科技
 * <AUTHOR>
 *
 */
@WebObject(name="smsTemplate")
public class SmsTemplateDao extends AppDaoContext {

	private Logger logger = CommonLogger.getLogger();
	/**
	 * 获取短信模板信息
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb")
	@WebControl(name="templateList",type=Types.LIST)
	public JSONObject templateList(){
		UserModel user = UserUtil.getUser(request);

		if(!UserUtil.canAccess(this.request,user,"cc-tx-sms-dxmb")){
		    JSONObject resultJson = new JSONObject();
		    resultJson.put("msg", "没有权限");
		    resultJson.put("data", new JSONObject());
		    resultJson.put("state", Integer.valueOf(0));
		    return resultJson;
		}
		
		EasySQL sql = this.getEasySQL("SELECT * FROM " + this.getTableName("C_SMS_TEMPLATE") + " WHERE 1=1 ");
		
		String keyWord = param.getString("KEY_WORD");
		if(StringUtils.notBlank(keyWord)) {
			
		sql.append(" AND (");
		sql.appendLike(keyWord, " NAME like ?");
	
		sql.appendLike(keyWord, "or CREATE_ACC LIKE ?");
	
		sql.appendLike(keyWord, "or CREATE_USER_NAME LIKE ?");
	
		sql.append(" ) ");
		
		}
		
		sql.append(this.param.getString("PARENT_ID"),"AND TYPE = ?");
		sql.append(this.param.getString("deptId"),"AND TYPE_ID = ?");
		sql.append(this.param.getString("TYPE"),"AND TYPE_ID = ?");
		sql.append(this.param.getString("status"),"AND STATUS = ?");
		sql.append(this.param.getString("isPublic"),"AND IS_PUBLIC = ?");
		sql.append(this.param.getString("startDate"),"AND CREATE_TIME > ?");
		sql.append(this.param.getString("endDate"),"AND CREATE_TIME < ?");
		
		
		sql.appendLike(this.param.getString("MSG_CODE"),"AND MSG_CODE LIKE ?");

		
		sql.append(getEntId()," AND EP_CODE = ? ",false);
		sql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ",false);
		sql.append("ORDER BY CREATE_TIME DESC");
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) +"获取短信模板,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		}
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		return obj;
	}
	
	/**
	 * 获取短信模板内容
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb")
	@WebControl(name="getTemplateInfo",type=Types.LIST)
	public  JSONObject getTemplateInfo(){
		
		UserModel user = UserUtil.getUser(request);

		if(!UserUtil.canAccess(this.request,user,"cc-tx-sms-dxmb,")){
		    JSONObject resultJson = new JSONObject();
		    resultJson.put("msg", "没有权限");
		    resultJson.put("data", new JSONObject());
		    resultJson.put("state", Integer.valueOf(0));
		    return resultJson;
		}
		
		String channelId = param.getString("channelId");
	
		String channelType = "";
		try {
			JSONObject channelJson =getChannelById(channelId);
			channelType = channelJson.getString("TYPE");

			
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error("根据渠道id查询渠道失败："+e.getMessage());
			return EasyResult.error(500,"根据渠道id查询渠道失败："+e.getMessage());

		}
		
		
		
		String userAccount=UserUtil.getUser(this.request).getUserAcc();//登陆账号
		EasySQL sql = this.getEasySQL("SELECT t1.* FROM " + this.getTableName("C_SMS_TEMPLATE")+" t1 LEFT JOIN "+this.getTableName("C_SMS_TEMPLATE_TYPE")+" t2 on t2.id=t1.type_id where 1=1 ");
		sql.append(this.param.getString("smsTemplateType"),"and t1.TYPE_ID= ? ");
		sql.append("AND t1.STATUS='1' AND T2.STATUS='1' ");
		sql.append(getEntId()," AND t1.EP_CODE = ? ",false);
		sql.append(getBusiOrderId()," AND t1.BUSI_ORDER_ID = ? ",false);
		sql.appendLike(this.param.getString("templet_content"),	"AND t1.CONTENT LIKE ?");
		sql.appendLike(this.param.getString("templet_name"),	"AND t1.NAME LIKE ?");
		sql.append(" AND (t1.CREATE_ACC='"+userAccount+"' OR t1.IS_PUBLIC='Y') ");

		if("02".equals(channelType) || "03".equals(channelType)){
			sql.append(channelId," and t1.CHANNEL_ID = ? AND t1.ADUIT_STATUS = '1' ");
		}
		

		
		sql.append("ORDER BY t1.CREATE_TIME DESC");
	
		
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取短信模板内容,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		}
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
	/**
	 * 获取模板类型树
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name="getFrameworkTree",type=Types.OTHER)
	public JSONObject getFrameworkTree(){
		EasySQL sql = this.getEasySQL("SELECT ID,NAME,PARENT_ID FROM " + this.getTableName("C_SMS_TEMPLATE_TYPE") + " WHERE 1=1 ");
		sql.append(getEntId()," AND EP_CODE = ? ",false);
		sql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ",false);
		sql.append(" ORDER BY CREATE_TIME DESC");
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取模板类型树json,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		}
		JSONObject obj=this.queryForList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
	/**
	 * 获取模板类型信息
	 * @return
	 * @throws SQLException 
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name="getTypeInfo",type=Types.RECORD)
	public JSONObject getTypeInfo() {
		EasySQL sql = this.getEasySQL("SELECT NAME AS PARNAME FROM " + this.getTableName("C_SMS_TEMPLATE_TYPE") + " WHERE id = ( ");
		sql.append("SELECT PARENT_ID FROM "+ this.getTableName("C_SMS_TEMPLATE_TYPE")+" WHERE 1 = 1");
		sql.append(getEntId()," AND EP_CODE = ? ",false);
		sql.append(getBusiOrderId()," AND BUSI_ORDER_ID = ? ",false);
		sql.append(this.param.getString("id"),"AND ID = ?");
		sql.append(")");
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "获取一条短信模板分类,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		}
		JSONObject obj = new JSONObject();
//		EasyRecord obj=this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			JSONObject newDataJson=new JSONObject();
			newDataJson.put("MODLE"+"."+"PARNAME", obj.getString("PARNAME"));
			obj.put("data", newDataJson);
			//StringUtil.addPrefixS(obj, "MODLE");
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询错误" + e.getMessage(), e);
		}
		return obj;
	}
	
	/**
	 * 短信模板类型下拉列表
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name = "smsTemplateTypeList", type = Types.DICT)
	public JSONObject smsTemplateTypeList() {
		JSONObject obj = getDictByQuery("select ID,NAME from " + this.getTableName("C_SMS_TEMPLATE_TYPE") + " where 1=1 " + " AND EP_CODE = ? AND BUSI_ORDER_ID = ? " + "ORDER BY create_time desc", new Object[] {getEntId(),getBusiOrderId()});
		return obj;
	}
	
	/**
	 * 短信模板下拉列表
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name = "smsTemplateList", type = Types.DICT)
	public JSONObject smsTemplateList() {
		JSONObject obj = getDictByQuery("SELECT ID,NAME FROM " + this.getTableName("C_SMS_TEMPLATE") + " where 1=1 AND TYPE_ID = ? "
				+ " AND EP_CODE = ? AND BUSI_ORDER_ID = ? "
				+ "ORDER BY CREATE_TIME DESC",
				new Object[] {getMethodParam(0),getEntId(),getBusiOrderId()});
		return obj;
	}
	
	/**
	 * 系统变量分组下拉列表
	 * 
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name = "smsVarGroup", type = Types.DICT)
	public JSONObject smsVarGroup() {
		EasySQL sql = new EasySQL("SELECT ID AS GROUP_ID,NAME AS GROUP_NAME FROM "+getTableName("C_CF_VAR_GROUP"));
		sql.append("WHERE 1=1");
		sql.append(Constants.VAR_GROUP_TYPE_PUB,"AND (GROUP_TYPE = ?",false);// 01-公用 02-短信模板 03-邮件模板
		sql.append(Constants.VAR_GROUP_TYPE_SMS,"OR GROUP_TYPE = ?)",false);
		sql.append(getEntId(),"AND ENT_ID = ?",false);
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?",false);
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	/**
	 * 系统变量下拉列表
	 * 
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name = "smsVarList", type = Types.DICT)
	public JSONObject smsVarList() {
		EasySQL sql = new EasySQL("SELECT CODE AS VAR_CODE,NAME AS VAR_NAME FROM "+getTableName("C_CF_VAR"));
		sql.append("WHERE 1=1");
		sql.append(param.getString("GROUP_ID"),"AND VAR_GROUP_ID = ?",false);
		sql.append("01","AND ENABLE_STATUS = ?");// 01-启用 02-禁用
		sql.append(getEntId(),"AND ENT_ID = ?",false);
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?",false);
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	
	public JSONObject getChannelById(String channelId) throws SQLException{
		
		EasySQL sql = new EasySQL("select * from "+this.getTableName("c_sms_channel"));
		sql.append(channelId," where ID = ? ");
		return this.getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		
		//return getEntId()+"_"+getBusiOrderId()+"_dxqd";
	}
	
	
	
	
	/**
	 * @return
	 */
	@InfAuthCheck(resId="cc-tx-sms-dxmb", msg="没有权限")
	@WebControl(name="selectTemplateById",type=Types.RECORD)
	public JSONObject selectTemplateById(){
		EasySQL sql = this.getEasySQL("SELECT  T.* ");
		sql.append("FROM " + this.getTableName("c_sms_template") + " T WHERE 1=1 ");
		sql.append(this.param.getString("ID")," AND T.ID = ?");

		JSONObject obj = this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		return obj;
	}
	
	/**
	 * 查询当前企业知识库推送模板
	 * @return
	 */
	@WebControl(name="selectTemplateByKm",type=Types.RECORD)
	public JSONObject selectTemplateByKm() {
		String templateId = this.getEntId() + "_" + this.getBusiOrderId() + "_km_detail";
		
		return this.queryForRecord("select * from " + this.getTableName("C_SMS_TEMPLATE") + " where ID = ?", templateId);
	}
	
}