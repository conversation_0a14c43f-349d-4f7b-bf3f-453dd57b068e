<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="反馈"></title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="easyform" data-mars="KmFeedbackInfoSimplifyDao.record" data-mars-prefix="kmFeedbackInfo.">
			<input type="hidden" name="kmFeedbackInfo.ID" value="${param.id}">
			<input type="hidden" name="kmFeedbackInfo.QUERY_DIR" value="${param.dirId}">
			<c:if test="${not empty param.infoId}">
				<input type="hidden" name="kmFeedbackInfo.KM_INFO_ID" value="${param.infoId}">
				<input type="hidden" name="kmFeedbackInfo.KM_INFO_TYPE" value="03">
				<input type="hidden" name="kmFeedbackInfo.TYPE" value="02">
			</c:if>

			<table class="table table-edit text-c mt-20">
				<tbody>
					<tr>
						<td width="100px" class="required" i18n-content="关键词"></td>
						<td><input type="text" name="kmFeedbackInfo.KEY_CODE" class="form-control input-sm" data-rules="required|maxlength=60" value="${param.keyCode}" maxlength="60"></td>
					</tr>
					<tr>
						<td i18n-content="建议内容"></td>
						<td><textarea i18n-placeholder="您可以输入建议的知识内容,300字以内" rows="6" cols="50" class="form-control" name="kmFeedbackInfo.SUGGEST_CONTENT" maxlength="300" data-rules="maxlength=300" data-title="建议内容"></textarea></td>
					</tr>
				</tbody>
			</table>
			<div class="layer-foot text-c">
				<button type="button" class="btn btn-primary btn-sm" onclick="KmFeedbackInfo.ajaxSubmitForm()" i18n-content="提交"></button>
				<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this);" i18n-content="关闭"></button>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	  var KmFeedbackInfo={
			 id : '${param.id}',
			 ajaxSubmitForm:function(){
				 if(form.validate("easyform")){
					 if(!this.id){
						 this.add();
					 } else {
						 this.update();
					 }
				 }
			 },
			 add:function(){
				var data = form.getJSONObject("#easyform");
				ajax.remoteCall("${ctxPath}/servlet/kmFeedbackInfoSimplify?action=add",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,function(){
							popup.layerClose("#easyform");
							window.KmFeedbackInfoList&&KmFeedbackInfoList.query();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			 },
			 update:function(){
				 var data = form.getJSONObject("easyform");
				 ajax.remoteCall("${ctxPath}/servlet/kmFeedbackInfoSimplify?action=update",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								popup.layerClose("#easyform");
								doQuery();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
			 }
	 }
	 
	 $(function(){
		 $("#easyform").render();
		 execI18n();
	 });
	</script>
</EasyTag:override>
<c:choose>
	<c:when test="${empty param.isNotDiv}">
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_list.jsp"%>
	</c:otherwise>
</c:choose>