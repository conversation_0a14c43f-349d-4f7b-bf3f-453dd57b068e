<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2">
    <meta name="format-detection" content="telephone=no">
    <title>智能知识库</title>
    <link href="css/bee.css" rel="stylesheet" data-role="global">
    <link href="favicon.ico" rel="icon" type="image/x-icon">

    <link rel="stylesheet" href="css/yc-ai.css">
    <style>
        
    </style>
</head>
<body style="">
    <div id="pageWrapper">
        <!-- <div class="qc-header-nav"></div> -->
        <div class="container container-smarty" id="container">
            <div class="main" id="appArea">
                <div class="chat-section J-parantContainer">
                    <div class="section-footer">
                        <span style="padding: 3px 10px;float: right;">Power by Yunqu</span>
                       <!--  <div style="padding: 3px 10px;">当前模式：
                            <label>
                                <input type="radio" checked value="1" name="chatmode"> 智能客服
                            </label>

                            <label>
                                <input type="radio" value="0" name="chatmode"> 人工客服
                            </label>
                        </div> -->

                    </div>
                    <div class="section-header">
                        <span >云趣智能知识库</span>
                        <div class="section-header-logo">
                            <img src="images/robot.png" alt="" style="height: 90px;vertical-align: bottom;">
                        </div>
                        <div class="ai-chat-btns">
                            <a href="javascript:void(0)" id="ai-reset" title="清屏" class="ai-chat-btn resetchat"></a>
                            <a href="javascript:void(0)" id="ai-close" title="关闭" class="ai-chat-btn closechat"></a>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="main-wrap clearfix">
                            <!-- 左侧-对话框部分 -->
                            <!-- 根据resize后屏幕实际高度计算 -->
                            <div class="left-area">
                                
                                <div id="chatwrap" class="chat-wrap J-chatContainer"></div>
                                <div class="quiz-wrap ">
                                   
                                    <div class="question-todo" style="display: none">
                                        <ul></ul>
                                    </div>

                                    <div class="question-input-wrap">
                                        <textarea id="question-input" name="" class="J-contentContainer" placeholder="请简要描述您的问题"></textarea>
                                    </div>
                                    <div class="btn-group">
                                        <button id="resetContent" type="button" class="tc-15-btn weak m J-clearContent">清空</button>
                                        <button id="sendContent" type="button" class="tc-15-btn m J-sendContent">发送</button>
                                    </div>
                                </div>
                            </div>
                            <!-- 左侧-对话框部分 -->
                            <!-- 右侧-文档部分 -->
                            <!-- 根据resize后屏幕实际高度计算 -->
                            <div class="right-area">
                                <div class="faq-wrap  J-articleListContainer">
                                    <div class="faq-hd">
                                        <div class="search-box m">
                                            <div class="search-input-wrap">
                                                <input id="search-input" type="text" class="tc-15-input-text search-input J-searchContent" maxlength="100" placeholder="请简要输入你要搜索的问题,最大长度为100">
                                            </div>
                                            <span id="search-btn" class="search-btn J-searchBtn">
                                                <i class="icon-search"></i>
                                            </span>
                                        </div>
                                    </div>
                                   
                                    <div id="search-result" class="recommend-question J-hotQuestion search-result" style="margin-bottom: 20px;display: none">
                                        <div class="tit">搜索结果</div>
                                        <ul class="recommend-list">
                                            <li class="J-userQuestionItem" data-id="1"><a href="javascript:void(0);" class="ai-keyword">使用流程</a></li>
                                            
                                        </ul>
                                    </div>
                                    

                                    <div id="commend-list"></div>
                                    <div id="hot-list"></div>
                                    
                                
                            </div>
                            <!-- 右侧-文档部分 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
   

    <script id="reply-template" type="text/x-jsrender">
                
        {{if info && info.ITEM_TITLE}}
                <p class="tit">{{:info.ITEM_TITLE}}<p>
        {{/if}}
       
        <pre>{{formatcontent:info.CONTENT||info.CONTENT}}</pre>
        
       
        {{include tmpl="#attach-template"/}}
        

        {{if info && info.ITEM_TITLE}}
        <div class="feedback-wrap">
            <div class="feedback-btn">
                <div class="helpful-btn J-questionFeedbackBtn" data-searchtime="{{:searchTime}}" data-infoid="{{:info.INFO_ID}}" data-itemid="{{:info.ITEM_ID}}" data-question="{{:question}}" data-value="1" data-kmid="{{:info.ID}}"><a href="javascript:void(0);"><i class="icon-helpful"></i><span>有帮助</span></a></div>
                <b class="seporator"></b>
                <div class="helpless-btn J-questionFeedbackBtn" data-searchtime="{{:searchTime}}" data-infoid="{{:info.INFO_ID}}" data-itemid="{{:info.ITEM_ID}}" data-question="{{:question}}" data-value="0" data-kmid="{{:info.ID}}"><a href="javascript:void(0);"><i class="icon-helpless"></i><span>无帮助</span></a></div>
            </div>
        </div>
        {{/if}}

    </script>

    <script id="agent-reply-template" type="text/x-jsrender">
        <div>{{:msgContent}}</div>
    </script>
    
    <script id="search-template" type="text/x-jsrender">

        {{for data ~question=question}}
        	<li class="J-userQuestionItem">
                <a href="javascript:void(0);" data-type="question" data-id="{{:ID}}" data-question="{{:~question}}" class="ai-keyword">{{:ITEM_TITLE}}</a>
            </li>
        {{/for}}
    </script>

    <script id="navList-template" type="text/x-jsrender">
        
        <div class="relative-warp">
            <p style="margin-bottom:5px">热门问题</p>
            <ul class="list-navlist">
            {{for data}}
                <li><a data-id="{{:id}}" data-type="nav" href="javascript:void(0)" data-question="{{:content}}" class="ai-keyword">{{:content}}</a></li>
            {{/for}}
            </ul>
        </div>
        
        
    </script>
    <script id="navList-template" type="text/x-jsrender">
        
        <div class="relative-warp">
            <p style="margin-bottom:5px">最新问题</p>
            <ul class="list-relative">
            {{for data}}
                <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-question="{{:content}}" class="ai-keyword">{{:content}}</a></li>
            {{/for}}
            </ul>
        </div>
        
        
    </script>
    <!-- 最新查询 -->
    <script id="topList-template" type="text/x-jsrender">
        
            <div class="relative-warp">
            <p>Hi , 我是云趣智能知识库~ 有什么问题快来问我吧~</p>
            <p>猜您想问的问题是：</p>
            <ul class="list-relative">
            {{for data}}
                <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-question="{{:content}}" class="ai-keyword">{{:content}}</a></li>
            {{/for}}
            </ul>
            <p>都不是? 请一句话描述您的问题</p>
            </div>
       
    </script>

    <script id="refList-template" type="text/x-jsrender">
        {{if refList && refList.length>0}}
            <div class="relative-warp">
            <p>相关问题</p>
            <ul class="list-relative">
            {{for refList}}
                <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-question="{{:title}}" data-type="question" class="ai-keyword">{{:title}}</a></li>
            {{/for}}
            </ul>
            </div>
        {{/if}}
    </script>

    <script id="attach-template" type="text/x-jsrender">
        {{if info.files && info.files.length>0}}
            <div class="relative-warp">
            <p>附件列表</p>
            <ul class="list-relative">
            {{for info.files}}
                <li>{{:#getIndex()+1}}. <a href="{{:path}}" data-id="{{:ATTACH_ID}}" target="_blank" class="">{{:name}}</a></li>
            {{/for}}
            </ul>
            </div>
        {{/if}}
    </script>


    <script id="search-keyword-template" type="text/x-jsrender">
        {{if data && data.length>0}}
            {{for data}}
            <li>{{:#getIndex()+1}}. <a href="javascript:void(0)" data-id="{{:ID}}" class="ai-keyword" data-type="question" data-question="{{:title}}">{{:title}}</a></li>
            {{/for}}
        {{else}}
         <li>没有找到相关记录</li>
        {{/if}}
    </script>
    <!-- 目录分支 -->
    <script id="search-nav-template" type="text/x-jsrender">
        {{if data && data.length>0}}
            <div class="relative-warp2">
            <p>请选择您想咨询{{:keyword}}的问题</p>
            <ul class="list-relative">
            {{for data}}
                <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-infoid="{{:infoId}}" data-infoid="{{:infoId}}" data-searchTime="{{:searchTime}}" data-question="{{:question}}" data-type="cat" class="ai-keyword">{{:title}}</a></li>
            {{/for}}
            </ul>
            </div>
        {{else}}
        {{include tmpl="#notfound-template"/}}
        {{/if}}
    </script>

    <!-- 问题分支 -->
    <script id="search-cat-template" type="text/x-jsrender">
        {{if data && data.length>0}}
            <div class="relative-warp2">
            <p>请选择您想咨询<font style="color:red">{{:question}}</font>的具体问题</p>
            <ul class="list-relative">
            {{for data ~question=question}}
                <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-itemid="{{:ITEM_ID}}" data-infoid="{{:INFO_ID}}" data-searchTime="{{:searchTime}}" data-question="{{:~question}}" data-type="question" class="ai-keyword">{{:ITEM_TITLE}}</a></li>
            {{/for}}
            </ul>
            </div>
        {{else}}
        {{include tmpl="#notfound-template"/}}
        {{/if}}
    </script>

    <script id="search-commend-template" type="text/x-jsrender">
        {{if data && data.length>0}}
        <div class="recommend-question J-hotQuestion">
                <div class="tit">热门问题</div>
                <ul class="recommend-list">
                    {{for data}}
                        <li>{{:#getIndex()+1}}. <a href="javascript:void(0);" data-id="{{:ID}}" data-itemid="{{:ITEM_ID}}" data-infoid="{{:INFO_ID}}"  data-question="{{:title}}" data-type="question" class="ai-keyword">{{:title}}</a></li>
                    {{/for}}
                </ul>
            </div>
        </div>
        {{/if}}
    </script>

    <script id="notfound-template" type="text/x-jsrender">
        <p>没有找到相关记录,请尝试更换问题描述</p>
    </script>


	<script type="text/javascript">
	   var entId='${param.entId}';
       var entToken = '${param.token}';
	</script>
    <script src="/easitline-static/js/jquery.min.js"></script>
    <script src="/easitline-static/js/jsrender.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
    <!-- <script src="/easitline-static/js/easitline.core-2.0.0.js?v=20180117"></script> -->
    
    <script src="js/yc-ai.js?2240"></script>
    <script>
        $.views.converters("formatcontent", function(content) {
            return content.replace(/[\n\r]/g,'<br>');
        });
    </script>
  </body>
</html>