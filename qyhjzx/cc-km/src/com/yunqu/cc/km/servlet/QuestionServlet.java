package com.yunqu.cc.km.servlet;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.km.base.AppBaseServlet;
import com.yunqu.cc.km.base.CommonLogger;
import com.yunqu.cc.km.utils.ContentDealUtils;
import com.yunqu.cc.km.utils.InfoUtil;
import com.yunqu.cc.km.utils.RobotUtils;
@WebServlet("/servlet/question")
public class QuestionServlet extends AppBaseServlet {
	private static final long serialVersionUID = 5054944876079785280L;
	
	private Logger logger = CommonLogger.logger;
	
	/**
	 * 保持FAQ库内容
	 * @return
	 */
	@InfAuthCheck(resId = "cc-km-manage-question-faq", msg = "没有权限")
	public EasyResult actionForQuestionAdd(){
		EasyQuery query = this.getQuery();
		boolean isRoolback = false;
		try {
			query.begin();
			JSONObject json = this.getJSONObject();
			String infoId = RandomKit.randomStr();
			String itemId = json.getString("itemId");
			String dirId = json.getString("dirId");
			String infoType = json.getString("infoType");
			
			String date = DateUtil.getCurrentDateStr();
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			String schema = this.getDbName();
			UserModel user = UserUtil.getUser(getRequest());
			
			String keyWord = json.getString("keyWord");
			//如果FAQ库已经有该标题，不允许重复插入
			String questionTitle = json.getString("questionTitle");
			String kmProvider = json.getString("kmProvider");
			String secretLevel = json.getString("secretLevel");
//			boolean isExists = query.queryForExist("SELECT COUNT(1) FROM " + this.getTableName("KM_INFO") + " WHERE ENT_ID=? AND BUSI_ORDER_ID=? and KM_INFO_TYPE='02' and INFO_TOPIC=?", new Object[]{entId,busiOrderId,questionTitle});
//			if (isExists) {
//				isRoolback = true;
//				return EasyResult.error(500, "FAQ库已存在该问题，请不要重复插入！");
//			}
			EasySQL titleSql = new EasySQL("SELECT COUNT(*) FROM "+this.getTableName("KM_INFO")+" ");
			titleSql.append(infoType,"WHERE KM_INFO_TYPE = ?");
			titleSql.append(questionTitle,"AND INFO_TOPIC = ?");
			if(query.queryForExist(titleSql.getSQL(), titleSql.getParams())) {
				isRoolback = true;
				if ("02".equals(infoType)) {
					return EasyResult.error(500, getI18nValue("已存在相同问题，请重新输入问题名称！"));
				}
				return EasyResult.error(500, getI18nValue("标题重复，请重新输入标题！"));
			}
			
			String nameStr = ";" + questionTitle + ";" + keyWord + ";";
			
			if(StringUtils.isNotBlank(keyWord)) {
				for(String keyCode : keyWord.split(";")) {
					if(nameStr.indexOf(";" + keyCode +";") != nameStr.lastIndexOf(";" + keyCode +";")) {
						isRoolback = true;
						return EasyResult.error(500, getI18nValue("关键字[") + keyCode + getI18nValue("]重复"));
					}
				}
			}
			
			if (StringUtils.isNoneBlank(questionTitle)) {
				//查无结果优化界面中的操作，在km_no_info_record表中添加描述
				EasySQL sql = new EasySQL(" UPDATE " + getTableName("KM_NO_INFO_RECORD"));
				sql.append(" SET STATUS='1'");
				String desc=" /添加到FAQ库";
				sql.append(desc,", HANDLE_DESC = CONCAT((CASE WHEN HANDLE_DESC IS NOT NULL THEN HANDLE_DESC ELSE '' END),?)");
				sql.append(user.getUserAcc(), ", HANDLE_ACC=? ");// 获取当前用户账户
				sql.append(user.getUserName(), " ,HANDLE_ACC_NAME=? ");// 获取当前用户姓名
				sql.append(date, " ,HANDLE_TIME=? ");// 获取当前时间
				sql.append("WHERE 1 = 1 ");
				sql.append(questionTitle, " AND KEY_WORD = ?");
				query.execute(sql.getSQL(), sql.getParams());
			}
			String infoStatus = "8";
			// faq需要支持审核
			if("02".equals(infoType)) {
				infoStatus = "1";
			}
			
			String validDate = json.getString("startDate").trim();
			String expireDate = json.getString("endDate").trim();
			
			// 判断当前日期是否在有效期内
			String newDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			if(DateUtil.getDate("yyyy-MM-dd", expireDate).before(DateUtil.getDate("yyyy-MM-dd", newDate)) ) {
				infoStatus = "4";
			} else if(DateUtil.getDate("yyyy-MM-dd", newDate).before(DateUtil.getDate("yyyy-MM-dd", validDate))) {
				infoStatus = "3";
			}
			// 添加寒暄信息
			EasyRecord record=new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
			record.set("ENT_ID", this.getEntId());
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("INFO_ID", infoId);
			record.set("DIR_ID", dirId);
			record.set("SEARCH_CODE", keyWord);
			record.set("VALID_DATE", validDate);
			record.set("EXPIRE_DATE", expireDate);
			if(StringUtils.isNotBlank(kmProvider)) {
				record.set("KM_PROVIDER", kmProvider);
				record.set("SECRET_LEVEL", secretLevel);
			}
			record.set("INFO_TOPIC", json.getString("questionTitle"));
			record.set("KM_INFO_TYPE", infoType);
			record.set("INFO_BOUND", "0");
			record.set("INFO_LEVEL", "1");
			record.set("INFO_KIND", "1");
			record.set("INFO_STATUS", infoStatus); // 设置同步状态为未同步
			record.set("CREATE_USER", user.getUserAcc());
			record.set("CREATE_DATE", date);
			record.set("CREATE_USERNAME", user.getUserName());
			query.save(record);
			
			record = new EasyRecord(this.getTableName("KM_INFO_ITEM"),"ID");
			record.put("ID", itemId);
			record.put("INFO_ID", infoId);
			record.put("TITLE", json.getString("questionTitle"));
			record.put("EX_FILE", json.getString("EX_FILE"));
			record.put("ENT_ID", entId);
			record.put("BUSI_ORDER_ID", busiOrderId);
			query.save(record);
			
			//相似问
			List<String> questionList = new ArrayList<String>();
			JSONArray questionArray = json.getJSONArray("question");
			if(questionArray != null && questionArray.size() > 0) {
				for (Object question : questionArray) {
					if(questionList.contains(question)) {
						continue;
					}
					
					if(nameStr.indexOf(";" + question.toString() + ";") > -1) {
						isRoolback = true;
						return EasyResult.error(500, getI18nValue("相似问[") + question + getI18nValue("]重复"));
					}
					
					record = new EasyRecord(this.getTableName("KM_INFO_Q"),"ID");
					record.put("ID", RandomKit.randomStr());
					record.put("KM_INFO_ID", infoId);
					record.put("QUESTIONS", question);
					record.put("IDX_ORDER", 0);
					record.put("CREATE_ACC", user.getUserAcc());
					record.put("CREATE_NAME", user.getUserName());
					record.put("CREATE_DEPT", user.getDeptCode());
					record.put("CREATE_DEPT_NAME", user.getDeptName());
					record.put("CREATE_TIME", date);
					query.save(record);
					
					questionList.add(question.toString());
				}
			}
			
			//多渠道内容
			JSONArray contentArray = json.getJSONArray("content");
			for(int i = 0; i < contentArray.size(); i++ ) {
				String channelKey = contentArray.getJSONObject(i).getString("channelKey");
				String contentVal = contentArray.getJSONObject(i).getString("contentVal");
				String contentId = RandomKit.randomStr();
				record = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT"),"ID");
				record.put("ID", contentId);
				record.put("INFO_ITEM_ID", itemId);
				String content = URLDecoder.decode(URLEncoder.encode(contentVal, "utf-8"), "utf-8");
				
				record.put("CONTENT", ContentDealUtils.parseUtf8(URLDecoder.decode(content, "utf-8")));
				if(i == 0) {
					record.put("IS_MAIN", "0");
				}
				record.put("CREATE_ACC", user.getUserAcc());
				record.put("CREATE_NAME", user.getUserName());
				record.put("CREATE_DEPT", user.getDeptCode());
				record.put("CREATE_DEPT_NAME", user.getDeptName());
				record.put("CREATE_TIME", date);
				record.put("STATUS", "01");
				query.save(record);
				
				// 当内容为空时将知识点状态设置为待完善
				if(StringUtils.isBlank(contentVal)) {
					infoStatus = "7";
				}
				
				String [] channelArray = channelKey.split("-");
				for (String channel : channelArray) {
					if(StringUtils.isNotBlank(channel)) {
						record = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_REF"),"ID");
						record.put("ID", RandomKit.randomStr());
						record.put("INFO_ITEM_ID", itemId);
						record.put("INFO_ITEM_CONTENT_ID", contentId);
						record.put("REF_TYPE", "1");
						record.put("REF_ID", channel);
						query.save(record);
					}
				}
			}
			
			//关联业务分类
			String classifyCode = json.getString("classifyCode");
			if(StringUtils.isNotBlank(classifyCode)) {
				String [] classifyCodeArr = classifyCode.split(",");
				for (int i = 0; i < classifyCodeArr.length; i++ ) {
					EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_CLASSIFY_REF"),"ID");
					questionRecord.put("ID", RandomKit.randomStr());
					questionRecord.put("CLASSIFY_ID", classifyCodeArr[i]);
					questionRecord.put("TYPE", "1");
					questionRecord.put("REF_ID", infoId);
					query.save(questionRecord);
				}
			}
			
			
			// 修改知识点同步状态为待完善
			record=new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
			record.put("INFO_ID", infoId);
			record.put("INFO_STATUS", infoStatus);
			query.update(record);
			
			query.commit();
			
			InfoUtil.getInstance().operLog(infoId, null, infoType, "", "01", user, schema, entId, busiOrderId);
			
			if("8".equals(infoStatus)) {
				RobotUtils.getInstance().synRobotByInfoId(schema, infoId, user, entId, busiOrderId);
				RobotUtils.getInstance().reloadRobot(infoType, entId, busiOrderId);
			}
		} catch (Exception e) {
			isRoolback = true;
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("操作失败!"));
		} finally {
			if(isRoolback == true) {
				try { query.roolback(); } catch (SQLException e1) { }
			}
		}
		return EasyResult.ok(null,getI18nValue("操作成功!"));
	}
	
	/**
	 * FAQ库修改
	 * @return
	 */
	@InfAuthCheck(resId = "cc-km-manage-question-faq", msg = "没有权限")
	public EasyResult actionForQuestionEdit(){
		EasyQuery query = this.getQuery();
		boolean isRoolback = false;
		try {
			query.begin();
			JSONObject json = this.getJSONObject();
			
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			String date = DateUtil.getCurrentDateStr();
			String schema = this.getDbName();
			UserModel user = UserUtil.getUser(getRequest());
			
			String infoId = json.getString("ID");
			String dirId = json.getString("dirId");
			String itemId = query.queryForString("SELECT ID FROM " + this.getTableName("KM_INFO_ITEM") + " WHERE INFO_ID = ?", infoId);
			String infoType = json.getString("infoType");
			
			String keyWord = json.getString("keyWord");
			
			String infoStatus = "8";
			// faq需要支持审核
			if("02".equals(infoType)) {
				infoStatus = "1";
			}
			
			String validDate = json.getString("startDate").trim();
			String expireDate = json.getString("endDate").trim();
			
			// 判断当前日期是否在有效期内
			String newDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			if(DateUtil.getDate("yyyy-MM-dd", expireDate).before(DateUtil.getDate("yyyy-MM-dd", newDate))) {
				infoStatus = "4";
			} else if(DateUtil.getDate("yyyy-MM-dd", newDate).before(DateUtil.getDate("yyyy-MM-dd", validDate))) {
				infoStatus = "3";
			}
			
			String questionTitle = json.getString("questionTitle");
			String kmProvider = json.getString("kmProvider");
			String secretLevel = json.getString("secretLevel");
			EasySQL titleSql = new EasySQL("SELECT COUNT(*) FROM "+this.getTableName("KM_INFO")+" ");
			titleSql.append(infoType,"WHERE KM_INFO_TYPE = ?");
			titleSql.append(infoId,"AND INFO_ID <> ?");
			titleSql.append(questionTitle,"AND INFO_TOPIC = ?");
			if(query.queryForExist(titleSql.getSQL(), titleSql.getParams())) {
				isRoolback = true;
				return EasyResult.error(500, getI18nValue("标题重复，请重新输入标题！"));
			}
			
			String nameStr = ";" + questionTitle + ";" + keyWord + ";";
			
			if(StringUtils.isNotBlank(keyWord)) {
				for(String keyCode : keyWord.split(";")) {
					if(nameStr.indexOf(";" + keyCode + ";") != nameStr.lastIndexOf(";" + keyCode + ";")) {
						isRoolback = true;
						return EasyResult.error(500, getI18nValue("关键字[") + keyCode + getI18nValue("]重复"));
					}
				}
			}
			// 修改问题库信息
			EasyRecord record=new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
			record.set("INFO_ID", infoId);
			record.set("SEARCH_CODE", keyWord);
			record.set("KM_INFO_TYPE", infoType);
			record.set("INFO_TOPIC", questionTitle);
			record.set("VALID_DATE", validDate);
			// record.set("DIR_ID", dirId);
			record.set("EXPIRE_DATE", expireDate);
			record.set("INFO_STATUS", infoStatus); // 设置同步状态为未同步
			record.set("UPDATE_DATE", DateUtil.getCurrentDateStr());
			if(StringUtils.isNotBlank(kmProvider)) {
				record.set("KM_PROVIDER", kmProvider);
				record.set("SECRET_LEVEL", secretLevel);
			}
			query.update(record);
			
			// 修改关联到此知识项的知识项状态
			query.execute("UPDATE " + this.getTableName("KM_INFO_ITEM") + " SET NEED_UPDATE = 'Y' WHERE ID IN (SELECT INFO_ITEM_ID FROM " + this.getTableName("KM_LINK_INFO_ITEM") + " WHERE LINK_INFO_ITEM_ID = ?)", itemId);
			
			record = new EasyRecord(this.getTableName("KM_INFO_ITEM"),"ID");
			record.put("ID", itemId);
			record.put("TITLE", questionTitle);
			record.put("NEED_UPDATE", "N");
			record.put("EX_FILE", json.getString("EX_FILE"));
			query.update(record);
			
			// 删除寒暄相似问信息
			query.execute("DELETE FROM " + this.getTableName("KM_INFO_Q") + " WHERE KM_INFO_ID = ?", infoId);
			// 删除寒暄内容信息
			query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT") + " WHERE INFO_ITEM_ID = ?", itemId);
			// 删除寒暄内容关联渠道信息
			query.execute("DELETE FROM " + this.getTableName("KM_INFO_ITEM_CONTENT_REF") + " WHERE INFO_ITEM_ID = ?", itemId);
			// 删除业务分类信息
			query.execute("DELETE FROM " + this.getTableName("KM_CLASSIFY_REF") + " WHERE REF_ID = ? AND TYPE=?", infoId,"1");
			
			// 添加寒暄相似问信息
			List<String> questionList = new ArrayList<String>();
			JSONArray questionArray = json.getJSONArray("question");
			if(questionArray != null && questionArray.size() > 0) {
				for (Object question : questionArray) {
					if(questionList.contains(question)) {
						continue;
					}
					if(nameStr.indexOf(";" + question.toString() + ";") > -1) {
						isRoolback = true;
						return EasyResult.error(500, getI18nValue("相似问[") + question + getI18nValue("]重复"));
					}
					
					record = new EasyRecord(this.getTableName("KM_INFO_Q"),"ID");
					record.put("ID", RandomKit.randomStr());
					record.put("KM_INFO_ID", infoId);
					record.put("QUESTIONS", question);
					record.put("IDX_ORDER", 0);
					record.put("CREATE_ACC", user.getUserAcc());
					record.put("CREATE_NAME", user.getUserName());
					record.put("CREATE_DEPT", user.getDeptCode());
					record.put("CREATE_DEPT_NAME", user.getDeptName());
					record.put("CREATE_TIME", date);
					query.save(record);
					
					questionList.add(question.toString());
				}
			}
			
			// 添加寒暄内容信息
			JSONArray contentArray = json.getJSONArray("content");
			for(int i = 0; i < contentArray.size(); i++ ) {
				String channelKey = contentArray.getJSONObject(i).getString("channelKey");
				String contentVal = contentArray.getJSONObject(i).getString("contentVal");
				String contentId = RandomKit.randomStr();
				record = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT"),"ID");
				record.put("ID", contentId);
				record.put("INFO_ITEM_ID", itemId);

				String content = URLDecoder.decode(URLEncoder.encode(contentVal, "utf-8"), "utf-8");
				
				record.put("CONTENT", ContentDealUtils.parseUtf8(URLDecoder.decode(content, "utf-8")));
				record.put("CONTENT_TYPE", contentArray.getJSONObject(i).getString("contentType"));
				if(i == 0) {
					record.put("IS_MAIN", "0");
				}
				record.put("CREATE_ACC", user.getUserAcc());
				record.put("CREATE_NAME", user.getUserName());
				record.put("CREATE_DEPT", user.getDeptCode());
				record.put("CREATE_DEPT_NAME", user.getDeptName());
				record.put("CREATE_TIME", date);
				record.put("STATUS", "01");
				query.save(record);
				
				// 当内容为空时将知识点状态设置为待完善
				if (StringUtils.isBlank(contentVal)) {
					infoStatus = "7";
				}
				
				String [] channelArray = channelKey.split("-");
				for (String channel : channelArray) {
					if(StringUtils.isNotBlank(channel)) {
						// 添加寒暄内容关联渠道信息
						record = new EasyRecord(this.getTableName("KM_INFO_ITEM_CONTENT_REF"),"ID");
						record.put("ID", RandomKit.randomStr());
						record.put("INFO_ITEM_ID", itemId);
						record.put("INFO_ITEM_CONTENT_ID", contentId);
						record.put("REF_TYPE", "1");
						record.put("REF_ID", channel);
						query.save(record);
					}
				}
			}
			
			//关联业务分类
			String classifyCode = json.getString("classifyCode");
			if(StringUtils.isNotBlank(classifyCode)) {
				String [] classifyCodeArr = classifyCode.split(",");
				for (int i = 0; i < classifyCodeArr.length; i++ ) {
					EasyRecord questionRecord = new EasyRecord(this.getTableName("KM_CLASSIFY_REF"),"ID");
					questionRecord.put("ID", RandomKit.randomStr());
					questionRecord.put("CLASSIFY_ID", classifyCodeArr[i]);
					questionRecord.put("TYPE", "1");
					questionRecord.put("REF_ID", infoId);
					query.save(questionRecord);
				}
			}
			
			// 修改知识点同步状态为待完善
			record=new EasyRecord(this.getTableName("KM_INFO"), "INFO_ID");
			record.put("INFO_ID", infoId);
			record.put("INFO_STATUS", infoStatus);
			query.update(record);
			query.commit();
			
			InfoUtil.getInstance().operLog(infoId, null, infoType, "", "02", user, schema, entId, busiOrderId);
			
			if("8".equals(infoStatus)) {
				RobotUtils.getInstance().synRobotByInfoId(schema, infoId, user, entId, busiOrderId);
				RobotUtils.getInstance().reloadRobot(infoType, entId, busiOrderId);
			} else {
				RobotUtils.getInstance().delRobot(infoId, null, infoType, entId, busiOrderId, schema);
				RobotUtils.getInstance().reloadRobot(infoType, entId, busiOrderId);
			}
		} catch (Exception e) {
			isRoolback = true;
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail(getI18nValue("操作失败!"));
		} finally {
			if(isRoolback) {
				try { query.roolback(); } catch (SQLException e1) { }
			}
		}
		return EasyResult.ok(null,getI18nValue("操作成功!"));
	}
	
}
