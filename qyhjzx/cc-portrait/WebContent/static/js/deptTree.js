var cityObj;
var setting = {
	view: {
		dblClickExpand: false
	},
	data: {
		simpleData: {
			enable: true,
			idKey: "id",
			pIdKey: "pId"
		},
		key: {
        	name: "name"
        }
	},
	callback: {
		onClick:zTreeOnclick
	}
};

function zTreeOnclick(e, treeId, treeNode) {
	var zTree = $.fn.zTree.getZTreeObj("deptTree"),
	nodes = zTree.getSelectedNodes(),
	v = "";
	nodes.sort(function compare(a,b){return a.id-b.id;});
	for (var i=0, l=nodes.length; i<l; i++) {
		v += nodes[i].name + ",";
	}
	if (v.length > 0 ) v = v.substring(0, v.length-1);
	//var cityObj = $("#deptInput");
	cityObj.prop("value", v);
	hideMenu();
}
function toggle(e){
	var hidden = $("#menuContent").is(":hidden");
	if(hidden){
		var event = e||window.event;
		cityObj = $(event.target);
		var cityOffset = $(event.target).offset();
		showMenu(cityOffset);
	}else{
		hideMenu();
	}
}
function showMenu(cityOffset) {
	$("#menuContent").css({left:cityOffset.left + "px", top:cityOffset.top + cityObj.outerHeight()+ "px"}).slideDown("fast");
	$("body").bind("mousedown", onBodyDown);	
}
function hideMenu() {
	$("#menuContent").fadeOut("fast");
	$("body").unbind("mousedown", onBodyDown);
}
function onBodyDown(event) {
	if (!(event.target.id == "menuContent" || $(event.target).parents("#menuContent").length>0)) {
		hideMenu();
	}
}

$(document).ready(function(){
	var treeObj = $.fn.zTree.init($("#deptTree"), setting, zNodes);
	fillter(treeObj);//设置展开前三级
});

function fillter(treeObj) {
	//获得树形图对象
	var nodeList = treeObj.getNodes();//展开第一个根节点
	for(var i = 0; i < nodeList.length; i++) { //设置节点展开第二级节点
		treeObj.expandNode(nodeList[i], true, false, true);
		var nodespan = nodeList[i].children;
		for(var j = 0; j < nodespan.length; j++) { //设置节点展开第三级节点
			treeObj.expandNode(nodespan[j], true, false, true);
		}
	}
}