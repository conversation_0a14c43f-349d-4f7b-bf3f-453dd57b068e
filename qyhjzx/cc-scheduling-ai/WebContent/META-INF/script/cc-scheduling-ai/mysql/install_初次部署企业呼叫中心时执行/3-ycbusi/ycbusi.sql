CREATE TABLE cc_sd_admin (
  ID varchar(64) NOT NULL COMMENT 'ID',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CLASS_GROUP_ID varchar(64) DEFAULT NULL COMMENT '班组ID',
  ADMIN_ACC varchar(20) DEFAULT NULL COMMENT '管理员账号',
  ADMIN_NAME varchar(30) DEFAULT NULL COMMENT '管理员姓名',
  TYPE varchar(2) DEFAULT NULL COMMENT '1-排班管理员 2-审核管理员\n            \n            业务字典：PB_MANAGE_TYPE',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  PRIMARY KEY (ID),
  KEY IDX_CSA_BRANCH (BRANCH_ID,CLASS_GROUP_ID,TYPE)
) ENGINE=InnoDB   COMMENT='管理员信息表';


CREATE TABLE cc_sd_branch (
  BRANCH_ID varchar(64) NOT NULL COMMENT '分中心ID',
  BRANCH_NAME varchar(64) DEFAULT NULL COMMENT '分中心名称',
  STATUS varchar(2) DEFAULT NULL COMMENT '数据字典 ENABLE_STATUS',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (BRANCH_ID),
  KEY IDX_CSB_ENT_BUSI (ENT_ID,BUSI_ORDER_ID)
) ENGINE=InnoDB   COMMENT='分中心信息表';


CREATE TABLE cc_sd_class_group (
  GROUP_ID varchar(64) NOT NULL COMMENT '分中心ID',
  GROUP_NAME varchar(64) DEFAULT NULL COMMENT '分中心名称',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (GROUP_ID),
  KEY IDX_CSCG_GROUP (GROUP_ID,GROUP_NAME,BRANCH_ID)
) ENGINE=InnoDB   COMMENT='班组信息表';


CREATE TABLE cc_sd_class_group_user (
  ID varchar(64) NOT NULL COMMENT 'ID',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  GROUP_ID varchar(64) NOT NULL COMMENT '班组ID',
  USER_ACC varchar(64) DEFAULT NULL COMMENT '用户账号',
  USER_NAME varchar(64) DEFAULT NULL COMMENT '用户姓名',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_CSCGU_GROUP (BRANCH_ID,GROUP_ID,USER_NAME)
) ENGINE=InnoDB   COMMENT='班组人员关系表';


CREATE TABLE cc_sd_clock_detail (
  ID varchar(64) NOT NULL COMMENT 'ID',
  USER_ACC varchar(20) DEFAULT NULL COMMENT '账号',
  USER_NAME varchar(30) DEFAULT NULL COMMENT '姓名',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CONFIG_ID varchar(64) DEFAULT NULL COMMENT '周期ID',
  SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '班次编号',
  SCHEDULING_NAME varchar(64) DEFAULT NULL COMMENT '班次名称',
  WORK_START_TIME varchar(13) DEFAULT NULL COMMENT '上班时间',
  WORK_END_TIME varchar(13) DEFAULT NULL COMMENT '下班时间',
  START_CLOCK_TIME varchar(19) DEFAULT NULL COMMENT '上班打卡时间',
  END_CLOCK_TIME varchar(19) DEFAULT NULL COMMENT '下班打卡时间',
  WORK_START_STATUS varchar(2) DEFAULT NULL COMMENT '上班考勤状态',
  WORK_END_STATUS varchar(2) DEFAULT NULL COMMENT '下班考勤状态',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  CLOCK_DATE varchar(19) DEFAULT NULL COMMENT '考勤日期',
  OVERTIME_LONG varchar(5) DEFAULT NULL COMMENT '加班时长',
  LACK_LONG varchar(5) DEFAULT NULL COMMENT '迟到/早退时长',
  UPDATE_ACC varchar(64) DEFAULT NULL COMMENT '修改人账号',
  UPDATE_NAME varchar(64) DEFAULT NULL COMMENT '修改人姓名',
  UPDATE_DEPT varchar(64) DEFAULT NULL COMMENT '修改人部门',
  UPDATE_TIME varchar(19) DEFAULT NULL COMMENT '修改时间',
  IS_SHIFTS varchar(2) DEFAULT 'N' COMMENT '是否换班',
  OUTBOUND_NOTREADY_TIME varchar(10) DEFAULT '0' COMMENT '置忙总时长',
  OUTBOUND_NOTREADY_TIMEOUT varchar(10) DEFAULT '0' COMMENT '置忙超时时长',
  IS_OUTBOUND_NOTREADY_TIMEOUT varchar(10) DEFAULT 'N' COMMENT '是否置忙超时',
  NORMAL_COUNT varchar(2) DEFAULT NULL COMMENT '正常次数',
  LATE_COUNT varchar(2) DEFAULT NULL COMMENT '迟到次数',
  LATE_TIME varchar(5) DEFAULT NULL COMMENT '迟到累积时长',
  EARLY_COUNT varchar(5) DEFAULT NULL COMMENT '早退次数',
  EARLY_TIME varchar(5) DEFAULT NULL COMMENT '早退累积时长',
  IS_NOT_WORK varchar(2) DEFAULT NULL COMMENT '是否计算旷工',
  IS_LEAVE varchar(2) DEFAULT 'N' COMMENT '是否请假 Y-是 N-否',
  RECORD_STATUS varchar(2) DEFAULT '01' COMMENT '考勤记录状态 01-执行中 02-执行完成',
  NOT_CLOCK_COUNT int(2) DEFAULT '0' COMMENT '未打卡次数',
  SCHEDULING_DETAIL_ID varchar(64) DEFAULT NULL COMMENT '排班明细记录ID',
  PRIMARY KEY (ID),
  KEY IDX_CSSD_CONFIG (USER_ACC)
) ENGINE=InnoDB   COMMENT='考勤记录表';


CREATE TABLE cc_sd_clock_detail2 (
  ID varchar(64) NOT NULL COMMENT 'ID',
  USER_ACC varchar(20) DEFAULT NULL COMMENT '账号',
  USER_NAME varchar(30) DEFAULT NULL COMMENT '姓名',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CONFIG_ID varchar(64) DEFAULT NULL COMMENT '周期ID',
  SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '班次编号',
  SCHEDULING_NAME varchar(64) DEFAULT NULL COMMENT '班次名称',
  WORK_START_TIME varchar(13) DEFAULT NULL COMMENT '上班时间',
  WORK_END_TIME varchar(13) DEFAULT NULL COMMENT '下班时间',
  START_CLOCK_TIME varchar(19) DEFAULT NULL COMMENT '上班打卡时间',
  END_CLOCK_TIME varchar(19) DEFAULT NULL COMMENT '下班打卡时间',
  WORK_START_STATUS varchar(2) DEFAULT NULL COMMENT '上班考勤状态',
  WORK_END_STATUS varchar(2) DEFAULT NULL COMMENT '下班考勤状态',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  CLOCK_DATE varchar(19) DEFAULT NULL COMMENT '考勤日期',
  OVERTIME_LONG varchar(5) DEFAULT NULL COMMENT '加班时长',
  LACK_LONG varchar(5) DEFAULT NULL COMMENT '迟到/早退时长',
  UPDATE_ACC varchar(64) DEFAULT NULL COMMENT '修改人账号',
  UPDATE_NAME varchar(64) DEFAULT NULL COMMENT '修改人姓名',
  UPDATE_DEPT varchar(64) DEFAULT NULL COMMENT '修改人部门',
  UPDATE_TIME varchar(19) DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (ID),
  KEY IDX_CSSD_CONFIG (USER_ACC)
) ENGINE=InnoDB   COMMENT='考勤记录表';


CREATE TABLE cc_sd_clock_record (
  ID varchar(64) NOT NULL,
  CLOCK_DATE varchar(30) DEFAULT NULL COMMENT '打卡日期',
  USER_ACC varchar(64) DEFAULT NULL COMMENT '坐席账号',
  USER_NAME varchar(64) DEFAULT NULL COMMENT '坐席名称',
  CLOCK_TIME varchar(30) DEFAULT NULL COMMENT '打卡时间',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  CREATE_ACC varchar(30) DEFAULT NULL COMMENT '创建人账号',
  PRIMARY KEY (ID),
  KEY IDX_CCSD_CLOCK_RECORD2 (ENT_ID,BUSI_ORDER_ID,CLOCK_DATE)
) ENGINE=InnoDB   COMMENT='打卡明细记录';


CREATE TABLE cc_sd_day_task (
  ID varchar(64) NOT NULL COMMENT 'ID',
  SCHEDULING_DETAIL_ID varchar(64) DEFAULT NULL COMMENT '排班详细记录表ID',
  TASK_DATE varchar(10) DEFAULT NULL COMMENT '日程日期，格式如 2020-06-20',
  WORK_TYPE varchar(20) DEFAULT NULL COMMENT '工作类型,取数据字典 USER_WORK_TYPE 1-正常办公 2-在家办公 3-SOHO模式 字典可由用户修改',
  POSITION varchar(20) DEFAULT NULL COMMENT '岗位属性,取数据字典 USER_POSITION 1-班长 2-坐席 用户可以修改',
  WORK_SUMMARY varchar(1000) DEFAULT NULL COMMENT '工作小结',
  OTHER_SUMMARY varchar(1000) DEFAULT NULL COMMENT '其他工作内容',
  EX_JSON varchar(1000) DEFAULT NULL COMMENT '扩展字段(JSON),存储外呼数、呼入数、在线服务数等字段',
  CREATE_USER_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(50) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT_CODE varchar(30) DEFAULT NULL COMMENT '创建人部门编号',
  CREATE_DEPT_NAME varchar(50) DEFAULT NULL COMMENT '创建人部门名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  STATUS varchar(10) DEFAULT '1' COMMENT '状态, 取数据字典 1-待提交 2-已提交',
  PRIMARY KEY (ID),
  KEY IDX_SD_CSDT_USER (CREATE_USER_ACC,ENT_ID,BUSI_ORDER_ID,CREATE_TIME)
) ENGINE=InnoDB   COMMENT='排班日程信息,用户当天的工作内容';


CREATE TABLE cc_sd_day_task_detail (
  ID varchar(64) NOT NULL COMMENT 'ID',
  DAY_TASK_ID varchar(64) DEFAULT NULL COMMENT '日程记录ID',
  START_TIME varchar(10) DEFAULT NULL COMMENT '开始时间',
  END_TIME varchar(10) DEFAULT NULL COMMENT '结束时间',
  CONTENT varchar(2000) DEFAULT NULL COMMENT '工作内容',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '所属企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_SD_CSDTD_TASK (DAY_TASK_ID,ENT_ID,BUSI_ORDER_ID)
) ENGINE=InnoDB   COMMENT='排班日程明细信息';


CREATE TABLE cc_sd_day_task_rece (
  ID varchar(64) NOT NULL COMMENT 'ID',
  DAY_TASK_ID varchar(64) DEFAULT NULL COMMENT '日程记录ID',
  FIRST_READ_TIME varchar(19) DEFAULT NULL COMMENT '首次阅读时间',
  RECE_ACC varchar(30) DEFAULT NULL COMMENT '接收人账号',
  READ_TIMES int(11) DEFAULT '0' COMMENT '阅读次数',
  RECE_TYPE varchar(10) DEFAULT '2' COMMENT '接收类型,1-汇报 2-抄送',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_SC_CSDTR_TASK (DAY_TASK_ID,ENT_ID,CREATE_TIME,BUSI_ORDER_ID),
  KEY IDX_SC_CSDTR_TIME (ENT_ID,BUSI_ORDER_ID,RECE_ACC,CREATE_TIME)
) ENGINE=InnoDB   COMMENT='日程接收人';


CREATE TABLE cc_sd_emp_report (
  ID varchar(64) NOT NULL,
  EMP_REPORT_NAME varchar(64) DEFAULT NULL COMMENT '员工报备类别名称',
  CREATE_ACC varchar(30) DEFAULT '1' COMMENT '操作员账号',
  CREATE_NAME varchar(50) DEFAULT NULL COMMENT '操作员名称',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  CREATE_TIME varchar(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (ID)
) ENGINE=InnoDB  ;


CREATE TABLE cc_sd_emp_report_log (
  ID varchar(64) NOT NULL,
  EMP_REPORT_ID varchar(64) DEFAULT NULL COMMENT '员工报备类别名称',
  NEED_CLOCK_RECORD_ID varchar(64) DEFAULT NULL COMMENT '需打卡明细ID',
  CREATE_ACC varchar(30) DEFAULT '1' COMMENT '操作员账号',
  CREATE_NAME varchar(50) DEFAULT NULL COMMENT '操作员名称',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  EMP_REPORT_RESON varchar(500) DEFAULT NULL COMMENT '报备原因',
  CREATE_TIME varchar(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (ID)
) ENGINE=InnoDB  ;


CREATE TABLE cc_sd_leave (
  ID varchar(64) NOT NULL COMMENT 'ID',
  APPLY_ACC varchar(20) DEFAULT NULL COMMENT '申请人账号',
  APPLY_NAME varchar(30) DEFAULT NULL COMMENT '申请人名称',
  GROUP_ID varchar(64) DEFAULT NULL COMMENT '班级ID',
  GROUP_NAME varchar(30) DEFAULT NULL COMMENT '班级名称',
  SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '班次ID',
  SCHEDULING_NAME varchar(30) DEFAULT NULL COMMENT '班次名称',
  LEAVE_TYPE varchar(10) DEFAULT NULL COMMENT '1-产假 2-流产假 3-陪护假 4-婚假 5-取环假 6-上环假7-结扎假 8-病假 9-事假\n            \n            对于请假原因为：产假、流产假、陪护假、婚假、取环假、上环假、结扎假、病假，必须提交附件。事假，不用提交附件。\n            \n            业务字典：SD_LEAVE_TYPE',
  LEAVE_REASON varchar(500) DEFAULT NULL COMMENT '请假事由',
  LEAVE_DATE varchar(13) DEFAULT NULL COMMENT '请假日期',
  LEAVE_START_TIME varchar(19) DEFAULT NULL COMMENT '请假开始时间',
  LEAVE_END_TIME varchar(19) DEFAULT NULL COMMENT '请假结束时间',
  LEAVE_TIME_LONG decimal(8,0) DEFAULT NULL COMMENT '一天为8H，员工手工填写，请假为8H的日期，将其班次系统自动调整为休整；请假为小于8H的，扣休时长添加在临时加扣休中',
  URGENT_CONTACTS_NAME varchar(30) DEFAULT NULL COMMENT '紧急联系人',
  URGENT_CONTACTS_PHONE varchar(39) DEFAULT NULL COMMENT '紧急联系人电话',
  APPLY_TIME varchar(19) DEFAULT NULL COMMENT '申请时间',
  SQUAD_LEADER_ACC varchar(20) DEFAULT NULL COMMENT '班长审核人账号',
  SQUAD_LEADER_NAME varchar(30) DEFAULT NULL COMMENT '班长审核人姓名',
  SQUAD_LEADER_CHECK_TIME varchar(19) DEFAULT NULL COMMENT '班长审核时间',
  SQUAD_LEADER_CHECK_DESC varchar(300) DEFAULT NULL COMMENT '班长审核描述',
  SATRAP_ACC varchar(20) DEFAULT NULL COMMENT '主管审核人账号',
  SATRAP_NAME varchar(30) DEFAULT NULL COMMENT '主管审核人姓名',
  SATRAP_CHECK_TIME varchar(19) DEFAULT NULL COMMENT '主管审核时间',
  SATRAP_CHECK_DESC varchar(300) DEFAULT NULL COMMENT '主管审核描述',
  STATUS varchar(2) DEFAULT NULL COMMENT '1-待班长审核 2-待主管审核 3-审核通过 4-审核不通过\n            \n            业务字典：REST_CHECK_STATUS',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门编号',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  DETAIL_ID varchar(64) DEFAULT NULL COMMENT '排班详情ID',
  IS_CLOCK varchar(2) DEFAULT 'N' COMMENT '是否需要打卡 Y-是 N-否',
  PRIMARY KEY (ID),
  KEY IDX_CSL_USER (APPLY_ACC,LEAVE_TYPE,LEAVE_DATE,STATUS)
) ENGINE=InnoDB   COMMENT='请假信息表';


CREATE TABLE cc_sd_need_clock_record (
  ID varchar(64) NOT NULL,
  CLOCK_DATE varchar(30) DEFAULT NULL COMMENT '打卡日期',
  USER_ACC varchar(64) DEFAULT NULL COMMENT '坐席账号',
  USER_NAME varchar(64) DEFAULT NULL COMMENT '坐席名称',
  CLOCK_START_TIME varchar(30) DEFAULT NULL COMMENT '打卡开始时间',
  CLOCK_END_TIME varchar(30) DEFAULT NULL COMMENT '打卡开始时间',
  CLOCK_TIME varchar(30) DEFAULT NULL COMMENT '实际打卡时间',
  NORMAL_CLOCK_TIME varchar(30) DEFAULT NULL COMMENT '正常打卡时间',
  STATUS varchar(30) DEFAULT NULL COMMENT '状态（正常打卡、请假、未打卡）',
  CLOCK_TYPE int(2) DEFAULT NULL COMMENT '打卡类型 1.上班打卡 2.下班打卡',
  LATE_TIME int(10) DEFAULT '0' COMMENT '迟到时长',
  LATE_COUNT int(10) DEFAULT '0' COMMENT '迟到次数',
  EARLY_TIME int(10) DEFAULT '0' COMMENT '早退时长',
  EARLY_COUNT int(10) DEFAULT '0' COMMENT '早退次数',
  SOURCE varchar(30) DEFAULT NULL COMMENT '来源',
  IS_ACROSS varchar(2) DEFAULT 'N' COMMENT '是否跨天',
  SCHEDULING_DETAIL_ID varchar(64) DEFAULT NULL COMMENT '排班明细记录ID',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  PRIMARY KEY (ID)
) ENGINE=InnoDB  ;


CREATE TABLE cc_sd_post (
  POST_ID varchar(64) NOT NULL COMMENT '岗位ID',
  POST_NAME varchar(64) DEFAULT NULL COMMENT '岗位名称',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (POST_ID),
  KEY IDX_CSP_BRANCH (POST_NAME,BRANCH_ID)
) ENGINE=InnoDB   COMMENT='岗位信息表';


CREATE TABLE cc_sd_post_user (
  ID varchar(64) NOT NULL COMMENT 'ID',
  POST_ID varchar(64) NOT NULL COMMENT '岗位ID',
  USER_ACC varchar(64) DEFAULT NULL COMMENT '用户账号',
  USER_NAME varchar(64) DEFAULT NULL COMMENT '用户姓名',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_CSPU_POST (POST_ID,USER_NAME)
) ENGINE=InnoDB   COMMENT='岗位人员关系表';


CREATE TABLE cc_sd_rest (
  ID varchar(64) NOT NULL COMMENT 'ID',
  APPLY_ACC varchar(20) DEFAULT NULL COMMENT '申请人账号',
  APPLY_NAME varchar(30) DEFAULT NULL COMMENT '申请人名称',
  GROUP_ID varchar(64) DEFAULT NULL COMMENT '班级ID',
  GROUP_NAME varchar(30) DEFAULT NULL COMMENT '班级名称',
  REST_REASON varchar(500) DEFAULT NULL COMMENT '调休原因',
  REST_START_DATE varchar(19) DEFAULT NULL COMMENT '调休开始时间',
  REST_END_DATE varchar(19) DEFAULT NULL COMMENT '调休结束时间',
  URGENT_CONTACTS_NAME varchar(30) DEFAULT NULL COMMENT '紧急联系人',
  URGENT_CONTACTS_PHONE varchar(39) DEFAULT NULL COMMENT '紧急联系人电话',
  APPLY_TIME varchar(19) DEFAULT NULL COMMENT '申请时间',
  SQUAD_LEADER_ACC varchar(20) DEFAULT NULL COMMENT '班长审核人账号',
  SQUAD_LEADER_NAME varchar(30) DEFAULT NULL COMMENT '班长审核人姓名',
  SQUAD_LEADER_CHECK_TIME varchar(19) DEFAULT NULL COMMENT '班长审核时间',
  SQUAD_LEADER_CHECK_DESC varchar(300) DEFAULT NULL COMMENT '班长审核描述',
  SATRAP_ACC varchar(20) DEFAULT NULL COMMENT '主管审核人账号',
  SATRAP_NAME varchar(30) DEFAULT NULL COMMENT '主管审核人姓名',
  SATRAP_CHECK_TIME varchar(19) DEFAULT NULL COMMENT '主管审核时间',
  SATRAP_CHECK_DESC varchar(300) DEFAULT NULL COMMENT '主管审核描述',
  STATUS varchar(2) DEFAULT NULL COMMENT '1-待班长审核 2-待主管审核 3-审核通过 4-班长审核不通过 5-主管审核不通过\n            \n            业务字典：REST_CHECK_STATUS',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门编号',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_CSR_USER (APPLY_ACC,STATUS)
) ENGINE=InnoDB   COMMENT='调休信息表';


CREATE TABLE cc_sd_scheduling (
  SCHEDULING_ID varchar(64) NOT NULL COMMENT '班次ID',
  SCHEDULING_NAME varchar(64) DEFAULT NULL COMMENT '班次名称',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  START_TIME varchar(19) DEFAULT NULL COMMENT '开始时间',
  END_TIME varchar(19) DEFAULT NULL COMMENT '结束时间',
  IS_ACROSS_DAY varchar(19) DEFAULT NULL COMMENT '是否跨天 Y是N否',
  WORK_TIME_LONG varchar(10) DEFAULT NULL,
  REST_START_TIME_1 varchar(19) DEFAULT NULL COMMENT '中途休息开始时间',
  REST_END_TIME_1 varchar(19) DEFAULT NULL COMMENT '中途休息结束时间',
  CLOCK_START_TIME varchar(5) DEFAULT NULL COMMENT '打卡开始计算时长（min）',
  CLOCK_END_TIME varchar(5) DEFAULT NULL COMMENT '打卡结束计算时间(min)',
  CLOCK_START_OVERTIME varchar(19) DEFAULT NULL COMMENT '加班开始计算时间',
  CLOCK_END_OVERTIME varchar(19) DEFAULT NULL COMMENT '下班之后加班开始计算时间',
  COLOR varchar(30) DEFAULT NULL,
  STATUS varchar(2) DEFAULT NULL COMMENT '启用状态 字典SF_YN',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT varchar(30) DEFAULT NULL COMMENT '创建人部门编号',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  OUTBOUND_NOTREADY_TIMELONG varchar(10) DEFAULT '0' COMMENT '每小时允许置忙时长  0为不计',
  CLOCK_JSON varchar(648) DEFAULT NULL COMMENT '考请明细字符串',
  ABSENTEEISM_TIME varchar(10) DEFAULT NULL COMMENT '旷工累计时长（MIN）',
  PRIMARY KEY (SCHEDULING_ID),
  KEY IDX_CSS_BRANCH (BRANCH_ID,SCHEDULING_NAME)
) ENGINE=InnoDB   COMMENT='班次信息表';


CREATE TABLE cc_sd_scheduling_config (
  ID varchar(64) NOT NULL COMMENT 'ID',
  NAME varchar(64) DEFAULT NULL COMMENT '名称',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  START_DATE varchar(19) DEFAULT NULL COMMENT '开始日期',
  END_DATE varchar(19) DEFAULT NULL COMMENT '结束日期',
  FINALIZE_DATE varchar(19) DEFAULT NULL COMMENT '定稿日期',
  USER_TYPE varchar(2) DEFAULT NULL COMMENT '1-按人员 2-按班组',
  STATUS varchar(2) DEFAULT NULL COMMENT '状态  01-待配置  02-待定稿   03-已定稿',
  USER_LIST varchar(1024) DEFAULT NULL COMMENT '参与人员集合 JSON串',
  USER_COUNT varchar(2) DEFAULT NULL COMMENT '参与人数',
  SCHEDULING_LIST varchar(1024) DEFAULT NULL COMMENT '班次集合 JSON串',
  POST_LIST varchar(1024) DEFAULT NULL COMMENT '岗位集合 JSON串',
  WORK_TIME_LONG varchar(19) DEFAULT NULL COMMENT '法定工时',
  CONFIG_JSON varchar(4096) DEFAULT NULL COMMENT '周期配置信息JSON串',
  SPECIAL_CONFIG_JSON varchar(4096) DEFAULT NULL COMMENT '特殊规则配置信息JSON串',
  IS_CREATE_TABLE varchar(2) DEFAULT NULL COMMENT '是否生成班表',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  STRATEGY_JSON varchar(4096) DEFAULT NULL COMMENT '排班策略规则',
  PRIMARY KEY (ID),
  KEY IDX_CSSC_BRANCH (BRANCH_ID,STATUS,IS_CREATE_TABLE)
) ENGINE=InnoDB   COMMENT='排班表';


CREATE TABLE cc_sd_scheduling_detail (
  ID varchar(64) NOT NULL COMMENT 'ID',
  USER_ACC varchar(20) DEFAULT NULL COMMENT '账号',
  USER_NAME varchar(30) DEFAULT NULL COMMENT '姓名',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  BRANCH_NAME varchar(64) DEFAULT NULL COMMENT '分中心名称',
  SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '班次ID',
  SCHEDULING_NAME varchar(50) DEFAULT NULL COMMENT '班次名称',
  POST_ID varchar(64) DEFAULT NULL COMMENT '岗位ID',
  POST_NAME varchar(64) DEFAULT NULL COMMENT '岗位名称',
  PLAN_DATE varchar(19) DEFAULT NULL COMMENT '日期',
  START_TIME varchar(19) DEFAULT NULL COMMENT '开始时间',
  END_TIME varchar(19) DEFAULT NULL COMMENT '结束时间',
  WORK_TIME_LONG varchar(10) DEFAULT NULL COMMENT '上班时长',
  WORK_TYPE varchar(2) DEFAULT NULL COMMENT '工作情况 1-正常出勤 2-请假 3-临时下班',
  REALITY_TIME_LONG varchar(10) DEFAULT NULL COMMENT '实际工作时长',
  IS_ACROSS varchar(10) DEFAULT NULL COMMENT '是否跨天 Y-是 N-否\n            \n            数据字典：SF_YN ',
  STATUS varchar(2) DEFAULT NULL COMMENT '状态 1-可用 2-禁用\n            \n            数据字典：PB_PERSON_STATE',
  CONFIG_ID varchar(64) DEFAULT NULL COMMENT '排班配置表ID',
  OVER_TIME_LONG varchar(8) DEFAULT NULL COMMENT '加班时长',
  OVER_TIME_REASON varchar(500) DEFAULT NULL COMMENT '加班原因',
  UPDATE_ACC varchar(64) DEFAULT NULL COMMENT '加班修改人账号',
  UPDATE_NAME varchar(20) DEFAULT NULL COMMENT '加班修改人姓名',
  REST_START_TIME_1 varchar(19) DEFAULT NULL COMMENT '中途休息开始时间',
  REST_END_TIME_1 varchar(19) DEFAULT NULL COMMENT '中途休息结束时间',
  REST_START_TIME_2 varchar(19) DEFAULT NULL COMMENT '第二轮休息开始时间',
  REST_ENT_TIME_2 varchar(19) DEFAULT NULL COMMENT '第二轮休息结束时间',
  PREV_SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '原班次ID',
  PREV_SCHEDULING_NAME varchar(30) DEFAULT NULL COMMENT '原班次名称',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  IS_SHIFTS varchar(2) DEFAULT 'N' COMMENT '是否换班',
  PRIMARY KEY (ID),
  KEY IDX_CSSD_CONFIG (USER_ACC,CONFIG_ID)
) ENGINE=InnoDB   COMMENT='人员排班';


CREATE TABLE cc_sd_scheduling_log (
  ID varchar(64) NOT NULL COMMENT 'ID',
  CONFIG_ID varchar(64) DEFAULT NULL COMMENT '排班配置表ID',
  PLAN_DATE varchar(19) DEFAULT NULL COMMENT '日期',
  CONTENT text COMMENT '日志内容',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (ID),
  KEY IDX_CSSL_CONFIGID (CONFIG_ID,PLAN_DATE)
) ENGINE=InnoDB   COMMENT='排班过程日志表';


CREATE TABLE cc_sd_shifts (
  ID varchar(64) NOT NULL COMMENT 'ID',
  SCHEDULING_DATE varchar(10) DEFAULT NULL COMMENT '排班日期',
  SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '班次ID',
  SCHEDULING_NAME varchar(30) DEFAULT NULL COMMENT '班次名称',
  APPLY_ACC varchar(20) DEFAULT NULL COMMENT '申请人账号',
  APPLY_NAME varchar(30) DEFAULT NULL COMMENT '申请人名称',
  SHIFT_REASON varchar(300) DEFAULT NULL COMMENT '换班原因',
  NEW_SCHEDULING_DATE varchar(10) DEFAULT NULL COMMENT '新排班日期',
  NEW_SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '新班次ID',
  NEW_SCHEDULING_NAME varchar(30) DEFAULT NULL COMMENT '新班次名称',
  SHIFT_SCHEDULING_ID varchar(64) DEFAULT NULL COMMENT '换班人班级ID',
  SHIFT_ACC varchar(20) DEFAULT NULL COMMENT '换班人账号',
  SHIFT_NAME varchar(30) DEFAULT NULL COMMENT '换班人名称',
  CHECK_ACC varchar(20) DEFAULT NULL COMMENT '审核人账号',
  CHECK_NAME varchar(30) DEFAULT NULL COMMENT '审核姓名',
  CHECK_INFO varchar(500) DEFAULT NULL COMMENT '审核信息',
  STATUS varchar(2) DEFAULT NULL COMMENT '1-待确认 2-待审核 3-未同意 4-审核通过\n            \n            业务字典：PB_SHIFT_STATUS',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门编号',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  SHIFTS_TYPE varchar(10) DEFAULT NULL COMMENT '换班类型(1.整班 2.时间段)',
  SHIFTS_START_TIME varchar(10) DEFAULT NULL COMMENT '换班开始时间',
  SHIFTS_END_TIME varchar(10) DEFAULT NULL COMMENT '换班结束时间',
  CHECK_START_TIME varchar(10) DEFAULT NULL COMMENT '目标开始时间',
  CHECK_END_TIME varchar(10) DEFAULT NULL COMMENT '目标结束时间',
  PRIMARY KEY (ID)
) ENGINE=InnoDB   COMMENT='换班信息表';


CREATE TABLE cc_sd_user_config (
  ID varchar(64) NOT NULL COMMENT 'ID',
  CONFIG_ID varchar(64) DEFAULT NULL COMMENT '排班配置ID',
  BRANCH_ID varchar(64) DEFAULT NULL COMMENT '分中心ID',
  CLASS_GROUP_ID varchar(64) DEFAULT NULL COMMENT '班组ID',
  USER_ACC varchar(20) DEFAULT NULL COMMENT '人员账号',
  USER_NAME varchar(30) DEFAULT NULL COMMENT '人员姓名',
  MIN_WORK_TIME_LONG varchar(10) DEFAULT NULL COMMENT '最少工时',
  MAX_WORK_TIME_LONG varchar(10) DEFAULT NULL COMMENT '最大工时',
  WORK_TIME varchar(10) DEFAULT NULL COMMENT '剩余工时',
  NO_DATE varchar(520) DEFAULT NULL COMMENT '不参与排班日期JSON串',
  NO_SCHEDULING varchar(520) DEFAULT NULL COMMENT '不参与班次JSON串',
  CREATE_ACC varchar(20) DEFAULT NULL COMMENT '创建人账号',
  CREATE_USER_NAME varchar(30) DEFAULT NULL COMMENT '创建人名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(10) DEFAULT NULL COMMENT '所属企业编号',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '业务订购ID',
  CREATE_DEPT varchar(64) DEFAULT NULL COMMENT '创建人部门',
  PRIMARY KEY (ID)
) ENGINE=InnoDB   COMMENT='排班人员配置信息表';


CREATE TABLE cc_sd_worktime_class (
  ID varchar(64) NOT NULL,
  WORKTIME_CLASS_NAME varchar(128) DEFAULT NULL COMMENT '工时分类名称',
  WORKTIME_DEFAULT varchar(10) DEFAULT NULL COMMENT '默认工时',
  WORKTIME_CLASS_TYPE varchar(10) DEFAULT NULL COMMENT '工时类型 1.增加 2.减少',
  REMAKE varchar(256) DEFAULT NULL COMMENT '描述',
  CREATE_ACC varchar(30) DEFAULT '1' COMMENT '创建人账号',
  CREATE_NAME varchar(50) DEFAULT NULL COMMENT '创建人名称',
  CREATE_DEPT varchar(30) DEFAULT NULL COMMENT '创建人部门编号',
  CREATE_DEPT_NAME varchar(100) DEFAULT NULL COMMENT '创建人部门名称',
  CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_CSWC_WORKTIME_CLASS1 (ENT_ID,BUSI_ORDER_ID,WORKTIME_CLASS_NAME)
) ENGINE=InnoDB   COMMENT='工时类型';


CREATE TABLE cc_sd_worktime_log (
  ID varchar(64) NOT NULL,
  CLOCK_DETAIL_ID varchar(64) DEFAULT NULL COMMENT '工时分类名称',
  WORKTIME_CLASS_ID varchar(64) DEFAULT NULL COMMENT '工时分类名称',
  WORKTIME_CLASS_NAME varchar(64) DEFAULT NULL COMMENT '工时分类名称',
  WORKTIME_CLASS_TYPE varchar(10) DEFAULT NULL COMMENT '工时分类类型',
  WORKTIME_LONG varchar(10) DEFAULT NULL COMMENT '工时',
  CREATE_ACC varchar(30) DEFAULT '1' COMMENT '操作员账号',
  CREATE_NAME varchar(50) DEFAULT NULL COMMENT '操作员名称',
  ENT_ID varchar(30) DEFAULT NULL COMMENT '企业ID',
  BUSI_ORDER_ID varchar(64) DEFAULT NULL COMMENT '企业业务订购ID',
  PRIMARY KEY (ID),
  KEY IDX_CSWL_WORKTIME_LOG1 (ENT_ID,BUSI_ORDER_ID,WORKTIME_CLASS_ID,WORKTIME_CLASS_TYPE),
  KEY IDX_CSWL_WORKTIME_LOG2 (CLOCK_DETAIL_ID)
) ENGINE=InnoDB   COMMENT='工时调整日志';

