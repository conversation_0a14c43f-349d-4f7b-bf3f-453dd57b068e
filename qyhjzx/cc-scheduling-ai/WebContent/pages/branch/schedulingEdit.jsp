<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="班次管理"></title>
	<link href="${ctxPath}/static/css/bootstrap-colorpicker.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off"  id="schedulingEditForm" data-mars="schedulingDao.schedulingInfo" method="post" autocomplete="off" data-mars-prefix="CC_SD_SCHEDULING." >
		<input name="CC_SD_SCHEDULING.SCHEDULING_ID" id="SCHEDULING_ID" type="hidden" value='${param.schedulingId}'>
		<input name="CC_SD_SCHEDULING.BRANCH_ID" type="hidden" value="${param.branchId}"><br/>
		<table class="table  table-vzebra mt-5">
	    	<tbody>
	        	<tr>
		        	<td class="required" i18n-content="班次名称"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.SCHEDULING_NAME" maxlength="20" data-rules="required" class="form-control input-sm"></td>
		        	<td class="required"  i18n-content="上班时长(h)"></td>
		          	<td><input id="WORK_TIME_LONG" type="text" name="CC_SD_SCHEDULING.WORK_TIME_LONG"  maxlength="5" data-rules="required|number" class="form-control input-sm"></td>
		        </tr>
		        <tr>
		        	<td class="required" i18n-content="开始时间"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.START_TIME" id="START_TIME" data-rules="required" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'HH:mm:00',autoPickDate:true})"></td>
		            <td class="required" i18n-content="结束时间"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.END_TIME" id="END_TIME" data-rules="required" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'HH:mm:00',autoPickDate:true})"></td>
		        </tr>
		        <tr>
		        	<td i18n-content="休息开始时间"></td>
		        	<td><input type="text" name="CC_SD_SCHEDULING.REST_START_TIME_1" id="REST_START_TIME_1" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'HH:mm:00',autoPickDate:true})"></td>
		          	<td i18n-content="休息结束时间"></td>
		           	<td><input type="text" name="CC_SD_SCHEDULING.REST_END_TIME_1" id="REST_END_TIME_1" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'HH:mm:00',autoPickDate:true})"></td>
		   		</tr>
				<tr>
		        	<td class="required" i18n-content="是否跨天"></td>
		         	<td>
			         	<select name="CC_SD_SCHEDULING.IS_ACROSS_DAY" data-mars="commonDao.getDict(SF_YN)" data-rules="required" class="form-control input-sm" >
							<option value="" i18n-content="请选择"></option>
						</select>
		           	</td>
		           <!-- 	<td class="required">状态</td>
		        	<td>
		             	<select name="CC_SD_SCHEDULING.STATUS" data-mars="commonDao.getDict(ENABLE_STATUS)"   data-rules="required" class="form-control input-sm" >
							<option value="">请选择</option>
						</select>
		        	</td> -->
		           	<td class="required" i18n-content="颜色"></td>
		        	<td>
		             	<input type="text"  id="color" name="CC_SD_SCHEDULING.COLOR" data-rules="required" class="form-control input-sm" />
		        	</td>
		        </tr>
		        <tr>
		        	<td class="required" i18n-content="打卡开始计算时长(min)"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.CLOCK_START_TIME" value="120" maxlength="20" data-rules="required" class="form-control input-sm"></td>
		            <td class="required" i18n-content="打卡结束计算时长(min)"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.CLOCK_END_TIME" value="300" maxlength="20" data-rules="required" class="form-control input-sm"></td>
		   		</tr>
		        <tr>
		        	<td class="required" i18n-content="上班之前加班开始计算时间"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.CLOCK_START_OVERTIME" id="CLOCK_START_OVERTIME" data-rules="required" class="form-control input-sm"></td>
		        	<td class="required" i18n-content="下班之后加班开始计算时间"></td>
		            <td><input type="text" name="CC_SD_SCHEDULING.CLOCK_END_OVERTIME" id="CLOCK_END_OVERTIME" data-rules="required" class="form-control input-sm"></td>
		   		</tr>
	     	</tbody>
		</table>
		<div class="layer-foot text-c">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="schedulingEditTmp.ajaxSubmitForm(this)" i18n-content="保存"></button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
	   </div>
	</form>		
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/bootstrap-colorpicker.js" ></script>
	<script type="text/javascript">
	
	jQuery.namespace("schedulingEditTmp");
	
	layui.use(['laydate'], function(){
		var laydate = layui.laydate;
		laydate.render({
			elem: '#START_TIME',
			type: 'time'
		});
		laydate.render({
			elem: '#END_TIME',
			type: 'time'
		});
		laydate.render({
			elem: '#REST_START_TIME_1',
			type: 'time'
		});
		laydate.render({
			elem: '#REST_END_TIME_1',
			type: 'time'
		});
		laydate.render({
			elem: '#CLOCK_START_OVERTIME',
			type: 'time'
		});
		laydate.render({
			elem: '#CLOCK_END_OVERTIME',
			type: 'time'
		});
	});
	
	$(function(){
		$("#schedulingEditForm").render({success:function() {
			execI18n();
		}});
		 $('#color').colorpicker();
	});
	
	schedulingEditTmp.schedulingId = '${param.schedulingId}';
	
	schedulingEditTmp.ajaxSubmitForm = function(obj){
		if(!form.validate("#schedulingEditForm")){
			return;
		};
		var workTimeLong = $("#WORK_TIME_LONG").val();
		arr = workTimeLong.split(".");
		if(arr[0] == ""){
			layer.alert(getI18nValue("上班时长格式不正确"), {icon: 5});
			$("#WORK_TIME_LONG").focus();
			return;
		}
		if(workTimeLong < 0){
			layer.alert(getI18nValue("上班时长不能小于0"), {icon: 5});
			$("#WORK_TIME_LONG").focus();
			return;
		}
		var data = form.getJSONObject("schedulingEditForm");
		ajax.remoteCall("${ctxPath}/servlet/scheduling?action=edit",data,function(result) { 
			if(result.state == 1){
				$(obj).attr("disabled","disabled");
				layer.msg(result.msg,{icon:1,time:1200},function(){
					layer.closeAll();
					schedulingListTmp.searchData();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>