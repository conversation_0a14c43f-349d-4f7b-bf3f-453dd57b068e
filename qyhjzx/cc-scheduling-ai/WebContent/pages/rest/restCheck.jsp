<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="调休审核"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off"  id="restCheckForm" data-pk="" data-mars="restDao.restInfo" method="post" autocomplete="off" data-mars-prefix="CC_SD_REST." >
		<input type="hidden" id="REST_ID" name="CC_SD_REST.ID" value="${param.id}" />
		<table class="table  table-vzebra mt-120">
        	<tbody>
            	<tr>
					<td class="required" i18n-content="班组"></td>
					<td>
						<input readonly="readonly" type="text" name="CC_SD_REST.GROUP_NAME" value="${param.groupName}" class="form-control input-sm" style="width:180px">
					</td>
                 	<td class="required" i18n-content="姓名"></td>
					<td>
						<input readonly="readonly" type="text" name="CC_SD_REST.APPLY_NAME" value="${param.applyName}" class="form-control input-sm" style="width:180px">
					</td>
                </tr>
              	<tr>
               		<td class="required" i18n-content="开始时间"></td>
					<td><input readonly="readonly" type="text" id="REST_START_DATE" data-rules="required"  name="CC_SD_REST.REST_START_DATE"  class="form-control input-sm"></td>
					<td class="required" i18n-content="结束时间"></td>
					<td><input readonly="readonly" type="text" id="REST_END_DATE" data-rules="required"  name="CC_SD_REST.REST_END_DATE"  class="form-control input-sm"></td>
                 </tr>
                 <tr>
               	</tr>
	           	<tr>
					<td i18n-content="紧急联系人"></td>
					<td><input readonly="readonly" type="text" name="CC_SD_REST.URGENT_CONTACTS_NAME"  class="form-control input-sm" style="width:180px"></td>
                 	<td i18n-content="紧急联系人电话"></td>
					<td><input readonly="readonly" type="text" name="CC_SD_REST.URGENT_CONTACTS_PHONE" class="form-control input-sm" style="width:180px"></td>
                </tr>
                <tr>			   
                  	<td class="required" i18n-content="调休原因"></td>
                	<td colspan="3"><textarea readonly="readonly" name="CC_SD_REST.REST_REASON" data-rules="required" class="form-control input-sm" rows="3"></textarea></td>
                </tr>
                <tr>
                 	<td i18n-content="当月已调休天数"></td>
                 	<td>
	                	<input readonly="readonly" type="text" name="CC_SD_REST.THIS_MONTH_REST_DAY" class="form-control input-sm" style="width:180px">
			        </td>
                 	<td i18n-content="附件"></td>
                 	<td>
	                	<button class="btn btn-sm btn-primary" type="button" onclick="restCheckTmp.attachment();"><span class="glyphicon glyphicon-folder-close"></span> <span i18n-content="附件管理"></span></button>
			        </td>
                </tr>
                <c:if test="${param.status == 2 }" >
                    <tr>
                 		<td i18n-content="主管账号"></td>
						<td><input type="text" name="SATRAP_ACC" readonly="readonly" class="form-control input-sm" value="${param.userAcc}" style="width:150px"></td>
	                 	<td i18n-content="主管名称"></td>
						<td><input type="text" name="SATRAP_NAME"readonly="readonly" class="form-control input-sm" value="${param.userName}" style="width:150px"></td>
	                </tr>
	                 <tr>
						<td class="required" i18n-content="主管审核情况"></td>
						<td>
							<select name="STATUS" data-rules="required" class="form-control input-sm" style="width:150px">
								<option value="" selected="selected" i18n-content="请选择"></option>
								<option value="3" i18n-content="通过"></option>
								<option value="5" i18n-content="不通过"></option>
							</select>
						</td>
	                </tr>
	                <tr>			   
                        <td i18n-content="主管审核描述"></td>
                        <td colspan="3"><textarea name="SATRAP_CHECK_DESC" class="form-control input-sm" rows="3" maxlength="330"></textarea></td>
                    </tr>
                </c:if>
           	</tbody>
      	</table>
	  	<div class="layer-foot text-c">
	  		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="restCheckTmp.ajaxSubmitForm()" i18n-content="提交"></button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();" i18n-content="关闭"></button>
	  	</div>
	</form>		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("restCheckTmp");
	$(function(){
		$("#restCheckForm").render({success:function() {
			execI18n();
		}});
	});
	restCheckTmp.attachment = function() {
    	var busiId = $("#REST_ID").val();
    	var busiType = "rest";
    	var requestType= "download"
    	popup.layerShow({
			type : 1,
			title : getI18nValue("文件管理"),
			offset : '20px',
			area : [ '800px', '70%' ]
		}, "${ctxPath}/servlet/attachment?action=attachment", {busiId:busiId,requestType:requestType,busiType:busiType});
	}
	restCheckTmp.ajaxSubmitForm = function(){
		if(!form.validate("#restCheckForm")){
			return;
		};
		var data = form.getJSONObject("restCheckForm");
		ajax.remoteCall("${ctxPath}/servlet/rest?action=restCheck",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					 layer.closeAll();
					 restCheckListTmp.searchData();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
 	}
	function setBusiId(busiId){
    	$("#REST_ID").val(busiId);
    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>