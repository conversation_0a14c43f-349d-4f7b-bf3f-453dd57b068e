<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="调休管理"></title>
	
			<style type="text/css">
		a:link{ color:#00adff;}
		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}

		::-webkit-scrollbar-track {
			background: transparent;
		}

		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}

		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}

		.btn {
			margin-right: 10px;
		}

		.right_div_button {
			width: 30px;
			height: 30px;
			position: absolute;
			top: 46%;
			right: -8px;
			z-index: 998;
			cursor: pointer;
			transform: rotateZ(45deg);
			background-color: #337AB7;
			border-radius: 50%;
		}

		.out_a:hover .right_div_button {
			background-color: #179F69;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}

		/* .container-fluid {
   		 	padding: 0px 15px;
		} */
		.layui-tab {
			text-align: left !important;
			margin: 5px 0px;
		}

		/* .layer-skin.layui-layer {z-index:500 !important;} */
		 
		.flex {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-row {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-item {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: auto;
}

#contents>.chat-panel{
	position: absolute;
	top:15px;
	right:15px;
	bottom:15px;
	left:15px;
}

#order_card{
	height: 100%;
	overflow-x: auto;
	/* overflow-y: hidden; */
}
body,html{height: 100%;width: 100%}

.out_a{
	position: absolute;
    top: 50%;
    right: 15px;
    z-index: 333;
}

/* .layui-table-hover{
    background-color: #409eff;
} */

.custIcon {
	color: #31b0d5;
	font-size: 17px;
	cursor:pointer;
}
	</style>
<style>
	/* .layui-layer-content{
		overflow-x: hidden !important;
	} */

	.container-fluid {
		padding-right: 0px;
		padding-left: 0px;
		margin-right: 0px;
		margin-left: 0px;
		padding: 0px;
	}
	.layui-table-click{
				/* 1. table默认是有背景色的你要保证优先加载你的,所以使用important */
				background-color: #c9e2f9 !important;
				color: #FFF;
	}
	.btn {
    margin-right: 0px;
}
.layui-table{
	margin-top: -10px;
	}

 .ibox-content{
		margin-top: 0px;
		padding-top:10px;
	} 
.ibox{
	padding:1px 0px 0px 0px;
	margin-top: 0px;
}

</style>
	
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off"  name="restCheckListForm" class="form-inline" id="restCheckListForm">
		<div style="height: 100%;" class="right">
			<div class="ibox">
				<div class="ibox-title clearfix">
					<div class="form-group">
	                    <div class="input-group input-group-sm all" style="width: 210px;">
							<span class="input-group-addon" i18n-content="申请时间"></span>
							<input type="text" class="form-control input-sm" id="startDate" name="startDate"  style="width:135px" >
							<span class="input-group-addon">-</span>	
							<input type="text" class="form-control input-sm" id="endDate" name="endDate"  style="width:135px">									  
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="申请人姓名"></span> 
							<input type="text" name="AGENT_NAME" autocomplete="off" class="form-control input-sm" style="width: 122px">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="审核状态"></span>
							<select class="form-control input-sm" name="STATUS" data-mars="commonDao.getDict(REST_CHECK_STATUS)" >
								<option value="" i18n-content="请选择"></option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="restCheckListTmp.searchData(true)">
								<span class="glyphicon glyphicon-search" ></span> <span i18n-content="查询"></span>
							</button>
						</div>
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="restCheckListTmp.reset()">
								<span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span>
							</button>
						</div>	
					</div>
				</div>
				<div class="ibox-content">
					<table id="restCheckListTable"></table>
				</div>
			</div>
		</div>
	</form>
	<script type="text/html" id="operationTpl">
  		{{#if(d.STATUS == 1 || d.STATUS == 2){}}<a class="layui-btn layui-btn-xs" lay-event="restCheckListTmp.restCheck" i18n-content="审核"></a>{{#}}}
  		{{#if(d.WITHDRAW_STATUS == 2){}}<a class="layui-btn layui-btn-xs" lay-event="restCheckListTmp.withdrawCheck" i18n-content="撤回审核"></a>{{#}}}
	</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript">
		jQuery.namespace("restCheckListTmp");
		
		$(function(){
			$("#startDate").val(getTodayDate());
	    	$("#endDate").val(getThisMonthEndDate());
			$("#restCheckListForm").render({success:function(result) {
				layui.use('laydate',function() {
	    			var laydate = layui.laydate;
	        		laydate.render({ elem: '#startDate' ,type: 'date',lang:getDateLang(),btns: ['confirm'] });
			  		laydate.render({ elem: '#endDate' ,type: 'date',lang:getDateLang(),btns: ['confirm'] });
					restCheckListTmp.loadData();
	        	});
			}});
		});
		//重置
		restCheckListTmp.reset = function(){
			$("#restCheckListForm .form-group select").val("");
	    	$("#restCheckListForm .form-group input").val("");
		};
		restCheckListTmp.loadData = function () {
        	$("#restCheckListForm").initTableEx({
            	mars:'restDao.restCheckList',
            	id:'restCheckListTable',
				height: 'full-80',
            	limit:15,
            	limits: [15,30,50,100],
            	cols: [
            		[
	            		{
            				field:'LAY_INDEX', 
            				title: getI18nValue('序号'),
            				align: 'center',
            				width:60,
							type:'numbers'
            			},
						{
							field: 'APPLY_ACC',
							title: getI18nValue('申请人'),
							align: 'center',
							minWidth:120,
						},
						{
							field: 'APPLY_NAME',
							title: getI18nValue('申请人姓名'),
							align: 'center',
							minWidth:120,
						},
						{
							field: 'REST_DATE',
							title: getI18nValue('调休时间'),
							sort:true,
							align: 'center',
							minWidth:100,
							templet: function(row) {
								return row.REST_START_DATE + " - " + row.REST_END_DATE;
							}
						},
						{
							field: 'REST_REASON',
							title: getI18nValue('调休原因'),
							align: 'center',
							minWidth:100
						},
						{
							field: 'APPLY_TIME',
							title: getI18nValue('申请时间'),
							sort:true,
							align: 'center',
							minWidth:100
						},
						{
							field: 'STATUS',
							title: getI18nValue('审核状态'),
							align: 'center',
							width:100,
							templet: function(row) {
								if(row.WITHDRAW_STATUS == '2') {
									return getI18nValue('撤回中');
								} else {
									return getDictTextByCode('REST_CHECK_STATUS',row.STATUS);
								}
							}
						},
						{
							field: 'STATUS',
							title: getI18nValue('操作'),
							align: 'center',
							minWidth:100,
							templet: '#operationTpl'
						}
					]
            	],
            	done: function() {
            		execI18n();
            	}
            });
        }

		restCheckListTmp.restCheck = function(obj) {
			ajax.remoteCall("${ctxPath}/servlet/rest?action=toRestCheck",{},function(result) { 
				if(result.state == 1){
					popup.layerShow({
						type : 1,
						title : getI18nValue('调休审核'),
						shadeClose:false,
						offset : '20px',
						area : [ '640px', '480px' ],
						url:"${ctxPath}/pages/holiday/rest/restCheck.jsp",
						data:{
							status: obj.STATUS,
							id: obj.ID,
							userAcc: result.userAcc,
							userName: result.userName
						}
					});
				}else{
					layer.alert(result.msg);
				}
			});
		}
		
		restCheckListTmp.withdrawCheck = function(obj) {
			popup.layerShow({
				type : 1,
				title : getI18nValue('调休撤销审核'),
				shadeClose:false,
				offset : '20px',
				area : [ '640px', '480px' ],
				url:"${ctxPath}/pages/holiday/rest/restWithdraw.jsp",
				data:{
					id: obj.ID
				}
			});
		}
		
		restCheckListTmp.searchData = function() {
			var t1=$("#startDate").val();
			var t2=$("#endDate").val();
			if(t1!=''&&t2!=''){
				if(t1>t2){
					layer.alert(getI18nValue("开始时间不能大于结束时间"),{icon: 2});				
					return false;
				}
			}else{
				layer.alert(getI18nValue("请输入查询时间段"),{icon: 2});	
				return false;
			}
			$("#restCheckListForm").queryData({id:'restCheckListTable'});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>