package com.yunqu.cc.email.dao;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.email.base.AppDaoContext;
import com.yunqu.cc.email.base.CommonLogger;
import com.yunqu.cc.email.base.Constants;

@WebObject(name= "emailHandle" )
public class EmailHandleDao extends AppDaoContext{
	private Logger logger = CommonLogger.logger;
	/*
	 * 查询已处理的会话
	 */
	@WebControl(name="list",type = Types.LIST)
	@InfAuthCheck(resId = "cc-tx-email-myprocessed", msg = "您无权访问!")
	public JSONObject list(){
		UserModel user = UserUtil.getUser(this.request);
		String type = param.getString("ProListType");	
		
		
		String startDate = param.getString("sendDateBegin");
		String endDate = param.getString("sendDateEnd");
		//查询天数限制
		if (Constants.judgeDateQueryScope(getEntId(), getBusiOrderId(), startDate, endDate)) {
			return EasyResult.fail(getI18nValue("查询天数超出最大限制"));
		}
		
		EasySQL sql = new EasySQL();
		sql.append("SELECT t1.*, t2.NAME DIR_NAME, t5.NAME GROUP_NAME,T6.TAG_NAME ");
		sql.append("FROM "+this.getTableName("C_EMAIL_SESSION")+" t1");
		sql.append("LEFT JOIN "+this.getTableName("C_EMAIL_DIR")+" t2 on t1.DIR_ID = t2.ID");
		sql.append("LEFT JOIN "+this.getTableName("C_EMAIL_CUSTOMER_ACC")+" t4 on t1.EMAIL_FROM = t4.CUST_EMAIL_ACC and t1.ent_id = t4.ent_id and t1.busi_order_id = t4.busi_order_id");
		sql.append("LEFT JOIN "+this.getTableName("C_EMAIL_CUST_GROUP")+" t5 on t4.CUST_GROUP_ID = t5.ID");
		//关联标签
		String concat = "";
		if(DBTypes.MYSQL == getQuery().getTypes()){
			concat = "GROUP_CONCAT";
		}else if(DBTypes.ORACLE == getQuery().getTypes()){
			concat = "WM_CONCAT";
		}else if(DBTypes.PostgreSql == getQuery().getTypes()) {
			concat = "ARRAY_AGG";
		}else{
			concat = "GROUP_CONCAT";
		}
		sql.append(" LEFT JOIN (SELECT T1.CUSTOMER_ID AS TAG_EMAIL_ID, " + concat + "(T2.NAME) AS TAG_NAME FROM "
				+ getTableName("C_CM_CUSTOMER_TAG") + " T1 LEFT JOIN " + getTableName("C_CM_TAG") + " T2 "
				+ " ON T1.TAG_ID=T2.ID GROUP BY T1.CUSTOMER_ID) T6 ON T6.TAG_EMAIL_ID=T1.ID ");
		
		sql.append(" WHERE 1=1 ");
		
		sql.append(getEntId()," AND T1.ENT_ID = ?");
		sql.append(getBusiOrderId()," AND T1.BUSI_ORDER_ID = ?");
		
		sql.appendLike(param.getString("TITLE"),"AND T1.TITLE Like ?");
		sql.append(param.getString("EMAIL_TO")," AND T1.EMAIL_TO = ?");
		sql.append(param.getString("sendDateBegin"),"AND T1.CREATE_TIME >= ?");
		sql.append(param.getString("sendDateEnd"),"AND T1.CREATE_TIME <= ?");
		sql.append(param.getString("status")," AND T1.STATUS = ?");
		
		sql.appendLike(param.getString("emailFrom"), "AND t1.EMAIL_FROM LIKE ?");//发件人
		sql.appendLike(param.getString("emailTo"), "AND t1.EMAIL_TO LIKE ?");//收件人
		sql.appendLike(param.getString("caseNum"), "AND t1.CASE_NUM LIKE ?");//回执码
		
		sql.appendLike(param.getString("orderNo"), "AND t1.ORDER_NO LIKE ?");//工单编号
		
		String isOrderNo = param.getString("isOrderNo");
		if("Y".equals(isOrderNo)){
			sql.append("AND t1.ORDER_NO !=''");//派发工单
		}else if("N".equals(isOrderNo)){
			sql.append("AND t1.ORDER_NO is null ");//没有派发工单
		}
		
		if("1".equals(type)){
		sql.append(UserUtil.getUser(request).getUserAcc(),"AND T1.HANDLE_ACC = ? ");
		 }else{
			 JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(getEntId(), getBusiOrderId(), user.getUserAcc() +"~"+"cc-tx-email-allprocessed");
			 StringBuilder dataAuthSql = new StringBuilder();
			 if(obj!=null){
				 
				 	String depts = obj.getString("depts");
				 	dataAuthSql.append("AND ( 1<>1 ");
				 	if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)){
				 		dataAuthSql.append("OR T1.HANDLE_DEPT_CODE IN('" + String.join("','", depts.split(","))+"')");
				 	}
				 	if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))){
				 		dataAuthSql.append("OR T1.HANDLE_ACC = '" + user.getUserAcc() + "' ");
				 	}
				 	dataAuthSql.append(")");
				 	if(StringUtils.isNotBlank(obj.getString("all"))){
				 		dataAuthSql = new StringBuilder();
				 	}
			 	}else{
			 		dataAuthSql.append("AND 1<>1");
			 	}
			 sql.append(dataAuthSql.toString());
		 }
		sql.append("ORDER BY T1.CREATE_TIME DESC");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
