package com.yunqu.cc.performance.model.jsonMapper;

import com.yunqu.cc.performance.base.CommonLogger;
import com.yunqu.cc.performance.model.CycleModel;
import org.easitline.common.db.EasyRowMapper;

import java.sql.ResultSet;

/**
 * <p>
 *
 * </p >
 *
 * @ClassName: CycleModelMapperImpl
 * @Author: liyingya
 * @Description: 考核周期查询实体返回数据处理类 对应数据表：C_JX_CYCLE
 * @since: create in 2022/8/16 10:03
 */
public class CycleModelMapperImpl implements EasyRowMapper<CycleModel> {

    public CycleModelMapperImpl() {
    }

    public CycleModel mapRow(ResultSet rs, int convertField) {
        try {
            CycleModel cycleModel = new CycleModel();
            cycleModel.setId(rs.getString("ID"));
            cycleModel.setName(rs.getString("NAME"));
            cycleModel.setExamineType(rs.getString("EXAMINE_TYPE"));
            cycleModel.setCycleStart(rs.getString("CYCLE_START"));
            cycleModel.setCycleEnd(rs.getString("CYCLE_END"));
            cycleModel.setIsCross(rs.getString("IS_CROSS"));
            cycleModel.setCrossCycle(rs.getInt("CROSS_CYCLE"));
            cycleModel.setBakup(rs.getString("BAKUP"));
            return cycleModel;
        } catch (Exception var9) {
            CommonLogger.logger.error("CycleModelMapperImpl.mapRow() exception , cause:" + var9.getMessage());
            return null;
        }
    }
}
