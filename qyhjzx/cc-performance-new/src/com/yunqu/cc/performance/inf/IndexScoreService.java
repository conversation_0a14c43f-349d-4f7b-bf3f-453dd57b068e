package com.yunqu.cc.performance.inf;

import com.alibaba.fastjson.JSONObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.string.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 指标获取分数接口
 */
public class IndexScoreService {

    private static final Map<String, Function<JSONObject, String>> INDEX_FUNC_CACHE = new HashMap<>();

    /**
     * 系统指标评分接口，针对某个指标进行评分统计
     */
    static {
        INDEX_FUNC_CACHE.put("CallInNum", IndexInf::callInNum);
        INDEX_FUNC_CACHE.put("CallOutNum", IndexInf::callOutNum);
        INDEX_FUNC_CACHE.put("CallBillTime", IndexInf::callBillTime);
        INDEX_FUNC_CACHE.put("AvgWorkReadyTime", IndexInf::avgWorkReadyTime);
        INDEX_FUNC_CACHE.put("CallSatisfRate", IndexInf::callSatisfRate);
        INDEX_FUNC_CACHE.put("CallConnectRate", IndexInf::callConnectRate);
        INDEX_FUNC_CACHE.put("RgQcAvgScore", IndexInf::rgQcAvgScore);
        INDEX_FUNC_CACHE.put("ZnQcAvgScore", IndexInf::znQcAvgScore);
        INDEX_FUNC_CACHE.put("RgQcScore", IndexInf::rgQcScore);
        INDEX_FUNC_CACHE.put("ZnQcScore", IndexInf::znQcScore);
        INDEX_FUNC_CACHE.put("CallInNum2", IndexInf::callInNum2);
        INDEX_FUNC_CACHE.put("AgentAnswerRate5s", IndexInf::AgentAnswerRate5s);
        INDEX_FUNC_CACHE.put("AgentAnswerRateOver10s", IndexInf::AgentAnswerRateOver10s);
        INDEX_FUNC_CACHE.put("OnlineTime", IndexInf::OnlineTime);
        INDEX_FUNC_CACHE.put("NoAnswerCountSkill", IndexInf::NoAnswerCountSkill);
        INDEX_FUNC_CACHE.put("NoAnswerRateSkill", IndexInf::NoAnswerRateSkill);
        INDEX_FUNC_CACHE.put("NoAnswerCount", IndexInf::NoAnswerCount);
        INDEX_FUNC_CACHE.put("UnderIndexScore", IndexInf::underIndexScore);
    }

    /**
     * 根据企业信息执行数据处理接口
     * @param command
     * @throws Exception
     */
    public String doService(String command,JSONObject jsonObject, String serviceId){
        Function<JSONObject, String> fun = INDEX_FUNC_CACHE.get(command);
        if (fun != null) {
            return fun.apply(jsonObject);
        }else{
            //内置指标，可根据定制ISERVICE获取得分
            if(StringUtils.isNotBlank(serviceId)){
                try {
                    IService service = ServiceContext.getService(serviceId);
                    JSONObject result = service.invoke(jsonObject);
                    if(result!=null){
                        return result.getString("data");
                    }
                    return "";
                }catch (Exception e){
                    return "";
                }
            }
            return IndexInf.otherIndexScore(jsonObject);
        }
    }
}
