package com.yunqu.cc.performance.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.performance.base.AppDaoContext;
import com.yunqu.cc.performance.base.CommonLogger;
import com.yunqu.cc.performance.model.ModelResultBakup;
import com.yunqu.cc.performance.model.ModelSectionModel;
import com.yunqu.cc.performance.utils.DbUtil;

import oracle.net.aso.l;

/**
 * <AUTHOR>
 */
@WebObject(name = "achievementModel")
public class AchievementModelDao extends AppDaoContext {

    /**
     * 模型列表查询
     *
     * <AUTHOR>
     * @date 2022年8月18日上午11:23:01
     */
    @WebControl(name = "getModelList", type = Types.LIST)
    public JSONObject getModelList() {
        EasySQL sql = new EasySQL("select t1.*,t2.OBJECT_NUM ,round((case WHEN t1.RESULT_SCORE>0 and t1.RESULT_NUM >0 and t2.OBJECT_NUM THEN (t1.RESULT_SCORE)/(t1.RESULT_NUM)/(t2.OBJECT_NUM) else 0 end  ),2)  EVG_SCORE from " + getTableName("c_jx_model") + " t1 ");
        sql.append("left join " + getTableName("c_jx_object") + " t2 on t1.OBJECT_ID = t2.id ");
        sql.append("where 1=1");
        sql.appendLike(param.getString("NAME"), " and t1.name like ?");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 修改模型数据回显接口
     *
     * <AUTHOR>
     * @date 2022年8月18日上午11:26:53
     */
    @WebControl(name = "getModelRecord", type = Types.RECORD)
    public JSONObject getModelRecord() {
        return this.queryForRecord(new EasyRecord(getTableName("c_jx_model"), "ID")
                .setPrimaryValues(param.getString("ID")));
    }

    /**
     * 获取指标、区间查询列表
     *
     * <AUTHOR>
     * @date 2022年8月23日下午2:48:49
     */
    @WebControl(name = "getZbList", type = Types.LIST)
    public JSONObject getZbList() {
        String modelId = param.getString("MODEL_ID");
        EasySQL sql = new EasySQL("select * from " + getTableName("c_jx_model_record") + " where 1=1  ");
        sql.append(modelId, "and MODEL_ID = ?");
        CommonLogger.logger.info("获取模型sql" + sql.getSQL() + "   " + modelId);

        CompletableFuture<JSONObject> jsonObjectCompletableFuture = CompletableFuture.supplyAsync(() ->
                queryForPageList(sql.getSQL(), sql.getParams()));

        try {
            Map<String, List<ModelSectionModel>> resultBakup = new HashMap<>();
            if (StringUtils.isNotBlank(modelId)) {
                CompletableFuture<Map<String, List<ModelSectionModel>>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                    List<ModelSectionModel> modelResultBakup = null;
                    try {
                        modelResultBakup = getModelResultBakup(modelId);
                        return modelResultBakup.stream().collect(Collectors.groupingBy(ModelSectionModel::getModelRecordId));
                    } catch (SQLException e) {
                        return null;
                        //throw new RuntimeException(e);
                    }
                });
                resultBakup = mapCompletableFuture.get();
            }
            CommonLogger.logger.info("获取模型对应的区间细项" + JSONObject.toJSONString(resultBakup));
            //3:数据整合
            JSONObject jsonObject = jsonObjectCompletableFuture.get();
            CommonLogger.logger.info("获取模型指标列表" + jsonObject);
            List<JSONObject> data = jsonObject.getJSONArray("data").toJavaList(JSONObject.class);
            Map<String, List<ModelSectionModel>> finalResultBakup = resultBakup;
            List<JSONObject> collect = data.stream().map(o -> {
                String id = o.getString("ID");
                if (finalResultBakup.containsKey(id)) {
                    o.put("Bakup", finalResultBakup.get(id));
                } else {
                    o.put("Bakup", new ArrayList<>());
                }
                return o;
            }).collect(Collectors.toList());

            jsonObject.put("data", collect);
            return jsonObject;

        } catch (Exception ex) {
            return null;
            //throw new RuntimeException(e);
        }

    }

    /**
     * 获取模型对应的区间细项
     *
     * @param modelId
     * @return
     * @throws SQLException
     */
    public List<ModelSectionModel> getModelResultBakup(String modelId) throws SQLException {
        EasySQL sql = new EasySQL("select * from " + getTableName("c_jx_model_section") + " ");
        sql.append(modelId, " where MODEL_ID= ?", false);
        List<ModelSectionModel> rows = getQuery().queryForList(sql.getSQL(), sql.getParams(), new EasyRowMapper<ModelSectionModel>() {
            @Override
            @SuppressWarnings("unchecked")
            public ModelSectionModel mapRow(ResultSet rs, int arg1) {
                ModelSectionModel modelResultBakup = new ModelSectionModel();
                try {
                    modelResultBakup.setId(rs.getString("ID"));
                    modelResultBakup.setBeginVal(rs.getString("BEGIN_VAL"));
                    modelResultBakup.setEndVaL(rs.getString("END_VAL"));
                    modelResultBakup.setIndexId(rs.getString("INDEX_ID"));
                    modelResultBakup.setModelId(rs.getString("MODEL_ID"));
                    modelResultBakup.setModelRecordId(rs.getString("MODEL_RECORD_ID"));
                    modelResultBakup.setLeftSign(rs.getString("LEFT_SIGN"));
                    modelResultBakup.setRightSign(rs.getString("RIGHT_SIGN"));
                    modelResultBakup.setScore(rs.getString("SCORE"));
                } catch (SQLException e) {
                    CommonLogger.logger.error(e.getMessage(), e);
                }

                return modelResultBakup;
            }
        });
        return rows;
    }

    @WebControl(name = "getZbRecord", type = Types.RECORD)
    public JSONObject getZbRecord() {
        return this.queryForRecord(new EasyRecord(getTableName("c_jx_model_record"), "ID")
                .setPrimaryValues(param.getString("ID")));
    }


    /**
     * 考核指标下拉框
     *
     * <AUTHOR>
     * @date 2022年8月19日上午9:41:57
     */
    @WebControl(name = "getJxIndex", type = Types.DICT)
    public JSONObject getJxIndex() {
        String OBJECT_TYPE = param.getString("OBJECT_TYPE");
        String EXAMINE_TYPE = param.getString("EXAMINE_TYPE");
        String IS_HANDEXAMINE = param.getString("IS_HANDEXAMINE");
        String MODEL_ID = param.getString("MODEL_ID");
        EasySQL sql = new EasySQL("select DISTINCT t1.CODE ,t1.NAME from " + getTableName("c_jx_index") + " t1 ");
        sql.append("left join " + getTableName("c_jx_index_config") + " t2 on t1.id = t2.index_id ");
        sql.append("where 1=1");
        sql.append(OBJECT_TYPE, " and (t2.index_object_type=?");
        sql.append(EXAMINE_TYPE, "or t2.index_examine_type = ?)");
        if (StringUtils.endsWith("N", IS_HANDEXAMINE)) {
            sql.append("and STATIS_TYPE != '04'");
        }
        if (StringUtils.isNotBlank(MODEL_ID)) {
            sql.append("and not exists (select * from " + getTableName("c_jx_model_record") + " t3 where 1=1 ");
            sql.append(MODEL_ID, "and t3.model_id = ?");
            sql.append("and t3.index_id=t1.code and t1.ep_code =t3.ENT_ID )");
        }
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "IDEX_COLUMN", type = Types.DICT)
    public JSONObject getIndexColumn() {
        List<Map<String, Object>> columns = new ArrayList<Map<String, Object>>();
        Map<String, String> m = new LinkedHashMap<String, String>();
        if (columns == null || columns.isEmpty()) {
            try {
                columns = DbUtil.sysoutStrTablePdmCloumns("C_JX_MODLE_RESULT", "", getDbName().toUpperCase());
            } catch (SQLException e) {
                getLogger().error(e.getMessage(), e);
            }
        }

        EasySQL sql = new EasySQL("select");
        if (this.getQuery().getTypes().equals(DBTypes.MYSQL))
            sql.append(" group_concat(INDEX_CODE) st");
        else sql.append(" wmsys.wm_concat(INDEX_CODE) st");
        sql.append(" from " + getTableName("C_JX_MODEL_RECORD") + " where 1=1");
        sql.append(param.getString("MODEL_ID"), " and MODEL_ID =?");
        sql.append(param.getString("ID"), " and ID <>?");
        try {
            String usedColumn = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
            for (Map<String, Object> cs : columns) {
                String columnEn = cs.get("Code").toString();
                if (validate(columnEn, usedColumn)) {
                    m.put(columnEn, columnEn);
                }
            }
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
        }
        return DbUtil.getDicJson(m);
    }

    private boolean validate(String col, String usedColumn) {
        if (usedColumn.contains(col) || !col.startsWith("EX")
                || "examer_code".equalsIgnoreCase(col)) {
            return false;
        }
        return true;
    }


}
