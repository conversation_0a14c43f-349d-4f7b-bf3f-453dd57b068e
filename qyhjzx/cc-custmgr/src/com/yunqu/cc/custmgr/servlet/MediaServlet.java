package com.yunqu.cc.custmgr.servlet;

import java.sql.SQLException;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.cc.custmgr.base.AppBaseServlet;
import com.yunqu.cc.custmgr.base.CommonLogger;
import com.yunqu.cc.custmgr.base.Constants;
import com.yunqu.cc.custmgr.utils.StringUtil;

/**
 * @Description：   <p> 页面路由 </p>
 */
@MultipartConfig
@WebServlet("/servlet/page/*")
public class MediaServlet extends AppBaseServlet {
    protected Logger logger = CommonLogger.logger;
    private static final long serialVersionUID = 1061198963491367812L;



    /**
     * @Description: 客户资料
     */
    public String actionForCustomInfo1() {
        HttpServletRequest request = this.getRequest();

        EasySQL sql = new EasySQL("select * from " + this.getTableName("C_CM_TABLE"));
        sql.append(getEntId(), " where ENT_ID=?", false);
        sql.append(getBusiOrderId(), " and BUSI_ORDER_ID=?", false);
        sql.append("4", " and TBL_TYPE = ?");
        sql.append("Y", " and IS_DEFAULT= ?");
        try {
            Map<String, String> map = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
            if (map != null) {
                this.setAttr("tableId", map.get("ID"));
                this.setAttr("tableName", map.get("TBL_NAME"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            this.error(e.getMessage(), e);
        }

        //servicePhone  平台接入号
        //sourceType:  voice-话务，media-全媒体，email-邮件，不能为空
        //sessionId：该客户在当前渠道的唯一标识，voice传接入电话号码；media传渠道sessionId，email传邮箱地址；，不能为空
        //channelKey：该客户的在不同渠道的渠道标识，voice传平台号码，medai传渠道key，email传平台邮箱账号，不能为空
        //sourceRecordId：渠道的业务记录id，无实际意义，voice传通话记录id，media传会话记录id，email传邮件会话id，可以为空

		/*String servicePhone = request.getParameter("servicePhone");
		String sourceType = request.getParameter("sourceType");
		String sessionId = request.getParameter("sessionId");
		String channelKey = request.getParameter("channelKey");
		String sourceRecordId = request.getParameter("sourceRecordId");*/

        String callId = request.getParameter("callId");
        String sourcePage = request.getParameter("sourcePage");
        String sourceType = request.getParameter("sourceType");
        if (CommonUtil.isNotBlank(callId) || (StringUtils.equals("mediaPage", sourcePage) && StringUtils.equals(sourceType, "voice"))) {
            keepPara();
            //来电客户资料
            String custPhone = request.getParameter("custPhone");
            if (StringUtils.isBlank(custPhone)) {
                custPhone = request.getParameter("caller");
            }
            if (StringUtils.isBlank(custPhone)) {
                custPhone = "";
            }
            this.setAttr("mobile", custPhone);
            this.setAttr("account", custPhone);
            this.setAttr("chatSessionId", callId);
            String custChannelKey = request.getParameter("custChannelKey");
            if (CommonUtil.isNotBlank(custChannelKey)) {
                this.setAttr("channelKey", custChannelKey);
            } else {
                this.setAttr("channelKey", "99");
            }

            this.setAttr("summaryRelationId", callId);//小结关联ID，话单ID/全媒体ID
            this.setAttr("summaryType", 1);//小结类型，1话单， 3全媒体

            String autoSearch = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "AUTO_SEARCH");
            this.setAttr("autoSearch", autoSearch);
            this.setAttr("sourcePage", request.getParameter("sourcePage"));
            this.setAttr("statisyStay", SystemParamUtil.getEntParam(getDbName(), getEntId(),
                    getBusiOrderId(), "cc-base", "SHOW_STATUS"));

        } else if ("email".equals(sourcePage)) {
            String chatSessionId = request.getParameter("chatSessionId");
            keepPara();
            this.setAttr("email", request.getParameter("email"));
            this.setAttr("chatSessionId", chatSessionId);
            this.setAttr("channelKey", Constants.EMAIL_CHANNEL_CALL);
            //覆盖账号为sessionId
            this.setAttr("account", request.getParameter("sessionId"));
        } else {
            keepPara();
            String chatSessionId = request.getParameter("chatSessionId");
            String mobile = request.getParameter("mobile");
            //如果mobile不存在，尝试获取p_mobile
            if (StringUtil.isNull(mobile)) {
                mobile = StringUtil.nvl(request.getParameter("p_mobile"), "");
            }
            this.setAttr("mobile", mobile);
            String custname = request.getParameter("p_username");
            this.setAttr("custname", StringUtil.nvl(custname, ""));
            this.setAttr("accountType", request.getParameter("accountType"));
            String account = request.getParameter("account");
            String sessionId = request.getParameter("sessionId");
            if (StringUtil.isNull(account)) {
                account = sessionId;
            }
            this.setAttr("account", account);
            this.setAttr("sessionId", sessionId);
            this.setAttr("chatSessionId", chatSessionId);
            this.setAttr("channelType", request.getParameter("channelType"));
            this.setAttr("nickname", request.getParameter("nickname"));
            this.setAttr("channelKey", request.getParameter("channelKey"));
            this.setAttr("summaryRelationId", chatSessionId);//小结关联ID，话单ID/全媒体ID
            this.setAttr("summaryType", 3);//小结类型，1话单， 3全媒体
            this.setAttr("sourcePage", request.getParameter("sourcePage"));
            this.setAttr("statisyStay", SystemParamUtil.getEntParam(getDbName(), getEntId(),
                    getBusiOrderId(), "cc-base", "SHOW_STATUS"));
        }

        //查询配置项，判断是否一键拨打弹屏参数
        String callScreen = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "CALL_SCREEN");
        if (StringUtils.isBlank(callScreen)) {
            callScreen = "N";
        }
        this.setAttr("CALL_SCREEN", callScreen);
        //侧边栏是否展示URL参数
        String showUrl = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "SHOW_URL");
        this.setAttr("showUrl", showUrl);
        //旧客户
        return "/pages/media/customInfo-side.jsp";
    }
    
    /**
     * @Description: 客户资料
     */
    public String actionForCustomInfo() {
        HttpServletRequest request = this.getRequest();

        EasySQL sql = new EasySQL("select * from " + this.getTableName("C_CM_TABLE"));
        sql.append(getEntId(), " where ENT_ID=?", false);
        sql.append(getBusiOrderId(), " and BUSI_ORDER_ID=?", false);
        sql.append("4", " and TBL_TYPE = ?");
        sql.append("Y", " and IS_DEFAULT= ?");
        try {
            Map<String, String> map = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
            if (map != null) {
                this.setAttr("tableId", map.get("ID"));
                this.setAttr("tableName", map.get("TBL_NAME"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            this.error(e.getMessage(), e);
        }

        //servicePhone  平台接入号
        //sourceType:  voice-话务，media-全媒体，email-邮件，不能为空
        //sessionId：该客户在当前渠道的唯一标识，voice传接入电话号码；media传渠道sessionId，email传邮箱地址；，不能为空
        //channelKey：该客户的在不同渠道的渠道标识，voice传平台号码，medai传渠道key，email传平台邮箱账号，不能为空
        //sourceRecordId：渠道的业务记录id，无实际意义，voice传通话记录id，media传会话记录id，email传邮件会话id，可以为空

		/*String servicePhone = request.getParameter("servicePhone");
		String sourceType = request.getParameter("sourceType");
		String sessionId = request.getParameter("sessionId");
		String channelKey = request.getParameter("channelKey");
		String sourceRecordId = request.getParameter("sourceRecordId");*/

        String callId = request.getParameter("callId");
        String sourcePage = request.getParameter("sourcePage");
        if (CommonUtil.isNotBlank(callId)) {
            keepPara();
            //来电客户资料
            String custPhone = request.getParameter("custPhone");
            if (StringUtils.isBlank(custPhone)) {
                custPhone = request.getParameter("caller");
            }
            if (StringUtils.isBlank(custPhone)) {
                custPhone = "";
            }
            this.setAttr("mobile", custPhone);
            this.setAttr("account", custPhone);
            this.setAttr("chatSessionId", callId);
            String custChannelKey = request.getParameter("custChannelKey");
            if (CommonUtil.isNotBlank(custChannelKey)) {
                this.setAttr("channelKey", custChannelKey);
            } else {
                this.setAttr("channelKey", "99");
            }

            this.setAttr("summaryRelationId", callId);//小结关联ID，话单ID/全媒体ID
            this.setAttr("summaryType", 1);//小结类型，1话单， 3全媒体

            String autoSearch = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "AUTO_SEARCH");
            this.setAttr("autoSearch", autoSearch);
            this.setAttr("sourcePage", request.getParameter("sourcePage"));
            this.setAttr("statisyStay", SystemParamUtil.getEntParam(getDbName(), getEntId(),
                    getBusiOrderId(), "cc-base", "SHOW_STATUS"));

        } else if ("email".equals(sourcePage)) {
            String chatSessionId = request.getParameter("chatSessionId");
            keepPara();
            this.setAttr("email", request.getParameter("email"));
            this.setAttr("chatSessionId", chatSessionId);
            this.setAttr("channelKey", Constants.EMAIL_CHANNEL_CALL);
            //覆盖账号为sessionId
            this.setAttr("account", request.getParameter("sessionId"));
        } else {
            keepPara();
            String chatSessionId = request.getParameter("chatSessionId");
            String mobile = request.getParameter("mobile");
            //如果mobile不存在，尝试获取p_mobile
            if (StringUtil.isNull(mobile)) {
                mobile = StringUtil.nvl(request.getParameter("p_mobile"), "");
            }
            this.setAttr("mobile", mobile);
            String custname = request.getParameter("p_username");
            this.setAttr("custname", StringUtil.nvl(custname, ""));
            this.setAttr("accountType", request.getParameter("accountType"));
            String account = request.getParameter("account");
            String sessionId = request.getParameter("sessionId");
            if (StringUtil.isNull(account)) {
                account = sessionId;
            }
            this.setAttr("account", account);
            this.setAttr("sessionId", sessionId);
            this.setAttr("chatSessionId", chatSessionId);
            this.setAttr("channelType", request.getParameter("channelType"));
            this.setAttr("nickname", request.getParameter("nickname"));
            this.setAttr("channelKey", request.getParameter("channelKey"));
            this.setAttr("summaryRelationId", chatSessionId);//小结关联ID，话单ID/全媒体ID
            this.setAttr("summaryType", 3);//小结类型，1话单， 3全媒体
            this.setAttr("sourcePage", request.getParameter("sourcePage"));
            this.setAttr("statisyStay", SystemParamUtil.getEntParam(getDbName(), getEntId(),
                    getBusiOrderId(), "cc-base", "SHOW_STATUS"));
        }

        //查询配置项，判断是否一键拨打弹屏参数
        String callScreen = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "CALL_SCREEN");
        if (StringUtils.isBlank(callScreen)) {
            callScreen = "N";
        }
        this.setAttr("CALL_SCREEN", callScreen);
        //侧边栏是否展示URL参数
        String showUrl = SystemParamUtil.getEntParam(getDbName(), getEntId(), getBusiOrderId(), "cc-custmgr", "SHOW_URL");
        this.setAttr("showUrl", showUrl);
        //旧客户
        //return "/pages/media/customInfo-side.jsp";
        return "/pages/custmgrNew/custInfoSide/index.html";
    }

    /**
     * @Description: 客户资料
     */
    public String actionForEmailCustomInfo() {
        HttpServletRequest request = this.getRequest();

        String chatSessionId = request.getParameter("chatSessionId");

        this.setAttr("email", request.getParameter("email"));
        this.setAttr("chatSessionId", chatSessionId);

        this.setAttr("channelKey", Constants.EMAIL_CHANNEL_CALL);
        this.setAttr("account", chatSessionId);
        this.setAttr("account", request.getParameter("email"));

        EasySQL sql = new EasySQL("select * from " + this.getTableName("C_CM_TABLE"));
        sql.append(getEntId(), " where ENT_ID=?", false);
        sql.append(getBusiOrderId(), " and BUSI_ORDER_ID=? ");
        sql.append("4", " and TBL_TYPE = ?");
        sql.append(" and IS_DEFAULT='Y' ");
        try {
            Map<String, String> map = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
            if (map != null) {
                this.setAttr("tableId", map.get("ID"));
                this.setAttr("tableName", map.get("TBL_NAME"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            this.error(e.getMessage(), e);
        }

        //旧客户
        //return "/pages/media/customInfo-side.jsp";
        return "/pages/custmgrNew/custInfoSide/index.html";
    }

    /**
     * @Description: 客户资料(侧边栏不可修改)
     */
    public String actionForCustomNotModifyInfo() {
        HttpServletRequest request = this.getRequest();
        //判断页面来源     mediaPage：全媒体话单
        String sourcePage = request.getParameter("sourcePage");
        keepPara();
        EasySQL sql = new EasySQL("select * from " + this.getTableName("C_CM_TABLE"));
        sql.append(getEntId(), " where ENT_ID=?", false);
        sql.append(getBusiOrderId(), " and BUSI_ORDER_ID=? ");
        sql.append("4", " and TBL_TYPE = ?");
        sql.append(" and IS_DEFAULT='Y' ");
        try {
            Map<String, String> map = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
            if (map != null) {
                this.setAttr("tableId", map.get("ID"));
                this.setAttr("tableName", map.get("TBL_NAME"));
            }
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
        }
        //旧客户
        //return "/pages/media/customInfo-notModify-side.jsp";
        return "/pages/custmgrNew/custInfoSide/index.html";
    }
}
