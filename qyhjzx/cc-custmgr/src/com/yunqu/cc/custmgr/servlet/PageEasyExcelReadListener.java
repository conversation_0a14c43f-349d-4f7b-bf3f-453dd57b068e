package com.yunqu.cc.custmgr.servlet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.formula.functions.T;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;

import com.alibaba.excel.context.AnalysisContext;
import com.yq.busi.common.util.impt.easyexcel.listener.AbstractEasyExcelReadListener;
import com.yq.busi.common.util.impt.easyexcel.listener.ExcelPageDataHandle;
import com.yunqu.cc.custmgr.base.Constants;
import com.yunqu.cc.custmgr.dao.ColumnDao;

public  class PageEasyExcelReadListener<T> extends AbstractEasyExcelReadListener<T> {
	private static EasyCache cache = CacheManager.getMemcache();
    private ExcelPageDataHandle<T> handler;

    /**
     * 每隔100条分页处理，然后清理list ，方便内存回收
     */
    private int pageSize = 100;
    private String userAcc="";
    private String entId="";
    private Map<Integer, String> headMap = new HashMap<>();

    /**
     * 缓存的数据
     */
    private List<T> cachedDataList = new ArrayList<>(pageSize);

    public PageEasyExcelReadListener( int pageSize,String userAcc,String entId,ExcelPageDataHandle<T> handler) {
        this.handler = handler;
        this.pageSize = pageSize;
        this.userAcc = userAcc;
        this.entId = entId;
    }


    @Override
    public void invoke(T data, AnalysisContext context) {
    	
        if (cachedDataList == null || handler == null) {
            throw new IllegalStateException("cachedDataList or handler is not initialized.");
        }
        cachedDataList.add(data);
        if (cachedDataList.size() >= pageSize) {
            // 达到BATCH_COUNT了，需要处理一次，防止内存溢出
            handler.handle(cachedDataList);
            cachedDataList.clear();
        }
    }
    
    @Override 
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) { 
    	 logger.info("【表头数据】");
    	// 将表头数据添加到列表中 headerList.add(new HashMap<>(headMap)); }
    	 this.headMap = headMap; // 捕获表头数据   	 
    	 //把表头加入缓存
    	cache.put(Constants.CUST_DATA_HEADER+"_"+entId+"_"+userAcc,headMap);
    }

    
    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (handler != null && cachedDataList != null) {
            // 确保handler和cachedDataList不为null，防止空指针异常
            handler.handle(cachedDataList);
            cachedDataList.clear();
        } else {
            // 可以根据需要添加日志记录或抛出异常
            logger.warn("Error: handler or cachedDataList is null.");
        }
        //清空表头缓存
        logger.info("【数据加载完毕，清空表头缓存】");
        cache.delete(Constants.CUST_DATA_HEADER+"_"+entId+"_"+userAcc);
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
