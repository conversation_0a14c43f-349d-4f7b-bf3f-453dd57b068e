package com.yunqu.cc.custmgr.handler;


import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;

import com.yunqu.cc.custmgr.base.CustmgrLogger;

public class ExcelFileParser extends FileParser{

	private FileInputStream fis = null;
    private POIFSFileSystem excelFile = null;
    private HSSFWorkbook wb = null;
    private HSSFSheet sheet = null;
    private int rowCount = -1;
    private int cellCount = -1;
    private int readIndex = 2; // 从第二行开始读取，第一行是模板title   修改为从第三行开始读
    private HSSFRow row = null;
	    
	@Override
	public void start() throws Exception {
		 CustmgrLogger.logger.info("ExcelFileParse filePath = " + this.getFilePath());
		 CustmgrLogger.logger.info("ExcelFileParse sourceFilePath = " + this.getSourceFile());
		 CustmgrLogger.logger.info("ExcelFileParse allFilePath = " + this.getFilePath() +"/"+ this.getSourceFile());
		 fis = new FileInputStream(this.getFilePath() +"/"+ this.getSourceFile());
         excelFile = new POIFSFileSystem(fis);
         wb = new HSSFWorkbook(excelFile);
         sheet = wb.getSheetAt(0);
         rowCount = sheet.getLastRowNum();
         cellCount = sheet.getRow(0).getLastCellNum();
	}
	
	@Override
	public void finish() {
		try {
	         fis.close();
		} catch(Exception e) {
			;
		}
	}

	@Override
	public String[] readLine() throws Exception {
        String[] line = null;
        if(readIndex <= rowCount) {
        	row = sheet.getRow(readIndex);
        	if(row == null) {
        		return null;
        	}
        	line = new String[cellCount];
            for (int i = 0; i < cellCount; i++) {
            	line[i] = "";
                if (null != row.getCell((short) i)) {
                    switch (row.getCell((short) i).getCellType()) {
                        case HSSFCell.CELL_TYPE_FORMULA :
                        	line[i] = "FORMULA ";
                            break;
                        case HSSFCell.CELL_TYPE_NUMERIC :
//                        	line[i] = String.valueOf(row.getCell((short) i).getNumericCellValue());
                            Cell cell = (Cell)row.getCell((short) i);
                            //line[i] = String.valueOf((long)cell.getNumericCellValue()); //将excel中的数字转为字符串保存 
                            String temp = "";
                            if (DateUtil.isCellDateFormatted(cell)) {
                                Date date = cell.getDateCellValue();
                                DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                                temp = df.format(date);
                              } else {
                            	temp = String.valueOf((long)cell.getNumericCellValue());
                              }
                            line[i] = temp;
                            break;
                        case HSSFCell.CELL_TYPE_STRING :
                        	line[i] = row.getCell((short) i).getStringCellValue();
                            break;
                    }
                }
            }
            readIndex++;
            if(isEmptyRow(line)) {
            	return null; // 如果读到一条完全为空值的行，也视为最后一行结束
            } else {
            	return line;
            }
        }
		return null;
	}
	
	
	private boolean isEmptyRow(String[] line) {
		for(int i=0; i<line.length; i++) {
			if( ! "".equals(line[i]) ) {
				return false;
			}
		}
		return true;
	}
	

}
