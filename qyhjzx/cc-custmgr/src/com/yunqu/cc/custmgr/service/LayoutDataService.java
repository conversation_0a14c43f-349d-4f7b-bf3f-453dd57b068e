package com.yunqu.cc.custmgr.service;

import java.io.File;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.custmgr.dao.LayoutDao;
import com.yunqu.cc.custmgr.model.DBInfo;
import com.yunqu.cc.custmgr.utils.FileUtil;
import com.yunqu.cc.custmgr.utils.StringUtil;

public class LayoutDataService {
    private LayoutDao dao = LayoutDao.getInstance();	
	private HttpServletRequest resp = null;
	
	public LayoutDataService(HttpServletRequest resp){
		this.resp = resp;
	}
	
	public LayoutDataService(){
		
	}
	public HttpServletRequest getResp() {
		return resp;
	}
	public void setResp(HttpServletRequest resp) {
		this.resp = resp;
	}

	public Map getLayoutById(String layoutId,String schema) {
		return dao.getLayoutById(layoutId,schema);
	}


	public String buildUpdateSql(Map map,DBInfo info) {
		String layoutName = (String)map.get("LYT_NAME");
		String layoutFile = (String)map.get("LYT_FILE");
		String layoutIsDefault = (String)map.get("LYT_IS_DEFAULT");
		String layoutId = (String)map.get("ID");
		String tableId = (String)map.get("TABLE_ID");
		Map dbMap = (Map)dao.getLayoutById(layoutId,info.getSchema());
		String dbLayoutFile =  (String)dbMap.get("LYT_FILE");
		String saveDir = FileUtil.getFilePath();
		File existFile = new File(saveDir + "/" + dbLayoutFile);
		dao.updateLayoutById(tableId, layoutId, layoutName, layoutFile, layoutIsDefault,info);
		return "";
	}	
	
	public String doInsert(Map map,DBInfo info) {
		String layoutName = (String)map.get("LYT_NAME");
		String layoutFile = (String)map.get("LYT_FILE");
		String tableId = (String)map.get("TABLE_ID");
		String userAcc =UserUtil.getUser(resp).getUserAcc();
		if(userAcc == null) {
			userAcc = "";
		}
		String layoutId = dao.addLayout(layoutName, layoutFile, userAcc, tableId,info);
		if( StringUtil.isNull(layoutFile) ) {
			layoutFile = "layout_"+layoutId+".html";
		}
		if(!StringUtil.isNull(layoutFile) && !layoutFile.startsWith("/")) {
			// 如果是非url形式，则创建本地布局文件
			this.writeLayoutFile(layoutFile, "<!--空布局文件-->");
		}
		return layoutId;
	}

	
	public String buildDeleteSql(Map map,String schema) {
		String layoutId = (String)map.get("ID");				
		return dao.deleteLayoutById(layoutId,schema);
	}
	
	public String readLayoutFile(String layoutId,String schema) throws Exception {
		String fileName = (String)this.getLayoutById(layoutId,schema).get("LYT_FILE");
		if(fileName.startsWith("/")) {
			// 如果布局以url形式存在于其它模块，则用URL方式读取内容
			String url = "http://"+this.resp.getLocalAddr()+":"+this.resp.getLocalPort()+fileName;
			return FileUtil.readUrl(url);
		} else {
			String saveDir = FileUtil.getFilePath();
			return FileUtil.readFile(saveDir+"/"+fileName);
		}
	}

	
	public String  writeLayoutFile(String layoutFile, String htmlStr) {
	    String saveDir = FileUtil.getFilePath();
	    String msg = "ok";
		try {
			String filePath = saveDir + "/" + layoutFile;
			LayoutDao.logger.debug("创建布局文件 路径>>> " + filePath);
			FileUtil.writeFile(filePath, htmlStr);
		} catch(Exception e) {
			msg = "ERROR:"+e.getMessage();
			LayoutDao.logger.error(e, e);
		}
		return msg;
	}
	
	
	public String setDefaultLayoutForTable(String tableId, String layoutId,DBInfo info) {
		return dao.setDefaultLayoutForTable(tableId, layoutId,info);
	}
	
	
	public Map getDefaultLayoutByTableId(String tableId,DBInfo info) {
		return dao.getDefaultLayoutByTableId(tableId,info);
	}
	
}
