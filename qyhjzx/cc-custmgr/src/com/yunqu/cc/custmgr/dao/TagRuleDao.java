package com.yunqu.cc.custmgr.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.custmgr.base.AppDaoContext;
import com.yunqu.cc.custmgr.base.CommonLogger;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动打标签规则查询类
 *
 * <AUTHOR>
 * @since 2023-09-20 09:55:17
 */
@WebObject(name="tagRule")
public class TagRuleDao extends AppDaoContext {
	
	@WebControl(name="getTagRuleList",type=Types.LIST)
	public  JSONObject getTagRuleList(){
		EasySQL sql = getEasySQL("SELECT * FROM "+getTableName("C_CM_TAG_RULE"));	
		sql.append(getEntId()," where ENT_ID=?");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.appendRLike(param.getString("RULE_NAME"), "and RULE_NAME like ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="getTagRuleRecord",type=Types.RECORD)
	public  JSONObject getTagRuleRecord(){
		EasySQL sql = getEasySQL("SELECT * FROM "+getTableName("C_CM_TAG_RULE t1"));
		sql.append(getEntId()," where t1.ENT_ID=?");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID=?");
		sql.append(param.getString("ID")," and t1.ID=?", false);
		CommonLogger.logger.info(sql.getSQL()+"-- "+JSONObject.toJSONString(sql.getParams()));
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="getTagRuleColumn",type=Types.LIST)
	public  JSONObject getTagRuleColumn(){
		JSONObject result = new JSONObject();
		result.put("state", 1);
		result.put("total", 0);
		EasySQL sql = getEasySQL("SELECT * FROM "+getTableName("C_CM_TAG_RULE_COLUMN")).append("t2");
		sql.append(param.getString("RULE_ID")," where t2.RULE_ID=?", false);
		sql.append(param.getString("RULE_COLUMN_ID"), "and t2.RULE_COLUMN_ID=?");
		sql.appendRLike(param.getString("RULE_COLUMN_NAME"), "and RULE_COLUMN_NAME like ?");
		sql.append("order by RULE_COLUMN_ID, INDEX_NUM");
		CommonLogger.logger.info(sql.getSQL()+"-- "+JSONObject.toJSONString(sql.getParams()));

		try {
			List<JSONObject> list = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String, List<JSONObject>> map = list.stream().collect(Collectors.groupingBy(t->t.getString("RULE_COLUMN_ID")));

			result.put("total", map.keySet().size());
			result.put("data", map);
		}catch (Exception e){
			
		}
		return result;
	}

	@WebControl(name="getTagRuleTagName",type=Types.LIST)
	public  JSONObject getTagRuleTagName(){
		String id = param.getString("ids");

		String[] idArr = id.split(",");
		JSONObject result = new JSONObject();
		result.put("state", 1);
		result.put("total", 0);
		EasySQL sql = getEasySQL("SELECT * FROM "+getTableName("c_cm_tag")).append("t2");
		sql.appendIn(idArr," where t2.ID");
		CommonLogger.logger.info(sql.getSQL()+"-- "+JSONObject.toJSONString(sql.getParams()));
		try {
			List<JSONObject> list = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String, List<JSONObject>> map = list.stream().collect(Collectors.groupingBy(t->t.getString("ID")));
			result.put("total", map.keySet().size());
			result.put("data", map);
		}catch (Exception e){

		}
		return result;
	}
	
	/**
	 * 模型执行记录
	 */
	@WebControl(name="getTagRuleLogList",type=Types.LIST)
	public  JSONObject getTagRuleLogList(){
		String startDate=param.getString("startDate");
		String endDate=param.getString("endDate");
		EasySQL sql = getEasySQL("SELECT * FROM "+getTableName("C_CM_TAG_RULE_LOG"));	
		sql.append(getEntId()," where ENT_ID=?");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(param.getString("ruleId")," and RULE_ID=?", false);
		if (StringUtils.isNotBlank(startDate)) {
			sql.append(startDate+" 00:00:00"," and EXE_TIME>=?");
		}
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate+" 23:59:59"," and EXE_TIME<=?");
		}
	
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 模型执行记录明细
	 */
	@WebControl(name="getTagRuleDetailList",type=Types.LIST)
	public  JSONObject getTagRuleDetailList(){
		EasySQL sql = getEasySQL("SELECT T1.*, T2.CUST_CODE CUSTOM_CODE, T2.CUST_NAME CUSTOM_NAME,t3.NAME  TAG_NAME FROM "+getTableName("C_CM_TAG_RULE_DETAIL_LOG")+" t1");
		sql.append(" left join "+getTableName("C_CM_CUSTINFO")+" t2 ON T1.CUSTOM_ID = T2.ID ");
		sql.append(" left join "+getTableName("C_CM_TAG")+" T3 ON T1.TAG_ID = T3.ID ");
		sql.append(getEntId()," where  t1.ENT_ID=?");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID=?");
		sql.append(param.getString("ruleId")," and t1.RULE_ID=?", false);
		sql.append(param.getString("ruleLogId")," and t1.RULE_LOG_ID=?", false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
