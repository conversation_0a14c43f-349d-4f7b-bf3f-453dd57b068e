<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<%@ page import="com.yunqu.cc.custmgr.dao.RelationDao" %>
<%@ page import="com.yunqu.cc.custmgr.dao.ColumnDao" %>
<%@ page import="com.yunqu.cc.custmgr.dao.TableDao" %>
<%@ page import="com.yunqu.cc.custmgr.utils.StringUtil" %>
<%@ page import="com.yq.busi.common.util.ConfigUtil" %>
<%@ page import="com.yunqu.cc.custmgr.base.Constants" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.List" %>
<%@ page import="com.yunqu.cc.custmgr.utils.DicColumnHelper" %>
<%@ page import="com.yq.busi.common.util.UserUtil" %>
<%@ page import="com.yunqu.cc.custmgr.model.DBInfo" %>
<EasyTag:override name="head">
	<title>客户资料信息</title>
	<style>
	.itemlabel { width:80px; background:#fbfbfb; text-align:center}
	.itemvalue { background:#ffffff}
	.outcall { background:#fff url(../../static/images/phone.png) no-repeat right;vertical-align:middle;}
	 input.canSearch{ 
		vertical-align:middle;
		padding-right:15px;
	}
	.leftspan{font-family:Microsoft YaHei;font-size: 14px;float:left;}
	</style>
</EasyTag:override>
<%
    DBInfo info = new DBInfo(request);
    String multiview = request.getParameter("multiview");
    String schema = UserUtil.getRequestUserSchema(request);
    
    String mainFrame = request.getParameter("mainFrame");
	String isDebugModel = ConfigUtil.getString(Constants.APP_NAME, "IS_DEBUG_MODEL");
	String colsInRow = ConfigUtil.getString(Constants.APP_NAME, "LAYOUT_COLS_IN_ROW");
	colsInRow = colsInRow.equals("") ? "3" : colsInRow;
	String caller = request.getParameter("caller");
	if(caller == null) { caller = ""; }
	String action = request.getParameter("action");
	if(action == null) { action = "add"; }
	String tableId = request.getParameter("tableId");
	String recordId = request.getParameter("recordId");
	String custId = request.getParameter("custId");//决定了该从表数据在主表中对应的数据，cust_id为主表数据ID
	String isCacheRecord = request.getParameter("isCacheRecord");
	request.getSession().setAttribute("tableId", tableId);//从表ID
	// isCacheRecord参数指定是否将此条客户记录存入openCC缓存中，以备其它业务模块获取
	request.getSession().setAttribute("isCacheRecord", isCacheRecord);
	List<Map> relationList = RelationDao.getInstance().getRelationListByTableId(tableId,info);
	String tableName = TableDao.getInstance().getTableNameById(tableId,info);
	//获取该子表的外键字段名
	String fkey = request.getParameter("fkey");
	if(fkey == null){
		fkey = ""; //默认外键为F_ID
	}
	String userCode =  request.getRemoteUser();
	
	boolean hasAdd = true;
	boolean hasEdit = true;
	boolean hasDelete = true;
	String[] columnNameAndTitles = ColumnDao.getInstance().getQueryColumnListByTableId(tableId,info);
	String queryColumns = "";
	String selectorTitles = "";
	if(null != columnNameAndTitles && columnNameAndTitles.length == 2){
	    queryColumns = columnNameAndTitles[0];
	    selectorTitles = columnNameAndTitles[1];
	}
	String hidePhone = StringUtil.nullToSpace(request.getParameter("hidePhone"));
	
	//查询访问控制字段
	String[] columnNameAndValues = ColumnDao.getInstance().getAclColumnListByTableId(tableId,userCode,info);
	String aclColumns = "";
	String aclColumnValues = "";
	boolean hasAclColumn = false;
	if(null != columnNameAndValues && columnNameAndValues.length == 2){
		hasAclColumn = true;
		aclColumns = columnNameAndValues[0];
		aclColumnValues = columnNameAndValues[1];
	}
	String relationStyle="height:650px;";
	String queryStyle = "";
	if("yes".equals(mainFrame)){
		 relationStyle="height:282px;";
		 queryStyle ="height:282px;overflow-y:auto;overflow-x:hidden;";
	}
	List<Map> infoList = TableDao.getInstance().getCustinfoByTelphone("4", schema+"."+tableName, tableId, caller,info);
%>
<EasyTag:override name="content"> 
<form id="custeform" name="custeform" class="form-inline" autocomplete="off"  onsubmit="return false;">
  <input type="hidden" name="USER_CODE" id="USER_CODE" value="<%=userCode%>" style="width: 0;height: 0"/>
  <input type="hidden" name="ID">	
<div class="ibox">
	<div class="ibox-title clearfix">
		<div class="form-group">	
				<span style="vertical-align:middle;font-size:15"><%=tableName %></span>
			<%
						// 如果有关联子表信息，则进行相关按钮的显示
						// 另外，只有在客户信息修改模式下才需要显示
						if(relationList.size() > 0 && !"add".equals(action)) { 
				%>
						<div class="input-group input-group-sm ml-30">
							<button type="button" class="btn btn-sm btn-primary" onclick="showBasicInfo()">
							<span class="fa fa-user"></span><span i18n-content="基本信息"></span></button>
				        </div>
				<%
							// 循环显示关联子表的信息按钮
							for(Map m : relationList) {
								String subTableId = (String)m.get("ID");
								if(subTableId==null||"".equals(subTableId))
									return;
				%>
				        <div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="showRelationData('<%=subTableId%>','<%=m.get("SUB_TABLE_ID")%>')">
							</span><%=(String)m.get("RELATION_NAME")%></button>
				        </div>
				<%
							}
						}
				%>	
			 <%if("true".equals(multiview)&&infoList.size()>1){%>
				     <div class="input-group input-group-sm pull-right mr-5">
				        <span class="input-group-addon" i18n-content="可选记录"></span>
						 <select id="selectedId" class="form-control input-sm" style="width: 150px" onchange="selector()">
						 <%for(Map map:infoList){
							 String custCode = (String)map.get("CUST_CODE");	
						     String custName = 	(String)map.get("CUST_NAME");
						     String text = custCode+"--"+custName; 
						 %>						     
							<option value="<%=(String)map.get("ID")%>"><%=text%></option>
						<%}%>
						</select>
				     </div>
				<%}%>
			</div>
  <hr style="margin: 5px -15px">		
	<center>
	<div id="layoutDiv" style="padding-top:10px;<%=queryStyle%>"></div>
	</center>
</div>
</div>
<%
	//加载子表菜单
	if(action.equals("edit")) {
		for(Map m : relationList) {
	%>
		<div id='relationDiv_<%=m.get("ID")%>' style='display:none;padding:0px;margin:0px;<%=relationStyle%>'>
			<iframe width="100%" height="100%" src="" id="relationFrame_<%=m.get("ID")%>" frameborder="0" scrolling="yes"></iframe>
		</div>
	<%
		}
	}
	%>	
	<div class="layer-foot text-c">
	    <button type="button" id="queryC" class="btn btn-sm btn-default" onclick="queryCustInfo()" i18n-content="查询"></button>
	    <button type="button" id="btnEdit" class="btn btn-sm btn-primary ml-10" onclick="saveCustInfo()" i18n-content="保存"></button>
	    <button type="button" id="cancel" class="btn btn-sm btn-default ml-10" onclick="closeLayer()" i18n-content="关闭"></button>
	</div>			
</form>	
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/layout.js"></script>
<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	<%
	   out.println(DicColumnHelper.generateJsonCode(tableId,info,request));

	%>
	var isDebugModel = "<%=isDebugModel%>";
	var LAYOUT_COLS_IN_ROW = <%=colsInRow%>; // 布局默认显示字段的列数配置
	var tableId = "<%=tableId%>";
	var recordId = "<%=recordId%>";
	var custId = "<%=custId%>";
	var action = "<%=action%>";
	var caller = "<%=caller%>";
	var f = document.getElementById("custeform");
	var layoutId = ""; // 标识当前采用的layout
    var fkey = "<%=fkey%>";
    var queryColumns = "<%=queryColumns%>";
    var selectorTitles = "<%=selectorTitles%>";
    var selectorFields = "<%=queryColumns%>";
    var aclColumns = "<%=aclColumns%>";
    var aclColumnValues = "<%=aclColumnValues%>";
    var hasAclColumn = <%=hasAclColumn%>;
    var aclColumnMaps = {};
    var hidePhone = "<%=hidePhone%>";
    function initAclColunms(){
	    if(hasAclColumn == true){
	    	var aclColumnArray = aclColumns.split(',');
	    	var aclColumnValueArray = aclColumnValues.split(';');
	    	if(aclColumnArray.length > 0 && aclColumnArray.length == aclColumnValueArray.length){
	    		for(var i=0;i<aclColumnArray.length;i++){
		    		var values = aclColumnValueArray[i].split(',');
		    		var valueMap = {};
		    		for(var j=0;j<values.length;j++)
		    			valueMap[values[j]] = values[j];
	    			aclColumnMaps[aclColumnArray[i]] = valueMap;
	    		}
	    	}
	    }
		    
    }
    initAclColunms();
  
	getTelColumnsFromTable(tableId);  // 第一步：读取表中的电话字段列表，完成以后再开始展现布局
	function startLayoutRender() {
		ajax.remoteCall("${ctxPath}/servlet/custData?action=defaultLayout", {tableId:tableId}, function(result) {	
			if(result.state==1&&!isNull(result.data)){	
  				var layoutId = result.data;
  				readDefaultLayoutForTable(layoutId, custInfoRender);
			}else{
				// 尚未设置过布局，则从表中初始化; 
				// 此function在布局定义页面中复用;
				getLayoutHtmlFromTable2(tableId,fkey,custInfoRender);
			}
		});
	}
	
	function isNull(str){
		return str==null||str==''||str=='null';
	}
	function selector(){
		recordId = $("#selectedId").val();
		startLayoutRender();
		if(parent.resetFrameUrl){
			parent.resetFrameUrl(recordId);
		}	
	}

	function custInfoRender(str) {	
		// 先将布局html中的{COL_NAME}标识清除掉,以避免初始化延时
		str = str.replace(/(value=\{[^\}]+\})|(value='\{[^\}]+\}')/g, "value=''");
		var footTips = '<hr style="margin: 5px -15px"><span class="leftspan text-info" style="font-size:14px">'+getI18nValue('温馨提示：文本框获取焦点并回车后执行查询')+'</span>';	
		$("#layoutDiv").html(str+footTips);
		if(layoutId != "") {
			// 如果采用自定义布局展现，则要遍历替换掉页面上的字典字段
			$("input[type='button']").each(function(){
				var obj = $(this);
				var name = obj.attr("id") 
				var display = parseInt(obj.attr("display"));
				obj.replaceWith(selectableColumnRender(name, display));
			})
		}

		// 如果字段为日期类型，则自动加入DatePicker绑定
		$("input[isDate='true']").each(function(){
			var obj = $(this);
			obj.addClass("Wdate");
		    obj.on('click', function() {
				WdatePicker({dateFmt:obj.attr('dateFormat'),isShowToday:true,isShowClear:false,lang:getDateLang()});
			});
		})
		
		// 如果字段为日期类型，则自动加入DatePicker绑定
		$("input[isNumber='true']").each(function(){
			var obj = $(this);
			obj.attr("type","number");
		})
		
		// 跟踪一下自定义处理方法
		if(isDebugModel == "yes") {
			try{layer.alert(getI18nValue("自定义扩展处理方法 : ") + typeof(extendDataHandler));
			layer.alert(getI18nValue("自定义扩展处理方法 : ") + extendDataHandler);}catch(e){}
		}

		if(action == "edit") {
			getCustInfoById(tableId, recordId);
		} else {
			resetForm(); // 清除表单元素的value属性
			initTelColumnsValue(); // 自动初始化电话字段值
			// 如果存在自定义数据处理方法，则进行调用
			if(typeof(extendDataHandler) == "function") {
				extendDataHandler();
			}
			renderLayoutDiv();
		}

		if(custId != "null") {
			// 新增或修改子表记录时，自动增加一个CUST_ID项
			$(f).append("<input type='hidden' name='CUST_ID' value='"+custId+"'>");
		}

		//bindTelColumnsCallEvent(); // 绑定电话字段的外呼事件 modify by lxh 2011-10-26
		bindSpecifiedColumnsSearchEvent(); // 绑定指定字段的搜索事件

	    if (hidePhone == "yes" && action != "add") { //设置了隐藏，并且不是新增的客户资料
		     displayTelColumnsAfterQuery();
	    }
	}

	var specifiedColunns = ["CUST_PHONE","CUST_NAME"]; //TODO 这里修改为从配置文件中读取
    function handlerResultCallback(result){
		//alert('回车查询后进入了回车函数，参数返回:' + result);
		fillRecord(result,null,',',null);
		renderCity();
	}

	function readDefaultLayoutForTable(layoutId, callback) {
		if(isDebugModel == "yes") {
			layer.alert(getI18nValue("读取自定义布局文件ID : ")+layoutId,{title:getI18nValue('提示')});
		}
		ajax.remoteCall("${ctxPath}/servlet/layout?action=readLayoutFile", {layoutId:layoutId}, function(reply) {					
			if(reply.state==1){
				var html = reply.data;
				// 如果尚未编辑保存过布局，则自动读取默认布局
				callback(html);
			}else{
				layer.alert(reply.msg,{title:getI18nValue("提示")});
			}
		});
	}


	var telColumns = [];
	function getTelColumnsFromTable(tableId) {	
		ajax.remoteCall("${ctxPath}/servlet/custColumn?action=telColumnsByTableId", {tableId:tableId}, function(reply) {
			telColumns = reply.data;
			startLayoutRender();  // 开始进行布局的展现处理
		});
	}

	function initTelColumnsValue() {
		if(!checkNull(caller)) {
			var isPhone = !!caller.match(/^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$/);	//手机号码
			for(var i=0; i<telColumns.length; i++) {
				if(isPhone&&$("#"+telColumns[i]).parent().prev().html().indexOf('手机')>-1){	//手机
					$("#"+telColumns[i]).val(caller);
				}else if(!isPhone&&$("#"+telColumns[i]).parent().prev().html().indexOf('电话')>-1){	//电话
					$("#"+telColumns[i]).val(caller);
				}
			}
		}
	}


	function bindTelColumnsCallEvent() {
		for(var i=0; i<telColumns.length; i++) {
			$("#"+telColumns[i]).addClass("outcall").attr("title","双击可进行外呼操作").bind("dblclick",
				function(){ 
					var v = $(this).val();
					if(v == "") {
						layer.alert(getI18nValue("外呼号码不能为空！"));
					} else if(confirm(getI18nValue('确定要对号码[')+v+getI18nValue(']进行外呼操作吗？'))) {
						doCallOut("", v);  // 执行外呼动作
					}
				}
			);
		}
	}
	function getCustInfoById(tableId, recordId) {
		var data ={
				recordId:recordId,
				tableId:tableId
		}
		ajax.remoteCall("${ctxPath}/servlet/custData?action=custRecord",data, function(reply) {
			var map = reply.data;
			for(var k in map) {
				if(map[k] == "null") {
					//去掉所有null字段值，置成空串比较友好
					map[k] = "";
				} else if(map[k]['_CCFTAG_OBJECT_CLASS_']=="java.sql.Date") {
					// sybase下，日期类型字段值会被convert成object形式
					// 取object中的value属性； By ZhouHuan, 2010-11-10
					map[k] = map[k]['value']; 
				}
				// oracle下，日期类型会自动补上 12:00:00
				// 这里判断一下，是否需要截断时间部分；By ZhouHuan, 2010-11-17
				if($('#'+k).attr("dateFormat") == "yyyy-MM-dd") {
					map[k] = map[k].substring(0, 10);
				}
			}
			fillRecord(map,null,',',null);
			renderCity();			
			for(var i=0; i<checkboxColumns.length; i++) {
				setCheckboxValues(checkboxColumns[i], map[checkboxColumns[i]]);
			}
		
			// 如果存在自定义数据处理方法，则进行调用
			if(typeof(extendDataHandler) == "function") {
				extendDataHandler();
			}
			renderLayoutDiv(map);
		});
	}

	function showSubpage(url,w,h){
		layer.open({
	        type: 2,
	        area: [w,h],
	        title: [getI18nValue('子表维护界面'),"background-color:#fff"],
	        content: url
	    }); 
	}
	
	function saveCustInfo() {
		if(form.validate(f)) {
			var map = form.getJSONObject(f);	
			var custId = map["CUST_ID"];
			if(fkey != ""){
				map[fkey] = custId;
			}
			for(var i=0; i<checkboxColumns.length; i++) {
				var obj = $("input[name='"+checkboxColumns[i]+"']:checked");
				var required = $($("input[name='"+checkboxColumns[i]+"']")[0]).attr("required");
				if(required!='true'&&obj.length==0){
					layer.alert(getI18nValue("带<font color=red>*</font>标记的复选框至少选一个"),{title:getI18nValue("提示")});
					return ;
				}
				map[checkboxColumns[i]] = getCheckboxValues(checkboxColumns[i]);
			}
			map["TABLE_ID"] = tableId; // 这里将tableId传入备用
			map["USER_CODE"] = $('#USER_CODE').val(); // 这里将tableId传入备用
			map["CREATE_ACC"] = $('#USER_CODE').val();
			var data = {};
			data.map = map;
			data.tableId = tableId;
			if(action == "add") {					
				ajax.remoteCall("${ctxPath}/servlet/custData?action=insertRecord", data, function(reply) {
					var result = reply.data;
					if(reply.state==1&&result.indexOf("ok") == 0) {
						layer.msg(reply.msg,{icon: 1,time:1200},function(){
							closeWindowNew();
						});
						action = "edit";  // 改变当前操作标识
						$("div[class='DHTMLSuite_menuItem_textContent']:first").html(getI18nValue("保存")).prev().html("<img src='/eaptag2/css/images/ui.form/edit.png'/>"); // 改变按钮图标

						var id = -1;
						if(result.indexOf("$") > 0) {
							// 如果在返回结果中附带了新记录id，则直接刷新！
							id = result.split("$")[1];
						}
						f.ID.value = id; // 将新id存入form中
						// 新增客户资料成功后(如果在关联弹屏模式下)需要调用上层方法显示子页面
						// 新增完成后，新记录会送入缓存中，关联页面可以用OpenCC接口获取到
						if(typeof(top.loadSubPages) == "function") {
							top.loadSubPages(id);
						}
						// 调用一个自定义save处理句柄
						if(typeof(extendSaveHandler) == "function") {
							extendSaveHandler();
						}
					} else {
						layer.alert(reply.msg,{icon : 5});
					}
				});
			} else if(action == "edit"){
				ajax.remoteCall("${ctxPath}/servlet/custData?action=updateRecord", data, function(reply) {
					var result = reply.data;
					if(result == "ok") {
						layer.msg(reply.msg,{icon: 1,time:1200},function(){
							closeWindowNew();
						});
						// 调用一个自定义save处理句柄
						if(typeof(extendSaveHandler) == "function") {
							extendSaveHandler();
						}
					} else {
						layer.alert(reply.msg,{icon : 5});
					}
				});
			}
		}
	}
	function closeWindowNew() {
		try{
			var params={};
			popup.layerClose(f);
			parent.refreshGrid();
		} catch(e) {
			
		}
	}


	function resetForm() {
		for(var i=0; i<f.elements.length; i++) {
			if(f.elements[i].type=="radio" || f.elements[i].type=="checkbox") {
				continue;
			}
			// 这里要跳过radio和checkbox两种元素
			// 不然新增操作时取到的value值会为空
			f.elements[i].value = '';
		}
	}
	

	function getCheckboxValues(name) {
		var values = [];
		var obj = f.elements[name];
		for(var i=0; i<obj.length; i++) {
			if(obj[i].checked) {
				values.push(obj[i].value);
			}
		}
		return values.join(",");
	}


	function setCheckboxValues(name, values) {
		var obj = f.elements[name];
		for(var i=0; i<obj.length; i++) {
			if(values.indexOf(obj[i].value) >= 0) {
				obj[i].checked = true;
			}
		}
	}

	function controlByAclColumn(columnName,columnValues){
		if(hasAclColumn == true){
			var aclValues = aclColumnMaps[columnName];
			var values = {};
			if(aclValues){
				for(var k in aclValues){
					values[k] = columnValues[k];
				}
			}
			return values;
		}
		else
			return columnValues;
	}
	var checkboxColumns = [];
	
	function selectableColumnRender(name, display,required,property) {
		var i = display;
		var n = name;
		var m_col = DIC_COLUMN_DATA[n];
		//var required = COLUMN_REQUIRED[n];
		var html = [];
     	// 控制访问    	
		m = controlByAclColumn(n,m_col);
     	
		var rules = "";
		if(required=='Y'){
			rules = "data-rules='required'";
		}
		
     	if("PROVINCE"==n||"CITY"==n){
     		switch( n ) {
	     		case "PROVINCE":
					html.push("<select class='form-control input-sm' "+rules+" data-mars='custTable.treeDictProvince()' name='"+n+"' id='"+n+"' onchange='cascadeSelectProvince(this)' style='width:173px'>");
					html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
					html.push("</select>");
					break;
	     		case "CITY":
	     			html.push("<select class='form-control input-sm' "+rules+" name='"+n+"' id='"+n+"' pid='PROVINCE' onchange='cascadeSelectProvince(this)' style='width:173px'>");
					html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
					html.push("</select>");
					break;
     		}
     		
     	}else{
     		switch( i ) {
			case 4: 
				var cc = 0;
				var checked = null;
				for(var k in m) {
					cc++;
					if(cc==1&&required=='Y') checked ="checked";
					  else checked = "";
					html.push("<input type='radio' "+rules+" style='vertical-align:top;' "+checked+" name='"+n+"' value='"+k+"'>"+m[k]+" ");
				}
				break;
			case 5:
				checkboxColumns.push(n);
				// checkbox的取值要特别处理，目前直接将value置成key值，并写入表中
				var cc = 0;
				var checked = null;
				for(var k in m) {
					cc++;
					if(required=='required'){
						checked = "required='true'";
						if(cc==1) checked +=" checked";
					}else checked = "required='false'";					
					html.push("<input type='checkbox' style='vertical-align:top;' "+checked+" name='"+n+"' value='"+k+"'>"+m[k]+" ");
				}
				break;
			case 6:
				if(n=='PROVINCE'){
					html.push("<select class='form-control input-sm' name='"+n+"' id='"+n+"' data-rules='"+required+"' onchange='cityConfirm()' style='width:173px'>");
				} else {
					html.push("<select class='form-control input-sm' "+rules+" name='"+n+"' id='"+n+"' style='width:173px'>");
				}
				html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
				for(var k in m) {
					html.push("<option value='"+k+"'>"+m[k]+"</option>");
				}
				html.push("</select>");
				break;
			case 7:
				html.push("<select class='form-control input-sm' "+rules+" name='"+n+"' id='"+n+"' style='width:173px'>");
				html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
				for(var k in m_col) {
					html.push("<option value='"+k+"'>"+m_col[k]+"</option>");
				}
				html.push("</select>");
				break;
			case 8:
				var code = "";
				if(property){
					var po = $.parseJSON(property);
					code = po.CODE;
				}
				html.push("<select class='form-control input-sm' "+rules+" data-mars='custTable.treeDictP(\"" + code + "\")' name='"+n+"' id='"+n+"' onchange='cascadeSelect(this)' style='width:173px'>");
				html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
				/*
				for(var k in m_col) {
					html.push("<option value='"+k+"'>"+m_col[k]+"</option>");
				}*/
				html.push("</select>");
				break;
			case 9:
				html.push("<select class='form-control input-sm' "+rules+" name='"+n+"' id='"+n+"' pid='" + property + "' onchange='cascadeSelect(this)' style='width:173px'>");
				html.push("<option value=''>"+getI18nValue("请选择")+"</option>");
				/* for(var k in m_col) {
					html.push("<option value='"+k+"'>"+m_col[k]+"</option>");
				} */
				html.push("</select>");
				break;
			}
     	}
		
		return html.join("");
	}
	
	function cascadeSelect(e){
		var pid = $(e).attr("id");
		var selectCId = $(e).val();
		$.each($("select[pid=" + pid + "]"), function(index, sel){
			$(sel).data("mars", "custTable.treeDictC('" + selectCId + "')").render();
			$(sel).val("");
			$(sel).data("");
			$(sel).trigger('change', [sel]);
		});
	}
	function cascadeSelectProvince(e){
		var pid = $(e).attr("id");
		var selectCId = $(e).val();
		$.each($("select[pid=" + pid + "]"), function(index, sel){
			$(sel).data("mars", "custTable.treeDictCity('" + selectCId + "')").render();
			$(sel).val("");
			$(sel).data("");
			$(sel).trigger('change', [sel]);
		});
	}
	
	function renderLayoutDiv(mapValue){
		$.each($("#layoutDiv select[data-mars]"), function(index, sel){
			var id = $(sel).attr("id");
			if(mapValue && mapValue[id]){
				$(sel).data("value", mapValue[id]);
			}
			$(sel).render();
		});
		$.each($("#layoutDiv select[pid]"), function(index, sel){
			var id = $(sel).attr("id");
			var pid = $(sel).attr("pid")
			if("CITY"==id){
				if(mapValue && mapValue[id]){
					$(sel).data("value", mapValue[id]);
					$(sel).data("mars", "custTable.treeDictCity('" + mapValue[pid] + "')");
				}
			}else{
				if(mapValue && mapValue[id]){
					$(sel).data("value", mapValue[id]);
					$(sel).data("mars", "custTable.treeDictS('" + mapValue[id] + "')");
				}else if(mapValue && mapValue[pid]){
					$(sel).data("mars", "custTable.treeDictC('" + mapValue[pid] + "')");
				}
			}
			$(sel).render();
		});
		
		renderCustTag();
	}
	
	
	function bindSpecifiedColumnsSearchEvent() {
		if("" == queryColumns){
			return;
		}
		
		var customerClientDAO = new CustomerClientDAO();  
		if("" != selectorTitles) customerClientDAO.setSelectorTitles(selectorTitles); //设置客户确认弹出窗口Grid标题
		if("" != selectorFields) customerClientDAO.setSelectorFields(selectorFields); //设置客户确认弹出窗口Grid标题对应属性
		
		
		var specifiedColunns = queryColumns.split(",");
		for(var i = 0; i < specifiedColunns.length; i++) {
			var tip = "";
			$("#"+specifiedColunns[i]).addClass("canSearch").attr("title",tip).bind("keyup",
				function(event){ 
					if(event.keyCode=="13"){
						var v = $(this).val();
						if(v == "") {
							return;
						} 
						// 执行查找动作
						var param = {};
						param[$(this).attr('id')] = v;
						param['tableId'] = tableId;
						customerClientDAO.getCustomerByKeyValues(param,handlerResultCallback);
					}
				}
			);
		}
	}

	function queryCustInfo(){	
	if("" == queryColumns){
		   var msg = getI18nValue("没有指定查询属性，请管理员到客户属性管理设置查询属性.");
			layer.alert(msg,{icon:5});
			return;
	}
		var customerClientDAO = new CustomerClientDAO();  
		var specifiedColunns = queryColumns.split(",");
		var param = {};
		for(var i = 0; i < specifiedColunns.length; i++) {
			var columnValue = $("#"+specifiedColunns[i]).val();
			if(columnValue){
				param[specifiedColunns[i]] = columnValue;
			}
		}
		param['tableId'] = tableId;
		customerClientDAO.getCustomerByKeyValues(param,handlerResultCallback);
	}

	/**
	  * 客户表动态输出布局后控制号码的显隐
	  */
	function displayTelColumnsAfterQuery() {
		    $.each(telColumns, function(index, value) {
				var key = telColumns[index];
				$('#'+key).css('display','none').after('<input value="******" id="' + key + '_hide" readonly="readonly"'
						+ '" class="value-check-textbox textbox-border-blur" type="text" style="width:200px" />');
			});			
	}
	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>
<script type="text/javascript" src="${ctxPath}/static/js/comUtils.js"></script>
<script type="text/javascript" src="${ctxPath}/static/js/showData.js"></script>
<script type="text/javascript" src="${ctxPath}/static/js/minigrid.js"></script>
<script type="text/javascript" src="${ctxPath}/static/js/customerDao.js"></script>