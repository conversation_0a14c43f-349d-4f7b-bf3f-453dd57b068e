package com.yunqu.cc.cccase.dao.sql;

import org.apache.commons.lang3.StringUtils;

import com.yq.busi.common.base.CEConstants;

public class BaseSql {

	/**
	 * 获取Stat数据库的表名
	 */
	protected static String getStatTableName(String tableName){
		if(StringUtils.isBlank(CEConstants.getStatSchema())){
			return tableName;
		}
		return CEConstants.getStatSchema() + "." + tableName;
	}
	

	/**
	 * 
	 */
	protected static String getTableName(String schema,String tableName){
		if(StringUtils.isBlank(schema)){
			return tableName;
		}
		return schema + "." + tableName;
	}
}
