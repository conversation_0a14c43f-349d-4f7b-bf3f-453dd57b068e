<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">

	<style type="text/css">
		a:link {
			color: #00adff;
		}

		.el-dialog__body{
		    padding: 0px;
		   
		}
		.gray-bg, .bg-muted {
		    background-color: WHITE;
		}
		.mt-20 {
		    margin-top: -20px;
		}
				
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="searchFormSend" action="javaScript:void(0)"  name="searchFormForm" autocomplete="off" >

		
   			
      			<div class="grid-demo grid-demo-bg1">
      						
						<div class="input-group" >
						
							<div id="count" style="white-space:break-spaces">
							</div>
						 <img src="" id="imageId" style="height:400px;margin-left:70px"  class="tdImg" tb-img/>
							
							
      			</div>
    		</div>

  		
  		<!-- 
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-default ml-20"  type="button" onclick="closePage()"  i18n-content="关闭"></button>
		</div> -->
	</form>
</EasyTag:override>

<EasyTag:override name="script">
 <script src="${ctxPath}/static/js/upload.js"></script>
 <script type="text/javascript" src="/yc-media/static/js/chat/qq-wechat-emotion-parser.min.js"></script>
 <script type="text/javascript">
	$(function() {
		var id = getQueryVariable('id');
		
		//根据id获取图片路径和祝福语
		getData(id);
	})

	function getData(id){
		ajax.remoteCall("${ctxPath}/webcall?action=birthday.getByIdTab", {id:id}, function(result) {
			var arr=result.data;
  			if(arr){
  				msgContent=qqWechatEmotionParser(arr.CONTENT);
  				$('#count').html(msgContent);
  				$("#imageId").attr("src",arr.IMG_URL);
			}
		});
	}
	
	
		function getQueryVariable(variable) {
			var query = window.location.search.substring(1);
			var vars = query.split("&");
			for (var i = 0; i < vars.length; i++) {
				var pair = vars[i].split("=");
				if (pair[0] == variable) {
					return pair[1];
				}
			}
			return (false);
		}
		

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>