.agent-manage-box {
  min-height: 400px;
  padding: 10px;
  overflow: auto;
  border: 1px solid #ddd;
  text-align: center;
}
.agent-manage-main {
  display: inline-block;
  position: relative;
  text-align: left;
}
.agent-manage-main .agent-item {
  vertical-align: top;
  display: inline-block;
  box-sizing: border-box;
  position: relative;
  margin: 5px 10px;
}
.agent-manage-main .agent-item .content {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 50px;
  border: 2px solid #c1c1c1;
  box-sizing: border-box;
  border-radius: 3px;
}
.agent-manage-main .agent-item .content:hover i {
  outline: 1px dashed #ccc;
}
.agent-manage-main .agent-item .drag {
  top: 0;
  right: 0;
  display: inline-block;
  position: absolute;
  width: 32px;
  height: 32px;
  background: url(../images/drag.png) no-repeat;
  cursor: pointer;
}
.agent-manage-main .agent-item[data-type='2'] {
  opacity: 0.7;
}
.agent-manage-main .agent-item[data-type='2'] .content {
  border: 2px dashed #c1c1c1;
}
.agent-manage-main.onlyShow [data-type='2'] {
  opacity: 0;
}
.agent-manage-header {
  padding: 10px;
}
.agent-manage-content {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.agent-manage-content > div {
  display: table-cell;
  vertical-align: top;
}
.agent-manage-content .box-left {
  padding: 20px;
}
.agent-manage-content .box-right {
  width: 320px;
}
