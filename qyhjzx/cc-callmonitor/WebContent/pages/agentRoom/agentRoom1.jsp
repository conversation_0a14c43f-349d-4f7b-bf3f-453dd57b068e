<%@page import="com.yunqu.yc.sso.impl.YCUserPrincipal" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<%
	YCUserPrincipal principal = (YCUserPrincipal) request.getUserPrincipal();
	if (principal.getEcUserPrincipal() != null) {
		principal = principal.getEcUserPrincipal();
	}
	request.setAttribute("userAcct", principal.getLoginAcct());
%>
<EasyTag:override name="head">
	<title>房间</title>
	<style type="text/css">
		#searchForm .form-group .input-group {
			width: 200px;
		}

		#searchForm .form-group .input-group-addon {
			width: 70px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
						  <div class="form-group">
						  	   <div class="form-group" style="margin-bottom: 10px">
	             		       		<h5><span class="glyphicon glyphicon-list"></span>房间</h5>
						  	   </div>
						  	   <div class="input-group input-group-sm">
								      <span class="input-group-addon">创建时间</span>	
									 <input type="text" name="startDate" id="startDate" class="form-control input-sm" style="width:140px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,maxDate:'#F{$dp.$D(\'endDate\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="endDate" id="endDate" class="form-control input-sm" style="width:140px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,minDate:'#F{$dp.$D(\'startDate\')}'})">		
							   </div>
						  	   <div class="input-group input-group-sm">
								      <span class="input-group-addon">房间名称</span>	
									  <input type="text" id="NAME" name="NAME" class="form-control input-sm">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="agentRoom.query();"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
							   <div class="pull-right">
									<button type="button" class="btn btn-sm btn-success" onclick="agentRoom.add()"><span class="glyphicon glyphicon-plus"></span> 新增</button>
							   </div>
						  </div>
             	    </div>  
			<div class="ibox-content table-responsive">
				<table id="agentRoomTable" class="layui-hide"></table>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		var agentRoom = {
			query: function () {
				$("#searchForm").queryData();
			},
			initTable: function () {
				$("#searchForm").initTable({
					id: 'agentRoomTable',
					mars: 'agentRoom.agentRoomList',
					height: 'full-150',
					limits: [10, 15, 25, 50, 100],
					cols : [[
						{title:'房间名称',field:'NAME'},
						{title:'座位数',field:'SEATS'},
						{title:'备注',field:'BAKUP'},
						{title:'创建人',field:'CREATE_ACC'},
						{title:'创建时间',field:'CREATE_TIME'},
						{title:'操作',templet:function(row){
							var html = '';
							if (row.ID) {
								html += '<a href="javascript:agentRoom.edit('+row.ID+')" style="color:#337ab7">编辑</a>&nbsp;<a href="javascript:agentRoom.del('+row.ID+');" style="color:#337ab7">删除</a>&nbsp;';
							}
							return html;
							}
						}
					]]
				});
			},
			add : function() {
				popup.layerShow({type:1,title:'添加房间',offset:'20px',area:['600px;','380px']},"${ctxPath}/pages/agentRoom/agentRoomEdit.jsp",{});
			},
			edit : function(id) {
				popup.layerShow({type:1,title:'编辑房间',offset:'20px',area:['1000px;','800px']},"${ctxPath}/pages/agentRoom/agentRoomEdit.jsp",{id:id});
			},
			del : function(id){
				if(id.length<1){
					layer.alert('请选择需要删除的行！');
				}else {
					layer.confirm('确认要删除这条信息吗？', {
			            btn : [ '确定', '取消' ],offset:'40px'//按钮
			        }, function(index) {
						var data={};
						data.ID=id;
						ajax.remoteCall("${ctxPath}/servlet/room?action=delete",data, function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon: 1,offset:'60px',time:1200},function(){
									agentRoom.query();
								});
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						}); 
				    });
				}
			}
		}
		$(function () {
			requreLib.setplugs('wdate', function () {
				agentRoom.initTable();
			});
		});
		
		layui.use(
				[ 'laydate', 'laypage', 'layer', 'table', 'element', 'form' ],
				function() {
					var laydate = layui.laydate //日期
					, laypage = layui.laypage //分页
					, layer = layui.layer //弹层
					, table = layui.table //表格
					, form = layui.form;
					//, iframeindex=parent.layer.getFrameIndex(window.name);
					
					//年选择器
					laydate.render({
						elem : '#year',
						type : 'year'
					});
					//日期范围选择器
					laydate.render({
						elem : '#day'
						,range: true
					});
				});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>