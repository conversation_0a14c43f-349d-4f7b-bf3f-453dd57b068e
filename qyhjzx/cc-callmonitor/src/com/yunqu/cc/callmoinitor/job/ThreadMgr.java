package com.yunqu.cc.callmoinitor.job;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.yunqu.cc.callmoinitor.base.CommonLogger;


/**
 * <p>
 * Title:简单线程管理
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2013
 * </p>
 * <p>
 * Company: 佳都新太
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ThreadMgr {

	// 日志打印类
	protected static Logger logger = CommonLogger.logger;
	private static int POOL_SIZE = 8;
	private static int CPU_NUMS = Runtime.getRuntime().availableProcessors();
	
	private static ThreadMgr instance = new ThreadMgr();
	
	//执行循环的线程
	private static ScheduledExecutorService scheService = Executors.newScheduledThreadPool(CPU_NUMS);
	
	//执行非循环的线程
	private static ScheduledExecutorService scheOneService = Executors.newScheduledThreadPool(CPU_NUMS*POOL_SIZE);
	
	
	private ThreadMgr(){}
	
	public static ThreadMgr getInstance(){
		return instance;
	}
	//标识线程池是否已经启动
	private static  boolean isStart = false; 
	
	private static boolean isClosed = false;
	
	/**
	 * 初始化一些需要循环执行的任务
	 */
	public  void  start(){
		if(isStart){
			logger.error("[ThreadMgr-start]The ThreadMgr has been started.");
			return;
		}
		logger.info("[ThreadMgr-start] ThreadMgr  start begin.");
		
		isStart = true;
		isClosed = false;
		logger.info("[ThreadMgr-start] ThreadMgr  start end.");
	}
	
	/**
	 * 添加只需要循环执行的线程
	 * @param runnable
	 * @param delay
	 * @param period
	 * @param unit
	 * @throws Exception
	 */
	@SuppressWarnings("rawtypes")
	public  ScheduledFuture  executeRepeat(Runnable runnable,long delay, long period, TimeUnit unit){
		logger.debug("[ThreadMgr-executeRepeat] executeRepeat add:" + runnable.getClass());
		return scheService.scheduleAtFixedRate(runnable, delay, period, unit);
	}
	
	/**
	 * 添加只需要执行一次的线程
	 * @param args
	 * @throws Exception
	 */
	public  void executeOneTimes(Runnable runnable){
		scheOneService.submit(runnable);
	}
	
	/**
	 * 线程池是否已经关闭
	 * @return
	 */
	public  boolean isShutDown(){
		return scheService.isShutdown() && scheOneService.isShutdown();
	}
	/**
	 * 关闭线程池
	 * @return
	 */
	public  void shutDown(){
		logger.info("[ThreadMgr-shutDown] ThreadMgr shutDown begin...");
		if(!scheService.isShutdown()){
			scheService.shutdown();
		}
		if(!scheOneService.isShutdown()){
			scheOneService.shutdown();
		}
		logger.info("[ThreadMgr-shutDown] ThreadMgr scheService shutDown...");
		logger.info("[ThreadMgr-shutDown] ThreadMgr shutDown end...");
		isStart = false;
		isClosed = true;
	}
	
	public  boolean isClosed(){
		return isClosed;
	}
	
	public static void main(String[] args) throws Exception {
		
	}
}


