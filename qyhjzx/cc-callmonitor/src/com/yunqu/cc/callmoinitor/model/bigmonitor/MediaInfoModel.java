package com.yunqu.cc.callmoinitor.model.bigmonitor;

import com.alibaba.fastjson.JSONObject;

public class MediaInfoModel {

	private String agentCount; // 坐席数

	private String onlineAgentCount; // 在线坐席数
	
	private String mediaCount; // 总会话数
	
	private String mediaCountPresent; // 当前会话数
	
	private String avgAgentCount; // 人均话务量
	
	private String avgAgentTime; // 平均服务时长
	
	private String satisfRatio; // 评价率
	
	private String satisfGoodRatio; // 满意率
	
	private String avgFirstReply; // 平均首响时长
	
	private String custCount; // 客户数
	
	private String toAgentRaito; // 请求人工率
	
	private String reqAgentNum; // 请求人工数
	
	private String needFollowRecord; // 待跟进会话数

	public String getAgentCount() {
		return agentCount;
	}

	public void setAgentCount(String agentCount) {
		this.agentCount = agentCount;
	}

	public String getOnlineAgentCount() {
		return onlineAgentCount;
	}

	public void setOnlineAgentCount(String onlineAgentCount) {
		this.onlineAgentCount = onlineAgentCount;
	}

	public String getMediaCount() {
		return mediaCount;
	}

	public void setMediaCount(String mediaCount) {
		this.mediaCount = mediaCount;
	}

	public String getMediaCountPresent() {
		return mediaCountPresent;
	}

	public void setMediaCountPresent(String mediaCountPresent) {
		this.mediaCountPresent = mediaCountPresent;
	}

	public String getAvgAgentCount() {
		return avgAgentCount;
	}

	public void setAvgAgentCount(String avgAgentCount) {
		this.avgAgentCount = avgAgentCount;
	}

	public String getAvgAgentTime() {
		return avgAgentTime;
	}

	public void setAvgAgentTime(String avgAgentTime) {
		this.avgAgentTime = avgAgentTime;
	}

	public String getSatisfRatio() {
		return satisfRatio;
	}

	public void setSatisfRatio(String satisfRatio) {
		this.satisfRatio = satisfRatio;
	}

	public String getSatisfGoodRatio() {
		return satisfGoodRatio;
	}

	public void setSatisfGoodRatio(String satisfGoodRatio) {
		this.satisfGoodRatio = satisfGoodRatio;
	}
	
	public String getAvgFirstReply() {
		return avgFirstReply;
	}

	public void setAvgFirstReply(String avgFirstReply) {
		this.avgFirstReply = avgFirstReply;
	}

	public String getCustCount() {
		return custCount;
	}

	public void setCustCount(String custCount) {
		this.custCount = custCount;
	}

	public String getToAgentRaito() {
		return toAgentRaito;
	}

	public void setToAgentRaito(String toAgentRaito) {
		this.toAgentRaito = toAgentRaito;
	}

	public String getReqAgentNum() {
		return reqAgentNum;
	}

	public void setReqAgentNum(String reqAgentNum) {
		this.reqAgentNum = reqAgentNum;
	}


	public MediaInfoModel() {
		
	}
	
	public MediaInfoModel(JSONObject json) {
		int agentCount = json.getIntValue("AGENT_COUNT");
		int mediaCount = json.getIntValue("MEDIA_COUNT");
		int serverTotalTime = json.getIntValue("SERVER_TOTAL_TIME");
		int satisfCount = json.getIntValue("SATISF_COUNT");
		int satisfGoodCount = json.getIntValue("SATISF_GOOD_COUNT");
		int inMediaCount = json.getIntValue("IN_MEDIA_COUNT");
		int toAgentCount = json.getIntValue("TO_AGENT_COUNT");

		this.setNeedFollowRecord(json.getString("NEED_FOLLOW_RECORD"));
		this.setReqAgentNum(toAgentCount + "");
		this.setAgentCount(agentCount + "");
		this.setMediaCount(mediaCount + "");
		this.setMediaCountPresent(json.getIntValue("MEDIA_COUNT_PRESENT") + "");
		this.setAvgFirstReply(String.format("%.2f", json.getDoubleValue("AVG_FIRST_REPLY")));
		this.setCustCount(json.getIntValue("CUST_COUNT") + "");
		this.setOnlineAgentCount("0"); // 设置默认值
		if(agentCount > 0) {
			this.setAvgAgentTime(String.format("%.2f", serverTotalTime/(double)agentCount));
			this.setAvgAgentCount(String.format("%.2f", mediaCount/(double)agentCount));
		} else {
			this.setAvgAgentCount("0.00");
			this.setAvgAgentTime("0.00");
		}
		if(mediaCount > 0) {
			this.setSatisfRatio(String.format("%.2f", satisfCount * 100/(double)mediaCount));
		} else {
			this.setSatisfRatio("0.00");
		}
		
		if(satisfCount > 0) {
			this.setSatisfGoodRatio(String.format("%.2f", satisfGoodCount * 100/(double)satisfCount));
		} else {
			this.setSatisfGoodRatio("0.00");
		}
		
		if(inMediaCount > 0) {
			this.setToAgentRaito(String.format("%.2f", toAgentCount * 100/(double)inMediaCount));
		} else {
			this.setToAgentRaito("0.00");
		}
		
	}

	public String getNeedFollowRecord() {
		return needFollowRecord;
	}

	public void setNeedFollowRecord(String needFollowRecord) {
		this.needFollowRecord = needFollowRecord;
	}
	
}
