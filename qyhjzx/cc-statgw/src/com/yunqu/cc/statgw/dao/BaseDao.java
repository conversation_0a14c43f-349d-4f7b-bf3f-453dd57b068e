package com.yunqu.cc.statgw.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.statgw.base.CommonLogger;
import com.yunqu.cc.statgw.base.Constants;


public class BaseDao {
	public static Logger logger = CommonLogger.logger;
	protected EasyQuery queryHelper = null;
	protected EasyQuery queryHelperRead = null;

	public BaseDao() {
		queryHelper = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		queryHelperRead = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
	}

	
	public void batchExecSql(EasyQuery queryHelper, ArrayList<String> sqlList) throws SQLException {
		if (CommonUtil.listIsNull(sqlList)) {
			return;
		}
		if (queryHelper != null) {
			queryHelper.executeBatch(sqlList);
		} else {
			this.queryHelper.executeBatch(sqlList);
		}
	}

	
	public void map2Save(ArrayList<String> sqlList, Map<String, Map<String, Object>> map, String tableName) {
		if(map == null || map.size() == 0)
			return;
			
		for (String key : map.keySet()) {
			Map<String, Object> param = (HashMap<String, Object>)map.get(key);
			mapToSave(sqlList, param, tableName);
		}
	}
	
	
	public void mapToSave(ArrayList<String> sqlList, Map<String, Object> param, String tableName) {
		if (param == null || param.size() == 0)
			return;

		StringBuffer sql = new StringBuffer();
		StringBuffer values = new StringBuffer();

		sql.append("insert into ").append(tableName).append("(");
		for (String key : param.keySet()) {
			sql.append(key).append(",");
			Object obj = param.get(key);
			if (obj instanceof String)
				values.append("'").append(obj).append("',");
			else
				values.append("").append(obj).append(",");
		}
		// 删除最后一个","
		sql.deleteCharAt(sql.length() - 1);
		values.deleteCharAt(values.length() - 1);
		sql.append(") values (").append(values.toString()).append(")");
		sqlList.add(sql.toString());
	}
	
	
	public void mapToUpdate(ArrayList<String> sqlList, Map<String, Object> param, String tableName) {
		if (param == null || param.size() == 0)
			return;

		String id = (String) param.get("ID");
		param.remove("ID");
		
		StringBuffer sql = new StringBuffer();
		sql.append("update ").append(tableName).append(" set ");		
		for (String key : param.keySet()) {
			sql.append(key).append("=");
			Object obj = param.get(key);
			if (obj instanceof String)
				sql.append("'").append(obj).append("',");
			else
				sql.append("").append(obj).append(",");
		}
		// 删除最后一个","
		sql.deleteCharAt(sql.length() - 1);
		sql.append(" where ID='").append(id).append("'");
		sqlList.add(sql.toString());
	}
	
	/**
	 * insert true插入 false 修改
	 * 
	 * @param tableName
	 * @param json
	 * @param insert
	 * @return
	 */
	public boolean saveOrUpdate(String tableName, JSONObject json, boolean insert) {
		if (StringUtils.isBlank(tableName)) {
			return false;
		}

		String id = json.getString("ID");
		StringBuffer sql = new StringBuffer();
		StringBuffer valuesSql = new StringBuffer();

		if (insert) {// 新增
			sql.append(" INSERT INTO ").append(tableName).append("(");
			Set<String> keys = json.keySet();
			for (String key : keys) {
				sql.append(key).append(",");
				Object obj = json.get(key);
				if (obj instanceof String) {
					valuesSql.append("'").append(obj).append("',");
				} else {
					valuesSql.append("").append(obj).append(",");
				}

			}
			// 删除最后一个","
			sql.deleteCharAt(sql.length() - 1);
			valuesSql.deleteCharAt(valuesSql.length() - 1);

			sql.append(") VALUES (").append(valuesSql.toString()).append(")");
		} else {

			sql.append(" UPDATE ").append(tableName).append(" SET ");
			Set<String> keys = json.keySet();
			for (String key : keys) {
				sql.append(key).append("=");
				Object obj = json.get(key);
				if (obj instanceof String) {
					sql.append("'").append(obj).append("',");
				} else {
					sql.append("").append(obj).append(",");
				}

			}
			// 删除最后一个","
			sql.deleteCharAt(sql.length() - 1);
			sql.append(" WHERE ID = '").append(id).append("'");
		}

		// 执行sql
		try {
			// logger.debug(CommonUtil.getClassNameAndMethod(this)
			// +"执行新增或修改sql：" + sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 执行新增或修改sql时出错", e);
		}
		return false;
	}

	public EasyQuery getQueryHelper() {
		return queryHelper;
	}
	
	/**
	 * 根据id字段，从某个表里查询出唯一记录
	 * @param queryHelper
	 * @param prompt    提示语
	 * @param tableName 表名
	 * @param idFiled   要作为查询条件的字段名
	 * @param id        字段值
	 * @return
	 */
	public EasyRow getRowById(EasyQuery queryHelper,String prompt,String tableName,String idFiled,String id) {
		StringBuffer sql = new StringBuffer(" SELECT * from ").append(tableName).append(" WHERE ").append(idFiled).append("='").append(id).append("'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+prompt+" :"+sql.toString());
			EasyRow row = queryHelper.queryForRow(sql.toString(),null);
			return row;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+prompt+" 失败.",e);
		}
		return null;
	}

}
