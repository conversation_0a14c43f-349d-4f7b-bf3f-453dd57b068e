package com.yunqu.cc.statgw.dao;

import java.util.List;

import org.easitline.common.db.EasyRow;

import com.yq.busi.common.util.CommonUtil;


public class QcAgentDao extends BaseDao {

	/**
	 * 找出所有的质检人员
	 * @return
	 */
	public List<EasyRow> findAllQcAgent(String stDate,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.USER_ACC QC_ACC,t2.USER_NAME QC_NAME,t4.QC_GROUP,t5.DEPT_CODE,t5.DEPT_NAME,");			
			sql.append("0 AREACODE,0 AREA_NAME,t2.ENT_ID EP_CODE,0 ENTRY_MONTHS,t3.SCHEDULING_ID,t3.SCHEDULING_NAME from ");	
			sql.append("(select t1.USER_ID,max(t1.EXAM_GROUP_ID) QC_GROUP from ");	
			sql.append(""+schema+".CC_QC_GROUP_INSPECTOR t1 group by t1.USER_ID)t4  ");	
			sql.append("left join "+schema+".V_CC_USER t2 on t4.USER_ID=t2.USER_ID ");	
			sql.append("left join "+schema+".V_CC_DEPT_USER t5 on t2.USER_ID=t5.USER_ID ");	
			sql.append("left join "+schema+".C_PB_SCHEDULING_PERSON t3 on t2.USER_ACC=t3.USER_ACC  and t3.PLAN_DATE='").append(stDate).append("' ");
			logger.debug(CommonUtil.getClassNameAndMethod(this) + "找出所有的质检人员:" + sql);
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "找出所有的质检人员失败.", e);
		}
		return null;
	}

	
	/**
	 * 删除质检员的汇总记录
	 * @param tableName
	 * @param stDate
	 * @param stType
	 */
	public void delQcRecord(String tableName, String stDate, String stType,String schema,String sessionType) {
		try {
			String sql = "delete from "+schema+"." + tableName + " where ST_QCAGENT_ID in (select  ID from "+schema+".C_ST_QCAGENT where ST_DATE='"
					+ stDate + "' and ST_TYPE='" + stType  + "') and SESSION_TYPE='"+sessionType+"'";
			logger.info(CommonUtil.getClassNameAndMethod(this) + "删除质检员的明细记录:" + sql);
			queryHelper.execute(sql, null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除质检员的汇总记录失败.", e);
		}
	}
	
	
	/**
	 * 获取质检员汇总记录
	 * @param qcAcc
	 * @param stDate
	 * @param stType
	 * @return
	 */
	public String getQcAgentRecord(String qcAcc, String stDate, String stType,String schema) {
		String id = "";
		
		try {
			String sql = "select ID from "+schema+".C_ST_QCAGENT where ST_TYPE='" + stType + "' and ST_DATE='" + stDate
					+ "' and QCAGENT_ACC='" + qcAcc + "'";			
			id = queryHelper.queryForString(sql, null);					
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "获取质检员汇总记录失败", e);
		}
		return id;
	}

	
	/**
	 * 按照渠道、方向统计质检员的提取量、监听未完成量
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentExtract(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t3.USER_ACC QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) EXTRACT_NUM,");
			sql.append("sum(case when t2.QC_STATE='0' then 1 else 0 end) WAIT_LISTEN_NUM ");
			sql.append("from "+schema+".CC_QC_RESULT t1 ");
			sql.append("left join "+schema+".V_CALL_RECORD t2 on t1.SERIAL_ID = t2.ID ");
			sql.append("left join "+schema+".V_CC_USER t3 on t1.INSPECTOR_ID=t3.USER_ID ");
			sql.append("where substr(t1.QC_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t3.USER_ACC,t2.CHANNEL_ID,t2.DIRECTION");
			
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的提取量、监听未完成量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的提取量、监听未完成量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 按照渠道、方向统计某个质检员的待定量
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentWaitDicision(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t1.QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) WAIT_DICISION_NUM ");
			sql.append("from "+schema+".CC_QC_RESULT t1 ");
			sql.append("left join "+schema+".V_CALL_RECORD t2 on t1.SERIAL_ID=t2.ID ");
			sql.append("where t1.AGENT_ID='08' and substr(t1.QC_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t1.AGENT_ID,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的待定量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的待定量失败.",e);
		}
		return null;
	}
	
	/**
	 * 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentListen(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t3.USER_ACC QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) LISTEN_NUM,sum(t2.LENS) LISTEN_SECONDS,");
			sql.append("sum(case when t1.SCORE=0 then 1 else 0 end) MANUAL_ZERO_NUM,");
			sql.append("sum(t1.SCORE) MANUAL_QC_SCORE ");
			sql.append("from "+schema+".CC_QC_RESULT t1 ");
			sql.append("left join "+schema+".V_CALL_RECORD t2 on t1.SERIAL_ID = t2.ID ");
			sql.append("left join "+schema+".V_CC_USER t3 on t1.INSPECTOR_ID=t3.USER_ID ");
			sql.append("where t2.QC_STATE='1' and substr(t1.QC_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t3.USER_ACC,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分失败.",e);
		}
		return null;
	}
	/**
	 * 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentListenM(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t3.USER_ACC QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) LISTEN_NUM,sum(t2.LENS) LISTEN_SECONDS,");
			sql.append("sum(case when t1.SCORE=0 then 1 else 0 end) MANUAL_ZERO_NUM,");
			sql.append("sum(t1.SCORE) MANUAL_QC_SCORE ");
			sql.append("from "+schema+".CC_QC_RESULT t1 ");
			sql.append("left join "+schema+".V_MEDIA_RECORD t2 on t1.SERIAL_ID = t2.ID ");
			sql.append("left join "+schema+".V_CC_USER t3 on t1.INSPECTOR_ID=t3.USER_ID ");
			sql.append("where t2.QC_STATE='1' and substr(t1.QC_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t3.USER_ACC,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的监听完成量、监听时长、质检总分、平均分失败.",e);
		}
		return null;
	}
	
	/**
	 * 按照渠道、方向统计质检员的申诉量(语音)
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentComplaint(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t4.USER_ACC QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) COMPLAINT_NUM from "+schema+".CC_QC_RESULT t1 left join "+schema+".V_CALL_RECORD t2 on t1.SERIAL_ID=t2.ID ");
			sql.append("left join "+schema+".CC_QC_RESULT_RECONSIDER t3 on t1.QC_RESULT_ID=t3.QC_RESULT_ID ");
			sql.append("left join "+schema+".V_CC_USER t4 on t1.INSPECTOR_ID=t4.USER_ID ");
			sql.append("where t3.APPEAL_COUNT>0 and substr(t3.OPERAT_DATE,1,10)='").append(stDate).append("' ");
			sql.append("group by t4.USER_ACC,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的申诉量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的申诉量失败.",e);
		}
		return null;
	}
	/**
	 * 按照渠道、方向统计质检员的申诉量(全媒体)
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentComplaintM(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t4.USER_ACC QC_ACC,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) COMPLAINT_NUM from "+schema+".CC_QC_RESULT t1 left join "+schema+".V_MEDIA_RECORD t2 on t1.SERIAL_ID=t2.ID ");
			sql.append("left join "+schema+".CC_QC_RESULT_RECONSIDER t3 on t1.QC_RESULT_ID=t3.QC_RESULT_ID ");
			sql.append("left join "+schema+".V_CC_USER t4 on t1.INSPECTOR_ID=t4.USER_ID ");
			sql.append("where t3.APPEAL_COUNT>0 and substr(t3.OPERAT_DATE,1,10)='").append(stDate).append("' ");
			sql.append("group by t4.USER_ACC,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的申诉量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的申诉量失败.",e);
		}
		return null;
	}
	
	/**
	 * 按照渠道、方向统计质检员的审核通过量（语音）
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentComPass(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t4.USER_ACC QC_ACC,t3.CHANNEL_ID,t3.DIRECTION,");
			sql.append("count(1) COMPLAINT_PASS_NUM ");
			sql.append("from "+schema+".CC_QC_RESULT_RECONSIDER t1 left join "+schema+".CC_QC_RESULT t2 on t1.QC_RESULT_ID=t2.QC_RESULT_ID ");
			sql.append("left join "+schema+".V_CALL_RECORD t3 on t3.ID=t2.SERIAL_ID ");
			sql.append("left join "+schema+".V_CC_USER t4 on t2.INSPECTOR_ID=t4.USER_ID ");
			sql.append("where t1.STATE='2' and substr(t1.OPERAT_DATE,1,10)='").append(stDate).append("' ");
			sql.append("group by t4.USER_ACC,t3.CHANNEL_ID,t3.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的审核通过量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的审核通过量失败.",e);
		}
		return null;
	}
	
	/**
	 * 按照渠道、方向统计质检员的审核通过量(全媒体)
	 * @param stDate
	 * @return 
	 */
	public List<EasyRow> stQcAgentComPassM(String stDate,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t4.USER_ACC QC_ACC,t3.CHANNEL_ID,t3.DIRECTION,");
			sql.append("count(1) COMPLAINT_PASS_NUM ");
			sql.append("from "+schema+".CC_QC_RESULT_RECONSIDER t1 left join "+schema+".CC_QC_RESULT t2 on t1.QC_RESULT_ID=t2.QC_RESULT_ID ");
			sql.append("left join "+schema+".V_MEDIA_RECORD t3 on t3.ID=t2.SERIAL_ID ");
			sql.append("left join "+schema+".V_CC_USER t4 on t2.INSPECTOR_ID=t4.USER_ID ");
			sql.append("where t1.STATE='2' and substr(t1.OPERAT_DATE,1,10)='").append(stDate).append("' ");
			sql.append("group by t4.USER_ACC,t3.CHANNEL_ID,t3.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的审核通过量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(),null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 按照渠道、方向统计质检员的审核通过量失败.",e);
		}
		return null;
	}
	
}
