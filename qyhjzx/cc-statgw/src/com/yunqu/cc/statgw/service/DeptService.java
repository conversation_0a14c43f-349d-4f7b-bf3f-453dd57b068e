package com.yunqu.cc.statgw.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.statgw.base.Constants;
import com.yunqu.cc.statgw.dao.DeptDao;


public class DeptService extends BaseService {
	private DeptDao deptDao = new DeptDao();
	private Map<String, DeptModel> depts = new HashMap<String, DeptModel>();
	private Map<String, String> stIds = new HashMap<String, String>();
	
	public DeptService(String schema,String endId) {
		List<DeptModel> list = DeptMgr.getDeptByLevel("",schema, 3, false,endId);
		for (DeptModel dept : list) {
			depts.put(dept.getDeptCode(), dept);
		}
	}

		
	/**
	 *  统计班组的工作量
	 * @param stDate
	 */
	public void stDeptQc(String stDate,String schema) {
		// 删除班组的质检明细汇总记录
		deptDao.delDeptRecord( "C_ST_DEPT_QC", stDate, Constants.ST_TYPE_DAY,schema,"01");
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 质检量、关键错误量
		List<EasyRow> list = deptDao.stDeptQc(stDate,schema);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围 ：" 		
				+ stDate + "，查询到质检量、关键错误量记录数:" + (list == null ? 0 :list.size()));	
		if(list!=null&&list.size()>0){
			for (EasyRow row : list) {
				String deptCode = row.getColumnValue("AGENT_DEPT");
				String sessionType = row.getColumnValue("SESSION_TYPE");
				String key = deptCode + sessionType;
				
				Map<String, Object> param = new HashMap<String, Object>();
				if (map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stDeptId = stIds.get(deptCode);
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = deptDao.getDeptRecord(deptCode, stDate, Constants.ST_TYPE_DAY,schema);
						stIds.put(deptCode, stDeptId);
					}			
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = IDGenerator.getDefaultNUMID();
						DeptModel dept = depts.get(deptCode);
						if(dept == null)
							continue;
						stRecord(sqlList, dept, stDeptId, stDate, Constants.ST_TYPE_DAY);
						stIds.put(deptCode, stDeptId);
					}
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_DEPT_ID", stDeptId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
					param.put("SESSION_TYPE", sessionType);
				}
				int manualQcNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_QC_NUM"));
				int manualZeroNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_ZERO_NUM"));
				double manualQcScore = CommonUtil.parseDouble(row.getColumnValue("MANUAL_QC_SCORE"));
				double manualQcAvgScore = calcAvg(manualQcNum - manualZeroNum, manualQcScore);					
				int keyErrorNum = CommonUtil.parseInt(row.getColumnValue("KEY_ERROR_NUM"));
				int serErrorNum = CommonUtil.parseInt(row.getColumnValue("SERVICE_ERROR_NUM"));
				int notSerErrorNum = keyErrorNum - serErrorNum;
				
				param.put("MANUAL_QC_NUM", manualQcNum);
				param.put("MANUAL_ZERO_NUM", manualZeroNum);
				param.put("MANUAL_QC_SCORE", manualQcScore);
				param.put("QC_AVG_SCORE", manualQcAvgScore);			
				param.put("KEY_ERROR_NUM", keyErrorNum);
				param.put("SERVICE_ERROR_NUM", serErrorNum);
				param.put("NOTSERVICE_ERROR_NUM", notSerErrorNum);
				map.put(key, param);
			}
		}
		
		
		// 满意量（语音）
		list = deptDao.stDeptSatisVoice(stDate,schema);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到满意量（语音）记录数:" + (list == null ? 0 :list.size()));	
		if(list!=null&&list.size()>0){
			for (EasyRow row : list) {
				String deptCode = row.getColumnValue("AGENT_DEPT");
				String sessionType = row.getColumnValue("SESSION_TYPE");
				String key = deptCode + sessionType;
				
				Map<String, Object> param = new HashMap<String, Object>();
				if (map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stDeptId = stIds.get(deptCode);
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = deptDao.getDeptRecord(deptCode, stDate, Constants.ST_TYPE_DAY,schema);
						stIds.put(deptCode, stDeptId);
					}	
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = IDGenerator.getDefaultNUMID();
						DeptModel dept = depts.get(deptCode);
						if(dept == null)
							continue;
						stRecord(sqlList, dept, stDeptId, stDate, Constants.ST_TYPE_DAY);
						stIds.put(deptCode, stDeptId);
					}
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_DEPT_ID", stDeptId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
					param.put("SESSION_TYPE", sessionType);
				}	
				int satisTotal = CommonUtil.parseInt(row.getColumnValue("SATIS_TOTAL"));
				int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
				double satisRate = calcRate(satisTotal, satisNum);
				
				param.put("SATISFACTION_RATIO", satisRate);
				map.put(key, param);
			}
		}
		
		
		/*// 满意量（全媒体）
		list = deptDao.stDeptSatisMedia(stDate);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到满意量（全媒体）记录数:" + (list == null ? 0 :list.size()));	
		for (EasyRow row : list) {
			String deptCode = row.getColumnValue("AGENT_DEPT");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String key = deptCode + sessionType;
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stDeptId = stIds.get(deptCode);
				if (StringUtils.isBlank(stDeptId)) {
					stDeptId = deptDao.getDeptRecord(deptCode, stDate, Constants.ST_TYPE_DAY);
					stIds.put(deptCode, stDeptId);
				}
				if (StringUtils.isBlank(stDeptId)) {
					stDeptId = IDGenerator.getDefaultNUMID();
					DeptModel dept = depts.get(deptCode);
					if(dept == null)
						continue;
					stRecord(sqlList, dept, stDeptId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(deptCode, stDeptId);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_DEPT_ID", stDeptId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
				param.put("SESSION_TYPE", sessionType);
			}	
			int satisTotal = CommonUtil.parseInt(row.getColumnValue("SATIS_TOTAL"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			double satisRate = calcRate(satisTotal, satisNum);
			
			param.put("SATISFACTION_RATIO", satisRate);
			map.put(key, param);
		}*/
		
		
		// 离职人数
		list = deptDao.stDeptDeparture(stDate,schema);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到离职人数记录数:" + (list == null ? 0 :list.size()));	
		if(list!=null&&list.size()>0){
			for (EasyRow row : list) {
				String deptCode = row.getColumnValue("DEPT_CODE");
				String sessionType = row.getColumnValue("SESSION_TYPE");
				String key = deptCode + sessionType;
				
				Map<String, Object> param = new HashMap<String, Object>();
				if (map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stDeptId = stIds.get(deptCode);
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = deptDao.getDeptRecord(deptCode, stDate, Constants.ST_TYPE_DAY,schema);
						stIds.put(deptCode, stDeptId);
					}
					if (StringUtils.isBlank(stDeptId)) {
						stDeptId = IDGenerator.getDefaultNUMID();
						DeptModel dept = depts.get(deptCode);
						if(dept == null)
							continue;
						stRecord(sqlList, dept, stDeptId, stDate, Constants.ST_TYPE_DAY);
						stIds.put(deptCode, stDeptId);
					}
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_DEPT_ID", stDeptId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
					param.put("SESSION_TYPE", sessionType);
				}				
				param.put("DEPARTURE_NUM", CommonUtil.parseInt(row.getColumnValue("DEPARTURE_NUM")));
				map.put(key, param);
			}
		}
		
		try {
			deptDao.map2Save(sqlList, map, ""+schema+".C_ST_DEPT_QC");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计班组的工作量 ,入库记录数:" + sqlList.size());
			deptDao.batchExecSql(deptDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			System.out.println("baocuo"+e.getMessage());
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 统计班组的工作量 ,入库时失败", e);
		}
	}
	
	
	/**
	 * 添加班组某天的汇总记录
	 * @param sqlList
	 * @param row
	 * @param stDate
	 * @param stType
	 * @return
	 */
	public void stRecord(ArrayList<String> sqlList, DeptModel dept, String stDeptId, String stDate, String stType) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("ID", stDeptId);
		param.put("DEPT_CODE", dept.getDeptCode());
		param.put("DEPT_NAME", dept.getDeptName());
		param.put("EP_CODE", dept.getDeptCode().substring(0, 3));
		param.put("AREA_CODE", dept.getpDeptCode());		
		param.put("ST_TYPE", stType);
		param.put("ST_DATE", stDate);
		param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		
		deptDao.mapToSave(sqlList, param, "C_ST_DEPT");
	}
	
}
