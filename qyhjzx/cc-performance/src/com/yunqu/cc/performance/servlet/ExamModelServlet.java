package com.yunqu.cc.performance.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.performance.base.AppBaseServlet;
import com.yunqu.cc.performance.base.CommonLogger;
import com.yunqu.cc.performance.base.Constants;
import com.yunqu.cc.performance.model.base.SysInfo;
import com.yunqu.cc.performance.utils.PerfManager;

/**
 * Servlet implementation class typeMaintenance
 */
@WebServlet("/servlet/examModel")
public class ExamModelServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 1L;
    private static final Logger logger = CommonLogger.logger;
	    
    private boolean valiModel(String id,String name) throws SQLException{
    	EasySQL sql = new EasySQL("select count(1) as TCOUNT  from "+getTableName("C_JX_MODEL"));
    	sql.append(name," where NAME = ?");
    	sql.append(id," and ID !=?");
    	sql.append(getEntId()," AND EP_CODE=?");
    	sql.append(getBusiOrderId()," and BUSI_ORDER_ID=?");
    	if (getQuery().queryForExist(sql.getSQL(), sql.getParams())) {
	        return false;
	     }
    	return true;
    }
    
    /**
	 * 添加考核模型
	 * 
	 * @return
	 * @throws SQLException 
	 */
	public EasyResult actionForAddExamModel() {
		JSONObject model = getJSONObject("model");
		EasyQuery query = getQuery();
		try {
			if(!valiModel("",model.getString("NAME")))
				return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "名称已存在！"));
			UserModel user = UserUtil.getUser(getRequest());			
			/***********模型信息**********/
			EasyRecord easyRecord = new EasyRecord(getTableName("C_JX_MODEL"),new String[] { "ID" });
			String modelId = RandomKit.randomStr();					
			model.put("ID",modelId );
			easyRecord.setColumns(model);
			easyRecord.set("CODE", "C_JX_MODLE_RESULT");
			easyRecord.set("IS_ENABLE", DictConstants.DICT_SY_YN_Y);
			easyRecord.set("OBJECT_TYPE", Constants.JX_OBJECT_TYPE_USER);
			easyRecord.set("OBJECT_NAME", PerfManager.getObjectName("C_JX_OBJECT", model.getString("OBJECT_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			easyRecord.set("CYCLE_NAME", PerfManager.getObjectName("C_JX_CYCLE", model.getString("CYCLE_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			easyRecord.set("CREATE_TIME", EasyDate.getCurrentDateString());
			easyRecord.set("CREATE_ACC", user.getUserAcc());
			easyRecord.set("CREATE_USER_NAME", user.getUserName());
			easyRecord.set("CREATE_DEPT", user.getDeptCode());
			easyRecord.set("EP_CODE", getEntId());
			easyRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			query.save(easyRecord);	// 保存信息									
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"新增考核模型异常："+e.getMessage(),e);			
			return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "新增失败"));
		}
		return EasyResult.ok("", BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "新增成功"));
	}
	
	/**
	 * 编辑修改
	 * 
	 * @return
	 */
	public EasyResult actionForUpdateExamModel() {
		String epCode = getEntId();
		JSONObject model = getJSONObject("model");
		EasyQuery query = getQuery();		
		try {
			if(!valiModel(model.getString("ID"),model.getString("NAME")))
				return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "名称已存在"));
			EasyRecord easyRecord = new EasyRecord(getTableName("C_JX_MODEL"),new String[] { "ID" });
			easyRecord.setColumns(model);
			easyRecord.set("CODE", "C_JX_MODLE_RESULT");
			easyRecord.set("OBJECT_NAME", PerfManager.getObjectName("C_JX_OBJECT", model.getString("OBJECT_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			easyRecord.set("CYCLE_NAME", PerfManager.getObjectName("C_JX_CYCLE", model.getString("CYCLE_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			easyRecord.set("EP_CODE", epCode);
			easyRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			query.update(easyRecord);	
		} catch (SQLException e) {
			logger.error(e.getMessage(),e);			
			return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "修改失败"));
		}

		return EasyResult.ok(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "修改成功"));

	}
	/**
	 * 删除当前记录
	 * 
	 * @return
	 */
	public EasyResult actionForDele() {
		EasyQuery query = getQuery();
		List<String> sql = new ArrayList<String>();	
		try {
			sql.add("delete from "+getTableName("C_JX_MODEL")+" where ID = '"+getJsonPara("ID")+"'");
			sql.add("delete from "+getTableName("C_JX_MODEL_RECORD")+" where MODEL_ID = '"+getJsonPara("ID")+"'");
			sql.add("delete from "+getTableName("C_CF_TABLE_DEF")+" "
					+ "where TABLE_NAME = '"+PerfManager.getObjectName("C_JX_MODEL", getJsonPara("ID"), new SysInfo(getDbName(),getEntId(),getBusiOrderId()))+"'");		
			query.executeBatch(sql);		
		} catch (SQLException ex) {
			logger.error(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "删除考核模型失败，原因：") + ex.getMessage(), ex);
		}
		return EasyResult.ok("", BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "删除成功"));
	}
	/**
	 * 移除模型指标
	 * @return
	 */
	 public EasyResult actionForRemoveMember() {
		    EasyQuery query = getQuery();
		    EasySQL sql = new EasySQL("DELETE FROM "+getTableName("C_JX_MODEL_RECORD"));
		    sql.append(getJsonPara("modelId")," where MODEL_ID = ?",false);
		    sql.append(getJsonPara("memberId")," and INDEX_ID = ?");
		    sql.append(getEntId()," and ENT_ID= ?");
		    sql.append(getBusiOrderId()," and BUSI_ORDER_ID=?");
		  //指标字段定义
			EasyRecord fRecord = new EasyRecord(getTableName("C_CF_TABLE_DEF"),new String[] { "TABLE_NAME","FIELD_EN" });
			fRecord.set("TABLE_NAME",PerfManager.getObjectName("C_JX_MODEL",getJsonPara("modelId"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));		
			fRecord.set("FIELD_EN",getJsonPara("indexCode"));	
			try {
		      query.begin();
		      query.execute(sql.getSQL(),sql.getParams());
		      query.deleteById(fRecord);
		      query.commit();		     
		      return EasyResult.ok("", BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "删除成功"));
		    } catch (SQLException e) {
		      e.printStackTrace();
		      logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败！发生异常："+e.getMessage(), e);
		      return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "删除失败！发生异常：") + e.getMessage());
		    }
		  }
	 
	     /**
		 * 添加模型指标
		 * 
		 * @return
		 */
	  public EasyResult actionForAddModelIndex() {
			JSONObject index = getJSONObject("index");
			EasyQuery query = getQuery();
			//考核模型明细
			EasyRecord easyRecord = new EasyRecord(getTableName("C_JX_MODEL_RECORD"),new String[] { "ID" });
			easyRecord.setColumns(index);
			easyRecord.set("ID", RandomKit.randomStr());
			easyRecord.set("ENT_ID", getEntId());
			easyRecord.set("BUSI_ORDER_ID",getBusiOrderId());
			//easyRecord.set("INDEX_NAME", PerfManager.getObjectName("C_JX_INDEX", index.getString("INDEX_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));	
			//指标字段定义
			EasyRecord fRecord = new EasyRecord(getTableName("C_CF_TABLE_DEF"),new String[] { "ID" });
			fRecord.set("ID", RandomKit.randomStr());
			fRecord.set("TABLE_NAME",PerfManager.getObjectName("C_JX_MODEL", index.getString("MODEL_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			fRecord.set("FIELD_EN", index.getString("INDEX_CODE"));
			fRecord.set("FIELD_CN", index.getString("INDEX_NAME"));			
			fRecord.set("BULIT_IN", DictConstants.DICT_SY_YN_Y);
			fRecord.set("EP_CODE", getEntId());
			fRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			fRecord.set("SORT_NUM", 99);
			try {
				// 保存信息
				query.begin();				
				query.save(easyRecord);
				query.save(fRecord);
				query.commit();
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+"新增模型指标异常："+e.getMessage(),e);					
				return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "新增失败"));
			}
			return EasyResult.ok("", BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "新增成功"));
		}
		/**
		 * 修改模型指标 
		 * @return
		 */
		public EasyResult actionForUpdateModelIndex() {
			JSONObject index = getJSONObject("index");		
			EasyQuery query = getQuery();
			EasyRecord easyRecord = new EasyRecord(getTableName("C_JX_MODEL_RECORD"),new String[] { "ID" });
			easyRecord.setColumns(index);
			//easyRecord.set("INDEX_NAME", PerfManager.getObjectName("C_JX_INDEX", index.getString("INDEX_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));		
			//指标字段定义
			EasyRecord fRecord = new EasyRecord(getTableName("C_CF_TABLE_DEF"),new String[] { "TABLE_NAME","FIELD_EN" });
			fRecord.set("TABLE_NAME",PerfManager.getObjectName("C_JX_MODEL", index.getString("MODEL_ID"),new SysInfo(getDbName(),getEntId(),getBusiOrderId())));
			fRecord.set("FIELD_EN", index.getString("INDEX_CODE"));
			fRecord.set("FIELD_CN", index.getString("INDEX_NAME"));			
			fRecord.set("BULIT_IN", DictConstants.DICT_SY_YN_Y);
			fRecord.set("EP_CODE", getEntId());
			fRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			fRecord.set("SORT_NUM", 99);
			try {
				query.begin();
				query.update(easyRecord);
				query.update(fRecord);
				query.commit();
			} catch (SQLException e) {
				try {
					logger.error(CommonUtil.getClassNameAndMethod(this)+"修改模型指标异常："+e.getMessage(),e);
					query.roolback();
				} catch (SQLException e1) {
					logger.error(e1.getMessage(),e1);
				}
				return EasyResult.fail(BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "修改失败"));
			}

			return EasyResult.ok(easyRecord, BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "修改成功"));

		}	
		public  JSONObject actionForGetExamObject() {
			String objectId = getJSONObject().getString("objectId");
			JSONObject jsonObject = new JSONObject();
			String sql = "SELECT TYPE FROM "+getTableName("C_JX_OBJECT")+" WHERE IS_ENABLE='Y' AND ID=? ";
			try {
				EasyRow row = getQuery().queryForRow(sql, new Object[]{objectId});
				if(row!=null){
					jsonObject.put("type", row.getColumnValue("TYPE"));
				}		
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}		
			return jsonObject;
		}
		 
	   public  JSONObject actionForGetExamCycle() {
		  	String cycleId = getJSONObject().getString("cycleId");
			JSONObject jsonObject = new JSONObject();
			String sql = "SELECT EXAMINE_TYPE,CYCLE_START,CYCLE_END FROM "+getTableName("C_JX_CYCLE")+" WHERE IS_ENABLE=? AND ID=? ";
			try {
				EasyRow row = getQuery().queryForRow(sql, new Object[]{"Y",cycleId});
				if(row!=null){
					jsonObject.put("examineType", row.getColumnValue("EXAMINE_TYPE"));
					jsonObject.put("cycleStart", row.getColumnValue("CYCLE_START"));
					jsonObject.put("cycleEnd", row.getColumnValue("CYCLE_END"));
				}		
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}		
			return jsonObject;
		}
	    public  JSONObject actionForGetModelIndex() {
		  	String indexId = getJSONObject().getString("indexId");
			JSONObject jsonObject = new JSONObject();
			String sql = "SELECT INDEX_RATIO,CODE FROM "+getTableName("C_JX_INDEX")+" WHERE IS_ENABLE=? AND ID=? ";
			try {
				EasyRow row = getQuery().queryForRow(sql, new Object[]{"Y",indexId});
				if(row!=null){
					jsonObject.put("indexCode", row.getColumnValue("CODE"));
					jsonObject.put("indexRatio", row.getColumnValue("INDEX_RATIO"));
				}		
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}		
			return jsonObject;
		}
	protected String getResId() {
		return null;
	}
	/**
	 * 编辑保存评分区间
	 * @return
	*/
	public EasyResult actionForSaveRule(){
		EasyQuery query = this.getQuery();
		
		//评分区间对象
		EasyRecord record = new EasyRecord(getTableName("C_JX_MODEL_SECTION"),new String[]{"ID"});
		record.set("MODEL_RECORD_ID", getJsonPara("glId"));
		record.set("MODEL_ID", getJsonPara("modelId"));
		record.set("INDEX_ID", getJsonPara("targetId"));
		record.set("CREATE_ACC", getUserAcc());
		record.set("CREATE_USER_NAME", getUserName());
		record.set("CREATE_DEPT",getUserDept());
		record.set("EP_CODE", getEntId());
		record.set("CREATE_TIME", EasyDate.getCurrentDateString());
		record.set("BUSI_ORDER_ID", getBusiOrderId());
		//评分区间明细记录：{"END_VAL_1L":"20","SCORE_2L":"70","END_VAL_2L":"30","RIGHT_SIGN_1L":"&lt;=","RIGHT_SIGN_2L":"&lt;=","BEGIN_VAL_1L":"1","BEGIN_VAL_2L":"21","LEFT_SIGN_2L":"&gt;","ID":["1L","2L"],"LEFT_SIGN_1L":"&gt;","SCORE_1L":"30"}
		JSONObject qtItem =  getJSONObject("qtItem");

		int itemSecNum = 0;
		//评分区间记录的id集合 ["1L","2L"]
		Object itemObj =qtItem.get("ID");
		String delItemIds = getJsonPara("delItemIds");
		try {
			query.begin();			
			//删除原有的评分区间
			if(StringUtils.isNotBlank(delItemIds)){
				String items[] =delItemIds.split(",");
				EasySQL sql = new EasySQL("delete from "+getTableName("C_JX_MODEL_SECTION")+" where ID in('-1'");
				for(int i=0;i<items.length;i++){
					sql.append(items[i],",?");
				}
				sql.append(")");
				query.execute(sql.getSQL(), sql.getParams());
			}	
			if(itemObj!=null){
				if(itemObj instanceof JSONArray){
					JSONArray itemArray = qtItem.getJSONArray("ID");
					for(int j=itemArray.size()-1;j>=0;j--){						
						saveItem(record, qtItem, itemArray.getString(j), query);
						itemSecNum++;
					}
				}else{
					saveItem(record, qtItem, qtItem.getString("ID"), query);	//保存item
					itemSecNum++;
				}	
			}		
			query.commit();
		} catch (SQLException e) {
			itemSecNum = -1;
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error(e.getMessage(),e);
			}
			
			logger.error(e.getMessage());
			return EasyResult.error(500,BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "保存评分区间失败"));
		}	
		return EasyResult.ok(itemSecNum, BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, "保存成功"));
	}
	
	/**
	 * 保存评分区间
	 * @param item   评分区间对象基本信息
	 * @param qtItem  评分区间配置信息
	 * @param itemId  评分区间id
	 * @param query
	 * @throws SQLException
	 */
	private void saveItem(EasyRecord item,JSONObject qtItem,String itemId,EasyQuery query) throws SQLException{
		
		item.set("LEFT_SIGN", qtItem.getString("LEFT_SIGN_"+itemId));
		item.set("RIGHT_SIGN", qtItem.getString("RIGHT_SIGN_"+itemId));
		item.set("BEGIN_VAL", qtItem.getString("BEGIN_VAL_"+itemId));
		item.set("END_VAL", qtItem.getString("END_VAL_"+itemId));
		item.set("SCORE", qtItem.getString("SCORE_"+itemId));
		if(itemId.indexOf('L')>-1){
			item.set("ID", RandomKit.randomStr());
			query.save(item);
		}else{
			item.set("ID", itemId);
			query.update(item);
		}
	}
	
}
