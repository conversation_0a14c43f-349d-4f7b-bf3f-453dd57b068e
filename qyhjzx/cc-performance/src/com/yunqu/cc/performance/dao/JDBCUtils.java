package com.yunqu.cc.performance.dao;
 
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import com.yunqu.cc.performance.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;

import com.yunqu.cc.performance.base.Constants;
import com.yunqu.cc.performance.base.QueryFactory;
 
public class JDBCUtils {
 
	private static EasyQuery query = QueryFactory.getWriteQuery();
	private static final Logger logger = CommonLogger.logger;
		
	/**
	 * 获取数据库连接
	 * @return
	 */
	public static Connection getConnection(){		
		try {
			return query.getConnection();
		} catch (SQLException e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 释放数据库连接资源
	 * @param conn
	 * @param st
	 * @param rs
	 */
	public static void release(Connection conn,Statement st,ResultSet rs){
		/*if (rs!=null) {*/
			try {
				rs.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}finally{
				rs = null;
			}
		/*}*/
		
		if (st!=null) {
			try {
				st.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}finally{
				st = null;
			}
		}
		
		if (conn!=null) {
			try {
				conn.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}finally{
				conn = null;
			}
		}
	}
}
