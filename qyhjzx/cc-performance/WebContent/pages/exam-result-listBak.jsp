<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="考核结果查询"></title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> <span i18n-content="考核结果查询"></span></h5>
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon" i18n-content="考核对象"></span>	
									  <input type="text" name="OBJECT_NAME" class="form-control input-sm" style="width:140px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon" i18n-content="考核周期"></span>	
									  <select name="CYCLE_ID" class="form-control input-sm" data-mars="dict.getExamInfoList('C_JX_CYCLE')">
									      <option value="" i18n-content="请选择"></option>
									  </select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="examResult.loadData()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="搜索"></span></button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="examResult.resultList">
                             <thead>
	                         	 <tr>
	                         	      <th class="text-c" i18n-content="时间"></th>
								      <th class="text-c" i18n-content="考核周期"></th>
								      <th class="text-c" i18n-content="考核对象"></th>
								      <th class="text-c" i18n-content="考核成员"></th>
								      <th class="text-c" i18n-content="考核总分"></th>
								      <th class="text-c" i18n-content="操作"></th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>      
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:SCORE_TIME}}</td>
											<td>{{:CYCLE_NAME}}</td>
											<td>{{:OBJECT_NAME}}</td>
											<td>{{:NAME}}</td>
											<td>{{:SCORE}}</td>
											<td><a href="javascript:examResult.examResultDetail('{{:CODE}}','{{:ID}}')" i18n-content="考核明细"></a></td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("examResult");
		examResult.examResultDetail = function(code,id){
			popup.layerShow({type:1,title:getI18nValue('考核明细'),offset:'20px',area:['560px','420px']},"${ctxPath}/pages/exam-result-detail.jsp",{code:code,id:id});
		}
		examResult.loadData = function(){
			searchData("#searchForm","#tableHead");
		}
		$(function(){
			$("#searchForm").render({complete:function(){
				execI18n();
			}});
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>