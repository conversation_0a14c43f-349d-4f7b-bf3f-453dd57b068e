<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="考核手动评分"></title>
	<style type="text/css">
		a:link {
			color: #00adff;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline shadow" id="searchForm" onsubmit="return false"
		 >
		<input name="isAudit" type="hidden" value="Y">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5><span class="glyphicon"></span><span i18n-content="坐席绩效审核"></span></h5>
				</div>
				<hr style="margin: 3px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="坐席账号/名称"></span>
						<input type="text" name="USER_ACC" class="form-control input-sm" style="width:120px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="模型名称"></span>
						<input type="text" name="MODEL_NAME" class="form-control input-sm" style="width:120px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="指标名称"></span>
						<input type="text" name="INDEX_NAME" class="form-control input-sm" style="width:120px">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="申请时间"></span>
						<input type="text" class="form-control input-sm" id=pubCreateDateStar name="START_TIME" style="width:120px"
									data-mars-reload="false" data-mars-top='true' autocomplete="off"> 
								<span class="input-group-addon" i18n-content="至"></span> 
								<input type="text" class="form-control input-sm" id="pubCreateDateEnd" name="END_TIME"  style="width:120px" 
									data-mars-reload="false" data-mars-top='true' autocomplete="off"> 
					</div>

					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="审核状态"></span>
						<select id="status" name="AUDIT_STATUS" onchange="person.getData('1')"
							class="form-control input-sm" style="width:93px;">
							<option value="0" i18n-content="未审核"></option>
							<option value="1" i18n-content="已审核"></option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="person.getData('1')"><span
								class="glyphicon glyphicon-search"></span> <span i18n-content="搜索"></span></button>
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="person.batchAudit();"><span
								class="glyphicon glyphicon-check"></span><span i18n-content="批量审核"></span></button>
					</div>
				</div>
				<div class="form-group">

				</div>
			</div>
			<div class="ibox-content">
				<table id="dataList"></table>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("person");
		layui.use('laydate', function(){
 			  var laydate = layui.laydate;
 			  laydate.render({ elem: '#pubCreateDateStar' ,type: 'date'});
 			  laydate.render({ elem: '#pubCreateDateEnd' ,type: 'date'});
 		}) 
		$(function () {
			$("#searchForm").render();
			person.initData();
		});
		person.initData = function () {
			$("#searchForm").initTable({
				mars: 'personAudit.auditList',
				id: 'dataList',
				height: 'full-180',
				limit: '15',
				limits: [15, 25, 50, 100, 200],
				cols: [[
					{
						field: 'ID',
						title: getI18nValue('选择'),
						type: 'checkbox',

					}, {
						field: 'ID',
						align: 'center',
						title: getI18nValue('操作'),
						templet: function (row) {
							var temp = '<a  href="javascript:void(0)"  onclick="	person.Pass(\'' + row.ID + '\')"> 审核</a> ';
							return temp;
						}
					},
					{
						field: 'CREATE_USER_ACC',
						align: 'center',
						title: getI18nValue('坐席账号')
					},
					{
						field: 'CREATE_USER_NAME',
						align: 'center',
						title: getI18nValue('坐席名称')
					},
					{
						field: 'BEGIN_TIME',
						align: 'center',
						title: getI18nValue('周期开始时间')
					},
					{
						field: 'END_TIME',
						align: 'center',
						title: getI18nValue('周期结束时间')
					},
					{
						field: 'STATUS',
						align: 'center',
						title: getI18nValue('审核状态'),
						templet: function (row) {
							return getDictTextByCode("JX_AUDIT_RESULT", row.STATUS);
						}
					},
					{
						field: 'MODEL_NAME',
						align: 'center',
						title: getI18nValue('模型名称')
					},
					{
						field: 'INDEX_NAME',
						align: 'center',
						title: getI18nValue('指标名称')
					},
					{
						field: 'OLD_SCORE',
						align: 'center',
						title: getI18nValue('原始分数')
					},
					{
						field: 'NEW_SCORE',
						align: 'center',
						title: getI18nValue('修改分数')
					},
					{
						field: 'CREATE_TIME',
						align: 'center',
						title: getI18nValue('申请时间')
					},
					{
						field: 'REASON',
						align: 'center',
						title: getI18nValue('申请原因')
					},
					{
						field: 'BAKUP',
						align: 'center',
						title: getI18nValue('备注')
					}]]
			});
		}

		//搜索
		person.getData = function (flag) {
			if (flag == '1') {
				$("#searchForm").queryData({
					id: 'dataList',
					page: { curr: 1 }
				});
			} else {
				$("#searchForm").queryData({
					id: 'dataList'
				});
			}
		};
		person.Pass = function (id) {
			var status = $('#status').val();
			popup.layerShow({
				type: 2,
				title: getI18nValue('审核界面'),
				area: ['570px', "440px"]
			},
				"${ctxPath}/pages/personAudit-Pass.jsp",
				{
					id: id,
					status: status
				});
		}
		//批量审批
		person.batchAudit = function () {
			var status = $('#status').val();
			if ('1' == status) {
				layer.alert(getI18nValue('请选择未审核数据'), { icon: 7, title: '提示', offset: '40px' });
				return;
			}
			var checkStatus = table.checkStatus('dataList');
			var checkedRecord = checkStatus.data;
			if (checkedRecord.length < 1) {
				layer.alert(getI18nValue('请选择审核记录'), { icon: 7, title: '提示', offset: '40px' });
			} else {
				var arr = new Array();
				var data = {};
				for (var i = 0; i < checkedRecord.length; i++) {
					arr.push(checkedRecord[i].ID);
				}
				var data = {
					ids: JSON.stringify(arr)
				};
				popup.layerShow({
					type: 2,
					title: getI18nValue('批量审核界面'),
					area: ['520px', '260px']
				},
					"${ctxPath}/pages/personAudit-Pass.jsp",
					data);
			}
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp" %>