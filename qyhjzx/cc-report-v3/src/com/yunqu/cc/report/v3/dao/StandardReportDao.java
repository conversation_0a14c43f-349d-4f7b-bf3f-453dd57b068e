package com.yunqu.cc.report.v3.dao;

import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.report.base.AppDaoContext;
import com.yunqu.cc.report.base.CommonLogger;
import com.yunqu.cc.report.utils.ReportUtils;
import com.yunqu.cc.report.v3.sql.StandardReportSQL;
import com.yunqu.cc.report.v3.vo.CommonCallRowMapper;

@WebObject(name="standardReport")
public class StandardReportDao extends AppDaoContext {

	private StandardReportSQL standardReportSQL = new StandardReportSQL();


	protected Logger logger =CommonLogger.logger;

	/**
	 * 每日运营情况
	 *@return
	 */
	@InfAuthCheck(resId = {"cc-report-call-wholeOperate","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="wholeOperateList",type=Types.LIST)
	public JSONObject wholeOperateList(){

//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("dateId");
//		sumColumns.add("onlineAgentCount");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentCallTime");
//		sumColumns.add("callInAvgAgentAnswerTime");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutCallTime");
//		sumColumns.add("callOutAvgAnswerTime");

		JSONObject result = new JSONObject();
		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";

			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于") + timeLimited +getI18nValue("天"));
			}

			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.wholeOperateSql(getDbName(),getEntId(),getBusiOrderId(),this.param);

			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());

			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));


		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);

		}

		return result;
	}
	/**
	 * 语音满意度统计
	 *@return
	 */
	@InfAuthCheck(resId = {"cc-report-call-recordSatisfStat","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="recordSatisfStat",type=Types.LIST)
	public JSONObject recordSatisfStat(){
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("agentName");
//		sumColumns.add("dateId");
//		sumColumns.add("caller");
//		sumColumns.add("groupName");
//		sumColumns.add("callSuccCount");
//		sumColumns.add("callAgentReleaseCount");
//		sumColumns.add("callSatisfCount");
//		sumColumns.add("callSatisf1Count");
//		sumColumns.add("callSatisf2Count");
//		sumColumns.add("callSatisf3Count");
//		sumColumns.add("callSatisf4Count");
//		sumColumns.add("callSatisf5Count");
//		sumColumns.add("callNoSatisfCount");
		JSONObject result = new JSONObject();
		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}


			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.statisfyStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);

			logger.info(sql.getSQL());
			logger.info(sql.getParamsList().toString());

			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));

		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}



		return result;
	}

	/**
	 * 获取满意度列表
	 *@return
	 */
	@WebControl(name="satisfList",type=Types.LIST)
	public JSONObject satisfList(){
		EasySQL dictSql = standardReportSQL.getEnabledDict(getDbName(), getBusiOrderId(), getEntId(), "VOICE_SATISF");
		JSONObject result = this.queryForList(dictSql.getSQL(),dictSql.getParams(), new JSONMapperImpl());
		return result;
	}

	/**
	 * 获取满意度列表 0> CODE <6
	 *@return
	 */
	@WebControl(name="schedulingList",type=Types.LIST)
	public JSONObject schedulingList(){
		EasySQL sql = new EasySQL("SELECT t1.ID,t1.NAME,t1.CODE FROM "+getTableName("C_CF_DICT")+" t1 ");
		sql.append(" left join "+getTableName("C_CF_DICTGROUP")+" t2 ");
		sql.append(" on t1.DICT_GROUP_ID = t2.ID  ");
		sql.append(" and t1.ENT_ID = t2.EP_CODE  ");
		sql.append(" and t1.BUSI_ORDER_ID = t2.BUSI_ORDER_ID ");
		sql.append(this.getBusiOrderId()," where t1.BUSI_ORDER_ID = ? ",false );
		sql.append(this.getEntId(), " AND t1.ENT_ID = ? ",false);
		sql.append("VOICE_SATISF"," and t2.CODE = ? ");
		sql.append("Y"," and t1.ENABLE_STATUS = ? ");
		sql.append("0"," AND t1.CODE > ? ");
		sql.append("6"," AND t1.CODE < ? ");
		JSONObject result = this.queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
		return result;
	}

	/**
	 * 整体话务分析报表
	 * @return
	 */

	@InfAuthCheck(resId = {"cc-report-call-entCallStat","cc-mobile-v-report-1"}, msg = "您无权访问!")
	@WebControl(name="entCallStatList", type=Types.LIST)
	public JSONObject entCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("dateId");
//		sumColumns.add("calcallInAgentCountler");
//		sumColumns.add("callInCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAlteringCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");

		JSONObject result = new JSONObject();

		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";

			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}

			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.entCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));


		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}



		return result;
	}

	/**
	 * 坐席话务分析
	 * xml中已注释了请求改接口的页面，越权注解中的第一个id为注释中的页面id，可能会改动
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report_call_agentCallStat","cc-mobile-v-report-3","cc-report-call-zxhw"}, msg = "您无权访问!")
	@WebControl(name="agentCallStatList", type=Types.LIST)
	public JSONObject agentCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("groupName");
//		sumColumns.add("agentName");
//		sumColumns.add("dateId");
//		sumColumns.add("callInCallCount");
//		sumColumns.add("callInAlteringCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAnswer20Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer15Count");
//		sumColumns.add("betweenCallInAnswer20Count");
		JSONObject result = new JSONObject();
		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}

			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.agentCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}



		return result;

	}

	/**
	 * 技能组话务量统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-groupCallStat-1","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="groupCallStatList", type=Types.LIST)
	public JSONObject groupCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("dateId");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAlteringCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAnswer20Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer5Count");
//		sumColumns.add("betweenCallInAnswer10Count");
//		sumColumns.add("betweenCallInAnswer15Count");
//		sumColumns.add("betweenCallInAnswer20Count");

		JSONObject result = new JSONObject();

		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}

			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.groupCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));

		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}




		return result;
	}


	/**
	 * 部门话务量统计报表
	 * @return
	 */
	@WebControl(name="deptCallStatList", type=Types.LIST)
	public JSONObject deptCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("groupName");
//		sumColumns.add("dateId");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInCallCount");
//		sumColumns.add("callInAgentNoAnswerCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAnswer20Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer5Count");
//		sumColumns.add("betweenCallInAnswer10Count");
//		sumColumns.add("betweenCallInAnswer15Count");
//		sumColumns.add("betweenCallInAnswer20Count");
		JSONObject result = new JSONObject();

		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}


			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.deptCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			 result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));


		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}

		return result;
	}


	/**
	 * 部门话务量统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-deptCallStat","cc-mobile-v-report-2"}, msg = "您无权访问!")
	@WebControl(name="deptCallStat", type=Types.LIST)
	public JSONObject deptCallStat() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("groupName");
//		sumColumns.add("dateId");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInCallCount");
//		sumColumns.add("callInAgentNoAnswerCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAnswer20Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer5Count");
//		sumColumns.add("betweenCallInAnswer10Count");
//		sumColumns.add("betweenCallInAnswer15Count");
//		sumColumns.add("betweenCallInAnswer20Count");
		JSONObject result = new JSONObject();
		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";

			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}

			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.deptCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}
		return result;
	}


	/**
	 * 地区话务量统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-dqhw","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="areaCallStatList", type=Types.LIST)
	public JSONObject areaCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("areaName");
//		sumColumns.add("dateId");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAlteringCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer5Count");
//		sumColumns.add("betweenCallInAnswer10Count");
//		sumColumns.add("betweenCallInAnswer15Count");

		JSONObject result = new JSONObject();
		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}


			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.areaCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);

			 result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));

		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}


		return result;
	}


	/**
	 * 日话务统计报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-dayCallStat","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="dayCallStatList", type=Types.LIST)
	public JSONObject dayCallStatList() {
//		HashSet<String> sumColumns = new HashSet<String>();
//		sumColumns.add("areaName");
//		sumColumns.add("dateId");
//		sumColumns.add("callInAgentCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAlteringCount");
//		sumColumns.add("callInAgentAnswerCount");
//		sumColumns.add("callInAgentAnswer5sCount");
//		sumColumns.add("callInAgentAnswer10sCount");
//		sumColumns.add("callInAgentAnswer15sCount");
//		sumColumns.add("callInAgentAnswer20sCount");
//		sumColumns.add("callInAvgAnswerTime");
//		sumColumns.add("callInAvgAnswerCallCount");
//		sumColumns.add("callInQueueCount");
//		sumColumns.add("callInAvgQueueTime");
//		sumColumns.add("callInAvgAlteringTime");
//		sumColumns.add("callInAvgAcwTime");
//		sumColumns.add("callOutAgentCount");
//		sumColumns.add("callOutCallCount");
//		sumColumns.add("callOutAnswerCount");
//		sumColumns.add("callOutAnswer5Count");
//		sumColumns.add("callOutAnswer10Count");
//		sumColumns.add("callOutAnswer15Count");
//		sumColumns.add("callOutAvgAnswerTime");
//		sumColumns.add("callOutAvgAnswerCallCount");
//		sumColumns.add("callOutAvgAlteringTime");
//		sumColumns.add("callOutAvgAcwTime");
//		sumColumns.add("betweenCallInCount");
//		sumColumns.add("betweenCallOutCount");
//		sumColumns.add("betweenCallInAnswer5Count");
//		sumColumns.add("betweenCallInAnswer10Count");
//		sumColumns.add("betweenCallInAnswer15Count");

		JSONObject result = new JSONObject();
		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";
			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());
			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}
			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.dayCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			 result = queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this) + "日话务分析：sql:"+sql.getSQL()+",param:"+JSONObject.toJSONString(sql.getParams()) );
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}
		return result;
	}

	/**
	 * 坐席服务水平报表
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-serviceLevelReport","cc-mobile"}, msg = "您无权访问!")

	@WebControl(name="serviceLevelList", type=Types.LIST)
	public JSONObject serviceLevelList() {
		JSONObject result = new JSONObject();
		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}


			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.serviceLevelSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			 result = queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this) + "坐席服务水平报表：sql:"+sql.getSQL()+",param:"+JSONObject.toJSONString(sql.getParams()) );
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));

		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}


		return result;
	}

    /**
	 * 分时话务分析（新）
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-call-halfHourCallStat","cc-mobile"}, msg = "您无权访问!")
	@WebControl(name="halfHourCallList", type=Types.LIST)
	public JSONObject halfHourCallList() {
		JSONObject result = new JSONObject();

		try {


			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";


			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			}




			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.halfHourCallSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			 result = queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));

		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}



		return result;
	}

	/**
	 * 分时话务分析
	 * @return
	 */
	@WebControl(name="hourCallStatList", type=Types.LIST)
	@InfAuthCheck(resId = {"cc-report-call-hourCallStat","cc-mobile"}, msg = "您无权访问!")
	public JSONObject hourCallStatList() {
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
		EasySQL sql = standardReportSQL.hourCallSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
		JSONObject result = queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this) + "分时话务分析：sql:"+sql.getSQL()+",param:"+JSONObject.toJSONString(sql.getParams()) );
		//添加一个数据更新时间
		result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		return result;
	}



	/**
	 * 坐席话务考核
	 * @return
	 */
	@WebControl(name="agentCallAssess", type=Types.LIST)
	public JSONObject agentCallAssess() {
		JSONObject result = new JSONObject();
		try {

			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";

			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());

			//if (timeLimited!=-1) {
			//	return EasyResult.fail(getI18nValue("查询时间不能为空或大于")+timeLimited+getI18nValue("天"));
			//}
			DBTypes types = this.getQuery().getTypes();
			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
			EasySQL sql = standardReportSQL.agentCallAssessSql(getDbName(),getEntId(),getBusiOrderId(),this.param,types);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}



		return result;

	}

	/**
	 * 队列话务分析报表
	 *@return
	 */
	@WebControl(name="queueCallList",type=Types.LIST)
	@InfAuthCheck(resId = {"cc-call-report-queueList","cc-mobile"}, msg = "您无权访问!")
	public JSONObject queueCallList(){

		JSONObject result = new JSONObject();
		try {
			String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";
			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());
			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于") + timeLimited +getI18nValue("天"));
			}
			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.queueCallSql(getDbName(),getEntId(),getBusiOrderId(),this.param);
			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}
		return result;
	}

	/**
	 * 接入号话务分析报表
	 *@return
	 */
	@WebControl(name="calledCallList",type=Types.LIST)
	@InfAuthCheck(resId = {"cc-call-report-calledList","cc-mobile"}, msg = "您无权访问!")
	public JSONObject calledCallList(){
		String stType = StringUtils.isNotBlank(param.getString("stType"))?param.getString("stType"):"01";
		JSONObject result = new JSONObject();
		try {
			int timeLimited = ReportUtils.getTimeLimited(param.getString("startDate"), param.getString("endDate"), stType, getDbName(), getEntId(), getBusiOrderId(), getAppName());
			if (timeLimited!=-1) {
				return EasyResult.fail(getI18nValue("查询时间不能为空或大于") + timeLimited +getI18nValue("天"));
			}
			Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALLIN_STAT");
			EasySQL sql = standardReportSQL.calledCallSql(getDbName(),getEntId(),getBusiOrderId(),this.param);

			result = this.queryForSumPageList(sql.getSQL(), sql.getParams(),new CommonCallRowMapper());
			//添加一个数据更新时间
			result.put("updateTime", ycstatTableInfo.get("UPDATE_TIME"));
		} catch (Exception e) {
			logger.error("查询异常:"+e.getMessage(),e);
		}
		return result;
	}


}
