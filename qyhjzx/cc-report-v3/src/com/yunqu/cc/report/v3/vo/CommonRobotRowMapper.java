package com.yunqu.cc.report.v3.vo;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.util.BaseI18nUtil;
import com.yunqu.cc.report.base.Constants;
import com.yunqu.cc.report.log.ReportLogger;

public class CommonRobotRowMapper implements EasyRowMapper<CommonRobotModel> {
	
	private CommonRobotModel sumModel = new CommonRobotModel();

	private List<Map<String, String>> schedulingList;

	@SuppressWarnings("unchecked")
	public CommonRobotModel mapRow(ResultSet rs, int rownum) {
		CommonRobotModel channel = new CommonRobotModel();

		Set<String> columns = getColomnInfo(rs,rownum);
		try {
			if (columns.contains("DATE_ID")){
				channel.setDateId(rs.getString("DATE_ID"));
			}
				
			if (columns.contains("CHANNEL_NAME")){
				channel.setChannelName(rs.getString("CHANNEL_NAME"));
			}
				
			if (columns.contains("IN_NUM")){
				channel.setInCount(rs.getInt("IN_NUM"));
				sumModel.setInCount(sumModel.getInCount()+rs.getInt("IN_NUM"));
			}
				
			if (columns.contains("TOTAL")){
				channel.setConnectCount(rs.getInt("TOTAL"));
				sumModel.setConnectCount(sumModel.getConnectCount()+rs.getInt("TOTAL"));
			}
				
			if (columns.contains("RECORD_NUM")){
				channel.setRecordCount(rs.getInt("RECORD_NUM"));
				sumModel.setRecordCount(sumModel.getRecordCount()+rs.getInt("RECORD_NUM"));
			}
				
			if (columns.contains("IN_ROBOT_NUM")){
				channel.setInRobotCount(rs.getInt("IN_ROBOT_NUM"));
				sumModel.setInRobotCount(sumModel.getInRobotCount()+rs.getInt("IN_ROBOT_NUM"));
			}
			
			//3.5#20241220-1 新增机器人有效会话量统计字段
			if (columns.contains("IN_ROBOT_VALID_NUM")){
				channel.setInRobotValidNum(rs.getInt("IN_ROBOT_VALID_NUM"));
				sumModel.setInRobotValidNum(sumModel.getInRobotValidNum()+rs.getInt("IN_ROBOT_VALID_NUM"));
			}
				
			if (columns.contains("ROBOT_DONE_NUM")){
				channel.setRobotDoneCount(rs.getInt("ROBOT_DONE_NUM"));
				sumModel.setRobotDoneCount(sumModel.getRobotDoneCount()+rs.getInt("ROBOT_DONE_NUM"));
			}
			
			//3.5#20241220-1 新增机器人有效会话解决量统计字段
			if (columns.contains("ROBOT_VALID_DONE_NUM")){
				channel.setRobotValidDoneNum(rs.getInt("ROBOT_VALID_DONE_NUM"));
				sumModel.setRobotValidDoneNum(sumModel.getRobotValidDoneNum()+rs.getInt("ROBOT_VALID_DONE_NUM"));
			}
				
			if (columns.contains("ROBOT_SERVICE_LEN")){
				channel.setRobotServiceTime(rs.getInt("ROBOT_SERVICE_LEN"));
				sumModel.setRobotServiceTime(sumModel.getRobotServiceTime()+rs.getInt("ROBOT_SERVICE_LEN"));
			}
				
			if (columns.contains("REQ_AGENT_NUM")){
				channel.setReqAgentCount(rs.getInt("REQ_AGENT_NUM"));
				sumModel.setReqAgentCount(sumModel.getReqAgentCount()+rs.getInt("REQ_AGENT_NUM"));
			}
				
			if (columns.contains("ROBOT_ANS_EVAL_NUM")){
				channel.setRobotEvalCount(rs.getInt("ROBOT_ANS_EVAL_NUM"));
				sumModel.setRobotEvalCount(sumModel.getRobotEvalCount()+rs.getInt("ROBOT_ANS_EVAL_NUM"));
			}
				
			if (columns.contains("ROBOT_ANS_UP_TIMES")){
				channel.setRobotEvalSatify(rs.getInt("ROBOT_ANS_UP_TIMES"));
				sumModel.setRobotEvalSatify(sumModel.getRobotEvalSatify()+rs.getInt("ROBOT_ANS_UP_TIMES"));
			}
				
			if (columns.contains("ROBOT_ANS_DOWN_TIMES")){
				channel.setRobotEvalUnSatify(rs.getInt("ROBOT_ANS_DOWN_TIMES"));
				sumModel.setRobotEvalUnSatify(sumModel.getRobotEvalUnSatify()+rs.getInt("ROBOT_ANS_DOWN_TIMES"));
			}
			
			//未邀评的机器人会话
			if (columns.contains("ROBOT_SESSION_NOT_INVITED_EVAL")){
				channel.setRobotSessionNotInvitedEval(rs.getInt("ROBOT_SESSION_NOT_INVITED_EVAL"));
				sumModel.setRobotSessionNotInvitedEval(sumModel.getRobotSessionNotInvitedEval()+rs.getInt("ROBOT_SESSION_NOT_INVITED_EVAL"));
			}
			//未评价的机器人会话
			if (columns.contains("ROBOT_SESSION_NOT_EVAL")){
				channel.setRobotSessionNotEval(rs.getInt("ROBOT_SESSION_NOT_EVAL"));
				sumModel.setRobotSessionNotEval(sumModel.getRobotSessionNotEval()+rs.getInt("ROBOT_SESSION_NOT_EVAL"));
			}
			//评价为满意的会话
			if (columns.contains("ROBOT_SESSION_EVAL_SATISF")){
				channel.setRobotSessionEvalSatify(rs.getInt("ROBOT_SESSION_EVAL_SATISF"));
				sumModel.setRobotSessionEvalSatify(sumModel.getRobotSessionEvalSatify()+rs.getInt("ROBOT_SESSION_EVAL_SATISF"));
			}
			//评价为不满意的会话
			if (columns.contains("ROBOT_SESSION_EVAL_NOT_SATISF")){
				channel.setRobotSessionEvalNotSatify(rs.getInt("ROBOT_SESSION_EVAL_NOT_SATISF"));
				sumModel.setRobotSessionEvalNotSatify(sumModel.getRobotSessionEvalNotSatify()+rs.getInt("ROBOT_SESSION_EVAL_NOT_SATISF"));
			}
				
		} catch (SQLException ex) {
			ReportLogger.getLogger().error(ex, ex);
		}
		return channel;
	}

	public CommonRobotModel getSumModel(HttpServletRequest request) {
		sumModel.setDateId(BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, "汇总"));
		sumModel.setChannelName(BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, "汇总"));
		return sumModel;
	}



	public void setSumModel(CommonRobotModel sumModel) {
		this.sumModel = sumModel;
	}


	
	private static Set<String> getColomnInfo(ResultSet rs, int convertField) {
		Set<String> columns = new HashSet<String>();
		try {
			java.sql.ResultSetMetaData meta = rs.getMetaData();
			// 取得列数
			int columnCount = meta.getColumnCount();
			// 保存列相关的信息
			for (int i = 0; i < columnCount; ++i) {

				if (convertField == 2) {
					columns.add(meta.getColumnLabel(i + 1).toUpperCase());
				} else if (convertField == 1) {
					columns.add(meta.getColumnLabel(i + 1).toLowerCase());
				} else if (convertField == 3) {
					columns.add(StringUtils.toCamelCase(meta.getColumnLabel(i + 1)));
				} else {
					columns.add(meta.getColumnLabel(i + 1));
				}
			}
			return columns;
		} catch (Exception ex) {
			ReportLogger.getLogger().error("EasyRowMapperImpl.mapRow() exception , cause:" + ex.getMessage());
		}
		return columns;
	}

	public List<Map<String, String>> getSchedulingList() {
		return schedulingList;
	}

	public void setSchedulingList(List<Map<String, String>> schedulingList) {
		this.schedulingList = schedulingList;
	}

}
