<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>

<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title i18n-content="客户未接数据析"></title>
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/cc-report/static/css/admin.css" media="all">
    <style>
    	html {padding-bottom: 0px !important;}
        .numberInfoSubTitle {
            color: rgba(0, 0, 0, .45);
            font-size: 14px;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;
        }

        .numberInfoValue {
            color: rgba(0, 0, 0, .85);
            font-size: 24px;
            margin-top: 10px;
            height: 32px;
            line-height: 32px;
        }

        .numberInfoSuffix {
            color: rgba(0, 0, 0, .65);
            font-size: 16px;
            font-style: normal;
            margin-left: 4px;
            line-height: 32px;
        }
        .layui-form-label{
        	width: 130px;
        }
        .layui-tab-item{
        	background-color: #f2f2f2;
        	outline: 1px solid #e6e6e6;
        	padding: 5px;
        }
        .car_item{
        	background: #FFFFFF;
        	margin-top: 5px;
        }
        .itemName{
        	width:80%;
        	height: 25px;
        	margin-top:5px;
        	line-height: 25px;
        	cursor: context-menu;
        	overflow: hidden;
    		white-space: nowrap;
        }
        .itemNameIcon{
        	display: inline-block;
    		width: 40px;
    		height: 25px;
    		margin-left: 5px;
    		padding-left: 5px;
    		border-radius: 3px;
    		background: #0067b3;
    		color: #FFFFFF;
        }
        .itemNameText{
        	width: calc(100% - 60px);
        	margin-left: 5px;
        	line-height: 25px;
        }
        .layui-layer-tips .layui-layer-content{
        	font-size: 14px!important;
        }
        .score-grade-h{
        	display: none;
        }
        div.layui-layer-tips i.layui-layer-TipsT {
   			 bottom: -8px;
		}
        div.layui-layer-tips i.layui-layer-TipsB,
        div.layui-layer-tips i.layui-layer-TipsT {
    		left: 5px;
    		border-right-style: solid;
    		border-right-color: rgba(50, 50, 50, 0.7);
    		border-top-style: none;
		}
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.clearfix:after {
			content: '';
			height: 0;
			line-height: 0;
			display: block;
			visibility: hidden;
			clear: both;
		}
    </style>
</head>

<body>
	<div class="layui-col-xs12 layui-col-md12">
    	<div class="layui-card shadow" style="">
           	<div class="layui-card-body">
			    <div class="layui-inline">
			      	<label class="layui-form-label" i18n-content="日期范围"></label>
			      	<div class="layui-input-inline">
			        	<input type="text" class="layui-input" id="beginStatDate" name="beginStatDate" autocomplete="off" style="height:30px;width:142px;display: inline-block;" lay-key="1"> 
	                	<span class="input-group-addon">~</span>
	                	<input type="text" class="layui-input" id="endStatDate" name="endStatDate" autocomplete="off" style="height:30px;width:142px;display: inline-block;" lay-key="2"> 
	                	<span class="input-group-addon">-</span>
	                	<select class="selectpicker form-control" name="dateRange" id="dateRange" onchange="onCasecade($(this), 'beginStatDate', 'endStatDate')" style="height: 28px;border: 1px solid #b7b4b4;border-radius: 3px;">
	                   		<option value="" i18n-content="请选择"></option>
							<option value="today" i18n-content="今天"></option>
							<option value="yesterday" i18n-content="昨天"></option>
							<option value="thisWeek" i18n-content="本周"></option>
							<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
						</select>
			      	</div>
			      	<button class="layui-btn layui-btn-normal" onclick="custCallData.initData()" i18n-content="统计" style="height: 30px;line-height: 30px;"></button>
			 	</div>
           	</div>
       </div>
    </div>
    
 	<div class="layui-tab-item layui-show layui-col-xs12 layui-col-md12" style="padding: 0">
    	<div class="layui-card layui-col-md12">
    		<div class="layui-card">
	        	<div style="max-width: 100%;">
	        		<i class="layui-icon layui-icon-about tips" style="color: #1ec5ff;position: relative;left: 5px;top: 29px;z-index: 99;"
        			tips-content="1、未接来电、语音漏话、语音留言参考话务管理-通话记录</br>2、离线消息、在线留言参考在线管理-会话记录-全媒体记录"></i>
	            	<div id="allDataChart" style="width:100%;height:300px;padding: 10px 0 10px 0;display: inline-block; vertical-align: middle;"></div>
	         	</div>
	        </div>
	    	<div class="layui-card mt-5">
	        	<div style="max-width: 100%;">
	        		<i class="layui-icon layui-icon-about tips" style="color: #1ec5ff;position: relative;left: 5px;top: 29px;z-index: 99;"
        			tips-content="1、排行榜只取前10的数据</br>2、只统计已领取但未处理的数据"></i>
	            	<div id="undoAgentRank" style="width:100%;height:300px;padding: 10px 0 10px 0;display: inline-block; vertical-align: middle;"></div>
	         	</div>
	        </div>
	    	<div class="layui-card mt-5">
	        	<div style="max-width: 100%;">
	        		<i class="layui-icon layui-icon-about tips" style="color: #1ec5ff;position: relative;left: 5px;top: 29px;z-index: 99;"
        			tips-content="1、排行榜只取前10的数据</br>2、只统计已领取但未处理的数据"></i>
	            	<div id="undoDeptRank" style="width:100%;height:300px;padding: 10px 0 10px 0;display: inline-block; vertical-align: middle;"></div>
	         	</div>
	        </div>
	    	<div class="layui-card mt-5">
	        	<div style="max-width: 100%;">
	        		<i class="layui-icon layui-icon-about tips" style="color: #1ec5ff;position: relative;left: 5px;top: 29px;z-index: 99;"
        			tips-content="1、排行榜只取前10的数据"></i>
	            	<div id="doneAgentRank" style="width:100%;height:300px;padding: 10px 0 10px 0;display: inline-block; vertical-align: middle;"></div>
	         	</div>
			</div>
	    	<div class="layui-card mt-5">
	        	<div style="max-width: 100%;">
	        		<i class="layui-icon layui-icon-about tips" style="color: #1ec5ff;position: relative;left: 5px;top: 29px;z-index: 99;"
        			tips-content="1、排行榜只取前10的数据"></i>
	            	<div id="doneDeptRank" style="width:100%;height:300px;padding: 10px 0 10px 0;display: inline-block; vertical-align: middle;"></div>
	         	</div>
			</div>
		</div>
	</div>
	<!-- js部分 -->
	<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script> 
	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
	<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
	<script type="text/javascript" src="/easitline-static/js/layTable.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
	<script type="text/javascript" src="/cc-report/static/js/echarts.min.js"></script>
	<script type="text/javascript" src="/cc-report/static/js/time.js"></script>
	<script type="text/javascript" src="/cc-report/static/js/my_i18n.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
	<script type="text/javascript" src="/cc-report/static/js/chart-common.js"></script>
	<script type="text/javascript" src="/cc-report/pages/stat/cust/custCallDataAnaylse.js"></script>
</body>

</html>