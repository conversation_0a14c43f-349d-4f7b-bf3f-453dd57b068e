<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>客户通话分析</title>
	<style>
		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<div class="layui-tab layui-tab-brief" lay-filter="tabSwitch">
	<form action="" method="post" name="searchForm" class="form-inline shadow" id="searchForm" onsubmit="return false" data-toggle="">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5 i18n-content="客户通话分析"></h5>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group" id="divId">
					<div class="input-group ">
						<span class="input-group-addon">
							<span style="color:red">*</span>
							<span i18n-content="客户号码"></span>
						</span> 
						<input type="text" id="custPhone" name="custPhone" class="form-control input-sm" style="width: 150px">
					</div>
					<div class="input-group ">
						<span class="input-group-addon" i18n-content="时间段"></span> 
						<input type="text" class="form-control input-sm" id="startDate" name="startDate" data-mars="common.limitSevenBeginDate" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">~</span>
	                	<input type="text" class="form-control input-sm" id="endDate" name="endDate" data-mars="common.todayEndTime" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
					</div>
					<div class="input-group" >
						<button type="button" class="btn btn-sm btn-default" onclick="Analysis.searchData()">
							<span class="glyphicon glyphicon-search" ></span><span i18n-content="查询"></span>
						</button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="Analysis.reset()">
							<span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</form>
			
	<ul class="layui-tab-title">
	    <li class="layui-this" i18n-content="通话记录"></li>
	    <li i18n-content="未接来电"></li>
	    <li i18n-content="语音漏话"></li>
	    <li i18n-content="语音留言"></li>
	    <li i18n-content="IVR轨迹"></li>
	</ul>
	<div class="layui-tab-content" style="height: 100px;">	
		<!-- 通话记录 -->
		<div class="layui-tab-item layui-show">
			<form method="post" name="cdrQueryForm" class="form-inline" id="cdrQueryForm" onsubmit="return false" data-toggle="">
				<div class="ibox">
					<div class="ibox-content">
						<table id="cdrQueryTable"></table>
					</div>
				</div>
			</form>
		</div>
		<!-- 未接来电 -->
		<div class="layui-tab-item">
			<div class="layui-tab-item layui-show">
				<form method="post" name="callNoForm" class="form-inline" id="callNoForm" onsubmit="return false" data-toggle="">
					<div class="ibox">
						<div class="ibox-content">
							<table id="callNoTable"></table>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- 语音漏话 -->
		<div class="layui-tab-item">
			<div class="layui-tab-item layui-show">
				<form method="post" name="callMiscallForm" class="form-inline" id="callMiscallForm" onsubmit="return false" data-toggle="">
					<div class="ibox">
						<div class="ibox-content">
							<table id="callMiscallTable"></table>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- 语音留言 -->
		<div class="layui-tab-item">
			<div class="layui-tab-item layui-show">
				<form method="post" name="callWordForm" class="form-inline" id="callWordForm" onsubmit="return false" data-toggle="">
					<div class="ibox">
						<div class="ibox-content">
							<table id="callWordTable"></table>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- IVR轨迹 -->
		<div class="layui-tab-item">
			<div class="layui-tab-item layui-show">
				<form method="post" name="callNoForm" class="form-inline" id="ivrTraceForm" onsubmit="return false" data-toggle="">
					<div class="ibox">
						<div class="ibox-content">
							<table id="ivrTraceTable"></table>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
</EasyTag:override>

<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Analysis");
		
		var startDate = "";
		var endDate = "";
		Analysis.recordUrlPrefix = "";//录音文件前缀
		
		$(function(){
			$("#searchForm").render({success:function(){
				//记录默认时间段
				startDate = $("#startDate").val();
				endDate = $("#endDate").val();
				requreLib.setplugs('layui',function(){
					//Tab的切换功能
					layui.use(['element','laydate'], function(){
						//加载时间控件
						var laydate = layui.laydate;
						laydate.render({elem:'#startDate',type:'datetime',lang:getDateLang()});
						laydate.render({elem:'#endDate',type:'datetime',lang:getDateLang()});
						//Tab的切换功能，切换事件监听等，需要依赖element模块
						var element = layui.element;
						element.on('tab(tabSwitch)', function(elem){
							if(!$("#custPhone").val()){
								layer.msg(getI18nValue('请输入客户号码！'),{icon: 5});
								return;
							}
							if (elem.index == '0') {
								//通话记录
								Analysis.cdrQueryList();
							} else if (elem.index == '1') {
								//未接来电
								Analysis.callNoList();
							}else if (elem.index == '2') {
								//语音漏话
								Analysis.callMiscallList();
							}else if (elem.index == '3') {
								//语音留言
								Analysis.callWordList();
							}else if(elem.index == '4'){
								//IVR轨迹
								Analysis.ivrTraceForm();
							}
						});
					});
				})
			}});
		})
		
		//重置
		Analysis.reset = function(){
	    	$("#divId select").val("");
	    	$("#divId input").val("");
	    	$("#startDate").val(startDate);
	    	$("#endDate").val(endDate);
		}
		//查询
		Analysis.searchData = function(){
			if(!$("#custPhone").val()){
				layer.msg(getI18nValue('请输入客户号码！'),{icon: 5});
				return;
			}
			$(".layui-this").click();
		}
		
		//通话记录
		Analysis.cdrQueryList = function(){
			$("#cdrQueryForm").initTableEx({
				url:'/cc-report/webcall?action=report.list',
				id: 'cdrQueryTable',
				limit: '15',
				data:form.getJSONObject("searchForm"), 	
				height: 'full-170',
				limits: [15, 25, 50, 100, 200],
				page:true,
				title:getI18nValue('通话录音'),
				cols: [
					[
					{width:60,field:'', title: getI18nValue('选择'), type:'checkbox'},
					 {width:80,align:'center',title: getI18nValue('序号') ,type:'numbers'},
					{minWidth:100,align:'center',field:'DATE_ID', title: getI18nValue('日期'),sort:true,
			        	 templet:function(row){
			        		 return  row.DATE_ID.substr(0,4)+"-"+row.DATE_ID.substr(4,2)+"-"+row.DATE_ID.substr(6,2);
			        }},
					{minWidth:100,align:'center',field:'BILL_BEGIN_TIME', title: getI18nValue('开始时间'),
			        	 templet:function(row){
			        		 return  dateToTime(row.BILL_BEGIN_TIME); 
			        }},
					{minWidth:100,align:'center',field:'BILL_END_TIME', title: getI18nValue('结束时间'),
			        	 templet:function(row){
			        		 return  dateToTime(row.BILL_END_TIME); 
			        }},
					{minWidth:150,align:'center',field:'BILL_TIME', title: getI18nValue('通话时长(s)'),sort:true},
					
					{minWidth:100,align:'center',field:'AGENT_PHONE', title: getI18nValue('坐席账号')},
					{minWidth:100,align:'center',field:'AGENT_NAME', title: getI18nValue('坐席姓名')},
			        {minWidth:100,align:'center',field:'AGENT_STAY_TIME', title: getI18nValue('振铃时长'),sort:true},
			        {minWidth:150,align:'center',field:'WORKREADY_STAY_TIME', title: getI18nValue('话后整理时长'),sort:true},
			        
					{minWidth:120,align:'center',field:'GROUP_NAME', title: getI18nValue('技能组名称'),sort:true},
					{minWidth:100,align:'center',field:'CALLER', title: getI18nValue('主叫号码'),sort:true, templet:function(row){
						
						 var phone = row.CALLER;
		        		 if(phone){
							 return getPhone(row.CALLER,row.CALLER,"${ctxPath}")+'&nbsp;<a title="'+getI18nValue('拨打电话')+''+getPhone(phone,phone,"${ctxPath}")+'" href="javascript:void(0)" onclick="callPhone(\''+phone+'\')"><i class="glyphicon glyphicon-earphone"></i></a>'
						 } else{
							 return "";
						 }
						
			             //return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
			        }},
					{minWidth:150,align:'center',field:'CALLED', title: getI18nValue('被叫号码'),sort:true, templet:function(row){
			        	//return getPhone(row.CALLED,row.CALLED,"${ctxPath}");
			        		 
		        		 var phone = row.CALLED;
		        		 if(phone){
							 return getPhone(row.CALLED,row.CALLED,"${ctxPath}")+' &nbsp;<a title="'+getI18nValue('拨打电话')+''+getPhone(phone,phone,"${ctxPath}")+'" href="javascript:void(0)" onclick="callPhone(\''+phone+'\')"><i class="glyphicon glyphicon-earphone"></i></a>'
						 } else{
							 return "";
						 }
			        }},
					{minWidth:100,align:'center',field:'CREATE_CAUSE', title: getI18nValue('呼叫方式'),
			        	 templet:function(row){
			        		 return  calltype(row.CREATE_CAUSE); 
			        }},
					{minWidth:150,align:'center',field:'CLEAR_CAUSE', title: getI18nValue('呼叫结果'),
			        	 templet:function(row){
			        		 return  getDictTextByCode('CLEAR_CAUSE_CN',row.CLEAR_CAUSE);
			        }},
					{minWidth:100,align:'center',field:'SATISF_ID', title: getI18nValue('满意度'),
			        	 templet:function(row){
			        		 return  getDictTextByCode('VOICE_SATISF',row.SATISF_ID);
			        }},
					{minWidth:100,align:'center',field:'AGENT_RELEASE', title: getI18nValue('挂断类型'),
			        	 templet:function(row){
			        		 return  agentype(row.AGENT_RELEASE); 
			        }},
					{minWidth:100,align:'center',field:'',fixed:'right', title: getI18nValue('录音'),
			        	 templet:function(row){
			        		 
			        		 var temp=" ";
								if(row.RECORD_FILE){
									temp='<input type="hidden" class="layui-btn layui-btn-xs" name="files" value="'+row.RECORD_FILE+'"><span href="javascript:void(0)" class="layui-btn layui-btn-xs " onclick="recoredListener(\''+row.SERIAL_ID+'\')" > '+getI18nValue('播放录音')+' </span> </input>';
								}
					        return temp;
                         
			        }},
			        {minWidth:230,align:'center',field:'',fixed:'right', title: getI18nValue('操作'),
			        	templet:function(row){
			        		var temp= '';
			        		var caseStatus = row.CASE_STATUS;
			        		temp += '<a href="javascript:void(0)" onclick="summaryRecord(\''+row.SERIAL_ID+'\')">'+getI18nValue('小结')+'</a>'
			        		var callType = ['1','2'];// 呼入
			        		/*呼叫创建原因 1外线呼入 2IVR转入 3席间转移呼入 4IVR咨询呼入 5席间咨询呼入 6呼出 8预拨号呼出 9呼叫前转呼入 10转移呼入 14席间呼入 29 席间呼出  */
			        		if(callType.indexOf(row.CREATE_CAUSE) > -1){ // 呼入的记录才显示IVR轨迹
			        			temp += ' - <a href="javascript:void(0)" onclick="IVRTrace(\''+row.BILL_BEGIN_TIME+'\',\''+row.BILL_END_TIME+'\',\''+row.CALLER+'\',\''+row.CALLED+'\')">'+getI18nValue("进线轨迹")+'</a>';
			        		}
							
			        		<EasyTag:res resId="cc-case-apply-access">
								if (row.CASE_STATUS == "1"){// 1-未申请 2-待审核 3-审核通过，4-待二次申请，5-审核不通过
									temp += ' - <a href="javascript:void(0)" onclick="application(\''+row.CASE_STATUS+'\',\'' + row.SERIAL_ID + '\')" > ' + getI18nValue("案例申请") + '</a>';
								}else if (row.CASE_STATUS == "4"){
									temp += ' - <a href="javascript:void(0)" onclick="application(\''+row.CASE_STATUS+'\',\'' + row.SERIAL_ID + '\')" > ' + getI18nValue("二次申请") + '</a>';
								}
							</EasyTag:res>
							<EasyTag:res resId="cc-case-follow-access">
								if(row.CASE_STATUS=="2" || row.CASE_STATUS =="3"){
									temp += ' - <a href="javascript:void(0)" onclick="caseFollow(\''+row.CASE_STATUS+'\',\'' + row.SERIAL_ID + '\')" > ' + getI18nValue("案例跟进") + '</a>';
								}
							</EasyTag:res>
							return temp;
			        	}
			        },
		         ]
				],
				done: function(obj, event) {
					execI18n();
				}
			});
		}
		
		//未接来电
		Analysis.callNoList = function(){
			var data = form.getJSONObject("searchForm");
			data.caller = data.custPhone;
			$("#callNoForm").initTableEx({
				url:'/cc-callloss/webcall?action=callNoAnswer.noAnswerList',
				id: 'callNoTable',
				data:data, 	
				limit: '15',
				height: 'full-160',
				title:getI18nValue("未接来电列表"),
				limits: [15, 25, 50, 100, 200],
				cellMinWidth:60,
				cols: [
					[
					 {width:120,align:'center',title:'序号',type:'numbers'},
					{minWidth:100,align:'center',field:'DATE_ID', title:'日期',sort:true},
					{minWidth:140,align:'center',field:'', title:'主叫' ,
			        	 templet:function(row){
			        		 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
		            }},
		            {minWidth:100,align:'center',field:'', title:'回拨' ,
			        	 templet:function(row){
			        		 var phone = row.CALLER;
			        		 if(phone){
									return '<a title="'+getI18nValue('拨打电话')+''+getPhone(phone,phone,"${ctxPath}")+'"  href="javascript:void(0)"  onclick="callPhone(\''+phone+'\',\''+row.SERIAL_ID+'\')"><i class="glyphicon glyphicon-earphone"></i></a>'
							 }else{
								 return "";
							 }
			        }},
					{minWidth:100,align:'center',field:'', title: '被叫',
			        	 templet:function(row){
			        		 return getPhone(row.CALLED,row.CALLED,"${ctxPath}");
		            }},
		            {minWidth:90,align:'center',field:'COUNT', title: '次数'},
					{minWidth:110,align:'center',field:'ALERING_TIME', title:'振铃开始时间' ,
			        	 templet:function(row){
		        			 // return substrHMS(row.ALERING_TIME);
		        			 return row.ALERING_TIME;
		            }},
		            {minWidth:110,align:'center',field:'END_TIME', title:'振铃结束时间' ,
			        	 templet:function(row){
		        			 // return substrHMS(row.END_TIME);
		        			 return row.END_TIME;
		            }},
					{minWidth:180,align:'center',field:'AGENT_STAY_TIME', title:'振铃时长' ,sort:true,
			        	 templet:function(row){
		        			 // return formatSeconds(row.AGENT_STAY_TIME);
		        			 return row.AGENT_STAY_TIME;
		            }},
					{minWidth:115,align:'center',field:'AGENT_ID', title: '坐席账号'},
					{minWidth:110,align:'center',field:'AGENT_NAME', title:'坐席姓名' },
					{minWidth:150,align:'center',field:'QUEUE_ID', title:'队列ID' },
					{minWidth:100,align:'center',field:'QUEUE_NAME', title: '队列名称'},
					{minWidth:100,align:'center',field:'GROUP_NAME', title: '技能组'},
					{minWidth:150,align:'center',field:'RECALL_TIME', sort:true, title: '回拨时间',
			        	 templet:function(row){
			        		 if (row.RECALL_TIME){
			        			 return row.RECALL_TIME
			        		 }else{
			        			 return "-";
			        		 }
			        }},
					{minWidth:150,align:'center',field:'STATE', title:'处理状态',
			        	 templet:function(row){
			        		 return getI18nValue(getDictTextByCode('STATE',row.STATE));
			        		 //var json={0:'layui-btn-danger',1:'label-success'}; 
							 //return "<span class='layui-btn layui-btn-xs "+json[row.STATE]+"'>"+ getI18nValue(getDictTextByCode('STATE',row.STATE)) +"</span>"
			        }},
					{minWidth:100,align:'center',field:'USERNAME', title:'处理人' ,
			        	 templet:function(row){
			        		 if (row.USERNAME){
			        			 return row.USERNAME
			        		 }else{
			        			 return "-";
			        		 }
			        }},
					{minWidth:180,align:'center',field:'HANDLE_TIME', title:'处理时间' ,sort:true,
			        	 templet:function(row){
			        		 if (row.HANDLE_TIME){
			        			 return row.HANDLE_TIME
			        		 }else{
			        			 return "-";
			        		 }
			        }},
					{fixed:'right',minWidth:220,align:'center',field:'STATE', title: '操作',templet:function(row){
						var temp="";
						if(row.STATE==0){
							temp='<a href="javascript:void(0)" class="layui-btn layui-btn-xs"    onclick="noListHandle(\''+row.SERIAL_ID+'\')">'+getI18nValue('标记处理')+'</a>&nbsp;&nbsp;'
						}
						temp += '<a href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="IVRTrace(\''+row.BEGIN_TIME+'\',\''+row.END_TIME+'\',\''+row.CALLER+'\',\''+row.CALLED+'\')">'+getI18nValue('进线轨迹')+'</a>&nbsp;&nbsp;';
			        	return temp;
			         }},
		         ]
				],
				done: function(obj, event) {
					execI18n();
				}
			});
		}
		
		//语音漏话
		Analysis.callMiscallList = function(){
			var data = form.getJSONObject("searchForm");
			data.caller = data.custPhone;
			$("#callMiscallForm").initTableEx({
				url:'/cc-callloss/webcall?action=callNoAnswer.miscallList',
				id: 'callMiscallTable',
				data:data, 	
				limit: '15',
				height: 'full-170',
				title:getI18nValue("语音漏话"),
				limits: [15, 25, 50, 100, 200],
				cellMinWidth:60,
				cols: [
					[
					 {width:120,align:'center',title:'序号',type:'numbers'},
					 {minWidth:150,align:'center',field:'DATE_ID', title:'日期',sort:true},
					   {minWidth:150,align:'center',field:'CALLER', title:'主叫' ,
						 templet:function(row){
							 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
						}},
					   {minWidth:100,align:'center',field:'', title:'回拨' ,
					    	 templet:function(row){
					    		 if (row.CALLER){
					    			 return '<a title="'+getI18nValue('拨打电话')+''+getPhone(row.CALLER,row.CALLER,"${ctxPath}")+'" href="javascript:void(0)" onclick="callPhone(\''+row.CALLER+'\',\''+row.SERIAL_ID+'\')"><i class="glyphicon glyphicon-earphone"></i></a>'
					    		 }else{
					    			 return "";
					    		 }
					    }},
					   {minWidth:150,align:'center',field:'_CALLED', title:'被叫' ,
					    	templet:function(row){
					    		 return getPhone(row._CALLED,row._CALLED,"${ctxPath}");
					    }},
					   {minWidth:90,align:'center',field:'COUNT', title: '次数'},
						  
					   {minWidth:150,align:'center',field:'BEGIN_TIME', title: '开始时间',
					    	 templet:function(row){
								 return substrHMS(row.BEGIN_TIME);
					    }},
					   {minWidth:150,align:'center',field:'QUEUE_TIME', title:'排队时间',
					    	 templet:function(row){
								 return substrHMS(row.QUEUE_TIME);
					    }},
					   {minWidth:150,align:'center',field:'END_TIME', title:'挂机时间' ,
					    	 templet:function(row){
								 return substrHMS(row.END_TIME);
					    }},
					   {minWidth:150,align:'center',field:'QUEUE_STAY_TIME', title:'排队停留时间(秒)' ,
					    	 templet:function(row){
								 return formatSeconds(row.QUEUE_STAY_TIME);
					    }},
					   {minWidth:180,align:'center',field:'CLEAR_CAUSE', title:'挂机原因',
					    	 templet:function(row){// getDictTextByCode('CC_CALLLOSS_CLEAR_CAUSE',row.CLEAR_CAUSE);
					    		 return getI18nValue(getDictTextByCode('CC_CALLLOSS_CLEAR_CAUSE',row.CLEAR_CAUSE));
					    }},
					    {minWidth:160,align:'center',field:'AGENT_RELEASE', title:'结束通话原因', templet:function(row){// getDictTextByCode('CC_CALLLOSSS_CALL_STEP_ID',row.CALL_STEP_ID);
			        		 return getI18nValue(getDictTextByCode('CC_CALLLOSS_AGENT_RELEASE',row.AGENT_RELEASE));
			            }},
					   {minWidth:150,align:'center',field:'', title:'回拨时间',
					    	 templet:function(row){
					    		 if (row.RECALL_TIME){
					    			 return row.RECALL_TIME
					    		 }else{
					    			 return "-";
					    		 }
					    }},
					   {minWidth:150,align:'center',field:'QUEUE_ID', title:'队列ID' },
					   {minWidth:150,align:'center',field:'QUEUE_NAME', title:'队列名称' },
					   {minWidth:150,align:'center',field:'GROUP_NAME', title:'技能组' },
					   {minWidth:150,align:'center',field:'STATE', title: '处理状态',
					    	 templet:function(row){
					    		 return getI18nValue(getDictTextByCode('STATE',row.STATE));
					    		 //var json={0:'layui-btn-danger',1:'label-success'};
								 //return "<span class='layui-btn layui-btn-xs "+json[row.STATE]+"'>"+ getI18nValue(getDictTextByCode('STATE',row.STATE)) +"</span>"
					    }},
					   {minWidth:150,align:'center',field:'USERNAME', title: '处理人'},
					   {minWidth:180,align:'center',field:'HANDLE_TIME',sort:true, title: '处理时间',
					    	 templet:function(row){
					    		 if (row.HANDLE_TIME){
					    			 return row.HANDLE_TIME
					    		 }else{
					    			 return "-";
					    		 }
					   }},
					   {minWidth:200,align:'center',field:'',fixed:'right', title:'操作' ,templet:function(row){
							var temp="";
							if(row.STATE==0){
								temp='<a href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="missCallHandle(\''+row.SERIAL_ID+'\')">'+getI18nValue('标记已处理')+'</a>&nbsp;&nbsp;'
							}
							temp += '<a href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="IVRTrace(\''+row.BEGIN_TIME+'\',\''+row.END_TIME+'\',\''+row.CALLER+'\',\''+row.CALLED+'\')">'+getI18nValue('进线轨迹')+'</a>&nbsp;&nbsp;';
					    	return temp;
					   }},
					]
				],
				done:function(res,curr,count){
		            layui.use(['jquery', 'layer'], function(){ 
  							var $ = layui.$ ,layer = layui.layer;//重点
		                     res.data.forEach(function (item, index) {
		 		                if (item.STATE == 1) {
		 		                	$("[lay-id=tree-table]").find(".layui-table-box tbody tr[data-index='" + index + "']").addClass("layui-bg-gray");
		 		                } 
		 		            });
		            });
				}
			});
		}
		
		//语音留言
		Analysis.callWordList = function(){
			var data = form.getJSONObject("searchForm");
			data.caller = data.custPhone;
			$("#callWordForm").initTableEx({
				url:'/cc-callloss/webcall?action=callNoAnswer.wordList',
				id: 'callWordTable',
				limit: '15',
				data:data, 	
				height: 'full-170',
				title:getI18nValue("电话留言列表"),
				limits: [15, 25, 50, 100, 200],
				cellMinWidth:60,
				cols: [
					[
					 {width:120,align:'center',title:'序号' ,type:'numbers'},
					  {minWidth:100,align:'center',field:'DATE_ID', sort:true, title: '日期'},
					  {minWidth:100,align:'center',field:'CALLER', title:'主叫' ,sort:true,
					    	 templet:function(row){
					    		 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
					    }},
					   {minWidth:100,align:'center',field:'REPLY_PHONE', title:'回拨号码' ,sort:true,
					    	 templet:function(row){
					    		 if(!row.REPLY_PHONE){
					    			 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
					    		 }
					    		 return getPhone(row.REPLY_PHONE,row.REPLY_PHONE,"${ctxPath}");
					   }},
					   {minWidth:100,align:'center',field:'REPLY_PHONE', title:'回拨' ,
					    	 templet:function(row){
					    		 var phone = row.REPLY_PHONE;
					    		 if(!phone){
					    			 phone = row.CALLER;
					    		 }
					    		 if (phone){
					    			 var temp = '<a title="'+getI18nValue('拨打电话')+''+getPhone(phone,phone,"${ctxPath}")+'" href="javascript:void(0)" onclick="callPhone(\''+phone+'\',\''+row.SERIAL_ID+'\')"><i class="glyphicon glyphicon-earphone"></i></a>';
					    			 return temp;
					    		 }else{
					    			 return "";
					    		 }
					    }},
					  {minWidth:100,align:'center',field:'CALLED', title:'被叫' ,sort:true,
					    	 templet:function(row){
					    		 return getPhone(row.CALLED,row.CALLED,"${ctxPath}");
								 //return getPhone(row.CALLED,row._CALLED);
					    }},
					  {minWidth:100,align:'center',field:'BEGIN_TIME', title:'开始时间' ,sort:true,
					    	 templet:function(row){
								 return substrHMS(row.BEGIN_TIME);
					    }},
					  {minWidth:100,align:'center',field:'END_TIME', title: '结束时间',sort:true,
					    	 templet:function(row){
								 return substrHMS(row.END_TIME);
					    }},
					  {minWidth:130,align:'center',field:'RECALL_TIME', title: '回拨时间',sort:true,
					    	 templet:function(row){
					    		 if (row.RECALL_TIME){
					    			 return row.RECALL_TIME
					    		 }else{
					    			 return "-";
					    		 }
					    }},
					  {minWidth:140,align:'center',field:'STATE', title: '处理状态',sort:true,
					    	 templet:function(row){
					    		 return getI18nValue(getDictTextByCode('STATE',row.STATE));
					    		 //var json={0:'layui-btn-danger',1:'label-success'};			/* +"1".equals(row.STATE) ?getI18nValue('已经处理'):getI18nValue('未处理')+ */
								 //return "<span class='layui-btn layui-btn-xs "+json[row.STATE]+"'>"+ getI18nValue(getDictTextByCode('STATE',row.STATE)) +"</span>"
					    }},
					  {minWidth:100,align:'center',field:'USERNAME', title:'处理人',sort:true,
					    	 templet:function(row){
					    		 if (row.USERNAME){
					    			 return row.USERNAME
					    		 }else{
					    			 return "-";
					    		 }
					    }},
					  {minWidth:150,align:'center',field:'HANDLE_TIME', title:'处理时间',sort:true ,
					    	 templet:function(row){
					    		 if (row.HANDLE_TIME){
					    			 return row.HANDLE_TIME
					    		 }else{
					    			 return "-";
					    		 }
					    }},
					  {minWidth:260,align:'center',field:'', title: '操作' ,fixed:'right',templet:function(row){
							var temp="";
							if(row.STATE==0){
								temp='<a href="javascript:void(0)"  class="layui-btn layui-btn-xs"  onclick="wordListHandle(\''+row.SERIAL_ID+'\')">'+getI18nValue('标记处理')+'</a>&nbsp;&nbsp;'
							}
							if(row.RECORD_FILE){
								/* if(row.RECORD_FILE<3){ */
									temp=temp+'<a  href="javascript:void(0)" class="layui-btn layui-btn-xs"  onclick="wordRecordListener(\''+row.SERIAL_ID+'\')">'+getI18nValue('播放录音')+'</a>&nbsp;&nbsp;'
								/* } */
							}
							temp += '<a href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="IVRTrace(\''+row.BEGIN_TIME+'\',\''+row.END_TIME+'\',\''+row.CALLER+'\',\''+row.CALLED+'\')">'+getI18nValue('进线轨迹')+'</a>&nbsp;&nbsp;';
							return temp;
					     }},
					]
				],
				done:function(res,curr,count){
					var data = res;
					if(data && data != undefined){
						Analysis.recordUrlPrefix = data.recordUrlPrefix;
					}
		        }
			});
		}
		//ivr轨迹
		Analysis.ivrTraceForm = function(){
			var data = {"CALLER":$("#custPhone").val(),"getStartDate":$("#startDate").val(),"getEndDate":$("#endDate").val()};
			$("#ivrTraceForm").initTableEx({
				url:'/cc-base/webcall?action=ivr.list',
				id: 'ivrTraceTable',
				data:data, 	
				limit: '15',
				height: 'full-170',
				title:getI18nValue("进线轨迹"),
				limits: [15, 25, 50, 100, 200],
				cellMinWidth:60,
				cols: [
                    [
                        {width: 60, field: 'TRACE_ID', title: '选择', type: 'checkbox'}
                        , {width: 60, field: 'TRACE_ID', title: '序号', type: 'numbers'}
                        , {width: 150, field: 'CALLER', title: '来电号码'}
                        , {width: 150, field: 'IVR_BEGIN_TIME', title: '进入IVR时间'}
                        , {width: 150, field: 'KEY_TIME', title: '按键时间'}
                        , {field: 'KEY_NAME', title: '按键名称'}
                        , {field: 'END_CAUSE', title: '结束原因'}
                    ]
                ]
			})
		}
		
		
		//--------------------------------------表单操作----------------------------------
		
		//语音留言 - 标记处理
		function wordListHandle(serialId){
			layer.confirm(getI18nValue('确定标记为已处理'),{
				btn : [ getI18nValue('确定'), getI18nValue('取消')] ,title:getI18nValue('信息'),offset:'40px'},
				function(index, layero) {
					layer.close(index);
					ajax.remoteCall("/cc-callloss/servlet/callNoanswer?action=updateWord", {serialId:serialId}, function(result) {
			  			if(result.state == 1){
			  				Analysis.callWordList();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
		  			});
				},function(index){
					layer.close(index);
				});
		}
		//语音漏话 - 标记处理
		function missCallHandle (serialId){
			var data = {};
			data.serialId = serialId;
			data.updateAll = 'n';//'是否同时处理该号码的其他漏话?'
			layer.confirm(getI18nValue('是否同时处理该号码的其他漏话'),{btn : [getI18nValue('确定') , getI18nValue('取消') ],title:getI18nValue('信息')},function(index) {
				layer.close(index);
				data.updateAll = 'y';
				ajax.remoteCall("/cc-callloss/servlet/callNoanswer?action=updateMiscall", data, function(result) {
		  			if(result.state == 1){
		  				Analysis.callMiscallList();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			},function(index){
				layer.close(index);
				data.updateAll = 'n';
				ajax.remoteCall("/cc-callloss/servlet/callNoanswer?action=updateMiscall", data, function(result) {
		  			if(result.state == 1){
		  				Analysis.callMiscallList();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		//未接来电 - 标记处理
		function noListHandle(serialId){
			var data = {};
			data.serialId = serialId;//'是否同时处理该号码的其他未接来电?'
			layer.confirm(getI18nValue('是否同时处理该号码的其他未接来电'),{
				btn : [ getI18nValue('确定'), getI18nValue('取消')] ,
				offset:'40px',
				title:getI18nValue('信息')},
				function(index, layero) {
					layer.close(index);
					data.updateAll = 'y';
					ajax.remoteCall("/cc-callloss/servlet/callNoanswer?action=updateNoAnswer", data, function(result) {
			  			if(result.state == 1){
			  				Analysis.callNoList();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
		  			});
				},function(index){
					layer.close(index);
				});
		}
		//打开IVR轨迹
		function IVRTrace(startDate,endDate,caller,called){
			var data = {startDate:startDate,endDate:endDate,caller:caller,called:called};
			popup.openTab("/cc-base/pages/ivr/ivr-monitor-list.jsp", getI18nValue("进线轨迹跟踪"), data);
		}
		//案例申请
		function application(caseStatus,serialId) {
			popup.layerShow({
				type: 2,
				title: getI18nValue("案例申请"),
				offset: 'r',
				area: ['800px', '100%'],
				url: "/cc-case/pages/case/caseApply.jsp",
				data: {serialId: serialId,caseStatus:caseStatus,caseType:'01',appleType:2},
			});
		}
		//案例跟进
		function caseFollow(caseStatus,serialId){
			popup.layerShow({
				type: 1,
				title: getI18nValue("案例跟进"),
				offset: 'r',
				area: ['800px', '100%'],
				url: "/cc-case/pages/case/callSchedule.jsp",
				data: {serialId: serialId,caseStatus:caseStatus,caseType:'01',appleType:2}
			});
		}
		//小结
		function summaryRecord (refId){
			popup.openTab("/cc-summary/servlet/summaryConfig?action=ToAdd", getI18nValue("小结"), {refId: refId, type:"1"});
		}
		//播放录音
		function recoredListener(serialId) {
			 popup.layerShow({type:2,title:getI18nValue('播放录音'),offset:'20px',area:['750px','450px']},"/cc-base/pages/record/record-play.jsp",{serialId:serialId});
		}
		//播放留言
		function wordRecordListener(serialId) {
			 popup.layerShow({type:2,title:getI18nValue('播放录音'),offset:'20px',area:['750px','450px']},"/cc-base/pages/record/record-play.jsp",{wordId:serialId});
		}
		//拨打电话
		function callPhone(num){
			if(parent.parent.CallControl.getFunc('makecall')){
				parent.parent.ccbar_plugin.callControl.makeCall(num);
			} else{
				layer.alert(getI18nValue('无法外呼'));	
			}
		}
		
		function dateToTime(datetime){
			if(!datetime) return "";
			return datetime.toString().substr(11)
		}
		function calltype(calltype){
			//CALL_TYPE_ID 呼入呼出类型，1 呼出(6,8)  2 呼入(1,2) 3 席间呼入(14)  4 席间呼出(29)  5 咨询呼入(4,5)  6 转移呼入(3,9,10)
			if("6" == calltype || "8" == calltype){
				return getI18nValue("呼出");
			}else if("1" == calltype || "2" == calltype){
				return getI18nValue("呼入");
			}else if("14" == calltype ){
				return getI18nValue("席间呼入");
			}else if("29" == calltype ){
				return getI18nValue("席间呼出");
			}else if("4" == calltype || "5" == calltype ){
				return getI18nValue("咨询呼入");
			}else{
				return getI18nValue("转移呼入");
			}
		}
		function agentype(agentype){
			if("1" == agentype){
				return getI18nValue("用户挂断");
			}else if("2" == agentype){
				return getI18nValue("坐席挂断");
			}else if("3" == agentype){
				return getI18nValue("系统挂断");
			}else{
				return getI18nValue("暂无");
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>