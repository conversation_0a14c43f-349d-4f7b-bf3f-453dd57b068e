<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page import="com.yunqu.cc.sorder.base.Constants" %>
<%
boolean showCallBtn = Constants.isShowCallBtn();
%>
	<title>处理工单</title>
	<style>
		.gray-bg, .bg-muted {
    		background-color: #ffffff !important;
		}
		#EditTable>tr>td>div{margin-right: 0;padding-left: 0;}
		label.layui-form-label {width:100px}
		a:link{ color:#00adff;}
		div.container-fluid {
			padding-top: 6px;
			padding-bottom: 8px;
     		padding-right: 0px;
    		padding-left: 0px;
    		margin-right: auto;
    		margin-left: auto;
		}
		#tempInfo_bom .ew-cascader-group .layui-anim.layui-icon{
			display: none;
		}
		#easyform .layui-disabled{
			background-color: #FFFDFA;
    		opacity: 1;
    		box-shadow: none;
    		border-color: #ccc;
    		color: rgb(85, 85, 85) !important;
		}
		#easyform .layui-disabled, .layui-disabled:hover{
			color: rgb(85, 85, 85) !important;
		}
		#ALL_RECONTENT textarea, #OPER_HISTORY textarea{
			resize: none;	
		}
		#easyform .readonlyClass{
			background-color: #FFFDFA;
    		opacity: 1;
   	 		box-shadow: none;
    		border-color: #ccc;
    		cursor: not-allowed;
		}
		.callBtn{
			width: 40px;
    		height: 36px;
    		line-height: 36px;
    		padding: 0 10px;
    		font-size: 12px;
    		margin-bottom: 4px;
    		padding-left: 8px;
    		padding-right: 8px
		}
		.mobile_dom .call_input{
			width: calc(100% - 45px);
			display: inline-block;
		}
		#easyform .layui-btn-agent{
			background-color: #20d99b;
		}
		
		#easyform .layui-card-header{
			background-color: #ffffff;
			padding-left: 30px;
			border: none;
			color: #009688;
		}
		#easyform .layui-card {
    		margin-bottom: 0px;
		}
		#tempInfo_bom .dataInf{
			width: calc(100% - 55px);
    		display: inline-block;
		}
		#tempInfo_bom .findBtn-icon{
		    width: 55px;
		    height: 37px;
		    line-height: 36px;
		    padding: 0 10px;
		    font-size: 14px;
		    margin-bottom: 1px;
		    padding-left: 8px;
		    padding-right: 8px;
		}
		#tempInfo_bom .findBtn-icon>i{
			font-size: 12px!important;
		}
		
		#tempInfo_bom .layui-form-label {
    		float: left;
    		display: block;
    		padding: 9px 15px;
    		width: 100px;
    		font-weight: 400;
    		line-height: 20px;
    		text-align: right;
    		word-wrap: break-word;
		}
	</style>
<EasyTag:override name="content">
<form id="easyform"  method="post" class="layui-form" 
		data-mars="order.orderInfo" data-pk="${param.ID}">
<input type="hidden" name="SESSION_RECORD_ID" />
<input type="hidden" name="ID" id="ID" value="${param.ID}" >
<input type="hidden" name="ORDER_NO" id="ORDER_NO" value="" disabled="disabled" >
<input type="hidden" name="SOURCE" id="SOURCE" >
<div class="">
  <div class="layui-row layui-col-space15">
    <div class="layui-collapse layui-col-md12" style="margin-bottom: 30px;padding-top: 0;border-bottom: none;">
      <div class="layui-colla-item layui-card" id="baseInfo">
        <div class="layui-card-header layui-colla-title" i18n-content="基本信息">
        </div>
        <div class="layui-colla-content layui-row layui-show" id="baseFieldList">
          
        </div>
        
      </div>
      
      <div class="layui-colla-item layui-card" id="temp_car" style="display: none">
        <div class="layui-card-header layui-colla-title"  i18n-content="模板信息 "> 
		</div>
        <div class="layui-card-body layui-row  layui-colla-content layui-show" id="tempInfo_bom">
        </div>
      </div>
     </div><!-- layui-collapse end -->
  </div>
</div>
      <div class="layui-card" style="width:100%;position: relative;bottom:0">
      	<div class="layui-card-body layui-row " style="text-align: center;">
      		<button class="btn btn-sm layui-btn layui-btn-normal"  type="button"
      		 onclick="attaMsg.attachment();" i18n-content="附件管理">  </button>
      		<button class="btn btn-sm layui-btn"  type="button" 
			 onclick="return orderInfo.tempSaveBefore()" i18n-content="暂存">  </button>
			<button class="btn btn-sm layui-btn"  type="button" 
			 onclick="return orderInfo.submitSaveBefore()" i18n-content="提交">  </button>
      		<button class="btn btn-sm layui-btn"  type="button" 
			 onclick="return orderInfo.closeSaveBefore()" i18n-content="归档 "> </button>
			<button class="btn btn-sm layui-btn"  type="button" 
			 lay-submit lay-filter="submit1" id="submit1" style="display: none" i18n-content="提交">  </button>
        </div>
     </div>
    
      <div class="layui-card" id="history-bom" style="width:100%;position: relative;bottom:0;outline: #fff solid 2px;">
      	<div class="layui-tab">
  			<ul class="layui-tab-title">
    			<li class="layui-this" i18n-content="所有回复"></li>
    			<li i18n-content="操作历史"></li>
 			 </ul>
 			 <div class="layui-tab-content">
   				<div class="layui-tab-item layui-show" id="ALL_RECONTENT"
   					style="margin-left: 15px" data-mars="order.allReContent" data-pk="${param.ID}">
   					
    			</div>
    			<div class="layui-tab-item" id="OPER_HISTORY"
    				style="margin-left: 15px" data-mars="order.allOperHistory" data-pk="${param.ID}">
					
				</div>
  			</div>
      	</div>
      </div>
<script id="base-field-list" type="text/html">
{{#  
  var showHtml = function(name, data){
	if(data[name]){
		return '';
	}else{
		return 'style="display: none;"';
	}
  }; 
}}
		  <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" {{showHtml('TITLE', d)}}>
          			<!-- 移动：6/12 | 平板：6/12 | 桌面：4/12 -->
          	<label class="layui-form-label" i18n-content="标题"></label>
    		<div class="layui-input-block">
      			<input type="text" name="TITLE" id="TITLE" 
      				lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=200"
      				autocomplete="off" i18n-placeholder="请输入标题" class="layui-input">
    		</div>
          </div>
        
          <div class="layui-col-xs12 layui-col-sm12 layui-col-md12" {{showHtml('CONTENT', d)}}>
          	<label class="layui-form-label required" i18n-content="内容"></label>
    		<div class="layui-input-block">
				<textarea id="CONTENT" lay-verType="tips" lay-verify="required|formVerify" data-rules="required|maxlength=2000"
					 name="CONTENT" i18n-placeholder="请输入内容" class="layui-textarea" style="margin-bottom: 5px;"></textarea>
    		</div>
          </div>
        
          <div class="layui-col-xs8 layui-col-sm6 layui-col-md4" {{showHtml('CUSTOMER_MOBILE', d)}}>
          	<label class="layui-form-label"i18n-content="客户号码"></label>
    		<div class="layui-input-block">
      			<input type="hidden" readonly="readonly" name="CUSTOMER_ID" id="CUSTOMER_ID" value="">
      			<input type="search" name="CUSTOMER_MOBILE" id="CUSTOMER_MOBILE" 
      				autocomplete="off" class="layui-input" value="">
    		</div>
          </div>
          <div class="layui-col-xs4 layui-col-sm6 layui-col-md8" {{showHtml('CUSTOMER_MOBILE', d)}}>
          	<label style="height: 38px;line-height: 38px;margin-left: 5px">
    			<button type="button" onclick="orderAdd.addCustomer()"
    				class="layui-btn layui-btn-sm layui-btn-normal" i18n-content="新建客户"></button>
    		</label>
    		<label style="height: 38px;line-height: 38px">
      			<button type="button" onclick="orderAdd.selectCustomer()"
      				class="layui-btn layui-btn-sm layui-btn-normal" i18n-content="查找客户"></button>
      		</label>
      		<label style="height: 38px;line-height: 38px;<%=showCallBtn?"":"display: none;"%>">
      			<button type="button" onclick="callMobile($('#CUSTOMER_MOBILE').val())"
      				class="layui-btn layui-btn-sm layui-btn-agent glyphicon glyphicon-earphone" style="padding: 0 4px;" i18n-content="拨打"></button>
      		</label>
          </div>
        
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('CUSTOMER_NAME', d)}}>
          	<label class="layui-form-label" i18n-content="客户姓名"></label>
    		<div class="layui-input-block">
      			<input type="text" name="CUSTOMER_NAME" id="CUSTOMER_NAME" 
      				autocomplete="off" class="layui-input" value="">
    		</div>
          </div>
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('CALLER', d)}}>
          	<label class="layui-form-label" i18n-content="来电号码"></label>
    		<div class="layui-input-block <%=showCallBtn?"mobile_dom":""%>">
      			<input type="text" name="CALLER" id="CALLER"
      				lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=20"
      				autocomplete="off" class="layui-input call_input" value="">
      			<label style="height: 36px;line-height: 36px;margin-bottom: 0px;<%=showCallBtn?"":"display: none;"%>">
      				<button type="button" onclick="callMobile($('#CALLER').val())"
      					class="layui-btn layui-btn-sm layui-btn-agent callBtn glyphicon glyphicon-earphone" style="height:35px;padding: 0 2px;" i18n-content="拨打"></button>
      			</label>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('CALL_TIME', d)}}>
          	<label class="layui-form-label" i18n-content="来电时间"></label>
    		<div class="layui-input-block">
      			<input type="text" name="CALL_TIME" id="CALL_TIME"
      				autocomplete="off" class="layui-input" value=">">
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_TYPE', d)}}>
          	<label class="layui-form-label" i18n-content="工单分类"></label>
    		<div class="layui-input-block">
      			<select name="ORDER_TYPE" id="ORDER_TYPE" data-mars="common.getDict(SO_ORDER_TYPE)" 
      					lay-filter="ORDER_TYPE_CHANGE">
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
        
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('TEMPLATE_ID', d)}}>
          	<label class="layui-form-label" i18n-content="工单模板"></label>
    		<div class="layui-input-block">
      			<select name="TEMPLATE_ID" id="TEMPLATE_ID" lay-filter="EMPLATE_ID_CHANGE">
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_CHANNEL', d)}}>
          	<label class="layui-form-label" i18n-content="渠道来源"></label>
    		<div class="layui-input-block">
      			<select data-mars="common.getDict(SO_ORDER_CHANNEL)" name="ORDER_CHANNEL" id="ORDER_CHANNEL">
					<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_STATUS', d)}}>
          	<label class="layui-form-label" i18n-content="状态"></label>
    		<div class="layui-input-block">
      			<select name="ORDER_STATUS" id="ORDER_STATUS" data-mars="common.getDict(ORDER_STATUS)"
      				 lay-filter="ORDER_STATUS_CHANGE">
				</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('PROVINCE', d)}}>
          	<label class="layui-form-label" i18n-content="省份"></label>
    		<div class="layui-input-block">
      			<select name="PROVINCE" id="PROVINCE" data-mars="common.provinceChooseSel()" 
      					lay-filter="PROVINCE_CHANGE" lay-search>
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
        
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('CITY', d)}}>
          	<label class="layui-form-label" i18n-content="城市"></label>
    		<div class="layui-input-block">
      			<select name="CITY" id="CITY" lay-search>
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_PRIORITY', d)}}>
          	<label class="layui-form-label" i18n-content="优先级"></label>
    		<div class="layui-input-block">
      			<select name="ORDER_PRIORITY" id="ORDER_PRIORITY" data-mars="common.getDict(SO_ORDER_PRIORITY)">

				</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('CARBON_COPY', d)}}>
          	<label class="layui-form-label" i18n-content="抄送人"></label>
    		<div class="layui-input-block">
    			<select name="CARBON_COPY" id="CARBON_COPY" data-mars="common.acctNotCurrDict" 
    					multiple="multiple" size="1" lay-ignore style="width: 100%">
        		</select>
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_TAG', d)}}>
          	<label class="layui-form-label" i18n-content="标签"></label>
    		<div class="layui-input-block">
      			<input type="text" name="ORDER_TAG" id="ORDER_TAG" autocomplete="off" 
      				lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=200"
      				i18n-placeholder="请输入标签  多个用;隔开" class="layui-input">
    		</div>
          </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('TIME_LIMIT', d)}}>
          	<label class="layui-form-label" i18n-content="处理时限"></label>
    		<div class="layui-input-block">
      			<select name="TIME_LIMIT" id="TIME_LIMIT" data-mars="common.getDict(TIME_LIMIT)">
      				<option value="0" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>

		  <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ORDER_EMAIL', d)}}>
			<label class="layui-form-label" i18n-content="邮箱地址"></label>
			<div class="layui-input-block">
				<input type="text" name="ORDER_EMAIL" id="ORDER_EMAIL" 
					lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=50"
					autocomplete="off" class="layui-input" value="">
			</div>
		  </div>
        
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('ASSIGN_RULE', d)}}>
          	<label class="layui-form-label" i18n-content="流转规则"></label>
    		<div class="layui-input-block">
      			<select name="ASSIGN_RULE" id="ASSIGN_RULE" lay-filter="ASSIGN_RULE_CHANGE" >
      				<option value="01" i18n-content="指定派发"></option>
      				<option value="02" i18n-content="随机派发"></option>
				</select>
    		</div>
    	  </div>
          
          <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" id="ORDER_GROUP_ID_DIV" {{showHtml('ORDER_GROUP', d)}}>
          	<label class="layui-form-label" i18n-content="受理组"></label>
    		<div class="layui-input-block">
      			<select name="CURR_ORDER_GROUP_ID" id="CURR_ORDER_GROUP_ID" 
      					lay-filter="CURR_ORDER_GROUP_ID_CHANGE" lay-verType="tips"
      					data-mars="workorderGroup.getEnableDict('01')">
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
    	  </div>
    	  
    	  <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" id="GROUP_USER_DIV" {{showHtml('ORDER_GROUP_USER', d)}}>
          	<label class="layui-form-label" i18n-content="受理人"></label>
    		<div class="layui-input-block">
    			<input type="hidden" name="CURR_ORDER_GROUP_USER_NAME" id="CURR_ORDER_GROUP_USER_NAME">
      			<select name="CURR_ORDER_GROUP_USER" id="CURR_ORDER_GROUP_USER" lay-verType="tips"
      					lay-filter="CURR_ORDER_GROUP_USER_CHANGE" lay-search >
      				<option value="" i18n-content="请选择"></option>
				</select>
    		</div>
          </div>
    	  
    	  <div class="layui-col-xs4 layui-col-sm4 layui-col-md4" {{showHtml('OPERATOR', d)}}>
          	<label class="layui-form-label" i18n-content="接线人员"></label>
    		<div class="layui-input-block">
    			<input type="text" id="CALL_OPERATOR" readonly class="layui-input readonlyClass" 
    				value="">
    		</div>
          </div>
</script>
</form>		
</EasyTag:override>
<!-- <textarea readonly="readonly" style="width:100%;height:80px;border: none;outline: none;">{{ item.CONTENT }}</textarea> -->
<EasyTag:override name="script">
<script id="history-list-temp" type="text/html">
{{#  
  var getShowStatus = function(code){
	var showStatus = '';
	switch (code) {
		case '01':showStatus='所有人可见';break;
		case '02':showStatus='仅创建人可见';break;
		default:break;
	}
	return showStatus
  }; 
}}
{{#  layui.each(d.list, function(index, item){ }}
	<img src="{{ item.ICON }}" class="layui-nav-img"
		onerror="javascript:this.src='/cc-workbench/static/images/user-avatar-large.png'"/>
	{{ item.USER_ACC }} | <label style="font-weight: normal;color: #FFB800;">{{getShowStatus(item.SHOW_STATUS)}}</label><br>
	<div style="margin-left: 40px;">
		<p class="layui-word-aux">{{ item.CREATE_TIME }} 回复 {{ item.FOLLOW_PROCESS.CURR_ORDER_GROUP_USER }}</p>
		<p class="layui-word-aux"><textarea readonly="readonly" class="layui-text" style="width:100%;height:70px;border: none;outline: none;">{{ item.CONTENT }}</textarea>
		</p>
	</div>
<hr>
{{#  }); }}
</script>
<script id="oper-history-list-temp" type="text/html">
{{#  
  var getOperType = function(code){
	var OperType = '';
	switch (code) {
		case '00':OperType=getI18nValue('暂存工单');break;
		case '01':OperType=getI18nValue('创建工单');break;
		case '02':OperType=getI18nValue('分派工单');break;
		case '03':OperType=getI18nValue('领取工单');break;
		case '04':OperType=getI18nValue('处理工单');break;
		case '05':OperType=getI18nValue('关闭工单');break;
		case '06':OperType=getI18nValue('删除工单');break;
		case '98':OperType=getI18nValue('服务升级');break;
		case '99':OperType=getI18nValue('催单');break;
		default:break;
	}
	return OperType
  }; 
}}
{{#  layui.each(d.list, function(index, item){ }}
<div>
	<img src="{{ item.ICON }}" class="layui-nav-img"
		onerror="javascript:this.src='/cc-workbench/static/images/user-avatar-large.png'"/>
	{{ item.USER_ACC }}<br>
	<div style="margin-left: 40px;">
		<p class="layui-word-aux">{{ item.CREATE_TIME }}  {{getOperType(item.OPER_TYPE)}}</p>
		<p style="color: #666;padding: 0 5px!important;">
			<span  i18n-content="内容:" /><textarea readonly="readonly" class="layui-text" style="width:95%;
			height:70px;border: none;outline: none;vertical-align: top;">{{ item.CONTENT }}</textarea>
		</p>
	</div>
</div>
<hr>
{{#  }); }}
</script>
<script type="text/javascript">
	jQuery.namespace("orderInfo");
	jQuery.namespace("orderAdd");
	jQuery.namespace("attaMsg");
	var laydate;
	var laytpl;
	var layform;
	var element;
	var cascader;
	var menu = '${param.menu}';
	var isReadOnly = false;
	
	// 附件上传
	attaMsg.attachment = function () {
		var attaBusiId = $("#ID").val() || "";
		var busiType = "sorder";
		var requestType = "upload"
		popup.layerShow({
			type: 2,
			title: getI18nValue("附件管理"),
			offset: '20px',
			shadeClose: false,
			area: ['800px', '650px'],
			end:function(){}
		}, "/cc-base/servlet/attachment?action=attachment", { busiId: attaBusiId, requestType: requestType, busiType: busiType });
	}
	
	$(function(){
 		layui.use(['laydate', 'laytpl', 'form', 'element','cascader'], function(){
 			execI18n();
			laydate = layui.laydate;
			laytpl = layui.laytpl;
			layform = layui.form;
			element = layui.element;
			cascader = layui.cascader;
			
			//表单校验
			layform.verify({
				formVerify: function(value, item){ //value：表单的值、item：表单的DOM对象
					var flag=true;
			 	    var n=$(item),valType = $(n).data("rules"),info="";
			 	    value=value||'';
			 	    value=value.trim();
				    //if(valType == null || valType == '') return true;
				    if(valType){
				    	var infoTitle=$(n).data("title");
					    if(infoTitle===undefined) infoTitle="";
					    $.each(valType.split("|"), function(idx,rule) {
				 			if(rule=='required') {//对不能为空的文本框进行验证
				 	            if(value=='') {  
				 	             info=form.getWarnMessage(infoTitle,rule,'');
				 	             flag = false;
				 	         }
				 	        }else if(rule.indexOf("/")==0||rule.indexOf("^")==0){
				 	        	info=form.getWarnMessage(infoTitle,rule,'');
				 	        	flag= (new RegExp(rule)).test($(n).val());
				 	        }else if(value!=''){ //如果值不为空，则进行规则判断！
				 	        	if(rule.indexOf("minlength")==0){ //判断长度是否在最少范围内
					 				var items = rule.split("=");
					 				if(items.length==2){
					 					if($(n).val().length<items[1]){
					 						 info=form.getWarnMessage(infoTitle,items[0],items[1]);
					 						 flag = false;
					 					}
					 				}
					 			}else if(rule.indexOf("maxlength")==0){ //判断长度是否在最大的范围内
					 				var items = rule.split("=");
					 				if(items.length==2){
					 					if($(n).val().length>items[1]){
					 						info=form.getWarnMessage(infoTitle,items[0],items[1]);
					 						flag = false;
					 					}
					 				}
					 			}else if(rule.indexOf("match")==0){ //判断两个元素值是否相同
					 				var items = rule.split("=");
					 				if(items.length==2){
					 					if(value != $("#"+items[1]).val()){
					 						if(infoTitle!=''){
					 							info=infoTitle;
					 						}else{
					 							var itemMsg=$($g(items[1])).data("title");
					 							itemMsg=itemMsg===undefined?"前一项":itemMsg;
					 							info=form.getWarnMessage(infoTitle,items[0],itemMsg);	
					 						}
					 						 flag = false;
					 					}
					 				}
					 			}else{  //最后判断常规的规则是否满足
						 			if(!form.matchRule(rule,value)) {
						 				 info=form.getWarnMessage(infoTitle,rule,'');
							             flag = false;
							        }
					 			}
				 			}
				 		});
				 	    if(!flag){
				 	    	return info;
				 	    }
				    }
			 	},
			 	otherReq: function(value,item){
					var $ = layui.$;
					var verifyName=$(item).attr('name')
						, verifyType=$(item).attr('type')
						,formElem=$(item).parents('.layui-form')//获取当前所在的form元素，如果存在的话
						,verifyElem=formElem.find('input[name='+verifyName+']')//获取需要校验的元素
						,isTrue= verifyElem.is(':checked')//是否命中校验
						,focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
					if(!isTrue || !value){
						//定位焦点
						focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
						//对非输入框设置焦点
						focusElem.first().attr("tabIndex","1").css("outline","0").blur(function() {
						focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
						}).focus();
						return getI18nValue('不能为空!');
					}
				}
			});
			
			orderInfo.renderBaseInfoHtml();
			
			//监听提交
			layform.on('submit(submit1)', function(data){
			   orderInfo.updateAllData('submit')
			   return false;
			});
		});
	});
	orderAdd.temlInfCfg = {};//模板数据接口类型配置缓存
	orderInfo.renderBaseInfoHtml = function () {
		//data-mars="baseField.showInfo"
		ajax.daoCall({"params":{},"controls":["baseField.showInfo"]}, function (result){
			//渲染基本信息
			var showBase = result['baseField.showInfo']?result['baseField.showInfo']:{};
			if(showBase && showBase.showMap){
				var getTpl = $("#base-field-list").html()
				,view = $("#baseFieldList");
				laytpl(getTpl).render(showBase.showMap, function(html){
					view.html(html);
					//渲染表单数据
					orderInfo.render();
					//渲染样式
					layform.render();
					element.render();
					
				});
			}
		});
	}
	orderInfo.tempSaveBefore = function () {
		$('#ORDER_STATUS').siblings("div.layui-form-select").find('dl dd[lay-value=00]').click();
		return $("#submit1").click();
	}
	orderInfo.submitSaveBefore = function () {
		var status = $('#ORDER_STATUS').val();
		if(status == '00'){
			$('#ORDER_STATUS').siblings("div.layui-form-select").find('dl dd[lay-value=01]').click();
		}
		return $("#submit1").click();
	}
	orderInfo.closeSaveBefore = function () {
		$('#ORDER_STATUS').siblings("div.layui-form-select").find('dl dd[lay-value=05]').click();
		return $("#submit1").click();
	}
	
	orderInfo.orderData;
	orderInfo.render = function(){
		$("#easyform").render({success:function(result){
			execI18n();
			var orderData = result["order.orderInfo"].data;
			orderInfo.orderData = orderData;
			//设置接线员
			$("#CALL_OPERATOR").val(orderData.CREATE_USER_ACC);
			var ORDER_TYPE = orderData.ORDER_TYPE;
			var TEMPLATE_ID = orderData.TEMPLATE_ID;
			if(ORDER_TYPE){
				$("#TEMPLATE_ID").data("mars", "orderTemp.getAllTempDict('" + ORDER_TYPE + "')");
				$("#TEMPLATE_ID").render({
					success : function() {
						$("#TEMPLATE_ID").val(TEMPLATE_ID);
						layform.render('select');
					}
				});
			}
			
			requreLib.setplugs('select2',function(){
				$("#CARBON_COPY").select2({allowClear: true});
				var ccUsers = orderData.CARBON_COPY;
				if(ccUsers){
					$("#CARBON_COPY").val(ccUsers.split(",")).trigger("change");
				}
			});
			
			orderInfo.EX_VALUE = result["order.orderInfo"].EX_VALUE;
			//渲染模板字段
			orderInfo.renderTempFields(result["order.orderInfo"].fields, result["order.orderInfo"].EX_VALUE);
			layform.render();
			execI18n();
			element.render();
			orderInfo.addChangeEvent();
			
			//触发处理组选择事件，初始化处理人选项
			var CURR_ORDER_GROUP_ID = result["order.orderInfo"].data.CURR_ORDER_GROUP_ID;
			if(CURR_ORDER_GROUP_ID){
				var CURR_ORDER_GROUP_USER = result["order.orderInfo"].data.CURR_ORDER_GROUP_USER;
				var CURR_ORDER_GROUP_USER_NAME = result["order.orderInfo"].data.CURR_ORDER_GROUP_USER_NAME;
				$("#CURR_ORDER_GROUP_USER").val(CURR_ORDER_GROUP_USER).data("value", CURR_ORDER_GROUP_USER);
				$("#CURR_ORDER_GROUP_USER_NAME").val(CURR_ORDER_GROUP_USER_NAME);
				layui.event.call($("#CURR_ORDER_GROUP_ID").get(0), 'form', 'select(CURR_ORDER_GROUP_ID_CHANGE)',
						{elem:$("#CURR_ORDER_GROUP_ID").get(0), value:$("#CURR_ORDER_GROUP_ID").val()});
			}
			//触发省份选择，初始化城市
			$("#CITY").data("value", orderData.CITY);
			layui.event.call($("#PROVINCE").get(0), 'form', 'select(PROVINCE_CHANGE)',
					{elem:$("#PROVINCE").get(0), value:$("#PROVINCE").val()});
			
			//渲染来电时间
   			orderInfo.renderDateRange($("#CALL_TIME").get(0), 'datetime');
			
			//所有回复
			var allReContentInfo = result["order.allReContent"] ? result["order.allReContent"] : {};
			orderInfo.renderAllReContent(allReContentInfo.data);
			//操作历史
			var allReContentInfo = result["order.allOperHistory"] ? result["order.allOperHistory"] : {};
			orderInfo.renderOperHistory(allReContentInfo.data);
			
			//index页面需要的工单信息页面渲染完成后才能取到客户Id渲染客户相关信息
			/* if(parent && parent.renderIndexJsp){
				parent.renderIndexJsp();
			}else if(renderIndexJsp){
				renderIndexJsp();
			} */
		}});
	}
	//设置只读
	orderInfo.setReadonly = function (){
		$("#tempInfo_bom,#baseInfo").find("input,select,textarea").attr("readonly", "readonly")
			.attr("disabled", "disabled")
			.addClass("form-control");
	}
	
	//设置可改
	orderInfo.removeReadonly = function (){
		$("#tempInfo_bom,#baseInfo").find("input,select,textarea").removeAttr("readonly", "readonly")
			.removeAttr("disabled", "disabled")
			.removeClass("form-control");
	}
	
	//渲染模板字段
	orderInfo.renderTempFields = function (fields, values) {
		if(fields && fields.length > 0){
   			var tempFieldHtml = '';
   			//var bomStart = '<div class="layui-card-body layui-row ">',bomEnd = '</div>';
   			for(var index=0; index < fields.length; index++){
   				var item = fields[index];
   				tempFieldHtml += orderInfo.createTempFieldHtml(item.DATA_TYPE, item, values[item.EN_NAME]);
   			}
   			$("#tempInfo_bom").html($(tempFieldHtml));
   			$("#temp_car").show();
   			orderInfo.addTemlCascadeSelect();
   			//渲染级联select
   			if($("#tempInfo_bom").find('select[data-mars],.tree').length > 0){
   				$("#tempInfo_bom").render({
   	   				success : function(result) {
   	   					orderAdd.treeMap = {};//缓存级联对象，方便重新设值
   	   					//渲染树组件
			   			$("#tempInfo_bom input.tree").each(function (index, tree) {
			   				var dataMars = $(tree).data("mars")
			   				var caDate = result[dataMars] ? result[dataMars].tree : "";
				   			if(cascader){
				   				var cas = cascader.render({
				   					elem: tree,
				   					data: caDate,
				   					disabled: isReadOnly,
				   				 	filterable: !isReadOnly,
				   				 	clearable:  !isReadOnly
				   				});
				   				orderAdd.treeMap[$(tree).attr('name')] = cas;
				   			}
			    		});
   	   					layform.render('select');
   	   				}
   	   			});
   			}
   			
   			//渲染日期组件
   			$("#tempInfo_bom input.tempFieldDate").each(function (index, edata) {
   				orderInfo.renderDateRange(edata, 'date', $(edata).val());
    		});
   			
   			//渲染日期时间组件
   			$("#tempInfo_bom input.tempFieldTime").each(function (index, edata) {
   				orderInfo.renderDateRange(edata, 'datetime', $(edata).val());
    		});
		}
	}
	
	orderInfo.addTemlCascadeSelect = function () {
		//注册模板级联选择事假
		layform.on('select(cascadeP_CHANGE)', function(data){
			if(data.elem){
				var pid = data.elem.id //当前对象的id为级联子对象的pid
				var selects = $("#tempInfo_bom select[pid=" + pid + "]");
				if(selects.length > 0){
					$(selects).each(function(index, elem){
						$(elem).data("mars", "order.orderDict('" + data.value + "')");
						$(elem).render({
							success : function() {
								layform.render('select');
								layui.event.call(elem, 'form', 'select(cascadeP_CHANGE)', {elem:elem, value:elem.value});
							}
						});
					});
				}
			}
		});
	}
	
	//渲染时间空间
	orderInfo.renderDateRange = function (e, t, v){
		laydate.render({
			elem: e
			,type: t
			,value: v
			,trigger: 'click'
		});
	}
	//创建模板字段
	orderInfo.createTempFieldHtml = function (dataType, fieldInfo, orderValue) {
		//01-普通文本框、02-多行文本框、03-单选框、04-下拉框、05-级联父、06-级联子、07-日期、08-时间'
		if(!orderValue){
			orderValue = "";
		}
		var width = 4;
		if(dataType == '02'){
			//文本框独占一行
			width = 12;
		}
		var isRequired = fieldInfo.IS_REQUIRED && fieldInfo.IS_REQUIRED == 'Y';
		var requiredClass = "";
		var requiredRule = "";
		if(isRequired) {
			requiredClass = "required";
			requiredRule = "required";
		}
		var html = '<div class="layui-col-xs' + width + ' layui-col-sm' + width + ' layui-col-md' + width + '">';
			html += '<label class="layui-form-label ' + requiredClass + '">' + fieldInfo.CN_NAME + '</label>';
			html += '<div class="layui-input-block">';
		//获取值
		var value = orderInfo.parseDefaultValue(fieldInfo.FIELD_DESC, dataType);
		var maxLength = orderInfo.parseMaxLength(fieldInfo.FIELD_DESC);
		//普通文本框 单选框 日期 时间 使用input
		if(dataType == '01'){
			html += '<input type="text" name="' + fieldInfo.EN_NAME + '"'
			+ ' lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=' + maxLength + joinRule('|', requiredRule) + '"'
			+ ' value="' + orderValue + '" autocomplete="off" class="layui-input">';
		}if(dataType == '02'){
			html += '<textarea name="' + fieldInfo.EN_NAME + '"'
			+ ' lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=' + maxLength + joinRule('|', requiredRule) + '"'
			+ ' class="layui-textarea" rows="3" style="margin-bottom:5px;min-height: 38px;">' + orderValue + '</textarea>';
		}else if(dataType == '03'){
			html += orderInfo.createRadio(value, fieldInfo.EN_NAME, orderValue, isRequired);
		}else if(dataType == '07'){
			//layui需要单独渲染日期组件，添加class tempFieldDate 标识
			html += '<input type="text" name="' + fieldInfo.EN_NAME + '" value="'
			+ orderValue + '" autocomplete="off" lay-verType="tips" lay-verify="formVerify" class="layui-input tempFieldDate" data-rules="' + joinRule('', requiredRule) + '">';
		}else if(dataType == '08'){
			//layui需要单独渲染时间组件，添加class tempFieldtime 标识
			html += '<input type="text" name="' + fieldInfo.EN_NAME + '" value="'
			+ orderValue + '" autocomplete="off" lay-verType="tips" lay-verify="formVerify" class="layui-input tempFieldTime"  data-rules="' + joinRule('', requiredRule) + '">';
		}else if(dataType == '04'){
			html += '<select name="' + fieldInfo.EN_NAME + '" id="' + fieldInfo.EN_NAME + '" lay-verType="tips" lay-verify="formVerify" data-rules="' + joinRule('', requiredRule) + '" lay-search>';
			html += '<option value=""  i18n-content="请选择"></option>';
			html += orderInfo.createSelectOption(value, orderValue);
			html += '</select>';
		}else if("05" == dataType){
			var dataValue = $.parseJSON(value);
			html += '<select class="form-control input-sm" lay-verType="tips" lay-verify="formVerify" name="' + fieldInfo.EN_NAME+ '" data-value="' + orderValue + '" data-rules="' + joinRule('', requiredRule) + '" ';
			//添加layui lay-filter="" 作为选择事件
			html += ' lay-filter="cascadeP_CHANGE" ';
			html += ' data-mars="order.orderDictP(\'' + dataValue.code + '\')" id="' + dataValue.id + '" lay-search> ';
			html += '<option value=""  i18n-content="请选择"></option>';
			html += '</select>';
		}else if("06" == dataType){
			var dataValue = $.parseJSON(value);
			html += '<select class="form-control input-sm" lay-verType="tips" lay-verify="formVerify" pid="' + dataValue.pId + '" name="' + fieldInfo.EN_NAME+ '" data-value="' + orderValue + '" data-rules="' + joinRule('', requiredRule) + '" ';
			html += ' data-mars="order.orderDictsC(\'' + orderValue + '\')" id="' + dataValue.id + '" lay-search> ';
			html += '<option value=""  i18n-content="请选择"></option>';
			html += '</select>';
		}else if(dataType == '09'){
			//layui需要单独渲染树组件，添加class 标识
			html += '<input type="text" name="' + fieldInfo.EN_NAME + '" value="' + orderValue + '"'
			+ ' lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=' + maxLength + joinRule('|', requiredRule) + '"'
			+ ' data-mars="common.getTree(' + value + ')" class="tree">';
		}else if(dataType == '10'){
			//layui需要单独渲染树组件，添加class 标识
			html += '<input type="text" name="' + fieldInfo.EN_NAME + '"'
			+ ' lay-verType="tips" lay-verify="formVerify" data-rules="maxlength=' + maxLength + joinRule('|', requiredRule) + '"'
			+ ' value="' + orderValue + '" autocomplete="off" class="layui-input dataInf dataInf_' + fieldInfo.EN_NAME + '">';
			
			html += '<label style="height: 36px;line-height: 36px"> '
				 + '<button type="button" onclick="getInfData(\'' + fieldInfo.EN_NAME + '\')" '
				 + 'class="layui-btn layui-btn-sm layui-btn-normal findBtn-icon">'
				 + '<i class="glyphicon glyphicon-search"></i></button> </label>'
			orderAdd.temlInfCfg[fieldInfo.EN_NAME] = value;
		}
		html += '</div></div>';
		return html;
	}
	//生成单选内容
	orderInfo.createRadio = function(values, name, orderValue, isRequired){
		var radioHtml = '';
		var requiredRuel = "";
		if(isRequired){
			requiredRuel = ' lay-verType="tips" lay-verify="otherReq" ';
		}
		//单选框需要遍历value数组创建
		if(values && values.length > 0){
			$.each(values, function (index, item) {
				var checked = item == orderValue ? "checked" : "";
				radioHtml += '<input type="radio" name="' + name
				+ '" value="' + item + '" title="' + item + '" ' + checked + ' ' + requiredRuel + ' >';
   			});
		}
		return radioHtml;
	}
	//生成下拉内容
	orderInfo.createSelectOption = function(optionArr, orderValue){
		var optionHtml = '';
		if(optionArr && optionArr.length > 0){
			$.each(optionArr, function (index, item) {
				var selected = item == orderValue ? "selected" : "";
				optionHtml += '<option value="' + item + '" ' + selected + ' >' + item + '</option>';
   			});
		}
		return optionHtml;
	}
	//解析模板字段默认值
	orderInfo.parseDefaultValue = function(FIELD_DESC, DATA_TYPE){
		var value = '';
		var jsonDesc = $.parseJSON(FIELD_DESC) || '';
		//单选框和下拉框是数组
		if(jsonDesc && jsonDesc.defaultValue && (DATA_TYPE == '03' || DATA_TYPE == '04'|| DATA_TYPE == '10')){
			value = $.parseJSON(jsonDesc.defaultValue) || '';
		}else{
			value = jsonDesc.defaultValue;
		}
		return value;
	}
	
	orderInfo.parseMaxLength = function(FIELD_DESC){
		var maxLength = '10';
		var jsonDesc = $.parseJSON(FIELD_DESC) || '';
		//单选框和下拉框是数组
		if(jsonDesc && jsonDesc.LENGTH){
			maxLength = $.parseJSON(jsonDesc.LENGTH) || '10';
		}
		return maxLength;
	}
	
	//模板内容显示隐藏
	orderInfo.isShowTempInfo = false;
	orderInfo.showOrHideTempInfo = function () {
		//showTempInfo_icon tempInfo_bom
		if(!orderInfo.isShowTempInfo){
			$("#showTempInfo_icon").removeClass("layui-icon-up").addClass("layui-icon-down");
			$("#tempInfo_bom").show();
		}else{
			$("#showTempInfo_icon").removeClass("layui-icon-down").addClass("layui-icon-up");
			$("#tempInfo_bom").hide();
		}
		orderInfo.isShowTempInfo = !orderInfo.isShowTempInfo;
	}
	orderInfo.addChangeEvent = function () {
		//选择工单类型后查找可用工单模板
		layform.on('select(ORDER_TYPE_CHANGE)', function(data){
			orderAdd.temlInfCfg = {};//模板数据接口类型配置缓存
			if(data.value){
				$("#TEMPLATE_ID").data("mars", "orderTemp.getAllTempDict('" + data.value + "')");
				$("#TEMPLATE_ID").render({
					success : function() {
						//清空旧模板字段
			   			$("#tempInfo_bom").html("");
			   			$("#temp_car").hide();
						layform.render('select');
					}
				});
			}
		});
		
		//选择工单模板后显示对应模板字段
		layform.on('select(EMPLATE_ID_CHANGE)', function(data){
			orderAdd.temlInfCfg = {};//模板数据接口类型配置缓存
			if(data.value){
				$("#temp_car").show();
				ajax.daoCall({"params":{pk: data.value, FIELD_ENABLE_STATUS: "01"},"controls":[
			   		 "orderTemp.record"]}, function (result){
			   		 var record = result['orderTemp.record'] ? result['orderTemp.record'] : {};
			   		 if(record && record.fields && record.fields.length > 0){
			   			var fields = record.fields;
			   			var tempFieldHtml = '';
			   			//var bomStart = '<div class="layui-card-body layui-row ">',bomEnd = '</div>';
			   			for(var index=0; index < fields.length; index++){
			   				var item = fields[index];
			   				if(orderInfo.orderData && orderInfo.orderData.TEMPLATE_ID == data.value){
			   					var exValue;
			   					if(orderInfo.EX_VALUE){
			   						exValue = orderInfo.EX_VALUE[item.EN_NAME]
			   					}
			   					tempFieldHtml += orderInfo.createTempFieldHtml(item.DATA_TYPE, item, exValue);
			   				}else{
			   					tempFieldHtml += orderInfo.createTempFieldHtml(item.DATA_TYPE, item);
			   				}
			   			}
			   			$("#tempInfo_bom").html($(tempFieldHtml));
			   			//渲染日期组件
			   			$("#tempInfo_bom input.tempFieldDate").each(function (index, edata) {
			   				orderInfo.renderDateRange(edata, 'date', $(edata).val());
			    		});
			   			
			   			//渲染日期时间组件
			   			$("#tempInfo_bom input.tempFieldTime").each(function (index, edata) {
			   				orderInfo.renderDateRange(edata, 'datetime');
			    		});
			   			
			   			orderInfo.addTemlCascadeSelect();
			   			//渲染级联select
			   			$("#tempInfo_bom").render({
			   				success : function(result) {
			   					orderAdd.treeMap = {};//缓存级联对象，方便重新设值
			   					//渲染树组件
					   			$("#tempInfo_bom input.tree").each(function (index, tree) {
					   				var dataMars = $(tree).data("mars")
					   				var caDate = result[dataMars] ? result[dataMars].tree : "";
						   			if(cascader){
						   				var cas = cascader.render({
						   					elem: tree,
						   					data: caDate,
						   				    filterable: true,
						   				 	clearable:true,
						   				 	trigger:'click'
						   				});
						   				orderAdd.treeMap[$(tree).attr('name')] = cas;
						   			}
					    		});
					   			layform.render();
			   					//layform.render('select');
			   				}
			   			});
			   			
			   			layform.render();
						element.render();
			   		 }else{
						$("#tempInfo_bom").html("");
					 }
			   	});
			}else{
				$("#tempInfo_bom").html("");
				$("#temp_car").hide();
			}
		});
		
		//选择省份
		layform.on('select(PROVINCE_CHANGE)', function(data){
			var value = data.value ? data.value : "null";
			if(value){
				$("#CITY").data("mars", "common.cityChooseSel('" + value + "')");
				$("#CITY").render({
					success : function() {
						layform.render('select');
						/* if(isReadOnly){
							orderInfo.setReadonly();
						}else{
							orderInfo.removeReadonly();
						} */
					}
				});
			}
		});
		
		//选择受理组后查找受理组员
		layform.on('select(CURR_ORDER_GROUP_ID_CHANGE)', function(data){
			var value = data.value ? data.value : "null";
			if(value){
				$("#CURR_ORDER_GROUP_USER").data("mars", "workorderGroup.allGroupUserList('" + value + "')");
				$("#CURR_ORDER_GROUP_USER").render({
					success : function() {
						layform.render('select');
						$("#CURR_ORDER_GROUP_USER").data("value", "");
					}
				});
			}
		});
		//选择受理组员后设置表单中组员的名字
		layform.on('select(CURR_ORDER_GROUP_USER_CHANGE)', function(data){
			if(data.elem){
				var username = $(data.elem).find("option:selected").text();
				$("#CURR_ORDER_GROUP_USER_NAME").val(username);
			}
		});
		
		//选择状态
		layform.on('select(ORDER_STATUS_CHANGE)', function(data){
			var assignRule = $("#ASSIGN_RULE").val();
			orderAdd.setGroupUserRequired(data.value, assignRule);
		});
		
		//流转规则
		layform.on('select(ASSIGN_RULE_CHANGE)', function(data){
			var assign = $("#ASSIGN_RULE").val();
			var form = layui.form;
			if ('02' == assign) {
				$("#CURR_ORDER_GROUP_ID option:first").prop('selected', true);
				$("#CURR_ORDER_GROUP_USER option:first").prop('selected', true);
				$("#CURR_ORDER_GROUP_ID").prop('disabled', true);
				$("#CURR_ORDER_GROUP_USER").prop('disabled', true);
			} else {
				$("#CURR_ORDER_GROUP_ID").prop('disabled', false);
				$("#CURR_ORDER_GROUP_USER").prop('disabled', false);
			}
			form.render();
			var status = $("#ORDER_STATUS").val();
			orderAdd.setGroupUserRequired(status, data.value);
		});
	}
	
	orderAdd.setGroupUserRequired = function (status, assignRule) {
		if((status == '02' || status == '03') && assignRule == '01'){
			//状态为待处理和处理中时必须填写受理人
			$("#ORDER_GROUP_ID_DIV label").addClass("required")
			$("#ORDER_GROUP_ID_DIV select").attr("lay-verify", "required");
			$("#GROUP_USER_DIV label").addClass("required")
			$("#GROUP_USER_DIV select").attr("lay-verify", "required");
		}else{
			//受理组，受理人不是必填
			$("#ORDER_GROUP_ID_DIV label").removeClass("required")
			$("#ORDER_GROUP_ID_DIV select").removeAttr("lay-verify");
			$("#GROUP_USER_DIV label").removeClass("required")
			$("#GROUP_USER_DIV select").removeAttr("lay-verify");
		}
	}
	
	orderInfo.updateAllData = function(oper){
		if(form.validate("#easyform")){
			var data = form.getJSONObject("#easyform");
			data.oper = oper;
			 ajax.remoteCall("${ctxPath}/servlet/order?action=UpdateAllData", data, function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			); 
		}
	}
	
	orderInfo.receiveOrder = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/order?action=Receive", data, function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					layer.closeAll();
					window.location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		}); 
	}
	orderInfo.urgentOrder = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/order?action=Urgent", data, function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					layer.closeAll();
					window.location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		}); 
	}
	
	orderInfo.serviceUpgrade = function () {
		var orderId = $("#ID").val();
		var orderNo = $("#ORDER_NO").val();
		popup.layerShow({type:1,title:getI18nValue('服务升级'),offset:'120px',area:['500px;','350px;']
				,scrollbar: false
				,end:function () {
					layer.closeAll();
				}
			}
			,"${ctxPath}/pages/sorder/order-urgent-info.jsp"
			,{orderId:orderId,orderNo:orderNo}
		);
	}
	
	orderInfo.closeOrder = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/order?action=Close", data, function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					layer.closeAll();
					window.location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		}); 
	}
	
	orderInfo.deleteOrder = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/order?action=Delete", data, function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					layer.closeAll();
					window.location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		}); 
	}
	
	//渲染所有回复
	orderInfo.renderAllReContent = function (edata) {
		if(edata){
			var data = {
			  "title":""
			  ,"list": edata
			}
			var getTpl = $("#history-list-temp").html();
			laytpl(getTpl).render(data, function(html){
				$("#ALL_RECONTENT").html(html);
				
				$('#ALL_RECONTENT textarea').autoHeight();    
			});
		}
	}
	//oper-history-list-temp
	//渲染操作历史
	orderInfo.renderOperHistory = function (edata) {
		if(edata){
			var data = {
			  "title":""
			  ,"list": edata
			}
			var getTpl = $("#oper-history-list-temp").html();
			laytpl(getTpl).render(data, function(html){
				$("#OPER_HISTORY").html(html);
				
				$('#OPER_HISTORY textarea').autoHeight();
			});
		}
	}
	
	var showDataInfo = true; 
	orderAdd.addCustomer = function (){
		var caller = '${param.caller }';
		ajax.remoteCall("/cc-custmgr/webcall?action=custTable.customerAddDataInit", {}, function(result) { 
			if(result.joData && result.joData.ID){
				popup.layerShow({
					type : 2,
					title : getI18nValue('添加客户'),
					offset : '20px',
					area : [ '1000px', '500px' ],
					end: function(){
					}
				}, "/cc-custmgr/pages/custmgrConfig/showDataInfoNopadding.jsp", 
					{tableId:result.joData.ID, tableName:result.joData.TBL_NAME, caller:caller});
			}
		})
	}
	
	orderAdd.selectCustomer = function (id, phone){
		popup.layerShow({
			type : 2,
			title : getI18nValue('选择客户'),
			offset : '20px',
			area : [ '800px', '600px' ],
			end: function(){
			}
		}, "/cc-custmgr/pages/custmgrConfig/customerEasyList.jsp", 
			{});
	}
	orderAdd.clearCustomer = function () {
		$("#CUSTOMER_NAME").val("");
		$("#CUSTOMER_MOBILE").val("");
		setCustomer();
	}
	function setCustomer (id, phone){
		var oladVal = $("#CUSTOMER_ID").val();
		id = id ? id : "";
		phone = phone ? phone : "";
		$("#CUSTOMER_ID").val(id);
		//$("#caller").val(phone);
		//$("#CUSTOMER_NAME").val(phone);
		//$("#CUSTOMER_MOBILE").val(phone);
		if(oladVal != id){
			$("#customerId").val(id);
			if(oladVal != id && parent && parent.orderIndex){
				//$("#customerId").val(id);
				if(parent.orderIndex.paramObj){
					parent.orderIndex.paramObj.customerId = id
					parent.orderIndex.paramObj.telNum = phone;
					if(parent.orderIndex.reSetSidebarSrc){
						parent.orderIndex.reSetSidebarSrc();
					}
				}
			}
		}
	}
	
	window.onresize = function () {
		$('#ALL_RECONTENT textarea').autoHeight();
		$('#OPER_HISTORY textarea').autoHeight();
	}
	
	function getInfData(enName) {
		var val = $(".dataInf_" + enName).val();
		var cfg = orderAdd.temlInfCfg[enName];
		getInfResult(val, cfg, function(result){
			console.log(result);
			if(result && result.state == 1){
				var data = result.data || {};
				for(var key in data){
					var value = data[key] || "";
					var ele = $("[name=" + key + "]");
					if(ele.is("input") && ele.attr("type") == 'radio' && ele.attr("value") == value){
						//单选框设置选中
						ele.attr('checked', '');
					}else if(ele.is("input") && ele.hasClass('tree')){
						ele.val(value);
						var treeObj = orderAdd.treeMap[key];
						if(treeObj && treeObj.setValue){
							treeObj.setValue(value);
						}
					}else if(ele.is("select")){
						ele.siblings("div.layui-form-select").find('dl dd[lay-value=' + value + ']').click();
					}else{
						ele.val(value);
					}
				}
				layform.render();
				element.render();
			}else{
	   			layer.alert(getI18nValue("查询出错"), {icon: 5});
	   		}
		});
	}
</script>
</EasyTag:override>

<%@ include file="/pages/common/layout_list.jsp" %>