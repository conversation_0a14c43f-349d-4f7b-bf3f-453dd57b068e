package com.yunqu.cc.sorder.product.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.sorder.base.AppDaoContext;

/**
 * 产品查询（仅用在海康演示，不提交）
 * <AUTHOR>
 *
 */
@WebObject(name="product")
public class ProductDao extends AppDaoContext{

	/**
	 * 产品查询
	 * @return
	 */
	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		EasySQL sql = new EasySQL("SELECT * FROM " + getTableName("C_PRODUCT"));
		sql.append("WHERE 1=1");
		sql.appendRLike(param.getString("MODEL"), " AND MODEL like ?");
		sql.appendRLike(param.getString("NAME"), " AND NAME like ?");
		//sql.append(getEntId(), "AND ENT_ID=?");
		//sql.append(getBusiOrderId(), "AND BUSI_ORDER_ID=?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
