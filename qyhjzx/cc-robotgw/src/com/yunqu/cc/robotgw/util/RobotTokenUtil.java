package com.yunqu.cc.robotgw.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.MacUtil;
import com.yunqu.cc.robotgw.base.CommonLogger;
import com.yunqu.cc.robotgw.base.QueryFactory;
import com.yunqu.cc.robotgw.job.JobMgr;
import com.yunqu.cc.robotgw.model.ChannelCcData;
import com.yunqu.cc.robotgw.model.RobotToken;
import com.yunqu.cc.robotgw.service.robot.RobotService;
import com.yunqu.cc.robotgw.service.robot.factory.RobotServiceFactory;

public class RobotTokenUtil {
	
	private static Logger logger = CommonLogger.logger;
	
	/**
	 * 存放正式使用的机器人token值
	 * key：机器人appid
	 */
//	private static Map<String,RobotToken> tokenMap = new HashMap<String,RobotToken>();
	
	/**
	 * 存放临时使用的机器人token值，获取token时使用
	 * key：机器人appid
	 */
	private static Map<String,RobotToken> tempTokenMap = new HashMap<String,RobotToken>();
	
	/**
	 * 根据渠道key获取该渠道对应的机器人的token值
	 * @param channelKey
	 * @return
	 */
	public static String getTokenByChannelKey(String channelKey){
		ChannelCcData ccData = YcMideaUtil.findChannelCcDataFromCache(channelKey);
		if(ccData==null || StringUtils.isBlank(ccData.getRobotType())){
			logger.error("渠道["+channelKey+"]获取token失败, ccData里机器人渠道信息为空.");
			return null;
		}
		
		String robotAppId = ccData.getRobotAppId();
		String key = robotAppId;
		RobotToken robotToken = getToken(key);
		if(robotToken!=null){
			return robotToken.getToken();
		}
		
		logger.info("渠道["+channelKey+"]的机器人token为空，重新获取一次.");
		robotToken = reqToken(channelKey);
		if(robotToken!=null){
			return robotToken.getToken();
		}
		return null;
	}
	
	

	/**
	 * 根据机器人appId获取token值
	 * @param robotAppId
	 * @return
	 */
	public static String getTokenByRobotAppId(String robotAppId){
		String key = robotAppId;
		RobotToken robotToken = getToken(key);
		if(robotToken!=null){
			return robotToken.getToken();
		}
		return null;
	}

	/**
	 * 根据渠道信息，找渠道对应的机器人请求获取token值
	 * @param channelKey
	 * @return
	 */
	public static RobotToken reqToken(String channelKey) {
		String flagKey = "robotgw-reqToken-"+channelKey;
		String flag = CacheUtil.get(flagKey);
		
		if(StringUtils.isNotBlank(flag)){
			logger.info("渠道["+channelKey+"]的获取token正在处理中，此处不获取.");
			return null;
		}
		CacheUtil.put(flagKey, "Y",120);
		
		try {
			JSONObject json = new JSONObject();
			json.put("serialId", IDGenerator.getDefaultNUMID());
			json.put("channelKey", channelKey);
			json.put("schema", "ycmain");
			
			ChannelCcData ccData = YcMideaUtil.findChannelCcDataFromCache(channelKey);
			if(ccData==null || StringUtils.isBlank(ccData.getRobotType())){
				logger.error("渠道["+channelKey+"]获取token失败, ccData里机器人渠道信息不正确.");
				return null;
			}
			
			String robotIp = ccData.getRobotIp();
			String robotAppId = ccData.getRobotAppId();
			String key = robotAppId;
			RobotToken robotToken = tempTokenMap.get(key);
			
			logger.info("渠道["+channelKey+"] 开始获取token, robotAppId = "+robotAppId+", robotIp = " + robotIp);
			
			//从临时map里获取token，不存在时则新增，存在时判断有效期，如果未失效则直接返回
			if(robotToken==null){
				robotToken = new RobotToken();
				robotToken.setChannelKey(channelKey);
				robotToken.setRobotAppId(robotAppId);
				robotToken.setRobotIp(robotIp);
				robotToken.setTokenMachine(MacUtil.getLocalMacName());
			}else{
				long reqTimestamp = robotToken.getReqTimestamp();
				if(System.currentTimeMillis()-reqTimestamp < 1000*60*5){ //有效期小于5分钟，则不重新获取
					logger.info("渠道["+channelKey+"]获取token，token值未失效，直接返回.  appId:["+robotToken.getRobotAppId()+"] robotIp:["+robotToken.getRobotIp()+"],token: "+robotToken.getToken());
					return robotToken;
				}
			}
			
			//通过接口查询token值
			RobotService service = RobotServiceFactory.getService(ccData.getRobotType());
			if(service==null){
				logger.error("渠道["+channelKey+"]获取token失败, 无法找到对应的机器人处理类:"+ccData.getRobotType());
				return null;
			}
			if(ServerContext.isDebug()){
				logger.info("渠道["+channelKey+"]获取token,请求 >> "+json.toJSONString());
			}
			JSONObject result = service.getToken(json);
			if(ServerContext.isDebug()){
				logger.info("渠道["+channelKey+"]获取token,响应 << "+result);
			}
			
			//存在token则写入缓存
			if(result!=null && StringUtils.isNotBlank(result.getString("token"))){
				String token = result.getString("token");
				robotToken.setReqTimestamp(System.currentTimeMillis());
				robotToken.setToken(token);
				robotToken.setReqTime(DateUtil.getCurrentDateStr());
				
				tempTokenMap.put(key, robotToken);
				
				//同步更新到正式的token缓存
				addToken(key,robotToken);
				
				
				return robotToken;
			}else{
				logger.error("渠道["+channelKey+"]获取token失败, 机器人token处理结果里未返回token:"+result);
			}
		} catch (Exception e) {
			logger.error("渠道["+channelKey+"]获取token异常:"+e.getMessage(),e);
		}finally {
			CacheUtil.delete(flagKey);
		}
		
		return null;
	}

	/**
	 * 重新获取所有的token
	 */
	public static void reqAllToken() {
		long start = System.currentTimeMillis();
		try {
			logger.info("------开始获取token...------");
			
			tempTokenMap.clear();
			logger.info("清空临时token存储map.");
			
			EasyQuery query = QueryFactory.getWriteQuery();
			
			List<EasyRow>  list = query.queryForList(" SELECT * FROM  CC_CHANNEL WHERE CHANNEL_STATE=? AND CC_DATA IS NOT NULL", 0);
			
			logger.info(" 本次需要获取token的渠道数量:【"+ (list==null ? 0 : list.size())+"】");
			
			if(CommonUtil.listIsNull(list)){
				logger.error("系统里没有配置任何渠道,无法根据渠道读取配置信息、获取token...");
				return;
			}
			
			int success = 0 ;
			
			//逐个渠道遍历
			for(int i = 0 ; i<list.size();i++){
				EasyRow row = list.get(i);
				String channelKey = row.getColumnValue("CHANNEL_KEY");
				String ccDataStr = row.getColumnValue("CC_DATA");

				logger.info("【"+(i+1)+"、】【"+channelKey+"】获取token, channelKey:"+channelKey+",ccData:"+ccDataStr);
				
				RobotToken token = RobotTokenUtil.reqToken(channelKey);
				
				if(token!=null){
					success++;
					logger.info("["+channelKey+"]获取token成功,token:"+token.getToken()+",appId:"+token.getRobotAppId());
				}else{
					logger.info("["+channelKey+"]获取token失败.");
				}
				
			}
			
			logger.info("结束获取token,成功量:【"+success+"】,失败量:【"+ (list.size()-success)+"】");
			
		} catch (Exception e) {
			logger.error("执行获取token任务报错:"+e.getMessage(),e);
		}finally {
			
			logger.info("缓存中token总量:"+tempTokenMap.size());
			Set<String> keys = tempTokenMap.keySet();
			for(String key : keys){
				RobotToken robotToken = tempTokenMap.get(key);
				logger.info("机器人token信息, appId:["+robotToken.getRobotAppId()+"] robotIp:["+robotToken.getRobotIp()+"],token: "+robotToken.getToken());
			}
			
			long end = System.currentTimeMillis();
			
			logger.info("------结束获取token...耗时["+((end-start)/1000)+"]秒------");
			
			tempTokenMap.clear();
		}
		
	}
	

	/**
	 * 将获取到的token写入到缓存中
	 * @param robotAppId
	 * @param robotToken
	 */
	private static void addToken(String robotAppId, RobotToken robotToken) {
//		tokenMap.put(key, robotToken);
		robotAppId = "cc-robotgw-token-"+robotAppId;
		
		CacheUtil.put(robotAppId, JsonUtil.toJSONString(robotToken),7200);
		
		logger.info("机器人token加入缓存,channelKey:["+robotToken.getChannelKey()+"], appId:["+robotToken.getRobotAppId()+"] robotIp:["+robotToken.getRobotIp()+"],token: "+robotToken.getToken());
	}

	/**
	 * 从缓存里读取token，返回token对象
	 * @param key
	 * @return
	 */
	private static RobotToken getToken(String key) {
//		tokenMap.get(key);
		
		key = "cc-robotgw-token-"+key;
		
		String tk = CacheUtil.get(key);
		if(StringUtils.isNotBlank(tk)){
			return (RobotToken) JsonUtil.parseObject(tk, RobotToken.class);
		}
		return null;
	}
}
