(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f290f8e"],{"14d9":function(t,e,i){"use strict";var a=i("23e7"),n=i("7b0b"),o=i("07fa"),s=i("3a34"),l=i("3511"),r=i("d039"),c=r((function(){return 4294967297!==[].push.call({length:4294967296},1)})),p=!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}();a({target:"Array",proto:!0,arity:1,forced:c||p},{push:function(t){var e=n(this),i=o(e),a=arguments.length;l(i+a);for(var r=0;r<a;r++)e[i]=arguments[r],i++;return s(e,i),i}})},2708:function(t,e,i){"use strict";var a=i("365c");e["a"]={data(){return{state:{0:"待启动",1:"启动中",2:"已暂停",3:"已完成"}}},methods:{getDict(t,e,i){console.log(t,e);let n={data:JSON.stringify({params:t,controls:e})};Object(a["N"])(n).then(t=>{i&&i(t)})}}}},"2afb":function(t,e,i){"use strict";i("d314")},3511:function(t,e){var i=TypeError,a=9007199254740991;t.exports=function(t){if(t>a)throw i("Maximum allowed index exceeded");return t}},"3a34":function(t,e,i){"use strict";var a=i("83ab"),n=i("e8b5"),o=TypeError,s=Object.getOwnPropertyDescriptor,l=a&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=l?function(t,e){if(n(t)&&!s(t,"length").writable)throw o("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},d314:function(t,e,i){},d97e:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"page",attrs:{"element-loading-text":"加载中"}},[e("div",{staticClass:"header fullX"},[t._v(" "+t._s(t.$t("质检组分析"))+" "),e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.handleSearch}},[t._v(t._s(t.$t("查询")))]),e("el-select",{attrs:{size:"mini"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}},[e("el-option",{key:"1",attrs:{label:t.$t("近一个月"),value:1}}),e("el-option",{key:"3",attrs:{label:t.$t("近三个月"),value:3}}),e("el-option",{key:"6",attrs:{label:t.$t("近六个月"),value:6}})],1),e("el-date-picker",{attrs:{type:"daterange",size:"mini",clearable:!1,"range-separator":"-","start-placeholder":"","end-placeholder":"","value-format":"yyyy-MM-dd"},model:{value:t.date,callback:function(e){t.date=e},expression:"date"}})],1),e("div",{staticClass:"yq-card"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("质检员所在组的数量TOP10")))]),e("div",{ref:"InspectorTop10",staticClass:"content",attrs:{id:"InspectorTop10-chat"}}),e("el-empty",{staticStyle:{height:"70%"},attrs:{description:"暂无数据",id:"InspectorTop10-empty"}})],1),e("div",{staticClass:"yq-card"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("坐席所在组的数量TOP10")))]),e("div",{ref:"AgentTop10",staticClass:"content",attrs:{id:"AgentTop10-chat"}}),e("el-empty",{staticStyle:{height:"70%"},attrs:{description:"暂无数据",id:"AgentTop10-empty"}})],1),e("div",{staticClass:"yq-card"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("渠道类型数量")))]),e("div",{ref:"channelQualitySum",staticClass:"content",attrs:{id:"channelQualitySum-chat"}}),e("el-empty",{staticStyle:{height:"70%"},attrs:{description:"暂无数据",id:"channelQualitySum-empty"}})],1),e("div",{staticClass:"fullX",staticStyle:{display:"flex","justify-content":"space-between"}},[e("div",{staticClass:"yq-card",staticStyle:{width:"100%"}},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("质检员质检数量top10")))]),e("div",{ref:"InspectorQcTop10",staticClass:"content",attrs:{id:"InspectorQcTop10-chat"}}),e("el-empty",{staticStyle:{height:"70%"},attrs:{description:"暂无数据",id:"InspectorQcTop10-empty"}})],1),e("div",{staticClass:"yq-card",staticStyle:{width:"100%"}},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("坐席质检数量top10")))]),e("div",{ref:"AgentQcTop10",staticClass:"content",attrs:{id:"AgentQcTop10-chat"}}),e("el-empty",{staticStyle:{height:"70%"},attrs:{description:"暂无数据",id:"AgentQcTop10-empty"}})],1)]),e("div",{staticClass:"yq-card fullX"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("质检组数量统计")))]),e("div",{ref:"GroupStatic",staticClass:"content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%",height:"28vh",overflow:"auto","margin-bottom":"10px"},attrs:{data:t.tableData,stripe:"",fit:"",size:"mini","element-loading-text":"加载中"}},[e("el-table-column",{attrs:{type:"index",label:t.$t("序号"),minWidth:"60",align:"center"}}),e("el-table-column",{attrs:{prop:"EXAM_GROUP_NAME",label:t.$t("质检组名称"),minWidth:"240",align:"center"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("span",{staticClass:"linking",on:{click:function(e){return t.toSingle(i.row.EXAM_GROUP_ID,i.row.EXAM_GROUP_NAME)}}},[t._v(t._s(i.row.EXAM_GROUP_NAME))])]}}])}),e("el-table-column",{attrs:{prop:"CHANNEL_TYPE",label:t.$t("类型"),minWidth:"240",align:"center"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("yq-tag",{attrs:{background:t.setColor(i.row.CHANNEL_TYPE)}},[t._v(t._s(t.QC_CHANNEL_TYPE[i.row.CHANNEL_TYPE]||i.row.CHANNEL_TYPE))])]}}])}),e("el-table-column",{attrs:{prop:"USER_COUNT",label:t.$t("质检员数量"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENT_COUNT",label:t.$t("坐席数量"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"QC_TOTAL",label:t.$t("抽检数"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"QC_COUNT",label:t.$t("人工质检数"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"AVG_SCORE",label:t.$t("质检平均分"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"RG_RECONSIDER_COUNT",label:t.$t("申诉量"),minWidth:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"RG_RECONSIDER_SUCC_COUNT",label:t.$t("申诉通过量"),minWidth:"120",align:"center"}})],1),e("el-pagination",{attrs:{background:"","current-page":t.page,"page-sizes":[10,20,50,100],"page-size":t.size,layout:"prev, pager, next, sizes",total:t.totalRow},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])])},n=[],o=(i("14d9"),i("2708")),s=i("365c"),l={mixins:[o["a"]],data(){return{loading:!1,tableLoading:!1,dateRange:3,date:"",InspectorTop10:"",InspectorTop10List:[],AgentTop10:"",AgentTop10List:[],channelQualitySum:"",channelQualitySumList:[],InspectorQcTop10:"",InspectorQcTop10List:[],AgentQcTop10:"",AgentQcTop10List:[],tableData:[],page:1,size:20,totalRow:0}},watch:{dateRange:{handler(t){this.date=[this.$dayjs().subtract(t,"month").format("YYYY-MM-DD"),this.$dayjs().format("YYYY-MM-DD")]}}},methods:{setColor(t){switch(t){case"1":return"#4aa6ff";case"2":return"#14d1d3";case"3":return"#f8c004";case"4":return"#19cd6b";case"9":return"#f8595b";default:break}},handleSizeChange(t){this.size=t,this.getGroupStat()},handleCurrentChange(t){this.page=t,this.getGroupStat()},handleSearch(){this.loading=!0,this.getAgentTop(),this.getGroupByType(),this.getInspectorTop(),this.getInspectorQcTop(),this.getAgentQcTop10(),this.getGroupStat()},getInspectorTop(){Object(s["n"])().then(t=>{this.InspectorTop10List=t.data,this.initInspectorTop10Chart()})},getAgentTop(){Object(s["d"])().then(t=>{this.AgentTop10List=t.data,this.initAgentTop10Chart(),setTimeout(()=>{this.loading=!1},500)})},getGroupByType(){Object(s["h"])().then(t=>{this.channelQualitySumList=t.data,this.channelQualitySumList.forEach(t=>{switch(Number(t.CHANNEL_TYPE)){case 1:t.CHANNEL_NAME="语音";break;case 2:t.CHANNEL_NAME="全媒体";break;case 3:t.CHANNEL_NAME="邮件";break;case 4:t.CHANNEL_NAME="工单";break;case 9:t.CHANNEL_NAME="自定义";break;default:break}}),this.initChannelQualitySumChart(),setTimeout(()=>{this.loading=!1},500)})},getInspectorQcTop(){let t={data:JSON.stringify({startDate:this.date[0],endDate:this.date[1]})};Object(s["m"])(t).then(t=>{this.InspectorQcTop10List=t.data,this.initInspectorQcTop10Chart(),setTimeout(()=>{this.loading=!1},500)})},getAgentQcTop10(){let t={data:JSON.stringify({startDate:this.date[0],endDate:this.date[1]})};Object(s["c"])(t).then(t=>{this.AgentQcTop10List=t.data,this.initAgentQcTop10Chart(),setTimeout(()=>{this.loading=!1},500)})},getGroupStat(){this.tableLoading=!0;let t={data:JSON.stringify({pageSize:this.size,pageIndex:this.page,pageType:"3",startDate:this.date[0],endDate:this.date[1]})};Object(s["j"])(t).then(t=>{this.tableData=t.data,this.totalRow=t.totalRow||0,setTimeout(()=>{this.tableLoading=!1},500)})},initInspectorTop10Chart(){this.InspectorTop10=this.$echarts.init(this.$refs.InspectorTop10);let t=this.InspectorTop10List.map(t=>t.EXAM_GROUP_NAME),e=this.InspectorTop10List.map(t=>t.USER_COUNT);0==this.InspectorTop10List.length?(document.getElementById("InspectorTop10-chat").style.display="none",document.getElementById("InspectorTop10-empty").style.display=""):(document.getElementById("InspectorTop10-chat").style.display="",document.getElementById("InspectorTop10-empty").style.display="none");let i={title:{text:this.$t("单位（次）"),textStyle:{fontSize:12,color:"#66717C"},top:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}}},grid:{top:30,left:10,right:10,bottom:5,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:t,axisLine:{lineStyle:{color:"#E9ECEF"}},axisLabel:{color:"#5C656C",fontSize:12,interval:0,formatter:(t,e)=>{let i="";return i=t.length>5?t.substring(0,3)+"...":t,"("+(e+1)+")"+i},rich:{l:{align:"left",width:0,fontSize:13},m:{align:"center"},r:{align:"right",width:0,fontSize:13}}},axisTick:{show:!1},offset:2},yAxis:{type:"value",axisLabel:{fontSize:12,color:"#2D374B"},splitLine:{lineStyle:{type:"dashed"}}},toolbox:{show:!1},series:[{name:this.$t("质检员数量"),data:e,type:"bar",barWidth:20,itemStyle:{color:function(t){let e=["#fe5150","#05d3d6","#fdc401","#2592FF"];return t.dataIndex>2?e[3]:e[t.dataIndex]}},label:{show:!0,position:"top"}}],dataZoom:{type:"inside"}};this.InspectorTop10.setOption(i),window.addEventListener("resize",()=>{this.InspectorTop10.resize()},!1)},initAgentTop10Chart(){this.AgentTop10=this.$echarts.init(this.$refs.AgentTop10);let t=this.AgentTop10List.map(t=>t.EXAM_GROUP_NAME),e=this.AgentTop10List.map(t=>t.USER_COUNT);0==this.AgentTop10List.length?(document.getElementById("AgentTop10-chat").style.display="none",document.getElementById("AgentTop10-empty").style.display=""):(document.getElementById("AgentTop10-chat").style.display="",document.getElementById("AgentTop10-empty").style.display="none");let i={title:{text:this.$t("单位（次）"),textStyle:{fontSize:12,color:"#66717C"},top:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}}},grid:{top:30,left:10,right:10,bottom:5,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:t,axisLine:{lineStyle:{color:"#E9ECEF"}},axisLabel:{color:"#5C656C",fontSize:12,interval:0,formatter:(t,e)=>{let i="";return i=t.length>5?t.substring(0,3)+"...":t,"("+(e+1)+")"+i},rich:{l:{align:"left",width:0,fontSize:13},m:{align:"center"},r:{align:"right",width:0,fontSize:13}}},axisTick:{show:!1},offset:2},yAxis:{type:"value",axisLabel:{fontSize:12,color:"#2D374B"},splitLine:{lineStyle:{type:"dashed"}}},toolbox:{show:!1},series:[{name:this.$t("坐席数量"),data:e,type:"bar",barWidth:20,itemStyle:{color:function(t){let e=["#fe5150","#05d3d6","#fdc401","#2592FF"];return t.dataIndex>2?e[3]:e[t.dataIndex]}},label:{show:!0,position:"top"}}],dataZoom:{type:"inside"}};this.AgentTop10.setOption(i),window.addEventListener("resize",()=>{this.AgentTop10.resize()},!1)},initChannelQualitySumChart(){this.channelQualitySum=this.$echarts.init(this.$refs.channelQualitySum);let t=this.channelQualitySumList.map(t=>t.CHANNEL_NAME),e=this.channelQualitySumList.map(t=>t.GROUP_COUNT);0==this.channelQualitySumList.length?(document.getElementById("channelQualitySum-chat").style.display="none",document.getElementById("channelQualitySum-empty").style.display=""):(document.getElementById("channelQualitySum-chat").style.display="",document.getElementById("channelQualitySum-empty").style.display="none");let i={title:{text:this.$t("单位（次）"),textStyle:{fontSize:12,color:"#66717C"},top:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}}},grid:{top:30,left:10,right:10,bottom:5,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:t,axisLine:{lineStyle:{color:"#E9ECEF"}},axisLabel:{color:"#5C656C",fontSize:12,interval:0,formatter:(t,e)=>{let i="";return i=t.length>5?t.substring(0,5)+"...":t,i},rich:{l:{align:"left",width:0,fontSize:13},m:{align:"center"},r:{align:"right",width:0,fontSize:13}}},axisTick:{show:!1},offset:2},yAxis:{type:"value",axisLabel:{fontSize:12,color:"#2D374B"},splitLine:{lineStyle:{type:"dashed"}}},toolbox:{show:!1},series:[{name:this.$t("质检组数量"),data:e,type:"bar",barWidth:20,itemStyle:{color:"#2592FF"},label:{show:!0,position:"top"}}],dataZoom:{type:"inside"}};this.channelQualitySum.setOption(i),window.addEventListener("resize",()=>{this.channelQualitySum.resize()},!1)},initInspectorQcTop10Chart(){this.InspectorQcTop10=this.$echarts.init(this.$refs.InspectorQcTop10);let t=this.InspectorQcTop10List.map(t=>t.USER_NAME),e=this.InspectorQcTop10List.map(t=>t.QC_COUNT);0==this.InspectorQcTop10List.length?(document.getElementById("InspectorQcTop10-chat").style.display="none",document.getElementById("InspectorQcTop10-empty").style.display=""):(document.getElementById("InspectorQcTop10-chat").style.display="",document.getElementById("InspectorQcTop10-empty").style.display="none");let i={title:{text:this.$t("单位（条）"),textStyle:{fontSize:12,color:"#66717C"},top:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}}},grid:{top:30,left:10,right:10,bottom:5,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:t,axisLine:{lineStyle:{color:"#E9ECEF"}},axisLabel:{color:"#5C656C",fontSize:12,interval:0,formatter:(t,e)=>{let i="";return i=t.length>5?t.substring(0,3)+"...":t,"("+(e+1)+")"+i},rich:{l:{align:"left",width:0,fontSize:13},m:{align:"center"},r:{align:"right",width:0,fontSize:13}}},axisTick:{show:!1},offset:2},yAxis:{type:"value",axisLabel:{fontSize:12,color:"#2D374B"},splitLine:{lineStyle:{type:"dashed"}}},toolbox:{show:!1},series:[{name:this.$t("质检数量"),data:e,type:"bar",barWidth:20,itemStyle:{color:function(t){let e=["#fe5150","#05d3d6","#fdc401","#2592FF"];return t.dataIndex>2?e[3]:e[t.dataIndex]}},label:{show:!0,position:"top"}}],dataZoom:{type:"inside"}};this.InspectorQcTop10.setOption(i),window.addEventListener("resize",()=>{this.InspectorQcTop10.resize()},!1)},initAgentQcTop10Chart(){this.AgentQcTop10=this.$echarts.init(this.$refs.AgentQcTop10);let t=this.AgentQcTop10List.map(t=>t.AGENT_NAME),e=this.AgentQcTop10List.map(t=>t.QC_COUNT);0==this.AgentQcTop10List.length?(document.getElementById("AgentQcTop10-chat").style.display="none",document.getElementById("AgentQcTop10-empty").style.display=""):(document.getElementById("AgentQcTop10-chat").style.display="",document.getElementById("AgentQcTop10-empty").style.display="none");let i={title:{text:this.$t("单位（次）"),textStyle:{fontSize:12,color:"#66717C"},top:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}}},grid:{top:30,left:10,right:10,bottom:5,containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:t,axisLine:{lineStyle:{color:"#E9ECEF"}},axisLabel:{color:"#5C656C",fontSize:12,interval:0,formatter:(t,e)=>{let i="";return i=t.length>5?t.substring(0,3)+"...":t,"("+(e+1)+")"+i},rich:{l:{align:"left",width:0,fontSize:13},m:{align:"center"},r:{align:"right",width:0,fontSize:13}}},axisTick:{show:!1},offset:2},yAxis:{type:"value",axisLabel:{fontSize:12,color:"#2D374B"},splitLine:{lineStyle:{type:"dashed"}}},toolbox:{show:!1},series:[{name:this.$t("坐席质检数量"),data:e,type:"bar",barWidth:20,itemStyle:{color:function(t){let e=["#fe5150","#05d3d6","#fdc401","#2592FF"];return t.dataIndex>2?e[3]:e[t.dataIndex]}},label:{show:!0,position:"top"}}],dataZoom:{type:"inside"}};this.AgentQcTop10.setOption(i),window.addEventListener("resize",()=>{this.AgentQcTop10.resize()},!1)},toSingle(t,e){this.$router.push({path:"singleInspectorGroup",query:{id:t,groupName:e}})}},mounted(){this.getAgentTop(),this.getGroupByType(),this.getInspectorTop(),this.getInspectorQcTop(),this.getAgentQcTop10(),this.getGroupStat()},created(){this.date=[this.$dayjs().subtract(3,"month").format("YYYY-MM-DD"),this.$dayjs().format("YYYY-MM-DD")],this.getDict({},["QcCommonDao.getDict(QC_CHANNEL_TYPE)"],t=>{this.QC_CHANNEL_TYPE=t["QcCommonDao.getDict(QC_CHANNEL_TYPE)"].data})}},r=l,c=(i("2afb"),i("2877")),p=Object(c["a"])(r,a,n,!1,null,"78a0a6e9",null);e["default"]=p.exports},e8b5:function(t,e,i){var a=i("c6b6");t.exports=Array.isArray||function(t){return"Array"==a(t)}}}]);