<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="工单质检统计"></title>
	<style>
	.layer_notice {
	    background: #5FB878;
	    padding: 10px;
	    height: 75px;
    	width: 330px;
	    display: none;
	    color: white;
	}
	.shadow{
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline shadow" id="searchForm">
       <input type="hidden" name="statisticType" id="statisticType" value="${param.statisticType}">
             	<div class="ibox">
             		<div class="ibox-title clearfix" id="resetid">
             			<div class="form-group">
	             		      <h5>
	             		      <span id="titleAndTime"> </span>
	             		      	<span  id="titleSpan"></span>
	             		     	<i id="alertMsg" class="layui-icon layui-icon-about layui-icon-tips" style="color: #1E9FFF;"></i>
	             		     	<div class="tipMsg" id="tipMsg" style="display: none;">
									<div style="background:#ffffff;color:#676A6C;padding: 5px;width: 540px;font-size: 14px;">
										<p><span i18n-content="字段说明"></span><hr></p>
										<!-- <p i18n-content="1、话单总数：对应坐席、质检员或者按日期统计的话单总数" ></p><p i18n-content="8、智能质检总分：每个任务中每个智能质检结果得分的总和" ></p>
										<p i18n-content="9、智能质检平均分：每个任务中每个智能质检结果得分的平均分" ></p> -->
										<p i18n-content="1、质检总数：各个任务质检数据的总和" ></p>
										<p i18n-content="2、待质检数：已人工抽取但没有完成人工质检的数据" ></p>
										<p i18n-content="3、已质检数：已人工抽取并且进行人工质检的数据,若开启发布就是已发布的数据" ></p>
										<p i18n-content="4、及格数：已人工抽取并且进行人工质检合格的数据" ></p>
										<p i18n-content="5、未及格数：人工质检不合格的数据，已质检数 - 及格数" ></p>
										<p i18n-content="6、人工质检总分：每个任务中每个人工质检结果得分的总和" ></p>
										<p i18n-content="7、人工质检平均分每个任务中每个人工质检结果得分的平均分" ></p>
										
										<p i18n-content="8、申诉数：对应发起申诉的数量" ></p>
										<p i18n-content="9、人工一票否决量：人工质检结果命中一票否决的记录" ></p>
										<br>
										<p i18n-content="备注：如果账号修改过统计会出现同一个账号有多条数据的情况" ></p>
									</div>
								</div> 
	             		     </h5>
	             		      <div class="input-group input-group-sm pull-right">
								     <button type="button" class="btn btn-sm btn-success btn-outline " onclick="QcStatRecord.exportRptTaskStat()"><i class="glyphicon glyphicon-export"></i>  <span i18n-content="导出"></span> </button>
						      </div>
						      
						      <!-- <span class="input-group input-group-sm pull-right" style="width: 5px;"> </span>
							  <div class="input-group input-group-sm pull-right">
							       <button type="button" class="btn btn-sm btn-info btn-outline" onclick="QcStatRecord.showQcRnResultList()"><span i18n-content="人工质检结果"></span> </button>
							  </div>
							  <span class="input-group input-group-sm pull-right" style="width: 5px;"> </span>
							  <div class="input-group input-group-sm pull-right">
							       <button type="button" class="btn btn-sm btn-info btn-outline" onclick="QcStatRecord.showZnRnResultList()"><span i18n-content="智能质检结果"></span> </button>
							  </div> -->
	             		</div>
	             		      <hr style="margin: 3px -15px">
						 <div class="form-group">
		             		<!-- 	<div class="input-group input-group-sm ml-20 hidden">
		               			<label class="radio-inline  ">
		                        	<input type="radio" value="agent" name="statisticType" > <span i18n-content="按坐席"></span>
		               			</label>
		               			<label class="radio-inline">
		                        	<input type="radio" value="inspector" name="statisticType"> <span i18n-content="按质检员"></span>
		               			</label>
		               			<label class="radio-inline">
		                        	<input type="radio" value="date" name="statisticType" checked="checked"> <span i18n-content="按日期"></span>
		               			</label>
							 </div> -->
							 
							 
							   <div class="input-group">
					 			<span class="input-group-addon" i18n-content="服务日期"></span>
					        	<input type="text" class="form-control input-sm" id="beginStartDate" name="beginStartDate" autocomplete="off" style="height:30px;width:142px" > 
			                	<span class="input-group-addon">~</span>
			                	<input type="text" class="form-control input-sm" id="endStartDate" name="endStartDate" autocomplete="off" style="height:30px;width:142px" > 
			                	<span class="input-group-addon">-</span>
			                	<select class="form-control input-sm" name="dateRange" id="dateRange" onchange="onCasecade($(this), 'beginStartDate', 'endStartDate')">
			                   		<option value="" i18n-content="请选择"></option>
									<option value="today" i18n-content="今天"></option>
									<option value="yesterday" i18n-content="昨天"></option>
									<option value="thisWeek" i18n-content="本周"></option>
									<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
								</select>
		                	  </div>
							 
							 
							  <div class="input-group input-group-sm ml-20" style="margin-left: 0px;">
								    <span class="input-group-addon" i18n-content="查找对象账号"></span>	
									<input type="text" name="agentKey" class="form-control input-sm" style="width:120px" placeholder="" value="">									  
							  </div>
							  
							
							  
							  <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="QcStatRecord.searchData()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span></button>
							  </div>
							   <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="repeat()">
									 <span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span></button>
								</div>
						  </div>
             	    </div> 
	              	<div class="ibox-content">
	              		<table id="main">
	              	    </table>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("QcStatRecord");
		var statName ;
		var statType ;
		var cols = [] ;
		$(function(){
			
			$("#dateRange").val("today").trigger("change");
			$("#searchForm").render();
			/* QcStatRecord.loadData(); */
			//监听radio改变事件
			/* $('input[name="statisticType"]').change(function(event){ */
	         	var resut = $("#statisticType").val();
         		if(resut=="agent"){
         			statType = 'agent';
         			$("#statName").text("坐席");
         			statName = '坐席';
         			$("#titleSpan").html(getI18nValue("坐席质检统计"));
         		}
         		if(resut=="date"){
         			statType = 'date';
         			$("#statName").text("日期");
         			statName = '日期';
         			$("#titleSpan").html(getI18nValue("整体工单质检"));
         		}
         		if(resut=="inspector"){
         			statType = 'inspector';
         			$("#statName").text("质检员");
         			statName = '质检员';
         			$("#titleSpan").html(getI18nValue("质检员评分统计"));
         		}
         		
         		
         		if(resut == 'inspector'){
         			cols = [
				         {width:80, title: '序号',type:'numbers'}
				         ,{minWidth:120,field:'STA_NAME', title: statName, sort: true, templet:function(row){
				        	if(statType == 'date'){
				        		//按日期
				        		return formatStringyyyyMMddToyyyy_MM_dd(row.STA_NAME);
				        	}else{
					        	return  row.STA_NAME || '';
				        	}
				         }}
				         ,{minWidth:120,field:'QC_COUNT', title: '质检总数', sort: true, templet:function(row){
				        	 //if(statType == 'inspector'){
				        	//	this.field = 'RG_COUNT';
							//	return row.RG_COUNT || 0;
							// }
				        	 return row.QC_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'RG_WAIT_COUNT', title: '待质检数', templet:function(row){
				        	 var rgWaitCount = row.RG_WAIT_COUNT;
				        	 if(rgWaitCount){
				        		 return rgWaitCount;
				        	 }else{
				        		 return 0;
				        	 }
				         }}
				         ,{minWidth:120,field:'RG_SUCC_COUNT', title: '已质检数', sort: true ,templet:function(row){
				        	 return row.RG_SUCC_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'PASS_COUNT', title: '及格数', sort: true,templet:function(row){
				        	 return row.PASS_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'NOTPASS_COUNT', title: '未及格数', templet:function(row){
				        	 var rgSuccCount = row.RG_SUCC_COUNT || 0;
				        	 var passCount = row.PASS_COUNT || 0;
				        	 var num = rgSuccCount - passCount;
				        	 return num > 0 ? num : 0;
				         }}
				         ,{minWidth:160,field:'SUM_SCORE', title: '人工质检总分', sort: true, templet:function(row){
				        	 return parseFloat(row.SUM_SCORE) || '0.00';
				         }}
				         ,{minWidth:160,field:'AVG_SCORE', title: '人工质检平均分', sort: true, templet:function(row){
				        	 return parseFloat(row.AVG_SCORE) || '0.00';
				         }}
				         /**
				         ,{minWidth:160,field:'ZN_SUM_SCORE', title: '智能质检总分', sort: true, templet:function(row){
				        	 return parseFloat(row.ZN_SUM_SCORE) || '0.00';
				         }}
				         ,{minWidth:160,field:'ZN_AVG_SCORE', title: '智能质检平均分', sort: true, templet:function(row){
				        	 return parseFloat(row.ZN_AVG_SCORE) || '0.00';
				         }}**/
				         ,{minWidth:160,field:'ONE_VOTE_COUNT', title: '人工一票否决量' ,sort: true,templet:function(row){
				        	 return row.ONE_VOTE_COUNT || 0;
				         } }
				         ,{minWidth:160,field:'RECONSIDER_COUNT', title: '申诉数', sort: true,templet:function(row){
				        	 return row.RECONSIDER_COUNT || 0;
				         }}
				         ,{minWidth:160,field:'RECONSIDER_SUCC_COUNT', title: '申诉成功数', sort: true,templet:function(row){
				        	 return row.RECONSIDER_SUCC_COUNT || 0;
				         }}
				        
				         ]
         		}else{
         			cols = [
				         {width:80, title: '序号',type:'numbers'}
				         ,{minWidth:120,field:'STA_NAME', title: statName, sort: true, templet:function(row){
				        	if(statType == 'date'){
				        		//按日期
				        		return formatStringyyyyMMddToyyyy_MM_dd(row.STA_NAME);
				        	}else{
					        	return  row.STA_NAME || '';
				        	}
				         }}
				         /**
				         ,{minWidth:120,field:'TOTAL_COUNT', title: '话单总数', sort: true,templet:function(row){
				        	 return row.TOTAL_COUNT || 0;
				         }}**/
				         ,{minWidth:120,field:'QC_COUNT', title: '质检总数', sort: true, templet:function(row){
				        	
				        	 return row.QC_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'RG_WAIT_COUNT', title: '待质检数', templet:function(row){
				        	 var rgWaitCount = row.RG_WAIT_COUNT;
				        	 if(rgWaitCount){
				        		 return rgWaitCount;
				        	 }else{
				        		 return 0;
				        	 }
				         }}
				         ,{minWidth:120,field:'RG_SUCC_COUNT', title: '已质检数', sort: true ,templet:function(row){
				        	 return row.RG_SUCC_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'PASS_COUNT', title: '及格数', sort: true,templet:function(row){
				        	 return row.PASS_COUNT || 0;
				         }}
				         ,{minWidth:120,field:'NOTPASS_COUNT', title: '未及格数', templet:function(row){
				        	 var rgSuccCount = row.RG_SUCC_COUNT || 0;
				        	 var passCount = row.PASS_COUNT || 0;
				        	 var num = rgSuccCount - passCount;
				        	 return num > 0 ? num : 0;
				         }}
				         ,{minWidth:160,field:'SUM_SCORE', title: '人工质检总分', sort: true, templet:function(row){
				        	 return parseFloat(row.SUM_SCORE) || '0.00';
				         }}
				         ,{minWidth:160,field:'AVG_SCORE', title: '人工质检平均分', sort: true, templet:function(row){
				        	 return parseFloat(row.AVG_SCORE) || '0.00';
				         }}
				      
				         ,{minWidth:160,field:'ONE_VOTE_COUNT', title: '人工一票否决量' ,sort: true ,templet:function(row){
				        	 return row.ONE_VOTE_COUNT || 0;
				         }}
				         ,{minWidth:160,field:'RECONSIDER_COUNT', title: '申诉数', sort: true,templet:function(row){
				        	 return row.RECONSIDER_COUNT || 0;
				         }}
				         ,{minWidth:160,field:'RECONSIDER_SUCC_COUNT', title: '申诉成功数', sort: true,templet:function(row){
				        	 return row.RECONSIDER_SUCC_COUNT || 0;
				         }}
				        
				         ]
         		}
         		
         		QcStatRecord.loadData();
         		/*  return true;
		        });  */
			
			
			
			layui.use('laydate', function(){
				  var laydate = layui.laydate;
				  laydate.render({
					  elem: '#beginStartDate',
					  range: false,
					  lang:getDateLang()
					});
				  laydate.render({
					  elem: '#endStartDate',
					  range: false,
					  lang:getDateLang()
					});
				});
			$("#alertMsg").hover(function() {
				openMsg();
			}, function() {
				//layer.close(subtips);
			});
		});
		var QcStatRecord={
				loadData:function(){
					var beginStartDate = $("#beginStartDate").val();
					var endStartDate = $("#endStartDate").val();
					if(!beginStartDate || !endStartDate){
						layer.msg(getI18nValue("日期不能为空"), {icon:5});
						return false;
					}
					$("#searchForm").initTableEx({
						mars:'QcOrderDao.statList',
						limit:15,
						height: 'full-160',
						limits:[15,25,50,100,200],
						autoFill:true,
						autoSort:false,
						id:'main',
						cols: [
							cols
						]
						,rowEvent: function (row, event){
						}
						, done: function(res,curr,count){
							console.log("res   : ", res);
							console.log("curr  : ", res);
							console.log("count : ", count);
							if(res) {
				        		var updateTime = res.updateTime;
								if(updateTime&&updateTime!=""){
									$("#titleAndTime").html("<font color='#5cb85c'>("+getI18nValue("数据更新时间")+""+updateTime+")</font>");
								}
								resultJson = res;
				        	}
						}
					});
				},
				goDetails:function(agentId,dateId,count){
					var data = form.getJSONObject("#searchForm");
					data = $.extend(data,{agentId:agentId,dateId:dateId});
					popup.openTab('${ctxPath}/pages/media/media-qc-list.jsp',"工单质检",data);
				},
				searchData:function(){
					QcStatRecord.loadData();
				},
				exportRptTaskStat:function(){
					var statisticType = '${param.statisticType}';
					var alertName = getI18nValue('是否导出工单质检统计表');
					if(statisticType == 'agent'){
						alertName = getI18nValue('是否导出坐席质检统计表');
					}else if (statisticType == 'date'){
						alertName = getI18nValue('是否导出整体工单质检统计表');
					}else if (statisticType == 'inspector'){
						alertName = getI18nValue('是否导出质检员评分统计表');
					}
					layer.confirm(alertName, {icon: 3, title:getI18nValue('提示'),btn: [getI18nValue('确认'), getI18nValue('取消')],offset:'20px'}, function(index){
						layer.close(index);
						location.href = "/cc-quality/servlet/qcExport?action=recordQcOrderStat&"+$("#searchForm").serialize();
					});
				},
				showQcRnResultList: function () {
					popup.openTab({url: '${ctxPath}/pages/result/qc-result-list.jsp',
						title: getI18nValue("人工质检结果"), id : 'cc-qc-zjjg'});
				},
				showZnRnResultList: function () {
					popup.openTab({url: '${ctxPath}/pages/result/qc-zn-result-list.jsp',
						title: getI18nValue("智能质检结果"), id : 'cc-qc-znzjjg'});
				}
		}
		function compute(data){
			var vint1 = 0;
			var vint2 = 0;
			if(parseInt(data.TOTAL_COUNT).toString() != 'NaN'){
				vint1 = parseInt(data.TOTAL_COUNT);
			}
			if(parseInt(data.UNCHECK_COUNT).toString() != 'NaN'){
				vint2 = parseInt(data.UNCHECK_COUNT);
			}
			return vint1-vint2;
		}
		function repeat() {
	 		$("#resetid input[type!=radio]").val("");
	 		$("#resetid select").val("");
	 		$("#dateRange").val("today").trigger("change");
	 	}
		function openMsg() {
			var msg = $("#tipMsg").html();
			layer.tips("<span style='color:#676A6C'>"+msg+"</span>", '#alertMsg',{tips:[1,'#ffffff'],closeBtn:1,time:0,area: ['auto', 'auto']});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>