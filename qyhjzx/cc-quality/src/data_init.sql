 
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('1', '日期', 'date', 'DATE_ID', '1', '{"format":"yyyyMMdd"}', 1,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('2', '计费开始时间', 'time', 'BILL_BEGIN_TIME', '1', '{"format":"yyyy-MM-dd HH:mm:ss"}', 2,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSO<PERSON>, INDEX_NUM,BUSI_TYPE) VALUES ('3', '计费结束时间', 'time', 'BILL_END_TIME', '1', '{"format":"yyyy-MM-dd HH:mm:ss"}', 3,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('4', '通话时长(s)', 'int', 'BILL_TIME', '1', '', 4,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('5', '呼叫创建原因', 'dict', 'CREATE_CAUSE', '1', '{"code":"CALL_CREATE_CAUSE"}', 8,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('6', '话机号码', 'varchar', 'PHONE_NUM', '1', '', 5,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('101', '主叫', 'varchar', 'CALLER', '1', '', 6,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('102', '被叫', 'varchar', 'CALLED', '1', '', 7,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('103', '呼叫结果', 'dict', 'CLEAR_CAUSE', '1', '{"code":"CLEAR_CAUSE_CN"}', 9,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('104', '满意度', 'dict', 'SATISF_ID', '1', '{"code":"VOICE_SATISF"}', 10,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('105', '技能组', 'selectData', 'GROUP_ID', '1', '{"dataMars":"QcCommonDao.skillDictByType(voice)"}', 11,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('106', '挂断类型', 'dict', 'AGENT_RELEASE', '1', '{"code":"QC_AGENT_RELEASE"}', 12,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('107', '来电地区', 'selectData', 'AREA_CODE', '1', '{"dataMars":"QcCommonDao.areaDict"}', 13,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('108', '是否为新员工', 'selectOne', 'IS_NEW', '1', '{"dataMars":"QcCommonDao.getDict(SF_YN)"}', 14,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('10', '时段', 'int', 'HOUR_ID', '1', '', 15, '1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('110', '小结', 'selectData', 'SUMMARY_ID', '1', '{"dataMars":"QcCommonDao.summaryDictByType(1)"}', 16, '1');

INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('7', '日期', 'date', 'DATE_ID', '2', '{"format":"yyyyMMdd"}', 201,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('8', '服务开始时间', 'time', 'BEGIN_TIME', '2', '{"format":"yyyy-MM-dd HH:mm:ss"}', 202,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('9', '服务结束时间', 'time', 'END_TIME', '2', '{"format":"yyyy-MM-dd HH:mm:ss"}', 203,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('201', '渠道类型', 'dict', 'CHANNEL_TYPE', '2', '{"code":"CC_BASE_CHANNEL_TYPE"}', 205,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('202', '满意度', 'selectData', 'SATISF_CODE', '2', '{"dataMars":"QcCommonDao.mediaSatisfDict"}', 206,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('203', '技能组', 'selectData', 'GROUP_ID', '2', '{"dataMars":"QcCommonDao.skillDictByType(media)"}', 207,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('204', '结束原因', 'dict', 'CLEAR_CAUSE', '2', '{"code":"REPORT_CLEAR_CAUSE_SERVER"}', 209,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('205', '创建原因', 'dict', 'CREATE_CAUSE', '2', '{"code":"REPORT_CREATE_CAUSE_SERVER"}', 208,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('206', '服务时长(s)', 'int', 'SERVER_TIME', '2', '', 204,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('208', '所属渠道', 'selectData', 'CHANNEL_ID', '2', '{"dataMars":"QcCommonDao.getCfChannel"}', 211,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('209', '是否为新员工', 'selectOne', 'IS_NEW', '2', '{"dataMars":"QcCommonDao.getDict(SF_YN)"}', 212,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('210', '小结', 'selectData', 'SUMMARY_ID', '2', '{"dataMars":"QcCommonDao.summaryDictByType(3)"}', 213, '1');



UPDATE ycbusi.cc_qc_column SET JSON='{"code":"CLEAR_CAUSE_CN"}',COLUMN_NAME='呼叫结果' WHERE ID='103';
UPDATE ycbusi.cc_qc_column SET JSON='{"code":"VOICE_SATISF"}' WHERE ID='104';
UPDATE ycbusi.cc_qc_column SET JSON='{"code":"CC_BASE_CHANNEL_TYPE"}' WHERE ID='201';
UPDATE ycbusi.cc_qc_column SET JSON='{"dataMars":"QcCommonDao.mediaSatisfDict"}', COLUMN_TYPE='selectData', COLUMN_VALUE='SATISF_CODE' WHERE ID='202';
UPDATE ycbusi.cc_qc_column SET JSON='{"dataMars":"QcCommonDao.skillDictByType(voice)"}' WHERE ID='105';
UPDATE ycbusi.cc_qc_column SET JSON='{"dataMars":"QcCommonDao.skillDictByType(media)"}' WHERE ID='203';
UPDATE ycbusi.cc_qc_column SET JSON='{"code":"REPORT_CLEAR_CAUSE_SERVER"}' WHERE ID='204';
UPDATE ycbusi.cc_qc_column SET JSON='{"code":"REPORT_CREATE_CAUSE_SERVER"}' WHERE ID='205';
UPDATE ycbusi.cc_qc_column SET COLUMN_NAME='是否为新员工',COLUMN_TYPE='selectOne',COLUMN_VALUE='IS_NEW',CHANNEL_TYPE='2',JSON='{"dataMars":"QcCommonDao.getDict(SF_YN)"}',INDEX_NUM=212 WHERE ID='209';


-- 全媒体质检详情侧边栏
INSERT INTO ycbusi.C_MEDIA_OP_MENU_BUSI(ID,OP_MENU_ID,BUSI_ID) VALUES ('@ENT_ID_@BUSI_ORDER_ID_khxxmedia_8', '@ENT_ID_@BUSI_ORDER_ID_khxxmedia','8');


-- 邮件规则抽取配置
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('301', '日期', 'date', 'DATE_ID', '3', '{"format":"yyyyMMdd"}', 1,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('302', '邮件发送号码', 'selectData', 'EMAIL_FROM', '3', '{"dataMars":"QcCommonDao.emailDict(form)"}', 2,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('303', '邮件接收号码', 'selectData', 'EMAIL_TO', '3', '{"dataMars":"QcCommonDao.emailDict(to)"}', 3,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('304', '标题', 'varchar', 'TITLE', '3', '', 4,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('305', '邮件满意度', 'dict', 'SATISF_CODE', '3', '{"code":"EMAIL_SATISF_CODE"}', 5,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('306', '是否标星', 'dict', 'IS_STAR', '3', '{"code":"SF_YN"}', 6,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('307', '工作组', 'selectData', 'WORK_GROUP_ID', '3', '{"dataMars":"QcCommonDao.workGroupDict()"}',7,'1');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('308', '是否解决', 'dict', 'IS_SOLVE', '3', '{"code":"IS_SOLVE"}', 8,'1');

INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('401', '智能质检分数', 'int', 'ZN_QC_SCORE', '1', '', 212,'2');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('402', '满意度', 'dict', 'SATISF_ID', '1', '{"code":"VOICE_SATISF"}', 10,'2');
INSERT INTO ycbusi.cc_qc_column (ID, COLUMN_NAME, COLUMN_TYPE, COLUMN_VALUE, CHANNEL_TYPE, JSON, INDEX_NUM,BUSI_TYPE) VALUES ('403', '通话时长(s)', 'int', 'BILL_TIME', '1', '', 4,'2');




 
