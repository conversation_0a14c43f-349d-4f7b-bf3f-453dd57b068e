package com.yunqu.yc.quality.model;

import com.yunqu.yc.quality.utils.Column;

public class ItemExportModel {
    @Column("AGENT_NAME")
    private String agentName;
    @Column("CALL_TYPE")
    private String callType;
    @Column("QUALITY_TOTAL")
    private String QUALITY_TOTAL;
    @Column("PASS_NUM")
    private String PASS_NUM;
    @Column("NOT_PASS_NUM")
    private String NOT_PASS_NUM;
    @Column("RG_QC_SCORE_AVG")
    private String RG_QC_SCORE_AVG;
    @Column("APPL_NUM")
    private String APPL_NUM;
    @Column("APPL_PASS_NUM")
    private String APPL_PASS_NUM;
    @Column("ONE_VOTE_ITEM")
    private String ONE_VOTE_ITEM;
}
