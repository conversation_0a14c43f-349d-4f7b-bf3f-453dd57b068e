package com.yunqu.yc.quality.dao.stat.sql;

import com.yq.busi.common.model.UserModel;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.base.QueryFactory;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;

import java.util.HashMap;
import java.util.Map;

import static com.yq.busi.common.util.CommonUtil.getTableName;

public class QCGroupCheckAnalysisSql {

    /**
     * 质检组坐席相关信息
     */
    public static EasySQL qcGroupAgentDataSql(UserModel user,String groupId){
        EasySQL sql = new EasySQL("select *");
        sql.append("from " + getTableName(user.getSchemaName(), "CC_QC_GROUP") );
        sql.append("where 1=1");
        sql.append(user.getEpCode(), "and ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and BUSI_ORDER_ID=?");
        sql.append(groupId,"AND EXAM_GROUP_ID = ?");
        return sql;
    }

    /**
     * 质检组质检员相关信息
     */
    public static EasySQL qcGroupInspectorDataSql(UserModel user,String groupId){
        EasySQL sql = new EasySQL("select count(*) as INSPECTOR_COUNT");
        sql.append("from " + getTableName(user.getSchemaName(), "CC_QC_GROUP_INSPECTOR") );
        sql.append("where 1=1");
        sql.append(user.getEpCode(), "and ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and BUSI_ORDER_ID=?");
        sql.append(groupId,"AND EXAM_GROUP_ID = ?");
        return sql;
    }

    /**
     * 质检组相关信息
     */
    public static EasySQL qcGroupDataSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("select");
        sql.append("sum(RG_QC_TOTAL) QC_TOTAL"); //质检完成数
        sql.append(",sum(RG_PASS_COUNT) PASS_COUNT");//及格数
        sql.append(",sum(RG_RECONSIDER_COUNT) RECONSIDER_COUNT");//申述数
        sql.append(",sum(RG_ONE_VOTE_COUNT) ONE_VOTE_COUNT");//一票否决数
        sql.append(",sum(RG_SUM_SCORE) SUM_SCORE");//总分
        sql.append(",count(*) QC_COUNT");//总分
        sql.append("from " + getRgStatName() );
        sql.append("where 1=1");
        sql.append(user.getEpCode(), "and ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND DATE_ID <= ?");
        sql.append(groupId,"AND QC_GROUP_ID = ?");
        return sql;
    }

    /**
     * 质检组需要辅导次数
     */
    public static EasySQL qcGroupCachCountSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("select");
        sql.append("SUM( CASE WHEN  CACH_RESULT> 0 THEN 1 ELSE 0 END ) CACH_COUNT");
        sql.append("from " + getTableName(user.getSchemaName(),"cc_qc_result"));
        sql.append("where 1=1");
        sql.append(user.getEpCode(), "and ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND DATE_ID <= ?");
        sql.append(groupId,"AND EXAM_GROUP_ID = ?");
        return sql;
    }


    /**
     * 质检员质检数量排行
     */
    public static EasySQL qcDataCheckCountRankSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.INSPECTOR,T1.INSPECTOR_NAME,COUNT(*) AS CHECK_COUNT");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.INSPECTOR,T1.INSPECTOR_NAME");
        sql.append("order by CHECK_COUNT DESC");
        return sql;
    }

    /**
     * 质检员被申述次数排行
     */
    public static EasySQL qcDataReconsiderCountRankByInspectorSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.INSPECTOR,T1.INSPECTOR_NAME,SUM(CASE WHEN RECONSIDER_FLAG is not null and RECONSIDER_FLAG!=0 THEN 1 ELSE 0 END ) RECONSIDER_COUNT");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.INSPECTOR,T1.INSPECTOR_NAME");
        sql.append("order by RECONSIDER_COUNT DESC");
        return sql;
    }

    /**
     * 坐席分数排行
     */
    public static EasySQL qcDataAgentScoreRankSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME,AVG(RG_QC_SCORE) SCORE");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append("LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        sql.append("order by SCORE DESC");
        return sql;
    }

    /**
     * 坐席申述次数排行
     */
    public static EasySQL qcDataReconsiderCountRankByAgentSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME,SUM(CASE WHEN RECONSIDER_FLAG is not null and RECONSIDER_FLAG!=0 THEN 1 ELSE 0 END ) RECONSIDER_COUNT");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        sql.append("order by RECONSIDER_COUNT DESC");
        return sql;
    }

    /**
     * 一票否决排行
     */
    public static EasySQL qcDataAgentOneVoteRankSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME,SUM(CASE WHEN T1.ONE_VOTE_ITEM> 0 THEN 1 ELSE 0 END ) ONE_VOTE_COUNT");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        sql.append("order by ONE_VOTE_COUNT DESC");
        return sql;
    }

    /**
     * 优秀/警示案例排名
     */
    public static EasySQL qcDataAgentOBJTypeRankSql(UserModel user,String startDate,String endDate,String OBJType,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        if ("EXCELLENT".equals(OBJType)) {
            sql.append(",SUM(CASE WHEN OBJ_TYPE = '1' THEN 1 ELSE 0 END ) COUNT");
        }
        if ("WARN".equals(OBJType)) {
            sql.append(",SUM(CASE WHEN OBJ_TYPE = '2' THEN 1 ELSE 0 END ) COUNT");
        }
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        sql.append("order by COUNT DESC");
        return sql;
    }



    /**
     * 坐席需要辅导次数排行
     */
    public static EasySQL qcDataCachCountRankSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("SELECT T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME,SUM( CASE WHEN  CACH_RESULT> 0 THEN 1 ELSE 0 END ) CACH_COUNT");
        sql.append("FROM " + getTableName(user.getSchemaName(),"CC_QC_TASK_OBJ T1"));
        sql.append(" LEFT JOIN "+ getTableName(user.getSchemaName(),"CC_QC_RESULT T2") +" ON T1.RG_RESULT_ID = T2.QC_RESULT_ID " );
        sql.append("where 1 = 1");
        sql.append(user.getEpCode(), " AND T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), " AND T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append("AND T1.RG_STATE IN ('3','4')"); //质检完成
        sql.append(groupId,"AND T2.EXAM_GROUP_ID = ?");
        sql.append("group by T1.AGENT_ID,T1.AGENT_ACC,T1.AGENT_NAME");
        sql.append("order by CACH_COUNT DESC");
        return sql;
    }


    /**
     * 坐席综合能力分析
     */
    public static EasySQL qcDataAgentAvgSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("select AVG(RG_AA_SCORE_1) RG_AA_SCORE_1,AVG(RG_AA_SCORE_2) RG_AA_SCORE_2");
        sql.append(",AVG(RG_AA_SCORE_3) RG_AA_SCORE_3,AVG(RG_AA_SCORE_4) RG_AA_SCORE_4,AVG(RG_AA_SCORE_5) RG_AA_SCORE_5");
        sql.append("from " + getRgStatName() );
        sql.append("where 1=1");
        sql.append(user.getEpCode(), "and ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND DATE_ID <= ?");
        sql.append(groupId,"AND QC_GROUP_ID = ?");
        return sql;
    }

    /**
     * 质检员质检数量趋势/坐席质检平局分趋势
     */
    public static EasySQL qcGroupTrendDataSql(UserModel user,String startDate,String endDate,String groupId){
        EasySQL sql = new EasySQL("select *");
        sql.append("from " + getRgStatName() + " T1");
        sql.append("where 1=1 ");
        sql.append(user.getEpCode(), "and T1.ENT_ID=?");
        sql.append(user.getBusiOrderId(), "and T1.BUSI_ORDER_ID=?");
        sql.append(startDate.replaceAll("-", ""), "AND T1.DATE_ID >= ?");
        sql.append(endDate.replaceAll("-", ""), "AND T1.DATE_ID <= ?");
        sql.append(groupId, "AND T1.QC_GROUP_ID = ?");
        return sql;
    }


    //表：C_ST_QC_RG_STAT1/2
    public static String getRgStatName() {
        // getYcstatTableByTaget 获取最新统计表
        return getStatTableName(getYcstatTableByTaget("C_ST_QC_RG_STAT").get("TARGET_TABLE_NAME"));
    }

    /**
     * 获取Stat数据库的表名
     */
    protected static String getStatTableName(String tableName){
        return Constants.getStatSchema() + "." + tableName;
    }

    /**
     * 获取统计库中的最新统计表名和统计时间
     * @param tableName
     * @return
     */
    public static Map<String, String> getYcstatTableByTaget(String tableName){
        Map<String, String> tabInfo = null;
        try {
            String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME from "+getStatTableName("cc_stat_table_info")+" where TABLE_ID = ?  ";
            tabInfo = QueryFactory.getReadQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
            //设置默认的统计表
            if(tabInfo == null){
                tabInfo = new HashMap<>();
                tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
                tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
            }
        } catch (Exception ex) {
            CommonLogger.getLogger().error(ex.getMessage(), ex);
        }
        return tabInfo;
    }
}
