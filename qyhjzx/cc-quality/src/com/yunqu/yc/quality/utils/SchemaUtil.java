package com.yunqu.yc.quality.utils;

import java.sql.SQLException;

import org.apache.log4j.Logger;

import com.yq.busi.common.util.EasyQueryUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;

public class SchemaUtil {
	private static Logger logger = CommonLogger.getLogger();
	/**
	 * 根据企业id查询配置的订购id
	 * @param entId
	 * @return
	 */
	public static String getCcBusiOrderIdByEntId(String entId){
		String busiOrderId = "";
		try {
			busiOrderId = EasyQueryUtil.getBusiQuery().queryForString(" SELECT BUSI_ORDER_ID FROM CC_BUSI_ORDER WHERE ENT_ID=? AND BUSI_ID=? ", entId,Constants.getBusiId());
		} catch (SQLException e) {
			logger.error("查询企业呼叫中心订购id异常:"+e.getMessage(),e);
		}
		return busiOrderId;
	}

}
