package com.yunqu.yc.quality.utils;

import java.sql.SQLException;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;

public class LogQuaityUtil {
	
	/**
	 * 操作日志
	 * @param type：新增还是修改还是删除
	 * @param content: 日志描述,Yqlogger.OPER_TYPE_ADD;
	 */
	public static void getLogUtil(UserModel user,String content,String type){
		Yqlogger log=new Yqlogger();
		log.setCreateAcc(user.getUserAcc());
		log.setCreateName(user.getUserName());
		log.setCreateTime(DateUtil.getCurrentDateStr());
		log.setModule(Constants.APP_NAME);
		log.setOperType(type);
		log.setContent(user.getUserAcc()+content);
		log.setBakup("");
		log.setEntId(user.getEpCode());
		log.setBusiOrderId(user.getBusiOrderId());
		LogUtil.insertLog(user.getSchemaName(),log);
	}
	
	/**
	 * 添加质检抽取日志
	 * @param serialId
	 * @param extractLog
	 * @param user 
	 * @param query
	 * @throws SQLException 
	 */
	public static void addExtractLog(String serialId, String schema, JSONObject extractLog, UserModel user, EasyQuery query) {
		try {
			EasyRecord record = new EasyRecord(CommonUtil.getTableName(schema, "CC_QC_RGEXTRACT_LOG"), "ID");
			extractLog.put("CREATE_ACC", user.getUserAcc());
			extractLog.put("CREATE_NAME", user.getUserName());
			extractLog.put("CREATE_DEPT", user.getDeptCode());
			extractLog.put("CREATE_DEPT_NAME", user.getDeptName());
			extractLog.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			extractLog.put("ENT_ID", user.getEpCode());
			extractLog.put("BUSI_ORDER_ID", user.getBusiOrderId());
			record.setPrimaryValues(serialId);
			record.setColumns(extractLog);
			query.save(record);
		} catch (Exception e) {
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(LogQuaityUtil.class) + "添加人工抽取日志错误 " , e);
		}
	}
	
	/**
	 * 更新质检抽取日志
	 * @param serialId
	 * @param extractLog
	 * @param user 
	 * @param query
	 * @throws SQLException 
	 */
	public static void updateExtractLog(String serialId, String schema, JSONObject extractLog, EasyQuery query) {
		try {
			EasyRecord record = new EasyRecord(CommonUtil.getTableName(schema, "CC_QC_RGEXTRACT_LOG"), "ID");
			record.setPrimaryValues(serialId);
			record.setColumns(extractLog);
			query.update(record);
		} catch (Exception e) {
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(LogQuaityUtil.class) + "更新人工抽取日志错误 " , e);
		}
	}
	
	/**
	 * 更新质检抽取日志
	 * @param serialId
	 * @param extractLog
	 * @param user 
	 * @param query
	 * @throws SQLException 
	 */
	public static void updateExtractLog(String serialId, String schema, String isSuccess, String mac, String result, EasyQuery query) {
		JSONObject extractLog = new JSONObject();
		extractLog.put("IS_SUCCESS", isSuccess);
		extractLog.put("EXEC_RESULT", result);
		extractLog.put("EXEC_MAC", mac);
		updateExtractLog(serialId, schema, extractLog, query);
	}
}
