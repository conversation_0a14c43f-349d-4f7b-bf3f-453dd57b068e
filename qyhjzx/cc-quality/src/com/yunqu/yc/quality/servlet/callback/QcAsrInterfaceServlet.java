package com.yunqu.yc.quality.servlet.callback;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.*;
import com.yq.busi.common.util.http.HttpResp;
import com.yunqu.yc.quality.base.AppBaseServlet;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.utils.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

@WebServlet("/qcTask/asrCallBack")
public class QcAsrInterfaceServlet extends AppBaseServlet{
	private Logger logger = CommonLogger.getLogger("asrCallback");
	private static EasyCache cache = CacheManager.getMemcache();
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForIndex(){
		return actionForAsrCallBack();
	}

	private JSONObject actionForAsrCallBack() {
		String resultStr= getRequestBody();
		JSONObject result = JSONObject.parseObject(resultStr);
		String serialId = result.getString("serialId");
		JSONObject params = result.getJSONObject("customParamJson");
		JSONObject chatText = result.getJSONObject("chatText");
		if (params.isEmpty() || chatText.isEmpty()){
			logger.info(CommonUtil.getClassNameAndMethod(this) + "消息记录:" + serialId +",转写文本结果为空或携带参数未返回,中止调用质检接口");
			return getReturnJson(serialId, 0);
		}
//		params.put("chatText", chatText);
		JSONArray data = params.getJSONArray("data");
		JSONObject obj = data.getJSONObject(0);
		obj.put("chatText",chatText);
		data = new JSONArray();
		data.add(obj);
		params.put("data",data);

		String sendRecordURL = ConfigUtil.getString(Constants.APP_NAME, "SEND_RECORD_URL");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "消息记录:" + serialId +",质检调用地址:" + sendRecordURL + ",请求参数:" + params.toJSONString());
		HttpResp resp = HttpUtil.sendPost(sendRecordURL, params.toJSONString(), "UTF-8", "application/json", HttpUtil.TYPE_JSON);
		if (resp != null && resp.getCode() == 200) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "质检推送结果:" + "result=" +resp.getResult());
			JSONObject respJson = JSONObject.parseObject(resp.getResult());
			if ("200".equals(respJson.getString("code"))){
				logger.info(CommonUtil.getClassNameAndMethod(this) + "消息记录:" + serialId +",质检推送成功");
			}else {
				logger.info(CommonUtil.getClassNameAndMethod(this) + "消息记录:" + serialId +",质检推送失败");
			}
		}else {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "消息记录:" + serialId +",调用质检接口失败");
		}
		return getReturnJson(serialId,1);
	}

	private JSONObject getReturnJson(String serialId, int type) {
		JSONObject returnJson = new JSONObject();
		if(type == 1){
			returnJson.put("serialId", serialId);
			returnJson.put("respCode", "000");
			returnJson.put("respDesc", "成功");
		}else {
			returnJson.put("serialId", serialId);
			returnJson.put("respCode", "999");
			returnJson.put("respDesc", "失败");
		}
		return returnJson;
	}


	private JSONObject parseJSONObject(JSONObject src, String property) {
		if(src == null || StringUtils.isBlank(property)) {
			return null;
		}
		JSONObject jObj = null;
		try{
			jObj = src.getJSONObject(property);
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(QcAsrInterfaceServlet.class)
				+ "解析JSON失败，属性" + property, e);
			LogUtil.addSystemErrorLog(getUser(), Constants.APP_NAME, "【质检结果回传】解析JSON失败，属性"+property+":"+e.getMessage(), e, CommonLogger.getLogger());
		}
		return jObj;
	}
	
	private JSONArray parseJSONArray(JSONObject src, String property) {
		if(src == null || StringUtils.isBlank(property)) {
			return null;
		}
		JSONArray jArr = null;
		try{
			jArr = src.getJSONArray(property);
		}catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(QcAsrInterfaceServlet.class)
				+ "解析JSON失败，属性" + property, e);
			LogUtil.addSystemErrorLog(getUser(), Constants.APP_NAME, "【质检结果回传】解析JSON失败，属性"+property+":"+e.getMessage(), e, CommonLogger.getLogger());
		}
		return jArr;
	}

	/**
	 * 获取请求体数据
	 */
	private String getRequestBody() {
		HttpServletRequest request = getRequest();
		try {
			request.setCharacterEncoding("UTF-8");
			//从请求体里获取数据
			String json = getRequestStr(request);
			return json;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "从请求体里获取数据", e);
			LogUtil.addSystemErrorLog(getUser(), Constants.APP_NAME, "【质检结果回传】从请求体里获取数据异常:"+e.getMessage(), e, CommonLogger.getLogger());
		}
		return "";
	}
	
	
}
