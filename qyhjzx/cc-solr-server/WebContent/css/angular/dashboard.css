/*

Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

*/

#content #dashboard .block
{
  background-image: none;
  width: 49%;
}

#content #dashboard .fieldlist
{
  float: left;
}

#content #dashboard .fieldlist dt,
#content #dashboard .fieldlist dd
{
  display: block;
  float: left;
}

#content #dashboard .fieldlist dt
{
  clear: left;
  margin-right: 2%;
  text-align: right;
  width: 23%;
}

#content #dashboard .fieldlist dd
{
  width: 74%;
}

#content #dashboard .fieldlist .index_optimized
{
  margin-top: 10px;
}

#content #dashboard .fieldlist .ico
{
  background-image: url( ../../img/ico/slash.png );
  height: 20px;
}

#content #dashboard .fieldlist .ico.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #dashboard .fieldlist .ico span
{
  display: none;
}

#content #dashboard #statistics .index_optimized.value a
{
  display: none;
}

#content #dashboard #statistics .index_optimized.value.ico-0 a
{
  background-color: #f0f0f0;
  background-image: url( ../../img/ico/hammer-screwdriver.png );
  background-position: 5px 50%;
  border: 1px solid #c0c0c0;
  display: block;
  float: left;
  margin-left: 50px;
  padding: 1px 5px;
  padding-left: 26px;
}

#content #dashboard #statistics .index_has-deletions
{
  display: none;
}

#content #dashboard #statistics .index_has-deletions.value.ico-0
{
  background-image: url( ../../img/ico/tick-red.png );
}

#content #dashboard #replication
{
  float: left;
}

#content #dashboard #replication .is-replicating
{
  background-position: 99% 50%;
  display: block;
}

#content #dashboard #replication #details table thead td span
{
  display: none;
}

#content #dashboard #instance
{
  float: right;
}

#content #dashboard #instance .dir_impl
{
  margin-top: 10px;
}

#content #dashboard #healthcheck
{
  float: right;
}

#content #dashboard #healthcheck .ico
{
  background-image: url( ../../img/ico/slash.png );
  height: 20px;
  padding-left: 20px;
  width: 60%;
}

#content #dashboard #healthcheck .ico.ico-1
{
  background-image: url( ../../img/ico/tick.png );
}

#content #dashboard #system h2 { background-image: url( ../../img/ico/server.png ); }
#content #dashboard #statistics h2 { background-image: url( ../../img/ico/chart.png ); }
#content #dashboard #replication h2 { background-image: url( ../../img/ico/node.png ); }
#content #dashboard #replication.master h2 { background-image: url( ../../img/ico/node-master.png ); }
#content #dashboard #replication.slave h2 { background-image: url( ../../img/ico/node-slave.png ); }
#content #dashboard #instance h2 { background-image: url( ../../img/ico/server.png ); }
#content #dashboard #collection h2 { background-image: url( ../../img/ico/book-open-text.png ); }
#content #dashboard #shards h2 { background-image: url( ../../img/ico/documents-stack.png ); }

#content #dashboard #shards { margin-left: 20px;}

#dashboard #shards .shard h3.shard-title {
    display: block;
    background-color: #c8c8c8;
    font-weight: bold;
    padding: 3px;
    padding-left: 30px;
    margin-left: 20px;
    margin-top: 20px;
    background-image: url( ../../img/ico/document-text.png );
    background-position-x: 10px;
    background-position-y: 3px;
}

#dashboard #shards .shard .shard-detail {
    margin-bottom: 25px;
    margin-top: 7px;
}

#dashboard #shards .shard .replica {
    background-color: #e4e4e4;
}

#dashboard #shards .shard .replica.odd {
    background-color: #fff;
}
