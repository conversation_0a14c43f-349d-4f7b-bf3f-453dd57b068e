#**
 *  Global macros used by other templates.
 *  This file must be named VM_global_library.vm
 *  in order for Velocity to find it.
 *#

#macro(param $key)$request.params.get($key)#end

#macro(url_root)/solr#end

## TODO: s/url_for_solr/url_for_core/ and s/url_root/url_for_solr/
#macro(core_name)$request.core.name#end
#macro(url_for_solr)#{url_root}#if($request.core.name != "")/$request.core.name#end#end
#macro(url_for_home)#url_for_solr/browse#end

#macro(q)&q=$!{esc.url($params.get('q'))}#end

#macro(fqs $p)#foreach($fq in $p)#if($velocityCount>1)&#{end}fq=$esc.url($fq)#end#end

#macro(debug)#if($request.params.get('debugQuery'))&debugQuery=true#end#end

#macro(boostPrice)#if($request.params.get('bf') == 'price')&bf=price#end#end        

#macro(annotate)#if($request.params.get('annotateBrowse'))&annotateBrowse=true#end#end

#macro(annTitle $msg)#if($annotate == true)title="$msg"#end#end

#macro(spatial)#if($request.params.get('sfield'))&sfield=store#end#if($request.params.get('pt'))&pt=$request.params.get('pt')#end#if($request.params.get('d'))&d=$request.params.get('d')#end#end

#macro(qOpts)#set($queryOpts = $request.params.get("queryOpts"))#if($queryOpts && $queryOpts != "")&queryOpts=$queryOpts#end#end

#macro(group)#if($request.params.getBool("group") == true)&group=true#end#if($request.params.get("group.field"))#foreach($grp in $request.params.getParams('group.field'))&group.field=$grp#end#end#end

#macro(sort $p)#if($p)#foreach($s in $p)&sort=$esc.url($s)#end#end#end

#macro(lensNoQ)?#if($request.params.getParams('fq') and $list.size($request.params.getParams('fq')) > 0)&#fqs($request.params.getParams('fq'))#end#sort($request.params.getParams('sort'))#debug#boostPrice#annotate#spatial#qOpts#group#end
#macro(lens)#lensNoQ#q#end
        

#macro(url_for_lens)#{url_for_home}#lens#end

#macro(url_for_start $start)#url_for_home#lens&start=$start#end

#macro(url_for_filters $p)#url_for_home?#q#boostPrice#spatial#qOpts#if($list.size($p) > 0)&#fqs($p)#end#debug#end

#macro(url_for_nested_facet_query $field)#url_for_home#lens&fq=$esc.url($field)#end

## TODO: convert to use {!raw f=$field}$value (with escaping of course)
#macro(url_for_facet_filter $field $value)#url_for_home#lens&fq=#if($value!=$null)$esc.url($field):%22$esc.url($value)%22#else-$esc.url($field):[*+TO+*]#end#end

#macro(url_for_facet_date_filter $field $value)#url_for_home#lens&fq=$esc.url($field):$esc.url($value)#end

#macro(url_for_facet_range_filter $field $value)#url_for_home#lens&fq=$esc.url($field):$esc.url($value)#end


#macro(link_to_previous_page $text)
  #if($page.current_page_number > 1)
    #set($prev_start = $page.start - $page.results_per_page)
    <a class="prev-page" href="#url_for_start($prev_start)">$text</a>
  #end
#end

#macro(link_to_next_page $text)
  #if($page.current_page_number < $page.page_count)
    #set($next_start = $page.start + $page.results_per_page)
    <a class="next-page" href="#url_for_start($next_start)">$text</a>
  #end
#end

#macro(link_to_page $page_number $text)
  #if($page_number == $page.current_page_number)
    $text
  #else
    #if($page_number <= $page.page_count)
      #set($page_start = $page_number * $page.results_per_page - $page.results_per_page)
      <a class="page" href="#url_for_start($page_start)">$text</a>
    #end
  #end
#end

#macro(display_facet_query $field, $display, $fieldName)
  #if($field.size() > 0)
  <span class="facet-field">$display</span>
    <ul>
    #foreach ($facet in $field)
      #if ($facet.value > 0)
        #set($facetURL = "#url_for_nested_facet_query($facet.key)")
        #if ($facetURL != '')
          <li><a href="$facetURL">$facet.key</a> ($facet.value)</li>
        #end
      #end
    #end
    </ul>
  #end      
#end


#macro(display_facet_range $field, $display, $fieldName, $start, $end, $gap, $before, $after)
  <span class="facet-field">$display</span>
    <ul>
    #if($before && $before != "")
      #set($value = "[* TO " + "#format_value($start)" + "}")
      #set($facetURL = "#url_for_facet_range_filter($fieldName, $value)")
      <li><a href="$facetURL">Less than #format_value($start)</a> ($before)</li>
    #end
    #foreach ($facet in $field)
      #set($rangeEnd = "#range_get_to_value($facet.key, $gap)")
      #set($value = "[" + $facet.key + " TO " + $rangeEnd + "}")
      #set($facetURL = "#url_for_facet_range_filter($fieldName, $value)")
      #if ($facetURL != '')
        <li><a href="$facetURL">$facet.key - #format_value($rangeEnd)</a> ($facet.value)</li>
      #end
    #end
    #if($end && $end != "" && $after > 0)
      #set($value = "[" + "#format_value($end)" + " TO *}")
      #set($facetURL = "#url_for_facet_range_filter($fieldName, $value)")
      <li><a href="$facetURL">More than #format_value($end)</a> ($after)</li>
    #end
    </ul>
#end

## $pivots is a list of facet_pivot
#macro(display_facet_pivot $pivots, $display)
  #if($pivots.size() > 0)
  <span class="facet-field">$display</span>
    <ul>
      #foreach ($pivot in $pivots)
        #foreach ($entry in $pivot.value)
          <a href="#url_for_facet_filter($entry.field, $entry.value)">$entry.field::#if($entry.value!=$null)$entry.value#else<em>missing</em>#end</a> ($entry.count)
          <ul>
            #foreach($nest in $entry.pivot)
              <li>
                #if($nest.value != $null)
                  <a href="#url_for_facet_filter($entry.field, $entry.value)&fq=$esc.url($nest.field):%22$esc.url($nest.value)%22">$nest.field::$nest.value</a>
                #else
                  <a href="#url_for_facet_filter($entry.field, $entry.value)&fq=-$esc.url($nest.field):[*+TO+*]">$nest.field::<em>missing</em></a>
                #end
                ($nest.count)
              </li>
            #end
          </ul>
        #end
      #end
    </ul>
  #end
#end

#macro(field $f)
  #if($response.response.highlighting.get($docId).get($f).get(0))
    #set($pad = "")
    #foreach($v in $response.response.highlighting.get($docId).get($f))
$pad$v##
      #set($pad = " ... ")
    #end
  #else
    #foreach($v in $doc.getFieldValues($f))
$v##
    #end
  #end
#end  

#macro(utc_date $theDate)
$date.format("yyyy-MM-dd'T'HH:mm:ss'Z'",$theDate,$date.getLocale(),$date.getTimeZone().getTimeZone("UTC"))##
#end

#macro(format_value $val)
#if(${val.class.name} == "java.util.Date")
#utc_date($val)##
#else
$val##
#end
#end

#macro(range_get_to_value $inval, $gapval)
#if(${gapval.class.name} == "java.lang.String")
#if($gapval.startsWith("+"))
$inval$gapval## Typically date gaps start with +
#else
$inval+$gapval## If the gap does not start with a "+", we add it, such as for currency value
#end
#elseif(${gapval.class.name} == "java.lang.Float" || ${inval.class.name} == "java.lang.Float")
$math.toDouble($math.add($inval,$gapval))##
#else
$math.add($inval,$gapval)##
#end
#end
