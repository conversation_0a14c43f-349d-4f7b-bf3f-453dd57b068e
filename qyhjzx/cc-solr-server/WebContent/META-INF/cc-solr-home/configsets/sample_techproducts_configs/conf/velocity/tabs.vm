#**
 *  Provides navigation/access to Advanced search options
 *  Usually displayed near the top of the page
 *#

##TODO: Make some nice tabs here

#set($queryOpts = $params.get("queryOpts"))

<div class="tabs-bar" #annTitle("Click the link to demonstrate various Solr capabilities")>

  <span>Type of Search:</span>

  ##queryOpts=$queryOpts

  ## return to Simple Search
  ##set( $selected = ($queryOpts && $queryOpts != "") )
  #set( $selected = ! $queryOpts )
  <span class="tab #if($selected)selected#end">
    #if($selected)
      Simple
    #else
      <a href="#url_for_home/?#debug#annotate">
        Simple</a>
    #end
  </span>

  ## GEO-Spatial / Location Based
  #set( $selected = ($queryOpts == "spatial") )
  <span class="tab #if($selected)selected#end">
    #if($selected)
      Spatial
    #else
      <a href="#url_for_home?&queryOpts=spatial#debug#annotate">
        Spatial</a>
    #end
  </span>

  ## Group By Field
  #set( $selected = ($queryOpts == "group") )
  <span class="tab #if($selected)selected#end">
    #if($selected)
      Group By
    #else
      <a href="#url_for_home?#debug#annotate&queryOpts=group&group=true&group.field=manu_exact">
        Group By</a>
    #end
  </span>

</div>
