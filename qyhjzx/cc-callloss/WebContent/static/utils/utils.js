function getShortcuts() {
  return [{
    text: getI18nValue('今天'),
    onClick(picker) {
      picker.$emit('pick', getDateRange());
    }
  }, {
    text: getI18nValue('昨天'),
    onClick(picker) {
      const start = getDateRange(2)[0]
      const end = new Date(start.getTime() + 3600 * 1000 * 24 - 1)
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: getI18nValue('近一周'),
    onClick(picker) {
      picker.$emit('pick', getDateRange(7));
    }
  }, {
    text: getI18nValue('近一月'),
    onClick(picker) {
      picker.$emit('pick', getDateRange(30));
    }
  }, {
    text: getI18nValue('近三月'),
    onClick(picker) {
      picker.$emit('pick', getDateRange(90));
    }
  }]
}

// 获取某日0点整到今日23:59:59的时间数组
function getDateRange(day = 1) {
  let someDaysSeconds = 3600 * 1000 * 24 * day
  var endDate = new Date()
  endDate = new Date(endDate.setHours(23, 59, 59, 999))
  var startDate = new Date(endDate.getTime() - someDaysSeconds + 1)
  return [startDate, endDate]
}

// 计算两天相差的天数
function twoDaysDifference(day1, day2){
  day1 = Date.parse(new Date(day1))
  day2 = Date.parse(new Date(day2))
  return Math.abs(parseInt((day2 - day1) / 1000 / 3600 / 24))
}

// 将url上的所有参数转为对象
function urlParamsToObj() {
  let paramObj = {}
  let parmaStr = window.location.href.split('?')[1]
  let paramlist = (parmaStr && parmaStr.split('&')) || []
  paramlist.map((item) => {
    let list = item.split('=')
    paramObj[list[0]] = decodeURIComponent(list[1])
  })
  return paramObj
}

// 获取url上的所有参数
function getUrlParams() {
  let obj = {}
  if (location.href.split('?').length != 2) return
  let parmasList = location.href.split('?')[1].split('&')
  parmasList.map(item => {
    let param = item.split('=')
    obj[param[0]] = param[1] || ''
  })
  return obj
}

// 将对象的所有非空属性转为url参数
function objToUrlParams(obj = {}) {
  let paramList = []
  for (var key in obj) {
    if (obj[key] == '' || obj[key] == undefined || obj[key] == null) continue
    paramList.push(`${key}=${obj[key]}`)
  }
  return paramList.join('&')
}


function loadEntModuleInfo() {
  //100s内不更新
  var timestamp = Date.parse(new Date());
  var loadEntModuleFlag = sessionStorage.getItem("cc-loadEntModuleFlag");
  if(loadEntModuleFlag ){
      loadEntModuleFlag = Number(loadEntModuleFlag);
      if(timestamp-loadEntModuleFlag<100000){
          return;
      }
  }
  //3.1#20210803-1 改为查询当前用户相关的模块-增加按角色控制模块脱敏权限功能
  yq.remoteCall("/cc-base/servlet/entModule?action=getEncryEntModuleByRole",{},function(result) {
    if(result.state == 1){
      value =  result.value;
      if(value){
        //value = eval("("+value+")");
        for(var i in value){
          var appId = value[i].APP_ID;
          var busiOrderId = value[i].BUSI_ORDER_ID;
          var entId = value[i].ENT_ID;
          var entId = value[i].ENT_ID;
          var key = "ENT-MODULE-"+entId+"-"+busiOrderId+"-"+appId;
          sessionStorage.setItem(key,JSON.stringify(value[i]));
        }
        sessionStorage.setItem("cc-loadEntModuleFlag",timestamp);
      }
    } 
  },{async:false});
}
/**
* 获取模块配置信息
*/
function getEntModule(appId) {
  if (!appId || appId == undefined ) {
    return null;
  }
  appId = appId.replace("/", "");
  loadEntModuleInfo();
  var key = "ENT-MODULE-"+localStorage.getItem("userEntId")+"-"+localStorage.getItem("busiOrderId")+"-"+appId;
  var entModule = sessionStorage.getItem(key);
  if (!entModule) {
    return null;
  } else {
    entModule = JSON.parse(entModule);
  }	
  return entModule;
}

function getPhone(val,realPhone,appId,encrypType) {
  if(val=='' || realPhone =='' || val ==undefined || realPhone ==undefined){
    return '--';
  }
  
  if(val && val.substring(0,1)=='#'){
    return '******';
  }
  
  if(realPhone==""){
    return "--";
  }
  
  if(!encrypType  || encrypType ==undefined || encrypType==""){
    encrypType = "03";
  }
  if(encrypType=="01"){
    return realPhone;
  }
  
  //兼容原有配置
  var iscrypt=localStorage.getItem("iscrypt");
  
  //参与脱敏的数据类型
  var projectDataType = "01,02,03,04,05,06,07,08,09,10,99,";
  
  //读取每个模块的配置
  var entModule = getEntModule(appId);
  if(entModule){
    if("Y" == entModule.NUM_PROTECT){
      iscrypt = true;
    }else{
      iscrypt = false;
    }
    if(!entModule.projectDataType){
      projectDataType = entModule.projectDataType
    }
  }
  
  //如果不需脱敏，则直接返回
  if(!iscrypt){
    return realPhone;
  }
  
  //如果脱敏类型没有包含当前类型，则直接返回
  if(!projectDataType && projectDataType.indexOf(encrypType)==-1){
    return realPhone;
  }
  
  var len = realPhone.length;
  if(len==1){
    return realPhone;
  } else if(len <= 3){
    return "*" + realPhone.substring(1,len);
  } else if(len > 3 && len <= 6){
    return "**" + realPhone.substring(2,len);
  }
  
  if(encrypType=="02"){ //姓名脱敏
    return realPhone.substring(0,2) + "****" + realPhone.substring(6,len);
  }
  
  if(encrypType=="03"){ //手机号脱敏
    var pat=/(\d{3})\d{4}(\d*)/;
    return realPhone.replace(pat,'$1****$2');
  }
  
  if(encrypType=="04"){ //身份证号脱敏
    return realPhone.replace(/^(.{6})(?:\d+)(.{4})$/, "$1****$2");
  }
  
  if(encrypType=="98"){ //邮箱
    return "****" + realPhone.substring(realPhone.indexOf('@'));
  }
  
  if(encrypType=="99"){ //其他：保留第一位和最后一位
    return realPhone.substring(0,1) + "****" + realPhone.substring(len-1,len);
  }
  return realPhone.substring(0,1) + "****" + realPhone.substring(len-1,len);
}

// 校验字段是否为空
function isNull(str) {
  return str == '' || str == null || str == 'null' ||
    str == undefined || str == 'undefined' || str.trim() == '';
}

//创建临时的输入框元素
function createElement(text) {
  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';
  var element = document.createElement('textarea');
  // 防止在ios中产生缩放效果
  element.style.fontSize = '12pt';
  // 重置盒模型
  element.style.border = '0';
  element.style.padding = '0';
  element.style.margin = '0';
  // 将元素移到屏幕外
  element.style.position = 'absolute';
  element.style[isRTL ? 'right' : 'left'] = '-9999px';
  // 移动元素到页面底部
  let yPosition = window.pageYOffset || document.documentElement.scrollTop;
  element.style.top = '${yPosition}px';
  //设置元素只读
  element.setAttribute('readonly', '');
  element.value = text;
  document.body.appendChild(element);
  return element;
}

/**
* 判断当前用户是否可以查询某个模块的脱敏数据
* @param appId  模块id，如 cc-custmgr，/cc-custmgr
*/
function moduleIscrypt(appId){
  var iscrypt = false;
  var entModule = getEntModule(appId);
  if(!entModule){
    return iscrypt;
  }
  if("Y" == entModule.SEARCH_PROTECT_DATA){
    iscrypt = true;
  }else{
    iscrypt = false;
  }
  return iscrypt;
}