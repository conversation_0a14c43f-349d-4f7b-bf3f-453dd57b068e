<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page import="com.yunqu.cc.callloss.base.Constants" %>

<EasyTag:override name="head">
	<title i18n-content="电话留言列表"></title>
	<style type="text/css">
		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		
		.container-fluid{
		    height: 100%;
		}
		
		#searchForm{
			height:100%;
		}
		
		#searchForm .layui-table-view {
		    height: 100%;
		}
		
		#searchForm .layui-table-box {
		    height: 93%;
		}
		
		.ibox {
		    height: 100%;
		}
		
		.ibox-content {
		    height: 100%;
		}
		
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
        </style>
</EasyTag:override>
<EasyTag:override name="content">
	<input type="hidden" id="dateTemp">
       <form action="" method="post" name="searchForm" class="form-inline " id="searchForm" onsubmit="return false;" autocomplete="off" >
       		<input type="hidden" name="type" value="all">
       		<!-- 用于被客户资料页面的引用 -->
			<input type="hidden" id="custPhone" name="custPhone" value="${param.custPhone }">
			<input type="hidden" id="custPhone2" name="custPhone2" value="${param.custPhone2 }">
             	<div class="ibox shadow" >
             		<div class="ibox-title clearfix cust-hide"  id="divId">
						 <div class="form-group">
	             			<h5><span i18n-content="电话留言列表"></span><span id="sub" ><i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i></span></h5>
	             			<EasyTag:res resId="cc-call-callloss-permission-dcqx">
								<div class="input-group input-group-sm pull-right btn-group">
						   			<a class="btn btn-sm btn-info btn-outline"  href="javascript:void(0)" onclick="callWordNS.exportList()" i18n-content="导出"> </a>
						    	</div>
	             			</EasyTag:res>
						  </div>
						<hr style="margin: 5px -15px">
						<div class="input-group input-group-sm">
							<span class="input-group-addon" i18n-content="开始时间"></span>
							<input type="text" class="form-control input-sm" id="startDate" name="startDate" data-mars="common.todayFirstTime" style="width:150px" data-mars-reload="false" data-mars-top='true' autocomplete="off">
							<span class="input-group-addon">-</span>	
							<input type="text" class="form-control input-sm" id="endDate" name="endDate" data-mars="common.todayEndTime" style="width:150px" data-mars-reload="false" data-mars-top='true' autocomplete="off">									  
	                 		<span class="input-group-addon">-</span>
	                        <select class="form-control input-sm" name="dateRange" onchange="onCasecadeTime($(this))" data-cust-context-path="/cc-base" data-cust-mars="dict.getDictList('SEARCH_TIME_SCOPE')">
									<option value="" i18n-content="请选择"></option>
							</select>							
                    	</div>
                    	<div class="input-group input-group-sm" id="agentDiv">
	        		         <span class="input-group-addon" i18n-content="坐席"></span>
	        		         <select name="agentId" id="agentId" data-mars="common.userDict" data-mars-top="true" multiple="multiple" size="1">
	        		         </select>
					    </div> 
						<div class="input-group input-group-sm">
							  <button type="button" class="btn btn-sm btn-default" onclick="callWordNS.searchData('1')"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span> </button>
					  	</div>
					  	<div class="input-group ">
							<button type="button" class="btn btn-sm btn-default" onclick="callWordNS.reset()"><span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置"></span>  </button>
					  	</div> 
					  	<div class="input-group">
					  		<button type="button" class="btn btn-sm btn-default"  onclick="moreQuery()"><span i18n-content="高级查询"></span></button>
					  	</div>
					  	<div class="form-group" id="more-query-content" style="display: none;" id="divId">
					 	  <div class="input-group input-group-sm" >
								<span class="input-group-addon" style="width:69px;"  i18n-content="主叫"></span> 
								<input name="caller" value="${param.custPhone.concat(',').concat(param.custPhone2) }" id="caller" class="form-control input-sm" style="width:150px;">
						  </div><div class="input-group input-group-sm" >
								<span class="input-group-addon" style="width:69px;"  i18n-content="被叫" ></span> 
								<input name="called" class="form-control input-sm" style="width:132px;">
						  </div><div class="input-group input-group-sm" >
							    <span class="input-group-addon" i18n-content="处理状态"></span>	
							    <select class="form-control input-sm" name="state" data-cust-context-path="/cc-base" data-cust-mars="dict.getDictList('STATE')" >
	                      			<option value="" i18n-content="请选择"></option>
	                      		</select>
						  </div>
            	    	</div>
					</div>        	      

		   <div class="table-responsive">
				<table class="" id="tree-table" lay-size="sm"></table>
				<table id="tree-table"></table>
				<div class="stat-desc" id="statDesc" style="display:none;color:#676A6C;">
							<fieldset class="content-title">
					  			<legend i18n-content="备注"></legend>
						    </fieldset>
						    <p i18n-content="客户拨打服务热线电话"></p>
							<!-- 1、客户拨打服务热线电话，非工作时间或无坐席时，可以进入到留言流程进行留言<br> -->
		              	</div>
			</div>
		</div>
   </form>
</EasyTag:override>

<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/cc-callloss/static/js/common.js"></script>
<script type="text/javascript" src="/cc-callloss/static/js/time.js"></script>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("callWordNS");
		//重置
		callWordNS.reset=function(){
			$("#divId select").val("");
			$("#divId input").val("");
	    	$("#startDate").val(startDate);
	    	$("#endDate").val(endDate);
	    	
	    	//复选框重置
			$("#agentId").multiselect("destroy");
	    	
			requreLib.setplugs('multiselect',function(){
				$("#agentId").multiselect({
					 enableFiltering: true,
					 maxHeight: 200,
					 includeSelectAllOption: true,
					 selectAllText:getI18nValue('全选'),
					 nonSelectedText: getI18nValue('请选择')
				});
			});
		};
		function moreQuery(){
			$('#more-query-content').toggle();
		}
		callWordNS.searchData=function(flag){
			if(flag=='1'){
				$("#searchForm").queryData({id:'tree-table',page:{curr:1}});
			}else{
				$("#searchForm").queryData({id:'tree-table'});
			}
		}
		var startDate = "";
		var endDate = "";
        requreLib.setplugs("layui",function(){
			// 设置layui组件引入路径 ======> 兼容IE
			layui.config({
				dir: '/easitline-static/lib/layui/'
			});
        	var startDate = "";
    		var endDate = "";
        	$("#searchForm").render({success:function(){
        		startDate = $("#startDate").val();
				endDate = $("#endDate").val();
	        	//加载时间控件
				layui.use('laydate', function(){
			  		var laydate = layui.laydate;
			  		laydate.render({ 
			  			elem: '#startDate' ,
			  			type: 'datetime',
			  			lang:getDateLang(),
			  			btns: ['confirm'] 
			  		});
			  		laydate.render({ 
			  			elem: '#endDate' ,
			  			type: 'datetime',
			  			lang:getDateLang(),
			  			btns: ['confirm'] 
			  		});
				});
	        	
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect({
  					 	 enableFiltering:true,
					 	 maxHeight: 400,
						 includeSelectAllOption: true,
						 selectAllText:getI18nValue('全选'),
						 nonSelectedText: getI18nValue('请选择')
					});
    			});
	        	
	        	
	        	$("#dateTemp").val($("#startDate").val()+","+$("#endDate").val());
	        	//被客户资料页面引用时执行
				callWordNS.refCustOption();
	        	callWordNS.loadData();
        	}});
        });
        callWordNS.recordUrlPrefix = '';
        
        callWordNS.loadData=function(){
        	$("#searchForm").initTableEx({
				url:'${ctxPath}/webcall?action=callNoAnswer.wordList'
				,id:'tree-table'
				,limit:20
				,page:true 	
				,title:getI18nValue('电话留言列表')
				,height: 'full-165'
				,cellMinWidth:60
				,loading:true
				,cols: [
					[
						 {width:50,align:'center',title:'序号' ,type:'numbers'},
					      {minWidth:100,align:'center',field:'DATE_ID', sort:true, title: '日期'},
					      {minWidth:100,align:'center',field:'CALLER', title:'主叫' ,sort:true,
					        	 templet:function(row){
					        		 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
				            }},
				           {minWidth:100,align:'center',field:'REPLY_PHONE', title:'回拨号码' ,sort:true,
					        	 templet:function(row){
					        		 if(!row.REPLY_PHONE){
					        			 return getPhone(row.CALLER,row.CALLER,"${ctxPath}");
					        		 }
					        		 return getPhone(row.REPLY_PHONE,row.REPLY_PHONE,"${ctxPath}");
				           }},
				           {minWidth:80,align:'center',field:'REPLY_PHONE', title:'回拨' ,
					        	 templet:function(row){
					        		 var phone = row.REPLY_PHONE;
					        		 if(!phone){
					        			 phone = row.CALLER;
					        		 }
					        		 if (phone){
					        			 var temp = '<a title="'+getI18nValue('拨打电话')+''+getPhone(phone,phone,"${ctxPath}")+'" href="javascript:void(0)" onclick="callWordNS.callPhone(\''+phone+'\',\''+row.SERIAL_ID+'\')"><i class="glyphicon glyphicon-earphone"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;';
					        			 
					        			 if(row.RECORD_FILE){
											temp=temp+'&nbsp;&nbsp;<a title="'+getI18nValue('播放录音')+'"  href="javascript:void(0)"   onclick="callWordNS.recoredListener(\''+row.SERIAL_ID+'\')"><i class="glyphicon glyphicon-headphones"></i></a>&nbsp;&nbsp;'
										 }
					        			 
					        			 return temp;
					        		 }else{
					        			 return "";
					        		 }
					        }},
					      {minWidth:100,align:'center',field:'CALLED', title:'被叫' ,sort:true,
					        	 templet:function(row){
					        		 return getPhone(row.CALLED,row.CALLED,"${ctxPath}");
				        			 //return getPhone(row.CALLED,row._CALLED);
				            }},
					      {minWidth:100,align:'center',field:'BEGIN_TIME', title:'开始时间' ,sort:true,
					        	 templet:function(row){
				        			 return substrHMS(row.BEGIN_TIME);
				            }},
					      {minWidth:100,align:'center',field:'END_TIME', title: '结束时间',sort:true,
					        	 templet:function(row){
				        			 return substrHMS(row.END_TIME);
				            }},
					      {minWidth:130,align:'center',field:'RECALL_TIME', title: '回拨时间',sort:true,
					        	 templet:function(row){
					        		 if (row.RECALL_TIME){
					        			 return row.RECALL_TIME
					        		 }else{
					        			 return "-";
					        		 }
					        }},
					      {minWidth:140,align:'center',field:'STATE', title: '处理状态',sort:true,
					        	 templet:function(row){
					        		 var json={0:'layui-btn-danger',1:'label-success'};			/* +"1".equals(row.STATE) ?getI18nValue('已经处理'):getI18nValue('未处理')+ */
									 return "<span class='layui-btn layui-btn-xs "+json[row.STATE]+"'>"+ getI18nValue(getDictTextByCode('STATE',row.STATE)) +"</span>"
					        }},
					      {minWidth:100,align:'center',field:'USERNAME', title:'处理人',sort:true,
					        	 templet:function(row){
					        		 if (row.USERNAME){
					        			 return row.USERNAME
					        		 }else{
					        			 return "-";
					        		 }
					        }},
					      {minWidth:150,align:'center',field:'HANDLE_TIME', title:'处理时间',sort:true ,
					        	 templet:function(row){
					        		 if (row.HANDLE_TIME){
					        			 return row.HANDLE_TIME
					        		 }else{
					        			 return "-";
					        		 }
					        }},
					      {minWidth:200,align:'left',field:'', title: '操作' ,fixed:'right',templet:function(row){
								var temp="";
								temp += '<EasyTag:res resId="cc-call-ivr-monitor"><a href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="callWordNS.IVRTrace(\''+row.BEGIN_TIME+'\',\''+row.END_TIME+'\',\''+row.CALLER+'\',\''+row.CALLED+'\')">'+getI18nValue('进线轨迹')+'</a>&nbsp;&nbsp;</EasyTag:res>';
								if(row.STATE==0){
									temp += '<a href="javascript:void(0)"  class="layui-btn layui-btn-xs layui-btn-normal"  onclick="callWordNS.updateData(\''+row.SERIAL_ID+'\')">'+getI18nValue('标记处理')+'</a>&nbsp;&nbsp;'
								}
								return temp;
					         }},
				         
				         
			         ]
				],
				done:function(res,curr,count){
					var data = res;
					if(data && data != undefined){
						callWordNS.recordUrlPrefix = data.recordUrlPrefix;
					}
		        } 
			});
		}
        
		callWordNS.updateData = function(serialId){
			layer.confirm(getI18nValue('确定标记为已处理'),{
				btn : [ getI18nValue('确定'), getI18nValue('取消')] ,offset:'40px'},
				function(index, layero) {
					layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/callNoanswer?action=updateWord", {serialId:serialId}, function(result) {
			  			if(result.state == 1){
			  				callWordNS.loadData();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
		  			});
				},function(index){
					layer.close(index);
				});
			
			
		}
		callWordNS.recoredListener = function(id) {
			popup.layerShow({type:2,title:getI18nValue('播放录音'),offset:'20px',area:['750px','250px']},"${ctxPath}/pages/record/record-play.jsp",{wordId:id});
		}
		
		callWordNS.callPhone = function(num,serailId){
			if(parent.CallControl.getFunc('makecall')){
				var data = {};
				data.serialId =serailId;
				data.phone =num;
				ajax.remoteCall("${ctxPath}/servlet/callNoanswer?action=updateWordCalback", data, function(result) { 
					parent.ccbar_plugin.callControl.makeCall(num,{wordSerailId:serailId,makeCallType: 'agentManualCall'});
					layer.close(index);
				}); 
			}else{
				layer.alert(getI18nValue('无法外呼'),{title:getI18nValue('提示'),btn:[getI18nValue('确定')]});	
			}
		}
		//'是否导出留言记录？'"无法外呼!"
		callWordNS.exportList = function(){			
			layer.confirm(getI18nValue('是否导出留言记录'),{icon: 3, title:getI18nValue('导出提示'),offset:'20px',btn : [getI18nValue('确定') , getI18nValue('取消') ]}, function(index){
				layer.close(index);
				
				var agentId = "";
				var data = form.getJSONObject("#searchForm");
				if(data.agentId){
					agentId = JSON.stringify(data.agentId);
				}
				
				location.href = "${ctxPath}/servlet/export?action=exportCallWord&"+$("#searchForm").serialize()+"&agentIds="+encodeURI(agentId);
			});
		}
		//打开IVR轨迹
		callWordNS.IVRTrace = function(startDate,endDate,caller,called){
			var data = {startDate:startDate,endDate:endDate,caller:caller,called:called};
			popup.openTab("/cc-base/pages/ivr/ivr-monitor-list.jsp", getI18nValue("进线轨迹跟踪"), data);
		}
		
		
		// 鼠标悬停提示特效
		$("#sub").hover(function() {
			openMsg();
		}, function() {
			// layer.close(subtips);
		});
		function openMsg() {
			var msg = $("#statDesc").html();
			subtips = layer.tips("<span style='color:#676A6C'>" + msg + "</span>",
					'#sub', {
						tips : [ 1, '#ffffff' ],
						closeBtn : 1,
						time : 0,
						area : [ 'auto', 'auto' ]
					});
		}
		
		
		//作为iframe被引用时
		callWordNS.refCustOption = function(){
			if('${param.opener}'=='cust'){
				$("body").removeClass("gray-bg");
				$(".cust-hide").hide();
				$("#startDate").val("");
			}else if('${param.opener}'=='Analysis'){
				$("body").removeClass("gray-bg");
				$(".cust-hide").hide();
				$("#startDate").val("${param.startDate}");
				$("#endDate").val("${param.endDate}");
			}
		}

		window.onload = function(){
			if(parent.renderIframeStyle){
				parent.renderIframeStyle();
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>