
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>留言</title>
	<style>
	   .dropdown-icon>li>.addon{margin-top:-0.7em}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						  <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 留言列表</h5>
	             		       
	             		  </div>
	             		  <!-- 操作栏 div start -->
	             		  <div id = 'waitDiv' style="display:none">
	             		  		<div class="btn-group btn-group-xs dropdown ml-10">
													<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
														操作  <span class="caret"></span>
													</button>
													<ul class="dropdown-menu dropdown-icon" role="menu">
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-edit"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.deal('leavid')">处理</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-earphone"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.Call('leavid','phone')">外呼</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-eye-open"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.detail('leavid')">查看明细</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-remove-circle"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.ignore('leavid')">忽略</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-play"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.play('leavid','downloadurl')">播放</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-download"></i>
																</div>
																<a href="javascript:void(0)" target="_blank" onclick="messList.download('leavid')">下载</a>
															</li>
													</ul>
										  </div>
	             		  </div>
	             		  <div id = 'handleDiv' style="display:none">
	             		  	 <div class="btn-group btn-group-xs dropdown ml-10">
													<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
														操作  <span class="caret"></span>
													</button>
													<ul class="dropdown-menu dropdown-icon" role="menu">
													        <li>
																<div class="addon">
																	<i class="glyphicon glyphicon-eye-open"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.detail('leavid')">查看明细</a>
															</li>
													        <li>
																<div class="addon">
																	<i class="glyphicon glyphicon-play"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.play('leavid','downloadurl')">播放</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-download"></i>
																</div>
																<a href="javascript:void(0)" target="_blank" onclick="messList.download('leavid')">下载</a>
															</li>
												    </ul>
										    </div>
	             		  </div>
	             		  <div id = 'ignoreDiv' style="display:none">
	             		  	 <div class="btn-group btn-group-xs dropdown ml-10">
													<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
														操作  <span class="caret"></span>
													</button>
													<ul class="dropdown-menu dropdown-icon" role="menu">
													        <li>
																<div class="addon">
																	<i class="glyphicon glyphicon-eye-open"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.detail('leavid')">查看明细</a>
															</li>
													        <li>
																<div class="addon">
																	<i class="glyphicon glyphicon-play"></i>
																</div>
																<a href="javascript:void(0)" onclick="messList.play('leavid','downloadurl')">播放</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-download"></i>
																</div>
																<a href="javascript:void(0)" target="_blank" onclick="messList.download('leavid')">下载</a>
															</li>
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-download"></i>
																</div>
																<a href="javascript:void(0)" target="_blank" onclick="messList.withdraw('leavid')">撤回</a>
															</li>
												    </ul>
										    </div>
	             		  </div>
	             		  <!-- 操作栏 div  end -->
	             		  
	             		  <hr style="margin:5px -15px">
	             		  <div class="form-group">
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon">客户号码</span>	
									  <input type="text" name="CALLER" class="form-control input-sm" style="width:90px">
							   </div>
							  <div class="input-group input-group-sm">
								      <span class="input-group-addon">处理人账号</span>	
									  <input type="text" name="HAND_USER_ACC" class="form-control input-sm" style="width:90px">
							   </div>
							    <!-- <div class="input-group input-group-sm">
								      <span class="input-group-addon">处理人工号</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:90px">
							   </div> -->
							    <div class="input-group input-group-sm">
								      <span class="input-group-addon">处理状态</span>	
									  <select name="STATUS" class="form-control input-sm" data-cust-context-path="/cc-base"
							data-cust-mars="dict.getDictList('LEAVE_MSG_STATUS')" >
									      <option value="">请选择</option>
									  </select>
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">服务热线</span>	
									  <select name="QUEUE_HOT_LINE" class="form-control input-sm" data-cust-context-path="/cc-base"
							data-cust-mars="dict.getDictList('QUEUE_HOT_LINE')" >
									      <option value="">请选择</option>
									  </select>
							   </div>
							   <div class="input-group input-group-sm">
		          		              <span class="input-group-addon">留言时间</span>	
									  <input type="text" name="CREATE_TIME_SATET" id="startDate"  class="form-control input-sm Wdate" style="width:110px" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="CREATE_TIME_END" id="endDate"  class="form-control input-sm Wdate" style="width:110px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}'})">									  
							  	</div>
							    <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="messList.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							    </div>
							    <div class="input-group input-group-sm pull-right">
							    	<button type="button" class="btn btn-sm btn-success pull-right mr-10" onclick="messList.allocate()">分配</button>
							    </div>
							    
							    
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="leavingDao.leavinglist">
                             <thead>
	                         	 <tr>
	                         	     <th class="text-c" width="50"><label
									class="checkbox checkbox-info"><input type="checkbox"
										data-id="" name="checkAll" value=""><span></span></label></th>
	                         	      <th>序号</th>
								      <th>客户号码</th>
								      <th>被叫号码</th>
								      <th>服务热线</th>
								      <th>留言时间</th>
								      <th>处理状态</th>
								      <th>处理时间</th>
								      <th>处理人账号</th>
								      <th>处理人姓名</th>
								      <th>处理结果描述</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                 
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c">
												<label class="checkbox checkbox-info">
												<input type="checkbox" data-state="{{:STATUS}}" data-id="{{:ID}}">
												<span></span></label>
												
											</td>

											<td>{{:#index+1}}</td>
											<td>{{:CALLER}}</td>
											<td>{{:CALLED}}</td>
											<td>{{dictFUN:CALLED 'QUEUE_HOT_LINE'}}</td>
											<td>{{:CREATE_TIME}}</td>
											<td>{{dictFUN:STATUS 'LEAVE_MSG_STATUS'}}</td>
											<td>{{:HAND_TIME}}</td>     
											<td>{{:HAND_USER_ACC}}</td>
											<td>{{:HAND_USER_NAME}}</td>
											<td>{{:HAND_BAKUP}}</td>   
											<td id="leav"> 
												{{judgeState:ID STATUS CALLER VOICE_URL fn='judgeState'}}
											</td>    
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		 requreLib.setplugs("wdate");
		 
		 jQuery.namespace("messList");
         //明细
		 messList.detail = function(id){
			 popup.layerShow({type:1,title:'留言明细',shadeClose:false,area:['660px','500px'],offset:'20px'},"${ctxPath}/pages/together/message-detail.jsp",{id:id});
		 }
		 //处理
		 messList.deal = function(id){
			 
			 popup.layerShow({type:1,title:'留言处理',shadeClose:false,area:['400px','260px'],offset:'20px'},"${ctxPath}/pages/together/message-deal.jsp",{id:id});
		 }
		//外呼 
		 messList.Call = function(id,PHONENUM){
			 top.myCCbar.call(PHONENUM,{},function(result){
				 if(result.state=="1"){
					 var data = {};
						data.id = id;
						data.PHONENUM = PHONENUM;
						ajax.remoteCall("${ctxPath}/servlet/leaving?action=callAdd", data,
								function(result) {
									/* if (result.state == 1) {
										layer.msg(result.msg, {
											icon : 1
										});
										//list.searchData();
										$("#searchForm").searchData();
									} else {
										layer.msg(result.msg, {
											icon : 5
										});
									} */
								});
				 }else{
					 layer.msg("外呼失败", {
							icon : 5
						});
				 }
				 
			 })
			 
		 }
		//忽略
		 messList.ignore = function(id){
			 var data = {};
				data.id = id;
				ajax.remoteCall("${ctxPath}/servlet/leaving?action=Ignore", data,
						function(result) {
							if (result.state == 1) {
								layer.msg(result.msg, {
									icon : 1
								});
								//list.searchData();
								$("#searchForm").searchData();
							} else {
								layer.msg(result.msg, {
									icon : 5
								});
							}
						});
		 }
		//撤回
		 messList.withdraw = function(id){
			 var data = {};
				data.id = id;
				ajax.remoteCall("${ctxPath}/servlet/leaving?action=withdraw", data,
						function(result) {
							if (result.state == 1) {
								layer.msg(result.msg, {
									icon : 1
								});
								//list.searchData();
								$("#searchForm").searchData();
							} else {
								layer.msg(result.msg, {
									icon : 5
								});
							}
						});
		 }
		 //下载
		 messList.download = function(id){
			 window.location.href="${ctxPath}/servlet/leaving?action=Download&id="+id; 
			 /* var data = {};
				data.filePath = url;
			ajax.remoteCall("${ctxPath}/servlet/leaving?action=Download", data,
						function(result) {
						}); */
		 }
		 //分配
		 messList.allocate = function(id){
			 var ids = $("#dataList").find("input[type='checkbox']:checked");
			 
			 if (ids.length < 1) {
					layer.alert('请选择需要分配的数据！');
				} else {
					var state;
					var arr = new Array();
					for (var i = 0; i < ids.length; i++) {
						state = $(ids[i]).attr("data-state");
						if ('01' == state) {
							arr.push($(ids[i]).attr("data-id"));
						} else {
							layer.msg("只能分配待处理的数据");
							return;
						}

					}
				 popup.layerShow({type:1,title:'留言分配',shadeClose:false,area:['400px','260px'],offset:'20px'},"${ctxPath}/pages/together/allocate.jsp",{ids:arr.toString()});
				}
			 
		 }
		 
		 $("input[name='checkAll']").click(
					function() {
						var ifChecked = $(this).prop("checked");
						$("#dataList input:checkbox").prop("checked",
								ifChecked);
					})
		$.views.converters("judgeState", function( id, state,CALLER,VOICE_URL) {
			var temp = "";
			if (state == '01' || state == '04') {
				temp = $("#waitDiv").html();//
			}else if(state == '03'){
				temp = $("#ignoreDiv").html();//
			} else {
				temp = $("#handleDiv").html();
			}
			//temp=temp.replace("leavid",id);
			temp=temp.replace(new RegExp(/(leavid)/g),id);
			temp=temp.replace(new RegExp(/(phone)/g),CALLER);
			temp=temp.replace(new RegExp(/(downloadurl)/g),VOICE_URL);
			return temp;
		});
		//播放
		messList.play = function(id,voiceurl){
			popup.layerShow({
				type: 2,
				title: '录音播放',
				shadeClose : false,
				area: ['650px', '550px'],
				offset: '20px'
			}, "${ctxPath}/pages/together/playVideoView.jsp",{id:id,voiceurl:voiceurl});	
		}
		messList.searchData = function() {

			$("#searchForm").searchData();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>