<!DOCTYPE html>
<html lang="en">
<head>
  <title>通话信息</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
  <style>
    .card-content {
      height: 100% !important;
      width: 100%;
    }
    .yq-card {
      display: flex;
      position: relative;
    }
    .el-tabs.el-tabs--top {
      margin: 0 -24px;
    }
    .el-tabs__nav-wrap.is-top {
      padding: 0 24px;
    }
    .pane-content {
      height: calc(100vh - 124px);
    }
    .el-tabs .el-tabs__header {
      margin-bottom: 0 !important;
    }
  </style>
</head>
<body class="yq-page-full">
  <div id="mainPage" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
      <div class="card-content">
        <!-- 查询表单 -->
        <el-form class="search-form" :model="searchForm" ref="searchForm" label-width="80px" size="small">
          <el-form-item :label="getI18nValue('创建时间')" prop="dateRange">
            <el-date-picker 
              v-model="searchForm.dateRange" 
              type="datetimerange"
              :clearabl="false"
              value-format="yyyy-MM-dd HH:mm:ss"
              :start-placeholder="getI18nValue('开始时间')" 
              :end-placeholder="getI18nValue('结束时间')"
              :default-time="['00:00:00', '23:59:59']"
              @change="daterangeChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="getI18nValue('主叫')" prop="caller">
            <el-input v-model="searchForm.caller" :placeholder="getI18nValue('请输入')" clearable maxlength="12"></el-input>
          </el-form-item>
          <el-form-item :label="getI18nValue('被叫')" prop="called">
            <el-input v-model="searchForm.called" :placeholder="getI18nValue('请输入')" clearable maxlength="12"></el-input>
          </el-form-item>
          <!-- 表单按钮 -->
          <el-form-item class="btns" label-width="0px">
            <el-button type="primary" icon="el-icon-refresh" plain @click="resetForm">
              {{ getI18nValue('重置') }}
            </el-button>
            <el-button type="primary" icon="el-icon-search" @click="doSearch">
              {{ getI18nValue('查询') }}
            </el-button>
          </el-form-item>
        </el-form>
        <!-- Tab列表 -->
        <el-tabs v-model="activeTab" @tab-click="tabClick">
          <el-tab-pane name="1" :label="getI18nValue('通话记录')">
            <div class="pane-content" id="pane1">
              <iframe :src="urls['1']" frameborder="0" style="width: 100%; height: 100%;"></iframe>
            </div>
          </el-tab-pane>
          <el-tab-pane name="2" :label="getI18nValue('未接来电')">
            <div class="pane-content" id="pane2">
              <iframe :src="urls['2']" frameborder="0" style="width: 100%; height: 100%;"></iframe>
            </div>
          </el-tab-pane>
          <el-tab-pane name="3" :label="getI18nValue('语音留言')">
            <div class="pane-content" id="pane3">
              <iframe :src="urls['3']" frameborder="0" style="width: 100%; height: 100%;"></iframe>
            </div>
          </el-tab-pane>
          <el-tab-pane name="4" :label="getI18nValue('语音漏话')">
            <div class="pane-content" id="pane4">
              <iframe :src="urls['4']" frameborder="0" style="width: 100%; height: 100%;"></iframe>
            </div>
          </el-tab-pane>
          <el-tab-pane name="5" :label="getI18nValue('进线轨迹')">
            <div class="pane-content" id="pane5">
              <iframe :src="urls['5']" frameborder="0" style="width: 100%; height: 100%;"></iframe>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</body>
<!-- 基础的 js 资源 -->
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script type="text/javascript" src="/cc-callloss/static/js/my_i18n.js?v=2021110901"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>
<script type="text/javascript" src="/cc-callloss/static/utils/utils.js"></script>
<script type="text/javascript" src="/cc-callloss/static/js/time.js"></script>

<script>
  var mainPage = new Vue({
    el: '#mainPage',
    data() {
      return {
        loading: false,
        urlParams: {},
        activeTab: '1',
        searchForm: {
          stats: '1',
          dateRange: [],
          custPhone: '',
          custPhone2: '',
          caller: '',
          called: '',
          opener: '',
          agentId: ''
        },
        urls: {
          '1': '/cc-report/pages/vue/callRecord/tabs/callRecordList.html?ciframe=true'
        }
      }
    },
    methods: {
      resetForm() {
        for (var key in this.searchForm) {
          if (key == 'dateRange') {
            this.searchForm.dateRange = [getTodayStartTime(), getTodayEndTime()]
          } else this.searchForm[key] = ''
        }
        // this.$refs.searchForm.resetFields()
      },
      tabClick() {
        let urlMap = {
          '2': '/cc-callloss/pages/vue/record/callMissedList.html?ciframe=true',
          '3': '/cc-callloss/pages/vue/record/voiceMsgList.html?ciframe=true',
          '4': '/cc-callloss/pages/vue/record/voiceMissedList.html?ciframe=true',
          '5': '/cc-report/pages/vue/callRecord/tabs/ivrTraceList.html?ciframe=true',
        }
        // 若未加载或此界面
        if (!this.urls[this.activeTab]) {
          if (this.searchForm.dateRange != null && this.searchForm.dateRange.length) {
            urlMap[this.activeTab] += `&startDate=${this.searchForm.dateRange[0]}&endDate=${this.searchForm.dateRange[1]}&caller=${this.searchForm.caller}&called=${this.searchForm.called}`
          }
          this.$set(this.urls, this.activeTab, urlMap[this.activeTab])
        } else this.doSearch()
      },
      doSearch() {
        let iframe = document.querySelector(`#pane${this.activeTab} iframe`)
        if (iframe) {
          let paneVue = iframe.contentWindow ? iframe.contentWindow.mainPage : {}
          paneVue.searchForm.dateRange = this.searchForm.dateRange
          paneVue.searchForm.caller = this.searchForm.caller
          paneVue.searchForm.called = this.searchForm.called
          paneVue.doSearch()
        }
      },
      daterangeChange(val) {
        if (val && val.length) {
          if (twoDaysDifference(val[0], val[1]) > 31) {
            this.$message.warning(getI18nValue("该条件下，最长可选时间为一个月！"))
            this.searchForm.dateRange = [getTodayStartTime(), getTodayEndTime()]
          }
        } else {
          this.$message.warning(getI18nValue("选择时间不能为空！"))
          this.searchForm.dateRange = [getTodayStartTime(), getTodayEndTime()]
        }
        this.doSearch()
      },
    },
    mounted() {
      this.urlParams = urlParamsToObj()
      this.searchForm.dateRange = [getTodayStartTime(), getTodayEndTime()]
      this.searchForm.custPhone = this.urlParams.custPhone || ''
      this.searchForm.custPhone2 = this.urlParams.custPhone2 || ''
      this.searchForm.caller = this.urlParams.caller || ''
      this.searchForm.opener = this.urlParams.opener || ''
      if (this.urlParams.vuebox) document.body.classList.add('vue-box')

    }
  })
</script>
</html>
