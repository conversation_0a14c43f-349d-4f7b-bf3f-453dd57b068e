/* ������ʽ��css */
html,
body {
	color: #333;
	margin: 0;
	height: 100%;
	font-family: "Myriad Set Pro", "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
}

* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

a {
	text-decoration: none;
	color: #000;
}

img {
	border: 0;
}

body {
	background: #fff;
	color: #666;
	font: 12px/150% <PERSON><PERSON>, Verdana, "microsoft yahei";
}

html,
body,
div,
dl,
dt,
dd,
ol,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
button,
fieldset,
form,
input,
legend,
textarea,
th,
td {
	margin: 0;
	padding: 0;
}

a {
	text-decoration: none;
	color: #08acee;
}

button {
	outline: 0;
}

img {
	border: 0;
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	font: inherit;
	color: inherit;
	outline: none;
}

li {
	list-style: none;
}

a {
	color: #666;
}

a:hover {
	color: #eee;
}

.clearfix::after {
	clear: both;
	content: ".";
	display: block;
	height: 0;
	visibility: hidden;
}

.clearfix {
	content: '';
	height: 0;
	line-height: 0;
	display: block;
	visibility: hidden;
	clear: both;
}

/* ��Ҫ������ʽcss */
.service-content {
	padding: 45px 10px 50px;
	background: #f7f8fa;
	position: relative;
	/* overflow: hidden; */
	height: 100%;
}

.service-box {
	max-width: 90%;
	margin: 0 auto;
	padding: 0 20px;
	position: relative;
	z-index: 2;
	margin-top: 100px;
}

.service-title h3 {
	text-align: center;
	color: #333;
	font-weight: 600;
	line-height: 1.5;
	font-size: 28px;
}

.service-info {
	font-size: 18px;
	text-align: center;
}

.service-info p {
	color: #333;
}

.service-info a {
	display: block;
	margin: 15px auto 0;
	font-size: 14px;
	color: #00a4ff;
}

.service-info a:hover {
	text-decoration: underline;
}

.service-title {
	margin-bottom: 30px;
}

.service-list {
	/* -webkit-box-shadow: rgba(0, 0, 0, .1) 0 0 5px;
	box-shadow: rgba(0, 0, 0, .1) 0 0 5px; */
	margin-top: 50px;
	font-size: 0;
	/* background-color: #fff; */
	text-align: center;
	display: flex;
	/* flex-wrap:wrap; */
}
.Boxitem{
	font-size: 18px;text-align: left;padding: 10px;border-left: 5px solid #00A4FF;background-color:#fff;border-bottom: 1px solid #ccc;
}
.shadow{
	flex: 1;
	margin-right: 100px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}
.shadow:last-child{
	flex: 1;
	margin-right: 0;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.service-list ul {
	display: flex;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
}

.service-list ul li {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	/* display: table-cell; */
	flex: 1;
	width: 16.6666%;
	position: relative;
	border-right: 1px solid #f5f6f7;
}

.service-list ul li a {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: block;
	padding: 20px 0 25px;
	height: 100%;
}

.service-list ul li a h4 {
	color: #333;
	font-size: 16px;
	font-weight: 400;
	margin-top: 15px;
}

.service-list-bj {
	position: relative;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100px;
	height: 100px;
	border-radius: 10%;
	display: inline-block;
	overflow: hidden;
}

.service-list-bj img {
	position: absolute;
	left: 50%;
	top: 50%;
	margin: -24px 0 0 -24px;
	width: 48px;
	height: 48px;
}

.active-img {
	opacity: 0;
}

.service-list ul li:hover .active-img {
	opacity: 1
}

.service-list ul li:hover h4 {
	color: #08acee;
}

.service-animation-bg {
	position: fixed;
	top: 0;
	left: 50%;
	margin-left: -960px;
	width: 1920px;
	z-index: 1;
	opacity: .07;
	font-size: 0;
	text-align: center;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-animation: bgScroll 24s infinite linear;
	animation: bgScroll 24s infinite linear;
	will-change: transform;
}

.service-animation-group {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	padding: 30px;
	min-height: 280px;
}

.service-item {
	height: 80px;
	margin: 0 50px 50px;
	display: inline-block;
	vertical-align: middle;
	line-height: 80px;
	-webkit-filter: grayscale(100%);
	-webkit-filter: gray;
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: gray;
}

.service-item.item001 {
	width: 300px;
	height: 60px;
	background-image: url(../images/logo.png);
}

.service-item.item002 {
	width: 300px;
	height: 81px;
	background-image: url(../images/logo.png);
}

.service-item.item003 {
	width: 150px;
	height: 57px;
	background-image: url(../images/logo.png);
}

.service-item.item004 {
	width: 300px;
	height: 79px;
	background-image: url(../images/logo.png);
}

.service-item.item005 {
	width: 94px;
	height: 94px;
	background-image: url(../images/logo.png);
}

.service-item.item006 {
	width: 103px;
	height: 55px;
	background-image: url(../images/logo.png);
}

.service-item.item007 {
	width: 220px;
	height: 81px;
	background-image: url(../images/logo.png);
}

.service-item.item008 {
	width: 72px;
	height: 72px;
	background-image: url(../images/logo.png);
}

.service-item.item009 {
	width: 300px;
	height: 66px;
	background-image: url(../images/logo.png);
}

.service-item.item010 {
	width: 216px;
	height: 35px;
	background-image: url(../images/logo.png);
}

.service-item.item011 {
	width: 177px;
	height: 32px;
	background-image: url(../images/logo.png);
}

.service-item.item012 {
	width: 178px;
	height: 53px;
	background-image: url(../images/logo.png);
}

.service-item.item013 {
	width: 200px;
	height: 58px;
	background-image: url(../images/logo.png);
}

.service-item.item014 {
	width: 190px;
	height: 85px;
	background-image: url(../images/logo.png);
}

@-webkit-keyframes bgScroll {
	from {
		-webkit-transform: translateY(0);
		transform: translateY(0)
	}

	to {
		-webkit-transform: translateY(-33.333%);
		transform: translateY(-33.333%)
	}
}

@keyframes bgScroll {
	from {
		-webkit-transform: translateY(0);
		transform: translateY(0)
	}

	to {
		-webkit-transform: translateY(-33.333%);
		transform: translateY(-33.333%)
	}
}



#toLast{
	display: none;color: #00A4FF;font-size: 16px;transform: translateY(20px);cursor: pointer;
	position: absolute;
}





