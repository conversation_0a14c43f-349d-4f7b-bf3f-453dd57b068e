<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="抽取规则配置"></title>
	</EasyTag:override>
<EasyTag:override name="content">
		<form id="easyform" data-mars="queryRuleDao.queryRuleRecord" method="post" data-pk="${param.id}" data-mars-prefix="queryRule." class="xssFilter">
				  <input type="hidden" name="queryRule.ID" value="${param.id}">
				  <input type="hidden" name="source" value="${param.source}">
				  <table class="table table-edit table-vzebra mt-10" >
	                    <tbody >
		                    <tr>
			                   <td class="required" width="100px" i18n-content="名称"></td>
			                   <td><input type="text" name="queryRule.NAME" data-rules="required|maxlength=100" i18n-data-original-title="不能为空" class="form-control input-sm"></td>
			                </tr>
		                    <!-- <tr>
			                   <td class="required" width="100px" i18n-content="业务分类"></td>
			                   <td>
			                   		<select name="queryRule.BUSI_TYPE" class="form-control input-sm" i18n-data-original-title="不能为空" data-rules="required" 
			                   				data-mars="QcCommonDao.getDict(QC_CHANNEL_TYPE)">
			                        	<option value="" i18n-content="请选择"></option>
			                        </select></td>
			                </tr> -->
			                <!-- <tr>
			                   <td class="required" width="100px" i18n-content="类型"></td>
			                   <td>
			                   		<select name="queryRule.TYPE" class="form-control input-sm" i18n-data-original-title="不能为空" data-rules="required">
			                        	<option value="" i18n-content="请选择"></option>
			                        	<option value="01" i18n-content="公共"></option>
			                        	<option value="02" i18n-content="个人"></option>
			                        </select></td>
			                </tr> -->
				            <tr>
				                <td i18n-content="备注"></td>
				               	<td>
				               	    <textarea cols="5" rows="4" name="queryRule.BACKUP" class="form-control input-sm" data-rules="maxlength=200"  ></textarea>
				               	</td>
				            </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="queryRule.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("queryRule");
	
	queryRule.id='${param.id}';
	queryRule.qcItemData={};
	
	$(function(){
		$("#easyform").render(); 
	});
	
	queryRule.ajaxSubmitForm = function(){
		if(!form.validate("#easyform")){
			i18nTooltip();
			return;
		};
		if(form.validate("#easyform")){
			if(queryRule.id=="null"||queryRule.id==''){
				queryRule.insertData(); 
			}else{
				queryRule.updateData(); 
			}
		}
	}
	
	queryRule.insertData = function() {
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/queryRule?action=addQueryRule",data,function(result) { 
			if(result.state == 1){
				layer.msg(getI18nValue(result.msg),{icon: 1},function(){
					queryRule.searchData();
					layer.closeAll();
				});
			}else{
				layer.alert(getI18nValue(result.msg),{icon: 5,title:getI18nValue('信息'),
					btn : [ getI18nValue('确定'), getI18nValue('取消') ]});
			}
		});
	}
	
	queryRule.updateData = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/queryRule?action=updateQueryRule",data,function(result) { 
			if(result.state == 1){
				layer.msg(getI18nValue(result.msg),{icon: 1},function(){
					queryRule.searchData();
					layer.closeAll();
				});
			}else{
				layer.alert(getI18nValue(result.msg),{icon: 5,title:getI18nValue('信息'),
					btn : [ getI18nValue('确定'), getI18nValue('取消') ]});
			}
		});
	}
	
	queryRule.updateTotalScore = function(obj){
		if(obj&&obj.val()){
			var thisVal = obj.val();
			var patrn=/^([1-9]\d*|0)(\.\d*[1-9])?$/; 
	        if (!patrn.exec(thisVal)){
	            test_value=Number(thisVal);
	            obj.val(thisVal);
	        }
			if(thisVal<0){
				obj.val(0);
			}
			if(thisVal > 10000){
				obj.val(10000);
			}
			
		}
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>