<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择技能组</title>
	<style>
		#dataList2 tr{cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="PrefixSelectorForm" data-pk="${param.userId }" style="margin-bottom: 65px">
             	<div class="ibox">
	              	<div class="ibox-content" style="padding: 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="6" data-container="dataList2" data-template="list-template2" data-auto-fill="5" data-mars="prefix.selectorList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">选择</th>
								      <th>分组名称</th>
								      <th>外显号码</th>
								      <th>状态</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList2">
                             </tbody>
		                 </table>
                        	 <script id="list-template2" type="text/x-jsrender">
								   {{for  list}}
										<tr class="{{:SKILL_GROUP_ID}}">
											<td class="text-c"><label class="radio radio-info"><input type="radio" data-name="{{:PREFIX_NUM}}" name="prefixNum" {{if PREFIX_NUM == '${param.prefixNum }'}}checked="checked"{{/if}} value="{{:PREFIX_NUM}}"/><span></span></label></td>
											<td>{{:GROUP_NAME}}</td>                                         
											<td>{{:PREFIX_NUM}}</td>                            
											<td>{{if PREFIX_STATE == 0}}正常{{else}}暂停{{/if}}</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp">
	                     		<jsp:param value="6" name="pageSizes"/>
	                     	</jsp:include>
	                     </div>
	              	</div> 
	              	<div class="layer-foot text-c" style="position: fixed;">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="PrefixSelector.saveData()">确定</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("PrefixSelector");
		
		$(function(){
			$("#PrefixSelectorForm").render({success:function(){
				$("#dataList2 tr").on('click',function(event){
					if(event.target.type == 'radio'|| event.target.type == 'checkbox') { event.stopPropagation(); return;}
					var val=$(this).find("input").prop("checked");
					$(this).find("input").prop("checked",!val);
				});
			}});
		});
		
		PrefixSelector.saveData=function(){
			var val=$('input:radio[name="prefixNum"]:checked').val();
			if(val==null){
				layer.msg("请选择技能组!");
				return;
			}
			
			parent.changePrefix(val);
			popup.layerClose();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>