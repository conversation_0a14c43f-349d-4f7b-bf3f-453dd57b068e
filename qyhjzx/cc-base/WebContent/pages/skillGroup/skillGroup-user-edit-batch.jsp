<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="技能组用户编辑"></title>
	</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" method="post"  autocomplete="off" data-mars-prefix="gkuser.">
				  <input type="hidden" name="gkuser.USER_ID" class="form-control input-sm" value="${param.userId}">
				  <input type="hidden" name="gkuser.USER_ACC" class="form-control input-sm" value="${param.userAcc}">
				  <input type="hidden" name="gkuser.SKILL_GROUP_ID" class="form-control input-sm" value="${param.skillGroupId}">
				  <input type="hidden" name="gkuser.SKILL_GROUP_NAME" class="form-control input-sm" value="${param.skillGroupName}">
				  <table class="table table-edit table-vzebra mt-10">
	                    <tbody>
	                    	  <tr>
								<td style="width: 100px" >
								<span class="required" i18n-content="优先级"></span>
								<span id="sub"><i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i></span>
								</td>
								<td>
									<select class="form-control input-sm" onchange="GroupUserEidt.selectChange(this)">
										<option value="99" i18n-content="高"></option>
										<option value="50" i18n-content="中"></option>
										<option value="1" i18n-content="低"></option>
										<option value="0" i18n-content="自定义"></option>
									</select>
								</td>
							</tr>
	                        <tr>
		                        <td class="required" style="width: 100px" i18n-content="优先级"></td>
		                        <td><input type="number" name="gkuser.IDX_ORDER" id="IDX_ORDER" data-rules="required" value="99" oninput="if(value>100)value=100;if(value<0)value=0;" readOnly class="form-control input-sm"></td>
	                        </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="GroupUserEidt.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				    </div>
				    <div class="stat-desc" id="statDesc" style="display:none;color:#676A6C;">
			            <p i18n-content="数字越大越优先"></p>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("GroupUserEidt");
	
	GroupUserEidt.ajaxSubmitForm = function(){
		if(form.validate("#editForm")){
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=batchUpdateGroupUserOrder",data,function(result) { 
				if(result.state == 1){
					parent.layer.closeAll();
					parent.GroupUser.loadData();
				}else{
					parent.layer.alert(result.msg);
				}
			});
		};
	}
	
	GroupUserEidt.selectChange = function(obj){
		if(obj.value=="99"||obj.value=="50"||obj.value=="1"){
			$("#IDX_ORDER").val(obj.value);
			$("#IDX_ORDER").attr("readOnly","true");
		}else{
			$("#IDX_ORDER").val("");
			$("#IDX_ORDER").removeAttr("readOnly");
		}
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>