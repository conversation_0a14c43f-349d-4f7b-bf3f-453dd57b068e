<%@page import="org.easitline.common.utils.calendar.EasyDate"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%
	request.setAttribute("curTime", EasyDate.getCurrentDateString("yyyy-MM-dd"));
%>
<EasyTag:override name="head">
	<title i18n-content="新增配置"></title>
	<style>
		.req{ z-index:3 !important}
		.ibox-content-header{font-size: 18px;border-bottom: none;margin-top: 5px;border-bottom: 1px solid #eee;text-align: left;line-height: 22px }
		.table-vzebra tbody > tr > td:nth-child(odd), .table-vzebra tbody > tr > th:nth-child(odd){line-height: 40px}
		.layer-skin .layui-layer-content>.container-fluid:after {content: '';display: block;height: 0px;}
		.radio-inline{line-height:20px;padding-top:3px;height:30px;}
	</style>
	<style>
		.select2-container {
			z-index: 9999999999999999;
		}
	</style>
</EasyTag:override>

<EasyTag:override name="content">
		    <form id="easyform" class="form-horizontal">
				<table class="table table-edit table-vzebra mt-20">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required" i18n-content="菜单标志"></td>
		                    <td><input type="text" name="menu.MENU_ID" i18n-data-title="菜单标志" class="form-control input-sm" data-rules="required" i18n-data-original-title="不能为空!"></td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required" i18n-content="名称"></td>
		                    <td><input type="text" name="menu.MENU_NAME" i18n-data-title="名称" class="form-control input-sm" data-rules="required" i18n-data-original-title="不能为空!"></td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required" i18n-content="URL"></td>
		                    <td><input type="text" name="menu.MENU_URL" i18n-data-title="URL" class="form-control input-sm" data-rules="required" i18n-data-original-title="不能为空!"></td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required" i18n-content="相关业务"></td>
    						<td>
    							<select  multiple="multiple" 	name="menu.BUSI_ID" id="BUSI_ID" class="form-control input-sm" 
									i18n-data-title="相关业务" style="width: 250px; display: inline-block; " data-mars="common.getDict(OP_MENU_BUSI)" >
								</select>
							</td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required" i18n-content="关联角色"></td>
    						<td><select  multiple="multiple" 	name="menu.ROLE_ID" id="ROLE_ID" class="form-control input-sm"
								i18n-data-title="关联角色" style="width: 288px;display: inline-block;" data-mars="role.roleDict" >
							</select></td>	
			            </tr>
			            <tr>
		                    <td width="150px" class="required" i18n-content="状态"></td>
		                    <td><select name="menu.STATUS" data-rules="required" class="form-control input-sm"
								i18n-data-title="状态" style="width: 288px; display: inline-block;" data-mars="common.getDict(ENABLE_STATUS)">
							</select></td>
			            </tr>
			            <tr>
		                    <td width="150px" class="required" i18n-content="全媒体自动打开"></td>
		                    <td><select name="menu.MEDIA_AUTO_OPEN" data-rules="required" class="form-control input-sm"
								i18n-data-title="全媒体自动打开" style="width: 288px; display: inline-block;">
								<option value="N" i18n-content="否"></option>
								<option value="Y" i18n-content="是"></option>
							</select></td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required" i18n-content="序号"></td>
		                  	<td><input type="number" name="menu.SORT_NUM" i18n-data-title="序号" value="1" class="form-control input-sm" data-rules="required" i18n-data-original-title="不能为空!"></td>
			            </tr>
			             <tr>
		                    <td width="80px" i18n-content="备注"></td>
		                    <td>
		                    <textarea rows="5" cols="80" name="menu.BAKUP" i18n-data-title="备注" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
			    </table>	
			    <div class="layer-foot text-c">
				      <button type="button" class="btn btn-primary btn-sm" onclick="menu.addMenu()" id="saveBtn" i18n-content="提交">  </button>
				      <button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	requreLib.setplugs('select2');
	var menu={
		addMenu:function(){
			if(form.validate("easyform")){
				var data = form.getJSONObject("#easyform");
				var  busis=$("#BUSI_ID").val();
				if(!busis){
					parent.layer.alert(getI18nValue("请选择相关业务"),{icon: 5,btn:[getI18nValue('确定')]});
					return;
				}
				var  roleId=$("#ROLE_ID").val();
				if(!roleId){
					parent.layer.alert(getI18nValue("请选择相关角色"),{icon: 5,btn:[getI18nValue('确定')]});
					return;
				}
			 	ajax.remoteCall("${ctxPath}/servlet/menu?action=menuAdd",data,function(result) { 
					if(result.state == 1){
						parent.layer.msg(getI18nValue(result.msg),{icon: 1,time:1200,offset:'40px'},function(){
							
						});
						popup.layerClose("#easyform");
						parent.sidebarLayui.searchMenuData();
					}else{
						parent.layer.alert(getI18nValue(result.msg),{icon: 5});
					}
				}); 	 
			}				 
		},
	 }
	$(function() {
		$("#easyform").render({success:function(data){
			$("#BUSI_ID").select2({ theme:'bootstrap', width:'288px'});
			$("#ROLE_ID").select2({ theme:'bootstrap', width:'288px'});
		}});
	});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
	