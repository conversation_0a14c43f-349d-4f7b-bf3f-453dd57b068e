<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>全媒体回复语</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 回复语管理</h5>
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon">渠道编号</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">渠道名称</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">类型</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick=""><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							    <div class="input-group input-group-sm pull-right">
									  <button type="button" class="btn btn-sm btn-success" onclick=""> +添加</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="">
                             <thead>
	                         	 <tr>
	                         	      <th>序号</th>
	                         	      <th>类型</th>
	                         	      <th>渠道类型</th>
	                         	      <th>渠道编号</th>
	                         	      <th>渠道名称</th>
	                         	      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                   <tr>
                                       <td>1</td>
                                       <td>语音</td>
                                       <td>语音</td>
                                       <td>1</td>
                                       <td>语音</td>
                                       <td>
                                           <a href="javascript:;">修改</a>&nbsp;
                                           <a href="javascript:;" onclick="replyWord.config()">配置</a>&nbsp;
                                           <a href="javascript:;">删除</a>
                                       </td>
                                   </tr>
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("replyWord");
		
		replyWord.config = function(){
			 popup.openTab("${ctxPath}/pages/agentconfig/media/replyword-config.jsp","全媒体回复语配置",null)
			 //popup.openTab("${ctxPath}/pages/media/zjzxto.jsp","开始质检",null)
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>