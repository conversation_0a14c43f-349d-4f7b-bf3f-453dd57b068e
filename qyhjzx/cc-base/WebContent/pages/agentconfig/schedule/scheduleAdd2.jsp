<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="工作时间段配置"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
		.layui-input{
		height: 30px;
		}
		.work-time{
			width: 47%;
	    	display: inline;
	    }
	    .add{
	        position: relative;
		    height: 42px;
		    line-height: 42px;
		   
		    color: #333;
		    background-color: #f2f2f2;
		    cursor: pointer;
		    font-size: 14px;
		   
		    }
		    .del{
		    margin-left: 12px;
		    }
	</style>
</EasyTag:override>

<EasyTag:override name="content">
	<form id="editForm" class="form-horizontal" method="post">
	    <div class="layui-form">
	    	<div class="layui-fluid" id="insertHtml">
	    	<div class="layui-collapse " lay-filter="test1">
	    	 <div class="layui-colla-item layui-col-xs12 layui-col-sm12 layui-col-md12" >
				    <h2 class="layui-colla-title" i18n-content="基本信息"></h2>
				     <div class="layui-colla-content  layui-show" >
	    		<div class="layui-row">
	    			<div class="layui-col-md6">
	    				<div class="layui-form-item">
						    <label class="layui-form-label required" style="width: 100px" i18n-content="名称"></label>
						    <div class="layui-input-block">
						    	<input type="text" name="NAME" id="NAME"  autocomplete="off" class="layui-input" maxlength="19" data-rules="required">
						    </div>
						</div>
	    			</div>
	    			<div class="layui-col-md6">
	    				<div class="layui-form-item">
						    <label class="layui-form-label required" style="width: 100px" i18n-content="类型"></label>
						    <div class="layui-input-block">
						    	<select data-rules="required" id="TIME_TYPE" name="TIME_TYPE" data-rules="required"  data-mars="dict.getDictList(TIME_TYPE)"  >
					        		
					        	</select>
						    	
						    </div>
						</div>
	    			</div>
	    		</div>
	    		
	    		<div class="layui-row">
	    			<div class="layui-col-md6">
	    				<div class="layui-form-item">
						    <label class="layui-form-label required" style="width: 100px" i18n-content="启用状态"></label>
						    <div class="layui-input-block">
						    <select   id="ENABLE_STATUS" name="ENABLE_STATUS" data-rules="required" data-context-path="/cc-base" data-mars="dict.getDictList(ENABLE_STATUS)" >
					        	</select>
						    	
						    </div>
						</div>
	    			</div>
	    			<div class="layui-col-md6">
	    				<div class="layui-form-item">
						    <label class="layui-form-label required" style="width: 100px" i18n-content="序号"></label>
						    <div class="layui-input-block">
						    	<input type="number" name="SORT_NUM" id="SORT_NUM" data-rules="required" value="1" autocomplete="off" class="layui-input" maxlength="19" >
						    	
						    </div>
						</div>
	    			</div>
	    		</div>
	    		</div>
	    		</div>
	    		</div>
	    	</div>
	    </div>
	    
    	<div class="layer-foot text-c">
	      	<button type="button" class="btn btn-primary btn-sm" onclick="scheduleAdd.ajaxSubmitForm()"  i18n-content="保存">  </button>
	      	<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	 jQuery.namespace("scheduleAdd");
		var layuiForm;
		var records = new Map();
		$(function() {
			init();
			execI18n();
		});
		
		layui.use(['laydate','form'], function(){
			var laydate = layui.laydate;
			var form = layui.form;
			layuiForm = form;
			
			for(var i=0;i<7;i++){
				for(var j=1;j<=1;j++){
					laydate.render({
						elem: '#BEGIN_TIME'+i+'-'+j
						,type: 'time'
						,format: 'HH:mm'
						,lang: getDateLang()
						,done: function(value, date){
							var id = this.elem.attr("id").replace("BEGIN_TIME","");
							var arr = id.split("-");
							var i = Number(arr[0]);
							var j = Number(arr[1]);
							scheduleAdd.checkTime(value,i,j,'begin');
						  }
						
					});
					laydate.render({
						elem: '#END_TIME'+i+'-'+j
						,type: 'time'
						,format: 'HH:mm'
						,lang: getDateLang()
						,done: function(value, date){
							var id = this.elem.attr("id").replace("END_TIME","");
							var arr = id.split("-");
							var i = Number(arr[0]);
							var j = Number(arr[1]);
							scheduleAdd.checkTime(value,i,j,'end');
						  }
					});
				}
			}
			
			
			form.render();
		});
		
		$("#editForm").render({success:function(result){
		if(layuiForm){
			layuiForm.render();
		}
		}});  
		
		scheduleAdd.ajaxSubmitForm = function(type){
				 if(form.validate("#editForm")){				 
					 $('#divBtn button').prop('disabled', true);
					 var data =  scheduleAdd.getData();
					 ajax.remoteCall("${ctxPath}/servlet/schedule?action=save",data,  function(result)  {                        
	                        if  (result.state  ==  1)  {                            
	                            layer.closeAll();                            
	                            layer.msg(result.msg,   {                                
	                                icon:  1                            
	                            });                            
	                            window.location.reload()
	  
	                        } 
	                        else  {                            
	                            layer.alert(result.msg,   {                                
	                                icon:  5                            
	                            });                        
	                        }                    
	                    });
				 }else{
					 i18nTooltip();
					 return;
				 };
				 
			}
		
		function init(){
	          var week = ["星期一","星期二","星期三","星期四","星期五","星期六","星期日"];
	          var html ='';
	          for(var i=0;i<7;i++){
	        	  
	        	  html +='<div class="layui-colla-item layui-col-xs12 layui-col-sm12 layui-col-md12" >'+
	        		  ' <div class="layui-row">'+
		    		'	<div class="layui-col-md11">'+
				'	 <h2 class="layui-colla-title" i18n-content="'+week[i]+'"></h2>'+
				'<input type="hidden" value="'+i+'" name="BUSI_ID'+i+'" id="BUSI_ID'+i+'" >'+
			'</div><div class="layui-col-md1 add" ><button type="button" class="btn btn-sm btn-success btn-outline" onclick="scheduleAdd.addRecord('+i+')" i18n-content="新增"></button></div>'+
		'</div>'+
		'<div class="layui-colla-content layui-show " id="week'+i+'">';
		var record = [];
		for(var j= 1 ;j<=1;j++){
			record.push(j);
			html+=	' <div class="layui-row " id="time'+i+'-'+j+'">'+
			'<div class="layui-col-md2">'+
			'<div class="layui-form-item">'+
					'<input type="text" value="'+getI18nValue("时间段")+j+'" name="TIME_CODE'+i+'-'+j+'"  id="TIME_CODE'+i+'-'+j+'" autocomplete="off" class="layui-input" data-rules="required" >'+
						'</div>'+
				'</div>'+
				'<div class="layui-col-md2">'+
				'	<div class="layui-form-item" style="width: 120px">'+
				'<input type="hidden" value="" id ="NEW_HIDDEN_WORK_TYPE'+i+'-'+j+'"/>'+
						    ' <select lay-filter="WORK_TYPE" data-rules="required" name="WORK_TYPE'+i+'-'+j+'" id="WORK_TYPE'+i+'-'+j+'" class="NEW_WORK_TYPE" data-rules="required"  data-mars="dict.getDictList(WORK_TYPE)">'+
					        		
					        	'</select>'+
					'	</div>'+
				'</div>'+
				'<div class="layui-col-xs7 layui-col-sm7 layui-col-md7">'+
					'<div class="layui-form-item"> '+
						 '   <label class="layui-form-label required" style="width: 100px" i18n-content="工作时间"></label>'+
						 '   <div class="layui-input-block">'+
						   ' <input type="text" data-rules="required" value="00:00" style="width: 140px" name="BEGIN_TIME'+i+'-'+j+'" id="BEGIN_TIME'+i+'-'+j+'"  autocomplete="off" class="form-control input-sm work-time">-'+
						   ' <input type="text" data-rules="required" value="23:59" style="width: 140px" name="END_TIME'+i+'-'+j+'" id="END_TIME'+i+'-'+j+'"  autocomplete="off" class="form-control input-sm work-time" >'+
						    	'</div>'+
						'</div>'+
				'</div>'+
				'<div class="layui-col-md1">'+
				'	<div class="layui-form-item" >'+
						    ' <button type="button" class="btn btn-sm btn-danger btn-outline del" onclick="scheduleAdd.delRecord('+i+','+j+')" i18n-content="删除"></button>'+
					'	</div>'+
				'</div>'+
				'</div>'
		}
			html+='</div></div>';
		
			records.set(i,record);
	          }
	        	
	          $("#insertHtml").append(html);
	          
	          
		}
		
		scheduleAdd.delRecord = function (i,j){
			var record = [];
			record =  records.get(i);
			 if("undefined"==typeof(record)){
        		 return;
        	 }
			record.remove(j);
			records.set(i,record);
			$("#time"+i+"-"+j).remove();
		}
		
		scheduleAdd.addRecord = function (i){
			//记录当前页面现有的工作时间样式
			$(".NEW_WORK_TYPE").each(function() {
    		    var id = this.id;
    			$("#NEW_HIDDEN_"+id).val($("#"+id).val());
    		});
			var record =[];
			record = records.get(i);
			var j = 1;
			 if("undefined"!=typeof(record)&&record.length>0){
				j = Math.max.apply(null,record)+1
			}
			record.push(j);
			records.set(i,record);
			var html=' <div class="layui-row " id="time'+i+'-'+j+'">'+
			'<div class="layui-col-md2">'+
			'<div class="layui-form-item">'+
					'<input type="text" value="'+getI18nValue("时间段")+j+'" name="TIME_CODE'+i+'-'+j+'"  id="TIME_CODE'+i+'-'+j+'" autocomplete="off" class="layui-input" data-rules="required">'+
						'</div>'+
				'</div>'+
				'<div class="layui-col-md2">'+
				'	<div class="layui-form-item" style="width: 120px">'+
				'<input type="hidden" value="" id ="NEW_HIDDEN_WORK_TYPE'+i+'-'+j+'"/>'+
						    ' <select  data-rules="required" name="WORK_TYPE'+i+'-'+j+'" id="WORK_TYPE'+i+'-'+j+'" class="NEW_WORK_TYPE" autocomplete="off" data-rules="required" data-mars="dict.getDictList(WORK_TYPE)" >'+
					        	'</select>'+
					'	</div>'+
				'</div>'+
				'<div class="layui-col-xs7 layui-col-sm7 layui-col-md7">'+
					'<div class="layui-form-item"> '+
						 '   <label class="layui-form-label required" style="width: 100px" i18n-content="工作时间"></label>'+
						 '   <div class="layui-input-block">'+
						   ' <input type="text" data-rules="required" style="width: 140px" name="BEGIN_TIME'+i+'-'+j+'" id="BEGIN_TIME'+i+'-'+j+'"  autocomplete="off" class="form-control input-sm work-time">-'+
						   ' <input type="text" data-rules="required" style="width: 140px" name="END_TIME'+i+'-'+j+'" id="END_TIME'+i+'-'+j+'"  autocomplete="off" class="form-control input-sm work-time" >'+
						    	'</div>'+
						'</div>'+
				'</div>'+
				'<div class="layui-col-md1">'+
				'	<div class="layui-form-item" >'+
						    ' <button type="button" class="btn btn-sm btn-danger btn-outline del" onclick="scheduleAdd.delRecord('+i+','+j+')" i18n-content="删除"></button>'+
					'	</div>'+
				'</div>'+
				'</div>';
				
				$("#week"+i).append(html);
				layui.use(['laydate','form'], function(){
					var laydate = layui.laydate;
					var form = layui.form;
					laydate.render({
						elem: '#BEGIN_TIME'+i+'-'+j
						,type: 'time'
						,format: 'HH:mm'
						,lang: getDateLang()
						,done: function(value, date){
							var id = this.elem.attr("id").replace("BEGIN_TIME","");
							var arr = id.split("-");
							var i = Number(arr[0]);
							var j = Number(arr[1]);
							scheduleAdd.checkTime(value,i,j,'begin')
							/* if(!scheduleAdd.checkTime(value,i,j,'begin')){
								 this.elem.value="";
								 form.render();
							} */
						  }
						
					});
					laydate.render({
						elem: '#END_TIME'+i+'-'+j
						,type: 'time'
						,format: 'HH:mm'
						,lang: getDateLang()
						,done: function(value, date){
							var id = this.elem.attr("id").replace("END_TIME","");
							var arr = id.split("-");
							var i = Number(arr[0]);
							var j = Number(arr[1]);
							scheduleAdd.checkTime(value,i,j,'end');
						  }
						
					});
					form.render();
					execI18n();
					$("#editForm").render({success:function(result){
						if(layuiForm){
							$(".NEW_WORK_TYPE").each(function() { //回显关键
			                	var id = this.id;
								var value = $("#NEW_HIDDEN_"+id).val();
			                    $(this).children("option").each(function() { //循环读取
			                        if (this.value == value) { //进行比较
			                            $(this).attr("selected","selected"); //选中
			                        }
			                    });
			                });
							layuiForm.render();
						}
						}}); 
				});
			
		}
		
		scheduleAdd.getData = function (){
			var data = {};
			var name = $("#NAME").val();
			var enable = $("#ENABLE_STATUS").val();
			var sort = $("#SORT_NUM").val();
			var mark = $("#BAKUP").val();
			var time_type =$("#TIME_TYPE").val();
			data.NAME=name;
			data.ENABLE_STATUS=enable;
			data.SORT_NUM=sort;
			data.BAKUP=mark;
			data.TIME_TYPE=time_type;
			var day = {};
			for(var i=0;i<7;i++){
				var time=[];
				var busi_id = $("#BUSI_ID"+i).val();
				var record =[];
				record = records.get(i);
				 if("undefined"==typeof(record)){
	        		 continue;
	        	 }
				for(var j=1;j<=record.length;j++){
					var detail={};
					var time_code =$("#TIME_CODE"+i+"-"+j).val();
					var work_type =$("#WORK_TYPE"+i+"-"+j).val();
					var begin_time = $("#BEGIN_TIME"+i+"-"+j).val();
					var end_time = $("#END_TIME"+i+"-"+j).val();
					detail.BUSI_ID=busi_id;
					detail.TIME_CODE=time_code;
					detail.WORK_TYPE=work_type;
					detail.BEGIN_TIME=begin_time;
					detail.END_TIME=end_time;
					detail.ENABLE_STATUS=enable;
					time.push(detail);
					}
				day[busi_id]=time;
			}
			data.week=day;
			console.log(data);
			
			return data;
			}
		
		
		
		scheduleAdd.checkTime = function (data,i,j,type){
			if(typeof(data) == "undefined"||data == ""){
				return;
			}
			var record =[];
			record = records.get(i);
			 if("undefined"==typeof(record)){
        		return;
        	 }
			for(var n=0;n<record.length;n++){
				if(record[n]==j){
					continue;
				}
				var arr = [];
				var begin = $("#BEGIN_TIME"+i+"-"+record[n]).val()
				arr = begin.split(":");
				var beginTime = Number(arr[0])*60+Number(arr[1]);
				var end = $("#END_TIME"+i+"-"+record[n]).val()
				arr2 = end.split(":");
				var endTime = Number(arr2[0])*60+Number(arr2[1]);
				
				arr3 = data.split(":");
				var nowTime = Number(arr3[0])*60+Number(arr3[1]);
				//console.log(beginTime+"-"+endTime+"<>"+nowTime)
				if(nowTime>= beginTime && nowTime<=endTime){
					var name=$("#TIME_CODE"+i+"-"+record[n]).val();
					if("begin"==type){
						$("#BEGIN_TIME"+i+"-"+j).focus();
						$("#BEGIN_TIME"+i+"-"+j).val("");
					}
					if("end"==type){
						$("#END_TIME"+i+"-"+j).focus();
						$("#END_TIME"+i+"-"+j).val("");
					}
					layer.msg(getI18nValue("时间段冲突")+"-"+name);
					
					return false;
					break;
				}
				return true;
			}
		    
		  }
		// 返回相同数组位置
		Array.prototype.arrindex = function(val) {
		        for (var i = 0; i < this.length; i++) {
		                if (this[i] == val) return i;
		        }
		        return -1;
		};
		// 移除数组指定位置的值
		Array.prototype.remove = function(val) { 
		        var index = this.arrindex(val); 
		        if (index > -1) { 
		                this.splice(index, 1); 
		        } 
		};
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>