<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>技能组</title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
		#myTable{
			margin-top: 16px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform"  method="post"  data-mars-prefix="skillShare.">
  		<input type="hidden" name="skillShare.SKILL_GROUP_ID" value="${param.skillGroupId}">
  		<table class="table table-edit table-vzebra mt-30" id="myTable">
			<tbody>
				<tr>
			  		<td class="required" width="100px" i18n-content="共享企业"></td>
			  		<td>
			  			<select id="shareEntId" data-rules="required" class="form-control input-sm" data-extis-filter="true" data-group-id="${param.skillGroupId}" name="skillShare.SHARE_ENT_ID" data-mars="skillGroup.shareEntDict">
					    	<option value="" i18n-content="请选择"></option>	
					    </select>
			  		</td>
		    	</tr>
		    	<tr>
		    		<td i18n-content="有效期"></td>
                  	<td>
                  		<input type="text" class="form-control input-sm" name="skillShare.VAILD_TIME" id="vaildTime" />
               		</td>
		    	</tr>
		    	<tr>
		    		<td i18n-content="备注"></td>
                  	<td>
                  		<textarea name="skillShare.BAKUP" class="form-control input-sm" rows="3"></textarea>
               		</td>
		    	</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary"  type="button" onclick="groupShareAdd.ajaxSubmitForm()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
	   	</div>
	</form>		

</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("groupShareAdd");
	$(function(){
		$("#shareEntId").render();
		
		layui.use('laydate', function(){
	  		var laydate = layui.laydate;
	  		 laydate.render({ elem: '#vaildTime' ,format: 'yyyy-MM-dd'});
		});
		
		execI18n();
	});
	
	groupShareAdd.ajaxSubmitForm = function(){
		if(form.validate("#easyform")){
			var data = form.getJSONObject("#easyform");
			ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=addSkillShare",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg, {icon: 1, time: 1200, offset: '40px' }, function() {
						parent.loadData();
						popup.layerClose("#easyform");
                    });
				}else{
					parent.layer.alert(result.msg,{icon: 5});
				}
			});
		};
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>