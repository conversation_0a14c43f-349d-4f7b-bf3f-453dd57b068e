<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业通讯录</title>
    <link rel="stylesheet" href="/yc-ccbar/ccbar/font/iconfont.css?v=20180803">
	<style type="text/css">
		 div#rMenu {position:absolute; visibility:hidden; top:0;text-align: left;padding: 2px;}
		.dropdown-menu>li>a{font-size: 13px;}
	    .ibox-header{margin-bottom:10px}
	    .ibox-header-content{background-color: #ffffff;padding: 10px 20px;border-color: #e7eaec;border-style: solid solid none;border-width: 1px 0;}
	    .ibox-header-content h4{font-weight:600;font-size:16px;margin:8px 6px 0px}
	     a:link{ color:#00adff;}
		 .btn-group button{
			 margin-right: 10px;
		 }
		 .container-fluid{
		 	overflow-x: hidden;
		 }
		 .gray-bg{
		     overflow-y: hidden;
             padding: 10px;
		 }
		 .slimScrollBar{
		 		overflow-y: hidden;
		 }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" id="searchForm" name="searchForm" class="form-inline">
		<div class="row" style="overflow-y: hidden; ">
			<div style="height: 100%;">
				<div class="ibox">
             	    
             	    <div class="ibox-header">
	             		<div class="ibox-title">
							 <div class="form-group" id="divId">
	             		          <div class="input-group input-group-sm">
									      <span class="input-group-addon" i18n-content="姓名"></span>	
										  <input type="text" name="memberName" class="form-control input-sm reset" style="width:100px">
								   </div>
								    <div class="input-group input-group-sm">
										<span class="input-group-addon" i18n-content="性别"></span>
											<select name="memberSex" data-mars="common.getDict(SEX)" class="form-control input-sm reset" style="width: 132px">
												<option value="" i18n-content="-请选择-"></option>
											</select>
									</div>
								   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="addressBook.searchData('1')"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span> </button>
								   </div>
								   <div class="input-group ">
								       <button type="button" class="btn btn-sm btn-default" onclick="addressBook.reset()">
								           <span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置"></span> 
								       </button>
							       </div>

                                   <div class="input-group input-group-sm" style="float: right;">
                                        <!-- <span class="input-group-addon" i18n-content="外显号码"></span> -->
                                        <select class="form-control input-sm" style="width: 132px" id="outPhone">   
                                        </select>
                                   </div>
							  </div>
	             	    </div>
	              	    <div class="ibox-content" style=" padding-bottom: 3px;">
		              	    <table id="dataList">
              	    		</table>
						 </div>
              	    </div>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("addressBook");
		var height=$(window).height();
        var state = getUrlParam('ccbarState') || ''
        var layerIndex = parent.layer.getFrameIndex(window.name);
		requreLib.setplugs('layui',function(){
			addressBook.loadData();
		});
		addressBook.loadData = function(){
		  	$("#searchForm").render();
			$("#searchForm").initTableEx({
				mars:'Diction.memberDetails',
				id:'dataList',
				totalRow:false,//显示汇总行
				limit:15,
				page:true,
				height: 'full-140',
				toolbar:false,
				cols: [
					[
				     {width:80, title: getI18nValue('序号'),type:'numbers'},
				     {width:100, title: getI18nValue('分组'),field:'GROUP_NAME'},
			         {minWidth:100,field:'NAME', title: getI18nValue('姓名')},
			         {minWidth:80,field:'SEX', title: getI18nValue('性别'),templet:function(row){
			        	 return getDictTextByCode('SEX',row.SEX);
						}
			        	 },
			         {minWidth:120,field:'PHONE_NUM', title: getI18nValue('手机号码')},
			         {minWidth:80,align:'center',field:'PHONE_NUM', title: getI18nValue('操作'),
			        	 templet:function(row){
                             var html = ''
			        		 if (row.PHONE_NUM){
                                html += '<button type="button"'+ (state == 'TALK' || state == 'WORKNOTREADY' ? 'disabled' : '') +'  class="layui-btn layui-btn layui-btn-xs'+(state == 'TALK' || state == 'WORKNOTREADY' ? ' layui-btn-disabled' : '')+'" onclick="addressBook.callPhone(\''+row.PHONE_NUM+'\')" i18n-content="外呼"></button>'
                                html += '<button type="button"'+ (state !== 'TALK' ? 'disabled' : '') +' class="layui-btn layui-btn-normal layui-btn layui-btn-xs'+(state !== 'TALK' ? ' layui-btn-disabled' : '')+'" onclick="addressBook.consultPhone(\''+row.PHONE_NUM+'\')" i18n-content="咨询"></button>'
                                html += '<button type="button"'+ (state !== 'TALK' ? 'disabled' : '') +' class="layui-btn layui-btn-warm layui-btn layui-btn-xs'+(state !== 'TALK' ? ' layui-btn-disabled' : '')+'" onclick="addressBook.transferPhone(\''+row.PHONE_NUM+'\')" i18n-content="转移"></button>'
                                 if (row.PHONE_NUM == '90006') {
	                                html += '<button type="button"'+ (state !== 'TALK' ? 'disabled' : '') +' class="layui-btn layui-btn-warm layui-btn layui-btn-xs'+(state !== 'TALK' ? ' layui-btn-disabled' : '')+'" onclick="addressBook.sstransfer(\''+row.PHONE_NUM+'\')" i18n-content="IVR转移"></button>'
                                }  
			        		 }
                             return html;
			        		 
			        }},
			         ]
				],
                done: function() {
                    execI18n()
                }
			});
		}
		
		addressBook.callPhone = function(num){
			if(parent.CallControl&&parent.CallControl.getFunc('makecall')){
                layer.confirm(getI18nValue('是否拨打')+num, {icon: 3, title: getI18nValue('外呼提示'), offset: '20px',
                btn: [getI18nValue('确定'), getI18nValue('取消')]}, function(index) {
                    layer.close(index)
                    var userData = {}
                    top.CallControl.makeCall(num, $('#outPhone').val(), JSON.stringify(userData), function(result) {
                        top.tipsMsg('正在呼叫'+num);
                        parent.layer.close(layerIndex);
                        if(result.data.content !='succ'){
                            top.tipsMsg(result.data.content);
                        }
                    })
                }, function(index) {
                    layer.close(index)
                })
			}else{
				layer.alert(getI18nValue("无法外呼!"),{title:getI18nValue("提示"),btn:[getI18nValue("确定")]});	
			}
		}

        addressBook.consultPhone = function(num) {
            top.CallControl.consultation(num,$('#outPhone').val(),'','3',{}, function(result) {
                if(result && result.data == 'fail'){ top.tipsMsg(result.msg);}
                if (result && result.data == 'succ') {
                    top.tipsMsg('咨询成功');
                    parent.layer.close(layerIndex);
                }
            })
        }

        addressBook.transferPhone = function(num) {
            top.CallControl.sstransfer(num,$('#outPhone').val(),'','3',{}, function(result) {
                if(result && result.data == 'fail'){ top.tipsMsg(result.msg);}
                if (result && result.data == 'succ') {
                    top.tipsMsg('转移成功');
                    parent.layer.close(layerIndex);
                }
            })
        }
        addressBook.sstransfer = function(num) {
            top.CallControl.sstransfer(num,top.latestCall.event.custPhone,'','2',{}, function(result) {
                if(result && result.data == 'fail'){ top.tipsMsg(result.msg);}
                if (result && result.data == 'succ') {
                    top.tipsMsg('转移成功');
                    parent.layer.close(layerIndex);
                }
            })
        }
		
		addressBook.searchData=function(flag){
			if(flag=="1"){
				$("#searchForm").queryData({id:'dataList',page:{curr:1}});
			}else{
				$("#searchForm").queryData({id:'dataList'});
			}
		}
	  	
		addressBook.reset=function(){
			$(".reset").val("");
		};

        addressBook.getCallers = function() {
            top.CallControl.callerList(function(result){
                var tempOptions = '';                          
                if(result.data.result.callers){
                    for (var i = 0; i < result.data.result.callers.length; i++) {
                        if (result.data.result.callers[i]) {
                            tempOptions += '<option value="'+result.data.result.callers[i]+'">'+result.data.result.callers[i]+'</option>'
                        }
                    }

                }
                $("#outPhone").html(tempOptions);
                $("#outPhone").val($('#outPhone option').eq(0).val())
            });
        }

        addressBook.getCallers()

        window.addEventListener('message', function(e) {
            if (e.origin !== location.origin && !e.data.ccbarState) return;
            if (e.data.ccbarState !== state) {
                state = e.data.ccbarState
                console.log('reload');
                $("#searchForm").queryData({id:'dataList'});
            }
        })
	</script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>