body .layui-layer-tips .layui-layer-content {
    line-height: 1.5;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #303133;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
}

body .layui-layer-tips i.layui-layer-TipsG {
    border-width: 5px;
}

body .layui-layer-tips i.layui-layer-TipsR, body .layui-layer-tips i.layui-layer-TipsL {
    top: 11px;
}

body .layui-layer-tips i.layui-layer-TipsT, body .layui-layer-tips i.layui-layer-TipsB {
    left: 12px;
}

body .layui-layer-tips i.layui-layer-TipsT {
    bottom: -10px;
}

body .layui-layer-tips i.layui-layer-TipsT {
    border-right-color: transparent;
    border-top-style: solid;
    border-top-color: #303133;
}

body .layui-layer-tips i.layui-layer-TipsB {
    top: -10px;
}

body .layui-layer-tips i.layui-layer-TipsB {
    border-right-color: transparent;
    border-bottom-style: solid;
    border-bottom-color: #303133;
}

body .layui-layer-tips i.layui-layer-TipsL {
    right: -10px;
}

body .layui-layer-tips i.layui-layer-TipsL {
    border-bottom-color: transparent;
    border-left-style: solid;
    border-left-color: #303133;
}

body .layui-layer-tips i.layui-layer-TipsR {
    left: -10px;
}

body .layui-layer-tips i.layui-layer-TipsR {
    border-bottom-color: transparent;
    border-right-style: solid;
    border-right-color: #303133;
}


/** loading样式 */
.page-loading {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 19891017;
    background-color: #fff;
}

.page-no-scroll {
    overflow: hidden;
    overflow-x: hidden;
    overflow-y: hidden;
    min-height: 80px;
}

.rubik-loader, .ball-loader, .signal-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
}

.ball-loader > span, .signal-loader > span {
    background-color: #4aca85;
    display: inline-block;
}

.ball-loader > span:nth-child(1), .ball-loader.sm > span:nth-child(1), .signal-loader > span:nth-child(1), .signal-loader.sm > span:nth-child(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

.ball-loader > span:nth-child(2), .ball-loader.sm > span:nth-child(2), .signal-loader > span:nth-child(2), .signal-loader.sm > span:nth-child(2) {
    -webkit-animation-delay: 0.1s;
    animation-delay: 0.1s;
}

.ball-loader > span:nth-child(3), .ball-loader.sm > span:nth-child(3), .signal-loader > span:nth-child(3), .signal-loader.sm > span:nth-child(3) {
    -webkit-animation-delay: 0.15s;
    animation-delay: 0.15s;
}

.ball-loader > span:nth-child(4), .ball-loader.sm > span:nth-child(4), .signal-loader > span:nth-child(4), .signal-loader.sm > span:nth-child(4) {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
}

.ball-loader > span {
    width: 20px;
    height: 20px;
    margin: 0 3px;
    border-radius: 50%;
    transform: scale(0);
    -ms-transform: scale(0);
    -webkit-transform: scale(0);
    animation: ball-load 1s ease-in-out infinite;
    -webkit-animation: 1s ball-load ease-in-out infinite;
}

@-webkit-keyframes ball-load {
    0% {
        transform: scale(0);
        -webkit-transform: scale(0);
    }
    50% {
        transform: scale(1);
        -webkit-transform: scale(1);
    }
    100% {
        transform: scale(0);
        -webkit-transform: scale(0);
    }
}

@keyframes ball-load {
    0% {
        transform: scale(0);
        -webkit-transform: scale(0);
    }
    50% {
        transform: scale(1);
        -webkit-transform: scale(1);
    }
    100% {
        transform: scale(0);
        -webkit-transform: scale(0);
    }
}

.ball-loader.sm > span {
    width: 15px;
    height: 15px;
    margin: 0 2px;
}

.layui-input {
    border-color: #C9C9C9 !important;
}

.layui-input:focus {
    border-color: #009688 !important;
}

.layui-btn-sm {
    padding: 0 8px 0 5px;
}

.page-wrapper {
    margin: 0 auto;
    padding: 0 35px;
}

.right-desc {
    position: absolute;
    right: 0;
    top: 0;
    width: 280px;
    padding-top: 6px;
}

.desc-item {
    background-color: #FFF8DB;
    color: #40485b;
    padding: 10px 15px;
    margin: 15px 15px 0 0;
    font-size: 12px;
    line-height: 1.6;
    border-radius: 4px;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, .15);
}

html, body {
    min-width: 1200px;
    background-color: #fff;
    position: relative;
}

@media screen and (max-width: 1480px) {
    .page-wrapper {
        margin: 0 280px auto auto;
    }
}