package com.yunqu.cc.base.dao.ent;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.model.Phone;

@WebObject(name="phone")
public class PhoneDao extends AppDaoContext {

	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		String phoneNum = param.getString("phoneNum");
		String entId = param.getString("entId");
		EasySQL sql = this.getEasySQL("select t1.*,t2.PETRA_NAME from CC_PHONE t1 left join cc_petra_res t2 on t1.PETRA_ID = t2.PETRA_ID where 1=1 ");
		sql.append(entId, " and t1.ENT_ID = ?",false);
		sql.appendLike(phoneNum, " and t1.PHONE_NUM like ? ");
		sql.append(param.getInteger("phoneType"), " and t1.PHONE_TYPE = ? ");
		sql.append(param.getInteger("joinType"), " and t1.JOIN_TYPE = ?");
		sql.append(param.getString("petraId"), " and t1.PETRA_ID = ?");
		sql.append("order by t1.PHONE_NUM");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getPhone",type=Types.RECORD)
	public  JSONObject getPhone(){
		String phoneNum = param.getString("phonenum");
		String entId = param.getString("entid");
		Phone phone=new Phone();
		phone.setDbName("ycmain");
		phone.setPrimaryValues(new Object[]{entId,phoneNum});
		return queryForRecord(phone);
	}
	
	@WebControl(name="selectPhoneRes",type=Types.TEMPLATE)
	public JSONObject selectPhoneRes(){
		EasySQL sql = this.getEasySQL("select t1.PHONE_NUM from CC_PHONE_RES t1 where t1.USE_FLAG=0 and not exists( select 1 from cc_phone where PHONE_NUM = t1.PHONE_NUM");
		sql.append(param.getString("entId"), " and ENT_ID = ?)");
		sql.append(" order by t1.PHONE_NUM");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="getMaxPhone", type=Types.TEXT)
	public JSONObject getMaxPhone() {
		String maxPhone = "";
		try {
			String sql = "select max(PHONE_NUM) from CC_PHONE where ENT_ID = ?";
			maxPhone = getQuery().queryForString(sql, new Object[]{param.getString("entId")});
			if(StringUtils.isBlank(maxPhone)){
				maxPhone = "8001";
			}else{
				sql = "select PHONE_PREFIX from CC_ENT_RES where ENT_ID = ?";
				String phonePrefix = getQuery().queryForString(sql, new Object[]{param.getString("entId")});
				maxPhone = maxPhone.substring(phonePrefix.length());
				maxPhone = (Integer.parseInt(maxPhone) + 1)+"";
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getText(maxPhone);
	}
	
	@WebControl(name="outPhoneDict",type=Types.DICT)
	public JSONObject outPhoneDict(){
		String entId = param.getString("user.ENT_ID");
		return this.getDictByQuery("select PHONE_NUM,PHONE_NUM from cc_phone where PHONE_TYPE = 4 and ENT_ID = ?", new Object[]{entId});
	}

}
