package com.yunqu.cc.base.dao.dict;

import java.util.ArrayList;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.TreeNode;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.Constants;

@WebObject(name = "dict")
public class DictDao extends AppDaoContext {

	/**
	 * 根据字典编号从缓存中获取数据字典
	 * @return
	 */
	@WebControl(name="getDictList",type=Types.RECORD)
	public JSONObject getDictList(){
		String code = (String)this.getMethodParam(0);
		String epCode = UserUtil.getUser(request).getEpCode();
		epCode = StringUtils.isNotBlank(epCode)?epCode: "001";
		JSONObject obj=DictCache.getJsonEnableDictListByGroupCode(epCode, code,Constants.APP_NAME,this.request);
		return obj;
	}
	
	/**
	 * 查询所有的字典组列表
	 * @return
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk-sjzd",msg = "您无权访问！")
	@WebControl(name = "dictGroupList", type = Types.LIST)
	public JSONObject list() {
		EasySQL sql = this.getEasySQL("select ");
		sql.append("ID,CODE,NAME,TYPE,BAKUP,MODULE,SORT_NUM,ENABLE_STATUS,CREATE_ACC,UPDATE_ACC,");
		sql.append("CASE WHEN TYPE='0' THEN '系统字典' WHEN TYPE='1' THEN '业务字典' WHEN TYPE='2' THEN '定制字典' ELSE '' END TYPE_NAME ");
		sql.append(" from  "+this.getTableName("C_CF_DICTGROUP"));
		sql.append(" where 1=1  ");
		sql.append(param.getString("type"), " and  TYPE= ?");
		sql.append(param.getString("modeleType"), " and  MODULE= ?");
		sql.append(param.getString("enableStatus"), " and  ENABLE_STATUS= ?");
		sql.append(param.getString("code"), " and  MODULE= ?");// 所属模块
		sql.appendLike(param.getString("GroupNmae"), "  and (  name like ?  or code  like ? or MODULE like ?)");
		sql.append(getEntId(), " and  EP_CODE= ?",false);
		sql.append(getBusiOrderId(), " and  busi_order_id= ?",false);
		sql.append(" order by SORT_NUM, MODULE");
		return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 根据字典组查询字典项列表
	 * @return
	 */
	@WebControl(name = "dictList", type = Types.LIST)
	@InfAuthCheck(resId = {"cc-base-system-xtpz-yxjk-sjzd","cc-erder-process-processList"},msg = "您无权访问！")
	public JSONObject dictList() {
		EasySQL sql = this.getEasySQL("select  t1.*,t2.type ");
		sql.append(" from   "+this.getTableName("C_CF_DICT")+" t1");
		
		sql.append(" left join "+this.getTableName("C_CF_DICTGROUP")+" t2");
		sql.append(" on t1.DICT_GROUP_ID = t2.id ");
		sql.append(" where 1=1  ");
		String id=param.getString("ID");
		if(StringUtils.isBlank(id)) {
			return EasyResult.error(401, "无数据");
		}
		
		sql.append(param.getString("ID")," and  t1.DICT_GROUP_ID= ?");
		//sql.append(getEntId(), " and  t1.ENT_ID= ?",false);
		sql.append(getBusiOrderId(), " and  t1.busi_order_id= ?",false);
		sql.append(" order by t1.SORT_NUM ,t1.ID ");
		JSONObject queryForPageList = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
		return queryForPageList;
	}

	@WebControl(name = "getUsersByDeptCode", type = Types.TREE)
	public JSONObject getUsersByDeptCode() {
		UserModel user = UserUtil.getUser(this.request);
		String depCode = user.getLevelOneDeptCode();
		TreeNode usersByDeptCode = DeptMgr.getDeptTreeNode( new ArrayList<TreeNode>(), depCode,user.getSchemaName(),true,true,true,user.getEpCode());
		JSONObject jsonObject = JsonUtil.toJSONObject(usersByDeptCode);
		return getTree(jsonObject);
	}

	@WebControl(name = "group", type = Types.RECORD)
	public JSONObject getgroup() {
		return this.queryForRecord(new EasyRecord(this.getTableName("C_CF_DICTGROUP"), "ID").setPrimaryValues(param.getString("dict.ID")));
	}

	@WebControl(name = "edit", type = Types.RECORD)
	public JSONObject getEdit() {
		return this.queryForRecord(new EasyRecord(this.getTableName("C_CF_DICT"), "ID").setPrimaryValues(param.getString("dict.ID")));
	}

	@WebControl(name = "module", type = Types.RECORD)
	public JSONObject getmodule() {
		String epCode = UserUtil.getUser(this.request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(epCode, "SYSTEM_MODULE",Constants.APP_NAME,this.request);
	}

	@WebControl(name = "status", type = Types.RECORD)
	public JSONObject getStatus() {
		String depCode = UserUtil.getUser(this.request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "CC_BASE_DICT_SF_YN",Constants.APP_NAME,this.request);
	}

	@WebControl(name = "type", type = Types.RECORD)
	public JSONObject getType() {
		String depCode = UserUtil.getUser(this.request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "LOG_OPER_TYPE",Constants.APP_NAME,this.request);
	}
}
