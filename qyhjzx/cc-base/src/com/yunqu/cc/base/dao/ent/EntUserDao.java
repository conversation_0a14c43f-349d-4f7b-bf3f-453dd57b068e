package com.yunqu.cc.base.dao.ent;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.base.base.AppDaoContext;

/**
 * 企业用户信息表  Dao类
 * <AUTHOR>
 *
 */
@WebObject(name="entUser")
public class EntUserDao extends AppDaoContext {
	
	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		String userAcct = param.getString("userAcct");
		String agentPhone = param.getString("agentPhone");
		String entId = param.getString("entId");
		EasySQL sql = this.getEasySQL("select * from CC_USER where ADMIN_FLAG = 0 ");
		sql.append(entId, " and ENT_ID = ?",false);
		sql.append(param.getInteger("userState"), " and USER_STATE = ?");
		sql.appendLike(userAcct, " and USER_ACCT like ?");
		sql.appendLike(agentPhone, " and AGENT_PHONE like ?");
		sql.appendSort(param.getString("sortName"),param.getString("sortType")," AGENT_PHONE");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getUser",type=Types.RECORD)
	public  JSONObject getUser(){
		String pk=param.getString("pk");
		return queryForRecord(new EasyRecord("CC_USER","USER_ID").setPrimaryValues(pk));
	}
	
	@WebControl(name="getMaxAgent", type=Types.TEXT)
	public JSONObject getMaxAgent() {
		String maxAgent = "";
		try {
			String sql = "select max(AGENT_PHONE)+1 from CC_USER where USER_STATE <> 9 and ADMIN_FLAG = 0 and ENT_ID = ?";
			if(this.getQuery().getTypes() == DBTypes.MYSQL) {//如果是MySQL数据库，使用函数convert转化为数值
				sql = "select max(convert(AGENT_PHONE,signed)) + 1 from CC_USER where USER_STATE <> 9 and ADMIN_FLAG = 0 and ENT_ID = ?";
			}else if(this.getQuery().getTypes() == DBTypes.ORACLE) {//如果是MySQL数据库，使用函数to_number转化为数值
				sql = "select max(to_number(AGENT_PHONE)) + 1 from CC_USER where USER_STATE <> 9 and ADMIN_FLAG = 0 and ENT_ID = ?";
			}
			maxAgent = getQuery().queryForString(sql, new Object[]{param.getString("entId")});
			if(StringUtils.isBlank(maxAgent)){
				maxAgent = "8001";
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getText(maxAgent);
	}
	
	/**
	 * 获取尚未分配的外显号码字典
	 */
	@WebControl(name="phoneDict", type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql = this.getEasySQL("select t1.PHONE_NUM,t1.PHONE_NUM from CC_PHONE t1 where 1=1");
		sql.append(param.getString("user.ENT_ID"), " and t1.ENT_ID = ?");
		sql.append(param.getString("user.ENT_ID"), "and not exists(select 1 from CC_USER where PHONE_NUM = t1.PHONE_NUM and ENT_ID = ?");
		sql.append(param.getString("user.USER_ID"), "and USER_ID <> ?");
		sql.append(")");
		//sql.append("and t1.PHONE_TYPE in(1,2,3)");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="busiOrderUser", type=Types.LIST)
	public JSONObject busiOrderUser(){
		EasySQL sql = this.getEasySQL("SELECT t1.BUSI_NAME,t2.BUSI_ORDER_ID,t3.user_id,t3.USER_STATE from cc_busi t1,cc_busi_order t2, "+getTableName(param.getString("user.ENT_ID"), "CC_BUSI_USER")+" t3");
		sql.append("where t1.BUSI_ID = t2.BUSI_ID and t2.BUSI_ORDER_ID = t3.BUSI_ORDER_ID and t2.ENT_ID = t3.ENT_ID");
		sql.append(param.getString("user.USER_ID"), "and t3.USER_ID = ?");
		sql.append(param.getString("user.ENT_ID"), "and t3.ENT_ID = ?");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 用户锁表
	 * @return
	 */
	@WebControl(name="userLoginSecurityList", type=Types.LIST)
	public JSONObject userLoginSecurityList(){
		EasySQL sql = this.getEasySQL("SELECT t1.*,t2.ENT_ID,t3.ENT_NAME from EASI_USER_LOGIN_SECURITY t1 left join CC_USER t2 on t1.USER_ACCT = t2.USER_ACCT left join CC_ENT t3 on t2.ENT_ID = t3.ENT_ID where t1.LOGIN_LOCK = 1");
		sql.appendLike(param.getString("userAcct"), "and t1.USER_ACCT like ?");
		//return this.queryForPageList(sql.getSQL(), sql.getParams());
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	
}
