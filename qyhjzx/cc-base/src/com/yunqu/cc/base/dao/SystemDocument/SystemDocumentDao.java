package com.yunqu.cc.base.dao.SystemDocument;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.base.base.AppDaoContext;

@WebObject(name="systemDocument")
public class SystemDocumentDao extends AppDaoContext {
	/*
	 * 系统文档列表信息
	 */
	@WebControl(name="getList",type=Types.LIST)
	public JSONObject getList(){
		EasySQL sql = new EasySQL("select * from "+getTableName("c_cf_doc")+" where 1=1 ");
		sql.append(param.getString("getStartDate"),"and CREATE_TIME>= ?");
		sql.append(param.getString("getEndDate"),"and CREATE_TIME<= ?");
		sql.appendLike(param.getString("NAME")," and NAME like ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/*
	 * 编辑页面数据回显
	 */
	@WebControl(name = "getRecord", type = Types.RECORD)
	public JSONObject getRecord(){
		EasyRecord record = new EasyRecord(getTableName("c_cf_doc"),"ID").setPrimaryValues(param.getString("ID"));
		return queryForRecord(record);
	}
}
