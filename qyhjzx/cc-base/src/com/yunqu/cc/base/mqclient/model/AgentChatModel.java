package com.yunqu.cc.base.mqclient.model;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.mq.model.MQMessageModel;

/**
 * 坐席聊天消息模型
 */
public class AgentChatModel extends MQMessageModel{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 消息ID
	 */
	private String contentId;
	
	/**
	 * 会话ID
	 */
	private String chatRecordId;
	
	/**
	 * 发送坐席
	 */
	private String userId;
	/**
	 * 发送坐席账号
	 */
	private String userAcc;
	/**
	 * 发送坐席姓名
	 */
	private String userName;
	/**
	 * 发送时间
	 */
	private String sendTime;
	/**
	 * 发送时间戳
	 */
	private String sendTimestamp;
	
	/**
	 * 发送日期
	 */
	private String dateId;
	
	/**
	 * 消息类型
	 */
	private String msgType;
	/**
	 * 消息内容
	 */
	private String msgContent;
	/**
	 * 消息发送状态，0：发送失败，1：发送成功
	 */
	private String sendState;
	/**
	 * 消息已读状态，0：未读，1：已读, 仅仅针对双人对话，另一方的阅读状态
	 */
	private String readState;
	/**
	 * 发送失败原因
	 */
	private String sendFialDesc;
	/**
	 * 消息撤回标志，默认0，1撤回
	 */
	private String withdraw;
	/**
	 * 扩展JSON
	 */
	private String exJson;
	
	private String entId;
	
	private String busiOrderId;
	
	private String schema;
	
	public AgentChatModel() {
		
	}
	
	/**
	 * 根据json生成对象
	 * @param data
	 * @return
	 */
	public static AgentChatModel createByJson(JSONObject data) {
		if(data!=null){
			return (AgentChatModel)JsonUtil.parseObject(data.toJSONString(), AgentChatModel.class);
		}
		return null;
	}

	public String getContentId() {
		return contentId;
	}

	public void setContentId(String contentId) {
		this.contentId = contentId;
	}

	public String getChatRecordId() {
		return chatRecordId;
	}

	public void setChatRecordId(String chatRecordId) {
		this.chatRecordId = chatRecordId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserAcc() {
		return userAcc;
	}

	public void setUserAcc(String userAcc) {
		this.userAcc = userAcc;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}

	public String getSendTimestamp() {
		return sendTimestamp;
	}

	public void setSendTimestamp(String sendTimestamp) {
		this.sendTimestamp = sendTimestamp;
	}

	public String getDateId() {
		return dateId;
	}

	public void setDateId(String dateId) {
		this.dateId = dateId;
	}

	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	public String getMsgContent() {
		return msgContent;
	}

	public void setMsgContent(String msgContent) {
		this.msgContent = msgContent;
	}

	public String getSendState() {
		return sendState;
	}

	public void setSendState(String sendState) {
		this.sendState = sendState;
	}

	public String getReadState() {
		return readState;
	}

	public void setReadState(String readState) {
		this.readState = readState;
	}

	public String getSendFialDesc() {
		return sendFialDesc;
	}

	public void setSendFialDesc(String sendFialDesc) {
		this.sendFialDesc = sendFialDesc;
	}

	public String getWithdraw() {
		return withdraw;
	}

	public void setWithdraw(String withdraw) {
		this.withdraw = withdraw;
	}

	public String getExJson() {
		return exJson;
	}

	public void setExJson(String exJson) {
		this.exJson = exJson;
	}

	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getBusiOrderId() {
		return busiOrderId;
	}

	public void setBusiOrderId(String busiOrderId) {
		this.busiOrderId = busiOrderId;
	}

	public String getSchema() {
		return schema;
	}

	public void setSchema(String schema) {
		this.schema = schema;
	}
	
}
