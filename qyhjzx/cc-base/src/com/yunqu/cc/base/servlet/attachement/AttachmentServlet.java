package com.yunqu.cc.base.servlet.attachement;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.catalina.core.ApplicationPart;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.DuplicateSubmit;
import com.yq.busi.common.annontation.DuplicateSubmit.LoseType;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.AttachmentUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;
import com.yunqu.cc.base.base.QueryFactory;
import com.yunqu.cc.oss.OSSTools;
import com.yunqu.cc.oss.base.OSSConstants;
import com.yunqu.cc.oss.model.AttachmentDbinfo;
import com.yunqu.cc.oss.model.AttachmentFile;
import com.yunqu.cc.oss.model.OSSAttachmentUtil;

@MultipartConfig
@WebServlet("/servlet/attachment/*")
public class AttachmentServlet extends AttachmentBaseServlet {
	protected Logger logger =CommonLogger.getLogger();
	private static final long serialVersionUID = 7467895634123782087L;

	public String actionForAttachment() {
		HttpServletRequest request = getRequest();
		String busiId = StringUtils.isBlank(getPara("busiId")) ? RandomKit.randomStr()
				: getPara("busiId");
		String busiType = StringUtils.isBlank(getPara("busiType")) ? "" : getPara("busiType");
		String requestType = StringUtils.isBlank(getPara("requestType")) ? "" : getPara("requestType");

		// 附件格式
		String fileSuffix = ConfigUtil.getString("cc-base", "ATTACHMENT_SUFFIX", "jpg;jpeg;xls;txt;");
		
		UserModel user = UserUtil.getUser(request);
		//是否开启附件预览
		boolean showPreview = Constants.getShowPreview(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId());
		String delFileOthers = Constants.delFileOthers();

		//附件大小
		long maxSize = ConfigUtil.getInt("cc-base", "ATTACHMENT_MAX_LEN", 50);
		setAttr("maxSize", maxSize);// 附件大小
		setAttr("showPreview", showPreview);// 是否开启附件预览
		setAttr("fileSuffix", fileSuffix);// 附件格式
		setAttr("busiId", busiId);// 业务Id，比如公告Id
		setAttr("busiType", busiType);// 业务类型,(比如“01”对应公告)
		setAttr("requestType", requestType);// 上传的时候传upload，下载的时候传download
		setAttr("listFlag", request.getParameter("listFlag"));// online list 页面调用
		setAttr("delFileOthers", delFileOthers);//是否允许删除他人删除的文件
		setAttr("agentAcc", UserUtil.getUser(this.getRequest()).getUserAcc());//当前账号
		return "/pages/attachment/attachment/attachment.jsp";
	}

	/**
	 * 上传附件
	 * 推荐统一使用  actionForUpload2
	 * @return
	 */
	@Deprecated
	@DuplicateSubmit(loseType=LoseType.CUSTOMIZE)
	public EasyResult actionForUpload() {
		EasyResult result = new EasyResult();
		try {
			UserModel user = UserUtil.getUser(this.getRequest());
			if(user==null){
				logger.error("用户上传附件失败:用户未登录");
				return EasyResult.error(500, "用户未登录.");
			}
//			DiskFileItemFactory factory = new DiskFileItemFactory();
//			ServletFileUpload upload = new ServletFileUpload(factory);
//			List<FileItem> items = upload.parseRequest(this.getRequest());
			String busiType = this.getRequest().getParameter("busiType");
			String busiId = this.getRequest().getParameter("busiId");
			String createPreview = this.getRequest().getParameter("createPreview");
//			String listFlag = this.getRequest().getParameter("listFlag");//online 页面上传

			String isDelHistory = this.getRequest().getParameter("isDelHistory");
			
			if(StringUtils.equals("true", isDelHistory)) {
				AttachmentUtil.deleteHistory(this.getDbName(),busiId, busiType);
			}
			
			//校验附件：检查业务对象的附件量是否超标
			String checkResult = AttachmentUtil.checkBusiAttachmentSize(this.getDbName(),busiId);
			if(StringUtils.isNotBlank(checkResult)){
				logger.error(getI18nValue("上传附件失败:")+checkResult);
				result.addFail(checkResult);
				return result;
			}

			if(StringUtils.isNotBlank(busiType)){
				if(busiType.contains(".")||busiType.contains("/")){
					logger.error(getI18nValue("上传附件失败:")+getI18nValue("busiType不能包含.或/"));
					result.addFail(getI18nValue("上传附件失败:")+getI18nValue("busiType不能包含.或/"));
					return result;
				}
			}

			Collection<Part> parts = this.getRequest().getParts();
			for(Part part : parts){
				try {
					String submittedFileName = part.getSubmittedFileName();
					if(StringUtils.isBlank(submittedFileName)){
						continue;
					}
					if(part instanceof ApplicationPart){
						ApplicationPart p = (ApplicationPart)part;
						long sizeInBytes = p.getSize();
						String fileName = submittedFileName;
						long fileSize = sizeInBytes; //byteToMB(sizeInBytes);
						//int a = 1/0;

						//校验附件：检查后缀、文件大小
						String checkResult2 = OSSAttachmentUtil.checkSizeAndSuffix(fileName,sizeInBytes);
						if(StringUtils.isNotBlank(checkResult2)){
							logger.error("上传附件["+fileName+","+sizeInBytes+"]失败:"+checkResult2);
							String[] check = checkResult2.split(" ");
							result.addFail(getI18nValue(check[0])+check[1]+getI18nValue(check[2]));
							return result;
						}

						AttachmentFile afile = new AttachmentFile(OSSConstants.getOssMarsServerBucket(),part.getInputStream(), busiType, fileName);
						afile.setFileSize(fileSize);
						afile.setBusiId(busiId);
						
						if("true".equals(createPreview)) {
							afile.needCreatePdf();
						}

						/*if("true".equals(createPreview)) {
							afile.needCreatePdf();
						}*/

						//上传文件
						OSSTools.getInstance().uploadFile(afile);
						if(user!=null){
							afile.setUserAcc(user.getUserAcc());
							afile.setUserName(user.getUserName());
						}
						AttachmentDbinfo attachmentDbinfo = new AttachmentDbinfo(afile,QueryFactory.getWriteQuery(),getDbName(),getEntId(),getBusiOrderId());
						if(user!=null){//该代码是个没用 留着兼容吧 有问题杰哥处理
							attachmentDbinfo.setUserAcc(user.getUserAcc());
							attachmentDbinfo.setUserName(user.getUserName());
							attachmentDbinfo.setAgentphone(user.getUserNo());
						}

						//数据入库
						boolean flag = AttachmentUtil.saveAttachment(attachmentDbinfo);
						if(!flag){
							result.put("errno",500);
							return result;
						}

						JSONObject data = new JSONObject();
						data.put("id", afile.getFileId());
						data.put("name", fileName);
						data.put("path", afile.getFilePath());
						String idUrl = "/cc-base/out/attachment?action=download&fileId="+afile.getFileId()+"&schema="+attachmentDbinfo.getDbName();
						
						data.put("url", idUrl);
						data.put("idUrl", idUrl);
						
						return EasyResult.ok(data,getI18nValue("上传成功"));
					}
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this)+"上传附件失败，原因：" +e.getMessage(),e);
					result.addFail(getI18nValue("操作失败"));
				}finally {
					if(part!=null){
						try {
							part.delete();
						} catch (Exception e2) {
						}
					}
				}

			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"上传附件失败，原因：" +e.getMessage(),e);
		}
		return result;
	}

	/**
	 * 下载附件
	 * 推荐使用 OutAttachmentServlet 的方法
	 * @return
	 */
	@Deprecated
	public EasyResult actionForDownload() {
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户下载附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		// 获得请求文件名
		String filePath = this.getRequest().getParameter("filePath");
		if(StringUtils.isNotBlank(filePath)) {
			try {
				filePath = URLEncoder.encode(filePath, "utf-8");
			} catch (UnsupportedEncodingException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " filePath[" + filePath + "] error:" + e.getMessage(), e);
			}
		}
		String fileId = this.getRequest().getParameter("fileId");
		if(StringUtils.isBlank(fileId)) {
			fileId = "";
		}
		String type = this.getRequest().getParameter("type");
		if(StringUtils.isBlank(type)) {
			type = "";
		}
		String oper = this.getRequest().getParameter("oper"); //view-普通查询  download-下载文件流
		if(StringUtils.isBlank(oper)) {
			oper = "";
		}
		this.redirect("/cc-base/out/attachment?action=download&filePath="+filePath+"&type="+type+"&from=in&fileId="+fileId+"&schema="+this.getDbName()+"&oper="+oper);
		return null;
	}


	public EasyResult actionForBatchDownload() {
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户下载附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		// 获得请求文件名
		String busiId = this.getRequest().getParameter("busiId");
		String bucket = this.getRequest().getParameter("bucket");
		this.redirect("/cc-base/out/attachment?action=batchDown&busiId="+busiId+"&schema="+this.getDbName()+"&bucket="+bucket);
		return null;
	}

	/**
	 * 伪删除
	 * @return
	 */
	public EasyResult actionForDelete() {
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户删除附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		String delFileOthers = Constants.delFileOthers();
		int errorNum=0;
		try {
			for (int i = 0; i < ids.size(); i++) {
				
//				String sql = "update "+this.getTableName("C_CF_ATTACHMENT")+" set IS_DEL = ? where ID= ?";
				EasySQL sql=new EasySQL();
				sql.append("update "+this.getTableName("C_CF_ATTACHMENT"));
				sql.append("Y","set IS_DEL = ?",false);
				sql.append(ids.getString(i),"where ID= ?",false);
				if("Y".equals(delFileOthers)){
					sql.append(user.getUserAcc(),"and ( CREATE_ACC= ? or CREATE_ACC is  null)",false);
				}
				int delNum = this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
				if("Y".equals(delFileOthers)){
					if(delNum==0){
						errorNum++;
					}
				}
			}
			if(errorNum!=0){
				return EasyResult.ok("",getI18nValue("操作失败")+","+getI18nValue("失败次数")+":"+errorNum);
			}
			return EasyResult.ok("",getI18nValue("删除成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) +"删除失败，原因：" +e.getMessage(),e);
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
	/**
	 * 重新上传到s3之类的存储服务器
	 * @return
	 */
	public EasyResult actionForReUpload() {
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户上传附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		try {
			for (int i = 0; i < ids.size(); i++) {
				String id = ids.getString(i);
				logger.info("开始重新上传文件:"+id);
				
				//先修改时间戳，判断为过期
				this.getQuery().execute(" UPDATE " + this.getTableName("C_CF_ATTACHMENT") + " SET UPDATE_TIMESTAMP = 0 WHERE ID = ? ", id);
				
				//查询附件信息
				EasySQL sql = new EasySQL();
				sql.append(" SELECT * FROM " + this.getTableName("C_CF_ATTACHMENT") + " WHERE ID = ? ");
				JSONObject json = this.getQuery().queryForRow(sql.getSQL(), new Object[]{id},new JSONMapperImpl());
				if(json==null){
					logger.error("重传文件失败,无法找到对应的附件记录:"+id);
					continue;
				}
				String ossType = json.getString("OSS_TYPE");
				if(StringUtils.isBlank(ossType) || ( !"3".equals(ossType) && !"5".equals(ossType)) ){
					logger.error("重传文件失败,存储方式["+ossType+"]不支持重传:"+id);
					continue;
				}
				String ossSwitch = OSSConstants.getOssSwitch();
				if(!ossType.equals(ossSwitch)){
					logger.error("重传文件失败,存储方式["+ossType+"],与当前系统的存储方式["+ossSwitch+"]不匹配，不支持重传:"+id);
					continue;
				}
				
				//通过获取访问url的方式，系统在返回url时，如果链接已过期会重新生成链接；生成链接时，如果文件不存在、而本地又存在时，会重新上传，已存在则不会重新上传，只会重新生成链接
				OSSAttachmentUtil.getOssPath(this.getQuery(), "C_CF_ATTACHMENT", json, "", this.getDbName());
				
			}
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) +"重新上传失败，原因：" +e.getMessage(),e);
			return EasyResult.error(500, "重新上传失败，原因：" + e.getMessage());
		}
	}
	/**
	 * 根据id删除文件
	 * @return
	 */
	public EasyResult actionForDeleteById(){
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户删除附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		JSONObject obj=this.getJSONObject();
		JSONArray ids=obj.getJSONArray("ids");
		try {
			for(int i=0;i<ids.size();i++){
				AttachmentUtil.deleteHistoryById(this.getDbName(),ids.getString(i));
			}
			return EasyResult.ok("删除成功！");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败");
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}


	/**
	 * 用于给其他模块进行文件上传，由界面上控制文件类型、大小
	 * @return 成功上传后，返回文件访问地址
	 */
	public JSONObject actionForUpload2(){
		HttpServletRequest request=getRequest();
		UserModel user =UserUtil.getUser(request);
		JSONObject result=new JSONObject();
		result.put("errno",403);
		result.put("state",0);
		result.put("msg", "请求参数异常");
		
		if(user==null){
			result.put("msg", "用户未登录");
			logger.error("用户上传附件失败:"+result);
			return result;
		}
		
		//该附件的访问权限，public-所有人都可以访问 ，protect-按系统配置的权限进行控制
		String auth = request.getParameter("auth");    
		//上传附件的客户端，不同客户端，返回不同的消息格式，cc-默认值，普通上传，UMeditor-百度编辑器，需要返回特定的格式
		String client = request.getParameter("client"); 
		
		try {
			String busiType = request.getParameter("busiType");
			String busiId = request.getParameter("busiId");
			String isDelHistory = request.getParameter("isDelHistory");
			String createThumb = this.getRequest().getParameter("createThumb");//
			String createPreview = this.getRequest().getParameter("createPreview");//
			String flags = request.getParameter("flag");
			int width = CommonUtil.parseInt(this.getRequest().getParameter("width"));//
			int height = CommonUtil.parseInt(this.getRequest().getParameter("height"));//
			if(StringUtils.isBlank(busiType)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+"用户["+user.getUserAcc()+"]上传附件, 未获取到busiType 不能上传!");
				result.put("errno",404);
				result.put("msg", "缺少必填参数");
				result.put("state",0);
				result =  formatResult(client,auth,result);
				return result;
			}
			
			logger.info(String.format("用户["+user.getUserAcc()+"]上传附件  << busiType = %s,busiId= %s,isDelHistory = %s,createThumb = %s,"
					+ "createPreview = %s,flags = %s,width = %s,height = %s,auth = %s, client = %s",
					busiType,busiId,isDelHistory,createThumb,createPreview,flags,width,height,auth,client));

			if(StringUtils.equals("true", isDelHistory)) {
				AttachmentUtil.deleteHistory(this.getDbName(),busiId, busiType);
			}

			//校验附件：检查业务对象的附件量是否超标
			String checkResult = AttachmentUtil.checkBusiAttachmentSize(this.getDbName(),busiId);
			if(StringUtils.isNotBlank(checkResult)){
				logger.error("用户["+user.getUserAcc()+"]上传附件失败:"+checkResult);
				result.put("errno",500);
				result.put("msg", "上传附件失败");
				result.put("state",0);
				String[] check = checkResult.split(" ");
				result.put("errmsg",getI18nValue(check[0])+check[1]+getI18nValue(check[2]));
				result =  formatResult(client,auth,result);
				return result;
			}


			Collection<Part> fileList = request.getParts();
			if(fileList==null||fileList.size()<=0){
				logger.error("用户["+user.getUserAcc()+"]上传附件失败:请求中无文件流");
				result.put("errno",500);
				result.put("msg", "上传附件失败");
				result.put("state",0);
				result =  formatResult(client,auth,result);
				return result;
			}

			//3.4.1#20231101-1 支持一次性上传多个附件；增加 返回参数 dataArray，返回每个附件的上传结果
			JSONArray dataArray = new JSONArray();

			for (Part part:fileList) {
				try {
					JSONObject  json  = upload(user.getEpCode(),user.getBusiOrderId(),user.getSchemaName(),part,busiType,busiId,isDelHistory,createThumb,createPreview,flags,width,height);
					logger.info("用户["+user.getUserAcc()+"]上传附件结果:"+json);

					if("1".equals(json.getString("errno"))) {
						continue;
					}
					dataArray.add(json);
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) +" 用户["+user.getUserAcc()+"] 文件上传失败："+e.getMessage(),e);
				}finally {
					if(part!=null){
						try {
							part.delete();
						} catch (Exception e2) {
						}
					}
				}
			}

			result.put("data",dataArray.size()>0 ? JsonUtil.toJSONObject(dataArray.getJSONObject(0).toJSONString()): null );  //需要转换，不能直接引用，否则影响前端解析
			result.put("dataArray", dataArray);
			result.put("errno",0);
			result.put("state",1);
			result.put("msg", "上传附件成功");
			result =  formatResult(client,auth,result);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(CommonUtil.getClassNameAndMethod(this) +" 用户["+user.getUserAcc()+"]文件上传失败："+e.getMessage(),e);
			result.put("errno",500);
			result.put("state",0);
			result.put("msg", "上传附件失败");
			result =  formatResult(client,auth,result);
			return result;
	    }finally {
	    	//返回前，对格式进行处理
	    	logger.info("用户["+user.getUserAcc()+"]上传附件返回  >>  "+result);
		}
	}

	/**
	 * 对于上传附件后返回的格式，按不同的客户端进行适配
	 * @param client 如：cc、UMeditor
	 * @param auth   如：public 、 protect
	 * @param result
	 * @return
	 */
	private JSONObject formatResult(String client, String auth, JSONObject result) {
		//该附件的访问权限，public-所有人都可以访问 ，protect-按系统配置的权限进行控制
		if(StringUtils.isBlank(auth)){
			auth = "protect";
		}
				
		//上传附件的客户端，不同客户端，返回不同的消息格式，cc-默认值，普通上传，UMeditor-百度编辑器，需要返回特定的格式
		if(StringUtils.isBlank(client)){
			client = "cc";
		}

		//百度编辑器
		if("UMeditor".equalsIgnoreCase(client)){
			JSONObject rs = new JSONObject();
			int state = result.getIntValue("state");
			
			//默认只返回state
			rs.put("state", result.getString("msg"));
			
			//成功
			if(state==1){
				JSONArray array = result.getJSONArray("dataArray");
				if(array.size()>0){
					JSONObject file = array.getJSONObject(0);
					rs.put("originalName", file.getString("name"));
					rs.put("size", file.getLongValue("size"));
					rs.put("name", file.getString("id") + "." + file.getString("suffix"));
					rs.put("state", "SUCCESS");
					rs.put("type", "."+file.getString("suffix"));
					
					String url = file.getString("idUrl");
					if("public".equals(auth)){
						url = url + "&publicKey=" + SecurityUtil.encryptMsgByMD5(file.getString("id")+"yq-cc-base");
					}
					rs.put("url", url);
				}
			}
			
			return rs;
		}
		result.put("format", "true");
		return result;
	}

	/*public JSONObject result(JSONObject json,String filePath,String fileName){
		JSONObject Path = new JSONObject();
		Path.put(fileName,filePath);

		return Path ;
	}*/
	/**
	 * 用于给其他模块进行文件上传，由界面上控制文件类型、大小
	 * 推荐使用  actionForUpload2
	 * @return 成功上传后，返回文件访问地址
	 */
	@Deprecated
	public JSONObject actionForUpload3(){
		
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户上传附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
		
		HttpServletRequest request=getRequest();
		JSONObject result=new JSONObject();
		result.put("errno",403);
		try {
			String busiType = request.getParameter("busiType");
			String busiId = request.getParameter("busiId");
			String createThumb = this.getRequest().getParameter("createThumb");//
			String createPreview = this.getRequest().getParameter("createPreview");//
			int width = CommonUtil.parseInt(this.getRequest().getParameter("width"));//
			int height = CommonUtil.parseInt(this.getRequest().getParameter("height"));//
			String icon = request.getParameter("icon");
			String fn = StringUtils.equals("icon", icon)?"file1":"file2";
			if(StringUtils.isBlank(busiType)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 未获取到busiType 不能上传!");
				result.put("errno",404);
				return result;
			}

			//校验附件：检查业务对象的附件量是否超标
			String checkResult = AttachmentUtil.checkBusiAttachmentSize(this.getDbName(),busiId);
			if(StringUtils.isNotBlank(checkResult)){
				logger.error("上传附件失败:"+checkResult);
				result.put("errno",500);
				result.put("errmsg",checkResult);
				return result;
			}

			Collection<Part> fileList=request.getParts();
			if(fileList==null||fileList.size()<=0){
				return result;
			}
			JSONObject  json = new JSONObject();
			for (Part part:fileList) {
				try {
					if(part.getSubmittedFileName()==null){
						continue;
					}
					String name = part.getName();
					long fileSize = part.getSize();
					if(!StringUtils.equals(name, fn)) {
						continue;
					}
					long size = part.getSize();
					String oldFileName = new String(getFilename(part).getBytes(), "UTF-8");
					if(StringUtils.isBlank(oldFileName)){
						continue;
					}

					//校验附件：检查后缀、文件大小
					String checkResult2 = OSSAttachmentUtil.checkSizeAndSuffix(oldFileName,size);
					if(StringUtils.isNotBlank(checkResult2)){
						logger.error("上传附件["+oldFileName+","+size+"]失败:"+checkResult2);
						result.put("errno",500);
						result.put("errmsg",checkResult2);
						return result;
					}

					String fileId=IDGenerator.getDefaultNUMID();
					String suffix = oldFileName.substring(oldFileName.lastIndexOf(".") + 1);
					String fileName = fileId+"."+suffix;

					AttachmentFile afile = new AttachmentFile(OSSConstants.getOssMarsServerBucket(),part.getInputStream(), busiType, fileName);
					afile.setFileId(fileId);
					afile.setFileSize(fileSize);
					afile.setBusiId(busiId);

					if("true".equals(createThumb)){
						afile.needCreateThumb();
						afile.setThumbWidth(width);
						afile.setThumbHeigh(height);
					}
					if("true".equals(createPreview)) {
						afile.needCreatePdf();
					}

					//上传文件
					OSSTools.getInstance().uploadFile(afile);
					
					AttachmentDbinfo attachmentDbinfo = new AttachmentDbinfo(afile,QueryFactory.getWriteQuery(),getDbName(),getEntId(),getBusiOrderId());
					if(user!=null){
						attachmentDbinfo.setUserAcc(user.getUserAcc());
						attachmentDbinfo.setUserName(user.getUserName());
						attachmentDbinfo.setAgentphone(user.getUserNo());
					}

					//数据入库
					boolean flag = AttachmentUtil.saveAttachment(attachmentDbinfo);
					if(!flag){
						result.put("errno",500);
						return result;
					}

					logger.info(CommonUtil.getClassNameAndMethod(this)+" 文件上传路径："+afile.getFullPath()+",原文件名:"+oldFileName);
					json.put("id", fileId);
					json.put("name", fileName);
					json.put("path", OSSTools.getInstance().getOssUrl(AttachmentUtil.BUCKET_NAME, afile.getThumbFilePath(), null));
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 文件上传失败："+e.getMessage(),e);
				}finally {
					if(part!=null){
						try {
							part.delete();
						} catch (Exception e2) {
						}
					}
				}
			}

			result.put("data",json );
			result.put("errno",0);
			return result;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 文件上传失败："+e.getMessage(),e);
			result.put("errno",500);
			return result;
		}
	}


	 /**
	  * 检查附件是否存在
	  * @return
	  */
	 public JSONObject actionForCheckAttachment(){
		 UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			logger.error("用户检查附件失败:用户未登录");
			return EasyResult.error(500, "用户未登录.");
		}
			
		 String rootPath = OSSAttachmentUtil.getRootPath();
		 if(StringUtils.isBlank(rootPath)){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 检查附件出错,未配置文件根目录!");
			return EasyResult.error();
		}

		// 获得请求文件名
		String filePath = this.getRequest().getParameter("filePath");
		String id = this.getRequest().getParameter("id");
		if(StringUtils.isAllBlank(filePath,id)){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 检查附件出错,未传入参数!");
			return EasyResult.error();
		}
		
		if(StringUtils.isNotBlank(filePath)){
			filePath = URLDecoder.decode(URLDecoder.decode(filePath));
			if(!filePath.toLowerCase().startsWith(rootPath.toLowerCase())) {
				filePath = rootPath+filePath;
			}
			//判断文件是否存在
			File file = new File(filePath);
			if(!file.exists()){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 检查附件失败,文件不存在:"+file.getAbsolutePath());
				return EasyResult.error();
			}
			return EasyResult.ok(null, "附件存在");
		} else if(StringUtils.isNotBlank(id)){
			try {
				JSONObject row = this.getQuery().queryForRow(" SELECT * FROM "+getTableName("C_CF_ATTACHMENT")+" where ID=? ", new Object[]{id},new JSONMapperImpl());
				if(row==null){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 检查附件出错,附件记录不存在!");
					return EasyResult.error();
				}
				String path = row.getString("FILE_PATH");
				String thumbFilePath = row.getString("THUMB_FILE_PATH");
				String previewPath = row.getString("PREVIEW_PATH");
				
				StringBuffer sb = new StringBuffer();
				if(StringUtils.isNotBlank(path)){
					sb.append(path).append(":").append( new File(OSSAttachmentUtil.getRootPath()+path).exists() ? "存在" : "不存在").append("<br>");
				}
				if(StringUtils.isNotBlank(thumbFilePath)){
					sb.append(thumbFilePath).append(":").append( new File(OSSAttachmentUtil.getRootPath()+thumbFilePath).exists() ? "存在" : "不存在").append("<br>");
				}
				if(StringUtils.isNotBlank(previewPath)){
					sb.append(previewPath).append(":").append( new File(OSSAttachmentUtil.getRootPath()+previewPath).exists() ? "存在" : "不存在").append("<br>");
				}
				return EasyResult.ok(null, sb.toString());
			} catch (Exception e) {
				logger.error("检查附件失败:"+e.getMessage(),e);
			}
		}
		return EasyResult.error();
	 }
	 
	 /**
	  * 富文本图片上传
	  * @return 成功上传后，返回文件访问地址
	 */
	public JSONObject actionForEditorUpload(){
		HttpServletRequest request=getRequest();
		UserModel user =UserUtil.getUser(request);
		JSONObject result=new JSONObject();
		result.put("errno",403);
		result.put("state",0);
		result.put("msg", "请求参数异常");
		
		if(user==null){
			result.put("msg", "用户未登录");
			logger.error("用户上传附件失败:"+result);
			return result;
		}
		try {
			String busiType = request.getParameter("busiType");
			String busiId = request.getParameter("busiId");
//			String isDelHistory = request.getParameter("isDelHistory");
			String createThumb = this.getRequest().getParameter("createThumb");//
			String createPreview = this.getRequest().getParameter("createPreview");//
			String flags = request.getParameter("flag");
			int width = CommonUtil.parseInt(this.getRequest().getParameter("width"));//
			int height = CommonUtil.parseInt(this.getRequest().getParameter("height"));//
			if(StringUtils.isBlank(busiType)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+"用户["+user.getUserAcc()+"]上传富文本图片, 未获取到busiType 不能上传!");
				result.put("errno",404);
				result.put("msg", "缺少必填参数");
				result.put("state",0);
				result.put("uploaded","0");
				return result;
			}
			
			String[] fileSuffix = ConfigUtil.getString("cc-base", "ATTACHMENT_ONLINE_SUFFIX", ".jpg;.jpeg;.png;").split(";");
							
//			if(StringUtils.equals("true", isDelHistory)) {
//				AttachmentUtil.deleteHistory(this.getDbName(),busiId, busiType);
//			}

//			//校验附件：检查业务对象的附件量是否超标
//			String checkResult = AttachmentUtil.checkBusiAttachmentSize(this.getDbName(),busiId);
//			if(StringUtils.isNotBlank(checkResult)){
//				logger.error("用户["+user.getUserAcc()+"]上传附件失败:"+checkResult);
//				result.put("errno",500);
//				result.put("msg", "上传附件失败");
//				result.put("state",0);
//				String[] check = checkResult.split(" ");
//				result.put("errmsg",getI18nValue(check[0])+check[1]+getI18nValue(check[2]));
//				return result;
//			}

			Collection<Part> fileList=request.getParts();
			if(fileList==null||fileList.size()<=0){
				logger.error("用户["+user.getUserAcc()+"]上传富文本图片失败:请求中无文件流");
				result.put("errno",500);
				result.put("msg", "上传附件失败");
				result.put("state",0);
				result.put("uploaded","0");
				return result;
			}
			
			//3.4.1#20231101-1 支持一次性上传多个附件；增加 返回参数 dataArray，返回每个附件的上传结果
			JSONArray dataArray = new JSONArray();
			
			for (Part part:fileList) {
				try {
					String fileName = new String(getFilename(part).getBytes(), "UTF-8"); 
					
					String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
					if (!fileSuffixSet(fileSuffix, suffix)) {
						JSONObject error = new JSONObject();
						error.put("message", "上传图片失败，图片格式应为:" + JSONObject.toJSONString(fileSuffix) + "中的一种");
						result.put("error", error);
						result.put("uploaded","0");
						return result;
					}
					
					JSONObject  json  = upload(user.getEpCode(),user.getBusiOrderId(),user.getSchemaName(),part,busiType,busiId, "false",createThumb,createPreview,flags,width,height);
					if(json!=null){
						logger.info("用户["+user.getUserAcc()+"]上传富文本图片结果:"+json);
						
						if(json.getIntValue("errno")==0){
							dataArray.add(json);
						}
						result.put("url", json.getString("idUrl"));
						result.put("uploaded","1");
						result.put("errno",0);
						result.put("state",1);
						result.put("msg", "上传成功");
						
					}else{
						logger.info("用户["+user.getUserAcc()+"]上传富文本图片结果为空...");
					}
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) +" 用户["+user.getUserAcc()+"] 富文本图片上传失败："+e.getMessage(),e);
				}finally {
					if(part!=null){
						try {
							part.delete();
						} catch (Exception e2) {
						}
					}
				}
			}
			return result;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) +" 用户["+user.getUserAcc()+"]富文本图片上传失败："+e.getMessage(),e);
			result.put("errno",500);
			result.put("state",0);
			result.put("msg", "上传附件失败");
			result.put("uploaded","0");
			return result;
	    }
	}
	
	public boolean fileSuffixSet(String[] arr, String value) {
		Set<String> set = new HashSet<String>(Arrays.asList(arr));
		return set.contains(value);
	}

}
