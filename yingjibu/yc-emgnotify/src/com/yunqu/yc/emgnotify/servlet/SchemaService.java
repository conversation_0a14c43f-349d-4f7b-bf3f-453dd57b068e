package com.yunqu.yc.emgnotify.servlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgnotify.base.Constants;
import com.yunqu.yc.emgnotify.log.CommonLogger;
import com.yunqu.yc.emgnotify.utils.CommonUtil;
import com.yunqu.yc.emgnotify.utils.EasyQueryUtil;
import com.yunqu.yc.emgnotify.utils.JsonUtil;

/**
 * 处理企业、用户所在数据库的相关方法
 * 
 * <AUTHOR>
 *
 */
public class SchemaService {

	private static Logger logger = CommonLogger.logger;
	
	private static EasyCache cache = CacheManager.getMemcache();
	
	private static AppContext context = AppContext.getContext("yc-emgnotify");

	/**
	 * 根据企业ID获取对应的数据库名
	 * 
	 * @return SCHEMA_NAME 字段为数据库名
	 */
	public static String findSchemaByEntId(String entId) {
		String key  = Constants.CK_CC_ENT_SCHEMA+entId;
		String cacheStr = cache.get(key);
		if(StringUtils.isNotBlank(cacheStr)){
			JSONObject json= JsonUtil.toJSONObject(cacheStr);
			return json.getString("SCHEMA_NAME");
		}
		String sql = "SELECT DISTINCT T2.SCHEMA_NAME FROM CC_ENT_RES T  JOIN CC_SCHEMA_RES T2 ON T.SCHEMA_ID = T2.SCHEMA_ID AND T2.SCHEMA_NAME IS NOT NULL AND  "
				+ " (T.ENT_ID=? OR EXISTS (SELECT 1 FROM CC_EC_NODE T3 WHERE T3.CENTER_ID=? AND T3.ENT_ID = T.ENT_ID) ) ";  //update by lj 20200225 兼容虚拟运营中心，虚拟运营中心获取到的是机构id，要根据机构id获取到企业id
		try {
			JSONObject row = EasyQueryUtil.getBusiQuery().queryForRow(sql, new Object[] {entId,entId}, new JSONMapperImpl());
			if (row != null) {
				//3.2#20220228-1 数据源为空时直接忽略，避免各个模块报错
				String schema = row.getString("SCHEMA_NAME");
				if(StringUtils.isBlank(schema)){
					return null;
				}
				
				JSONObject j = new JSONObject();
				j.put("ENT_ID", entId);
				j.put("SCHEMA_NAME", schema);
				//update20201101 原有效期为100小时改成2小时，由于某现场将mars切换了不同的库，在不同库里同一企业的业务库不一致，导致切换后缓存未失效一直查询的原来的业务库
				cache.put(key, j.toJSONString(),3600*2);
				
				return schema;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(SchemaService.class) + " 根据企业ID获取对应的数据库名出错." + e.getMessage(),e);
		}
		return null;

	}
}
