package com.yunqu.yc.emgnotify.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgnotify.base.AppBaseServlet;
import com.yunqu.yc.emgnotify.base.Constants;
import com.yunqu.yc.emgnotify.base.QueryFactory;
import com.yunqu.yc.emgnotify.dict.DictCacheMgr;
import com.yunqu.yc.emgnotify.log.CommonLogger;
import com.yunqu.yc.emgnotify.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 接口监听请求Servlet
 */
@WebServlet("/post/interface")
public class InterfaceServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    protected Logger logger = CommonLogger.getLogger("interface");

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        super.doGet(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        req.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html; charset=UTF-8");
        String json = getRequestStr(req);
        JSONObject data = JSONObject.parseObject(json);
        logger.info("接收原始报文数据==>>" + json);
        JSONObject res = new JSONObject();
        try {
            String timestamp = data.getString("timestamp");
            if (StringUtils.isBlank(timestamp)) {
                res.put("respCode", "400");
                res.put("respDesc", "请求参数错误");
                resp.getWriter().print(res);
                return;
            }
            // 白名单
            String refererHost = Constants.getServerWhiteHost();
            if (StringUtils.isBlank(refererHost)) {
                res.put("respCode", "999");
                res.put("respDesc", "请先配置白名单");
                resp.getWriter().print(res);
                return;
            }
            String referer = IPCheckerUtils.getIpAddr(req);
            logger.info("白名单:" + refererHost + ";实际请求ip:" + referer);
            if (StringUtils.isNotBlank(referer)) {
                boolean refererCheckResult = IPCheckerUtils.isIpInSubnet(referer, refererHost);
                if (!refererCheckResult) {
                    // resp.sendError(403, "Illegal Access!");
                    res.put("respCode", "403");
                    res.put("respDesc", "Illegal Access!");
                    resp.getWriter().print(res);
                    return;
                }
            }
            // 时间戳校验
            //检查timestamp是否过期
            long miseconds = DateUtil.calDurationMills(timestamp);
            if (miseconds > Constants.getRequestTimestamp()) {
                res.put("respCode", "999");
                res.put("respDesc", "请求已过期，当前时间:" + DateUtil.getCurrentDateStr() + ",请求时间:" + timestamp);
                resp.getWriter().print(res);
                return;
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            res.put("respCode", "999");
            res.put("respDesc", "接口异常:" + ex.getMessage());
            resp.getWriter().print(res);
            return;
        }

        /**
         * 000 成功
         * 001 包文格式错误
         * 002 无效的操作请求
         * 400 请求参数错误
         * 403 未配置白名单
         * 999 未定义错误
         */

        //固定企业Id
        String entId = data.getString("entId");
        //固定业务Id
        String busiOrderId = data.getString("busiOrderId");
        if (StringUtils.isBlank(entId) || StringUtils.isBlank(busiOrderId)) {
            res.put("respCode", "004");
            res.put("respDesc", "无效的业务订购");
            resp.getWriter().print(res);
            return;
        }
        String command = data.getString("command");
        //新增：add，修改：update
        String serviceType = data.getString("serviceType");
        if (StringUtils.isBlank(command) || StringUtils.isBlank(serviceType)) {
            res.put("respCode", "003");
            res.put("respDesc", "请求参数错误");
            resp.getWriter().print(res);
            return;
        }
        if ("rescueInterface".equals(command)) {
            if ("add".equals(serviceType)) {
                // 新增
                res = addorUpdateRescueInterface(data);
            } else if ("update".equals(serviceType)) {
                // 修改
                res = addorUpdateRescueInterface(data);
            } else if ("delete".equals(serviceType)) {
                // 删除
                res = deleteRescueInterface(data);
            } else {
                res.put("respCode", "002");
                res.put("respDesc", "无效的操作请求");
                resp.getWriter().print(res);
                return;
            }
        } else {
            res.put("respCode", "002");
            res.put("respDesc", "无效的操作请求");
            resp.getWriter().print(res);
            return;
        }
        resp.getWriter().print(res);
    }

    //删除||批量删除接话人接口
    public JSONObject deleteRescueInterface(JSONObject data) {
        JSONObject result = new JSONObject();
        //接话人唯一标识
        String rescueId = data.getString("rescueId");
        if (StringUtils.isBlank(rescueId)) {
            result.put("respCode", "003");
            result.put("respDesc", "请求参数错误");
            logger.error("rescueId不能为空,请检查！");
            return result;
        }
        EasyQuery query = QueryFactory.getWriteQuery();
        try {
            String queryIphone = "SELECT RESCUE_PHONE FROM " + Constants.getBusiName() + ".jy_rescue_user  WHERE ID = ? and IS_DELETE = '0'";
            String phone = query.queryForString(queryIphone, rescueId);

            //修改接话人优先级
            EasyRecord record = new EasyRecord(Constants.getBusiName() + ".jy_rescue_user", "ID");
            //1 删除  0未删除
            record.put("IS_DELETE", "1");
            record.put("RESCUE_STATE", Constants.RESCUE_STATE2);
            record.put("RESCUE_PHONE", phone + System.currentTimeMillis() / 1000);
            result.put("respCode", "000");

            String[] ids = rescueId.split(",");
            query.begin();
            for (String id : ids) {
                record.put("ID", id);
                query.update(record);
            }
            query.commit();
            result.put("respDesc", "删除成功");
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (Exception e2) {
                logger.error("删除回滚失败，原因：" + e2.getMessage());
            }
            logger.error("删除失败，原因：" + e.getMessage());
            result.put("respCode", "999");
            result.put("respDesc", "删除失败");
        }
        // 删除缓存数据
//        deleteRescueUserCache(rescueId);
        return result;
    }

    //添加或者修改单条接话人同步接口
    public JSONObject addorUpdateRescueInterface(JSONObject data) {
        JSONObject result = new JSONObject();
        //固定企业Id
        String entId = Constants.getEntId();
        //固定业务Id
        String busiOrderId = Constants.getBusiOrderId();
        //新增：add，修改：update
        String serviceType = data.getString("serviceType");
        //接话人唯一标识
        String rescueId = data.getString("rescueId");
        //接话人名称
        String rescueName = data.getString("rescueName");
        //接话人电话
        String rescuePhone = data.getString("rescuePhone");
        // 灾害事件id
        String rescueEventID = data.getString("rescueEventID");
        // 灾害事件名称
        String rescueEventName = data.getString("rescueEventName");
        // 灾害事件code
        String rescueTypeCode = data.getString("rescueTypeCode");
        // 灾害事件类型名称
        String rescueTypeName = data.getString("rescueTypeName");
        //接话人省
        String rescueProvince = data.getString("rescueProvince");
        //接话人市
        String rescueArea = data.getString("rescueArea");
        //接话人详细地址
        String rescueDetailed = data.getString("rescueDetailed");

        if (StringUtils.isBlank(rescueId) || StringUtils.isBlank(rescueName) || StringUtils.isBlank(rescuePhone) || StringUtils.isBlank(rescueEventID) || StringUtils.isBlank(rescueEventName) || StringUtils.isBlank(rescueProvince)) {
            result.put("respCode", "003");
            result.put("respDesc", "请求参数错误");
            return result;
        }
        EasyQuery query = QueryFactory.getWriteQuery();
        //修改接话人优先级
        EasyRecord record = new EasyRecord(Constants.getBusiName() + ".jy_rescue_user", "ID");
        record.put("ID", rescueId);
        /** 密评改造留痕 dongfenghua 测试通过后删除本条注释*/
        record.put("RESCUE_PHONE", rescuePhone);
        record.put("RESCUE_NAME", rescueName);
        record.put("RESCUE_PROVINCE", rescueProvince);
        record.put("RESCUE_AREA", rescueArea);
        record.put("RESCUE_DETAILED", rescueDetailed);
        record.put("RESCUE_EVENT_ID", rescueEventID);
        record.put("RESCUE_EVENT_NAME", rescueEventName);
        record.put("RESCUE_TYPE_CODE", rescueTypeCode);
        record.put("RESCUE_TYPE_NAME", rescueTypeName);
        record.put("RESCUE_REMARK", null);//备注
        record.put("BUSI_ORDER_ID", busiOrderId);//订购id
        record.put("ENT_ID", entId);//企业id
        record.put("RESCUE_STATE", Constants.RESCUE_STATE1);//01-在线，02-离线
        //1 删除  0未删除
        record.put("IS_DELETE", "0");
        // 1-应急管理部
        record.put("SOURCE_TYPE", Constants.SOURCE_TYPE_95707);
        SecretDataUtil.encrypt(record);
        try {
            // 保存 事件字典
            saveDict(data);
            // 判断该手机号是否被厅里维护
            String sql = "select count(1) from " + Constants.getBusiName() + ".jy_rescue_user where RESCUE_PHONE = ? and IS_DELETE = '0' and SOURCE_TYPE = '0'";
            int i = query.queryForInt(sql, SecretDataUtil.encrypt(rescuePhone));
            if (i > 0) {
                logger.info(rescuePhone + "号码已被厅里维护！");
                result.put("respCode", "006");
                result.put("respDesc", "手机号" + rescuePhone + "已被厅里维护!");
                return result;
            }
            result.put("respCode", "000");
            if ("add".equals(serviceType)) {
                record.put("RESCUE_PRIORITY", Constants.RESCUE_PRIORITY_ONE);//优先级01-高，02-中，03-低
                record.put("RESCUE_WORK_STATE", Constants.RESCUE_WORK_STATE_ONE);//通话状态 01-空闲中，02-通话中
                record.put("CALL_COUNT", 0);//通话次数默认为0
                record.put("PRIORITY_COUNT", 0);//用来判断最少的接话人优先接话 默认0
                record.put("CREATE_TIME", DateUtil.getCurrentDateStr());//创建时间
                if (query.queryForExist("select count(1) from " + Constants.getBusiName() + ".jy_rescue_user where ID=?", rescueId)) {
                    record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());//更新时间
                    query.update(record);
                    result.put("respDesc", "该接话人已存在，更新成功");
                } else {
                    query.save(record);
                    result.put("respDesc", "新增成功");
                }
                // 异步 提醒
                CompletableFuture.runAsync(() -> {
                    try {
                        // 外呼
                        addTask(rescueName, rescueTypeName, Constants.TASK_TYPE_IVR, rescuePhone);
                        // 短信
                        addTask(rescueName, rescueTypeName, Constants.TASK_TYPE_MESSAGE, rescuePhone);
                    } catch (Exception e) {
                        logger.info("[短信、外呼提醒异常]" + e.getMessage(), e);
                    }
                });
            } else if ("update".equals(serviceType)) {
                record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());//更新时间
                query.update(record);
                result.put("respDesc", "修改成功");
            }
        } catch (SQLException e) {
            result.put("respCode", "999");
            result.put("respDesc", "失败");
            logger.error("修改或新增失败，原因：" + e.getMessage());
        }
        return result;
    }


    private void saveDict(JSONObject data) {
        try {
            //固定企业Id
            String entId = Constants.getEntId();
            //固定业务Id
            String busiOrderId = Constants.getBusiOrderId();
            //事件code
            String rescueTypeCode = data.getString("rescueTypeCode");
            //事件类型名称
            String rescueTypeName = data.getString("rescueTypeName");
            if (StringUtils.isBlank(rescueTypeCode) || StringUtils.isBlank(rescueTypeName)) {
                return;
            }
            EasyQuery query = QueryFactory.getWriteQuery();
            // 保存事件字典。
            String id = query.queryForString("SELECT ID from " + Constants.getBusiName() + ".C_CF_DICTGROUP where CODE= ?", "RESCUE_EVENT");
            if (StringUtils.isNotBlank(id)) {
                if (!query.queryForExist("SELECT count(*) from " + Constants.getBusiName() + ".C_CF_DICT where DICT_GROUP_ID = ? and name = ?", id, rescueTypeName)) {
                    EasyRecord dictRecord = new EasyRecord(Constants.getBusiName() + ".C_CF_DICT", "ID");
                    dictRecord.put("ID", RandomKit.randomStr());
                    dictRecord.put("CREATE_TIME", EasyDate.getCurrentDateString());
                    dictRecord.put("NAME", rescueTypeName);
                    dictRecord.put("CODE", rescueTypeCode);
                    dictRecord.put("SORT_NUM", 1);
                    dictRecord.put("ENABLE_STATUS", "Y");
                    dictRecord.put("DICT_GROUP_ID", id);
                    dictRecord.put("BUSI_ORDER_ID", busiOrderId);
                    dictRecord.put("ENT_ID", entId);
                    if (query.save(dictRecord)) {
                        DictCacheMgr.reloadAll();
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /*
     *
     * <AUTHOR>
     * @date  2024/1/10 09:14
     * @param taskName = rescueEventName + "接话人外呼提醒任务";
     * @param entId
     * @param busiOrderId
     * @param taskType    任务类型 3-ivr任务 4-短信任务 6-广播
     * @param robotId    机器人id
     * @param f1    String f1 = "{姓名}您好，您已成为 {灾情事件名称} 的接话响应人员，请注意及时接听响应95707来电。";
     * @return void
     */
    public void addTask(String rescueName, String rescueTypeName, Integer taskType, String caller) {
        try {
            EasyQuery writequery = QueryFactory.getWriteQuery();
            String entId = Constants.getEntId();
            String busiOrderId = Constants.getBusiOrderId();
            // 获取接话人提醒短信模版
            JSONObject smsTemp = SMSUtil.getOnlySmsTemp(writequery, Constants.JY_TASK_CREATE_TYPE4);
            if (smsTemp == null) {
                return;
            }
            String content = smsTemp.getString("TEMP_CONTENT");//内容
            String channelId = smsTemp.getString("CHANNEL_ID");
            String tempId = smsTemp.getString("ID");
            content = content.replace("{姓名}", rescueName).replace("{事件类型}", rescueTypeName).trim();
            JSONObject recordJson = new JSONObject();
            String taskName = "";
            recordJson.put("TASK_TYPE", taskType);
            recordJson.put("TASK_SOURCE", "3");//任务来源 1:人工创建 2:系统自动创建每日定时完成 3:系统自动创建长期启动  9:一键通任务
            recordJson.put("TASK_STATE", Constants.TASK_STATE_005);
            recordJson.put("TASK_NAME", taskName);
            //企业id
            recordJson.put("ENT_ID", entId);
            //订购id
            recordJson.put("BUSI_ORDER_ID", busiOrderId);

            recordJson.put("NODE_CODE", Constants.getProvinceCode()); //部门编码
            String taskId = "";
            if (Constants.TASK_TYPE_IVR.equals(taskType)) {
                recordJson.put("TEMPLATE_TYPE", Constants.JY_TASK_CREATE_TYPE4);
                //拓展1 机器人类型 1-通知 2-交互
                recordJson.put("ROBOT_TYPE", "1");
                //机器人id
                recordJson.put("IVR_FLOW_NAME", Constants.getRobotIdForInform());
                taskName = rescueTypeName + "【接话人通知外呼任务】";
                if (RedissonUtil.exist(taskName)) {
                    Thread.sleep(500);
                }
                // 查询任务id
                taskId = TaskUtil.getTaskIdByName(taskName, null, recordJson);
                RedissonUtil.setEx(taskName, taskId, 1);
            } else {
                recordJson.put("TEMPLATE_TYPE", Constants.JY_TASK_CREATE_TYPE8);
                //拓展2 短信渠道id
                recordJson.put("SMS_CHANNEL_ID", channelId);
                //模版id
                recordJson.put("SMS_TEMPLATE_ID", tempId);
                taskName = rescueTypeName + "【接话人通知短信任务】";
                if (RedissonUtil.exist(taskName)) {
                    Thread.sleep(500);
                }
                // 查询任务id
                taskId = SMSUtil.getSmsTaskIdByName(taskName, null, recordJson);
                RedissonUtil.setEx(taskName, taskId, 1);
            }
            String insertSql = "";
            String tableName = "";
            Object[] paramObj;
            if (Constants.TASK_TYPE_IVR.equals(taskType)) {
                insertSql = "insert  into " + Constants.getBusiName() + ".cc_task_obj (OBJ_ID,DATE_ID,ENT_ID,BUSI_ORDER_ID,TASK_ID,MONTH_ID,TASK_STATE,TEL_NUM1,CUST_NAME,F1) values (?,?,?,?,?,?,?,?,?,?)";
                paramObj = new Object[]{RandomKit.uuid(), EasyDate.getCurrentDateString("yyyyMMdd"), entId, busiOrderId, taskId, EasyCalendar.newInstance().getFullMonth(), 0, SecretDataUtil.encrypt(caller), rescueName, content};
                tableName = "cc_task";
            } else {
                insertSql = "insert  into " + Constants.getBusiName() + ".jy_sms_task_obj (OBJ_ID,DATE_ID,ENT_ID,BUSI_ORDER_ID,TASK_ID,MONTH_ID,TASK_STATE,TEL_NUM1,CUST_NAME,F1) values (?,?,?,?,?,?,?,?,?,?)";
                paramObj = new Object[]{RandomKit.uuid(), EasyDate.getCurrentDateString("yyyyMMdd"), entId, busiOrderId, taskId, EasyCalendar.newInstance().getFullMonth(), 0, SecretDataUtil.encrypt(caller), rescueName, content};
                tableName = "jy_sms_task";
            }
            writequery.execute(insertSql, paramObj);
            writequery.executeUpdate("update " + Constants.getBusiName() + "." + tableName + " set OBJ_COUNT = OBJ_COUNT + 1 where TASK_ID = ?", taskId);
            // 如果是外呼任务，通知ocs
            if (Constants.TASK_TYPE_IVR.equals(taskType)) {
                TaskUtil.syncTaskInfo(taskId, "3", entId, 3);
            }
        } catch (Exception e) {
            logger.error("创建接话人通知任务异常" + e.getMessage(), e);
        }
    }

    public String getRequestStr(HttpServletRequest request) {
        StringBuilder dataBui = new StringBuilder(); //
        String line = null;
        BufferedReader reader = null;

        try {
            reader = request.getReader();
            while ((line = reader.readLine()) != null) {
                dataBui.append(line);
            }
        } catch (Exception e) {
            logger.error("[InterfaceServlet.getRequestStr] error:" + e.getMessage(), e);
            return null;
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                logger.error("[BufferedReader关闭异常] error:" + e.getMessage(), e);
            }
        }
        return dataBui.toString();
    }
}
