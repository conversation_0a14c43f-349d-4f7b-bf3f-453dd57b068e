/**
 *
 */
package com.yunqu.yc.emgnotify.dao.param;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgnotify.base.AppDaoContext;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name="param")
public class ParamDao extends AppDaoContext {
/**
	 * 资源鉴权处理留痕 dongfenghua 测试通过后删除
	 * edit:中心基础子系统-回访规则配置
	 */

	/**
	 * 系统参数配置
	 * @return
	 */
/**
	 * 鉴权改造-wcz
	 * edit 叫应规则配置
	 */

	@PreAuthorize(resId = {"yc-emgnotify-call-95707-callRuleConfig","yc-emgnotify-call-callRuleConfig","yc-emgnotify-call-addTask"})
	@WebControl(name = "edit", type = Types.RECORD)
	public JSONObject edit() {
		String code = param.getString("pk");
		if(StringUtils.isNotBlank(code)) {
			EasySQL query=this.getEasySQL("select PARAM_CONFIG from "+this.getTableName("C_CF_ENT_PARAM")+" where 1=1 ");
			query.append(" and LABEL_CODE=? and BUSI_ORDER_ID=? and ENT_ID=? ");
			try {
				String config = getQuery().queryForString(query.getSQL(), new Object[]{code,this.getBusiOrderId(),this.getEntId()});
				if(StringUtils.isNotBlank(config)) {
					JSONObject cfg = JSON.parseObject(config);
					return EasyResult.ok(cfg);
				}
			} catch (Exception e) {
				this.error("获取参数错误", e);
			}
		}
		return EasyResult.ok();
	}
}
