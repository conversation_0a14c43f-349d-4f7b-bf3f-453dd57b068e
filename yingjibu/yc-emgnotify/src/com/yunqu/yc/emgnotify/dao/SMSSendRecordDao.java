package com.yunqu.yc.emgnotify.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgnotify.base.AppDaoContext;
import com.yunqu.yc.emgnotify.utils.SecretDataUtil;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import java.util.List;

@WebObject(name = "SMSSendRecordDao")
public class SMSSendRecordDao extends AppDaoContext {

    /**
     * 审核列表(包括待审核，审核通过，审核不通过)
     * @return
     */
    /**
     * 鉴权改造-wcz
     * list 安全管控配置
     */
    @PreAuthorize(resId = {"yc-emgnotify-sms-checkList"})
    @WebControl(name = "list", type = Types.LIST)
    public JSONObject list() {
        EasySQL sql = DataStatSql.getSMSSendRecordSql(param, getDbName());
        // 密评改造
        JSONObject jsonObject = this.queryForPageList(sql.getSQL(), sql.getParams());
        JSONArray dataArray = jsonObject.getJSONArray("data");
        List<JSONObject> data = dataArray.toJavaList(JSONObject.class);
        List<JSONObject> decrypt = SecretDataUtil.decrypt(data);
        jsonObject.put("data", decrypt);
        return jsonObject;
    }


}
