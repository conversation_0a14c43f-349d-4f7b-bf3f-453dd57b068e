package com.yunqu.yc.emgmonitor.config;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgmonitor.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.string.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.*;

import java.util.stream.Stream;

public class RedissonClientKit {

    public static Logger logger = CommonLogger.logger;
    //使用静态内部类方式完成单例模式
    private static class SingletonHolder {
        private static final RedissonClientKit INSTANCE = new RedissonClientKit();

        private static RedissonClient REDISSONCLI;

        static {
            REDISSONCLI = redissonClient();
        }

        /**
         * 实例化redissonClient
         *
         * @return
         */
        public static RedissonClient redissonClient() {
            String addrInfo = ServerContext.getCacheAddr();
            String redisConf = ServerContext.getProperties("redisConf", "");
            JSONObject params = new JSONObject();
            if (StringUtils.notBlank(redisConf)) {
                params = JSONObject.parseObject(redisConf);
            } else {
                params.put("auth", "");
                params.put("connType", "cluster");
            }
            String connType = params.getString("connType");
            String database = params.getString("database");
            String auth = params.getString("auth");
            String masterName = params.getString("masterName");
            if (StringUtils.equals("single", connType)) {
                return single(addrInfo, database, auth);
            } else if (StringUtils.equals("sentinel", connType)) {
                return sentinel(addrInfo, database, auth, masterName);
            } else if (StringUtils.equals("cluster", connType)) {
                return cluster(addrInfo, database, auth);
            }
            return null;
        }


        /**
         * 单机模式
         *
         * @param addrInfo
         * @param database
         * @param auth
         * @return
         */
        private static RedissonClient single(String addrInfo, String database, String auth) {
            addrInfo = "redis://" + addrInfo;
            if (StringUtils.isBlank(database)) {
                database = "0";
            }
            Config config = new Config();
            config.setCodec(JsonJacksonCodec.INSTANCE);
            config.setNettyThreads(128);
            config.setThreads(64);
            config.setTransportMode(TransportMode.NIO);
            config.setLockWatchdogTimeout(10000);
            //config.setExecutor(RedissonThreadPoolExecutor.getInstance().getExecutor());
            SingleServerConfig singleServerConfig = config.useSingleServer();
            singleServerConfig.setAddress(addrInfo);
            if(StringUtils.isNotBlank(auth)) {
                singleServerConfig.setPassword(auth);
            }
            singleServerConfig.setConnectionPoolSize(800);
            singleServerConfig.setConnectionMinimumIdleSize(20);
            singleServerConfig.setConnectTimeout(100000);
            singleServerConfig.setTimeout(100000);
            singleServerConfig.setRetryInterval(10000);
            singleServerConfig.setPingConnectionInterval(100000);
            singleServerConfig.setIdleConnectionTimeout(100000);
            singleServerConfig.setKeepAlive(true);
            return Redisson.create(config);
        }

        /**
         * 哨兵模式
         *
         * @param addrInfo
         * @param database
         * @param auth
         * @param masterName
         * @return
         */
        private static RedissonClient sentinel(String addrInfo, String database, String auth, String masterName) {
            if (StringUtils.isBlank(database)) {
                database = "0";
            }
            logger.info("init redis sentinel[" + addrInfo + "][" + database + "][" + auth + "][" + masterName + "]...");
            String[] addrs = addrInfo.split(",");
            Config config = new Config();
            config.setNettyThreads(128);
            config.setThreads(64);
            config.setTransportMode(TransportMode.NIO);
            config.setLockWatchdogTimeout(10000);
            config.setCodec(JsonJacksonCodec.INSTANCE);
            //config.setExecutor(RedissonThreadPoolExecutor.getInstance().getExecutor());
            SentinelServersConfig sentinelConfig = config.useSentinelServers();
            sentinelConfig.addSentinelAddress(Stream.of(addrs).map(o -> "redis://" + o).distinct().toArray(String[]::new));
            sentinelConfig.setMasterName(masterName);
            sentinelConfig.setMasterConnectionPoolSize(800);
            sentinelConfig.setSlaveConnectionPoolSize(800);
            sentinelConfig.setMasterConnectionMinimumIdleSize(20);
            sentinelConfig.setSlaveConnectionMinimumIdleSize(20);
            sentinelConfig.setConnectTimeout(10000);
            sentinelConfig.setTimeout(10000);
            //sentinelConfig.setDatabase(Integer.parseInt(database));
            if(StringUtils.isNotBlank(auth)) {
                sentinelConfig.setPassword(auth);
            }
            sentinelConfig.setPingConnectionInterval(10000);
            sentinelConfig.setIdleConnectionTimeout(10000);
            sentinelConfig.setKeepAlive(true);
            return Redisson.create(config);
        }

        /**
         * 集群模式
         *
         * @param addrInfo 地址
         * @param database 数据库
         * @param auth     密码
         * @return
         */
        private static RedissonClient cluster(String addrInfo, String database, String auth) {
            if (StringUtils.isBlank(database)) {
                database = "0";
            }
            logger.info("init redis cluster[" + addrInfo + "][" + database + "][" + auth + "]...");
            String[] addrs = addrInfo.split(",");
            Config config = new Config();
            config.setNettyThreads(128);
            config.setThreads(64);
            config.setTransportMode(TransportMode.NIO);
            config.setLockWatchdogTimeout(10000);
            config.setCodec(JsonJacksonCodec.INSTANCE);
            //config.setExecutor(RedissonThreadPoolExecutor.getInstance().getExecutor());
            ClusterServersConfig clusterServersConfig = config.useClusterServers();
            clusterServersConfig.addNodeAddress(Stream.of(addrs).map(o -> "redis://" + o).distinct().toArray(String[]::new));
            if(StringUtils.isNotBlank(auth)) {
                clusterServersConfig.setPassword(auth);
            }
            clusterServersConfig.setMasterConnectionPoolSize(800);
            clusterServersConfig.setSlaveConnectionPoolSize(800);
            //clusterServersConfig.setDatabase(Integer.parseInt(database));
            clusterServersConfig.setMasterConnectionMinimumIdleSize(20);
            clusterServersConfig.setSlaveConnectionMinimumIdleSize(20);
            clusterServersConfig.setConnectTimeout(10000);
            clusterServersConfig.setTimeout(10000);
            clusterServersConfig.setPingConnectionInterval(10000);
            clusterServersConfig.setIdleConnectionTimeout(10000);
            clusterServersConfig.setKeepAlive(true);
            return Redisson.create(config);
        }
    }

    private RedissonClientKit() {
    }

    public static final RedissonClientKit getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public RedissonClient getRedissonClient() {
        return SingletonHolder.REDISSONCLI;
    }

}
