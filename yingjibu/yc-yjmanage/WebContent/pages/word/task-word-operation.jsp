<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>话术</title>
	<style type="text/css">
		.taskWordContent{word-break:break-all}
		.taskWordTextArea{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<textarea id="taskWordTextArea" rows="5" class="form-control input-sm taskWordTextArea" placeholder="暂时没有设置话术,您可以填写设置">
		
	</textarea>
	<p class="taskWordTextArea"><button class="btn btn-info btn-sm mt-5 text-c" onclick="saveTaskWord();" type="button">保存</button></p>
	
	<div class="taskWordContent">
		
	</div>
	<p style="float: right;bottom: 0"><button class="btn btn-xs btn-warning" id="notShow" onclick="notShow()" type="button">不再自动显示</button></p>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		var wordId='${param.wordId}';
		$(function(){
			if(wordId){
				ajax.daoCall({params:{wordId:wordId},controls:['TaskWordDao.record']},function(result){
					if(result['TaskWordDao.record']){
						var title=result['TaskWordDao.record'].data.WORD_TITLE;
						var content=result['TaskWordDao.record'].data.WORD_TEXT;
						$(".taskWordContent").html(content);
						layer.title(title, sessionStorage.index);
					}
				});
				
				 var storage=window.localStorage;
				 if(storage&&storage.getItem("showAgentTaskWord")==1){
					 $("#notShow").text("自动弹出话术");
				 }
				
			}else{
				if(window.localStorage){
					 var storage=window.localStorage;
					 $("#taskWordTextArea").val(storage.getItem("agentTaskWord"));
					 $(".taskWordTextArea").show();
				}else{
					$(".taskWordContent").html("没有设置话术!");
				}
			}
		});
		function replaceTextarea1(str){
		    var reg=new RegExp("\r\n","g");
		    str = str.replace(reg,"<br>");
		    return str;
		}

		function saveTaskWord(){
			  var content=$("#taskWordTextArea").val();
			  var storage=window.localStorage;
			  storage.setItem("agentTaskWord",content);
			  layer.msg("保存成功!");
			  popup.layerClose("taskWordContent");
			
		}
		function notShow(){
			 var storage=window.localStorage;
			 if(storage){
				 var flag=storage.getItem("showAgentTaskWord");
				 if(flag==1){
					 storage.setItem("showAgentTaskWord","0")
				 }else{
					 storage.setItem("showAgentTaskWord","1")
				 }
				 layer.msg("设置完毕!");
				}
			 popup.layerClose(".taskWordContent");
			 
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>