package com.yunqu.yc.yimanage.task;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.yimanage.base.CommonLogger;
import com.yunqu.yc.yimanage.base.Constants;
import com.yunqu.yc.yimanage.base.QueryFactory;
import com.yunqu.yc.yimanage.base.TaskConstants;
import com.yunqu.yc.yimanage.inf.InitListener;
import com.yunqu.yc.yimanage.utils.DateUtil;
import com.yunqu.yc.yimanage.utils.SmsUtil;

public class SmsTaskThread extends Thread {
	
	private static boolean isRuning = false;

	private String taskId;
	// 定时发送时间
	private String timingSend;
	private EasyQuery query;
	private Logger logger;
	
	public SmsTaskThread(){
		
	}
	public SmsTaskThread(String taskId,String timingSend){
		this.taskId = taskId;
		this.timingSend = timingSend;
		this.query = QueryFactory.getWriteQuery();
		this.logger = CommonLogger.getLogger("tasksms");
	}

	@Override
	public void run() {
		isRuning = true;
		
		if(query == null){
			query = EasyQuery.getQuery(Constants.APP_NAME,Constants.DS_NAME);
		}
		if(logger == null){
			this.logger = CommonLogger.logger;
		}
		logger.info(">>>>>>>>smsTask Thread start:taskId="+taskId);
		String orgcode = Constants.getSmsSendOrgCode();
		String btype = Constants.getSmsSendType();
		String pwd = Constants.getSmsSendPwd();
		//查询task_obj列表和短信内容
		String sql = "select t1.OBJ_ID,t1.TEL_NUM1,t2.SMS_CONTENT from " + Constants.getBusiName()+ ".cc_task_obj t1 left join " + Constants.getBusiName() + ".yj_task_info t2 on t1.TASK_ID = t2.TASK_ID where t1.TASK_ID = ? and t1.IS_SEND_SMS = 0 order by t1.OBJ_ID";
		Object[] param = new Object[]{taskId};
		// 更新任务状态
		String taskSql = "update "+Constants.getBusiName()+".CC_TASK set TASK_STATE=?,STATE_TIME=?,END_DATE=? where TASK_ID = ?";
		try {
//			int total = query.queryForInt("select count(1) from " + Constants.getBusiName() + ".cc_task_obj where TASK_ID = ?" , param);
			// 总页数
//			Integer totalPage = getBigDecimalDivideRoundUp(total,1000);
			int pageIndex = 1;
			//isRuning 是任务执行完、暂停、删除的标志，runState是启动war包，或者卸载时的标志。
			while (isRuning && InitListener.runState) {
				
				List<JSONObject> taskObjs = query.queryForList(sql, param,  pageIndex, 1000, new JSONMapperImpl());//每次取1000条记录
				logger.info("任务列表数量：" + taskObjs.size());
				//没有任务后结束
				if(taskObjs==null||taskObjs.size()==0){
					stopTask();
					break;
				}
				for (int i = 0; i < taskObjs.size(); i++) {
					JSONObject cust = taskObjs.get(i);
					String objId = cust.getString("OBJ_ID");
					String mobile = cust.getString("TEL_NUM1");
					String smsContent = cust.getString("SMS_CONTENT");
					// 发送时间
					String sendTime = EasyDate.getCurrentDateString();
					// 发送短信
					String result = SmsUtil.sendSmsData(orgcode, btype, pwd, mobile, smsContent);
					logger.info("发送短信结果：" + result);
					String resultState = "";
					String resultDesc = "";
					if(StringUtils.isNotBlank(result) && result.indexOf("|") > -1) {
						resultState = result.substring(0, result.indexOf("|"));
				        resultDesc = result.substring(result.indexOf("|") + 1);
						if("1".equals(resultState)) {
							// 更新 task_obj 任务状态
							query.execute("update "+Constants.getBusiName()+".cc_task_obj set TASK_STATE = '9',IS_SEND_SMS = '1' where OBJ_ID = ?", new Object[] { objId });		
						}
					}
					try {
						// 短信记录入库
						addSMSLog(taskId,sendTime,objId,resultState,resultDesc,smsContent,mobile);
					} catch (Exception e) {
						logger.error("发送短信结果："+result+"，入库异常:"+e.getMessage(),e);
					}
				}
				pageIndex ++;
			}
			//没有可执行的数据，更新任务状态为已完成。
			Thread.sleep(5*1000);
			//更新任务状态为完成
			query.executeUpdate(taskSql,new Object[] {TaskConstants.TASK_STATE_008,System.currentTimeMillis(),DateUtil.getCurrentDateStr("yyyy-MM-dd"),taskId});
			// 完成任务，将任务从内存中移除
			SmsTaskCommon.finishTask(taskId);
			logger.info(">>>>>>>>smsTask Thread finish:taskId="+taskId);
		} catch (Exception e) {
			logger.error("发送短信线程异常:"+e.getMessage(),e);
			stopTask();
		}
	}
	
	
	public void stopTask(){
		isRuning = false;
	}
	
	public boolean getIsRuning(){
		return isRuning;
	}

	public static boolean isIsRuning() {
		return isRuning;
	}

	public static void setIsRuning(boolean isRuning) {
		SmsTaskThread.isRuning = isRuning;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String toString(){
		return JSONObject.toJSONString(this);
	}
	
	public String getTimingSend() {
		return timingSend;
	}
	public void setTimingSend(String timingSend) {
		this.timingSend = timingSend;
	}
	/**
	 * 添加发送结果日志
	 * @param taskId
	 * @param operDesc 操作内容
	 */
	public void addSMSLog(String taskId,String sendTime,String objId,String result,String resultDesc,String smsContent,String mobile){
		try {
			EasyRecord record=new EasyRecord(Constants.getBusiName()+".YJ_SMS_RECORD", "ID");
			record.setPrimaryValues(RandomKit.randomStr());
			//任务id 
			record.set("TASK_ID",taskId);
			//对象id
			record.set("OBJ_ID",objId);
			//发送号码
			record.set("MOBILE",mobile);
			//发送结果
			record.set("SMS_RESULT",result);
			//描述
			record.set("RESULT_DESC",resultDesc);
			//发送时间
			record.set("SEND_TIME",sendTime);
			//创建时间
			record.set("CREATE_DATE",EasyDate.getCurrentDateString());
			//发送内容
			record.set("CONTENT",smsContent);
			query.save(record);
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
	
	public  Integer getBigDecimalDivideRoundUp(Integer number1, Integer number2) {
        BigDecimal res = BigDecimal.ZERO;
        if (Objects.nonNull(number1) && Objects.nonNull(number2)) {
            res = new BigDecimal(number1).divide(new BigDecimal(number2), BigDecimal.ROUND_UP);
        }
        return res.intValue();
    }
}
