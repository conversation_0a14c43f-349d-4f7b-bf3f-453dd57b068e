package com.yunqu.yc.yimanage.msg;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.yimanage.msg.impl.EarthquakeInfoMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.InformantInfoMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.CallOutGetMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.SMSMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.TaskMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.YjRobotMessageImpl;
import com.yunqu.yc.yimanage.msg.impl.YjTaskAuditMessageImpl;

/**
 * 消息处理类，后续涉及接口方法存放
 *
 */
public abstract class Message {

	private static Map<String, Message> messages = new HashMap<String, Message>();

	static {
		messages.put("SMSMessage", new SMSMessageImpl()); //短信相关方法
		messages.put("YjRobotMessageCommand", new YjRobotMessageImpl()); //机器人接口
		messages.put("CallOutGetMessageCommand", new CallOutGetMessageImpl()); //基本查询
		messages.put("InformantInfoMessage", new InformantInfoMessageImpl()); //获取信息员相关方法
		messages.put("TaskAuditMessageCommand", new YjTaskAuditMessageImpl()); //外呼审核相关方法
		messages.put("TaskMessage", new TaskMessageImpl()); //外呼任务相关方法
		messages.put("EarthquakeInfoMessage", new EarthquakeInfoMessageImpl()); //外呼任务相关方法
	}

	/**
	 * 消息处理具体类
	 * @param messageId
	 * @return
	 */
	public static Message getMessage(String messageId) {
		return messages.get(messageId);
	}

	/**
	 * 处理消息请求
	 * @param request
	 * @return
	 */
	public abstract JSONObject proxy(HttpServletRequest request);

}
