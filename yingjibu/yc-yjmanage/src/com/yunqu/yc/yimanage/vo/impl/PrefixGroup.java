package com.yunqu.yc.yimanage.vo.impl;

import java.sql.SQLException;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.yimanage.model.UserModel;
import com.yunqu.yc.yimanage.vo.BaseVo;

/**
 * 外显号码组
 * <AUTHOR>
 *
 */
public class PrefixGroup extends BaseVo{

	public PrefixGroup(UserModel user, String centerId) {
		super(user, centerId);
	}

	/**
	 * 新增外显组
	 * @return
	 */
	public String add(){
		try {
			EasyRecord record=new EasyRecord("CC_PREFIX_GROUP", "PREFIX_GROUP_ID");
			record.setPrimaryValues(RandomKit.randomStr());
			record.set("PREFIX_GROUP_NAME", "外显组");
			record.set("CREATE_TIME", this.getCurrentTime());
			record.set("CREATOR",this.getUserId());
			record.set("ENT_ID", centerId);
			this.getQuery().save(record);
			return record.getString("PREFIX_GROUP_ID");
		} catch (SQLException e) {
			this.getLogger().error("添加失败，原因："+e.getMessage(),e);
			return FAIL_CODE;
		}
	}
	
	/**
	 * 获取外显组Id
	 * @return
	 */
	public String getGroupId(){
		try {
			String groupId = this.getQuery().queryForString("select PREFIX_GROUP_ID from CC_PREFIX_GROUP where ENT_ID = ?", new Object[]{centerId});
			if(StringUtils.isBlank(groupId)){
				return add();
			}
			return groupId;
		} catch (SQLException e) {
			this.getLogger().error("添加失败，原因："+e.getMessage(),e);
			return FAIL_CODE;
		}
	}
	
	/**
	 * 删除
	 */
	public void delete(){
		try {
			this.getQuery().execute("delete from CC_PREFIX_GROUP where ENT_ID = ?", new Object[]{centerId});
			this.getQuery().execute("delete from CC_PREFIX_GROUP_PREFIX where ENT_ID = ?", new Object[]{centerId});
		} catch (SQLException e) {
			this.getLogger().error("删除失败，原因："+e.getMessage(),e);
		}
	}
	
	/**
	 * 添加外显号码
	 * @param phone
	 * @return
	 */
	public String addPrefix(String phone){
		String groupId = this.getGroupId();
		try {

			EasyRecord easyRecord=new EasyRecord("CC_PREFIX",new String[]{"ENT_ID","PREFIX_NUM"});
			easyRecord.set("PREFIX_STATE", 0);
			easyRecord.set("PREFIX_TYPE", 1);
			easyRecord.set("CREATOR", this.getUserId());
			easyRecord.set("CREATE_TIME", this.getCurrentTime());
			easyRecord.setPrimaryValues(centerId,phone);
			this.getQuery().save(easyRecord);
			
			easyRecord=new EasyRecord("CC_PREFIX_GROUP_PREFIX",new String[]{"ENT_ID","PREFIX_GROUP_ID","PREFIX_NUM"});
			easyRecord.set("CREATOR", this.getUserId());
			easyRecord.set("CREATE_TIME", this.getCurrentTime());
			easyRecord.setPrimaryValues(centerId,groupId,phone);
			this.getQuery().save(easyRecord);
		} catch (SQLException e) {
			this.getLogger().error("添加失败，原因："+e.getMessage(),e);
			return FAIL_CODE;
		}
		return SUCC_CODE;
	}
}
