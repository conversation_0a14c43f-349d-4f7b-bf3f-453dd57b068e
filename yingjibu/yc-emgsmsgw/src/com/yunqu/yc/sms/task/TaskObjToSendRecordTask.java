package com.yunqu.yc.sms.task;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.sms.base.CommonLogger;
import com.yunqu.yc.sms.base.Constants;
import com.yunqu.yc.sms.base.QueryFactory;
import com.yunqu.yc.sms.monitor.DBMonitor;
import com.yunqu.yc.sms.monitor.RedisMonitor;
import com.yunqu.yc.sms.service.ChannelService;
import com.yunqu.yc.sms.service.ForkJoinBaseTask;
import com.yunqu.yc.sms.thread.poolManager.TaskObjToSmsRecordThreadPool;
import com.yunqu.yc.sms.utils.DateUtil;
import com.yunqu.yc.sms.utils.RedissonUtil;
import com.yunqu.yc.sms.utils.SecretDataUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;

/**
 * 每5秒执行一次 扫描jy_sms_task表 查询启动中任务下未处理完的短信名单写到网关下发记录表
 */
public class TaskObjToSendRecordTask implements Job {

    private final Logger logger = CommonLogger.getLogger("TaskObjToSendRecord");
    // 任务标识
    public static String taskKey = "JY_OBJ_TO_SMS_RECORD_TASK";

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        // 判断redis db是否正常
        if (RedisMonitor.isOk() && DBMonitor.isOk()) {
            String serialId = RandomKit.smsAuthCode(6);
            List<JSONObject> smsTaskList = null;
            EasyQuery query = QueryFactory.getReadQuery();
            String schemaName = Constants.getBusiTableName();
            try {
                String tag = RedissonUtil.get(taskKey);
                if (StringUtils.isNotBlank(tag)) {
                    logger.info("[" + serialId + "]上次任务未执行完毕，请稍后重试！");
                    return;
                }
                RedissonUtil.setEx(taskKey, "Y", 10 * 60);
                // 查询所有启动中、外呼的任务
                smsTaskList = query.queryForList("SELECT T1.TASK_ID,T1.TASK_NAME,T1.SMS_CHANNEL_ID,T1.SMS_TEMPLATE_ID,T1.CREATOR,T1.TASK_SOURCE,T2.IS_TIMING,T2.TIMING_TIME FROM " + schemaName + ".JY_SMS_TASK T1 LEFT JOIN " + schemaName + ".JY_TASK_FIELD T2 ON T1.TASK_ID = T2.TASK_ID WHERE T1.TASK_STATE = ? AND T1.OBJ_COUNT > T1.OBJ_USE_COUNT", new Object[]{5}, new JSONMapperImpl());
                if (smsTaskList == null || smsTaskList.isEmpty()) {
                    logger.info("[" + serialId + "]没有启动中的任务");
                    RedissonUtil.del(taskKey);
                    return;
                }
                logger.info("[" + serialId + "][SendSmsTask.execute] >>> excuete stat report data start");
                EasyCalendar cal = EasyCalendar.newInstance();
                String monthId = cal.getFullMonth();
                int dateId = cal.getDateInt();
                int sendCount = query.queryForInt("SELECT COUNT(*) FROM " + schemaName + ".JY_SMS_SEND_RECORD WHERE DATE_ID = ? AND MONTH_ID = ? AND SEND_STATUS = '1'", dateId, monthId);
                if (sendCount > 5000) {
                    logger.info("[" + serialId + "]待下发数量超过5000，暂缓取数据");
                    RedissonUtil.del(taskKey);
                    return;
                }
            } catch (Exception e) {
                logger.error("[" + serialId + "][SendSmsTask.execute] >>> excuete stat report data error:" + e.getMessage(), e);
                RedissonUtil.del(taskKey);
                return;
            }
            ForkJoinPool forkJoinPool = TaskObjToSmsRecordThreadPool.getForkJoinPoolInstance();
            for (JSONObject json : smsTaskList) {
                String taskId = json.getString("TASK_ID");
                String taskName = json.getString("TASK_NAME");
                // Y 立即发送  N 定时
                String isTiming = json.getString("IS_TIMING");
                // 发送时间
                String timingTime = json.getString("TIMING_TIME");
                if ("N".equals(isTiming)) {
                    // 判断当前时间是否大于发送时间 btime>etime 返回1; btime<etime 返回-1; 相等则返0
                    if (DateUtil.compareDate(DateUtil.getCurrentDateStr(), timingTime, "yyyy-MM-dd HH:mm:ss") == -1) {
                        logger.info("[" + serialId + "]" + taskName + "[" + taskId + "]未到发送时间，跳过");
                        continue;
                    }
                }
                try {
                    // 查询任务未提交的名单
                    List<JSONObject> objList = query.queryForList("SELECT OBJ_ID,TEL_NUM1,CUST_NAME,F1 FROM " + schemaName + ".JY_SMS_TASK_OBJ WHERE TASK_ID= ? AND TASK_STATE = ?", new Object[]{taskId, 0}, 0, Constants.getSmsMix(), new JSONMapperImpl());
                    if (objList == null || objList.isEmpty()) {
                        // logger.info(taskName + "[" + taskId + "]的名单查询为空");
                        continue;
                    }
                    // 查询出名单后，增加调用密评解密
                    objList = SecretDataUtil.decrypt(objList);
                    logger.info("[" + serialId + "]" + taskName + "[" + taskId + "]名单数量为:" + objList.size());
                    List<JSONObject> receiverArr = new ArrayList<>();
                    List<Object[]> objIds = new ArrayList<>();
                    for (JSONObject obj : objList) {
                        JSONObject param = new JSONObject();
                        param.put("receiver", obj.getString("TEL_NUM1"));//手机号
                        param.put("content", obj.getString("F1"));//内容
                        param.put("taskObjId", obj.getString("OBJ_ID"));//id
                        param.put("custName", obj.getString("CUST_NAME"));//姓名
                        receiverArr.add(param);
                        objIds.add(new Object[]{DateUtil.getCurrentDateStr(), obj.getString("OBJ_ID")});
                    }
                    long start = System.currentTimeMillis();
                    String updateSql = "UPDATE " + Constants.getBusiTableName() + ".JY_SMS_TASK_OBJ SET TASK_STATE = 1 ,SUBMIT_TIME = ? WHERE OBJ_ID = ?";
                    ForkJoinBaseTask task = new ForkJoinBaseTask(objIds, 50, updateSql, QueryFactory.getWriteQuery());
                    // 提交任务
                    ForkJoinTask<Integer> submit = forkJoinPool.submit(task);
                    int sum = submit.get();// 获得结果
                    long end = System.currentTimeMillis();
                    logger.info("[" + serialId + "]" + taskName + "[" + taskId + "] 查询数量 = " + objList.size() + " 更新数量 = " + sum + " 时间:" + (end - start));
                    toSmsRecord(json, receiverArr);
                } catch (Exception e) {
                    logger.error("[" + serialId + "]修改JY_SMS_TASK_OBJ状态或保存下发表异常" + e.getMessage(), e);
                }
            }
            logger.info("[" + serialId + "][SendSmsTask.execute] >>> excuete stat report data over");
            // 任务执行完毕删除任务标识
            RedissonUtil.del(taskKey);
        }
    }

    public void toSmsRecord(JSONObject json, List<JSONObject> receiverArr) {
        String chanelId = json.getString("SMS_CHANNEL_ID");
        String tempId = json.getString("SMS_TEMPLATE_ID");
        String taskId = json.getString("TASK_ID");
        String taskName = json.getString("TASK_NAME");
        String createName = json.getString("CREATOR");
        // 来源 4 外部  其他 内部
        String taskSource = json.getString("TASK_SOURCE");
        JSONObject serObj = new JSONObject();
        // 固定
        serObj.put("command", "SENDMESSAGE");
        // 流水id
        serObj.put("serialId", RandomKit.uuid());
        // 模版id
        serObj.put("tempId", tempId);
        // 任务id
        serObj.put("taskId", taskId);
        // 任务名称
        serObj.put("taskName", taskName);
        // 创建时间
        serObj.put("createName", createName);
        // 渠道id
        serObj.put("chanelId", chanelId);
        // 来源 0 内部 1外部
        serObj.put("sourceType", "4".equals(taskSource) ? "1" : "0");
        serObj.put("receivers", receiverArr);
        ChannelService.sendMessage(serObj);
    }
}
