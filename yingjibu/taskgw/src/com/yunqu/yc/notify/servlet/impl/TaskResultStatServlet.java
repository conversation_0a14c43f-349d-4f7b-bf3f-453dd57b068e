package com.yunqu.yc.notify.servlet.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.notify.base.EntContext;
import com.yunqu.yc.notify.base.QueryFactory;
import com.yunqu.yc.notify.model.RequestData;
import com.yunqu.yc.notify.servlet.AbstractServlet;
import com.yunqu.yc.notify.util.ParamsUtlis;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import javax.servlet.annotation.WebServlet;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 智能外呼平台_任务呼叫结果回显
 */
@WebServlet("/task/query/resultStat")
public class TaskResultStatServlet extends AbstractServlet {

    @Override
    protected String getName() {
        return "TaskResultStat";
    }

    @Override
    protected String proxy(RequestData requestData) throws Exception {
        JSONObject params = requestData.getParams();
        if (params == null) {
            params = new JSONObject();
        }
        String entId = requestData.getEntId();
        EasySQL sql = new EasySQL("select TASK_ID,TASK_NAME,TASK_STATE,CREATOR,CREATE_TIME,START_DATE,END_DATE,OBJ_COUNT,OBJ_USE_COUNT,CALL_COUNT,CALL_SUCCESS_COUNT,SALE_SUCCESS_COUNT "
        		+ " from "+EntContext.getContext(entId).getTableName("cc_task")+" where 1=1 ");
        sql.appendIn(params.getString("taskId").split(",")," and TASK_ID ");
        sql.append("  order by CREATE_TIME");
        
        List<JSONObject> list = QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        //转小写
        List<JSONObject> jsonObjects = new ArrayList<>();
        for (JSONObject object : list) {
            Set<String> keySet = object.keySet();
            JSONObject jsonObject = new JSONObject();
            for (String s : keySet) {
                jsonObject.put(ParamsUtlis.formatKey(s), object.get(s));
            }
            jsonObjects.add(jsonObject);
        }
        JSONObject result = getSuccResult("查询成功");
        result.put("list", jsonObjects);
        return result.toJSONString();
    }
    
}
