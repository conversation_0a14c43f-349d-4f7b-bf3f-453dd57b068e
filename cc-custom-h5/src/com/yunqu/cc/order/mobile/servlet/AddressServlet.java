package com.yunqu.cc.order.mobile.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.order.mobile.base.AppBaseServlet;
import com.yunqu.cc.order.mobile.base.QueryFactory;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;

import javax.servlet.annotation.WebServlet;
import java.util.ArrayList;
import java.util.List;

@WebServlet("/servlet/address/*")
public class AddressServlet extends AppBaseServlet {

    /**
     * 地区级联控件数据获取
     * @return
     */
    public List<JSONObject> actionForAddress() {
        String code = this.getPara("code");
        String level = this.getPara("level");
        EasyQuery query = QueryFactory.getReadQuery();
        try {
            if(StringUtils.isBlank(code)) {
                return toLowerCaseList(query.queryForList("SELECT PROVINCE_CODE \"value\",PROVINCE_NAME \"label\" FROM CC_PROVINCE ORDER BY PROVINCE_CODE", new Object[]{}, new JSONMapperImpl()));
            } else {
                if(code.indexOf(",") == -1) {
                    if("1".equals(level)) {
                        return toLowerCaseList(query.queryForList("SELECT ADMINISTRATIVE_CODE \"value\",AREA_NAME \"label\" FROM CC_AREA WHERE PROVINCE_CODE = ? ORDER BY ADMINISTRATIVE_CODE", new Object[]{code}, new JSONMapperImpl()));
                    } else if("2".equals(level)){
                        return toLowerCaseList(query.queryForList("SELECT COUNTY_CODE \"value\",COUNTY_NAME \"label\",'true' as \"leaf\" FROM CC_COUNTY WHERE ADMINISTRATIVE_CODE = ? ORDER BY COUNTY_CODE", new Object[]{code}, new JSONMapperImpl()));
                    }
                } else {
                    // 动态回显
                    String [] codeArr = code.split(",");
                    if("1".equals(level)) {
                        List<JSONObject> areaList = toLowerCaseList(query.queryForList("SELECT ADMINISTRATIVE_CODE \"value\",AREA_NAME \"label\" FROM CC_AREA WHERE PROVINCE_CODE = ?", new Object[]{codeArr[0]}, new JSONMapperImpl()));
                        if(areaList != null && areaList.size() > 0 && codeArr.length > 1) {
                            for (JSONObject areaJson : areaList) {
                                if(areaJson.getString("value").equals(codeArr[1])) {
                                    // 加载区域节点
                                    areaJson.put("children", toLowerCaseList(query.queryForList("SELECT COUNTY_CODE \"value\",COUNTY_NAME \"label\",'true' as \"leaf\" FROM CC_COUNTY WHERE ADMINISTRATIVE_CODE = ?", new Object[]{codeArr[1]}, new JSONMapperImpl())));
                                }
                            }
                        }
                        return areaList;
                    } else if ("2".equals(level)) {
                        return toLowerCaseList(query.queryForList("SELECT COUNTY_CODE \"value\",COUNTY_NAME \"label\",'true' as \"leaf\" FROM CC_COUNTY WHERE ADMINISTRATIVE_CODE = ?", new Object[]{codeArr[1]}, new JSONMapperImpl()));
                    }else {
                        // 加载省级节点
                        List<JSONObject> provinceList = toLowerCaseList(query.queryForList("SELECT PROVINCE_CODE \"value\",PROVINCE_NAME \"label\" FROM CC_PROVINCE", new Object[]{}, new JSONMapperImpl()));
                        for (JSONObject provinceJson : provinceList) {
                            if(provinceJson.getString("value").equals(codeArr[0])) {
                                // 加载指定省份下的市级节点
                                List<JSONObject> areaList = toLowerCaseList(query.queryForList("SELECT ADMINISTRATIVE_CODE \"value\",AREA_NAME \"label\" FROM CC_AREA WHERE PROVINCE_CODE = ?", new Object[]{codeArr[0]}, new JSONMapperImpl()));
                                if(areaList != null && areaList.size() > 0 && codeArr.length > 1) {
                                    for (JSONObject areaJson : areaList) {
                                        if(areaJson.getString("value").equals(codeArr[1])) {
                                            // 加载区域节点
                                            areaJson.put("children", toLowerCaseList(query.queryForList("SELECT COUNTY_CODE \"value\",COUNTY_NAME \"label\",'true' as \"leaf\" FROM CC_COUNTY WHERE ADMINISTRATIVE_CODE = ?", new Object[]{codeArr[1]}, new JSONMapperImpl())));
                                        }
                                    }
                                    provinceJson.put("children", areaList);
                                }
                            }
                        }
                        return provinceList;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public static List<JSONObject> toLowerCaseList(List<JSONObject> list) {
        if(CommonUtil.listIsNotNull(list)) {
            List<JSONObject> newList = new ArrayList<JSONObject>();
            for (JSONObject json : list) {
                JSONObject obj = new JSONObject();
                for (String key : json.keySet()) {
                    obj.put(key.toLowerCase(), json.getString(key));
                }
                newList.add(obj);
            }
            return newList;
        }
        return list;
    }

}
