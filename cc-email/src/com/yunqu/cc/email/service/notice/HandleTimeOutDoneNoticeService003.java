package com.yunqu.cc.email.service.notice;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.NoticeMessage;
import com.yq.busi.common.util.CacheUtil;

/**
 * 
 */
public class HandleTimeOutDoneNoticeService003  extends CcEmailNoticeService{

	public HandleTimeOutDoneNoticeService003(){
		super.init();
		this.code = NOTICE_CONDITION_003;
		this.name = "首次回复超时提醒（已分配）";
	}
	
	/**
	 * 获取该类通知的配置信息
	 * @return
	 */
	public  JSONObject getNoticeInfo(){
		// 置忙超时提醒
		JSONObject condition1 = new JSONObject();
		condition1.put("code", code);
		condition1.put("name", name);
		
		JSONObject exJson1 = new JSONObject();
		exJson1.put("tips", "首次回复超时提醒（已分配）需要通知的对象。");
		exJson1.put("noticeTitle", "首次回复超时提醒（已分配）通知");
		exJson1.put("noticeContent", "您共有#unHandleCountDone#封邮件超时未回复，请尽快处理！");
		condition1.put("exJson", exJson1);
		
		//业务对象
		JSONArray array1 = new JSONArray();
		array1.add(this.getNoticeObject003());
		condition1.put("objects", array1);
		
		return condition1;
	}

	/**
	 * 处理通知
	 * @param entId
	 * @param busiOrderId
	 * @param user
	 * @param json  业务参数
	 * @return
	 */
	public  JSONObject notice(NoticeMessage message){
		return super.notice(message);
	}

}
