package com.yunqu.cc.email.engine;

import com.yunqu.cc.email.base.Constants;
import com.yunqu.cc.email.engine.impl.EmailEngineEwsService;
import com.yunqu.cc.email.engine.impl.EmailEngineSmtpService;

/**
 * 邮件引擎处理控制类，单例
 */
public class EmailEngine {
	
	private static EmailEngine instance = new EmailEngine();
	
	private EmailEngineService emailEngineSmtpService = new EmailEngineSmtpService();
	private EmailEngineService emailEngineEwsService = new EmailEngineEwsService();

	private EmailEngine(){
		
	}
	
	public static EmailEngine getEngine(){
		return instance;
	}
	
	/**
	 * 根据邮件协议获取邮件处理类
	 * @param protocol
	 * @return
	 */
	public EmailEngineService getService(String protocol){
		if(Constants.EMAIL_PROTOCOL_EWS.equalsIgnoreCase(protocol)){
			return emailEngineEwsService;
		}
		return emailEngineSmtpService;
	}
}
