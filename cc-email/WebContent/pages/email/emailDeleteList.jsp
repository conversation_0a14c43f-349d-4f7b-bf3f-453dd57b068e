<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<%@ page import="com.yq.busi.common.util.DateUtil" %>
<EasyTag:override name="head">
	<title i18n-content="已删除"></title>
	<style type="text/css">
.labelListDiv {
	height: 32px;
	line-height: 32px;
	padding: 0px 10px;
	font-size: 13px;
}

.labelListDiv.active {
	background-color: #f8f8f8
}

.labelListDiv a {
	text-decoration: none;
	color: #666;
}

.ibox-content table tr td label+label {
	margin-left: 10px
}

td[data-field="TYPE"] .layui-table-cell{overflow: visible;}

.ibox-content a {
    color: #20a0ff;
    text-decoration: none;
}
</style>
<style>
	/* .layui-layer-content{
		overflow-x: hidden !important;
	} */

	.container-fluid {
		padding-right: 0px;
		padding-left: 0px;
		margin-right: 0px;
		margin-left: 0px;
		padding: 0px;
	}
	.layui-table-click{
				/* 1. table默认是有背景色的你要保证优先加载你的,所以使用important */
				background-color: #c9e2f9 !important;
				color: #FFF;
	}
	.btn {
    margin-right: 0px;
}
.layui-table{
	margin-top: -10px;
	}

 .ibox-content{
		margin-top: 0px;
		padding-top:10px;
		padding-bottom: 0px;
	} 
.ibox{
	padding:1px 0px 0px 0px;
	margin-top: 0px;
}

</style>
</EasyTag:override>
<EasyTag:override name="content">
	<input type="hidden" id="sendTimeBeginSet">
	<input type="hidden" id="sendTimeEndSet">
	<form name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix" id="divId">
						 <div class="form-group">
	             		       <h5 i18n-content="已删除"></h5>
	             		      	<div class="input-group pull-right">
							<button type="button" class="btn btn-sm btn-danger btn-outline" onclick="emailReceiveTemp.deleteEmail()" i18n-content="批量彻底删除"></button>
		   				</div>
	             	<div class="input-group pull-right ">
			    			<button type="button" class="btn btn-sm btn-success btn-outline" onclick="emailReceiveTemp.restore()" i18n-content="批量恢复"></button>
		 	 		</div>
						  </div>
					<hr style="margin: 5px -15px"> 
					<div class="form-group" >
						  
						  <div class="input-group">
			 			<span class="input-group-addon" i18n-content="发送时间"></span>
			        	<input type="text" class="form-control input-sm" id="sendDateBegin" name="sendDateBegin"  data-mars="common.getLastMonthTime" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
	                	<span class="input-group-addon">~</span>
	                	<input type="text" class="form-control input-sm" id="sendDateEnd" name="sendDateEnd"  data-mars="common.getTodayTime" data-mars-top="true" autocomplete="off" style="height:30px;width:142px" > 
	                	<!-- <span class="input-group-addon">-</span>
	                	<select class="form-control input-sm" name="dateRange" onchange="emailReceiveTemp.onCasecade($(this))">
	                   		<option value="" i18n-content="请选择"></option>
							<option value="today" i18n-content="今天"></option>
							<option value="yesterday" i18n-content="昨天"></option>
							<option value="thisWeek" i18n-content="本周"></option>
							<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
							<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
						</select> -->
                	</div> 
                	
					<div class="input-group ">
						<span class="input-group-addon" i18n-content="邮件标题"></span>	
						<input type="text" name="title" class="form-control input-sm" style="width:120px">
					</div>
					
					<div class="input-group ">
						<span class="input-group-addon" i18n-content="发件人" style="width: 79px;"></span>	
						<input type="text" name="emailFrom" class="form-control input-sm" style="width:122px">
					</div>
					<div class="input-group ">
						<span class="input-group-addon" i18n-content="收件人" style="width: 79px;"></span>
						<input type="text" name="emailTo" class="form-control input-sm" style="width:122px">
					</div>
				
					<!-- <div class="input-group ">
				      	<span class="input-group-addon" i18n-content="收件账号"></span>	
					  	<select class="form-control input-sm" name="emailToAcc" data-mars="common.geteEmailFromDict" style="width:120px">
							<option value="" i18n-content="请选择"></option>
						</select>
				   	</div> -->
		         	
				    <div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="emailReceiveTemp.searchData('1')"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询" ></span></button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="emailReceiveTemp.reset()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置" ></span></button>
					</div>
					<!-- <div class="input-group">
						<button type="button" class="btn btn-sm btn-default" onclick="emailReceiveTemp.moreQuery()"> <span i18n-content="高级查询" ></span></button>
					</div> -->
					
					 
					
			   	</div>
			   	<div class="form-group" id="more-query-content" style="display: none;">
				</div>
         	</div>  
	        <div class="ibox-content">
	        	<table id="main" lay-filter="tableTbBas"></table>
	     	</div> 
      	</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/cc-email/static/js/time.js"></script>
	
	<script type="text/javascript">
	jQuery.namespace("emailReceiveTemp");
	var par_title;
	
	requreLib.setplugs("layui",function(){
      	$("#searchForm").render({success:function(){
      		//时间控件 加载 laydate模块
	   		layui.use('laydate', function(){
	   			var laydate = layui.laydate;
	   			laydate.render({elem:'#sendDateBegin',type:'datetime',lang:getDateLang()});
	   			laydate.render({elem:'#sendDateEnd',type:'datetime',lang:getDateLang()});
	   		}) 
	       	$("#sendTimeBeginSet").val($("#sendDateBegin").val());
		    $("#sendTimeEndSet").val($("#sendDateEnd").val());
	   		emailReceiveTemp.loadData();
      	}});
      });
	
	// 初始化加载数据
	emailReceiveTemp.loadData = function(){
		$("#searchForm").initTableEx({
			 mars:'emailDelete.list',
			 id:'main',
			 limit:'15',
			 height: 'full-128',
			 limits:[15,25,50,100,200],
			 cols: [
				[
				  {width:60,field:'ID', title: getI18nValue('选择'), type: 'checkbox',fixed:'left'}
				 ,{width:60,align:'center',title: getI18nValue('序号') ,type:'numbers',fixed:'left'}
				 ,{minWidth:180,field:'TITLE', title: getI18nValue('邮件标题'), align: 'left',fixed:'left',sort: true, templet:function(row){
		        	 var temp='';
		        	 temp = temp + '<a href="javascript:void(0)" title=\''+row.TITLE+'\' onclick="emailReceiveTemp.sel(\''+row.ID+'\',\''+row.STATUS+'\')">'+ row.TITLE +'</a>';
		        	 return temp;
		         }}
		         ,{width:170,field:'EMAIL_FROM', title: getI18nValue('发件人'), align: 'left',
		        	 templet:function(row){
		        		 return getPhone(row.EMAIL_FROM,row.EMAIL_FROM,"${ctxPath}",'98');
	             }}
		         ,{width:170,field:'EMAIL_TO_ALL', title: getI18nValue('抄送人'), align: 'left',
		        	 templet:function(row){
		        		 return getPhone(row.EMAIL_TO_ALL,row.EMAIL_TO_ALL,"${ctxPath}",'98');
	             }}
		         ,{width:170, field:'SEND_TIME', title: getI18nValue('发送时间'), align: 'center',sort:true}
		         ,{width:170, field:'RECEIVE_TIME', title: getI18nValue('接收时间'), align: 'center',sort:true}
		         ,{width:150, field:'EMAIL_TO', title: getI18nValue('收件账号'), align: 'left'}
		         ,{width:100,field:'DIR_NAME', title: getI18nValue('文件夹'), align: 'center'}
		         ,{width:220,align:'center', title: getI18nValue('操作'),fixed:'right',templet:function(row){
	        		 var temp=' <span href="javascript:void(0)" class="layui-btn layui-btn-xs " onclick="emailReceiveTemp.restoreOne(\''+row.ID+'\')"> '+getI18nValue('恢复')+' </span>'
	        		 temp=temp+	' <span href="javascript:void(0)" class="layui-btn layui-btn-danger layui-btn-xs" onclick="emailReceiveTemp.deleteEmailOne(\''+row.ID+'\')"> '+getI18nValue('彻底删除')+' </span>'
					return temp;
	           	  }}
		         ]
			]
			
		});
	}
	
	// 查询
	emailReceiveTemp.searchData = function(flag) {
		if(flag=='1'){
			$("#searchForm").queryData({id:'main', page:{curr: 1}});
		}else{
			$("#searchForm").queryData({id:'main'});
		}
	}
	
	//高级查询
	emailReceiveTemp.moreQuery = function(){
		$('#more-query-content').toggle();
	}
	
	// 设置命名空间后获取当前时间
	function setCurrTime() {
		var currTime = getCurrDate();
		$("#startDate").val(currTime+" 00:00:00");
		$("#endDate").val(currTime+" 23:59:59");
	}
	
	//重置
	emailReceiveTemp.reset=function(){
		$("#divId select").val("");
		$("#divId input:not(input[id=startDate],input[id=endDate])").val("");
		$("#sendDateEnd").val($("#sendTimeEndSet").val());
		$("#sendDateBegin").val($("#sendTimeBeginSet").val());
	}
	
	//明细
	emailReceiveTemp.sel = function(id,status){
		popup.layerShow({type:2,title:getI18nValue('详情'),zIndex:5,area:['800px','100%'],offset : 'r'},"${ctxPath}/pages/email/emailReceivePageDetails2.jsp?id="+id+"&status="+status+"&isDel="+"Y",null);
	}
	 
	 //恢复成正常邮件
	 emailReceiveTemp.restore = function(){
   		 var checkStatus = table.checkStatus('main');
   		 var ids = checkStatus.data;
		 if (ids.length < 1) {
			layer.msg(getI18nValue('请选择需要恢复的邮件！'),{icon:0});
		} else {
			layer.confirm(getI18nValue('确定要恢复成正常邮件吗？'), {
				 title:getI18nValue('信息'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ],offset:'40px'
	        }, function(index) {
	        	var state;
				var arr = new Array();
				for (var i = 0; i < ids.length; i++) {
					arr.push(ids[i].ID);
				}
				var data={};
				data.ids=arr;
				ajax.remoteCall("${ctxPath}/servlet/emailDelete?action=restore",data,function(result) {
					if(result.state == 1){
						layer.msg(getI18nValue(result.msg),{icon: 1});
						emailReceiveTemp.searchData();
					}else{
						layer.alert(getI18nValue(result.msg),{icon: 5});
					}
				});
		    });
		}
	 }
	 
	//恢复成正常邮件（单个）
	 emailReceiveTemp.restoreOne = function(id){
			layer.confirm(getI18nValue('确定要恢复成正常邮件吗？'), {
				 title:getI18nValue('信息'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ],offset:'40px'
	        }, function(index) {
	        	var state;
				var arr = new Array();
				arr.push(id);
				var data={};
				data.ids=arr;
				ajax.remoteCall("${ctxPath}/servlet/emailDelete?action=restore",data,function(result) {
					if(result.state == 1){
						layer.msg(getI18nValue(result.msg),{icon: 1});
						emailReceiveTemp.searchData();
					}else{
						layer.alert(getI18nValue(result.msg),{icon: 5});
					}
				});
		    });
	 }

	 //彻底删除邮件
	 emailReceiveTemp.deleteEmail = function(){
   		 var checkStatus = table.checkStatus('main');
   		 var ids = checkStatus.data;
		 if (ids.length < 1) {
			layer.msg(getI18nValue('请选择需要彻底删除的邮件！'),{icon:0});
		} else {
			layer.confirm(getI18nValue('确定要彻底删除邮件吗？(慎重)'), {
				 title:getI18nValue('信息'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ],offset:'40px'
	        }, function(index) {
	        	var state;
				var arr = new Array();
				for (var i = 0; i < ids.length; i++) {
					arr.push(ids[i].ID);
				}
				var data={};
				data.ids=arr;
				ajax.remoteCall("${ctxPath}/servlet/emailDelete?action=delete",data,function(result) {
					if(result.state == 1){
						layer.msg(getI18nValue(result.msg),{icon: 1});
						emailReceiveTemp.searchData();
					}else{
						layer.alert(getI18nValue(result.msg),{icon: 5});
					}
				});
		    });
		}
	 }
	 //彻底删除邮件(单个)
	 emailReceiveTemp.deleteEmailOne = function(id){
			layer.confirm(getI18nValue('确定要彻底删除邮件吗？(慎重)'), {
				 title:getI18nValue('信息'),
	            btn : [ getI18nValue('确定'), getI18nValue('取消') ],offset:'40px'
	        }, function(index) {
	        	var state;
				var arr = new Array();
				arr.push(id);
				var data={};
				data.ids=arr;
				ajax.remoteCall("${ctxPath}/servlet/emailDelete?action=delete",data,function(result) {
					if(result.state == 1){
						layer.msg(getI18nValue(result.msg),{icon: 1});
						emailReceiveTemp.searchData();
					}else{
						layer.alert(getI18nValue(result.msg),{icon: 5});
					}
				});
		    });
	 }
	 
	 // 回复
	 function Reply(ID,EMAIL_FROM,EMAIL_TO,STATUS){
		 popup.layerShow({type:1,title:getI18nValue('发送'),zIndex:5,shadeClose:false,area:['980px','800px'],offset:'20px'},"${ctxPath}/pages/email/emailSendPage.jsp?parentID="+ID+"&EMAIL_FROM="+EMAIL_FROM+"&EMAIL_TO="+EMAIL_TO+"&STATUS="+STATUS,null);
	}
	 
	 // 回收的方法
	 function commonFunc(ID,STATUS,busiId) {
			var data={};
			data.ID=ID;
			data.STATUS=STATUS;
			data.busiId=busiId;
			ajax.remoteCall("${ctxPath}/servlet/emailReceive?action=Reply",data,function(result) {
				if(result.state == 1){
					layer.msg(getI18nValue(result.msg),{icon: 1});
					emailReceiveTemp.searchData();
				}else{
					layer.alert(getI18nValue(result.msg),{icon: 5});
				}
			});
	 }
	 //无需处理
	 function commonFunc2(ID,STATUS,busiId) {
			var data={};
			data.ID=ID;
			data.STATUS=STATUS;
			data.busiId=busiId;
			ajax.remoteCall("${ctxPath}/servlet/emailReceive?action=Reply",data,function(result) {
				if(result.state == 1){
					layer.msg(getI18nValue(result.msg),{icon: 1});
					emailReceiveTemp.searchData();
				}else{
					layer.alert(getI18nValue(result.msg),{icon: 5});
				}
			});
	 }
	 
	 //根据id分配
     function allocateById(id){
		 popup.layerShow({type:1,title:getI18nValue('分派'),shadeClose:false,area:['400px','260px'],offset:'20px'},"${ctxPath}/pages/email/allocate.jsp",{ids:id});		 
	 }
   //设置时间
	 emailReceiveTemp.onCasecade = function(p){
		 var dateRange = p.val();
	     	if(dateRange == "today") {
	     		$("#sendDateBegin").val(getTodayStartTime());
	     		$("#sendDateEnd").val(getTodayEndTime());
	     	}else if(dateRange == "yesterday") {
	  			$("#sendDateBegin").val(getYesterDayStartTime());
	 			$("#sendDateEnd").val(getYesterDayEndTime());
	  		}else if(dateRange == "thisWeek") {
	     		$("#sendDateBegin").val(getThisWeekStartTime());
	     		$("#sendDateEnd").val(getThisWeekEndTime());
	     	}else if(dateRange == "RecentlyOneMonth") {
	     		$("#sendDateBegin").val(getRecentlyOneMonthStartTime());
	     		$("#sendDateEnd").val(getTodayEndTime());
	     	}else if(dateRange == "RecentlyThreeMonth") {
	  			$("#sendDateBegin").val(getRecentlyThreeMonthStartTime());
	 			$("#sendDateEnd").val(getTodayEndTime());
	  		}
	   }
</script>

<script id="waitDiv" type="text/javascript">

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>