<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title></title>



		<link href="/easitline-static/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
		<link href="/easitline-static/lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
		<link href="/easitline-static/css/easitline.ui.css?v=20180129" rel="stylesheet">
		<link href="/easitline-static/lib/check/awesome-bootstrap-checkbox.css" rel="stylesheet">
		<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" />
		<link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css" />




		<link rel="stylesheet" type="text/css" href="../lib/layui/css/layui.css" />
		<!-- <link rel="stylesheet" type="text/css" href="../css/mailServe.css" /> -->
		<link rel="stylesheet" type="text/css" href="./mailServe.css" />
		<style type="text/css">
			html {
				padding: 13px;
				padding-right: 0;
				background-color: #F6F7F9;
			}

			.bkyy {
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			}

			* {
				box-shadow: none !important;
			}

			#test1 {
				background-color: transparent !important;
			}

			.layui-btn-sm {
				padding: 0 8px;
			}
		</style>
	</head>
	<body>
		<!-- 更多搜索 -->
		<!-- <div id="configurePage3" style="display: none;padding: 10px;">
			<form class="layui-form" action="" lay-filter="configs3">
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="收件人"></label>
					<div class="layui-input-block">
						<input type="text" name="getHuman" i18n-placeholder="请输入收件人" autocomplete="off"
							class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="创建时间"></label>
					<div class="layui-input-block">
						<input type="text" name="createTime" class="layui-input" id="test16" >
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="收件账号"></label>
					<div class="layui-input-block">
						<select name="getAcc" id="getAcc">
							<option value=""></option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="处理状态"></label>
					<div class="layui-input-block">
						<select name="state" id="state">
							<option value=""></option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="回执码"></label>
					<div class="layui-input-block">
						<input type="text" name="code" i18n-placeholder="请输入回执码" autocomplete="off" class="layui-input">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="标星"></label>
					<div class="layui-input-block">
						<select name="star" id="star">
							<option value="" i18n-content="请选择"></option>
							<option value="Y" i18n-content="是"></option>
							<option value="N" i18n-content="否"></option>
						</select>
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="客户分组"></label>
					<div class="layui-input-block">
						<select name="group" id="group">
							<option value=""></option>
						</select>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="关键词"></label>
					<div class="layui-input-block">
						<input type="text" name="gjcode" i18n-placeholder="请输入标题/内容发件人/收件人" autocomplete="off"
							class="layui-input">
					</div>
				</div>


				<div class="layui-form-item">
					<div class="layui-input-block" style="text-align: center;margin-left: 0;">
						<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo3"
							i18n-content="搜索"></button>
						<button type="reset" class="layui-btn layui-btn-primary" i18n-content="重置"></button>
					</div>
				</div>
			</form>
		</div>
 -->
		<div id="configureChoose" style="display: none;padding: 10px;">
			<form class="layui-form" action="" lay-filter="configs">
				<div class="layui-form-item">
					<label class="layui-form-label">是否多选</label>
					<div class="layui-input-block">
						<input type="radio" name="Choice" value="0" title="是">
						<input type="radio" name="Choice" value="1" title="否" checked>
						<!-- <button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
							style="float: right;margin-right: 10px;" onclick="multipleChoice()" id="multipleChoice">开启多选</button> -->
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-input-block" style="text-align: center;margin-left: 0;">
						<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo1">保存</button>
						<button type="reset" class="layui-btn layui-btn-primary">重置</button>
					</div>
				</div>
			</form>
		</div>

		<!-- 配置 -->
		<div id="configurePage" style="display: none;padding: 10px;">
			<form class="layui-form" action="" lay-filter="configs">
				<div class="layui-form-item">
					<label class="layui-form-label" style="width:90px" i18n-content="排序方式"></label>
					<div class="layui-input-block">
						<input type="radio" name="sort" value="1" i18n-title="创建时间" checked>
						<input type="radio" name="sort" value="2" i18n-title="回复时间">
						<input type="radio" name="sort" value="3" i18n-title="响应时间">
					</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label" i18n-content="超时高亮显示"></label>
					<div class="layui-input-block">
						<input type="number" name="timeRed" i18n-placeholder="请输入超时高亮显示" autocomplete="off"
							class="layui-input">
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-input-block" style="text-align: center;margin-left: 0;">
						<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo0"
							i18n-content="保存"></button>
						<button type="reset" class="layui-btn layui-btn-primary" i18n-content="重置"></button>
					</div>
				</div>
			</form>
			<form class="layui-form" action="" lay-filter="configs00">
				<div class="layui-form-item layui-form-text">
    				<label class="layui-form-label" i18n-content="邮箱签名"></label>
    				<div class="layui-input-block">
      					<textarea i18n-placeholder="请输入内容" class="layui-textarea" name="EMAIL_SIGNATURE" id="EMAIL_SIGNATURE"></textarea>
    				</div>
  				</div>
				<div class="layui-form-item layui-form-text">
    				<label class="layui-form-label" i18n-content="免责声明"></label>
    				<div class="layui-input-block">
      					<textarea i18n-placeholder="请输入内容" class="layui-textarea" name="EMAIL_DISCLAIMERS" id="EMAIL_DISCLAIMERS"></textarea>
    				</div>
  				</div>
				<div class="layui-form-item">
					<div class="layui-input-block" style="text-align: center;margin-left: 0;">
						<button class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo00"
							i18n-content="保存"></button>
					</div>
				</div>
			</form>
		</div>






















		<div style="display: flex;width: 100%;height: 100%;background-color: #F6F7F9;">
			<div style="width: 360px;background-color: #fff; display: flex;flex-direction: column;height: 99%;"
				class="bkyy">

				<div
					style="height: 76px;background-color: #fff;border: 1px solid rgba(0,0,0,0.1);border-bottom: none;padding: 10px 16px;position: relative;">
					<img src="../css/defaultAvatar.png" id="zxheadImg">
					<span id="zxName">坐席昵称</span>


					<!-- <span onclick="configure()"
						style="display: block;float: right;cursor: pointer;transform: translateY(12px);"><i
							style="font-size: 24px;transform: translateY(-2px);color: #8a8a8a;"
							class="layui-icon layui-icon-set-fill"></i></span> -->

					<!-- <span onclick="configure2()"
						style="display: block;float: right;cursor: pointer;transform: translateY(15px);margin-right: 10px;"><img
							src="/cc-email/static/images/choose.png" style="width: 20px;height: 20px;"></span> -->

					<!-- <span onclick="configure3()"
						style="display: block;float: right;cursor: pointer;transform: translateY(15px);margin-right: 10px;"><img
							src="/cc-email/static/images/email.png" style="width: 22px;height: 22px;"></span> -->


					<!-- <div style="position: absolute;width: 100px;text-align: center;right: 10px;top: 35px;">
						<button class="layui-btn layui-btn-primary  layui-btn-sm" style="float: right;border:1px solid #2592FF;background-color: #fff;color: #2592FF;border-radius: 2px;margin-left: 7px;"
							onclick="configure3()" i18n-content="新增"></button>
							
						<button class="layui-btn layui-btn-primary  layui-btn-sm" style="float: right;border:1px solid #2592FF;background-color: #fff;color: #2592FF;border-radius: 2px;"
							onclick="configure()" i18n-content="配置"></button>	
					</div> -->


				</div>

				<div style="flex: 1;overflow: hidden;">
					<div class="layui-tab  layui-tab-brief" style="height: 100%;" lay-filter="demo">
						<ul class="layui-tab-title"
							style="display: flex;height: 50px;border-left: 1px solid rgba(0,0,0,0.1);border-right: 1px solid rgba(0,0,0,0.1);">
							<!-- <li style="flex:1;" class="layui-this" >待处理<span id="dcl"></span></li>
							<li style="flex:1;">未处理<span id="wcl"></span></li>
							<li style="flex:1;">已分组<span id="yfz"></span></li>
							<li style="flex:1;">已处理<span id="ycl"></span></li> -->

							<li class="tabsItem" lay-id="layId01" ;
								style="flex:1;border-top: 2px solid red;color:red;background-color: #F6F7F9;"
								class="layui-this">
								<div style="height:50%;line-height: 33px;color: #FF2626;font-weight: 700;font-size: 15px;"
									id="await">0</div>
								<div style="height:50%;line-height: 25px;" i18n-content="待处理"></div>
							</li>
							<li class="tabsItem" lay-id="layId02" ; style="flex:1;color:#76838F;">
								<div style="height:50%;line-height: 33px;color: #FF9000;font-weight: 700;font-size: 15px;"
									id="untreated">0</div>
								<div style="height:50%;line-height: 25px;" i18n-content="待领取"></div>
							</li>
							<li class="tabsItem" lay-id="layId03" ; style="flex:1;display: none;color:#76838F;"
								id="wfzTabId">
								<div style="height:50%;line-height: 33px;color: #0080FF;font-weight: 700;font-size: 15px;"
									id="unGroup">0</div>
								<div style="height:50%;line-height: 25px;" i18n-content="未分组"></div>
							</li>
							<li class="tabsItem" lay-id="layId04" ; style="flex:1;color:#76838F;">
								<div style="height:50%;line-height: 33px;color: #02C559;font-weight: 700;font-size: 15px;"
									id="dispose">0</div>
								<div style="height:50%;line-height: 25px;" i18n-content="已处理"></div>
							</li>
						</ul>
						<div class="layui-tab-content  layui-form"
							style="height:calc(100% - 24px);padding: 0;display: flex;flex-direction: column;background-color: #F6F7F9;"
							lay-filter="msg" id="myform">
							<div
								style="padding-left: 10px;margin-top: 10px;margin-bottom: 10px;border-bottom: 1px solid #DEE4EC;">
								<div class="search"
									style="padding-left: 10px;margin-top: 10px;display: flex;">

									<div class="layui-form-item" style="flex: 1;margin-bottom: 0;">

										<label class="layui-form-label"
											style="width: 74px;border: 1px solid #C8D2DD;text-align: center;padding: 4px 8px;font-size: 12px;"
											i18n-content="发件人"></label>

										<div class="layui-input-block width_250 pos-r"
											style="margin-left: 74px;border: 1px solid #C8D2DD;border-left: none;min-height: 28px;">

											<input type="text" id="searchInp" name="title" style="height: 28.4px;"
												autocomplete="off" class="layui-input" >
											<i class="icon_ca_layui"></i>

										</div>

									</div>

									<!-- <button type="button" onclick="searchMsg()" style="margin-left: 3px;"
									class="layui-btn layui-btn-normal"><i
										class="layui-icon  layui-icon-search"></i></button> -->

									<button type="button" onclick="searchMore()"
										style="margin-left: 8px;margin-top: 1px;height: 28.4px;line-height: 28.4px;margin-right: 10px;padding: 0 7px;"
										class="layui-btn layui-btn-primary"><span id="ssIcon"
											style="border: 1px solid #C8D2DD;text-align: center;width: 15px;height: 16px;line-height: 16px;display: inline-block;margin-right: 3px;"><i
												class="layui-icon"
												style="font-size: 12px;font-size: 9px;transform: scale(0.8);display: inline-block;padding: 0;">&#xe61a;</i></span><span id="shousuo" i18n-content="展开"></span></button>




								</div>
								<div id="moreSearch" style="display: none;height: 330px;overflow: auto;">
									<form class="layui-form" action="" lay-filter="configs3" id="addGoodsForm">
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="收件人"></label>
											<div class="layui-input-block">
												<input type="text" name="getHuman" i18n-placeholder="请输入收件人"
													autocomplete="off" class="layui-input">
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="创建时间"></label>
											<div class="layui-input-block">
												<!-- <input type="text" name="createTime" class="layui-input" id="test15" placeholder=" ~ "> -->
												<input type="text" name="createTime" class="layui-input" id="test16">
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="收件账号"></label>
											<div class="layui-input-block">
												<select name="getAcc" id="getAcc">
													<option value=""></option>
												</select>
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="处理状态"></label>
											<div class="layui-input-block">
												<select name="state" id="state">
													<option value=""></option>
												</select>
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="回执码"></label>
											<div class="layui-input-block">
												<input type="text" name="code" i18n-placeholder="请输入回执码"
													autocomplete="off" class="layui-input">
											</div>
										</div>

										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="标星"></label>
											<div class="layui-input-block">
												<select name="star" id="star">
													<option value="" i18n-content="请选择"></option>
													<option value="Y" i18n-content="是"></option>
													<option value="N" i18n-content="否"></option>
												</select>
											</div>
										</div>

										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="客户分组"></label>
											<div class="layui-input-block">
												<select name="group" id="group">
													<option value=""></option>
												</select>
											</div>
										</div>
										<div class="layui-form-item">
											<label class="layui-form-label" i18n-content="关键词"></label>
											<div class="layui-input-block">
												<input type="text" name="gjcode" i18n-placeholder="请输入标题/内容发件人/收件人"
													autocomplete="off" class="layui-input">
											</div>
										</div>


										<div class="layui-form-item">
											<div class="layui-input-block" style="text-align: right;margin-left: 0;">
												<!-- <button class="layui-btn layui-btn-normal" lay-submit
													lay-filter="formDemo3" i18n-content="搜索"></button> -->
												
												
												<button style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);" class="layui-btn layui-btn-sm " lay-submit lay-filter="formDemo3" i18n-content="搜索"></button>
												
												<div style="display: inline-block;background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);" class="layui-btn layui-btn-sm "   onclick="chongzhi()" i18n-content="重置"></div>
												<!-- <button type="reset" class="layui-btn layui-btn-primary"
													i18n-content="重置"></button> -->
											</div>
										</div>
									</form>
								</div>


							</div>

							<div class="layui-form-item" id="allbtn">
								<div class="layui-input-block"
									style="margin-left: 0;padding-top: 5px;padding-right: 2px;">

									<img onclick="allSelect(1)" src="/cc-email/pages/email/newEmail/css/select.png"
										style="width: 20px;height: 20px;margin-right: 10px;cursor: pointer;"
										id="blackSelect">
									<img onclick="allSelect(2)" src="/cc-email/pages/email/newEmail/css/selected.png"
										style="width: 20px;height: 20px;margin-right: 10px;display: none;cursor: pointer;"
										id="greenSelect">

									<button
										style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
										class="layui-btn layui-btn-sm operBtn" onclick="Handle(1)"
										i18n-content="忽略"></button>
									<button
										style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
										class="layui-btn layui-btn-normal  layui-btn-sm operBtn" onclick="Handle(2)"
										i18n-content="合并"></button>
									<button
										style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
										class="layui-btn  layui-btn-warm  layui-btn-sm operBtn" onclick="Handle(3)"
										i18n-content="回复"></button>
									<button
										style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
										class="layui-btn layui-btn-danger  layui-btn-sm operBtn" onclick="Handle(4)"
										i18n-content="解决">
									</button>
									<button
											style="background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
											class="layui-btn layui-btn-danger  layui-btn-sm operBtn" onclick="Handle(7)"
											i18n-content="批量转派">
									</button>
									<button id="receive"
											class="layui-btn layui-btn-normal  layui-btn-sm operBtn"
											style="display:none;background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
											onclick="Handle(6)" i18n-content="领取"></button>
									<button
											id="receive2"
											class="layui-btn layui-btn-normal  layui-btn-sm operBtn"
											style="display:none;background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
											onclick="Handle(1)"
											i18n-content="批量忽略">

									</button>
									<button class="layui-btn layui-btn-warm  layui-btn-sm operBtn"
										style="display:none;background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
										id="assignment" onclick="Handle(7)" i18n-content="分派"></button>
									<button
											id="assignment2"
											class="layui-btn layui-btn-normal  layui-btn-sm operBtn"
											style="display:none;background-color: #fff;color: rgb(30 ,159 ,255);border: 1px solid rgb(30 ,159 ,255);"
											onclick="Handle(1)"
											i18n-content="批量忽略">
									<!-- <button type="reset" class="layui-btn layui-btn-primary  layui-btn-sm operBtn"
										id="qx" onclick="Handle(5)" i18n-content="取消"></button> -->



									<button class="layui-btn layui-btn-primary  layui-btn-sm"
										style="float: right;border:1px solid #2592FF;background-color: #fff;color: #2592FF;border-radius: 2px;margin-left: 7px;"
										onclick="configure3()" i18n-content="新增"></button>

									<button class="layui-btn layui-btn-primary  layui-btn-sm"
										style="float: right;border:1px solid #2592FF;background-color: #fff;color: #2592FF;border-radius: 2px;"
										onclick="configure()" i18n-content="配置"></button>




								</div>
							</div>

							<div class="msg" id="demo">
							</div>


							<div id="test1"></div>


						</div>
					</div>
				</div>
			</div>
			<div id="rightDivId" style="flex: 1;height: 99%;background-color: #F6F7F9;margin-left: 13px;">
				<iframe src="/cc-email/pages/common/noData.jsp" width="100%" height="100%"
					style="border: medium none;"></iframe>
			</div>
		</div>


		<script type="text/javascript" src="/easitline-static/js/xss.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
		<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
		<script type="text/javascript" src="/cc-email/static/js/my_i18n.js?v=202006156"></script>
		<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=1231"></script>
		<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
		<script type="text/javascript" src="/easitline-static/js/layTable.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>

		<script type="text/javascript" src="/cc-email/static/js/time.js"></script>


		<script src="../lib/jquery-1.9.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
		<script src="../lib/layui/layui.js" type="text/javascript" charset="utf-8"></script>
		<!-- <script src="../js/mailServe.js?v=123456" type="text/javascript" charset="utf-8"></script> -->
		<script src="./mailServe.js?v=20241022" type="text/javascript" charset="utf-8"></script>
<!--
		<script src="../../../noLogin/js/mailServe.js?v=1234561" type="text/javascript" charset="utf-8"></script>
-->
	</body>
</html>
