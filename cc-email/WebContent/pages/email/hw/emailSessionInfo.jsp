<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>海外邮件</title>
	<!-- <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/tag.css" /> -->

	<!-- <link href="/easitline-static/lib/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="/easitline-static/lib/umeditor/third-party/template.min.js"></script>
	<script type="text/javascript" charset="utf-8" src="/easitline-static/lib/umeditor/umeditor.config.js"></script>
	<script type="text/javascript" charset="utf-8" src="/easitline-static/lib/umeditor/umeditor.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/umeditor/lang/zh-cn/zh-cn.js"></script>

	<script src="/easitline-static/lib/umeditor/dialogs/link/link.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/image/image.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/video/video.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/map/map.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/formula/formula.js" type="text/javascript" defer="defer"></script>
	<script src="/easitline-static/lib/umeditor/dialogs/emotion/emotion.js" type="text/javascript" defer="defer"></script> -->
	<style>
		#customer_card {
			padding: 0px 10px 35px 10px;
			height: 800px;
			background-color: #fff;
		}

		a,
		a:link,
		a:hover,
		a:focus,
		a:hover {
			color: #00adff;
		}

		#customer_card a,
		#customer_card a:link,
		#customer_card a:hover,
		#customer_card a:focus,
		#customer_card a:hover {
			color: #00adff;
		}

		p label {
			font-weight: normal
		}

		.list-curr {
			position: absolute;
			right: 5px;
			top: 11px;
		}

		.text-center {
			text-align: center;
		}

		.layui-col-md3 {
			width: 420px !important;
		}

		.layui-col-md9 {
			width: calc(100% - 430px) !important;
		}

		#customerInfo_iframe body {
			background-color: #ffffff
		}

		#contents,
		#contents>div {
			padding-left: 5px;
			padding-right: 0px;
		}

		#customer_card .layui-tab>.layui-tab-title {
			overflow: hidden;
			background-color: #fff;
			border-radius: 4px;
		}

		#order-bom {
			/* background: #f8f8f8; */
			margin-right: 15px;
		}

		#sidebar {
			margin: 0 0;
			/* margin-left: 10px; */
		}

		#sidebar .layui-tab-content {
			padding: 0px;
		}

		#sidebar_titles>.downLi {
			position: relative;
			background-color: #ffffff;
			width: 150px;
			display: block;
			margin: 0 0px 0 auto;
			border-bottom: 1px solid #f2f2f2;
			border-left: 1px solid #cecece;
			border-right: 1px solid #cecece;
		}

		#sidebar_titles>.downLi:last-of-type {
			border-bottom: 1px solid #cecece;
		}

		#sidebar_titles>.downLi:hover {
			color: #009688;
		}

		#sidebar>ul {
			height: 40px !important;
			overflow: inherit !important;
		}

		#sidebar>ul>span {
			display: none;
		}

		#sidebar .sidebarMore {
			border: none;
			/* padding: 0 8px; */
		}

		.layui-tab-brief>.layui-tab-title .layui-this {
			color: #337AB7;
		}

		.layui-tab-brief>.layui-tab-more li.layui-this:after,
		.layui-tab-brief>.layui-tab-title .layui-this:after {
			border-bottom: 2px solid #337AB7;
		}

		.history {
			padding-top: 40px;
			background-color: #fff;
			padding-left: 10px;
		}

		.box {
			padding-top: 20px !important;
			background-color: #f0f0f0;
			border-radius: 6px;
			color: #212121;
			line-height: 1.8;
			padding: 8px;
			margin: 30px 10px;
		}

		.box:nth-child(1) {
			margin-top: 0;
		}

		.box:last-child {
			margin-bottom: 10px;
		}

		.box .email_p .left_span {
			margin-right: 10px;
		}

		.msg_p {
			background-color: #fff;
			padding: 8px;
			border-radius: 4px;
		}

		.title_div_right {
			text-align: right;
		}

		.title_div_left,
		.title_div_right {
			padding: 10px;
		}

		.right_title {
			display: inline-block;
			text-align: left;
		}

		.max_length {
			width: 300px;
			display: inline-block;
			white-space: nowrap;
			/*控制单行显示*/
			overflow: hidden;
			/*超出隐藏*/
			text-overflow: ellipsis;
			/*隐藏的字符用省略号表示*/
			vertical-align: top;
			line-height: 25px;
		}

		.right_div_button {
			width: 30px;
			height: 30px;
			position: absolute;
			top: 360px;
			right: -16px;
			z-index: 998;
			cursor: pointer;
			transform: rotateZ(45deg);
			background-color: #337AB7;
			border-radius: 50%;
		}

		.out_a:hover .right_div_button {
			background-color: #179F69;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}

		.btn-color1 {
			background-color: rgb(177, 177, 84);
			border-color: rgb(177, 177, 84);
			color: #fff;
		}

		.btn-color1:hover {
			background-color: rgba(177, 177, 84, 0.8);
			color: #fff;
		}

		.btn-color2 {
			background-color: rgb(155, 197, 57);
			border-color: rgb(155, 197, 57);
			color: #fff;
		}

		.btn-color2:hover {
			background-color: rgba(155, 197, 57, 0.8);
			color: #fff;
		}

		.btn-color3 {
			background-color: rgb(64, 212, 176);
			border-color: rgb(64, 212, 176);
			color: #fff;
		}

		.btn-color3:hover {
			background-color: rgba(64, 212, 176, 0.8);
			color: #fff;
		}

		.btn-color4 {
			background-color: rgb(12, 160, 124);
			border-color: rgb(64, 212, 176);
			color: #fff;
		}

		.btn-color4:hover {
			background-color: rgba(12, 160, 124, 0.8);
			color: #fff;
		}

		.btn-color5 {
			background-color: rgb(89, 173, 12);
			border-color: rgb(64, 212, 176);
			color: #fff;
		}

		.btn-color5:hover {
			background-color: rgba(89, 173, 12, 0.8);
			color: #fff;
		}

		.like-a-tab {
			cursor: pointer;
			color: #01AAED
		}

		.like-a-tab:hover {
			text-decoration: underline;
		}

		.cke_reset_all {
			z-index: ********* !important;
		}

		.cke_dialog_background_cover {
			z-index: 999999998 !important;

		}

		.cke_bottom {
			position: static !important;
		}

		#titlePId {
			display: inline-block;
			font-size: 16px;
			font-weight: 700;
			overflow: hidden;
			/* text-overflow: ellipsis; */
			/* white-space: nowrap; */
			width: 100%;
			text-align: left;
		}

		#caseNumPId {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 90%
		}

		#LastContactTime {
			color: red;
			cursor: pointer;
		}

		.layui-layer-tips .layui-layer-content{
			color: #000000 !important;
		}
		.table{
			display: none;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="layui-fluid" id="contents">
		<div class="container-fluid chat-panel layui-row">
			<div class="layui-card layui-tab-card" id="order-bom" style="border: none;">
				<div class="layui-tab layui-tab-brief shadow" id="main">
					<form id="editForm" data-mars="emailSession.getEmailSessionById"
						data-pk="${param.emailSessionId}" method="post" autocomplete="off"
						data-mars-prefix="C_EMAIL_SEND."
						style="padding-top: 15px;padding-left: 50px;position: relative;">
						<input type="hidden" name="tempId" id="tempId" value="">
						<input type="hidden" name="C_EMAIL_SEND.EMAIL_FORM" id="STATUS" value="10" />
						<input type="hidden" name="C_EMAIL_SEND.state" id="state" value="${param.state }">
						<input type="hidden" name="firstRealyTime" id="firstRealyTime"
							value="${param.firstRealyTime }">
						<input type="hidden" name="sendTime" id="sendTime" value="${param.sendTime }">
						<input type="hidden" name="P_STATUS" id="P_STATUS" value="${param.STATUS }">
						<input type="hidden" name="caseNum" value="${param.CASE_NUM }">
						<input type="hidden" name="parentID" id="parentID" value="${param.parentID }">
						<input type="hidden" name="sessionId" id="sessionId" value="${param.emailSessionId }">
						<input type="hidden" name="C_EMAIL_SEND.STATUS" id="STATUS" value="10" />

						<img src="../../../static/images/kehu.jpg" style="width: 60px;height: 60px;position: absolute; top: 12px;left: 23px;border-radius: 50%;">
						<div
							style="display: flex;width: 100%;top: 12px;left: 0px;padding-left: 40px;padding-right: 10px;">
							<div style="flex:1;text-align:left;">
								<p style="font-weight: 700;font-size: 20px;color: #000;" id="titlePId" title=""></p>
								<p style="font-size:20px;margin-top:10px;max-width: 520px;" id="caseNumPId"></p>
							</div>
						</div>
						<table class="table  table-edit table-vzebra mt-10">
							<tbody>
								<tr>
									<td class="required" i18n-content="主题"></td>
									<td colspan="3"><input id="titleId" name="C_EMAIL_SEND.TITLE"
											data-rules="required" class="form-control input-sm" type="text"
											maxlength="400">
									</td>
								</tr>
								<tr>
									<td>
										<h5 class="required" i18n-content="收件人"></h5>
									</td>
									<td colspan="3">
										<div class="input-group input-group-sm" style="width:100%">
											<input id="EMAIL_TO" name="C_EMAIL_SEND.EMAIL_TO" data-rules="required"
												   class="form-control input-sm" type="text"
												   value="${param.EMAIL_TO}">
											<span class="input-group-addon btn "
												  onclick="orderIndex.selectGroup('1')">
													<span class="glyphicon glyphicon-search"></span>
												</span>
										</div>
									</td>
								</tr>
								<tr>
									<td>
										<h5 i18n-content="抄送"></h5>
									</td>
									<td colspan="3">
										<div class="input-group input-group-sm" style="width:100%">
											<input id="EMAIL_CC" name="C_EMAIL_SEND.EMAIL_CC"
												   class="form-control input-sm" type="text"
												   value="${param.EMAIL_CC}">
											<span class="input-group-addon btn "
												  onclick="orderIndex.selectGroup('2')">
													<span class="glyphicon glyphicon-search"></span>
												</span>
										</div>
									</td>
								</tr>
								<tr>
									<td width="50px" i18n-content="模板分类"></td>
									<td width="100%" colspan="3">
										<select name="" id="templateTypeId" class="input-sm form-control"
											data-mars="emailModel.getEmailTempTypeId"
											onchange="orderIndex.changeTemp();">
											<option value="" i18n-content="请选择"></option>
										</select>
									</td>
								</tr>
								<tr>
									<td width="50px" i18n-content="选择模板"></td>
									<td width="100%" colspan="3">
										<select name="C_EMAIL_SEND.TEMPLATE_NAME" id="templateId"
											class="input-sm form-control" data-mars="emailModel.getEmailId"
											onchange="orderIndex.changeContent();">
											<option value="" i18n-content="请选择"></option>
										</select>
									</td>
								</tr>
								<tr>
									<td class="required" i18n-content="内容"></td>
									<td colspan="3">
										<div><textarea name="editor"></textarea></div>
									</td>
								</tr>
								<tr>
									<td i18n-content="附件上传"></td>
									<td>
										<button class="btn btn-sm btn-primary" type="button"
											onclick="orderIndex.attachment();"><span
												class="glyphicon glyphicon-folder-close"></span> <span
												i18n-content="附件管理"></span></button>
									</td>
								</tr>
								<tr>
									<td i18n-content="模板附件"></td>
									<td>
										<div id="tempListId">
										</div>
									</td>
								</tr>
								<tr>
									<td align="center" colspan="4">
										<div style="text-align:center">
											<button class="btn btn-sm btn-success " type="button"
												onclick="orderIndex.submitForm('1');" i18n-content="回复"></button>
											<button class="btn btn-sm btn-success btn-color5" type="button"
												onclick="orderIndex.submitForm2('1');"
												i18n-content="回复并解决"></button>
											<button class="btn btn-sm btn-info" type="button"
												onclick="orderIndex.myd();" i18n-content="满意度推送"></button>
											<button class="btn btn-sm btn-primary btn-color2" type="button"
												onclick="orderIndex.remarks();" i18n-content="备注"></button>
											<EasyTag:res resId="cc-tx-email-auth-func-botton1">
												<button class="btn btn-sm btn-primary btn-color3" type="button"
													onclick="orderIndex.Transfer();" i18n-content="转派"></button>
											</EasyTag:res>
											<EasyTag:res resId="cc-tx-email-auth-func-botton2">
												<button class="btn btn-sm btn-success btn-color1" type="button"
													onclick="orderIndex.solve();" i18n-content="解决"></button>
											</EasyTag:res>
											<EasyTag:res resId="cc-tx-email-auth-func-botton3">
												<button class="btn btn-sm btn-warning" type="button"
													onclick="orderIndex.hang();" i18n-content="挂起"></button>
											</EasyTag:res>
											<EasyTag:res resId="cc-tx-email-auth-func-botton4">
												<button class="btn btn-sm btn-primary" type="button"
													onclick="orderIndex.solutionsHanging();"
													i18n-content="解挂"></button>
											</EasyTag:res>
											<EasyTag:res resId="cc-tx-email-auth-func-botton5">
												<button class="btn btn-sm btn-danger" type="button"
													onclick="orderIndex.closeEmail();" i18n-content="办结"></button>
											</EasyTag:res>
										</div>
									</td>
								</tr>
							</tbody>
						</table>

						<div
							style="width: 100%;padding: 20px;box-sizing: border-box;margin-left: 50%;transform: translateX(-49%);display: flex;">
							<p style="margin-top:20px;margin-right: 20px;" i18n-content="标签："></p>
							<div style="flex: 1;" id="tag">

							</div>
						</div>
					</form>
					<br>
					<br>
					<div>
						<div class="layui-card-body">
							<div class="layui-tab layui-tab-brief" lay-filter="test">
								<ul class="layui-tab-title">
									<li class="layui-this" i18n-content="邮件交互"></li>
									<li i18n-content="备注"></li>
								</ul>
								<div class="layui-tab-content">
									<!-- 邮件交互 -->
									<div class="layui-tab-item layui-show">
										<div class="history">
											<ul class="layui-timeline" id="msgBox" style="padding: 0 20px;"></ul>
										</div>
									</div>
									<!-- 备注 -->
									<div class="layui-tab-item">
										<div class="history">
											<ul class="layui-timeline" id="remarksBox" style="padding: 0 20px;">
											</ul>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/lib/ckeditor/ckeditor.js"></script>
	<script type="text/javascript">
		jQuery.namespace("orderIndex");
		var sessionId;
		jQuery.namespace("emailHandleTemp");
		
		var isStar0 = false;
		var currentId = 0;
		
		
		emailHandleTemp.searchData = function() {
			ajax.remoteCall("/cc-base/servlet/labeldirectory?action=tagInfo", {
				emailSessionId: sessionId
			}, function(reply) {
				var map = reply.data;
				
				if (map) {
					var str = '';
					for (var i = 0; i < map.length; i++) {
						if (map[i].NAME && map[i].NAME.trim() !== '') {
							str +=
								'<div style="display: inline-block;margin-right:10px;margin-top:10px;padding: 0px 12px;background-color: ' +
								randomColor(2) +
								';color: #fff;text-align: center;height: 36px;line-height: 36px;border-radius: 8px;">' +
								map[i].NAME + '</div>';
						}
					}
					//str +=
						//'<div style="display: inline-block;"><p onclick="addTag()"  style="cursor: pointer;line-height: 16px;width: 36px;height: 36px;background-color: #1E9FFF;color:#fff;border-radius:50%;padding:10px;text-align:center;"><i class="layui-icon layui-icon-addition"></i></p></div>';
					$('#tag').html(str)
				}
			});
		}
		var topid = top.$('li.layui-this').attr("lay-id");
		var lastContent = "";
		var lastInfo = "";
		CKEDITOR.replace("editor", {
			toolbar: [{
					name: 'colors',
					items: ['TextColor', 'BGColor']
				},
				{
					name: 'clipboard',
					items: ['Undo', 'Redo', 'Outdent', 'Indent']
				},
				{
					name: 'basicstyles',
					items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript']
				},
				{
					name: 'paragraph',
					items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock']
				},
				{
					name: 'insert',
					items: ['Smiley', 'HorizontalRule', 'PageBreak']
				},
				{
					name: 'styles',
					items: ['Styles', 'Format', 'Font', 'FontSize', 'lineheight']
				}
			],
			height: 270,
			language:getDateLangCk(),
			allowedContent: true,
			filebrowserImageUploadUrl: "/cc-base/servlet/attachment?action=editorUpload&busiType=email"
		});
		CKEDITOR.instances["editor"].on("instanceReady", function() {});

		function timeFn(d1) { //di作为一个变量传进来
			//如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
			var dateBegin = new Date(d1.replace(/-/g, "/")); //将-转化为/，使用new Date
			var dateEnd = new Date(); //获取当前时间
			var dateDiff = dateEnd.getTime() - dateBegin.getTime(); //时间差的毫秒数
			var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)); //计算出相差天数
			var leave1 = dateDiff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
			var hours = Math.floor(leave1 / (3600 * 1000)) //计算出小时数
			//计算相差分钟数
			var leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
			var minutes = Math.floor(leave2 / (60 * 1000)) //计算相差分钟数
			//计算相差秒数
			var leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
			var seconds = Math.round(leave3 / 1000)
			// console.log(" 相差 "+dayDiff+"天 "+hours+"小时 "+minutes+" 分钟"+seconds+" 秒")
			var str = dayDiff + getI18nValue("天") + hours + getI18nValue("小时") + minutes + getI18nValue("分钟") + seconds + getI18nValue("秒");
			$('#LastContactTime').html(str)
			
			
			layui.use(['layer'], function() {
				var layer = layui.layer;
				var tips;
				$('#LastContactTime').on({
					mouseenter: function() {
						 tips = layer.tips("<span style='color:#000'>"+getI18nValue("距离收到最后一封邮件的时间")+"</span>", "#LastContactTime",{tips:[3,'#fff']});//弹出框加回调函数
					},
					mouseleave: function() {
						layer.close(tips);
					}
				});
			});
			// console.log(dateDiff+"时间差的毫秒数",dayDiff+"计算出相差天数",leave1+"计算天数后剩余的毫秒数",hours+"计算出小时数",minutes+"计算相差分钟数",seconds+"计算相差秒数");
		}
		// var t3="2017-08-18 04:56:38";
		// timeFn(t3);
		function addTag(params) {
			ajax.remoteCall("/cc-base/servlet/labeldirectory?action=tagInfo", {
				emailSessionId: sessionId
			}, function(reply) {
				var map = reply.data;
				if (reply.data) {
					addCustLabelList = reply.data;
					console.log('addCustLabelList', addCustLabelList)
					popup.layerShow({
						type: 2,
						title: getI18nValue('设置标签'),
						offset: '20px',
						area: ['500px', '360px']
					}, "/cc-base/pages/tag/userlabel-add-media.jsp", {
						"id": sessionId,
						addCustLabelList: JSON.stringify(addCustLabelList),
						busiType: 'email'
					});
				}
			});
		}


		$(function() {
			renderCustRecord();
			currentId='${param.emailSessionId}'	
			$("#editForm").render({
				success: function(result) {
					var contentJo = result['emailSession.getEmailSessionById'];
					var emailToTemp = contentJo.EMAIL_TO;
					var emailFromTemp = contentJo.EMAIL_FROM;
					var titleTemp = contentJo.TITLE;
					var caseNumTemp = contentJo.CASE_NUM;
					var isStarTemp = contentJo.IS_STAR;
					var handleAccTemp = contentJo.HANDLE_ACC;
					var statusTemp = contentJo.STATUS;
					var star = "";
					if("Y"==isStarTemp){
						star = '<i class="layui-icon layui-icon-rate-solid isStar" style="color: #FF9A00;font-size: 26px;font-weight: 300;transform:translateY(2px);display:inline-block;margin-left:10px"></i>';
					}else{
						star = '<i class="layui-icon layui-icon-rate isStar" style="font-size: 26px;font-weight: 300;transform:translateY(2px);display:inline-block;margin-left:10px"></i>';
					}
					
					$("#titleId").val(titleTemp);
					$("#titlePId").html(titleTemp +
							'<span style="font-size: 12px;margin-left:10px">(' + emailToTemp +
							')</span>'+star);
					$("#titlePId").attr("title", titleTemp);
					
					var span = " > <span style='color:red;'>"+caseNumTemp+"</span>";
					var states = statusTemp;
					var statesStr = "";
					if (states == '02') {
						statesStr = getI18nValue('待处理');
					} else if (states == '01') {
						statesStr = getI18nValue('待领取');
					} else if (states == '09') {
						statesStr = getI18nValue('未分组');
					} else if (states == '03') {
						statesStr = getI18nValue('已处理');
					}else if (states == '04') {
						statesStr = getI18nValue('已退回');
					}else if (states == '05') {
						statesStr = getI18nValue('无需处理');
					}else if (states == '06') {
						statesStr = getI18nValue('已挂起');
					}else if (states == '07') {
						statesStr = getI18nValue('已解决');
					}else if (states == '08') {
						statesStr = getI18nValue('已关闭');
					}

					var HANDLE_ACC = handleAccTemp;
					if (HANDLE_ACC) {
						$("#caseNumPId").html(emailFromTemp + span + " > " + statesStr + " > " +
							HANDLE_ACC);
					} else {
						$("#caseNumPId").html(emailFromTemp + span + " > " + statesStr);
					}

					var aChildren = document.createElement('div')
					aChildren.style.position = 'fixed';
					aChildren.style.padding = '5px 10px';
					aChildren.style.fontSize = '12px';
					aChildren.style.borderRadius = '5px';
					aChildren.style.border = '1px solid #000';
					aChildren.style.backgroundColor = '#fff';
					aChildren.style.textAlign = 'center';
					aChildren.style.display = 'none';
					aChildren.style.zIndex = '99999';
					aChildren.style.left = $("#caseNumPId").offset().left + 220 + 'px';
					aChildren.style.top = $("#caseNumPId").offset().top + 30 + 'px';
					aChildren.innerHTML = getI18nValue('邮件会话回执码:') + $("#caseNumPId").find('span').text();
					$("#caseNumPId").append(aChildren)

					$("#caseNumPId").mouseenter(function(params) {
						$(aChildren).show()
					})
					$("#caseNumPId").mouseleave(function(params) {
						$(aChildren).hide()
					})
					$("#caseNumPId").text(emailFromTemp + "(" + (caseNumTemp) + ")");
					$("#createTimePId").text(('${param.sendTime}'));
					<%--$("#EMAIL_TO").val(unescape('${param.EMAIL_TO}'));--%>
					<%--$("#EMAIL_CC").val(unescape('${param.EMAIL_CC}'));--%>

				}
			});

			var caseNum = "${param.caseNum}";
			sessionId = "${param.emailSessionId}";
			emailHandleTemp.searchData()

			ajax.daoCall({
				"params": {
					sessionId: sessionId
				},
				"controls": ["emailHis.emailHisList", ]
			}, function(result) {
				var data = result['emailHis.emailHisList'];
				var list = data['data'];
				var reContent = '';
				
				setInterval(function() {
					var timeTemp = "";
					for (var i=0;i<list.length;i++){ 
						if("2"==list[i].TYPE){
							timeTemp = list[i].SEND_TIME;
							break;
						}
					}
					timeFn(timeTemp)
				}, 1000);

				var fromName = getI18nValue("发件人");
				var titleName = getI18nValue("主题");
				var ccName = getI18nValue("收件人");
				var bcName = getI18nValue("抄送");
				var contentList = new Array();
				var emailReceiveId='';
				if (list) {
					for (var i = 0; i < list.length; i++) {
						var content = list[i]['CONTENT'];
						var type = list[i]['TYPE'];
						content = decodeURIComponent(content.replace(/\+/g, '%20'));
						contentList[i] = content;
						//contentSub 该字段没有定义，删了 否则会报错
						if ('1' == type) {
							reContent +=
									'<li class="layui-timeline-item"><img src="../../../static/images/kefu2.jpg" style="width:42px;border-radius:50%;transform:translate(-38%);">' +
									'<div class="layui-timeline-content layui-text" style="transform:translateY(-38px);">' +
									'<div style="padding:15px;background-color:#f3f3f3;border-radius:10px;margin-left:10px;">' +
									'<div style="font-weight:600;word-break: break-all;">' +
									'<span style="font-size:16px;white-space: normal;word-break: break-all;overflow: hidden;">' +
									fromName + '：' + list[i]['EMAIL_FROM'] + '</span>&nbsp;&nbsp;' + list[i]['SEND_TIME'] +
									
									'</div><div style="white-space: normal;word-break: break-all;overflow: hidden;">' +
									titleName + '： ' + list[i]['TITLE'] +
									'</div><div style="white-space: normal;word-break: break-all;overflow: hidden;">' +
									ccName + '： ' + list[i]['EMAIL_TO'] + '</div>' + bcName + '：' + list[i]['EMAIL_CC'];
						} else {
							reContent +=
									'<li class="layui-timeline-item"><img src="../../../static/images/kehu.jpg" style="width:42px;border-radius:50%;transform:translate(-38%);">' +
									'<div class="layui-timeline-content layui-text" style="transform:translateY(-38px);">' +
									'<div style="padding:15px;background-color:#E4ECF5;border-radius:10px;margin-left:10px;">' +
									'<div style="font-weight:600;word-break: break-all;">' +
									'<span style="font-size:16px;white-space: normal;word-break: break-all;overflow: hidden;">' +
									fromName + '：' + list[i]['EMAIL_FROM'] + '</span>&nbsp;&nbsp;' + list[i]['SEND_TIME'] + '<span>' +
									'<span i18n-title="转发" onclick="forward(\'' + emailReceiveId + '\',\'' + encodeURIComponent(list[i]['TITLE']) + '\')" ' +
									'style="display: block;float: right;cursor: pointer;transform: translateY(-5px);">' +
									'<i style="font-size: 24px;transform: translateY(-2px);color: #8a8a8a;" class="layui-icon layui-icon-share"></i>' +
									'</span>' +
									'</span>' +
									'</div><div style="white-space: normal;word-break: break-all;overflow: hidden;">' +
									titleName + '： ' + list[i]['TITLE'] +
									'</div><div style="white-space: normal;word-break: break-all;overflow: hidden;">' +
									ccName + '： ' + list[i]['EMAIL_TO'] + '</div>' + bcName + '：' + list[i]['EMAIL_CC'];

						}
						//附件
						var fileList = list[i]['FILE_LIST'];
						var fjName = getI18nValue("附件列表");
						if (fileList && fileList.length > 0) {
							reContent += '<br>' + fjName + '：';
							for (var j = 0; j < fileList.length; j++) {
								var tempname = fileList[j]['NAME'];
								var tempName = fileList[j]['NAME'];
								var tempPath = fileList[j]['FILE_PATH'];
								reContent += '<br><span class="like-a-tab" onclick="previewPdf(\'' +
										tempPath + '\');">' + tempName + '</span>' +
										'<span class="like-a-tab" onclick="orderIndex.download(\'' +
										tempPath + '\');"> 下载</span>';
							}
						}
						reContent +=
							'<div style="padding:10px;box-sizing:border-box;background-color:#fff;margin-top:10px;border-radius:5px;overflow:auto;">' +
							'<textarea name="editor_' + i + '">' + content + '</textarea>' +
							'</div></div></div></li>';

					}
				}
				$('#msgBox').html(reContent);
				if (list) {
					for (var i = 0; i < list.length; i++) {
						CKEDITOR.replace("editor_" + i, {
							toolbar: [{

							}],
							height: 270,
							language:getDateLangCk(),
							allowedContent: true,
							readOnly: true
						});
						//CKEDITOR.config.readOnly = true;
						CKEDITOR.instances["editor_" + i].setData(contentList[i]);
					}
				}

				$(".history").find("p").each(function() {
					$(this).css("word-break", "break-all");
				});
				$(".history li font").find("a").each(function() {
					$(this).css("word-break", "break-all");
				});
				$(".history li p").find("img").each(function() {
					if ($(this).width() >= $(".history li p").width()) {
						$(this).css("width", "100%");
					}
				});
			});

			var logType = "04";
			ajax.daoCall({
				"params": {
					sessionId: sessionId,
					logType: logType
				},
				"controls": ["emailFollowLog.list", ]
			}, function(result) {
				var data = result['emailFollowLog.list'];
				var list = data['data'];
				var reContent = '';

				var temp1 = getI18nValue("操作人名称");
				var temp2 = getI18nValue("操作人部门名称");
				var temp3 = getI18nValue("操作时间");

				if (list) {
					for (var i = 0; i < list.length; i++) {
						var content = list[i]['CONTENT'];
						content = decodeURIComponent(content.replace(/\+/g, '%20'));
						reContent +=
							'<li class="layui-timeline-item"><img src="../../../static/images/remarks.png" style="width:42px;border-radius:50%;transform:translate(-38%);"><div class="layui-timeline-content layui-text" style="transform:translateY(-38px);"><div style="padding:15px;background-color:#f3f3f3;border-radius:10px;margin-left:10px;"> <div style="font-weight:600"><span style="font-size:16px;">' +
							temp3 + '：' + list[i].CREATE_USER_TIME + '</span>' +
							'</div>' + temp1 + '： ' + list[i].CREATE_USER_NAME + '<br>' + temp2 + '： ' + list[
								i].CREATE_DEPT_NAME +
							'<br><div style="padding:10px;box-sizing:border-box;background-color:#fff;margin-top:10px;border-radius:5px;">' +
							content +
							'</div></div></div></li>';
					}
				}
				$('#remarksBox').html(reContent);
				$(".history").find("p").each(function() {
					$(this).css("word-break", "break-all");
				});
				$(".history li font").find("a").each(function() {
					$(this).css("word-break", "break-all");
				});
				$(".history li p").find("img").each(function() {
					if ($(this).width() >= $(".history li p").width()) {
						$(this).css("width", "100%");
					}
				});
			});

			/* requreLib.setplugs('layui',function(){
				//Tab的切换功能
				layui.use(['element','laydate'], function(){
					//Tab的切换功能，切换事件监听等，需要依赖element模块
					var element = layui.element;
					element.on('tab(test)', function(elem){
						console.log(elem);
					});
				});
			}) */



			//渲染侧边栏
			orderIndex.renderRightPage();

		})

		orderIndex.renderRightPage = function() {

			var source = "",
				title = "",
				source = "",
				emailId = "",
				caseNum = "",
				ID = "",
				customerId = "",
				chatSessionId = "",
				email = "",
				emailSessionId = "";

			source = "email";
			title = "${param.oldTitle}";
			emailId = "${param.ID}";
			caseNum = "${param.caseNum}";
			ID = "${param.orderId}";
			customerId = "${param.customerId}";
			chatSessionId = "${param.ID}";
			email = unescape('${param.EMAIL_FROM}');
			emailSessionId = "${param.emailSessionId}";
			orderId = "${param.orderId}";
			account = "${param.emailSessionId}";

			var sidebarParam = {
				source: source,
				title: encodeURIComponent(encodeURIComponent(title)),
				emailId: emailId,
				caseNum: caseNum,
				ID: ID,
				customerId: customerId,
				chatSessionId: chatSessionId,
				email: email,
				emailSessionId: emailSessionId,
				sourcePage: "email",
				orderId: orderId,
				account: account
			};
		}
		//备注
		orderIndex.remarks = function() {
			//var myEditor=CKEDITOR.instances.editor.getData();
			var emailSessionId = '${param.emailSessionId}';
			var logType = "04";
			var url = "${ctxPath}/pages/email/remarks/remarks.jsp?emailSessionId=" + emailSessionId + "&logType=" +
				logType;
			popup.layerShow({
				type: 2,
				title: getI18nValue('备注'),
				zIndex: *********,
				offset: '40px',
				area: ['400px', '320px']
			}, url, null);
		}
 
 
		var renderCustRecord = function() {
			requreLib.setplugs('layui', function() {
				//Tab的切换功能
				layui.use('element', function() {
					var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
					element.on('tab(test)', function(elem) {
						location.hash = 'test=' + $(this).attr('lay-id');
					});
				});
				layui.use('form', function() {
					var form = layui.form;
				});
			})
		}
   
		orderIndex.download = function(filePath) {
			window.location.href = "/cc-base/servlet/attachment?action=download&filePath=" + filePath;
		}
		
		function getDateLangCk() {
			var ckLang = getDateLang();
			if('cn'==ckLang){
				ckLang = 'zh-cn';
			}else if('en'==ckLang){
				ckLang = 'en';
			}else{
				ckLang = 'zh-cn';
			}
			return ckLang;
		}

		function randomColor(num) {
			// 1 代表获取 浅色的随机色    2代表获取深色的随机色   不传参数num代表获取随机色
			if (!num) {
				var color = "";
				for (var i = 0; i < 6; i++) {
					color += (Math.random() * 16 | 0).toString(16);
				}
				return "#" + color;
			}
			if (num == 1) {
				return '#' +
					(function(color) {
						return (color += '5678956789defdef' [Math.floor(Math.random() * 16)]) &&
							(color.length == 6) ? color : arguments.callee(color);
					})('');
			} else if (num == 2) {
				return '#' +
					(function(color) {
						return (color += '0123401234abcabc' [Math.floor(Math.random() * 16)]) &&
							(color.length == 6) ? color : arguments.callee(color);
					})('');
			}
		}
		// form 表单获取pdf文件流，进行预览
		function previewPdf(tempPath,tempName) {
			// 构建查询参数
			var params = new URLSearchParams();
			params.append('tempPath', tempPath);
			params.append('tempName', tempName);
			params.append('type', 'preview');

			console.log(params);

			// 构建 URL
			var url = '/cc-email/servlet/previewPdf?action=PreviewPdf&' + params.toString();

			// 打开一个新窗口并在其中显示 PDF
			window.open(url, '_blank');

		}
	</script>
</EasyTag:override>
<%@include file="/pages/common/layout_list.jsp"%>
