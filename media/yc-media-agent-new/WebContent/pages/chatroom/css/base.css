    html,body{
      height:100%;
      font-family: 微软雅黑;
      line-height:1.5;
    }
    html,body,div,p,h1,h2,h3,ul,li,ol,input{
      margin:0;
      padding:0;
      box-sizing: border-box;
    }
    body{
      background-color: #f5f5f5;
      text-align: center;
      color:#333;
    }
    body>*{
      text-align:left;
    }
    video{
      width:100%;
      height:100%;
      object-fit: cover;
    }
    button{
      border:0;
      border-radius: 6px;
    }
    button[disabled]{
      background-color:#9e9e9e !important;
    }
    button:focus,input:focus{
      outline:none;
    }
    button:hover{
      opacity: .7;
    }
    input,select{
      border:1px solid #efefef;
      height:40px;
      border-radius: 6px;
    }
    body::before{
      content:'';
      display:inline-block;
      vertical-align: middle;
      font-size:0;
      width:0;
      height:100%;
    }
    .container{
      display:inline-block;
      vertical-align: middle;
      text-align:left;
      font-size:14px;
      border-radius: 12px;
      background-color:#fff;
      width:95%;
      max-width:900px;
      height:95%;
      min-height:500px;
      /*max-height:700px;*/
      box-shadow: 0 0 8px #d8d8d8;
      position:relative;
    }
    .container .head{
      padding:10px 20px;
      border-bottom:1px solid #eee;
    }
    .container .head h1{
      line-height:30px;
      font-size:16px;
    }
    .container .body{
      height:calc(100% - 50px);
      position:relative;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
      overflow:hidden;
    }
    .container .option{
      /*position:absolute;
      right:0;
      bottom:100%;
      margin-bottom:5px;*/
      float: right;
      z-index:2;
    }
    .head .btn{
      width:50px;
      height:30px;
      background-color:#5f7ace;
      color:#fff;
      font-size:12px;
      position: absolute;
      right: 20px;
      top: 12px;
    }
    .createPart,.optionPart .inner{
      width:80%;
      margin:0 auto;
      padding-top:10px;
    }
    .optionPart{
      position:absolute;
      left:0;
      top:0;
      bottom:0;
      width:100%;
      background-color:#fff;
      height: 100%;
      overflow: auto;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
      z-index:999;
    }
    .meetingPart{
      height:100%;
      overflow:hidden;
      position:relative;
    }
    .meetingPart .inner{
      height:100%;
      overflow:auto;
    }
    .mobile .inner{
      display:flex;
      flex-direction: column;
    }
    .userListPart{
      position:absolute;
      left:0;
      top:0;
      height:100%;
      width:200px;
      overflow:auto;
    }
    .mobile .userListPart{
      position:relative;
      height:100px;
      width:100%;
    }
    .userListPart .title{
      padding:10px;
      color:#999;
    }
    .userListPart .item{
      height:40px;
      padding:10px;
      white-space: nowrap;
      position:relative;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 50px;
      border-bottom: 1px solid rgb(239 239 239);
    }
    .mobile .userListPart .item{
      margin:10px;
      margin-top:0;
      border:1px solid rgb(239 239 239);
      border-radius: 5px;
    }
    .userListPart .item .btn{
      position:absolute;
      right:10px;
      top:8px;
      padding:0 5px;
      height:25px;
      color:#fff;
      background-color:#4caf50;
      z-index:2;
    }
    .videoListPart{
      margin-left:200px;
      /*padding:10px;*/
      padding-left:0;
      height:100%;
      position:relative;
      background-color:#333;
      font-size:0;
      overflow:auto;
    }
    .mobile .videoListPart{
      margin-left:0 !important;
      flex:1;
      min-height:0;
      height:auto;
    }
    .videoListPart .item{
      width:45%;
      max-width:180px;
      height:150px;
      position:relative;
      background-color:#000;
      border-radius: 5px;
      display:inline-block;
      font-size:12px;
      margin-left:10px;
      margin-bottom:10px;
      vertical-align: middle;
    }
    .videoListPart .item .kick{
      position:absolute;
      left:-8px;
      top:-8px;
      width:20px;
      height:20px;
      border-radius: 100%;
      background-color:#dc143c;
      color:#fff;
      font-style: normal;
      text-align: center;
      line-height: 20px;
      font-size: 12px;
      cursor:default;
      z-index:15;
    }
    .videoListPart .item .name{
      position:absolute;
      left:0;
      bottom:0;
      width:100%;
      white-space: nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
      background-color: rgba(0,0,0,.5);
      color:#fff;
      text-align:center;
      padding:5px;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      z-index:5;
    }
    .videoListPart .item video{
      border-radius: 5px;
      overflow:hidden;
      position:relative;
      z-index:1;
    }
    .meetingPart .btnGroup{
      position:absolute;
      left:0;
      bottom:10px;
      width:100%;
      z-index:99;
    }
    .btnGroup{
      text-align:center;
      margin-top:30px;
    }
    .btnGroup button{
      background-color:#2196f3;
      color:#fff;
      border-radius: 6px;
      min-width:80px;
      height:40px;
      padding:0 10px;
      margin-bottom:10px;
      font-size:12px;
    }
    .label{
      padding:10px 0 5px;
    }
    .inp input,.inp select{
      width:100%;
      padding:5px 10px;
    }
    .status{
      display:inline-block;
      vertical-align: middle;
    }
    .status span{
      display:inline-flex;
      flex-direction: column;
      justify-content: center;
      align-items:center;
      font-size:12px;
      color:#999;
      margin-right:15px;
      margin-left:5px;
    }
    .status i{
      display:inline-block;
      width:15px;
      height:15px;
      border-radius:100%;
      vertical-align: middle;
      background-color: #666;
      box-shadow: 0 0 5px #9b9b9b;
    }
    .inviting{
      color:#fff;
      text-align:center;
      height:100%;
      position:absolute;
      left:0;
      top:0;
      width:100%;
      z-index:3;
    }
    .inviting::after{
      content:'';
      display: inline-block;
      height:100%;
      font-size:0;
      width:0;
      vertical-align: middle;
    }
    .operator{
      position:absolute;
      top:0;
      right:0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      opacity:0.9;
      z-index:9;
    }
    .operator i{
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      background-color: #5f7ace;
      color:#fff;
      font-size:12px;
      padding:2px 5px;
      margin-top:5px;
      cursor:default;
      text-align: right;
      font-style: initial;
    }
    .custom i{
      background-color: #009688;
      cursor: not-allowed;
    }
    .operator .on::before{
      content: '';
      display:inline-block;
      vertical-align: middle;
      width:2px;
      height:10px;
      background-color:#ff3c3c;
      margin-right:3px;
    }
    .ok{
      background-color: #4caf50 !important;
    }
    .fail{
      background-color:#dc143c !important;
    }
    .viewer{
      position: fixed;
      left:0;
      top:0;
      width:100%;
      height:100%;
      background-color: rgba(0,0,0,.7);
      font-size:0;
      display:flex;
      flex-direction: column;
      justify-content: center;
      align-items:center;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      z-index:9999;
    }
    .viewer img{
      width:90%;
      height:auto;
      display:inline-block;
    }