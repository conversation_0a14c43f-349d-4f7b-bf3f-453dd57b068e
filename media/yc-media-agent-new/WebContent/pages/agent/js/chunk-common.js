(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-common"],{"0692":function(t,e,s){"use strict";var n,a=s("2b0e"),i=s("a925"),o={title:{onlineService:"Online service"},transfer:{"000":"Successfully transferred","001":"The transfer failed, the chat has been served by other agents","002":"The transfer failed,the target agent offline","003":"The transfer failed,there are no free agent in this skill group","004":"The transfer failed,the target agent rejected","005":"Waiting for confirmation from the target agent","006":"Session is transferring, please do not resubmit"},message:{selectService:"Please select the service you require",placeholder:"Please enter the message",msgRollback:"The messaged has been recalled",consult:"I would like to ask",screenshot:"Sending screenshots",history:"History messages",leaveMsg:"Please enter a forwarded message...",sessionOffline:"The user is offline",closeNoReplyChat:"Still have unanswered customers, confirm to close?",closeChat:"End Chat?",enterLimitCount:"Please enter the concurrent service line",maxLimitCount:"Maximum",minLimitCount:"Minimum",success:"Succeed",fail:"fail",EnterInteger:"Please enter an integer value",AgentLogout:"The agent not signed in",CloseRepliedChats:"Are you sure to end all the replied chats? The unreplied chats will not be affected.",ResetStyle:"Are you going to reset all customized configurations?",LoggedElsewhere:"The agent has signed in elsewhere",Loging:"Signing in..",LoginSucc:"Signed in successfully",transferCommandSucc:"Transfer request sent successfully",transferSucc:"Successfully transferred",transferFail:"Transfer rejected ",selectTransferTarget:"Please select the target agent",satisfactionSurvey:"The agent initiates a satisfaction survey",sensitiveWordsCount:"Content contains sensitive words:",typeLimit:"Upload file type restrictions:",sizeLimit:"Upload file size limit:",unread:"new message",notEmpty:"cannot be empty",serviceError:"Service error,please check the network",CloseAllEndChats1:"Do you want to close all ended chats without interaction for ",CloseAllEndChats2:" minutes?",VideoTips:"Please download and play the video if the video cannot be played in the system.",respHeartbeatLogout:"心跳超时,被系统签出,请重新签入",holdTime:"Hold time",withdrawTips:"Message has been withdrawn"},name:{system:"System",agent:"Agent",user:"Visitor",robot:"Robot"},btn:{yes:"Yes",send:"Send",cancel:"Cancel",selectEmoji:"Select an emoji",selectImage:"Select an image",selectFile:"Select a file",selectVideo:"Select a video",selectVoice:"Select an audio",Transfer:"Transfer",Top:"Sticky on top",TopTips:"Topping up the dialogue",Unpink:"Un-top the dialogue",Hold:"Hanging",HoldTips:"The chats will not timeout after hold.",Unhold:"Unhanging",Evaluation:"Evaluation",EvaluationTips:"Evaluation",Reload:"Refresh",ReloadTips:"Reload the record",End:"End the chat",Close:"Close",CloseTips:"Are you sure to end the chat?",Unanswered:"No reply",LoggedElsewhere:"Your account is logged-in in other device.",searchKMS:"Search Knowledge Base",searchCommomWord:"Search Macros",CommomWord:"Macros",askRobot:"Search Chat Robot",withdraw:"Recall",withdrawn:"Recalled",Tag:"Mark",Untag:"Unmark",connect:"Access",Screenshot:"Screenshot",resend:"Resend",copy:"Copy",closeAllOfflineChat:"Close all ended chats",ReloadList:"Refresh list",quote:"Quote",invite:"Invite"},state:{ALL:"ALL",IDLE:"Online",BUSY:"Away",LOGOUT:"Sign out",LOGOFF:"Sign out",NO:"Sign out",Customize:"Customize",Online:"Online"},type:{ALL:"ALL",text:"Text",image:"Picture",emoji:"Emoji",video:"Video",voice:"Audio",file:"File",ywflow:"Link",news:"News",system:"System Message"},channelType:{1:"Web",2:"WeChat",3:"Weibo",4:"Voico",5:"APP",6:"Video",7:"WeCom",8:"Alipay",9:"Applet"},agent:{setting:{Setting:"Settings",Reset:"Reset the style settings",CloseEndedChats:"End all the replied chats",Font:"Font",Bg:"Background ",FontSize:"Font Size",FontColor:"Color",SortBy:"Sort the chat by",AccessTime:"Call Access Time",ResponseTime:"Answer Duration",Sound:"Sound",NewCustomer:"New Visitor",NewMsg:"New Message",Layout:"Interface Settings",SidebarBg:"Sidebar",ChatBg:"Chat Background",TopBg:"Chat on top",NewBg:"new chat",SidebarPersent:"Size of the right sidebar",AgentMsgBoxSetting:"Agent Message",UserMsgBoxSetting:"Visitor Message",HotkeyList:'\n            Shortcuts as below::<br/>\n            Chat list switching:<br/>\nCTRL+Q: Switch between ongoing/history chat list.<br/>\nChat switching:<br/>\n\nALT+1: Switch to the first chat on the onging chat list.<br/>\nALT+2: Switch to the second chat on the onging chat list.r<br/>\nALT+N: Switch to the Nth chat on the onging chat list.<br/>\n\nRight Side Bar switching.:<br/>\nSHIFT+1: Switch to the first page of right side bar.<br/>\nSHIFT+2: Switch to the second page of right side bar.<br/>\nSHIFT+3: Switch to the third page of right side bar.<br/>\n\nMessage operation:<br/>\nALT+Z: Select an emoticon to send<br/>\nALT+X: Select a picture to send.<br/>\nALT+C: Select a file to send.<br/>\nALT+V: Select an audio to send<br/>\nALT+B: Select a video to send<br/>\nSHIFT+A: Screenshot, Please download the plug-in and install <a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a><br/>\nALT+N: Send Satisfaction<br/>\nALT+M: Enable to lock the screen, and disable to unlock. <br/>\nCTRL+ENTER, ENTER: Send message<br/>\nToolbar operation:<br/>\nALT+T: Transfer chat<br/>\nALT+H: Chat history\n'},side:{online:"Ongoing",history:"History",queue:"Queue",browse:"Website",service:"Chat",delay:"Latency",limit:"Chat Limit",total:"Served",search:"Search visitor...",SmsInviteCode:"Send Invitation Code",Invite:"Invite",ChoiceChannel:"Please select channel",EnterMobile:"Please input the phone number"},main:{flush:"Refresh",static:"Sticky",close:"Close",cancel:"Cancel",Assist:"Agent Assistant",AssistTips:"Search the related answers of the customer's question when enabled",Lock:"Scroll Lock",LockTips:"Scroll Lock the history when enabled ",History:"History Record",Transfer:"Transfer chat",PrtSc:"Screenshot",PrtScSend:'Screenshot need install plugin <a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a>',Typing:"Typing",Uploading:"Uploading",Reactivate:"Reactivate the chat",ReactivateTips:"The chat is ended, click the button above to reactivate",leavingMsg:"Leave a message",autocomplete:"Autocomplete",autocompleteTips:"Robot and Common Phrases Input Guide"},third:{name:"Tripartite",invite:"Invite",canInvite:"Inviteable",isInvited:"Invited",copyLink:"Copy link",Evaluation:"Evaluation",end:"End",resend:"Resend",reInvite:"Re-Invite",add:"Add",enterNickname:"Please enter nickname",enterMobile:"Please enter mobile",enterWork:"Please enter unit",addAndInvite:"Add&Invite",enterSearch:"Search the nickname/mobile/unit...",inviteState:{1:"Inviting",2:"Connected",3:"Ended"}}},follow:{follow:"Follow up",followed:"Followed up",needFollow:"Do you need follow-up",followLevel:"Follow-up level",lv1:"General",lv2:"Important",lv3:"Very important",planFollowTime:"Plan follow-up time",followResult:"Follow up results",followBakup:"Follow-up notes",submit:"Submit"}},r=o,c={title:{onlineService:"在线客服"},transfer:{"000":"转移成功","001":"转移失败，该会话已被其他坐席接待","002":"转移失败，坐席不在线","003":"转移失败，该技能组下无空闲坐席","004":"转移失败，目标坐席拒绝","005":"等待目标坐席确认","006":"会话正在转移，请勿重复提交"},message:{selectService:"请选择所需要的服务",placeholder:"请输入内容...",msgRollback:"消息已撤回",consult:"我想咨询",screenshot:"发送截图",history:"以上是历史消息",leaveMsg:"请输入转移留言...",sessionOffline:"用户已离线",closeNoReplyChat:"您有未回复的客户，确认要关闭吗?",closeChat:"确认要关闭吗?",enterLimitCount:"请输入要修改的服务上限数",maxLimitCount:"最大值为",minLimitCount:"最小值为",success:"成功",fail:"失败",EnterInteger:"请输入整数",AgentLogout:"坐席未签入",CloseRepliedChats:"是否结束所有已回复的客户会话?未回复的用户将保留",LoggedElsewhere:"坐席已在其他地方签入",ResetStyle:"是否重置所有个性化配置?",Loging:"签入中..",LoginSucc:"签入成功",transferCommandSucc:"已发送转移请求",transferSucc:"转移成功",transferFail:"转移已拒绝",selectTransferTarget:"请选择目标坐席",satisfactionSurvey:"坐席发起满意度调查",sensitiveWordsCount:"内容包含敏感词:",typeLimit:"上传文件类型限制:",sizeLimit:"上传文件大小限制:",unread:"条消息未读",notEmpty:"不能为空",serviceError:"服务链接不可用,请检查网络",CloseAllEndChats1:"是否关闭",CloseAllEndChats2:"分钟没有交互的离线会话?",VideoTips:"部分视频编码格式可能无法支持在线播放，如遇到请尝试下载后查看。",respHeartbeatLogout:"心跳超时,被系统签出,请重新签入",queue:"心跳超时,被系统签出,请重新签入",holdTime:"挂起时间",withdrawTips:"消息已撤回"},name:{system:"系统",agent:"客服",user:"用户",robot:"机器人"},btn:{yes:"确定",send:"发送",cancel:"取消",selectEmoji:"选择表情",selectImage:"选择图片",selectFile:"选择文件",selectVideo:"选择视频",selectVoice:"选择音频",Top:"置顶",TopTips:"置顶会话",Transfer:"转移",Unpink:"取消置顶",Hold:"挂起",HoldTips:"挂起后，不做超时处理",Unhold:"取消挂起",Evaluation:"评价",EvaluationTips:"发起满意度调查",Reload:"刷新",ReloadTips:"重载会话记录",End:"结束会话",Close:"关闭",CloseTips:"是否关闭会话?未结束的会话会结束",Unanswered:"未回复",LoggedElsewhere:"坐席已在其他地方签入",searchKMS:"搜知识库",searchCommomWord:"搜常用语",CommomWord:"常用语",askRobot:"搜机器人",withdraw:"撤回",withdrawn:"已撤回",Tag:"标记",Untag:"取消标记",connect:"接入",invite:"邀请",Screenshot:"截图",resend:"重发",copy:"复制",closeAllOfflineChat:"关闭所有结束的会话",ReloadList:"刷新列表",quote:"引用"},state:{ALL:"全部",IDLE:"置闲",BUSY:"置忙",LOGOFF:"签出",LOGOUT:"签出",NO:"未签",Customize:"自定义",Online:"在线"},type:{ALL:"全部",text:"文本",image:"图片",emoji:"表情",video:"视频",voice:"音频",file:"文件",ywflow:"链接",news:"消息",system:"系统消息"},channelType:{1:"网页",2:"微信",3:"微博",4:"语音",5:"APP",6:"视频",7:"企业微信",8:"支付宝",9:"小程序"},agent:{setting:{Setting:"设置",Reset:"重置样式",CloseEndedChats:"结束所有已回复的客户会话",Font:"字体",Bg:"背景",FontSize:"字号",FontColor:"颜色",SortBy:"用户排序方式",AccessTime:"接入时间",ResponseTime:"响应时间",Sound:"声音提醒",NewCustomer:"新客户",NewMsg:"新消息",Layout:"界面设置",SidebarBg:"侧栏背景",ChatBg:"聊天背景",TopBg:"置顶背景",NewBg:"新用户背景",SidebarPersent:"侧栏百分比",AgentMsgBoxSetting:"坐席消息框设置",UserMsgBoxSetting:"用户消息框设置",HotkeyList:'全媒体支持的快捷键如下：\n            客户列表切换：<br/>\n            CTRL+Q：如果此时打开的客户列表，则切换到历史联系人；如果此时打开的是历史联系人，则切换到客户列表\n            客户切换：<br/>\n            ALT+1：定位到第一位客户<br/>\n            ALT+2：定位到第二位客户<br/>\n            ALT+N：定位到第N位客户<br/>\n            侧边栏切换：<br/>\n            SHIFT+1：切换到右边第一个侧边栏<br/>\n            SHIFT+2：切换到右边第M个侧边栏<br/>\n            SHIFT+3：切换到右边第N个侧边栏<br/>\n            发送栏操作：<br/>\n            ALT+Z：弹出表情选择框，再按一次，则关闭表情选择框<br/>\n            ALT+X：弹出图片发送选择框<br/>\n            ALT+C：弹出文件发送选择框<br/>\n            ALT+V：弹出语音文件发送选择框<br/>\n            ALT+B：弹出视频文件发送选择框<br/>\n            SHIFT+A：截图,需要先安装插件下载<a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a><br/>\n            ALT+N：弹出发送满意度提示框<br/>\n            ALT+M：锁定屏幕消息，再按一次，解除锁定<br/>\n            CTRL+ENTER、ENTER：发送消息<br/>\n            工具栏操作：<br/>\n            ALT+T：弹出消息转移选择框，再按一次，则关闭消息转移选择框<br/>\n            ALT+H：弹出消息记录框，再按一次，则关闭消息记录框'},side:{online:"在线",history:"历史",queue:"排队",browse:"网站",service:"聊天服务",delay:"延迟",limit:"服务上限",total:"今日接待",search:"搜索用户...",SmsInviteCode:"发送邀请码",Invite:"邀请",ChoiceChannel:"请选择渠道",EnterMobile:"请输入手机号码"},main:{flush:"刷新",static:"置顶",close:"关闭",cancel:"取消",Assist:"坐席辅助",AssistTips:"开启后,识别到关键字时将查找相关内容",Lock:"滚动锁定",LockTips:"锁定屏幕消息，再按一次，解除锁定",History:"历史记录",Transfer:"转移会话",PrtSc:"截图",PrtScSend:'需要先安装插件 <a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a>',Typing:"正在输入",Uploading:"上传中",Reactivate:"重新激活",ReactivateTips:"该会话已结束,点击按钮重新激活",leavingMsg:"留言",autocomplete:"输入引导",autocompleteTips:"查询机器人和常用语进行输入引导"},third:{name:"三方",invite:"邀请",canInvite:"可邀请",isInvited:"已邀请",copyLink:"复制邀请链接",Evaluation:"评价",end:"结束",resend:"重发",reInvite:"重新邀请",add:"新增",enterNickname:"请输入昵称",enterMobile:"请输入手机号码",enterWork:"请输入所属单位",addAndInvite:"新增并邀请",enterSearch:"请输入昵称/手机/单位进行搜索...",inviteState:{1:"邀请中",2:"已接入",3:"已结束"}}},follow:{follow:"跟进",followed:"已跟进",needFollow:"是否需要后续跟进",followLevel:"跟进等级",lv1:"一般",lv2:"重要",lv3:"非常重要",planFollowTime:"计划跟进时间",followResult:"跟进结果",followBakup:"跟进备注",submit:"提交"}},l=c,d={title:{onlineService:"在线客服"},transfer:{"000":"転送 成功","001":"転送に失敗しました, 現在のチャットは終了しました","002":"転送に失敗しました,エージェントはオンラインではありません","003":"転送に失敗しました,このスキルグループには空席はありません","004":"転送に失敗しました,転送が拒否されました","005":"目標の座席確認待ち","006":"セッションは転送中です。コミットを繰り返さないでください"},message:{selectService:"サービス内容を選択する",placeholder:"内容入力...",msgRollback:"撤回",consult:"問合せ希望",screenshot:"スクリーンショットを送る",history:"メッセージ送信履歴",leaveMsg:"伝言メッセージ入力...",sessionOffline:"顧客退室",closeNoReplyChat:"対応中のチャットがあります、閉じますか?",closeChat:"閉じる?",enterLimitCount:"対応可能な人数を入力する",maxLimitCount:"最大値",minLimitCount:"最小値",success:"成功",fail:"失敗",EnterInteger:"数値を入力する",AgentLogout:"サインインしていない",CloseRepliedChats:"対応済のチャットを終了にしますか？未対応のチャットは保留します",LoggedElsewhere:"このアカウントはすでにサインインしています",ResetStyle:"個人用設定をリセットしますか?",Loging:"ログイン..",LoginSucc:"ログインに成功した",transferCommandSucc:"転送リクエストが送信されました",transferSucc:"正常に転送されました",transferFail:"転送が拒否されました",selectTransferTarget:"対象の座席を選択してください",satisfactionSurvey:"エージェントが満足度調査を開始します",sensitiveWordsCount:"コンテンツに敏感な言葉が含まれている:",typeLimit:"アップロードファイルタイプの制限:",sizeLimit:"アップロードファイルのサイズ制限:",unread:"新しいニュース",notEmpty:"する必要がある",serviceError:"Service error,ネットワークを確認してください",CloseAllEndChats1:"終了したセッションを",CloseAllEndChats2:"分間対話せずに閉じますか？",VideoTips:"Please download and play the video if the video cannot be played in the system.",holdTime:"保留時間",withdrawTips:"メッセージが撤回されました"},name:{system:"システム",agent:"サポート",user:"顧客",robot:"ロボット"},btn:{yes:"はい",send:"送信",cancel:"取消",selectEmoji:"絵文字を選択する",selectImage:"画像を選択する",selectFile:"ファイルを選択する",selectVideo:"動画を選択する",selectVoice:"音源のデータを選択する",Top:"ピン留め",TopTips:"トークルームがピン留めされる",Transfer:"転送",Unpink:"ピン留めを解除する",Hold:"保留",HoldTips:"保留中のチャットは、タイムアウト処理しない",Unhold:"保留を取り消す",Evaluation:"満足度調査",EvaluationTips:"満足度調査",Reload:"更新",ReloadTips:"再読み込み",End:"終わり",Close:"閉じる",CloseTips:"閉じますか？対応中のチャットは終了にまります",Unanswered:"未返信",LoggedElsewhere:"このアカウントはすでにサインインしています",searchKMS:"ナレッジベースを検索する",searchCommomWord:"テンプレを検索する",CommomWord:"テンプレ",askRobot:"ロボットを検索する",withdraw:"撤回",withdrawn:"撤回済",Tag:"标记",Untag:"取消标记",connect:"アクセス",Screenshot:"スクリーンショット",resend:"再送",copy:"コピー",closeAllOfflineChat:"終了したチャットをすべて閉じる",ReloadList:"更新リスト",quote:"引用",invite:"邀请"},state:{ALL:"全部",IDLE:"オンライン",BUSY:"ビジー",LOGOFF:"サインアウト",LOGOUT:"サインアウト",NO:"サインアウト",Customize:"カスタマイズ",Online:"オンライン"},type:{ALL:"全部",text:"テキスト",image:"画像",emoji:"絵文字",video:"動画",voice:"音源のデータ",file:"ファイル",ywflow:"Link",news:"News",system:"システムメッセージ"},channelType:{1:"ウェブページ",2:"WeChat",3:"Weibo",4:"ボイス",5:"APP",6:"動画",7:"WeCom",8:"Alipay",9:"アプレット"},agent:{setting:{Setting:"設定",Reset:"スタイル設定リセット",CloseEndedChats:"返信済のチャットすべてを終了します",Font:"フォント",Bg:"背景",FontSize:"フォントサイズ",FontColor:"色",SortBy:"顧客一覧の並び順",AccessTime:"アクセス時間",ResponseTime:"返信時間",Sound:"サウンド設定",NewCustomer:"新規顧客",NewMsg:"新しいニュース",Layout:"画面設定",SidebarBg:"サイドバー背景",ChatBg:"チャット画面背景",TopBg:"背景を一番上に固定する",NewBg:"新規チャット背景",SidebarPersent:"サイドバーサイズ",AgentMsgBoxSetting:"メッセージ欄設定（エージェント）",UserMsgBoxSetting:"メッセージ欄設定（顧客）",HotkeyList:'ホットキーの説明です<br/>\n            リスト切替：<br/>\n            CTRL+Q：対応中/対応済リストの切り替え<br/>\n\n            顧客切替：<br/>\n            ALT+1：一番目の顧客に切り替え<br/>\n            ALT+2：二番目の顧客に切り替え<br/>\n            ALT+N：N番目の顧客に切り替え <br/>\n\n            サイドバーに切り替え：<br/>\n            SHIFT+1：右側一番目のサイドバーに切り替え<br/>\n            SHIFT+2：右側二番目のサイドバーに切り替え<br/>\n            SHIFT+3：右側三番目のサイドバーに切り替え<br/>\n\n            メッセージ操作：<br/>\n            ALT+Z：絵文字を送信する<br/>\n            ALT+X：写真を送信する<br/>\n            ALT+C：ファイルを送信する<br/>\n            ALT+V：音源のデータを送信する<br/>\n            ALT+B：動画を送信する<br/>\n            SHIFT+A：スクリーンショット　ソフトをインストール必要があります <a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a><br/>\n            ALT+N：満足度調査を送信する<br/>\n            ALT+M：画面ロック/ロック解除<br/>\n            CTRL+ENTER、ENTER：メッセージを送信する<br/>\n            ツールバー設定：<br/>\n            ALT+T：転送/キャンセル<br/>\n            ALT+H：履歴表示/キャンセル\n            '},side:{online:"オンライン",history:"履歴",queue:"対応待ち",browse:"网站",service:"チャット",delay:"遅延",limit:"サービスオンライン",total:"今日の対応",search:"顧客検索...",SmsInviteCode:"招待コードを送信する",Invite:"招待",ChoiceChannel:"チャンネルを選択する",EnterMobile:"電話番号入力"},main:{flush:"更新",static:"ピン留め",close:"閉じる",cancel:"取り消す",Assist:"エージェント　アシスタント",AssistTips:"チェック入れると、キーワードが認識されたら関連コンテンツを検索します",Lock:"スクロールロック",LockTips:"スクロールロック",History:"履歴",Transfer:"転送",PrtSc:"スクリーンショット",PrtScSend:'スクリーンショット　ソフトをインストール必要があります <a target="_blank" href="../static/download/CaptureInstall.exe">windows</a> <a target="_blank" href="../static/download/CaptureInstall.dmg">MacOS</a>',Typing:"入力中",Uploading:"アップロード中",Reactivate:"チャットを再開する",ReactivateTips:"終了。再開ボタンをクリックし、チャットを再開する",leavingMsg:"メッセージ"},third:{name:"三者",invite:"招待",canInvite:"招待可能",isInvited:"招待",copyLink:"招待リンクをコピーする",Evaluation:"満足度調査",end:"終了",resend:"再送",reInvite:"再招待",add:"新着",enterNickname:"ニックネームを入力してください",enterMobile:"電話番号を入力してください",enterWork:"所属を入力してください",addAndInvite:"新着-招待",enterSearch:"ニックネーム/モバイル/所属の検索...",inviteState:{1:"招待",2:"受信",3:"終了"}}},follow:{follow:"ファローアップ",followed:"フォローアップ",needFollow:"フォローアップが必要ですか",followLevel:"フォローアップレベル",lv1:"一般",lv2:"重要",lv3:"非常に重要",planFollowTime:"フォローアップ時間を計画する",followResult:"フォローアップ結果",followBakup:"フォローアップノート",submit:"送信"}},g=d,u={en:r,zh:l,cn:l,ja:g},m=localStorage&&localStorage["MULTI-LANG-KEY"],f=navigator.appName;m?n=m.toLocaleLowerCase():(n="Netscape"==f?navigator.language:navigator.userLanguage,n=n.substr(0,2)),console.log("浏览器语言",n,u,u[n]),a["default"].use(i["a"]);var h=new i["a"]({locale:u[n]?n:"en",messages:u});e["a"]=h},"0dbd":function(t,e,s){},"0e8c":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("virtual-list",{ref:"vsl",staticStyle:{height:"100%","overflow-y":"auto"},attrs:{"extra-props":{history:t.history},"data-key":"serialId","data-sources":t.msgList,"data-component":t.itemComponent},on:{totop:t.onTotop}})},a=[],i=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("Msg",{attrs:{msgOrigin:t.source,sessionId:t.source.sessionId,"msg-item":t.source,"msg-type":t.source.msgType,sender:t.source.sender||"system",history:t.history}})},o=[],r=(s("a9e3"),s("a84e")),c={props:{index:{type:Number},source:{type:Object,default:function(){return{}}},history:history},components:{Msg:r["a"]}},l=c,d=s("2877"),g=Object(d["a"])(l,i,o,!1,null,null,null),u=g.exports,m=s("89c1"),f=s.n(m),h={props:{msgList:{type:Array,default:function(){return[]}},isRecord:{type:Boolean,default:function(){return!1}},history:history},components:{MsgVirtual:u,"virtual-list":f.a},data:function(){return{itemComponent:u}},mounted:function(){},computed:{showMsgList:function(){var t=this.msgList;return t}},methods:{toBottom:function(){this.$refs.vsl&&this.$refs.vsl.scrollToBottom()},onTotop:function(){this.isRecord||this.$parent.loadMessage()}}},p=h,v=Object(d["a"])(p,n,a,!1,null,null,null);e["a"]=v.exports},1443:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("a-tabs",{staticStyle:{width:"400px"},attrs:{"default-active-key":"1",tabPosition:"bottom"}},[s("a-tab-pane",{key:"1",attrs:{tab:t.$t("type.emoji")}},[s("div",{staticClass:"emoji-list",staticStyle:{width:"400px",height:"400px",overflow:"auto"}},[t._l(t.emojiList,(function(e,n){return[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e,expression:"item"}],staticClass:"emoji-slt",attrs:{title:n,fit:"cover"},on:{click:function(e){return e.stopPropagation(),t.selectEmoji(n)}}})]}))],2)]),s("a-tab-pane",{key:"2",attrs:{tab:t.$t("type.image")}},[s("div",{staticClass:"custom-emoji-list",staticStyle:{width:"400px",height:"400px",overflow:"auto"}},[t._l(t.customList,(function(e,n){return[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.picUrl,expression:"item.picUrl"}],staticClass:"emoji-slt",staticStyle:{width:"40px",height:"40px"},attrs:{title:e.name,fit:"cover"},on:{click:function(s){return s.stopPropagation(),t.selectImg(e.picUrl)}}})]}))],2)])],1)},a=[],i=s("b8ac"),o={props:["channelKey","skillGroupId"],data:function(){return{active:0,emojiList:{},customList:[]}},mounted:function(){this.emojiList=i["a"].list,this.loadImg()},methods:{selectEmoji:function(t){this.$emit("select",t)},selectImg:function(t){this.$emit("selectImg",t)},loadImg:function(){var t=this,e={channelKey:this.channelKey,skillGroupId:this.skillGroupId};this.$api.agentPic(e).then((function(e){"000"==e.respCode&&e.respData&&(t.customList=e.respData)}))}}},r=o,c=(s("a47e"),s("2877")),l=Object(c["a"])(r,n,a,!1,null,"feb07052",null);e["a"]=l.exports},"1a0d":function(t,e,s){"use strict";var n=s("d7a7"),a=s.n(n);a.a},"1e65":function(t,e,s){"use strict";var n=s("2b0e");e["a"]=new n["default"]},"1ed0":function(t,e,s){},"22b6":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.src?s("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{id:t.id,src:t.src,frameborder:"0"}}):s("div",{})},a=[],i={props:["id","src","pageId"],data:function(){return{pageUrl:""}},beforeDestroy:function(){},watch:{src:function(t,e){console.log("new src",t,e)}}},o=i,r=s("2877"),c=Object(r["a"])(o,n,a,!1,null,null,null);e["a"]=c.exports},2395:function(t,e,s){},"24e8":function(t,e,s){},"27fb":function(t,e,s){"use strict";var n=s("4bb5"),a=s.n(n);a.a},"2c00":function(t,e,s){"use strict";s("4de4"),s("c975"),s("a9e3"),s("d3b7"),s("ac1f"),s("3ca3"),s("1276"),s("ddb0"),s("2b3d");var n=function(t){document.addEventListener("paste",(function(e){if(t.paste&&(e.clipboardData||e.originalEvent)){var s=e.clipboardData||e.originalEvent.clipboardData;if(s.items){var n=s.items,a=n.length,i=null;!0;for(var o=0;o<a;o++)-1!==n[o].type.indexOf("image")&&(i=n[o].getAsFile());if(null!==i){var r=URL.createObjectURL(i);t.paste&&t.paste(i,r,!0)}}}})),hotkeys("ctrl+q",{keyup:!0,keydown:!1},(function(t){t.preventDefault();var e=$(".ant-menu-item.ant-menu-item-selected").index();3==e?$(".ant-menu-item").eq(0).click():$(".ant-menu-item").eq(1).click()})),hotkeys("alt+z",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-select-emoji").click()})),hotkeys("alt+x",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-select-img .ant-upload").click()})),hotkeys("alt+c",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-select-file .ant-upload").click()})),hotkeys("alt+v",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-select-voice .ant-upload").click()})),hotkeys("alt+b",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-select-video .ant-upload").click()})),hotkeys("alt+n",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-static i").click()})),hotkeys("alt+m",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-lock").click(),$(".activeSession .toolbar-left").click()})),hotkeys("shift+x",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),"ant-input"==t.target.className||(t.preventDefault(),$(".activeSession .chat-tool-transfer i").click())})),hotkeys("alt+t",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),$(".activeSession .chat-tool-transfer i").click()})),hotkeys("alt+h",(function(t){t.preventDefault(),$(".activeSession .chat-history").click()})),hotkeys("alt+1,alt+2,alt+3,alt+4,alt+5,alt+6,alt+7,alt+8,alt+9,alt+0",{keyup:!0,keydown:!1},(function(t,e){t.preventDefault();var s=e.key.split("+")[1];return s="0"==s?9:Number(s)-1,$(".list-useritem .userItem").eq(s).click(),n(),!1})),hotkeys("shift+1,shift+2,shift+3,shift+4,shift+5,shift+6,shift+7,shift+8,shift+9,shift+0",{keyup:!0,keydown:!1},(function(t,e){if("ant-input"!=t.target.className){var s=e.key.split("+")[1];return s="0"==s?9:Number(s)-1,$(".activeSession .chat-box-sidebar .ant-tabs-tab").eq(s).click(),!1}})),hotkeys("shift+a",{keyup:!0,keydown:!1},(function(t){"ant-input"==t.target.className||(t.preventDefault(),$(".activeSession .chat-prtsc").click())}));var e=null,s=0;function n(){webChat.$nextTick((function(t){$(".activeSession textarea").focus()}))}function a(){var t=$(".list-useritem .userItem.active").index(),e=$(".list-useritem .userItem").length,s=0;-1==t||(t+1<e?s=t+1:t+1==e&&(s=0)),$(".list-useritem .userItem").eq(s).click(),$(".list-useritem .userItem").eq(s)[0].scrollIntoView()}function i(){var t=$(".list-useritem .userItem").length;t>0&&($(".list-useritem .userItem").eq(t-1).click(),$(".list-useritem .userItem").eq(t-1)[0].scrollIntoView(),n())}hotkeys("esc",{keyup:!0,keydown:!1},(function(t){if("undefined"==typeof webIM_ex_config||!1!==webIM_ex_config.escClose){var n=(new Date).getTime();!n||n-e>200?(s=1,e=n):n-e<200&&1==s&&(s=0,e=null,console.log("ESC*2,auto close"),"undefined"!=typeof webChat&&webChat.escCloseChat())}})),hotkeys("ctrl+esc",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation(),"undefined"!=typeof webChat&&webChat.escCloseChat()})),hotkeys("ctrl+f5",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation(),window.sessionStorage&&sessionStorage.setItem("mediabAgengLastState","F5"),location.reload()})),hotkeys("ctrl+up",{keyup:!0,keydown:!1},(function(t){console.log("触发ctrl+up"),t.preventDefault(),t.stopPropagation();var e=$(".list-useritem .userItem.active").index(),s=$(".list-useritem .userItem"),a=0;e>=1?a=e-1:0==e&&(a=s.length-1),$(".list-useritem .userItem").eq(a).click(),$(".list-useritem .userItem").eq(a)[0].scrollIntoView(),n()})),hotkeys("ctrl+Down",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation(),a()})),hotkeys("shift+ctrl+Down",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation(),i()})),hotkeys("shift+ctrl+Up",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation();var e=$(".list-useritem .userItem");e.length>0&&($(".list-useritem .userItem").eq(0).click(),$(".list-useritem .userItem").eq(0)[0].scrollIntoView(),n())})),hotkeys("alt+`",{keyup:!0,keydown:!1},(function(t){t.preventDefault(),t.stopPropagation(),n(),$(".activeSession .chat-box").data("sessionId")&&testChatBox.$refs["session-"+$(".activeSession .chat-box").data("sessionId")][0].$refs.acbox.focus()})),hotkeys.filter=function(t){return!0}};e["a"]={init:n}},"2d9c":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{domProps:{innerHTML:t._s(t.setStyle(t.style))}})},a=[],i=(s("99af"),{data:function(){return{}},methods:{setStyle:function(t){var e="";return t.userBgColor&&(e+="\n                .chat-box-msg .chat-box-msg-info .chat-box-msg-detail {\n                    background-color: ".concat(t.userBgColor,";\n                }\n                .chat-box-msg .chat-box-msg-info .chat-box-msg-detail:after{\n                    color: ").concat(t.userBgColor,";\n                }")),(t.userColor||t.userFontSize)&&(e+="\n                .chat-box-msg .chat-box-msg-info .chat-box-msg-detail,\n                .chat-box-msg .chat-box-msg-info .chat-box-msg-detail pre{color:".concat(t.userColor,";font-size:").concat(t.userFontSize,"px}\n                ")),t.agentBgColor&&(e+="\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail,\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail {\n                    background-color: ".concat(t.agentBgColor,";\n                }\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail:after,\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail:after {\n                    color: ").concat(t.agentBgColor,";\n                }")),(t.agentColor||t.agentFontSize)&&(e+="\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail,\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail,\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail pre,\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail pre{\n                    color: ".concat(t.agentColor,";\n                    font-size:").concat(t.agentFontSize,"px;\n                }\n                ")),t.stickColor&&(e+=".userItem.stick{\n                    background-color: ".concat(t.stickColor,"!important\n                }")),t.isNewColor&&(e+=".userItem.isNew,.tag-horizontal{\n                    background-color: ".concat(t.isNewColor,"!important\n                }\n                .tag-horizontal:after{color: ").concat(t.isNewColor,"!important}\n                ")),t.sideBg&&(e+=".webIM-box .webIM-sidebar{\n                    background: ".concat(t.sideBg,"\n                }")),t.chatBg&&(e+=".chat-box .chat-box-main .chat-box-content,.chat-box-header~.flex-item{\n                    background: ".concat(t.chatBg,"\n                }")),t.sideWidth&&(e+=".chat-box .chat-box-sidebar{width:".concat(t.sideWidth,"%}"),e+=".clipboard-box{right:".concat(t.sideWidth,"%}"),e+=".ex-side-pages{width: calc( (100% - 320px) * .".concat(t.sideWidth," )!important;")),t.footerHeight&&(e+="\n                .chat-box .chat-box-main .chat-box-footer{\n                    height:".concat(t.footerHeight,"px;\n                }\n\n                ")),'<style id="webimcss">'.concat(e,"</style>")}},computed:{style:function(){return this.$store.getters.style}}}),o=i,r=s("2877"),c=Object(r["a"])(o,n,a,!1,null,"098bd8cb",null);e["a"]=c.exports},3228:function(t,e,s){"use strict";s("b0c0"),s("d3b7"),s("ac1f"),s("466d"),s("1276"),s("5cc6"),s("9a8c"),s("a975"),s("735e"),s("c1ac"),s("d139"),s("3a7b"),s("d5d6"),s("82f8"),s("e91f"),s("60bd"),s("5f96"),s("3280"),s("3fcc"),s("ca91"),s("25a1"),s("cd26"),s("3c5d"),s("2954"),s("649e"),s("219c"),s("170b"),s("b39a"),s("72f7");var n=s("ade3"),a=s("5e9c"),i="undefined"!=typeof ctxBasePath?ctxBasePath:"/yc-media",o=$("#ccbarBasePath").val()||"/yc-ccbar",r=a["a"].GetQueryObj()||{},c=r.agentId||localStorage.getItem("userAccount")||"",l=r.entId||localStorage.getItem("entId")||localStorage.getItem("userEntId")||"",d=r.busiOrderId||localStorage.busiOrderId||"",g={entId:l,userAcc:c,agentId:c,busiOrderId:d},u=function(t,e,s){return e._msgId=a["a"].getSerialId(),a["a"].post({url:t,data:e},s)},m=function(t,e,s){return e._msgId=a["a"].getSerialId(),a["a"].request({url:t,data:e},s)},f=function(t){var e="/yc-mediagw/agentOpMenu",s={},n=Object.assign({},g,s,t);return u(e,n)},h=function(t){var e="/yc-mediagw/srhPhrase",s={},n=Object.assign({},g,s,t);return u(e,n,{timeout:5e3})},p=function(t){var e="/yc-mediagw/userPhrase",s={busiType:"01"},n=Object.assign({},g,s,t);return u(e,n,{timeout:5e3})},v=function(t){var e="/yc-mediagw/robotAgentAsk",s={},n=Object.assign({},g,s,t);return u(e,n)},y=function(t){},b=function(t){var e="/yc-mediagw/srhSensitive",s={},n=Object.assign({},g,s,t);return u(e,n,{timeout:5e3})},w=function(t){var e="/yc-mediagw/agentPic",s={type:2,command:"mideaPicDir",sender:"MIDEA_GW",password:"YQ_85521717"},n=Object.assign({},g,s,t);return u(e,n)},S=function(t,e){var s=i+"/servlet/upload",n=new FormData;try{e||(t=C(t))}catch(c){}var o="array"==typeof t?t[0]:t;if(!o.name){o.name=a["a"].uuid()+".jpg";try{o=new window.File([o],o.name,{type:o.type})}catch(c){}}n.append("filedata",o),n.append("sessionId",testChatBox.activeChat);var r={headers:{"Content-Type":"multipart/form-data"}};return a["a"].post({url:s,data:n},r)};function C(t){var e=t.split(","),s=e[0].match(/:(.*?);/)[1],n=atob(e[1]),a=n.length,i=new Uint8Array(a);while(a--)i[a]=n.charCodeAt(a);return new Blob([i],{type:s})}function k(t){var e=Object(n["a"])({agentId:g.agentId,busiOrderId:g.busiOrderId,entId:g.entId},"entId",l),s=$.param(e);return u(o+"/mediaEvent?action=getMyChannels&"+s,e)}function I(t){var e={agentId:g.agentId,busiOrderId:g.busiOrderId,entId:g.entId},s=Object.assign({},e,t),n=$.param(s);return u("/yc-mediagw/mediaApi?action=generateCode&"+n,s)}function T(t){return u(o+"/mediaEvent?action=searchFollow",t)}function _(t){return u(o+"/mediaEvent?action=saveFollow",t)}function x(){return g}function L(t){return u(o+"/mediaEvent?action=tag",t)}function O(t){return a["a"].request({url:o+"/mediaEvent?action=GetChatStat",data:{cmdJson:JSON.stringify(t)}})}function j(){return u("/yc-mediabar/servlet/mediaEvent?action=treeOrderAi",{})}var A={};function N(t,e){return new Promise((function(s,n){if(A[t])return new Promise((function(s,n){s(A[t]),e&&e(A[t])}));u("/yc-mediagw/channelConfig",{channelKey:t,configList:JSON.stringify(["MAX_SEND_MSG_SWITCH","SEATROBOT_FLAG"])}).then((function(n){"000"==n.respCode&&(A[t]=n.data,s(A[t]),e&&e(A[t]))}))}))}e["a"]={agentOpMenu:f,srhPhrase:h,userPhrase:p,robotAgentAsk:v,findKnowledge:y,srhSensitive:b,uploadImage:S,agentPic:w,getAgentGroup:k,inviteCode:I,searchFollow:T,saveFollow:_,post:u,get:m,getCommon:x,setTag:L,getChatStat:O,treeOrderAi:j,channelConfig:N}},"3dfd":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"app"}},[s("a-locale-provider",{attrs:{locale:t.currentLang}},[s("router-view")],1)],1)},a=[],i=s("677e"),o=s.n(i),r=s("766a"),c=s.n(r),l=s("c1df"),d=s.n(l),g=(s("5c3a"),{data:function(){return{currentLang:c.a}},mounted:function(){var t=localStorage&&localStorage["MULTI-LANG-KEY"];t&&"CN"!=t||(this.currentLang=o.a,d.a.locale("zh-cn")),document.getElementById("loadingPanel").style.display="none",$("body").on("click",'.chat-box-msg-detail[data-msg-type="richText"] .bubble img',(function(t){$(this).attr("data-enable")||($(this).attr("data-enable","1"),$(this).attr("data-url",$(this).attr("src")),$(this)[0].click())}))}}),u=g,m=(s("7c55"),s("2877")),f=Object(m["a"])(u,n,a,!1,null,null,null);e["a"]=f.exports},4360:function(t,e,s){"use strict";var n=s("2b0e"),a=s("2f62"),i=s("0e44");n["default"].use(a["a"]),e["a"]=new a["a"].Store({state:{style:{agentColor:"",agentFontSize:"",agentBgColor:"",userColor:"",userFontSize:"",userBgColor:"",curUserBgColor:"",stickColor:"",isNewColor:"",sideBg:"",chatBg:"",sideWidth:"30",footerHeight:""},stick:{},tag:{},listType:"begin",listSort:"reverse",isHelper:!1,isAutocomplet:!0,enterKey:1,audio:{connectMsg:!0,newMsg:!0}},getters:{style:function(t){return t.style},enterKey:function(t){return t.enterKey},soundNotify:function(t){return t.audio},stickList:function(t){return t.stick},tagList:function(t){return t.tag},listType:function(t){return t.listType},isStick:function(t,e){return function(e){return t.stick[e]||!1}},isTag:function(t,e){return function(e){return t.tag[e]||!1}},isHelper:function(t){return t.isHelper||!1},isAutocompleteClose:function(t){return t.isAutocomplet},listSort:function(t){return t.listSort}},mutations:{setStyle:function(t,e){var s=Object.assign({},t.style);s[e.key]=e.val,t.style=s},setAudio:function(t,e){var s=Object.assign({},t.audio);s[e.key]=e.val,t.audio=s},setSendKey:function(t,e){t.enterKey=e},setStick:function(t,e){var s=Object.assign({},t.stick);s[e.key]=e.val,t.stick=s},setTag:function(t,e){var s=Object.assign({},t.tag);s[e.key]=e.val,t.tag=s},setListType:function(t,e){t.listType=e},setListSort:function(t,e){t.listSort=e},resetStyle:function(t,e){if(e)t.style[e]="";else for(var s in t.style)t.style[s]="","sideWidth"==s&&(t.style[s]=30)},setHelper:function(t,e){t.isHelper=e},setAutoCompleteClose:function(t,e){t.isAutocomplet=e}},actions:{},modules:{},plugins:[Object(i["a"])()]})},4678:function(t,e,s){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df4","./fa.js":"8df4","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b4","./gd.js":"f6b4","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c0","./lv.js":"b97c0","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function a(t){var e=i(t);return s(e)}function i(t){if(!s.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}a.keys=function(){return Object.keys(n)},a.resolve=i,t.exports=a,a.id="4678"},"4bb5":function(t,e,s){},"4dbf":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"setting-box"},[s("a-btn"),s("a-form-model",[s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.SortBy"))+"  "),s("span",[s("span",{class:{activeSort:"reverse"==t.listSort},on:{click:function(e){return t.setListSort("reverse")}}},[t._v("降序")]),s("span",{class:{activeSort:"normal"==t.listSort},on:{click:function(e){return t.setListSort("normal")}}},[t._v("升序")])])]),s("a-radio-group",{attrs:{"default-value":t.listType,"button-style":"solid"}},[s("a-radio-button",{attrs:{value:"begin"},on:{click:function(e){return t.updateListType("begin")}}},[t._v(" "+t._s(t.$t("agent.setting.AccessTime"))+" ")]),s("a-radio-button",{attrs:{value:"reply"},on:{click:function(e){return t.updateListType("reply")}}},[t._v(" "+t._s(t.$t("agent.setting.ResponseTime"))+" ")])],1)],1),s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.Sound"))+" ")]),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.NewCustomer")))]),s("a-switch",{on:{change:t.onChangeConnect},model:{value:t.soundNotify.connectMsg,callback:function(e){t.$set(t.soundNotify,"connectMsg",e)},expression:"soundNotify.connectMsg"}}),s("span",{staticClass:"ml-10 mr-10"},[t._v(t._s(t.$t("agent.setting.NewMsg")))]),s("a-switch",{on:{change:t.onChangeNewMsg},model:{value:t.soundNotify.newMsg,callback:function(e){t.$set(t.soundNotify,"newMsg",e)},expression:"soundNotify.newMsg"}})],1),s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.Layout"))+"  ")]),s("p",{staticStyle:{display:"none"}},[s("span",{staticClass:"mr-10"},[t._v("显示标签页")]),s("a-switch")],1),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.SidebarBg")))]),s("ColorPicker",{attrs:{color:t.style.sideBg},on:{change:t.onChangeSideBgColor}}),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.ChatBg")))]),s("ColorPicker",{attrs:{color:t.style.chatBg},on:{change:t.onChangeChatBg}}),s("br"),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.TopBg")))]),s("ColorPicker",{attrs:{color:t.style.stickColor},on:{change:t.onChangeStickColor}}),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.NewBg")))]),s("ColorPicker",{attrs:{color:t.style.isNewColor},on:{change:t.onChangeNewUserColor}})],1),s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.SidebarPersent"))+"  ")]),s("a-slider",{attrs:{min:20,max:70},on:{change:t.onChangeSide},model:{value:t.style.sideWidth,callback:function(e){t.$set(t.style,"sideWidth",e)},expression:"style.sideWidth"}})],1),s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.AgentMsgBoxSetting"))+"  ")]),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.Font")))]),s("a-select",{staticClass:"mr-10",staticStyle:{width:"70px"},attrs:{"default-value":""},on:{change:t.onChangeAgentFontSize},model:{value:t.style.agentFontSize,callback:function(e){t.$set(t.style,"agentFontSize",e)},expression:"style.agentFontSize"}},[s("a-select-option",{attrs:{value:""}},[t._v(" -- ")]),t._l([12,13,14,15,16,17,18,19,20],(function(e,n){return s("a-select-option",{key:n,attrs:{value:e}},[t._v(t._s(e))])}))],2),s("span",{staticClass:"ml-10 mr-10"},[t._v(t._s(t.$t("agent.setting.FontSize")))]),s("ColorPicker",{attrs:{icon:"font-colors",color:t.style.agentColor},on:{change:t.onChangeAgentColor}}),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.Bg")))]),s("ColorPicker",{attrs:{color:t.style.agentBgColor},on:{change:t.onChangeAgentBgColor}})],1),s("div",{staticClass:"chat-box-msg",staticStyle:{padding:"10px 20px"},attrs:{"data-sender":"agent"}},[s("div",{staticClass:"chat-box-msg-info"},[s("div",{staticClass:"chat-box-msg-detail",attrs:{"data-msg-type":"text"}},[s("div",{staticClass:"msg-detail"},[s("div",{staticClass:"bubble"},[s("pre",[t._v("Aa123一二")])])])])])]),s("a-form-item",[s("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("agent.setting.UserMsgBoxSetting"))+"  ")]),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.Font")))]),s("a-select",{staticStyle:{width:"70px"},attrs:{"default-value":""},on:{change:t.onChangeUserFontSize},model:{value:t.style.userFontSize,callback:function(e){t.$set(t.style,"userFontSize",e)},expression:"style.userFontSize"}},[s("a-select-option",{attrs:{value:""}},[t._v(" -- ")]),t._l([12,13,14,15,16,17,18,19,20],(function(e,n){return s("a-select-option",{key:n,attrs:{value:e}},[t._v(t._s(e))])}))],2),s("span",{staticClass:"ml-10 mr-10"},[t._v(t._s(t.$t("agent.setting.FontSize")))]),s("ColorPicker",{attrs:{icon:"font-colors",color:t.style.userColor},on:{change:t.onChangeUserColor}}),s("span",{staticClass:"mr-10"},[t._v(t._s(t.$t("agent.setting.Bg")))]),s("ColorPicker",{attrs:{color:t.style.userBgColor},on:{change:t.onChangeUserBgColor}})],1),s("div",{staticClass:"chat-box-msg",staticStyle:{padding:"10px 20px"},attrs:{"data-sender":"user"}},[s("div",{staticClass:"chat-box-msg-info"},[s("div",{staticClass:"chat-box-msg-detail",attrs:{"data-msg-type":"text"}},[s("div",{staticClass:"msg-detail"},[s("div",{staticClass:"bubble"},[s("pre",[t._v("Aa123一二")])])])])])])],1),s("div",{staticClass:"setting-box"},[s("div",{staticStyle:{"font-size":"12px"},domProps:{innerHTML:t._s(t.$t("agent.setting.HotkeyList"))}})])],1)},a=[],i=s("c345"),o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("a-popover",{staticStyle:{"margin-left":"-5px"},attrs:{placement:"bottom",trigger:"click"}},[s("sketch-picker",{attrs:{slot:"content"},on:{input:t.onChange},slot:"content",model:{value:t.pickerColor,callback:function(e){t.pickerColor=e},expression:"pickerColor"}}),s("a-icon",{style:t.color?"color:"+t.color:"",attrs:{type:t.icon||"bg-colors"}})],1)},r=[],c={components:{"sketch-picker":i["Sketch"]},props:["color","icon"],data:function(){return{pickerColor:""}},mounted:function(){this.pickerColor=this.color},methods:{onChange:function(t){this.$emit("change",t.hex)}},watch:{}},l=c,d=(s("bb83"),s("2877")),g=Object(d["a"])(l,o,r,!1,null,"1f245042",null),u=g.exports,m={components:{"sketch-picker":i["Sketch"],ColorPicker:u},data:function(){return{}},methods:{onChangeConnect:function(t){this.$store.commit("setAudio",{key:"connectMsg",val:t})},onChangeNewMsg:function(t){this.$store.commit("setAudio",{key:"newMsg",val:t})},onChangeAgentFontSize:function(t){this.setStyle("agentFontSize",t)},onChangeUserFontSize:function(t){this.setStyle("userFontSize",t)},onChangeAgentColor:function(t){this.setStyle("agentColor",t)},onChangeUserColor:function(t){this.setStyle("userColor",t)},onChangeAgentBgColor:function(t){this.setStyle("agentBgColor",t)},onChangeUserBgColor:function(t){this.setStyle("userBgColor",t)},onChangeSideBgColor:function(t){this.setStyle("sideBg",t)},onChangeChatBg:function(t){this.setStyle("chatBg",t)},onChangeStickColor:function(t){this.setStyle("stickColor",t)},onChangeNewUserColor:function(t){this.setStyle("isNewColor",t)},setStyle:function(t,e){this.$store.commit("setStyle",{key:t,val:e})},onChange:function(t){console.log(t)},updateListType:function(t){this.$store.commit("setListType",t)},resetStyle:function(t){this.$store.commit("resetStyle",t)},onChangeSide:function(t){this.setStyle("sideWidth",t)},setListSort:function(t){this.$store.commit("setListSort",t)}},computed:{style:function(){return this.$store.state.style},soundNotify:function(){return this.$store.getters.soundNotify},listType:function(){return this.$store.getters.listType},listSort:function(){return this.$store.getters.listSort}}},f=m,h=(s("6b22"),Object(d["a"])(f,n,a,!1,null,null,null));e["a"]=h.exports},"52d3":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex robot-res-box",staticStyle:{height:"400px"}},[s("div",{staticClass:"flex-item"},[t._l(t.robotList,(function(t,e){}))],2),s("div",{staticClass:"robot-search"})])},a=[],i={props:["sessionId","channelKey"],data:function(){return{robotList:[]}},mounted:function(){},methods:{search:function(t){console.log("搜素机器人",t);this.sessionId,channelKey;this.$api.robotAgentAsk().then((function(t){"000"==t.respCode&&t.data.answer}))}}},o=i,r=(s("c059"),s("2877")),c=Object(r["a"])(o,n,a,!1,null,"07350d02",null);e["a"]=c.exports},"5e9c":function(t,e,s){"use strict";s("99af"),s("c975"),s("a15b"),s("baa5"),s("fb6a"),s("d3b7"),s("4d63"),s("ac1f"),s("25f0"),s("3ca3"),s("466d"),s("5319"),s("841c"),s("1276"),s("ddb0"),s("2b3d"),s("cd17");var n=s("ed3b"),a=s("3835"),i=s("b85c"),o=(s("3b18"),s("f64c")),r=s("bc3a"),c=s.n(r),l=s("5a0c"),d=s.n(l),g="undefined"!=typeof ctxBasePath?ctxBasePath:"/yc-media";o["a"].config({maxCount:2});var u=0;function m(){return d()().format("hhmmss")+"-"+ ++u}function f(t){return new Promise((function(e,s){c.a.request({url:t.url,params:t.data,method:"GET",timeout:1e4}).then((function(s){t.success&&t.success(s.data),e(s.data)})).catch((function(t){o["a"].destroy(),o["a"].error({content:t.message}),s(t)}))}))}function h(t,e){var s={url:t.url,data:t.data,method:"POST",timeout:1e4},n=Object.assign(s,e);return new Promise((function(e,s){c.a.request(n).then((function(s){t.success&&t.success(s.data),e(s.data)})).catch((function(t){o["a"].destroy(),o["a"].error({content:t.message}),s(t)}))}))}function p(t){var e=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),s=window.location.search.substr(1).match(e);return null!=s?unescape(s[2]):null}function v(t){var e=t||window.location.search||window.location.hash;if("undefined"!=typeof URLSearchParams){var s,n=new URLSearchParams(e.split("?")[1]),o={},r=Object(i["a"])(n);try{for(r.s();!(s=r.n()).done;){var c=s.value,l=Object(a["a"])(c,2),d=l[0],g=l[1];o[d]=g}}catch(w){r.e(w)}finally{r.f()}return o}var u,m=e.substring(e.lastIndexOf("?")+1),f={},h=Object(i["a"])(m.toString().split("&"));try{for(h.s();!(u=h.n()).done;){var p=u.value,v=p.split("="),y=decodeURIComponent(v[0]),b=decodeURIComponent(v.slice(1).join("="));f[y]=b}}catch(w){h.e(w)}finally{h.f()}return f}function y(t){o["a"].warning(t)}var b=function(t){return Math.round(Math.random()*t)};function w(t){n["a"].info(t)}function S(t,e,s,n,a){var i=window.Notification||window.mozNotification||window.webkitNotification,o={tag:n||"msgAgentNotify",body:e,icon:a||"/yc-media-agent/pages/agent/images/newimg.png"};if(t="客服系统消息-"+t,i)if("granted"==i.permission){var r=new i(t,o),c=setTimeout(r.close,5e3);r.onclick=function(){window.focus(),s&&s(),c&&clearTimeout(c),r.close()}}else i.requestPermission((function(e){if("granted"!==e)return!1;var n=new i(t,o);n.onclick=function(){window.focus(),s&&s()}}));else console.log("浏览器不支持桌面通知！")}function C(){for(var t=[],e="0123456789abcdef",s=0;s<32;s++)t[s]=e.substr(Math.floor(16*Math.random()),1);t[14]="4",t[19]=e.substr(3&t[19]|8,1);var n=t.join("");return n=n.replace("-",""),n}function k(t,e){var s={xls:"xls",xlsx:"xls",doc:"word",docx:"word",ppt:"ppt",pptx:"ppt",pdf:"pdf",txt:"txt",jpg:"img",jpeg:"img",png:"img",bmp:"img",gif:"img",mp3:"voice",vox:"voice",wav:"voice",mp4:"video",zip:"zip",rar:"zip"},n="file";return String(t).indexOf(".")>-1&&(t=t.substring(t.lastIndexOf(".")+1),t=String(t).toLocaleLowerCase(),n=s[t]||"file"),e?n:"".concat(g,"/static/images/filetype/").concat(n,".png")}function I(t){return t.replace(/<[^>]+>/g,"")}function T(t){var e=/^[^@\/\'\\\"#$,，‘“%&\^\*]+$/;return!!e.test(t)}function _(t){for(var e=0,s=0;s<t.length;s++){var n=t.charAt(s);/^[\u0000-\u00ff]$/.test(n)?e+=1:e+=2}return e}function x(t){var e=t.getBoundingClientRect();return e.top>=0&&e.left>=0&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&e.right<=(window.innerWidth||document.documentElement.clientWidth)}e["a"]={request:f,post:h,message:o["a"],warning:y,alert:w,random:b,GetQueryObj:v,GetQueryString:p,getSerialId:m,notification:S,uuid:C,getFileType:k,delHtmlTag:I,checkStr:T,getCharByte:_,isElementInViewport:x}},"667e":function(t,e,s){"use strict";var n=s("d7fd"),a=s.n(n);a.a},"6b22":function(t,e,s){"use strict";var n=s("8a7e"),a=s.n(n);a.a},"7c55":function(t,e,s){"use strict";var n=s("2395"),a=s.n(n);a.a},"8a7e":function(t,e,s){},9732:function(t,e,s){},"9b93":function(t,e,s){"use strict";s("c975"),s("a15b"),s("b0c0"),s("d3b7"),s("ac1f"),s("3ca3"),s("466d"),s("5319"),s("1276"),s("ddb0");for(var n=s("ade3"),a=s("5e9c"),i=s("1e65"),o=s("ebf4"),r=s("b8ac"),c=s("5a0c"),l=s.n(c),d=(l()().format("YYYY-MM-DD"),o["a"].host,localStorage.getItem("userAccount"),function(){i["a"].$on("wsMessage",x)}),g=function(){i["a"].$off("wsMessage",x)},u=function(t){return{cmdJson:JSON.stringify(t)}},m={},f=$("#AGENT_WORK_CONFIG").val()||"",h=""==f||"''"==f?[]:f.split(","),p=0;p<h.length;p++){var v=h[p].split("=");m[v[0]]=v.length>1?v[1]:""}function y(t){return t?m[t]:m}var b=Object(n["a"])({channelType:{2:"微信",1:"网页",3:"微博",5:"APP"},avatar:{system:"agent/images/avatar/system.png",agent:"agent/images/avatar/agent.png",user:"agent/images/avatar/user.png",third:"agent/images/avatar/user.png",robot:"agent/images/avatar/robot.jpg",2:"/static/img/avatar/logo-wechat.jpg",1:"/static/img/avatar/logo-web.png",3:"/static/img/avatar/logo-weibo.jpg",5:"/static/img/avatar/logo-app.jpg"},info:{userInfo:{nickname:"我",headimgurl:"../static/images/user-avatar.png"},robotInfo:{nickname:"机器人",headimgurl:"../static/images/robot-reply.png"},sysInfo:{nickname:"系统消息",headimgurl:"../static/images/system-msg.png"}},icon:{1:"chrome",2:"wechat",3:"weibo",5:"mobile",user:"user",agent:"user",system:"bell",robot:"android"},sounds:{connect:{flag:!0,src:"dingdong"==y("connectSound")||localStorage&&"CN"!=localStorage["MULTI-LANG-KEY"]?"agent/sound/dingdong.mp3":"agent/sound/cnConnect.mp3"},disconnect:{flag:!0,src:"agent/sound/Global.mp3"},message:{flag:!0,src:"agent/sound/newMsg.mp3"},transfer:{flag:!0,src:"agent/sound/tran.mp3"},video:{flag:!0,src:"agent/sound/videomsg.mp3"}}},"channelType",{1:{icon:"ie",name:"网页",color:"#40a9ff"},2:{icon:"wechat",name:"微信",color:"#52c41a"},3:{icon:"weibo",name:"微博",color:"#E22823"},4:{icon:"sound",name:"语音",color:"#faad14"},5:{icon:"mobile",name:"APP",color:"#096dd9"},6:{icon:"video-camera",name:"视频",color:"#ca6e42"},7:{icon:"wechat",name:"企业微信",color:"#3482EC"},8:{icon:"alipay-circle",name:"支付宝",color:"#387BFD"},9:{icon:"api",name:"小程序",color:"#51D22B"}}),w=function(t){var e=t.sender||"system",s=("user"==t.sender||"third"==t.sender)&&t.userInfo&&t.userInfo.headimgurl||b.avatar[e]||"";return s},S=function(t){var e=t.sender||"system",s=b.icon[e];return s},C=function(t,e){return e?b.channelType[t]||{name:"其他",color:"#387BFD",icon:"user"}:b.channelType[t].name||"其他"},k=function(t){var e={msgContent:"",msgType:"text"};Object.assign({},e,t);return new Promise((function(e,s){o["a"].sendMessage("mediaEvent","sendMessage",u(t)).then((function(t){e(t)}))}))},I=function(t){return l()(t).format("MM-DD HH:mm:ss")};function T(t){o["a"].sendMessage("MsgConfirm","sendMessage",u({serialId:t}))}var _=!0,x=function(t){try{if(t=t.data||t,"string"==typeof t)try{t=JSON.parse(t)}catch(r){}t.serialId&&_&&T(t.serialId);t.messageId;if(t.messageId&&"tuoguan"==t.messageId)return void i["a"].$emit("wsTuoguan",t);if(t.messageId&&"tuoguanState"==t.messageId)return void i["a"].$emit("wsTuoguanState",t);if(t.messageId&&"evtAgentState"==t.messageId)return void L(t);if(t.messageId&&"respForceLogout"==t.messageId)return i["a"].$emit("respForceLogout",t.cmddata),void o["a"].close(!0);if(t.messageId&&"respHeartbeatLogout"==t.messageId)return i["a"].$emit("respHeartbeatLogout",t.cmddata),void o["a"].close(!0);if(t.messageId&&"respLogin"==t.messageId)return;if(t.messageId&&"msgReceived"==t.messageId)return void i["a"].$emit("msgReceived",t);if(t.messageId&&"msgConfirm"==t.messageId)return void i["a"].$emit("msgConfirm",t);if(t.messageId&&"respLogout"==t.messageId)return L({agentStatus:"LOGOFF"}),void o["a"].close(!0);if(t.messageId&&"getChannelQueueUser"==t.messageId)return void i["a"].$emit("wsQueue",t.cmddata);if(t.action&&"sendMessage"==t.action)return;if(t.messageId&&"monitorInfo"==t.messageId)return void j(t);if(t.messageId&&"userTmpMsg"==t.messageId)return void i["a"].$emit("wsUserTmpMsg",t.cmddata);if(t.messageId&&"againChat"==t.messageId)return void(t.cmddata.msg&&a["a"].message.info({content:t.cmddata.msg}));if(t.messageId&&"withdraw"==t.messageId)return void(1!=t.cmddata.state?a["a"].message.info({content:t.cmddata.msg}):i["a"].$emit("wsWithdraw",t.cmddata));if(t.messageId&&"userWithdraw"==t.messageId)return void(1!=t.cmddata.state?a["a"].message.info({content:t.cmddata.msg}):i["a"].$emit("wsUserWithdraw",t.cmddata));if(t.messageId&&"ResultTransfer"==t.messageId)return t.eventData.sessionId=t.eventData.sessionId||t.sessionId,i["a"].$emit("ResultTransfer",t.eventData),void mediabarEvent.emitEvent("ResultTransfer",t.eventData);if(t.messageId&&"todayMonitorData"==t.messageId)return void i["a"].$emit("wsTodayMonitorData",t.cmddata);if(t.messageId&&"msgAssistant"==t.messageId)return void i["a"].$emit("msgAssistant",t.cmddata);if(t.messageId&&"webSite"==t.messageId)return void i["a"].$emit("webSite",t.cmddata);if(t.messageId&&"mediaEvent"==t.messageId&&"event"==t.cmddata.msgType){var e=t.serialId,s=t.agentId;return t=t.cmddata||t,t.serialId=t.serialId||e,void A(t,s)}var n=t.serialId;t=t.cmddata||t,t.serialId=t.serialId||n,t.timestamp=t.timestamp||(new Date).getTime(),t.sender=t.sender||"user","event"==t.msgType?A(t):O(t)}catch(r){console.info(r)}},L=function(t){i["a"].$emit("wsAgentState",t)},O=function(t){i["a"].$emit("wsMsg",t)},j=function(t){i["a"].$emit("wsMonitor",t)},A=function(t){i["a"].$emit("wsEvent",t)},N=function(t){return o["a"].loadMessage(t)},M=null;function E(){return M}var P=function(){var t=y("pageSize")||200,e=o["a"].chatList({pageSize:t}),s=o["a"].hisChatList({pageSize:t});return new Promise((function(t,n){return Promise.all([e,s]).then((function(e){var s=(new Date).getTime();M=s;for(var n={lastSend:s,stick:!1,isTag:!1,unread:0,sender:"user",userInfo:{}},a=e[0].data||[],i=e[1].data&&e[1].data.data?e[1].data.data:[],o={},r=0;r<i.length;r++){var c=i[r],l=c.sessionId;(!o[l]||c.beginTime>o[l].beginTime)&&(o[l]=Object.assign({},n,{unread:0,offline:!0},c))}for(var d={1:"user",2:"agent",3:"robot"},g=0;g<a.length;g++){var u=a[g],m=u.sessionId,f={};u.msgObject&&(f.lastSend=u.msgObject.msgTimestamp,f.sender=d[u.msgObject.sender]||"user"),o[m]=Object.assign({},n,{unread:0,offline:!1},u,f)}for(var h in o)o[h].userInfo&&!o[h].userInfo.nickname&&(o[h].userInfo.nickname=it(h));t(o)}))}))},R=function(t){var e=(new Date).getTime(),s=(t.msgTime,Object.assign({},{unread:0,offline:!1},t));return s},D=function(t){return t=B(t),t=F(t),t=r["a"].parse(t),t};function B(t){if(String(t).indexOf("<a")>=0||String(t).indexOf("src=")>=0||String(t).indexOf("href=")>=0)return t;var e=/(http[s]?:\/\/([\w-]+.)+([:\d+])?([/0-9a-z.]+)?(\?[0-9a-z&_=-]+)?(#[0-9-a-z]+)?)/gi,s=a["a"].delHtmlTag(t),n=s.match(e);if(null==n||0==n.length)return t;for(var i=0;i<n.length;i++){var o=n[i];o=o&&o.split(" ")[0];var r='<a href="'+o+'" target=_blank>'+o+"</a>";t=t.replace(o,r)}return t}function F(t){if(String(t).indexOf("<a")>=0||String(t).indexOf("src")>=0)return t;var e=/(1[3|4|5|7|8][\d]{9}|0[\d]{2,3}-[\d]{7,8}|400[-]?[\d]{3}[-]?[\d]{4})/g,s=t.match(e);if(null==s)return t;for(var n=0;n<s.length;n++){var a=s[n];a='<span class="chat-phonenum">'+a+"</span>",t=t.replace(s[n],a)}return t}var U=function(t,e){t.sender=t.sender||"system";var s=H(t),n=Object.assign({userInfo:{}},t,s);return n.avatar=w(n),n.avatarIcon=S(n),n.time=I(n.msgTime),n},H=function(t){var e=t.msgType,s=t.sender,n=t.msgContent;if("robot"==s){try{n="string"==typeof n?JSON.parse(n):n}catch(r){}n.content&&"news"!=e&&(n=n.content)}switch(e){case"text":n="robot"==s&&"undefined"==typeof n?"":"robot"==s?n.content||n:D(n);break;case"file":try{n=JSON.parse(n)}catch(r){}break;case"richText":break;case"image":var a="robot"==s?n.url:n,i="robot"==s?n.content:n;"robot"==s&&(n={src:a,title:i});break;case"audio":a="robot"==s?n.url:n,i="robot"==s?n.content:"Audio";n={src:a,title:i};break;case"voice":a="robot"==s?n.url:n,i="robot"==s?n.content:"Audio";n={src:a,title:i};break;case"video":a="robot"==s?n.url:n,i="robot"==s?n.content:"Voice";n={src:a,title:i};break;case"news":break}var o={msgContent:n,sender:s,msgType:e};return t.robotData&&(o.robotData=t.robotData),o},z=function(t,e,s){var n={event:t,sessionId:e,msgType:"event",eventData:s};return new Promise((function(t,e){o["a"].sendMessage("mediaEvent","sendMessage",u(n)).then((function(e){t(e)}))}))},G=function(t,e){return z("StartTransfer",t,e)},K=function(t,e){return z("ConfirmTransfer",t,e)},W=function(t,e){return z("Released",t,e)},V=function(t){var e={IDLE:{name:"IDLE",color:"#11f95a"},BUSY:{name:"BUSY",color:"#f5cf55"},LOGOFF:{name:"LOGOFF",color:"#f5222d"},UNKNOW:{name:"NO",color:"#ccc"}};return e[t]||e["UNKNOW"]},q=function(t){return new Promise((function(e,s){o["a"].agentList(t).then((function(t){if(1==t.state){for(var s=t.data.result.groups,n={},a=0,i=0;i<s.length;i++)if(s[i].agents&&s[i].agents.length){a+=s[i].agents.length;for(var o=0;o<s[i].agents.length;o++){var r=s[i].agents[o].agentId;n[r]={SKILL_GROUP_ID:s[i].SKILL_GROUP_ID,SKILL_GROUP_NAME:s[i].SKILL_GROUP_NAME,agentId:s[i].agents[o].agentId,agentName:s[i].agents[o].agentName}}}e({total:a,group:n})}}))}))},J=function(t){if(!t)return"";var e=t.msgType,s=t.msgContent,n={isType:!0,msgType:e};return"system"==t.sender?{isType:!0,msgType:"system"}:"text"==e?s?a["a"].delHtmlTag(s):s:"image"==e||"video"==e||"voice"==e||"file"==e||"ywflow"==e||"news"==e?n:s};function Y(){for(var t=[],e="0123456789abcdef",s=0;s<32;s++)t[s]=e.substr(Math.floor(16*Math.random()),1);t[14]="4",t[19]=e.substr(3&t[19]|8,1);var n=t.join("");return n=n.replace("-",""),n}function Q(t){o["a"].sendCommand(null,"getChannelQueueUser",{channelKey:t})}function X(t,e){o["a"].sendCommand(t,"addChatByQueueUser",{sessionId:t,chatSessionId:e}),o["a"].heartbeat(!0)}function Z(t,e){console.log("主动接入",t),o["a"].sendCommand(t,"againChat",{sessionId:t})}function tt(t){}var et={audio:null,audio2:null,inited:!1,init:function(){et.audio=document.createElement("audio"),et.audio.controls=!1,et.audio2=document.createElement("audio"),et.audio2.controls=!1,et.inited=!0},play:function(t,e){et.inited||et.init();var s=b.sounds[t].src,n="connect"==t?"audio2":"audio";et[n].pause(),et[n].src=s,et[n].loop=!!e,setTimeout((function(){try{et[n].play(),et[n].addEventListener("ended",(function(){}),!1)}catch(t){console.error(t)}}),100)},stop:function(){et.inited&&setTimeout((function(){try{!et.audio.paused&&et.audio.pause()}catch(t){console.error(t)}}),100)}};function st(t,e){var s=Object.assign({},t,e);for(var n in s)t[n]&&new Date(t[n].beginTime).getTime()>=new Date(s[n].beginTime).getTime()&&(s[n]=t[n]);return s}function nt(t,e,s){for(var n={1:"user",2:"agent",3:"robot"},a=0;a<e.length;a++){var i=e[a].sessionId;if(t[i]&&e[a].chatSessionId==t[i].chatSessionId)t[i].offline=!1,t[i].skillGroupId=t[i].skillGroupId||e[a].skillGroupId||"";else if(t[i]){var o=void 0;t[i]&&t[i].msgObject&&(o=t[i].msgObject),t[i]=R(e[a]),t[i].msgObject=t[i].msgObject||o||{},t[i].skillGroupId=t[i].skillGroupId||e[a].skillGroupId||"",t[i].msgObject&&(t[i].lastSend=t[i].msgObject.msgTimestamp,t[i].sender=n[t[i].msgObject.sender]||"user")}}return t}function at(t){_=t}function it(t){return t=String(t),t.length>6?"User-"+t.substr(t.length-6):"User-"+t}e["a"]={init:d,destory:g,sendMessage:k,loadMessage:N,formatMessage:H,formatContent:U,formatTime:I,resMap:b,getStateName:V,getList:P,newSession:R,getChannelType:C,command:z,transfer:G,confirm:K,release:W,agentList:q,msgTips:J,uuid:Y,getChannelQueueUser:Q,addChatByQueueUser:X,audio:et,getInputMonitor:tt,againChat:Z,mergeList:st,mergeListNew:nt,confirmMsg:T,setMsgConfirm:at,userNickName:it,getExConfig:y,getListTime:E}},a47e:function(t,e,s){"use strict";var n=s("cc5a"),a=s.n(n);a.a},a84e:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return"event"!=t.msg.sender&&"system"!=t.msg.sender&&t.msg.sender?s("div",{staticClass:"chat-box-msg",attrs:{"data-serial-id":t.msg.serialId,"data-sender":t.msg.sender||"system","data-msg-id":t.msg.msgId}},[s("a-avatar",{staticClass:"userItem-avator",attrs:{size:"large",src:t.msg.avatar,icon:t.msg.avatarIcon||"user"},on:{dblclick:function(e){return e.stopPropagation(),t.copyMsgInfo.apply(null,arguments)}}}),s("div",{staticClass:"chat-box-msg-state"}),"fail"!=t.msg.state&&"sending"!=t.msg.state&&1==t.msg.withdraw&&"user"!=t.msg.sender&&"third"!=t.msg.sender?s("div",{staticClass:"msg-rollback"},[t._v(" "+t._s(t.$t("btn.withdrawn"))+" "),"text"==t.msg.msgType?[s("br"),s("a-icon",{attrs:{type:"edit"},on:{click:function(e){return t.reEdit(t.msg)}}})]:t._e()],2):t._e(),"fail"!=t.msg.state&&"sending"!=t.msg.state&&!t.msg.sendFailDesc&&t.canIRollback(t.msg)?s("div",{staticClass:"msg-rollback-btn",on:{click:function(e){return e.stopPropagation(),t.rollbackMsg(t.msg)}}},[t._v(t._s(t.$t("btn.withdraw")))]):t._e(),s("div",{staticClass:"chat-box-msg-info",class:{history:t.history}},[s("div",{staticClass:"chat-box-msg-nickname"},[s("span",{staticClass:"chat-nickname"},[t._v(t._s(t.getNickName(t.msg))),t.history?s("span",[t._v("["+t._s(t.msg.channelName)+"]")]):t._e()]),"sending"==t.msg.state?s("span",{staticClass:"chat-time"},[s("a-icon",{attrs:{type:"loading"}})],1):"fail"==t.msg.state?s("span",{staticClass:"chat-time",staticStyle:{cursor:"pointer"},attrs:{title:t.msg.result},on:{click:function(e){return e.stopPropagation(),t.resend.apply(null,arguments)}}},[s("a-icon",{staticStyle:{color:"#ff5722"},attrs:{theme:"filled",type:"exclamation-circle"}}),t._v(" "+t._s(t.$t("btn.resend")))],1):"checkfail"==t.msg.state?s("span",{staticClass:"chat-time",staticStyle:{cursor:"pointer"},attrs:{title:t.msg.result}},[s("a-icon",{staticStyle:{color:"#ff5722"},attrs:{theme:"filled",type:"exclamation-circle"}}),t._v(" "+t._s(t.msg.result))],1):t.msg.sendFailDesc?s("span",{staticClass:"chat-time",staticStyle:{cursor:"pointer"},attrs:{title:t.msg.sendFailDesc},on:{click:function(e){return e.stopPropagation(),t.resendObj(t.msg)}}},[s("a-icon",{staticStyle:{color:"#ff5722"},attrs:{theme:"filled",type:"exclamation-circle"}}),t._v(" "+t._s(t.msg.msgTime?t.formatTime(t.msg.msgTime):""))],1):s("span",{staticClass:"chat-time"},["succ"==t.msg.state?s("a-icon",{staticStyle:{color:"#aaa"},attrs:{theme:"filled",type:"check-circle"}}):"received"==t.msg.state||1==t.msg.readState&&!t.msg.readState?s("a-icon",{staticStyle:{color:"#76dc7a"},attrs:{theme:"filled",type:"check-circle"}}):"read"==t.msg.state||1==t.msg.readState?s("a-icon",{staticStyle:{color:"#76dc7a"},attrs:{theme:"filled",type:"eye"}}):t._e(),t._v(" "+t._s(t.msg.msgTime?t.formatTime(t.msg.msgTime):"")+" ")],1)]),s("div",[1!=t.msg.withdraw||"user"!=t.msg.sender&&"third"!=t.msg.sender?[s("div",{staticClass:"chat-box-msg-detail",attrs:{"data-msg-type":t.msg.msgType}},[s("div",{staticClass:"msg-detail"},[t.msgOrigin.areaInfo?s("a-tag",{staticClass:"addr-tag",on:{click:function(e){return e.stopPropagation(),t.showArea.apply(null,arguments)}}},[s("a-icon",{attrs:{type:"environment",theme:"filled"}})],1):t._e(),"image"==t.msg.msgType||"video"==t.msg.msgType||"voice"==t.msg.msgType?s("div",{staticClass:"msg-tools keywords"},[s("a-button-group",[s("a-button",{attrs:{size:"default",title:"reload",icon:"reload"},on:{click:function(e){return e.stopPropagation(),t.reloadImgSrc(t.msg)}}}),s("a-button",{attrs:{size:"default",title:"info",icon:"share-alt"},on:{click:function(e){return e.stopPropagation(),t.quoteThis(t.msg)}}}),s("a-button",{attrs:{size:"default",title:"info",icon:"info-circle"},on:{click:function(e){return e.stopPropagation(),t.copyText(t.mediaUrl)}}}),s("a-button",{attrs:{size:"default",title:"download",icon:"download"},on:{click:function(e){return e.stopPropagation(),t.downloadFile(t.msg.msgContent)}}}),s("a-button",{attrs:{size:"default",title:"quote",icon:"unordered-list"},on:{click:function(e){return e.stopPropagation(),t.menuSearch(t.msgOrigin.msgContent,"quote",t.msgOrigin)}}}),t.showOcr?s("a-button",{attrs:{size:"default",title:"OCR"},on:{click:function(e){return e.stopPropagation(),t.ocr(t.msgOrigin.msgContent,"ocr",t.msgOrigin)}}},[t._v("OCR")]):t._e()],1)],1):t._e(),"text"==t.msgType||"event"==t.msgType?s("div",{staticClass:"bubble",staticStyle:{"max-height":"50vh","overflow-y":"auto"}},[t.history?s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msg.msgContent))}}):s("a-dropdown",{attrs:{trigger:["contextmenu"]}},[s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msg.msgContent))},on:{contextmenu:t.setRightClick}}),s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[t.isHik?s("a-menu-item",{key:"6",on:{click:function(e){return t.menuSearch(t.msgOrigin.msgContent,"de",t.msgOrigin)}}},[t._v(" DE ")]):t._e(),t.isHik?s("a-menu-item",{key:"7",on:{click:function(e){return t.menuSearch(t.msgOrigin.msgContent,"pis",t.msgOrigin)}}},[t._v(" PIS ")]):t._e(),t.hasNav("zsk")?s("a-menu-item",{key:"1",on:{click:function(e){return t.menuSearch(t.msg.msgContent,"zsk")}}},[t._v(" "+t._s(t.$t("btn.searchKMS"))+" ")]):t._e(),t.hasNav("cyy")?s("a-menu-item",{key:"2",on:{click:function(e){return t.menuSearch(t.msg.msgContent,"cyy")}}},[t._v(" "+t._s(t.$t("btn.searchCommomWord"))+" ")]):t._e(),t.hasNav("jqr")?s("a-menu-item",{key:"3",on:{click:function(e){return t.menuSearch(t.msg.msgContent,"robot")}}},[t._v(" "+t._s(t.$t("btn.askRobot"))+" ")]):t._e(),s("a-menu-item",{key:"4",on:{click:function(e){return t.menuSearch(t.msg.msgContent,"copy")}}},[t._v(" "+t._s(t.$t("btn.copy"))+" ")]),s("a-menu-item",{key:"5",on:{click:function(e){return t.menuSearch(t.msgOrigin.msgContent,"quote",t.msgOrigin)}}},[t._v(" "+t._s(t.$t("btn.quote"))+" ")]),t.aiContextMenu.length>0?s("a-sub-menu",{key:"test",attrs:{title:"智能填单"}},[t._l(t.aiContextMenu,(function(e,n){return[e.children?s("a-sub-menu",{key:"test-"+n,attrs:{title:e.name}},[t._l(e.children,(function(e,a){return[e.children?s("a-sub-menu",{key:"test2-"+n+"-"+a,attrs:{title:e.name}},t._l(e.children,(function(e,i){return s("a-menu-item",{key:"test3-"+n+"-"+a+"-"+i,on:{click:function(s){return t.aiTreeDataSet(e.id,t.msg.msgContent,e)}}},[t._v(t._s(e.name))])})),1):s("a-menu-item",{key:"test2-"+n+"-"+a},[t._v(t._s(e.name))])]}))],2):s("a-menu-item",{key:"test-"+n},[t._v(t._s(e.name))])]}))],2):t._e()],1)],1),t.translationContent&&t.translationContent!=t.msg.msgContent?s("div",{staticClass:"tran-text",domProps:{innerHTML:t._s(t.contentHtml(t.translationContent))}}):t._e()],1):"voice"==t.msgType?s("div",{staticClass:"bubble voice"},[s("audio",{staticClass:"webAudio",attrs:{src:t.urlSessionId(t.msg.msgContent.src),controlslist:"nodownload nopicureinpicture",preload:"none",controls:""}})]):"video"==t.msgType?s("div",{staticClass:"bubble video"},[s("video",{staticClass:"webAudio",staticStyle:{"max-width":"100%","max-height":"400px"},attrs:{src:t.urlSessionId(t.msg.msgContent.src),controlslist:"nodownload nopicureinpicture",preload:"none",controls:""}}),s("div",{staticClass:"video-tips"},[t._v(t._s(t.$t("message.VideoTips")))])]):"image"==t.msgType?s("div",{staticClass:"bubble img",staticStyle:{"max-width":"400px"}},[t.showOcrFlag?[s("div",{staticClass:"flex-row",staticStyle:{width:"80%"}},[s("div",{staticClass:"flex-item",staticStyle:{"text-align":"center"}},[s("img",{staticStyle:{"max-width":"100%","max-height":"100%"},attrs:{src:t.imgOrigin}})]),s("div",{staticClass:"flex-item"},[s("div",{staticStyle:{"text-align":"center"}},[s("a-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.copyArray(t.ocrData)}}},[t._v("复制")])],1),t._l(t.ocrData,(function(e,n){return s("div",{staticStyle:{"margin-bottom":"10px"}},[t._v(t._s(e.text))])}))],2)])]:[t.msg.isRrror?t.msg.isRrror?s("a-icon",{staticStyle:{"font-size":"30px"},attrs:{type:"file-image"},on:{click:t.reloadImgError}}):t._e():s("img",{staticStyle:{display:"block","max-width":"100%"},attrs:{"lazy-load":!0,mode:"widthFix","data-enable":"1",src:t.imgThumb,"data-url":t.imgOrigin,"vv-lazy":t.imgThumb},on:{load:t.loadImage}})]],2):"file"==t.msgType?s("div",{staticClass:"bubble file",on:{click:function(e){return t.downloadFile(t.msg.msgContent)}}},[s("a-card",{staticStyle:{width:"300px","max-width":"100%"},attrs:{hoverable:""}},[s("template",{staticClass:"ant-card-actions",slot:"actions"},[s("a-icon",{key:"download",attrs:{type:"download"}})],1),s("a-card-meta",{attrs:{description:t.msg.msgContent.name||t.msg.msgContent}},[s("a-avatar",{attrs:{slot:"avatar",shape:"square",src:t.fileType},slot:"avatar"})],1)],2)],1):"template"==t.msgType?s("div",[s("Msgtemplate",{attrs:{msgItem:t.msg}})],1):"news"==t.msgType?s("div",[s("div",{staticClass:"ywflow-card flex-row",staticStyle:{width:"240px","max-width":"100%",padding:"5px",background:"#fff","justify-content":"center","align-items":"center"},attrs:{hoverable:""},on:{click:function(e){return t.jump(t.newsContent.url,t.newsContent)}}},[t.newsContent.picurl||t.baseNewImg?s("div",{staticStyle:{"margin-right":"10px"}},[s("img",{staticStyle:{width:"50px",height:"50px",border:"1px solid #ddd","object-fit":"cover",display:"inline-block"},attrs:{slot:"cover","lazy-load":!0,onerror:"this.style.display='none'",alt:t.newsContent.content,"data-url":t.imgOrigin,src:t.newsContent.picurl||t.baseNewImg,loading:"lazy"},slot:"cover"})]):t._e(),s("div",{staticClass:"flex-item"},[s("div",{staticStyle:{color:"#333","margin-bottom":"5px"}},[t._v(t._s(t.newsContent.title))]),s("div",{staticStyle:{color:"#aaa"}},[t._v(t._s(t.newsContent.content))])])])]):"ywflow"==t.msgType?s("div",[s("div",{staticClass:"ywflow-card flex-row",staticStyle:{width:"240px","max-width":"100%",padding:"5px",background:"#fff","justify-content":"center","align-items":"center"},attrs:{hoverable:""},on:{click:function(e){return t.jump(t.msg.robotData&&t.msg.robotData.url||t.msg.url||t.ywflowContent||t.msg.msgContent)}}},[s("div",{staticStyle:{"margin-right":"10px"}},[s("img",{staticStyle:{width:"50px",height:"50px",border:"1px solid #ddd"},attrs:{slot:"cover",src:t.ywImg,loading:"lazy"},slot:"cover"})]),s("div",{staticClass:"flex-item",staticStyle:{color:"#333"}},[t._v(t._s(t.ywTitle))])])]):s("div",{staticClass:"bubble"},[s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msg.msgContent))}}),t.msgOrigin.tempConfig&&t.msgOrigin.tempConfig.translationContent?s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msgOrigin.tempConfig.translationContent))}}):t._e()])],1),t.msg.robotData&&t.answerList?s("div",{staticClass:"robot-data"},[s("div",{domProps:{innerHTML:t._s(t.answerList.beforeWord)}}),t._l(t.answerList.itemList,(function(e,n){return s("p",[t._v("["+t._s(e.seq)+"]"+t._s(e.title))])})),s("div",{domProps:{innerHTML:t._s(t.answerList.afterWord)}}),0===t.msgOrigin.evalFlag||1==t.msgOrigin.evalFlag?s("a-tag",{attrs:{color:0===t.msgOrigin.evalFlag?"green":"red"}},[0===t.msgOrigin.evalFlag?s("img",{staticStyle:{height:"14px"},attrs:{src:"/yc-media-agent/pages/agent/images/ok.png",type:"smile"}}):t._e(),1===t.msgOrigin.evalFlag?s("img",{staticStyle:{height:"14px"},attrs:{src:"/yc-media-agent/pages/agent/images/no.png",type:"frown"}}):t._e(),t.msgOrigin.evaluationData&&t.msgOrigin.evaluationData.satisfResult?s("span",[t._v(t._s(t.msgOrigin.evaluationData.satisfResult))]):t._e()]):t._e()],2):t._e()]),t.hasFollow||t.follow?s("a-icon",{staticStyle:{margin:"0 10px"},style:{color:t.showFollowList||t.msgOrigin.serialId==t.latestSerialId?"#03A9F4":"#333"},attrs:{type:"ordered-list"},on:{click:t.toggleFollow}}):t._e(),t.msg.quoteData?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.stringToObj(t.msg.quoteData),(function(e,n){return s("a-popover",[s("template",{slot:"content"},["image"==e.msgType?s("div",{staticClass:"img",staticStyle:{"max-width":"400px"}},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.msgContent,expression:"item.msgContent"}],staticStyle:{display:"block","max-width":"100%"},attrs:{"lazy-load":!0,mode:"widthFix","data-enable":"1","data-url":e.msgContent}})]):s("span",{attrs:{type:e.msgType}},[t._v(t._s(e.msgContent))])]),s("a-tag",{staticClass:"quote-tag",staticStyle:{"max-width":"100px",overflow:"hidden"},attrs:{"data-enable":"image"==e.msgType?1:0,"data-url":"image"==e.msgType?e.msgContent:""}},[s("a-icon",{attrs:{type:t.getTagIcon(e,"sender")}}),t._v(" : "),s("a-icon",{attrs:{type:t.getTagIcon(e)}}),t._v(t._s(t._f("quoteTextFormat")(e))+" ")],1)],2)})),1):t._e(),t.msg.keywords?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.msg.keywords,(function(e,n){return s("a-tag",{key:n,attrs:{color:"blue"},on:{click:function(s){return t.searchKey(e.keyWord)}}},[t._v(t._s(e.keyWord))])})),1):t._e(),t.follow?s("div",{directives:[{name:"show",rawName:"v-show",value:t.msgOrigin.serialId==t.latestSerialId||t.showFollowList,expression:"msgOrigin.serialId == latestSerialId || showFollowList"}],staticClass:"keywords",staticStyle:{padding:"5px",border:"1px dashed #ddd","font-size":"12px",background:"#f9e8ce"}},t._l(t.follow,(function(e,n){return s("div",{staticStyle:{cursor:"pointer"},on:{click:function(s){return s.stopPropagation(),t.showFollow(e)}}},[s("a-tag",[t._v(t._s(n+1))]),t._v(t._s(e.showCode))],1)})),0):t._e(),t.aiData&&t.aiData.sensTip?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.sensTip,(function(e,n){return s("a-popover",[s("template",{slot:"content"},[s("div",{staticStyle:{"max-width":"40vw"},domProps:{innerHTML:t._s(e)}})]),s("a-tag",{staticStyle:{color:"#666","white-space":"initial"},attrs:{color:"#EFDEDE"},on:{click:function(s){return s.stopPropagation(),t.copyText(e)}}},[s("a-icon",{attrs:{type:"book"}}),t._v(t._s(e)+" ")],1)],2)})),1):t._e(),t.slotList?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.slotList,(function(e,n){return s("div",{directives:[{name:"show",rawName:"v-show",value:t.msgOrigin.serialId==e.txtId,expression:"msgOrigin.serialId == item.txtId"}]},[s("a-tag",{staticStyle:{color:"#666","white-space":"initial"},attrs:{color:"#D7E6FE"},on:{click:function(s){return s.stopPropagation(),t.copyText(e.slotValue)}}},[t._v(" "+t._s(e.slotName)+":"+t._s(e.slotValue)+" ")]),e.tip?s("span",{staticStyle:{color:"#red"}},[s("a-icon",{attrs:{type:"warning"}}),t._v(t._s(e.tip))],1):t._e(),s("span",{staticStyle:{color:"#4393E4",cursor:"pointer"},on:{click:function(s){return s.stopPropagation(),t.sendSlot(e)}}},[s("a-icon",{attrs:{type:"edit"}}),t._v("填入")],1)],1)})),0):t._e()]:[s("div",{staticClass:"chat-box-msg-detail",attrs:{"data-msg-type":t.msg.msgType}},[t._m(0)])]],2)])],1):s("div",{directives:[{name:"show",rawName:"v-show",value:t.msg.msgContent,expression:"msg.msgContent"}],staticClass:"msg-event-box",attrs:{"data-serial-id":t.msg.serialId}},["template"!=t.msgType?s("div",{staticClass:"chat-time"},[t._v(t._s(t.msg.msgTime?t.formatTime(t.msg.msgTime):""))]):t._e(),"template"==t.msgType?s("div",{staticClass:"msg-content"},[s("Msgtemplate",{attrs:{msgItem:t.msg}})],1):s("div",{staticClass:"msg-content",attrs:{"data-type":"tempalte"},domProps:{innerHTML:t._s(t.contentHtml(t.msg.msgContent))}}),t.msg.quoteData?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.stringToObj(t.msg.quoteData),(function(e,n){return s("a-popover",[s("template",{slot:"content"},["image"==e.msgType?s("div",{staticClass:"img",staticStyle:{"max-width":"400px"}},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:e.msgContent,expression:"item.msgContent"}],staticStyle:{display:"block","max-width":"100%"},attrs:{"lazy-load":!0,mode:"widthFix","data-enable":"1","data-url":e.msgContent}})]):s("span",{attrs:{type:e.msgType}},[t._v(t._s(e.msgContent))])]),s("a-tag",{staticClass:"quote-tag",attrs:{"data-enable":"image"==e.msgType?1:0,"data-url":"image"==e.msgType?e.msgContent:""}},[s("a-icon",{attrs:{type:t.getTagIcon(e,"sender")}}),t._v(" : "),s("a-icon",{attrs:{type:t.getTagIcon(e)}}),t._v(t._s(t._f("quoteTextFormat")(e))+" ")],1)],2)})),1):t._e(),t.msg.keywords?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.msg.keywords,(function(e,n){return s("a-tag",{key:n,attrs:{color:"blue"},on:{click:function(s){return t.searchKey(e.keyWord)}}},[t._v(t._s(e.keyWord))])})),1):t._e(),t.aiData&&t.aiData.sensTip?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.sensTip,(function(e,n){return s("a-popover",[s("template",{slot:"content"},[s("div",{staticStyle:{"max-width":"40vw"},domProps:{innerHTML:t._s(e)}})]),s("a-tag",{staticStyle:{color:"#666","white-space":"initial"},attrs:{color:"#EFDEDE"},on:{click:function(s){return s.stopPropagation(),t.copyText(e)}}},[s("a-icon",{attrs:{type:"book"}}),t._v(t._s(e)+" ")],1)],2)})),1):t._e(),t.slotList?s("div",{staticClass:"keywords",staticStyle:{"padding-top":"5px"}},t._l(t.slotList,(function(e,n){return s("div",{directives:[{name:"show",rawName:"v-show",value:t.msgOrigin.serialId==e.txtId,expression:"msgOrigin.serialId == item.txtId"}]},[s("a-tag",{staticStyle:{color:"#666","white-space":"initial"},attrs:{color:"#D7E6FE"},on:{click:function(s){return s.stopPropagation(),t.copyText(e.slotValue)}}},[t._v(" "+t._s(e.slotName)+":"+t._s(e.slotValue)+" ")]),e.tip?s("span",{staticStyle:{color:"#red"}},[s("a-icon",{attrs:{type:"warning"}}),t._v(t._s(e.tip))],1):t._e(),s("span",{staticStyle:{color:"#4393E4",cursor:"pointer"},on:{click:function(s){return s.stopPropagation(),t.sendSlot(e)}}},[s("a-icon",{attrs:{type:"edit"}}),t._v("填入")],1)],1)})),0):t._e()])},a=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"msg-detail"},[s("div",{staticClass:"bubble"},[t._v("消息已撤回")])])}],i=(s("99af"),s("c975"),s("d81d"),s("45fc"),s("b0c0"),s("d3b7"),s("4d63"),s("ac1f"),s("25f0"),s("5319"),s("1276"),s("2ca0"),s("498a"),s("1e25"),s("53ca")),o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"mod-box"},["satisfy1"==t.tempMod?[s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msgItem.msgContent))}})]:"satisfy2"==t.tempMod?[s("div",{staticClass:"nps-box"},[s("a-button-group",{attrs:{size:"small"}},t._l(10,(function(e){return s("a-button",{attrs:{type:e==t.tempConfig.tempResult?"primary":""}},[t._v(t._s(e))])})),1)],1),t._v(" "+t._s(t.showRes&&t.tempConfig.remark)+" ")]:"satisfy3"==t.tempMod?[t.showRes?t._l(t.list,(function(e,n){return s("img",{style:e.kvalue==t.tempConfig.tempResult?"opacity:1":"opacity:0.3",attrs:{src:e.img,height:"40px",v2:e.kvalue,"data-c1":t.tempConfig.satisfyCode}})})):[s("img",{attrs:{src:"/yc-media-agent/pages/agent/images/smile.png",alt:"",height:"40px"}})],t.showRes&&t.tempConfig.remark?s("div",[t._v(t._s(t.tempConfig.remark))]):t._e()]:"satisfy4"==t.tempMod?[s("a-rate",{attrs:{disabled:""},model:{value:t.tempConfig.tempResult,callback:function(e){t.$set(t.tempConfig,"tempResult",e)},expression:"tempConfig.tempResult"}}),t.showRes&&t.tempConfig.remark?s("div",[t._v(t._s(t.tempConfig.remark))]):t._e()]:"satisfy5"==t.tempMod?[s("SatisfyNews",{attrs:{"msg-item":t.msgItem,tempData:t.tempConfig.tempData,history:t.history}})]:"guideKeys"==t.tempMod?[s("p",[t._v(t._s(t.tempConfig.tempData.content))]),t._l(t.tempConfig.tempData.channelKeys,(function(e,n){return s("div",[s("p",{on:{click:function(s){return t.selectKey(e)}}},[t._v(t._s(e.KEY_NAME))])])}))]:"robotSummary2"==t.tempMod||"robotSummary1"==t.tempMod?[s("div",{staticClass:"robot-summary"},[s("div",{staticClass:"robot-summary-header"},[t._m(0),s("span",[t._v(t._s(t.msgItem.msgTime))])]),s("div",{staticClass:"robot-summary-content",domProps:{innerHTML:t._s(t.contentHtml(t.msgItem.msgContent))}})])]:[s("div",{domProps:{innerHTML:t._s(t.contentHtml(t.msgItem.msgContent))}})]],2)},r=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"robot-summary-title"},[s("img",{attrs:{src:"/yc-media-agent/pages/agent/images/jqrxj.png"}})])}],c=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"satisfy5-box satisfy5-news",attrs:{"data-satisfy-id":t.msgItem.serialId}},t._l(t.tempData,(function(e,n){return s("div",{staticClass:"ywflow-card flex-row",staticStyle:{width:"240px",padding:"5px",background:"#fff","justify-content":"center","align-items":"center","max-width":"100%"},attrs:{hoverable:""},on:{click:function(s){return t.jump(e.newsUrl)}}},[s("div",{staticStyle:{"margin-right":"10px"}},[s("img",{staticStyle:{width:"50px",height:"50px",border:"1px solid #ddd","object-fit":"cover",display:"inline-block"},attrs:{slot:"cover",alt:e.newsTitle,src:e.newsPicUrl,loading:"lazy"},slot:"cover"})]),s("div",{staticClass:"flex-item",staticStyle:{color:"#333"}},[e.newsTitle?s("div",[s("b",[t._v(t._s(e.newsTitle))])]):t._e(),s("div",[t._v(t._s(e.newsDesc))])])])})),0)},l=[],d={props:["msgItem","tempData","history"],data:function(){return{isSend:!1}},methods:{jump:function(t){console.log("url")},loadImage:function(t){this.$parent.$parent.loadImage&&this.$parent.$parent.loadImage(this.msgItem.serialId)}},mounted:function(){},computed:{}},g=d,u=(s("27fb"),s("2877")),m=Object(u["a"])(g,c,l,!1,null,"77e5d6e4",null),f=m.exports,h={props:["msgItem","history"],components:{SatisfyNews:f},data:function(){return{isImgSatisfyStar:!1,isImgSatisfySmile:!1,list:[{title:this.$t("vote.VerySatisfaction"),img:"/yc-media/pages/client/images/avatar/satisfy/4.png",kvalue:"4"},{title:this.$t("vote.Satisfaction"),img:"/yc-media/pages/client/images/avatar/satisfy/3.png",kvalue:"3"},{title:this.$t("vote.Dissatisfied"),img:"/yc-media/pages/client/images/avatar/satisfy/1.png",kvalue:"1"}]}},mounted:function(){"undefined"!=typeof webIM_ex_config&&!0===webIM_ex_config.isImgSatisfyStar&&(this.isImgSatisfyStar=!0)},methods:{textFormat:function(){var t=this.msgItem.content||this.msgItem.msgContent;t=String(t).trimEnd("\n"),t=t.replace(/['\\\']/g,""),t=t.replace(/['\"']/g,'"');var e=this.$client.textFormat(t);return e},contentHtml:function(t){if(""==t||"{}"==t||!t)return"";t=String(t).trimEnd("\n");try{t=emoji.parse(String(t).replace(/[\n]/g,"<br>"))}catch(e){t=String(t).replace(/[\n]/g,"<br>")}return t=t.replace(/['\\\']/g,""),t=t.replace(/['\"']/g,'"'),t},selectKey:function(t){}},computed:{showRes:function(){return this.tempConfig&&1==this.tempConfig.showSatisfy},tempConfig:function(){if(this.msgItem.tempConfig){var t=JSON.parse(this.msgItem.tempConfig);return t}return{}},tempMod:function(){var t="text";if(this.msgItem.tempConfig){var e=JSON.parse(this.msgItem.tempConfig);return e.tempId||"text"}return t}}},p=h,v=(s("1a0d"),Object(u["a"])(p,o,r,!1,null,"6854023c",null)),y=v.exports,b="undefined"!=typeof ctxBasePath?ctxBasePath:"/yc-media";function w(t){return t.replace(/<[^>]+>/g,"")}var S={props:["msgOrigin","msgType","sender","history","sessionId","follow","latestSerialId","hasFollow","navs"],components:{Msgtemplate:y},data:function(){return{playMsgid:null,str:"",baseNewImg:b+"/pages/agent/images/newsimg.png",aiContextMenu:[],showFollowList:!1,ocrData:[],showOcrFlag:!1,isReload:!1}},mounted:function(){window.treeOrderAi&&(this.aiContextMenu=window.treeOrderAi)},filters:{quoteTextFormat:function(t){var e=t.msgContent;switch(t.msgType){case"image":e="[图片]";break;case"video":e="[视频]";break;case"voice":e="[音频]";break}return e}},beforeDestroy:function(){this.aiContextMenu=null},methods:{reloadImgSrc:function(){this.isReload=!0,this.$nextTick((function(){this.isReload=!1}))},stringToObj:function(t){try{t=JSON.parse(t)}catch(e){}return t},contentHtml:function(t){if(""==t||"{}"==t||!t)return"";t=String(t).trimEnd("\n");try{t=emoji.parse(String(t).replace(/[\n]/g,"<br>"))}catch(i){t=String(t).replace(/[\n]/g,"<br>")}if("robot"==this.sender&&(t=t.replace(/['\"']/g,'"'),t=t.replace(/['\\\']/g,"")),this.aiData&&this.aiData.sensWord){for(var e=String(this.aiData.sensWord).split(","),s=[],n=0;n<e.length;n++)s=s.concat(e[n].split("+"));for(var a=0;a<s.length;a++)t=this.highLightKeywords(t,s[a])}return t},playVoice:function(t){var e=this.getMsgContent(t).src;this.$parent.playMsgid=e,this.$webIM.audioPlay(e)},getMsgContent:function(t){var e=this.$webIM.formatMessage(t);return e.msgContent},getSelect:function(){var t="";return t=window.getSelection?window.getSelection().toString():document.selection.createRange().text,t},hasNav:function(t){return this.navs.some((function(e){return e.id===t}))},ocr:function(t){if(this.ocrData.length>0)this.showOcrData();else{var e=this,s=t;t.startsWith("/")&&(t=location.origin);var n="/yc-mediagw/hkcloudapi?action=getOcrContent&imgUrl="+s;$.post(n,(function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(s){}"200"==t.code&&t.result&&t.result.data&&t.result.data.list?(e.ocrData=t.result.data.list,e.showOcrData()):e.$utils.message.info({content:t.msg})}))}},showOcrData:function(){this.ocrData.length>0&&(this.showOcrFlag=!this.showOcrFlag),this.$parent.imageUrl=this.imgOrigin,this.$parent.ocrData=this.ocrData},menuSearch:function(t,e,s){var n=this,a=this.str||t;switch(a=w(a),e){case"cyy":this.$parent.callPage("cyy","loadingSearchData",a);break;case"zsk":this.$parent.callPage("zsk","findKnowledge",a);break;case"robot":this.$parent.callPage("jqr","findRobotAnswer",a);case"copy":this.copyText(a);break;case"de":window.open("/yc-mediagw/hkcloudapi?action=goDe&serialNo="+a);break;case"pis":var i="/yc-mediagw/hkcloudapi?action=getPisUrl&serialNo="+a;$.post(i,(function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(e){}"200"==t.code?window.open(t.data.url):n.$utils.message.info({content:t.msg})}));break;case"quote":this.$emit("quote",{msgContent:s.msgContent,msgType:s.msgType,chatId:s.serialId,sender:s.sender});break}},copyText:function(t){$("body").append('<textarea id="copyArea"></textarea>'),$("#copyArea").html(t),$("#copyArea")[0].select(),document.execCommand("Copy"),$("#copyArea").remove(),this.$utils.message.success({content:"Copy success"})},copyArray:function(t){for(var e="",s=0;s<t.length;s++)e=e+t[s].text+"\n";this.copyText(e)},testOcr:function(t){this.$parent.imageUrl=t,this.$parent.ocrData=["第一句","第二句","第三句"]},downloadFile:function(t){var e="object"==Object(i["a"])(t)?t.url||t.src:t;if(e=this.urlSessionId(e),e+="&source=wx","undefined"==typeof saveAs)window.open(e);else try{saveAs(e)}catch(s){window.open(e)}},setRightClick:function(t){this.str=this.getSelect()},formatTime:function(t){return this.$webIM.formatTime(t)},getNickName:function(t){var e=this.$t("name.system");switch(t.sender){case"user":e="",e=t.userInfo.nickname||t.userInfo.custName||this.$t("name.user");break;case"third":e=t.userInfo.nickname||t.userInfo.custName||this.$t("name.user");break;case"agent":e=t.agentNickName||t.agentName||t.agentId;break;case"aiAgent":e=t.senderName;break;case"robot":e=this.$t("name.robot");break;case"system":break;default:}return e},canIRollback:function(t){return!(1==t.withdraw||!this.$ccbar.mediaWithdrawMsgAuth(t.agentId))&&("agent"==t.sender||"2"==t.sender)},rollbackMsg:function(t){t.serialId&&(this.$ccbar.rollback(t.serialId,this.sessionId,t),"hik"==window.pageVersion&&"text"==t.msgType&&this.reEdit(t))},searchKey:function(t){this.$parent.toSearchKeyword(t)},resend:function(){this.$parent.resendMsg(this.msg.msgIndex)},resendObj:function(t){this.$parent.sendMessage({msgType:this.msgOrigin.msgType||"text",msgContent:this.msgOrigin.msgContent})},reEdit:function(t){ChatControl.addContentToInput(t.msgContent,null,t.sessionId)},jump:function(t,e){e&&e.window?layer.open({type:2,title:e.title,area:["50%","60%"],content:e.url}):t&&window.open(t)},loadImage:function(t){},reloadImgError:function(){this.msgOrigin.isRrror=!1},loadImgError:function(){this.msgOrigin.isRrror=!0},goBack:function(){window.history.go(-1)},iframeOpenUrl:function(){var t=this.msg.robotData&&this.msg.robotData.url||this.msg.url||this.ywflowContent||this.msg.msgContent;window.open(t)},copyMsgInfo:function(){var t={sessionId:this.sessionId,serialId:this.msgOrigin.serialId,msgType:this.msgOrigin.msgType,msgContent:this.msgOrigin.msgContent};this.copyText(JSON.stringify(t))},quoteText:function(t){this.$parent.inputVal='<div class="quote-text" style="background-color:#E8E8E8;color:#333">'.concat(this.$t("btn.quote"),":").concat(t,"</div>")},quoteThis:function(t){this.$emit("quote",{msgContent:t.msgContent,msgType:t.msgType,chatId:t.serialId,sender:t.sender})},getTagIcon:function(t,e){var s={image:"picture",video:"video-camera",audio:"audio",user:"user",agent:"smile"};return e?s[t.sender]:s[t.msgType]},urlSessionId:function(t){if(!t)return t;var e=this.sessionId||this.msgOrigin.sessionId||"";return t=-1!=String(t).indexOf("?")?t+"&sessionId="+e:t+"?sessionId="+e,t},highLightKeywords:function(t,e,s,n,a){if(""==$.trim(String(e)))return t;n=n||"span",s=s||"";var i=["*",".","?","+","$","^","[","]","{","}","|","\\","(",")","/","%"];i.map((function(t,s){-1!=e.indexOf(t)&&(e=e.replace(new RegExp("\\"+t,"g"),"\\"+t))}));var o=new RegExp(e,"g");return o.test(t)&&(t=t.replace(o,"<"+n+' class="vh-highlight '+s+'">'+e+"</"+n+">")),t},showArea:function(){this.$parent.showArea&&this.$parent.showArea(this.msgOrigin.areaInfo)},sendSlot:function(t){this.$parent.sendSlot&&this.$parent.sendSlot(t)},aiTreeDataSet:function(t,e,s){var n=this.str||e;window["AiHelper"+this.sessionId]&&window["AiHelper"+this.sessionId].handleSlotChange(t,n,s)},showFollow:function(t){this.$parent.callPage("cc-knowledge","toDetails",t),this.$ai.followPush({type:"aiUser",event:"toDetails",sessionId:this.sessionId,data:t})},toggleFollow:function(){this.hasFollow&&!this.follow?(this.$parent.callPage("cc-knowledge","getHistoryFollowingData",this.msgOrigin),this.$ai.followPush({type:"aiUser",event:"getHistoryFollowingData",sessionId:this.sessionId,data:this.msgOrigin})):this.showFollowList=!this.showFollowList}},computed:{showOcr:function(){return"image"==this.msgOrigin.msgType&&window.useOcr},isShowOriginImg:function(){return window.useOrigin},isHik:function(){return"hik"==window.pageVersion},answerList:function(){var t=this.msgOrigin.robotData||null;if(t){try{t=JSON.parse(t)}catch(e){return null}if(t&&t.answerList)return t.answerList}return t},translationContent:function(){var t=this.msgOrigin.tempConfig||this.msgOrigin.userInfo;if(t){try{t=JSON.parse(t)}catch(e){}return t.translationContent||""}return!1},msg:function(){var t=this.$webIM.formatContent(this.msgOrigin);return t},fileType:function(){var t=this.msg.msgContent.name||this.msg.msgContent,e=this.$utils.getFileType(t);return e||b+"/static/images/file.png"},ywImg:function(){return b+"/static/images/qa.jpg"},ywTitle:function(){var t=this.msg.msgContent;try{t=JSON.parse(t)}catch(e){}return t.title||"云问流程"},newsContent:function(){var t=this.msg.msgContent;try{t=JSON.parse(t)}catch(e){}return t},ywflowContent:function(){var t=this.msg.msgContent;try{t=JSON.parse(t)}catch(e){}return"object"==Object(i["a"])(t)?t.fileUrl||t.url||t.content:t},imgOrigin:function(){var t="object"==Object(i["a"])(this.msg.msgContent)?this.msg.msgContent.src:this.msg.msgContent;return t=this.urlSessionId(t),t},imgThumb:function(){if(this.isReload)return"";var t=this.imgOrigin;return t?(this.isHik&&window.useOrigin||(-1!=t.indexOf("?")?t+="&_thumb=1":t+="?_thumb=1"),t):t},mediaUrl:function(){var t=this.msg.msgContent,e="object"==Object(i["a"])(t)?t.url||t.src:t;return e=this.urlSessionId(e),e},sensTip:function(){return this.aiData&&this.aiData.sensTip?String(this.aiData.sensTip).split(",,"):[]},aiData:function(){var t=this.msgOrigin.assistantData;if(!t)return null;if("string"==typeof t)try{t=JSON.parse(t)}catch(e){console.log(e)}return t.cmd?t:t.cmddata?t.cmddata:{}},slotList:function(){return this.msgOrigin.fillObj?this.msgOrigin.fillObj.slotList:[]}}},C=S,k=(s("e1bd"),s("a8ae"),Object(u["a"])(C,n,a,!1,null,"e3fa5294",null));e["a"]=k.exports},a8ae:function(t,e,s){"use strict";var n=s("9732"),a=s.n(n);a.a},ac57:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex"},[s("div",{staticClass:"info"},[s("span",{domProps:{textContent:t._s(t.updateTime)}})]),s("div",{staticClass:"tran-btns"},[s("a-textarea",{ref:"refSend",attrs:{maxLength:"800",allowClear:"",placeholder:t.$t("message.leaveMsg"),"auto-size":{minRows:3,maxRows:5}},model:{value:t.remark,callback:function(e){t.remark=e},expression:"remark"}}),s("a-button",{attrs:{type:"primary",block:""},on:{click:function(e){return e.stopPropagation(),t.tranBtn.apply(null,arguments)}}},[t._v(t._s(t.$t("btn.Transfer")+"("+t.remark.length+"/800)"))])],1),t.centers.length>0?s("div",{staticStyle:{"text-align":"center",padding:"10px"}},[s("a-button-group",{attrs:{size:"mini"}},[s("a-button",{attrs:{type:1==t.activeType?"primary":"default"},on:{click:function(e){e.stopPropagation(),t.activeType=1}}},[s("a-icon",{attrs:{type:"home"}})],1),s("a-button",{attrs:{type:2==t.activeType?"primary":"default"},on:{click:function(e){e.stopPropagation(),t.activeType=2}}},[s("a-icon",{attrs:{type:"team"}})],1)],1)],1):t._e(),s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.activeType,expression:"activeType == 1"}],staticClass:"flex-item"},[s("div",{staticClass:"info"},[t._v(" "+t._s(t.$t("state.Online"))+" "),s("a-tag",{attrs:{color:"green"}},[t._v(t._s(t.onlineCount))])],1),s("div",{staticClass:"tran-btns",staticStyle:{"text-align":"center"}},[s("a-input-group",{attrs:{compact:""}},[s("a-select",{attrs:{"default-value":"ALL"},model:{value:t.typeVal,callback:function(e){t.typeVal=e},expression:"typeVal"}},[s("a-select-option",{attrs:{value:"ALL"}},[t._v(" "+t._s(t.$t("state.ALL"))+" ")]),s("a-select-option",{attrs:{value:"Ready"}},[t._v(" "+t._s(t.$t("state.IDLE"))+" ")]),s("a-select-option",{attrs:{value:"NotReady"}},[t._v(" "+t._s(t.$t("state.BUSY"))+" ")])],1),s("a-input",{staticStyle:{width:"60%"},model:{value:t.searchName,callback:function(e){t.searchName=e},expression:"searchName"}},[s("a-icon",{attrs:{slot:"addonAfter",type:"sync"},on:{click:t.getAgents},slot:"addonAfter"})],1)],1)],1),""==t.searchName?s("div",[s("a-empty",{directives:[{name:"show",rawName:"v-show",value:0==t.groups.length,expression:"groups.length==0"}],attrs:{description:!1}}),s("a-radio-group",{staticStyle:{display:"block"},attrs:{name:"radioGroup"},on:{change:t.clickGroup},model:{value:t.targetVal,callback:function(e){t.targetVal=e},expression:"targetVal"}},[t.groups.length>0?s("a-collapse",{attrs:{bordered:!1,accordion:!0},on:{change:t.showChannelUser}},t._l(t.groups,(function(e,n){return s("a-collapse-panel",{directives:[{name:"show",rawName:"v-show",value:e.agents&&e.agents.length>0,expression:"group.agents && group.agents.length>0"}],key:e.SKILL_GROUP_ID,attrs:{header:e.SKILL_GROUP_NAME}},[t._l(e.agents,(function(a,i){return s("div",{directives:[{name:"show",rawName:"v-show",value:t.showAgent(a),expression:"showAgent(agentItem)"}],key:a.sessionId,staticClass:"queue-userinfo agentItem"},[s("a-radio",{staticStyle:{float:"right"},attrs:{slot:"extra",value:a.agentId+"|"+n+"|"+e.SKILL_GROUP_ID},on:{click:function(e){return e.stopPropagation(),t.clickGroupAgent.apply(null,arguments)}},slot:"extra"}),s("a-tag",{attrs:{color:"Ready"==a.agentState?"blue":"#f5cf55"}},[t._v(t._s("Ready"==a.agentState?t.$t("state.IDLE"):t.$t("state.BUSY")))]),s("a-tag",[t._v(t._s(t.servicePresent(a)))]),t._v(t._s(a.agentName)+" ")],1)})),s("a-radio",{staticStyle:{transform:"scale(1.5)"},attrs:{slot:"extra",value:n+"|"+e.SKILL_GROUP_ID},on:{click:function(e){return e.stopPropagation(),t.clickGroupBtn.apply(null,arguments)}},slot:"extra"})],2)})),1):t._e()],1)],1):t._e(),""!=t.searchName?s("div",[s("a-empty",{directives:[{name:"show",rawName:"v-show",value:0==t.groups.length,expression:"groups.length==0"}],attrs:{description:!1}}),s("a-radio-group",{staticStyle:{display:"block"},attrs:{name:"radioGroup"},on:{change:t.clickGroup},model:{value:t.targetVal,callback:function(e){t.targetVal=e},expression:"targetVal"}},t._l(t.showGroups,(function(e,n){return s("div",{directives:[{name:"show",rawName:"v-show",value:t.showAgent(e),expression:"showAgent(agentItem)"}],key:e.sessionId,staticClass:"queue-userinfo agentItem",attrs:{title:e.SKILL_GROUP_NAME}},[s("a-radio",{staticStyle:{float:"right"},attrs:{slot:"extra",value:e.agentId+"|"+e.SKILL_GROUP_NAME+"|"+e.SKILL_GROUP_ID+"|agent"},on:{click:function(e){return e.stopPropagation(),t.clickGroupAgent.apply(null,arguments)}},slot:"extra"}),s("a-tag",{attrs:{color:"Ready"==e.agentState?"blue":"#f5cf55"}},[t._v(t._s("Ready"==e.agentState?t.$t("state.IDLE"):t.$t("state.BUSY")))]),s("a-tag",[t._v(t._s(t.servicePresent(e)))]),t._v(" "+t._s(e.agentName)+" "),s("div",{staticClass:"textoverflow"},t._l(e.groups,(function(e){return s("a-tag",{staticStyle:{margin:"2px 5px"},attrs:{color:"orange"}},[t._v(t._s(e))])})),1)],1)})),0)],1):t._e()]),s("div",{directives:[{name:"show",rawName:"v-show",value:2==t.activeType,expression:"activeType == 2"}],staticClass:"flex-item"},[s("div",{staticClass:"center-groups"},t._l(t.centers,(function(t,e){return s("div",{staticClass:"c-centers"},[s("div",{staticClass:"name"})])})),0),s("div",{staticClass:"info"},[s("a-select",{staticStyle:{width:"80%",margin:"0 auto"},attrs:{placeholder:"..."},on:{change:t.handleCenterChange}},t._l(t.centers,(function(e,n){return s("a-select-option",{attrs:{value:e.CENTER_ID}},[t._v(t._s(e.CENTER_NAME))])})),1)],1),s("div",{staticClass:"center-group-skills"},[s("a-radio-group",{staticStyle:{display:"block"},attrs:{name:"radioCenterGroup"},on:{change:t.clickCenterGroup},model:{value:t.targetVal2,callback:function(e){t.targetVal2=e},expression:"targetVal2"}},t._l(t.centerGroups,(function(e,n){return e.agents.length>0?s("div",{key:e.SKILL_GROUP_CODE,staticClass:"queue-userinfo agentItem",attrs:{title:e.SKILL_GROUP_NAME}},[s("a-radio",{staticStyle:{float:"right"},attrs:{slot:"extra",value:n},slot:"extra"}),s("a-tag",[t._v(t._s(e.agents.length))]),t._v(" "+t._s(e.SKILL_GROUP_NAME)+" ")],1):t._e()})),0)],1)])])},a=[],i=(s("99af"),s("c975"),s("b680"),s("ac1f"),s("1276"),{props:["sessionId","nickname","channelName","offline","monitor","chatSessionId","channelKey"],data:function(){return{updateTime:"",searchName:"",targetVal:"",targetVal2:"",targetAgentId:"",remark:"",agents:[],groups:[],typeVal:"ALL",activeType:1,centers:[],centerGroups:[],isSelGroup:!1}},mounted:function(){this.getAgents()},computed:{showGroups:function(){for(var t=this.searchName,e=JSON.parse(JSON.stringify(this.groups)),s=[],n={},a=0;a<e.length;a++)if(e[a].agents)for(var i=0;i<e[a].agents.length;i++){var o=e[a].agents[i],r=!1;o&&(-1==o.agentName.indexOf(t)&&-1==o.agentId.indexOf(t)&&-1==e[a].SKILL_GROUP_NAME.indexOf(t)||(r=!0),r&&(s.push(o),n[o.agentId]=n[o.agentId]?n[o.agentId]:o,"undefined"==typeof n[o.agentId].groups&&(n[o.agentId].groups=[]),n[o.agentId].groups.push(e[a].SKILL_GROUP_NAME),"undefined"==typeof n[o.agentId].groupIds&&(n[o.agentId].groupIds=[]),n[o.agentId].groupIds.push(e[a].SKILL_GROUP_ID),n[o.agentId].SKILL_GROUP_ID=e[a].SKILL_GROUP_ID,n[o.agentId].SKILL_GROUP_NAME=e[a].SKILL_GROUP_NAME))}return console.log("转移:在线坐席对象",n),n},onlineCount:function(){for(var t=0,e=0;e<this.groups.length;e++){var s=this.groups[e].agents&&this.groups[e].agents.length;s&&(t+=s)}return t}},methods:{getAgents:function(){var t=this;this.$ccbar.agentList(this.sessionId,this.channelKey).then((function(e){t.updateTime=t.$webIM.formatTime(new Date),"1"==e.state?(t.groups=e.data.result.groups,t.centers=e.data.result.centers||[],!t.targetVal&&t.groups.length>0&&(t.targetVal=t.groups[0].SKILL_GROUP_ID)):(t.$utils.message.warning({content:e.msg}),t.groups=[])}))},clickGroupBtn:function(){this.isSelGroup=!0},clickGroupAgent:function(){this.isSelGroup=!1},clickGroup:function(t){this.targetAgentId=t.target.value,this.$refs.refSend.focus()},tranBtn:function(){if(console.log("转移",this.targetVal),this.offline)this.$utils.message.warning({content:this.$t("message.sessionOffline")});else if(2!=this.activeType)if(""!=this.targetAgentId)if(String(this.targetAgentId).indexOf("|")>=0){var t=this.targetAgentId.split("|");if(2==t.length)this.transferToGroup(t[1],t[0]);else if(4==t.length)this.transferToAgent(t[0],t[2],t[1]);else{var e=this.groups[t[1]].SKILL_GROUP_NAME;this.transferToAgent(t[0],t[2],e)}}else this.transferToAgent(this.targetAgentId);else this.$utils.message.warning({content:this.$t("message.selectTransferTarget")});else this.transferToCenterGroup()},transferToAgent:function(t,e,s,n){var a={type:1,targetAgentId:t,remark:this.remark,nickname:this.nickname,channelName:this.channelName,chatSessionId:this.chatSessionId,transferType:n?"skillGroup":"agent"};e&&(a.skillGroupId=e,a.skillGroupName=s),this.monitor?this.$emit("transfer",a):this.$webIM.transfer(this.sessionId,a),this.$utils.message.success({content:this.$t("message.transferCommandSucc")}),this.remark="",this.$emit("close")},transferToGroup:function(t,e){var s=this.groups[e].agents,n=s.length,a=n<2?0:this.$utils.random(n),i=s[a].agentId,o=this.groups[e].SKILL_GROUP_NAME;this.transferToAgent(i,t,o,!0)},handleCenterChange:function(t){var e=this,s={sessionId:this.sessionId,channelKey:this.channelKey,centerId:t};this.$ccbar.agentListForCenter(s).then((function(t){e.centerGroups=t.data.result.groups||[]}))},transferToCenterGroup:function(){var t=this.targetVal2,e=this.centerGroups[t],s={type:1,remark:this.remark,nickname:this.nickname,channelName:this.channelName,chatSessionId:this.chatSessionId,skillGroupId:e.SKILL_GROUP_ID,skillGroupName:e.SKILL_GROUP_NAME};console.log("转租户",s),this.monitor?this.$emit("transfer",s):this.$webIM.transfer(this.sessionId,s)},showAgent:function(t){return"ALL"==this.typeVal||this.typeVal==t.agentState},servicePresent:function(t){return"undefined"!=typeof webIM_ex_config&&webIM_ex_config.transferPersent?t.serviceLimitCount>0?(t.curServiceCount/t.serviceLimitCount*100).toFixed(2)+"%":"--":"".concat(t.curServiceCount,"/").concat(t.serviceLimitCount)}}}),o=i,r=(s("667e"),s("2877")),c=Object(r["a"])(o,n,a,!1,null,"3341b513",null);e["a"]=c.exports},b8ac:function(t,e,s){"use strict";s("d81d"),s("fb6a"),s("ac1f"),s("841c"),s("1276");var n="undefined"!=typeof ctxBasePath?ctxBasePath:"/yc-media",a=n+"/pages/",i={},o=a+"agent/images/emotion/emoji_",r="[微笑],[撇嘴],[色],[发呆],[得意],[流泪],[害羞],[闭嘴],[睡],[大哭],[尴尬],[发怒],[调皮],[呲牙],[惊讶],[难过],[囧],[抓狂],[吐],[偷笑],[愉快],[白眼],[傲慢],[困],[惊恐],[流汗],[憨笑],[悠闲],[奋斗],[咒骂],[疑问],[嘘],[晕],[衰],[骷髅],[敲打],[再见],[擦汗],[抠鼻],[鼓掌],[坏笑],[左哼哼],[右哼哼],[哈欠],[鄙视],[委屈],[快哭了],[阴险],[亲亲],[可怜],[菜刀],[西瓜],[啤酒],[咖啡],[猪头],[玫瑰],[凋谢],[嘴唇],[爱心],[心碎],[蛋糕],[炸弹],[便便],[月亮],[太阳],[拥抱],[强],[弱],[握手],[胜利],[抱拳],[勾引],[拳头],[OK],[跳跳],[发抖],[怄火],[转圈],[汗],[生病],[破涕为笑],[吐舌],[脸红],[恐惧],[失望],[无语],[Hey],[Facepalm],[Smirk],[Smart],[Concerned],[Yeah!],[幽灵],[合十],[强壮],[喝彩],[礼物],[红包],[發],[福],[Hungry],[Ruthless],[Blush],[Tormented],[Shame],[Wrath],[Basketball],[PingPong],[Rice],[Lightning],[Dagger],[Soccer],[Ladybug],[Kotow],[Dramatic],[JumpRope],[Surrender],[InLove],[Blowkiss],[Pinky],[RockOn],[Nuh - uh]",c="[Smile],[Grimace],[Drool],[Scowl],[CoolGuy],[Sob],[Shy],[Silent],[Sleep],[Cry],[Awkward],[Angry],[Tongue],[Grin],[Surprise],[Frown],[Awkward],[Scream],[Puke],[Chuckle],[Joyful],[Slight],[Smug],[Drowsy],[Panic],[Sweat],[Laugh],[Commando],[Determined],[Scold],[Shocked],[Shhh],[Dizzy],[Toasted],[Skull],[Hammer],[Bye],[Wipe],[NosePick],[Clap],[Trick],[Bah！ L],[Bah！ R],[Yawn],[Pooh - pooh],[Shrunken],[TearingUp],[Sly],[Kiss],[Whimper],[Cleaver],[Watermelon],[Beer],[Coffee],[Pig],[Rose],[Wilt],[Lips],[Heart],[BrokenHeart],[Cake],[Bomb],[Poop],[Moon],[Sun],[Hug],[ThumbsUp],[ThumbsDown],[Shake],[Peace],[Fight],[Beckon],[Fist],[OK],[Waddle],[Tremble],[Aaagh!],[Twirl],[Sweat],[Sick],[Lol],[Wink],[Blush],[Terror],[Let Down],[Speechless],[嘿哈],[捂脸],[奸笑],[机智],[皱眉],[耶],[Ghost],[Namaste],[Strong],[Cheers],[Gift],[Packet],[Rich],[Blessing],[饥饿],[酷],[冷汗],[疯了],[糗大了],[吓],[篮球],[乒乓],[饭],[闪电],[刀],[足球],[瓢虫],[磕头],[回头],[跳绳],[挥手],[爱情],[飞吻],[差劲],[爱你],[NO]",l=["/::)","/::~","/::B","/::|","/:8-)","/::<","/::$","/::X","/::Z","/::'(","/::-|","/::@","/::P","/::D","/::O","/::(","/::-|","/::Q","/::T","/:,@P","/:,@-D","/::d","/:,@o","/:|-)","/::!","/::L","/::>","/::,@","/:,@f","/::-S","/:?","/:,@x","/:,@@","/:,@!","/:!!!","/:xx","/:bye","/:wipe","/:dig","/:handclap","/:B-)","/:<@","/:@>","/::-O","/:>-|","/:P-(","/::'|","/:X-)","/::*","/:8*","/:pd","/:<W>","/:beer","/:coffee","/:pig","/:rose","/:fade","/:showlove","/:heart","/:break","/:cake","/:bome","/:shit","/:moon","/:sun","/:hug","/:strong","/:weak","/:share","/:v","/:@)","/:jj","/:@@","/:ok","/:jump","/:shake","/:<O>","/:circle","/::L","[生病]","[破涕为笑]","[吐舌]","[脸红]","[恐惧]","[失望]","[无语]","[嘿哈]","[捂脸]","[奸笑]","[机智]","[皱眉]","[耶]","[幽灵]","[合十]","[强壮]","[喝彩]","/:gift","[红包]","[發]","[福]","/::g","/::+","/:--b","/::8","/:&-(","/:@x","/:basketb","/:oo","/:eat","/:li","/:kn","/:footb","/:ladybug","/:kotow","/:turn","/:skip","/:oY","/:love","/:<L>","/:bad","/:lvu","/:no"];i.emotion_map={},i.emotion_mapShow={};for(var d,g,u,m=r.split(","),f=c.split(","),h=0;h<m.length;h++){var p=h>=10?h:"0"+h;i.emotion_map[m[h]]=o+p+".png",h<100&&(i.emotion_mapShow[m[h]]=o+p+".png")}for(var v=0;v<c.length;v++){var y=v>=10?v:"0"+v;i.emotion_map[f[v]]=o+y+".png"}for(var b=0;b<l.length;b++){var w=b>=10?b:"0"+b;i.emotion_map[l[b]]=o+w+".png"}function S(){this.words=0,this.empty=1,this.index=0,this.children={}}function C(){u=T(d),g=new S,g.build(u)}function k(t){var e=g.search(t);return e.reverse().map((function(e){var s=e[0],n=u[e[1]],a='<img width="30px" src="'+d[n]+'" alt="'+n+'">';t=I(t,s,n.length,a)})),t}function I(t,e,s,n){return t.slice(0,e)+n+t.slice(e+s)}function T(t){var e=[];for(var s in t)t.hasOwnProperty(s)&&e.push(s);return e}function _(t){for(var e in t)i.emotion_map[e]=t[e],i.emotion_mapShow[e]=t[e]}S.prototype={insert:function(t,e,s){if(0!==t.length){var n,a,i=this;void 0===e&&(e=0),e!==t.length?(n=t[e],void 0===i.children[n]&&(i.children[n]=new S,i.empty=0,i.children[n].words=this.words+1),a=i.children[n],a.insert(t,e+1,s)):i.index=s}},build:function(t){for(var e=t.length,s=0;s<e;s++)this.insert(t[s],0,s)},searchOne:function(t,e){void 0===e&&(e=0);var s={};if(0===t.length)return s;var n,a,i=this;return s.arr=[],a=t[e],n=i.children[a],void 0!==n&&e<t.length?n.searchOne(t,e+1):void 0===n&&0===i.empty?s:1==i.empty?(s.arr[0]=e-i.words,s.arr[1]=i.index,s.words=i.words,s):s},search:function(t){if(1==this.empty)return[];for(var e,s=t.length,n=[],a=0;a<s-1;a++)e=this.searchOne(t,a),"undefined"!==typeof e.arr&&e.arr.length>0&&(n.push(e.arr),a=a+e.words-1);return n}},d=i.emotion_map,C(),d=i.emotion_map,e["a"]={parse:k,list:i.emotion_mapShow,updateList:_}},bb83:function(t,e,s){"use strict";var n=s("1ed0"),a=s.n(n);a.a},c059:function(t,e,s){"use strict";var n=s("0dbd"),a=s.n(n);a.a},cc5a:function(t,e,s){},d480:function(t,e,s){"use strict";var n=s("e87a"),a=s.n(n);a.a},d7a7:function(t,e,s){},d7fd:function(t,e,s){},dcb7:function(t,e,s){"use strict";var n,a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"history-box flex"},[s("div",{staticClass:"history-box-filter",staticStyle:{padding:"10px 5px"}},[s("div",{staticStyle:{"text-align":"center"}},[s("a-input-search",{staticStyle:{overflow:"hidden","max-width":"350px"},attrs:{placeholder:t.$t("message.placeholder"),maxLength:100},on:{search:t.onSearch},model:{value:t.key,callback:function(e){t.key=e},expression:"key"}},[s("a-icon",{attrs:{slot:"addonBefore",type:"calendar"},on:{click:function(e){e.stopPropagation(),t.toggleCalendar=!t.toggleCalendar}},slot:"addonBefore"})],1)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:t.toggleCalendar,expression:"toggleCalendar"}],staticStyle:{"text-align":"center"}},[s("a-range-picker",{attrs:{value:t.createValue,"show-time":""},on:{change:t.createChange},scopedSlots:t._u([{key:"dateRender",fn:function(e){return[s("div",{staticClass:"ant-calendar-date"},[t._v(" "+t._s(e.date())+" ")])]}}])})],1)]),s("div",{staticStyle:{"text-align":"center"}},[s("a-menu",{attrs:{mode:"horizontal"},model:{value:t.selectedTags,callback:function(e){t.selectedTags=e},expression:"selectedTags"}},t._l(t.typeList,(function(e,n){return s("a-menu-item",{key:e.key,attrs:{value:e.key}},[t._v(t._s(t.$t("type."+e.key)))])})),1)],1),1==t.pageMode?s("div",{directives:[{name:"viewer",rawName:"v-viewer",value:t.viewerOptions,expression:"viewerOptions"}],ref:"hisBox",staticClass:"history-box-list flex-item",staticStyle:{"overflow-x":"hidden"}},[s("MsgList",{ref:"msgListBoxVls",attrs:{sessionId:t.sessionId,history:!0,msgList:t.showMsgList}})],1):s("div",{directives:[{name:"viewer",rawName:"v-viewer",value:t.viewerOptions,expression:"viewerOptions"}],ref:"hisBox",staticClass:"history-box-list flex-item",staticStyle:{"overflow-x":"hidden"},on:{"&scroll":function(e){return t.onScroll.apply(null,arguments)}}},[s("div",{staticClass:"chat-box-msg-list"},t._l(t.showMsgList,(function(t,e){return s("div",{key:t.serialId},[s("Msg",{attrs:{msgOrigin:t,"msg-item":t,"msg-type":t.msgType,sender:t.sender||"system",history:!0}})],1)})),0)])])},i=[],o=(s("99af"),s("4de4"),s("13d5"),s("d3b7"),s("53ca")),r=s("a84e"),c=s("0e8c"),l={props:["sessionId","chatSessionId","isRecord"],components:{MsgList:c["a"],Msg:r["a"]},data:function(){return{startTime:null,endTime:null,endOpen:!1,key:"",pageMode:2,msgList:[],createValue:[],selectedTags:"ALL",toggleCalendar:!1,viewerOptions:{keyboard:!1,url:"data-url",filter:function(t){return"1"===t.dataset.enable}},typeList:[{key:"ALL",name:"全部"},{key:"image",name:"图片"},{key:"file",name:"文件"},{key:"video",name:"视频"},{key:"voice",name:"音频"}]}},mounted:function(){this.pageMode=2,this.getHistory()},computed:{showMsgList:function(){var t=this,e=this.msgList;e=e.filter((function(e){return"ALL"==t.selectedTags||t.selectedTags==e.msgType}));var s={},n=e.reduce((function(t,e){return!s[e.serialId]&&(s[e.serialId]=t.push(e)),t}),[]);return n.sort((function(t,e){return t.timestamp-e.timestamp})),n}},methods:{onScroll:function(t){0==this.$refs.hisBox.scrollTop&&this.loadMessage()},onSearch:function(){this.getHistory()},createChange:function(t,e){this.createValue=t,this.startTime=e[0],this.endTime=e[1]},showSerialIdMsg:function(t){var e=$('.history-box-list .chat-box-msg[data-serial-id="'+t+'"]')[0];e&&e.scrollIntoView&&e.scrollIntoView()},getHistory:function(t){var e=this,s={sessionId:this.sessionId,key:this.key,startTime:this.startTime,endTime:this.endTime,pageSize:50},a="object"==Object(o["a"])(this.selectedTags)?this.selectedTags[0]:this.selectedTags;if(s.msgType="ALL"==a?"":a,t&&(s=Object.assign({},t,s)),this.chatSessionId||this.isRecord)return s.chatSessionId=this.chatSessionId,void this.$ccbar.searchHisMessage(s).then((function(s){if(1==s.state){if(0==s.data.length)return;t?(e.msgList&&e.msgList.length>0&&(n=e.showMsgList[0].serialId),e.msgList=s.data.concat(e.msgList),n&&e.$nextTick((function(){this.showSerialIdMsg&&this.showSerialIdMsg(n),n=null}))):(e.msgList=s.data,e.$nextTick((function(){this.$refs.msgListBoxVls?this.$refs.msgListBoxVls.toBottom():this.$refs["hisBox"].scrollTop=99999})))}else e.$utils.message.warning({content:s.msg})}));this.$ccbar.loadHistory(s).then((function(s){if(1==s.state){if(0==s.data.length)return;t?(e.msgList&&e.msgList.length>0&&(n=e.showMsgList[0].serialId),e.msgList=s.data.concat(e.msgList),n&&e.$nextTick((function(){this.showSerialIdMsg&&this.showSerialIdMsg(n),n=null}))):(e.msgList=s.data,e.$nextTick((function(){this.$refs.msgListBoxVls?this.$refs.msgListBoxVls.toBottom():this.$refs["hisBox"].scrollTop=99999})))}else e.$utils.message.warning({content:s.msg})})).finally((function(t){n=null}))},loadMessage:function(){if(this.msgList.length>0){var t=this.msgList[0];this.getHistory({serialId:t.serialId,msgTime:t.msgTime,timeStamp:t.timestamp})}},onChange:function(t,e){e&&(this.selectedTags=e)},disabledStartDate:function(t){var e=this.endTime;return!(!t||!e)&&t.valueOf()>e.valueOf()},disabledEndDate:function(t){var e=this.startTime;return!(!t||!e)&&e.valueOf()>=t.valueOf()},handleStartOpenChange:function(t){t||(this.endOpen=!0)},handleEndOpenChange:function(t){this.endOpen=t},formatTime:function(t){return this.$webIM.formatTime(t)}}},d=l,g=(s("d480"),s("2877")),u=Object(g["a"])(d,a,i,!1,null,"668a727e",null);e["a"]=u.exports},e1bd:function(t,e,s){"use strict";var n=s("24e8"),a=s.n(n);a.a},e87a:function(t,e,s){},ebf4:function(t,e,s){"use strict";s("99af"),s("d3b7"),s("ac1f"),s("25f0"),s("3ca3"),s("5319"),s("ddb0");var n=s("5e9c"),a=s("1e65"),i=$("#ccbarBasePath").val()||"/yc-ccbar",o=location.origin,r=o+i+"/mediaEvent?action=",c=o.replace("http","ws")+i+"/websocket",l="/pc",d="",g="ws",u={agentId:null,skillInfo:null},m=!1,f=!0,h=!1,p=!1,v=5e3,y={},b=function(t,e,s){var a=r+t;return e=S(e),n["a"].request({url:a,data:e,success:s&&s.success})},w=function(t,e,s){var a=r+t;return e=S(e),n["a"].post({url:a,data:e,success:s&&s.success})},S=function(t){return{cmdJson:JSON.stringify(t)}},C=function(t){var e={agentId:"",entId:entId,token:"",productId:productId},s=Object.assign(e,t);return u.agentId=s.agentId,!1,b("SDKLogin",s)},k=function(t){var e={},s=Object.assign(e,t),i="CN"==localStorage["MULTI-LANG-KEY"]?"签入中":"Signing in..";n["a"].message.loading({content:i}),b("login",s).then((function(t){if("1"==t.state&&"succ"==t.data.code){g=t.data.result.token||"ws",y=t.data.result,t.data.result.media_add_chat_auth&&(p=t.data.result.media_add_chat_auth),t.data.result.media_withdraw_msg_auth&&(h=t.data.result.media_withdraw_msg_auth),t.data.result.media_video_start_auth&&t.data.result.media_video_start_auth,m=!0,K();var e="CN"==localStorage["MULTI-LANG-KEY"]?"签入成功":"Signed in successfully";n["a"].message.success({content:e}),a["a"].$emit("onAgentLogon",t)}else t.data&&t.data.content&&(console.log("Login fail,close ws:",t.data.content),n["a"].message.error({content:t.data.content}),t.data.code)}))},I=function(t){var e={messageId:"cmdNotReady"},s=Object.assign(e,{busyType:t});if(f=!1,!1,z)return b("event",s);k({readyState:"Busy",busyType:t})},T=function(t){var e={messageId:"cmdReady"},s=Object.assign(e,t);if(f=!0,!1,z)return b("event",s);k({readyState:"Ready"})},_=function(t){var e={messageId:"cmdLogout"},s=Object.assign(e,t);return!0,b("event",s)},x=function(){return b("chatList")},L=function(){return b("todayHisChatList")},O=function(t){return b("HisChatList",t)},j=function(){var t=x(),e=O({pageSize:10});return Promise.all([t,e])},A=function(t){var e={count:null},s=Object.assign(e,t);return b("limit",s)},N=function(t){return b("serviceInfo")},M=function(t){var e={sessionId:null,msgTime:null},s=Object.assign(e,t);return b("loadMessage",s)},E=function(t,e){return b("agentList",{sessionId:t,channelKey:e})},P=function(t){return b("agentList",t)},R=function(t){return b("findSessionMessage",t)},D=function(t,e,s){return b("loadHistMessage",{serialId:t,msgTime:e},{success:s})},B=function(t,e){var s;return s="string"==typeof t?{sessionId:t}:t,b("custHistChatList",s,{success:e})},F=function(t,e){var s;return s="string"==typeof t?{sessionId:t}:t,w("HisChatPageList",s,{success:e})},U=function(t,e){return b("HisUserList",t,{success:e})},H=function(t,e){return b("searchHisMessage",t,{success:e})},z=!1,G=null,K=function(t){if(!G||G.readyState!=WebSocket.OPEN&&G.readyState!=WebSocket.CONNECTING){(new Date).getTime();var e=t?"reload":"init",s=c+"/"+g+"/"+e+l,a="CN"==localStorage["MULTI-LANG-KEY"]?"服务连接中":"CONNECTING...";n["a"].message.loading({content:a}),G=new WebSocket(s),G.onmessage=W,G.onopen=V,G.onclose=q,G.onerror=J}else console.log("ws is open",G.readyState)},W=function(t){(new Date).getTime();var e=JSON.parse(t.data);if(e.messageId&&"queueUp"==e.messageId)return console.log("queueUp事件",e),void(window.notSamesite||"BUSY"!=top.CallControl.state||(T(),console.log((new Date).toString()+"状态",top.CallControl)));a["a"].$emit("wsMessage",t.data)},V=function(t){z=!0,console.log("WebSocket连接已打开！");var e="CN"==localStorage["MULTI-LANG-KEY"]?"服务连接成功":"Connect success";n["a"].message.success({content:e}),setTimeout((function(){1==f?T():I()}),500),tt(),a["a"].$emit("wsOpen",t)},q=function(t){z=!1,console.log("WebSocket连接已关闭！",t),a["a"].$emit("wsClose",t),window.UnifiedRoute&&T(),1e3!=t.code&&m&&Q()},J=function(t){console.log("wsError",t),a["a"].$emit("wsError",t),window.UnifiedRoute&&T()},Y=function(t){t&&(m=!1),G.close()},Q=function(){console.log("重连?","[ws链接]",z,"[签入]",m,!z&&m),!z&&m&&K(!0)};function X(t,e,s,a){var i={event:t,action:e};return i=Object.assign({},i,s),i="string"==typeof i?i:JSON.stringify(i),new Promise((function(e,s){if(G.readyState!==WebSocket.OPEN){var a={0:"CONNECTING",1:"OPEN",2:"CLOSING",3:"CLOSED"};if("MsgMonitor"==t)return;var o="[".concat(G.readyState,"] WebSocket Service ").concat(a[G.readyState]||"UNKNOW");throw e(G.readyState),s(o),n["a"].message.error({content:o}),newError(o)}G.send(i),e(G.readyState)})).catch((function(t){console.log("catch:",t)}))}var Z=null,tt=function t(e){var s=0;for(var n in sessionList)0==sessionList[n].offline&&s++;z&&X("heartbeat","sendMessage",S({msgType:"heartbeat",onlineCount:s})),z&&m&&!e&&(Z&&clearTimeout(Z),Z=setTimeout(t,v))};function et(t){g=t,m=!!t}var st=function(t,e,s,n){var a=Object.assign({},{event:e,msgType:"event",sessionId:t,eventData:s},n);return X("mediaEvent","sendMessage",{cmdJson:JSON.stringify(a)})};function nt(t,e,s){console.log("撤回消息",e,"-",t),st(e,"withdraw",{serialId:t,sessionId:e,chatSessionId:s.chatSessionId},{serialId:t,sessionId:e,chatSessionId:s.chatSessionId})}function at(t,e){console.log("挂起",t,"-",e),st(t,"chatHangup",{sessionId:t,chatSessionId:e},{sessionId:t,chatSessionId:e})}function it(t,e){console.log("取消挂起",t,"-",e),st(t,"chatNotHangup",{sessionId:t,chatSessionId:e},{sessionId:t,chatSessionId:e})}function ot(t){}function rt(t){var e=localStorage.getItem("userAccount")||"",s=!t||t==e;return h&&s}function ct(){return p}function lt(){return{login:m,ws:z}}function dt(){return y}function gt(t){return b("setCustName",t)}function ut(t){v=t}window.onbeforeunload=function(){console.log("==>close page and close ws"),G.close()},e["a"]={sdkLogin:C,ready:T,notReady:I,login:k,logoff:_,chatList:x,todayChatList:L,hisChatList:O,limit:A,serviceInfo:N,getChatList:j,agentList:E,agentListForCenter:P,loadMessage:M,sendMessage:X,wsInit:K,uploadPath:d,isLogin:m,close:Y,host:o,reconnect:Q,setLoginState:et,heartbeat:tt,sendCommand:st,loadHistory:R,loadHistoryBySerialId:D,custHistChatList:B,hisUserList:U,searchHisMessage:H,rollback:nt,chatHangup:at,chatNotHangup:it,isWsConnect:z,setState:ot,mediaWithdrawMsgAuth:rt,mediaAddChatAuth:ct,getLoginState:lt,HisChatPageList:F,getloginResult:dt,setCustName:gt,updateHeartbeatTime:ut}}}]);