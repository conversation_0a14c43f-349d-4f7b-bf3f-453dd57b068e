(function(t){function e(e){for(var i,o,l=e[0],c=e[1],r=e[2],u=0,h=[];u<l.length;u++)o=l[u],Object.prototype.hasOwnProperty.call(a,o)&&a[o]&&h.push(a[o][0]),a[o]=0;for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(t[i]=c[i]);d&&d(e);while(h.length)h.shift()();return n.push.apply(n,r||[]),s()}function s(){for(var t,e=0;e<n.length;e++){for(var s=n[e],i=!0,l=1;l<s.length;l++){var c=s[l];0!==a[c]&&(i=!1)}i&&(n.splice(e--,1),t=o(o.s=s[0]))}return t}var i={},a={app:0},n=[];function o(e){if(i[e])return i[e].exports;var s=i[e]={i:e,l:!1,exports:{}};return t[e].call(s.exports,s,s.exports,o),s.l=!0,s.exports}o.m=t,o.c=i,o.d=function(t,e,s){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:s})},o.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var s=Object.create(null);if(o.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)o.d(s,i,function(e){return t[e]}.bind(null,i));return s},o.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="";var l=window["webpackJsonp"]=window["webpackJsonp"]||[],c=l.push.bind(l);l.push=e,l=l.slice();for(var r=0;r<l.length;r++)e(l[r]);var d=c;n.push([0,"chunk-vendors","chunk-common"]),s()})({0:function(t,e,s){t.exports=s("56d7")},"19d0":function(t,e,s){"use strict";s.d(e,"a",(function(){return i})),s.d(e,"b",(function(){return a}));var i=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div")},a=[]},2395:function(t,e,s){},"33a2":function(t,e,s){"use strict";s("b681")},"4f9a":function(t,e,s){"use strict";s("b9ee")},"56d7":function(t,e,s){"use strict";s.r(e);s("0fb4"),s("450d");var i=s("9944"),a=s.n(i),n=(s("28b2"),s("c7ad")),o=s.n(n),l=(s("0c67"),s("299c")),c=s.n(l),r=(s("560b"),s("dcdc")),d=s.n(r),u=(s("e612"),s("dd87")),h=s.n(u),g=(s("075a"),s("72aa")),p=s.n(g),m=(s("be4f"),s("896a")),f=s.n(m),v=(s("279e"),s("7d94")),C=s.n(v),w=(s("186a"),s("301f")),b=s.n(w),y=(s("96dc"),s("9cea")),_=s.n(y),x=(s("06f1"),s("6ac9")),S=s.n(x),T=(s("b5d8"),s("f494")),k=s.n(T),O=(s("e3ea"),s("7bc3")),M=s.n(O),L=(s("a7cc"),s("df33")),E=s.n(L),I=(s("920a"),s("4f0c")),N=s.n(I),H=(s("fd71"),s("a447")),U=s.n(H),R=(s("b8e0"),s("a4c4")),D=s.n(R),A=(s("3db2"),s("58b8")),B=s.n(A),P=(s("acb6"),s("c673")),F=s.n(P),z=(s("f225"),s("89a9")),V=s.n(z),j=(s("aaa5"),s("a578")),G=s.n(j),q=(s("cbb5"),s("8bbc")),K=s.n(q),Q=(s("10cb"),s("f3ad")),Y=s.n(Q),W=(s("ae26"),s("845f")),J=s.n(W),X=(s("1951"),s("eedf")),Z=s.n(X),tt=(s("e260"),s("e6cf"),s("cca6"),s("a79d"),s("ac1f"),s("466d"),s("db4d"),s("2b0e")),et=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"app"}},[s("pc")],1)},st=[],it=(s("1e25"),s("00b4"),s("fb6a"),s("4d63"),s("c607"),s("2c3e"),s("25f0"),function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"chat-box",class:t.clientType},[s("div",{staticClass:"sidebar-nav"},[s("el-tooltip",{staticClass:"item",attrs:{"hide-after":2e3,effect:"dark",content:"回到顶部",placement:"left"}},[s("div",{on:{click:t.scrollTopMsg}},[s("el-avatar",{attrs:{size:36,icon:"el-icon-caret-top"}})],1)]),s("el-tooltip",{staticClass:"item",attrs:{"hide-after":2e3,effect:"dark",content:"电话联系",placement:"left"}},[t.channelConfig.TEL_NUM?s("div",{on:{click:t.callTel}},[s("el-avatar",{staticClass:"green",attrs:{size:36,icon:"el-icon-phone-outline"}})],1):t._e()]),s("el-tooltip",{staticClass:"item",attrs:{"hide-after":2e3,effect:"dark",content:"刷新聊天记录",placement:"left"}},[s("div",{on:{click:t.handleRefresh}},[s("el-avatar",{staticClass:"yellow",attrs:{size:36,icon:"el-icon-refresh"}})],1)]),t.showSearchBtn?s("el-tooltip",{staticClass:"item",attrs:{"hide-after":2e3,effect:"dark",content:"查找聊天记录",placement:"left"}},[s("div",{on:{click:function(e){t.showHistory=!t.showHistory}}},[s("el-avatar",{staticClass:"blue",attrs:{size:36,icon:"el-icon-search"}})],1)]):t._e()],1),s("div",{staticClass:"style",domProps:{innerHTML:t._s(t.customerStyle)}}),s("el-dialog",{staticClass:"select-service",attrs:{title:t.$t("message.selectService"),"custom-class":"custom-dialog-class","close-on-click-modal":!1,"modal-append-to-body":!1,"show-close":!1,"close-on-press-escape":!1,visible:t.setting.showKeys},on:{"update:visible":function(e){return t.$set(t.setting,"showKeys",e)}}},[s("div",{staticClass:"chat-channel-keys fiveclass"},t._l(t.keys,(function(e){return s("div",{key:t.index,staticClass:"link-item link-item-grid",on:{click:function(s){return t.selectKey(e)}}},[e.IMG_URL?s("el-image",{staticStyle:{width:"30px",height:"30px"},attrs:{fit:"contain",src:e.IMG_URL}}):s("i",{staticClass:"el-icon-menu",staticStyle:{"font-size":"30px"}}),s("span",{staticClass:"item-text",staticStyle:{flex:"1","vertical-align":"top"},domProps:{textContent:t._s(e.KEY_NAME)}}),s("i",{staticClass:"el-icon-arrow-right item-text-arr"})],1)})),0)]),s("el-dialog",{attrs:{title:t.$t("title.message"),"custom-class":"custom-dialog-class",visible:t.isMessage,width:t.msgWidth,"modal-append-to-body":!1},on:{"update:visible":function(e){t.isMessage=e},open:t.openCallBack,close:t.closeMsgCallBack}},[s("div",{staticClass:"message-wrapper"},[s("iframe",{attrs:{src:t.msgUrl,frameborder:"0",id:"message",width:"100%",height:"100%"}})])]),t.hwHeader?s("div",{staticClass:"hw-style-box-header flex-row"},[t._m(0),s("div",{staticClass:"hw-style-box-title flex-item textoverflow",domProps:{textContent:t._s(t.urlTile||t.channelConfig.H5_TITLE)}}),s("div",{staticClass:"header-tools"},[t.isIframe?s("i",{staticClass:"el-icon-minus header-tools-icon",on:{click:t.miniSizePage}}):t._e(),s("i",{directives:[{name:"show",rawName:"v-show",value:t.hotImgList.length>0||t.hotQuestionList.length>0||t.personList.length>0,expression:"(hotImgList.length>0 || hotQuestionList.length>0 || personList.length>0)"}],staticClass:"header-tools-icon",class:t.showSide?"el-icon-s-unfold":"el-icon-s-fold",on:{click:function(e){t.showSide=!t.showSide}}}),s("i",{staticClass:" header-tools-icon",class:t.audioNotify?"el-icon-bell":"el-icon-close-notification",on:{click:t.audioToggle}})])]):s("div",{directives:[{name:"show",rawName:"v-show",value:t.showHeader,expression:"showHeader"}],staticClass:"chat-box-header"},[t.channelConfig.H5_LOGO_URL?s("el-image",{staticClass:"logo",staticStyle:{height:"50px",width:"inherit","margin-right":"10px"},attrs:{src:t.channelConfig.H5_LOGO_URL}}):t._e(),s("div",{staticClass:"title",on:{dblclick:t.initDebug}},[s("span",{domProps:{textContent:t._s(t.channelConfig.H5_TITLE||t.$t("title.onlineService")||"在线客服")}}),s("div",{staticClass:"sub-title",domProps:{textContent:t._s(t.channelConfig.H5_SUB_TITLE)}})]),s("div",{staticClass:"header-tools"},[t.isIframe?s("i",{staticClass:"el-icon-minus header-tools-icon",on:{click:t.miniSizePage}}):t._e(),s("i",{directives:[{name:"show",rawName:"v-show",value:t.hotImgList.length>0||t.hotQuestionList.length>0||t.personList.length>0,expression:"(hotImgList.length>0 || hotQuestionList.length>0 || personList.length>0)"}],staticClass:"header-tools-icon",class:t.showSide?"el-icon-s-unfold":"el-icon-s-fold",on:{click:function(e){t.showSide=!t.showSide}}}),s("i",{staticClass:" header-tools-icon",class:t.audioNotify?"el-icon-bell":"el-icon-close-notification",on:{click:t.audioToggle}})])],1),s("div",{staticClass:"chat-box-content",staticStyle:{position:"relative"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"chat-box-content-area",staticStyle:{position:"absolute",width:"100%",height:"100%",left:"0",top:"0","z-index":"3",background:"#fff"}},[s("div",{staticClass:"flex"},[s("div",{staticClass:"chat-search"},[s("el-input",{staticClass:"input-with-select",attrs:{placeholder:"请输入搜索关键字"},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}},[s("el-button",{attrs:{slot:"append",icon:"el-icon-search"},slot:"append"})],1)],1),s("div",{staticClass:"flex-item"},t._l(t.searchList,(function(e,i){return s("Msg",{key:i,attrs:{index:i,history:!0,"msg-item":e,preMsgTime:i>0?t.searchList[i-1].msgTime:0}})})),1)])]),s("div",{staticClass:"chat-box-content-area"},[s("div",{staticClass:"chat-box-main"},[s("el-drawer",{staticStyle:{width:"100%",height:"100%","z-index":"2333"},attrs:{title:"查找历史",visible:t.showHistory,direction:"ttb",modal:!1,size:"100%",keyword:t.searchKeyword1,"custom-class":"his-drawer"},on:{"update:visible":function(e){t.showHistory=e}}},[s("History",{ref:"hisBox"})],1),s("div",{staticClass:"flex",staticStyle:{position:"relative"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.agentInputState,expression:"agentInputState"}],staticClass:"agentInputState-msg"},[t._v(t._s(t.$t("message.typing")))]),t.channelConfigData.ADVERT_MSG&&t.channelConfigData.ADVERT_MSG.trim()?s("div",{directives:[{name:"show",rawName:"v-show",value:t.showAdv,expression:"showAdv"}],staticClass:"adv-msg"},[s("div",{staticClass:"flex-row"},[s("i",{staticClass:"el-icon-chat-line-round",staticStyle:{"font-size":"22px","margin-right":"5px"}}),s("div",{staticClass:"flex-item ad-msg-detail",style:t.adTransform,attrs:{id:"adLines"},domProps:{innerHTML:t._s(t.channelConfigData.ADVERT_MSG)},on:{click:function(e){return e.stopPropagation(),t.showAdvDetail(t.channelConfigData.ADVERT_MSG)}}})]),s("i",{staticClass:"el-icon-close myclose",staticStyle:{position:"absolute","font-size":"14px",top:"50%",right:"3px",color:"#000",transform:"translateY(-50%)",border:"1px solid transparent",padding:"5px"},on:{click:function(e){e.stopPropagation(),t.showAdv=!1}}})]):t._e(),s("div",{staticClass:"flex-item",staticStyle:{overflow:"hidden",position:"relative","z-index":"1"},attrs:{"data-v-loading":"loadingHis"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.percent,expression:"percent"}],staticClass:"agentInputState-msg",staticStyle:{width:"100%",top:"inherit",bottom:"0","box-sizing":"border-box",left:"0"}},[s("div",{staticClass:"upload-bar",staticStyle:{height:"100%","min-width":"100px"},style:{width:t.percent+"%"}},[t._v(" "+t._s(t.$t("message.uploading"))+" "+t._s(t.percent+"%")+" ")])]),t.previewMsg?s("div",{staticClass:"msg-presend"},[1==t.urlObj.inviteFlag?[s("p",{staticStyle:{"margin-bottom":"15px"}},[t._v(t._s(t.$t("message.invite")))])]:[s("p",[t._v(t._s(t.$t("message.ask")))]),s("Msg",{attrs:{msgItem:t.previewMsg}})],s("div",{staticClass:"clipboard-box-btns"},[s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){t.previewMsg=null}}},[t._v(t._s(t.$t("btn.cancel")))]),s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.previewMsgSend}},[t._v(t._s(this.msgOkText||t.$t("btn.send")))])],1)],2):t._e(),s("div",{ref:"msgBox",staticClass:"chat-area",staticStyle:{height:"100%",overflow:"auto"},on:{"&scroll":function(e){return t.onScroll.apply(null,arguments)}}},[s("div",{staticClass:"ex-box"}),s("div",{directives:[{name:"viewer",rawName:"v-viewer"}],staticClass:"message-list",on:{touchstart:function(e){return e.stopPropagation(),t.hideInputFocus.apply(null,arguments)}}},[s("div",{staticClass:"load-more-btn"},[s("span",{on:{click:function(e){return e.stopPropagation(),t.loadChatRecord()}}},[t._v(t._s(t.$t("btn.history")))])]),t._l(t.historyMsgList,(function(e,i){return s("Msg",{key:i,attrs:{index:i,history:!0,"msg-item":e,preMsgTime:i>0?t.historyMsgList[i-1].msgTime:0}})})),t.historyMsgList.length>0?s("div",{staticClass:"message-system-tips"},[t._v(t._s(t.$t("message.history")))]):t._e(),t.channelConfig.showWelcome||t.urlObj.showWelcome?s("WelcomeCard",{attrs:{clientData:t.clientData},on:{clickItem:t.sendHot}}):t._e(),t._l(t.msgList,(function(e,i){return s("Msg",{directives:[{name:"show",rawName:"v-show",value:"queue"!=e.event||e.serialId==t.lastQueue,expression:"msgItem.event!='queue' || msgItem.serialId == lastQueue"}],key:i,ref:e.serialId,refInFor:!0,attrs:{history:!1,"msg-item":e,preMsgTime:i>0?t.msgList[i-1].msgTime:0}})}))],2)])]),t.bottomNavList.length>0||t.personList.length>0||t.hotQuestionList.length>0?s("div",{staticClass:"chat-box-recommond"},[s("swiper",{ref:"mySwiper",attrs:{options:t.bottomNavbSwiperOption}},[t.hotQuestionList.length>0?s("swiper-slide",[s("el-popover",{ref:"hotRef",attrs:{width:"300",trigger:"click"}},[s("div",t._l(t.hotQuestionList,(function(e,i){return s("div",{key:i,staticClass:"link-item",on:{click:function(s){return t.sendHot(e)}}},[s("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"mini",effect:"plain"}},[t._v(t._s(i+1))]),s("span",{staticStyle:{flex:"1"},domProps:{textContent:t._s(e.questionName)}}),s("i",{staticClass:"el-icon-arrow-right",staticStyle:{float:"right"}})],1)})),0),s("el-button",{attrs:{slot:"reference",size:"mini",round:""},slot:"reference"},[t._v(t._s(t.$t("message.consult")))])],1)],1):t._e(),t.personList.length>0?s("swiper-slide",[s("el-popover",{attrs:{width:"300",trigger:"click"}},[s("div",t._l(t.personList,(function(e,i){return s("div",{key:i,staticClass:"link-item link-item-grid",on:{click:function(s){return t.sendHot(e)}}},[e.picUrl?s("el-image",{staticStyle:{width:"30px",height:"30px"},attrs:{fit:"contain",src:e.picUrl}}):s("i",{staticClass:"el-icon-menu",staticStyle:{"font-size":"30px"}}),s("span",{staticClass:"item-text",staticStyle:{flex:"1","vertical-align":"top"},domProps:{textContent:t._s(e.questionName)}}),s("i",{staticClass:"el-icon-arrow-right item-text-arr"})],1)})),0),s("el-button",{attrs:{slot:"reference",size:"mini",round:""},slot:"reference"},[t._v(t._s(t.$t("message.service")))])],1)],1):t._e(),"1"==t.channelConfig.SHOW_END_NAV&&t.showEndChat?s("swiper-slide",[s("el-button",{attrs:{size:"mini",round:""},on:{click:function(e){return e.stopPropagation(),t.endChat.apply(null,arguments)}}},[t._v("结束会话")])],1):t._e(),t._l(t.bottomNavList,(function(e,i){return s("swiper-slide",[s("el-button",{key:i,attrs:{size:"mini",round:""},on:{click:function(s){return s.stopPropagation(),t.clickNav(e)}}},[t._v(t._s(e.questionName))])],1)}))],2)],1):t._e(),s("div",{staticClass:"chat-box-footer"},["mobile"==t.clientType?s("div",{directives:[{name:"show",rawName:"v-show",value:t.visibleRecord,expression:"visibleRecord "}],staticClass:"record-mobile-box",style:{opacity:t.recording?1:0}},[s("div",{staticClass:"mainBox"},[s("div",{staticClass:"ctrlProcessWave",staticStyle:{height:"50px","vertical-align":"bottom"}}),s("div",{staticClass:"ctrlProcessT",staticStyle:{position:"relative","text-align":"center"}},[t._v(t._s(t.durationTime(t.duration)+"/0:10.00"))])])]):t._e(),""!=t.srcUrl?s("div",{staticClass:"clipboard-box"},[s("div",{domProps:{textContent:t._s(t.$t("message.screenshot"))}}),s("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:t.srcUrl,fit:"contain"}}),s("div",{staticClass:"clipboard-box-btns"},[s("el-button",{attrs:{size:"mini",round:""},on:{click:t.hideSc}},[t._v(t._s(t.$t("btn.cancel")))]),s("el-button",{attrs:{size:"mini",round:""},on:{click:t.sendSc}},[t._v(t._s(t.$t("btn.send")))])],1)],1):t._e(),s("div",{staticClass:"chat-input-box"},[s("div",{staticClass:"flex-row"},["mobile"==t.clientType&&t.showRecord?s("a",{directives:[{name:"show",rawName:"v-show",value:!t.visibleRecord,expression:"!visibleRecord"}],staticClass:"m-chat-btn",attrs:{href:"javascript:void(0);"},on:{click:t.toggleRecordShow}},[s("i",{staticClass:"icon-maikefeng-XDY iconfont"})]):t._e(),"mobile"==t.clientType&&t.showRecord?s("a",{directives:[{name:"show",rawName:"v-show",value:t.visibleRecord,expression:"visibleRecord"}],staticClass:"m-chat-btn",attrs:{href:"javascript:void(0);"},on:{click:t.toggleRecordShow}},[s("i",{staticClass:"icon-chat iconfont"})]):t._e(),s("a",{staticClass:"m-chat-btn",attrs:{href:"javascript:void(0);",title:"表情/emoji"},on:{click:t.toggleMobileEmoji}},[s("i",{staticClass:"icon-emoji iconfont"})]),s("div",{directives:[{name:"show",rawName:"v-show",value:"mobile"==t.clientType&&t.visibleRecord,expression:"clientType == 'mobile' && visibleRecord"}],staticClass:"start-record flex-item",on:{touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.recStart.apply(null,arguments)},touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.recStop.apply(null,arguments)}}},[s("span",{directives:[{name:"show",rawName:"v-show",value:!t.recording,expression:"!recording"}]},[t._v("按住录音")]),s("span",{directives:[{name:"show",rawName:"v-show",value:t.recording,expression:"recording"}]},[t._v("松开结束")])]),s("el-autocomplete",{directives:[{name:"show",rawName:"v-show",value:"pc"==t.clientType||!t.visibleRecord||!t.showRecord,expression:"clientType == 'pc' || !visibleRecord || !showRecord"}],ref:"autoBox",staticClass:"flex-item",class:{"show-suggestion":t.suggestionsList.length>0},attrs:{"popper-class":"im-autocomplete","popper-append-to-body":(t.clientType,!1),"fetch-suggestions":t.querySearchAsync,"hide-loading":!0,placement:"bottom-start",placeholder:t.channelConfig.CHAT_INPUT_TIPS?t.channelConfig.CHAT_INPUT_TIPS:t.$t("message.placeholder"),autosize:{minRows:t.rows,maxRows:4},type:"textarea","select-when-unmatched":!1,rows:t.rows,maxlength:800,"trigger-on-focus":!1,"show-word-limit":"",resize:"none"},on:{focus:t.inputFocusFunc,blur:function(e){t.inputFocus=!1},select:t.handleSelect},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.sendTextByEnter.apply(null,arguments)}},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.item;return[s("div",{staticClass:"name",domProps:{innerHTML:t._s(t.highLight(i.hlQuestion))}})]}}]),model:{value:t.inputVal,callback:function(e){t.inputVal=e},expression:"inputVal"}}),s("div",{staticClass:"drag-upload"},[s("el-upload",{ref:"uploadFile3",staticClass:"upload-demo",staticStyle:{position:"absolute",top:"0",left:"0",width:"100%",height:"70%","z-index":"3"},attrs:{drag:"","before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,action:t.uploadUrl,"on-success":t.handleUploadFile,limit:1}},[s("i",{staticClass:"el-icon-upload"})])],1),t.showPlus?s("a",{staticClass:"m-chat-btn m-plus",attrs:{href:"javascript:void(0);",title:"切换/toggle"},on:{click:t.toggleMobileTool}},[s("i",{staticClass:"icon-tianjia-xue iconfont"})]):t._e(),""!=t.inputVal&&t.isMini?s("a",{staticClass:"el-button chat-send el-button--default el-button--mini is-plain is-round",staticStyle:{"line-height":"16px"},attrs:{size:"mini",round:"",plain:""},on:{touchstart:function(e){return e.stopPropagation(),e.preventDefault(),t.sendText.apply(null,arguments)},click:t.sendText}},[t._v(t._s(t.$t("btn.send")))]):t._e()],1)]),s("div",{staticClass:"chat-footer-toolbar"},[s("el-popover",{ref:"emojiBox",attrs:{placement:"top",width:"400",trigger:"click"},model:{value:t.showEmoji,callback:function(e){t.showEmoji=e},expression:"showEmoji"}},[1==t.showEmoji?s("Emoji",{on:{select:t.addEmojiFunc}}):t._e(),s("a",{staticClass:"toobar-btn",attrs:{slot:"reference",href:"javascript:void(0);",title:t.$t("btn.selectEmoji"),id:"addEmoji"},slot:"reference"},[s("i",{staticClass:"icon-emoji iconfont"})])],1),"0"!==t.channelConfig.BTN_AUTH_PIC?s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:t.$t("btn.selectImage"),"data-upload-check":"images",id:"imgUpload"}},[s("i",{staticClass:"icon-tupian iconfont"}),s("el-upload",{ref:"uploadImg1",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,accept:"image/*",action:t.uploadUrl,"on-success":t.handleUploadImage,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1):t._e(),"0"!==t.channelConfig.BTN_AUTH_FILE?s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:t.$t("btn.selectFile"),id:"addFile","data-upload-check":"files"}},[s("i",{staticClass:"icon-wendang iconfont"}),s("el-upload",{ref:"uploadFile1",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,action:t.uploadUrl,"on-success":t.handleUploadFile,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1):t._e(),t.showVideo||"0"===t.channelConfig.BTN_AUTH_VIDEO?t._e():s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:t.$t("btn.selectVideo"),"data-upload-check":"videos",id:"addVideo"}},[s("i",{staticClass:"icon-shiping-xue iconfont"}),s("el-upload",{ref:"uploadVideo1",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,action:t.uploadUrl,accept:"video/*","on-success":t.handleUploadVideo,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1),t.showVideo?s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:t.$t("btn.selectVideo")},on:{click:t.inviteAgentVideo}},[s("i",{staticClass:"icon-shiping-xue iconfont"})]):t._e(),t.showVideo?s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:"语音通话"},on:{click:t.inviteAgentVideoSound}},[s("i",{staticClass:"icon-zuoxi iconfont"})]):t._e(),"pc"==t.clientType&&t.showRecord?s("el-popover",{attrs:{placement:"top",title:"录音",width:"200",trigger:"manual"},on:{show:t.recOpen},model:{value:t.visibleRecord,callback:function(e){t.visibleRecord=e},expression:"visibleRecord"}},[t._t("default",(function(){return[s("div",{staticClass:"mainBox"},[s("div",{staticClass:"ctrlProcessWave",staticStyle:{height:"50px","vertical-align":"bottom"}}),s("div",{staticClass:"ctrlProcessT",staticStyle:{position:"relative","text-align":"center"}},[t._v(t._s(t.durationTime(t.duration)+"/0:10.00"))])]),t.recording?t._e():s("el-button",{staticStyle:{width:"100%",display:"block","margin-top":"10px"},attrs:{size:"small",type:"primary"},on:{click:function(e){return e.stopPropagation(),t.recStart.apply(null,arguments)}}},[t._v("开始")]),t.recording?s("el-button",{staticStyle:{width:"100%",display:"block","margin-top":"10px"},attrs:{size:"small",type:"danger"},on:{click:function(e){return e.stopPropagation(),t.recStop.apply(null,arguments)}}},[t._v("停止")]):t._e(),s("audio",{ref:"LogAudioPlayer"})]})),s("div",{staticClass:"toobar-btn",attrs:{slot:"reference",href:"javascript:void(0);",title:t.$t("btn.selectVoice")},on:{click:function(e){t.visibleRecord=!t.visibleRecord}},slot:"reference"},[s("i",{staticClass:"icon-maikefeng-XDY iconfont"})])],2):"0"!==t.channelConfig.BTN_AUTH_AUDIO?s("div",{staticClass:"toobar-btn",attrs:{href:"javascript:void(0);","data-upload-check":"audios",title:t.$t("btn.selectVoice"),id:"addVoice"}},[s("i",{staticClass:"icon-maikefeng-XDY iconfont"}),s("el-upload",{ref:"uploadVoice1",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,accept:"audio/*",action:t.uploadUrl,"on-success":t.handleUploadVoice,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1):t._e(),s("div",{directives:[{name:"show",rawName:"v-show",value:t.showSatisfy,expression:"showSatisfy"}],staticClass:"toobar-btn",attrs:{href:"javascript:void(0);",title:t.$t("btn.satisfy")},on:{click:function(e){return e.stopPropagation(),t.userSatisfy.apply(null,arguments)}}},[s("i",{staticClass:"el-icon-star-off"})]),t.isMsgBtn?s("div",{staticClass:"toobar-btn",on:{click:t.openMessage}},[s("i",{staticClass:"el-icon-chat-line-square"})]):t._e(),s("el-button",{directives:[{name:"preventClick",rawName:"v-preventClick"}],staticClass:"chat-send",attrs:{type:"success",size:"small",plain:""},on:{"&touchstart":function(e){return e.stopPropagation(),t.sendText.apply(null,arguments)},click:t.sendText}},[t._v(t._s(t.$t("btn.send")))])],1),s("div",{staticClass:"chat-footer-toolbar-mobile clear",class:{fadein:!t.inputFocus&&t.showMobileEmoji}},[1==t.showMobileEmoji?s("Emoji",{on:{select:t.addText}}):t._e()],1),s("div",{staticClass:"chat-footer-toolbar-mobile clear",class:{fadein:!t.inputFocus&&t.showMobileTool}},["0"!==t.channelConfig.BTN_AUTH_PIC?s("div",{staticClass:"m-tool upload-img",attrs:{"data-type":"image","data-upload-check":"images"}},[s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-tupian iconfont"}),s("el-upload",{ref:"uploadImg2",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,accept:"image/*",action:t.uploadUrl,"on-success":t.handleUploadImage,capture:"camera",limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.selectImage")))])]):t._e(),"0"!==t.channelConfig.BTN_AUTH_FILE?s("div",{staticClass:"m-tool upload-file",attrs:{"data-type":"file","data-upload-check":"files"}},[s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-wendang iconfont"}),s("el-upload",{ref:"uploadFile2",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,action:t.uploadUrl,"on-success":t.handleUploadFile,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.selectFile")))])]):t._e(),t.showVideo||"0"===t.channelConfig.BTN_AUTH_VIDEO?t._e():s("div",{staticClass:"m-tool upload-file",attrs:{"data-type":"file","data-upload-check":"videos"}},[s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-shiping-xue iconfont"}),s("el-upload",{ref:"uploadVideo2",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,accept:"video/*",action:t.uploadUrl,"on-success":t.handleUploadVideo,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.selectVideo")))])]),t.showVideo?s("div",{staticClass:"m-tool upload-video",attrs:{"data-type":"video"},on:{click:t.inviteAgentVideo}},[t._m(1),s("span",{staticClass:"m-tool-label"},[t._v("视频")])]):t._e(),t.showVideo?s("div",{staticClass:"m-tool upload-video",attrs:{"data-type":"video"},on:{click:t.inviteAgentVideoSound}},[t._m(2),s("span",{staticClass:"m-tool-label"},[t._v("通话")])]):t._e(),"0"!==t.channelConfig.BTN_AUTH_AUDIO?s("div",{staticClass:"m-tool upload-file",attrs:{"data-type":"file","data-upload-check":"audios"}},[s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-maikefeng-XDY iconfont"}),s("el-upload",{ref:"uploadVoice2",staticClass:"upload-demo",staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{"before-upload":t.testBeforeUpload,"on-progress":t.uploadingFile,accept:"audio/*",action:t.uploadUrl,"on-success":t.handleUploadVoice,limit:1}},[s("el-button",{staticStyle:{opacity:"0","z-index":"2",position:"absolute",width:"100%",height:"100%",top:"0",left:"0"},attrs:{size:"small",type:"primary"}},[t._v("点击上传")])],1)],1),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.selectVoice")))])]):t._e(),s("div",{directives:[{name:"show",rawName:"v-show",value:t.showSatisfy,expression:"showSatisfy"}],staticClass:"m-tool ",on:{click:function(e){return e.stopPropagation(),t.userSatisfy.apply(null,arguments)}}},[t._m(3),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.satisfy")))])]),t.isMsgBtn?s("div",{staticClass:"m-tool",on:{click:t.openMessage}},[t._m(4),s("span",{staticClass:"m-tool-label"},[t._v(t._s(t.$t("btn.message")))])]):t._e()])])])],1),"mobile"==t.clientType&&t.showSide?s("div",{staticClass:"chat-box-side-mask show",on:{click:function(e){t.showSide=!t.showSide}}}):t._e(),t.hotImgList.length>0||t.hotQuestionList.length>0||t.personList.length>0?s("div",{staticClass:"chat-box-side ",class:{show:t.showSide}},[t.channelConfig.LOGO?s("img",{staticClass:"side-logo",attrs:{src:t.channelConfig.LOGO}}):t._e(),t.hotImgList.length>0?s("el-carousel",{staticStyle:{"margin-bottom":"10px"},attrs:{height:"150px",autoplay:!0,interval:5e3,"indicator-position":"none"}},t._l(t.hotImgList,(function(e){return s("el-carousel-item",{key:e,on:{click:function(s){return t.jumpPage(e)}}},[s("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{fit:"contain",src:e.picUrl},on:{click:function(s){return t.jumpPage(e)}}}),s("div",{staticClass:"c-title",on:{click:function(s){return t.jumpPage(e)}}},[t._v(t._s(e.name))])],1)})),1):t._e(),t.hotQuestionList.length>0?s("el-card",{staticClass:"box-card",staticStyle:{"margin-bottom":"10px"}},[s("div",{staticStyle:{"margin-bottom":"20px","font-size":"16px","font-weight":"600"}},[t._v(" "+t._s(t.$t("message.consult"))+" ")]),t._l(t.hotQuestionList,(function(e,i){return s("div",{key:i,staticClass:"link-item",on:{click:function(s){return t.sendHot(e)}}},[s("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{size:"mini",effect:"plain"}},[t._v(t._s(i+1))]),s("span",{staticStyle:{flex:"1"},domProps:{textContent:t._s(e.questionName)}}),s("i",{staticClass:"el-icon-arrow-right",staticStyle:{float:"right"}})],1)}))],2):t._e(),t.personList.length>0?s("div",{staticStyle:{"margin-bottom":"10px"}},[s("div",{staticStyle:{"margin-bottom":"10px","font-size":"16px","font-weight":"600","padding-left":"10px"}},[t._v(" "+t._s(t.$t("message.service"))+" ")]),t._l(t.personList,(function(e,i){return s("div",{key:i,staticClass:"link-item link-item-grid",on:{click:function(s){return t.sendHot(e)}}},[e.picUrl?s("el-image",{staticStyle:{width:"30px",height:"30px"},attrs:{fit:"contain",src:e.picUrl}}):s("i",{staticClass:"el-icon-menu",staticStyle:{"font-size":"30px"}}),s("span",{staticClass:"item-text",staticStyle:{flex:"1","vertical-align":"top"},domProps:{textContent:t._s(e.questionName)}}),s("i",{staticClass:"el-icon-arrow-right item-text-arr"})],1)}))],2):t._e()],1):t._e()])]),t.showViewer?s("el-image-viewer",{attrs:{"on-close":function(){t.showViewer=!1},"url-list":t.imgList}}):t._e()],1)}),at=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("a",{staticClass:"hw-style-back",attrs:{href:"javascript:window.history.go(-1);"}},[s("i",{staticClass:"el-icon-back"})])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-shiping-xue iconfont"})])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"icon-zuoxi iconfont"})])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"el-icon-star-off"})])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"m-tool-icon"},[s("i",{staticClass:"el-icon-chat-line-square"})])}],nt=(s("5319"),s("d3b7"),s("3ca3"),s("ddb0"),s("2b3d"),s("9861"),s("498a"),s("5b81"),s("99af"),s("e9c4"),s("b0c0"),s("c761")),ot=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"bubble km-box"},[s("div",{ref:"flowPage",staticClass:"flowPage",staticStyle:{background:"#fff"}}),s("div",{staticClass:"flow-btn-group"},[s("el-button-group",{staticClass:"flex-row",attrs:{size:"mini"}},[s("el-button",{staticClass:"flex-item",attrs:{type:"primary",size:"mini",icon:"el-icon-arrow-left"}},[t._v("自动识别")]),s("el-button",{staticClass:"flex-item",attrs:{type:"primary",size:"mini"}},[t._v("提交")]),s("el-button",{staticClass:"flex-item",attrs:{type:"primary",size:"mini",icon:"el-icon-paperclip"}},[t._v("确定")])],1)],1)])},lt=[],ct={data:function(){return{}},methods:{}},rt=ct,dt=s("2877"),ut=Object(dt["a"])(rt,ot,lt,!1,null,null,null),ht=ut.exports,gt=s("bbe3"),pt=(s("9186"),s("1e65")),mt=s("7262"),ft=s("da97"),vt=s("7e68"),Ct=s("6944"),wt=s.n(Ct);s("0808"),s("2ca0"),s("1276"),s("7db0"),s("b680");var bt,yt={data:function(){return{percent:null,showSearchBtn:!1}},mounted:function(){var t=this;if(this.initUploadCheck(),pt["a"].$on("addMsg",this.onReciveChildrenMsg),pt["a"].$on("msgScrollBottom",(function(){t.$nextTick((function(){t.$refs["msgBox"].scrollTop=99999}))})),navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)&&($("body").on("touchstart","input,textarea",(function(t){$(this).is(":focus")||$(this).focus()})),$("body").on("touchend","a,button,.el-autocomplete-suggestion__list>li",(function(t){$(this)[0].click(),t.stopPropagation(),t.preventDefault()})),window.visualViewport)){var e=this.$utils.debounce(this.setViewport,100);window.visualViewport.addEventListener("resize",e),window.visualViewport.addEventListener("scroll",e)}$("body").on("click","a",(function(e){var s=$(this).attr("href");s&&(s.startsWith("//startApp")||s.startsWith("//startapp")||s.startsWith("//AlipayJSBridge/"))&&(e.stopPropagation(),e.preventDefault(),t.$client.openPage(s))}))},methods:{initAli:function(){},setViewport:function(){var t={width:window.visualViewport.width,height:window.visualViewport.height};$("body").css(t),window.scroll(0,0)},uploadTips:function(t){var e="";if("cn"==this._i18n.locale||"zh-CN"==this._i18n.locale){var s={files:"文件",images:"图片",videos:"视频",audios:"音频",voices:"音频"},i=s[t]||"文件";e=this.channelConfig.BTN_AUTH_TIPS?this.channelConfig.BTN_AUTH_TIPS.split(";")[0].replaceAll("#btnName#",i):"需要使用您设备的".concat(i,"用于匹配用户购买需求和支持用户产品使用")}else e="The ".concat(t," of your device are needed for your purchasing needs and product use support");return e},initUploadCheck:function(){for(var t=["files","images","videos","voices","audios"],e=0;e<t.length;e++){var s=t[e];localStorage.getItem("UploadChecked"+s+this.$client.getBaseConfig().sessionId)||$('[data-upload-check="'+s+'"]').append('<div data-type="'+s+'" class="btnUploadChecked"></div>')}$("body").on("click",".btnUploadChecked",this.checkBeforeUpload)},checkBeforeUpload:function(t){if(0!=$(t.target).parents("[data-upload-check]").length){var e=t.target.dataset.type,s={voices:"BTN_AUTH_AUDIO",audios:"BTN_AUTH_AUDIO",files:"BTN_AUTH_FILE",images:"BTN_AUTH_PIC",videos:"BTN_AUTH_VIDEO"};if(e&&s[e]&&"1"===this.channelConfig[s[e]]){var i="UploadChecked"+e+this.$client.getBaseConfig().sessionId,a=["Allow","Deny"];return"cn"!=this._i18n.locale&&"zh-CN"!=this._i18n.locale||(a=["同意","拒绝"]),localStorage[i]?($(t.target).parents("[data-upload-check]").find("input").click(),!0):(layer.confirm(this.uploadTips(e),{title:null,btn:a},(function(e){layer.close(e),localStorage.setItem(i,1),$(t.target).parents("[data-upload-check]").find("input").click()})),!1)}return $(t.target).parents("[data-upload-check]").find("input").click(),!0}},callTel:function(){var t=this,e=this.channelConfig.TEL_NUM;e&&layer.alert("联系方式:"+e,{yes:function(s){layer.close(s),t.isMini&&(location.href="tel:"+e)},btn:t.isMini?["拨打","取消"]:["确定"]})},toAgent:function(){var t="咨询问题后,主动转人工",e={msgContent:t,msgType:"userReqGetAgent",userData:{hide:!0}};this.prevMsg&&(e.userData.prevMsg={msgType:this.prevMsg.msgType,msgContent:this.prevMsg.msgContent}),this.sendMessage(e,!1,!0)},onReciveChildrenMsg:function(t){t.msgTime||(t.msgTime=(new Date).getTime()),this.msgList.push(t),this.scrollToBottom()},onReciveSendMsg:function(t){this.sendMessage(t)},scrollToBottom:function(){var t=this;this.unreadCount=0,this.$nextTick((function(){t.$refs["msgBox"].scrollTop=99999}))},uploadingFile:function(t){this.percent=100==t.percent?null:t.percent.toFixed(2)},endChat:function(){var t=this,e="是否结束会话?";this.$utils.messageBox.confirm(e,"提示",{closeOnClickModal:!1,closeOnPressEscape:!1,type:"info",customClass:"videoCssAlter",distinguishCancelAndClose:!0,showClose:!1}).then((function(e){t.showEndChat=!1,t.sendMessage({msgType:"text",msgContent:"88"},null,!0)})).catch((function(t){}))}},computed:{showEndNav:function(){return!0}}},_t=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex"},[s("div",{staticClass:"his-search",staticStyle:{padding:"10px"}},[s("el-input",{directives:[{name:"show",rawName:"v-show",value:!t.showChatSession,expression:"!showChatSession"}],staticClass:"input-with-select",attrs:{placeholder:"请输入内容"},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}},[s("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.searchChatRecord},slot:"append"})],1),s("el-button",{directives:[{name:"show",rawName:"v-show",value:t.showChatSession,expression:"showChatSession"}],attrs:{icon:"el-icon-arrow-left"},on:{click:function(e){t.showChatSession=!1}}},[t._v("返回")])],1),s("div",{staticClass:"flex-item"},[s("div",{directives:[{name:"show",rawName:"v-show",value:!t.showChatSession,expression:"!showChatSession"}],staticClass:"his-search-list"},[t._l(t.searchResultList,(function(e,i){return s("Msg",{key:i,attrs:{index:i,history:"search","msg-item":e,preMsgTime:i>0?t.searchResultList[i-1].msgTime:0}})})),0==t.searchResultList.length?s("div",{staticStyle:{padding:"20px","text-align":"center"}},[t._v("暂无记录")]):t._e()],2),t.showChatSession?s("div",{staticClass:"his-chatSession-list"},t._l(t.chatSessionList,(function(e,i){return s("Msg",{key:i,attrs:{index:i,history:"searchdetail","msg-item":e,preMsgTime:i>0?t.chatSessionList[i-1].msgTime:0}})})),1):t._e()])])},xt=[],St={name:"HistorySearch",props:["searchKeyword"],data:function(){return{keyword:"",highLightKeyword:"",searchResultList:[],chatSessionList:[],showChatSession:!1,isSearchPage:!0}},components:{Msg:nt["a"]},provide:function(){var t=this;return{highLightKeyword:function(){return t.highLightKeyword},isSearch:function(){return t.isSearchPage}}},mounted:function(){pt["a"].$on("showChatSessionList",this.getHistoryByChatSessionId)},destroyed:function(){pt["a"].$off("showChatSessionList",this.getHistoryByChatSessionId)},methods:{searchChatRecord:function(){var t=this,e={keyword:this.keyword,pageSize:100,pageIndex:0};this.$api.searchChatRecord(e).then((function(e){e.data&&(t.highLightKeyword=t.keyword,t.searchResultList=e.data.data||[])}))},getHistoryByChatSessionId:function(t){var e=this,s={chatSessionId:t.chatSessionId};this.$api.searchChatRecord(s).then((function(s){s.data&&(e.showChatSession=!0,e.chatSessionList=s.data.data||[],e.$nextTick((function(e){var s=$('.his-chatSession-list [data-serial-id="'+t.serialId+'"]')[0];s&&s.scrollIntoView&&s.scrollIntoView()})))}))},updateKeyword:function(t){console.log("更新搜索==>",t),this.keyword=t,this.highLightKeyword=t,this.searchChatRecord()}}},Tt=St,$t=Object(dt["a"])(Tt,_t,xt,!1,null,"692260e6",null),kt=$t.exports,Ot={components:{History:kt},data:function(){return{curLine:0,maxLine:0,showHistory:!1,searchKeyword1:""}},methods:{autoScroll:function(){var t=this,e=Math.floor($("#adLines").height()/20);this.maxLine=e,setInterval((function(){t.curLine++}),3e3)}},computed:{adTransform:function(){var t="";return this.maxLine&&(t="transform:translateY(-".concat(this.curLine%this.maxLine*20,"px)")),t}}},Mt=s("7212"),Lt=(s("dfa4"),function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"welcome-card"},[t.adsList.length>0?s("el-carousel",{staticStyle:{"margin-bottom":"10px"},attrs:{height:"150px",autoplay:!0,interval:5e3,"indicator-position":"none"}},t._l(t.adsList,(function(e,i){return s("el-carousel-item",{key:e},[s("el-card",{staticClass:"guess-box flex box-card web-box"},[s("div",{staticClass:"guess-box-main flex-item",on:{click:function(s){return t.clickItem(e)}}},[s("div",{staticClass:"welcome-card-news"},[s("div",{staticClass:"welcome-card-news-title"},[t._v(t._s(e.questionName))]),s("div",{staticClass:"welcome-card-news-info clearfix"},[e.picUrl?s("div",{staticClass:"welcome-card-news-img"},[s("img",{attrs:{src:e.picUrl}})]):t._e(),s("div",{staticClass:"welcome-card-news-content"},[t._v(" "+t._s(e.bakup)+" ")])])])])])],1)})),1):t._e(),t.navList.length>0?s("div",{staticClass:"welcome-card-swiper"},[s("swiper",{ref:"mySwiper",attrs:{options:t.bottomNavbSwiperOption}},t._l(t.navList,(function(e,i){return s("swiper-slide",[s("div",{staticClass:"welcome-card-swiper-item",style:{backgroundColor:t.colorList[i%5]},on:{click:function(s){return t.clickItem(e)}}},[s("div",{staticClass:"welcome-card-swiper-icon"},[s("el-avatar",{attrs:{src:e.picUrl,icon:"el-icon-user-solid"}})],1),s("div",{staticClass:"welcome-card-swiper-title"},[t._v(t._s(e.questionName))])])])})),1)],1):t._e(),t.gridList.length>0?s("div",{staticClass:"welcome-card-swiper"},[s("el-card",{staticClass:"flex box-card"},[s("div",{staticClass:"grid-container"},t._l(t.gridList,(function(e,i){return s("div",{staticClass:"grid",domProps:{innerHTML:t._s(e.questionName)},on:{click:function(s){return t.clickItem(e)}}})})),0)])],1):t._e(),t.hotList.length>0?s("div",{staticClass:"welcome-hot"},[s("el-card",{staticClass:"guess-box flex box-card web-box"},[s("div",{staticClass:"guess-box-main flex-item hot-list"},[s("div",{staticClass:"hot-list-title"},[s("div",{staticClass:"hot-title-tool"},[s("span",{directives:[{name:"show",rawName:"v-show",value:"toggle"==t.pageMode&&this.client209["6"]&&this.client209["6"].length>5,expression:"pageMode == 'toggle' && (this.client209['6'] && this.client209['6'].length>5)"}],staticStyle:{"margin-right":"10px"},on:{click:function(e){e.stopPropagation(),t.pageMode="all"}}},[s("i",{staticClass:"el-icon-unfold"}),t._v(" 全部")]),s("span",{directives:[{name:"show",rawName:"v-show",value:"toggle"==t.pageMode&&this.client209["6"]&&this.client209["6"].length>5,expression:"pageMode == 'toggle' && (this.client209['6'] && this.client209['6'].length>5)"}],on:{click:function(e){return e.stopPropagation(),t.changePage.apply(null,arguments)}}},[s("i",{staticClass:"el-icon-refresh-right"}),t._v(" 换一换")])]),s("span",{staticClass:"hot-list-title-text"},[t._v("猜你想问")])]),s("div",{staticClass:"hot-list-content"},t._l(t.hotList,(function(e,i){return s("div",{staticClass:"hot-list-item",on:{click:function(s){return t.clickItem(e)}}},[s("el-link",{attrs:{type:"info"}},[s("span",{domProps:{innerHTML:t._s(e.questionName)}})])],1)})),0)])])],1):t._e(),t.multHotList&&t.multHotList.length>0&&t.swiperOptions?s("el-card",{staticClass:"guess-box flex box-card web-box",staticStyle:{height:"184px"}},[s("div",{staticClass:"guess-box-side",staticStyle:{width:"70px"}},[s("div",{staticClass:"guess-img",staticStyle:{"padding-top":"10px"}},[s("img",{attrs:{src:t.imgUrl,width:"48px"}}),s("el-button",{staticStyle:{"margin-top":"10px"},attrs:{size:"mini",type:"text"},on:{click:function(e){return e.stopPropagation(),t.changeAllGuess.apply(null,arguments)}}},[t._v("换一批 "),s("i",{staticClass:"el-icon-refresh-left"})])],1)]),s("div",{staticClass:"guess-box-main flex-item"},[s("div",{staticClass:"flex"},[s("div",{staticClass:"guess-box-title",staticStyle:{height:"30px"}},t._l(t.multHotListShow,(function(e,i){return s("div",{staticClass:"guess-box-title-name",class:{active:i==t.activeGuess},attrs:{"data-uuid":t.uuid,"data-index":i},domProps:{textContent:t._s(e.questionName)},on:{click:function(e){return e.stopPropagation(),t.changeGuess(i)}}})})),0),s("div",{staticClass:"flex-item"},[s("swiper",{ref:"guessSwiper",attrs:{options:t.swiperOptions},on:{slideChange:t.changeGuessActive}},[s("div",{staticClass:"swiper-pagination1",attrs:{slot:"pagination"},slot:"pagination"}),t._l(t.multHotListShow,(function(e,i){return s("swiper-slide",{attrs:{label:e.questionName,name:""+i}},[s("div",{staticClass:"msg-guess-box",staticStyle:{position:"relative"}},[0==e.questions2.length?s("div",{staticClass:"abs-center"},[s("i",{staticClass:"el-icon-chat-dot-round"}),t._v(" 暂无内容")]):t._e(),t._l(e.questions2,(function(e){return s("div",{staticClass:"guess-link",on:{click:function(s){return t.clickItem(e)}}},[s("el-link",{attrs:{type:"info"}},[t._v(t._s(e.questionName))])],1)}))],2)])}))],2)],1)])])]):t._e()],1)}),Et=[],It={props:["clientData"],components:{swiper:Mt["swiper"],swiperSlide:Mt["swiperSlide"]},data:function(){return{news:{},swiperList:[],colorList:["#E0F1FE","#E5FAF0","#FCF3ED","#EDE0FB","#E0F1FE"],bottomNavbSwiperOption:{freeMode:!0,scrollbarHide:!0,slidesPerView:"auto",centeredSlides:!1,spaceBetween:10,mousewheel:!0,grabCursor:!0,on:{resize:function(){this.update()}}},client209:{},pageIndex:0,pageMode:"toggle",imgUrl:"/yc-media/pages/client/images/guess-title.png",currentPage:0,activeGuess:0,uuid:null,swiperOptions:{observer:!0,observeParents:!0},multHotList:[]}},mounted:function(){this.clientData&&(this.client209=this.clientData,this.client209.hasOwnProperty("8")&&(this.multHotList=this.client209["8"]||[])),this.uuid=this.$utils.uuid(),this.activeGuess="0",$(".msg-guess .web-box-type2 .el-tabs__nav .el-tabs__item").eq(0)[0]&&$(".msg-guess .web-box-type2 .el-tabs__nav .el-tabs__item").eq(0)[0].click()},methods:{getInfo:function(){var t=this;this.$api.client({operType:209}).then((function(e){"000"==e.respCode&&e.data.data&&(t.client209=e.data.data,t.client209.hasOwnProperty("8")&&(t.multHotList=t.client209["8"]||[]))}))},clickItem:function(t){this.$emit("clickItem",t)},changePage:function(){this.pageIndex++},changeGuess:function(t){this.$refs.guessSwiper.swiper.slideTo(t)},changeGuessActive:function(t){this.activeGuess=this.$refs.guessSwiper.swiper.activeIndex;var e=$('.guess-box-title-name[data-uuid="'+this.uuid+'"][data-index="'+this.activeGuess+'"]')[0];e&&e.scrollIntoView({block:"nearest",inline:"center"})},changeAllGuess:function(){this.currentPage++}},watch:{clientData:function(t,e){this.client209=t,console.log("clientData",this.client209),this.client209.hasOwnProperty("8")&&(this.multHotList=this.client209["8"]||[])}},computed:{hotList:function(){var t=this.client209.hasOwnProperty("6")?this.client209["6"]:[];if(console.log("热门问题猜你想问",t),"all"==this.pageMode||t.length<=5)return t;var e=t,s=this.pageIndex,i=Math.ceil(e.length/5);if(e.length<=5)t=e;else{var a=i>s?s:s%i,n=this.$utils.paging(e,a,5);t=n}return t},adsList:function(){return this.client209.hasOwnProperty("5")?this.client209["5"]:[]},navList:function(){return this.client209.hasOwnProperty("2")?this.client209["2"]:[]},gridList:function(){return this.client209.hasOwnProperty("7")?this.client209["7"]:[]},multHotListShow:function(){for(var t=this.currentPage,e=this.multHotList,s=0;s<e.length;s++){var i=e[s].children||[];e[s].questions2=i;var a=Math.ceil(i.length/4);if(i.length<=4)e[s].questions2=i;else{var n=a>t?t:t%a,o=this.$utils.paging(i,n,4);e[s].questions2=o}}return e}}},Nt=It,Ht=(s("4f9a"),s("33a2"),Object(dt["a"])(Nt,Lt,Et,!1,null,"5879b3de",null)),Ut=Ht.exports,Rt=s("ba9f");tt["default"].use(wt.a),wt.a.setDefaults({Options:{inline:!0,button:!0,navbar:!1,title:!0,toolbar:!0,tooltip:!0,movable:!0,zoomable:!0,rotatable:!0,scalable:!0,transition:!0,fullscreen:!0,keyboard:!0,url:"data-source"},url:"data-url",filter:function(t){return!($(t).hasClass("im-emoji")||$(t).parents(".l-img").length>0)&&!!($(t).parents(".bubble").length>0&&0==$(t).parents(".bubble.file").length||$(t).hasClass("quotoImg"))}});var Dt={components:{Msg:nt["a"],NormalMsg:mt["a"],Emoji:gt["a"],swiper:Mt["swiper"],swiperSlide:Mt["swiperSlide"],WelcomeCard:Ut,MapMsg:ht},mixins:[ft["a"],vt["a"],yt,Ot],data:function(){return{urlObj:{},isShow:!1,showAdv:!0,msgList:[],historyMsgList:[],style:{},rows:4,channelConfigData:{},channelConfig:{H5_LOGO_URL:""},setting:{showTitle:!0,showKeys:!1},isAgentChat:!1,inputVal:"",keys:[],bottomNavList:[],showMobileTool:!1,showMobileEmoji:!1,showHeader:!0,noMore:!1,pageInfo:{pageNo:1},isMini:!1,hotQuestionList:[],personList:[],hotImgList:[],srcUrl:"",count:0,switch:!1,lockCount:20,sendTime:null,lockTime:null,lockTimeMs:6e4,msg:"发言过频繁，请您耐心等待",showViewer:!1,imgList:[],showSide:!0,audioNotify:!1,lang:"cn",langList:[],urlOject:{},isSending:!1,inputFocus:!1,question:"",previewMsg:null,flowVH:null,msgOkText:null,showEmoji:!1,firstMsg:!0,showSatisfy:!1,loadingHis:!1,isIframe:!1,lastViewTime:null,isMessage:!1,msgUrl:"",isMsgBtn:!1,isIOS:!1,suggestionsList:[],urlTile:"",hwHeader:!1,myAvatar:null,lastQueue:null,bottomNavbSwiperOption:{freeMode:!0,scrollbarHide:!0,slidesPerView:"auto",centeredSlides:!1,spaceBetween:10,mousewheel:!0,grabCursor:!0,on:{resize:function(){this.update()}}},agentInputState:!1,showSearch:!1,searchKeyword:"",searchList:[],clientData:{},showEndChat:!1,lastSSE_index:-1}},created:function(){this.resize()},mounted:function(){window.testChat=this,window.mySenderType="user",this.isIOS=/macintosh|mac os x/i.test(navigator.userAgent.toLowerCase());var t=this,e=this.$client.getQueryObject();this.urlObj=e||{},"true"==e.record&&(this.showRecord=!0),"true"==e.huawei&&(this.hwHeader=!0),e.title&&(this.urlTile=e.title),"ar"==e.language&&$("body").addClass("lang-ar"),"large"==e.fontSize&&$("body").addClass("font-size-large"),e.messageOkText&&(this.msgOkText=e.messageOkText),this.urlOject=e,pt["a"].$on("clientMsg",this.onMessage),t.$api.closeLongPolling(),this.init(),document.addEventListener("resize",this.resize,!1),"true"!=e.app&&"true"!=e.hideHeader||(this.showHeader=!1),this.resize(),"true"==e.video&&(this.showVideo=!0),"true"!=e.iframe&&top==self||(this.isIframe=!0),document.addEventListener("paste",(function(e){if(t.paste&&(e.clipboardData||e.originalEvent)){var s=e.clipboardData||e.originalEvent.clipboardData;if(s.items){var i=s.items,a=i.length,n=null;!0;for(var o=0;o<a;o++)-1!==i[o].type.indexOf("image")&&(n=i[o].getAsFile());if(null!==n){var l=URL.createObjectURL(n);t.captureCallback&&t.captureCallback(n,l,!0)}}}})),Date.prototype.format=function(t){var e={"Y+":this.getFullYear(),"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};for(var s in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),e)new RegExp("("+s+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?e[s]:("00"+e[s]).substr((""+e[s]).length)));return t},window.localStorage&&(this.audioNotify=localStorage.audioPlay||!1);var s=sessionStorage.getItem("isSend");s&&sessionStorage.removeItem("isSend"),$(window).on("beforeunload",(function(){var e=$("#closeStopChat").val();if("1"==e){console.log("退出关闭会话"),t.sendMessage({msgType:"text",msgContent:"",stopChat:!0,userInfo:{stopChat:!0}});var s=new Date;while(new Date-s<100);}})),window.endChat=function(){t.sendMessage({msgContent:"88"})},window.addEventListener("online",(function(){this.$utils.message.info({message:"Network OnLine"})})),window.addEventListener("offline",(function(){this.$utils.message.error({message:"Network OffLine"})}));var i,a=document.getElementsByTagName("textarea")[0],n=this.$client.isMobile();$(window).height();a.scrollIntoViewIfNeeded&&(a.onfocus=function(){t.$utils.isWebView()&&$("body").height("50%"),i=setInterval((function(){a.scrollIntoViewIfNeeded()}),500),n&&(bt=setTimeout((function(){t.$refs["msgBox"].scrollTop=99999}),300))},a.onblur=function(){t.$utils.isWebView()&&$("body").height("100%"),$("body").height("100%"),window.scroll(0,0),clearInterval(i),bt&&clearTimeout(bt)}),$("body").on("focus","input,textarea",(function(){var e=$(this)[0];t.$nextTick((function(){e.scrollIntoView()}))}))},methods:{switchLang:function(t){this.lang=t,this.$i18n.locale=this.lang},init:function(){var t=this,e=this;this.resize(),this.$client.init(),this.getChannelConfig().then((function(){e.loadChatRecord(!0).finally((function(){if(e.urlOject.message){var t=Base64.decode(String(e.urlOject.message).replaceAll(" ","+")).trim();try{t=JSON.parse(t)}catch(n){console.log("url message error",t,n)}if(t.msgType||t.type){var s=t.msgType||t.type||"text",i=t.msgContent||t.content;t.messageOkText&&(e.msgOkText=t.messageOkText),"news"==s&&(t.msgContent||(i={content:t.content,picurl:t.picurl,url:t.url}));var a={msgType:s,msgContent:i};e.previewMsg=a}}e.checkSession()}))}));try{this.$api.client({operType:209,pageSize:20,pageNo:1}).then((function(e){if("000"==e.respCode&&e.data.data){var s=e.data.data;t.clientData=s,s.hasOwnProperty("1")&&(t.hotQuestionList=s[1]||[]),s.hasOwnProperty("4")&&(t.personList=s[4]||[]),s.hasOwnProperty("3")&&(t.bottomNavList=s[3]||[]),s.hasOwnProperty("201")&&(t.hotImgList=s[201]||[])}}))}catch(s){}this.initIosMsgReload(),pt["a"].$on("sendMsg",this.sendMessage),pt["a"].$on("sseMsg",this.sseMessage)},paste:function(t){},sseMessage:function(t){if(200!=t.code&&0!=t.code)this.$sse.closeSSE();else if(t.msgId&&this.lastSSE_index>=0&&1!=t.end){var e=this.msgList[this.lastSSE_index].robotData.content;"正在思考中..."==e&&(e="");var s=this.msgList[this.lastSSE_index].robotData.reasonContent||"";s+=t.reasonContent,e+=t.content,this.$set(this.msgList[this.lastSSE_index].robotData,"content",e),this.$set(this.msgList[this.lastSSE_index].robotData,"reasonContent",s)}},initIosMsgReload:function(){var t=this,e=this,s=navigator.userAgent,i=!!s.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),a=-1!=s.indexOf("micromessenger"),n=-1!=s.indexOf("webview");!i||a||n||document.addEventListener("visibilitychange",(function(){if(document.hidden)e.lastViewTime=(new Date).getTime();else{var s=(new Date).getTime();e.lastViewTime&&s-e.lastViewTime>1e4&&(t.$utils.message.info({message:"同步记录"}),t.pageInfo.pageNo=1,t.loadChatRecord(!0))}}))},getChannelConfig:function(){var t=this,e=this;return this.$api.channelConfig().then((function(e){if(e.data){if(t.channelConfig=e.data.styleConf||{},t.channelConfigData=e.data,t.$store.commit("setChannelConfig",t.channelConfig),t.$store.commit("setChannelConfigData",t.channelConfigData),"true"!=t.urlOject.gatherJump&&1==t.channelConfig.H5_INFO_GATHER_FLAG&&t.channelConfig.H5_INFO_GATHER_URL){var s=t.channelConfig.H5_INFO_GATHER_URL,i=t.urlOject;t.channelConfig.H5_INFO_GATHER_LOGO&&(i.infoGatherLogo=t.channelConfig.H5_INFO_GATHER_LOGO);var a=$.param(i);return s=s.indexOf("?")>=0?s+"&"+a:s+"?"+a,void(location.href=s)}if(1==t.channelConfig.REALTIME_VOICE_FLAG){t.showRecord=!0,t.channelConfig.REALTIME_VOICE_TYPE&&(window.recordType=t.channelConfig.REALTIME_VOICE_TYPE,t.type,t.channelConfig.REALTIME_VOICE_TYPE);for(var n=["/yc-media/pages/client/static/recorder/recorder.".concat("wav"==window.recordType?"wav":"mp3",".mix.js")],o=0;o<n.length;o++){var l=document.createElement("script");l.src=n[o],document.body.appendChild(l)}}t.channelConfig.VOICE_TOWORD_URL&&(window.asrUrl=t.channelConfig.VOICE_TOWORD_URL),$("title").text(t.channelConfig.H5_TITLE||t.$t("title.onlineService")),$("title").appendTo("head"),t.channelConfig.H5_LOGO_URL&&$('link[rel="icon"]').attr("href",t.channelConfig.H5_LOGO_URL),t.channelConfig.H5_ROBOT_ICON&&t.$client.updateRes("avatar","robot",t.channelConfig.H5_ROBOT_ICON),t.channelConfig.H5_ROBOT_NAME&&t.$client.updateRes("name","robot",t.channelConfig.H5_ROBOT_NAME),t.channelConfig.H5_SYSTEM_NAME&&t.$client.updateRes("name","system",t.channelConfig.H5_SYSTEM_NAME),t.channelConfig.H5_AGENT_ICON&&t.$client.updateRes("avatar","agent",t.channelConfig.H5_AGENT_ICON),t.channelConfig.H5_AGENT_NAME&&t.$client.updateRes("name","agent",t.channelConfig.H5_AGENT_NAME),"https:"==location.protocol&&"1"==t.channelConfigData.videoSwitch&&(t.showVideo=!0),t.channelConfig.H5_CUSTOMER_ICON&&t.$client.updateRes("avatar","user",t.channelConfig.H5_CUSTOMER_ICON),t.channelConfig.H5_CUSTOMER_NAME&&t.$client.updateRes("name","user",t.channelConfig.H5_CUSTOMER_NAME),t.channelConfig.H5_SYSTEM_ICON&&t.$client.updateRes("avatar","system",t.channelConfig.H5_SYSTEM_ICON),t.channelConfig.H5_SYSTEM_NAME&&t.$client.updateRes("name","system",t.channelConfig.H5_SYSTEM_NAME);var c=e.data.BUSI_SCRIPT_URL||t.channelConfig.BUSI_SCRIPT_URL||"";if(c){var r=document.createElement("script");r.src=c,document.getElementsByTagName("body")[0].appendChild(r)}t.channelConfig.ENABLE_CUST_LEAVE_MSG&&(t.isMsgBtn="1"==t.channelConfig.ENABLE_CUST_LEAVE_MSG),t.channelConfig.H5_SKIN_CODE&&($("body").removeAttr("class"),$("body").addClass("theme-"+t.channelConfig.H5_SKIN_CODE)),t.channelConfig.BTN_AUTH_AUDIO,t.channelConfig.CUSTOM_CONFIG_ITEM&&(t.$client.updateExConfig(t.channelConfig.CUSTOM_CONFIG_ITEM),"true"!=t.$client.getExConfig("search")&&"1"!=t.$client.getExConfig("search")||(t.showSearchBtn=!0))}else console.log("获取渠道配置异常")})).finally((function(t){document.getElementById("loadingPanel").style.display="none",$("#adLines a").off("click").on("click",(function(t){console.log("sssssssss");var s=$(this).attr("href");s&&(t.stopPropagation(),t.preventDefault(),e.$client.openPage(s))}))}))},loadChatRecord:function(t){var e=this;if(!this.loadingHis){var s={pageSize:50,pageIndex:this.pageInfo.pageNo,userInfo:this.$utils.getQueryObject()};return t&&(this.pageInfo.pageNo=1),this.loadingHis=!0,this.$api.loadChatRecord(s).then((function(s){if(e.loadingHis=!1,1==s.state&&s.data){if(s.data.totalPage==e.pageInfo.pageNo&&(e.noMore=!0),s.data.data.length>0?e.pageInfo.pageNo=e.pageInfo.pageNo+1:t||e.$utils.message.info({message:"没有更多消息了"}),t)e.historyMsgList=s.data.data,e.msgList=[],s.data.data.length>0&&"Y"==s.data.data[s.data.data.length-1].agentEvaluation&&(e.showSatisfy=!0),e.$nextTick((function(){this.$refs["msgBox"].scrollTop=99999}));else{var i=null;e.historyMsgList.length>0&&(i=e.historyMsgList[0].serialId),e.historyMsgList=s.data.data.concat(e.historyMsgList),i&&e.$nextTick((function(){this.showSerialIdMsg&&this.showSerialIdMsg(i)}))}"Y"==s.data.agentEvaluation&&(_self.showSatisfy=!0),"Y"==s.data.closeChat&&(_self.showEndNav=!0),"robot"==s.data.bizType&&pt["a"].$emit("addMsg",{event:"selfHelp",msgType:"selfHelp",msgContent:"selfHelp",welcome:!0})}})).finally((function(t){e.loadingHis=!1}))}},showSerialIdMsg:function(t){var e=$('[data-serial-id="'+t+'"]')[0];e&&e.scrollIntoView&&e.scrollIntoView()},checkSession:function(){var t=this,e=this;return this.$api.checkSession().then((function(s){if(s.state&&(t.showEndChat=!0),s.data&&"queue"==s.data.bizType&&s.data.queueNo&&(pt["a"].$emit("addMsg",{event:"queue",queueNo:s.data.queueNo,visitorQueueMsg:s.data.visitorQueueMsg,msgType:"sessionQueue",msgContent:"queue"}),e.channelConfig.H5_STOP_QUEUE_SHOW_TIME&&s.data.inQueueTime&&s.data.timestamp)){var i=s.data.timestamp-s.data.inQueueTime;i>60*t.channelConfig.H5_STOP_QUEUE_SHOW_TIME*1e3?e.showQueue():window.queueTimeclock=setTimeout(e.showQueue,60*e.channelConfig.H5_STOP_QUEUE_SHOW_TIME*1e3)}s.data&&s.data.chatSessionId?(t.isAgentChat=!0,t.$client.start()):"invite"==t.urlOject.pageType&&t.urlOject.code?(t.$client.start(),t.sendMessage({msgType:"text",msgContent:t.urlOject.code,isChat:!0},null,!0)):"1"==t.urlOject.channelNavType||1==t.channelConfig.CHANNEL_NAV_TYPE||3==t.channelConfig.CHANNEL_NAV_TYPE?(t.$client.start(),1==$("#isSendZxkf").val()&&t.sendMessage({msgType:"event",msgContent:"zxkf",isChat:!0},null,!0)):t.$api.getChannelKeys().then((function(e){t.keys=e.data.keys,"undefined"==typeof t.urlOject.autoConnect||0!=t.urlOject.autoConnect?t.urlOject.keyCode?t.setKey(t.urlOject.keyCode):t.setting.showKeys=!0:t.$client.start()}))}))},onMessage:function(t){for(var e=!1,s="",i=!1,a=0;a<t.length;a++){var n=t[a].data;if("agentOper_updateUserInfo"==t[a].event){var o=n.userData.nickname;o&&this.$client.updateLocaleNickname(o)}if("agent"==n.sender&&(s=n.channelKey,e=!0),n&&(n.msgTime=n.msgTime||t[a].timestamp,n.event=t[a].event,n.serialId=t[a].serialId,n.msgContent&&(i=!0),n.chatSessionId&&this.$api.setData("chatSessionId",t[a].data.chatSessionId)),n.videoSatisfyUrl){var l=["100%","100%"];layer.open({title:"满意度调查",id:"videoSatisfy",content:n.videoSatisfyUrl,area:l,type:2,shade:!1,offset:"t",closeBtn:0,success:function(){},end:function(){}})}if("start"==n.event?(this.hideQueue(),window.queueTimeclock&&clearTimeout(window.queueTimeclock)):"end"==n.event?(this.agentInputState=!1,window.queueTimeclock&&clearTimeout(window.queueTimeclock)):"queue"==n.event&&this.channelConfig.H5_STOP_QUEUE_SHOW_TIME&&(window.queueTimeclock||(this.hideQueue(),window.queueTimeclock=setTimeout(this.showQueue,60*this.channelConfig.H5_STOP_QUEUE_SHOW_TIME*1e3))),"withdraw"!=n.event){if("agentInputState"==n.event){console.log("msg.eventData",n.inputState);var c=!(!n||"1"!=n.inputState);this.agentInputState=c}if("Y"==n.agentEvaluation?this.showSatisfy=!0:this.showSatisfy=!1,this.msgList.push(n),n.robotData&&1==n.robotData.flowSpecialTreatment&&this.msgList.push({msgType:"template",msgContent:"发送定位信息",serialId:this.$utils.uuid(),tempData:{tempId:"mapCard1",tempType:7}}),n.robotData&&n.robotData.stream&&(this.$sse.openSSE({cmdJson:JSON.stringify({chatSessionId:n.chatSessionId,sessionId:n.sessionId,channelKey:n.channelKey,serialId:n.serialId,msgType:n.robotData.msgType,content:n.robotData.question})}),this.lastSSE_index=this.msgList.length-1),n.event){this.showEndChat="end"!=n.event;try{"undefined"!=typeof mediaClientEvent&&mediaClientEvent.emitEvent(n.event,t[a]),this.videoCommand&&this.videoCommand(n.event,n)}catch(r){console.log(r)}}}else this.withdraw(n.serialId)}if(e&&localStorage.setItem("newAgentMsg"+s,(new Date).getTime()),this.$nextTick((function(){this.$refs["msgBox"].scrollTop=99999})),t.length>0&&this.$api.confirmMsg&&this.$api.confirmMsg(t),i&&this.audioNotify)try{this.$client.voice.play()}catch(r){console.info(r)}},withdraw:function(t){for(var e=this.msgList,s=this.historyMsgList,i=!1,a=0;a<e.length;a++)if(e[a].serialId==t){i=!0,this.$set(this.msgList[a],"withdraw",1);break}if(!i)for(var n=0;n<s.length;n++)if(s[n].serialId==t){i=!0,this.$set(this.historyMsgList[n],"withdraw",1);break}},sendMessage:function(t,e,s){var i=this;if(!this.sendLockCheck())return!1;t.sessionId=this.sessionId;var a=new Date,n=t.serialId||this.$utils.uuid();t.serialId=n;var o=this.msgList.length,l={sender:"user",msgTime:"",msgType:t.msgType||"text",msgContent:t.msgContent,serialId:n,state:"sending",msgIndex:o};return s||this.msgList.push(l),this.firstMsg&&this.urlOject.keyCode&&(t.keyCode=this.urlOject.keyCode),this.$client.sendMessage(t).then((function(n){if(n.data.serialId&&i.msgList[o]&&(i.msgList[o].serialId=n.data.serialId),i.firstMsg=!1,!t.stopChat){if(!s){var c=n.data&&n.data.timeStr?n.data.timeStr:a.getTime();i.$set(i.msgList[o],"msgTime",c),i.$set(i.msgList[o],"state","succ"),l.msgTime=c,l.state="succ",i.syncMsg&&i.syncMsg(l)}i.$nextTick((function(){this.$refs["msgBox"].scrollTop=99999,e&&(this.inputVal="",this.$refs.autoBox.handleChange([]),this.$refs.autoBox.suggestions=[],this.suggestionsList=[],this.$api.custInputPush({msgContent:""}))}))}}),(function(t){s||i.$set(i.msgList[o],"state","fail")}))},resendMsg:function(t){var e=this,s=this.msgList[t];s&&this.$client.sendMessage(s).then((function(s){var i=s.data&&s.data.timeStr?s.data.timeStr:nowDate.getTime();e.$set(e.msgList[t],"msgTime",i),e.$set(e.msgList[t],"state","succ"),e.$nextTick((function(){this.$refs["msgBox"].scrollTop=99999}))}),(function(s){e.$set(e.msgList[t],"state","fail")}))},sendText:function(t){var e=this;if(t.preventDefault(),t.stopPropagation(),!this.isSending){var s=this.inputVal||this.$refs.autoBox.value||document.getElementsByTagName("textarea")[0].value;if(s&&""!=$.trim(String(s))){var i={msgContent:s,msgType:"text"};if("undefined"!=typeof filterXSS&&(!i.msgType||"text"==i.msgType)){var a=i.msgContent;i.msgContent=filterXSS(a)}(window.clearHtml||"0"==$("#loadHtmlSwitch").val())&&(i.msgContent=this.$client.delHtmlTag(i.msgContent));var n={content:String(i.msgContent).trim(),userType:"user"};if(""==n.content)return this.$utils.message.warning({message:"发送失败"}),void(this.isSending=!1);this.isSending=!0,this.$api.srhSensitive(n).then((function(t){if(e.isSending=!1,200==t.code&&t.total>0){var s=t.sensitiveWordTips?t.sensitiveWordTips:"内容包含敏感词"+t.sensitiveWordStr;e.$utils.message.warning({message:s})}else 200!=t.code?e.$utils.message.warning({message:t.msg}):(e.sendMessage(i,!0),e.inputVal="",e.$refs.autoBox.handleChange([]),e.$refs.autoBox.suggestions=[],e.suggestionsList=[])}),(function(t){e.isSending=!1,e.$utils.message.warning({message:"发送失败"})})).finally((function(t){e.isSending=!1}))}}},sendTextByEnter:function(t){var e=this;if(!t.shiftKey){("mobile"!=e.clientType||this.isIOS)&&t.preventDefault();var s=this.inputVal;this.$nextTick((function(){setTimeout((function(){"mobile"!=e.clientType&&s==e.inputVal&&e.sendText(t)}),300)}))}},sendTextByEnterKeyUp:function(t){this.isIOS&&(this.sendTextByEnter(t),t.preventDefault&&t.preventDefault())},sendTextByEnterKeyDown:function(t){this.isIOS||this.sendTextByEnter(t)},querySearchAsync:function(t,e){var s=this;this.custInputPush({msgContent:t}),String(t).length<2?(e([]),this.suggestionsList=[]):(this.suggestionsList=[],this.$api.robotInputGuide({question:t}).then((function(i){s.question=t,200==i.code&&i.data&&i.data.total>0?""==s.inputVal?(e([]),s.suggestionsList=[]):(e(i.data.inputGuides),s.suggestionsList=i.data.inputGuides):(e([]),s.suggestionsList=[])})))},highLight:function(t){return this.question&&t?t.replace(this.question,'<font style="color:red">'+this.question+"</font>"):t},addText:function(t){var e=this.inputVal+t;e.length>800&&(e=e.substring(0,800)),this.inputVal=e},addEmojiFunc:function(t){this.addText(t),this.showEmoji=!1,this.$refs.autoBox.focus()},handleSelect:function(t){if(this.inputVal=t.question||t.hlQuestion,1!=this.channelConfig.H5_INPUT_C_SEND_TYPE){var e={msgContent:this.inputVal,msgType:"text"};this.sendMessage(e,!0),this.inputVal="",this.$refs.autoBox.handleChange([]),this.$refs.autoBox.suggestions=[],this.suggestionsList=[]}},clickNav:function(t){var e=t.operType;switch(e){case"1":this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}});break;case"2":this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}});break;case"3":t.linkUrl&&window.open(t.linkUrl);break;case"4":this.$utils.message.warning({message:"暂不支持"});break;case"5":var s=t.exJson&&t.exJson.style||{},i=s.width||"80%",a=s.height||"80%",n="auto";s.top&&s.left?n=[s.top,s.left]:s.top&&(n=s.top),layer.open({title:t.questionName,content:t.linkUrl,area:this.isMini?["100%","100%"]:[i,a],offset:this.isMini?"auto":n,shade:!1,maxmin:!0,type:2});break;case"6":t.linkUrl&&window.open(t.linkUrl);break;default:this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}})}},handleUploadImage:function(t){if(this.clearFiles(),1==t.state){var e="image",s=t.data.name,i=t.data.url,a=this.$utils.getFileType(s,!0);-1!=["image","video","voice","img"].indexOf(a)&&(i=t.data.url,e="img"==a?"image":a);var n={msgContent:i,msgType:e};this.sendMessage(n)}else this.$utils.message.warning({message:t.msg})},handleUploadFile:function(t){if(this.clearFiles(),1==t.state){var e=JSON.stringify({url:t.data.url,name:t.data.name}),s="file",i=t.data.name,a=this.$utils.getFileType(i,!0);-1!=["image","video","voice","img"].indexOf(a)&&(e=t.data.url,s="img"==a?"image":a);var n={msgContent:e,msgType:s};this.sendMessage(n)}else this.$utils.message.warning({message:t.msg})},handleUploadVoice:function(t){if(this.clearFiles(),1==t.state){var e="voice",s=t.data.name,i=t.data.url,a=this.$utils.getFileType(s,!0);-1!=["image","video","voice","img"].indexOf(a)&&(i=t.data.url,e="img"==a?"image":a);var n={msgContent:i,msgType:e};this.sendMessage(n)}else this.$utils.message.warning({message:t.msg})},handleUploadVideo:function(t){if(this.clearFiles(),1==t.state){var e="video",s=t.data.name,i=t.data.url,a=this.$utils.getFileType(s,!0);-1!=["image","video","voice","img"].indexOf(a)&&(i=t.data.url,e="img"==a?"image":a);var n={msgContent:i,msgType:e};this.sendMessage(n)}else this.$utils.message.warning({message:t.msg})},resize:function(){var t=document.getElementsByTagName("body")[0].clientWidth,e=$(".chat-area").height()-40;e=e>300?e:300,this.flowVH=e,t>800?(this.rows=4,this.isMini=!1):(this.rows=1,this.isMini=!0,this.showSide=!1)},toggleMobileEmoji:function(){this.$nextTick((function(){$("textarea").blur(),this.showMobileTool=!1,this.showMobileEmoji=!this.showMobileEmoji}))},toggleMobileTool:function(){$("textarea").blur(),this.showMobileEmoji=!1,this.showMobileTool=!this.showMobileTool},onScroll:function(t){},custInputPush:function(){this.$api.custInputPush({msgContent:this.inputVal})},sendHot:function(t){var e=t.operType;switch(e){case"1":this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}});break;case"2":this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}});break;case"3":t.linkUrl&&this.$client.openPage(t.linkUrl);break;case"4":this.$utils.message.warning({message:"暂不支持"});break;case"5":var s=t.exJson&&t.exJson.style||{},i=s.width||"80%",a=s.height||"80%",n="auto";s.top&&s.left?n=[s.top,s.left]:s.top&&(n=s.top),layer.open({title:t.questionName,content:t.linkUrl,area:this.isMini?["100%","100%"]:[i,a],offset:this.isMini?"auto":n,shade:!1,maxmin:!0,type:2});break;case"6":window.open(t.linkUrl);break;default:this.sendMessage({msgContent:t.questionName,robotData:{userData:t.userData}})}},jumpPage:function(t){var e=t.url||t.linkUrl;console.log("跳转",e),e&&this.$client.openPage(e)},setKey:function(t){this.$client.start(),this.setting.showKeys=!1,this.sendMessage({isChat:!0,msgType:"event",msgContent:"zxkf",keyCode:t,userData:{keyCode:t}})},selectKey:function(t){this.$client.start(),this.setting.showKeys=!1,this.sendMessage({isChat:!0,msgType:"event",msgContent:t.KEY_NAME||"zxkf",keyCode:t.KEY_CODE,userData:{keyCode:t.KEY_CODE}})},captureCallback:function(t,e,s){this.srcUrl=s?e:"data:image/jpg;base64,".concat(t),this.blobData=t},hideSc:function(){this.srcUrl="",this.blobData=""},sendSc:function(){var t=this;this.$api.uploadImage(this.blobData).then((function(e){if(1==e.state&&e.data.url){var s={msgContent:e.data.url,msgType:"image"};t.sendMessage(s)}})),this.hideSc()},sendLockCheck:function(){if('["on"]'!=this.channelConfigData.MAX_SEND_MSG_SWITCH&&"on"!=this.channelConfigData.MAX_SEND_MSG_SWITCH)return!0;var t=!0,e=new Date,s=e.format("hhmm"),i=e.getTime();return this.lockTime&&i-this.lockTime<this.lockTimeMs?(this.$utils.message.warning({message:this.$t("message.frequently")}),!1):(this.lockTime=null,s==this.sendTime?(this.count++,this.count==this.channelConfigData.MAX_SEND_MSG_COUNT&&(this.lockTime=i,t=!1)):(this.sendTime=s,this.count=0,t=!0),t||this.$utils.message.warning({message:this.channelConfigData.MAX_SEND_MSG_COUNT_MSG||this.msg}),t)},audioToggle:function(){this.audioNotify=!this.audioNotify,window.localStorage&&(localStorage.audioPlay=this.audioNotify)},initDebug:function(){if("undefined"!=typeof VConsole)new VConsole},clearFiles:function(){this.$refs.uploadFile1&&this.$refs.uploadFile1.clearFiles(),this.$refs.uploadFile2&&this.$refs.uploadFile2.clearFiles(),this.$refs.uploadFile3&&this.$refs.uploadFile3.clearFiles(),this.$refs.uploadImg1&&this.$refs.uploadImg1.clearFiles(),this.$refs.uploadImg2&&this.$refs.uploadImg2.clearFiles(),this.$refs.uploadVoice1&&this.$refs.uploadVoice1.clearFiles(),this.$refs.uploadVoice2&&this.$refs.uploadVoice2.clearFiles(),this.$refs.uploadVideo1&&this.$refs.uploadVideo1.clearFiles(),this.$refs.uploadVideo2&&this.$refs.uploadVideo2.clearFiles()},testBeforeUpload:function(t,e){var s=this;if($("body").removeClass("draging"),t.name){var i=t.name,a=$("#uploadWhiteList").val();if(!(String(i).indexOf(".")>-1))return this.$utils.message.warning({message:this.$t("message.typeLimit")}),this.clearFiles(),!1;if(i=i.substring(i.lastIndexOf(".")+1),i=String(i).toLocaleLowerCase(),-1==a.indexOf(i))return this.$utils.message.warning({message:this.$t("message.typeLimit")}),this.clearFiles(),!1}if(t.size){var n=$("#limitValue").val()||20,o=1024*n*1024;if(t.size>o)return this.$utils.message.warning({message:this.$t("message.sizeLimit")+n+"M"}),this.clearFiles(),!1}if("ActiveXObject"in window)return!0;var l="image/jpeg"===t.type||"image/png"===t.type;if(!l)return!0;try{return new Promise((function(e){Rt["compressAccurately"](t,{size:1024,width:1600}).then((function(i){try{var a=new window.File([i],t.name,{type:t.type});console.log("压缩后文件",a,t);var n={originSize:t.size,size:a.size,originType:t.type,type:a.type,name:t.name};s.$api.log({cmd:JSON.stringify(n)}),0==a.size?e(t):e(a)}catch(l){var o={originSize:t.size,originType:t.type,name:t.name,zip:!1};s.$api.log({cmd:JSON.stringify(o)}),e(t)}}))}))}catch(r){var c={originSize:t.size,originType:t.type,name:t.name,zip:!1};return this.$api.log({cmd:JSON.stringify(c)}),!0}},previewMsgSend:function(){this.sendMessage(this.previewMsg),this.previewMsg=null},syncMsg:function(t){var e=this.$client.getBaseConfig(),s={sessionId:e.sessionId,channelKey:e.channelKey,msgData:t};s=JSON.stringify(s);var i="clientMsgSync"+e.channelKey+e.sessionId;localStorage&&localStorage.setItem(i,s)},initSync:function(){var t=this,e=this.$client.getBaseConfig(),s="clientMsgSync"+e.channelKey+e.sessionId;window&&window.addEventListener("storage",(function(e){if(e.key==s)try{var i=JSON.parse(e.newValue).msgData;t.msgList.push(i),t.$nextTick((function(){t.$refs["msgBox"].scrollTop=99999}))}catch(a){}}))},userSatisfy:function(){this.sendMessage({msgContent:"",msgType:"userReqSatisfy",bizType:"userReqSatisfy"})},scrollTopMsg:function(){this.$refs.msgBox.scrollTop=0},miniSizePage:function(){if(this.isIframe){var t={from:"mediaClient",command:"mini"};window.parent.postMessage(t,"*")}},openMessage:function(){this.isMessage=!0;this.$client.getQueryObject();var t=this.channelConfigData.WORD_FORM_URL||"/yc-media/pages/message/index.html",e=this.$client.getBaseConfig(),s={sessionId:e.sessionId,channelKey:e.channelKey,isMini:this.isMini?"1":""},i=$.param(s);t=t.indexOf("?")>=0?t+"&"+i:t+"?"+i,this.msgUrl=t},openCallBack:function(){this.$nextTick((function(){var t=document.getElementById("message");t.onload=function(){var e=t.contentWindow||t.contentDocument.parentWindow,s=window.getComputedStyle(e.document.documentElement).getPropertyValue("height");t.height=s}}))},closeMsgCallBack:function(){this.msgUrl=""},inputFocusFunc:function(){this.inputFocus=!0,this.showMobileTool=!1,this.showMobileEmoji=!1,this.urlOject.iosapp&&$("body").addClass("iosapp-input")},hideInputFocus:function(){this.showMobileTool=!1,this.showMobileEmoji=!1,$("textarea").blur(),this.urlOject.iosapp&&$("body").removeClass("iosapp-input")},showAdvDetail:function(t){layer.alert(t)},handleRefresh:function(){this.pageInfo.pageNo=1,this.loadChatRecord(!0)},showQueue:function(){$("body").removeClass("hideQueueQuit")},hideQueue:function(){$('[data-event="queue"]').hide(),$("body").addClass("hideQueueQuit")}},computed:{uploadUrl:function(){return"/yc-media/servlet/upload?sessionId="+this.$client.getBaseConfig().sessionId},customerStyle:function(){var t="",e=this.channelConfig;return t+="\n                .flow-btn-group .el-button,\n                .satisfy-submit-btn,\n\t            .chat-box .chat-box-header ,\n                .chat-box .chat-box-footer .chat-footer-toolbar .chat-send,\n                .chat-box-msg .userItem-avator,.upload-bar{\n\t    \t\t\tbackground-color:".concat(e.H5_HEAD_COLOR,";\n\t\t\t\t}\n                .flow-btn-group .el-button,\n                .satisfy-submit-btn,\n\t\t\t\t.chat-box .chat-box-header ,\n                .chat-box .chat-box-footer .chat-footer-toolbar .chat-send,\n                .chat-box-msg .userItem-avator,.upload-bar{\n\t    \t\t\tcolor:").concat(e.H5_HEAD_FONT_COLOR,";\n\t\t\t\t}\n\t            "),e.H5_CUSTOMER_FONT_BG_COLOR&&(t+="\n                .chat-box-msg[data-sender=user] .chat-box-msg-info .chat-box-msg-detail {\n                    background-color: ".concat(e.H5_CUSTOMER_FONT_BG_COLOR,";\n                }\n                .chat-box-msg[data-sender=user] .chat-box-msg-info .chat-box-msg-detail:after{\n                    color: ").concat(e.H5_CUSTOMER_FONT_BG_COLOR,";\n                }")),(e.H5_CUSTOMER_FONT_COLOR||e.userFontSize)&&(t+="\n                .chat-box-msg[data-sender=user] .chat-box-msg-info .chat-box-msg-detail,\n                .chat-box-msg[data-sender=user] .chat-box-msg-info .chat-box-msg-detail pre{color:".concat(e.H5_CUSTOMER_FONT_COLOR,";font-size:").concat(e.userFontSize,"px}\n                ")),e.H5_AGENT_FONT_BG_COLOR&&(t+="\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail\n                 {\n                    background-color: ".concat(e.H5_AGENT_FONT_BG_COLOR,"!important;\n                }\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail:after,\n                {\n                    color: ").concat(e.H5_AGENT_FONT_BG_COLOR,"!important;\n                }")),(e.H5_AGENT_FONT_COLOR||e.agentFontSize)&&(t+="\n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail,\n                \n                .chat-box-msg[data-sender=agent] .chat-box-msg-detail pre,\n                {\n                    color: ".concat(e.H5_AGENT_FONT_COLOR,";\n                    font-size:").concat(e.agentFontSize,"px;\n                }\n                ")),e.H5_ROBOT_FONT_BG_COLOR&&(t+="\n                \n                .chat-box-msg[data-sender=robot] .chat-box-msg-detail\n               {\n                    background-color: ".concat(e.H5_ROBOT_FONT_BG_COLOR,"!important;\n                }\n                \n                .chat-box-msg[data-sender=robot] .chat-box-msg-detail:after\n                {\n                    color: ").concat(e.H5_ROBOT_FONT_BG_COLOR,"!important;\n                }")),e.H5_ROBOT_FONT_COLOR&&(t+="\n                .chat-box-msg[data-sender=robot] .chat-box-msg-detail,\n                .chat-box-msg[data-sender=robot] .chat-box-msg-detail pre{\n                    color: ".concat(e.H5_ROBOT_FONT_COLOR,";\n                }\n                ")),e.H5_SYSTEM_FONT_BG_COLOR&&(t+="\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail {\n                    background-color: ".concat(e.H5_SYSTEM_FONT_BG_COLOR,"!important;\n                }\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail:after {\n                    color: ").concat(e.H5_SYSTEM_FONT_BG_COLOR,"!important;\n                }")),e.H5_SYSTEM_FONT_COLOR&&(t+="\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail,\n                .chat-box-msg[data-sender=system] .chat-box-msg-detail pre{\n                    color: ".concat(e.H5_AGENT_FONT_COLOR,";\n                }\n                ")),0==e.H5_SHOW_NICKNAME&&(t+="\n                .chat-box-msg[data-sender=agent] .chat-box-msg-info .chat-box-msg-nickname .chat-nickname{\n                    display:none\n                }\n                "),e.H5_BACKGROUND_URL&&(t+="body:before {\n    \n    background: url(".concat(e.H5_BACKGROUND_URL,") no-repeat center;\n    background-size: cover;\n\n}")),this.flowVH&&(t+="\n\t\t\t\t\t\n\t\t\t\t\t.flowPage.opne{\n\t\t\t\t\t\theight:".concat(this.flowVH,"px;\n\t\t\t\t\t}\n\t\t\t\t\t")),e.H5_SKIN_CODE&&($("body").removeAttr("class"),$("body").addClass("theme-"+e.H5_SKIN_CODE)),"<style>".concat(t,"</style>")},clientType:function(){return this.isMini?"mobile":"pc"},msgWidth:function(){return this.isMini?"80%":"50%"},showPlus:function(){return"0"!==this.channelConfig.BTN_AUTH_PIC||"0"!==this.channelConfig.BTN_AUTH_FILE||!this.showVideo&&"0"!==this.channelConfig.BTN_AUTH_VIDEO||this.showVideo||"0"!==this.channelConfig.BTN_AUTH_AUDIO||this.showSatisfy||this.isMsgBtn}},watch:{inputVal:function(t,e){t.length>=800?document.getElementsByClassName("el-input__count")[0].style.color="red":document.getElementsByClassName("el-input__count")[0].style.color="#000"},bottomNavList:function(t,e){t.length?this.isShow=!0:this.isShow=!1}}},At=Dt,Bt=(s("862a"),s("7447"),s("9cab"),Object(dt["a"])(At,it,at,!1,null,"1ed6f26e",null)),Pt=Bt.exports,Ft=s("9ae2");String.prototype.trimEnd=function(t){if(null==t||""==t){var e=this,s=/s/,i=e.length;while(s.test(e.charAt(--i)));return e.slice(0,i+1)}e=this,s=new RegExp(t),i=e.length;while(s.test(e.charAt(--i)));return e.slice(0,i+1)};var zt={name:"App",components:{pc:Pt,mobile:Ft["default"]}},Vt=zt,jt=(s("7c55"),Object(dt["a"])(Vt,et,st,!1,null,null,null)),Gt=jt.exports,qt=s("4360"),Kt=s("268b"),Qt=s("fe3c"),Yt=s.n(Qt),Wt=s("0692"),Jt=s("3491"),Xt=s("02f1"),Zt=s("2daa"),te=s("90b9"),ee=s("3835");s("159b"),s("4fad"),s("bc3a");function se(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=new URL(t,window.location.origin);Object.entries(e).forEach((function(t){var e=Object(ee["a"])(t,2),i=e[0],a=e[1];s.searchParams.append(i,a)}));var i=new EventSource(s);return i.onopen=function(){console.log("连接已建立")},i.onmessage=function(t){try{if("[DONE]"===t.data||t.end||t.data.end)return console.log("流结束"),void i.close();var e=JSON.parse(t.data);pt["a"].$emit("sseMsg",e),1==e.end&&(console.log("收到消息end:",e),i.close())}catch(s){console.error("数据解析错误:",s)}},i.onerror=function(t){console.error("连接错误:",t),i.readyState===EventSource.CLOSED&&console.log("连接已关闭")},i}var ie={openSSE:function(t){se("/yc-mediagw/webchat/stream",t),console.log("SSE开启")},closeSSE:function(){eventSource.close()}},ae=navigator,ne=ae.userAgent,oe=!!ne.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);Yt.a.prototype.needsClick=function(t){switch(t.nodeName.toLowerCase()){case"button":if(oe&&"chat-send"==t.className)return!1;case"select":case"textarea":if(!t.disabled)return!0;break;case"input":if(oe&&"file"===t.type||!t.disabled)return!0;break;case"label":case"iframe":case"video":return!0;case"div":case"img":case"p":case"span":case"a":case"i":return!0}},Yt.a.attach(document.body),tt["default"].use(Kt["a"]),tt["default"].use(Z.a),tt["default"].use(J.a),tt["default"].use(Y.a),tt["default"].use(K.a),tt["default"].use(G.a),tt["default"].use(V.a),tt["default"].use(F.a),tt["default"].use(B.a),tt["default"].use(D.a),tt["default"].use(U.a),tt["default"].use(N.a),tt["default"].use(E.a),tt["default"].use(M.a),tt["default"].use(k.a),tt["default"].use(S.a),tt["default"].use(_.a),tt["default"].use(b.a),tt["default"].use(C.a),tt["default"].use(f.a),tt["default"].use(p.a),tt["default"].use(h.a),tt["default"].use(d.a),tt["default"].use(c.a),tt["default"].use(o.a),tt["default"].use(a.a),tt["default"].config.productionTip=!1,tt["default"].prototype.$client=Jt["a"],tt["default"].prototype.$api=Xt["a"],tt["default"].prototype.$thirdPartyApi=Zt["a"],tt["default"].prototype.$utils=te["a"],tt["default"].prototype.$sse=ie,window.vueClient=new tt["default"]({store:qt["a"],i18n:Wt["a"],render:function(t){return t(Gt)}}).$mount("#app"),document.addEventListener("dragstart",(function(t){$("body").addClass("draging")})),document.addEventListener("dragover",(function(t){$("body").addClass("draging")})),document.addEventListener("dragend",(function(t){$("body").removeClass("draging")})),document.addEventListener("mouseup",(function(t){$("body").removeClass("draging")}))},"6a8a":function(t,e,s){"use strict";var i=s("6b5d"),a=s.n(i);e["default"]=a.a},"6b2a":function(t,e,s){},"6b5d":function(t,e){},7447:function(t,e,s){"use strict";s("6b2a")},"7c55":function(t,e,s){"use strict";s("2395")},"862a":function(t,e,s){"use strict";s("9e0e")},"9ae2":function(t,e,s){"use strict";var i=s("19d0"),a=s("6a8a"),n=s("2877"),o=Object(n["a"])(a["default"],i["a"],i["b"],!1,null,null,null);e["default"]=o.exports},"9cab":function(t,e,s){"use strict";s("afa6")},"9e0e":function(t,e,s){},afa6:function(t,e,s){},b681:function(t,e,s){},b9ee:function(t,e,s){}});