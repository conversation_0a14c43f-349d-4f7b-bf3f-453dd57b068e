package com.yunqu.yc.agent.mqclient;

import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;

import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.MQContext;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.log.MonitorLogger;

/**
 * 会话监控消息队列生产者-广播模式
 * <AUTHOR>
 *
 */
public class MonitorProducerBroker {
	
	
	private static  Broker  monitorBroker;
	
	public static void sendMonitorMessage(String message) {
		if (monitorBroker == null) { 
			monitorBroker = BrokerFactory.getProducerTopicBroker(MQContext.getAddr(), Constants.getMonitorBrokerName(), MQContext.getUser(),MQContext.getPassword());
		}
		
		MonitorLogger.getLogger().info("<Producer>[MonitorProducerBroker] >> "+message);
		try {
			monitorBroker.sendMessage(message);
		} catch (Exception ex) {
			MonitorLogger.getLogger().error(" >> sendMonitorMessage("+Constants.getMonitorBrokerName()+") error,cause:"+ex.getMessage(),ex);
		}
	}
	
	/**
	 * 关闭所有MQ消息队列的生产者
	 * 只有MQ服务连接异常或中断，应用停止时调用
	 */
	public static void closeProducerBroker() {
		try {
			if(monitorBroker!=null) monitorBroker.close();
			monitorBroker = null;
		} catch (Exception e) {
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
	}
}

