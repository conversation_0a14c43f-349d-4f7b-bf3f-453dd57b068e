package com.yunqu.yc.agent.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.model.Channel;
import org.easitline.common.utils.string.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.util.*;


public class EntContext {
	
	private String petraGwUrl;
	private String marsGwUrl;
	private String entId;
	private String schemaId;
	private String marsId;
	private String petraId;
	private String bpoPrefix;
	private String entName;
	private String entCode;
	private String resEntId;
	private String satisfPrefix;  //SATISF_PREFIX
	private String ivrOrderPrefix;  //
	private String marsUrl;
	private int license = 0 ;
	/**
	 * 
	{
		"CCBAR_DES_KEY": "",
		"CCBAR_PHONE_CRYPT": "false",
		"RING_CHECK_FLAG": "0",
		"CALLBACK_URL": "",
		"RECORD_FLAG": "0",
		"CCBAR_PROXY": "1",  //0 JS-SDK方式   1  webSocket接口方式   2 http接口方式
		"PHONE_CRYPT_SHOW": "false"
	}
	 */
	private JSONObject extConf = new JSONObject();

	private Map<String,String> busiOrderIds = null;
	
	private static Logger logger = MediaLogger.getLogger();
	
	private static  Map<String,String> schemas = new HashMap<String,String>();

	private static Map<String,EntContext> contexts = new HashMap<String,EntContext>();
	
	private static Map<String,Channel> channels = new HashMap<String,Channel>();
	
	/**
	 * 坐席在该企业下的置忙原因类型
	 */
	private static  Map<String,JSONObject> busiTypeMap = new HashMap<String,JSONObject>();
	
	public static EntContext  getContext(String entId) throws Exception {
		if(StringUtils.isBlank(entId)){
			MediaLogger.getLogger().error("entId is blank!!!");
			throw new Exception("entId is blank!!!");
		}
		EntContext context = contexts.get(entId);
		if(context!=null){
			return context;
		}
		synchronized (entId.intern()){
			context = contexts.get(entId);
			if(context!=null){
				return context;
			}
			context = new EntContext(entId);
			try {
				context.init();
				context.initChannelInfo(null,null);
				contexts.put(entId, context);
			} catch (Exception ex) {
				logger.error("EntContext.init() error >>cause:"+ex.getMessage(),ex);
			}
		}
		return context;
	}
	
	public String getEntCode() {
		return entCode;
	}
	
	public int getLicense() {
		return license;
	}
	public void setLicense(int license) {
		this.license = license;
	}

	public String getResEntId() {
		if(StringUtils.isBlank(this.resEntId))  return this.entId;
		if("0".equals(this.resEntId)) return this.entId;
		return resEntId;
	}

	public void setResEntId(String resEntId) {
		this.resEntId = resEntId;
	}

	public String getMarsUrl() {
		return marsUrl;
	}

	
	public EntContext(String entId){
		this.entId = entId;
	}
	
	public String getEntName(){
		return this.entName;
	}
	
	public String getExtConf(String key){
		return this.extConf.getString(key);
	}
	
	public String getBusiOrderId(String busiId){
		return this.busiOrderIds.get(busiId);
	}
	
	public boolean isWebsocket(){
		return "1".equalsIgnoreCase(getExtConf("CCBAR_PROXY"));
	}
	
	public String getSatisfPrefix() {
		return satisfPrefix;
	}

	public String getIvrOrderPrefix() {
		return ivrOrderPrefix;
	}

	public String getEntId() {
		return entId;
	}

	public String getSchemaId() {
		return schemaId;
	}

	public String getMarsId() {
		return marsId;
	}

	public String getPetraId() {
		return petraId;
	}

	public String getPetraGwUrl(){
		return this.petraGwUrl;
	}
	
	public String getMarsGwUrl(){
		return this.marsGwUrl;
	}


	public String getBpoPrefix() {
		return bpoPrefix;
	}
	/**
	 * 获得企业所在的schema
	 * @param tableName 企业名称
	 * @return
	 */
	public String getTableName(String tableName){
		String schemaId = schemas.get(entId);
		if(StringUtils.isBlank(schemaId)){
			String sql = "select SCHEMA_ID from CC_ENT_RES where ENT_ID = ? ";
			try {
				schemaId = QueryFactory.getQuery(entId).queryForString(sql, this.getResEntId());
			} catch (Exception ex) {
				MediaLogger.getLogger().error("EntContext.getTableName error,cause:"+ex.getMessage(),ex);
			}
			schemas.put(entId, schemaId);
		}
		return schemaId + "." + tableName;
	}

	/**
	 * 根据置忙类型，获取该企业下对应的置忙原因名称
	 * @param busiType
	 * @return
	 */
//	public String getEntAgentBusiType(String busiType){
//		String busiTypeName = busiTypeMap.get(busiType);
//		if(StringUtils.isBlank(busiTypeName)){
//			busiTypeName = "忙";
//		}
//		return busiTypeName;
//	}
	
	/**
	 * 初始化企业的相关上下文信息，包括：磐石网关，mars网关和数据库等
	 * @throws Exception
	 */
	private void init() throws Exception{
		
		
		String sql = "select * from CC_ENT where ENT_ID = ?";
		
		
		EasyRow entRow = QueryFactory.getQuery(entId).queryForRow(sql, this.entId);
		
		this.resEntId = entRow.getColumnValue("P_ENT_ID");
		
		
		sql = "select  t1.ENT_ID ,t1.PETRA_ID,t1.MARS_ID,t2.PETRA_URL,t3.MARS_URL ,t1.SCHEMA_ID,t1.AGENT_LICENSE,t1.BPO_CALL_PREFIX,t2.SATISF_PREFIX ,t2.IVR_ORDER_PREFIX     " +
				"from CC_ENT_RES  t1  , CC_PETRA_RES t2 ,  CC_MARS_RES t3  where t1.PETRA_ID = t2.PETRA_ID and t1.MARS_ID = t3.MARS_ID  and t1.ENT_ID = ?";
		EasyRow row = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{this.getResEntId()});
		this.petraId = row.getColumnValue("PETRA_ID");
		this.marsId = row.getColumnValue("MARS_ID");
		this.petraGwUrl = row.getColumnValue("PETRA_URL")+"/petradatagw/interface";
		this.marsGwUrl = row.getColumnValue("MARS_URL") +"/yc-api/interface";
		this.marsUrl = row.getColumnValue("MARS_URL");
		this.schemaId = row.getColumnValue("SCHEMA_ID");
		this.bpoPrefix = StringUtils.trimToEmpty(row.getColumnValue("BPO_CALL_PREFIX"));
		this.satisfPrefix = row.getColumnValue("SATISF_PREFIX");
		this.ivrOrderPrefix = row.getColumnValue("IVR_ORDER_PREFIX");
		try {
			this.license = Integer.parseInt(row.getColumnValue("AGENT_LICENSE"));
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex,ex);
		}
		this.entCode = entRow.getColumnValue("ENT_CODE");
		this.entName =  entRow.getColumnValue("ENT_NAME");
		String extConfString = entRow.getColumnValue("ENT_EXT_CONF");
		if(StringUtils.isNotBlank(extConfString)){
			this.extConf = JSONObject.parseObject(extConfString);
		}
		
		busiOrderIds = new HashMap<String, String>();
		sql = "select BUSI_ORDER_ID,BUSI_ID  from   CC_BUSI_ORDER  where ENT_ID = ?";
		List<JSONObject>  orders =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
		for(JSONObject jsonObject:orders){
			busiOrderIds.put(jsonObject.getString("BUSI_ID"), jsonObject.getString("BUSI_ORDER_ID"));
		}
		
		//加载该企业下的置忙原因定义，缺点：修改后5分钟后才生效
//		if(Constants.isExtBusyType()){
//			sql = "select BUSY_TYPE ,BUSY_TYPE_NAME from " + this.schemaId + ".CC_AGENT_BUSY_TYPE where STATE = 1 and ENT_ID = ? order by IDX_ORDER ";
//			List<JSONObject>  busiTypes =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
//			for(JSONObject jsonObject:busiTypes){
//				busiTypeMap.put(jsonObject.getString("BUSY_TYPE"), jsonObject.getString("BUSY_TYPE_NAME"));
//			}
//		}else{
//			busiTypeMap.put("1", "小休");
//			busiTypeMap.put("2", "会议");
//			busiTypeMap.put("3", "培训");
//		}
	}
	
	/**
	 * 初始化渠道信息
	 */
	private void initChannelInfo(String channelId,String channelKey){
		EasySQL sql = new EasySQL("select * from CC_CHANNEL");
		sql.append(" where 1=1");
		sql.append(entId," and ENT_ID = ?");
		sql.append(channelId," and CHANNEL_ID = ?");
		sql.append(channelKey," and CHANNEL_KEY = ?");
		try {
			List<EasyRow> rows =  QueryFactory.getQuery(entId).queryForList(sql.getSQL(), sql.getParams());
			for(EasyRow row:rows){
				String channelId1 = row.getColumnValue("CHANNEL_ID");
				Channel channel = channels.get(channelId1);
				if(channel==null){
					channel = new Channel();
					channels.put(channelId1, channel);
				}
				channel.setChannelId(channelId1);  //这里送给平台的是坐席工号，例如：8001
				channel.setChannelName(row.getColumnValue("CHANNEL_NAME"));
				channel.setEntId(this.entId);
				channel.setChannelKey(row.getColumnValue("CHANNEL_KEY"));
				channel.setChannelType(row.getColumnValue("CHANNEL_TYPE"));
				try {
					channel.setChannelConf(JSON.parseObject(row.getColumnValue("CHANNEL_CONF"))); //3.1#20210520-1 渠道信息里增加配置相关的属性
					channel.setChannelAutoConf(JSON.parseObject(row.getColumnValue("CHANNEL_AUTO_CONF")));
					channel.setStyleConf(StringUtils.isBlank(row.getColumnValue("STYLE_CONF"))?new JSONObject():JSONObject.parseObject(row.getColumnValue("STYLE_CONF")));
				}catch (Exception e){
					MediaLogger.getLogger().error(e.getMessage(),e);
				}
				channel.reloadChannelKey();
			} 	
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	/**
	 * 根据渠道id查询渠道信息 3.1#20210520-1
	 * @param channelId
	 * @return
	 */
	public Channel getChannel(String channelId) {
		Channel channel = channels.get(channelId);
		channel.reload();
		return channel;
	}
	
	/**
	 * 根据渠道key查询渠道信息 3.1#20210520-1
	 * @param channelKey
	 * @return
	 */
	public Channel getChannelByKey(String channelKey) {
		if(StringUtils.isBlank(channelKey)){
			return null;
		}
		HashSet<Channel> channels = new HashSet<>(EntContext.channels.values());
		for (Channel channel : channels) {
			if(StringUtils.equals(channel.getChannelKey(), channelKey)) {
				channel.reload();
				return channel;
			}
		}
		synchronized (channelKey.intern()){
			MediaLogger.getLogger().warn("根据渠道key查询渠道信息，在内存中无法查询到渠道信息，重新初始化该渠道："+channelKey);
			this.initChannelInfo(null,channelKey);

			channels = new HashSet<>(EntContext.channels.values());
			for (Channel channel : channels) {
				if(StringUtils.equals(channel.getChannelKey(), channelKey)) {
					return channel;
				}
			}
		}
		MediaLogger.getLogger().warn("根据渠道key查询渠道信息，在内存中无法查询到渠道信息，请确定渠道key是否正确！！！！"+channelKey);
		return null;
	}
}
