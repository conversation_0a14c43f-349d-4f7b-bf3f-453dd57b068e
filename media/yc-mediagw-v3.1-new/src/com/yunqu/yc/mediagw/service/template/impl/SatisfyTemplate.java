package com.yunqu.yc.mediagw.service.template.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.MediaConstants;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.service.template.MediaTemplate;
import com.yunqu.yc.mediagw.service.template.TemplateMsgService;
import com.yunqu.yc.mediagw.util.CommonUtil;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: SatisfyTemplate
 * @Description: 满意度模板消息
 * <AUTHOR>
 * @Date 2022/7/14
 * @Version 1.0
 */
public class SatisfyTemplate extends MediaTemplate {
    public SatisfyTemplate() {
        super(TemplateMsgService.TEMP_TYPE_SATISFY);
    }

    public SatisfyTemplate(String tempId){
        super(TemplateMsgService.TEMP_TYPE_SATISFY,tempId);
    }
    @Override
    public void setData(String channelKey,String sessionId,JSONObject channelConf,JSONObject msgData) throws Exception{

        //3.1#20210602-1
//				String openSatisfy = channelConf.getString("OPEN_SATISFY");// 渠道是否开启满意度 0 否,1 是
//				if("0".equals(openSatisfy)) {//为了兼容旧版本，所以为空的默认开启满意度
//					MediagwServerLogger.getLogger().warn("TemplateMsgService.getTemplate() 渠道配置未开启满意度：openSatisfy="+openSatisfy);
//					break;
//				}
        //满意推送方式(SATISFY_TYPE)： 为空-文本，satisfy1-文本，  satisfy2-NPS，   satisfy3-表情(笑脸)	，   satisfy4-星星，   satisfy5-图文
        String satisfyType = channelConf.getString("SATISFY_TYPE");
        if(StringUtils.isBlank(satisfyType)) {
            satisfyType = "satisfy1";
        }else{
            this.setMsgType(MediaConstants.MSG_TYPE_TEMPLATE);
        }

        String  sql = " SELECT NAME,CODE,SORT_NUM,TYPE FROM CC_MEDIA_SATISF_QUOTA WHERE 1=1 AND CHANNEL_NO = ? AND ENABLE_STATUS = '01' AND SATISFY_FLAG=1 ORDER BY SORT_NUM";
        List<JSONObject> tempData = QueryFactory.getReadQuery().queryForList(sql, new Object[] {channelKey},new JSONMapperImpl());

        if("satisfy5".equals(satisfyType)||"satisfy6".equals(satisfyType)) {
            tempData = new ArrayList<>();
            JSONObject dataObj = new JSONObject();
            dataObj.put("newsTitle", channelConf.getString("CC_MEDIA_SATISFY_NEWS_TITLE"));
            dataObj.put("newsDesc", channelConf.getString("CC_MEDIA_SATISFY_NEWS_DESC"));
            String newsUrl = channelConf.getString("CC_MEDIA_SATISFY_NEWS_URL");
            // 从数据库里边获取最新的用户接入信息对象
            JSONObject lastRecord = VisitorInfos.getInstance().findLastRecord(sessionId, channelKey);
            String chatSessionId = lastRecord.getString("SERIAL_ID");//用户chatSessionId

            //3.1#20211126-1 兼容链接里没有参数的场景
            if(!newsUrl.contains("?")){
                newsUrl += "?";
            }else{
                newsUrl += "&";
            }

            if(!newsUrl.contains("#timestamp#")){
                newsUrl += "timestamp="+ System.currentTimeMillis();
            }

            String satisfyUrl = CommonUtil.getSatisfyUrl(newsUrl,chatSessionId, channelKey, sessionId,1, null, null);

            dataObj.put("newsUrl", satisfyUrl);
            dataObj.put("newsPicUrl", channelConf.getString("CC_MEDIA_SATISFY_NEWS_PICURL"));
            tempData.add(dataObj);
        }

        //查询满意度内容
        sql = " SELECT CONTENT FROM CC_MEDIA_SATISF WHERE CHANNEL_NO = ?";
        List<EasyRow> list = QueryFactory.getReadQuery().queryForList(sql, channelKey);
        if(list!=null&&list.size()>0){
            String content = list.get(0).getColumnValue("CONTENT");
            this.setTempContent(content);
        }
        this.setTempId(satisfyType);
        this.setTempData(tempData);
        this.setServiceId("TEMP-MSG-CBACK-SATISFY-SERVICE");//处理回调的服务id，通过渠道配置，由业务实现
    }
}
