package com.yunqu.yc.mediagw.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.mediagw.base.Constants;
import com.yunqu.yc.mediagw.base.EntContext;
import com.yunqu.yc.mediagw.base.MediaConstants;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.log.EventLogger;
import com.yunqu.yc.mediagw.log.MediagwServerLogger;
import com.yunqu.yc.mediagw.log.VisitorInfosLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.RequestDataModel;
import com.yunqu.yc.mediagw.model.VisitorModel;
import com.yunqu.yc.mediagw.service.clientMsg.impl.RobotService;
import com.yunqu.yc.mediagw.service.storage.AccessRecordService;
import com.yunqu.yc.mediagw.service.storage.ChatMessageService;
import com.yunqu.yc.mediagw.service.storage.MediaSatisfyService;
import com.yunqu.yc.mediagw.service.template.MediaTemplate;
import com.yunqu.yc.mediagw.service.template.TemplateMsgService;
import com.yunqu.yc.mediagw.thirdchat.service.ThirdChatService;
import com.yunqu.yc.mediagw.util.CacheUtil;
import com.yunqu.yc.mediagw.util.CommonUtil;
import com.yunqu.yc.mediagw.util.LogUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

/**
 * 全媒体路由中心（yc-mediacenter）发送消息到用户端
 * <AUTHOR>
 *
 */
public class CenterServerMessage extends ServerMessage {

	private static final Logger logger = MediagwServerLogger.getLogger();
	
	@Override
	public void invoke(JSONObject msgObj) {
		VisitorModel visitor =  null;
		try {
			MediagwServerLogger.getLogger().info("<CenterServerMessage> 收到yc-mediacenter消息 << "+msgObj);
			
			JSONObject _dataObject = msgObj.getJSONObject("data");

			String serialId = msgObj.getString("serialId");
			serialId = StringUtils.isNotBlank(serialId)?serialId:RandomKit.randomStr();

			if (_dataObject == null) {
				logger.error("<CenterServerMessage> error ,cause:jsonObject is null");
				return;
			}

			//系统回复语标志,replay=true时 需要入库
			boolean sysReplay = msgObj.getBooleanValue("replay");

			String sessionId = _dataObject.getString("sessionId");
			visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);

			if (visitor==null||StringUtils.isBlank(visitor.getChannelKey())) {
				logger.warn("<CenterServerMessage> error ,cause:user["+sessionId+"] VisitorModel is null!,sessionId:"+sessionId);
				visitor = VisitorInfos.getInstance().getVisitorByDB(sessionId,_dataObject.getString("channelKey"));
				VisitorInfosLogger.getLogger().info("VisitorInfos.getInstance().getVisitorByDB("+sessionId+")->"+visitor);
				logger.warn("<CenterServerMessage> getVisitorByDB("+sessionId+")--->"+visitor);
			}
			if (visitor == null) {
				logger.error("<CenterServerMessage> error ,cause:VisitorModel is null,sessionId:"+sessionId);
				return;
			}
			LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"<CenterServerMessage> 收到yc-mediacenter消息... ", msgObj.toString(), null);
			
			EntContext entContext = EntContext.getContext(visitor.getChannelKey());

			String callbackUrl = visitor.getCallbackUrl();
			if(StringUtils.isBlank(callbackUrl)){
				logger.warn("<CenterServerMessage> error ,cause:user["+sessionId+"] callbackUrl is null!");
			}

			String msgType = _dataObject.getString("msgType");
			_dataObject.put("serialId", serialId);
			JSONObject simpleUserInfo = visitor.getSimpleUserInfo();
			if(simpleUserInfo!=null) {
				simpleUserInfo.put("userData", _dataObject.getJSONObject("userData"));
				_dataObject.put("userInfo", simpleUserInfo);
			}
			_dataObject.put("entId", visitor.getEntId());
			if(StringUtils.isBlank(_dataObject.getString("sender"))){
				_dataObject.put("sender", "system");
			}
			if(StringUtils.isBlank(_dataObject.getString("chatSessionId"))){
				_dataObject.put("chatSessionId", visitor.getChatSessionId());
			}
			//20210310 mediacenter发送的系统消息data里没有channelKey需要写进去
			if(StringUtils.isBlank(_dataObject.getString("channelKey"))){
				_dataObject.put("channelKey", visitor.getChannelKey());
			}

			//20200813替换内容中的指定关键字
			replaceMsgContent(_dataObject,visitor);

			RequestDataModel  requestDataModel = new RequestDataModel(Constants.getSecretkey());
			requestDataModel.setSerialId(serialId);
			requestDataModel.setChannelKey(visitor.getChannelKey());
			requestDataModel.setEntId(visitor.getEntId());
			requestDataModel.setTimestamp(System.currentTimeMillis());
			String mediaEvent = _dataObject.getString("mediaEvent");
			if(StringUtils.isBlank(mediaEvent)) mediaEvent = msgObj.getString("event");

//			logger.info("<CenterServerMessage> 当前消息事件类型 mediaEvent<"+mediaEvent+"> 当前用户状态 bizType<"+visitor.getBizType()+">sessionId<"+visitor.getSessionId()+">");
			LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"<CenterServerMessage> 当前消息事件类型 mediaEvent<"+mediaEvent+"> 当前用户状态 bizType<"+visitor.getBizType()+"> visitor.hashCode():"+visitor.hashCode(), msgObj.toString(), logger);
			
			
			requestDataModel.setEvent(mediaEvent);
			if("event".equals(msgType))_dataObject.put("event", mediaEvent);

			if(StringUtils.isBlank(requestDataModel.getEvent())){
				requestDataModel.setEvent("agent");
			}

			//收到用户强制关闭会话,不回复任何消息,userData.stopChat = true
			if("true".equalsIgnoreCase(visitor.getUserInfo().getString("stopChat"))) {
				visitor.setChatSessionId(RandomKit.randomStr());
				visitor.getUserInfo().remove("stopChat");
				visitor.setBizType(MediaConstants.VISITOR_STATE_WELCOME);
				LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"用户强制关闭会话，不回复任何消息给客户，将bizType设置为welcome..", null, null);
				return;
			}

			if("start".equals(mediaEvent)){
				String agentId = msgObj.getString("agentId");
				String skillGroupId = msgObj.getString("skillGroupId");
				requestDataModel.setEvent("start");
				visitor.setAgentId(agentId);
				visitor.setSkillGroupId(skillGroupId);
				String curBizType = visitor.getBizType();//当前对象会话状态
				String curChatSessionId = visitor.getChatSessionId();//当前对象中的会话id
				String startChatSessionId = _dataObject.getString("chatSessionId");//接入人工时的会话id
//				会话状态：robot时，坐席可能激活用户会话
//				收到转人工成功的事件消息后，消息中的chatSessionId与当前会话对象的chatSessionId不一致时，更新当前会话chatSessionId对应的接入记录为“机器人正常结束”，通知机器人网关下线，
				if(!StringUtils.equals(curChatSessionId,startChatSessionId)){
					LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"开始接入人工会话，当前visitor对象的会话id与消息中的会话id不一致，startChatSessionId："+startChatSessionId, null, MediagwServerLogger.getLogger());
					if(MediaConstants.VISITOR_STATE_ROBOT.equals(curBizType)||MediaConstants.VISITOR_STATE_ROBOT_TO_AGENT_CONFIRM.equals(curBizType)){
						//更新接入记录
						String channelKey = visitor.getChannelKey();
						String dateTime = EasyCalendar.newInstance().getDateTime("-");
						JSONObject accesssData = new JSONObject();
						accesssData.put("CHAT_SESSION_ID", curChatSessionId);
						accesssData.put("ROBOT_END_TIME", dateTime);
						accesssData.put("END_TIME", dateTime);
						accesssData.put("CLEAR_CAUSE", 1);//1.机器人正常结束；
						AccessRecordService.getInstance().updateAccessRecord(channelKey, accesssData);
						//通知机器人下线，结束机器人会话
						CommonUtil.notifyEndRobotChat(visitor);
					}
				}
				visitor.setChatSessionId(startChatSessionId);
				visitor.setBizType(MediaConstants.VISITOR_STATE_ZXKF);
				CacheUtil.put("USER_VISITOR_"+sessionId,visitor.toJSONString(), 3600 * 8);
				CacheUtil.put("BROKER_USER__VISITOR_"+sessionId,visitor.toJSONString(), 3600 * 8);

				//同步第三方用户历史消息
				JSONObject channelConf = entContext.getChannelConf();
				String startAgentServiceId = channelConf.getString("START_AGENT_SERVICE_ID");
				if(StringUtils.isNotBlank(startAgentServiceId)){
					IService service = ServiceContext.getService(startAgentServiceId);
					if(service != null){
						JSONObject jsonObject = new JSONObject();
						jsonObject.put("command", "startEvent");
						jsonObject.put("sessionId", sessionId);
						jsonObject.put("entId", entContext.getEntId());
						jsonObject.put("schemaId", entContext.getSchemaId());
						jsonObject.put("chatSessionId", _dataObject.getString("chatSessionId"));
						EventLogger.getLogger().info("sessionId["+sessionId+"]触发第三方服务参数："+jsonObject);
						JSONObject thirdResp = service.invoke(jsonObject);
						EventLogger.getLogger().info("sessionId["+sessionId+"]触发第三方服务结果："+thirdResp);
					}
				}
			}

			//会话转移，更新agentId，skillGroupId，chatSessionId
			if("transfer".equals(mediaEvent)){
				ThirdChatService.getInstance().closeThirdChat(visitor.getChatSessionId(),MediaConstants.THIRD_CHAT_CLEAR_CAUSE_TRANSFER);
				String agentId = msgObj.getString("agentId");
				String skillGroupId = msgObj.getString("skillGroupId");
				visitor.setAgentId(agentId);
				visitor.setSkillGroupId(skillGroupId);
				visitor.setChatSessionId(_dataObject.getString("chatSessionId"));
				visitor.setBizType(MediaConstants.VISITOR_STATE_ZXKF);
				CacheUtil.put("USER_VISITOR_"+sessionId,visitor.toJSONString(), 3600 * 8);
				CacheUtil.put("BROKER_USER__VISITOR_"+sessionId,visitor.toJSONString(), 3600 * 8);
			}

			if("selectKey".equals(mediaEvent)){
				requestDataModel.setEvent("welcome");
				visitor.setBizType(MediaConstants.VISITOR_STATE_SELECTKEY);
			}

			if("welcome".equals(mediaEvent)){
				requestDataModel.setEvent("welcome");
				visitor.setBizType(MediaConstants.VISITOR_STATE_WELCOME);
			}
			//坐席发送的消息
			if("agent".equals(mediaEvent)){
				requestDataModel.setEvent("agent");
				if(!_dataObject.containsKey("sender")) _dataObject.put("sender", "agent");
			}

			//正在排队中，不接受88以外的命令
			if("queue".equals(mediaEvent)){
				requestDataModel.setEvent("queue");
				visitor.setQueueStartTime(System.currentTimeMillis());
				visitor.setBizType(MediaConstants.VISITOR_STATE_QUEUE);
				if(!_dataObject.containsKey("queueNo")){
					_dataObject.put("queueNo", CacheUtil.get("userQueueNo_" + sessionId));
					_dataObject.put("inQueueTime", CacheUtil.get("userInQueueTime_"+sessionId));
				}
			}
			//v2.5.1#20191126-1
			//正在等待确认排队中，不接受88以外的命令和确定排队命令“是”
			if("queueConfirm".equals(mediaEvent)){
				requestDataModel.setEvent("queueConfirm");
				visitor.setBizType(MediaConstants.VISITOR_STATE_QUEUECONFIRM);
			}

			//这里如果是自动回复，则设置转机器人
			if("automatic".equals(mediaEvent)){
				MediagwServerLogger.getLogger().info("<CenterServerMessage> 收到yc-mediacenter 识别按键：转机器人 << "+msgObj);
				requestDataModel.setEvent("robot");
				visitor.setSelectKey(_dataObject.getString("msgContent"));
				visitor.setBizType(MediaConstants.VISITOR_STATE_ROBOT);

				_dataObject.put("msgType",  MediaConstants.MSG_TYPE_TEXT);
				boolean welcomeFlag = entContext.sendRobotWelcomeFlag();
				if(welcomeFlag) {
					//发送到机器人网关
					_dataObject.put("welcomeFlag", 1);
					LogUtil.addTraceLog(visitor.getChannelKey(), visitor.getSessionId(),visitor.getChatSessionId(),visitor.getBizType(),"<CenterServerMessage> 收到yc-mediacenter 识别按键：转机器人，渠道配置开启了机器人欢迎语，将automatic转发给机器人，由机器人回复欢迎语...",msgObj.toJSONString(), logger);
					new RobotService().doing(visitor,msgObj);
					return;
				}
				String msgContent = entContext.getChannelAutoConf().getString("VISITOR_ACCESS_ROBOT_MSG");
//				String msgContent = visitor.getSysMsg("VISITOR_ACCESS_ROBOT_MSG");
//				msgContent = StringUtils.isNotBlank(msgContent)?msgContent:"请一句话描述您的问题，我们来帮您解决并转到合适的人工服务。";
				//保存系统回复语
				_dataObject.put("sender", "robot");
				_dataObject.put("msgContent",msgContent);
				msgObj.put("data", _dataObject);

				ChatMessageService.getInstance().saveMessage(visitor.getChannelKey(), _dataObject, MediaConstants.MSG_SENDER_ROBOT);
				//由于yc-mediacenter改了所有消息都是系统消息，所以此处不能保存为系统消息
				sysReplay = false;
				//机器人消息体
				JSONObject contentObj = new JSONObject();
				contentObj.put("content", msgContent);
				_dataObject.put("msgContent",contentObj.toJSONString());

				//2.0#202000507-1 更新接入记录的进机器人时间
				JSONObject data = new JSONObject();
				data.put("CHAT_SESSION_ID", visitor.getChatSessionId());
				data.put("IS_IN_ROBOT", 1);
				data.put("ROBOT_START_TIME", EasyCalendar.newInstance().getDateTime("-"));
				AccessRecordService.getInstance().updateAccessRecord(visitor.getChannelKey(),data);
			}

			//视频客服事件，20201207参照《2021年美的用户交互中心平台视频客服项目详细设计说明书-20201123》
			if(mediaEvent.toLowerCase().contains("video")) {
				logger.info("<CenterServerMessage>收到mediacenter的视频事件消息，mediaEvent<"+mediaEvent+">");
				requestDataModel.setEvent(mediaEvent);
				_dataObject.put("event", mediaEvent);
				visitor.setVideoState(mediaEvent);
				visitor.cache();
			}

			requestDataModel.setData(_dataObject);

			//处理close事件，关闭会话。// '关闭原因， 1 用户主动结束服务  2  坐席结束服务  3  超时结束  4 用户排队结束  5 用户排队超时 6 转移结束'
			//TODO:默认清除按键信息，坐席信息，技能组信息。
			//1.会话已接入人工，clearCause=（1,2,3），如果开启了满意度并且满意推送方式为空或者是“文本方式（satisfy1-）”用户会话id不能重置，会话状态设置为bizType = “satisfy”，否则重置用户会话id,会话状态设置为bizType = “end”；
			//2.用户取消排队，clearCause=4，重置用户会话id
			//3.排队超时，clearCause=5，判断是否渠道配置是否配置了排队超时进留言，未配置则重置用户会话id，配置了就进入留言状态，会话状态设置为bizType = “word”，用户会话id不能重置；
			//4.转移结束，clearCause=6
			if("close".equals(mediaEvent)){
				//结束所有三方会话
				ThirdChatService.getInstance().closeThirdChat(visitor.getChatSessionId(),MediaConstants.THIRD_CHAT_CLEAR_CAUSE_CHAT);
				String channelKey = requestDataModel.getChannelKey();
				JSONObject channelConf = entContext.getChannelConf();
				visitor.removeKey();
				visitor.setAgentId(null);
				visitor.setSkillGroupId(null);
				visitor.setLastChatTime(System.currentTimeMillis());
				visitor.setBizType(MediaConstants.VISITOR_STATE_END);
				requestDataModel.setEvent("end");

				String clearCause = _dataObject.getString("clearCause");

				// 1 用户主动结束服务  2  坐席结束服务  3  超时结束
				if("1".equals(clearCause) || "2".equals(clearCause) || "3".equals(clearCause)){
					// 渠道是否开启满意度 0 否,1 是
					String openSatisfy = channelConf.getString("OPEN_SATISFY");
					if(isSendSatisfy(openSatisfy, visitor)) {
						if("3".equals(clearCause)) visitor.setIstimeoutCloseChat(true);

						boolean checkDoSatisfy = entContext.checkDoSatisfy(visitor);
						logger.info("<CenterServerMessage> checkDoSatisfy："+checkDoSatisfy);
						if(!checkDoSatisfy) return;
						//调用满意度调查，把满意读调查返回的结果直接返回到用户
						requestDataModel.setData(_dataObject);
						try {
							String satisfyString = MediaSatisfyService.getInstance().getSatisfyContent(channelKey,visitor.getSessionId());
							_dataObject.put("msgContent", satisfyString);
							requestDataModel.setEvent("satisfy");
							String satisfyType = channelConf.getString("SATISFY_TYPE");//满意推送方式： 为空-文本，satisfy1-文本，  satisfy2-NPS，   satisfy3-表情(笑脸)
							if(StringUtils.isBlank(satisfyType)||"satisfy1".equals(satisfyType)) {
								visitor.setBizType(MediaConstants.VISITOR_STATE_SATISFY);//会话进入满意度流程
//							}else{
//								VisitorInfos.getInstance().removeLocalVisitorModel(visitor.getSessionId());
							}
							requestDataModel.setData(_dataObject);
						} catch (Exception e) {
							logger.error(e.getMessage(),e);
						}
					}

				}else if("4".equals(clearCause)){//用户取消排队

				}else if("5".equals(clearCause)){//排队超时
					JSONObject accesssData = new JSONObject();
					accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
					accesssData.put("END_TIME", EasyCalendar.newInstance().getDateTime("-"));

					//渠道配置开启了排队超时进入留言
					if(entContext.isQueueTimeoutWord()) {
						requestDataModel.setEvent("word");
						visitor.setBizType(MediaConstants.VISITOR_STATE_WORD);
						accesssData.put("IS_IN_WORD", 1);
						accesssData.put("CLEAR_CAUSE", 7);//7.排队超时进留言
					}else{
						accesssData.put("CLEAR_CAUSE", 3);//3.排队超时结束
					}
					AccessRecordService.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
				}else if("6".equals(clearCause)){//转移结束

				}
				visitor.cache();
			}

			//替换回调消息中的文件服务器地址
			_dataObject.put("msgContent", entContext.replaceFileServerUrl(_dataObject.getString("msgContent"),"out"));

			//处理结束事件或者会话状态bizType=end，重置会话id，会话状态设置为bizType=welcome
			if(MediaConstants.VISITOR_STATE_END.equals(visitor.getBizType())||"end".equals(mediaEvent)){
				//清除按键信息
				visitor.setChatSessionId(RandomKit.randomStr());
				visitor.removeKey();
				visitor.setBizType(MediaConstants.VISITOR_STATE_WELCOME);
			}

			//设置发送到用户的模板消息
			MediaTemplate setTempMsg = TemplateMsgService.getInstance().setTempMsg(requestDataModel);

			//保存模板配置
			if(setTempMsg!=null&&setTempMsg.getTempType()!=0&&setTempMsg.getTempId()!=null) {
				logger.debug("<CenterServerMessage>模板消息："+JSONObject.toJSONString(setTempMsg));
				JSONObject tempConfig = new JSONObject();
				tempConfig.put("tempId", setTempMsg.getTempId());
				tempConfig.put("tempType", setTempMsg.getTempType());
				tempConfig.put("tempData", setTempMsg.getTempData());
				_dataObject.put("tempConfig", tempConfig.toJSONString());
				if(StringUtils.isNotBlank(setTempMsg.getTempContent())) _dataObject.put("msgContent", setTempMsg.getTempContent());
				ChatMessageService.getInstance().saveMessage(visitor.getChannelKey(), _dataObject, MediaConstants.MSG_SENDER_SYS);//系统消息
				//缓存坐席发送的模板消息,用户回复或操作了模板消息,就清理掉
//				if(tempType.intValue()==2) CacheUtil.put(MediaConstants.AGENT_SATSFY_TEMP_MSG+visitor.getChatSessionId(), tempType+"##"+setTempMsg.getTempId(),100);
				sysReplay = false;
			}

			//msgContent为空时不发送到客户端（mediaEvent=start或close时）
			if(StringUtils.isBlank(requestDataModel.getData().getString("msgContent"))){
				return;
			}

			//保存系统回复语
			if(sysReplay){
				ChatMessageService.getInstance().saveMessage(visitor.getChannelKey(), _dataObject, MediaConstants.MSG_SENDER_SYS);//系统消息
			}

			msgObj.put("serialId", requestDataModel.getSerialId());
			msgObj.put("channelKey", requestDataModel.getChannelKey());
			msgObj.put("entId", requestDataModel.getEntId());
			msgObj.put("timestamp", requestDataModel.getTimestamp());
			msgObj.put("sign", requestDataModel.genraSign());
			msgObj.put("data", requestDataModel.getData());
			msgObj.put("event", requestDataModel.getEvent());
			msgObj.put("callbackUrl", callbackUrl);
			
			LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"<< 回复消息给客户: "+msgObj.getJSONObject("data").getString("msgContent"), msgObj.toJSONString(), null);
			
//			logger.debug("<CenterServerMessage> mediacenter发送消息到客户端 >> params:"+msgObj);
			this.sendMsgTOClient(msgObj.toJSONString());

			//进入留言流程，需要推送进入留言提示语
			if(MediaConstants.VISITOR_STATE_WORD.equals(visitor.getBizType())) {
				EntContext context = EntContext.getContext(requestDataModel.getChannelKey());
				String wordMsg = CommonUtil.replaceParam(visitor,context.getIntoWordMsg());
				if(StringUtils.isNotBlank(wordMsg)) {
					JSONObject _msgObj = new JSONObject();
					_msgObj.putAll(msgObj);
					String newSerialId = RandomKit.randomStr();
					JSONObject wordDataObj = new JSONObject();
					wordDataObj.putAll(_msgObj.getJSONObject("data"));
					wordDataObj.put("msgContent", wordMsg);
					wordDataObj.put("serialId",newSerialId);//3.0#20210425-1 解决排队超时时推送两条系统回复语的serialId 是一样的问题。
					_msgObj.put("data", wordDataObj);
					_msgObj.put("serialId", newSerialId);
					
					LogUtil.addTraceLog(visitor.getChannelKey(), sessionId,visitor.getChatSessionId(),null,"进入留言，推送留言提示语给客户: "+wordMsg, _msgObj.toJSONString(), null);
					
					this.sendMsgTOClient(_msgObj.toJSONString());
					ChatMessageService.getInstance().saveMessage(visitor.getChannelKey(), wordDataObj, MediaConstants.MSG_SENDER_SYS);//系统消息
				}
			}

		} catch (Exception ex) {
			logger.error(ex.getMessage(), ex);
		}

	}
	public void replaceMsgContent(JSONObject dataObject,VisitorModel visitor) {
		String msgContent = dataObject.getString("msgContent");
		String nickname = visitor.getUserInfo().getString("nickname");
		if(StringUtils.isNotBlank(msgContent)&&StringUtils.isNotBlank(nickname)) {
			msgContent = msgContent.replace("#nickname#", nickname);
		}
		dataObject.put("msgContent", msgContent);
	}
	
	/**
	 * 判断是否发送满意度
	 * 中邮随机满意度逻辑
	 * @param openSatisfy
	 * @param visitor
	 * @return
	 */
	private boolean isSendSatisfy(String openSatisfy,VisitorModel visitor) {
		
		logger.warn("是否开启满意度： "+openSatisfy);
		if("0".equals(openSatisfy)) {//为了兼容旧版本，所以为空的默认开启满意度
			return false;
		}
		
		//调用服务判断是否发送满意度，中邮消费金融在线客服项目
		try {
			IService service = ServiceContext.getService("RANDOM_STATIS_SERVICE");
			if(service == null){
				logger.warn("<RANDOM_STATIS_SERVICE>  service is not found! ");
				return true;
			}
//		入参：{"command":"statisfy","serialId":"111"}(serialId为请求Id，随机生成即可）
//				返回：{"respDesc":"处理成功","respCode":"000","statisFlag":true,"serialId":"111"}(respCode为999请求失败，具体看respDesc描述错误原因，statisFlag为是否能发送满意度）
			JSONObject params = new JSONObject();
			params.put("command", "statisfy");
			params.put("serialId", RandomKit.randomStr());
			params.put("chatSessionId", visitor.getChatSessionId());
			logger.debug("<RANDOM_STATIS_SERVICE>  >> "+params);
			JSONObject result = service.invoke(params);
			logger.debug("<RANDOM_STATIS_SERVICE>  << "+result);
			if(result == null){
				return true;
			}
			
			String respCode = (String) JSONPath.eval(result, "$.respCode");
			String respDesc = (String) JSONPath.eval(result, "$.respDesc");
			if(!"000".equals(respCode)){
				logger.warn("<RANDOM_STATIS_SERVICE>  service exception,cause：respCode："+respCode+"，respDesc："+respDesc);
				return true;
			}
			
			boolean statisFlag = (boolean) JSONPath.eval(result, "$.statisFlag");
            return statisFlag;
        } catch (Exception e) {
			logger.error("RANDOM_STATIS_SERVICE service exception,cause:"+e.getMessage());
		}
		
		return true;
	}
}
