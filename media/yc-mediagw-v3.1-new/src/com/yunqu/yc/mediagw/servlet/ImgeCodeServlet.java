package com.yunqu.yc.mediagw.servlet;

import java.io.IOException;
import java.io.OutputStream;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.easitline.common.utils.kit.VerifyCodeKit;
import org.easitline.common.utils.string.StringUtils;

@WebServlet("/captcha/*")
public class ImgeCodeServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		this.doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String params = req.getQueryString();
		if(StringUtils.notBlank(params)) {
			if(params.indexOf("<")>-1||params.indexOf(">")>-1||params.indexOf("%")>-1) {
				try {
					resp.sendError(404);
				} catch (IOException e) {
					e.printStackTrace();
					resp.sendError(404);
				}
				return;
			}
		}
		resp.setContentType("image/jpeg");
		try {
			OutputStream outstream = resp.getOutputStream();
			VerifyCodeKit vCode = new VerifyCodeKit(120,46,4,6);
			HttpSession session=req.getSession(true);
			session.setMaxInactiveInterval(300);//五分钟
			session.setAttribute("innerCode",vCode.getRandomString());
			vCode.write(resp.getOutputStream());
			outstream.flush();
			outstream.close();
		} catch (IOException ex) {
			ex.printStackTrace();
			resp.sendError(404);
		}
	}

	
	
}
