package com.yunqu.cc.media.utils;

import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.cc.media.base.Constants;

/** 
* 渠道工具类
*/
public class ChannelUtil {

	
	//更新渠道缓存
	public static void channelReloadCache(String channelId,String channelKey,String epCode,String busiOrderId) {
		CacheUtil.put("RELOAD_CHANNEL_" + channelId, "1",24*60*60);
		CacheUtil.put("RELOAD_CHANNEL_GW" + channelId, "1",24*60*60);
		CacheUtil.getCcChannelCache().reloadCache(epCode, busiOrderId, channelKey);	
	}
	
	//写入操作日志
	public static void insertChannelOpLog(UserModel user,String opType,String content) {
		Yqlogger logger = new Yqlogger();
		logger.setCreateAcc(user.getUserAcc());
		logger.setCreateName(user.getUserName());
		logger.setCreateNo(user.getUserNo());
		logger.setModule(Constants.APP_NAME);
		logger.setOperType(opType);
		logger.setContent(user.getUserName() + content);
		logger.setBusiOrderId(user.getBusiOrderId());
		logger.setEntId(user.getEpCode());
		logger.setBakup("");
		LogUtil.insertLog(user.getSchemaName(), logger);
	}
	
}
