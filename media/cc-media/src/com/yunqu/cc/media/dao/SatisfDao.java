package com.yunqu.cc.media.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.yunqu.cc.media.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.media.base.AppDaoContext;
import com.yunqu.cc.media.base.CommonLogger;
import com.yunqu.cc.media.base.Constants;

@WebObject(name = "satisf")
public class SatisfDao extends AppDaoContext {

	private Logger logger = CommonLogger.logger;
	
	/**
	 * 满意度指标列表
	 */
	@InfAuthCheck(resId = {"cc-media-mgr-cfg-satisf"},msg = "您无权访问!")
	@WebControl(name = "list",type = Types.LIST)
	public JSONObject satisfList() {
		EasySQL sql = new EasySQL("SELECT * FROM CC_MEDIA_SATISF_ITEM WHERE 1=1");
		//条件
		sql.appendLike(param.getString("NAME"),"AND NAME LIKE ?");
		sql.append(getEntId(),"AND ENT_ID = ?",false);
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?",false);
		sql.append("ORDER BY SORT_NUM");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 满意度指标细项列表
	 */
	@InfAuthCheck(resId = {"cc-media-mgr-cfg-satisf"},msg = "您无权访问!")
	@WebControl(name = "detailList",type = Types.LIST)
	public JSONObject satisfDetailList() {
		EasySQL sql = new EasySQL("SELECT * FROM CC_MEDIA_SATISF_ITEM2 WHERE 1=1");
		//条件
		sql.appendLike(param.getString("NAME"),"AND NAME LIKE ?");
		sql.append(getEntId(),"AND ENT_ID = ?",false);
//		sql.appendLike(param.getString("ITEM2_TYPE"),"AND ITEM2_TYPE = ?")
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?",false);
		sql.append(param.getString("satisfyType"),"AND ITEM2_TYPE = ?");
		sql.append("ORDER BY SORT_NUM");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 满意度指标配置项回显
	 */
	@InfAuthCheck(resId = {"cc-media-mgr-cfg-satisf"},msg = "您无权访问!")
	@WebControl(name = "info", type = Types.RECORD)
	public JSONObject satisfInfo() {
		String id = param.getString("id");
		if (StringUtils.isBlank(id)) {
			return EasyResult.error(500,BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, "参数传递错误"));
		}
		EasyRecord record = new EasyRecord("CC_MEDIA_SATISF_ITEM", "ID").setPrimaryValues(id);
		return this.queryForRecord(record);
	}
	
	/**
	 * 满意度指标配置项回显
	 */
	@InfAuthCheck(resId = {"cc-media-mgr-cfg-satisf"},msg = "您无权访问!")
	@WebControl(name = "detailInfo", type = Types.RECORD)
	public JSONObject detailInfo() {
		String id = param.getString("id");
		if (StringUtils.isBlank(id)) {
			return EasyResult.error(500,BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, "参数传递错误"));
		}
		EasyRecord record = new EasyRecord("CC_MEDIA_SATISF_ITEM2", "ID").setPrimaryValues(id);
		return this.queryForRecord(record);

	}
	
	/**
	 * 渠道满意度指标
	 */
	@InfAuthCheck(resId = {"cc-media-mgr-cfg"},msg = "您无权访问!")
	@WebControl(name = "channelSatisfInfo",type = Types.RECORD)
	public JSONObject channelSatisfInfo() {
		String channelNo = param.getString("channelNo");
		JSONObject result = new JSONObject();
		try {
			//查询满意度配置信息
			//满意度
			EasySQL sql = new EasySQL("SELECT NAME,CODE,ITEM_TYPE FROM CC_MEDIA_SATISF_ITEM WHERE 1=1");
			sql.append("01","AND ENABLE_STATUS = ?");// 启用
			sql.append(getEntId(), "AND ENT_ID = ?", false);
			sql.append(getBusiOrderId(), "AND BUSI_ORDER_ID = ?", false);
			sql.append("ORDER BY SORT_NUM");
			//细项
			EasySQL sql2 = new EasySQL("SELECT NAME,CODE,SORT_NUM,ITEM2_TYPE FROM CC_MEDIA_SATISF_ITEM2 WHERE 1=1");
			sql2.append("01","AND ENABLE_STATUS = ?");// 启用
			sql2.append("1","AND ITEM2_TYPE = ?");
			sql2.append(getEntId(), "AND ENT_ID = ?", false);
			sql2.append(getBusiOrderId(), "AND BUSI_ORDER_ID = ?", false);
			sql2.append("ORDER BY SORT_NUM");
			//指标评价标签
			EasySQL sql3 = new EasySQL("SELECT NAME,CODE,SORT_NUM FROM CC_MEDIA_SATISF_ITEM2 WHERE 1=1");
			sql3.append("01","AND ENABLE_STATUS = ?");// 启用
			sql3.append("2","AND ITEM2_TYPE = ?");
			sql3.append(getEntId(), "AND ENT_ID = ?", false);
			sql3.append(getBusiOrderId(), "AND BUSI_ORDER_ID = ?", false);
			sql3.append("ORDER BY SORT_NUM");
			List<JSONObject> satisfy = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			List<JSONObject> satisfyDetail = this.getQuery().queryForList(sql2.getSQL(), sql2.getParams(),new JSONMapperImpl());
            List<JSONObject> satisfyDetail2 = this.getQuery().queryForList(sql3.getSQL(), sql3.getParams(),new JSONMapperImpl());
            result.put("orgSatisfyList", satisfy);
            result.put("orgSatisfyDetail",satisfyDetail);
            result.put("orgSatisfyDetail2",satisfyDetail2);


			JSONArray satisfyList = getSatisfyList(channelNo,Constants.SATISFY_FLAG_1,"startWord","endWord",result);
			JSONArray robotSatisfyList = getSatisfyList(channelNo,Constants.SATISFY_FLAG_2,"robotStartWord","robotEndWord",result);
			JSONArray thirdSatisfyList = getSatisfyList(channelNo,Constants.SATISFY_FLAG_3,"thirdStartWord","thirdEndWord",result);
			JSONArray videoSatisfyList = getSatisfyList(channelNo,Constants.SATISFY_FLAG_4,"videoStartWord","videoEndWord",result);
			
			result.put("satisfy",satisfyList);
			result.put("robotSatisfy",robotSatisfyList);
			result.put("thirdSatisfy",thirdSatisfyList);
			result.put("videoSatisfy",videoSatisfyList);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "错误：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(result);
	}
	
	private JSONArray getSatisfyList(String channelNo,Integer satisfyFlag,String startWordKey,String endWordKey,JSONObject result) {
		JSONArray array = new JSONArray();
		try {
			//查询渠道满意度信息
			List<JSONObject> list = this.getQuery().queryForList("SELECT TYPE,NAME,SATISF_TYPE AS ITEM_TYPE,CODE FROM CC_MEDIA_SATISF_QUOTA WHERE CHANNEL_NO=? AND SATISFY_FLAG = ?", new Object[] {channelNo,satisfyFlag},new JSONMapperImpl());
			List<JSONObject> detailList = this.getQuery().queryForList("SELECT CODE,SATISF_QUOTA_CODE,ITEM2_TYPE FROM CC_MEDIA_SATISF_QUOTA_2 WHERE CHANNEL_NO = ? AND SATISFY_FLAG = ?", new Object[] {channelNo,satisfyFlag},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject obj:list) {
					if("01".equals(obj.getString("TYPE"))) {//开头语
						result.put(startWordKey,obj.getString("NAME"));
					}else if("02".equals(obj.getString("TYPE"))) {//结束语
						result.put(endWordKey,obj.getString("NAME"));
					}else if("03".equals(obj.getString("TYPE"))) {//指标项
						JSONArray detail = new JSONArray();
						JSONArray detail2 = new JSONArray();
						if(detailList!=null) {
							for(JSONObject data:detailList) {
								JSONObject d = JSONObject.parseObject(JSONObject.toJSONString(data));
								if(d.getString("SATISF_QUOTA_CODE").equals(obj.getString("CODE"))) {
									if(StringUtils.equals("1",d.getString("ITEM2_TYPE"))){
										detail.add(d);
									}else {
										detail2.add(d);
									}
								}
							}
						}
						obj.put("detail", detail);
						obj.put("detail2", detail2);
						array.add(obj);
					}
				}
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "错误：" + e.getMessage(),e);
		}
		return array;
	}
}

