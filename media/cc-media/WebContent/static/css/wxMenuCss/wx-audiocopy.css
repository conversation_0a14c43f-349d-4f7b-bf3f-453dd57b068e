.wx-audio-content .wx-audio-right .wx-audio-disc {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding-bottom: 10px;
    font-size: 12px;
    color: #8c8c8c
}
.wx-audio-content .wx-audio-right .wx-audio-title {
    padding-bottom: 6px;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}
.wx-audio-content .wx-audio-left .wx-audio-state {
    width: 100%;
    height: 100%
}
.wx-audio-content .wx-audio-left {
    display: -moz-inline-stack;
    display: inline-block;
    vertical-align: middle;
    *vertical-align: auto;
    zoom: 1;
    *display: inline;
    width: 42px;
    height: 100%
}
.wx-audio-content .wx-audio-right {
    display: -moz-inline-stack;
    display: inline-block;
    vertical-align: middle;
    *vertical-align: auto;
    zoom: 1;
    *display: inline;
    width: calc(100% - 42px);
    font-size: 16px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 10px
}
.wx-audio-content .wx-audio-right .wx-audio-progrees {
    height: 2px;
    width: calc(100% - 4px);
    position: relative
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-progrees-detail {
    height: 100%;
    width: 100%;
    background: #ebebeb;
    position: relative;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-progrees-detail .wx-voice-p {
    width: 0%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    background: #09bb07;
    z-index: 2
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-progrees-detail .wx-buffer-p {
    width: 0%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    background: #d9d9d9;
    z-index: 1
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-progrees-detail .wx-loading {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-progrees-detail .wx-loading .wx-loading-wrapper {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    animation: slidein 6s linear infinite normal;
    -webkit-animation: slidein 6s linear infinite normal;
    -moz-animation: slidein 6s linear infinite normal;
    width: 200%;
    max-width: none!important;
    background-image: -webkit-repeating-linear-gradient(-15deg,#d9d9d9,#d9d9d9 2px,#ebebeb 2px,#ebebeb 4px);
    background-image: -moz-repeating-linear-gradient(-15deg,#d9d9d9,#d9d9d9 2px,#ebebeb 2px,#ebebeb 4px);
    background-image: -o-repeating-linear-gradient(-15deg,#d9d9d9,#d9d9d9 2px,#ebebeb 2px,#ebebeb 4px);
    background-image: repeating-linear-gradient(-15deg,#d9d9d9,#d9d9d9 2px,#ebebeb 2px,#ebebeb 4px)
}







@keyframes slidein {
    0% {
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
        transform: translateX(-50%)
    }
    100% {
         -webkit-transform: translateX(0);
         -moz-transform: translateX(0);
         -ms-transform: translateX(0);
         -o-transform: translateX(0);
         transform: translateX(0);
         -webkit-transform: translateX(0);
         -moz-transform: translateX(0);
         -ms-transform: translateX(0);
         -o-transform: translateX(0);
         transform: translateX(0)
     }
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-audio-origin {
    width: 6px;
    height: 6px;
    margin-top: -3px;
    margin-left: 0;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    background-color: #09bb07;
    position: absolute;
    left: 0;
    top: 50%;
    z-index: 2
}
.wx-audio-content .wx-audio-right .wx-audio-progrees .wx-audio-origin:before {
    content: " ";
    display: block;
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    background: -webkit-radial-gradient(rgba(9,187,7,0.3) 20%,transparent 40%);
    background: -moz-radial-gradient(rgba(9,187,7,0.3) 20%,transparent 40%);
    background: -o-radial-gradient(rgba(9,187,7,0.3) 20%,transparent 40%);
    background: radial-gradient(rgba(9,187,7,0.3) 20%,transparent 40%);
    top: 50%;
    margin-top: -12px;
    margin-left: -9px;
    cursor: pointer;
    outline: 0;
    -webkit-tap-highlight-color: transparent
}

.wx-audio-content .wx-audio-right .wx-audio-time {
    width: 100%;
    padding-top: 6px;
    height: auto;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    overflow: hidden
}

.wx-audio-content .wx-audio-right .wx-audio-time span {
    font-size: 12px;
    color: #8c8c8c
}
.wx-audio-content {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    height: auto;
    padding: 8px;
    border: 1px solid rgba(33,44,55,.3);
    font-size: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    background: #fff
}
.wx-audio-content p {
    margin: 0
}
.wx-audio-content .wx-audio-info {
    display: none
}