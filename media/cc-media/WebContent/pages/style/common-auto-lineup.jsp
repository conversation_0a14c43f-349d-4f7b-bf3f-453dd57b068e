<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="自动回复语配置"></title>
	<link href="${ctxPath}/static/css/channelStyle.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
	<div class="blank-page">
		<form id="autoConfigForm" class="form-horizontal"
			data-mars="channelStyle.channelAutoInfo"
			data-mars-prefix="autoReplyConfig.">
			<input type="hidden" name="channelStyleId" value="${param.channelStyleId}">
			<input type="hidden" name="partName" value="lineUp">
			<div class="layui-form-item ">
				<label class="layui-form-label required"
					i18n-content="访客排队超时，自动进入留言"></label>
				<div class="layui-input-block">
					<label class="checkbox checkbox-success checkbox-inline"> <input
						type="checkbox" name="autoReplyConfig.TIMEOUT_INTO_WORD_FLAG" onclick="AutoConfig.handleCheckBox($(this))"><span>
					</span>
					</label>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label layui-form-required "
					i18n-content="访客排队号通知间隔时间"></label>
				<div class="layui-input-block">
					<input style="display: inline-block; width: 60px" type="text"
						name="autoReplyConfig.QUEUE_NOTIFY_TIME" value="5"
						i18n-placeholder="分钟" class="form-control input-sm"
						onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)"
						onblur="this.v();"> <span i18n-content="分钟"></span>
				</div>

			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required"
					i18n-content="访客接入排队时，系统自动回复内容"></label>
				<div class="layui-input-block">
					<textarea style="display: none" rows="3" data-rules="required"
						id="visitorQueueMsg" class="form-control input-sm"
						name="autoReplyConfig.VISITOR_QUEUE_MSG"
						i18n-placeholder="请填写内容 ..."
						i18n-content="您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。"></textarea>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required "
					i18n-content="访客正在排队，输入除88以外的其他内容，系统自动回复内容"></label>
				<div class="layui-input-block">
					<textarea style="display: none" rows="3" id="visitorQueueInMsg"
						class="form-control input-sm"
						name="autoReplyConfig.VISITOR_QUEUE_IN_MSG"
						data-rules="required"
						i18n-placeholder="请填写内容 ..."
						i18n-content="您当前正在排队，请耐心等待，输入88可取消当前排队。"></textarea>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required"
					i18n-content="访客取消排队时，系统自动回复内容"></label>
				<div class="layui-input-block">
					<textarea style="display: none" rows="3" data-rules="required"
						id="visitorCancelqueMsg" class="form-control input-sm"
						name="autoReplyConfig.VISITOR_CANCELQUE_MSG"
						i18n-placeholder="请填写内容 ..."
						i18n-content="您好，由于当前系统排队人数过多，给您带来的不便，敬请原谅，请稍后再试。"></textarea>
				</div>
			</div>
			<!-- <div class="layui-form-item " >
	                              <label class="layui-form-label layui-form-required " i18n-content="访客排队通知"></label>
	                              		 <div class="layui-input-block">
												<span i18n-content="当访客排队号在"></span>
								               	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_MIN_NO" value="5" data-rules="required|digits" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
								               	<span i18n-content="到"></span>
								               	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_MAX_NO" value="10" data-rules="required|digits" i18n-placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
								               	<span i18n-content="之间时，系统自动回复"></span>
							               	    <textarea  style="display: none" rows="3" class="form-control input-sm" name="autoReplyConfig.QUEUE_CONFIRM_MSG" i18n-placeholder=" ..." i18n-content="请问您是否还需要客服MM为您服务，如果需要请输入并发送“是”"></textarea>
							               	 	<span i18n-content="访客在"></span>
							               	 	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_TIMEOUT" value="1" data-rules="required|digits" i18n-placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
							               		<span i18n-content="分钟(系统时延15秒内)内未回复“是”，则结束排队"></span>	
							               </div>
		                     </div> -->
			<div class="layui-form-item ">
				<label class="layui-form-label layui-form-required "><span
					i18n-content="排队人数上限"></span><i i18n-title="为“0”时不限制"
					style="cursor: pointer;" class="glyphicon glyphicon-question-sign"
					data-toggle="tooltip"></i></label>
				<div class="layui-input-block">
					<input style="display: inline-block; width: 60px" type="text"
						name="autoReplyConfig.QUEUE_MAX_COUNT" value="200"
						class="form-control input-sm"
						onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)"
						onblur="this.v();"> /<span i18n-content="人"></span>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required "
					i18n-content="排队人数到达上限时，系统自动回复内容"></label>
				<div class="layui-input-block">
					<textarea style="display: none" data-rules="required" rows="3"
						id="queueFullMsg" class="form-control input-sm"
						name="autoReplyConfig.QUEUE_FULL_MSG" i18n-placeholder="请填写内容 ..."
						i18n-content="您好，当前排队人数已达系统上限，请稍后再试。"></textarea>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required" i18n-content="访客排队超时时间"></label>
				<div class="layui-input-block">
					<input style="display: inline-block; width: 60px" type="text"
						name="autoReplyConfig.VISITOR_QUEUE_TIMEOUT" value="5"
						data-rules="required" i18n-placeholder="分钟"
						class="form-control input-sm"
						onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)"
						onblur="this.v();"> /<span i18n-content="分钟"></span>&nbsp;&nbsp;<span
						i18n-content="当访客排队超过此时间(系统时延15秒内)，系统自动发送超时提示语。"></span>
				</div>
			</div>
			<div class="layui-form-item ">
				<label class="layui-form-label required" i18n-content="访客排队超时提示语"></label>
				<div class="layui-input-block">
					<textarea style="display: none" rows="3" data-rules="required"
						id="queueTimeoutMsg" class="form-control input-sm"
						name="autoReplyConfig.QUEUE_TIMEOUT_MSG"
						i18n-placeholder="请填写内容 ..." i18n-content="您好，当前排队已超时，请稍后再试。"> </textarea>
				</div>
			</div>
			<div class="layer-foot text-c">
				<button class="btn btn-sm btn-primary" id="saveChannelConfig"
					type="button" onclick="AutoConfig.update()" i18n-content="保存"></button>
				<button class="btn btn-sm btn-default ml-20" type="button"
					onclick="AutoConfig.upChannel()" i18n-content="应用"></button>
			</div>
		</form>
	</div>

</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript"
		src="${ctxPath}/static/lib/ckeditor/ckeditor.js"></script>
	<script type="text/javascript"
		src="${ctxPath}/static/js/channelStyle.js"></script>
	<script type="text/javascript">
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>