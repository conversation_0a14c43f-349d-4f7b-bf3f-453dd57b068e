<%@page import="com.yq.busi.common.util.IDGenerator"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="日志详情"></title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 1}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
		.gray-bg {
    		background-color: white;
		}
	</style>
	</EasyTag:override>
<EasyTag:override name="content">
			<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
  				<legend><span i18n-content="日志详情"></span></legend>
			</fieldset> 

			<form id="easyformUpdate" data-mars="errorLogDao.findMediaTraceList" id="easyform" autocomplete="off" method="post" autocomplete="off" >
				
				<input type="hidden" name="ID" value="${param.ID }">
				  <table class="table table-edit table-vzebra">
	              	<tbody>              	
	              	   			<tr>
			                        <td  i18n-content="渠道Key">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="CHANNEL_KEY_s" readonly  class="form-control input-sm readonly-class">
			                        </td>
			                        
			                          <td  i18n-content="渠道名称">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="CHANNEL_KEY_s" class="form-control input-sm readonly-class">
			                        </td>
			                       
			                        
		                     </tr>	
		                     <tr>
			                        <td  i18n-content="sessionId">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="SESSION_ID_s" readonly  class="form-control input-sm readonly-class">
			                        </td>
			                        
			                          <td  i18n-content="chatSessionId">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="CHAT_SESSION_ID_s" class="form-control input-sm readonly-class">
			                        </td>
			                       
			                        
		                     </tr>	
		                     
		                     
		                        <tr>
			                        <td  i18n-content="模块">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="MODULE" readonly  class="form-control input-sm readonly-class">
			                        </td>
			                        
			                          <td  i18n-content="业务类型">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="BIZ_TYPE_s" class="form-control input-sm readonly-class">
			                        </td>
			                       
			                        
		                     </tr>	
		                     
		                       <tr>
		                         <td  i18n-content="节点">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="MARS_NODE_NAME" class="form-control input-sm readonly-class">
			                        </td>
		                       
			                        <td  i18n-content="操作类型">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="OPER_TYPE_DESC" readonly  class="form-control input-sm readonly-class">
			                        </td>      
		                     </tr>	
		                     
		                     <tr>
		                       <td  i18n-content="操作时间">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="CREATE_TIME" class="form-control input-sm readonly-class">
			                        </td>
			                        <td  i18n-content="时间戳">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="TIMESTAMP" readonly  class="form-control input-sm readonly-class">
			                        </td>             
		                     </tr>
		                     	 <tr>
			                         <td i18n-content="内容"></td>
			                         <td colspan="8">
			                         	<textarea cols="60"  name="CONTENT"  class="form-control input-sm" rows="3"></textarea>
			                         </td>
			                      
		                     </tr>	
	              		
		                        <tr>
			                         <td i18n-content="随路数据"></td>
			                         <td colspan="8">
			                         	<textarea cols="60"  name="USER_DATA_ik"  class="form-control input-sm" rows="3"></textarea>
			                         </td>
			                      
		                     </tr>	
	              		
		                     
		                       <tr>
			                         <td i18n-content="调用栈"></td>
			                         <td colspan="8">
			                         	<textarea cols="60"  name="STACK_TRACE"  class="form-control input-sm" rows="4"></textarea>
			                         </td>
			                      
		                     </tr>
		                        <tr>
			                         <td i18n-content="异常栈"></td>
			                         <td colspan="8">
			                         	<textarea cols="60"  name="ERROR_EXCEPTION_s"  class="form-control input-sm" rows="4"></textarea>
			                         </td>			                      
		                     </tr>
		                        <tr>
			                        <td  i18n-content="创建人账号">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  name="CREATE_ACC" readonly  class="form-control input-sm readonly-class">
			                        </td>
			                        
			                          <td  i18n-content="创建人名称">
			                        </td>
			                        <td colspan="3">
			                        	<input type="text"  readonly name="CREATE_NAME" class="form-control input-sm readonly-class">
			                        </td>      
		                     </tr>	
		                  
		                </tbody>
		                  
		          </table>

				
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
<script>
$(function(){
	$("#easyformUpdate").render();
});	
</script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>