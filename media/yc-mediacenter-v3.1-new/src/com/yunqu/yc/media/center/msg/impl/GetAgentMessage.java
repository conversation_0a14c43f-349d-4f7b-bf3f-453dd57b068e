package com.yunqu.yc.media.center.msg.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.event.impl.UserEventDispatcher;
import com.yunqu.yc.media.center.event.impl.SQLExecutor;
import com.yunqu.yc.media.center.model.*;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;
import com.yunqu.yc.media.center.strategy.QueueStrategy;
import com.yunqu.yc.media.center.util.CacheUtil;
import com.yunqu.yc.media.center.util.CommonUtil;
import com.yunqu.yc.media.center.util.LogUtil;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.util.Calendar;

/**
 * 获取坐席，无坐席就排队
 * 队列排队：通过按键关联的队列，一个队列中关联多个技能组
 * 技能组排队：通过按键直接查找按键关联的技能组。
 * 排队方式：2队列，
 * 用户进入队列中排队，按队列中的技能组优先级由高到低依次查找“空闲坐席”，按照渠道中的“路由策略”找到坐席接入会话。
 * 空闲坐席：置闲状态，当前服务人数未达服务上限；
 * <p>
 * 路由策略：
 * 按坐席空闲时间，置闲状态保持时长越短优先接入。
 * 按服务人数，当前服务人数越少优先接入。
 * 按服务时长，坐席的服务时长最少，优先接入客户。
 *
 * <AUTHOR>
 */
public class GetAgentMessage extends Message {
    @Override
    public void onMessage(MessageModel messageModel) {
        long tid = Thread.currentThread().getId();
        boolean debug = ServerContext.isDebug();
        String sessionId = messageModel.getSessionId();
        messageModel.setMsgType("text");
        EntContext entContext = EntContext.getContext(messageModel.getEntId());

        Channel channel = entContext.getChannel(messageModel.getChannelId());
        String channelKeyStr = channel.getChannelKey();
        ChannelKey channelKey = channel.getChannelKeyByCode(messageModel.getMsgContent());
        String event = messageModel.getEvent();
        try {

            //如果输入的渠道信息错误,则推送导航菜单
            //2.0#20191126-1
            if (!"queueConfirm".equals(event) && channelKey == null) {
                logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 输入的渠道信息错误！");
                messageModel.setEvent("welcome");
                messageModel.setMsgContent(CommonUtil.replaceParam(messageModel, channel.getAutoConfig().getWelcomeMsg()));
                ProducerBroker.sendUserMessage(sessionId, messageModel.toString(), false);
                return;
            }

            //黑名单用户
            if (entContext.checkBlackList(sessionId)) {
                logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 黑名单用户！");
                messageModel.setEvent("end");
                messageModel.setMsgContent(CommonUtil.replaceParam(messageModel, channel.getAutoConfig().getBlacklistMsg()));
                ProducerBroker.sendUserMessage(sessionId, messageModel.toString(), false);
                return;
            }

            int maxQueue = channel.getAutoConfig().getQueueMaxCount();
            int channelCount = channel.getQueueCount();

            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 当前渠道[" + channel.getChannelName() + "]排队人数：" + channelCount + "，渠道配置排队上限：" + maxQueue);

            if (maxQueue > 0 && channelCount >= maxQueue) {
                String msg = channel.getAutoConfig().getQueueFullMsg();
                msg = CommonUtil.replaceParam(messageModel, msg); //3.1#20211130-1 替换回复语里的参数

                messageModel.setMsgContent(msg);
                messageModel.setEvent("end");
                logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 当前渠道[" + channel.getChannelName() + "]排队人数超过最大上限，maxQueue:" + maxQueue);
                ProducerBroker.sendUserMessage(sessionId, messageModel.toString(), false);
                return;
            }

            //用户已接入，不能再次接入或进入排队
            if (entContext.getUser(sessionId) != null) {
                logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 用户已接入，不能再次接入或进入排队，userSession:" + entContext.getUser(sessionId));
                //TODO 是否需要通知yc-mediagw更新用户会话状态为zxkf
                return;
            }

            if (entContext.getQueueUser(sessionId) != null || entContext.getQueueStrategy().isQueueUser(sessionId)) {
                logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 已经在排队，不能重新进入！");
                return;
            }


            UserSession userSession = UserSession.getInstance(sessionId);

            if (userSession.isInChat()) {
                logger.error("GetAgentMessage(" + sessionId + ") -> 用户已接入，不能进入排队，当前用户状态：" + userSession.getState() + "，userSession:" + userSession);
                return;
            }
            //3.1#20210622-1 用户接入是获取红名单
            JSONObject userInfo = messageModel.getUserInfo();
            JSONObject redList = entContext.getRedList(sessionId);
            if (redList.containsKey("levelCode")) userInfo.put("levelCode", redList.getString("levelCode"));
            if (redList.containsKey("levelName")) userInfo.put("levelName", redList.getString("levelName"));

            userSession.setChatSessionId(messageModel.getChatSessionId());
//			userSession.setSerialId(messageModel.getChatSessionId());
            userSession.setMessageModel(messageModel);
            userSession.setBeginTime(EasyCalendar.newInstance().getDateTime("-"));
            userSession.setEntId(messageModel.getEntId());
            userSession.setSessionId(messageModel.getSessionId());
            userSession.setRequestTime(messageModel.getRequestTime());
            userSession.setChannel(channel);
            userSession.setChannelKey(channelKey);
            userSession.setUserInfo(userInfo);
            userSession.setChannelId(channel.getChannelId());
            userSession.setChannelKeyId(channelKey.getChannelKeyId());
            addChannelKeyQueue(entContext, channel, messageModel, userSession);

            //3.2#20220630-1 当开启了抢单模式且设置了抢单回复语时，推送回复语给客户
            String agentGrab = channel.getChannelConfig().getString("AGENT_GRAB");//抢单模式 1-开启
            String agentGrabMsg = CommonUtil.replaceParam(userSession, channel.getAutoConfig().getAgentGrabMsg());

            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 当前渠道[" + channel.getChannelName() + "]抢单模式(1-开启)：" + agentGrab + "，抢单回复语：" + agentGrabMsg);

            if ("1".equals(agentGrab) && StringUtils.isNotBlank(agentGrabMsg)) {
                messageModel.setMsgContent(agentGrabMsg);
                messageModel.setEvent("message");
                ProducerBroker.sendUserMessage(sessionId, messageModel.toString(), false);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
//			messageModel.setMsgContent("渠道接入失败");
//			messageModel.setEvent("end");
//			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(),true);
        }
    }

    /**
     * 进队列排队方式：队列
     * 排队策略（CHANNEL_CONF.QUEUE_RULE）：1-默认（排队先后顺序），2-红名单优先，3-区域优先   优先排队区域：多选(从CC_AREA表) CHANNEL_CONF.AREA_CODES(字符串数组)
     *
     * @param entContext
     * @param channel
     * @param messageModel
     * @param userSession
     */
    public void addChannelKeyQueue(EntContext entContext, Channel channel, MessageModel messageModel, UserSession userSession) {
        try {
            String channelKeyStr = channel.getChannelKey();
            String sessionId = messageModel.getSessionId();
            ChannelKey channelKey = channel.getChannelKeyByCode(messageModel.getMsgContent());
            JSONObject channelConfig = channel.getChannelConfig();
            String queueId = channelKey.getQueueId();
            //渠道按键未配置队列
            try {
                if (StringUtils.isBlank(queueId)) {
                    logger.warn("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 104渠道按键未配置队列，接入按键技能组");
                    queueId = channelKey.getSkillGroupId();
                }
                channelKey.getQueueSkillGroup();
            } catch (Exception e1) {
                logger.error(e1.getMessage(), e1);
                messageModel.setMsgContent(CommonUtil.replaceParam(userSession, channel.getAutoConfig().getAgentOfflineMsg()));
                messageModel.setEvent("end");
                ProducerBroker.sendUserMessage(sessionId, messageModel.toString(), true);
                return;
            }
            //20201223
            userSession.setQueueId(queueId);

            //3.1#20211028-1
            userSession.setQueueName(channelKey.getQueueName());

            //检查用户接入时是否接入指定坐席或有专属坐席
            String selAgentId = messageModel.getUserInfo().getString("selAgentId");
            if (StringUtils.isNotBlank(selAgentId)) {
                logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 专属坐席或指定坐席优先：" + selAgentId);
            } else {
                String priortiyFlag = channelConfig.getString("PRIORTIY_LATEST_AGENT");
//			logger.info("[GetAgentMessage]["+channelKeyStr+"]["+sessionId+"] -> 是否熟客优先(1-开启)："+priortiyFlag);
                LogUtil.addTraceLog(channel.getChannelKey(), sessionId, null, userSession.getChatSessionId(), "getAgent", "[GetAgentMessage] -> 是否熟客优先(1-开启)：" + priortiyFlag, null, logger);
                if ("1".equals(priortiyFlag)) {
                    String agentId = getLatestAgent(entContext, sessionId, channel.getChannelKey(), channelConfig);
                    if (StringUtils.isNotBlank(agentId)) {
                        if (userSession.setQueueAgentId(agentId)) {
                            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 设置上次接待坐席：" + agentId);
                        } else {
                            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 设置上次接待坐席失败,非在线且空闲状态：" + agentId);
                        }
                    } else {
                        logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 未找到上次接待坐席：" + agentId);
                    }
                }
            }

            String queueRule = channelConfig.getString("QUEUE_RULE");
            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 渠道排队策略：" + queueRule);
            //boolean busiFlag = false;
            //2-红名单优先，检查用户是否在红名单内
            if ("2".equals(queueRule) && entContext.checkRedList(sessionId)) {
                JSONObject userInfo = userSession.getUserInfo();
                int levelCode = Integer.parseInt(userInfo.getString("levelCode"));
                int levelInt = 100 * levelCode;
                logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 红名单用户，红名单等级：" + userInfo.getString("levelName") + ",levelCode[" + levelCode + "],设置优先级为:" + levelInt);

                userSession.setLevel(levelInt); //红名单优先
            }
            QueueStrategy queueStrategy = entContext.getQueueStrategy();
            String routingRule = channelConfig.getString("ROUTING_RULE");
            logger.info("[GetAgentMessage][" + channelKeyStr + "][" + sessionId + "] -> 渠道路由策略：" + routingRule);

            //logger.info(" >> GetAgentMessage("+sessionId+") queueStrategy->"+queueStrategy);

            String entId = userSession.getEntId();
            //添加排队记录
            EasyCalendar cal = EasyCalendar.newInstance();
            EasyRecord record = new EasyRecord(EntContext.getContext(entId).getTableName("CC_MEDIA_QUEUE"), "SERIAL_ID");
            record.put("SERIAL_ID", userSession.getChatSessionId());
            record.put("DATE_ID", cal.getDateInt());
            record.put("ENT_ID", entId);
            record.put("SESSION_ID", userSession.getSessionId());
            record.put("GROUP_ID", queueId);
            record.put("CHANNEL_ID", userSession.getChannel().getChannelId());
            record.put("CHANNEL_KEY", userSession.getChannelKey().getChannelKeyId());
            record.put("QUEUE_TIME", cal.getDateTime("-"));
            SQLExecutor sqlExecutor = new SQLExecutor(userSession.getSessionId(), record, "insert");
            UserEventDispatcher.addEvent(sqlExecutor);

            userSession.setReportQueneNo(false);

            //3.1#20210621-1更新接入记录，进入排队
            JSONObject udata = new JSONObject();
            udata.put("CHAT_SESSION_ID", userSession.getChatSessionId());
            udata.put("GET_AGENT_TIME", EasyCalendar.newInstance().getDateTime("-"));
            udata.put("QUEUE_START_TIME", EasyCalendar.newInstance().getDateTime("-"));
            udata.put("IS_IN_QUEUE", 1);
            udata.put("ENT_ID", entId);
            //3.1#20210816-1 增加队列ID，必须执行最新脚本
            udata.put("QUEUE_ID", queueId);
            AccessRecord.getInstance().updateAccessRecord(userSession.getSessionId(), udata);

            queueStrategy.addQueueUser(userSession);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

    }

    /**
     * 根据渠道的配置，如果开启熟客优先模式，按配置的规则找到最近接待的坐席
     *
     * @param entContext
     * @param sessionId
     * @param channelKey
     * @param channelConfig
     * @return
     */
    private String getLatestAgent(EntContext entContext, String sessionId, String channelKey, JSONObject channelConfig) {
        String agentId = null;
        try {
            //最近服务坐席类型：1-最后服务坐席  2-服务最多坐席
            String latestAgentType = channelConfig.getString("PRIORTIY_LATEST_AGENT_TYPE");
            logger.info("[GetAgentMessage][" + sessionId + "] -> 熟客优先模式(1-最后服务坐席  2-服务最多坐席)：" + latestAgentType);
            if ("2".equals(latestAgentType)) {
                int latestDay = channelConfig.getIntValue("PRIORTIY_LATEST_DAY");  //1-近一天  2-近3天  3- 近7天
                if (latestDay == 1) {
                    latestDay = 1;
                } else if (latestDay == 2) {
                    latestDay = 3;
                } else if (latestDay == 3) {
                    latestDay = 7;
                } else {
                    latestDay = 3;
                }
                logger.info("[GetAgentMessage][" + sessionId + "] -> 查询服务最多坐席的日期范围：" + latestDay);
                String table = entContext.getTableName("CC_MEDIA_RECORD");
                EasySQL sql = new EasySQL();
                sql.append(" SELECT AGENT_ID FROM ( ");
                sql.append(" 	SELECT T1.AGENT_ID,COUNT(1) NUM FROM " + table + " T1 WHERE T1.SESSION_ID=? and t1.ENT_ID=? AND T1.CHANNEL_KEY=? and t1.DATE_ID>=? and t1.DATE_ID<=?  GROUP BY  T1.AGENT_ID  ");
                sql.append(" ) T2 ORDER BY T2.NUM DESC ");

                EasyCalendar ec = EasyCalendar.newInstance();
                int endDate = ec.getDateInt();
                ec.add(Calendar.DATE, -latestDay);
                int beginDate = ec.getDateInt();
                agentId = QueryFactory.getReadQuery().queryForString(sql.getSQL(), sessionId, entContext.getEntId(), channelKey, beginDate, endDate);

            } else {  //找最近服务的坐席
                String priortiyStr = (String) CacheUtil.get("PRIORTIY_LATEST_AGENT_" + channelKey + "_" + sessionId);
                logger.info("[GetAgentMessage][" + sessionId + "] -> 查到最近服务的坐席缓存：" + priortiyStr);
                if (StringUtils.isNotBlank(priortiyStr)) {
                    String[] split = priortiyStr.split("##");
                    agentId = split[0];
                }
            }
        } catch (Exception e) {
            logger.error("查询客户最近接待坐席异常:" + e.getMessage(), e);
        }

        return agentId;
    }

    @Override
    public String getCommandName() {
        return "GetAgentMessage";
    }
}