package com.yunqu.yc.media.center.event.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.event.EventExecutor;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;

import java.io.Serializable;

/**
 * 排队通知消息执行器，排队信息更新通知给在线坐席。
 * <AUTHOR>
 *
 */
public class AgentNotifyExecutor extends EventExecutor implements Serializable {

	public AgentNotifyExecutor(JSONObject jsonObject){
		super(jsonObject);
	}

	@Override
	public String getSessionId() {
		return this.getJSONObject().getString("agentId");
	}

	@Override
	public void handleMessage() throws Exception {
		JSONObject jsonObject = this.getJSONObject();
		ProducerBroker.sendAgentMessage(jsonObject.getString("agentId"),  jsonObject.toString());
	}
}
