html,
body {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

*::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

*::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    background-clip: padding-box;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}

*::-webkit-scrollbar-thumb:hover {
    background-color: #bbb;
}

* {
    margin: 0;
    padding: 0;
}

#analyModel {
    height: calc(100% - 40px);
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.main {
    display: flex;
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
}

.main .left {
    width: 22%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.left .boxList {
    flex: 1;
    overflow: auto;
    margin-bottom: 12px;
}

.main .right {
    flex: 1;
    margin-left: 16px;
    overflow: auto;
}

.model-box {
    border-radius: 4px;
    background-color: #fff;
    padding: 16px;
    margin-bottom: 16px;
    box-sizing: border-box;
    border: 2px solid transparent;
}

.model-box .header {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
}

.model-active {
    border: 2px solid #409eff;
}

.model-box .info-item {
    width: 100%;
    display: flex;
    font-size: 14px;
    line-height: 25px;
}

.info-label {
    text-align: right;
    width: 100px;
    color: #76838f;
}
.info-value {
    flex: 1;
}

.load-text {
    font-size: 14px;
    text-align: center;
}

.box-card {
    margin-bottom: 16px;
}

.flex-space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
