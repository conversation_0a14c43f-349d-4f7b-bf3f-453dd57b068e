"use strict";(self["webpackChunkmonitor_screen"]=self["webpackChunkmonitor_screen"]||[]).push([[312],{4223:function(t,a,s){s.d(a,{A:function(){return r}});var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"static-panel",staticStyle:{overflow:"hidden"}},[a("div",{staticClass:"bg-polygonal"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("各渠道质检质量分析"))+" ")]),a("div",{staticClass:"static-panel-header-right-opt"},[a("div",{staticClass:"baseflex"},[a("div",{staticClass:"opt-items opt-items-bg-one"}),a("div",{staticClass:"opt-items-name opt-items-color-one"},[t._v(" "+t._s(t.$t("质检数"))+" ")])]),a("div",{staticClass:"baseflex"},[a("div",{staticClass:"opt-items opt-items-bg-two"}),a("div",{staticClass:"opt-items-name opt-items-color-two"},[t._v(" "+t._s(t.$t("及格率"))+" ")])])])]),a("div",{staticClass:"channel_Boxs",class:["channdel__"+t.chanLen]},[a("div",{staticClass:"__total"},[a("div",{staticClass:"__total__data"},[a("div",{staticClass:"__data__tp1"},[t._v("质检总数")]),a("div",{staticClass:"__data__tp2"},[a("vue-count-to",{attrs:{endVal:Number(t.showData.ALL_FINISH_COUNT||0),duration:1e3}})],1)])]),t._l(t.QC_CHANNEL_TYPE,(function(s,i){return a("div",{key:"channel"+i,class:["__channel__type__"+i,"__channel__type"]},[a("div",{staticClass:"__channel__type__data"},[a("div",{staticClass:"_c_t_d_tp1"},[t._v(t._s(s))]),a("div",{staticClass:"_c_t_d_tp2"},[a("div",{staticClass:"blue__count __count"},[a("vue-count-to",{attrs:{endVal:Number(t.showData[i]?.qualiyCount||0),duration:1e3}})],1),a("div",{staticClass:"yellow_count mr-l16 __count"},[a("vue-count-to",{attrs:{endVal:Number(100*t.showData[i]?.passRate||0),duration:1e3}}),a("span",[t._v("%")])],1)])])])}))],2)])])},e=[],n={name:"channel-analysic",props:["QC_CHANNEL_TYPE","channelQualityAnalysic"],data(){return{chanLen:0,showData:{ALL_FINISH_COUNT:0,1:{},2:{},3:{},4:{},9:{}}}},watch:{QC_CHANNEL_TYPE:{deep:!0,immediate:!0,handler(t){if(t){let s=0;for(var a in t)s++;this.chanLen=s}}},channelQualityAnalysic:{deep:!0,handler(t){t&&(this.showData=t)}}},mounted(){},methods:{}},_=n,l=s(1656),c=(0,l.A)(_,i,e,!1,null,"45eaec38",null),r=c.exports},403:function(t,a,s){s.d(a,{A:function(){return r}});var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"static-charts"},[a("div",{staticClass:"quality__msg__top"},[a("div",{staticClass:"left"},["人工质检"==t.qcType?a("img",{attrs:{src:s(9995),alt:""}}):"智能质检"==t.qcType?a("img",{attrs:{src:s(6052),alt:""}}):t._e(),a("div",{staticClass:"mr-l8"},[t._v(t._s(t.$t(t.qualityMsg.title)))])]),a("vue-count-to",{staticClass:"blue__count",attrs:{endVal:Number(t.qualityMsg.CHECK_COUNT||0),duration:1e3}})],1),a("div",{staticClass:"quality__msg__data"},[a("div",{staticClass:"quality__loop blue__bg"},[a("el-progress",{attrs:{type:"circle",percentage:Math.round(100*t.qualityMsg.FINISH_RATE||0),"stroke-width":12,width:100,"define-back-color":"rgba(51, 154, 240, 0.2)","text-color":"#339AF0",color:"#339AF0"}})],1),a("div",{staticClass:"two__panel"},[a("div",{staticClass:"onePanel"},[a("div",{staticClass:"gray_text"},[t._v(t._s(t.$t("质检完成数")))]),a("vue-count-to",{staticClass:"black_text",attrs:{endVal:Number(t.qualityMsg.FINISH_COUNT||0),duration:1e3}})],1),a("div",{staticClass:"onePanel"},[a("div",{staticClass:"gray_text"},[t._v(t._s(t.$t("质检完成率")))]),a("div",{staticClass:"black_text"},[a("vue-count-to",{attrs:{decimals:2,endVal:Math.round(100*t.qualityMsg.FINISH_RATE||0),duration:1e3}}),a("span",[t._v("%")])],1)])])]),a("div",{staticClass:"quality__msg__data"},[a("div",{staticClass:"quality__loop yellow__bg"},[a("el-progress",{attrs:{type:"circle",percentage:Math.round(100*t.qualityMsg.PASS_RATE||0),"stroke-width":12,width:100,"define-back-color":"rgba(252, 196, 25, 0.2)","text-color":"#FCC419",color:"#FCC419"}})],1),a("div",{staticClass:"two__panel"},[a("div",{staticClass:"onePanel"},[a("div",{staticClass:"gray_text"},[t._v(t._s(t.$t("质检及格数")))]),a("vue-count-to",{staticClass:"black_text",attrs:{endVal:Number(t.qualityMsg.PASS_COUNT||0),duration:1e3}})],1),a("div",{staticClass:"onePanel"},[a("div",{staticClass:"gray_text"},[t._v(t._s(t.$t("质检及格率")))]),a("div",{staticClass:"black_text"},[a("vue-count-to",{attrs:{decimals:2,endVal:Math.round(100*t.qualityMsg.PASS_RATE||0),duration:1e3}}),a("span",[t._v("%")])],1)])])]),a("div",{staticClass:"quality__msg__bottom"},[a("el-divider",{attrs:{"content-position":"left"}},[t._v(t._s("人工质检"==t.qcType?"智能质检":"人工质检"))]),a("div",{staticClass:"baseflex",staticStyle:{"justify-content":"space-around"}},[a("div",{staticClass:"baseflex"},[a("div",{staticClass:"gray_text mr-r8"},[t._v(t._s(t.$t("推送数")))]),a("div",{staticClass:"black_text"},[a("vue-count-to",{attrs:{endVal:Number(t.qualityMsg.CHECK_COUNT_ORTHER||0),duration:1e3}})],1)]),a("div",{staticClass:"baseflex"},[a("div",{staticClass:"gray_text mr-r8"},[t._v(t._s(t.$t("完成率")))]),a("div",{staticClass:"black_text"},[a("vue-count-to",{attrs:{decimals:2,endVal:Math.round(100*t.qualityMsg.FINISH_RATE_ORTHER||0),duration:1e3}}),a("span",[t._v("%")])],1)]),a("div",{staticClass:"baseflex"},[a("div",{staticClass:"gray_text mr-r8"},[t._v(t._s(t.$t("及格率")))]),a("div",{staticClass:"black_text"},[a("vue-count-to",{attrs:{decimals:2,endVal:Math.round(100*t.qualityMsg.PASS_RATE_ORTHER||0),duration:1e3}}),a("span",[t._v("%")])],1)])])],1)])},e=[],n={name:"quality-message",props:{qcType:{type:String,default:"人工质检"},qualityBasicData:{type:Object,default:()=>({RG:{},ZN:{}})}},data(){return{qualityMsg:{title:"人工质检抽取数",count:0}}},watch:{qcType:{deep:!0,handler(t){if(t){this.qualityMsg.title=t+"推送数";let a=this.qualityBasicData;"人工质检"==this.qcType?this.qualityMsg={...this.qualityMsg,...a.RG,CHECK_COUNT_ORTHER:a.ZN.CHECK_COUNT,FINISH_RATE_ORTHER:a.ZN.FINISH_RATE,PASS_RATE_ORTHER:a.ZN.PASS_RATE}:this.qualityMsg={...this.qualityMsg,...a.ZN,CHECK_COUNT_ORTHER:a.RG.CHECK_COUNT,FINISH_RATE_ORTHER:a.RG.FINISH_RATE,PASS_RATE_ORTHER:a.RG.PASS_RATE}}}},qualityBasicData:{deep:!0,handler(t){t&&("人工质检"==this.qcType?this.qualityMsg={...this.qualityMsg,...t.RG,CHECK_COUNT_ORTHER:t.ZN.CHECK_COUNT,FINISH_RATE_ORTHER:t.ZN.FINISH_RATE,PASS_RATE_ORTHER:t.ZN.PASS_RATE}:this.qualityMsg={...this.qualityMsg,...t.ZN,CHECK_COUNT_ORTHER:t.RG.CHECK_COUNT,FINISH_RATE_ORTHER:t.RG.FINISH_RATE,PASS_RATE_ORTHER:t.RG.PASS_RATE})}}},mounted(){},methods:{}},_=n,l=s(1656),c=(0,l.A)(_,i,e,!1,null,"ec49145e",null),r=c.exports},4111:function(t,a,s){s.a(t,(async function(t,i){try{s.r(a);var e=s(3319),n=s(7624),_=(s(8879),s(1656)),l=t([n]);n=(l.then?(await l)():l)[0];var c=(0,_.A)(n.A,e.X,e.Y,!1,null,"2b7d234f",null);a["default"]=c.exports,i()}catch(r){i(r)}}))},8879:function(){},7624:function(t,a,s){s.a(t,(async function(t,i){try{var e=s(5855),n=t([e]);e=(n.then?(await n)():n)[0],a.A=e.A,i()}catch(_){i(_)}}))},3319:function(t,a,s){s.d(a,{X:function(){return i},Y:function(){return e}});var i=function(){var t=this,a=t._self._c;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"base__page",attrs:{"element-loading-text":"数据加载中"}},[a("base-header",{ref:"baseHeader",attrs:{title:t.$t("质检总体分析")},on:{updateDate:t.updateDate,changeQC:t.changeQC,changeChannel:t.changeChannel}},[a("template",{slot:"options"})],2),a("div",{staticClass:"base__content"},[a("div",{staticClass:"qc-total-analysic-top"},[a("div",[a("div",{staticClass:"static-panel h-50-8"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("各渠道任务抽取数"))+" ")])]),a("xBarChart",{ref:"chart1"})],1),a("div",{staticClass:"static-panel mr-t16 h-50-8"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("各渠道质检数平均分"))+" ")]),a("div",{staticClass:"static-panel-header-right-opt"},[a("div",{staticClass:"mr-r8",staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(t.$t("全年均分"))+" ")]),a("div",{staticClass:"mr-r8",staticStyle:{color:"#f03838","font-size":"14px","font-weight":"bold"}},[t._v(" — ")]),a("div",{staticClass:"mr-r8",staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(t.$t("该月均分"))+" ")]),a("div",{staticStyle:{color:"#52c41a","font-size":"14px","font-weight":"bold"}},[t._v(" — ")])])]),a("gridChart",{ref:"chart2"})],1)]),a("chanelanalysic",{attrs:{channelQualityAnalysic:t.BasicData.channelQualityAnalysic,QC_CHANNEL_TYPE:t.QC_CHANNEL_TYPE}}),a("div",[a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t(t.qualityTitle))+" ")])]),a("qualitymessage",{attrs:{qcType:t.qualityTitle,qualityBasicData:t.BasicData.qualityBasicData}})],1)])],1),a("div",{staticClass:"qc-total-analysic-middle mr-t16"},[a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("平均趋势情况"))+" ")])]),a("lineChart",{key:"lineChart_3",ref:"chart3",attrs:{QC_CHANNEL_TYPE:t.QC_CHANNEL_TYPE,currentBusiType:t.currentBusiType,tyP:"ava"}})],1),a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("不及格数趋势情况"))+" ")])]),a("lineChart",{key:"lineChart_4",ref:"chart4",attrs:{QC_CHANNEL_TYPE:t.QC_CHANNEL_TYPE,currentBusiType:t.currentBusiType,tyP:"noPass"}})],1)]),a("div",{staticClass:"qc-total-analysic-middle mr-t16"},[a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("申诉趋势分析"))+" ")])]),a("div",{ref:"chart5",staticClass:"static-charts",attrs:{id:"chart5"}})]),a("div",{staticStyle:{display:"grid","grid-template-columns":"1fr 1fr",gap:"16px",height:"100%"}},[a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("质检得分等级占比"))+" ")])]),a("div",{ref:"chart6",staticClass:"static-charts",attrs:{id:"chart6"}})]),a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("一票否决率"))+" ")])]),a("pictorialChart",{ref:"chart7",attrs:{currentBusiType:t.currentBusiType}})],1)])]),a("div",{staticClass:"qc-total-analysic-bottom mr-t16"},[a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("人工质检扣分项分析"))+" ")])]),a("ranking",{attrs:{showlist:t.rankingList1}})],1),a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("智能质检扣分项分析"))+" ")])]),a("ranking",{attrs:{showlist:t.rankingList2}})],1),a("div",{staticClass:"static-panel h-100"},[a("div",{staticClass:"static-panel-header"},[a("div",{staticClass:"static-panel-header-title"},[t._v(" "+t._s(t.$t("一票否决命中次数排行"))+" ")])]),a("ranking",{attrs:{showlist:t.rankingList3}})],1)])])],1)},e=[]},6124:function(t,a,s){s.d(a,{W:function(){return e},v:function(){return i}});const i={VOICE:{name:"语音",o_name:"话务",id:"1",taskKey:"CALL_TASK_COUNT",keys:{RG:"CALL_RG",ZN:"CALL_ZN"}},MEDIA:{name:"全媒体",o_name:"全媒体",id:"2",taskKey:"MEDIA_TASK_COUNT",keys:{RG:"MEDIA_RG",ZN:"MEDIA_ZN"}},EMAIL:{name:"邮件",o_name:"邮件",id:"3",taskKey:"EMAIL_TASK_COUNT",keys:{RG:"EMAIL_RG",ZN:"EMAIL_ZN"}},ORDER:{name:"工单",o_name:"工单",id:"4",taskKey:"ORDER_TASK_COUNT",keys:{RG:"ORDER_RG",ZN:"ORDER_ZN"}},THIRD:{name:"第三方",o_name:"其他",id:"9",taskKey:"THIRD_TASK_COUNT",keys:{RG:"THIRD_RG",ZN:"THIRD_ZN"}}},e={"语音":{RG:"CALL_RG_AVG_SCORE",ZN:"CALL_ZN_AVG_SCORE"},"话务":{RG:"CALL_RG_AVG_SCORE",ZN:"CALL_ZN_AVG_SCORE"},"全媒体":{RG:"MEDIA_RG_AVG_SCORE",ZN:"MEDIA_ZN_AVG_SCORE"},"邮件":{RG:"EMAIL_RG_AVG_SCORE",ZN:"EMAIL_ZN_AVG_SCORE"},"工单":{RG:"ORDER_RG_AVG_SCORE",ZN:"ORDER_ZN_AVG_SCORE"},"第三方":{RG:"THIRD_RG_AVG_SCORE",ZN:"THIRD_ZN_AVG_SCORE"},"其他":{RG:"THIRD_RG_AVG_SCORE",ZN:"THIRD_ZN_AVG_SCORE"}}},3893:function(t,a,s){s.d(a,{UF:function(){return A},VG:function(){return C},X6:function(){return o},_U:function(){return l},p_:function(){return E},qV:function(){return r},qW:function(){return c}});s(4114),s(8111),s(7588),s(1701);var i=s(2856),e=s(2661),n=s(6124);const _=e.A.isEmpty;async function l(t,a){try{const s=await t(a);return 1!==s.state?(i["default"].prototype.$message.error(s.msg),null):s.data}catch(s){return i["default"].prototype.$message.error("请求失败"),console.error(s),null}}function c(t,a,s,i){return t?Object.values(n.v).map((e=>({label:"EXIST"===i?e.name:e.o_name,value:t[`${e.keys[a]}_${s}`]||0}))):[]}function r(t,a){const s={ALL_FINISH_COUNT:t[`ALL_${a}_CHECK_COUNT`]};return Object.values(n.v).forEach((i=>{const e=t[`${i.keys[a]}_FINISH_COUNT`];_(e)||(s[i.id]={qualiyCount:e,passRate:t[`${i.keys[a]}_PASS_RATE`]})})),s}function A(t,a,s){["RG","ZN"].forEach((i=>{s[`${i}_ALL_CHANNEL_AVAER_SCORE`]=s[`${i}_ALL_CHANNEL_AVAER_SCORE`].map((s=>({...s,[a]:t[n.W[s.label][i]]})))}))}function C(t,a=5){return(t||[]).slice(0,a)}function o(t){return t?{RG:{CHECK_COUNT:t.ALL_RG_CHECK_COUNT||0,FINISH_COUNT:t.ALL_RG_FINISH_COUNT||0,FINISH_RATE:t.ALL_RG_FINISH_RATE||0,PASS_COUNT:t.ALL_RG_PASS_COUNT||0,PASS_RATE:t.ALL_RG_PASS_RATE||0},ZN:{CHECK_COUNT:t.ALL_ZN_CHECK_COUNT||0,FINISH_COUNT:t.ALL_ZN_FINISH_COUNT||0,FINISH_RATE:t.ALL_ZN_FINISH_RATE||0,PASS_COUNT:t.ALL_ZN_PASS_COUNT||0,PASS_RATE:t.ALL_ZN_PASS_RATE||0}}:{}}function E(t,a){if(!t)return[];const s=[];return"ZN"===a?(s.push(t.CALL_ZN_VOTE_RATE||0),s.push(t.ORDER_ZN_VOTE_RATE||0),s.push(t.MEDIA_ZN_VOTE_RATE||0),s.push(t.THIRD_ZN_VOTE_RATE||0),s.push(t.EMAIL_ZN_VOTE_RATE||0)):(s.push(t.CALL_VOTE_RATE||0),s.push(t.ORDER_VOTE_RATE||0),s.push(t.MEDIA_VOTE_RATE||0),s.push(t.THIRD_VOTE_RATE||0),s.push(t.EMAIL_VOTE_RATE||0)),s}},9150:function(t,a,s){s.a(t,(async function(t,i){try{s.d(a,{D$:function(){return L},MK:function(){return p},SC:function(){return d},fX:function(){return u}});s(8111),s(2489),s(1701),s(8237);var e=s(1864),n=s(3893),_=s(6124);const c=()=>({ALL_CHANNEL_TASKS:[],RG_ALL_CHANNEL_AVAER_SCORE:[],ZN_ALL_CHANNEL_AVAER_SCORE:[],RG_ALL_CHANNEL_QUALITY_ANALYSIC:{},ZN_ALL_CHANNEL_QUALITY_ANALYSIC:{},BASIC_QUALITY:[],RG_AVAER_TRENDS:[],ZN_AVAER_TRENDS:[],RG_NO_AVAER_TRENDS:[],ZN_NO_AVAER_TRENDS:[],APPEAL_TRENDS:[],QUALITY_SCORE_LEVEL_ZN:[],QUALITY_SCORE_LEVEL_RG:[],ONE_VOTE_RATE:[],ONE_ZN_VOTE_RATE:[],RG_BASIC_QUALITY_DEDUCTION:[],ZN_BASIC_QUALITY_DEDUCTION:[],ONE_VOTE_HIT:[]}),r=c();let A={},C={},o="EXIST",E=o;function d(t){return E=o,o=t,o}async function h(){const t=await e._q(["QC_CHANNEL_TYPE","THIRD_PARTY_BUSI_TYPE"]);return t&&(A=t["QcCommonDao.getDict(QC_CHANNEL_TYPE)"]?.data,C=t["QcCommonDao.getDict(THIRD_PARTY_BUSI_TYPE)"]?.data),{QC_CHANNEL_TYPE:A,THIRD_PARTY_BUSI_TYPE:C}}function u(){return"EXIST"===o?A:C}async function T(t){const a=await(0,n._U)(e.Jn,{...t,range:"year"});a&&(0,n.UF)(a,"year",r);const s=await(0,n._U)(e.Jn,{...t,range:"month"});s&&(0,n.UF)(s,"month",r)}async function N(t){const[a,s,i]=await Promise.all([(0,n._U)(e.rN,{...t,type:"RG"}),(0,n._U)(e.rN,{...t,type:"ZN"}),(0,n._U)(e.rN,{...t,type:"ONE"})]);r.RG_BASIC_QUALITY_DEDUCTION=(0,n.VG)(a),r.ZN_BASIC_QUALITY_DEDUCTION=(0,n.VG)(s),r.ONE_VOTE_HIT=i||[]}function R(t){return t?Object.values(_.v).map((a=>({channelName:"EXIST"===o?a.name:a.o_name,value:t[a.taskKey]||0}))):[]}async function L(t,a){const s=E!==o,i=async()=>{const a=await(0,n._U)("EXIST"===o?e.DC:e.Jx,t);a&&(r.ALL_CHANNEL_TASKS=R(a),r.RG_ALL_CHANNEL_QUALITY_ANALYSIC=(0,n.qV)(a,"RG"),r.ZN_ALL_CHANNEL_QUALITY_ANALYSIC=(0,n.qV)(a,"ZN"),r.BASIC_QUALITY=(0,n.X6)(a),r.ONE_VOTE_RATE=(0,n.p_)(a),r.ONE_ZN_VOTE_RATE=(0,n.p_)(a,"ZN"),r.RG_ALL_CHANNEL_AVAER_SCORE=(0,n.qW)(a,"RG","AVG_SCORE",o),r.ZN_ALL_CHANNEL_AVAER_SCORE=(0,n.qW)(a,"ZN","AVG_SCORE",o));const[s,i]=await Promise.all([(0,n._U)("EXIST"===o?e.K7:e.C5,{...t,type:"RG"}),(0,n._U)("EXIST"===o?e.K7:e.C5,{...t,type:"ZN"})]);r.RG_AVAER_TRENDS=s||[],r.ZN_AVAER_TRENDS=i||[];const[_,l]=await Promise.all([(0,n._U)("EXIST"===o?e.sH:e.Wm,{...t,type:"RG"}),(0,n._U)("EXIST"===o?e.sH:e.Wm,{...t,type:"ZN"})]);r.RG_NO_AVAER_TRENDS=_||[],r.ZN_NO_AVAER_TRENDS=l||[];const c=await(0,n._U)("EXIST"===o?e._d:e.UU,{...t,type:"RG"}),A=await(0,n._U)("EXIST"===o?e._d:e.UU,{...t,type:"ZN"});r.QUALITY_SCORE_LEVEL_RG=c||[],r.QUALITY_SCORE_LEVEL_ZN=A||[]},_=async()=>{const a=await(0,n._U)(e.jz,t);a&&(r.APPEAL_TRENDS=a.map((t=>({label:t.DATE_ID,value1:t.RECONSIDER_COUNT,value2:t.RECONSIDER_SUCC_COUNT})))),await T(t),await N(t)};return s?(await i(),E=o):await Promise.all([i(),_()]),p(a)}function p(t){if(!t)return;const a={ONE_VOTE_HIT:r.ONE_VOTE_HIT,ALL_CHANNEL_TASKS:r.ALL_CHANNEL_TASKS,ONE_VOTE_RATE:r.ONE_VOTE_RATE,APPEAL_TRENDS:r.APPEAL_TRENDS,QUALITY_SCORE_LEVEL_RG:r.QUALITY_SCORE_LEVEL_RG,QUALITY_SCORE_LEVEL_ZN:r.QUALITY_SCORE_LEVEL_ZN,BASIC_QUALITY:r.BASIC_QUALITY,RG_BASIC_QUALITY_DEDUCTION:(0,n.VG)(r.RG_BASIC_QUALITY_DEDUCTION),ZN_BASIC_QUALITY_DEDUCTION:(0,n.VG)(r.ZN_BASIC_QUALITY_DEDUCTION)};"ZN"===t&&(a.ONE_VOTE_RATE=r.ONE_ZN_VOTE_RATE);const s=Object.entries(r).filter((([a])=>a.includes(t))).reduce(((t,[a,s])=>({...t,[a]:s})),{});return{...a,...s}}await h(),i()}catch(l){i(l)}}),1)},5855:function(t,a,s){s.a(t,(async function(t,i){try{s(4114),s(8111),s(1701);var e=s(3847),n=s(9548),_=s(4769),l=s(2846),c=s(9303),r=s(4223),A=s(403),C=s(9150),o=t([C]);C=(o.then?(await o)():o)[0],a.A={name:"total-analysic",components:{chanelanalysic:r.A,xBarChart:e.A,gridChart:n["default"],qualitymessage:A.A,lineChart:_.A,pictorialChart:c.A,ranking:l.A},data(){return{currentBusiType:"EXIST",basic_data:{},qualityTitle:"人工质检",QC_CHANNEL_TYPE:{},rankingList1:[],rankingList2:[],rankingList3:[],BasicData:{channelTasks:[],channelQualityEverCount:[],channelQualityAnalysic:{},qualityBasicData:{},averageTrends:[],noPassTrends:[],appealTrends:[],qualityScoreLevelRadioRg:[],qualityScoreLevelRadioZn:[],oneVoteList:[]},loading:!1}},mounted(){this.setDT("RG"),this.QC_CHANNEL_TYPE=(0,C.fX)()},methods:{updateDate(t){"人工质检"==this.qualityTitle?this.setDT("RG"):this.setDT("ZN")},setDT(t){this.loading=!0,(0,C.D$)(this.$refs.baseHeader.getDate(),t).then((a=>{console.log("794::getApiData=>",a),this.BasicData.channelTasks=a.ALL_CHANNEL_TASKS,this.$refs.chart1.initCharts(this.BasicData.channelTasks),this.BasicData.qualityBasicData=a.BASIC_QUALITY,this.BasicData.appealTrends=a.APPEAL_TRENDS,this.initCharts5(),this.BasicData.qualityScoreLevelRadioRg=a.QUALITY_SCORE_LEVEL_RG,this.BasicData.qualityScoreLevelRadioZn=a.QUALITY_SCORE_LEVEL_ZN,this.initCharts6(t),this.BasicData.oneVoteList=a.ONE_VOTE_RATE,this.$refs.chart7.initCharts(0,this.BasicData.oneVoteList),this.rankingList1=a.RG_BASIC_QUALITY_DEDUCTION,this.rankingList2=a.ZN_BASIC_QUALITY_DEDUCTION;var s=JSON.parse(JSON.stringify(a.ONE_VOTE_HIT))||[];this.rankingList3=s.splice(0,5),this.fullData(a,t)}))},fullData(t,a){"RG"==a?(this.BasicData.channelQualityAnalysic=t.RG_ALL_CHANNEL_QUALITY_ANALYSIC,this.BasicData.channelQualityEverCount=t.RG_ALL_CHANNEL_AVAER_SCORE,this.$refs.chart2.initCharts(this.BasicData.channelQualityEverCount),this.BasicData.averageTrends=t.RG_AVAER_TRENDS,this.$refs.chart3.initCharts(this.BasicData.averageTrends),this.BasicData.noPassTrends=t.RG_NO_AVAER_TRENDS,this.$refs.chart4.initCharts(this.BasicData.noPassTrends),this.initCharts6(a)):"ZN"==a&&(this.BasicData.channelQualityAnalysic=t.ZN_ALL_CHANNEL_QUALITY_ANALYSIC,this.BasicData.channelQualityEverCount=t.ZN_ALL_CHANNEL_AVAER_SCORE,this.$refs.chart2.initCharts(this.BasicData.channelQualityEverCount),this.BasicData.averageTrends=t.ZN_AVAER_TRENDS,this.$refs.chart3.initCharts(this.BasicData.averageTrends),this.BasicData.noPassTrends=t.ZN_NO_AVAER_TRENDS,this.$refs.chart4.initCharts(this.BasicData.noPassTrends),this.BasicData.oneVoteList=t.ONE_VOTE_RATE,this.$refs.chart7.initCharts(0,this.BasicData.oneVoteList),this.initCharts6(a)),this.loading=!1},changeQC(t){this.qualityTitle=t,"人工质检"==t?this.fullData((0,C.MK)("RG"),"RG"):this.fullData((0,C.MK)("ZN"),"ZN")},changeChannel(t,a){console.log("::changeChannel=>",t,a),this.currentBusiType=(0,C.SC)(t),this.QC_CHANNEL_TYPE=(0,C.fX)(),"人工质检"==a?this.setDT("RG"):this.setDT("ZN")},generateRandomNumbers(t){for(var a=[],s=0;s<t;s++){var i=Math.floor(100*Math.random())+300;a.push(i)}return a},initCharts5(){let t=this.BasicData.appealTrends.map((t=>t.label)),a=this.BasicData.appealTrends.map((t=>t.value1)),s=this.BasicData.appealTrends.map((t=>t.value2)),i={color:["#FA9904","#339AF0"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{orient:"horizontal",x:"right"},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:[{type:"category",data:t}],yAxis:[{type:"value",name:this.$t("数量"),nameTextStyle:{color:"#222",fontSize:16,padding:[0,50,0,0]},splitLine:{show:!0,lineStyle:{type:"dashed"}}}],series:[{name:this.$t("申诉成功量"),type:"bar",barWidth:14,barGap:"-100%",z:"2",data:s},{name:this.$t("申诉量"),type:"bar",barWidth:14,data:a}]},e=this.$echarts.init(document.getElementById("chart5"));e.setOption(i),window.addEventListener("resize",(()=>{e.resize()}))},initCharts6(t){var a="qualityScoreLevelRadioRg";"ZN"==t&&(a="qualityScoreLevelRadioZn");let s=this.BasicData[a].map((t=>t.A)),i=this.BasicData[a].map((t=>t.B)),e=this.BasicData[a].map((t=>t.C)),n=this.BasicData[a].map((t=>t.D)),_={color:["#0555CE","#5189dd","#81a9e6","#b5cdf1"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{orient:"horizontal",x:"right"},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:[{type:"category",data:[this.$t("EXIST"==this.currentBusiType?"语音":"话务"),this.$t("全媒体"),this.$t("邮件"),this.$t("工单"),this.$t("EXIST"==this.currentBusiType?"第三方":"其他")]}],yAxis:[{type:"value",name:this.$t("数量"),nameTextStyle:{color:"#222",fontSize:16,padding:[0,50,0,30]},splitLine:{show:!0,lineStyle:{type:"dashed"}}}],series:[{name:this.$t("优秀"),type:"bar",barWidth:14,emphasis:{focus:"series"},data:s},{name:this.$t("良好"),type:"bar",barWidth:14,barGap:"-100%",z:"2",emphasis:{focus:"series"},data:i},{name:this.$t("及格"),type:"bar",barWidth:14,barGap:"-100%",z:"3",emphasis:{focus:"series"},data:e},{name:this.$t("不及格"),type:"bar",barWidth:14,barGap:"-100%",z:"4",emphasis:{focus:"series"},data:n}]},l=this.$echarts.init(document.getElementById("chart6"));l.setOption(_),window.addEventListener("resize",(()=>{l.resize()}))}}},i()}catch(E){i(E)}}))},9995:function(t){t.exports="data:image/png;base64,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"},6052:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAUzSURBVFiF3Zk9bBRHFMd/b3Zv97y213DBAQwECqIUVhIpkSKRNCBREKV3R0pcpUmd4iraKFWALkoTUURKgWgoIhEpRZQClEgg5YNIgAHDYR++9X3szkuxvs/du7MPx0L5S3tztzPz5j/vvXlvZk6YAApCmQLzeNUGnklwJcCIxQCowWqEtQ5x6NNklSZlWgK607FkR8TKuJUSgW+ZEgdnR30TkoZhs1QhkjLxrhLUqzg8ZyaKCHB3NqkMYjQIiNjPhiyRvDTBh5cJwpiwbb7dghps1aW6sEw0qt1QgqoIVwijJtO7SWwQgUeNC1RF8v0zl6CWMYTsiwoU/0tybQQt6lRZkzJ2sC5jNlVkL8kBRAWKhOxTzSos80IvMzeJWf1jB08L7mJSq//Qqjy7PwnRwKMmy6wPJfjwMsFck307FVw4cuis43vfYAVgpfno8Ue2Xm9MQnLdY6134XRMrFdxwphwEqFOwXkH3Zqr5XAxmJ6fRA5AGBPq1W6M7frgc2YmDSU2alxDaaACyu1oQhMDiMXwnJk+glrGjSKCSYU2nzy9KypPUMDqnUnltBFFBFrGBdKPSomgKMNjokzjuPMHzzmO8xYWeszZKVWZS5O0WSweOvZ5Wme62dcCyr1m9PjaWP90kUqJAKi6ClKzTI3KrN7Cka8R80k62FbKV8BIf6mgyCLGXUSkMwFR2VqOgj+1cGaz/tdnIwkCvmVK4YVLmcK4xC9iTqcE2kruIal532Vry9Pbtr2XMWfHkQMQB4cyBcM83tjWynSXxLBHMu9UBdWOjHa77UeKeTxTbWyHoGS0szXPHM0JoqlZ03Kwftv0qDbwjEnShTII/+jrp7zjC+dTgmS1ZPmTxF7CcjurORJs8r0m9ltUGhltA8XgxJI/dfz0KIImwXUlwAymaHdupiSB951Ycf1jh1ZyfG219Wjl46TVqInBKR48cV3VLLa1pTa+uPn0n0sA/twbN43xr3SEq+BPHT8n4n1pkNj1Zt+Omy+qeQQlwJi84CxFE6iKC4IYKQ36mlr9I2k1agBqSVT5fUtz6RMnt9qynGbtVp8FABEJSf3T9ez0UJ8UixmaOaTXXzKrU97zSgfeAijMHjiMmjPtPqIgUlgSk0YGWwjPZyxAP+FRcNVgc1NcW1gCODCgRd8UZ68XD8/cESsnFaY7gymIOEvFuZOnttLfycxqt6Y7xgiowboaYaU4SLDQM0PDkLjno+Zd0B+xya8Aag1YEMyHqDk1LD6mGJG62gQjrGsdYkP/Si60wPaZuF32ExWlUl/9+1O1/YcfMXxVnH3zN5QwLz62ZarmbEh7YB1iE/o0Bys2156tqNVfsNxPoo2bff7SpwkJnXDh/cH+XvHoB/3k+uNo0qj9jLKK6k9R/Gjozif0aYqW8aLXODBiIhSPnnjQGcB2NSKDpsszp82+22zcOTJqvDaCZzw1lGlpMuZ82jsAqVm2Ra5vczGwisdAExLKtIyANgybo1v3P50cOyYXM+jH2wwtAA3DpoAagFKFiHjE3Cx3MzkWyNXOCM2mffXeWHYxWqqk5xIDIGXiIBh+wtdm86Io9/o1N0JjuYQB1buJbX4xjl8QdO9vuuFlPxu6ylRe0G5UHtwAboyd+S5ADZb9bLR/d8jIEknVJTdp7yWqLtXeS6VdO7jvBvIO7tkcfIFq0KK+Z6y2ELSocyFrwexWS1CqrO0lyc7lUc4N1yt//TZ8PyioLLO+7rGmmT33y0MNdt1jTZZZH0YO/g9XwL14ZS/RM4Pt4d8Q/wKZCex8VuG6OwAAAABJRU5ErkJggg=="}}]);