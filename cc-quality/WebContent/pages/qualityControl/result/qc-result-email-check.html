<!DOCTYPE html>
<html lang="en">
  <head>
    <title>质检内容(邮件)</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- 基础的 css js 资源 -->
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0"
    />
    <link
      rel="stylesheet"
      href="/cc-quality/static/css/qualityControl.css?v=1.0.1"
    />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
    <script
      type="text/javascript"
      src="/cc-quality/static/js/echarts2.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/cc-quality/static/js/echarts-liquidfill.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/cc-quality/static/js/echarsWordcloud.js"
    ></script>
    <script
      type="text/javascript"
      src="/cc-quality/static/js/my_i18n.js?v=2021110901"
    ></script>
    <script
      type="text/javascript"
      src="/cc-base/static/js/i18n.js?v=20140426"
    ></script>
    <script
      type="text/javascript"
      src="/cc-email/static/lib/ckeditor/ckeditor.js"
    ></script>
    <script src="/cc-quality/pages/qualityControl/mixin/aiQcMixin.js"></script>
    <style>
      .quality-item-content {
        height: calc(100vh - 168px);
        overflow: auto;
      }

      .right-panel-bottom {
        position: sticky;
        width: 100%;
        bottom: 0;
        height: 72px;
        background-color: #fff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        border-top: 1px solid #ebf1f8;
        justify-content: flex-end;
        z-index: 99;
      }

      .pointText {
        color: #868686;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
      }

      .pointValue {
        font-size: 20px;
        font-weight: bold;
        line-height: 22px;
        color: #f03838;
      }
      
      /* 过渡动画相关样式 */
      .expand-enter-active,
      .expand-leave-active {
        transition: all 0.3s ease;
        max-height: 300px;
        overflow: hidden;
      }
      .expand-enter,
      .expand-leave-to {
        max-height: 0;
        opacity: 0;
      }
      
      /* 父级项目样式 */
      .parent-item {
        cursor: pointer;
      }
      
      .parent-item .quality-inspection-item-title {
        display: flex;
        align-items: center;
      }
      
      .expand-icon {
        margin-right: 5px;
        transition: transform 0.3s ease;
      }
      
      .expand-icon.expanded {
        transform: rotate(90deg);
      }
      
      /* 子级项目样式 */
      .child-item {
        padding-left: 35px;
      }
    </style>
  </head>

  <body class="yq-page-full vue-box">
    <div id="emailCheck" class="flex yq-table-page">
      <div class="panel">
        <div class="left-panel">
          <div class="flex yq-card">
            <div class="card-header">
              <div class="head-title">{{getI18nValue('质检内容')}}</div>
              <img
                v-if="ORDER_URL"
                id="orderBtn"
                :title="getI18nValue('点击查看工单数据')"
                style="
                  height: 18px;
                  width: 18px;
                  margin-left: 8px;
                  cursor: pointer;
                "
                src="/cc-quality/static/images/order.png"
                alt=""
                @click="goOrderUrl"
              />
              <div class="yq-table-control">
                <el-button
                  type="primary"
                  size="small"
                  plain
                  icon="el-icon-tickets"
                  v-if="isAppeal"
                  @click="appealContent"
                >
                  {{getI18nValue('申诉内容')}}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  plain
                  icon="my-icon-appeal"
                  v-if="showComparison"
                  @click="complaintComparison"
                >
                  {{getI18nValue('申诉比对')}}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  icon="my-icon-data"
                  @click="dataAnalysis"
                >
                  {{getI18nValue('数据分析')}}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  icon="my-icon-cust"
                  v-if="isShowCustmgr=='1'"
                  @click="custormerMessage"
                >
                  {{getI18nValue('客户信息')}}
                </el-button>
              </div>
            </div>
            <components
              :is="'email-panel'"
              ref="emailPanel"
              :params="params"
              @setcustmsg="setcustmsg"
              @opensessionrecord="sessionRecords"
              @setsessionlist="setSessionList"
              @openemailinteraction="openEmailInteraction"
              @setemailhislist="setEmailHisList"
              @openremarkmsg="openRemarkMsg"
              @setfollowlist="setFollowList"
            ></components>
          </div>
        </div>
        <div class="right-panel">
          <el-collapse
            v-model="activeNames1"
            class="base-panel"
          >
          <el-collapse-item name="11">
            <template slot="title">
              <div class="base-panel-title">
                <div>{{getI18nValue('智能质检数据')}}</div>
                <div style="display: flex; align-items: center">
                  <div
                    class="media-time el-icon-time"
                    v-if="userMsg.ZN_QC_TIME"
                  >
                    {{userMsg.ZN_QC_TIME}}
                  </div>
                  <el-divider direction="vertical"></el-divider>
                  <div style="color: #868686; font-weight: normal">
                    {{activeNames1.indexOf('11')!='-1'?getI18nValue('收起'):getI18nValue('展示')}}
                  </div>
                </div>
              </div>
            </template>
            <div style="border-top: 1px solid #e8e8e8; padding: 16px 24px" v-if="ZN_QC_LIST.length>0">
              <div style="border-radius: 4px; border: 1px solid #e8e8e8">
                <div class="quality-inspection-item blue-white-bk">
                  <div class="quality-inspection-item-title fz-b">
                    {{getI18nValue('质检项')}}
                  </div>
                  <div class="quality-inspection-item-titleRight fz-n">
                    {{getI18nValue('分值')}}
                  </div>
                </div>
                <template v-for="(item, index) in ZN_QC_LIST">
                  <!-- 父级项目 -->
                  <div 
                    :class="['quality-inspection-item', {'parent-item': item.ITEM_TYPE === '1' && item.children && item.children.length > 0}]" 
                    @click="item.ITEM_TYPE === '1' && item.children && item.children.length > 0 ? toggleExpand(index) : null"
                  >
                    <div class="quality-inspection-item-title max-w300">
                      <i 
                        v-if="item.ITEM_TYPE === '1' && item.children && item.children.length > 0" 
                        :class="['el-icon-arrow-right', 'expand-icon', {'expanded': expandedItems[index]}]"
                      ></i>
                      <el-tag 
                            style="margin-right: 8px;" 
                            size="small" 
                            type="danger"
                            v-if="item.IS_VOTE == '1'"
                          >
                            一票否决
                          </el-tag>
                      {{getI18nValue(item.ITEM_NAME)}}
                    </div>
                    <div v-if="item.NODE_TYPE == '1'"
                      class="quality-inspection-item-titleRight green-text"
                      @click.stop
                    >
                      {{getI18nValue(item.SCORE)}}
                      <span v-if="canChangeScore" @click.stop="editScore(index)" class="el-icon-edit-outline can-click" :title="getI18nValue('编辑')"></span>
                    </div>
                  </div>
                  
                  <!-- 子级项目 -->
                  <transition-group name="expand" v-if="item.ITEM_TYPE === '1' && item.children && item.children.length > 0">
                    <div 
                      v-show="expandedItems[index]" 
                      v-for="(child, childIndex) in item.children" 
                      :key="childIndex"
                      class="quality-inspection-item child-item"
                    >
                      <div class="quality-inspection-item-title max-w300">
                        <el-tag 
                            style="margin-right: 8px;" 
                            size="small" 
                            type="danger"
                            v-if="item.IS_VOTE == '1'"
                          >
                            一票否决
                          </el-tag>
                        {{getI18nValue(child.ITEM_NAME)}}
                      </div>
                      <div v-if="child.NODE_TYPE == '1'"
                        class="quality-inspection-item-titleRight green-text"
                      >
                        {{getI18nValue(child.SCORE)}}
                        <span v-if="canChangeScore" @click="editScore(index, childIndex)" class="el-icon-edit-outline can-click" :title="getI18nValue('编辑')"></span>
                      </div>
                    </div>
                  </transition-group>
                </template>
              </div>
            </div>
          </el-collapse-item>
          </el-collapse>
          <el-collapse v-model="activeNames" class="base-panel" v-if="!editZnScore">
            <el-collapse-item name="1">
              <template slot="title">
                <div class="base-panel-title">
                  <div>{{getI18nValue('质检评定')}}</div>
                  <div style="display: flex; align-items: center">
                    <div class="baseflex">
                      <div class="pointText">{{getI18nValue('分数')}}：</div>
                      <div class="pointValue">{{sumbitform.point < 0 ? 0 : sumbitform.point}}</div>
                    </div>
                    <el-divider direction="vertical"></el-divider>
                    <div class="baseflex">
                      <el-radio v-model="sumbitform.case" label="1">
                        {{getI18nValue('优秀案例')}}
                      </el-radio>
                      <el-radio v-model="sumbitform.case" label="2">
                        {{getI18nValue('警示案例')}}
                      </el-radio>
                      <el-radio v-model="sumbitform.case" label="0">
                        {{getI18nValue('暂不设置')}}
                      </el-radio>
                    </div>
                    <el-divider direction="vertical"></el-divider>
                    <div style="color: #868686; font-weight: normal">
                      {{activeNames.indexOf('1')!='-1'?getI18nValue('收起'):getI18nValue('展示')}}
                    </div>
                  </div>
                </div>
              </template>
              <div class="quality-item-content">
                <components
                  :is="'quality-check'"
                  ref="qualityCheck"
                  :params="params"
                  @setscore="setScore"
                  :cansign="false"
                  :scorestar="scorestar"
                  :isappeal="isAppeal"
                ></components>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div class="right-panel-bottom">
            <el-button
              type="primary"
              class="mr-r16"
              @click="ZnSaveScore"
              v-if="onlyZnCheck || showSaveZnScore"
              :loading="Znloading"
            >
              {{ getI18nValue('保存智能质检分数') }}
            </el-button>
            <el-button
              type="primary"
              plain
              class="mr-r16"
              @click="save"
              v-if="canShowSave == 'true' && !onlyZnCheck"
              :loading="loading"
            >
              {{ getI18nValue('暂存') }}
            </el-button>
            <el-button
              type="primary"
              plain
              class="mr-r16"
              @click="transfer"
              v-if="isAppeal"
            >
              {{ getI18nValue('转派') }}
            </el-button>
            <el-button
              type="primary"
              plain
              class="mr-r16"
              @click="doSendBack"
              v-if="isAppeal"
            >
              {{ getI18nValue('退回') }}
            </el-button>
            <el-button type="primary" class="mr-r16" @click="beforeSumbit" :loading="loading" v-if="!onlyZnCheck">
              {{ isAppeal?getI18nValue('通过并提交'):getI18nValue('提交') }}
            </el-button>
          </div>
        </div>
      </div>
      <!-- 数据分析 -->
      <components
        :is="'call-analysic'"
        ref="callAnalysic"
        :params="params"
      ></components>
      <!-- 客户信息 -->
      <components
        :is="'custormer-message'"
        ref="custormerMsg"
        :params="params"
      ></components>
      <!-- 会话记录 -->
      <components
        :is="'session-record'"
        ref="sessionRecord"
        :params="params"
        :sessionlist="SessionList"
      ></components>
      <!-- 邮件交互 -->
      <components
        :is="'email-interaction'"
        ref="emailInteraction"
        :params="params"
        :emailhislist="emailHisList"
      ></components>
      <!-- 备注信息 -->
      <components
        :is="'remark-message'"
        ref="remarkMsg"
        :params="params"
        :followlist="followList"
      ></components>
      <!-- 编辑智能质检评分 -->
      <components
        :is="'edit-quality-check'"
        ref="editQualityCheck"
        :params="params"
        @save-score="saveScore"
      ></components>
    </div>
    <script>
      var emailCheck = new Vue({
        el: '#emailCheck',
        components: {
          'email-panel': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/emailPanel.vue'
          ),
          'call-analysic': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/callAnalysic.vue'
          ),
          'custormer-message': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/custormerMessage.vue'
          ),
          'session-record': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/sessionRecord.vue'
          ),
          'email-interaction': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/emailInteraction.vue'
          ),
          'remark-message': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/remarkMsg.vue'
          ),
          'quality-check': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/quality-check.vue'
          ),
          'edit-quality-check': httpVueLoader(
            '/cc-quality/pages/qualityControl/components/editQualityCheck.vue'
          )
        },
        data() {
          return {
            loading: false,
            params: {
              objid: '',
              classId: '',
              callType: '',
              serialId: '',
              znClassId: '',
              qcResultId: '',
              caseStatus: '',
              'record.QC_RESULT_ID': '',
              qcRecId: false,
              recState: '',
              isAppealModify: ''
            },
            callBackFun: '',
            custMsg: {},
            SessionList: [],
            emailHisList: [],
            followList: [],
            activeNames: ['1'],
            activeNames1: ['11'],
            userMsg: {
              ZN_QC_TIME: ''
            },
            ZN_QC_LIST: [],
            expandedItems: {}, // 用于跟踪哪些父级项目被展开
            canChangeScore: false, // 是否可以编辑分数
            Znloading: false, // 智能质检保存加载状态
            editZnScore: false, // 是否正在编辑智能质检分数
            sumbitform: {
              case: '0',
              point: 100
            },
            scorestar: {
              CAPACITY_EVALUATE1: 0,
              CAPACITY_EVALUATE2: 0,
              CAPACITY_EVALUATE3: 0,
              CAPACITY_EVALUATE4: 0,
              CAPACITY_EVALUATE5: 0
            },
            isAppeal: false,
            showComparison: false,
            canShowSave: 'true',
            isShowCustmgr: '0',
            ORDER_URL: '',
            Urlparams:{}
          }
        },
        mixins: [commonMethods],
        mounted() {
          this.getDict()
          yq.remoteCall(
            '/cc-quality/servlet/qcCommon?action=GetConstants',
            {}
          ).then((res) => {
            if (res) {
              if (res.isShowCustmgr) {
                this.isShowCustmgr = res.isShowCustmgr
              }
              if (res.isShowCustmgr) {
                this.isShowCustmgr = res.isShowCustmgr
              }
            }
          })
          yq.remoteCall(
            '/cc-quality/webcall?action=QcTypeDetailDao.typeDeatil',
            {
              taskType: '1',
              serialId: this.params.serialId
            }
          ).then((res) => {
            if (res && res.data) {
              this.ORDER_URL = res.data.ORDER_URL
            } else {
              this.ORDER_URL = ''
            }
          })
        },
        created() {
          this.canShowSave = yq.q('canShowSave', 'true')
          this.params.recState = yq.q('recState', '')
          this.params.isAppealModify = yq.q('isAppealModify', '')
          this.Urlparams.state = yq.q('state', '')
          this.params.objid = yq.q('objId', '')
          this.params.classId = yq.q('classId', '')
          this.params.serialId = yq.q('serialId', '')
          this.params.qcResultId = yq.q('qcResultId', '')
          this.params.znClassId = yq.q('znClassId', '')
          this.params.callType = yq.q('callType', '')
          this.params.caseStatus = yq.q('caseStatus', '')
          this.params.qcRecId = yq.q('qcRecId', false)
          this.params.except = yq.q('except', '')
          this.params.groupId = yq.q('groupId', '')
          this.params['record.QC_RESULT_ID'] = yq.q('qcResultId', '')
          this.callBackFun = yq.q('layer', '')
          if (
            yq.q('isAppeal', false) == 'true' ||
            yq.q('isAppeal', false) == true
          ) {
            this.isAppeal = true
          } else {
            this.isAppeal = false
          }
          let showComparison = yq.q('showComparison', false)
          if (showComparison == true || showComparison == 'true') {
            this.showComparison = true
          } else {
            this.showComparison = false
          }
          this.getMessage()
          window.closeLayer = this.closeLayer
        },
        watch: {},
        methods: {
          goOrderUrl() {
            top.popup.openTab(this.ORDER_URL, getI18nValue('工单详情'), {})
          },
          getDict() {
            let data = {
              params: this.params,
              controls: ['QcTaskObjDao.qcDetailsList']
            }
            yq.daoCall(data, null, {
              contextPath: '/cc-quality'
            }).then((res) => {
              this.ZN_QC_LIST = res['QcTaskObjDao.qcDetailsList']
                ? res['QcTaskObjDao.qcDetailsList'].data
                : []
            })
          },
          transfer() {
            // 转派
            var data = {
              data: this.params.objid,
              except: this.params.except,
              isGroup: '1',
              groupId: this.params.groupId,
              isTransfer: true,
              qcRecId: this.params.qcRecId,
              qcResultId: this.params.qcResultId,
              isNotDiv: 1,
              funName: 'transferLayer'
            }
            this.transferLayer = yq.layerShow(
              {
                type: 2,
                title: getI18nValue('质检转派'),
                area: ['900px', '680px'],
                maxmin: true,
                shadeClose: false,
                moveOut: true
              },
              '/cc-quality/pages/qualityControl/components/qc-appeal-transfer-select.html',
              data
            )
          },
          closeLayer(funName, val) {
            if (funName) {
              window.layer.close(this[funName])
              if (funName == 'backLayer' && val == '1') {
                parent.closeLayer(this.callBackFun)
              } else if (funName == 'transferLayer' && val == '1') {
                parent.closeLayer(this.callBackFun)
              }
            }
          },
          appealContent() {
            // 申诉内容
            var data = {
              qcRecId: this.params.qcRecId,
              qcResultId: this.params.qcResultId,
              isEdit: 0,
              funName: 'appealLayer'
            }
            this.appealLayer = yq.layerShow(
              {
                type: 2,
                title: getI18nValue('质检申诉'),
                area: ['900px', '680px'],
                maxmin: true,
                shadeClose: false,
                moveOut: true
              },
              '/cc-quality/pages/qualityControl/appeal/qc-result-appeal-dialog.html',
              data
            )
          },
          doSendBack() {
            var operState = '6'
            if(this.Urlparams.state =='91' ||  this.Urlparams.state =='1'){
              operState = '3'
            }
            var data = { isNotDiv: 1, funName: 'backLayer' }
            var url =
              '/cc-quality/pages/qualityControl/appeal/qc-result-appeal-back.html?qcRecId=' +
              this.params.qcRecId +
              '&qcResultId=' +
              this.params.qcResultId +
              '&operState=' +
              operState
            this.backLayer = yq.layerShow(
              {
                type: 2,
                title: getI18nValue('申诉退回'),
                area: ['900px', '680px'],
                maxmin: true,
                shadeClose: false,
                moveOut: true
              },
              url,
              data
            )
          },
          getMessage() {
            let data = {
              'qcTaskObj.ID': this.params.objid
            }
            yq.remoteCall(
              '/cc-quality/webcall?action=QcTaskObjDao.taskObjRecord',
              data
            ).then((res) => {
              if (res && res.data) {
                this.sumbitform.case = res.data.OBJ_TYPE
                this.scorestar.CAPACITY_EVALUATE1 = res.data.CAPACITY_EVALUATE1
                this.scorestar.CAPACITY_EVALUATE2 = res.data.CAPACITY_EVALUATE2
                this.scorestar.CAPACITY_EVALUATE3 = res.data.CAPACITY_EVALUATE3
                this.scorestar.CAPACITY_EVALUATE4 = res.data.CAPACITY_EVALUATE4
                this.scorestar.CAPACITY_EVALUATE5 = res.data.CAPACITY_EVALUATE5

                this.scorestar.ZN_RESULT = res.data.ZN_RESULT
              }
            })
          },
          complaintComparison() {
            // 申诉比对
            let data = { qcResultId: this.params.qcResultId }
            parent.yq.layerShow(
              {
                type: 2,
                title: getI18nValue('申诉比对'),
                offset: '20px',
                area: ['80%', '80%'],
                maxmin: true,
                full: true,
                shadeClose: false,
                moveOut: true
              },
              '/cc-quality/pages/qualityControl/appeal/qc-result-appeal-comprison.html',
              data
            )
          },
          setScore(score) {
            this.sumbitform.point = score
          },
          custormerMessage() {
            // 客户信息
            let urlData = {
              caller: this.custMsg.CUST_PHONE,
              sessionId: this.custMsg.CUST_PHONE,
              custPhone: this.custMsg.CUST_PHONE,
              sourceType: 'voice',
              inspection: '1'
            }
            this.$refs.custormerMsg.openDrawer(urlData)
          },
          dataAnalysis() {
            // 数据分析
            this.$refs.callAnalysic.openDrawer()
          },
          sessionRecords() {
            // 会话记录
            this.$refs.sessionRecord.openDrawer()
          },
          openEmailInteraction() {
            // 邮件交互
            this.$refs.emailInteraction.openDrawer()
          },
          openRemarkMsg() {
            // 备注信息
            this.$refs.remarkMsg.openDrawer()
          },
          setcustmsg(msg) {
            this.custMsg = msg
          },
          setSessionList(list) {
            this.SessionList = list
          },
          setEmailHisList(list) {
            this.emailHisList = list
          },
          setFollowList(list) {
            this.followList = list
          },
          save() {
            // 暂存
            let form = this.$refs.qualityCheck.getForm()
            form.TYPE = '1'
            form.OBJ_TYPE = this.sumbitform.case
            form['record.SERIAL_ID'] = this.params.serialId
            form['record.EXAM_GROUP_ID'] = this.params.groupId
            form['record.CALL_TYPE'] = '3'
            form['record.QC_RESULT_ID'] = this.params['record.QC_RESULT_ID']
            form.objId = this.params.objid
            form.classId = this.params.classId
            form.znClassId = this.params.znClassId
            form.serialId = this.params.serialId
            form.userType = this.params.userType
            form.qcRecId = this.params.qcRecId
            form = { ...form, ...this.scorestar }
            this.doApi(form)
          },
          beforeSumbit(){
            if(this.ZnSaveError){
              this.sumbit()
              return;
            }
            if(this.showSaveZnScore){
              this.$confirm(getI18nValue('是否保存智能质检分数'), getI18nValue('提示'), {
                confirmButtonText: getI18nValue('确定'),
                cancelButtonText: getI18nValue('取消'),
                distinguishCancelAndClose: true,
                type: 'warning'
              }).then(async () => {
                await this.ZnSaveScore()
                this.sumbit()
              }).catch((action) => {
                if(action === 'cancel'){
                  this.sumbit()
                }
              })
            }else{
              this.sumbit()
            }
          },
          sumbit() {
            // 提交
            let form = this.$refs.qualityCheck.getForm(true)
            if (!form) return;
            form.TYPE = ''
            form.OBJ_TYPE = this.sumbitform.case
            form['record.SERIAL_ID'] = this.params.serialId
            form['record.EXAM_GROUP_ID'] = this.params.groupId
            form['record.CALL_TYPE'] = '3'
            form['record.QC_RESULT_ID'] = this.params['record.QC_RESULT_ID']
            form.objId = this.params.objid
            form.classId = this.params.classId
            form.znClassId = this.params.znClassId
            form.serialId = this.params.serialId
            form.userType = this.params.userType
            form.qcRecId = this.params.qcRecId
            form.recState = this.params.recState
            if (this.canShowSave == 'false' || this.canShowSave == false) {
              form.qcRecId = yq.q('qcRecId', '')
              form.CLASS_ID = yq.q('classId', '')
              form.pk = yq.q('serialId', '')
            }
            form = { ...form, ...this.scorestar }
            this.doApi(form)
          },
          doApi(form) {
            let resultId = form['record.QC_RESULT_ID']
            if (!yq.isNull(resultId)) {
              form['record.BASE_SCORE'] = form['record.TOTAL_SCORE']
              this.$delete(form, 'record.TOTAL_SCORE')
              if (
                form.TYPE != '1' &&
                !(this.canShowSave == 'false' || this.canShowSave == false)
              ) {
                form.ARCHIVED = '1'
              } else {
                form.ARCHIVED = ''
              }
              this.updateResult(form)
            } else {
              this.addResult(form)
            }
          },
          addResult(form) {
            this.loading = true
            yq.remoteCall(
              '/cc-quality/servlet/qcRecord?action=addResult',
              form
            ).then((res) => {
              this.loading = false
              if (res.state == 1) {
                // 获取父级搜索--获取未质检数据
                yq.msg({
                  type: 'success',
                  message: res.msg
                })
                parent.getPage ? parent.getPage(this.nextOrder) : parent.layer.closeAll()
              } else {
                yq.msg({
                  type: 'error',
                  message: res.msg
                })
              }
            })
          },
          updateResult(form) {
            this.loading = true
            yq.remoteCall(
              '/cc-quality/servlet/qcRecord?action=updateResult',
              form
            ).then((res) => {
              this.loading = false
              if (res.state == 1) {
                // 获取父级搜索--获取未质检数据
                yq.msg({
                  type: 'success',
                  message: res.msg
                })
                if (this.canShowSave == 'false' || this.canShowSave == false) {
                  setTimeout(() => {
                    parent.closeLayer(this.callBackFun)
                  }, 1000)
                  return
                }
                parent.getPage ? parent.getPage(this.nextOrder) : parent.layer.closeAll()
              } else {
                yq.msg({
                  type: 'error',
                  message: res.msg
                })
              }
            })
          },
          setParam(url, dt) {
            let ul = url
            for (var k in dt) {
              ul += k + '=' + dt[k] + '&'
            }
            return ul
          },
           // 新增的展开/收起方法
           toggleExpand(index) {
            this.$set(this.expandedItems, index, !this.expandedItems[index])
          },
          nextOrder(obj) {
            if (obj && JSON.stringify(obj) != '{}') {
              this.$confirm(
                getI18nValue('是否质检下一条?'),
                getI18nValue('提示'),
                {
                  confirmButtonText: getI18nValue('确定'),
                  cancelButtonText: getI18nValue('取消'),
                  type: 'warning'
                }
              )
                .then(() => {
                  var data = {}
                  var url = ''
                  if (obj.CHANNEL_TYPE == '1') {
                    // 语音
                    data = {
                      groupId: obj.EXAM_GROUP_ID,
                      serialId: obj.SERIAL_ID,
                      objId: obj.OBJ_ID,
                      classId: obj.CLASS_ID,
                      znClassId: obj.ZN_CLASS_ID,
                      qcResultId: obj.QC_RESULT_ID,
                      layer: 'checkLayer'
                    }
                    url =
                      '/cc-quality/pages/qualityControl/result/qc-result-voice-check.html?'
                  } else if (obj.CHANNEL_TYPE == '2') {
                    // 全媒体
                    data = {
                      groupId: obj.EXAM_GROUP_ID,
                      serialId: obj.SERIAL_ID,
                      objId: obj.OBJ_ID,
                      classId: obj.CLASS_ID,
                      znClassId: obj.QC_RESULT_ID,
                      qcResultId: obj.QC_RESULT_ID,
                      layer: 'checkLayer'
                    }
                    url =
                      '/cc-quality/pages/qualityControl/result/qc-result-media-check.html?'
                  } else if (obj.CHANNEL_TYPE == '3') {
                    // 邮件
                    data = {
                      groupId: obj.EXAM_GROUP_ID,
                      serialId: obj.SERIAL_ID,
                      objId: obj.OBJ_ID,
                      classId: obj.CLASS_ID,
                      znClassId: obj.QC_RESULT_ID,
                      qcResultId: obj.QC_RESULT_ID,
                      layer: 'checkLayer'
                    }
                    url =
                      '/cc-quality/pages/qualityControl/result/qc-result-email-check.html?'
                  } else if (obj.CHANNEL_TYPE == '4') {
                    // 工单
                    data = {
                      groupId: obj.EXAM_GROUP_ID,
                      objId: obj.OBJ_ID,
                      znClassId: obj.ZN_CLASS_ID,
                      taskId: yq.q('taskId', ''),
                      qcResultId: obj.QC_RESULT_ID,
                      classId: obj.CLASS_ID,
                      layer: 'checkLayer'
                    }
                    url =
                      '/cc-quality/pages/qualityControl/result/qc-result-order-check.html?'
                  } else if (obj.CHANNEL_TYPE == '9') {
                    // 自定义
                    data = {
                      type: '1',
                      templateId: obj.THIRD_TEMPLATE_ID || obj.TEMPLATE_ID,
                      groupId: obj.EXAM_GROUP_ID,
                      serialId: obj.SERIAL_ID,
                      objId: obj.OBJ_ID,
                      classId: obj.CLASS_ID,
                      znClassId: obj.ZN_CLASS_ID,
                      qcResultId: obj.QC_RESULT_ID,
                      layer: 'checkLayer'
                    }
                    url =
                      '/cc-quality/pages/qualityControl/result/qc-result-thrity-check.html?'
                  }
                  location.href = this.setParam(url, data)
                })
                .catch((e) => {
                  console.log(e)
                  parent.closeLayer(this.callBackFun)
                })
            } else {
              setTimeout(() => {
                parent.closeLayer(this.callBackFun)
              }, 1000)
            }
          }
        }
      })
    </script>
  </body>
</html>
