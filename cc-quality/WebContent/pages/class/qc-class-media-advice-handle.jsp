<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="质检评论"></title>
	</EasyTag:override>
<EasyTag:override name="content">
		<form id="easyform" method="post" class="xssFilter" data-mars="QcClassDao.getQcClassAndJSON" style="margin: 0px 29px 0px;">
		  <input type="hidden" name="ID" value="${param.ID}">
  		  <table class="table table-edit table-vzebra mt-30" style="margin: 10px 0;">
			 <tbody >
                  <tr>
                  <td  width="100px" i18n-content="规则名称"></td>
                  <td><input type="text" name="CLASS_NAME" readonly="readonly"  class="form-control input-sm"></td>
               </tr>
            <tr>
                  <td width="105px" i18n-content="一级质检项"></td>
                  <td><input type="text" id="ITEM_ID1" name="ITEM_ID1_NAME" readonly="readonly" class="form-control input-sm"> </td>
               </tr>
            <tr>
                  <td width="105px" i18n-content="二级质检项"></td>
                  <td><input type="text"  id="ITEM_ID2" name="ITEM_ID2_NAME" readonly="readonly"  class="form-control input-sm"></td>
            </tr>
            <tr>
                <td  i18n-content="建议内容"></td>
               	<td>
               	    <textarea cols="5" rows="4" name="CONTENT" disabled="disabled" class="form-control input-sm" i18n-title="填写建议内容，字数上限255字" maxlength="255">${param.CONTENT}</textarea>
               	</td>
            </tr>
            <tr>
                  <td class="required" i18n-content="是否采纳"></td>
                  <td><select type="text"  id="IS_ADOPT" name="IS_ADOPT" data-rules="required" class="form-control input-sm">
          				<option value="" i18n-content="请选择"></option>
                        <option value="Y" data-class="label label-default" i18n-content="是"></option>
                        <option value="N" data-class="label label-success" i18n-content="否"></option>
	                  </select>
			    </td>
            </tr>
            <tr>
                <td class="required" i18n-content="处理结果"></td>
               	<td>
               	    <textarea cols="5" rows="4" name="DEAL_RESULT" data-rules="required|maxlength=250" class="form-control input-sm" i18n-title="填写建议内容，字数上限255字" maxlength="255"></textarea>
               	</td>
            </tr>
		</table>
			<div class="layer-foot text-c">
			   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="qcCommentsEdit.ajaxSubmitForm()" i18n-content="保存"></button>
			   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
		   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("qcCommentsEdit");
	$(function(){
		$("#easyform").render(); 
	});
	
	qcCommentsEdit.ajaxSubmitForm = function(){
		if(form.validate("#easyform")){
			qcCommentsEdit.insertData(); 
		}
	}
	
	qcCommentsEdit.insertData = function() {
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/qcClass?action=classAdviceEdit",data,function(result) { 
			
			if(result.state == 1){
				parent.layer.msg(getI18nValue(result.msg),{icon: 1},function(){
					
				});
				parent.QcClassAdvice.searchData();
				popup.layerClose('#easyform')
			}else{
				parent.layer.alert(getI18nValue(result.msg),{icon: 5});
			}
		});
	}
	
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>