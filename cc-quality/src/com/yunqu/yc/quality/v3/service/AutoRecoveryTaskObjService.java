package com.yunqu.yc.quality.v3.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.base.QueryFactory;
import com.yunqu.yc.quality.base.ServiceID;
import com.yunqu.yc.quality.utils.DateUtil;

public class AutoRecoveryTaskObjService extends IBaseService {
	
	private static Logger logger = CommonLogger.getLogger();
	
	//全局变量、如果上一个作业任务没有完成则不进入当前任务
	private static boolean IS_RUNNING = false;
	
	@Override
	public JSONObject invokeMethod(JSONObject params) throws ServiceException {
		logger.info(CommonUtil.getClassNameAndMethod(this) + "[AutoRecoveryTaskObjService] 定时自动回收任务名单服务开始执行" );
		String command = params.getString("command");
		JSONObject result = JsonUtil.createInfRespJson(params);
		try {
			
			//查找业务数据库
			JSONArray entArr = SchemaService.findEntBusiSchema();
			for(int i=0;i<entArr.size();i++) {
				JSONObject entJson = entArr.getJSONObject(i);
				logger.info(CommonUtil.getClassNameAndMethod(this) + "[AutoRecoveryTaskObjService] entJson:"+entJson );
				RecoveryTaskObj(entJson);
			}
			logger.info(CommonUtil.getClassNameAndMethod(this) + "[AutoRecoveryTaskObjService] 定时自动回收任务名单服务执行完成" );
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "处理完成");
			return result;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "系统异常");
			return result;
		}
	}
	
	
	
	private void RecoveryTaskObj (JSONObject entJson) throws Exception {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		String currTime = DateUtil.getCurrentDateStr();
		String schema = entJson.getString("SCHEMA_NAME");
		String busiOrderId = entJson.getString("BUSI_ORDER_ID");
		String entId = entJson.getString("ENT_ID");
		Date date = new Date();
		//查找质检任务,只查找启用中
		EasySQL taskSql = new EasySQL("select T1.*  from " + CommonUtil.getTableName(schema, "CC_QC_TASK") + " T1");
		taskSql.append("where 1=1");
		taskSql.append("AND T1.STATE='1'");
		taskSql.append("AND T1.RECOVERY_NUM IS NOT NULL ");
		taskSql.append(entId, "AND T1.ENT_ID=?");
		List<JSONObject> taskList = query.queryForList(taskSql.getSQL(), taskSql.getParams(), new JSONMapperImpl());
		logger.info("RecoveryTaskObj sql:"+taskSql.getSQL()+",param:"+JSON.toJSONString(taskSql.getParams()));
		if (taskList!=null && taskList.size()>0) {
			for (JSONObject taskJson:taskList) {
				// 分钟
				int recoveryNum = taskJson.getIntValue("RECOVERY_NUM");
				
				if (recoveryNum>0) {
					Date recoveryDate = DateUtil.addMinute(date, -recoveryNum);
					String recoveryDateStr = DateUtil.formatDate(recoveryDate);
					//更新名单数据
					taskSql = new EasySQL();
					taskSql.append("update "+CommonUtil.getTableName(schema, "CC_QC_TASK_OBJ")+" set RG_STATE = "+Constants.QC_STATE_EXTRACT );
					taskSql.append(",INSPECTOR = '',INSPECTOR_NAME ='',INSPECTOR_TIME='',SOURCE='',UPDATE_TIME=?  ");
					taskSql.append("where TASK_ID = ? AND RG_STATE= "+Constants.QC_STATE_UNDERWAY+" AND INSPECTOR_TIME<?");
					
					taskSql.addParams(currTime);
					taskSql.addParams(taskJson.getString("ID"));
					taskSql.addParams(recoveryDateStr);
					
					logger.info("updateTaskObj sql:"+taskSql.getSQL()+",param:"+JSON.toJSONString(taskSql.getParams()));
					query.execute(taskSql.getSQL(), taskSql.getParams());
				}
			}
		}
	}
	
	
	
	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME from "+Constants.getStatSchema()+(".cc_stat_table_info")+" where TABLE_ID = ?  ";
			tabInfo = QueryFactory.getReadQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception ex) {
			logger.error(ex.getMessage(), ex);
		}
		return tabInfo;
	}

	
	@Override
	public String getServiceId() {
		return ServiceID.JOB_AUTO_RECOVERY_TASK_OBJ;
	}

	@Override
	public String getName() {
		return "定时自动回收任务名单服务";
	}
}
