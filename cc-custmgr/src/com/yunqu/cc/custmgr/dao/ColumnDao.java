package com.yunqu.cc.custmgr.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.custmgr.base.BaseDAO;
import com.yunqu.cc.custmgr.base.Constants;
import com.yunqu.cc.custmgr.model.DBInfo;
import com.yunqu.cc.custmgr.model.SqlOperEntity;
import com.yunqu.cc.custmgr.utils.DbUtil;
import com.yunqu.cc.custmgr.utils.DicColumnHelper;
import com.yunqu.cc.custmgr.utils.StringUtil;
import com.yunqu.cc.custmgr.utils.TableColumnHelper;

public class ColumnDao extends BaseDAO {	
	private static ColumnDao instance = new ColumnDao();
	private ColumnDao(){
	}
	public static ColumnDao getInstance(){
		return instance;
	}
		
	public List<Map> getColumnListByTableId(String tableId,DBInfo info, HttpServletRequest request) {
		EasySQL sql = new EasySQL("select * from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(" and COL_NAME!='BIGDATA' AND IS_PERSON = 'Y'");
		sql.appendSort(" COL_INDEX ","ASC");
		getLogger().debug("ColumnDAO.getColumnListByTableId() tableId = '" + tableId+"'");
		getLogger().debug("ColumnDAO.getColumnListByTableId() sql = " + sql);
		try {
			return DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(), sql.getParams()),request);
		} catch (SQLException e) {
			getLogger().error("[ColumnDAO.getColumnListByTableId][Exception]"+e.getMessage());
		}
		return null;
	} 
	public List<Map> getColumnListByTableIdUnit(String tableId,DBInfo info, HttpServletRequest request) {
		EasySQL sql = new EasySQL("select * from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(" and COL_NAME!='BIGDATA' AND IS_PERSON = 'N'");
		sql.appendSort(" COL_INDEX ","ASC");
		getLogger().debug("ColumnDAO.getColumnListByTableId() tableId = '" + tableId+"'");
		getLogger().debug("ColumnDAO.getColumnListByTableId() sql = " + sql.getSQL()+","+JSON.toJSONString(sql.getParams()));
		try {
			return DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(), sql.getParams()),request);
		} catch (SQLException e) {
			getLogger().error("[ColumnDAO.getColumnListByTableId][Exception]"+e.getMessage());
		}
		return null;
	} 
	public List<Map> getColumnListByTableId(String tableId,DBInfo info) {
		EasySQL sql = new EasySQL("select * from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append("Y"," and IS_PERSON=?");
		sql.append(" and COL_NAME!='BIGDATA'");
		sql.appendSort(" COL_INDEX ","ASC");
		getLogger().debug("ColumnDAO.getColumnListByTableId() tableId = '" + tableId+"'");
		getLogger().debug("ColumnDAO.getColumnListByTableId() sql = " + sql.getSQL());
		try {
			return DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			getLogger().error("[ColumnDAO.getColumnListByTableId][Exception]"+e.getMessage());
		}
		return null;
	} 
	
	public List<Map> getNotManualColumnsByTableId(String tableId,DBInfo info) {
		EasySQL sql = new EasySQL("select * from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append("Y"," and IS_PERSON=?");
		for(int i = 0;i<Constants.NONE_MANUAL_FILEDS.length;i++){
			sql.append(Constants.NONE_MANUAL_FILEDS[i]," and COL_NAME != ?");
		}
		sql.append(" and COL_NAME!='BIGDATA'");
		sql.appendSort(" COL_INDEX ","ASC");
		getLogger().debug("ColumnDAO.getColumnListByTableId() tableId = '" + tableId+"'");
		getLogger().debug("ColumnDAO.getColumnListByTableId() sql = " + sql);
		try {
			return DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(), sql.getParams()));
		} catch (SQLException ex) {
			getLogger().error("根据tableId获取列数据出错："+ex.getMessage());
		}
		return null;
	} 
	
	
	public Map getColumnById(String columnId,String schema) {
		String sql = "select * from "+schema+".C_CM_COLUMN where ID='" + columnId+"'";
		List<Map> mapl= null;
		try {
			mapl = DbUtil.fromEasyRowmap(jdbc.queryForList(sql, new Object[]{}));
		} catch (SQLException e) {
			getLogger().error("根据columnId获取列信息异常："+e.getMessage());
		}
		if(mapl!=null&&!mapl.isEmpty()){
			return mapl.get(0);
		}			
		return null;
	}
	
	public String getColumnTypeByIdAndName(String tableId,String columnName,DBInfo info){
		EasySQL sql = new EasySQL("select COL_TYPE from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(columnName," and COL_NAME= ?");
		getLogger().debug("ColumnDAO.getColumnListByTableId() tableId = '" + tableId+"'");
		getLogger().debug("ColumnDAO.getColumnListByTableId() sql = " + sql);
		try {
			return jdbc.queryForString(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error("getColumnTypeByIdAndName has error:"+e.getMessage());
		}
		return null;
	}
	
	@SuppressWarnings("rawtypes")
	public List<String> getColumnNamesByTableId(String tableId,DBInfo dbinfo){
		List<Map> list = getColumnListByTableId(tableId,dbinfo);
		List<String> columns = new ArrayList<String>();
		for(Map map:list){
			columns.add((String)map.get("COL_NAME"));
		}
		return columns;
	}
	
	public String getColumnNameById(String columnId,String schema) {
		Map map = getColumnById(columnId,schema);
		if(map!=null){
			return StringUtil.nullToSpace(map.get("COL_NAME"));
		}
		return "";
	}
	
	public String addColumnInTable(String tableId,  Map<String, String> column,DBInfo info) {
		List<SqlOperEntity> sqlList = new ArrayList<SqlOperEntity>();
		String tableName = TableDao.getInstance().getTableDsNameById(tableId,info);
		sqlList.add(new SqlOperEntity("1",getAddColumnSql(tableId, column,"special",info)));
		sqlList.add(new SqlOperEntity("",getCreateColumnSql(tableName, column,info.getSchema())));
		return DbUtil.executeBatch(jdbc, sqlList);
	}
	
	public String[] getQueryColumnListByTableId(String tableId,DBInfo info) {
		EasySQL sql = new EasySQL("select COL_NAME,COL_TITLE from "+info.getSchema()+".C_CM_COLUMN");
		sql.append(tableId," where TABLE_ID = ?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(DictConstants.DICT_SY_YN_Y," and COL_IS_QUERY = ?");
		sql.append(" order by COL_INDEX desc");
		List<Map> queryColList =null;
		try {
			queryColList = DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			getLogger().error("根据tableId获取列信息异常："+e.getMessage());
		}
		if(null == queryColList || queryColList.size() == 0){
		    return null;
		}
		String[] nameAndTitleArrs = new String[2];
		StringBuilder colNames = new StringBuilder();
		StringBuilder colTitles = new StringBuilder();
		for(int i = 0; i < queryColList.size(); i++)
		{
		    Map colMap = (Map)queryColList.get(i);
		    String colName = (String)colMap.get("COL_NAME");
		    String colTitle = StringUtil.nullToSpace(colMap.get("COL_TITLE"));
		    colNames.append(colName).append(",");
		    colTitles.append(colTitle).append(",");
		}
		colNames.deleteCharAt(colNames.length() - 1);
		colTitles.deleteCharAt(colTitles.length() - 1);
		
		nameAndTitleArrs[0] = colNames.toString();
		nameAndTitleArrs[1] = colTitles.toString();
		return nameAndTitleArrs;
	}
	
	public EasyRecord getAddColumnSql(String tableId,Map<String,String> column,DBInfo info) {
		String columnId = IDGenerator.getIDByCurrentTime(16);
		EasyRecord record = new EasyRecord(info.getSchema()+".C_CM_COLUMN",new String[]{"ID"});
		record.set("ID", columnId);
		record.set("TABLE_ID", tableId);
		record.set("COL_NAME", column.get("columnName"));
		record.set("COL_TITLE", column.get("columnTitle"));
		record.set("COL_TYPE", column.get("columnType"));
		record.set("COL_SIZE", column.get("columnSize"));
		record.set("COL_IS_EDIT", column.get("colIsEdit"));
		String columnIndex = column.get("columnIndex");
		if(StringUtils.isNotBlank(columnIndex)){
			record.set("COL_INDEX",Integer.parseInt(columnIndex));
		}
		record.set("ENT_ID", info.getEntId());
		record.set("BUSI_ORDER_ID", info.getBusiOrderId());
		return record;
	}
	
	public String getCreateColumnSql(String tableName, Map<String, String> column,String schema) {
		String sql = "alter table "+schema+"."+ tableName +" add "+ column.get("columnName") +" ";
		String columnType = column.get("columnType");
		sql += DbUtil.getColumnDbTypeName(columnType);
		String columnSize = column.get("columnSize");
		
		if(TableColumnHelper.canSetLength(columnType)&&columnSize.length() > 0) {
			sql += "("+  columnSize+")";
		}
		sql += " null";
		return sql;
	}
	
	public EasyRecord getAddColumnSql(String tableId, Map<String, String> column,String type,DBInfo info) {
		String columnId = IDGenerator.getIDByCurrentTime(16);
		EasyRecord record = new EasyRecord(info.getSchema()+".C_CM_COLUMN",new String[]{"ID"});
		record.setPrimaryValues(new Object[]{columnId});
		record.put("TABLE_ID", tableId);
		record.put("COL_NAME", column.get("columnName"));
		record.put("COL_TITLE", column.get("columnTitle"));
		record.put("COL_TYPE", column.get("columnType"));
		record.put("COL_SIZE", column.get("columnSize"));
		record.put("COL_INDEX",column.get("columnIndex"));
		record.put("COL_DISPLAY", column.get("columnDisplay"));	
        record.put("COL_IS_QUERY", DictConstants.DICT_SY_YN_Y);
        record.put("ENT_ID", info.getEntId());
        record.put("BUSI_ORDER_ID", info.getBusiOrderId());
		if("special".equals(type)){//手动添加字段,可以编辑
			record.put("COL_IS_EDIT", DictConstants.DICT_SY_YN_Y);
		}
		return record;
	}
	

	
	public String updateColumnById(String columnId, Map<String, String> params,DBInfo info) {
		
		String columnTitle = params.get("columnTitle");
		
		String columnIndex = params.get("columnIndex");
		String columnDisplay = params.get("columnDisplay");
		String columnProperty = params.get("columnProperty");
		String columnName = params.get("columnName");
		String columnIsQuery = params.get("columnIsQuery");
		String columnIsDisable = params.get("columnIsDisable");
		String columnType = params.get("columnType");
		String columnSize = params.get("columnSize");
		columnSize = StringUtil.isNull(columnSize) ? "100" : columnSize;//文本类型默认为100
		String tableId = params.get("tableId");
		
		List<SqlOperEntity> sqlList = new ArrayList<SqlOperEntity>();
		EasyRecord columnRec = new EasyRecord(info.getSchema()+".C_CM_COLUMN",new String[]{"ID"});
		columnRec.setPrimaryValues(new Object[]{columnId});
		columnRec.put("COL_TITLE", columnTitle);
		if(StringUtils.isNotBlank(columnIndex)){
			columnRec.put("COL_INDEX", Integer.parseInt(columnIndex));
		}
		columnRec.put("COL_TYPE", columnType);
		columnRec.put("COL_NAME", columnName);
		columnRec.put("COL_IS_QUERY", StringUtil.isNull(columnIsQuery)?"N":columnIsQuery);
		columnRec.put("COL_SIZE", columnSize);
		columnRec.put("COL_IS_DISABLE", StringUtil.isNull(columnIsDisable)?"N":columnIsDisable);
		columnRec.put("COL_DISPLAY", columnDisplay);
		columnRec.put("COL_PROPERTY", columnProperty);
		columnRec.put("COL_PROPERTY_KEY", params.get("columnPropertyKey"));
		columnRec.put("COL_IS_ENCRY", params.get("colIsEncry"));
		columnRec.put("DATA_ENCRY_WAY", params.get("dataEncryWay"));
		columnRec.put("ENT_ID", info.getEntId());
		columnRec.put("BUSI_ORDER_ID",info.getBusiOrderId());
		getLogger().info("更新CM_COLUMN字段属性SQL:" + columnRec.getColumns());
//		super.getJdbc().execute(sql);
		sqlList.add(new SqlOperEntity("2",columnRec));
		//修改CM_TABLE_INFO_表id中的属性的类型，不能允许修改表中的名称
		//修改字段类型
		//ALTER TABLE table_name  ALTER COLUMN column_name new_data_type
		String englishName = TableDao.getInstance().getTableDsNameById(tableId,info);
        //首先改表名
		Map colMap = getColumnById(columnId,info.getSchema());
		String oColumnName = (String)colMap.get("COL_NAME");
		if(!columnName.equalsIgnoreCase(oColumnName)){
			String alterNameSql ="ALTER TABLE "+info.getSchema()+"."+englishName +" RENAME COLUMN "+oColumnName+" TO "+columnName;
			getLogger().info("修改表"+englishName+"列名SQL:" + alterNameSql);
			sqlList.add(new SqlOperEntity("1",alterNameSql));
		}
		//其次，修改字段类型
		if(needAlter(columnId,params,info.getSchema())){//判断属性类型是否有改动，sybase数据库不允许无改动的modify	
			String realColumnType = DbUtil.getColumnDbTypeName(columnType);
			String alterColumnSQL = "ALTER TABLE  "+info.getSchema()+"." + englishName + "  MODIFY " + columnName  + " " + realColumnType;
			if("varchar".equals(realColumnType) || "varchar2".equals(realColumnType)) { //修改为字符类型 
				alterColumnSQL += "("+  columnSize+") ";
			}
			getLogger().info("修改表"+englishName+"中的列属性SQL:" + alterColumnSQL);
			sqlList.add(new SqlOperEntity("",alterColumnSQL));
		}
		return DbUtil.executeBatch(jdbc, sqlList);
	}
	
	private boolean needAlter(String columnId, Map<String, String> params,String schema){
		boolean isNeed = false;
		Map colMap = getColumnById(columnId,schema);
		String columnType = params.get("columnType");
		String columnSize = StringUtil.nullToSpace(params.get("columnSize"));
		
		String oColumnType = StringUtil.nullToSpace(colMap.get("COL_TYPE"));
		String oColumnSize = (String)colMap.get("COL_SIZE");
		if(!columnType.equals(oColumnType))//类型修改，可以
			isNeed = true;
		else {
			// 或者是文本类型修改长度，其他类型不涉及长度修改
			String realColumnType = DbUtil.getColumnDbTypeName(columnType);
			if("varchar".equals(realColumnType) || "varchar2".equals(realColumnType)) { //修改为字符类型 
				if(!columnSize.equals(oColumnSize))
					isNeed = true;
			}
		}
		
		getLogger().info("columnType:" + columnType+",columnSize:"+columnSize+",oColumnType:"+oColumnType+",oColumnSize:"+oColumnSize);
		System.out.println("columnType:" + columnType+",columnSize:"+columnSize+",oColumnType:"+oColumnType+",oColumnSize:"+oColumnSize);
		return isNeed;
	}
	
	public String[] getTelColumnsByTableId(String tableId,DBInfo info) {
		List<NameValuePair> pairs = getTelColNameValueByTableId(tableId, info);
		String[] columns = { };
		columns = new String[pairs.size()];
		for(int i=0;i<pairs.size();i++){
			columns[i] = pairs.get(i).getName();
		}		
		return columns;
	}
	
	public List<NameValuePair> getTelColNameValueByTableId(String tableId,DBInfo info) {
		EasySQL sql = new EasySQL("select COL_NAME,COL_TITLE from "+info.getSchema()+".c_CM_COLUMN");
		sql.append(tableId," where TABLE_ID=?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(" and (COL_TITLE like '%电话%' or COL_TITLE like '%手机%')");
		getLogger().info("根据tableId：" + tableId + "获取对应电话列SQL：" + sql);
		List<NameValuePair> pairs = new ArrayList<NameValuePair>();
		try {
			List<EasyRow> list = getJdbc().queryForList(sql.getSQL(),sql.getParams());
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row:list){
					NameValuePair value = new BasicNameValuePair(row.getColumnValue("COL_NAME"),row.getColumnValue("COL_TITLE"));
					pairs.add(value);
				}
			}			
		} catch (SQLException e) {
			logger.error("根据tableId获取对应电话列出错："+e.getMessage());
		}	
		return pairs;
	}
	
	@SuppressWarnings("rawtypes")
	public void deleteColumnFromTable(String columnId,DBInfo info) {
		List<String> sqlList = new ArrayList<String>();
		Map column = getColumnById(columnId,info.getSchema());
		String tableName = "";
		// column.get("TABLE_ID") 返回对象在sybase下是Integer, 在oracle下却是BigDecimal
		// 所以，这里要如此周转一下．或者将Object转成Integer有更简便的写法？
		String tableId = StringUtil.nullToSpace(column.get("TABLE_ID"));
		Map map = TableDao.getInstance().getTableById(tableId,info);
		if(map!=null)  tableName = (String)map.get("TBL_NAME");
		String columnName = (String)column.get("COL_NAME");
		sqlList.add("delete from "+info.getSchema()+".c_CM_COLUMN where ID="+ columnId);
		
		// 判断是否要在删除字段定义时，执行drop column语句,默认为删除物理表字段
		if("yes".equals(ConfigUtil.getString(Constants.APP_NAME,"NEED_DROP_COLUMN","yes"))) {
			String dbType = DbUtil.getDbType();
			if(dbType.equalsIgnoreCase("sybase")) {
				// 在sybase下，alter drop语句不能写在事务处理中，好像总有一些异常现象...
				sqlList.add("alter table "+info.getSchema()+"."+ tableName +" drop "+columnName);
			} else if(dbType.equalsIgnoreCase("oracle")){
				// 在oracle下，drop语法字段上要加一个括号
				sqlList.add("alter table "+info.getSchema()+"."+ tableName +" drop ("+columnName+")");
			}
		}
		DbUtil.writeBatchSqlLog("删除表字段",sqlList);
		// super.executeInTransaction(sqlList);
		// 由于sybase下drop column的事务执行问题，删除字段时改用batchUpdate
		DbUtil.batchCommitSQL(jdbc, sqlList);
	}
	
	
	public String getDicColumnByTableId(String tableId,DBInfo info){
        EasySQL sql = new EasySQL("select ID,COL_NAME,COL_TITLE,COL_PROPERTY from "+info.getSchema()+".C_CM_COLUMN");
        sql.append(tableId," where TABLE_ID =?",false);
        sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
        sql.append(" AND COL_DISPLAY in (4,5,6)");
		StringBuilder respString = new StringBuilder();
		try {
			List<Map> list = DbUtil.fromEasyRowmap(jdbc.queryForList(sql.getSQL(),sql.getParams()));
			for(int i = 0 ; i < list.size(); i++){
				Map map = (Map)list.get(i);
				String colTitle = StringUtil.nullToSpace(map.get("COL_TITLE"));
				String colId = String.valueOf(map.get("ID"));
				String colProperty = (String)map.get("COL_PROPERTY");
				String colName = (String)map.get("COL_NAME");
				String start = "";
				String middle = "";
				String end = "";
				//if(!"PROVINCE".equals(colName) && !"CITY".equals(colName)){ //省份，城市字典字段不进行数据控制
					start = "<item text=\"" + colTitle + "\" id=\"C-" + tableId + "-" + colId + "\">";
					middle = getDicValueBySQL(tableId,colId,colProperty);
					end = "</item>";
				//}
				respString.append(start);
				if(!StringUtil.isNull(middle.toString())) respString.append(middle.toString());
				respString.append(end);
			}
		} catch (SQLException e) {
			logger.error("根据tableId获取字典列："+e.getMessage());
		}
		return respString.toString();
	}
	
	public String getDicValueBySQL(String tableId ,String colId,String colProperty){
		getLogger().info("获取字典表属性SQL>>>" + colProperty);
		StringBuilder respString = new StringBuilder();
		try {
			List<Map> list = DbUtil.fromEasyRowmap(jdbc.queryForList(colProperty,new Object[]{}));
			for(int i = 0 ; i < list.size(); i++){
				Map map = (Map)list.get(i);
				String dicKey = (String)map.get("DIC_KEY");
				String dicValue = (String)map.get("DIC_VALUE");
				String middle = "<item text=\"" + dicKey + "\" id=\"D-"+ tableId + "-" + colId + "-" + dicValue + "\"></item>";
				respString.append(middle);
			}
		} catch (SQLException e) {
			logger.error("根据tableId获取字典属性："+e.getMessage());
		}
		
		return respString.toString();
	}
	
	public String[] getAclColumnListByTableId(String tableId,String userCode,DBInfo info) {
		EasySQL sql = new EasySQL();
		sql.append("select distinct TBL_COL_NAME,TBL_COL_VALUE from "+info.getSchema()+".C_CM_DATA_ACL");
		sql.append(tableId," where TBL_ID =?",false);
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(userCode," and AGENT_ACC =?");
		sql.append(" ORDER BY TBL_COL_NAME");
		List<EasyRow> aclColList =null;
		String[] nameAndValueArrs = new String[2];
		try {
			aclColList = jdbc.queryForList(sql.getSQL(),sql.getParams());
			if(null == aclColList || aclColList.size() == 0){
			    return null;
			}
			Map<String,StringBuilder> col_values = new HashMap<String,StringBuilder>();
			for(EasyRow acl:aclColList){
				String col_name = acl.getColumnValue("TBL_COL_NAME");
				String col_value = acl.getColumnValue("TBL_COL_VALUE");
				if(col_values.get(col_name) != null)
					col_values.get(col_name).append(",").append(col_value);
				else{
					StringBuilder colValues = new StringBuilder(col_value);
					col_values.put(col_name, colValues);
				}
			}
			StringBuilder colNames = new StringBuilder();
			StringBuilder colValues = new StringBuilder();
			for(String colName:col_values.keySet()){
				colNames.append(colName).append(",");
			    colValues.append(col_values.get(colName)).append(";");
			}
			colNames.deleteCharAt(colNames.length() - 1);
			colValues.deleteCharAt(colValues.length() - 1);
			
			nameAndValueArrs[0] = colNames.toString();
			nameAndValueArrs[1] = colValues.toString();
		} catch (SQLException e) {
			logger.error("根据tableId获取列权限控制出错："+e.getMessage());
		}
		
		return nameAndValueArrs;
	}
	
	public Map<String,Map<String,String>> getSelectMap(String tableId,DBInfo info){

		Map<String,Map<String,String>> selectMap = new HashMap<String,Map<String,String>>();
		String sql = "select COL_NAME,COL_DISPLAY,COL_PROPERTY from "+info.getSchema()+".C_CM_COLUMN "
				+ " where TABLE_ID=?";
		try {
			List<EasyRow> rows = jdbc.queryForList(sql, new Object[]{tableId});
			if(rows!=null){
				for(EasyRow row:rows){
					String name = row.getColumnValue("COL_NAME");
					String display = row.getColumnValue("COL_DISPLAY");
					String property = row.getColumnValue("COL_PROPERTY");
					if("7".equals(display)&&property!=null){
						JSONObject dictJson = JSON.parseObject(property.split(";")[0]);
						if(dictJson!=null){
							String dictGroup = dictJson.getJSONObject("dict").getString("code");
							selectMap.put(name, DicColumnHelper.getDictValueMap(dictGroup,info.getEntId()));
						}							
					}else if("PROVINCE".equals(name)||"CITY".equals(name)){
						Map<String,Object> dictMap = DicColumnHelper.getCityColumnData(row.toMap(),info);
						if(dictMap!=null){
							Map<String,String> dictValueMap = new HashMap<String,String>();
							for(String cityCode:dictMap.keySet()){
								dictValueMap.put((String)dictMap.get(cityCode), cityCode);								
							}
							selectMap.put(name, dictValueMap);
						}
					}else if(StringUtil.isNotNull(display)&&"4,5,6,".contains(display)&&property!=null){
						//String dictSql = property.split(",")[0];
						//String[] split = property.split(",");
						try {
							if("SEX".equals(name)||"CERTIFICATE_TYPE".equals(name)||"PROVINCE".equals(name)||"CITY".equals(name)
									||"VIP_LEVEL".equals(name)){
								continue;
							}
							JSONObject parseObject = JSONObject.parseObject(property);
							Map<String,String> dictValueMap = new HashMap<String,String>();
							if(parseObject!=null){
								for (Map.Entry entry : parseObject.entrySet()) {
						             Object key = entry.getKey();
						             Object value = entry.getValue();
						             if(key!=null&&value!=null){
						            	 dictValueMap.put((String)value,(String)key);
						             }
						        }
								selectMap.put(name, dictValueMap);
							}
						    /*List<EasyRow> dictRow = jdbc.queryForList(dictSql, new Object[]{});
						    if(dictRow!=null){
								Map<String,String> dictValueMap = new HashMap<String,String>();
								for(EasyRow dic:dictRow){
									String dictKey = dic.getColumnValue(1);
									String dictText = dic.getColumnValue(2);
									dictValueMap.put(dictText, dictKey);
								}
								selectMap.put(name, dictValueMap);
							}*/
						} catch (Exception ex) {
							logger.error("执行字典表sql出错："+ex.getMessage());
						}
						
					}
				}
			}
		} catch (SQLException ex) {
			logger.error("获取字典列value与text映射map出错："+ex.getMessage());
		}		
		return selectMap;
	}
	
	public Map<String,Map<String,String>> getJlMap(String tableId,DBInfo info){

		Map<String,Map<String,String>> selectMap = new HashMap<String,Map<String,String>>();
		String sql = "select COL_NAME,COL_DISPLAY,COL_PROPERTY from "+info.getSchema()+".C_CM_COLUMN "
				+ " where TABLE_ID=? and COL_DISPLAY = '8' and col_name !='PROVINCE' and  col_name !='CITY' ";
		String sqlSon = "select COL_NAME,COL_DISPLAY,COL_PROPERTY from "+info.getSchema()+".C_CM_COLUMN "
				+ " where TABLE_ID=? and COL_DISPLAY = '9' and col_name !='PROVINCE' and col_name !='COUNTY_CODE' and  col_name !='CITY' ";
		try {
			List<EasyRow> list = jdbc.queryForList(sql, new Object[]{tableId});
			if(list!=null&&list.size()>0){
				for (EasyRow easyRow : list) {
					String colStr = easyRow.getColumnValue("COL_PROPERTY");
					String name = easyRow.getColumnValue("COL_NAME");
					//String property = easyRow.getColumnValue("COL_PROPERTY");
					if(StringUtils.isNotBlank(colStr)){
						JSONObject parseObject = new JSONObject();
						try {
							parseObject = JSONObject.parseObject(colStr);
						} catch (Exception e) {
							continue;
						}
						String code = parseObject.getString("CODE");
						EasySQL sql2 = new EasySQL();
						sql2.append(" SELECT ID,NAME from " + info.getSchema()+(".C_CF_COMMON_TREE") +" WHERE 1=1 ");
						sql2.append(info.getBusiOrderId()," AND BUSI_ORDER_ID = ? ",false);
						sql2.append(info.getEntId()," AND EP_CODE = ? ",false);
						sql2.append(code," AND TYPE = ? ",false);
						sql2.append(" AND P_ID = '2000' ");
						List<EasyRow> queryForList = jdbc.queryForList(sql2.getSQL(),sql2.getParams());
						if(queryForList!=null&&queryForList.size()>0){
							Map<String,String> dictValueMap = new HashMap<String,String>();
							for (EasyRow row : queryForList) {
								dictValueMap.put(row.getColumnValue("NAME"), row.getColumnValue("ID"));
							}
							selectMap.put(name,dictValueMap );
						}
					}
				}
			}
			
			List<EasyRow> listSon = jdbc.queryForList(sqlSon, new Object[]{tableId});
			if(listSon!=null&&listSon.size()>0){
				for (EasyRow easyRow : listSon) {
					String colStr = easyRow.getColumnValue("COL_PROPERTY");
					String name = easyRow.getColumnValue("COL_NAME");
					if(StringUtils.isNotBlank(colStr)){
						JSONObject parseObject = new JSONObject();
						String sqlParent = "select COL_PROPERTY from "+info.getSchema()+".C_CM_COLUMN "
								+ " where TABLE_ID=? and COL_NAME = ? and col_name !='PROVINCE' and  col_name !='CITY' ";
						String colProperty = jdbc.queryForString(sqlParent, new Object[]{tableId,colStr});
						try {
							parseObject = JSONObject.parseObject(colProperty);
						} catch (Exception e) {
							continue;
						}
						String code = parseObject.getString("CODE");
						EasySQL sql2 = new EasySQL();
						sql2.append(" SELECT ID,NAME from " + info.getSchema()+(".C_CF_COMMON_TREE") +" WHERE 1=1 ");
						sql2.append(info.getBusiOrderId()," AND BUSI_ORDER_ID = ? ",false);
						sql2.append(info.getEntId()," AND EP_CODE = ? ",false);
						sql2.append(code," AND TYPE = ? ",false);
						sql2.append(" AND P_ID != '2000' ");
						List<EasyRow> queryForList = jdbc.queryForList(sql2.getSQL(),sql2.getParams());
						if(queryForList!=null&&queryForList.size()>0){
							Map<String,String> dictValueMap = new HashMap<String,String>();
							for (EasyRow row : queryForList) {
								dictValueMap.put(row.getColumnValue("NAME"), row.getColumnValue("ID"));
							}
							selectMap.put(name,dictValueMap );
						}
					}
				}
			}
			
		} catch (SQLException ex) {
			logger.error("获取字典列value与text映射map出错："+ex.getMessage());
		}		
		return selectMap;
	}
	
	public String getTableId(DBInfo info) {
		EasySQL sql = new EasySQL("select ID from "+info.getSchema()+".C_CM_TABLE"+" where 1=1 ");
		sql.append(info.getEntId()," and ENT_ID=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append("Y"," and IS_DEFAULT = ?");
		sql.append("4"," and TBL_TYPE = ?");
		sql.append("01"," and ENABLE_STATUS = ?");
		String tableId = "";
		try {
			EasyRow row = jdbc.queryForRow(sql.getSQL(), sql.getParams());
			if(row!=null){
				tableId = row.getColumnValue("ID");
			}
		} catch (Exception e) {
			getLogger().error("获取客户资料表ID异常："+e.getMessage(),e);
		}
		 
		return tableId;
	}
	
	public String getCustmgr(DBInfo info,String tableId,String mobile) {
		EasySQL sql = new EasySQL("select ID from "+info.getSchema()+".C_CM_CUSTINFO"+" where 1=1 ");
		sql.append(info.getEntId()," and EP_CODE=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(tableId," and TABLIE_ID = ?");
		sql.append(mobile," and  ( MOBILE = ? OR ");
		sql.append(mobile," MOBILE2 = ? ) ");
		String custmgrId = "";
		try {
			EasyRow row = jdbc.queryForRow(sql.getSQL(), sql.getParams());
			if(row!=null){
				custmgrId = row.getColumnValue("ID");
			}
		} catch (Exception e) {
			getLogger().error("获取客户资料ID异常："+e.getMessage(),e);
		}
		 
		return custmgrId;
	}

	  
	public List<JSONObject> getMapData(String tableId, DBInfo info, EasyQuery query) {
		query.setMaxRow(50000);
		EasySQL sql = new EasySQL("select CUST_CODE,MOBILE, ID,MOBILE2,CUST_PHONE,CUST_PHONE2 from "+info.getSchema()+".C_CM_CUSTINFO"+" where 1=1 ");
		sql.append(info.getEntId()," and EP_CODE=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(tableId," and TABLIE_ID = ?");
		sql.append(" and MOBILE is not null and MOBILE!='' ");
		sql.append(" and CUST_CODE is not null and CUST_CODE!='' ");
		try {			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			return list;			
		} catch (Exception e) {
			 ColumnDao.logger.error("获取客户资料ID异常："+e.getMessage(),e);
			 return null;
		}
	}
		
	public String getCustData(String tableId, DBInfo info, EasyQuery query,String filed,String value, String mobileEncry) {
	
		EasySQL sql = new EasySQL("select ID from "+info.getSchema()+".C_CM_CUSTINFO"+" where 1=1 ");
		sql.append(info.getEntId()," and EP_CODE=?");
		sql.append(info.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(tableId," and TABLIE_ID = ?");
		//sql.append(" and MOBILE is not null and MOBILE!='' ");
		sql.append(" and CUST_CODE is not null and CUST_CODE!='' ");
		sql.append(value," and "+filed+" = ?");
		if (StringUtils.isNotBlank(mobileEncry)) {
			sql.append(mobileEncry," or  "+filed+" = ?");
		}
		try {		
			
			return query.queryForString(sql.getSQL(), sql.getParams());			
		} catch (Exception e) {
			 ColumnDao.logger.error("获取客户资料ID异常："+e.getMessage(),e);
			 return null;
		}
	}
}
