-- 20230915

create table C_CM_STAT_DAY
(
   ID                   VARCHAR2(64)         not null,
   ENT_ID               VARCHAR2(64)         default NULL,
   BUSI_ORDER_ID        VARCHAR2(64)         default NULL,
   TABLE_ID             VARCHAR2(64)         default NULL,
   DATE_ID              INTEGER              default NULL,
   TOTAL_COUNT          INTEGER              default NULL,
   ADD_COUNT            INTEGER              default NULL,
   ACCESS_COUNT         INTEGER              default NULL,
   DAY_COUNT            INTEGER              default NULL,
   WEEK_COUNT           INTEGER              default NULL,
   MONTH_COUNT          INTEGER              default NULL,
   SILENT_HALF_YEAR_COUNT INTEGER              default NULL,
   SILENT_ONE_YEAR_COUNT INTEGER              default NULL,
   SILENT_ONE_MORE_YEAR_COUNT INTEGER              default NULL,
   EVAL_COUNT           INTEGER              default NULL,
   GOOD_COUNT           INTEGER              default NULL,
   BAD_COUNT            INTEGER              default NULL,
   constraint PK_C_CM_STAT_DAY primary key (ID)
);

comment on table C_CM_STAT_DAY is
'客户日快照信息表，每日对库里的客户进行一次汇总，每天每个客户模板一条记录';

comment on column C_CM_STAT_DAY.ID is
'ID';

comment on column C_CM_STAT_DAY.ENT_ID is
'企业ID';

comment on column C_CM_STAT_DAY.BUSI_ORDER_ID is
'订购ID';

comment on column C_CM_STAT_DAY.TABLE_ID is
'模版ID';

comment on column C_CM_STAT_DAY.DATE_ID is
'日期';

comment on column C_CM_STAT_DAY.TOTAL_COUNT is
'总客户数';

comment on column C_CM_STAT_DAY.ADD_COUNT is
'新增客户';

comment on column C_CM_STAT_DAY.ACCESS_COUNT is
'有接入的客户数';

comment on column C_CM_STAT_DAY.DAY_COUNT is
'日活客户数';

comment on column C_CM_STAT_DAY.WEEK_COUNT is
'周活客户数';

comment on column C_CM_STAT_DAY.MONTH_COUNT is
'月活客户数';

comment on column C_CM_STAT_DAY.SILENT_HALF_YEAR_COUNT is
'沉默半年客户数';

comment on column C_CM_STAT_DAY.SILENT_ONE_YEAR_COUNT is
'沉默1年客户数';

comment on column C_CM_STAT_DAY.SILENT_ONE_MORE_YEAR_COUNT is
'沉默1年以上客户数';

comment on column C_CM_STAT_DAY.EVAL_COUNT is
'参评客户数';

comment on column C_CM_STAT_DAY.GOOD_COUNT is
'好评客户数';

comment on column C_CM_STAT_DAY.BAD_COUNT is
'差评客户数';

create index IDX_C_CM_STAT_DAY_1 on C_CM_STAT_DAY(ENT_ID, BUSI_ORDER_ID);
create index IDX_C_CM_STAT_DAY_2 on C_CM_STAT_DAY(TABLE_ID);
create index IDX_C_CM_STAT_DAY_3 on C_CM_STAT_DAY(DATE_ID);

create table C_CM_CUST_STAT_INFO
(
   ID                   VARCHAR2(64)         not null,
   ENT_ID               VARCHAR2(64)         default NULL,
   BUSI_ORDER_ID        VARCHAR2(64)         default NULL,
   TABLE_ID             VARCHAR2(64)         default NULL,
   CREATE_TIME          VARCHAR2(19)         default NULL,
   BATCH_ID             INTEGER              default NULL,
   PROVINCE             VARCHAR2(64)         default NULL,
   SEX                  VARCHAR2(64)         default NULL,
   VIP_LEVEL            VARCHAR2(64)         default NULL,
   EDUCATION            VARCHAR2(64)         default NULL,
   USER_TYPE            VARCHAR2(64)         default NULL,
   SATISF_ID            VARCHAR2(64)         default NULL,
   CUST_COUNT           INTEGER              default NULL,
   constraint PK_C_CM_CUST_STAT_INFO primary key (ID)
);

create index IDX_C_CM_CUST_STAT_INFO_1 on C_CM_CUST_STAT_INFO(ENT_ID, BUSI_ORDER_ID);
create index IDX_C_CM_CUST_STAT_INFO_2 on C_CM_CUST_STAT_INFO(TABLE_ID);
create index IDX_C_CM_CUST_STAT_INFO_3 on C_CM_CUST_STAT_INFO(PROVINCE);

comment on table C_CM_CUST_STAT_INFO is
'客户信息批次统计表';

comment on column C_CM_CUST_STAT_INFO.ID is
'id';

comment on column C_CM_CUST_STAT_INFO.ENT_ID is
'企业ID';

comment on column C_CM_CUST_STAT_INFO.BUSI_ORDER_ID is
'订购ID';

comment on column C_CM_CUST_STAT_INFO.TABLE_ID is
'模版ID';

comment on column C_CM_CUST_STAT_INFO.CREATE_TIME is
'创建时间';

comment on column C_CM_CUST_STAT_INFO.BATCH_ID is
'批次ID：1-30';

comment on column C_CM_CUST_STAT_INFO.PROVINCE is
'省份';

comment on column C_CM_CUST_STAT_INFO.SEX is
'性别：读取数据字典：SEX';

comment on column C_CM_CUST_STAT_INFO.VIP_LEVEL is
'VIP等级：读取数据字典：CUSTOMER_VIP_LEVEL';

comment on column C_CM_CUST_STAT_INFO.EDUCATION is
'学历：读取数据字典：CC_CUSTMGR_EDUCATION';

comment on column C_CM_CUST_STAT_INFO.SATISF_ID is
'满意度ID：0未评价，1非常满意，2满意，3一版，4不满意';

comment on column C_CM_CUST_STAT_INFO.USER_TYPE is
'用于类型：个人，企业';

comment on column C_CM_CUST_STAT_INFO.CUST_COUNT is
'客户数';

create table C_CM_CUST_STAT_CHANNEL
(
   ID                   VARCHAR2(64)         not null,
   ENT_ID               VARCHAR2(64)         default NULL,
   BUSI_ORDER_ID        VARCHAR2(64)         default NULL,
   TABLE_ID             VARCHAR2(64)         default NULL,
   CREATE_TIME          VARCHAR2(19)         default NULL,
   BATCH_ID             INTEGER              default NULL,
   SOURCE_TYPE          VARCHAR2(255)        default NULL,
   PROVINCE             VARCHAR2(64)         default NULL,
   SEX                  VARCHAR2(64)         default NULL,
   VIP_LEVEL            VARCHAR2(64)         default NULL,
   EDUCATION            VARCHAR2(64)         default NULL,
   SATISF_ID            VARCHAR2(64)         default NULL,
   CUST_COUNT           INTEGER              default NULL,
   constraint PK_C_CM_CUST_STAT_CHANNEL primary key (ID)
);


create index IDX_C_CM_CUST_STAT_CHANNEL_1 on C_CM_CUST_STAT_CHANNEL(ENT_ID, BUSI_ORDER_ID);
create index IDX_C_CM_CUST_STAT_CHANNEL_2 on C_CM_CUST_STAT_CHANNEL(TABLE_ID);
create index IDX_C_CM_CUST_STAT_CHANNEL_3 on C_CM_CUST_STAT_CHANNEL(PROVINCE);

comment on table C_CM_CUST_STAT_CHANNEL is
'客户渠道批次统计表';

comment on column C_CM_CUST_STAT_CHANNEL.ID is
'id';

comment on column C_CM_CUST_STAT_CHANNEL.ENT_ID is
'企业ID';

comment on column C_CM_CUST_STAT_CHANNEL.BUSI_ORDER_ID is
'订购ID';

comment on column C_CM_CUST_STAT_CHANNEL.TABLE_ID is
'模版ID';

comment on column C_CM_CUST_STAT_CHANNEL.CREATE_TIME is
'创建时间';

comment on column C_CM_CUST_STAT_CHANNEL.BATCH_ID is
'批次ID：1-30';

comment on column C_CM_CUST_STAT_CHANNEL.SOURCE_TYPE is
'渠道类型，voice-语音 media-在线 email-邮件';

comment on column C_CM_CUST_STAT_CHANNEL.PROVINCE is
'省份';

comment on column C_CM_CUST_STAT_CHANNEL.SEX is
'性别：读取数据字典：SEX';

comment on column C_CM_CUST_STAT_CHANNEL.VIP_LEVEL is
'VIP等级：读取数据字典：CUSTOMER_VIP_LEVEL';

comment on column C_CM_CUST_STAT_CHANNEL.EDUCATION is
'学历：读取数据字典：CC_CUSTMGR_EDUCATION';

comment on column C_CM_CUST_STAT_CHANNEL.SATISF_ID is
'满意度ID：0未评价，1非常满意，2满意，3一版，4不满意';

comment on column C_CM_CUST_STAT_CHANNEL.CUST_COUNT is
'客户数';




create table C_CM_CUST_STAT_DAY
(
   ID                   VARCHAR2(64)         not null,
   ENT_ID               VARCHAR2(64)         default NULL,
   BUSI_ORDER_ID        VARCHAR2(64)         default NULL,
   TABLE_ID             VARCHAR2(64)         default NULL,
   CREATE_TIME          VARCHAR2(19)         default NULL,
   MONTH_ID             VARCHAR2(64)         default NULL,
   DATE_ID              INTEGER              default NULL,
   SOURCE_TYPE          VARCHAR2(64)         default NULL,
   CHANNEL_KEY          VARCHAR2(64)         default NULL,
   CHANNEL_ID           VARCHAR2(64)         default NULL,
   CUST_ID              VARCHAR2(64)         default NULL,
   ACCESS_COUNT         INTEGER              default NULL,
   ACCESS_SUCCESS_COUNT INTEGER              default NULL,
   ACCESS_SUCCESS_TIME  INTEGER              default NULL,
   ACCESS_SUCCESS_PER   VARCHAR2(64)         default NULL,
   INVITE_COUNT         INTEGER              default NULL,
   EVAL_COUNT           INTEGER              default NULL,
   GOOD_COUNT           INTEGER              default NULL,
   BAD_COUNT            INTEGER              default NULL,
   constraint PK_C_CM_CUST_STAT_DAY primary key (ID)
);
create index IDX_C_CM_CUST_STAT_DAY_1 on C_CM_CUST_STAT_DAY(ENT_ID, BUSI_ORDER_ID);
create index IDX_C_CM_CUST_STAT_DAY_2 on C_CM_CUST_STAT_DAY(TABLE_ID);
create index IDX_C_CM_CUST_STAT_DAY_3 on C_CM_CUST_STAT_DAY(DATE_ID);

comment on table C_CM_CUST_STAT_DAY is
'客户日接入分析表,每天按客户汇总渠道接入记录，每个客户每天可能存在多条记录';

comment on column C_CM_CUST_STAT_DAY.ID is
'id';

comment on column C_CM_CUST_STAT_DAY.ENT_ID is
'企业ID';

comment on column C_CM_CUST_STAT_DAY.BUSI_ORDER_ID is
'订购ID';

comment on column C_CM_CUST_STAT_DAY.TABLE_ID is
'模版ID';

comment on column C_CM_CUST_STAT_DAY.CREATE_TIME is
'创建时间';

comment on column C_CM_CUST_STAT_DAY.MONTH_ID is
'月份ID';

comment on column C_CM_CUST_STAT_DAY.DATE_ID is
'日期ID';

comment on column C_CM_CUST_STAT_DAY.SOURCE_TYPE is
'渠道类型，voice-语音 media-在线 email-邮件';

comment on column C_CM_CUST_STAT_DAY.CHANNEL_KEY is
'渠道标识，语音存99，在线存渠道key，邮件存邮箱';

comment on column C_CM_CUST_STAT_DAY.CHANNEL_ID is
'渠道ID';

comment on column C_CM_CUST_STAT_DAY.CUST_ID is
'客户画像ID';

comment on column C_CM_CUST_STAT_DAY.ACCESS_COUNT is
'接入数';

comment on column C_CM_CUST_STAT_DAY.ACCESS_SUCCESS_COUNT is
'接通数(邮件:有处理人算接通:c_email_session.HANDLE_ACC字段)';

comment on column C_CM_CUST_STAT_DAY.ACCESS_SUCCESS_TIME is
'接通时长(邮件时长:c_email_session.PROCESS_SECONDS字段)';

comment on column C_CM_CUST_STAT_DAY.ACCESS_SUCCESS_PER is
'呼损率';

comment on column C_CM_CUST_STAT_DAY.INVITE_COUNT is
'邀评数';

comment on column C_CM_CUST_STAT_DAY.EVAL_COUNT is
'评价数';

comment on column C_CM_CUST_STAT_DAY.GOOD_COUNT is
'好评数';

comment on column C_CM_CUST_STAT_DAY.BAD_COUNT is
'差评数';


create table C_CM_UNIT_STAT_DAY
(
   ID                   VARCHAR2(64)         not null,
   ENT_ID               VARCHAR2(64)         default NULL,
   BUSI_ORDER_ID        VARCHAR2(64)         default NULL,
   TABLE_ID             VARCHAR2(64)         default NULL,
   CREATE_TIME          VARCHAR2(19)         default NULL,
   MONTH_ID             VARCHAR2(64)         default NULL,
   DATE_ID              INTEGER              default NULL,
   SOURCE_TYPE          VARCHAR2(64)         default NULL,
   UNIT_ID              VARCHAR2(64)         default NULL,
   UNIT_NAME            VARCHAR2(64)         default NULL,
   ACCESS_COUNT         INTEGER              default NULL,
   ACCESS_SUCCESS_COUNT INTEGER              default NULL,
   ACCESS_SUCCESS_TIME  INTEGER              default NULL,
   INVITE_COUNT         INTEGER              default NULL,
   EVAL_COUNT           INTEGER              default NULL,
   GOOD_COUNT           INTEGER              default NULL,
   BAD_COUNT            INTEGER              default NULL,
   constraint PK_C_CM_UNIT_STAT_DAY primary key (ID)
);

comment on table C_CM_UNIT_STAT_DAY is
'企业日接入分析表';

comment on column C_CM_UNIT_STAT_DAY.ID is
'id';

comment on column C_CM_UNIT_STAT_DAY.ENT_ID is
'企业ID';

comment on column C_CM_UNIT_STAT_DAY.BUSI_ORDER_ID is
'订购ID';

comment on column C_CM_UNIT_STAT_DAY.TABLE_ID is
'模版ID';

comment on column C_CM_UNIT_STAT_DAY.CREATE_TIME is
'创建时间';

comment on column C_CM_UNIT_STAT_DAY.MONTH_ID is
'月份ID';

comment on column C_CM_UNIT_STAT_DAY.DATE_ID is
'日期ID';

comment on column C_CM_UNIT_STAT_DAY.SOURCE_TYPE is
'渠道类型，1-语音 2-在线 3-邮件';

comment on column C_CM_UNIT_STAT_DAY.UNIT_ID is
'单位ID';

comment on column C_CM_UNIT_STAT_DAY.UNIT_NAME is
'单位名称';

comment on column C_CM_UNIT_STAT_DAY.ACCESS_COUNT is
'接入数';

comment on column C_CM_UNIT_STAT_DAY.ACCESS_SUCCESS_COUNT is
'接通数(邮件:有处理人算接通:c_email_session.HANDLE_ACC字段)';

comment on column C_CM_UNIT_STAT_DAY.ACCESS_SUCCESS_TIME is
'接通时长(邮件时长:c_email_session.PROCESS_SECONDS字段)';

comment on column C_CM_UNIT_STAT_DAY.INVITE_COUNT is
'邀评数';

comment on column C_CM_UNIT_STAT_DAY.EVAL_COUNT is
'评价数';

comment on column C_CM_UNIT_STAT_DAY.GOOD_COUNT is
'好评数';

comment on column C_CM_UNIT_STAT_DAY.BAD_COUNT is
'差评数';
create index IDX_C_CM_UNIT_STAT_DAY_1 on C_CM_UNIT_STAT_DAY(ENT_ID, BUSI_ORDER_ID);
create index IDX_C_CM_UNIT_STAT_DAY_2 on C_CM_UNIT_STAT_DAY(TABLE_ID);
create index IDX_C_CM_UNIT_STAT_DAY_3 on C_CM_UNIT_STAT_DAY(DATE_ID);
