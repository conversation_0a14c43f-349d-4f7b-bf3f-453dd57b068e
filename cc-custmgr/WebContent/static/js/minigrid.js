
/**
* 自制迷你Grid，用于客户资料选择对话框
* 修改自龙泰OSS项目的mini-grid.js，做了一些优化及完善
* By ZhouHuan, 2010-12-14 
*
*/
var MiniGrid = function(id) {
	MiniGrid.$[id] = this;
	this.id = id;
	this.rowClickHandler = function(row) {}
	this.rowDblClickHandler = function(row) {}

	var focusRow = null;
	var focusRowId = null
	var renderRowFocus = function(row) {
		if(!checkNull(row.attr("id"))&&row.attr("id").indexOf("header")>-1) {
			return false;
		}
		if(focusRow != null) {
			focusRow.css("background-color", "#ffffff");
		}
		focusRow = row;
		focusRowId = row.attr("id");
		row.css("background-color", "#D3EDFA");
	}

	var rowOnClick = function() {
		renderRowFocus($(this));
		MiniGrid.$[id].rowClickHandler(focusRow);
	}

	var rowOnDblClick = function() {
		renderRowFocus($(this));
		MiniGrid.$[id].rowDblClickHandler(focusRowId);
	}


	this.getSelectedRow = function() {
		return focusRow;
	}
	
	this.getRow = function(i) {
		// Add At HeYuan, 2009.11.3
		return $("#"+ this.id +" tr[id='']")[i];
		/*$("#"+ this.id +" tr[id='']").each(
			function(j) {
				if(i == j) {
					alert($(this))
					return $(this);	
				}	
			}
		);*/
	}

	this.insertRow = function(position, value) {
		if(focusRow != null) { 
			if(position == "before") {
				this.createRow(value).insertBefore(focusRow);
			} else {
				this.createRow(value).insertAfter(focusRow);
			}
		}
	}

	this.deleteRow = function() {
		if(focusRow != null) {
			focusRow.remove();
			focusRow = null;
		}
	}

	this.appendRow = function(value) {
		$("#"+ this.id +" tr:last-child").after(this.createRow(value));
	}

	this.moveUp = function(value) {
		if(focusRow != null) {
			var row = focusRow.prev()[0];
			if(row.id == "") {
				focusRow.insertBefore(row);
			}
		}
	}

	this.moveDown = function(value) {
		if(focusRow != null && focusRow.next()[0]!=null) {
			focusRow.insertAfter(focusRow.next()[0]);
		}
	}

	this.focusRow = function(i) {
		$("#"+ this.id +" tr[id='']")[i].click();
	}

	this.createRow = function(value) {
		var new_row = $("#"+this.id+"_header").clone();
		new_row.children().each(
				function(i) {
					$(this).html(value ? value[i]+'' : '&nbsp');
				}	
		);
		new_row.attr("id", "");
		new_row.css("background-image", "");
		new_row.css("background-color", "white");
		new_row.bind("click", rowOnClick);
		new_row.bind("dblclick", rowOnDblClick); 
		return new_row;
	}

	this.removeAll = function() {
		$("#"+ this.id +" tr[id='']").each(
			function() {
				$(this).remove();
			}
		)
		focusRow = null;
	}

	this.title = [];
	this.setTitle = function(v) {
		this.title = v
	}
	this.titleWidth = 150;
	this.setTitleWidth = function(v) {
		this.titleWidth = v
	}

	this.data = [];
	this.setData = function(v) {
		this.data = v;
	}

	this.size = ['100%', 400];
	this.setSize = function(w, h) {
		this.size[0] = w || this.size[0];
		this.size[1] = h || this.size[1];
	}

	this.getRowCount = function() {
		var count = 0;
		$("#"+ this.id +" tr[id='']").each(
			function() {
				count++;
			}
		);
		return count;
	}

	this.getSelectedRowIndex = function() {
		var count = 0;
		var index = 0;
		$("#"+ this.id +" tr[id='']").each(
			function() {
				if($(this).css("background-color") != "white") {
					index = count;
				}
				count++;
			}
		);
		return index;
	}

	
	this.refresh = function() {
		this.removeAll();
		for(var i=0; i<this.data.length; i++) {
			this.appendRow(this.data[i]);
		}
	}

	this.reload = function() {
		$("#"+ this.id).replaceWith(this.html()); 
	}


	this.show = function() {
		document.write(this.html());
		this.init();
	}


	this.html = function() {
		if(this.title.length == 0) {
			layer.alert("请设置Grid的标题头！");
			return false;
		}
		var html = [];
		html.push("<div class='ibox-content table-responsive' style='margin-right:15px;height:285px'>");
		html.push("<table class='table table-auto table-bordered table-hover table-condensed' width='"+ this.size[0]+"' id='"+ this.id +"'>");
		html.push("<thead><tr id='"+ this.id +"_header'>");
		for(var i=0; i<this.title.length; i++) {
			html.push("<th width="+(this.titleWidth[i] ? this.titleWidth[i] : 100)+" nowrap>"+this.title[i]+"</th>");
		}
		html.push("</tr></thead>");
		for(var i=0; i<this.data.length; i++) {
			html.push("<tr bgcolor='white' id='"+this.data[i][this.title.length-1]+"'>");
			for(var j=0; j<this.title.length; j++) {
				if (this.data[i][j]){
					html.push("<td nowrap>"+ this.data[i][j] +"</td>");
				} else {
					html.push("<td>&nbsp;</td>");
				}
			}
			html.push("</tr>");
		}
		html.push("</table>");
		html.push("</div>");
		return html.join("");
	}


	this.init = function() {
		$("#"+ this.id +" tr").bind("click", rowOnClick).bind("dblclick", rowOnDblClick); 
	}
}
MiniGrid.$ = {}
