<!DOCTYPE html>
<html>

<head>
    <title>客户管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<!-- 基础的 css js 资源 -->
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
    <link rel="stylesheet" href="/cc-custmgr/static/css/custmgr.css">
    <style>
        #mainFrame {
            white-space: nowrap;
        }

        .tab-box {
            display: flex;
            gap: 48px;
            width: 100%;
            box-sizing: border-box;
            padding: 0 24px;
            border-bottom: 2px solid #F2F4F7;
            background: #fff;
            border-radius: 4px;
        }

        .tab-box .tab-item {
            cursor: pointer;
            color: #868686;
            font-size: 16px;
            line-height: 56px;
            height: 56px;
            box-sizing: border-box;
        }

        .tab-box .tab-item.active {
            color: #0555CE;
            font-weight: bold;
            border-bottom: 4px solid #0555CE;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="mainFrame" class="yq-page flex" v-loading="pageNav.loading">
        <div style="width: 100%; position: relative;height: 58px;">
            <div class="tab-box">
                <div v-for="(item, index) in panes" :key="index" class="tab-item"
                    :class="active == item.id ? 'active': ''" @click="handleClick(item)"
                    v-if="resList[item.role] == 'Y' || !item.role">
                    {{ item.label }}
                </div>
            </div>
        </div>
        <div id="content" style="height: 100%; width: 100%;"></div>
    </div>
    <!-- <script>
        var mainFrame = new Vue({
            el: '#mainFrame',
            data: function () {
                return {
                    pageNav: {
                        loading: false,
                    },
                    panes: [
                        { label: getI18nValue('客户管理'), key: 1, url: '/cc-custmgr/pages/custmgrNew/index.html', id: 'custmgrNew' },
                        { label: getI18nValue('信息表配置'), role: 'cc-custmgr-xxbpz', key: 2, url: '/cc-custmgr/pages/custmgrNew/tableConfig/index.html', id: 'tableConfig' },
                        { label: getI18nValue('客户标签配置'), role: 'cc-custmgr-khbq', key: 3, url: '/cc-custmgr/pages/custmgrConfig/Config.jsp', id: 'custmgrConfig' },
                        { label: getI18nValue('客户标签规则'), role: 'cc-custmgr-khbq-rule', key: 9, url: '/cc-custmgr/pages/custmgrNew/labelRules/index.html', id: 'labelRules' },
                        { label: getI18nValue('客户分析'), role: 'cc-custmgr-khfx', key: 4, url: '/cc-custmgr/pages/stat/customerStat.jsp', id: 'customerStat' },
                        { label: getI18nValue('客户分析new'), role: 'cc-custmgr-khfx', key: 5, url: '/cc-custmgr/pages/custmgrNew/custAnalysis/index.html', id: 'custAnalysis', iframe: true },
                        { label: getI18nValue('客户资料参数配置'), role: 'cc-base-system-xtpz-cspz-custmgr', key: 6, url: '/cc-custmgr/pages/param/param-cust.html', id: 'param-cust' },
                        { label: getI18nValue('外部公告'), role: 'cc-custmgr-wbgg', key: 7, url: '/cc-custmgr/pages/notice/noticeList.html', id: 'noticeList' },
                        { label: getI18nValue('信息发布'),  role: 'cc-custmgr-xxfb',key: 8, url: '/cc-custmgr/pages/notice/infoManage.html', id: 'infoManage' },
                    ],
                    active: 'custmgrNew',
                    resList: {}, // 权限字典
                }
            },
            mounted() {
                this.getcheckRes()
                this.loadHtml(this.panes[0].url)
            },
            methods: {
                // 查询权限
                getcheckRes() {
                    let ids = [
                        'cc-custmgr-xxbpz', 'cc-custmgr-khbq', 'cc-custmgr-khfx', 'cc-base-system-xtpz-cspz-custmgr', 'cc-custmgr-func-14',
                        'cc-custmgr-func-2', 'cc-custmgr-func-3', 'cc-custmgr-func-8', 'cc-custmgr-func-9', 'cc-custmgr-func-11',
                        'cc-custmgr-khzcsh', 'cc-custmgr-wbgg', 'cc-custmgr-func-1', 'cc-custmgr-func-10', 'cc-custmgr-func-13', 'cc-custmgr-khbq-rule','cc-custmgr-xxfb'
                    ]
                    $.ajax({
                        url: `/cc-base/servlet/menu?action=checkRes&ids=${ids.join(';')}`,
                        type: 'GET',
                        complete: (res) => {
                            if (res.responseJSON.state == 1) {
                                let data = res.responseJSON.data || []
                                data.map(item => {
                                    this.$set(this.resList, Object.keys(item)[0], Object.values(item)[0])
                                })
                            } else this.$message.error(getI18nValue(res.responseJSON.msg))
                        }
                    });
                },
                handleClick(item) {
                    this.active = item.id
                    this.loadHtml(item.url, item.iframe)
                },
                loadHtml(url,isIframe, callback) {
                    this.pageNavloading = true
                    // if(isIframe){
                        this.pageNav.loading = false
                        $('#content').html(`<iframe src="${url}" style="width: 100%; height: 100%; border: none;"></iframe>`)
                        callback && callback()
                    //     return;
                    // }
                    // $('#content').load(url, () => {
                    //     this.pageNav.loading = false
                    //     callback && callback()
                    // })
                },
            }
        })
    </script> -->
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/easitline-static/lib/layer/layer.js"></script>
<script src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script type="text/javascript" src="/cc-custmgr/static/js/my_i18n.js?v=2021110901"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>
  <!-- <script src="/cc-custmgr/static/js/cascader/cascader.js"></script> -->
<script type="text/javascript" src="/cc-custmgr/static/js/custmgrNew/custmgrNew.js"></script>
 <script src="/cc-base/static/js/yq/extends.js"></script>
  <script>
      var mainFrame = new Vue({
          el: '#mainFrame',
          data: function () {
              return {
                  pageNav: {
                      loading: false,
                  },
                  panes: [
                      { label: getI18nValue('客户管理'), key: 1, url: '/cc-custmgr/pages/custmgrNew/index.html', id: 'custmgrNew' },
                      { label: getI18nValue('信息表配置'), role: 'cc-custmgr-xxbpz', key: 2, url: '/cc-custmgr/pages/custmgrNew/tableConfig/index.html', id: 'tableConfig' },
                      { label: getI18nValue('客户标签配置'), role: 'cc-custmgr-khbq', key: 3, url: '/cc-custmgr/pages/custmgrConfig/Config.jsp', id: 'custmgrConfig' },
                      { label: getI18nValue('智能标签管理'), role: 'cc-custmgr-khbq-rule', key: 9, url: '/cc-custmgr/pages/custmgrNew/labelRules/index.html', id: 'labelRules' },
                      // { label: getI18nValue('客户分析'), role: 'cc-custmgr-khfx', key: 4, url: '/cc-custmgr/pages/stat/customerStat.jsp', id: 'customerStat' },
                      { label: getI18nValue('客户分析'), role: 'cc-custmgr-khfx', key: 5, url: '/cc-custmgr/pages/custmgrNew/custAnalysis/index.html', id: 'custAnalysis', iframe: true },
                      { label: getI18nValue('外部公告'), role: 'cc-custmgr-wbgg', key: 7, url: '/cc-custmgr/pages/notice/noticeList.html', id: 'noticeList' },
                      { label: getI18nValue('信息发布'),  role: 'cc-custmgr-xxfb',key: 8, url: '/cc-custmgr/pages/notice/infoManage.html', id: 'infoManage' },
                      { label: getI18nValue('客户账号'),  role: 'cc-base-h5user',key: 8, url: '/cc-custom-h5/pages/user-list.html', id: 'h5user' },
                      { label: getI18nValue('参数配置'), role: 'cc-base-system-xtpz-cspz-custmgr', key: 6, url: '/cc-custmgr/pages/param/param-cust.html', id: 'param-cust' },
                  ],
                  active: 'custmgrNew',
                  resList: {}, // 权限字典
              }
          },
          mounted() {
              this.getcheckRes()
              this.loadHtml(this.panes[0].url)
          },
          methods: {
              // 查询权限
              getcheckRes() {
                  let ids = [
                      'cc-custmgr-xxbpz', 'cc-custmgr-khbq', 'cc-custmgr-khfx','cc-custmgr-khfx-new', 'cc-base-system-xtpz-cspz-custmgr', 'cc-custmgr-func-14',
                      'cc-custmgr-func-2', 'cc-custmgr-func-3', 'cc-custmgr-func-8', 'cc-custmgr-func-9', 'cc-custmgr-func-11',
                      'cc-custmgr-khzcsh', 'cc-custmgr-wbgg', 'cc-custmgr-func-1', 'cc-custmgr-func-10', 'cc-custmgr-func-13', 'cc-custmgr-khbq-rule','cc-custmgr-xxfb','cc-base-h5user'
                  ]
                  $.ajax({
                      url: `/cc-base/servlet/menu?action=checkRes&ids=${ids.join(';')}`,
                      type: 'GET',
                      complete: (res) => {
                          if (res.responseJSON.state == 1) {
                              let data = res.responseJSON.data || []
                              data.map(item => {
                                  this.$set(this.resList, Object.keys(item)[0], Object.values(item)[0])
                              })
                          } else this.$message.error(getI18nValue(res.responseJSON.msg))
                      }
                  });
              },
              handleClick(item) {
                  this.active = item.id
                  this.loadHtml(item.url, item.iframe)
              },
              loadHtml(url,isIframe, callback) {
                  this.pageNavloading = true
                  // if(isIframe){
                      this.pageNav.loading = false
                      $('#content').html(`<iframe src="${url}" data-id="${this.active}" style="width: 100%; height: 100%; border: none;"></iframe>`)
                      this.$nextTick(() => {
                        this.addIframeCss()
                      })
                      callback && callback()
                  //     return;
                  // }
                  // $('#content').load(url, () => {
                  //     this.pageNav.loading = false
                  //     callback && callback()
                  // })
              },
              // 引入css文件
              addIframeCss(){
                var iframe = document.querySelector('iframe[data-id="'+ this.active +'"]');
                // 等待iframe加载完成
                iframe.onload = function() {
                    var innerDoc = this.contentDocument || this.contentWindow.document;
                    
                    // 创建一个link元素来引入样式文件
                    var link = innerDoc.createElement('link');
                    link.rel = 'stylesheet';
                    link.type = 'text/css';
                    link.href = '/cc-custmgr/static/css/tabs.css?v=1.0.0'; // 指定样式文件的路径
                    
                    // 将link元素添加到iframe的<head>中
                    var head = innerDoc.head || innerDoc.getElementsByTagName('head')[0];
                    head.appendChild(link);
                };
              },
          }
      })
  </script>
</html>