<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<%@ page import="com.yq.busi.common.util.ConfigUtil"%>
<EasyTag:override name="head">
	<title i18n-content="信息表录入"></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="CusttableForm" data-mars="custTable.tableEdit" data-pk="" method="post" autocomplete="off" data-mars-prefix="Custtable.">
		<input name="Custtable.ID" class="form-control input-sm" type="hidden" value="${param.ID }">
		<table class="table  table-edit table-vzebra mt-12" style="margin-top: 17px;">
			<tbody>
				<tr>
					<td class="required" i18n-content="表名称"></td>
					<td><input  data-rules="required" maxlength="30" name="Custtable.TBL_NAME" class="form-control input-sm" type="text"></td>			
				</tr>
				<tr>
					<td class="required" i18n-content="表类型"></td>
					<td>
						<select  data-rules="required"  name="Custtable.TBL_TYPE" class="form-control input-sm">
							<option value="" i18n-content="请选择"></option>
						    <option value="1" i18n-content="客户信息表"></option>
						</select>
					</td>
				</tr>				
				<tr>
					<td class="required" i18n-content="启用状态">启用状态</td>
					<td><select  data-rules="required" name="Custtable.ENABLE_STATUS" class="form-control input-sm">
							<option value="" i18n-content="请选择"></option>
							<option value="01" i18n-content="启用"></option>
							<option value="02" i18n-content="禁用"></option>
						</select>
					</td>
				</tr>				
				<tr>
					<td class="required" i18n-content="表注释名称"></td>
					<td><input data-rules="required"  maxlength="100" name="Custtable.TBL_TITLE" class="form-control input-sm" type="text"></td>
				</tr>
					<tr>
						<td class="required" width="60px" i18n-content="是否为默认表"></td>
						<td>				
	    				<label class="radio-inline">
	     					<input type="radio"  value="Y" name="Custtable.IS_DEFAULT" i18n-content="是">
	    				</label>
						<label class="radio-inline">
	     					<input type="radio"  value="N" name="Custtable.IS_DEFAULT" checked i18n-content="v">
	    				</label>
						</td>
					</tr>
				<tr>
					<td i18n-content="功能描述"></td>
					<td><textarea rows="3" name="Custtable.TBL_DESC" maxlength="200" class="form-control input-sm"></textarea></td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="CustTable.ajaxSubmitForm()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20" type="button" id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		requreLib.setplugs('wdate');
        var id= '${param.ID }';
		$(function(){
			$("#CusttableForm").render();
		});
		
		CustTable.ajaxSubmitForm = function(){
				if(!form.validate("#CusttableForm")){
					return;
				}; 
				var url ="${ctxPath}/servlet/custTable?action=";
				if(!checkNull(id)){
					url =url+"updateCustTable";
				}else{
					url =url+"addCustTable";
				}				
				var data = form.getJSONObject("#CusttableForm");
				ajax.remoteCall(url,data,function(result) { 
					if(result.state == 1){
						layer.closeAll();
						layer.msg(result.msg,{icon: 1});
						  CustTable.loadData();

					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
		 }
	</script>
</EasyTag:override>
<script type="text/javascript" src="${ctxPath}/static/js/my_i18n.js"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
<%@ include file="/pages/common/layout_div.jsp"%>