(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d3530172"],{c616:function(e,t,a){"use strict";a("e9b0")},e661:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"工号"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENTID,callback:function(t){e.$set(e.queryParams,"AGENTID",t)},expression:"queryParams.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"DAY",label:"时间段",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"话务员工号",align:"center"}}),t("el-table-column",{attrs:{prop:"LOGIN_TIME",label:"签入总时长",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_IN_TIME",label:"呼入通话时长",align:"center"}}),t("el-table-column",{attrs:{label:"呼入通话时长占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.CALL_IN_RATE)))]}}])}),t("el-table-column",{attrs:{prop:"CALL_OUT_TIME",label:"呼出通话时长",align:"center"}}),t("el-table-column",{attrs:{label:"呼出通话时长占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.CALL_OUT_RATE)))]}}])}),t("el-table-column",{attrs:{prop:"IDLE_TIME",label:"空闲时长",align:"center"}}),t("el-table-column",{attrs:{label:"空闲时长占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.IDLE_RATE)))]}}])}),t("el-table-column",{attrs:{prop:"ARRANGE_TIME",label:"整理时长",align:"center"}}),t("el-table-column",{attrs:{label:"整理时长占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.ARRANGE_RATE)))]}}])}),t("el-table-column",{attrs:{prop:"REST_TIME",label:"离席时长",align:"center"}}),t("el-table-column",{attrs:{label:"离席时长占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.REST_RATE)))]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、签入总时长：坐席的签入总时长")]),t("p",[e._v("2、呼入通话时长：坐席接听呼入电话的通话总时长")]),t("p",[e._v("3、呼入通话时长占比：呼入通话时长/签入总时长")]),t("p",[e._v("4、呼出通话时长：坐席呼出通话的通话总时长")]),t("p",[e._v("5、呼出通话时长占比：呼出通话时长/签入总时长")]),t("p",[e._v("6、空闲时长：坐席的空闲时长(签入时长-通话时长-离席时长-整理时长)")]),t("p",[e._v("7、空闲时长占比：空闲时长签入总时长")]),t("p",[e._v("8、整理时长：坐席的话后整理时长")]),t("p",[e._v("9、整理时长占比：整理时长/坐席的签入时长")]),t("p",[e._v("10、离席时长：坐席的离席时长")]),t("p",[e._v("12、离席时长占比：离席时长/坐席的签入时长")])])}],n=(a("a573"),a("0c9f")),s=a("c2d0"),i={name:"AgentOccupyStat",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},agentOptions:[],queryParams:{AGENTID:"",BEGIN_TIME:"",END_TIME:""}}},created(){this.initTime(),this.getAgentList(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(s["e"])(),this.queryParams.END_TIME=Object(s["d"])()},getAgentList(){Object(n["gb"])({AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},["common.agentList"]).then(e=>{if(e["common.agentList"]&&e["common.agentList"].data){const t=e["common.agentList"].data;this.agentOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}})},formatRate(e){return Object(s["b"])(e)},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(n["P"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={AGENTID:"",BEGIN_TIME:"",END_TIME:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出座席占用率图统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(n["h"])(this.searchedForm))}).catch(()=>{})}}},o=i,c=(a("c616"),a("2877")),p=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=p.exports},e9b0:function(e,t,a){}}]);