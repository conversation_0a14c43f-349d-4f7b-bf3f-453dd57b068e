(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-05c1b5f5"],{"0c82":function(e,t,a){"use strict";a("541a")},"541a":function(e,t,a){},"7e19":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",{attrs:{label:"工号"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.queryParams.AGENTID,callback:function(t){e.$set(e.queryParams,"AGENTID",t)},expression:"queryParams.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"WORK_GROUP",label:"班组",align:"center",width:"150"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"工号",align:"center"}}),t("el-table-column",{attrs:{prop:"NAME",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{prop:"LOGIN_TIME",label:"签入时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_COUNT",label:"受理量",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_ACK_TIME",label:"应答时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_ACK_AVG",label:"应答均长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_ACK_MAX",label:"应答最大时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_COUNT",label:"呼出总数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_SUCC",label:"呼出成功数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_PERCENT",label:"呼出成功率",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_TIME",label:"呼出总时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_AVG",label:"呼出均长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_MAX",label:"呼出最大时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_INNER_COUNT",label:"内部求助数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_INNER_TIME",label:"内部求助时长",align:"center"}}),t("el-table-column",{attrs:{prop:"BUSY_TIME",label:"置忙时长",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、签入时长：坐席的签入总时长")]),t("p",[e._v("2、受理量：12345呼入通话坐席应答时长+通话时长>0的次数")]),t("p",[e._v("3、应答时长：12345呼入通话坐席的总通话时长")]),t("p",[e._v("4、应答均长：应答时长/受理量")]),t("p",[e._v("5、呼出总数：12345坐席外呼的通话次数")]),t("p",[e._v("6、呼出成功数：12345坐席外呼通话时长>0的通话次数")]),t("p",[e._v("7、呼出成功率：呼出成功数/呼出总数")]),t("p",[e._v("8、呼出总时长：12345坐席外呼的通话总时长")]),t("p",[e._v("9、呼出均长：呼出总时长/呼出成功数")]),t("p",[e._v("10、呼出最大时长：12345坐席外呼的最大通话")]),t("p",[e._v("11、内部求助数：坐席进行内部求助的通话次数")]),t("p",[e._v("12、内部求助时长：坐席进行内部求助的通话总时长")]),t("p",[e._v("13、置忙时长：坐席的离席时长")])])}],n=(a("a573"),a("0c9f")),i=a("c2d0"),s={name:"AgentWorkCount",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},agentOptions:[],queryParams:{BEGIN_TIME:"",END_TIME:"",AGENTID:""}}},created(){this.initTime(),this.getAgentList(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(i["o"])(),this.queryParams.END_TIME=Object(i["p"])()},getAgentList(){Object(n["gb"])({AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},["common.agentList"]).then(e=>{if(e["common.agentList"]&&e["common.agentList"].data){const t=e["common.agentList"].data;this.agentOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}})},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(n["V"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={BEGIN_TIME:"",END_TIME:"",AGENTID:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出话务员工作量报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(n["m"])(this.searchForm))}).catch(()=>{})}}},o=s,c=(a("0c82"),a("2877")),p=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=p.exports}}]);