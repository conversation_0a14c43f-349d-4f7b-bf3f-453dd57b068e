(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-06989db0"],{"4ac1":function(e,t,a){},"820f":function(e,t,a){"use strict";a("4ac1")},"84d4":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small",model:e.searchForm}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",{attrs:{label:"统计步长"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{value:"0",label:"时段"}}),t("el-option",{attrs:{value:"2",label:"小时"}}),t("el-option",{attrs:{value:"3",label:"天"}}),t("el-option",{attrs:{value:"6",label:"月"}})],1)],1),t("el-form-item",{attrs:{label:"工号"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.AGENTID,callback:function(t){e.$set(e.searchForm,"AGENTID",t)},expression:"searchForm.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")])],1),t("el-divider",{attrs:{direction:"vertical"}}),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getDateTime(t.row))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_COUNT",label:"人工呼入次数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_SUCC",label:"人工接通数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_PERCENT",label:"人工接通率",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_TIME",label:"呼入通话总时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_AVG",label:"呼入通话均长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_TIME_MIN",label:"呼入最短通话时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLIN_TIME_MAX",label:"呼入最长通话时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_COUNT",label:"呼出数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_SUCC",label:"呼出成功数",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_PERCENT",label:"呼出成功率",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_TIME",label:"呼出通话总时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_AVG",label:"呼出通话均长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_TIME_MIN",label:"呼出最短通话时长",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_CALLOUT_TIME_MAX",label:"呼出最长通话时长",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、人工呼入次数：12345呼入转坐席的次数")]),t("p",[e._v("2、人工接通数：12345呼入坐席通话时长>0的次数+应答时长>0的次数")]),t("p",[e._v("3、人工接通率：人工接通数/人工呼入次数")]),t("p",[e._v("4、呼入通话总时长：12345呼入坐席接通的总通话时长")]),t("p",[e._v("5、呼入通话均长：呼入通话总时长/人工接通数")]),t("p",[e._v("6、呼入最短通话时长：12345呼入坐席接通的最小通话时长")]),t("p",[e._v("7、呼入最长通话时长：12345呼入坐席接通的最大通话时长")]),t("p",[e._v("8、呼出数：12345坐席呼出通话的次数")]),t("p",[e._v("9、呼出成功数：12345坐席呼出通话时长>0的次数")]),t("p",[e._v("10、呼出成功率：呼出成功数/呼出数")]),t("p",[e._v("11、呼出通话总时长：12345坐席呼出通话的总通话时长")]),t("p",[e._v("12、呼出通话均长：呼出通话总时长/呼出成功数")]),t("p",[e._v("13、呼出最短通话时长：12345坐席呼出通话的最短通话时长")]),t("p",[e._v("14、呼出最长通话时长：12345坐席呼出通话的最长通话时长")])])}],n=(a("0643"),a("4e3e"),a("a573"),a("88a7"),a("271a"),a("5494"),a("c2d0")),o=a("0c9f"),i={name:"CallOutAgentCount",data(){return{searchForm:{BEGIN_TIME:Object(n["e"])(),END_TIME:Object(n["d"])(),statStepSize:"0",AGENTID:"",pageIndex:1,pageSize:15},loading:!1,tableData:[],pagination:{currentPage:1,pageSize:15,total:0},agentOptions:[]}},created(){this.loadDictionaries(),this.loadTableData()},methods:{getDateTime:n["c"],loadDictionaries(){const e={params:{AGENTID:"",WORKGROUPID:"",statStepSize:this.searchForm.statStepSize,BEGIN_TIME:"",END_TIME:""},controls:["common.agentList","common.workGroupList"]};Object(o["G"])(e).then(e=>{if(e["common.agentList"]){const t=e["common.agentList"].data||{};this.agentOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}}).catch(e=>{this.$message.error("获取字典数据失败")})},loadTableData(){this.loading=!0;const e={...this.searchForm,pageType:3,pageIndex:this.pagination.currentPage,pageSize:this.pagination.pageSize};this.searchedForm=e,Object(o["E"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.pagination.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败")}).finally(()=>{this.loading=!1})},exportDetail(){this.$confirm("是否导出呼出电话统计报表?","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=new URLSearchParams;Object.keys(this.searchForm).forEach(t=>{e.append(t,this.searchForm[t])});const t=Object(o["d"])(e.toString()),a=document.createElement("a");a.href=t,a.target="_blank",document.body.appendChild(a),a.click(),document.body.removeChild(a),this.$message({type:"success",message:"导出中，请稍候"})}).catch(()=>{})},handleSizeChange(e){this.pagination.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pagination.currentPage=e,this.loadTableData()},resetForm(){this.searchForm={BEGIN_TIME:Object(n["e"])(),END_TIME:Object(n["d"])(),statStepSize:"0",AGENTID:"",pageIndex:1,pageSize:15},this.pagination.currentPage=1,this.loadTableData()}}},s=i,c=(a("820f"),a("2877")),p=Object(c["a"])(s,l,r,!1,null,null,null);t["default"]=p.exports}}]);