(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3cb22ee3"],{"089a":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",{attrs:{label:"坐席工号"}},[t("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENTID,callback:function(t){e.$set(e.queryParams,"AGENTID",t)},expression:"queryParams.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"业务类型"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.SERVICENO,callback:function(t){e.$set(e.queryParams,"SERVICENO",t)},expression:"queryParams.SERVICENO"}},e._l(e.serviceTypeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"主叫号码"}},[t("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:"主叫号码"},model:{value:e.queryParams.CALLERNO,callback:function(t){e.$set(e.queryParams,"CALLERNO",t)},expression:"queryParams.CALLERNO"}})],1),t("el-form-item",{attrs:{label:"呼叫时长范围"}},[t("el-input-number",{staticStyle:{width:"120px"},attrs:{min:0},model:{value:e.queryParams.BEGIN_NUMBER_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_NUMBER_TIME",t)},expression:"queryParams.BEGIN_NUMBER_TIME"}}),t("span",{staticStyle:{margin:"0 8px"}},[e._v("-")]),t("el-input-number",{staticStyle:{width:"120px"},attrs:{min:0},model:{value:e.queryParams.END_NUMBER_TIME,callback:function(t){e.$set(e.queryParams,"END_NUMBER_TIME",t)},expression:"queryParams.END_NUMBER_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"工号",align:"center"}}),t("el-table-column",{attrs:{prop:"NAME",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫号码",align:"center"}}),t("el-table-column",{attrs:{prop:"ACKBEGIN",label:"呼叫建立时间",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"ACKTIME",label:"呼叫持续时间",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、接听呼入12345且有坐席接通的通话明细")])])}],s=(a("a573"),a("0c9f")),i=a("c2d0"),n={name:"AgentCallDetail",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},agentOptions:[],serviceTypeOptions:[],queryParams:{BEGIN_TIME:"",END_TIME:"",AGENTID:"",SERVICENO:"",CALLERNO:"",BEGIN_NUMBER_TIME:0,END_NUMBER_TIME:0}}},created(){this.initTime(),this.getDicts(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(i["g"])(),this.queryParams.END_TIME=Object(i["f"])()},getDicts(){Object(s["gb"])({AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},["common.agentList","common.serviceTypeList"]).then(e=>{if(e["common.agentList"]&&e["common.agentList"].data){const t=e["common.agentList"].data;this.agentOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}if(e["common.serviceTypeList"]&&e["common.serviceTypeList"].data){const t=e["common.serviceTypeList"].data;this.serviceTypeOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}})},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(s["L"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={BEGIN_TIME:"",END_TIME:"",AGENTID:"",SERVICENO:"",CALLERNO:"",BEGIN_NUMBER_TIME:0,END_NUMBER_TIME:0},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出座席接线明细报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(s["c"])(this.searchedForm))}).catch(()=>{})}}},o=n,c=(a("a283"),a("2877")),m=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=m.exports},a283:function(e,t,a){"use strict";a("bfd6")},bfd6:function(e,t,a){}}]);