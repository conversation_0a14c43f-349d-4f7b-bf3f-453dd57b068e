(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-42d0fdd0"],{"0336":function(e,t,a){},2637:function(e,t,a){"use strict";a("0336")},"7b04":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"环比周期"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.qoqName,callback:function(t){e.$set(e.queryParams,"qoqName",t)},expression:"queryParams.qoqName"}},e._l(e.qoqOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"DATE_ID",label:"时间段",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"WAIT_COUNT",label:"人工呼入数",align:"center"}}),t("el-table-column",{attrs:{label:"人工呼入数("+e.typeName+")",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.LAST_WAIT_COUNT))]}}])}),t("el-table-column",{attrs:{label:"与"+e.typeName+"相比（%）",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.QOQ_WAIT_RATE)))]}}])}),t("el-table-column",{attrs:{prop:"AGENT_COUNT",label:"人工接通数",align:"center"}}),t("el-table-column",{attrs:{label:"人工接通数("+e.typeName+")",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.LAST_AGENT_COUNT))]}}])}),t("el-table-column",{attrs:{label:"与"+e.typeName+"相比（%）",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.QOQ_AGENT_RATE)))]}}])}),t("el-table-column",{attrs:{label:"人工接通率",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.AGENT_RATE)))]}}])}),t("el-table-column",{attrs:{label:"人工接通率("+e.typeName+"%)",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.LAST_AGENT_RATE)))]}}])}),t("el-table-column",{attrs:{label:"与"+e.typeName+"相比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.formatRate(t.row.QOQ_RATE)))]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、人工呼入数：呼入12345通话中存在转坐席和排队的通话次数")]),t("p",[e._v("2、人工呼入数(上期)：上期呼入12345通话中存在转坐席和排队的通话次数")]),t("p",[e._v("3、人工呼入数与上期环比：人工呼入数-人工呼入数(上期)/人工呼入数(上期)")]),t("p",[e._v("4、人工接通数：呼入12345通话中通话时长+应答时长>0的通话次数")]),t("p",[e._v("5、人工接通数(上期)：上期呼入12345通话中通话时长+应答时长>0的通话次数")]),t("p",[e._v("6、人工接通数与上期环比：人工接通数-人工接通数(上期)/人工接通数(上期)")]),t("p",[e._v("7、人工接通率：人工接通数/人工呼入数")]),t("p",[e._v("8、人工接通率(上期)：人工接通数(上期)/人工呼入数(上期)")]),t("p",[e._v("9、人工接通率与上期环比：人工接通率-人工接通率(上期)/人工接通率(上期)")])])}],n=a("0c9f"),s=a("c2d0"),i={name:"CallQoqStat",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},typeName:"上期",qoqOptions:[{value:"1",label:"天"},{value:"2",label:"周"},{value:"3",label:"月"},{value:"4",label:"年"}],queryParams:{qoqName:"1",BEGIN_TIME:"",END_TIME:""}}},created(){this.initTime(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(s["o"])(),this.queryParams.END_TIME=Object(s["p"])()},formatRate(e){return Object(s["b"])(e)},loadTableData(){this.loading=!0;const e=this.queryParams.qoqName;this.typeName="1"===e?"前一天":"2"===e?"前一周":"3"===e?"前一月":"4"===e?"前一年":"上期";const t={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=t,Object(n["db"])(t).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={qoqName:"1",BEGIN_TIME:"",END_TIME:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出人工话务量环比报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(n["u"])(this.searchForm))}).catch(()=>{})}}},o=i,c=(a("2637"),a("2877")),u=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=u.exports}}]);