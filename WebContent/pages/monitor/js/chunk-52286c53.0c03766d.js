(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52286c53"],{"27b0":function(t,e,a){"use strict";a("67d6")},"67d6":function(t,e,a){},"82f1":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page",class:{"iframe-mode":t.isIframeMode},on:{click:t.toBack}},[e("div",{staticClass:"chartsBox",class:{"iframe-mode":t.isIframeMode},on:{click:function(t){t.stopPropagation()}}},[t.isIframeMode?t._e():e("title-bg",{attrs:{title:"12345热线实时数据监控"}}),e("div",{staticClass:"top-box"},[e("div",{staticClass:"top-box-item bg1"},[e("span",{staticClass:"artFont"},[t._v("接线总人数:")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.leftNumber))])]),e("div",{staticClass:"top-box-item bg2"},[e("span",{staticClass:"artFont"},[t._v("总业务量:")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.rightNumber))])]),e("div",{staticClass:"top-box-item bg3"},[e("div",[e("span",{staticClass:"artFont"},[t._v("12345:")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.number12345))])]),e("div",{staticClass:"line"}),e("div",[e("span",{staticClass:"artFont"},[t._v("整合热线:")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.numberZhrx))])]),e("div",{staticClass:"line"}),e("div",[e("span",{staticClass:"artFont"},[t._v("网络量:")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.numberWll))])])])]),e("div",{staticClass:"box"},[e("div",{staticClass:"box_center"},[e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("呼叫量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.lineChartCount))])])]),t.lineWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.lineChart}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("IVR收听量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.callIvrChartCount))])])]),t.callIvrWaring?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.callIvrCharts}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("人工接通量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.centerlineChartCount2))])])]),t.centerlineWarning2?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.centerlineChart2}})],1)])]),e("div",{staticClass:"box_center"},[e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("排队量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.centerlineChartCount3))])])]),t.centerlineWarning3?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.centerlineChart3}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("待接听坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentIdleChartCount))])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("话后坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentWorkCount))])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("离席坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentSetbusyCount))])])]),t.agentIdleWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.agentIdleChart}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("未进入排队量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.overflowWaitChartCount))])])]),t.overflowWaitWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.overflowWaitChart}})],1)])]),e("div",{staticClass:"line l1"}),e("div",{staticClass:"box_bottom"},[e("div",{staticClass:"bottomChartBox"},[e("div",{staticClass:"titleBox BottomTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("全天接通率")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount1)+"%")])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("全业务接通率")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount3)+"%")])])]),t.bottomlineWarning1?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"bottomChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.bottomlineChart1}})],1)]),e("div",{staticClass:"line l3"}),e("div",{staticClass:"bottomChartBox"},[e("div",{staticClass:"titleBox BottomTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("主动挂断量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount2))])])]),t.bottomlineWarning2?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"bottomChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.bottomlineChart2}})],1)])])])],1),t.visible3?e("div",{staticClass:"custom-dialog"},[e("div",{staticClass:"header"},[e("div",{staticClass:"close",staticStyle:{"z-index":"9999"},on:{click:t.toggleDialog3}},[e("img",{attrs:{src:a("d991"),alt:""}}),e("div",{staticClass:"artFont"},[t._v("关闭")])]),e("title-bg",{attrs:{title:"整合热线电话量"}})],1),e("div",{staticClass:"dialog-body"},["city"==t.active2?e("div",{staticClass:"big-area"},[e("delay-dom",{staticClass:"h100 animate__animated animate__fadeIn",attrs:{delay:1,used:!0}},[e("echart",{staticClass:"echart",attrs:{data:t.acceptChartBig,visible:t.visible3}})],1)],1):t._e(),"jinJI"==t.active2?e("div",{staticClass:"big-area"},[e("delay-dom",{staticClass:"h100 animate__animated animate__fadeIn",attrs:{delay:1,used:!0}},[e("echart",{staticClass:"echart",attrs:{data:t.acceptChartBig1,visible:t.visible3}})],1)],1):t._e()])]):t._e()])},i=[],s=(a("14d9"),a("0643"),a("4e3e"),a("a573"),a("981c")),o=a("365c"),r=a("313e"),n=a("55f4"),c=a("737e");let h={trigger:"axis",borderColor:"#00AAFF",borderWidth:1,backgroundColor:"rgba(0, 27, 53, 0.75)",axisPointer:{type:"line",lineStyle:{color:"#00AAFF",width:1,type:"solid",shadowBlur:8,shadowColor:"rgba(255, 255, 255, 0.5)"}},textStyle:{color:"#fff"},formatter:function(t){var e="";return e+=t[0].data.time+"  <br/>",t.forEach((function(t){let a=`\n            <div style="display:inline-block;margin-right:8px;border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`;e+=`${a} ${t.data.name} : <span style="margin-left:4px;color:${t.color}">${t.value}${t.data.name.includes("率")?"%":""} </span><br/> `})),e}},d={left:0,right:20,top:32,bottom:0,containLabel:!0},g={left:0,right:32,top:32,bottom:0,containLabel:!0},p={name:"数量",type:"value",minInterval:1,axisLabel:{textStyle:{color:"#fff"}},splitLine:{lineStyle:{color:"#0080FF",type:"dashed",opacity:.3}},nameTextStyle:{color:"#fff"}},C={right:10,data:[{name:"告警基准线",lineStyle:{type:"solid",width:1,color:"#FF0080"},textStyle:{color:"#fff"}}],selectedMode:!1},u={type:"line",symbol:"none",name:"告警基准线",color:"transparent"};var v={name:"index",components:{Header:s["a"],TitleBg:n["a"],MySwitchBtn:c["a"]},computed:{},watch:{},data(){return{isIframeMode:!1,leftNumber:"",rightNumber:"",number12345:"",numberZhrx:"",numberWll:"",visible2:!0,visible3:!1,active:"city",active2:"city",btnList:[{label:"城区",value:"city"},{label:"京冀",value:"jinJI"}],lineChart:{color:["#00DCAA"],xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},legend:C,yAxis:p,tooltip:h,grid:{left:0,right:32,top:32,bottom:0,containLabel:!0},visualMap:{show:!1,dimension:0,pieces:[]},series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90,name:"告警基准线"}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},lineChartCount:0,lineWarning:!1,callIvrCharts:{color:["#00DCAA"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:h,grid:d,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},callIvrChartCount:0,callIvrWaring:!1,centerlineChart2:{color:["#00DCAA"],xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},legend:C,yAxis:p,tooltip:h,grid:d,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90,name:"告警基准线"}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},centerlineChartCount2:0,centerlineWarning2:!1,centerlineChart3:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:h,grid:d,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},centerlineChartCount3:0,centerlineWarning3:!1,agentIdleChart:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:h,grid:d,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},agentIdleChartCount:0,agentIdleWarning:!1,overflowWaitChart:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:h,grid:d,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},u]},overflowWaitChartCount:0,overflowWaitWarning:!1,bottomlineChart1:{color:["#00DCAA","#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:3},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:h,grid:g,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}}},{data:[],type:"line",smooth:!0},u]},bottomlineChartCount1:0,bottomlineChartCount3:0,bottomlineWarning1:!1,bottomlineChart2:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:3},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:p,tooltip:{trigger:"axis",borderColor:"#00AAFF",borderWidth:1,backgroundColor:"rgba(0, 27, 53, 0.75)",axisPointer:{type:"line",lineStyle:{color:"#00AAFF",width:1,type:"solid",shadowBlur:8,shadowColor:"rgba(255, 255, 255, 0.5)"}},textStyle:{color:"#fff"},formatter:function(t){var e="";return e+=t[0].data.time+"  <br/>",t.forEach((function(t){console.log(t);let a=`\n            <div style="display:inline-block;margin-right:8px;border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`,l=`\n            <div style="display:inline-block;margin-right:8px;visibility:hidden; border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`;e+=`${a} ${t.data.name} : <span style="margin-left:4px;color:${t.color}">${t.value}</span><br/>\n              ${l}\n              50内呼损 : <span style="margin-left:4px;color:${t.color}">${t.data.wait}</span><br/>\n              ${l}\n              IVR呼损 : <span style="margin-left:4px;color:${t.color}">${t.data.ivr} </span><br/>\n              `})),e}},grid:g,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}}},u]},bottomlineChartCount2:0,bottomlineWarning2:!1,agentSetbusyCount:0,agentWorkCount:0,acceptChartBig:{color:["#00DCAA","#FA66AF"],legend:{show:!1},tooltip:{show:!0,trigger:"axis",borderColor:"#01FFFF",backgroundColor:"#0E3B9F",textStyle:{color:"#FFFFFF"},formatter:t=>{let e=`<div><p style="font-weight:bold;margin:0 0px 10px;line-height: 1;font-size: 20px;">${t[0].name}</p></div>`;return t.forEach((t,a)=>{let l="";0==a&&(l="linear-gradient(90deg, rgba(23,255,138,0) 0%, #17FF8A 100%)",e+=`<div>\n                            <div style="margin: 0px;line-height: 1.5;font-size: 20px;">\n                                <span style="display:inline-block;margin-right:5px;width:10px;height:10px;background:${l};"></span>\n                                <span>${t.seriesName}</span>\n                                <span style="float:right;margin-left:20px;">${t.data}</span>\n                            </div>\n                            </div>`)}),e}},grid:{left:5,right:5,top:32,bottom:40,containLabel:!0},xAxis:{type:"category",data:[],axisLine:{lineStyle:{color:"rgba(10,135,236,0.2)"}},axisTick:{inside:!0,alignWithLabel:!0,lineStyle:{color:"#3A73F3"}},axisLabel:{color:"#FFFFFF",interval:0,fontSize:12,margin:15}},yAxis:{type:"value",name:"数量",nameTextStyle:{align:"right",padding:[0,-14,0,0],fontSize:12},axisLabel:{margin:3,fontSize:14},splitLine:{lineStyle:{color:"rgba(10,135,236,0.2)"}},axisLine:{lineStyle:{color:"#FFFFFF"}},splitNumber:2},series:[{name:"呼入量",type:"bar",barWidth:58,barGap:"-40%",itemStyle:{opacity:1,color:function(){return new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(23, 255, 138, 0.6)"},{offset:1,color:"rgba(1, 255, 255, 0.2)"}],!1)}},data:[]}]},acceptChartBig1:{color:["#00DCAA","#FA66AF"],legend:{show:!1},tooltip:{show:!0,trigger:"axis",borderColor:"#01FFFF",backgroundColor:"#0E3B9F",textStyle:{color:"#FFFFFF"},formatter:t=>{let e=`<div><p style="font-weight:bold;margin:0 0px 10px;line-height: 1;font-size: 20px;">${t[0].name}</p></div>`;return t.forEach((t,a)=>{let l="",i="";0!=a&&3!=a||(l="linear-gradient(90deg, rgba(23,255,138,0) 0%, #17FF8A 100%)",i="linear-gradient(90deg, rgba(250,102,175,0) 0%, #FA66AF 100%)",e+=`<div>\n                            <div style="margin: 0px;line-height: 1.5;font-size: 20px;">\n                                <span style="display:inline-block;margin-right:5px;width:10px;height:10px;background:${0==a?l:i};"></span>\n                                <span>${t.seriesName}</span>\n                                <span style="float:right;margin-left:20px;">${t.data}</span>\n                            </div>\n                            </div>`)}),e}},grid:{left:5,right:5,top:32,bottom:40,containLabel:!0},xAxis:{type:"category",data:[],axisLine:{lineStyle:{color:"rgba(10,135,236,0.2)"}},axisTick:{inside:!0,alignWithLabel:!0,lineStyle:{color:"#3A73F3"}},axisLabel:{color:"#FFFFFF",interval:0,fontSize:14,margin:15}},yAxis:{type:"value",name:"数量",nameTextStyle:{align:"right",padding:[0,-14,0,0],fontSize:14},axisLabel:{margin:3,fontSize:14},splitLine:{lineStyle:{color:"rgba(10,135,236,0.2)"}},axisLine:{lineStyle:{color:"#FFFFFF"}},splitNumber:2},series:[{name:"呼入量",type:"bar",barWidth:"23",itemStyle:{opacity:1,color:function(){return new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(23, 255, 138, 0.6)"},{offset:1,color:"rgba(1, 255, 255, 0.2)"}],!1)}},data:[]}]},timer:null}},methods:{getCharts(){let t=-1,e=!1;Object(o["e"])({serviceId:"MONITOR_CALL_DATA_SERVICE",dateType:"1"}).then(a=>{this.lineChart.series[0].data=a.callIn||[];let l=[],i=0;a.callIn.forEach((a,s)=>{1==a.thresholdType&&(Number(a.value)>Number(a.threshold)?e||(console.log("切换到预警",s),i<s&&l.push({gt:i,lte:s-1,color:"#00DCAA"}),t=s,e=!0):e&&(console.log("切换到正常",s),l.push({gt:t-1,lte:s-1,color:"#DC0055"}),i=s,e=!1))}),e?l.push({gt:t-1,lte:a.callIn.length-1,color:"#DC0055"}):i<a.callIn.length&&l.push({gt:i-1,lte:a.callIn.length-1,color:"#00DCAA"});let s=[],o=-1;l.forEach((t,e)=>{0===e&&t.gt>0&&s.push({gt:-1,lte:t.gt,color:"#00DCAA"}),s.push(t),o=t.lte,e<l.length-1&&l[e+1].gt>t.lte&&s.push({gt:t.lte,lte:l[e+1].gt,color:"#00DCAA"})}),o<a.callIn.length-1&&s.push({gt:o,lte:a.callIn.length-1,color:"#00DCAA"}),console.log(s,"finalPieces"),this.lineChart.visualMap.pieces=s,this.lineChart.series[0].markLine.data[0].yAxis=a.callIn[0].threshold||[],this.lineChartCount=a.callIn[a.callIn.length-1].value||0,this.lineChart.xAxis.data=a.callIn.map(t=>t.time),1==a.callIn[a.callIn.length-1].thresholdType?this.lineWarning=Number(a.callIn[a.callIn.length-1].value)>Number(a.callIn[a.callIn.length-1].threshold):this.lineWarning=Number(a.callIn[a.callIn.length-1].value)<Number(a.callIn[a.callIn.length-1].threshold),console.log(a.callIn,"callIn"),this.callIvrCharts.series[0].data=a.callIvr||[];let r=[],n=0,c=-1,h=!1;a.callIvr.forEach((t,e)=>{1==t.thresholdType&&(Number(t.value)>Number(t.threshold)?h||(n<e&&r.push({gt:n-1,lte:e-1,color:"#00DCAA"}),c=e-1,h=!0):h&&(r.push({gt:c,lte:e-1,color:"#DC0055"}),n=e,h=!1))}),h?r.push({gt:c,lte:a.callIvr.length-1,color:"#DC0055"}):n<a.callIvr.length&&r.push({gt:n-1,lte:a.callIvr.length-1,color:"#00DCAA"}),this.callIvrCharts.visualMap={show:!1,dimension:0,pieces:r},this.centerlineChart2.series[0].data=a.callConn||[];let d=[],g=0,p=-1,C=!1;a.callConn.forEach((t,e)=>{1==t.thresholdType&&(Number(t.value)>Number(t.threshold)?C||(g<e&&d.push({gt:g-1,lte:e-1,color:"#00DCAA"}),p=e-1,C=!0):C&&(d.push({gt:p,lte:e-1,color:"#DC0055"}),g=e,C=!1))}),C?d.push({gt:p,lte:a.callConn.length-1,color:"#DC0055"}):g<a.callConn.length&&d.push({gt:g-1,lte:a.callConn.length-1,color:"#00DCAA"}),this.centerlineChart2.visualMap={show:!1,dimension:0,pieces:d},this.centerlineChart3.series[0].data=a.callQueue||[];let u=[],v=0,b=-1,m=!1;a.callQueue.forEach((t,e)=>{1==t.thresholdType&&(Number(t.value)>Number(t.threshold)?m||(v<e&&u.push({gt:v-1,lte:e-1,color:"#FFDC00"}),b=e-1,m=!0):m&&(u.push({gt:b,lte:e-1,color:"#DC0055"}),v=e,m=!1))}),m?u.push({gt:b,lte:a.callQueue.length-1,color:"#DC0055"}):v<a.callQueue.length&&u.push({gt:v-1,lte:a.callQueue.length-1,color:"#FFDC00"}),this.centerlineChart3.visualMap={show:!1,dimension:0,pieces:u},this.agentIdleChart.series[0].data=a.agentIdle||[];let f=[],y=0,x=-1,F=!1;a.agentIdle.forEach((t,e)=>{1==t.thresholdType&&(Number(t.value)>Number(t.threshold)?F||(y<e&&f.push({gt:y-1,lte:e-1,color:"#FFDC00"}),x=e-1,F=!0):F&&(f.push({gt:x,lte:e-1,color:"#DC0055"}),y=e,F=!1))}),F?f.push({gt:x,lte:a.agentIdle.length-1,color:"#DC0055"}):y<a.agentIdle.length&&f.push({gt:y-1,lte:a.agentIdle.length-1,color:"#FFDC00"}),this.agentIdleChart.visualMap={show:!1,dimension:0,pieces:f},this.overflowWaitChart.series[0].data=a.overflowWait||[];let w=[],A=0,I=-1,_=!1;a.overflowWait.forEach((t,e)=>{1==t.thresholdType&&(Number(t.value)>Number(t.threshold)?_||(A<e&&w.push({gt:A-1,lte:e-1,color:"#FFDC00"}),I=e-1,_=!0):_&&(w.push({gt:I,lte:e-1,color:"#DC0055"}),A=e,_=!1))}),_?w.push({gt:I,lte:a.overflowWait.length-1,color:"#DC0055"}):A<a.overflowWait.length&&w.push({gt:A-1,lte:a.overflowWait.length-1,color:"#FFDC00"}),this.overflowWaitChart.visualMap={show:!1,dimension:0,pieces:w},this.callIvrCharts.series[0].markLine.data[0].yAxis=a.callIvr[0].threshold||[],this.callIvrChartCount=a.callIvr[a.callIvr.length-1].value||0,this.callIvrCharts.xAxis.data=a.callIvr.map(t=>t.time),this.callIvrWaring=a.callIvr[a.callIvr.length-1].value>a.callIvr[a.callIvr.length-1].threshold,this.centerlineChart2.series[0].data=a.callConn||[],this.centerlineChart2.series[0].markLine.data[0].yAxis=a.callConn[0].threshold||[],this.centerlineChartCount2=a.callConn[a.callConn.length-1].value||0,this.centerlineChart2.xAxis.data=a.callQueue.map(t=>t.time),1==a.callConn[a.callConn.length-1].thresholdType?this.centerlineWarning2=Number(a.callConn[a.callConn.length-1].value)>Number(a.callConn[a.callConn.length-1].threshold):this.centerlineWarning2=Number(a.callConn[a.callConn.length-1].value)<Number(a.callConn[a.callConn.length-1].threshold),this.centerlineChart3.series[0].data=a.callQueue||[],this.centerlineChart3.series[0].markLine.data[0].yAxis=a.callQueue[0].threshold||[],this.centerlineChartCount3=a.callQueue[a.callQueue.length-1].value||0,this.centerlineChart3.xAxis.data=a.callQueue.map(t=>t.time),1==a.callQueue[a.callQueue.length-1].thresholdType?this.centerlineWarning3=Number(a.callQueue[a.callQueue.length-1].value)>Number(a.callQueue[a.callQueue.length-1].threshold):this.centerlineWarning3=Number(a.callQueue[a.callQueue.length-1].value)<Number(a.callQueue[a.callQueue.length-1].threshold),this.bottomlineChart1.series[0].data=a.callRate||[],this.bottomlineChart1.series[0].markLine.data[0].yAxis=a.callRate[0].threshold||[],this.bottomlineChartCount1=a.callRate[a.callRate.length-1].value||0,this.bottomlineChart1.xAxis.data=a.callRate.map(t=>t.time),1==a.callRate[a.callRate.length-1].thresholdType?this.bottomlineWarning1=Number(a.callRate[a.callRate.length-1].value)>Number(a.callRate[a.callRate.length-1].threshold):this.bottomlineWarning1=Number(a.callRate[a.callRate.length-1].value)<Number(a.callRate[a.callRate.length-1].threshold),this.bottomlineChart2.series[0].data=a.callFail||[],this.bottomlineChart2.series[0].markLine.data[0].yAxis=a.callFail[0].threshold||[],this.bottomlineChartCount2=a.callFail[a.callFail.length-1].value||0,this.bottomlineChart2.xAxis.data=a.callFail.map(t=>t.time),1==a.callFail[a.callFail.length-1].thresholdType?this.bottomlineWarning2=Number(a.callFail[a.callFail.length-1].value)>Number(a.callFail[a.callFail.length-1].threshold):this.bottomlineWarning2=Number(a.callFail[a.callFail.length-1].value)<Number(a.callFail[a.callFail.length-1].threshold),this.agentIdleChart.series[0].data=a.agentIdle||[],this.agentIdleChart.series[0].markLine.data[0].yAxis=a.agentIdle[0].threshold||[],this.agentIdleChartCount=a.agentIdle[a.agentIdle.length-1].value||0,this.agentIdleChart.xAxis.data=a.agentIdle.map(t=>t.time),1==a.agentIdle[a.agentIdle.length-1].thresholdType?this.agentIdleWarning=Number(a.agentIdle[a.agentIdle.length-1].value)>Number(a.agentIdle[a.agentIdle.length-1].threshold):this.agentIdleWarning=Number(a.agentIdle[a.agentIdle.length-1].value)<Number(a.agentIdle[a.agentIdle.length-1].threshold),this.overflowWaitChart.series[0].data=a.overflowWait||[],this.overflowWaitChart.series[0].markLine.data[0].yAxis=a.overflowWait[0].threshold||[],this.overflowWaitChartCount=a.overflowWait[a.overflowWait.length-1].value||0,this.overflowWaitChart.xAxis.data=a.overflowWait.map(t=>t.time),1==a.overflowWait[a.overflowWait.length-1].thresholdType?this.overflowWaitWarning=Number(a.overflowWait[a.overflowWait.length-1].value)>Number(a.overflowWait[a.overflowWait.length-1].threshold):this.overflowWaitWarning=Number(a.overflowWait[a.overflowWait.length-1].value)<Number(a.overflowWait[a.overflowWait.length-1].threshold),this.agentWorkCount=a.agentWork[a.agentWork.length-1].value||0,this.agentSetbusyCount=a.agentSetbusy[a.agentSetbusy.length-1].value||0})},getNumber(){Object(o["i"])({}).then(t=>{if(console.log("getNumber API response:",t),t&&t.data){if(this.rightNumber=t.data.ACCEPTANCE_CALL_NETWORK_COUNT,this.leftNumber=t.data.SUM_TOTAL_COUNT,this.number12345=t.data.ACCEPTANCE_12345_COUNT,this.numberZhrx=t.data.ACCEPTANCE_OTHER_COUNT,this.numberWll=t.data.ACCEPTANCE_NETWORK,t.data.parallelNumber&&t.data.parallelNumber.cityJson){console.log("parallelNumber数据:",t.data.parallelNumber);const e=t.data.parallelNumber.cityJson;this.acceptChartBig={...this.acceptChartBig,xAxis:{...this.acceptChartBig.xAxis,data:e.groupNames||[]},series:[{...this.acceptChartBig.series[0],data:e.groupNumbers||[]}]}}else console.warn("parallelNumber数据不存在");if(t.data.parallelNumber&&t.data.parallelNumber.jinJIJson){console.log("jinJIJson数据:",t.data.parallelNumber.jinJIJson);const e=t.data.parallelNumber.jinJIJson;this.acceptChartBig1={...this.acceptChartBig1,xAxis:{...this.acceptChartBig1.xAxis,data:e.jinjiNames||[]},series:[{...this.acceptChartBig1.series[0],data:e.jinjiCallOutNumbers||[]}]}}else console.warn("jinJIJson数据不存在");this.bottomlineChart1.series[1].data=t.data.callRate||[],this.bottomlineChartCount3=t.data.callRate[t.data.callRate.length-1].value||0}else console.error("getNumber API返回数据格式错误")}).catch(t=>{console.error("获取数据失败:",t)})},toBack(){this.$emit("back")},toggleDialog2(){this.visible2=!this.visible2},toggleDialog3(){this.visible3=!this.visible3},handleChange(t){console.log("handleChange called with:",t),this.active=t.value},handleChange2(t){console.log("handleChange2 called with:",t),this.active2=t.value},handleChartReady(t){console.log(t+" chart is ready"),this.$nextTick(()=>{"acceptChartBig"===t?this.acceptChartBig={...this.acceptChartBig}:"acceptChartBig1"===t&&(this.acceptChartBig1={...this.acceptChartBig1})})},checkIframeMode(){const t=window.location.hash;if(console.log("完整哈希:",t),t.includes("?")){const e=t.split("?")[1];console.log("查询参数字符串:",e),this.isIframeMode=-1!==e.indexOf("iframe=true")}else this.isIframeMode=!1;console.log("iframe模式:",this.isIframeMode)}},created(){this.checkIframeMode()},beforeDestroy(){},mounted(){this.getCharts(),this.getNumber(),console.log("acceptChartBig配置:",this.acceptChartBig),console.log("acceptChartBig1配置:",this.acceptChartBig1),setInterval(()=>{this.getCharts(),this.getNumber()},5e3)},beforeDestroy(){clearInterval(this.timer)}},b=v,m=(a("27b0"),a("2877")),f=Object(m["a"])(b,l,i,!1,null,"a434d920",null);e["default"]=f.exports},e57e:function(t,e,a){t.exports=a.p+"img/warning.37f2056b.png"}}]);