(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-53c9f01f"],{cc2d:function(e,t,a){},d15c:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,model:e.searchForm,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择开始日期"},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择结束日期"},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",{attrs:{label:"维度"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:e.handleDateRangeChange},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{label:"日",value:"0"}}),t("el-option",{attrs:{label:"周",value:"1"}}),t("el-option",{attrs:{label:"月",value:"2"}}),t("el-option",{attrs:{label:"年",value:"3"}})],1)],1),t("el-form-item",{attrs:{label:"工作组"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.searchForm.WORKGROUPID,callback:function(t){e.$set(e.searchForm,"WORKGROUPID",t)},expression:"searchForm.WORKGROUPID"}},e._l(e.workGroupOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.handleExport}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"WORKGROUP",label:"班组",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"工号",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"NAME",label:"姓名",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_IN_COUNT_ALL",label:"呼入量",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_OUT_COUNT_ALL",label:"呼出量",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_IN_TIME_AVG",label:"平均呼入通话时长",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_IN_MAX_TIME",label:"最长呼入通话时长",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"ARRANGE_AVG_TIME",label:"平均话后处理时长",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"BUSY_COUNT",label:"离席次数",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"BUSY_TIME",label:"离席时长",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"EMO_COUNT",label:"情绪异常",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"SPEED_COUNT",label:"语速超快",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"ROB_TRAFFICE_COUNT",label:"抢话",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"DISABLE_COUNT",label:"禁用词/违规词",width:"150",align:"center"}}),t("el-table-column",{attrs:{label:"挂机满意度评价",align:"center"}},[t("el-table-column",{attrs:{prop:"PUSH_COUNT",label:"推送次数",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"MY_COUNT",label:"满意次数",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"BMY_COUNT",label:"不满意次数",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"VERY_BMY_COUNT",label:"非常不满意次数",width:"150",align:"center"}})],1)],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},l=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、情绪异常、语速超快、抢话和禁用词/敏感词：一通通话会出现多次")])])}],n=(a("a573"),a("88a7"),a("271a"),a("5494"),a("0c9f")),o=a("c2d0"),s=(a("cc2d"),{name:"AgentWorkPortrait",data(){return{loading:!1,searchForm:{BEGIN_TIME:Object(o["o"])().substring(0,10),END_TIME:Object(o["n"])().substring(0,10),statStepSize:"0",WORKGROUPID:""},searchedForm:{},workGroupOptions:[],tableData:[],currentPage:1,pageSize:15,total:0}},created(){this.getWorkGroupList(),this.fetchData()},methods:{async getWorkGroupList(){const e={params:{AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},controls:["common.workGroupList"]};try{const t=await Object(n["gb"])(e);if(t["common.workGroupList"]){const e=t["common.workGroupList"].data;this.workGroupOptions=Object.entries(e).map(([e,t])=>({value:e,label:t}))}}catch(t){console.error("获取工作组列表失败:",t)}},handleDateRangeChange(e){switch(e){case"0":this.searchForm.BEGIN_TIME=Object(o["o"])().substring(0,10),this.searchForm.END_TIME=Object(o["n"])().substring(0,10);break;case"1":this.searchForm.BEGIN_TIME=Object(o["k"])().substring(0,10),this.searchForm.END_TIME=Object(o["j"])().substring(0,10);break;case"2":this.searchForm.BEGIN_TIME=Object(o["i"])().substring(0,10),this.searchForm.END_TIME=Object(o["h"])().substring(0,10);break;case"3":this.searchForm.BEGIN_TIME=Object(o["m"])().substring(0,10),this.searchForm.END_TIME=Object(o["l"])().substring(0,10);break}},async fetchData(){this.loading=!0;try{const e={...this.searchForm,pageIndex:this.currentPage,pageSize:this.pageSize};this.searchedForm=e;const t=await Object(n["U"])(e);this.tableData=t.data,this.total=t.totalRow}catch(e){console.error("获取数据失败:",e)}finally{this.loading=!1}},handleSearch(){this.currentPage=1,this.fetchData()},handleSizeChange(){this.fetchData()},handleCurrentChange(){this.fetchData()},handleExport(){const e="/cx-report-12345",t=`${e}/servlet/export?action=exportAgentWorkPortraitStat&${new URLSearchParams(this.searchedForm).toString()}`;window.open(t)}}}),c=s,i=a("2877"),p=Object(i["a"])(c,r,l,!1,null,null,null);t["default"]=p.exports}}]);