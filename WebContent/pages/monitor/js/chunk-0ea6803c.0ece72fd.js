(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ea6803c"],{"906e":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"queryForm",attrs:{inline:!0,size:"small",model:e.queryParams}},[t("el-form-item",{attrs:{label:"工号"}},[t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入工号",clearable:""},model:{value:e.queryParams.agentId,callback:function(t){e.$set(e.queryParams,"agentId",t)},expression:"queryParams.agentId"}})],1),t("el-form-item",{attrs:{label:"姓名"}},[t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入姓名",clearable:""},model:{value:e.queryParams.agentName,callback:function(t){e.$set(e.queryParams,"agentName",t)},expression:"queryParams.agentName"}})],1),t("el-form-item",{attrs:{label:"手机号"}},[t("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入手机号",clearable:""},model:{value:e.queryParams.mobile,callback:function(t){e.$set(e.queryParams,"mobile",t)},expression:"queryParams.mobile"}})],1),t("el-form-item",{attrs:{label:"班组"}},[t("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择班组",filterable:"",clearable:""},model:{value:e.queryParams.workGroupId,callback:function(t){e.$set(e.queryParams,"workGroupId",t)},expression:"queryParams.workGroupId"}},e._l(e.workGroupOptions,(function(e,a){return t("el-option",{key:a,attrs:{label:e,value:a}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("查询")]),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-upload2"},on:{click:e.handleImport}},[e._v("导入")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"80",align:"center"}}),t("el-table-column",{attrs:{label:"用户头像",width:"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-image",{staticClass:"user-avatar",attrs:{src:e.row.IMG_URL||"/cc-base/static/images/user-avatar-large.png","preview-src-list":e.row.IMG_URL?[e.row.IMG_URL]:[],fit:"cover"}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),t("el-table-column",{attrs:{prop:"NAME",label:"姓名","min-width":"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleEdit(a.row)}}},[e._v(e._s(a.row.NAME))])]}}])}),t("el-table-column",{attrs:{prop:"AGENTID",label:"工号","min-width":"150",align:"center"}}),t("el-table-column",{attrs:{prop:"MOBILE",label:"手机号","min-width":"140",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTWORKGROUP",label:"工作组","min-width":"140",align:"center"}}),t("el-table-column",{attrs:{prop:"ROLE_NAME",label:"角色",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"SEX",label:"性别",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatSex(t.row.SEX))+" ")]}}])}),t("el-table-column",{attrs:{prop:"BIRTH_DAY",label:"出生日期","min-width":"140",align:"center"}}),t("el-table-column",{attrs:{prop:"ENTRY_DATE",label:"入职日期","min-width":"100",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"update:currentPage":function(t){return e.$set(e.pagination,"pageIndex",t)},"update:current-page":function(t){return e.$set(e.pagination,"pageIndex",t)},"update:pageSize":function(t){return e.$set(e.pagination,"pageSize",t)},"update:page-size":function(t){return e.$set(e.pagination,"pageSize",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),t("el-dialog",{attrs:{title:"导入用户",visible:e.importDialogVisible,width:"450px","custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importForm",attrs:{model:e.importForm}},[t("el-form-item",{attrs:{label:"导入文件"}},[t("el-upload",{ref:"upload",attrs:{action:"","file-list":e.fileList,"auto-upload":!1,accept:".xlsx,.xls",limit:1,"on-change":e.handleFileChange,"on-exceed":e.onExceed}},[t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("选择文件")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 只能上传xlsx/xls文件 ")])],1)],1),t("el-form-item",[t("el-link",{attrs:{type:"primary"},on:{click:e.downloadTemplateFile}},[e._v("下载导入模板")])],1),t("el-form-item",{attrs:{label:"文件名"}},[t("el-input",{attrs:{readonly:""},model:{value:e.importForm.fileName,callback:function(t){e.$set(e.importForm,"fileName",t)},expression:"importForm.fileName"}})],1)],1),t("template",{slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleFileUpload}},[e._v("上传")]),t("el-button",{on:{click:function(t){e.importDialogVisible=!1}}},[e._v("关闭")])],1)],2),t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.editDialogVisible,width:"700px","custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(t){e.editDialogVisible=t}}},[t("div",{staticClass:"avatar-section"},[t("div",{staticClass:"avatar-upload"},[t("el-upload",{staticClass:"avatar-uploader",attrs:{action:"","http-request":e.uploadAvatar,"show-file-list":!1,"before-upload":e.beforeAvatarUpload,accept:"image/gif,image/jpeg,image/jpg,image/png,image/svg"}},[e.form.IMG_URL?t("el-image",{staticClass:"avatar-image",attrs:{src:e.form.IMG_URL,fit:"cover"}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})],1)],1)]),t("div",{staticClass:"info-section"},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"small"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"账号",prop:"AGENTID"}},[t("el-input",{attrs:{disabled:e.isEdit},model:{value:e.form.AGENTID,callback:function(t){e.$set(e.form,"AGENTID",t)},expression:"form.AGENTID"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"姓名",prop:"NAME"}},[t("el-input",{attrs:{disabled:e.isEdit},model:{value:e.form.NAME,callback:function(t){e.$set(e.form,"NAME",t)},expression:"form.NAME"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"工作组",prop:"AGENTWORKGROUP"}},[t("el-input",{attrs:{disabled:e.isEdit},model:{value:e.form.AGENTWORKGROUP,callback:function(t){e.$set(e.form,"AGENTWORKGROUP",t)},expression:"form.AGENTWORKGROUP"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"手机号",prop:"MOBILE"}},[t("el-input",{model:{value:e.form.MOBILE,callback:function(t){e.$set(e.form,"MOBILE",t)},expression:"form.MOBILE"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"性别",prop:"SEX"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.SEX,callback:function(t){e.$set(e.form,"SEX",t)},expression:"form.SEX"}},e._l(e.sexOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"角色",prop:"ROLE"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.ROLE,callback:function(t){e.$set(e.form,"ROLE",t)},expression:"form.ROLE"}},e._l(e.roleOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"生日",prop:"BIRTH_DAY"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择生日","value-format":"yyyy-MM-dd"},model:{value:e.form.BIRTH_DAY,callback:function(t){e.$set(e.form,"BIRTH_DAY",t)},expression:"form.BIRTH_DAY"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"入职日期",prop:"ENTRY_DATE"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择入职日期","value-format":"yyyy-MM-dd"},model:{value:e.form.ENTRY_DATE,callback:function(t){e.$set(e.form,"ENTRY_DATE",t)},expression:"form.ENTRY_DATE"}})],1)],1)],1)],1)],1),t("template",{slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")]),t("el-button",{on:{click:function(t){e.editDialogVisible=!1}}},[e._v("关闭")])],1)],2)],1)},r=[],l=(a("0643"),a("fffc"),a("c24f")),s={name:"UserList",data(){return{queryParams:{agentId:"",agentName:"",mobile:"",workGroupId:""},loading:!1,tableData:[],pagination:{pageIndex:1,pageSize:15,total:0},workGroupOptions:{},sexOptions:[{value:"1",label:"男"},{value:"0",label:"女"}],roleOptions:[{value:"0",label:"坐席员"},{value:"1",label:"班长"},{value:"2",label:"值班长"},{value:"3",label:"督导"}],importDialogVisible:!1,importForm:{fileName:""},fileList:[],editDialogVisible:!1,form:{USER_ID:"",AGENTID:"",NAME:"",AGENTWORKGROUP:"",SEX:"",ROLE:"",BIRTH_DAY:"",ENTRY_DATE:"",IMG_URL:"",MOBILE:""},rules:{NAME:[{required:!0,message:"请输入姓名",trigger:"blur"}],AGENTID:[{required:!0,message:"请输入工号",trigger:"blur"}]},dialogTitle:"编辑用户",isEdit:!0}},created(){this.getList(),this.getWorkGroupDict()},methods:{getList(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pagination.pageIndex,pageSize:this.pagination.pageSize,pageType:3};Object(l["c"])(e).then(e=>{this.tableData=e.data||[],this.pagination.total=e.totalRow||0,this.loading=!1}).catch(()=>{this.$message.error("查询失败"),this.loading=!1})},handleQuery(){this.pagination.pageIndex=1,this.getList()},resetQuery(){this.$refs.queryForm.resetFields(),this.queryParams={agentId:"",agentName:"",mobile:"",workGroupId:""},this.handleQuery()},handleSizeChange(e){this.pagination.pageSize=e,this.getList()},handleCurrentChange(e){this.pagination.pageIndex=e,this.getList()},formatSex(e){const t=this.sexOptions.find(t=>t.value===e);return t?t.label:e},handleEdit(e){this.editDialogVisible=!0,this.isEdit=!0,this.dialogTitle="编辑用户",this.form=JSON.parse(JSON.stringify(e)),this.form.IMG_URL||(this.form.IMG_URL="")},uploadAvatar(e){const t=e.file;if(!t)return;const a=["image/gif","image/jpeg","image/jpg","image/png","image/svg"];a.includes(t.type)?t.size>4194304?this.$message.error("文件大小限制4M！"):Object(l["e"])(t,{storage:"local",fileType:".jpg,.png,.jpeg",createThumb:!0,fileMaxSize:4096,busiId:"10023",busiType:"headImg",isDelHistory:"true"}).then(e=>{if(1===e.state){const t=e.data.idUrl;this.form.IMG_URL=t||"",this.$message.success(e.msg||"上传成功")}else{const t=e.msg||"上传失败!";this.$message.error(t)}}).catch(()=>{this.$message.error("上传失败!")}):this.$message.error("上传仅支持格式：.jpg,.png,.jpeg,.gif,.svg")},beforeAvatarUpload(e){const t=["image/gif","image/jpeg","image/jpg","image/png","image/svg"];return t.includes(e.type)?!(e.size>4194304)||(this.$message.error("文件大小限制4M！"),!1):(this.$message.error("上传仅支持格式：.jpg,.png,.jpeg,.gif,.svg"),!1)},handleSubmit(){this.$refs.form.validate(e=>{if(!e)return;const t={userId:this.form.USER_ID,sex:this.form.SEX,role:this.form.ROLE,brithDate:this.form.BIRTH_DAY,entryDate:this.form.ENTRY_DATE,mobile:this.form.MOBILE,userNo:this.form.AGENTID,url:this.form.IMG_URL||""};this.isEdit||(t.agentName=this.form.NAME,t.workGroup=this.form.AGENTWORKGROUP),this.loading=!0;const a=this.isEdit?l["b"]:l["a"];a(t).then(e=>{1===e.state?(this.$message.success(this.isEdit?"修改信息成功":"新增用户成功"),this.editDialogVisible=!1,this.getList()):this.$message.error(e.msg||(this.isEdit?"修改失败":"新增失败")),this.loading=!1}).catch(()=>{this.$message.error(this.isEdit?"修改失败":"新增失败"),this.loading=!1})})},handleImport(){this.importDialogVisible=!0,this.fileList=[],this.importForm.fileName=""},handleFileChange(e,t){this.fileList=t,e&&(this.importForm.fileName=e.name)},onExceed(e,t){this.$message.warning("当前限制选择 1 个文件")},handleFileUpload(){if(0===this.fileList.length)return void this.$message.warning("请选择导入文件");const e=this.fileList[0],t=e.name.substring(e.name.lastIndexOf(".")+1).toLowerCase();if("xls"!==t&&"xlsx"!==t)return void this.$message.error("导入文件格式不正确");const a=new FormData;a.append("uploadFile",e.raw),this.loading=!0,Object(l["f"])(a).then(e=>{1===e.state?(this.$message.success(e.msg||"导入成功"),this.importDialogVisible=!1,this.getList()):this.$message.error(e.msg||"导入失败"),this.loading=!1}).catch(()=>{this.$message.error("导入失败"),this.loading=!1})},downloadTemplateFile(){window.open("/cx-monitordata-12345/servlet/user?action=DownLoadTemp","_blank")},handleAdd(){this.editDialogVisible=!0,this.isEdit=!1,this.dialogTitle="新增用户",this.form={USER_ID:"",AGENTID:"",NAME:"",AGENTWORKGROUP:"",SEX:"",ROLE:"",BIRTH_DAY:"",ENTRY_DATE:"",IMG_URL:"",MOBILE:""}},getWorkGroupDict(){Object(l["d"])().then(e=>{this.workGroupOptions=e.data||{}}).catch(()=>{this.$message.error("获取班组字典失败")})}}},o=s,n=(a("daa9"),a("2877")),c=Object(n["a"])(o,i,r,!1,null,"f7db901a",null);t["default"]=c.exports},c24f:function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return c}));a("a573");var i=a("b775");function r(e){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserList",method:"post",data:{data:e},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=EditAgent",method:"post",data:{data:e},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(e){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=AddAgent",method:"post",data:{data:e},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserUpload",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function n(){return Object(i["a"])({url:"/cx-monitordata-12345/webcall?action=common.workGroupPortraitList",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function c(e,t){const a=Object.entries(t).map(([e,t])=>`${e}=${t}`).join("&");return Object(i["a"])({url:"/cc-base/servlet/attachment?action=upload2&"+a,method:"post",data:{file:e},headers:{"Content-Type":"multipart/form-data"}})}},c829:function(e,t,a){},daa9:function(e,t,a){"use strict";a("c829")}}]);