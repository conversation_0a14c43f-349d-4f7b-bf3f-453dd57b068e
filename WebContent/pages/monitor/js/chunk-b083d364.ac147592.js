(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b083d364"],{"60c4":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"业务类型"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.SERVICENO,callback:function(t){e.$set(e.searchForm,"SERVICENO",t)},expression:"searchForm.SERVICENO"}},e._l(e.serviceTypeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"统计步长"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{label:"时段",value:"0"}}),t("el-option",{attrs:{label:"半小时",value:"1"}}),t("el-option",{attrs:{label:"小时",value:"2"}}),t("el-option",{attrs:{label:"天",value:"3"}}),t("el-option",{attrs:{label:"周",value:"4"}}),t("el-option",{attrs:{label:"旬",value:"5"}}),t("el-option",{attrs:{label:"月",value:"6"}}),t("el-option",{attrs:{label:"季度",value:"7"}}),t("el-option",{attrs:{label:"半年",value:"8"}}),t("el-option",{attrs:{label:"年",value:"9"}})],1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getDateTime(t.row))+" ")]}}])}),t("el-table-column",{attrs:{prop:"SERVICENO",label:"业务类型","min-width":"150",align:"center"}}),t("el-table-column",{attrs:{prop:"CALL_COUNT",label:"呼入电话量","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_COUNT",label:"应答量","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_RATE",label:"应答率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.AGENT_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"SUCC_COUNT",label:"接通数量","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"SUCC_RATE",label:"接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.SUCC_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AVG_WAIT_TIME",label:"平均排队时长","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"MAX_WAIT_TIME",label:"最长排队等待时间","min-width":"150",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_TIME",label:"通话时长","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"AVG_TIME",label:"平均通话时长","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"ACK_TIME",label:"应答时长","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"WAIT_FAIL_COUNT",label:"排队挂机数","min-width":"100",align:"center"}}),t("el-table-column",{attrs:{prop:"WAIT_FAIL_RATE",label:"排队挂机率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.WAIT_FAIL_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AVG_ACK_TIME",label:"平均排队挂机时长","min-width":"150",align:"center"}}),t("el-table-column",{attrs:{prop:"COUNT20",label:"等待小于20秒电话数量","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"COUNT20_40",label:"等待20秒~40秒电话数量","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"COUNT40_60",label:"等待40秒~60秒电话数量","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"COUNT60",label:"等待60秒以上电话数量","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"COUNT_RATE20",label:"20秒接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.COUNT_RATE20))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、呼入电话量：呼入通话次数")]),t("p",[e._v("2、应答量：呼入通话ivr时长+坐席应答时长>0的次数")]),t("p",[e._v("3、应答率：应答量/呼入电话量")]),t("p",[e._v("4、接通数量：呼入通话ivr时长+坐席通话时长>0的次数")]),t("p",[e._v("5、接通率：接通数量/呼入电话量")]),t("p",[e._v("6、平均排队时长：呼入通话进行排队的总时长/呼入通话进去排队的次数")]),t("p",[e._v("7、最长排队等待时间：呼入通话进行排队的最长时间")]),t("p",[e._v("8、通话时长：呼入通话坐席应答时长+坐席通话时长总和")]),t("p",[e._v("9、平均通话时长：通话时长/接通数量")]),t("p",[e._v("10、应答时长：呼入通话坐席的应答总时长")]),t("p",[e._v("11、排队挂机数：呼入通话存在排队次数并且坐席应答时长+坐席通话时长=0")]),t("p",[e._v("12、排队挂机率：排队挂机数/呼入通话进入排队的数量")]),t("p",[e._v("13、平均排队挂机时长：呼入通话进入排队的总时长/呼入通话进入排队的总次数")]),t("p",[e._v("14、等待小于20秒电话数量：呼入通话进入排队时间<20秒或者未进入排队的次数")]),t("p",[e._v("15、等待20秒~40秒电话数量：呼入通话进入排队时间介于20-40之间的次数")]),t("p",[e._v("16、等待40秒~60秒电话数量：呼入通话进入排队时间介于40-60之间的次数")]),t("p",[e._v("17、等待60秒以上电话数量：呼入通话进入排队时间大于60的次数")]),t("p",[e._v("18、20秒接通率：等待小于20秒电话数量/呼入电话量")]),t("p",[e._v("统计呼入号码来源：('12345','65603424','12365','12312','12385','12318','12301','65603429','12331','65603451','12319','65603450','96391')")])])}],n=(a("0643"),a("4e3e"),a("a573"),a("88a7"),a("271a"),a("5494"),a("c2d0")),i=a("0c9f"),o=a("b775"),s={name:"SkillGroupStatList",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(n["e"])(),END_TIME:Object(n["d"])(),SERVICENO:"",statStepSize:"3"},searchedForm:{},serviceTypeOptions:[]}},created(){this.getServiceTypeOptions(),this.loadData()},methods:{getServiceTypeOptions(){Object(i["gb"])({},["common.serviceTypeList"]).then(e=>{if(e["common.serviceTypeList"]){const t=e["common.serviceTypeList"].data||{};this.serviceTypeOptions=Object.entries(t).map(([e,t])=>({value:e,label:t.split("|")[1]||t}))}}).catch(()=>{this.$message.error("获取业务类型列表失败")})},loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,SERVICENO:this.searchForm.SERVICENO||"",statStepSize:this.searchForm.statStepSize};this.searchedForm=e,Object(o["a"])({url:"/cx-report-12345/webcall?action=statDao.skillGroupStat",method:"post",data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出技能组综合统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=new URLSearchParams;Object.keys(this.searchForm).forEach(t=>{e.append(t,this.searchForm[t]||"")}),window.location.href="/cx-report-12345/servlet/export?action=exportSkillGroupStat&"+e.toString()}).catch(()=>{})},getDateTime:n["c"],formatRate:n["b"]}},c=s,p=(a("735e"),a("2877")),m=Object(p["a"])(c,l,r,!1,null,null,null);t["default"]=m.exports},"735e":function(e,t,a){"use strict";a("d48d")},d48d:function(e,t,a){}}]);