(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-78fa235c"],{5800:function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-page"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[a("el-form-item",{attrs:{label:"主叫号码"}},[a("el-input",{attrs:{placeholder:"主叫号码",clearable:""},model:{value:e.queryParams.CALLERNO,callback:function(a){e.$set(e.queryParams,"CALLERNO",a)},expression:"queryParams.CALLERNO"}})],1),a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(a){e.$set(e.queryParams,"BEGIN_TIME",a)},expression:"queryParams.BEGIN_TIME"}})],1),a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(a){e.$set(e.queryParams,"END_TIME",a)},expression:"queryParams.END_TIME"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),a("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫号码",align:"center"}}),a("el-table-column",{attrs:{prop:"START_TIME",label:"呼叫开始时间",align:"center","min-width":"250"}}),a("el-table-column",{attrs:{prop:"IVR_TIME",label:"IVR持续时间",align:"center"}}),a("el-table-column",{attrs:{prop:"ACK_TIME",label:"人工应答时长",align:"center"}}),a("el-table-column",{attrs:{prop:"AGENT_TIME",label:"座席服务时间",align:"center"}}),a("el-table-column",{attrs:{prop:"USER_TIME",label:"客户通话时间",align:"center"}}),a("el-table-column",{attrs:{prop:"AGENTID",label:"坐席工号",align:"center"}})],1),a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,a=e._self._c;return a("div",{staticClass:"stat-desc"},[a("h4",[e._v("统计口径")]),a("p",[e._v("1、呼叫开始时间：呼入通话的此通通话开始时间")]),a("p",[e._v("2、IVR持续时间：最后一次进入ivr的时长")]),a("p",[e._v("3、人工应答时长：此通电话坐席的应答时长")]),a("p",[e._v("4、座席服务时间：此通电话坐席的通话时长")]),a("p",[e._v("5、客户通话时间：此通电话坐席通话时长+坐席应答时长")]),a("p",[e._v("6、坐席工号：此通电话第一个接通的坐席工号")]),a("p",[e._v("7、数据来源：被叫号码('12345','65603424','12365','12312','12385','12318','12301','65603429','12331','65603451','12319','65603450','96391')")])])}],i=t("0c9f"),s=t("c2d0"),n={name:"CallAnalysisStat",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},queryParams:{CALLERNO:"",BEGIN_TIME:"",END_TIME:""}}},created(){this.initTime(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(s["g"])(),this.queryParams.END_TIME=Object(s["f"])()},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(i["W"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={CALLERNO:"",BEGIN_TIME:"",END_TIME:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出全程通话明细报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(i["n"])(this.searchForm))}).catch(()=>{})}}},o=n,c=(t("fd05"),t("2877")),p=Object(c["a"])(o,l,r,!1,null,null,null);a["default"]=p.exports},7110:function(e,a,t){},fd05:function(e,a,t){"use strict";t("7110")}}]);