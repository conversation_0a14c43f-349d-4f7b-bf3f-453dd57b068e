(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-14e48f53"],{"6cd0":function(e,t,a){"use strict";a("f17a")},"97d8":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"坐席工号"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENTID,callback:function(t){e.$set(e.queryParams,"AGENTID",t)},expression:"queryParams.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"DATE_TIME",label:"查询时段",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"工号",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTNAME",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{prop:"OPERATETYPE",label:"操作类型",align:"center"}}),t("el-table-column",{attrs:{prop:"BEGIN_TIME",label:"开始时间",align:"center"}}),t("el-table-column",{attrs:{prop:"END_TIME",label:"结束时间",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、坐席的操作记录明细数据")])])}],r=(a("a573"),a("0c9f")),n=a("c2d0"),s={name:"AgentHistoricalMonitoring",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},agentOptions:[],queryParams:{AGENTID:"",BEGIN_TIME:"",END_TIME:""}}},created(){this.initTime(),this.getAgentList(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(n["g"])(),this.queryParams.END_TIME=Object(n["f"])()},getAgentList(){Object(r["gb"])({AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},["common.agentList"]).then(e=>{if(e["common.agentList"]&&e["common.agentList"].data){const t=e["common.agentList"].data;this.agentOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}})},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(r["O"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={AGENTID:"",BEGIN_TIME:"",END_TIME:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出座席历史监控报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(r["g"])(this.searchedForm))}).catch(()=>{})}}},o=s,c=(a("6cd0"),a("2877")),m=Object(c["a"])(o,l,i,!1,null,null,null);t["default"]=m.exports},f17a:function(e,t,a){}}]);