(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-611004e1"],{"1f63":function(e,t,a){},"3c58":function(e,t,a){"use strict";a("1f63")},"4a93":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{attrs:{inline:!0,size:"small",model:e.searchForm},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"操作时间"}},[t("el-date-picker",{attrs:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},model:{value:e.searchForm.timeRange,callback:function(t){e.$set(e.searchForm,"timeRange",t)},expression:"searchForm.timeRange"}})],1),t("el-form-item",{attrs:{label:"操作人姓名"}},[t("el-input",{attrs:{placeholder:"请输入操作人姓名",clearable:""},model:{value:e.searchForm.operatingName,callback:function(t){e.$set(e.searchForm,"operatingName",t)},expression:"searchForm.operatingName"}})],1),t("el-form-item",{attrs:{label:"操作人工号"}},[t("el-input",{attrs:{placeholder:"请输入操作人工号",clearable:""},model:{value:e.searchForm.operatingAcct,callback:function(t){e.$set(e.searchForm,"operatingAcct",t)},expression:"searchForm.operatingAcct"}})],1),t("el-form-item",{attrs:{label:"操作模块"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.operatingModel,callback:function(t){e.$set(e.searchForm,"operatingModel",t)},expression:"searchForm.operatingModel"}},[t("el-option",{attrs:{label:"预测",value:"0"}}),t("el-option",{attrs:{label:"报表",value:"1"}}),t("el-option",{attrs:{label:"配置",value:"2"}}),t("el-option",{attrs:{label:"调度",value:"3"}}),t("el-option",{attrs:{label:"干预",value:"4"}})],1)],1),t("el-form-item",{attrs:{label:"操作类型"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.operatingType,callback:function(t){e.$set(e.searchForm,"operatingType",t)},expression:"searchForm.operatingType"}},[t("el-option",{attrs:{label:"新增",value:"1"}}),t("el-option",{attrs:{label:"修改",value:"2"}}),t("el-option",{attrs:{label:"删除",value:"3"}}),t("el-option",{attrs:{label:"导入",value:"4"}}),t("el-option",{attrs:{label:"导出",value:"5"}}),t("el-option",{attrs:{label:"处理",value:"6"}}),t("el-option",{attrs:{label:"干预",value:"7"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v(e._s(e.$t("查询")))]),t("el-button",{on:{click:e.handleReset}},[e._v(e._s(e.$t("重置")))])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{prop:"OPERATING_NAME",label:"操作人姓名","min-width":"120"}}),t("el-table-column",{attrs:{prop:"OPERATING_ACCT",label:"操作人工号","min-width":"140"}}),t("el-table-column",{attrs:{prop:"OPERATING_TIME",label:"操作时间","min-width":"160"}}),t("el-table-column",{attrs:{prop:"OPERATING_MODEL",label:"操作模块","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getModuleName(t.row.OPERATING_MODEL))+" ")]}}])}),t("el-table-column",{attrs:{prop:"OPERATING_TYPE",label:"操作类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getTypeName(t.row.OPERATING_TYPE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"OPERATING_DESC",label:"操作描述","min-width":"300","show-overflow-tooltip":""}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.pageIndex,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)},o=[],r=a("b775");function i(e){return Object(r["a"])({url:"/cx-monitordata-12345/webcall",method:"POST",params:{action:"log.list"},data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}var n={name:"OperationLog",data(){return{loading:!1,searchForm:{timeRange:[],operatingName:"",operatingAcct:"",operatingModel:"",operatingType:""},tableData:[],pagination:{pageIndex:1,pageSize:10,total:0},moduleMap:{0:"预测",1:"报表",2:"配置",3:"调度",4:"干预"},typeMap:{1:"新增",2:"修改",3:"删除",4:"导入",5:"导出",6:"处理",7:"干预"}}},created(){this.getList()},methods:{async getList(){this.loading=!0;try{const e={pageIndex:this.pagination.pageIndex,pageSize:this.pagination.pageSize,pageType:2};this.searchForm.operatingName&&(e.operatingName=this.searchForm.operatingName),this.searchForm.operatingAcct&&(e.operatingAcct=this.searchForm.operatingAcct),this.searchForm.operatingModel&&(e.operatingModel=this.searchForm.operatingModel),this.searchForm.operatingType&&(e.operatingType=this.searchForm.operatingType),this.searchForm.timeRange&&2===this.searchForm.timeRange.length&&(e.startTime=this.searchForm.timeRange[0],e.endTime=this.searchForm.timeRange[1]);const t=await i(e);console.log("getOperationLogList",t),1===t.state?(this.tableData=t.data||[],this.pagination.total=t.totalRow||0):(this.tableData=[],this.pagination.total=0)}catch(e){console.error("获取操作日志列表失败:",e),this.$message.error("获取操作日志列表失败"),this.tableData=[],this.pagination.total=0}finally{this.loading=!1}},handleSearch(){this.pagination.pageIndex=1,this.getList()},handleReset(){this.searchForm={timeRange:[],operatingName:"",operatingAcct:"",operatingModel:"",operatingType:""},this.pagination.pageIndex=1,this.getList()},handleSizeChange(e){this.pagination.pageSize=e,this.pagination.pageIndex=1,this.getList()},handleCurrentChange(e){this.pagination.pageIndex=e,this.getList()},getModuleName(e){return this.moduleMap[e]||e},getTypeName(e){return this.typeMap[e]||e}}},s=n,p=(a("3c58"),a("2877")),c=Object(p["a"])(s,l,o,!1,null,"e2432bbc",null);t["default"]=c.exports}}]);