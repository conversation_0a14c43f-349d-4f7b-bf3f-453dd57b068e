(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8f84cd28"],{"9ac1":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",{attrs:{label:"统计步长"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{value:"0",label:"时段"}}),t("el-option",{attrs:{value:"1",label:"半小时"}}),t("el-option",{attrs:{value:"2",label:"小时"}}),t("el-option",{attrs:{value:"3",label:"天"}}),t("el-option",{attrs:{value:"6",label:"月"}}),t("el-option",{attrs:{value:"9",label:"年"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"120",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getDateTime(t.row))+" ")]}}])}),t("el-table-column",{attrs:{prop:"CALLIN_COUNT",label:"呼入次数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLIN_ANSWER_COUNT",label:"接通次数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLIN_RATE",label:"接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.CALLIN_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"IVR_USE_COUNT",label:"IVR占用数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"IVR_ANSWER_COUNT",label:"IVR连接数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"IVR_RATE",label:"IVR连接率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.IVR_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AGENT_USE_COUNT",label:"人工占用数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_ANSWER_COUNT",label:"人工接通数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_RATE",label:"人工接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.AGENT_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"LOST_COUNT",label:"大网呼损量","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"LOST_RATE",label:"大网呼损率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.LOST_RATE))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、呼入次数：呼入坐席的电话数量（根据坐席查询）")]),t("p",[e._v("2、接通次数：呼入的电话转坐席的数量")]),t("p",[e._v("3、接通率：接通次数/呼入次数")]),t("p",[e._v("4、IVR占用数：呼入电话进入ivr次数>0的数量")]),t("p",[e._v("5、IVR连接数：呼入电话进入ivr时长>0的数量")]),t("p",[e._v("6、IVR连接率：IVR连接数/IVR占用数")]),t("p",[e._v("7、人工占用数：呼入电话进入排队次数+坐席次数>0的数量")]),t("p",[e._v("8、人工接通数：呼入电话的坐席接入的数量")]),t("p",[e._v("9、人工接通率：人工接通数/人工占用数")]),t("p",[e._v("10、大网呼损量：大网呼损数量")]),t("p",[e._v("11、大网呼损率：大网呼损量/人工接通数")])])}],n=a("0c9f"),i=a("c2d0"),o={name:"AgentEntStat",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),statStepSize:"3"}}},created(){this.loadData()},methods:{loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize||"0"};this.searchedForm=e,Object(n["N"])(e).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出企业热线话务量报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.location.href=Object(n["f"])(this.searchForm)}).catch(()=>{})},getDateTime:i["c"],formatRate:i["b"]}},s=o,c=(a("e4bc"),a("2877")),p=Object(c["a"])(s,l,r,!1,null,null,null);t["default"]=p.exports},e4bc:function(e,t,a){"use strict";a("ff9b")},ff9b:function(e,t,a){}}]);