(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0139e053"],{"1a95":function(e,a,t){},"3d02":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-page"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small",model:e.searchForm}},[a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.BEGIN_TIME,callback:function(a){e.$set(e.searchForm,"BEGIN_TIME",a)},expression:"searchForm.BEGIN_TIME"}})],1),a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.END_TIME,callback:function(a){e.$set(e.searchForm,"END_TIME",a)},expression:"searchForm.END_TIME"}})],1),a("el-form-item",{attrs:{label:"业务类型"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.SERVICENO,callback:function(a){e.$set(e.searchForm,"SERVICENO",a)},expression:"searchForm.SERVICENO"}},e._l(e.serviceTypeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")])],1),a("el-divider",{attrs:{direction:"vertical"}}),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),a("el-table-column",{attrs:{prop:"SERVICEDESC",label:"业务类型",align:"center"}}),a("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫号码",align:"center"}}),a("el-table-column",{attrs:{prop:"WAITBEGIN",label:"排队开始时间",align:"center"}}),a("el-table-column",{attrs:{prop:"WAITEND",label:"通话开始时间/放弃开始时间",align:"center"}}),a("el-table-column",{attrs:{prop:"WAITTIME",label:"等待时长",align:"center"}})],1),a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},l=[function(){var e=this,a=e._self._c;return a("div",{staticClass:"stat-desc"},[a("h4",[e._v("统计口径")]),a("p",[e._v("1、排队开始时间：呼入通话如果进入排队，该时间为开始时间否则为空")]),a("p",[e._v("2、通话开始时间/放弃开始时间：如果排队过程中该通电话被接通，此时间为通话开始时间，否则为排队结束时间")]),a("p",[e._v("3、等待时长：排队开始时间-排队结束时间")])])}],i=(t("a573"),t("c2d0")),n=t("0c9f"),s={name:"CallQueueCount",data(){return{searchForm:{BEGIN_TIME:Object(i["g"])(),END_TIME:Object(i["f"])(),SERVICENO:"",pageIndex:1,pageSize:15},searchedForm:{},loading:!1,tableData:[],pagination:{currentPage:1,pageSize:15,total:0},serviceTypeOptions:[]}},created(){this.loadDictionaries(),this.loadTableData()},methods:{loadDictionaries(){const e={params:{AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},controls:["common.serviceTypeList"]};Object(n["G"])(e).then(e=>{if(e["common.serviceTypeList"]){const a=e["common.serviceTypeList"].data||{};this.serviceTypeOptions=Object.keys(a).map(e=>({value:e,label:a[e]}))}}).catch(e=>{this.$message.error("获取字典数据失败")})},loadTableData(){this.loading=!0;const e={...this.searchForm,pageType:3,pageIndex:this.pagination.currentPage,pageSize:this.pagination.pageSize};this.searchedForm=e,Object(n["F"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.pagination.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败")}).finally(()=>{this.loading=!1})},exportDetail(){this.$confirm("是否导出呼叫排队明细报表?","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(n["v"])(this.searchForm))})},handleSizeChange(e){this.pagination.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pagination.currentPage=e,this.loadTableData()},resetForm(){this.searchForm={BEGIN_TIME:Object(i["g"])(),END_TIME:Object(i["f"])(),SERVICENO:"",pageIndex:1,pageSize:15},this.pagination.currentPage=1,this.loadTableData()}}},o=s,c=(t("d861"),t("2877")),p=Object(c["a"])(o,r,l,!1,null,null,null);a["default"]=p.exports},d861:function(e,a,t){"use strict";t("1a95")}}]);