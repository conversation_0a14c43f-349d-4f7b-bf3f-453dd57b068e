(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-114442f8"],{"185c":function(t,a,i){t.exports=i.p+"img/icon-5.e6f9f41e.svg"},"2dea":function(t,a,i){t.exports=i.p+"img/icon-3.972f2549.svg"},"556f":function(t,a,i){t.exports=i.p+"img/icon-4.2a62f4ee.svg"},aac3:function(t,a,i){"use strict";i.r(a);var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"five-dim-config"},[t._l(t.parts,(function(i,e){return[a("div",{staticClass:"head-bar"},[a("RigthDetailBg",{attrs:{title:i+"五维图配置",hide:""}}),a("div",{staticClass:"btn artFont",on:{click:function(a){return t.openDrawer(e)}}},[t._v(" 修改 "),a("i",{staticClass:"el-icon-arrow-right"})])],1),a("div",{staticClass:"card-container"},t._l(t.cardList,(function(i){return a("div",{staticClass:"card"},[a("el-image",{staticStyle:{width:"110px",height:"128px"},attrs:{src:i.icon}}),a("div",{staticClass:"label artFont"},[t._v(t._s(i.label))]),a("div",{staticStyle:{flex:"1"}}),a("div",{staticClass:"data-wrapper"},t._l(t.dataMap,(function(s,r){return a("div",{staticClass:"data-item"},[a("div",{staticClass:"label"},[t._v(t._s(s))]),a("span",{staticClass:"value"},[t._v(" "+t._s(t.data[e]&&t.data[e][`${r}_${i.prop}`]||0)+" ")]),a("span",{staticClass:"unit"},[t._v(" "+t._s(i.unit)+" ")])])})),0)],1)})),0)]})),a("el-drawer",{attrs:{visible:t.drawerVisible,title:t.drawerTitle,size:"700px","custom-class":"default-drawer","append-to-body":""},on:{"update:visible":function(a){t.drawerVisible=a}}},[a("div",{staticClass:"drawer-body"},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px",size:"small"}},[t._l(t.cardList,(function(i){return[a("div",{staticClass:"item-title artFont"},[t._v(t._s(i.label))]),a("div",{staticStyle:{clear:"both",overflow:"hidden"}},t._l(t.dataMap,(function(e,s){return a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"flex-item",attrs:{label:e,prop:`${s}_${i.prop}`,rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:1e6,controls:!1},model:{value:t.form[`${s}_${i.prop}`],callback:function(a){t.$set(t.form,`${s}_${i.prop}`,a)},expression:"form[`${key}_${item.prop}`]"}}),a("span",{staticClass:"unit"},[t._v(t._s(i.unit))])],1)],1)})),1)]}))],2)],1),a("div",{staticClass:"drawer-footer"},[a("el-button",{attrs:{type:"primary",plain:""},on:{click:t.closeDrawer}},[t._v("关闭")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSave}},[t._v("保存")])],1)])],2)},s=[],r=i("764f"),n=i("b775");function o(t="WORK_NUMBER_CONFIG"){return Object(n["a"])({method:"POST",url:"/cx-monitordata-12345/servlet/conf",params:{action:"SelectById"},data:{type:t}})}function l(t){return Object(n["a"])({method:"POST",url:"/cx-monitordata-12345/servlet/conf",params:{action:"Update"},data:{...t,type:"WORK_NUMBER_CONFIG"}})}var c={components:{RigthDetailBg:r["a"]},data(){return{parts:{AGENT:"坐席",WORK_GROUP:"班组"},cardList:[{label:"接线量上限值",unit:"分",prop:"CALL_IN_NUMBER",icon:i("faa4")},{label:"平均通话时长上限值",unit:"秒",prop:"CALL_TIME_AVG",icon:i("ebc6")},{label:"平均话后处理时长上限值 ",unit:"秒",prop:"AFTER_TIME_AVG",icon:i("2dea")},{label:"签入时长上限值 ",unit:"小时",prop:"LOGIN_TIME",icon:i("556f")},{label:"工单量上限值 ",unit:"",prop:"ORDER_COUNT",icon:i("185c")}],dataMap:{DAY:"当天",WEEK:"前7天",MONTH:"前30天"},data:{},form:{},drawerVisible:!1,drawerTitle:"",type:""}},computed:{},created(){this.getFiveDimConfig()},methods:{async getFiveDimConfig(){const t=await o();1==t.state&&(this.data=t.data||{})},async handleSave(){const t=await l({type:this.type,config:JSON.stringify({...this.data,[this.type]:this.form})});1==t.state&&(this.$message.success("保存成功"),this.closeDrawer(),this.getFiveDimConfig())},openDrawer(t){var a;this.type=t,this.drawerTitle=this.parts[t]+"五维图配置",this.form={...this.data[t]},null===(a=this.$refs.form)||void 0===a||a.clearValidate(),this.drawerVisible=!0},closeDrawer(){this.drawerVisible=!1}}},d=c,p=(i("b34f"),i("2877")),f=Object(p["a"])(d,e,s,!1,null,"48c9a58e",null);a["default"]=f.exports},b34f:function(t,a,i){"use strict";i("f525")},ebc6:function(t,a,i){t.exports=i.p+"img/icon-2.cd4c26b5.svg"},f525:function(t,a,i){},faa4:function(t,a,i){t.exports=i.p+"img/icon-1.b4acaacf.svg"}}]);