(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b57c05a"],{3089:function(e,a,t){"use strict";t("a4ec")},"53e0":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-page"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small",model:e.searchForm}},[a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.BEGIN_TIME,callback:function(a){e.$set(e.searchForm,"BEGIN_TIME",a)},expression:"searchForm.BEGIN_TIME"}})],1),a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.END_TIME,callback:function(a){e.$set(e.searchForm,"END_TIME",a)},expression:"searchForm.END_TIME"}})],1),a("el-form-item",{attrs:{label:"查询类型"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.SELECT_TYPE,callback:function(a){e.$set(e.searchForm,"SELECT_TYPE",a)},expression:"searchForm.SELECT_TYPE"}},[a("el-option",{attrs:{value:"",label:"全部来电"}}),a("el-option",{attrs:{value:"0",label:"接通来电"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")])],1),a("el-divider",{attrs:{direction:"vertical"}}),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),a("el-table-column",{attrs:{prop:"CALLERNO",label:"来电号码",align:"center"}}),a("el-table-column",{attrs:{prop:"CALL_COUNT",label:"来电次数",align:"center"}})],1),a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},l=[function(){var e=this,a=e._self._c;return a("div",{staticClass:"stat-desc"},[a("h4",[e._v("统计口径")]),a("p",[e._v("1、来电次数：呼入12345通话号码的重复次数")])])}],i=(t("0643"),t("4e3e"),t("88a7"),t("271a"),t("5494"),t("c2d0")),n=t("0c9f"),s={name:"UserCallStat",data(){return{searchForm:{BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),SELECT_TYPE:"",pageIndex:1,pageSize:15},searchedForm:{},loading:!1,tableData:[],pagination:{currentPage:1,pageSize:15,total:0}}},created(){this.loadTableData()},methods:{loadTableData(){this.loading=!0;const e={...this.searchForm,pageType:3,pageIndex:this.pagination.currentPage,pageSize:this.pagination.pageSize};this.searchedForm=e,Object(n["I"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.pagination.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败")}).finally(()=>{this.loading=!1})},exportDetail(){this.$confirm("是否导出来电用户及接通次数统计报表?","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=new URLSearchParams;Object.keys(this.searchForm).forEach(a=>{e.append(a,this.searchForm[a])});const a=Object(n["D"])(e.toString()),t=document.createElement("a");t.href=a,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t),this.$message({type:"success",message:"导出中，请稍候"})}).catch(()=>{})},handleSizeChange(e){this.pagination.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pagination.currentPage=e,this.loadTableData()},resetForm(){this.searchForm={BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),SELECT_TYPE:"",pageIndex:1,pageSize:15},this.pagination.currentPage=1,this.loadTableData()}}},o=s,c=(t("3089"),t("2877")),h=Object(c["a"])(o,r,l,!1,null,null,null);a["default"]=h.exports},a4ec:function(e,a,t){}}]);