(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9b2d62c2"],{2590:function(t,e,a){"use strict";a("9784")},"271a":function(t,e,a){"use strict";var l=a("cb2d"),n=a("e330"),r=a("577e"),o=a("d6d6"),i=URLSearchParams,s=i.prototype,u=n(s.getAll),c=n(s.has),p=new i("a=1");!p.has("a",2)&&p.has("a",void 0)||l(s,"has",(function(t){var e=arguments.length,a=e<2?void 0:arguments[1];if(e&&void 0===a)return c(this,t);var l=u(this,t);o(e,1);var n=r(a),i=0;while(i<l.length)if(l[i++]===n)return!0;return!1}),{enumerable:!0,unsafe:!0})},5494:function(t,e,a){"use strict";var l=a("83ab"),n=a("e330"),r=a("edd0"),o=URLSearchParams.prototype,i=n(o.forEach);l&&!("size"in o)&&r(o,"size",{get:function(){var t=0;return i(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"577e":function(t,e,a){"use strict";var l=a("f5df"),n=String;t.exports=function(t){if("Symbol"===l(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},"88a7":function(t,e,a){"use strict";var l=a("cb2d"),n=a("e330"),r=a("577e"),o=a("d6d6"),i=URLSearchParams,s=i.prototype,u=n(s.append),c=n(s["delete"]),p=n(s.forEach),d=n([].push),h=new i("a=1&a=2&b=3");h["delete"]("a",1),h["delete"]("b",void 0),h+""!=="a=2"&&l(s,"delete",(function(t){var e=arguments.length,a=e<2?void 0:arguments[1];if(e&&void 0===a)return c(this,t);var l=[];p(this,(function(t,e){d(l,{key:e,value:t})})),o(e,1);var n,i=r(t),s=r(a),h=0,b=0,g=!1,v=l.length;while(h<v)n=l[h++],g||n.key===i?(g=!0,c(this,n.key)):b++;while(b<v)n=l[b++],n.key===i&&n.value===s||u(this,n.key,n.value)}),{enumerable:!0,unsafe:!0})},9784:function(t,e,a){},b929:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-page"},[e("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[e("el-form-item",{attrs:{label:"开始时间"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.searchForm.BEGIN_TIME,callback:function(e){t.$set(t.searchForm,"BEGIN_TIME",e)},expression:"searchForm.BEGIN_TIME"}})],1),e("el-form-item",{attrs:{label:"-"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.searchForm.END_TIME,callback:function(e){t.$set(t.searchForm,"END_TIME",e)},expression:"searchForm.END_TIME"}})],1),e("el-form-item",{attrs:{label:"统计步长"}},[e("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.searchForm.statStepSize,callback:function(e){t.$set(t.searchForm,"statStepSize",e)},expression:"searchForm.statStepSize"}},[e("el-option",{attrs:{value:"0",label:"时段"}}),e("el-option",{attrs:{value:"1",label:"半小时"}}),e("el-option",{attrs:{value:"2",label:"小时"}}),e("el-option",{attrs:{value:"3",label:"天"}}),e("el-option",{attrs:{value:"4",label:"周"}}),e("el-option",{attrs:{value:"5",label:"旬"}}),e("el-option",{attrs:{value:"6",label:"月"}}),e("el-option",{attrs:{value:"7",label:"季度"}}),e("el-option",{attrs:{value:"8",label:"半年"}}),e("el-option",{attrs:{value:"9",label:"年"}})],1)],1),e("el-form-item",{attrs:{label:"被叫号码"}},[e("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.searchForm.CALLEENO,callback:function(e){t.$set(t.searchForm,"CALLEENO",e)},expression:"searchForm.CALLEENO"}},[e("el-option",{attrs:{value:"",label:"全部"}}),e("el-option",{attrs:{value:"12345",label:"12345政府热线"}}),e("el-option",{attrs:{value:"12358",label:"12358(物价监督热线)"}}),e("el-option",{attrs:{value:"12356",label:"12356(计生委热线)"}}),e("el-option",{attrs:{value:"65192666",label:"65192666(监督投诉热线)"}}),e("el-option",{attrs:{value:"68812345",label:"68812345(石景山分中心)"}}),e("el-option",{attrs:{value:"12365",label:"12365(质监局热线)"}}),e("el-option",{attrs:{value:"12312",label:"12312(商委服务热线)"}}),e("el-option",{attrs:{value:"12385",label:"12385(残联服务热线)"}}),e("el-option",{attrs:{value:"12318",label:"12318(文化投诉举报热线)"}}),e("el-option",{attrs:{value:"12301",label:"12301(旅游服务热线)"}}),e("el-option",{attrs:{value:"68709990",label:"68709990(中关村管委会热线)"}}),e("el-option",{attrs:{value:"12331",label:"12331(市食药监局热线)"}}),e("el-option",{attrs:{value:"12319",label:"12319(市政管委热线)"}}),e("el-option",{attrs:{value:"68556666",label:"68556666(北京市水务局热线)"}}),e("el-option",{attrs:{value:"65603451",label:"65603451(北京市电台新闻热线)"}}),e("el-option",{attrs:{value:"96391",label:"96391(首都教育服务热线)"}}),e("el-option",{attrs:{value:"96156",label:"96156（民政局）"}}),e("el-option",{attrs:{value:"65603465",label:"65603465（歌华在线）"}}),e("el-option",{attrs:{value:"12350",label:"12350（安监局）"}}),e("el-option",{attrs:{value:"96119",label:"96119（消防局）"}}),e("el-option",{attrs:{value:"12329",label:"12329（公积金）"}}),e("el-option",{attrs:{value:"12369",label:"12369（环保局）"}}),e("el-option",{attrs:{value:"11185",label:"11185（邮政）"}}),e("el-option",{attrs:{value:"96777",label:"96777（燃气）"}}),e("el-option",{attrs:{value:"96116",label:"96116（自来水）"}}),e("el-option",{attrs:{value:"96165",label:"96165（地铁）"}}),e("el-option",{attrs:{value:"96166",label:"96166（公交）"}}),e("el-option",{attrs:{value:"96159",label:"96159（排水）"}}),e("el-option",{attrs:{value:"96069",label:"96069（热力）"}}),e("el-option",{attrs:{value:"96011",label:"96011（首发）"}}),e("el-option",{attrs:{value:"67601234",label:"67601234（市城市照明中心）"}}),e("el-option",{attrs:{value:"68317307",label:"68317307（交管局）"}}),e("el-option",{attrs:{value:"12320",label:"12320（卫计委）"}}),e("el-option",{attrs:{value:"65603473",label:"65603473 (12336自然资源委)"}}),e("el-option",{attrs:{value:"65603477",label:"65603477（延庆区政府热线）"}}),e("el-option",{attrs:{value:"65603476",label:"65603476（12316三农热线）"}}),e("el-option",{attrs:{value:"65603478",label:"65603478（12315热线）"}}),e("el-option",{attrs:{value:"12330",label:"12330（知识产权局）"}}),e("el-option",{attrs:{value:"12315",label:"12315热线"}}),e("el-option",{attrs:{value:"12338",label:"12338(妇联热线)"}}),e("el-option",{attrs:{value:"12305",label:"12305（12305邮政管理局）"}})],1)],1),e("el-form-item",{attrs:{label:"客户级别"}},[e("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.searchForm.USER_LEVEL,callback:function(e){t.$set(t.searchForm,"USER_LEVEL",e)},expression:"searchForm.USER_LEVEL"}},[e("el-option",{attrs:{value:"",label:"全部"}}),e("el-option",{attrs:{value:"0",label:"0|普通用户0"}}),e("el-option",{attrs:{value:"1",label:"1|普通用户0"}}),e("el-option",{attrs:{value:"10",label:"10|普通用户"}}),e("el-option",{attrs:{value:"15",label:"15|红名单"}}),e("el-option",{attrs:{value:"18",label:"18|普通客户18"}}),e("el-option",{attrs:{value:"19",label:"19|普通客户19"}}),e("el-option",{attrs:{value:"20",label:"20|重要用户"}}),e("el-option",{attrs:{value:"21",label:"21|特殊"}}),e("el-option",{attrs:{value:"22",label:"22|74重保"}}),e("el-option",{attrs:{value:"23",label:"23|12345视察"}}),e("el-option",{attrs:{value:"25",label:"25|1024(1)"}}),e("el-option",{attrs:{value:"26",label:"25|1024(2)"}}),e("el-option",{attrs:{value:"27",label:"25|1024(3)"}}),e("el-option",{attrs:{value:"28",label:"25|1024(4)"}}),e("el-option",{attrs:{value:"29",label:"25|1024(5)"}}),e("el-option",{attrs:{value:"31",label:"31|两委两组"}}),e("el-option",{attrs:{value:"32",label:"32|两委两组测试"}}),e("el-option",{attrs:{value:"33",label:"33|测试语音"}}),e("el-option",{attrs:{value:"34",label:"34|企业热线"}}),e("el-option",{attrs:{value:"35",label:"35|京津冀"}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索 ")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.exportDetail}},[e("i",{staticClass:"el-icon-download"}),t._v(" 导出 ")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"80",align:"center"}}),e("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getDateTime(e.row))+" ")]}}])}),e("el-table-column",{attrs:{prop:"CALLIN_COUNT",label:"呼入次数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"CALLIN_ANSWER_COUNT",label:"接通次数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"CALLIN_RATE",label:"接通率","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.CALLIN_RATE))+" ")]}}])}),e("el-table-column",{attrs:{prop:"IVR_USE_COUNT",label:"IVR占用数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"IVR_ANSWER_COUNT",label:"IVR连接数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"IVR_RATE",label:"IVR连接率","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.IVR_RATE))+" ")]}}])}),e("el-table-column",{attrs:{prop:"AGENT_USE_COUNT",label:"人工占用数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENT_ANSWER_COUNT",label:"人工接通数","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENT_RATE",label:"人工接通率","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.AGENT_RATE))+" ")]}}])}),e("el-table-column",{attrs:{prop:"LOST_COUNT",label:"大网呼损量","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"LOST_RATE",label:"大网呼损率","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.LOST_RATE))+" ")]}}])})],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":t.currentPage,"page-sizes":[15,25,50,100,200],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total,background:""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}}),t._m(0)],1)},n=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-desc"},[e("h4",[t._v("统计口径")]),e("p",[t._v("1、呼入次数：呼入的电话数量（包含成功失败，不包含高频呼叫）")]),e("p",[t._v("2、接通次数：呼入的电话 坐席应答时长+坐席通话时长+ivr时长>0 的数量")]),e("p",[t._v("3、接通率：接通次数/呼入次数")]),e("p",[t._v("4、IVR占用数：呼入电话进入ivr次数>0的数量")]),e("p",[t._v("5、IVR连接数：呼入电话进入ivr时长>0的数量")]),e("p",[t._v("6、IVR连接率：IVR连接数/IVR占用数")]),e("p",[t._v("7、人工占用数：呼入电话进入排队次数+坐席次数>0的数量")]),e("p",[t._v("8、人工接通数：呼入电话的坐席应答时长+坐席通话时长>0的数量")]),e("p",[t._v("9、人工接通率：人工接通数/人工占用数")]),e("p",[t._v("10、大网呼损量：大网呼损数量")]),e("p",[t._v("11、大网呼损率：大网呼损量/人工接通数")])])}],r=(a("0643"),a("4e3e"),a("88a7"),a("271a"),a("5494"),a("b775")),o=a("c2d0"),i={name:"AgentCallInStat",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(o["e"])(),END_TIME:Object(o["d"])(),statStepSize:"3",CALLEENO:"12345",USER_LEVEL:""}}},created(){this.loadData()},methods:{loadData(){this.loading=!0;const t={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize,CALLEENO:this.searchForm.CALLEENO,USER_LEVEL:this.searchForm.USER_LEVEL};this.searchedForm=t,Object(r["a"])({url:"/cx-report-12345/webcall?action=statDao.agentCallInStat",method:"post",data:{data:JSON.stringify(t)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(t=>{this.loading=!1,1===t.state?(this.tableData=t.data||[],this.total=t.totalRow||0):this.$message.error(t.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(t){this.pageSize=t,this.loadData()},handleCurrentChange(t){this.currentPage=t,this.loadData()},exportDetail(){this.$confirm("是否导出接入话务量报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=new URLSearchParams;Object.keys(this.searchForm).forEach(e=>{t.append(e,this.searchForm[e]||"")}),window.location.href="/cx-report-12345/servlet/export?action=ExportAgentCallInStat&"+t.toString()}).catch(()=>{})},getDateTime:o["c"],formatRate:o["b"]}},s=i,u=(a("2590"),a("2877")),c=Object(u["a"])(s,l,n,!1,null,null,null);e["default"]=c.exports},c2d0:function(t,e,a){"use strict";function l(){return r()}function n(){return i()}function r(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),l=String(t.getDate()).padStart(2,"0");return`${e}-${a}-${l} 00:00:00`}function o(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),l=String(t.getDate()).padStart(2,"0");return`${e}-${a}-${l} 23:59:59`}function i(){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1);let a=e.getMonth()+1;a<10&&(a="0"+a);let l=e.getDate();return l<10&&(l="0"+l),e.getFullYear()+"-"+a+"-"+l+" 00:00:00"}function s(){const t=new Date;return t.setHours(t.getHours()-1),u(t,"yyyy-MM-dd HH:mm:ss")}function u(t,e){const a={"M+":t.getMonth()+1,"d+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(const l in a)new RegExp("("+l+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?a[l]:("00"+a[l]).substr((""+a[l]).length)));return e}function c(t){var e;const a=(null===this||void 0===this||null===(e=this.searchedForm)||void 0===e?void 0:e.statStepSize)||"3";if(t.SELECT_TIME)return t.SELECT_TIME;if("1"===a){const e=d("HH:mm:ss",t.HALF_HOUR,"MINUTE",30);return t.DAY+" "+t.HALF_HOUR+"-"+e}if("2"===a){const e=d("HH",t.HOUR,"HOUR",1);return t.DAY+" "+t.HOUR+"-"+e}return"3"===a?t.DAY:"4"===a?"星期"+p(t.WEEK):"5"===a?"1"===t.XUN?t.MONTH+"上旬":"2"===t.XUN?t.MONTH+"中旬":"3"===t.XUN?t.MONTH+"下旬":t.MONTH+t.XUN:"6"===a?t.MONTH:"7"===a?t.YEAR+"第"+p(t.QUARTER)+"季度":"8"===a?"1"===t.HALFYEAR?t.YEAR+"上半年":"2"===t.HALFYEAR?t.YEAR+"下半年":t.YEAR+t.HALFYEAR:"9"===a?t.YEAR:""}function p(t){const e=["零","一","二","三","四","五","六","七","八","九"];if(t=parseInt(t,10),t<10)return e[t];if(t>=10&&t<20)return"十"+e[t%10];const a=Math.floor(t/10),l=t%10;return 0===l?e[a]+"十":e[a]+"十"+e[l]}function d(t,e,a,l){let n;n="HH"===t?"1970-01-01 "+e+":00:00":"HH:mm:ss"===t?"1970-01-01 "+e:e;const r=new Date(n);switch(a){case"MINUTE":r.setMinutes(r.getMinutes()+l);let t=r.getHours().toString();1===t.length&&(t="0"+t);let a=r.getMinutes().toString();1===a.length&&(a="0"+a);let n=r.getSeconds().toString();return 1===n.length&&(n="0"+n),t+":"+a+":"+n;case"HOUR":r.setHours(r.getHours()+l);let o=r.getHours().toString();return 1===o.length&&(o="0"+o),o;default:return e}}function h(t){if(void 0===t||null===t||""===t)return"00:00:00";t=parseInt(t);const e=Math.floor(t/3600);let a=Math.floor(t%3600/60),l=t%60;return a=a<10?"0"+a:a,l=l<10?"0"+l:l,e+":"+a+":"+l}function b(t){return void 0===t||null===t||""===t?"0.00%":"string"===typeof t&&t.indexOf("%")>=0?t:(t*=100,t=parseFloat(parseFloat(t).toPrecision(12)),t+"%")}function g(){const t=new Date;return u(t,"yyyy-MM-dd HH:mm:ss")}function v(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+1);const a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${a}-${l}-${n} 00:00:00`}function f(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+7);const a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${a}-${l}-${n} 23:59:59`}function m(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0");return`${e}-${a}-01 00:00:00`}function E(){const t=new Date,e=t.getFullYear(),a=t.getMonth()+1,l=new Date(e,a,0),n=String(l.getDate()).padStart(2,"0");return`${e}-${String(a).padStart(2,"0")}-${n} 23:59:59`}function S(){const t=(new Date).getFullYear();return t+"-01-01 00:00:00"}function _(){const t=(new Date).getFullYear();return t+"-12-31 23:59:59"}a.d(e,"e",(function(){return l})),a.d(e,"d",(function(){return n})),a.d(e,"o",(function(){return r})),a.d(e,"n",(function(){return o})),a.d(e,"p",(function(){return i})),a.d(e,"g",(function(){return s})),a.d(e,"c",(function(){return c})),a.d(e,"a",(function(){return h})),a.d(e,"b",(function(){return b})),a.d(e,"f",(function(){return g})),a.d(e,"k",(function(){return v})),a.d(e,"j",(function(){return f})),a.d(e,"i",(function(){return m})),a.d(e,"h",(function(){return E})),a.d(e,"m",(function(){return S})),a.d(e,"l",(function(){return _}))},d6d6:function(t,e,a){"use strict";var l=TypeError;t.exports=function(t,e){if(t<e)throw new l("Not enough arguments");return t}}}]);