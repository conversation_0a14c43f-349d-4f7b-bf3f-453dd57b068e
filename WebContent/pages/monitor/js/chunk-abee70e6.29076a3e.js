(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-abee70e6"],{"1a53":function(e,t,a){"use strict";a("c76b")},"5f30":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",{attrs:{label:"统计步长"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{value:"0",label:"时段"}}),t("el-option",{attrs:{value:"1",label:"半小时"}}),t("el-option",{attrs:{value:"2",label:"小时"}}),t("el-option",{attrs:{value:"3",label:"天"}}),t("el-option",{attrs:{value:"4",label:"周"}}),t("el-option",{attrs:{value:"5",label:"旬"}}),t("el-option",{attrs:{value:"6",label:"月"}}),t("el-option",{attrs:{value:"7",label:"季度"}}),t("el-option",{attrs:{value:"8",label:"半年"}}),t("el-option",{attrs:{value:"9",label:"年"}})],1)],1),t("el-form-item",{attrs:{label:"被叫号码"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.CALLEENO,callback:function(t){e.$set(e.searchForm,"CALLEENO",t)},expression:"searchForm.CALLEENO"}},[t("el-option",{attrs:{value:"12345",label:"12345政府热线"}}),t("el-option",{attrs:{value:"12358",label:"12358(物价监督热线)"}}),t("el-option",{attrs:{value:"12356",label:"12356(计生委热线)"}}),t("el-option",{attrs:{value:"65192666",label:"65192666(监督投诉热线)"}}),t("el-option",{attrs:{value:"68812345",label:"68812345(石景山分中心)"}}),t("el-option",{attrs:{value:"12365",label:"12365(质监局热线)"}}),t("el-option",{attrs:{value:"12312",label:"12312(商委服务热线)"}}),t("el-option",{attrs:{value:"12385",label:"12385(残联服务热线)"}}),t("el-option",{attrs:{value:"12318",label:"12318(文化投诉举报热线)"}}),t("el-option",{attrs:{value:"12301",label:"12301(旅游服务热线)"}}),t("el-option",{attrs:{value:"68709990",label:"68709990(中关村管委会热线)"}}),t("el-option",{attrs:{value:"12331",label:"12331(市食药监局热线)"}}),t("el-option",{attrs:{value:"12319",label:"12319(市政管委热线)"}}),t("el-option",{attrs:{value:"68556666",label:"68556666(北京市水务局热线)"}}),t("el-option",{attrs:{value:"65603451",label:"65603451(北京市电台新闻热线)"}}),t("el-option",{attrs:{value:"96391",label:"96391(首都教育服务热线)"}}),t("el-option",{attrs:{value:"96156",label:"96156（民政局）"}}),t("el-option",{attrs:{value:"65603465",label:"65603465（歌华在线）"}}),t("el-option",{attrs:{value:"65603477",label:"65603477（延庆区政府热线）"}}),t("el-option",{attrs:{value:"65603476",label:"65603476（12316三农热线）"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getDateTime(t.row))+" ")]}}])}),t("el-table-column",{attrs:{prop:"CALLIN_COUNT",label:"呼入次数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLIN_ANSWER_COUNT",label:"接通次数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLIN_PERCENT",label:"接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.CALLIN_PERCENT))+" ")]}}])}),t("el-table-column",{attrs:{prop:"IVR_USE_COUNT",label:"IVR占用数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"IVR_ANSWER_COUNT",label:"IVR接通数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"IVR_PERCENT",label:"IVR连接率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.IVR_PERCENT))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AGENT_USE_COUNT",label:"人工占用数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_ANSWER_COUNT",label:"人工接通数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_PERCENT",label:"人工接通率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.AGENT_PERCENT))+" ")]}}])}),t("el-table-column",{attrs:{prop:"LOST_COUNT",label:"大网呼损量","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"LOST_PERCENT",label:"大网呼损率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.LOST_PERCENT))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、呼入次数：高频次呼叫客户呼入的电话数量（包含成功失败）")]),t("p",[e._v("2、接通次数：高频次呼叫客户呼入的电话 坐席应答时长+坐席通话时长+ivr时长>0 的数量")]),t("p",[e._v("3、接通率：接通次数/呼入次数")]),t("p",[e._v("4、IVR占用数：高频次呼叫客户呼入电话进入ivr次数>0的数量")]),t("p",[e._v("5、IVR接通数：高频次呼叫客户呼入电话进入ivr时长>0的数量")]),t("p",[e._v("6、IVR连接率：IVR接通数/IVR占用数")]),t("p",[e._v("7、人工占用数：高频次呼叫客户呼入电话进入排队次数+坐席次数>0的数量")]),t("p",[e._v("8、人工接通数：高频次呼叫客户呼入电话的坐席应答时长+坐席通话时长>0的数量")]),t("p",[e._v("9、人工接通率：人工接通数/人工占用数")]),t("p",[e._v("10、大网呼损量：高频次呼叫客户大网呼损数量")]),t("p",[e._v("11、大网呼损率：高频次呼叫客户大网呼损量/人工接通数")])])}],o=a("c2d0"),n=a("0c9f"),i={name:"CallHighcallinCount",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(o["e"])(),END_TIME:Object(o["d"])(),statStepSize:"3",CALLEENO:"12345"},searchedForm:{}}},created(){this.loadData()},methods:{loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize||"3",CALLEENO:this.searchForm.CALLEENO||"12345"};this.searchedForm=e,Object(n["hb"])(e).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出高频次来电报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.location.href=Object(n["y"])(this.searchForm)}).catch(()=>{})},getDateTime:o["c"],formatRate:o["b"]}},s=i,c=(a("1a53"),a("2877")),p=Object(c["a"])(s,l,r,!1,null,null,null);t["default"]=p.exports},c76b:function(e,t,a){}}]);