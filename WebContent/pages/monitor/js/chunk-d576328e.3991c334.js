(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d576328e"],{"0133":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-page"},[e("el-form",{attrs:{inline:!0,size:"small",model:t.queryParams},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"月份"}},[e("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM",clearable:""},model:{value:t.queryParams.monthId,callback:function(e){t.$set(t.queryParams,"monthId",e)},expression:"queryParams.monthId"}})],1),e("el-form-item",{attrs:{label:"班组"}},[e("el-input",{attrs:{placeholder:"请输入班组",clearable:""},model:{value:t.queryParams.workGroup,callback:function(e){t.$set(t.queryParams,"workGroup",e)},expression:"queryParams.workGroup"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.handleQuery}},[t._v("查询")]),e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.resetQuery}},[t._v("重置")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-button",{attrs:{type:"primary"},on:{click:t.handleImport}},[t._v("导入")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"}},[e("el-table-column",{attrs:{prop:"MONTH_ID",label:"月份","min-width":"100"}}),e("el-table-column",{attrs:{prop:"WORK_GROUP",label:"班组","min-width":"120"}}),e("el-table-column",{attrs:{prop:"BUSI_NUMBER_SCORE",label:"业务量得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"QUALITY_SCORE",label:"质检得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"ATTENDANCE_SCORE",label:"出勤得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"MONTHLY_EXAM_SCORE",label:"月考得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"AFTER_LONG_SCORE",label:"话后处理得分","min-width":"120"}}),e("el-table-column",{attrs:{prop:"DEDUCTION",label:"扣减","min-width":"80"}}),e("el-table-column",{attrs:{prop:"REWARD",label:"奖励","min-width":"80"}}),e("el-table-column",{attrs:{prop:"MONTHLY_RANKING",label:"月度排名","min-width":"100"}}),e("el-table-column",{attrs:{prop:"ON_HOOK_SATISFACTION",label:"挂机满意度","min-width":"120"}}),e("el-table-column",{attrs:{prop:"ALL_SCORE",label:"总分","min-width":"80"}}),e("el-table-column",{attrs:{prop:"HONOR1",label:"班组荣誉1","min-width":"120"}}),e("el-table-column",{attrs:{prop:"HONOR2",label:"班组荣誉2","min-width":"120"}}),e("el-table-column",{attrs:{prop:"HONOR3",label:"班组荣誉3","min-width":"120"}})],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{layout:"total, sizes, prev, pager, next, jumper",background:"","page-sizes":[10,20,50,100],"page-size":t.pageSize,"current-page":t.pageNum,total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}}),e("el-dialog",{attrs:{title:"导入班组月度评分",visible:t.importDialogVisible,width:"500px","custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(e){t.importDialogVisible=e}}},[e("el-form",{ref:"importForm",attrs:{model:t.importForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"选择月份",prop:"monthId"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"请选择导入月份","value-format":"yyyy-MM",clearable:""},model:{value:t.importForm.monthId,callback:function(e){t.$set(t.importForm,"monthId",e)},expression:"importForm.monthId"}})],1),e("el-form-item",{attrs:{label:"班组评分"}},[e("el-link",{staticStyle:{float:"right","margin-top":"8px"},attrs:{type:"primary"},on:{click:t.downloadTemplate}},[t._v("下载模板")]),e("el-upload",{ref:"upload",attrs:{action:"","file-list":t.fileList,"auto-upload":!1,accept:".xlsx,.xls",limit:1,"on-change":t.handleFileChange,"on-exceed":t.onExceed}},[e("el-button",{attrs:{size:"small",type:"primary"}},[t._v("点击上传")]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 只能上传xlsx/xls文件，且不超过10MB ")])],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.importDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitImport}},[t._v("确 定")])],1)],1)],1)},i=[],r=a("b775");function l(t){return Object(r["a"])({url:"/cx-monitordata-12345/servlet/workScore?action=Select",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(t){return Object(r["a"])({url:"/cx-monitordata-12345/servlet/workScore?action=Import",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}a("c24f");var s={name:"TeamMonthlyScore",data(){return{loading:!1,pageNum:1,pageSize:10,total:0,queryParams:{monthId:"",workGroup:""},tableData:[],importDialogVisible:!1,importForm:{monthId:""},fileList:[]}},created(){this.getList()},methods:{getList(){this.loading=!0,l({pageNum:this.pageNum,pageSize:this.pageSize,...this.queryParams}).then(t=>{this.tableData=t.data||[],this.total=t.totalRow||0,this.loading=!1}).catch(()=>{this.loading=!1})},handleQuery(){this.pageNum=1,this.getList()},resetQuery(){this.queryParams={month:"",teams:[]},this.handleQuery()},handleSizeChange(t){this.pageSize=t,this.getList()},handleCurrentChange(t){this.pageNum=t,this.getList()},handleImport(){this.importDialogVisible=!0,this.importForm.month="",this.fileList=[]},handleFileChange(t,e){this.fileList=e,t&&(this.importForm.fileName=t.name)},onExceed(t,e){this.$message.warning("当前限制选择 1 个文件")},submitImport(){if(!this.importForm.monthId)return void this.$message.warning("请选择导入月份");if(0===this.fileList.length)return void this.$message.warning("请选择要上传的文件");const t=new FormData;t.append("file",this.fileList[0].raw),t.append("monthId",this.importForm.monthId),this.loading=!0,n(t).then(t=>{1===t.state?(this.$message.success(t.data||"导入成功"),this.importDialogVisible=!1,this.getList()):this.$message.error(t.msg||"导入失败"),this.loading=!1}).catch(()=>{this.$message.error("导入失败"),this.loading=!1})},downloadTemplate(){window.open("/cx-monitordata-12345/servlet/workScore?action=ExportTemplate","_blank")}}},m=s,p=(a("9feb"),a("2877")),d=Object(p["a"])(m,o,i,!1,null,"7923dd26",null);e["default"]=d.exports},"9feb":function(t,e,a){"use strict";a("e497")},c24f:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return l})),a.d(e,"f",(function(){return n})),a.d(e,"d",(function(){return s})),a.d(e,"e",(function(){return m}));a("a573");var o=a("b775");function i(t){return Object(o["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserList",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(o["a"])({url:"/cx-monitordata-12345/servlet/user?action=EditAgent",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(o["a"])({url:"/cx-monitordata-12345/servlet/user?action=AddAgent",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(t){return Object(o["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserUpload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}function s(){return Object(o["a"])({url:"/cx-monitordata-12345/webcall?action=common.workGroupPortraitList",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function m(t,e){const a=Object.entries(e).map(([t,e])=>`${t}=${e}`).join("&");return Object(o["a"])({url:"/cc-base/servlet/attachment?action=upload2&"+a,method:"post",data:{file:t},headers:{"Content-Type":"multipart/form-data"}})}},e497:function(t,e,a){}}]);