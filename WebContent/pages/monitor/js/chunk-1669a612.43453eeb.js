(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1669a612"],{"1a3c":function(e,t,a){},"567a":function(e,t,a){"use strict";a("1a3c")},c6d8:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"queryForm",attrs:{inline:!0,size:"small",model:e.queryParams}},[t("el-form-item",{attrs:{label:"话机号码"}},[t("el-input",{attrs:{placeholder:"请输入话机号码",clearable:""},model:{value:e.queryParams.phoneNumber,callback:function(t){e.$set(e.queryParams,"phoneNumber",t)},expression:"queryParams.phoneNumber"}})],1),t("el-form-item",{attrs:{label:"更新时间"}},[t("el-date-picker",{attrs:{type:"date",placeholder:"开始日期","value-format":"yyyy-MM-dd",clearable:""},model:{value:e.queryParams.startDate,callback:function(t){e.$set(e.queryParams,"startDate",t)},expression:"queryParams.startDate"}}),t("span",{staticStyle:{margin:"0 5px"}},[e._v("-")]),t("el-date-picker",{attrs:{type:"date",placeholder:"结束日期","value-format":"yyyy-MM-dd",clearable:""},model:{value:e.queryParams.endDate,callback:function(t){e.$set(e.queryParams,"endDate",t)},expression:"queryParams.endDate"}})],1),t("el-form-item",{attrs:{label:"横坐标"}},[t("el-input",{attrs:{placeholder:"请输入横坐标",clearable:""},model:{value:e.queryParams.hNumber,callback:function(t){e.$set(e.queryParams,"hNumber",t)},expression:"queryParams.hNumber"}})],1),t("el-form-item",{attrs:{label:"纵坐标"}},[t("el-input",{attrs:{placeholder:"请输入纵坐标",clearable:""},model:{value:e.queryParams.zNumber,callback:function(t){e.$set(e.queryParams,"zNumber",t)},expression:"queryParams.zNumber"}})],1),t("el-form-item",{attrs:{label:"房间号"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.ROOM_LOCATION,callback:function(t){e.$set(e.queryParams,"ROOM_LOCATION",t)},expression:"queryParams.ROOM_LOCATION"}},e._l(e.roomOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"是否为空"}},[t("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.IS_NULL,callback:function(t){e.$set(e.queryParams,"IS_NULL",t)},expression:"queryParams.IS_NULL"}},e._l(e.nullOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("查询")]),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-upload2"},on:{click:e.handleImport}},[e._v("导入")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"PHONE_NUMBER",label:"话机号",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"ROOM_LOCATION",label:"房间号",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRoomLocation(t.row.ROOM_LOCATION))+" ")]}}])}),t("el-table-column",{attrs:{prop:"SEAT_NO",label:"座位号",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"SEAT_IP",label:"座位ip",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"CREATE_TIME",label:"创建时间",align:"center","min-width":"180"}}),t("el-table-column",{attrs:{prop:"CREATE_ACC",label:"创建人",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"UPDATE_TIME",label:"更新时间",align:"center","min-width":"180"}}),t("el-table-column",{attrs:{prop:"UPDATE_ACC",label:"更新人",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"HORIZONTAL",label:"横坐标",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"LONGITUDINAL",label:"纵坐标",align:"center","min-width":"150"}}),t("el-table-column",{attrs:{prop:"IS_NULL",label:"是否为空",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatIsNull(t.row.IS_NULL))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",align:"center",width:"160",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleEdit(a.row)}}},[e._v("修改")]),t("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger"},on:{click:function(t){return e.handleDelete(a.row)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"update:currentPage":function(t){return e.$set(e.pagination,"pageIndex",t)},"update:current-page":function(t){return e.$set(e.pagination,"pageIndex",t)},"update:pageSize":function(t){return e.$set(e.pagination,"pageSize",t)},"update:page-size":function(t){return e.$set(e.pagination,"pageSize",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),t("el-dialog",{attrs:{title:"导入",visible:e.importDialogVisible,width:"420px","custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importForm",attrs:{model:e.importForm}},[t("el-form-item",{attrs:{label:"话机信息"}},[t("el-link",{staticStyle:{float:"right","margin-top":"8px"},attrs:{type:"primary",href:"/cx-mix-12345/template/phoneTemp.xlsx"}},[e._v("下载模板")]),t("el-upload",{ref:"upload",attrs:{action:"","file-list":e.fileList,"auto-upload":!1,accept:".xlsx,.xls",limit:1,"on-change":e.handleFileChange,"on-exceed":e.onExceed}},[t("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 只能上传xlsx/xls文件，且不超过10MB ")])],1)],1),t("el-form-item",{attrs:{label:"文件名"}},[t("el-input",{attrs:{readonly:""},model:{value:e.importForm.fileName,callback:function(t){e.$set(e.importForm,"fileName",t)},expression:"importForm.fileName"}})],1)],1),t("template",{slot:"footer"},[t("el-button",{on:{click:function(t){e.importDialogVisible=!1}}},[e._v("关闭")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleFileUpload}},[e._v("上传")])],1)],2),t("el-dialog",{attrs:{title:e.drawerTitle,visible:e.drawerVisible,"before-close":e.handleDrawerClose,"custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(t){e.drawerVisible=t}}},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px",size:"small"}},[t("el-form-item",{attrs:{label:"话机号码",prop:"PHONE_NUMBER"}},[t("el-input",{attrs:{disabled:e.editMode,placeholder:"请输入话机号码"},model:{value:e.form.PHONE_NUMBER,callback:function(t){e.$set(e.form,"PHONE_NUMBER",t)},expression:"form.PHONE_NUMBER"}})],1),t("el-form-item",{attrs:{label:"房间号",prop:"ROOM_LOCATION"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.ROOM_LOCATION,callback:function(t){e.$set(e.form,"ROOM_LOCATION",t)},expression:"form.ROOM_LOCATION"}},e._l(e.roomOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"座位IP",prop:"SEAT_IP"}},[t("el-input",{attrs:{placeholder:"请输入座位IP"},model:{value:e.form.SEAT_IP,callback:function(t){e.$set(e.form,"SEAT_IP",t)},expression:"form.SEAT_IP"}})],1),t("el-form-item",{attrs:{label:"座位号",prop:"SEAT_NO"}},[t("el-input",{attrs:{placeholder:"请输入座位号"},model:{value:e.form.SEAT_NO,callback:function(t){e.$set(e.form,"SEAT_NO",t)},expression:"form.SEAT_NO"}})],1),t("el-form-item",{attrs:{label:"横坐标",prop:"HORIZONTAL"}},[t("el-input",{attrs:{placeholder:"请输入横坐标"},model:{value:e.form.HORIZONTAL,callback:function(t){e.$set(e.form,"HORIZONTAL",t)},expression:"form.HORIZONTAL"}})],1),t("el-form-item",{attrs:{label:"纵坐标",prop:"LONGITUDINAL"}},[t("el-input",{attrs:{placeholder:"请输入纵坐标"},model:{value:e.form.LONGITUDINAL,callback:function(t){e.$set(e.form,"LONGITUDINAL",t)},expression:"form.LONGITUDINAL"}})],1),t("el-form-item",{attrs:{label:"是否为空",prop:"IS_NULL"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.IS_NULL,callback:function(t){e.$set(e.form,"IS_NULL",t)},expression:"form.IS_NULL"}},e._l(e.nullOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),t("template",{slot:"footer"},[t("el-button",{on:{click:function(t){e.drawerVisible=!1}}},[e._v("关闭")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保存")])],1)],2)],1)},r=[],i=(a("0643"),a("fffc"),a("b775"));function o(e){return Object(i["a"])({url:"/cx-mix-12345/webcall?action=statistic.phoneList",method:"post",data:{data:JSON.stringify({...e,pageType:3})},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function s(e){return Object(i["a"])({url:"/cx-mix-12345/servlet/data?action=delPhone",method:"post",data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function n(e){return Object(i["a"])({url:"/cx-mix-12345/servlet/data?action=phoneUpload",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function c(e){return Object(i["a"])({url:"/cx-mix-12345/servlet/data?action=addPhone",method:"post",data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function m(e){return Object(i["a"])({url:"/cx-mix-12345/servlet/data?action=editPhone",method:"post",data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function u(e){return Object(i["a"])({url:"/cx-mix-12345/webcall",method:"post",data:{data:JSON.stringify({params:{PHONE_NUMBER:e,mars:"statistic.phoneRecord"},controls:["statistic.phoneRecord"]})},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}var p={name:"PhoneList",data(){return{queryParams:{phoneNumber:"",startDate:"",endDate:"",hNumber:"",zNumber:"",ROOM_LOCATION:"",IS_NULL:""},loading:!1,tableData:[],pagination:{pageIndex:1,pageSize:15,total:0},roomOptions:[{value:"1",label:"8楼辅助话房"},{value:"2",label:"8楼回访话房"},{value:"3",label:"8楼企业话房"},{value:"4",label:"9楼东话房"},{value:"5",label:"9楼中话房"},{value:"6",label:"9楼西话房"},{value:"7",label:"9楼培训教室"},{value:"8",label:"六里桥4楼B区"},{value:"9",label:"六里桥4楼A区"},{value:"10",label:"六里桥4楼C区"},{value:"11",label:"六里桥5楼C区"},{value:"12",label:"六里桥5楼A区"}],nullOptions:[{value:"1",label:"不为空"},{value:"2",label:"为空"}],importDialogVisible:!1,importForm:{fileName:""},fileList:[],drawerVisible:!1,drawerTitle:"新增话机",editMode:!1,form:{PHONE_NUMBER:"",ROOM_LOCATION:"",SEAT_IP:"",SEAT_NO:"",HORIZONTAL:"",LONGITUDINAL:"",IS_NULL:"1"},rules:{PHONE_NUMBER:[{required:!0,message:"请输入话机号码",trigger:"blur"}],ROOM_LOCATION:[{required:!0,message:"请选择房间号",trigger:"change"}],SEAT_IP:[{required:!0,message:"请输入座位IP",trigger:"blur"}],SEAT_NO:[{required:!0,message:"请输入座位号",trigger:"blur"}],HORIZONTAL:[{required:!0,message:"请输入横坐标",trigger:"blur"}],LONGITUDINAL:[{required:!0,message:"请输入纵坐标",trigger:"blur"}],IS_NULL:[{required:!0,message:"请选择是否为空",trigger:"change"}]}}},created(){this.getList()},methods:{getList(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pagination.pageIndex,pageSize:this.pagination.pageSize};o(e).then(e=>{this.tableData=e.data||[],this.pagination.total=e.totalRow||0,this.loading=!1}).catch(()=>{this.$message.error("查询失败"),this.loading=!1})},handleQuery(){this.pagination.pageIndex=1,this.getList()},resetQuery(){this.$refs.queryForm.resetFields(),this.queryParams={phoneNumber:"",startDate:"",endDate:"",hNumber:"",zNumber:"",ROOM_LOCATION:"",IS_NULL:""},this.handleQuery()},handleSizeChange(e){this.pagination.pageSize=e,this.getList()},handleCurrentChange(e){this.pagination.pageIndex=e,this.getList()},formatRoomLocation(e){const t=this.roomOptions.find(t=>t.value===e);return t?t.label:e},formatIsNull(e){const t=this.nullOptions.find(t=>t.value===e);return t?t.label:e},handleAdd(){this.drawerTitle="新增话机",this.editMode=!1,this.form={PHONE_NUMBER:"",ROOM_LOCATION:"",SEAT_IP:"",SEAT_NO:"",HORIZONTAL:"",LONGITUDINAL:"",IS_NULL:"1"},this.drawerVisible=!0},handleEdit(e){this.drawerTitle="修改话机",this.editMode=!0,this.drawerVisible=!0,this.getDetail(e.PHONE_NUMBER)},getDetail(e){this.loading=!0,u(e).then(e=>{e["statistic.phoneRecord"]&&(this.form=e["statistic.phoneRecord"].data),this.loading=!1}).catch(()=>{this.$message.error("获取详情失败"),this.loading=!1})},handleSubmit(){this.$refs.form.validate(e=>{if(!e)return;this.loading=!0;const t=this.editMode,a=t?m:c,l=t?"修改成功":"新增成功";a(this.form).then(e=>{1===e.state?(this.$message.success(l),this.drawerVisible=!1,this.getList()):this.$message.error(e.msg||(t?"修改失败":"新增失败")),this.loading=!1}).catch(()=>{this.$message.error(t?"修改失败":"新增失败"),this.loading=!1})})},handleDelete(e){this.$confirm("确认删除?","信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s({id:e.PHONE_NUMBER}).then(e=>{1===e.state?(this.$message.success("删除成功"),this.getList()):this.$message.error(e.msg||"删除失败")}).catch(()=>{this.$message.error("删除失败")})})},handleImport(){this.importDialogVisible=!0,this.fileList=[],this.importForm.fileName=""},handleFileChange(e,t){this.fileList=t,e&&(this.importForm.fileName=e.name)},onExceed(e,t){this.$message.warning("当前限制选择 1 个文件")},handleFileUpload(){if(0===this.fileList.length)return void this.$message.warning("请选择要上传的文件");const e=new FormData;e.append("file",this.fileList[0].raw),n(e).then(e=>{1===e.state?(this.$message.success("导入成功"),this.importDialogVisible=!1,this.getList()):this.$message.error(e.msg||"导入失败")}).catch(()=>{this.$message.closeAll(),this.$message.error("导入失败")})},handleDrawerClose(e){this.$refs.form.resetFields(),this.$refs.form.clearValidate(),e()}}},d=p,h=(a("567a"),a("2877")),b=Object(h["a"])(d,l,r,!1,null,"7cb9cf32",null);t["default"]=b.exports}}]);