(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-71732545"],{"6ce2":function(e,a,l){"use strict";l("da89")},a96f:function(e,a,l){"use strict";l.r(a);var t=function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-page"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[a("el-form-item",{attrs:{label:"呼入类型"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.CALLTYPE,callback:function(a){e.$set(e.queryParams,"CALLTYPE",a)},expression:"queryParams.CALLTYPE"}},[a("el-option",{attrs:{label:"呼入",value:"1"}}),a("el-option",{attrs:{label:"呼出",value:"2"}})],1)],1),a("el-form-item",{attrs:{label:"主叫"}},[a("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"主叫"},model:{value:e.queryParams.CALLERNO,callback:function(a){e.$set(e.queryParams,"CALLERNO",a)},expression:"queryParams.CALLERNO"}})],1),a("el-form-item",{attrs:{label:"被叫"}},[a("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"被叫"},model:{value:e.queryParams.CALLEENO,callback:function(a){e.$set(e.queryParams,"CALLEENO",a)},expression:"queryParams.CALLEENO"}})],1),a("el-form-item",{attrs:{label:"业务类型"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.SERVICENO,callback:function(a){e.$set(e.queryParams,"SERVICENO",a)},expression:"queryParams.SERVICENO"}},e._l(e.serviceTypeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"客户级别"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.USER_LEVEL,callback:function(a){e.$set(e.queryParams,"USER_LEVEL",a)},expression:"queryParams.USER_LEVEL"}},[a("el-option",{attrs:{label:"0|普通用户0",value:"0"}}),a("el-option",{attrs:{label:"1|普通用户0",value:"1"}}),a("el-option",{attrs:{label:"9|高频次用户",value:"9"}}),a("el-option",{attrs:{label:"10|普通用户",value:"10"}}),a("el-option",{attrs:{label:"15|红名单",value:"15"}}),a("el-option",{attrs:{label:"18|普通客户18",value:"18"}}),a("el-option",{attrs:{label:"19|普通客户19",value:"19"}}),a("el-option",{attrs:{label:"20|重要用户",value:"20"}}),a("el-option",{attrs:{label:"21|特殊",value:"21"}}),a("el-option",{attrs:{label:"22|74重保",value:"22"}}),a("el-option",{attrs:{label:"23|12345视察",value:"23"}}),a("el-option",{attrs:{label:"25|1024(1)",value:"25"}}),a("el-option",{attrs:{label:"26|1024(2)",value:"26"}}),a("el-option",{attrs:{label:"27|1024(3)",value:"27"}}),a("el-option",{attrs:{label:"28|1024(4)",value:"28"}}),a("el-option",{attrs:{label:"29|1024(5)",value:"29"}}),a("el-option",{attrs:{label:"31|两委两组",value:"31"}}),a("el-option",{attrs:{label:"32|两委两组测试",value:"32"}}),a("el-option",{attrs:{label:"33|测试语音",value:"33"}}),a("el-option",{attrs:{label:"34|企业热线",value:"34"}}),a("el-option",{attrs:{label:"35|京津冀",value:"35"}})],1)],1),a("el-form-item",{attrs:{label:"技能组"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.SKILL_ID,callback:function(a){e.$set(e.queryParams,"SKILL_ID",a)},expression:"queryParams.SKILL_ID"}},[a("el-option",{attrs:{label:"1|市民服务",value:"1"}}),a("el-option",{attrs:{label:"99|企业服务",value:"99"}})],1)],1),a("el-form-item",{attrs:{label:"排队时长大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.WAIT_TIME0,callback:function(a){e.$set(e.queryParams,"WAIT_TIME0",a)},expression:"queryParams.WAIT_TIME0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"排队次数大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.WAIT_COUNT0,callback:function(a){e.$set(e.queryParams,"WAIT_COUNT0",a)},expression:"queryParams.WAIT_COUNT0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"应答时长大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.ACK_TIME0,callback:function(a){e.$set(e.queryParams,"ACK_TIME0",a)},expression:"queryParams.ACK_TIME0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"坐席次数大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENT_COUNT0,callback:function(a){e.$set(e.queryParams,"AGENT_COUNT0",a)},expression:"queryParams.AGENT_COUNT0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"通话时长大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENT_TIME0,callback:function(a){e.$set(e.queryParams,"AGENT_TIME0",a)},expression:"queryParams.AGENT_TIME0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"成功坐席次数大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.AGENT_ANSWER0,callback:function(a){e.$set(e.queryParams,"AGENT_ANSWER0",a)},expression:"queryParams.AGENT_ANSWER0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"ivr时长大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.IVR_TIME0,callback:function(a){e.$set(e.queryParams,"IVR_TIME0",a)},expression:"queryParams.IVR_TIME0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"ivr次数大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.IVR_COUNT0,callback:function(a){e.$set(e.queryParams,"IVR_COUNT0",a)},expression:"queryParams.IVR_COUNT0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"总时长大于0"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.ALL_TIME0,callback:function(a){e.$set(e.queryParams,"ALL_TIME0",a)},expression:"queryParams.ALL_TIME0"}},[a("el-option",{attrs:{label:"否",value:""}}),a("el-option",{attrs:{label:"是",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(a){e.$set(e.queryParams,"BEGIN_TIME",a)},expression:"queryParams.BEGIN_TIME"}})],1),a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(a){e.$set(e.queryParams,"END_TIME",a)},expression:"queryParams.END_TIME"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),a("el-table-column",{attrs:{prop:"CALLID",label:"CALLID",align:"center",width:"200"}}),a("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫",align:"center",width:"130"}}),a("el-table-column",{attrs:{prop:"CALLEENO",label:"被叫",align:"center",width:"130"}}),a("el-table-column",{attrs:{prop:"CALLTYPE",label:"呼叫类型",align:"center",width:"110"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.callTypeText(a.row.CALLTYPE))+" ")]}}])}),a("el-table-column",{attrs:{prop:"WAIT_COUNT",label:"排队次数",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"WAIT_TIME",label:"排队时长",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"ACK_TIME",label:"应答时长",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"AGENT_COUNT",label:"坐席次数",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"AGENT_TIME",label:"通话时长",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"AGENT_ANSWER",label:"成功坐席次数",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"IVR_COUNT",label:"ivr次数",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"IVR_TIME",label:"ivr时长",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"ALL_TIME",label:"总时长",align:"center",width:"110"}}),a("el-table-column",{attrs:{prop:"START_TIME",label:"开始时间",align:"center",width:"180"}}),a("el-table-column",{attrs:{prop:"END_TIME",label:"结束时间",align:"center",width:"180"}}),a("el-table-column",{attrs:{prop:"ALL_TIME",label:"总时长",align:"center",width:"110"}})],1),a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)},r=[],s=(l("a573"),l("0c9f")),o=l("c2d0"),i={name:"ACheckCallRecord",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},serviceTypeOptions:[],queryParams:{CALLTYPE:"",CALLERNO:"",CALLEENO:"",SERVICENO:"",USER_LEVEL:"",SKILL_ID:"",WAIT_TIME0:"",WAIT_COUNT0:"",ACK_TIME0:"",AGENT_COUNT0:"",AGENT_TIME0:"",AGENT_ANSWER0:"",IVR_TIME0:"",IVR_COUNT0:"",ALL_TIME0:"",BEGIN_TIME:"",END_TIME:""}}},created(){this.initTime(),this.getServiceTypeList(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(o["g"])(),this.queryParams.END_TIME=Object(o["f"])()},getServiceTypeList(){Object(s["gb"])({AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},["common.serviceTypeList"]).then(e=>{if(e["common.serviceTypeList"]&&e["common.serviceTypeList"].data){const a=e["common.serviceTypeList"].data;this.serviceTypeOptions=Object.keys(a).map(e=>({value:e,label:a[e]}))}})},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(s["eb"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={CALLTYPE:"",CALLERNO:"",CALLEENO:"",SERVICENO:"",USER_LEVEL:"",SKILL_ID:"",WAIT_TIME0:"",WAIT_COUNT0:"",ACK_TIME0:"",AGENT_COUNT0:"",AGENT_TIME0:"",AGENT_ANSWER0:"",IVR_TIME0:"",IVR_COUNT0:"",ALL_TIME0:"",BEGIN_TIME:"",END_TIME:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出通话明细核对报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(s["w"])(this.searchedForm))}).catch(()=>{})},callTypeText(e){const a=["0","1","2","3","4","15","20","21","22","23","32","33","5","46"];return a.includes(e)?"呼入":"7"===e?"呼出":"未知"}}},n=i,c=(l("6ce2"),l("2877")),u=Object(c["a"])(n,t,r,!1,null,null,null);a["default"]=u.exports},da89:function(e,a,l){}}]);