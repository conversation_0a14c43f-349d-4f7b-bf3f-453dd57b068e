(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bc505be"],{"39b4":function(e,t,a){"use strict";a("5217")},"463d":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",{attrs:{label:"业务类型"}},[t("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.queryParams.SERVICENO,callback:function(t){e.$set(e.queryParams,"SERVICENO",t)},expression:"queryParams.SERVICENO"}},e._l(e.serviceTypeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫号码",align:"center"}}),t("el-table-column",{attrs:{prop:"CALLBEGIN",label:"呼叫开始日期时间",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"CALLEND",label:"呼叫结束日期时间",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"CALLTIME",label:"通话时长",align:"center"}}),t("el-table-column",{attrs:{prop:"ACW_TIME",label:"话后处理时长",align:"center"}}),t("el-table-column",{attrs:{prop:"DEVICENO",label:"座席人员工号",align:"center"}}),t("el-table-column",{attrs:{prop:"NAME",label:"姓名",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、呼叫开始日期时间：12345呼入通话转入坐席的开始时间")]),t("p",[e._v("2、呼叫结束日期时间：12345呼入通话坐席结束通话的结束时间")]),t("p",[e._v("3、通话时长：呼叫结束日期时间-呼叫开始日期时间")]),t("p",[e._v("4、话后处理时长：坐席进行话后处理的时间")]),t("p",[e._v("5、座席人员工号：坐席工号")]),t("p",[e._v("6、姓名：坐席姓名")])])}],i=(a("a573"),a("0c9f")),s=a("c2d0"),n={name:"DetailedCallSituation",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},serviceTypeOptions:[],queryParams:{BEGIN_TIME:"",END_TIME:"",SERVICENO:""}}},created(){this.initTime(),this.getServiceTypeList(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(s["g"])(),this.queryParams.END_TIME=Object(s["f"])()},getServiceTypeList(){Object(i["gb"])({},["common.serviceTypeList"]).then(e=>{if(e["common.serviceTypeList"]&&e["common.serviceTypeList"].data){const t=e["common.serviceTypeList"].data;this.serviceTypeOptions=Object.keys(t).map(e=>({value:e,label:t[e]}))}}).catch(e=>{console.error("获取业务类型列表失败",e)})},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(i["fb"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={BEGIN_TIME:"",END_TIME:"",SERVICENO:""},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出详细呼叫情况统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(i["x"])(this.searchedForm))}).catch(()=>{})}}},o=n,c=(a("39b4"),a("2877")),p=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=p.exports},5217:function(e,t,a){}}]);