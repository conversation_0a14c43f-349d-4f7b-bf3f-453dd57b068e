(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4c26366a"],{"322a":function(e,a,l){"use strict";l.r(a);var t=function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-page"},[a("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small",model:e.searchForm}},[a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.BEGIN_TIME,callback:function(a){e.$set(e.searchForm,"BEGIN_TIME",a)},expression:"searchForm.BEGIN_TIME"}})],1),a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.searchForm.END_TIME,callback:function(a){e.$set(e.searchForm,"END_TIME",a)},expression:"searchForm.END_TIME"}})],1),a("el-form-item",{attrs:{label:"拨打次数"}},[a("el-input-number",{staticStyle:{width:"120px"},attrs:{min:1},model:{value:e.searchForm.CALL_COUNT,callback:function(a){e.$set(e.searchForm,"CALL_COUNT",a)},expression:"searchForm.CALL_COUNT"}})],1),a("el-form-item",{attrs:{label:"被叫号码"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.searchForm.CALLEENO,callback:function(a){e.$set(e.searchForm,"CALLEENO",a)},expression:"searchForm.CALLEENO"}},e._l(e.calleeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"客户级别"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.searchForm.USER_LEVEL,callback:function(a){e.$set(e.searchForm,"USER_LEVEL",a)},expression:"searchForm.USER_LEVEL"}},e._l(e.userLevelOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")])],1),a("el-divider",{attrs:{direction:"vertical"}}),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),a("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"}}),a("el-table-column",{attrs:{prop:"CALLEENO",label:"被叫号码",align:"center"}}),a("el-table-column",{attrs:{prop:"CALLERNO",label:"主叫号码",align:"center"}}),a("el-table-column",{attrs:{prop:"CALL_COUNT",label:"未接通次数",align:"center"}})],1),a("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pagination.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,a=e._self._c;return a("div",{staticClass:"stat-desc"},[a("h4",[e._v("统计口径")]),a("p",[e._v("1、未接通次数：呼入通话不存在坐席的通话次数")])])}],i=(l("0643"),l("4e3e"),l("88a7"),l("271a"),l("5494"),l("c2d0")),s=l("0c9f"),n={name:"CallNoAnswerCount",data(){return{searchForm:{BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),CALL_COUNT:2,CALLEENO:"12345",USER_LEVEL:"",statStepSize:"3",pageIndex:1,pageSize:15},loading:!1,tableData:[],pagination:{currentPage:1,pageSize:15,total:0},calleeOptions:[{value:"12345",label:"12345政府热线"},{value:"12356",label:"12356计生热线"},{value:"12312",label:"12312商委服务热线"},{value:"65246271",label:"65246271市公安局"},{value:"12318",label:"12318文化投诉举报热线"},{value:"12301",label:"12301文化和旅游热线"},{value:"68709990",label:"68709990中关村管理委会"},{value:"12319",label:"12319市城市管理委"},{value:"68556666",label:"68556666市水务局"},{value:"96156",label:"96156民政局"},{value:"68317307",label:"68317307市交管局"},{value:"12336",label:"12336市规则和自然资源委"},{value:"12350",label:"12350市应急管理局"},{value:"12369",label:"12369市生态环境局"},{value:"12320",label:"12320市卫健委"},{value:"12316",label:"12316市农业农村局"},{value:"12330",label:"12330市知识产权局"},{value:"65169063",label:"65159063新闻热线"},{value:"96391",label:"96391市教育热线"},{value:"12315",label:"12315市场监管局"},{value:"12385",label:"12385市残联"},{value:"68812345",label:"68812345石景山分中心"},{value:"69112345",label:"69112345延庆区分中心"},{value:"96196",label:"96196歌华有线"},{value:"11185",label:"11185邮政集团"},{value:"96165",label:"96165地铁运营"},{value:"96159",label:"96159排水集团"},{value:"96069",label:"96069热力集团"},{value:"96116",label:"96116自来水集团"},{value:"96166",label:"96166公交集团"},{value:"96116",label:"96116自来水集团"},{value:"96011",label:"96011首发集团"},{value:"96777",label:"96777燃气集团"},{value:"67601234",label:"67601234城市照明管理中心"}],userLevelOptions:[{value:"0",label:"0|普通用户0"},{value:"1",label:"1|普通用户0"},{value:"10",label:"10|普通用户"},{value:"15",label:"15|红名单"},{value:"18",label:"18|普通客户18"},{value:"19",label:"19|普通客户19"},{value:"20",label:"20|重要用户"},{value:"21",label:"21|特殊"},{value:"22",label:"22|74重保"},{value:"23",label:"23|12345视察"},{value:"25",label:"25|1024(1)"},{value:"26",label:"25|1024(2)"},{value:"27",label:"25|1024(3)"},{value:"28",label:"25|1024(4)"},{value:"29",label:"25|1024(5)"},{value:"31",label:"31|两委两组"},{value:"32",label:"32|两委两组测试"},{value:"33",label:"33|测试语音"},{value:"34",label:"34|企业热线"},{value:"35",label:"35|京津冀"}]}},created(){this.loadTableData(),this.loadDictionaries()},methods:{loadDictionaries(){const e={params:{AGENTID:"",WORKGROUPID:"",statStepSize:"3",BEGIN_TIME:"",END_TIME:""},controls:["common.agentList","common.workGroupList"]};Object(s["G"])(e).then(e=>{}).catch(e=>{this.$message.error("获取字典数据失败")})},loadTableData(){this.loading=!0;const e={...this.searchForm,pageType:3,pageIndex:this.pagination.currentPage,pageSize:this.pagination.pageSize};this.searchedForm=e,Object(s["H"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.pagination.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败")}).finally(()=>{this.loading=!1})},exportDetail(){this.$confirm("是否导出拨打若干次未接通的来电号码统计报表?","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=new URLSearchParams;Object.keys(this.searchForm).forEach(a=>{e.append(a,this.searchForm[a])});const a=Object(s["z"])(e.toString()),l=document.createElement("a");l.href=a,l.target="_blank",document.body.appendChild(l),l.click(),document.body.removeChild(l),this.$message({type:"success",message:"导出中，请稍候"})}).catch(()=>{})},handleSizeChange(e){this.pagination.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pagination.currentPage=e,this.loadTableData()},resetForm(){this.searchForm={BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),CALL_COUNT:2,CALLEENO:"12345",USER_LEVEL:"",statStepSize:"3",pageIndex:1,pageSize:15},this.pagination.currentPage=1,this.loadTableData()}}},o=n,c=(l("8cdd"),l("2877")),u=Object(c["a"])(o,t,r,!1,null,null,null);a["default"]=u.exports},"7cb1":function(e,a,l){},"8cdd":function(e,a,l){"use strict";l("7cb1")}}]);