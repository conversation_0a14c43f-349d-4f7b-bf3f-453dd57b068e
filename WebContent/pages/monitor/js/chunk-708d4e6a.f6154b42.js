(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-708d4e6a"],{"271a":function(t,e,n){"use strict";var a=n("cb2d"),r=n("e330"),l=n("577e"),o=n("d6d6"),i=URLSearchParams,s=i.prototype,c=r(s.getAll),u=r(s.has),d=new i("a=1");!d.has("a",2)&&d.has("a",void 0)||a(s,"has",(function(t){var e=arguments.length,n=e<2?void 0:arguments[1];if(e&&void 0===n)return u(this,t);var a=c(this,t);o(e,1);var r=l(n),i=0;while(i<a.length)if(a[i++]===r)return!0;return!1}),{enumerable:!0,unsafe:!0})},"37e7":function(t,e,n){},5494:function(t,e,n){"use strict";var a=n("83ab"),r=n("e330"),l=n("edd0"),o=URLSearchParams.prototype,i=r(o.forEach);a&&!("size"in o)&&l(o,"size",{get:function(){var t=0;return i(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"577e":function(t,e,n){"use strict";var a=n("f5df"),r=String;t.exports=function(t){if("Symbol"===a(t))throw new TypeError("Cannot convert a Symbol value to a string");return r(t)}},"68be":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-page"},[e("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[e("el-form-item",{attrs:{label:"开始时间"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:t.searchForm.BEGIN_TIME,callback:function(e){t.$set(t.searchForm,"BEGIN_TIME",e)},expression:"searchForm.BEGIN_TIME"}})],1),e("el-form-item",{attrs:{label:"-"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:t.searchForm.END_TIME,callback:function(e){t.$set(t.searchForm,"END_TIME",e)},expression:"searchForm.END_TIME"}})],1),e("el-form-item",{attrs:{label:"统计步长"}},[e("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.searchForm.statStepSize,callback:function(e){t.$set(t.searchForm,"statStepSize",e)},expression:"searchForm.statStepSize"}},[e("el-option",{attrs:{value:"0",label:"时段"}}),e("el-option",{attrs:{value:"3",label:"天"}}),e("el-option",{attrs:{value:"6",label:"月"}})],1)],1),e("el-form-item",{attrs:{label:"工号"}},[e("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:t.searchForm.AGENTID,callback:function(e){t.$set(t.searchForm,"AGENTID",e)},expression:"searchForm.AGENTID"}},t._l(t.agentList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"班组"}},[e("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:t.searchForm.AGENTWORKGROUP,callback:function(e){t.$set(t.searchForm,"AGENTWORKGROUP",e)},expression:"searchForm.AGENTWORKGROUP"}},t._l(t.workGroupList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索 ")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.exportDetail}},[e("i",{staticClass:"el-icon-download"}),t._v(" 导出 ")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"}},[e("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间","min-width":"150",align:"center",fixed:"left"}}),e("el-table-column",{attrs:{prop:"WORKNO",label:"工号","min-width":"150",align:"center",fixed:"left"}}),e("el-table-column",{attrs:{prop:"NAME",label:"姓名","min-width":"150",align:"center",fixed:"left"}}),e("el-table-column",{attrs:{prop:"SATISF_COUNT",label:"进入测评系统的量","min-width":"150",align:"center",fixed:"left"}}),e("el-table-column",{attrs:{prop:"SATISF_EFFECT_COUNT",label:"参与测评总量","min-width":"150",align:"center",fixed:"left"}}),e("el-table-column",{attrs:{prop:"SATISF_EFFECT_PERCENT",label:"测评率","min-width":"150",align:"center",fixed:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_EFFECT_PERCENT))+" ")]}}])}),e("el-table-column",{attrs:{label:"非常满意",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_VERYGOOD_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_VERYGOOD_PERCENT",label:"占参与测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_VERYGOOD_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"满意",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_GOOD_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_GOOD_PERCENT",label:"占参与测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_GOOD_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"一般",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_JUSTSOSO_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_JUSTSOSO_PERCENT",label:"占参与测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_JUSTSOSO_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"不满意",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_NOTGOOD_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_NOTGOOD_PERCENT",label:"占参与测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_NOTGOOD_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"非常不满意",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_NOTVERYGOOD_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_NOTVERYGOOD_PERCENT",label:"占参与测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_NOTVERYGOOD_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"放弃",align:"center"}},[e("el-table-column",{attrs:{prop:"SATISF_GIVEUP_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SATISF_GIVEUP_PERCENT",label:"占进入测评总量的百分比（℅）","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SATISF_GIVEUP_PERCENT))+" ")]}}])})],1),e("el-table-column",{attrs:{label:"满意率",align:"center"}},[e("el-table-column",{attrs:{prop:"SUM_GOOD_COUNT",label:"数量（次）","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"SUM_GOOD_PERCENT",label:"占参与测评总量(%)","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getPercent(e.row.SUM_GOOD_PERCENT))+" ")]}}])})],1)],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":t.currentPage,"page-sizes":[15,25,50,100,200],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total,background:""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}}),t._m(0)],1)},r=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-desc"},[e("h4",[t._v("统计口径")]),e("p",[t._v("1、进入测评系统的量：通话进入到测评流程的次数")]),e("p",[t._v("2、参与测评总量：参与测评存在有效数据的次数")]),e("p",[t._v("3、测评率：参与测评总量/进入测评系统的量")]),e("p",[t._v("4、数量（次）：坐席获取该满意度的次数")]),e("p",[t._v("5、占参与测评总量的百分比（℅）：数量（次）/参与测评总量")])])}],l=(n("0643"),n("4e3e"),n("a573"),n("88a7"),n("271a"),n("5494"),n("b775")),o=n("c2d0"),i={name:"AgentSatisfCount",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,agentList:[],workGroupList:[],searchForm:{BEGIN_TIME:Object(o["e"])(),END_TIME:Object(o["d"])(),statStepSize:"0",AGENTID:"",AGENTWORKGROUP:""}}},created(){this.getDictionaries(),this.loadData()},methods:{getDictionaries(){Object(l["a"])({url:"/cx-report-12345/webcall",method:"post",data:{data:JSON.stringify({params:{},controls:["common.agentList","common.workGroupList"]})},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(t=>{if(t){if(t["common.agentList"]){const e=t["common.agentList"].data||{};this.agentList=Object.keys(e).map(t=>({value:t,label:e[t]}))}if(t["common.workGroupList"]){const e=t["common.workGroupList"].data||{};this.workGroupList=Object.keys(e).map(t=>{const[n,a]=e[t].split("|");return{value:n,label:a}})}}}).catch(()=>{this.$message.error("获取字典数据失败")})},loadData(){this.loading=!0;const t={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize,AGENTID:this.searchForm.AGENTID||"0",AGENTWORKGROUP:this.searchForm.AGENTWORKGROUP||"0"};this.searchedForm=t,Object(l["a"])({url:"/cx-report-12345/webcall?action=statDao.getAgentSatisfStat",method:"post",data:{data:JSON.stringify(t)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(t=>{this.loading=!1,1===t.state?(this.tableData=t.data||[],this.total=t.totalRow||0):this.$message.error(t.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(t){this.pageSize=t,this.loadData()},handleCurrentChange(t){this.currentPage=t,this.loadData()},exportDetail(){this.$confirm("是否导出话务员满意度调查报表_12345","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=new URLSearchParams;Object.keys(this.searchForm).forEach(e=>{t.append(e,this.searchForm[e]||"")}),window.location.href="/cx-report-12345/servlet/export?action=exportAgentSatisfStat&"+t.toString()}).catch(()=>{})},getPercent(t){return(100*t).toFixed(2)+"%"}}},s=i,c=(n("b5e5"),n("2877")),u=Object(c["a"])(s,a,r,!1,null,null,null);e["default"]=u.exports},"88a7":function(t,e,n){"use strict";var a=n("cb2d"),r=n("e330"),l=n("577e"),o=n("d6d6"),i=URLSearchParams,s=i.prototype,c=r(s.append),u=r(s["delete"]),d=r(s.forEach),g=r([].push),p=new i("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&a(s,"delete",(function(t){var e=arguments.length,n=e<2?void 0:arguments[1];if(e&&void 0===n)return u(this,t);var a=[];d(this,(function(t,e){g(a,{key:e,value:t})})),o(e,1);var r,i=l(t),s=l(n),p=0,h=0,f=!1,m=a.length;while(p<m)r=a[p++],f||r.key===i?(f=!0,u(this,r.key)):h++;while(h<m)r=a[h++],r.key===i&&r.value===s||c(this,r.key,r.value)}),{enumerable:!0,unsafe:!0})},b5e5:function(t,e,n){"use strict";n("37e7")},c2d0:function(t,e,n){"use strict";function a(){return l()}function r(){return i()}function l(){const t=new Date,e=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0");return`${e}-${n}-${a} 00:00:00`}function o(){const t=new Date,e=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0");return`${e}-${n}-${a} 23:59:59`}function i(){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1);let n=e.getMonth()+1;n<10&&(n="0"+n);let a=e.getDate();return a<10&&(a="0"+a),e.getFullYear()+"-"+n+"-"+a+" 00:00:00"}function s(){const t=new Date;return t.setHours(t.getHours()-1),c(t,"yyyy-MM-dd HH:mm:ss")}function c(t,e){const n={"M+":t.getMonth()+1,"d+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(const a in n)new RegExp("("+a+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?n[a]:("00"+n[a]).substr((""+n[a]).length)));return e}function u(t){var e;const n=(null===this||void 0===this||null===(e=this.searchedForm)||void 0===e?void 0:e.statStepSize)||"3";if(t.SELECT_TIME)return t.SELECT_TIME;if("1"===n){const e=g("HH:mm:ss",t.HALF_HOUR,"MINUTE",30);return t.DAY+" "+t.HALF_HOUR+"-"+e}if("2"===n){const e=g("HH",t.HOUR,"HOUR",1);return t.DAY+" "+t.HOUR+"-"+e}return"3"===n?t.DAY:"4"===n?"星期"+d(t.WEEK):"5"===n?"1"===t.XUN?t.MONTH+"上旬":"2"===t.XUN?t.MONTH+"中旬":"3"===t.XUN?t.MONTH+"下旬":t.MONTH+t.XUN:"6"===n?t.MONTH:"7"===n?t.YEAR+"第"+d(t.QUARTER)+"季度":"8"===n?"1"===t.HALFYEAR?t.YEAR+"上半年":"2"===t.HALFYEAR?t.YEAR+"下半年":t.YEAR+t.HALFYEAR:"9"===n?t.YEAR:""}function d(t){const e=["零","一","二","三","四","五","六","七","八","九"];if(t=parseInt(t,10),t<10)return e[t];if(t>=10&&t<20)return"十"+e[t%10];const n=Math.floor(t/10),a=t%10;return 0===a?e[n]+"十":e[n]+"十"+e[a]}function g(t,e,n,a){let r;r="HH"===t?"1970-01-01 "+e+":00:00":"HH:mm:ss"===t?"1970-01-01 "+e:e;const l=new Date(r);switch(n){case"MINUTE":l.setMinutes(l.getMinutes()+a);let t=l.getHours().toString();1===t.length&&(t="0"+t);let n=l.getMinutes().toString();1===n.length&&(n="0"+n);let r=l.getSeconds().toString();return 1===r.length&&(r="0"+r),t+":"+n+":"+r;case"HOUR":l.setHours(l.getHours()+a);let o=l.getHours().toString();return 1===o.length&&(o="0"+o),o;default:return e}}function p(t){if(void 0===t||null===t||""===t)return"00:00:00";t=parseInt(t);const e=Math.floor(t/3600);let n=Math.floor(t%3600/60),a=t%60;return n=n<10?"0"+n:n,a=a<10?"0"+a:a,e+":"+n+":"+a}function h(t){return void 0===t||null===t||""===t?"0.00%":"string"===typeof t&&t.indexOf("%")>=0?t:(t*=100,t=parseFloat(parseFloat(t).toPrecision(12)),t+"%")}function f(){const t=new Date;return c(t,"yyyy-MM-dd HH:mm:ss")}function m(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+1);const n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return`${n}-${a}-${r} 00:00:00`}function S(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+7);const n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return`${n}-${a}-${r} 23:59:59`}function b(){const t=new Date,e=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0");return`${e}-${n}-01 00:00:00`}function E(){const t=new Date,e=t.getFullYear(),n=t.getMonth()+1,a=new Date(e,n,0),r=String(a.getDate()).padStart(2,"0");return`${e}-${String(n).padStart(2,"0")}-${r} 23:59:59`}function T(){const t=(new Date).getFullYear();return t+"-01-01 00:00:00"}function _(){const t=(new Date).getFullYear();return t+"-12-31 23:59:59"}n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return r})),n.d(e,"o",(function(){return l})),n.d(e,"n",(function(){return o})),n.d(e,"p",(function(){return i})),n.d(e,"g",(function(){return s})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return p})),n.d(e,"b",(function(){return h})),n.d(e,"f",(function(){return f})),n.d(e,"k",(function(){return m})),n.d(e,"j",(function(){return S})),n.d(e,"i",(function(){return b})),n.d(e,"h",(function(){return E})),n.d(e,"m",(function(){return T})),n.d(e,"l",(function(){return _}))},d6d6:function(t,e,n){"use strict";var a=TypeError;t.exports=function(t,e){if(t<e)throw new a("Not enough arguments");return t}}}]);