(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c977a250"],{"271a":function(t,e,a){"use strict";var n=a("cb2d"),r=a("e330"),l=a("577e"),o=a("d6d6"),i=URLSearchParams,s=i.prototype,c=r(s.getAll),u=r(s.has),p=new i("a=1");!p.has("a",2)&&p.has("a",void 0)||n(s,"has",(function(t){var e=arguments.length,a=e<2?void 0:arguments[1];if(e&&void 0===a)return u(this,t);var n=c(this,t);o(e,1);var r=l(a),i=0;while(i<n.length)if(n[i++]===r)return!0;return!1}),{enumerable:!0,unsafe:!0})},5494:function(t,e,a){"use strict";var n=a("83ab"),r=a("e330"),l=a("edd0"),o=URLSearchParams.prototype,i=r(o.forEach);n&&!("size"in o)&&l(o,"size",{get:function(){var t=0;return i(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"577e":function(t,e,a){"use strict";var n=a("f5df"),r=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return r(t)}},"5ca9":function(t,e,a){},"74ae":function(t,e,a){"use strict";a("5ca9")},"88a7":function(t,e,a){"use strict";var n=a("cb2d"),r=a("e330"),l=a("577e"),o=a("d6d6"),i=URLSearchParams,s=i.prototype,c=r(s.append),u=r(s["delete"]),p=r(s.forEach),d=r([].push),h=new i("a=1&a=2&b=3");h["delete"]("a",1),h["delete"]("b",void 0),h+""!=="a=2"&&n(s,"delete",(function(t){var e=arguments.length,a=e<2?void 0:arguments[1];if(e&&void 0===a)return u(this,t);var n=[];p(this,(function(t,e){d(n,{key:e,value:t})})),o(e,1);var r,i=l(t),s=l(a),h=0,m=0,g=!1,f=n.length;while(h<f)r=n[h++],g||r.key===i?(g=!0,u(this,r.key)):m++;while(m<f)r=n[m++],r.key===i&&r.value===s||c(this,r.key,r.value)}),{enumerable:!0,unsafe:!0})},c2d0:function(t,e,a){"use strict";function n(){return l()}function r(){return i()}function l(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${e}-${a}-${n} 00:00:00`}function o(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${e}-${a}-${n} 23:59:59`}function i(){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1);let a=e.getMonth()+1;a<10&&(a="0"+a);let n=e.getDate();return n<10&&(n="0"+n),e.getFullYear()+"-"+a+"-"+n+" 00:00:00"}function s(){const t=new Date;return t.setHours(t.getHours()-1),c(t,"yyyy-MM-dd HH:mm:ss")}function c(t,e){const a={"M+":t.getMonth()+1,"d+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(const n in a)new RegExp("("+n+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?a[n]:("00"+a[n]).substr((""+a[n]).length)));return e}function u(t){var e;const a=(null===this||void 0===this||null===(e=this.searchedForm)||void 0===e?void 0:e.statStepSize)||"3";if(t.SELECT_TIME)return t.SELECT_TIME;if("1"===a){const e=d("HH:mm:ss",t.HALF_HOUR,"MINUTE",30);return t.DAY+" "+t.HALF_HOUR+"-"+e}if("2"===a){const e=d("HH",t.HOUR,"HOUR",1);return t.DAY+" "+t.HOUR+"-"+e}return"3"===a?t.DAY:"4"===a?"星期"+p(t.WEEK):"5"===a?"1"===t.XUN?t.MONTH+"上旬":"2"===t.XUN?t.MONTH+"中旬":"3"===t.XUN?t.MONTH+"下旬":t.MONTH+t.XUN:"6"===a?t.MONTH:"7"===a?t.YEAR+"第"+p(t.QUARTER)+"季度":"8"===a?"1"===t.HALFYEAR?t.YEAR+"上半年":"2"===t.HALFYEAR?t.YEAR+"下半年":t.YEAR+t.HALFYEAR:"9"===a?t.YEAR:""}function p(t){const e=["零","一","二","三","四","五","六","七","八","九"];if(t=parseInt(t,10),t<10)return e[t];if(t>=10&&t<20)return"十"+e[t%10];const a=Math.floor(t/10),n=t%10;return 0===n?e[a]+"十":e[a]+"十"+e[n]}function d(t,e,a,n){let r;r="HH"===t?"1970-01-01 "+e+":00:00":"HH:mm:ss"===t?"1970-01-01 "+e:e;const l=new Date(r);switch(a){case"MINUTE":l.setMinutes(l.getMinutes()+n);let t=l.getHours().toString();1===t.length&&(t="0"+t);let a=l.getMinutes().toString();1===a.length&&(a="0"+a);let r=l.getSeconds().toString();return 1===r.length&&(r="0"+r),t+":"+a+":"+r;case"HOUR":l.setHours(l.getHours()+n);let o=l.getHours().toString();return 1===o.length&&(o="0"+o),o;default:return e}}function h(t){if(void 0===t||null===t||""===t)return"00:00:00";t=parseInt(t);const e=Math.floor(t/3600);let a=Math.floor(t%3600/60),n=t%60;return a=a<10?"0"+a:a,n=n<10?"0"+n:n,e+":"+a+":"+n}function m(t){return void 0===t||null===t||""===t?"0.00%":"string"===typeof t&&t.indexOf("%")>=0?t:(t*=100,t=parseFloat(parseFloat(t).toPrecision(12)),t+"%")}function g(){const t=new Date;return c(t,"yyyy-MM-dd HH:mm:ss")}function f(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+1);const a=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return`${a}-${n}-${r} 00:00:00`}function b(){const t=new Date,e=t.getDay()||7;t.setDate(t.getDate()-e+7);const a=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return`${a}-${n}-${r} 23:59:59`}function _(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0");return`${e}-${a}-01 00:00:00`}function E(){const t=new Date,e=t.getFullYear(),a=t.getMonth()+1,n=new Date(e,a,0),r=String(n.getDate()).padStart(2,"0");return`${e}-${String(a).padStart(2,"0")}-${r} 23:59:59`}function v(){const t=(new Date).getFullYear();return t+"-01-01 00:00:00"}function T(){const t=(new Date).getFullYear();return t+"-12-31 23:59:59"}a.d(e,"e",(function(){return n})),a.d(e,"d",(function(){return r})),a.d(e,"o",(function(){return l})),a.d(e,"n",(function(){return o})),a.d(e,"p",(function(){return i})),a.d(e,"g",(function(){return s})),a.d(e,"c",(function(){return u})),a.d(e,"a",(function(){return h})),a.d(e,"b",(function(){return m})),a.d(e,"f",(function(){return g})),a.d(e,"k",(function(){return f})),a.d(e,"j",(function(){return b})),a.d(e,"i",(function(){return _})),a.d(e,"h",(function(){return E})),a.d(e,"m",(function(){return v})),a.d(e,"l",(function(){return T}))},d482:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-page"},[e("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[e("el-form-item",{attrs:{label:"坐席工号"}},[e("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:t.searchForm.AGENTID,callback:function(e){t.$set(t.searchForm,"AGENTID",e)},expression:"searchForm.AGENTID"}},t._l(t.agentList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"班组"}},[e("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:t.searchForm.WORKGROUPID,callback:function(e){t.$set(t.searchForm,"WORKGROUPID",e)},expression:"searchForm.WORKGROUPID"}},t._l(t.workGroupList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"统计步长"}},[e("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.searchForm.statStepSize,callback:function(e){t.$set(t.searchForm,"statStepSize",e)},expression:"searchForm.statStepSize"}},[e("el-option",{attrs:{value:"0",label:"时段"}}),e("el-option",{attrs:{value:"1",label:"半小时"}}),e("el-option",{attrs:{value:"2",label:"小时"}}),e("el-option",{attrs:{value:"3",label:"天"}}),e("el-option",{attrs:{value:"6",label:"月"}}),e("el-option",{attrs:{value:"9",label:"年"}})],1)],1),e("el-form-item",{attrs:{label:"开始时间"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:t.searchForm.BEGIN_TIME,callback:function(e){t.$set(t.searchForm,"BEGIN_TIME",e)},expression:"searchForm.BEGIN_TIME"}})],1),e("el-form-item",{attrs:{label:"-"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:t.searchForm.END_TIME,callback:function(e){t.$set(t.searchForm,"END_TIME",e)},expression:"searchForm.END_TIME"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索 ")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.exportDetail}},[e("i",{staticClass:"el-icon-download"}),t._v(" 导出 ")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),e("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"250",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.getDateTime(e.row))+" ")]}}])}),e("el-table-column",{attrs:{prop:"AGENTWORKGROUP",label:"班组","min-width":"130",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENTID",label:"工号","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENTNAME",label:"姓名","min-width":"130",align:"center"}}),e("el-table-column",{attrs:{prop:"LOGIN_TIME",label:"签入时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_IN_COUNT",label:"应答量","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_IN_SUCC_COUNT",label:"呼入接通量","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_IN_TIME",label:"呼入通话时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AVG_CALL_IN_TIME",label:"平均呼入通话时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_IN_MAX_TIME",label:"最长呼入通话时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_OUT_COUNT",label:"呼出总数","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_OUT_SUCC_COUNT",label:"呼出成功数","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_OUT_SUCC_RATE",label:"呼出成功率","min-width":"110",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.CALL_OUT_SUCC_RATE))+" ")]}}])}),e("el-table-column",{attrs:{prop:"CALL_OUT_TIME",label:"呼出总时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AVG_CALL_OUT_TIME",label:"呼出均长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"CALL_OUT_MAX_TIME",label:"呼出最大时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"RESORT_COUNT",label:"内部求助数","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"RESORT_TIME",label:"内部求助时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"ARRANGE_TIME",label:"话后处理时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AVG_ARRANGE_TIME",label:"平均话后处理时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENT_COUNT",label:"通话总数","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"AGENT_TIME",label:"通话总时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"REST_TIME",label:"休息时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"ORDER_TIME",label:"公派时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"BUSY_TIME",label:"离席时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"IDLE_TIME",label:"空闲时长","min-width":"110",align:"center"}}),e("el-table-column",{attrs:{prop:"WORKING_HOURS_RATE",label:"接通率工时利用率工作时长[秒]/签入时长","min-width":"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.WORKING_HOURS_RATE))+" ")]}}])}),e("el-table-column",{attrs:{prop:"REALITY_WORKING_HOURS_RATE",label:"实际工作效率工作时长[小时]/接通量","min-width":"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatRate(e.row.REALITY_WORKING_HOURS_RATE))+" ")]}}])})],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":t.currentPage,"page-sizes":[15,25,50,100,200],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total,background:""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}}),t._m(0)],1)},r=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"stat-desc"},[e("h4",[t._v("统计口径")]),e("p",[t._v("1、签入时长：坐席签入状态的总时长")]),e("p",[t._v("2、应答量：呼入电话转坐席成功的数量")]),e("p",[t._v("3、呼入接通量：呼入电话转坐席通话时长>0的数量")]),e("p",[t._v("4、呼入通话时长：呼入电话坐席接通的总通话时长")]),e("p",[t._v("5、平均呼入通话时长：呼入通话时长/呼入接通量")]),e("p",[t._v("6、最长呼入通话时长：呼入电话中坐席通话时长最长的时长")]),e("p",[t._v("7、呼出总数：坐席外呼电话的数量")]),e("p",[t._v("8、呼出成功数：坐席外呼电话通话时长>0的数量")]),e("p",[t._v("9、呼出成功率：呼出成功数/呼出总数")]),e("p",[t._v("10、呼出总时长：坐席外呼通话的总时长")]),e("p",[t._v("11、呼出均长：呼出总时长/呼出成功数")]),e("p",[t._v("12、呼出最大时长：坐席外呼通话时长最长的时长")]),e("p",[t._v("13、内部求助数：坐席进行内部求助通话的数量")]),e("p",[t._v("14、内部求助时长：坐席进行内部求助通话的总时长")]),e("p",[t._v("15、话后处理时长：坐席挂断电话后进行整理态的总时长")]),e("p",[t._v("16、平均话后处理时长：话后处理时长/通话总数")]),e("p",[t._v("17、通话总数：呼入呼出接通总数")]),e("p",[t._v("18、通话总时长：呼入呼出通话总时长")]),e("p",[t._v("19、离席时长：坐席的离席时长")]),e("p",[t._v("20、空闲时长：坐席的空闲时长（签入时间-呼入通话时长-离席时长）")]),e("p",[t._v("21、接通率工时利用率：工作时长[秒]（通话时长+ 整理时长+内部求助时长+(签入时长-整理时长-通话时长-离席时长）/签入时长")]),e("p",[t._v("22、实际工作效率：工作时长[小时]（通话时长 + 整理时长）/接通量")])])}],l=(a("0643"),a("4e3e"),a("a573"),a("88a7"),a("271a"),a("5494"),a("b775")),o=a("c2d0"),i={name:"AgentComprehensiveStat",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,agentList:[],workGroupList:[],searchForm:{BEGIN_TIME:Object(o["e"])(),END_TIME:Object(o["d"])(),statStepSize:"3",AGENTID:"",WORKGROUPID:""}}},created(){this.getDictionaries(),this.loadData()},methods:{getDictionaries(){Object(l["a"])({url:"/cx-report-12345/webcall",method:"post",data:{data:JSON.stringify({params:{},controls:["common.agentList","common.workGroupList"]})},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(t=>{if(t){if(t["common.agentList"]){const e=t["common.agentList"].data||{};this.agentList=Object.keys(e).map(t=>({value:t,label:e[t]}))}if(t["common.workGroupList"]){const e=t["common.workGroupList"].data||{};this.workGroupList=Object.keys(e).map(t=>{const[a,n]=e[t].split("|");return{value:a,label:n}})}}}).catch(()=>{this.$message.error("获取字典数据失败")})},loadData(){this.loading=!0;const t={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize,AGENTID:this.searchForm.AGENTID,WORKGROUPID:this.searchForm.WORKGROUPID};this.searchedForm=t,Object(l["a"])({url:"/cx-report-12345/webcall?action=statDao.agentComprehensiveStat",method:"post",data:{data:JSON.stringify(t)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(t=>{this.loading=!1,1===t.state?(this.tableData=t.data||[],this.total=t.totalRow||0):this.$message.error(t.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(t){this.pageSize=t,this.loadData()},handleCurrentChange(t){this.currentPage=t,this.loadData()},exportDetail(){this.$confirm("是否导出座席综合统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=new URLSearchParams;Object.keys(this.searchForm).forEach(e=>{t.append(e,this.searchForm[e]||"")}),window.location.href="/cx-report-12345/servlet/export?action=exportAgentComprehensiveStat&"+t.toString()}).catch(()=>{})},getDateTime:o["c"],formatRate:o["b"],formatClock:o["a"]}},s=i,c=(a("74ae"),a("2877")),u=Object(c["a"])(s,n,r,!1,null,null,null);e["default"]=u.exports},d6d6:function(t,e,a){"use strict";var n=TypeError;t.exports=function(t,e){if(t<e)throw new n("Not enough arguments");return t}}}]);