(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ccc51b96"],{"0c3b":function(e,t,a){"use strict";a("4d30")},"4d30":function(e,t,a){},ae94:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"开始时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.BEGIN_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_TIME",t)},expression:"queryParams.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"结束时间"}},[t("el-date-picker",{staticStyle:{width:"170px"},attrs:{type:"datetime",placeholder:"结束时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.END_TIME,callback:function(t){e.$set(e.queryParams,"END_TIME",t)},expression:"queryParams.END_TIME"}})],1),t("el-form-item",{attrs:{label:"呼叫时长范围"}},[t("el-input-number",{staticStyle:{width:"120px"},attrs:{min:0},model:{value:e.queryParams.BEGIN_NUMBER_TIME,callback:function(t){e.$set(e.queryParams,"BEGIN_NUMBER_TIME",t)},expression:"queryParams.BEGIN_NUMBER_TIME"}}),t("span",{staticStyle:{margin:"0 8px"}},[e._v("-")]),t("el-input-number",{staticStyle:{width:"120px"},attrs:{min:0},model:{value:e.queryParams.END_NUMBER_TIME,callback:function(t){e.$set(e.queryParams,"END_NUMBER_TIME",t)},expression:"queryParams.END_NUMBER_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadTableData}},[e._v("搜索")]),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.resetForm}},[e._v("重置")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段",align:"center","min-width":"250"}}),t("el-table-column",{attrs:{prop:"AGENT_COUNT",label:"接通量",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v('1、接通量：接听12345呼入，坐席通话时长+应答时长符合"呼叫时长范围"条件的数量')])])}],l=a("0c9f"),s=a("c2d0"),n={name:"AgentCallConnStat",data(){return{loading:!1,tableData:[],total:0,pageIndex:1,pageSize:15,searchedForm:{},queryParams:{BEGIN_TIME:"",END_TIME:"",BEGIN_NUMBER_TIME:0,END_NUMBER_TIME:0}}},created(){this.initTime(),this.loadTableData()},methods:{initTime(){this.queryParams.BEGIN_TIME=Object(s["e"])(),this.queryParams.END_TIME=Object(s["d"])()},loadTableData(){this.loading=!0;const e={...this.queryParams,pageIndex:this.pageIndex,pageSize:this.pageSize,pageType:3};this.searchedForm=e,Object(l["K"])(e).then(e=>{1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(e=>{this.$message.error("查询失败"),console.error(e)}).finally(()=>{this.loading=!1})},handleSizeChange(e){this.pageSize=e,this.loadTableData()},handleCurrentChange(e){this.pageIndex=e,this.loadTableData()},resetForm(){this.queryParams={BEGIN_TIME:"",END_TIME:"",BEGIN_NUMBER_TIME:0,END_NUMBER_TIME:0},this.initTime(),this.pageIndex=1,this.loadTableData()},exportDetail(){this.$confirm("是否导出座席接通统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.open(Object(l["b"])(this.searchedForm))}).catch(()=>{})}}},o=n,c=(a("0c3b"),a("2877")),m=Object(c["a"])(o,r,i,!1,null,null,null);t["default"]=m.exports}}]);