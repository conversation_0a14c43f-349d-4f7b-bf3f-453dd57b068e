(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-509efe78"],{"0f6a":function(e,t,a){"use strict";a("9f9e")},"8bed":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"坐席工号"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.AGENTID,callback:function(t){e.$set(e.searchForm,"AGENTID",t)},expression:"searchForm.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"统计步长"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.statStepSize,callback:function(t){e.$set(e.searchForm,"statStepSize",t)},expression:"searchForm.statStepSize"}},[t("el-option",{attrs:{value:"0",label:"时段"}}),t("el-option",{attrs:{value:"1",label:"半小时"}}),t("el-option",{attrs:{value:"2",label:"小时"}}),t("el-option",{attrs:{value:"3",label:"天"}}),t("el-option",{attrs:{value:"4",label:"周"}}),t("el-option",{attrs:{value:"5",label:"旬"}}),t("el-option",{attrs:{value:"6",label:"月"}}),t("el-option",{attrs:{value:"7",label:"季度"}}),t("el-option",{attrs:{value:"8",label:"半年"}}),t("el-option",{attrs:{value:"9",label:"年"}})],1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"坐席工号","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTNAME",label:"坐席名称","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间段","min-width":"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getDateTime(t.row))+" ")]}}])}),t("el-table-column",{attrs:{prop:"CALL_COUNT",label:"呼出次数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"SUCC_COUNT",label:"呼出成功数","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"SUCC_RATE",label:"呼出成功率","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatRate(t.row.SUCC_RATE))+" ")]}}])}),t("el-table-column",{attrs:{prop:"FAIL_COUNT",label:"呼出失败次数","min-width":"140",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_TIME",label:"通话总时长","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatClock(t.row.AGENT_TIME))+" ")]}}])}),t("el-table-column",{attrs:{prop:"AVG_TIME",label:"通话均长","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatClock(t.row.AVG_TIME))+" ")]}}])}),t("el-table-column",{attrs:{prop:"MIN_TIME",label:"最短通话时长","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatClock(t.row.MIN_TIME))+" ")]}}])}),t("el-table-column",{attrs:{prop:"MAX_TIME",label:"最长通话时长","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatClock(t.row.MAX_TIME))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、呼出次数：坐席的外呼通话次数")]),t("p",[e._v("2、呼出成功数：坐席外呼通话时长>0的通话次数")]),t("p",[e._v("3、呼出成功率：呼出成功数/呼出次数")]),t("p",[e._v("4、呼出失败次数：坐席呼出通话的通话时长=0的次数")]),t("p",[e._v("5、通话总时长：坐席呼出通话的总通话时长")]),t("p",[e._v("6、通话均长：通话总时长/呼出成功数")]),t("p",[e._v("7、最短通话时长：坐席呼出通话的最小通话时长")]),t("p",[e._v("8、最长通话时长：坐席呼出通话的最大通话时长")])])}],n=(a("a573"),a("c2d0")),o=a("0c9f"),i={name:"AgentOutCall",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(n["e"])(),END_TIME:Object(n["d"])(),statStepSize:"3",AGENTID:""},searchedForm:{},agentOptions:[]}},created(){this.getAgentOptions(),this.loadData()},methods:{getAgentOptions(){Object(o["gb"])({},["common.agentList"]).then(e=>{if(e["common.agentList"]){const t=e["common.agentList"].data||{};this.agentOptions=Object.entries(t).map(([e,t])=>({value:e,label:t.split("|")[1]||t}))}}).catch(()=>{this.$message.error("获取坐席列表失败")})},loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,statStepSize:this.searchForm.statStepSize||"3",AGENTID:this.searchForm.AGENTID||""};this.searchedForm=e,Object(o["Q"])(e).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出话务员呼出话务量报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.location.href=Object(o["i"])(this.searchForm)}).catch(()=>{})},getDateTime:n["c"],formatClock:n["a"],formatRate:n["b"]}},s=i,c=(a("0f6a"),a("2877")),p=Object(c["a"])(s,l,r,!1,null,null,null);t["default"]=p.exports},"9f9e":function(e,t,a){}}]);