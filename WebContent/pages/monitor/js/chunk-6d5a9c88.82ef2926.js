(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d5a9c88"],{4225:function(t,e,a){"use strict";a("62b2")},"62b2":function(t,e,a){},e57e:function(t,e,a){t.exports=a.p+"img/warning.37f2056b.png"},e83a:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page",on:{click:t.toBack}},[e("div",{staticClass:"chartsBox",on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"back-btn",on:{click:t.toBack}},[e("img",{attrs:{src:a("d991"),alt:""}}),e("div",{staticClass:"artFont"},[t._v("关闭")])]),e("title-bg",{attrs:{title:"12345热线实时数据监控"}}),e("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[e("div",{staticClass:"top-left top-value"},[e("span",{staticClass:"artFont fz16"},[t._v("接线总人数:")]),e("span",{staticClass:"title-value fz16"},[t._v(t._s(t.leftNumber))])]),e("div",{staticClass:"top-right top-value"},[e("span",{staticClass:"artFont fz16"},[t._v("总业务量:")]),e("span",{staticClass:"title-value fz16"},[t._v(t._s(t.rightNumber))]),e("span",{staticClass:"artFont"},[t._v("(")]),e("span",{staticClass:"artFont fz16"},[t._v("12345:")]),e("span",{staticClass:"title-value fz16"},[t._v(t._s(t.number12345))]),e("span",{staticClass:"artFont fz16"},[t._v("整合热线:")]),e("span",{staticClass:"title-value fz16"},[t._v(t._s(t.numberZhrx))]),e("span",{staticClass:"artFont fz16"},[t._v("网络量:")]),e("span",{staticClass:"title-value fz16"},[t._v(t._s(t.numberWll))]),e("span",{staticClass:"artFont"},[t._v(")")])])]),e("div",{staticClass:"box"},[e("div",{staticClass:"box_center"},[e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("呼叫量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.lineChartCount))])])]),t.lineWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.lineChart}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("IVR收听量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.callIvrChartCount))])])]),t.callIvrWaring?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.callIvrCharts}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("人工接通量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.centerlineChartCount2))])])]),t.centerlineWarning2?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.centerlineChart2}})],1)])]),e("div",{staticClass:"box_center"},[e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("排队量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.centerlineChartCount3))])])]),t.centerlineWarning3?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.centerlineChart3}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("待接听坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentIdleChartCount))])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("话后坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentWorkCount))])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("离席坐席数")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.agentSetbusyCount))])])]),t.agentIdleWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.agentIdleChart}})],1)]),e("div",{staticClass:"line l2"}),e("div",{staticClass:"centerChartBox"},[e("div",{staticClass:"titleBox centerTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("未进入排队量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.overflowWaitChartCount))])])]),t.overflowWaitWarning?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"centerChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.overflowWaitChart}})],1)])]),e("div",{staticClass:"line l1"}),e("div",{staticClass:"box_bottom"},[e("div",{staticClass:"bottomChartBox"},[e("div",{staticClass:"titleBox BottomTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("全天接通率")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount1)+"%")])]),e("div",{staticClass:"psTitle1"},[e("span",{staticClass:"artFont"},[t._v("全业务接通率")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount3)+"%")])])]),t.bottomlineWarning1?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"bottomChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.bottomlineChart1}})],1)]),e("div",{staticClass:"line l3"}),e("div",{staticClass:"bottomChartBox"},[e("div",{staticClass:"titleBox BottomTitle"},[e("div",{staticClass:"psTitle"},[e("span",{staticClass:"artFont"},[t._v("主动挂断量")]),e("span",{staticClass:"title-value"},[t._v(t._s(t.bottomlineChartCount2))])])]),t.bottomlineWarning2?e("div",{staticClass:"warning"},[e("img",{attrs:{src:a("e57e"),alt:""}}),e("span",[t._v(" 超过阈值")])]):t._e(),e("div",{staticClass:"bottomChartS"},[e("echart",{staticClass:"echart",attrs:{data:t.bottomlineChart2}})],1)])])])],1)])},i=[],s=(a("0643"),a("4e3e"),a("a573"),a("981c")),n=a("365c"),r=a("313e");let o={trigger:"axis",borderColor:"#00AAFF",borderWidth:1,backgroundColor:"rgba(0, 27, 53, 0.75)",axisPointer:{type:"line",lineStyle:{color:"#00AAFF",width:1,type:"solid",shadowBlur:8,shadowColor:"rgba(255, 255, 255, 0.5)"}},textStyle:{color:"#fff"},formatter:function(t){var e="";return e+=t[0].data.time+"  <br/>",t.forEach((function(t){console.log(t);let a=`\n            <div style="display:inline-block;margin-right:8px;border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`;e+=`${a} ${t.data.name} : <span style="margin-left:4px;color:${t.color}">${t.value}${t.data.name.includes("率")?"%":""} </span><br/> `})),e}},c={left:0,right:20,top:32,bottom:0,containLabel:!0},h={left:0,right:32,top:32,bottom:0,containLabel:!0},d={name:"数量",type:"value",minInterval:1,axisLabel:{textStyle:{color:"#fff"}},splitLine:{lineStyle:{color:"#0080FF",type:"dashed",opacity:.3}},nameTextStyle:{color:"#fff"}},C={right:10,data:[{name:"告警基准线",lineStyle:{type:"solid",width:1,color:"#FF0080"},textStyle:{color:"#fff"}}],selectedMode:!1},g={type:"line",symbol:"none",name:"告警基准线",color:"transparent"};var v={name:"index",components:{Header:s["a"]},computed:{},watch:{},data(){return{leftNumber:"",rightNumber:"",number12345:"",numberZhrx:"",numberWll:"",lineChart:{color:["#00DCAA"],xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},legend:C,yAxis:d,tooltip:o,grid:{left:0,right:32,top:32,bottom:0,containLabel:!0},series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90,name:"告警基准线"}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},lineChartCount:0,lineWarning:!1,callIvrCharts:{color:["#00DCAA"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:o,grid:c,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},callIvrChartCount:0,callIvrWaring:!1,centerlineChart2:{color:["#00DCAA"],xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},legend:C,yAxis:d,tooltip:o,grid:c,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90,name:"告警基准线"}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},centerlineChartCount2:0,centerlineWarning2:!1,centerlineChart3:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:o,grid:c,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},centerlineChartCount3:0,centerlineWarning3:!1,agentIdleChart:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:o,grid:c,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},agentIdleChartCount:0,agentIdleWarning:!1,overflowWaitChart:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:5},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:o,grid:c,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}},areaStyle:{color:new r["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#FFDC00"},{offset:1,color:"rgb(0,0,0,0)"}])}},g]},overflowWaitChartCount:0,overflowWaitWarning:!1,bottomlineChart1:{color:["#00DCAA"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:3},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:o,grid:h,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}}},{data:[],type:"line",smooth:!0},g]},bottomlineChartCount1:0,bottomlineChartCount3:0,bottomlineWarning1:!1,bottomlineChart2:{color:["#FFDC00"],legend:C,xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"},showMaxLabel:!0,interval:3},nameTextStyle:{color:"#fff"},axisLine:{lineStyle:{color:"#00ffff",type:"solid",opacity:.2}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{color:"#0080FF",type:"solid",opacity:.3},alignWithLabel:!1},inverse:!1,data:Array.from({length:60},(t,e)=>e+1)},yAxis:d,tooltip:{trigger:"axis",borderColor:"#00AAFF",borderWidth:1,backgroundColor:"rgba(0, 27, 53, 0.75)",axisPointer:{type:"line",lineStyle:{color:"#00AAFF",width:1,type:"solid",shadowBlur:8,shadowColor:"rgba(255, 255, 255, 0.5)"}},textStyle:{color:"#fff"},formatter:function(t){var e="";return e+=t[0].data.time+"  <br/>",t.forEach((function(t){console.log(t);let a=`\n            <div style="display:inline-block;margin-right:8px;border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`,l=`\n            <div style="display:inline-block;margin-right:8px;visibility:hidden; border-radius:10px;width:10px;height:10px; position: relative;background-color:#fff; border: 2px solid ${t.color}; box-sizing: border-box; zIndex:2">\n               <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: 11px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n              <div style="width: 6px; height: 2px; background: ${t.color};  position: absolute; top: 50%; left: -4px; transform: translate(-50%, -50%)"; zIndex:1 ></div>\n            </div>`;e+=`${a} ${t.data.name} : <span style="margin-left:4px;color:${t.color}">${t.value}</span><br/>\n              ${l}\n              50内呼损 : <span style="margin-left:4px;color:${t.color}">${t.data.wait}</span><br/>\n              ${l}\n              IVR呼损 : <span style="margin-left:4px;color:${t.color}">${t.data.ivr} </span><br/>\n              `})),e}},grid:h,series:[{data:[],type:"line",smooth:!0,markLine:{symbol:["none","none"],silent:!0,data:[{yAxis:90}],lineStyle:{color:"#FF0080",type:"solid"},label:{show:!1}}},g]},bottomlineChartCount2:0,bottomlineWarning2:!1,agentSetbusyCount:0,agentWorkCount:0,timer:null}},methods:{getCharts(){Object(n["e"])({serviceId:"MONITOR_CALL_DATA_SERVICE",dateType:"1"}).then(t=>{this.lineChart.series[0].data=t.callIn||[],this.lineChart.series[0].markLine.data[0].yAxis=t.callIn[0].threshold||[],this.lineChartCount=t.callIn[t.callIn.length-1].value||0,this.lineChart.xAxis.data=t.callIn.map(t=>t.time),1==t.callIn[t.callIn.length-1].thresholdType?this.lineWarning=Number(t.callIn[t.callIn.length-1].value)>Number(t.callIn[t.callIn.length-1].threshold):this.lineWarning=Number(t.callIn[t.callIn.length-1].value)<Number(t.callIn[t.callIn.length-1].threshold),this.callIvrCharts.series[0].data=t.callIvr||[],this.callIvrCharts.series[0].markLine.data[0].yAxis=t.callIvr[0].threshold||[],this.callIvrChartCount=t.callIvr[t.callIvr.length-1].value||0,this.callIvrWaring=t.callIvr[t.callIvr.length-1].value>t.callIvr[t.callIvr.length-1].threshold,this.callIvrCharts.xAxis.data=t.callIvr.map(t=>t.time),1==t.callIvr[t.callIvr.length-1].thresholdType?this.callIvrWaring=Number(t.callIvr[t.callIvr.length-1].value)>Number(t.callIvr[t.callIvr.length-1].threshold):this.callIvrWaring=Number(t.callIvr[t.callIvr.length-1].value)<Number(t.callIvr[t.callIvr.length-1].threshold),this.centerlineChart2.series[0].data=t.callConn||[],this.centerlineChart2.series[0].markLine.data[0].yAxis=t.callConn[0].threshold||[],this.centerlineChartCount2=t.callConn[t.callConn.length-1].value||0,this.centerlineChart2.xAxis.data=t.callQueue.map(t=>t.time),1==t.callConn[t.callConn.length-1].thresholdType?this.centerlineWarning2=Number(t.callConn[t.callConn.length-1].value)>Number(t.callConn[t.callConn.length-1].threshold):this.centerlineWarning2=Number(t.callConn[t.callConn.length-1].value)<Number(t.callConn[t.callConn.length-1].threshold),this.centerlineChart3.series[0].data=t.callQueue||[],this.centerlineChart3.series[0].markLine.data[0].yAxis=t.callQueue[0].threshold||[],this.centerlineChartCount3=t.callQueue[t.callQueue.length-1].value||0,this.centerlineChart3.xAxis.data=t.callQueue.map(t=>t.time),1==t.callQueue[t.callQueue.length-1].thresholdType?this.centerlineWarning3=Number(t.callQueue[t.callQueue.length-1].value)>Number(t.callQueue[t.callQueue.length-1].threshold):this.centerlineWarning3=Number(t.callQueue[t.callQueue.length-1].value)<Number(t.callQueue[t.callQueue.length-1].threshold),this.bottomlineChart1.series[0].data=t.callRate||[],this.bottomlineChart1.series[0].markLine.data[0].yAxis=t.callRate[0].threshold||[],this.bottomlineChartCount1=t.callRate[t.callRate.length-1].value||0,this.bottomlineChart1.xAxis.data=t.callRate.map(t=>t.time),1==t.callRate[t.callRate.length-1].thresholdType?this.bottomlineWarning1=Number(t.callRate[t.callRate.length-1].value)>Number(t.callRate[t.callRate.length-1].threshold):this.bottomlineWarning1=Number(t.callRate[t.callRate.length-1].value)<Number(t.callRate[t.callRate.length-1].threshold),this.bottomlineChart2.series[0].data=t.callFail||[],this.bottomlineChart2.series[0].markLine.data[0].yAxis=t.callFail[0].threshold||[],this.bottomlineChartCount2=t.callFail[t.callFail.length-1].value||0,this.bottomlineChart2.xAxis.data=t.callFail.map(t=>t.time),1==t.callFail[t.callFail.length-1].thresholdType?this.bottomlineWarning2=Number(t.callFail[t.callFail.length-1].value)>Number(t.callFail[t.callFail.length-1].threshold):this.bottomlineWarning2=Number(t.callFail[t.callFail.length-1].value)<Number(t.callFail[t.callFail.length-1].threshold),this.agentIdleChart.series[0].data=t.agentIdle||[],this.agentIdleChart.series[0].markLine.data[0].yAxis=t.agentIdle[0].threshold||[],this.agentIdleChartCount=t.agentIdle[t.agentIdle.length-1].value||0,this.agentIdleChart.xAxis.data=t.agentIdle.map(t=>t.time),1==t.agentIdle[t.agentIdle.length-1].thresholdType?this.agentIdleWarning=Number(t.agentIdle[t.agentIdle.length-1].value)>Number(t.agentIdle[t.agentIdle.length-1].threshold):this.agentIdleWarning=Number(t.agentIdle[t.agentIdle.length-1].value)<Number(t.agentIdle[t.agentIdle.length-1].threshold),this.overflowWaitChart.series[0].data=t.overflowWait||[],this.overflowWaitChart.series[0].markLine.data[0].yAxis=t.overflowWait[0].threshold||[],this.overflowWaitChartCount=t.overflowWait[t.overflowWait.length-1].value||0,this.overflowWaitChart.xAxis.data=t.overflowWait.map(t=>t.time),1==t.overflowWait[t.overflowWait.length-1].thresholdType?this.overflowWaitWarning=Number(t.overflowWait[t.overflowWait.length-1].value)>Number(t.overflowWait[t.overflowWait.length-1].threshold):this.overflowWaitWarning=Number(t.overflowWait[t.overflowWait.length-1].value)<Number(t.overflowWait[t.overflowWait.length-1].threshold),this.agentWorkCount=t.agentWork[t.agentWork.length-1].value||0,this.agentSetbusyCount=t.agentSetbusy[t.agentSetbusy.length-1].value||0})},getNumber(){Object(n["i"])({}).then(t=>{console.log(t),this.rightNumber=t.data.ACCEPTANCE_CALL_NETWORK_COUNT,this.leftNumber=t.data.SUM_TOTAL_COUNT,this.number12345=t.data.ACCEPTANCE_12345_COUNT,this.numberZhrx=t.data.ACCEPTANCE_OTHER_COUNT,this.numberWll=t.data.ACCEPTANCE_NETWORK,this.bottomlineChart1.series[1].data=t.data.callRate||[],this.bottomlineChartCount3=t.data.callRate[t.data.callRate.length-1].value||0})},toBack(){this.$emit("back")}},created(){},beforeDestroy(){},mounted(){this.getCharts(),this.getNumber(),this.timer=setInterval(()=>{this.getCharts()},5e3)},beforeDestroy(){clearInterval(this.timer)}},p=v,u=(a("4225"),a("2877")),b=Object(u["a"])(p,l,i,!1,null,"df65d362",null);e["default"]=b.exports}}]);