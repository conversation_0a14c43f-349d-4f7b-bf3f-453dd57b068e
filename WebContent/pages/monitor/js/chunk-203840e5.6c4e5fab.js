(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-203840e5"],{"1c3e":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-page"},[e("el-form",{ref:"queryForm",attrs:{inline:!0,size:"small",model:t.queryParams}},[e("el-form-item",{attrs:{label:"月份"}},[e("el-date-picker",{attrs:{type:"month",placeholder:"请选择月份","value-format":"yyyy-MM",clearable:""},model:{value:t.queryParams.monthId,callback:function(e){t.$set(t.queryParams,"monthId",e)},expression:"queryParams.monthId"}})],1),e("el-form-item",{attrs:{label:"坐席姓名"}},[e("el-input",{attrs:{placeholder:"请输入坐席姓名",clearable:""},model:{value:t.queryParams.agentName,callback:function(e){t.$set(t.queryParams,"agentName",e)},expression:"queryParams.agentName"}})],1),e("el-form-item",{attrs:{label:"坐席工号"}},[e("el-input",{attrs:{placeholder:"请输入坐席工号",clearable:""},model:{value:t.queryParams.agentNo,callback:function(e){t.$set(t.queryParams,"agentNo",e)},expression:"queryParams.agentNo"}})],1),e("el-form-item",{attrs:{label:"班组"}},[e("el-input",{attrs:{placeholder:"请输入班组",clearable:""},model:{value:t.queryParams.workGroup,callback:function(e){t.$set(t.queryParams,"workGroup",e)},expression:"queryParams.workGroup"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v("查询")]),e("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v("重置")]),e("el-divider",{attrs:{direction:"vertical"}}),e("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-upload2"},on:{click:t.handleImport}},[t._v("导入")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"60"}}),e("el-table-column",{attrs:{prop:"MONTH_ID",label:"月份","min-width":"100"}}),e("el-table-column",{attrs:{prop:"AGENT_NAME",label:"坐席姓名","min-width":"100"}}),e("el-table-column",{attrs:{prop:"AGENT_NO",label:"坐席工号","min-width":"100"}}),e("el-table-column",{attrs:{prop:"WORK_GROUP",label:"班组","min-width":"120"}}),e("el-table-column",{attrs:{prop:"BUSI_NUMBER_SCORE",label:"业务量得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"QUALITY_SCORE",label:"质检得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"ATTENDANCE_SCORE",label:"出勤得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"MONTHLY_EXAM_SCORE",label:"月考得分","min-width":"100"}}),e("el-table-column",{attrs:{prop:"AFTER_LONG_SCORE",label:"话后处理得分","min-width":"120"}}),e("el-table-column",{attrs:{prop:"DEDUCTION",label:"扣减","min-width":"80"}}),e("el-table-column",{attrs:{prop:"REWARD",label:"奖励","min-width":"80"}}),e("el-table-column",{attrs:{prop:"MONTHLY_RANKING",label:"月度排名","min-width":"100"}}),e("el-table-column",{attrs:{prop:"ON_HOOK_SATISFACTION",label:"挂机满意度","min-width":"100"}}),e("el-table-column",{attrs:{prop:"ALL_SCORE",label:"总分","min-width":"80"}}),e("el-table-column",{attrs:{prop:"HONOR1",label:"个人荣誉1","min-width":"120"}}),e("el-table-column",{attrs:{prop:"HONOR2",label:"个人荣誉2","min-width":"120"}}),e("el-table-column",{attrs:{prop:"HONOR3",label:"个人荣誉3","min-width":"120"}})],1),e("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":t.pagination.pageIndex,"page-sizes":[15,25,50,100,200],"page-size":t.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total,background:""},on:{"update:currentPage":function(e){return t.$set(t.pagination,"pageIndex",e)},"update:current-page":function(e){return t.$set(t.pagination,"pageIndex",e)},"update:pageSize":function(e){return t.$set(t.pagination,"pageSize",e)},"update:page-size":function(e){return t.$set(t.pagination,"pageSize",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}}),e("el-dialog",{attrs:{title:"导入坐席月度评分",visible:t.importDialogVisible,width:"450px","custom-class":"default-dialog","append-to-body":""},on:{"update:visible":function(e){t.importDialogVisible=e}}},[e("el-form",{ref:"importForm",attrs:{model:t.importForm}},[e("el-form-item",{attrs:{label:"选择月份",prop:"monthId"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"请选择导入月份","value-format":"yyyy-MM",clearable:""},model:{value:t.importForm.monthId,callback:function(e){t.$set(t.importForm,"monthId",e)},expression:"importForm.monthId"}})],1),e("el-form-item",{attrs:{label:"坐席评分"}},[e("el-link",{staticStyle:{float:"right","margin-top":"8px"},attrs:{type:"primary"},on:{click:t.downloadTemplate}},[t._v("下载模板")]),e("el-upload",{ref:"upload",attrs:{action:"","file-list":t.fileList,"auto-upload":!1,accept:".xlsx,.xls",limit:1,"on-change":t.handleFileChange,"on-exceed":t.onExceed}},[e("el-button",{attrs:{size:"small",type:"primary"}},[t._v("点击上传")]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 只能上传xlsx/xls文件，且不超过10MB ")])],1)],1)],1),e("template",{slot:"footer"},[e("el-button",{on:{click:function(e){t.importDialogVisible=!1}}},[t._v("关闭")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleFileUpload}},[t._v("上传")])],1)],2)],1)},r=[],n=a("b775");function o(t){return Object(n["a"])({url:"/cx-monitordata-12345/servlet/agentScore?action=Select",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"/cx-monitordata-12345/servlet/agentScore?action=Import",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}var s=a("c24f"),p={name:"AgentScore",data(){return{queryParams:{monthId:"",agentName:"",agentNo:"",workGroup:""},loading:!1,tableData:[],pagination:{pageIndex:1,pageSize:15,total:0},workGroupOptions:{},importDialogVisible:!1,importForm:{monthId:"",fileName:""},fileList:[]}},created(){this.getList()},methods:{getList(){this.loading=!0;const t={...this.queryParams,pageIndex:this.pagination.pageIndex,pageSize:this.pagination.pageSize,pageType:3};o(t).then(t=>{this.tableData=t.data||[],this.pagination.total=t.totalRow||0,this.loading=!1}).catch(()=>{this.$message.error("查询失败"),this.loading=!1})},handleQuery(){this.pagination.pageIndex=1,this.getList()},resetQuery(){this.$refs.queryForm.resetFields(),this.queryParams={monthId:"",agentName:"",agentNo:"",workGroup:""},this.handleQuery()},handleSizeChange(t){this.pagination.pageSize=t,this.getList()},handleCurrentChange(t){this.pagination.pageIndex=t,this.getList()},handleImport(){this.importDialogVisible=!0,this.fileList=[],this.importForm={monthId:"",fileName:""}},handleFileChange(t,e){this.fileList=e,t&&(this.importForm.fileName=t.name)},onExceed(t,e){this.$message.warning("当前限制选择 1 个文件")},downloadTemplate(){window.open("/cx-monitordata-12345/servlet/agentScore?action=ExportTemplate","_blank")},handleFileUpload(){if(!this.importForm.monthId)return void this.$message.warning("请选择导入月份");if(0===this.fileList.length)return void this.$message.warning("请选择要上传的文件");const t=new FormData;t.append("file",this.fileList[0].raw),t.append("monthId",this.importForm.monthId),this.loading=!0,l(t).then(t=>{1===t.state?(this.$message.success(t.data||"导入成功"),this.importDialogVisible=!1,this.getList()):this.$message.error(t.msg||"导入失败"),this.loading=!1}).catch(()=>{this.$message.error("导入失败"),this.loading=!1})},getWorkGroupDict(){Object(s["d"])().then(t=>{this.workGroupOptions=t.data||{}}).catch(()=>{this.$message.error("获取班组字典失败")})}}},m=p,c=(a("8d62"),a("2877")),d=Object(c["a"])(m,i,r,!1,null,"4b596f5d",null);e["default"]=d.exports},"8d62":function(t,e,a){"use strict";a("bea2")},bea2:function(t,e,a){},c24f:function(t,e,a){"use strict";a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"f",(function(){return l})),a.d(e,"d",(function(){return s})),a.d(e,"e",(function(){return p}));a("a573");var i=a("b775");function r(t){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserList",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(t){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=EditAgent",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=AddAgent",method:"post",data:{data:t},headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"/cx-monitordata-12345/servlet/user?action=UserUpload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}function s(){return Object(i["a"])({url:"/cx-monitordata-12345/webcall?action=common.workGroupPortraitList",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}})}function p(t,e){const a=Object.entries(e).map(([t,e])=>`${t}=${e}`).join("&");return Object(i["a"])({url:"/cc-base/servlet/attachment?action=upload2&"+a,method:"post",data:{file:t},headers:{"Content-Type":"multipart/form-data"}})}}}]);