(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-302900c6"],{3260:function(e,t,a){"use strict";a("da4e")},"6fd5":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",{attrs:{label:"技能队列"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.SKILL_GROUP_ID,callback:function(t){e.$set(e.searchForm,"SKILL_GROUP_ID",t)},expression:"searchForm.SKILL_GROUP_ID"}},e._l(e.skillGroupOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"SELECT_TIME",label:"时间","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"SKILLGROUPNAME",label:"队列名称","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"LOGIN_COUNT",label:"签入人数","min-width":"120",align:"center"}})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._m(0)],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"stat-desc"},[t("h4",[e._v("统计口径")]),t("p",[e._v("1、签入人数：队列的签入人数")])])}],i=(a("a573"),a("c2d0")),s=a("0c9f"),n={name:"SkillLoginCount",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(i["e"])(),END_TIME:Object(i["d"])(),SKILL_GROUP_ID:""},searchedForm:{},skillGroupOptions:[]}},created(){this.getSkillGroupOptions(),this.loadData()},methods:{getSkillGroupOptions(){Object(s["gb"])({},["common.skillGroupList"]).then(e=>{if(e["common.skillGroupList"]){const t=e["common.skillGroupList"].data||{};this.skillGroupOptions=Object.entries(t).map(([e,t])=>({value:e,label:t.split("|")[1]||t}))}}).catch(()=>{this.$message.error("获取技能队列失败")})},loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,SKILL_GROUP_ID:this.searchForm.SKILL_GROUP_ID||""};this.searchedForm=e,Object(s["kb"])(e).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出队列在线人数统计报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{window.location.href=Object(s["C"])(this.searchForm)}).catch(()=>{})}}},o=n,c=(a("3260"),a("2877")),h=Object(c["a"])(o,l,r,!1,null,null,null);t["default"]=h.exports},da4e:function(e,t,a){}}]);