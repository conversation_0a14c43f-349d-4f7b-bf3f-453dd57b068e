(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3276eeed"],{"9d64":function(t,o,e){t.exports=e.p+"img/logo.386d01b7.png"},a8d7:function(t,o,e){"use strict";e("c83b")},c83b:function(t,o,e){},e37e:function(t,o,e){"use strict";e.r(o);var s=function(){var t=this,o=t._self._c;return o("div",{staticClass:"introduce"},[o("div",{staticClass:"video",attrs:{id:"video"}}),o("img",{staticClass:"logo",attrs:{src:e("9d64"),alt:""}}),o("div",{staticClass:"home-btn",on:{click:t.toIndex}},[o("svg-icon",{attrs:{icon:"home",width:"24",height:"24"}}),t._v(" 主页 ")],1)])},i=[],n=(e("14d9"),e("5392")),c=e.n(n),a=e("61da"),d={name:"info",data(){return{}},methods:{toIndex(){this.$router.push({path:"/"})}},created(){this.$socket.initWs()},mounted(){this.$nextTick(()=>{let{type:t=1}=this.$route.query,o=new c.a({id:"video",url:"/cx-monitor-12345/servlet/playVideo",autoplay:!0,width:"100%",height:"100%"});o.once("complete",()=>{console.log("complete"),o.play()})}),a["a"].$on("control-fczs",t=>{const o=this.$route.path;console.log(this.$route),"/"!==o&&this.toIndex()})},beforeDestroy(){a["a"].$off("control-fczs")}},r=d,l=(e("a8d7"),e("2877")),u=Object(l["a"])(r,s,i,!1,null,"c01cbb64",null);o["default"]=u.exports}}]);