(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b2ce64a4"],{"7e25":function(e,t,a){"use strict";a("bb55")},bb55:function(e,t,a){},d80f:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-page"},[t("el-form",{ref:"searchForm",attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"坐席工号"}},[t("el-select",{attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.searchForm.AGENTID,callback:function(t){e.$set(e.searchForm,"AGENTID",t)},expression:"searchForm.AGENTID"}},e._l(e.agentOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"开始时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择开始时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.BEGIN_TIME,callback:function(t){e.$set(e.searchForm,"BEGIN_TIME",t)},expression:"searchForm.BEGIN_TIME"}})],1),t("el-form-item",{attrs:{label:"-"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择结束时间","value-format":"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.searchForm.END_TIME,callback:function(t){e.$set(e.searchForm,"END_TIME",t)},expression:"searchForm.END_TIME"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadData}},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索 ")]),t("el-divider",{attrs:{direction:"vertical"}}),t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.exportDetail}},[t("i",{staticClass:"el-icon-download"}),e._v(" 导出 ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"}},[t("el-table-column",{attrs:{type:"index",label:"序号",width:"60",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENTID",label:"坐席工号","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"AGENT_NAME",label:"坐席姓名","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"BEGIN_TIME",label:"开始时间","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"END_TIME",label:"结束时间","min-width":"180",align:"center"}}),t("el-table-column",{attrs:{prop:"DIFF_TIME",label:"离席时长","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatClock(t.row.DIFF_TIME))+" ")]}}])})],1),t("el-pagination",{staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{"current-page":e.currentPage,"page-sizes":[15,25,50,100,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total,background:""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)},l=[],n=(a("0643"),a("4e3e"),a("a573"),a("88a7"),a("271a"),a("5494"),a("c2d0")),i=a("0c9f"),o=a("b775"),s={name:"AgentBusySituation",data(){return{loading:!1,tableData:[],total:0,currentPage:1,pageSize:15,searchForm:{BEGIN_TIME:Object(n["g"])(),END_TIME:Object(n["f"])(),AGENTID:""},searchedForm:{},agentOptions:[]}},created(){this.getAgentOptions(),this.loadData()},methods:{getAgentOptions(){Object(i["gb"])({},["common.agentList"]).then(e=>{if(e["common.agentList"]){const t=e["common.agentList"].data||{};this.agentOptions=Object.entries(t).map(([e,t])=>({value:e,label:t.split("|")[1]||t}))}}).catch(()=>{this.$message.error("获取坐席列表失败")})},loadData(){this.loading=!0;const e={pageIndex:this.currentPage,pageSize:this.pageSize,pageType:3,BEGIN_TIME:this.searchForm.BEGIN_TIME,END_TIME:this.searchForm.END_TIME,AGENTID:this.searchForm.AGENTID||""};this.searchedForm=e,Object(o["a"])({url:"/cx-report-12345/webcall?action=statDao.agentBusySituationStat",method:"post",data:{data:JSON.stringify(e)},headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(e=>{this.loading=!1,1===e.state?(this.tableData=e.data||[],this.total=e.totalRow||0):this.$message.error(e.msg||"查询失败")}).catch(()=>{this.loading=!1,this.$message.error("查询失败")})},handleSizeChange(e){this.pageSize=e,this.loadData()},handleCurrentChange(e){this.currentPage=e,this.loadData()},exportDetail(){this.$confirm("是否导出话务员离席情况报表","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=new URLSearchParams;Object.keys(this.searchForm).forEach(t=>{e.append(t,this.searchForm[t]||"")}),window.location.href="/cx-report-12345/servlet/export?action=exportAgentBusySituation&"+e.toString()}).catch(()=>{})},formatClock:n["a"]}},c=s,h=(a("7e25"),a("2877")),p=Object(h["a"])(c,r,l,!1,null,null,null);t["default"]=p.exports}}]);