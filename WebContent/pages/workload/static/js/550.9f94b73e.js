"use strict";(self["webpackChunkoverall_monitordata_vue"]=self["webpackChunkoverall_monitordata_vue"]||[]).push([[550],{7507:function(e,l,a){a.r(l),a.d(l,{default:function(){return la}});a(1484),a(6961),a(7354),a(9370),a(2807),a(4929);var t=a(9304),s=a(2681),n=a(4104),i=a(896),o=a(5034);var r=a(6762);var c=a(6779);const u={class:"iconPlay"},d={class:"iconPause"};var v={__name:"AudioPlayer",props:{audioSrc:{type:String,default:""}},setup(e){const l=e,a=(0,n.KR)(null),s=(0,n.KR)(null),i=(0,n.KR)(null),o=(0,n.KR)(null),r=(0,n.KR)(null),v=(0,n.KR)(null),L=(0,n.KR)(null),k=(0,n.KR)(!1),m=(0,n.KR)(0),f=(0,n.KR)(0),T=(0,n.KR)([]),p=(0,n.KR)(72),_=async()=>{try{r.value=new(window.AudioContext||window.webkitAudioContext),v.value=r.value.createAnalyser(),v.value.fftSize=2048,(0,t.dY)((()=>{w()}))}catch(e){console.error("初始化音频上下文失败:",e)}},h=async()=>{try{if(!l.audioSrc)return void console.warn("音频源未提供");r.value||await _(),console.log("开始加载音频:",l.audioSrc);const e=await fetch(l.audioSrc);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const a=await e.arrayBuffer();console.log("音频数据已获取，开始解码"),"suspended"===r.value.state&&await r.value.resume(),L.value=await r.value.decodeAudioData(a),f.value=L.value.duration,console.log("音频解码完成，时长:",f.value),(0,t.dY)((()=>{C(),w()}))}catch(e){console.error("加载音频失败:",e)}},C=()=>{if(L.value)try{console.log("开始生成波形数据");const e=L.value.getChannelData(0),l=Math.floor(i.value.width/5),a=Math.floor(e.length/l),s=[];for(let t=0;t<l;t++){const l=t*a,n=Math.min(l+a,e.length);let i=1,o=-1;for(let a=l;a<n;a++){const l=e[a];i=Math.min(i,l),o=Math.max(o,l)}s.push({min:i,max:o})}T.value=s,console.log("波形数据生成完成，数据点数:",T.value.length),(0,t.dY)((()=>{g()}))}catch(e){console.error("生成波形数据失败:",e)}else console.warn("没有音频数据可供生成波形")},g=()=>{if(!i.value||!T.value.length)return void console.warn("无法绘制波形：canvas未就绪或没有波形数据");console.log("开始绘制波形");const e=i.value.getContext("2d");if(!e)return void console.error("无法获取canvas上下文");e.clearRect(0,0,i.value.width,i.value.height);const l={left:10,right:10},a=i.value.width-l.left-l.right,t=i.value.height,s=t/2,n=Math.max(l.left,Math.min(i.value.width-l.right,l.left+m.value/f.value*a)),o=3,r=2,c=o+r,u=Math.floor(.6*t),d=o/2,v=Math.max(...T.value.map((e=>Math.abs(e.max-e.min))));T.value.forEach(((a,t)=>{const i=l.left+t*c,r=Math.abs(a.max-a.min),L=r/v*u,k=Math.max(4,L),m=i<=n;e.fillStyle=m?"#00feff":"#003790";const f=s-k/2;e.beginPath(),e.moveTo(i+d,f),e.lineTo(i+o-d,f),e.arc(i+o-d,f+d,d,-Math.PI/2,0),e.lineTo(i+o,f+k-d),e.arc(i+o-d,f+k-d,d,0,Math.PI/2),e.lineTo(i+d,f+k),e.arc(i+d,f+k-d,d,Math.PI/2,Math.PI),e.lineTo(i,f+d),e.arc(i+d,f+d,d,Math.PI,-Math.PI/2),e.closePath(),e.fill()}));const L=e.createLinearGradient(0,0,0,t);L.addColorStop(0,"#ff89b7"),L.addColorStop(.5,"#ee4586"),L.addColorStop(1,"#dd0559"),e.strokeStyle=L,e.lineWidth=2;const k=t-8;e.beginPath(),e.moveTo(n,0),e.lineTo(n,k-8),e.stroke(),e.save(),e.beginPath(),e.arc(n,k,8,0,2*Math.PI),e.clip();const p=e.createLinearGradient(n-8,k-8,n+8,k+8);p.addColorStop(0,"#d70156"),p.addColorStop(.5,"#aa105e"),p.addColorStop(1,"#c60358"),e.fillStyle=p,e.fill(),e.restore(),e.beginPath(),e.arc(n,k,3,0,2*Math.PI),e.fillStyle="#ffffff",e.fill(),console.log("波形绘制完成")},I=()=>{k.value?y():M()},M=e=>{k.value=!0,o.value.src=l.audioSrc,o.value.currentTime="undefined"!==typeof e?e:m.value,o.value.play().catch((e=>console.error("播放失败:",e)))},y=()=>{o.value.pause(),k.value=!1},E=()=>{m.value=o.value.currentTime,g()},R=()=>{k.value=!1,m.value=0,g()},A=e=>{const l=i.value.getBoundingClientRect(),a=e.clientX-l.left,t=a/i.value.width;m.value=t*f.value,k.value&&(y(),M()),g()},w=()=>{if(i.value&&a.value&&s.value)try{const e=a.value.clientWidth-s.value.clientWidth-16;i.value.width=Math.max(e,0),i.value.height=p.value,T.value&&T.value.length>0&&g()}catch(e){console.error("调整 canvas 尺寸失败:",e)}else console.warn("handleResize: DOM 元素未就绪")};return(0,t.wB)((()=>l.audioSrc),(e=>{e&&(console.log("音频源发生变化，重新加载"),h())})),(0,t.sV)((async()=>{console.log("组件挂载，初始化音频"),await _(),(0,t.dY)((async()=>{l.audioSrc&&await h(),window.addEventListener("resize",w)}))})),(0,t.xo)((()=>{r.value&&r.value.close(),window.removeEventListener("resize",w)})),(e,l)=>((0,t.uX)(),(0,t.CE)("div",{class:"audioContent",ref_key:"audioContent",ref:a},[(0,t.Lk)("div",{class:"btnContent",ref_key:"btnContent",ref:s},[(0,t.Lk)("div",{class:"playBtn",onClick:I},[(0,t.bo)((0,t.Lk)("div",u,null,512),[[c.aG,!k.value]]),(0,t.bo)((0,t.Lk)("div",d,null,512),[[c.aG,k.value]])])],512),(0,t.Lk)("canvas",{class:"canvasObj",ref_key:"canvas",ref:i,onClick:A},null,512),(0,t.Lk)("audio",{ref_key:"audio",ref:o,onTimeupdate:E,onEnded:R},null,544)],512))}};const L=(0,r.A)(v,[["__scopeId","data-v-7dc40402"]]);var k=L,m=a(4199),f=a.p+"static/img/icon_arrow_down.22fcd17f.svg",T=a(4225),p=a(1318),_=a(8074),h=a(482),C=a(9271),g=a(4739),I=a.n(g);const M=e=>((0,t.Qi)("data-v-279fb171"),e=e(),(0,t.jt)(),e),y={class:"containerLeft"},E={class:"staff-info"},R={class:"info"},A={class:"name fontStyle"},w={class:"id"},S={key:0,class:"realTimeContent"},H={class:"callList"},D=["onClick"],b={class:"liContent"},N=M((()=>(0,t.Lk)("i",{class:"iconCall"},null,-1))),K={class:"tel"},P=M((()=>(0,t.Lk)("div",{class:"label"},[(0,t.Lk)("span",{class:"txt"},"通话中")],-1))),Y={class:"callTime"},O=M((()=>(0,t.Lk)("div",{class:"callType"},[(0,t.Lk)("i",{class:"iconArrow"}),(0,t.eW)("呼入")],-1))),x={class:"historyContent"},X={class:"historyHead"},G=M((()=>(0,t.Lk)("i",{class:"iconTitle"},null,-1))),F=M((()=>(0,t.Lk)("span",{class:"title"},"历史通话",-1))),U={class:"filterContent"},W=M((()=>(0,t.Lk)("div",{class:"iconFilter"},null,-1))),V=[W],B={class:"filterPopoverContent"},$={class:"filterPopoverItem"},z=M((()=>(0,t.Lk)("i",{class:"iconReset"},null,-1))),Q=M((()=>(0,t.Lk)("span",{class:"txt"},"重置",-1))),q=M((()=>(0,t.Lk)("i",{class:"iconSearch"},null,-1))),j=M((()=>(0,t.Lk)("span",{class:"txt"},"搜索",-1))),J={class:"filterPopoverItem"},Z=M((()=>(0,t.Lk)("div",{class:"attr"},"开始时间",-1))),ee={class:"filterPopoverItem"},le=M((()=>(0,t.Lk)("div",{class:"attr"},"结束时间",-1))),ae=["onClick"],te={class:"historyCallTime"},se={class:"liContent"},ne=M((()=>(0,t.Lk)("i",{class:"iconCall"},null,-1))),ie={class:"tel"},oe={class:"callTime"},re=M((()=>(0,t.Lk)("div",{class:"callType"},[(0,t.Lk)("i",{class:"iconArrow"}),(0,t.eW)("呼入")],-1))),ce={key:0,class:"loading-item"},ue=M((()=>(0,t.Lk)("div",{class:"loading-content"},[(0,t.Lk)("i",{class:"loading-icon"}),(0,t.Lk)("span",null,"加载中...")],-1))),de=[ue],ve={key:1,class:"no-more-item"},Le=M((()=>(0,t.Lk)("div",{class:"no-more-content"},[(0,t.Lk)("span",null,"没有更多数据了")],-1))),ke=[Le],me={class:"containerRight"},fe={class:"statistics"},Te={class:"statisticsList"},pe={class:"infoItem infoItem1"},_e=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),he={class:"info"},Ce={class:"attr"},ge={class:"num"},Ie={class:"infoItem infoItem2"},Me=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),ye={class:"info"},Ee=M((()=>(0,t.Lk)("div",{class:"attr"},"平均通话时长",-1))),Re={class:"num"},Ae={class:"infoItem infoItem3"},we=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),Se={class:"info"},He=M((()=>(0,t.Lk)("div",{class:"attr"},"平均话后时长",-1))),De={class:"num"},be={class:"infoItem infoItem4"},Ne=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),Ke={class:"info"},Pe=M((()=>(0,t.Lk)("div",{class:"attr"},"签入总时长",-1))),Ye={class:"num"},Oe={class:"infoItem infoItem5"},xe=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),Xe={class:"info"},Ge={class:"attr"},Fe={class:"num"},Ue={class:"infoItem infoItem6"},We=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),Ve={class:"info"},Be=M((()=>(0,t.Lk)("div",{class:"attr"},"满意率",-1))),$e={class:"num"},ze={class:"fullScreen"},Qe=M((()=>(0,t.Lk)("div",{class:"fullScreenBtn"},[(0,t.Lk)("div",{class:"iconFullScreen"}),(0,t.Lk)("div",{class:"txt"},"返回")],-1))),qe=[Qe],je={class:"mainContent"},Je={class:"panel callInfo"},Ze={class:"panelHead"},el={class:"title"},ll={key:0,class:"tel"},al=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),tl=M((()=>(0,t.Lk)("div",{class:"txt"},"导出",-1))),sl={class:"panelBody"},nl={class:"timeInfo"},il={class:"timeItem"},ol=M((()=>(0,t.Lk)("div",{class:"attr"},"开始时间：",-1))),rl={class:"time"},cl={class:"timeItem"},ul=M((()=>(0,t.Lk)("div",{class:"attr"},"结束时间：",-1))),dl={class:"time"},vl={class:"timeItem"},Ll=M((()=>(0,t.Lk)("div",{class:"attr"},"通话时长：",-1))),kl={class:"time"},ml={class:"chatList"},fl=M((()=>(0,t.Lk)("div",{class:"icon"},null,-1))),Tl={class:"time"},pl={class:"msgLine"},_l={class:"msgContent"},hl={class:"txt"},Cl={class:"audio-container"},gl={class:"panel orderInfo"},Il=M((()=>(0,t.Lk)("div",{class:"panelHead"},[(0,t.Lk)("div",{class:"title"},"工单登记")],-1))),Ml={class:"panelBody"},yl=M((()=>(0,t.Lk)("div",{class:"orderTitle"},"来电人信息",-1))),El={class:"customTable"},Rl=M((()=>(0,t.Lk)("td",{class:"td1"},"姓名",-1))),Al=["innerHTML"],wl=M((()=>(0,t.Lk)("td",{class:"td1"},"性别",-1))),Sl=["innerHTML"],Hl=M((()=>(0,t.Lk)("td",{class:"td1"},"来电号码",-1))),Dl=["innerHTML"],bl=M((()=>(0,t.Lk)("td",{class:"td1"},"来电人要求信息保密",-1))),Nl=["innerHTML"],Kl=M((()=>(0,t.Lk)("div",{class:"orderTitle"},"反映问题",-1))),Pl={class:"customTable"},Yl=M((()=>(0,t.Lk)("td",{class:"td1"},"工单类型",-1))),Ol=["innerHTML"],xl=M((()=>(0,t.Lk)("td",{class:"td1"},"被反映者",-1))),Xl=["innerHTML"],Gl=M((()=>(0,t.Lk)("td",{class:"td1"},"标题",-1))),Fl=["innerHTML"],Ul=M((()=>(0,t.Lk)("td",{class:"td1"},"主要内容",-1))),Wl=["innerHTML"],Vl=M((()=>(0,t.Lk)("td",{class:"td1"},"所在区县",-1))),Bl=["innerHTML"],$l=M((()=>(0,t.Lk)("td",{class:"td1"},"街道",-1))),zl=["innerHTML"],Ql=M((()=>(0,t.Lk)("td",{class:"td1"},"详细地址",-1))),ql=["innerHTML"],jl=M((()=>(0,t.Lk)("td",{class:"td1"},"承办单位",-1))),Jl=["innerHTML"];var Zl={__name:"index",setup(e){const l=(0,m.rd)(),a=(0,m.lq)(),r=(0,n.KR)(a.params.id),c=(0,n.KR)({name:a.query.name,avatar:decodeURI(a.query.avatar||""),type:decodeURI(a.query.type)||"接线"}),{isIframe:u}=(0,o.u)(),d=(0,n.KR)([]),v=(0,n.KR)(""),L=(0,n.KR)(""),g=(0,n.KR)(""),M=(0,n.KR)(!1),W=(0,n.Kh)({keyWord:"",startTime:I()().startOf("day").format("YYYY-MM-DD HH:mm:ss"),endTime:I()().format("YYYY-MM-DD HH:mm:ss")}),ue=(0,n.KR)([]),Le=(0,n.Kh)({pageNo:1,pageSize:20,hasMore:!0,loading:!1,total:0}),Qe=(0,n.Kh)({AGENTID:"",CALL_IN_COUNT_ALL:"0",AVG_CALL_IN_TIME:"0",AVG_ARRANGE_TIME:"0",LOGIN_TIME:"0",CALL_IN_TIME_ALL:"0",GOOD_PERCENT:"0"}),Zl=(0,n.Kh)({startTime:"",endTime:"",chatTime:""}),ea=(0,n.KR)([]),la=(0,n.KR)(""),aa=((0,n.KR)([]),(0,n.KR)({})),ta=(0,n.KR)(null),sa=(0,n.KR)([]),na=(0,n.KR)([]),ia=async()=>{try{const e=await(0,_.Zc)();if("000"===e.result&&e.data){sa.value=e.data;const l=[];e.data.forEach((e=>{e.userList&&e.userList.length&&e.userList.forEach((a=>{l.push({...a,groupName:e.workGroupName})}))})),na.value=l}else C.nk.error(e.desc||"获取班组数据失败")}catch(e){console.error("获取班组数据失败",e)}},oa=e=>{e.AGENTID!==r.value&&l.push({path:"/call-monitor/"+e.AGENTID,query:{name:e.NAME,avatar:e.IMG_URL}})},ra=async(e=!1)=>{try{const l=await(0,_.m9)(r.value);"000"===l.result&&l.data?Object.assign(Qe,l.data):C.nk.error(l.desc||"获取坐席统计信息失败"),await ca(),e&&await da()}catch(l){console.error("获取坐席统计信息失败",l)}},ca=async()=>{try{const e=await(0,_.bc)(r.value);"000"===e.result?e.data&&e.data.CALL_ID?(d.value=[e.data],va()):d.value=[]:C.nk.error(e.desc||"获取通话中数据失败")}catch(e){console.error("获取通话中数据失败",e)}},ua=async e=>{try{const l=await(0,_.bW)(e?.ORDER_ID);"000"===l.result?aa.value=l.data||{}:(aa.value={},C.nk.error(l.desc||"获取工单详情失败"))}catch(l){console.error("获取工单详情失败",l)}},da=async(e=!1)=>{if(fa()&&(!e||Le.hasMore&&!Le.loading))try{Le.loading=!0,e||(Le.pageNo=1,Le.hasMore=!0,ue.value=[]);const l=await(0,_.$R)(W.startTime,W.endTime,r.value,W.keyWord,Le.pageNo,Le.pageSize);if("000"===l.result&&l.data){const a=l.data||[];e?ue.value=[...ue.value,...a]:(ue.value=a,va()),Le.total=l.totalRow||0,Le.hasMore=!Le.total||ue.value.length>=Le.total,Le.pageNo+=1}else C.nk.error(l.desc||"获取历史通话失败")}catch(l){console.error("获取历史通话失败",l)}finally{Le.loading=!1}},va=e=>{console.log("sessionChange called with item:",e);let l=v.value,a=e;if(a||(v.value&&(a=d.value.find((e=>e.CALL_ID===v.value)),a||(a=ue.value.find((e=>e.CALL_ID===v.value)))),a||(d.value.length>0?a=d.value[0]:ue.value.length>0&&(a=ue.value[0]))),!a)return void La();const t=d.value.some((e=>e.CALL_ID===a.CALL_ID));if(g.value=t?"0":"1",v.value=a.CALL_ID,L.value=a.CUST_PHONE,l!==a.CALL_ID||t){ua(a);try{if(l!==a.CALL_ID&&(Zl.startTime="",Zl.endTime="",Zl.chatTime="",ea.value=[],la.value=""),a.CALL_CONTENT){let e=[];try{e=JSON.parse(a.CALL_CONTENT)}catch(s){console.error("解析通话内容失败",s)}if(Zl.startTime=a.START_TIME,Zl.endTime=a.END_TIME,Zl.chatTime=a.CALL_TIME||"",ea.value=e.map((e=>({role:e.clientId,timestamp:Ta(e.timestamp),content:e.txt}))),a.RECORD_PATH){let e=new URL("/aiamgr/record/play.do",window.location.origin);const l={recordePath:a.RECORD_PATH,sessionId:a.CALL_ID};e.search=new URLSearchParams(l).toString(),la.value=e.toString()}else la.value=null;ga()}}catch(n){console.error("处理会话详情失败",n)}}},La=()=>{v.value="",L.value="",Zl.startTime="",Zl.endTime="",Zl.chatTime="",ea.value=[],la.value=""},ka=()=>{W.keyWord="",W.startTime=null,W.endTime=null},ma=()=>{console.log("handleClickOutside"),M.value=!1},fa=()=>{if(null!==W.startTime&&null===W.endTime)return C.nk.warning("请选择结束时间"),!1;if(null===W.startTime&&null!==W.endTime)return C.nk.warning("请选择开始时间"),!1;if(null!==W.startTime&&null!==W.endTime){const e=new Date(W.startTime).getTime(),l=new Date(W.endTime).getTime();if(e>l)return C.nk.warning("请选择正确的时间范围"),!1}return!0},Ta=e=>{if(!e)return"";if("string"!==typeof e||e.includes("-")||(e=Number(e)),"string"===typeof e&&e.includes("-"))return e;const l=new Date(e),a=l.getFullYear(),t=String(l.getMonth()+1).padStart(2,"0"),s=String(l.getDate()).padStart(2,"0"),n=String(l.getHours()).padStart(2,"0"),i=String(l.getMinutes()).padStart(2,"0"),o=String(l.getSeconds()).padStart(2,"0");return`${a}-${t}-${s} ${n}:${i}:${o}`},pa=(0,h.sg)((()=>{(0,C.nk)({message:"导出成功",type:"success"}),window.open("/cx-monitordata-12345/servlet/export?action=WorkMonitorRecord&callId="+v.value,"_blank")}),1e3,!0),_a=e=>{if(!e)return"";let l=e.replace(/\n/g,"<br>");return l},ha=(0,n.KR)(null),Ca=(0,n.KR)(null),ga=()=>{(0,t.dY)((()=>{let e=ha.value;e&&e.scrollTo({top:e.scrollHeight})}))},Ia=(0,h.sg)((()=>{const e=Ca.value;if(!e)return;const{scrollTop:l,scrollHeight:a,clientHeight:t}=e;l+t>=a-50&&da(!0)}),200);let Ma=null;const ya=()=>{Ma&&clearInterval(Ma),Ma=setInterval((()=>{ra(!1)}),1e4)},Ea=()=>{Ma&&(clearInterval(Ma),Ma=null)};return(0,t.sV)((()=>{ra(!0),ia(),ya()})),(0,t.n)((()=>{ra(!1),ya()})),(0,t.Y4)((()=>{Ea()})),(0,t.hi)((()=>{Ea()})),(e,a)=>{const o=(0,t.g2)("el-avatar"),m=(0,t.g2)("el-image"),_=(0,t.g2)("el-dropdown-item"),C=(0,t.g2)("el-dropdown-menu"),I=(0,t.g2)("el-dropdown"),na=(0,t.g2)("el-input"),ia=(0,t.g2)("el-button"),ra=(0,t.g2)("el-date-picker"),ca=(0,t.g2)("el-popover");return(0,t.uX)(),(0,t.CE)("div",{class:(0,s.C4)(["container",{"iframe-mode":(0,n.R1)(u)}])},[(0,t.Lk)("div",y,[(0,t.Lk)("div",E,[(0,t.bF)(o,{size:56,src:c.value.avatar||(0,n.R1)(T),onError:a[0]||(a[0]=e=>c.value.avatar=(0,n.R1)(T)),style:{flex:"0 0 56px",border:"1px solid #00ffff"}},null,8,["src"]),(0,t.Lk)("div",R,[(0,t.bF)(I,{trigger:"click","popper-class":"agent-dropdown"},{dropdown:(0,t.k6)((()=>[(0,t.bF)(C,null,{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(sa.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"dropdown-group-title",key:e.workGroupId},[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.userList,(e=>((0,t.uX)(),(0,t.Wv)(_,{key:e.AGENTID,onClick:l=>oa(e)},{default:(0,t.k6)((()=>[(0,t.eW)((0,s.v_)(e.NAME)+" "+(0,s.v_)(e.AGENTID),1)])),_:2},1032,["onClick"])))),128))])))),128))])),_:1})])),default:(0,t.k6)((()=>[(0,t.Lk)("div",A,[(0,t.eW)((0,s.v_)(c.value.name)+" ",1),(0,t.bF)(m,{src:(0,n.R1)(f),class:"arrow-down"},null,8,["src"])])])),_:1}),(0,t.Lk)("div",w,"工号："+(0,s.v_)(r.value),1)])]),d.value.length>0?((0,t.uX)(),(0,t.CE)("div",S,[(0,t.Lk)("ul",H,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(d.value,(e=>((0,t.uX)(),(0,t.CE)("li",{key:e.CALL_ID,class:(0,s.C4)({active:e.CALL_ID===v.value}),onClick:l=>va(e)},[(0,t.Lk)("div",b,[N,(0,t.Lk)("div",K,[(0,t.eW)((0,s.v_)(e.CUST_PHONE)+" ",1),P]),(0,t.Lk)("div",Y,"来电时间："+(0,s.v_)(e.START_TIME),1),O])],10,D)))),128))])])):(0,t.Q3)("",!0),(0,t.Lk)("div",x,[(0,t.Lk)("div",X,[G,F,(0,t.Lk)("div",U,[(0,t.bF)(ca,{placement:"top-end",trigger:"manual","popper-class":"el-popover-custom",visible:M.value},{reference:(0,t.k6)((()=>[(0,t.Lk)("div",{class:"filterBtn",onClick:a[1]||(a[1]=e=>M.value=!M.value)},V)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",B,[(0,t.Lk)("div",$,[(0,t.bF)(na,{class:"visitId",modelValue:W.keyWord,"onUpdate:modelValue":a[2]||(a[2]=e=>W.keyWord=e),placeholder:"请输入"},null,8,["modelValue"]),(0,t.bF)(ia,{class:"btnReset",onClick:ka},{default:(0,t.k6)((()=>[z,Q])),_:1}),(0,t.bF)(ia,{class:"btnSearch",onClick:a[3]||(a[3]=e=>da(!1))},{default:(0,t.k6)((()=>[q,j])),_:1})]),(0,t.Lk)("div",J,[Z,(0,t.bF)(ra,{modelValue:W.startTime,"onUpdate:modelValue":a[4]||(a[4]=e=>W.startTime=e),type:"datetime",placeholder:"请输入",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","default-time":new Date(2e3,1,1,0,0,0),"prefix-icon":(0,n.R1)(p["default"]),"popper-class":"el-picker-panel-custom"},null,8,["modelValue","default-time","prefix-icon"])]),(0,t.Lk)("div",ee,[le,(0,t.bF)(ra,{modelValue:W.endTime,"onUpdate:modelValue":a[5]||(a[5]=e=>W.endTime=e),type:"datetime",placeholder:"请输入",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","default-time":new Date(2e3,1,1,23,59,59),"prefix-icon":(0,n.R1)(p["default"]),"popper-class":"el-picker-panel-custom"},null,8,["modelValue","default-time","prefix-icon"])])])),[[(0,n.R1)(i.A),ma]])])),_:1},8,["visible"])])]),(0,t.Lk)("ul",{class:"callList",ref_key:"historyListContainer",ref:Ca,onScroll:a[6]||(a[6]=(...e)=>(0,n.R1)(Ia)&&(0,n.R1)(Ia)(...e))},[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(ue.value,(e=>((0,t.uX)(),(0,t.CE)("li",{key:e.CALL_ID,class:(0,s.C4)({active:e.CALL_ID===v.value}),onClick:l=>va(e)},[(0,t.Lk)("div",te,(0,s.v_)(e.START_TIME),1),(0,t.Lk)("div",se,[ne,(0,t.Lk)("div",ie,(0,s.v_)(e.CUST_PHONE),1),(0,t.Lk)("div",oe,"持续时间："+(0,s.v_)((0,n.R1)(h.Xo)(e.CALL_TIME)),1),re])],10,ae)))),128)),Le.loading?((0,t.uX)(),(0,t.CE)("li",ce,de)):ue.value.length>0&&!Le.hasMore?((0,t.uX)(),(0,t.CE)("li",ve,ke)):(0,t.Q3)("",!0)],544)])]),(0,t.Lk)("div",me,[(0,t.Lk)("div",fe,[(0,t.Lk)("div",Te,[(0,t.Lk)("div",pe,[_e,(0,t.Lk)("div",he,[(0,t.Lk)("div",Ce,(0,s.v_)("回访"===c.value.type?"呼入量":"接听量"),1),(0,t.Lk)("div",ge,(0,s.v_)(("回访"===c.value.type?Qe.CALL_OUT_SUCC_COUNT:Qe.CALL_IN_COUNT_ALL)||0),1)])]),(0,t.Lk)("div",Ie,[Me,(0,t.Lk)("div",ye,[Ee,(0,t.Lk)("div",Re,(0,s.v_)((0,n.R1)(h.Xo)("回访"===c.value.type?Qe.AVG_CALL_OUT_TIME:Qe.AVG_CALL_IN_TIME)),1)])]),(0,t.Lk)("div",Ae,[we,(0,t.Lk)("div",Se,[He,(0,t.Lk)("div",De,(0,s.v_)((0,n.R1)(h.Xo)(Qe.AVG_ARRANGE_TIME)),1)])]),(0,t.Lk)("div",be,[Ne,(0,t.Lk)("div",Ke,[Pe,(0,t.Lk)("div",Ye,(0,s.v_)((0,n.R1)(h.Xo)(Qe.LOGIN_TIME)),1)])]),(0,t.Lk)("div",Oe,[xe,(0,t.Lk)("div",Xe,[(0,t.Lk)("div",Ge,(0,s.v_)("回访"===c.value.type?"呼入总时长":"接听总时长"),1),(0,t.Lk)("div",Fe,(0,s.v_)((0,n.R1)(h.Xo)("回访"===c.value.type?Qe.CALL_OUT_TIME_ALL:Qe.CALL_IN_TIME_ALL)),1)])]),(0,t.Lk)("div",Ue,[We,(0,t.Lk)("div",Ve,[Be,(0,t.Lk)("div",$e,(0,s.v_)((Qe.GOOD_PERCENT||0)+"%"),1)])])]),(0,t.Lk)("div",ze,[(0,t.Lk)("div",{class:"fullscreen-container",onClick:a[7]||(a[7]=e=>(0,n.R1)(l).push("/"))},qe)])]),(0,t.Lk)("div",je,[(0,t.Lk)("div",Je,[(0,t.Lk)("div",Ze,[(0,t.Lk)("div",el,[(0,t.eW)(" 对话详情"),L.value?((0,t.uX)(),(0,t.CE)("span",ll,"（"+(0,s.v_)(L.value)+"）",1)):(0,t.Q3)("",!0)]),"1"===g.value?((0,t.uX)(),(0,t.Wv)(ia,{key:0,class:"btnExport",onClick:(0,n.R1)(pa)},{default:(0,t.k6)((()=>[al,tl])),_:1},8,["onClick"])):(0,t.Q3)("",!0)]),(0,t.Lk)("div",sl,[(0,t.Lk)("div",nl,[(0,t.Lk)("div",il,[ol,(0,t.Lk)("div",rl,(0,s.v_)(Zl.startTime.length>0?Zl.startTime:"--"),1)]),(0,t.Lk)("div",cl,[ul,(0,t.Lk)("div",dl,(0,s.v_)(Zl.endTime.length>0?Zl.endTime:"--"),1)]),(0,t.Lk)("div",vl,[Ll,(0,t.Lk)("div",kl,(0,s.v_)(Zl.chatTime.length>0?(0,n.R1)(h.Xo)(Zl.chatTime):"--"),1)])]),(0,t.Lk)("div",{class:"chatContent",ref_key:"chatContent",ref:ha},[(0,t.Lk)("ul",ml,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(ea.value,((e,l)=>((0,t.uX)(),(0,t.CE)("li",{key:l,class:(0,s.C4)({msgLiLeft:"1"===e.role,msgLiRight:"2"===e.role})},[fl,(0,t.Lk)("div",Tl,(0,s.v_)(e.timestamp),1),(0,t.Lk)("div",pl,[(0,t.Lk)("div",_l,[(0,t.Lk)("div",hl,(0,s.v_)(e.content),1)])])],2)))),128))])],512),(0,t.Lk)("div",Cl,[""!==la.value?((0,t.uX)(),(0,t.Wv)(k,{key:0,audioSrc:la.value,ref_key:"audioPlayer",ref:ta},null,8,["audioSrc"])):(0,t.Q3)("",!0)])])]),(0,t.Lk)("div",gl,[Il,(0,t.Lk)("div",Ml,[yl,(0,t.Lk)("table",El,[(0,t.Lk)("tr",null,[Rl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.CUST_NAME)},null,8,Al)]),(0,t.Lk)("tr",null,[wl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.CUST_SEX)},null,8,Sl)]),(0,t.Lk)("tr",null,[Hl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.CUST_PHONE)},null,8,Dl)]),(0,t.Lk)("tr",null,[bl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.SPECIAL_FLAG?"1"===aa.value.SPECIAL_FLAG?"是":"否":"")},null,8,Nl)])]),Kl,(0,t.Lk)("table",Pl,[(0,t.Lk)("tr",null,[Yl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.ORDER_TYPE)},null,8,Ol)]),(0,t.Lk)("tr",null,[xl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.REFLECT_NAME)},null,8,Xl)]),(0,t.Lk)("tr",null,[Gl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.TITLE)},null,8,Fl)]),(0,t.Lk)("tr",null,[Ul,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.MAJOR_CONTENT)},null,8,Wl)]),(0,t.Lk)("tr",null,[Vl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.AREA_NAME)},null,8,Bl)]),(0,t.Lk)("tr",null,[$l,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.STREET_NAME)},null,8,zl)]),(0,t.Lk)("tr",null,[Ql,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.ADDRESS)},null,8,ql)]),(0,t.Lk)("tr",null,[jl,(0,t.Lk)("td",{class:"td2",innerHTML:_a(aa.value.OFFICE_NAME)},null,8,Jl)])])])])])])],2)}}};const ea=(0,r.A)(Zl,[["__scopeId","data-v-279fb171"]]);var la=ea}}]);
//# sourceMappingURL=550.9f94b73e.js.map