{"version": 3, "file": "static/js/757.1bebef59.js", "mappings": "oTAWO,SAASA,EAAYC,EAAU,CAAC,GACrC,MAAM,YACJC,EAAc,EAAC,gBACfC,EAAkB,EAAC,aACnBC,EAAe,EAAC,WAChBC,EAAa,OAAM,UACnBC,EAAY,IACVL,EAGJ,IAAIM,EAAuB,KAC3B,MAAMC,EAAe,IAAIC,IACnBC,GAAkBC,EAAAA,EAAAA,IAAI,MAGtBC,EAA6BC,IAC5BA,EAAMC,eAETD,EAAMC,aAAeC,KAAKC,IAAIH,EAAMI,UAAUC,QAAU,EAAGhB,EAAcC,KAKvEgB,EAAmBN,IACvBD,EAA0BC,GAEtBA,EAAMO,OAEDP,EAAMI,SAASI,MAAM,EAAGR,EAAMC,cAG9BD,EAAMI,SAASI,MAAM,EAAGN,KAAKC,IAAId,EAAaW,EAAMC,gBAKzDQ,EAAsBT,IAC1BD,EAA0BC,GAEtBA,EAAMO,OAEDP,EAAMC,aAAeD,EAAMI,SAASC,OAGpCL,EAAMI,SAASC,OAAShB,GAAeW,EAAMC,aAAeZ,GAKjEqB,EAAiBA,CAACC,EAAIX,KACtBW,GAAMX,GACRL,EAAaiB,IAAID,EAAIX,IAKnBa,EAAoBA,CAACb,EAAOc,KAChCd,EAAMO,QAAUP,EAAMO,OAElBP,EAAMO,OAEJP,EAAMC,aAAeD,EAAMI,SAASC,SAEtCL,EAAMC,aAAeC,KAAKa,IAAIf,EAAMC,aAAcZ,EAAcC,IAChE0B,EAAAA,EAAAA,KAAS,KACPC,EAAoBH,OAKxBd,EAAMC,aAAeC,KAAKC,IAAIH,EAAMI,SAASC,OAAQhB,IAKnD6B,EAAgBA,CAAClB,EAAOc,KAC5B,MAAMK,EAA2BnB,EAAMO,OAASP,EAAMI,SAASC,OAAShB,EAClE+B,EAAkBlB,KAAKC,IAC3BH,EAAMC,aAAgBZ,EAAcE,EACpC4B,GAGEC,EAAkBpB,EAAMC,eAC1BD,EAAMC,aAAemB,GAGrBJ,EAAAA,EAAAA,KAAS,KACPC,EAAoBH,QAMpBO,EAA8BP,IAC9BpB,GACFA,EAAqB4B,aAGvB5B,EAAuB,IAAI6B,sBACxBC,IACCA,EAAQC,SAASC,IACf,GAAIA,EAAMC,eAAgB,CACxB,MAAM3B,EAAQL,EAAaiC,IAAIF,EAAMG,QACjC7B,IAEFN,EAAqBoC,UAAUJ,EAAMG,QAErCX,EAAclB,EAAOc,GAEzB,OAGJ,CACEiB,KAAMlC,EAAgBmC,MACtBxC,aACAC,eAMAwB,EAAuBH,IACtBpB,IAKLC,EAAasC,SAEbjB,EAAAA,EAAAA,KAAS,KAEP,MAAMkB,EAAWC,SAASC,iBAAiB,iBAE3CF,EAAST,SAAQ,CAACY,EAASC,KACzB,MAAMC,EAAUF,EAAQG,aAAa,iBAC/BxC,EAAQc,EAAWkB,MAAMS,MAAKC,GAAKA,EAAEC,cAAgBJ,IAEvDvC,IACFL,EAAaiB,IAAIyB,EAASrC,GAC1BN,EAAqBkD,QAAQP,YAO/BQ,EAAeA,CAAC/B,EAAYgC,KAChCjD,EAAgBmC,MAAQc,GAAcd,OAEtChB,EAAAA,EAAAA,KAAS,KACPK,EAA2BP,GAE3BiC,YAAW,KACT9B,EAAoBH,KACnB,SAKDkC,EAAuBC,GACpBA,EAAOC,KAAKlD,IACjB,MAAMmD,EAAsBjD,KAAKC,IAAIH,EAAMI,UAAUC,QAAU,EAAGhB,EAAcC,GAEhF,MAAO,IACFU,EACHO,QAAQ,EACRN,aAAckD,MAMdC,EAAmBtC,GAChBA,EAAWkB,MAAMqB,QAAO,CAACC,EAAKtD,KACnCsD,EAAItD,EAAM2C,aAAe,CACvBpC,OAAQP,EAAMO,OACdN,aAAcD,EAAMC,cAEfqD,IACN,CAAC,GAGAC,EAAqBA,CAACzC,EAAY0C,KACtC1C,EAAWkB,MAAMP,SAAQzB,IACvB,MAAMyD,EAAaD,EAAYxD,EAAM2C,aACjCc,IACFzD,EAAMO,OAASkD,EAAWlD,OAC1BP,EAAMC,aAAewD,EAAWxD,kBAMhCyD,EAAUA,KACVhE,IACFA,EAAqB4B,aACrB5B,EAAuB,MAEzBC,EAAasC,SAGf,MAAO,CAELpC,kBAGAS,kBACAG,qBACAC,iBACAG,oBACAgC,eACAG,sBACAI,kBACAG,qBACAG,UAGArE,cACAC,kBACAC,eAEJ,C,sPCtNA,MAAMoE,EAAQC,EAWRC,EAAOC,GAEbC,EAAAA,EAAAA,KACE,IAAMJ,EAAMK,aACXC,IACCJ,EAAK,SAAUI,MAInB,MAAMC,EAAelC,IACnB6B,EAAK,oBAAqB7B,I,wRCjC5B,MAAMmC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,+DCPOC,MAAM,iC,mCAQPC,EAAAA,EAAAA,IAAyB,OAApBD,MAAM,SAAO,W,GAEfA,MAAM,kB,GAEJA,MAAM,O,GAKRA,MAAM,S,0CAjBbE,EAAAA,EAAAA,IAmBM,MAnBNC,EAmBM,gBAlBJD,EAAAA,EAAAA,IAiBME,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAdkBC,EAAAC,MAAI,CAAlBC,EAAMC,M,WAHhBP,EAAAA,EAAAA,IAiBM,OAhBJF,OAAKU,EAAAA,EAAAA,IAAA,CAAC,YAAW,CAAAC,OACCL,EAAAM,aAAeJ,EAAKK,YAErCN,KAAMC,EACNM,IAAKN,EAAKK,UAAYJ,EACtBM,YAASC,GAAEC,EAAAC,MAAM,SAAUV,I,CAC5BW,GAEAlB,EAAAA,EAAAA,IAA6C,IAA7CmB,GAA6CC,EAAAA,EAAAA,IAAhBb,EAAKc,MAAI,IAEtCrB,EAAAA,EAAAA,IAIM,MAJNsB,EAIM,EAHJtB,EAAAA,EAAAA,IAE8C,QAD5CD,MAAM,WACLwB,OAAKC,EAAAA,EAAAA,IAAEC,EAAAC,YAAYnB,EAAKoB,a,WAE7B3B,EAAAA,EAAAA,IAAyC,IAAzC4B,GAAyCR,EAAAA,EAAAA,IAArBb,EAAKoB,WAAS,a,QAMxC,OACEE,WAAY,CAAC,EACbvC,MAAO,CACLgB,KAAM,CACJwB,KAAMC,MACNC,QAASA,IAAM,IAEjBrB,WAAY,CACVmB,KAAMG,OACND,QAAS,KAGb1B,IAAAA,GACE,MAAO,CAAC,CACV,EACA4B,SAAU,CACRC,UAAAA,GACE,MAAO,IAAIC,KAAK9B,MAAM+B,MAAK,CAACC,EAAGC,IAAMC,OAAOD,EAAEZ,WAAaa,OAAOF,EAAEX,YACtE,EACAc,OAAAA,GACE,OAA+B,IAA3BL,KAAKD,WAAWnG,OAAqB,EAClCwG,OAAOJ,KAAKD,WAAW,GAAGR,YAAc,CACjD,GAEFjC,MAAO,CAAC,EACRgD,QAAS,CACPhB,WAAAA,CAAY/D,GACV,OAAKA,GAA0B,IAAjByE,KAAKK,QACZ,CACLE,MAAQH,OAAO7E,GAASyE,KAAKK,QAAW,IAAM,KAFP,CAAEE,MAAO,KAIpD,IChDJ,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,Q,gKCQA,MAAMtD,EAAQC,EAWRsD,EAAY,CAChBC,gBAAiB,QACjBC,aAAc,OACdC,iBAAkB,OAClBC,eAAgB,SAChBC,gBAAiB,QAGbC,GAASjB,EAAAA,EAAAA,KAAS,KAOf,CAULkB,MAAO,CACLC,UAAWC,OAAOC,KAAKV,GAAWhE,KAAKgC,IAAG,CACxC2C,KAAM3C,EACNnE,IAAK,MAEP+G,OAAQ,CAAC,MAAO,OAChBC,OAAQ,MACRC,QAAS,EACTC,SAAU,CACRC,UAAYlG,GACH,eAAekF,EAAUlF,mBAAuB2B,EAAMgB,KAAK3C,MAEpEmG,KAAM,CACJC,WAAY,CACVC,UAAUC,EAAAA,EAAAA,IAAQ,IAClBC,MAAO,UACPC,cAAe,UAEjBC,WAAY,CACVC,WAAY,wBACZL,UAAUC,EAAAA,EAAAA,IAAQ,IAClBC,MAAO,UACPC,cAAe,YAIrBG,UAAW,CACTC,UAAW,CACTL,MAAO,gBAGXM,SAAU,CACRC,UAAW,CACTP,MAAO,2BAGXQ,UAAW,CACTD,UAAW,CACTP,MAAO,4BAIbS,OAAQ,CACN,CACEnB,KAAM,KACN1B,KAAM,QACNxB,KAAM,CACJ,CACEkD,KAAM,KACN7F,MAAO2F,OAAOC,KAAKV,GAAWhE,KAAKgC,GAAQvB,EAAMgB,KAAKO,EAAM,WAC5D+D,UAAW,CACTV,MAAO,WAETK,UAAW,CACTL,MAAO,oCASf,SAAEW,EAAQ,MAAEC,IAAUC,EAAAA,EAAAA,IAAS5B,G,uMC9GrC,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,mmECgLA,MAAM6B,GAASC,EAAAA,EAAAA,OAGT,SAAEC,IAAaC,EAAAA,EAAAA,MAGf,gBACJ3J,EACAS,gBAAiBmJ,EAAe,mBAChChJ,EAAkB,eAClBC,EAAc,kBACdG,EAAiB,aACjBgC,EAAY,oBACZG,EAAmB,gBACnBI,EAAe,mBACfG,EAAkB,QAClBG,GACEvE,EAAY,CACdE,YAAa,EACbC,gBAAiB,EACjBC,aAAc,EACdC,WAAY,OACZC,UAAW,KAIPiK,GAAY5J,EAAAA,EAAAA,IAAI,CACpB6J,iBAAkB,IAClBC,mBAAoB,IACpBC,sBAAuB,IACvBC,yBAA0B,KAC1BC,2BAA4B,KAC5BC,8BAA+B,OAI3BC,GAAWnK,EAAAA,EAAAA,IAAI,SACfoK,EAAa,CACjBC,MAAO,KACPC,KAAM,MAIFC,GAAgBvK,EAAAA,EAAAA,IAAI,IACpBwK,GAAWxK,EAAAA,EAAAA,IAAI,IACfkF,GAAalF,EAAAA,EAAAA,IAAI,MACjByK,GAAiBzK,EAAAA,EAAAA,IAAI,CAAC,GACtB0K,GAAc1K,EAAAA,EAAAA,IAAI,CAAC,GAGnBgB,GAAahB,EAAAA,EAAAA,IAAI,IAGjB2K,GAAsB3K,EAAAA,EAAAA,IAAI,GAG1B4K,EAA2B1K,IAC/Ba,EAAkBb,EAAOc,IAIrB6J,GAAiBC,UACrB,IACE,MAAMC,QAAYC,EAAAA,EAAAA,MACC,QAAfD,EAAIE,SACNrB,EAAU1H,MAAQ6I,EAAIlG,MAAQ,CAAC,EAEnC,CAAE,MAAOqG,GACPC,QAAQD,MAAM,WAAYA,EAC5B,GAIIE,GAAeN,UACnB,IACE,MAAMC,QAAYM,EAAAA,EAAAA,IAAoBlB,EAASjI,MAAOqI,EAAcrI,OACjD,QAAf6I,EAAIE,SACNT,EAAStI,MAAQ6I,EAAIlG,MAAQ,GACzB2F,EAAStI,MAAM3B,OAAS,EAC1B+K,GAAmBd,EAAStI,MAAM,KAElCgD,EAAWhD,MAAQ,KACnBuI,EAAevI,MAAQ,CAAC,EACxBwI,EAAYxI,MAAQ,CAAC,GAG3B,CAAE,MAAOgJ,GACPC,QAAQD,MAAM,YAAaA,EAC7B,GAgBII,GAAsBxG,IAC1BqG,QAAQI,IAAI,qBAAsBzG,GAC9BA,EAAKK,WAAaD,EAAWhD,QACjCgD,EAAWhD,MAAQ4C,EAAKK,SACxBsF,EAAevI,MAAQsI,EAAStI,MAAMS,MAAM6I,GAAMA,EAAErG,WAAaL,EAAKK,YAAa,CAAC,EAChFsF,EAAevI,MAAMiD,UAAYsF,EAAevI,MAAMuJ,WACxDf,EAAYxI,MAAQ,IAAKuI,EAAevI,UAMtCwJ,GAAkBZ,UACtB,IACE,MAAMC,QAAYY,EAAAA,EAAAA,MACC,QAAfZ,EAAIE,SACNE,QAAQI,IAAI,iBAAkBR,EAAIlG,MAAMtE,QACxCS,EAAWkB,MAAQgB,EAAoB6H,EAAIlG,MAAQ,IAGnD9B,EAAa/B,EAAYjB,GAE7B,CAAE,MAAOmL,GACPC,QAAQD,MAAM,WAAYA,EAC5B,GAIIU,IAAiBnF,EAAAA,EAAAA,KAAS,KAC9B,IAAKiE,EAAYxI,MAAM2J,sBAAuB,MAAO,CAAC,EAEtD,MAAM,sBAAEA,GAA0BnB,EAAYxI,MAC9C,OAAO2J,KAGHC,GAAcA,KAClBvB,EAAcrI,MAAQ,GACtBkJ,MAGIW,GAAwB7J,IAC5BkJ,MAIIY,GAAmBA,CAACC,EAAO/L,KAE/BqJ,EAAO2C,KAAK,CACVC,KAAM,iBAAiBF,EAAMG,UAC7BC,MAAO,CACLtE,KAAMkE,EAAMrG,KACZ0G,OAAQL,EAAMM,QACdlG,KAAMnG,EAAMsM,iBAMZC,GAAqBA,KACzB,MAAMC,EAAY3M,EAAgBmC,OAASG,SAASsK,cAAc,qBAC9DD,IACF/B,EAAoBzI,MAAQwK,EAAUE,YAKpCC,GAAwBA,KAC5B,MAAMH,EAAY3M,EAAgBmC,OAASG,SAASsK,cAAc,qBAC9DD,GAAa/B,EAAoBzI,MAAQ,GAC3Ce,YAAW,KACTyJ,EAAUE,UAAYjC,EAAoBzI,QACzC,MAKP,IAAI4K,GAAQ,K,OAEZC,EAAAA,EAAAA,KAAU,KACRlC,KACAO,KACAM,KAGAoB,GAAQE,aAAY,KAClBnC,KAEA,MAAMoC,EAAe3J,EAAgBtC,GAErC0K,KAAkBwB,MAAK,KAErBzJ,EAAmBzC,EAAYiM,QAEhC,SAGLE,EAAAA,EAAAA,KAAgB,KAEVL,KACFM,cAAcN,IACdA,GAAQ,MAIVlJ,QAIFyJ,EAAAA,EAAAA,IAAY,MACVnM,EAAAA,EAAAA,KAAS,KACP2L,YAKJS,EAAAA,EAAAA,KAAmB,CAACC,EAAGC,EAAIC,KACzBhB,KACAgB,O,moHC/YF,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,S", "sources": ["webpack://overall-monitordata-vue/./src/hooks/useLazyLoad.js", "webpack://overall-monitordata-vue/./src/components/SwitchBtn.vue", "webpack://overall-monitordata-vue/./src/components/SwitchBtn.vue?1e07", "webpack://overall-monitordata-vue/./src/views/home/<USER>/RankingList.vue", "webpack://overall-monitordata-vue/./src/views/home/<USER>/RankingList.vue?6f75", "webpack://overall-monitordata-vue/./src/views/home/<USER>/RadarChart.vue", "webpack://overall-monitordata-vue/./src/views/home/<USER>/RadarChart.vue?6797", "webpack://overall-monitordata-vue/./src/views/home/<USER>", "webpack://overall-monitordata-vue/./src/views/home/<USER>"], "sourcesContent": ["import { ref, nextTick } from 'vue'\r\n\r\n/**\r\n * 班组懒加载Hook\r\n * @param {Object} options 配置选项\r\n * @param {number} options.itemsPerRow 每行显示的项目数量\r\n * @param {number} options.initialLoadRows 初始加载的行数\r\n * @param {number} options.loadMoreRows 每次懒加载的行数\r\n * @param {string} options.rootMargin Intersection Observer的rootMargin\r\n * @param {number} options.threshold Intersection Observer的threshold\r\n */\r\nexport function useLazyLoad(options = {}) {\r\n  const {\r\n    itemsPerRow = 8,\r\n    initialLoadRows = 1,\r\n    loadMoreRows = 1,\r\n    rootMargin = '50px',\r\n    threshold = 0.1\r\n  } = options\r\n\r\n  // Intersection Observer\r\n  let intersectionObserver = null\r\n  const lazyTriggers = new Map() // 存储懒加载触发器与组的映射关系\r\n  const scrollContainer = ref(null)\r\n\r\n  // 初始化组的可见项目数量\r\n  const initializeGroupVisibility = (group) => {\r\n    if (!group.visibleCount) {\r\n      // 初始化时显示指定行数的项目\r\n      group.visibleCount = Math.min(group.userList?.length || 0, itemsPerRow * initialLoadRows)\r\n    }\r\n  }\r\n\r\n  // 获取应该显示的项目列表\r\n  const getVisibleItems = (group) => {\r\n    initializeGroupVisibility(group)\r\n    \r\n    if (group.expand) {\r\n      // 展开状态下，返回当前可见数量的项目\r\n      return group.userList.slice(0, group.visibleCount)\r\n    } else {\r\n      // 未展开状态下，最多显示一行项目\r\n      return group.userList.slice(0, Math.min(itemsPerRow, group.visibleCount))\r\n    }\r\n  }\r\n\r\n  // 判断是否需要显示\"加载更多\"触发器\r\n  const shouldShowLoadMore = (group) => {\r\n    initializeGroupVisibility(group)\r\n    \r\n    if (group.expand) {\r\n      // 展开状态：如果可见数量小于总数量，显示加载更多\r\n      return group.visibleCount < group.userList.length\r\n    } else {\r\n      // 未展开状态：如果项目总数大于一行且可见数量小于一行，显示加载更多\r\n      return group.userList.length > itemsPerRow && group.visibleCount < itemsPerRow\r\n    }\r\n  }\r\n\r\n  // 设置懒加载触发器\r\n  const setLazyTrigger = (el, group) => {\r\n    if (el && group) {\r\n      lazyTriggers.set(el, group)\r\n    }\r\n  }\r\n\r\n  // 处理组展开/收起\r\n  const toggleGroupExpand = (group, workGroups) => {\r\n    group.expand = !group.expand\r\n    \r\n    if (group.expand) {\r\n      // 展开时，如果当前可见数量少于总数量，需要启用懒加载\r\n      if (group.visibleCount < group.userList.length) {\r\n        // 确保至少显示指定行数\r\n        group.visibleCount = Math.max(group.visibleCount, itemsPerRow * initialLoadRows)\r\n        nextTick(() => {\r\n          observeLazyTriggers(workGroups)\r\n        })\r\n      }\r\n    } else {\r\n      // 收起时，重置为一行显示数量\r\n      group.visibleCount = Math.min(group.userList.length, itemsPerRow)\r\n    }\r\n  }\r\n\r\n  // 加载更多项目\r\n  const loadMoreItems = (group, workGroups) => {\r\n    const maxVisibleInCurrentState = group.expand ? group.userList.length : itemsPerRow\r\n    const newVisibleCount = Math.min(\r\n      group.visibleCount + (itemsPerRow * loadMoreRows),\r\n      maxVisibleInCurrentState\r\n    )\r\n    \r\n    if (newVisibleCount > group.visibleCount) {\r\n      group.visibleCount = newVisibleCount\r\n      \r\n      // 如果还有更多内容需要加载，继续观察\r\n      nextTick(() => {\r\n        observeLazyTriggers(workGroups)\r\n      })\r\n    }\r\n  }\r\n\r\n  // 创建 Intersection Observer\r\n  const createIntersectionObserver = (workGroups) => {\r\n    if (intersectionObserver) {\r\n      intersectionObserver.disconnect()\r\n    }\r\n\r\n    intersectionObserver = new IntersectionObserver(\r\n      (entries) => {\r\n        entries.forEach((entry) => {\r\n          if (entry.isIntersecting) {\r\n            const group = lazyTriggers.get(entry.target)\r\n            if (group) {\r\n              // 停止观察当前元素\r\n              intersectionObserver.unobserve(entry.target)\r\n              // 加载更多项目\r\n              loadMoreItems(group, workGroups)\r\n            }\r\n          }\r\n        })\r\n      },\r\n      {\r\n        root: scrollContainer.value,\r\n        rootMargin,\r\n        threshold\r\n      }\r\n    )\r\n  }\r\n\r\n  // 观察懒加载触发器\r\n  const observeLazyTriggers = (workGroups) => {\r\n    if (!intersectionObserver) {\r\n      return\r\n    }\r\n    \r\n    // 清除之前的映射关系\r\n    lazyTriggers.clear()\r\n    \r\n    nextTick(() => {\r\n      // 重新观察所有懒加载触发器\r\n      const triggers = document.querySelectorAll('.lazy-trigger')\r\n      \r\n      triggers.forEach((trigger, index) => {\r\n        const groupId = trigger.getAttribute('data-group-id')\r\n        const group = workGroups.value.find(g => g.workGroupId === groupId)\r\n        \r\n        if (group) {\r\n          lazyTriggers.set(trigger, group)\r\n          intersectionObserver.observe(trigger)\r\n        }\r\n      })\r\n    })\r\n  }\r\n\r\n  // 初始化懒加载\r\n  const initLazyLoad = (workGroups, containerRef) => {\r\n    scrollContainer.value = containerRef?.value\r\n    \r\n    nextTick(() => {\r\n      createIntersectionObserver(workGroups)\r\n      // 等待DOM更新后再观察\r\n      setTimeout(() => {\r\n        observeLazyTriggers(workGroups)\r\n      }, 100)\r\n    })\r\n  }\r\n\r\n  // 初始化组数据\r\n  const initializeGroupData = (groups) => {\r\n    return groups.map((group) => {\r\n      const initialVisibleCount = Math.min(group.userList?.length || 0, itemsPerRow * initialLoadRows)\r\n      \r\n      return {\r\n        ...group,\r\n        expand: false,\r\n        visibleCount: initialVisibleCount\r\n      }\r\n    })\r\n  }\r\n\r\n  // 保存和恢复组状态（用于数据更新时）\r\n  const saveGroupStates = (workGroups) => {\r\n    return workGroups.value.reduce((acc, group) => {\r\n      acc[group.workGroupId] = {\r\n        expand: group.expand,\r\n        visibleCount: group.visibleCount\r\n      }\r\n      return acc\r\n    }, {})\r\n  }\r\n\r\n  const restoreGroupStates = (workGroups, savedStates) => {\r\n    workGroups.value.forEach(group => {\r\n      const savedState = savedStates[group.workGroupId]\r\n      if (savedState) {\r\n        group.expand = savedState.expand\r\n        group.visibleCount = savedState.visibleCount\r\n      }\r\n    })\r\n  }\r\n\r\n  // 清理函数\r\n  const cleanup = () => {\r\n    if (intersectionObserver) {\r\n      intersectionObserver.disconnect()\r\n      intersectionObserver = null\r\n    }\r\n    lazyTriggers.clear()\r\n  }\r\n\r\n  return {\r\n    // 状态\r\n    scrollContainer,\r\n    \r\n    // 方法\r\n    getVisibleItems,\r\n    shouldShowLoadMore,\r\n    setLazyTrigger,\r\n    toggleGroupExpand,\r\n    initLazyLoad,\r\n    initializeGroupData,\r\n    saveGroupStates,\r\n    restoreGroupStates,\r\n    cleanup,\r\n    \r\n    // 常量\r\n    itemsPerRow,\r\n    initialLoadRows,\r\n    loadMoreRows\r\n  }\r\n} ", "<template>\r\n  <div class=\"switch-btn\">\r\n    <div\r\n      v-for=\"(label, value) in options\"\r\n      :key=\"value\"\r\n      class=\"switch-btn-item\"\r\n      :class=\"{ active: modelValue === value }\"\r\n      @click=\"handleClick(value)\">\r\n      <div class=\"switch-btn-item-text\">\r\n        {{ label }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { defineProps, defineEmits, watch } from 'vue'\r\n\r\nconst props = defineProps({\r\n  options: {\r\n    type: [Array, Object],\r\n    default: () => [],\r\n  },\r\n  modelValue: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n\r\nconst emit = defineEmits(['update:modelValue', 'change'])\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  (newVal) => {\r\n    emit('change', newVal)\r\n  }\r\n)\r\n\r\nconst handleClick = (value) => {\r\n  emit('update:modelValue', value)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.switch-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n\r\n  .switch-btn-item {\r\n    padding: 5px 22px;\r\n    border-radius: 4px;\r\n    background: linear-gradient(180deg, rgba(5, 85, 206, 0.5) 0%, rgba(58, 115, 244, 0) 100%);\r\n    border: 1px solid rgba(64, 92, 158, 0.5);\r\n    cursor: pointer;\r\n\r\n    &.active {\r\n      background: linear-gradient(180deg, #2b96bf 0%, #126592 50%, #2b96bf 100%);\r\n      border-image: linear-gradient(90deg, rgba(67, 126, 138, 0.35) 0%, #8ec3e8 40%, #8ec3e8 60%, rgba(67, 126, 138, 0.35) 100%) 1;\r\n      clip-path: inset(0 round 4px);\r\n\r\n      .switch-btn-item-text {\r\n        font-weight: bold;\r\n        color: unset;\r\n        background: linear-gradient(180deg, #ffffff 24%, #01ffff 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .switch-btn-item-text {\r\n    font-family: Alibaba PuHuiTi 3;\r\n    font-size: 14px;\r\n    color: rgba(255, 255, 255, 0.8);\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./SwitchBtn.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./SwitchBtn.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./SwitchBtn.vue?vue&type=style&index=0&id=13dba46a&lang=scss&scoped=true\"\n\nimport exportComponent from \"../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-13dba46a\"]])\n\nexport default __exports__", "<template>\r\n  <div class=\"ranking-list scroll-container\">\r\n    <div\r\n      class=\"rank-item\"\r\n      :class=\"{ active: activeItem === item.AGENT_NO }\"\r\n      v-for=\"(item, idx) in data\"\r\n      :data=\"item\"\r\n      :key=\"item.AGENT_NO || idx\"\r\n      @mouseover=\"$emit('active', item)\">\r\n      <div class=\"index\"></div>\r\n      <!-- <el-tooltip :content=\"item.NAME\" placement=\"top\"> -->\r\n      <p class=\"name fontStyle\">{{ item.NAME }}</p>\r\n      <!-- </el-tooltip> -->\r\n      <div class=\"bar\">\r\n        <span\r\n          class=\"bar-fill\"\r\n          :style=\"getBarStyle(item.ALL_SCORE)\"></span>\r\n      </div>\r\n      <p class=\"count\">{{ item.ALL_SCORE }}</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  components: {},\r\n  props: {\r\n    data: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    activeItem: {\r\n      type: String,\r\n      default: '',\r\n    },\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {\r\n    sortedData() {\r\n      return [...this.data].sort((a, b) => Number(b.ALL_SCORE) - Number(a.ALL_SCORE))\r\n    },\r\n    extreme() {\r\n      if (this.sortedData.length === 0) return 0\r\n      return Number(this.sortedData[0].ALL_SCORE) || 0\r\n    },\r\n  },\r\n  watch: {},\r\n  methods: {\r\n    getBarStyle(value) {\r\n      if (!value || this.extreme === 0) return { width: '0%' }\r\n      return {\r\n        width: (Number(value) / this.extreme) * 100 + '%',\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scroll-container {\r\n  margin-top: 16px;\r\n  counter-reset: itemCounter;\r\n}\r\n.rank-item {\r\n  @include flex-row;\r\n  position: relative;\r\n  padding: 0 16px 0 56px;\r\n  width: 100%;\r\n  height: 48px;\r\n  border-radius: 4px;\r\n  background: linear-gradient(270deg, rgba(0, 128, 255, 0.08) 0%, rgba(0, 128, 255, 0) 100%);\r\n  counter-increment: itemCounter;\r\n  cursor: pointer;\r\n\r\n  &.active {\r\n    border: 1px solid;\r\n    border-color: #fff197;\r\n    box-shadow: inset 0px 0px 36px 0px rgba(255, 220, 0, 0.72);\r\n  }\r\n\r\n  + .rank-item {\r\n    margin-top: 8px;\r\n  }\r\n\r\n  &:nth-child(1) {\r\n    &::before {\r\n      content: '';\r\n      top: 0;\r\n      left: 8px;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: no-repeat center/contain url('@/assets/image/rank-icon-1.svg');\r\n      border-radius: 0;\r\n    }\r\n  }\r\n  &:nth-child(2) {\r\n    &::before {\r\n      content: '';\r\n      top: 0;\r\n      left: 8px;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: no-repeat center/contain url('@/assets/image/rank-icon-2.svg');\r\n      border-radius: 0;\r\n    }\r\n  }\r\n  &:nth-child(3) {\r\n    &::before {\r\n      content: '';\r\n      top: 0;\r\n      left: 8px;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: no-repeat center/contain url('@/assets/image/rank-icon-3.svg');\r\n      border-radius: 0;\r\n    }\r\n  }\r\n\r\n  &::before {\r\n    content: '0' counter(itemCounter);\r\n    display: block;\r\n    position: absolute;\r\n    top: 12px;\r\n    left: 8px;\r\n    width: 40px;\r\n    height: 24px;\r\n    line-height: 24px;\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #ffffff;\r\n    background: linear-gradient(\r\n      270deg,\r\n      rgba(58, 115, 243, 0) 0%,\r\n      rgba(58, 115, 243, 0.4) 43%,\r\n      rgba(58, 115, 243, 0.4) 60%,\r\n      rgba(58, 115, 243, 0) 100%\r\n    );\r\n  }\r\n\r\n  &:nth-of-type(n + 10) {\r\n    &::before {\r\n      content: counter(itemCounter);\r\n    }\r\n  }\r\n\r\n  .name {\r\n    @include text-overflow(1);\r\n    flex: 0 0 64px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .count {\r\n    flex: 0 0 44px;\r\n    font-family: 'zcoolqingkehuangyouti';\r\n    font-size: 20px;\r\n    color: #00ffff;\r\n  }\r\n\r\n  .bar {\r\n    flex: 1;\r\n    margin: 0 8px;\r\n    height: 6px;\r\n    border-radius: 3px;\r\n    background-color: transparentize(#0080ff, 0.9);\r\n\r\n    .bar-fill {\r\n      display: block;\r\n      position: relative;\r\n      width: 0px;\r\n      height: 100%;\r\n      border-radius: 3px;\r\n      background: linear-gradient(270deg, #01ffff 0%, rgba(1, 255, 255, 0.05) 100%);\r\n      transition: all 0.3s;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        right: -3px;\r\n        transform: translateY(-50%);\r\n        width: 10px;\r\n        height: 16px;\r\n        background: url('@/assets/image/bar-icon-1.svg') no-repeat center/contain;\r\n      }\r\n    }\r\n  }\r\n\r\n  &:nth-of-type(n + 3) {\r\n    .bar-fill {\r\n      background: linear-gradient(270deg, #00aaff 0%, rgba(0, 170, 255, 0.05) 100%);\r\n\r\n      &::after {\r\n        background: url('@/assets/image/bar-icon-2.svg') no-repeat center/contain;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import { render } from \"./RankingList.vue?vue&type=template&id=5113c6e5&scoped=true\"\nimport script from \"./RankingList.vue?vue&type=script&lang=js\"\nexport * from \"./RankingList.vue?vue&type=script&lang=js\"\n\nimport \"./RankingList.vue?vue&type=style&index=0&id=5113c6e5&lang=scss&scoped=true\"\n\nimport exportComponent from \"../../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5113c6e5\"]])\n\nexport default __exports__", "<template>\r\n  <div class=\"radar-chart\">\r\n    <div\r\n      v-if=\"title\"\r\n      class=\"title\">\r\n      {{ title }}\r\n    </div>\r\n    <div\r\n      class=\"chart-container\"\r\n      ref=\"chartRef\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { defineProps, computed } from 'vue'\r\nimport { useChart, nowSize } from '@/hooks'\r\n\r\nconst props = defineProps({\r\n  data: {\r\n    type: Object,\r\n    required: true,\r\n  },\r\n  title: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n\r\nconst label_map = {\r\n  busiNumberScore: '业务量得分',\r\n  qualityScore: '质检得分',\r\n  monthlyExamScore: '月考得分',\r\n  afterLongScore: '话后处理得分',\r\n  attendanceScore: '出勤得分',\r\n}\r\n\r\nconst option = computed(() => {\r\n  // let max = 0\r\n  // for (const key in label_map) {\r\n  //   if (props.data[key] > max) {\r\n  //     max = props.data[key]\r\n  //   }\r\n  // }\r\n  return {\r\n    // tooltip: {\r\n    //   show: true,\r\n    //   appendToBody: true,\r\n    //   formatter: (params) => {\r\n    //     return Object.keys(label_map).map((key) => {\r\n    //       return `${label_map[key]}: ${props.data[key]}`\r\n    //     }).join('<br />')\r\n    //   },\r\n    // },\r\n    radar: {\r\n      indicator: Object.keys(label_map).map((key) => ({\r\n        name: key,\r\n        max: 5,\r\n      })),\r\n      center: ['50%', '60%'],\r\n      radius: '50%',\r\n      nameGap: 5,\r\n      axisName: {\r\n        formatter: (value) => {\r\n          return `{labelStyle|${label_map[value]}} {valueStyle|${props.data[value]}}`\r\n        },\r\n        rich: {\r\n          labelStyle: {\r\n            fontSize: nowSize(12),\r\n            color: '#FFFFFF',\r\n            verticalAlign: 'middle',\r\n          },\r\n          valueStyle: {\r\n            fontFamily: 'zcoolqingkehuangyouti',\r\n            fontSize: nowSize(14),\r\n            color: '#00FFFF',\r\n            verticalAlign: 'middle',\r\n          },\r\n        },\r\n      },\r\n      splitArea: {\r\n        areaStyle: {\r\n          color: 'transparent',\r\n        },\r\n      },\r\n      axisLine: {\r\n        lineStyle: {\r\n          color: 'rgba(0, 128, 255, 0.3)',\r\n        },\r\n      },\r\n      splitLine: {\r\n        lineStyle: {\r\n          color: 'rgba(0, 128, 255, 0.3)',\r\n        },\r\n      },\r\n    },\r\n    series: [\r\n      {\r\n        name: '得分',\r\n        type: 'radar',\r\n        data: [\r\n          {\r\n            name: '得分',\r\n            value: Object.keys(label_map).map((key) => props.data[key + 'Leven']),\r\n            itemStyle: {\r\n              color: '#00FFFF',\r\n            },\r\n            areaStyle: {\r\n              color: 'rgba(51, 250, 253, 0.3)',\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  }\r\n})\r\n\r\nconst { chartRef, chart } = useChart(option)\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.radar-chart {\r\n  position: relative;\r\n  background-color: transparentize(#0080ff, 0.9);\r\n  border-radius: 4px;\r\n\r\n  .title {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    padding: 8px 16px;\r\n    border-radius: 4px 0px 16px 0px;\r\n    background: transparentize(#00ffff, 0.9);\r\n    font-size: 14px;\r\n    color: #00ffff;\r\n  }\r\n\r\n  .chart-container {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./RadarChart.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./RadarChart.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./RadarChart.vue?vue&type=style&index=0&id=06e06084&lang=scss&scoped=true\"\n\nimport exportComponent from \"../../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-06e06084\"]])\n\nexport default __exports__", "<template>\n  <div\n    class=\"home\"\n    :class=\"{ 'iframe-mode': isIframe }\">\n    <!-- <div class=\"header\">\n      <h2 class=\"title fontStyle\">工作量监测</h2>\n    </div> -->\n    <div class=\"body\">\n      <div class=\"area\">\n        <p class=\"item\">\n          <span class=\"label fontStyle\">当天接线量</span>\n          <span class=\"value\">{{ formatFigure(totalData.ACCEPTANCE_COUNT) }}</span>\n        </p>\n        <p class=\"item\">\n          <span class=\"label fontStyle\">回访总量</span>\n          <span class=\"value\">{{ formatFigure(totalData.ACCEPTANCE_FLOW_UP) }}</span>\n        </p>\n        <p class=\"item\">\n          <span class=\"label fontStyle\">网络总量</span>\n          <span class=\"value\">{{ formatFigure(totalData.ACCEPTANCE_CALL_COUNT) }}</span>\n        </p>\n      </div>\n      <div class=\"area\">\n        <div class=\"title-secondary\">\n          <h3 class=\"fontStyle\">上月综合排名</h3>\n          <SwitchBtn\n            v-model=\"rankType\"\n            :options=\"btnOptions\"\n            @change=\"handleRankTypeChange\" />\n        </div>\n        <div class=\"search-bar\">\n          <el-form-item label=\"关键字\">\n            <el-input\n              v-model=\"searchKeyword\"\n              placeholder=\"请输入\"\n              clearable />\n          </el-form-item>\n          <el-button\n            @click=\"handleReset\"\n            :icon=\"Refresh\">\n            重置\n          </el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"handleSearch\"\n            :icon=\"Search\"\n            style=\"margin: 0\">\n            搜索\n          </el-button>\n        </div>\n        <RankingList\n          :data=\"rankList\"\n          :activeItem=\"activeItem\"\n          @active=\"handleActiveChange\"\n          style=\"\"></RankingList>\n        <div class=\"active-panel\">\n          <div class=\"part\">\n            <p class=\"item\">\n              <span class=\"label fontStyle\">总分</span>\n              <span class=\"value f-24\">{{ fiveWayData.ALL_SCORE || '-' }}</span>\n            </p>\n            <div class=\"sub-part\">\n              <p class=\"item\">\n                <span class=\"label fontStyle\">奖励分</span>\n                <span class=\"value\">{{ fiveWayData.REWARD || '-' }}</span>\n              </p>\n              <p class=\"item\">\n                <span class=\"label fontStyle\">扣减分</span>\n                <span class=\"value\">{{ fiveWayData.DEDUCTION || '-' }}</span>\n              </p>\n            </div>\n          </div>\n          <div class=\"part\">\n            <p class=\"item\">\n              <span class=\"label fontStyle\">月度排名</span>\n              <span class=\"value\">{{ fiveWayData.MONTHLY_RANKING || '-' }}</span>\n            </p>\n          </div>\n          <div class=\"part\">\n            <p class=\"item\">\n              <span class=\"label fontStyle\">挂机满意度</span>\n              <span class=\"value\">{{ fiveWayData.ON_HOOK_SATISFACTION || '-' }}%</span>\n            </p>\n          </div>\n        </div>\n        <RadarChart\n          v-if=\"fiveWayData.LAST_MONTH_SCORE_FIVE\"\n          :data=\"radarChartData\"\n          :title=\"activeItemData.NAME + '-五维图'\" />\n      </div>\n      <div class=\"area\">\n        <div class=\"title-secondary\">\n          <h3 class=\"fontStyle\">人员职场</h3>\n        </div>\n        <div class=\"total-data\">\n          <p class=\"item\">\n            <span class=\"label fontStyle\">接线量占比</span>\n            <span class=\"value\">{{ totalData.ACCEPTANCE_COUNT_PERCENT || '0%' }}</span>\n          </p>\n          <span class=\"divider\"></span>\n          <p class=\"item\">\n            <span class=\"label fontStyle\">回访量占比</span>\n            <span class=\"value\">{{ totalData.ACCEPTANCE_FLOW_UP_PERCENT || '0%' }}</span>\n          </p>\n          <span class=\"divider\"></span>\n          <p class=\"item\">\n            <span class=\"label fontStyle\">网络量占比</span>\n            <span class=\"value\">{{ totalData.ACCEPTANCE_CALL_COUNT_PERCENT || '0%' }}</span>\n          </p>\n        </div>\n        <div\n          class=\"scroll-container\"\n          ref=\"scrollContainer\">\n          <div\n            v-for=\"group in workGroups\"\n            :key=\"group.workGroupId\"\n            class=\"group-container\">\n            <div class=\"bar\">\n              <div class=\"name fontStyle\">{{ group.workGroupName }}</div>\n              <span class=\"label fontStyle\">{{ group.businessType }}</span>\n              <span class=\"value\">{{ group.workGroupWorkCount }}</span>\n              <div\n                v-if=\"group.userList.length > 8\"\n                class=\"btn fontStyle\"\n                @click=\"handleToggleGroupExpand(group)\">\n                {{ group.expand ? '收起班组' : '展开班组' }}\n                <el-icon v-if=\"group.expand\"><ArrowUp /></el-icon>\n                <el-icon v-else><ArrowDown /></el-icon>\n              </div>\n            </div>\n            <div\n              v-if=\"group.userList.length > 0\"\n              :class=\"['content', group.expand ? '' : 'not-expand']\">\n              <div\n                v-for=\"staff in getVisibleStaff(group)\"\n                :key=\"staff.AGENTID\"\n                @click=\"handleStaffClick(staff, group)\"\n                class=\"staff\">\n                <el-avatar\n                  :size=\"56\"\n                  :src=\"staff.IMG_URL || IconKefu\"\n                  @error=\"staff.IMG_URL = IconKefu\"\n                  style=\"flex: 0 0 56px; border: 1px solid #00ffff\" />\n                <div class=\"name fontStyle\">{{ staff.NAME }}</div>\n                <p>\n                  <span class=\"label fontStyle\">工作量</span>\n                  <span class=\"value\">{{ staff.WORK_COUNT }}</span>\n                </p>\n              </div>\n              <div\n                v-if=\"shouldShowLoadMore(group)\"\n                :ref=\"(el) => setLazyTrigger(el, group)\"\n                :data-group-id=\"group.workGroupId\"\n                class=\"lazy-trigger\">\n                <div class=\"loading-placeholder\">\n                  <div class=\"loading-text\">加载中</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, defineOptions, onMounted, computed, onBeforeUnmount, nextTick, onActivated, onDeactivated } from 'vue'\nimport { ArrowUp, ArrowDown, Search, Refresh } from '@element-plus/icons-vue'\nimport { useRouter } from 'vue-router'\nimport { onBeforeRouteLeave } from 'vue-router'\nimport { formatFigure } from '@/utils'\nimport { useIframe } from '@/hooks'\nimport { useLazyLoad } from '@/hooks/useLazyLoad'\nimport SwitchBtn from '@/components/SwitchBtn.vue'\nimport RankingList from '@/views/home/<USER>/RankingList.vue'\nimport RadarChart from '@/views/home/<USER>/RadarChart.vue'\nimport IconKefu from '@/assets/image/icon_kefu.svg'\nimport { getAllCallStat, getLastMonthRanking, getAgentFiveWayStat, getWorkGroup } from '@/api/workMonitor'\n\ndefineOptions({\n  name: 'HomePage',\n})\n\nconst router = useRouter()\n\n// iframe mode\nconst { isIframe } = useIframe()\n\n// 懒加载功能\nconst {\n  scrollContainer,\n  getVisibleItems: getVisibleStaff,\n  shouldShowLoadMore,\n  setLazyTrigger,\n  toggleGroupExpand,\n  initLazyLoad,\n  initializeGroupData,\n  saveGroupStates,\n  restoreGroupStates,\n  cleanup,\n} = useLazyLoad({\n  itemsPerRow: 8,\n  initialLoadRows: 1,\n  loadMoreRows: 1,\n  rootMargin: '50px',\n  threshold: 0.1,\n})\n\n// 总体数据\nconst totalData = ref({\n  ACCEPTANCE_COUNT: '0',\n  ACCEPTANCE_FLOW_UP: '0',\n  ACCEPTANCE_CALL_COUNT: '0',\n  ACCEPTANCE_COUNT_PERCENT: '0%',\n  ACCEPTANCE_FLOW_UP_PERCENT: '0%',\n  ACCEPTANCE_CALL_COUNT_PERCENT: '0%',\n})\n\n// 排名\nconst rankType = ref('agent')\nconst btnOptions = {\n  agent: '坐席',\n  work: '班组',\n}\n\n// 搜索相关\nconst searchKeyword = ref('')\nconst rankList = ref([])\nconst activeItem = ref(null)\nconst activeItemData = ref({})\nconst fiveWayData = ref({})\n\n// 班组数据\nconst workGroups = ref([])\n\n// 滚动位置记录\nconst savedScrollPosition = ref(0)\n\n// 处理班组展开/收起\nconst handleToggleGroupExpand = (group) => {\n  toggleGroupExpand(group, workGroups)\n}\n\n// 获取总体数据\nconst fetchTotalData = async () => {\n  try {\n    const res = await getAllCallStat()\n    if (res.result === '000') {\n      totalData.value = res.data || {}\n    }\n  } catch (error) {\n    console.error('获取总体数据失败', error)\n  }\n}\n\n// 获取排行榜数据\nconst handleSearch = async () => {\n  try {\n    const res = await getLastMonthRanking(rankType.value, searchKeyword.value)\n    if (res.result === '000') {\n      rankList.value = res.data || []\n      if (rankList.value.length > 0) {\n        handleActiveChange(rankList.value[0])\n      } else {\n        activeItem.value = null\n        activeItemData.value = {}\n        fiveWayData.value = {}\n      }\n    }\n  } catch (error) {\n    console.error('获取排行榜数据失败', error)\n  }\n}\n\n// 获取五维图数据\nconst fetchFiveWayData = async (agentNo, monthId) => {\n  try {\n    const res = await getAgentFiveWayStat(rankType.value, agentNo, monthId)\n    if (res.result === '000') {\n      fiveWayData.value = res.data || {}\n    }\n  } catch (error) {\n    console.error('获取五维图数据失败', error)\n  }\n}\n\n// 处理激活项变化\nconst handleActiveChange = (item) => {\n  console.log('handleActiveChange', item)\n  if (item.AGENT_NO === activeItem.value) return\n  activeItem.value = item.AGENT_NO\n  activeItemData.value = rankList.value.find((i) => i.AGENT_NO === item.AGENT_NO) || {}\n  if (activeItemData.value.AGENT_NO && activeItemData.value.MONTH_ID) {\n    fiveWayData.value = { ...activeItemData.value }\n    // fetchFiveWayData(activeItemData.value.AGENT_NO, activeItemData.value.MONTH_ID)\n  }\n}\n\n// 获取班组数据\nconst fetchWorkGroups = async () => {\n  try {\n    const res = await getWorkGroup()\n    if (res.result === '000') {\n      console.log('获取班组数据成功，数据数量:', res.data?.length)\n      workGroups.value = initializeGroupData(res.data || [])\n\n      // 数据加载完成后，初始化懒加载观察器\n      initLazyLoad(workGroups, scrollContainer)\n    }\n  } catch (error) {\n    console.error('获取班组数据失败', error)\n  }\n}\n\n// 转换雷达图数据\nconst radarChartData = computed(() => {\n  if (!fiveWayData.value.LAST_MONTH_SCORE_FIVE) return {}\n\n  const { LAST_MONTH_SCORE_FIVE } = fiveWayData.value\n  return LAST_MONTH_SCORE_FIVE\n})\n\nconst handleReset = () => {\n  searchKeyword.value = ''\n  handleSearch()\n}\n\nconst handleRankTypeChange = (value) => {\n  handleSearch()\n}\n\n// 处理员工点击事件\nconst handleStaffClick = (staff, group) => {\n  // 跳转到员工监控页面\n  router.push({\n    path: `/call-monitor/${staff.AGENTID}`,\n    query: {\n      name: staff.NAME,\n      avatar: staff.IMG_URL,\n      type: group.businessType\n    },\n  })\n}\n\n// 保存滚动位置\nconst saveScrollPosition = () => {\n  const container = scrollContainer.value || document.querySelector('.scroll-container')\n  if (container) {\n    savedScrollPosition.value = container.scrollTop\n  }\n}\n\n// 恢复滚动位置\nconst restoreScrollPosition = () => {\n  const container = scrollContainer.value || document.querySelector('.scroll-container')\n  if (container && savedScrollPosition.value > 0) {\n    setTimeout(() => {\n      container.scrollTop = savedScrollPosition.value\n    }, 100)\n  }\n}\n\n// 定时器\nlet timer = null\n\nonMounted(() => {\n  fetchTotalData()\n  handleSearch()\n  fetchWorkGroups()\n\n  // 设置轮询\n  timer = setInterval(() => {\n    fetchTotalData()\n    // 注意：重新获取班组数据时需要保持当前的展开状态和可见数量\n    const currentState = saveGroupStates(workGroups)\n\n    fetchWorkGroups().then(() => {\n      // 恢复之前的状态\n      restoreGroupStates(workGroups, currentState)\n    })\n  }, 10000)\n})\n\nonBeforeUnmount(() => {\n  // 清除定时器\n  if (timer) {\n    clearInterval(timer)\n    timer = null\n  }\n\n  // 清理懒加载相关资源\n  cleanup()\n})\n\n// keep-alive 组件激活时恢复滚动位置\nonActivated(() => {\n  nextTick(() => {\n    restoreScrollPosition()\n  })\n})\n\n// 路由离开前保存滚动位置\nonBeforeRouteLeave((_, __, next) => {\n  saveScrollPosition()\n  next()\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.home {\n  display: flex;\n  flex-direction: column;\n  padding: 24px;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(50% 50% at 50% 50%, #003792 0%, #001856 100%);\n  // background: url('@/assets/image/bg-black.jpg') no-repeat center center;\n  background-size: cover;\n  overflow: hidden;\n\n  .header {\n    margin: 6px 0 16px;\n    background: url('@/assets/image/title-bg.png') no-repeat center bottom/contain;\n\n    .title {\n      font-size: 32px;\n      font-weight: bold;\n      text-align: center;\n    }\n  }\n\n  .body {\n    flex: 1;\n    display: grid;\n    grid-template-areas:\n      'a b'\n      'c b';\n    grid-template-columns: 1fr 408px;\n    grid-template-rows: 60px 1fr;\n    gap: 16px;\n    overflow: hidden;\n\n    .area {\n      overflow: hidden;\n\n      &:nth-child(1) {\n        grid-area: a;\n        display: flex;\n        gap: 25px;\n\n        .item {\n          flex: 1;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          gap: 16px;\n          background: url('@/assets/image/name-bg.svg') no-repeat center center/100%;\n\n          .label {\n            font-size: 24px;\n            font-weight: bold;\n          }\n\n          .value {\n            font-family: 'zcoolqingkehuangyouti';\n            font-size: 40px;\n            color: #00ffff;\n          }\n        }\n      }\n\n      &:nth-child(2) {\n        grid-area: b;\n        padding: 24px;\n        display: flex;\n        flex-direction: column;\n        background-color: transparentize(#0080ff, 0.9);\n        border-radius: 4px;\n\n        .title-secondary {\n          display: flex;\n          justify-content: space-between;\n        }\n\n        .search-bar {\n          margin-top: 24px;\n          display: flex;\n          gap: 16px;\n\n          .el-input {\n            flex: 1;\n          }\n\n          .el-button {\n            color: #00ffff;\n            background: radial-gradient(91% 91% at 50% 50%, rgba(0, 255, 255, 0.24) 0%, rgba(0, 255, 255, 0) 100%);\n            border-image: linear-gradient(0deg, rgba(0, 255, 255, 0.3) 0%, #00ffff 74%, #ffffff 100%) 1;\n            box-shadow: inset 0px 0px 16px 0px rgba(0, 255, 255, 0.4);\n            clip-path: inset(0 round 4px);\n\n            &:hover,\n            &:focus {\n              background: rgba(0, 197, 255, 0.5);\n            }\n\n            &:active {\n              background: rgba(0, 197, 255, 0.2);\n            }\n          }\n        }\n\n        .ranking-list {\n          flex: 1 1 352px;\n        }\n\n        .radar-chart {\n          flex: 1 0 292px;\n        }\n\n        .active-panel {\n          flex: 0 0 96px;\n          margin: 16px 0;\n          display: grid;\n          grid-template-areas:\n            'a b'\n            'a c';\n          grid-template-columns: 196px 1fr;\n          grid-template-rows: 1fr 1fr;\n          gap: 16px;\n\n          .part {\n            padding: 16px;\n            background-color: transparentize(#0080ff, 0.9);\n            border-radius: 4px;\n\n            &:nth-child(1) {\n              grid-area: a;\n              display: flex;\n              justify-content: center;\n              align-items: flex-start;\n              flex-direction: column;\n              gap: 8px;\n\n              .sub-part {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                width: 100%;\n              }\n\n              .item {\n                display: flex;\n                justify-content: flex-start;\n                align-items: center;\n                gap: 8px;\n              }\n            }\n\n            &:nth-child(2) {\n              grid-area: b;\n\n              .item {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n              }\n            }\n\n            &:nth-child(3) {\n              grid-area: c;\n\n              .item {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n              }\n            }\n          }\n\n          .label {\n            font-size: 14px;\n            font-weight: bold;\n          }\n\n          .value {\n            font-family: 'zcoolqingkehuangyouti';\n            font-size: 16px;\n            color: #00ffff;\n          }\n\n          .f-24 {\n            font-size: 24px;\n          }\n        }\n      }\n\n      &:nth-child(3) {\n        grid-area: c;\n        padding: 24px;\n        display: flex;\n        flex-direction: column;\n        background-color: transparentize(#0080ff, 0.9);\n        border-radius: 4px;\n\n        .total-data {\n          margin: 16px 0 24px;\n          padding: 20px 29px;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background-color: transparentize(#0080ff, 0.9);\n          border-radius: 4px;\n\n          .item {\n            flex: 1;\n            display: flex;\n            justify-content: center;\n            align-items: flex-end;\n            gap: 8px;\n\n            .label {\n              font-size: 18px;\n              font-weight: bold;\n            }\n\n            .value {\n              font-family: 'zcoolqingkehuangyouti';\n              font-size: 32px;\n              color: #ffdc00;\n              line-height: 1;\n            }\n          }\n\n          .divider {\n            width: 1px;\n            height: 32px;\n            background-color: transparentize(#0080ff, 0.7);\n          }\n        }\n\n        .group-container {\n          margin-bottom: 24px;\n\n          .bar {\n            padding-right: 16px;\n            padding-bottom: 16px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .name {\n              margin-right: 16px;\n              font-size: 18px;\n              font-weight: bold;\n\n              &::before {\n                content: '';\n                margin-right: 8px;\n                display: inline-block;\n                width: 4px;\n                height: 14px;\n                background: #00ffff;\n              }\n            }\n\n            .label {\n              margin-right: 8px;\n              font-size: 14px;\n            }\n\n            .value {\n              flex: 1;\n              font-family: 'zcoolqingkehuangyouti';\n              font-size: 24px;\n              color: #00ffff;\n            }\n\n            .btn {\n              font-size: 14px;\n              cursor: pointer;\n\n              .el-icon {\n                color: #fff;\n              }\n            }\n          }\n\n          .content {\n            display: grid;\n            grid-template-columns: repeat(auto-fill, minmax(148px, 158px));\n            grid-template-rows: 158px;\n            gap: 16px;\n            overflow: hidden;\n            &.not-expand {\n              height: 158px;\n            }\n\n            .staff {\n              padding: 16px 0;\n              display: flex;\n              flex-direction: column;\n              justify-content: center;\n              align-items: center;\n              background: rgba(0, 128, 255, 0.2);\n              border-radius: 4px;\n              cursor: pointer;\n\n              .el-avatar {\n                --el-avatar-size: 56px !important;\n                flex: 0 0 56px !important;\n              }\n\n              .name {\n                margin: 4px 0;\n                font-size: 16px;\n                font-weight: bold;\n              }\n\n              .label {\n                margin-right: 8px;\n                font-size: 14px;\n              }\n\n              .value {\n                font-family: 'zcoolqingkehuangyouti';\n                font-size: 20px;\n                color: #ffdc00;\n              }\n            }\n\n            // 懒加载触发器样式\n            .lazy-trigger {\n              grid-column: 1 / -1;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              height: 40px;\n\n              .loading-placeholder {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                .loading-text {\n                  color: rgba(255, 255, 255, 0.6);\n                  font-size: 14px;\n\n                  &::after {\n                    content: '';\n                    display: inline-block;\n                    width: 0;\n                    height: 0;\n                    animation: loading-dots 1.5s infinite;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .title-secondary {\n        background: url('@/assets/image/title-secondary-bg.svg') no-repeat left bottom/352px 20px;\n\n        h3 {\n          margin-left: 24px;\n          font-size: 24px;\n          font-weight: bold;\n        }\n      }\n\n      .scroll-container {\n        flex: 1;\n        overflow-y: auto;\n      }\n    }\n  }\n}\n\n// 加载动画\n@keyframes loading-dots {\n  0%,\n  20% {\n    content: '.';\n  }\n  40% {\n    content: '..';\n  }\n  60%,\n  100% {\n    content: '...';\n  }\n}\n</style>\n\n", "import script from \"./index.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./index.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./index.vue?vue&type=style&index=0&id=4dbf9c78&lang=scss&scoped=true\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-4dbf9c78\"]])\n\nexport default __exports__"], "names": ["useLazyLoad", "options", "itemsPerRow", "initialLoadRows", "loadMoreRows", "rootMargin", "threshold", "intersectionObserver", "lazyTriggers", "Map", "scrollContainer", "ref", "initializeGroupVisibility", "group", "visibleCount", "Math", "min", "userList", "length", "getVisibleItems", "expand", "slice", "shouldShowLoadMore", "setLazyTrigger", "el", "set", "toggleGroupExpand", "workGroups", "max", "nextTick", "observeLazyTriggers", "loadMoreItems", "maxVisibleInCurrentState", "newVisibleCount", "createIntersectionObserver", "disconnect", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "get", "target", "unobserve", "root", "value", "clear", "triggers", "document", "querySelectorAll", "trigger", "index", "groupId", "getAttribute", "find", "g", "workGroupId", "observe", "initLazyLoad", "containerRef", "setTimeout", "initializeGroupData", "groups", "map", "initialVisibleCount", "saveGroupStates", "reduce", "acc", "restoreGroupStates", "savedStates", "savedState", "cleanup", "props", "__props", "emit", "__emit", "watch", "modelValue", "newVal", "handleClick", "__exports__", "class", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_Fragment", "_renderList", "$props", "data", "item", "idx", "_normalizeClass", "active", "activeItem", "AGENT_NO", "key", "onMouseover", "$event", "_ctx", "$emit", "_hoisted_3", "_hoisted_4", "_toDisplayString", "NAME", "_hoisted_5", "style", "_normalizeStyle", "$options", "getBarStyle", "ALL_SCORE", "_hoisted_6", "components", "type", "Array", "default", "String", "computed", "sortedData", "this", "sort", "a", "b", "Number", "extreme", "methods", "width", "render", "label_map", "busiNumberScore", "qualityScore", "monthlyExamScore", "afterLongScore", "attendanceScore", "option", "radar", "indicator", "Object", "keys", "name", "center", "radius", "nameGap", "axisName", "formatter", "rich", "labelStyle", "fontSize", "nowSize", "color", "verticalAlign", "valueStyle", "fontFamily", "splitArea", "areaStyle", "axisLine", "lineStyle", "splitLine", "series", "itemStyle", "chartRef", "chart", "useChart", "router", "useRouter", "isIframe", "useIframe", "getVisibleStaff", "totalData", "ACCEPTANCE_COUNT", "ACCEPTANCE_FLOW_UP", "ACCEPTANCE_CALL_COUNT", "ACCEPTANCE_COUNT_PERCENT", "ACCEPTANCE_FLOW_UP_PERCENT", "ACCEPTANCE_CALL_COUNT_PERCENT", "rankType", "btnOptions", "agent", "work", "searchKeyword", "rankList", "activeItemData", "fiveWayData", "savedScrollPosition", "handleToggleGroupExpand", "fetchTotalData", "async", "res", "getAllCallStat", "result", "error", "console", "handleSearch", "getLastMonthRanking", "handleActiveChange", "log", "i", "MONTH_ID", "fetchWorkGroups", "getWorkGroup", "radarChartData", "LAST_MONTH_SCORE_FIVE", "handleReset", "handleRankTypeChange", "handleStaffClick", "staff", "push", "path", "AGENTID", "query", "avatar", "IMG_URL", "businessType", "saveScrollPosition", "container", "querySelector", "scrollTop", "restoreScrollPosition", "timer", "onMounted", "setInterval", "currentState", "then", "onBeforeUnmount", "clearInterval", "onActivated", "onBeforeRouteLeave", "_", "__", "next"], "sourceRoot": ""}