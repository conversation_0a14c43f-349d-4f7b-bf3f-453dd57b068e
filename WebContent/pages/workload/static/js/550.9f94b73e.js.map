{"version": 3, "file": "static/js/550.9f94b73e.js", "mappings": "gSAaA,I,UCNA,I,0ICiBA,MAAMA,EAAQC,EAORC,GAAeC,EAAAA,EAAAA,IAAI,MACnBC,GAAaD,EAAAA,EAAAA,IAAI,MACjBE,GAASF,EAAAA,EAAAA,IAAI,MACbG,GAAQH,EAAAA,EAAAA,IAAI,MACZI,GAAeJ,EAAAA,EAAAA,IAAI,MACnBK,GAAWL,EAAAA,EAAAA,IAAI,MACfM,GAASN,EAAAA,EAAAA,IAAI,MACbO,GAAYP,EAAAA,EAAAA,KAAI,GAChBQ,GAAcR,EAAAA,EAAAA,IAAI,GAClBS,GAAWT,EAAAA,EAAAA,IAAI,GACfU,GAAeV,EAAAA,EAAAA,IAAI,IACnBW,GAAeX,EAAAA,EAAAA,IAAI,IAEnBY,EAAYC,UAChB,IACET,EAAaU,MAAQ,IAAKC,OAAOC,cAC/BD,OAAOE,oBACTZ,EAASS,MAAQV,EAAaU,MAAMI,iBACpCb,EAASS,MAAMK,QAAU,MAGzBC,EAAAA,EAAAA,KAAS,KACPC,MAEJ,CAAE,MAAOC,GACPC,QAAQD,MAAM,cAAeA,EAC/B,GAGIE,EAAYX,UAChB,IACE,IAAKhB,EAAM4B,SAET,YADAF,QAAQG,KAAK,UAGVtB,EAAaU,aACVF,IAGRW,QAAQI,IAAI,UAAW9B,EAAM4B,UAC7B,MAAMG,QAAiBC,MAAMhC,EAAM4B,UACnC,IAAKG,EAASE,GACZ,MAAM,IAAIC,MAAM,uBAAuBH,EAASI,UAElD,MAAMC,QAAoBL,EAASK,cACnCV,QAAQI,IAAI,gBAGqB,cAA7BvB,EAAaU,MAAMoB,aACf9B,EAAaU,MAAMqB,SAG3B7B,EAAOQ,YAAcV,EAAaU,MAAMsB,gBAAgBH,GACxDxB,EAASK,MAAQR,EAAOQ,MAAML,SAC9Bc,QAAQI,IAAI,aAAclB,EAASK,QAGnCM,EAAAA,EAAAA,KAAS,KACPiB,IACAhB,MAEJ,CAAE,MAAOC,GACPC,QAAQD,MAAM,UAAWA,EAC3B,GAGIe,EAAuBA,KAC3B,GAAK/B,EAAOQ,MAKZ,IACES,QAAQI,IAAI,YACZ,MAAMW,EAAchC,EAAOQ,MAAMyB,eAAe,GAE1CC,EAAeC,KAAKC,MAAMxC,EAAOY,MAAM6B,MAAQ,GAC/CC,EAAgBH,KAAKC,MAAMJ,EAAYO,OAASL,GAChDM,EAAkB,GAExB,IAAK,IAAIC,EAAI,EAAGA,EAAIP,EAAcO,IAAK,CACrC,MAAMC,EAAQD,EAAIH,EACZK,EAAMR,KAAKS,IAAIF,EAAQJ,EAAeN,EAAYO,QACxD,IAAIK,EAAM,EACNC,GAAO,EAEX,IAAK,IAAIC,EAAIJ,EAAOI,EAAIH,EAAKG,IAAK,CAChC,MAAMtC,EAAQwB,EAAYc,GAC1BF,EAAMT,KAAKS,IAAIA,EAAKpC,GACpBqC,EAAMV,KAAKU,IAAIA,EAAKrC,EACtB,CAEAgC,EAAgBO,KAAK,CAAEH,MAAKC,OAC9B,CAEAzC,EAAaI,MAAQgC,EACrBvB,QAAQI,IAAI,iBAAkBjB,EAAaI,MAAM+B,SAEjDzB,EAAAA,EAAAA,KAAS,KACPkC,MAEJ,CAAE,MAAOhC,GACPC,QAAQD,MAAM,YAAaA,EAC7B,MAnCEC,QAAQG,KAAK,iBAsCX4B,EAAeA,KACnB,IAAKpD,EAAOY,QAAUJ,EAAaI,MAAM+B,OAEvC,YADAtB,QAAQG,KAAK,2BAIfH,QAAQI,IAAI,UACZ,MAAM4B,EAAMrD,EAAOY,MAAM0C,WAAW,MACpC,IAAKD,EAEH,YADAhC,QAAQD,MAAM,iBAIhBiC,EAAIE,UAAU,EAAG,EAAGvD,EAAOY,MAAM6B,MAAOzC,EAAOY,MAAM4C,QAErD,MAAMC,EAAS,CACbC,KAAM,GACNC,MAAO,IAEHlB,EAAQzC,EAAOY,MAAM6B,MAAQgB,EAAOC,KAAOD,EAAOE,MAClDH,EAASxD,EAAOY,MAAM4C,OACtBI,EAAUJ,EAAS,EAGnBK,EAAYtB,KAAKU,IACrBQ,EAAOC,KACPnB,KAAKS,IACHhD,EAAOY,MAAM6B,MAAQgB,EAAOE,MAC5BF,EAAOC,KAAQpD,EAAYM,MAAQL,EAASK,MAAS6B,IAKnDqB,EAAW,EACXC,EAAS,EACTC,EAAaF,EAAWC,EACxBE,EAAe1B,KAAKC,MAAe,GAATgB,GAC1BU,EAAeJ,EAAW,EAG1BK,EAAe5B,KAAKU,OACrBzC,EAAaI,MAAMwD,KAAKC,GAAU9B,KAAK+B,IAAID,EAAMpB,IAAMoB,EAAMrB,QAIlExC,EAAaI,MAAM2D,SAAQ,CAACF,EAAOxB,KACjC,MAAM2B,EAAIf,EAAOC,KAAOb,EAAImB,EAGtBS,EAAYlC,KAAK+B,IAAID,EAAMpB,IAAMoB,EAAMrB,KACvC0B,EAAoBD,EAAYN,EAAgBF,EAChDU,EAAYpC,KAAKU,IAAI,EAAGyB,GAGxBE,EAAWJ,GAAKX,EAKpBR,EAAIwB,UAFFD,EAEc,UAGA,UAIlB,MAAME,EAAIlB,EAAUe,EAAY,EAGhCtB,EAAI0B,YACJ1B,EAAI2B,OAAOR,EAAIN,EAAcY,GAC7BzB,EAAI4B,OAAOT,EAAIV,EAAWI,EAAcY,GACxCzB,EAAI6B,IACFV,EAAIV,EAAWI,EACfY,EAAIZ,EACJA,GACC3B,KAAK4C,GAAK,EACX,GAEF9B,EAAI4B,OAAOT,EAAIV,EAAUgB,EAAIH,EAAYT,GACzCb,EAAI6B,IACFV,EAAIV,EAAWI,EACfY,EAAIH,EAAYT,EAChBA,EACA,EACA3B,KAAK4C,GAAK,GAEZ9B,EAAI4B,OAAOT,EAAIN,EAAcY,EAAIH,GACjCtB,EAAI6B,IACFV,EAAIN,EACJY,EAAIH,EAAYT,EAChBA,EACA3B,KAAK4C,GAAK,EACV5C,KAAK4C,IAEP9B,EAAI4B,OAAOT,EAAGM,EAAIZ,GAClBb,EAAI6B,IACFV,EAAIN,EACJY,EAAIZ,EACJA,EACA3B,KAAK4C,IACJ5C,KAAK4C,GAAK,GAEb9B,EAAI+B,YACJ/B,EAAIgC,UAKN,MAAMC,EAAejC,EAAIkC,qBAAqB,EAAG,EAAG,EAAG/B,GACvD8B,EAAaE,aAAa,EAAG,WAC7BF,EAAaE,aAAa,GAAK,WAC/BF,EAAaE,aAAa,EAAG,WAC7BnC,EAAIoC,YAAcH,EAClBjC,EAAIqC,UAAY,EAGhB,MAAMC,EAAUnC,EAAS,EACzBH,EAAI0B,YACJ1B,EAAI2B,OAAOnB,EAAW,GACtBR,EAAI4B,OAAOpB,EAAW8B,EAAU,GAChCtC,EAAIuC,SAGJvC,EAAIwC,OAGJxC,EAAI0B,YACJ1B,EAAI6B,IAAIrB,EAAW8B,EAAS,EAAG,EAAa,EAAVpD,KAAK4C,IACvC9B,EAAIyC,OAGJ,MAAMC,EAAW1C,EAAIkC,qBACnB1B,EAAY,EACZ8B,EAAU,EACV9B,EAAY,EACZ8B,EAAU,GAEZI,EAASP,aAAa,EAAG,WACzBO,EAASP,aAAa,GAAK,WAC3BO,EAASP,aAAa,EAAG,WACzBnC,EAAIwB,UAAYkB,EAChB1C,EAAIgC,OAGJhC,EAAI2C,UAGJ3C,EAAI0B,YACJ1B,EAAI6B,IAAIrB,EAAW8B,EAAS,EAAG,EAAa,EAAVpD,KAAK4C,IACvC9B,EAAIwB,UAAY,UAChBxB,EAAIgC,OAEJhE,QAAQI,IAAI,WAGRwE,EAAaA,KACb5F,EAAUO,MACZsF,IAEAC,KAIEA,EAAaC,IACjB/F,EAAUO,OAAQ,EAClBX,EAAMW,MAAMyF,IAAM1G,EAAM4B,SACxBtB,EAAMW,MAAMN,YACM,qBAAT8F,EAAuBA,EAAO9F,EAAYM,MACnDX,EAAMW,MAAM0F,OAAOC,OAAOC,GAAMnF,QAAQD,MAAM,QAASoF,MAGnDN,EAAaA,KACjBjG,EAAMW,MAAM6F,QACZpG,EAAUO,OAAQ,GAGd8F,EAAaA,KACjBpG,EAAYM,MAAQX,EAAMW,MAAMN,YAChC8C,KAGIuD,EAAaA,KACjBtG,EAAUO,OAAQ,EAClBN,EAAYM,MAAQ,EACpBwC,KAGIwD,EAAqBC,IACzB,MAAMC,EAAO9G,EAAOY,MAAMmG,wBACpBvC,EAAIqC,EAAMG,QAAUF,EAAKpD,KACzBuD,EAAUzC,EAAIxE,EAAOY,MAAM6B,MACjCnC,EAAYM,MAAQqG,EAAU1G,EAASK,MACnCP,EAAUO,QACZsF,IACAC,KAEF/C,KAGIjC,EAAeA,KACnB,GAAKnB,EAAOY,OAAUf,EAAae,OAAUb,EAAWa,MAKxD,IACE,MAAMsG,EACJrH,EAAae,MAAMuG,YAAcpH,EAAWa,MAAMuG,YAAc,GAClEnH,EAAOY,MAAM6B,MAAQF,KAAKU,IAAIiE,EAAa,GAC3ClH,EAAOY,MAAM4C,OAAS/C,EAAaG,MAG/BJ,EAAaI,OAASJ,EAAaI,MAAM+B,OAAS,GACpDS,GAEJ,CAAE,MAAOhC,GACPC,QAAQD,MAAM,kBAAmBA,EACnC,MAhBEC,QAAQG,KAAK,4B,OA0BjB4F,EAAAA,EAAAA,KACE,IAAMzH,EAAM4B,WACX8F,IACKA,IACFhG,QAAQI,IAAI,gBACZH,SAKNgG,EAAAA,EAAAA,KAAU3G,UACRU,QAAQI,IAAI,oBACNf,KAGNQ,EAAAA,EAAAA,KAASP,UACHhB,EAAM4B,gBACFD,IAERT,OAAO0G,iBAAiB,SAAUpG,UAItCqG,EAAAA,EAAAA,KAAgB,KACVtH,EAAaU,OACfV,EAAaU,MAAM6G,QAErB5G,OAAO6G,oBAAoB,SAAUvG,M,8dClYvC,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,83JCyYA,MAAMwG,GAASC,EAAAA,EAAAA,MACTC,GAAQC,EAAAA,EAAAA,MACRC,GAAKjI,EAAAA,EAAAA,IAAI+H,EAAMG,OAAOD,IACtBE,GAAYnI,EAAAA,EAAAA,IAAI,CACpBoI,KAAML,EAAMM,MAAMD,KAClBE,OAAQC,UAAUR,EAAMM,MAAMC,QAAU,IACxCE,KAAMD,UAAUR,EAAMM,MAAMG,OAAS,QAIjC,SAAEC,IAAaC,EAAAA,EAAAA,KAGfC,GAAc3I,EAAAA,EAAAA,IAAI,IAClB4I,GAAkB5I,EAAAA,EAAAA,IAAI,IACtB6I,GAAqB7I,EAAAA,EAAAA,IAAI,IACzB8I,GAAoB9I,EAAAA,EAAAA,IAAI,IACxB+I,GAAgB/I,EAAAA,EAAAA,KAAI,GAEpBgJ,GAAaC,EAAAA,EAAAA,IAAS,CAC1BC,QAAS,GACTC,UAAWC,MAAQC,QAAQ,OAAOC,OAAO,uBACzCC,QAASH,MAAQE,OAAO,yBAEpBE,IAAcxJ,EAAAA,EAAAA,IAAI,IAClByJ,IAAoBR,EAAAA,EAAAA,IAAS,CACjCS,OAAQ,EACRC,SAAU,GACVC,SAAS,EACTC,SAAS,EACTC,MAAO,IAEHC,IAAad,EAAAA,EAAAA,IAAS,CAC1Be,QAAS,GACTC,kBAAmB,IACnBC,iBAAkB,IAClBC,iBAAkB,IAClBC,WAAY,IACZC,iBAAkB,IAClBC,aAAc,MAEVC,IAAWtB,EAAAA,EAAAA,IAAS,CACxBE,UAAW,GACXI,QAAS,GACTiB,SAAU,KAENC,IAAWzK,EAAAA,EAAAA,IAAI,IACfyB,IAAWzB,EAAAA,EAAAA,IAAI,IAEf0K,KADa1K,EAAAA,EAAAA,IAAI,KACHA,EAAAA,EAAAA,IAAI,CAAC,IACnB2K,IAAc3K,EAAAA,EAAAA,IAAI,MAGlB4K,IAAa5K,EAAAA,EAAAA,IAAI,IACjB6K,IAAa7K,EAAAA,EAAAA,IAAI,IAGjB8K,GAAmBjK,UACvB,IACE,MAAMkK,QAAYC,EAAAA,EAAAA,MAClB,GAAmB,QAAfD,EAAIE,QAAoBF,EAAIG,KAAM,CACpCN,GAAW9J,MAAQiK,EAAIG,KAGvB,MAAMC,EAAU,GAChBJ,EAAIG,KAAKzG,SAAS2G,IACZA,EAAMC,UAAYD,EAAMC,SAASxI,QACnCuI,EAAMC,SAAS5G,SAAS6G,IACtBH,EAAQ9H,KAAK,IACRiI,EACHC,UAAWH,EAAMI,sBAKzBX,GAAW/J,MAAQqK,CACrB,MACEM,EAAAA,GAAUnK,MAAMyJ,EAAIW,MAAQ,WAEhC,CAAE,MAAOpK,GACPC,QAAQD,MAAM,WAAYA,EAC5B,GAIIqK,GAAiBC,IACjBA,EAAM5B,UAAY/B,EAAGnH,OAEzB+G,EAAOxE,KAAK,CACVwI,KAAM,iBAAmBD,EAAM5B,QAC/B3B,MAAO,CAAED,KAAMwD,EAAME,KAAMxD,OAAQsD,EAAMG,YAKvCC,GAAiBnL,MAAOoL,GAAiB,KAC7C,IAEE,MAAMlB,QAAYmB,EAAAA,EAAAA,IAAiBjE,EAAGnH,OACnB,QAAfiK,EAAIE,QAAoBF,EAAIG,KAC9BiB,OAAOC,OAAOrC,GAAYgB,EAAIG,MAE9BO,EAAAA,GAAUnK,MAAMyJ,EAAIW,MAAQ,oBAIxBW,KAGFJ,SACIK,IAEV,CAAE,MAAOhL,GACPC,QAAQD,MAAM,aAAcA,EAC9B,GAII+K,GAAoBxL,UACxB,IACE,MAAMkK,QAAYwB,EAAAA,EAAAA,IAAYtE,EAAGnH,OAEd,QAAfiK,EAAIE,OAEFF,EAAIG,MAAQH,EAAIG,KAAKsB,SAEvB7D,EAAY7H,MAAQ,CAACiK,EAAIG,MACzBuB,MAEA9D,EAAY7H,MAAQ,GAGtB2K,EAAAA,GAAUnK,MAAMyJ,EAAIW,MAAQ,YAEhC,CAAE,MAAOpK,GACPC,QAAQD,MAAM,YAAaA,EAC7B,GAIIoL,GAAiB7L,UACrB,IACE,MAAMkK,QAAY4B,EAAAA,EAAAA,IAAmBC,GAASC,UAC3B,QAAf9B,EAAIE,OACNP,GAAY5J,MAAQiK,EAAIG,MAAQ,CAAC,GAEjCR,GAAY5J,MAAQ,CAAC,EACrB2K,EAAAA,GAAUnK,MAAMyJ,EAAIW,MAAQ,YAEhC,CAAE,MAAOpK,GACPC,QAAQD,MAAM,WAAYA,EAC5B,GAGIgL,GAAwBzL,MAAOiM,GAAa,KAChD,GAAKC,QAKDD,GAAgBrD,GAAkBG,UAAWH,GAAkBI,SAInE,IACEJ,GAAkBI,SAAU,EAGvBiD,IACHrD,GAAkBC,OAAS,EAC3BD,GAAkBG,SAAU,EAC5BJ,GAAY1I,MAAQ,IAGtB,MAAMiK,QAAYiC,EAAAA,EAAAA,IAChBhE,EAAWG,UACXH,EAAWO,QACXtB,EAAGnH,MACHkI,EAAWE,QACXO,GAAkBC,OAClBD,GAAkBE,UAGpB,GAAmB,QAAfoB,EAAIE,QAAoBF,EAAIG,KAAM,CACpC,MAAM+B,EAAUlC,EAAIG,MAAQ,GAExB4B,EAEFtD,GAAY1I,MAAQ,IAAI0I,GAAY1I,SAAUmM,IAG9CzD,GAAY1I,MAAQmM,EACpBR,MAIFhD,GAAkBK,MAAQiB,EAAImC,UAAY,EAC1CzD,GAAkBG,SAAWH,GAAkBK,OAASN,GAAY1I,MAAM+B,QAAU4G,GAAkBK,MACtGL,GAAkBC,QAAU,CAC9B,MACE+B,EAAAA,GAAUnK,MAAMyJ,EAAIW,MAAQ,WAEhC,CAAE,MAAOpK,GACPC,QAAQD,MAAM,WAAYA,EAC5B,CAAE,QACAmI,GAAkBI,SAAU,CAC9B,GAII4C,GAAiBU,IACrB5L,QAAQI,IAAI,kCAAmCwL,GAE/C,IAAIC,EAAgBxE,EAAgB9H,MAChC8L,EAAUO,EA6Bd,GA1BKP,IAEChE,EAAgB9H,QAElB8L,EAAUjE,EAAY7H,MAAMuM,MAAMC,GAAMA,EAAEd,UAAY5D,EAAgB9H,QAGjE8L,IACHA,EAAUpD,GAAY1I,MAAMuM,MAAMC,GAAMA,EAAEd,UAAY5D,EAAgB9H,UAKrE8L,IAECjE,EAAY7H,MAAM+B,OAAS,EAC7B+J,EAAUjE,EAAY7H,MAAM,GAGrB0I,GAAY1I,MAAM+B,OAAS,IAClC+J,EAAUpD,GAAY1I,MAAM,OAM7B8L,EAEH,YADAW,KAKF,MAAMC,EAAoB7E,EAAY7H,MAAM2M,MAAMH,GAAMA,EAAEd,UAAYI,EAAQJ,UAM9E,GALA1D,EAAkBhI,MAAQ0M,EAAoB,IAAM,IACpD5E,EAAgB9H,MAAQ8L,EAAQJ,QAChC3D,EAAmB/H,MAAQ8L,EAAQc,WAG/BN,IAAkBR,EAAQJ,SAAWgB,EAAmB,CAE1Dd,GAAeE,GAEf,IAWE,GATIQ,IAAkBR,EAAQJ,UAC5BjC,GAASpB,UAAY,GACrBoB,GAAShB,QAAU,GACnBgB,GAASC,SAAW,GACpBC,GAAS3J,MAAQ,GACjBW,GAASX,MAAQ,IAIf8L,EAAQe,aAAc,CACxB,IAAIC,EAAc,GAClB,IACEA,EAAcC,KAAKC,MAAMlB,EAAQe,aACnC,CAAE,MAAOjH,GACPnF,QAAQD,MAAM,WAAYoF,EAC5B,CAiBA,GAdA6D,GAASpB,UAAYyD,EAAQmB,WAC7BxD,GAAShB,QAAUqD,EAAQoB,SAC3BzD,GAASC,SAAWoC,EAAQqB,WAAa,GAGzCxD,GAAS3J,MAAQ8M,EAAYtJ,KAAK4J,IACzB,CACLC,KAAMD,EAAQE,SACdC,UAAWC,GAAWJ,EAAQG,WAC9BE,QAASL,EAAQM,QAKjB5B,EAAQ6B,YAAa,CACvB,IAAIC,EAAM,IAAIC,IAAI,yBAA0B5N,OAAO6N,SAASC,QAC5D,MAAMxG,EAAQ,CACZyG,YAAalC,EAAQ6B,YACrBM,UAAWnC,EAAQJ,SAErBkC,EAAIM,OAAS,IAAIC,gBAAgB5G,GAAO6G,WACxCzN,GAASX,MAAQ4N,EAAIQ,UACvB,MACEzN,GAASX,MAAQ,KAGnBqO,IACF,CACF,CAAE,MAAO7N,GACPC,QAAQD,MAAM,WAAYA,EAC5B,CACF,GAIIiM,GAAcA,KAClB3E,EAAgB9H,MAAQ,GACxB+H,EAAmB/H,MAAQ,GAC3ByJ,GAASpB,UAAY,GACrBoB,GAAShB,QAAU,GACnBgB,GAASC,SAAW,GACpBC,GAAS3J,MAAQ,GACjBW,GAASX,MAAQ,IAIbsO,GAAoBA,KACxBpG,EAAWE,QAAU,GACrBF,EAAWG,UAAY,KACvBH,EAAWO,QAAU,MAIjB8F,GAAqBA,KACzB9N,QAAQI,IAAI,sBACZoH,EAAcjI,OAAQ,GAIlBiM,GAAaA,KACjB,GAA6B,OAAzB/D,EAAWG,WAA6C,OAAvBH,EAAWO,QAE9C,OADAkC,EAAAA,GAAU6D,QAAQ,YACX,EAET,GAA6B,OAAzBtG,EAAWG,WAA6C,OAAvBH,EAAWO,QAE9C,OADAkC,EAAAA,GAAU6D,QAAQ,YACX,EAET,GAA6B,OAAzBtG,EAAWG,WAA6C,OAAvBH,EAAWO,QAAkB,CAChE,MAAMgG,EAAQ,IAAIC,KAAKxG,EAAWG,WAAWsG,UACvCC,EAAQ,IAAIF,KAAKxG,EAAWO,SAASkG,UAC3C,GAAIF,EAAQG,EAEV,OADAjE,EAAAA,GAAU6D,QAAQ,eACX,CAEX,CACA,OAAO,GAIHhB,GAAcD,IAClB,IAAKA,EAAW,MAAO,GAQvB,GALyB,kBAAdA,GAA2BA,EAAUsB,SAAS,OACvDtB,EAAYuB,OAAOvB,IAII,kBAAdA,GAA0BA,EAAUsB,SAAS,KACtD,OAAOtB,EAGT,MAAMwB,EAAO,IAAIL,KAAKnB,GAChByB,EAAOD,EAAKE,cACZC,EAAQC,OAAOJ,EAAKK,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOJ,EAAKQ,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOJ,EAAKc,cAAcR,SAAS,EAAG,KACtD,MAAO,GAAGL,KAAQE,KAASI,KAAOE,KAASE,KAAWE,KAIlDE,IAAeC,EAAAA,EAAAA,KACnB,MACEpF,EAAAA,EAAAA,IAAU,CACRqF,QAAS,OACTtI,KAAM,YAGRzH,OAAOgQ,KAAK,wEAA0EnI,EAAgB9H,MAAO,YAE/G,KACA,GAWIkQ,GAAeC,IACnB,IAAKA,EAAM,MAAO,GAClB,IAAIC,EAAUD,EAAKE,QAAQ,MAAO,QAClC,OAAOD,GAGHE,IAAcpR,EAAAA,EAAAA,IAAI,MAClBqR,IAAuBrR,EAAAA,EAAAA,IAAI,MAG3BmP,GAAiBA,MACrB/N,EAAAA,EAAAA,KAAS,KACP,IAAIkQ,EAASF,GAAYtQ,MACzBwQ,GAAUA,EAAOC,SAAS,CAAEC,IAAKF,EAAOG,mBAKtCC,IAAsBb,EAAAA,EAAAA,KAAS,KACnC,MAAMc,EAAYN,GAAqBvQ,MACvC,IAAK6Q,EAAW,OAEhB,MAAM,UAAEC,EAAS,aAAEH,EAAY,aAAEI,GAAiBF,EAE9CC,EAAYC,GAAgBJ,EAAe,IAC7CnF,IAAsB,KAEvB,KAGH,IAAIwF,GAAQ,KAGZ,MAAMC,GAAmBA,KACnBD,IACFE,cAAcF,IAEhBA,GAAQG,aAAY,KAClBjG,IAAe,KACd,MAICkG,GAAkBA,KAClBJ,KACFE,cAAcF,IACdA,GAAQ,O,OAKZtK,EAAAA,EAAAA,KAAU,KACRwE,IAAe,GACflB,KAGAiH,SAIFI,EAAAA,EAAAA,IAAY,KACVnG,IAAe,GACf+F,SAIFK,EAAAA,EAAAA,KAAc,KACZF,SAIFG,EAAAA,EAAAA,KAAY,KACVH,Q,6vOCt2BF,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,S", "sources": ["webpack://overall-monitordata-vue/./src/views/callMonitor/components/FullscreenToggle.vue", "webpack://overall-monitordata-vue/./src/views/callMonitor/components/FullscreenToggle.vue?74fd", "webpack://overall-monitordata-vue/./src/views/callMonitor/components/AudioPlayer.vue", "webpack://overall-monitordata-vue/./src/views/callMonitor/components/AudioPlayer.vue?474d", "webpack://overall-monitordata-vue/./src/views/callMonitor/index.vue", "webpack://overall-monitordata-vue/./src/views/callMonitor/index.vue?7304"], "sourcesContent": ["<template>\r\n  <div class=\"fullscreen-container\">\r\n    <!-- 使用作用域插槽，将全屏状态和方法传递给父组件 -->\r\n    <slot :isFullscreen=\"isFullscreen\" :toggle=\"toggleFullscreen\">\r\n      <!-- 默认插槽内容 -->\r\n      <button @click=\"toggleFullscreen\">\r\n        {{ isFullscreen ? '退出全屏' : '进入全屏' }}\r\n      </button>\r\n    </slot>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'FullscreenToggle',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    // 监听全屏状态变化\r\n    this.addFullscreenListeners()\r\n  },\r\n  beforeUnmount() {\r\n    // 移除事件监听\r\n    this.removeFullscreenListeners()\r\n  },\r\n  methods: {\r\n    addFullscreenListeners() {\r\n      document.addEventListener('fullscreenchange', this.handleFullscreenChange)\r\n      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)\r\n      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)\r\n      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)\r\n    },\r\n    removeFullscreenListeners() {\r\n      document.removeEventListener('fullscreenchange', this.handleFullscreenChange)\r\n      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)\r\n      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)\r\n      document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)\r\n    },\r\n    toggleFullscreen() {\r\n      if (!this.isFullscreen) {\r\n        this.enterFullscreen()\r\n      } else {\r\n        this.exitFullscreen()\r\n      }\r\n    },\r\n    enterFullscreen() {\r\n      const element = document.documentElement\r\n      if (element.requestFullscreen) {\r\n        element.requestFullscreen()\r\n      } else if (element.webkitRequestFullscreen) {\r\n        element.webkitRequestFullscreen()\r\n      } else if (element.mozRequestFullScreen) {\r\n        element.mozRequestFullScreen()\r\n      } else if (element.msRequestFullscreen) {\r\n        element.msRequestFullscreen()\r\n      }\r\n    },\r\n    exitFullscreen() {\r\n      if (document.exitFullscreen) {\r\n        document.exitFullscreen()\r\n      } else if (document.webkitExitFullscreen) {\r\n        document.webkitExitFullscreen()\r\n      } else if (document.mozCancelFullScreen) {\r\n        document.mozCancelFullScreen()\r\n      } else if (document.msExitFullscreen) {\r\n        document.msExitFullscreen()\r\n      }\r\n    },\r\n    handleFullscreenChange() {\r\n      // 检查当前是否处于全屏状态\r\n      this.isFullscreen = !!(\r\n        document.fullscreenElement ||\r\n        document.webkitFullscreenElement ||\r\n        document.mozFullScreenElement ||\r\n        document.msFullscreenElement\r\n      )\r\n    }\r\n  }\r\n}\r\n</script>", "import { render } from \"./FullscreenToggle.vue?vue&type=template&id=86d0540e\"\nimport script from \"./FullscreenToggle.vue?vue&type=script&lang=js\"\nexport * from \"./FullscreenToggle.vue?vue&type=script&lang=js\"\n\nimport exportComponent from \"../../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "`\r\n<template>\r\n  <div class=\"audioContent\" ref=\"audioContent\">\r\n    <div class=\"btnContent\" ref=\"btnContent\">\r\n      <div class=\"playBtn\" @click=\"togglePlay\">\r\n        <div class=\"iconPlay\" v-show=\"!isPlaying\"></div>\r\n        <div class=\"iconPause\" v-show=\"isPlaying\"></div>\r\n      </div>\r\n    </div>\r\n    <canvas class=\"canvasObj\" ref=\"canvas\" @click=\"handleCanvasClick\"></canvas>\r\n    <audio ref=\"audio\" @timeupdate=\"updateTime\" @ended=\"onAudioEnd\"></audio>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport {\r\n  ref,\r\n  onMounted,\r\n  onBeforeUnmount,\r\n  defineProps,\r\n  watch,\r\n  nextTick,\r\n} from \"vue\";\r\n\r\nconst props = defineProps({\r\n  audioSrc: {\r\n    type: String,\r\n    default: \"\",\r\n  },\r\n});\r\n\r\nconst audioContent = ref(null);\r\nconst btnContent = ref(null);\r\nconst canvas = ref(null);\r\nconst audio = ref(null);\r\nconst audioContext = ref(null);\r\nconst analyser = ref(null);\r\nconst buffer = ref(null);\r\nconst isPlaying = ref(false);\r\nconst currentTime = ref(0);\r\nconst duration = ref(0);\r\nconst waveformData = ref([]);\r\nconst canvasHeight = ref(72);\r\n\r\nconst initAudio = async () => {\r\n  try {\r\n    audioContext.value = new (window.AudioContext ||\r\n      window.webkitAudioContext)();\r\n    analyser.value = audioContext.value.createAnalyser();\r\n    analyser.value.fftSize = 2048;\r\n\r\n    // 确保在初始化后立即设置 canvas 尺寸\r\n    nextTick(() => {\r\n      handleResize();\r\n    });\r\n  } catch (error) {\r\n    console.error(\"初始化音频上下文失败:\", error);\r\n  }\r\n};\r\n\r\nconst loadAudio = async () => {\r\n  try {\r\n    if (!props.audioSrc) {\r\n      console.warn(\"音频源未提供\");\r\n      return;\r\n    }\r\n    if (!audioContext.value) {\r\n      await initAudio();\r\n    }\r\n\r\n    console.log(\"开始加载音频:\", props.audioSrc);\r\n    const response = await fetch(props.audioSrc);\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n    const arrayBuffer = await response.arrayBuffer();\r\n    console.log(\"音频数据已获取，开始解码\");\r\n\r\n    // 确保音频上下文是活跃的\r\n    if (audioContext.value.state === \"suspended\") {\r\n      await audioContext.value.resume();\r\n    }\r\n\r\n    buffer.value = await audioContext.value.decodeAudioData(arrayBuffer);\r\n    duration.value = buffer.value.duration;\r\n    console.log(\"音频解码完成，时长:\", duration.value);\r\n\r\n    // 使用 nextTick 确保 DOM 更新后再生成波形\r\n    nextTick(() => {\r\n      generateWaveformData();\r\n      handleResize();\r\n    });\r\n  } catch (error) {\r\n    console.error(\"加载音频失败:\", error);\r\n  }\r\n};\r\n\r\nconst generateWaveformData = () => {\r\n  if (!buffer.value) {\r\n    console.warn(\"没有音频数据可供生成波形\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    console.log(\"开始生成波形数据\");\r\n    const channelData = buffer.value.getChannelData(0);\r\n    // 增加采样点数量以获得更细腻的波形\r\n    const numberOfBars = Math.floor(canvas.value.width / 5); // 每5像素一个波形条\r\n    const samplesPerBar = Math.floor(channelData.length / numberOfBars);\r\n    const newWaveformData = [];\r\n\r\n    for (let i = 0; i < numberOfBars; i++) {\r\n      const start = i * samplesPerBar;\r\n      const end = Math.min(start + samplesPerBar, channelData.length);\r\n      let min = 1.0;\r\n      let max = -1.0;\r\n\r\n      for (let j = start; j < end; j++) {\r\n        const value = channelData[j];\r\n        min = Math.min(min, value);\r\n        max = Math.max(max, value);\r\n      }\r\n\r\n      newWaveformData.push({ min, max });\r\n    }\r\n\r\n    waveformData.value = newWaveformData;\r\n    console.log(\"波形数据生成完成，数据点数:\", waveformData.value.length);\r\n\r\n    nextTick(() => {\r\n      drawWaveform();\r\n    });\r\n  } catch (error) {\r\n    console.error(\"生成波形数据失败:\", error);\r\n  }\r\n};\r\n\r\nconst drawWaveform = () => {\r\n  if (!canvas.value || !waveformData.value.length) {\r\n    console.warn(\"无法绘制波形：canvas未就绪或没有波形数据\");\r\n    return;\r\n  }\r\n\r\n  console.log(\"开始绘制波形\");\r\n  const ctx = canvas.value.getContext(\"2d\");\r\n  if (!ctx) {\r\n    console.error(\"无法获取canvas上下文\");\r\n    return;\r\n  }\r\n\r\n  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);\r\n\r\n  const margin = {\r\n    left: 10,\r\n    right: 10,\r\n  };\r\n  const width = canvas.value.width - margin.left - margin.right;\r\n  const height = canvas.value.height;\r\n  const centerY = height / 2;\r\n\r\n  // 计算进度位置\r\n  const progressX = Math.max(\r\n    margin.left,\r\n    Math.min(\r\n      canvas.value.width - margin.right,\r\n      margin.left + (currentTime.value / duration.value) * width\r\n    )\r\n  );\r\n\r\n  // 设置波形参数\r\n  const barWidth = 3; // 每个波形条的宽度\r\n  const barGap = 2; // 波形条之间的间隔\r\n  const barSpacing = barWidth + barGap; // 总间距\r\n  const maxBarHeight = Math.floor(height * 0.6); // 最大波形高度为容器高度的60%\r\n  const cornerRadius = barWidth / 2; // 圆角半径\r\n\r\n  // 找出所有波形数据中的最大振幅，用于归一化\r\n  const maxAmplitude = Math.max(\r\n    ...waveformData.value.map((point) => Math.abs(point.max - point.min))\r\n  );\r\n\r\n  // 绘制波形\r\n  waveformData.value.forEach((point, i) => {\r\n    const x = margin.left + i * barSpacing;\r\n\r\n    // 计算波形高度（归一化后乘以最大高度）\r\n    const amplitude = Math.abs(point.max - point.min);\r\n    const normalizedHeight = (amplitude / maxAmplitude) * maxBarHeight;\r\n    const barHeight = Math.max(4, normalizedHeight); // 最小高度设为4px以确保圆角效果\r\n\r\n    // 确定是否在播放进度之前\r\n    const isPlayed = x <= progressX;\r\n\r\n    // 设置渐变色\r\n    if (isPlayed) {\r\n      // 已播放部分（亮蓝色）\r\n      ctx.fillStyle = \"#00feff\";\r\n    } else {\r\n      // 未播放部分（深蓝色）\r\n      ctx.fillStyle = \"#003790\";\r\n    }\r\n\r\n    // 绘制圆角波形条\r\n    const y = centerY - barHeight / 2;\r\n\r\n    // 开始绘制圆角矩形\r\n    ctx.beginPath();\r\n    ctx.moveTo(x + cornerRadius, y);\r\n    ctx.lineTo(x + barWidth - cornerRadius, y);\r\n    ctx.arc(\r\n      x + barWidth - cornerRadius,\r\n      y + cornerRadius,\r\n      cornerRadius,\r\n      -Math.PI / 2,\r\n      0\r\n    );\r\n    ctx.lineTo(x + barWidth, y + barHeight - cornerRadius);\r\n    ctx.arc(\r\n      x + barWidth - cornerRadius,\r\n      y + barHeight - cornerRadius,\r\n      cornerRadius,\r\n      0,\r\n      Math.PI / 2\r\n    );\r\n    ctx.lineTo(x + cornerRadius, y + barHeight);\r\n    ctx.arc(\r\n      x + cornerRadius,\r\n      y + barHeight - cornerRadius,\r\n      cornerRadius,\r\n      Math.PI / 2,\r\n      Math.PI\r\n    );\r\n    ctx.lineTo(x, y + cornerRadius);\r\n    ctx.arc(\r\n      x + cornerRadius,\r\n      y + cornerRadius,\r\n      cornerRadius,\r\n      Math.PI,\r\n      -Math.PI / 2\r\n    );\r\n    ctx.closePath();\r\n    ctx.fill();\r\n  });\r\n\r\n  // 绘制进度指示器\r\n  // 粉色竖线\r\n  const lineGradient = ctx.createLinearGradient(0, 0, 0, height);\r\n  lineGradient.addColorStop(0, \"#ff89b7\");\r\n  lineGradient.addColorStop(0.5, \"#ee4586\");\r\n  lineGradient.addColorStop(1, \"#dd0559\");\r\n  ctx.strokeStyle = lineGradient;\r\n  ctx.lineWidth = 2;\r\n\r\n  // 绘制从顶部到圆形指示器顶部的线\r\n  const markerY = height - 8;\r\n  ctx.beginPath();\r\n  ctx.moveTo(progressX, 0);\r\n  ctx.lineTo(progressX, markerY - 8); // 停在圆形指示器上方\r\n  ctx.stroke();\r\n\r\n  // 保存当前上下文状态\r\n  ctx.save();\r\n\r\n  // 创建圆形裁剪区域以确保渐变只在圆内显示\r\n  ctx.beginPath();\r\n  ctx.arc(progressX, markerY, 8, 0, Math.PI * 2);\r\n  ctx.clip();\r\n\r\n  // 绘制粉色大圆背景\r\n  const bgColor1 = ctx.createLinearGradient(\r\n    progressX - 8,\r\n    markerY - 8,\r\n    progressX + 8,\r\n    markerY + 8\r\n  );\r\n  bgColor1.addColorStop(0, \"#d70156\");\r\n  bgColor1.addColorStop(0.5, \"#aa105e\");\r\n  bgColor1.addColorStop(1, \"#c60358\");\r\n  ctx.fillStyle = bgColor1;\r\n  ctx.fill();\r\n\r\n  // 恢复上下文状态\r\n  ctx.restore();\r\n\r\n  // 绘制白色小圆\r\n  ctx.beginPath();\r\n  ctx.arc(progressX, markerY, 3, 0, Math.PI * 2);\r\n  ctx.fillStyle = \"#ffffff\";\r\n  ctx.fill();\r\n\r\n  console.log(\"波形绘制完成\");\r\n};\r\n\r\nconst togglePlay = () => {\r\n  if (isPlaying.value) {\r\n    pauseAudio();\r\n  } else {\r\n    playAudio();\r\n  }\r\n};\r\n\r\nconst playAudio = (time) => {\r\n  isPlaying.value = true;\r\n  audio.value.src = props.audioSrc;\r\n  audio.value.currentTime =\r\n    typeof time !== \"undefined\" ? time : currentTime.value;\r\n  audio.value.play().catch((e) => console.error(\"播放失败:\", e));\r\n};\r\n\r\nconst pauseAudio = () => {\r\n  audio.value.pause();\r\n  isPlaying.value = false;\r\n};\r\n\r\nconst updateTime = () => {\r\n  currentTime.value = audio.value.currentTime;\r\n  drawWaveform();\r\n};\r\n\r\nconst onAudioEnd = () => {\r\n  isPlaying.value = false;\r\n  currentTime.value = 0;\r\n  drawWaveform();\r\n};\r\n\r\nconst handleCanvasClick = (event) => {\r\n  const rect = canvas.value.getBoundingClientRect();\r\n  const x = event.clientX - rect.left;\r\n  const percent = x / canvas.value.width;\r\n  currentTime.value = percent * duration.value;\r\n  if (isPlaying.value) {\r\n    pauseAudio();\r\n    playAudio();\r\n  }\r\n  drawWaveform();\r\n};\r\n\r\nconst handleResize = () => {\r\n  if (!canvas.value || !audioContent.value || !btnContent.value) {\r\n    console.warn(\"handleResize: DOM 元素未就绪\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const canvasWidth =\r\n      audioContent.value.clientWidth - btnContent.value.clientWidth - 16;\r\n    canvas.value.width = Math.max(canvasWidth, 0);\r\n    canvas.value.height = canvasHeight.value;\r\n\r\n    // 只有在有波形数据时才重新绘制\r\n    if (waveformData.value && waveformData.value.length > 0) {\r\n      drawWaveform();\r\n    }\r\n  } catch (error) {\r\n    console.error(\"调整 canvas 尺寸失败:\", error);\r\n  }\r\n};\r\n\r\n// const formatTime = (seconds) => {\r\n//   const minutes = Math.floor(seconds / 60);\r\n//   seconds = Math.floor(seconds % 60);\r\n//   return `${minutes}:${seconds < 10 ? \"0\" : \"\"}${seconds}`;\r\n// };\r\n\r\n// 添加 watch 来监听 audioSrc 的变化\r\nwatch(\r\n  () => props.audioSrc,\r\n  (newSrc) => {\r\n    if (newSrc) {\r\n      console.log(\"音频源发生变化，重新加载\");\r\n      loadAudio();\r\n    }\r\n  }\r\n);\r\n\r\nonMounted(async () => {\r\n  console.log(\"组件挂载，初始化音频\");\r\n  await initAudio();\r\n\r\n  // 确保在组件完全挂载后再加载音频\r\n  nextTick(async () => {\r\n    if (props.audioSrc) {\r\n      await loadAudio();\r\n    }\r\n    window.addEventListener(\"resize\", handleResize);\r\n  });\r\n});\r\n\r\nonBeforeUnmount(() => {\r\n  if (audioContext.value) {\r\n    audioContext.value.close();\r\n  }\r\n  window.removeEventListener(\"resize\", handleResize);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.audioContent {\r\n  display: flex;\r\n  width: 100%;\r\n  min-height: 72px;\r\n  position: relative;\r\n\r\n  .btnContent {\r\n    flex-shrink: 0;\r\n    align-items: center;\r\n    .playBtn {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 40px;\r\n      background: linear-gradient(134deg, #00dcaa -1%, #00aaff 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin: 14px 8px 0 0px;\r\n      cursor: pointer;\r\n      &:hover {\r\n        opacity: 0.8;\r\n      }\r\n      .iconPlay,\r\n      .iconPause {\r\n        width: 20px;\r\n        height: 20px;\r\n      }\r\n      .iconPlay {\r\n        background: url(\"@/assets/image/icon_play.svg\") no-repeat center center;\r\n        background-size: 100% 100%;\r\n      }\r\n      .iconPause {\r\n        background: url(\"@/assets/image/icon_pause.svg\") no-repeat center\r\n          center;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .canvasObj {\r\n    flex: 1;\r\n    cursor: pointer;\r\n    height: 72px;\r\n    display: block;\r\n    min-width: 100px;\r\n  }\r\n}\r\n</style>\r\n`\r\n", "import script from \"./AudioPlayer.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./AudioPlayer.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./AudioPlayer.vue?vue&type=style&index=0&id=7dc40402&lang=scss&scoped=true\"\n\nimport exportComponent from \"../../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7dc40402\"]])\n\nexport default __exports__", "<template>\r\n  <div\r\n    class=\"container\"\r\n    :class=\"{ 'iframe-mode': isIframe }\">\r\n    <div class=\"containerLeft\">\r\n      <div class=\"staff-info\">\r\n        <el-avatar\r\n          :size=\"56\"\r\n          :src=\"staffInfo.avatar || IconKefu\"\r\n          @error=\"staffInfo.avatar = IconKefu\"\r\n          style=\"flex: 0 0 56px; border: 1px solid #00ffff\" />\r\n        <div class=\"info\">\r\n          <el-dropdown\r\n            trigger=\"click\"\r\n            popper-class=\"agent-dropdown\">\r\n            <div class=\"name fontStyle\">\r\n              {{ staffInfo.name }}\r\n              <el-image\r\n                :src=\"ArrowDown\"\r\n                class=\"arrow-down\"></el-image>\r\n            </div>\r\n            <template #dropdown>\r\n              <el-dropdown-menu>\r\n                <div\r\n                  class=\"dropdown-group-title\"\r\n                  v-for=\"group in workGroups\"\r\n                  :key=\"group.workGroupId\">\r\n                  <!-- <span>{{ group.workGroupName }}</span> -->\r\n                  <el-dropdown-item\r\n                    v-for=\"member in group.userList\"\r\n                    :key=\"member.AGENTID\"\r\n                    @click=\"switchToAgent(member)\">\r\n                    {{ member.NAME }} {{ member.AGENTID }}\r\n                  </el-dropdown-item>\r\n                </div>\r\n              </el-dropdown-menu>\r\n            </template>\r\n          </el-dropdown>\r\n          <div class=\"id\">工号：{{ id }}</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"realTimeContent\"\r\n        v-if=\"sessionList.length > 0\">\r\n        <ul class=\"callList\">\r\n          <li\r\n            v-for=\"item in sessionList\"\r\n            :key=\"item.CALL_ID\"\r\n            :class=\"{ active: item.CALL_ID === activeSessionId }\"\r\n            @click=\"sessionChange(item)\">\r\n            <div class=\"liContent\">\r\n              <i class=\"iconCall\"></i>\r\n              <div class=\"tel\">\r\n                {{ item.CUST_PHONE }}\r\n                <div class=\"label\"><span class=\"txt\">通话中</span></div>\r\n              </div>\r\n              <div class=\"callTime\">来电时间：{{ item.START_TIME }}</div>\r\n              <div class=\"callType\"><i class=\"iconArrow\"></i>呼入</div>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"historyContent\">\r\n        <div class=\"historyHead\">\r\n          <i class=\"iconTitle\"></i>\r\n          <span class=\"title\">历史通话</span>\r\n          <div class=\"filterContent\">\r\n            <el-popover\r\n              placement=\"top-end\"\r\n              trigger=\"manual\"\r\n              popper-class=\"el-popover-custom\"\r\n              :visible=\"filterVisible\">\r\n              <template #reference>\r\n                <div\r\n                  class=\"filterBtn\"\r\n                  @click=\"filterVisible = !filterVisible\">\r\n                  <div class=\"iconFilter\"></div>\r\n                </div>\r\n              </template>\r\n\r\n              <div\r\n                class=\"filterPopoverContent\"\r\n                v-click-outside=\"handleClickOutside\">\r\n                <div class=\"filterPopoverItem\">\r\n                  <el-input\r\n                    class=\"visitId\"\r\n                    v-model=\"filterForm.keyWord\"\r\n                    placeholder=\"请输入\"></el-input>\r\n                  <el-button\r\n                    class=\"btnReset\"\r\n                    @click=\"handleFilterReset\">\r\n                    <i class=\"iconReset\"></i>\r\n                    <span class=\"txt\">重置</span>\r\n                  </el-button>\r\n                  <el-button\r\n                    class=\"btnSearch\"\r\n                    @click=\"getHistorySessionList(false)\">\r\n                    <i class=\"iconSearch\"></i>\r\n                    <span class=\"txt\">搜索</span>\r\n                  </el-button>\r\n                </div>\r\n                <div class=\"filterPopoverItem\">\r\n                  <div class=\"attr\">开始时间</div>\r\n                  <el-date-picker\r\n                    v-model=\"filterForm.startTime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"请输入\"\r\n                    format=\"YYYY-MM-DD HH:mm:ss\"\r\n                    value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n                    :default-time=\"new Date(2000, 1, 1, 0, 0, 0)\"\r\n                    :prefix-icon=\"Calendar\"\r\n                    popper-class=\"el-picker-panel-custom\">\r\n                  </el-date-picker>\r\n                </div>\r\n                <div class=\"filterPopoverItem\">\r\n                  <div class=\"attr\">结束时间</div>\r\n                  <el-date-picker\r\n                    v-model=\"filterForm.endTime\"\r\n                    type=\"datetime\"\r\n                    placeholder=\"请输入\"\r\n                    format=\"YYYY-MM-DD HH:mm:ss\"\r\n                    value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n                    :default-time=\"new Date(2000, 1, 1, 23, 59, 59)\"\r\n                    :prefix-icon=\"Calendar\"\r\n                    popper-class=\"el-picker-panel-custom\">\r\n                  </el-date-picker>\r\n                </div>\r\n              </div>\r\n            </el-popover>\r\n          </div>\r\n        </div>\r\n        <ul\r\n          class=\"callList\"\r\n          ref=\"historyListContainer\"\r\n          @scroll=\"handleHistoryScroll\">\r\n          <li\r\n            v-for=\"item in historyList\"\r\n            :key=\"item.CALL_ID\"\r\n            :class=\"{ active: item.CALL_ID === activeSessionId }\"\r\n            @click=\"sessionChange(item)\">\r\n            <div class=\"historyCallTime\">{{ item.START_TIME }}</div>\r\n            <div class=\"liContent\">\r\n              <i class=\"iconCall\"></i>\r\n              <div class=\"tel\">{{ item.CUST_PHONE }}</div>\r\n              <div class=\"callTime\">持续时间：{{ formatSeconds(item.CALL_TIME) }}</div>\r\n              <div class=\"callType\"><i class=\"iconArrow\"></i>呼入</div>\r\n            </div>\r\n          </li>\r\n          <!-- 加载更多状态 -->\r\n          <li\r\n            v-if=\"historyPagination.loading\"\r\n            class=\"loading-item\">\r\n            <div class=\"loading-content\">\r\n              <i class=\"loading-icon\"></i>\r\n              <span>加载中...</span>\r\n            </div>\r\n          </li>\r\n          <!-- 没有更多数据提示 -->\r\n          <li\r\n            v-else-if=\"historyList.length > 0 && !historyPagination.hasMore\"\r\n            class=\"no-more-item\">\r\n            <div class=\"no-more-content\">\r\n              <span>没有更多数据了</span>\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n    <div class=\"containerRight\">\r\n      <div class=\"statistics\">\r\n        <div class=\"statisticsList\">\r\n          <div class=\"infoItem infoItem1\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">{{ staffInfo.type === '回访' ? '呼入量' : '接听量' }}</div>\r\n              <div class=\"num\">{{ (staffInfo.type === '回访' ? statistics.CALL_OUT_SUCC_COUNT : statistics.CALL_IN_COUNT_ALL) || 0 }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"infoItem infoItem2\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">平均通话时长</div>\r\n              <div class=\"num\">{{ formatSeconds(staffInfo.type === '回访' ? statistics.AVG_CALL_OUT_TIME : statistics.AVG_CALL_IN_TIME) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"infoItem infoItem3\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">平均话后时长</div>\r\n              <div class=\"num\">{{ formatSeconds(statistics.AVG_ARRANGE_TIME) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"infoItem infoItem4\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">签入总时长</div>\r\n              <div class=\"num\">{{ formatSeconds(statistics.LOGIN_TIME) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"infoItem infoItem5\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">{{ staffInfo.type === '回访' ? '呼入总时长' : '接听总时长' }}</div>\r\n              <div class=\"num\">{{ formatSeconds(staffInfo.type === '回访' ? statistics.CALL_OUT_TIME_ALL : statistics.CALL_IN_TIME_ALL) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"infoItem infoItem6\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"info\">\r\n              <div class=\"attr\">满意率</div>\r\n              <div class=\"num\">{{ (statistics.GOOD_PERCENT || 0) + '%' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"fullScreen\">\r\n          <div\r\n            class=\"fullscreen-container\"\r\n            @click=\"router.push('/')\">\r\n            <div class=\"fullScreenBtn\">\r\n              <div class=\"iconFullScreen\"></div>\r\n              <div class=\"txt\">返回</div>\r\n            </div>\r\n          </div>\r\n          <!-- <fullscreen-toggle v-slot=\"{ isFullscreen, toggle }\">\r\n            <div\r\n              class=\"fullScreenBtn\"\r\n              :class=\"{ fullScreenBtn2: isFullscreen }\"\r\n              @click=\"toggle\">\r\n              <div\r\n                class=\"iconFullScreen\"\r\n                :class=\"{ iconFullScreen2: isFullscreen }\"></div>\r\n              <div class=\"txt\">{{ isFullscreen ? '缩放' : '全屏' }}</div>\r\n            </div>\r\n          </fullscreen-toggle> -->\r\n        </div>\r\n      </div>\r\n      <div class=\"mainContent\">\r\n        <div class=\"panel callInfo\">\r\n          <div class=\"panelHead\">\r\n            <div class=\"title\">\r\n              对话详情<span\r\n                class=\"tel\"\r\n                v-if=\"activeSessionPhone\"\r\n                >（{{ activeSessionPhone }}）</span\r\n              >\r\n            </div>\r\n            <el-button\r\n              class=\"btnExport\"\r\n              @click=\"handleExport\"\r\n              v-if=\"activeSessionType === '1'\">\r\n              <div class=\"icon\"></div>\r\n              <div class=\"txt\">导出</div>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"panelBody\">\r\n            <div class=\"timeInfo\">\r\n              <div class=\"timeItem\">\r\n                <div class=\"attr\">开始时间：</div>\r\n                <div class=\"time\">{{ chatInfo.startTime.length > 0 ? chatInfo.startTime : '--' }}</div>\r\n              </div>\r\n              <div class=\"timeItem\">\r\n                <div class=\"attr\">结束时间：</div>\r\n                <div class=\"time\">{{ chatInfo.endTime.length > 0 ? chatInfo.endTime : '--' }}</div>\r\n              </div>\r\n              <div class=\"timeItem\">\r\n                <div class=\"attr\">通话时长：</div>\r\n                <div class=\"time\">{{ chatInfo.chatTime.length > 0 ? formatSeconds(chatInfo.chatTime) : '--' }}</div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              class=\"chatContent\"\r\n              ref=\"chatContent\">\r\n              <ul class=\"chatList\">\r\n                <li\r\n                  v-for=\"(item, index) in chatList\"\r\n                  :key=\"index\"\r\n                  :class=\"{ msgLiLeft: item.role === '1', msgLiRight: item.role === '2' }\">\r\n                  <div class=\"icon\"></div>\r\n                  <div class=\"time\">{{ item.timestamp }}</div>\r\n                  <div class=\"msgLine\">\r\n                    <div class=\"msgContent\">\r\n                      <div class=\"txt\">{{ item.content }}</div>\r\n                      <!-- <div class=\"playContent\" v-if=\"item.start!==undefined && item.end!==undefined\"><i class=\"iconPlay\" @click=\"handleChatPlay(item)\"></i>{{item.end-item.start}}''</div> -->\r\n                    </div>\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div class=\"audio-container\">\r\n              <audio-player\r\n                :audioSrc=\"audioSrc\"\r\n                v-if=\"audioSrc !== ''\"\r\n                ref=\"audioPlayer\"></audio-player>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"panel orderInfo\">\r\n          <div class=\"panelHead\">\r\n            <div class=\"title\">工单登记</div>\r\n          </div>\r\n          <div class=\"panelBody\">\r\n            <div class=\"orderTitle\">来电人信息</div>\r\n            <table class=\"customTable\">\r\n              <tr>\r\n                <td class=\"td1\">姓名</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.CUST_NAME)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">性别</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.CUST_SEX)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">来电号码</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.CUST_PHONE)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">来电人要求信息保密</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.SPECIAL_FLAG ? (orderDetail.SPECIAL_FLAG === '1' ? '是' : '否') : '')\"></td>\r\n              </tr>\r\n            </table>\r\n            <div class=\"orderTitle\">反映问题</div>\r\n            <table class=\"customTable\">\r\n              <tr>\r\n                <td class=\"td1\">工单类型</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.ORDER_TYPE)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">被反映者</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.REFLECT_NAME)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">标题</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.TITLE)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">主要内容</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.MAJOR_CONTENT)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">所在区县</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.AREA_NAME)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">街道</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.STREET_NAME)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">详细地址</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.ADDRESS)\"></td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"td1\">承办单位</td>\r\n                <td\r\n                  class=\"td2\"\r\n                  v-html=\"replaceHtml(orderDetail.OFFICE_NAME)\"></td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, nextTick, computed, onUnmounted, onActivated, onDeactivated, getCurrentInstance, watch } from 'vue'\r\nimport { ClickOutside as vClickOutside } from 'element-plus'\r\nimport { useIframe } from '@/hooks'\r\nimport FullscreenToggle from './components/FullscreenToggle.vue'\r\nimport AudioPlayer from './components/AudioPlayer.vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport ArrowDown from '@/assets/image/icon_arrow_down.svg'\r\nimport IconKefu from '@/assets/image/icon_kefu.svg'\r\nimport Calendar from '@element-plus/icons-vue'\r\nimport { getAgentCallStat, getHistoryCall, getCallData, getWorkGroup, exportCallRecord, getWorkOrderDetail } from '@/api/workMonitor'\r\nimport { formatSeconds, debounce } from '@/utils'\r\nimport { ElMessage } from 'element-plus'\r\nimport dayjs from 'dayjs'\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst id = ref(route.params.id)\r\nconst staffInfo = ref({\r\n  name: route.query.name,\r\n  avatar: decodeURI(route.query.avatar || ''),\r\n  type: decodeURI(route.query.type) || '接线',\r\n})\r\n\r\n// iframe mode\r\nconst { isIframe } = useIframe()\r\n\r\n// 响应式数据\r\nconst sessionList = ref([]) // 实时监控列表\r\nconst activeSessionId = ref('') // 当前查看通话详情的sessionId\r\nconst activeSessionPhone = ref('') // 当前查看通话详情的话机号码\r\nconst activeSessionType = ref('') // 当前查看通话详情的类型，0-实时；1-历史；\r\nconst filterVisible = ref(false) // 筛选条件显示/隐藏\r\n\r\nconst filterForm = reactive({\r\n  keyWord: '',\r\n  startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 当天00:00:00\r\n  endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认现在\r\n}) // 筛选条件\r\nconst historyList = ref([]) // 历史通话列表\r\nconst historyPagination = reactive({\r\n  pageNo: 1,\r\n  pageSize: 20,\r\n  hasMore: true,\r\n  loading: false,\r\n  total: 0,\r\n}) // 历史通话分页信息\r\nconst statistics = reactive({\r\n  AGENTID: '',\r\n  CALL_IN_COUNT_ALL: '0',\r\n  AVG_CALL_IN_TIME: '0',\r\n  AVG_ARRANGE_TIME: '0',\r\n  LOGIN_TIME: '0',\r\n  CALL_IN_TIME_ALL: '0',\r\n  GOOD_PERCENT: '0',\r\n}) // 统计数据\r\nconst chatInfo = reactive({\r\n  startTime: '',\r\n  endTime: '',\r\n  chatTime: '',\r\n}) // 聊天信息\r\nconst chatList = ref([]) // 聊天内容\r\nconst audioSrc = ref('') // 音频链接\r\nconst customInfo = ref([]) // 来电人信息\r\nconst orderDetail = ref({}) // 工单详情\r\nconst audioPlayer = ref(null) // 音频播放器DOM引用\r\n\r\n// 班组和成员数据\r\nconst workGroups = ref([])\r\nconst allMembers = ref([])\r\n\r\n// 获取班组和成员数据\r\nconst getWorkGroupData = async () => {\r\n  try {\r\n    const res = await getWorkGroup()\r\n    if (res.result === '000' && res.data) {\r\n      workGroups.value = res.data\r\n\r\n      // 提取所有成员到一个扁平数组\r\n      const members = []\r\n      res.data.forEach((group) => {\r\n        if (group.userList && group.userList.length) {\r\n          group.userList.forEach((user) => {\r\n            members.push({\r\n              ...user,\r\n              groupName: group.workGroupName,\r\n            })\r\n          })\r\n        }\r\n      })\r\n      allMembers.value = members\r\n    } else {\r\n      ElMessage.error(res.desc || '获取班组数据失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取班组数据失败', error)\r\n  }\r\n}\r\n\r\n// 切换到其他坐席\r\nconst switchToAgent = (agent) => {\r\n  if (agent.AGENTID === id.value) return\r\n\r\n  router.push({\r\n    path: '/call-monitor/' + agent.AGENTID,\r\n    query: { name: agent.NAME, avatar: agent.IMG_URL },\r\n  })\r\n}\r\n\r\n// 获取统计数据和会话列表\r\nconst getSessionStat = async (includeHistory = false) => {\r\n  try {\r\n    // 获取坐席统计信息\r\n    const res = await getAgentCallStat(id.value)\r\n    if (res.result === '000' && res.data) {\r\n      Object.assign(statistics, res.data)\r\n    } else {\r\n      ElMessage.error(res.desc || '获取坐席统计信息失败')\r\n    }\r\n\r\n    // 获取通话中数据\r\n    await getActiveCallData()\r\n\r\n    // 只在初始化时获取历史通话数据，定时器不再拉取历史数据\r\n    if (includeHistory) {\r\n      await getHistorySessionList()\r\n    }\r\n  } catch (error) {\r\n    console.error('获取坐席统计信息失败', error)\r\n  }\r\n}\r\n\r\n// 获取通话中数据\r\nconst getActiveCallData = async () => {\r\n  try {\r\n    const res = await getCallData(id.value)\r\n\r\n    if (res.result === '000') {\r\n      // 有通话中数据时\r\n      if (res.data && res.data.CALL_ID) {\r\n        // 更新实时会话列表\r\n        sessionList.value = [res.data]\r\n        sessionChange()\r\n      } else {\r\n        sessionList.value = []\r\n      }\r\n    } else {\r\n      ElMessage.error(res.desc || '获取通话中数据失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取通话中数据失败', error)\r\n  }\r\n}\r\n\r\n// 获取工单详情\r\nconst getOrderDetail = async (session) => {\r\n  try {\r\n    const res = await getWorkOrderDetail(session?.ORDER_ID)\r\n    if (res.result === '000') {\r\n      orderDetail.value = res.data || {}\r\n    } else {\r\n      orderDetail.value = {}\r\n      ElMessage.error(res.desc || '获取工单详情失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取工单详情失败', error)\r\n  }\r\n}\r\n// 获取历史通话列表\r\nconst getHistorySessionList = async (isLoadMore = false) => {\r\n  if (!filterFlag()) {\r\n    return\r\n  }\r\n\r\n  // 如果是加载更多，但没有更多数据或正在加载中，则返回\r\n  if (isLoadMore && (!historyPagination.hasMore || historyPagination.loading)) {\r\n    return\r\n  }\r\n\r\n  try {\r\n    historyPagination.loading = true\r\n\r\n    // 如果不是加载更多，重置分页信息\r\n    if (!isLoadMore) {\r\n      historyPagination.pageNo = 1\r\n      historyPagination.hasMore = true\r\n      historyList.value = []\r\n    }\r\n\r\n    const res = await getHistoryCall(\r\n      filterForm.startTime,\r\n      filterForm.endTime,\r\n      id.value,\r\n      filterForm.keyWord,\r\n      historyPagination.pageNo,\r\n      historyPagination.pageSize\r\n    )\r\n\r\n    if (res.result === '000' && res.data) {\r\n      const newData = res.data || []\r\n\r\n      if (isLoadMore) {\r\n        // 加载更多时追加数据\r\n        historyList.value = [...historyList.value, ...newData]\r\n      } else {\r\n        // 首次加载时替换数据\r\n        historyList.value = newData\r\n        sessionChange()\r\n      }\r\n\r\n      // 更新分页信息\r\n      historyPagination.total = res.totalRow || 0\r\n      historyPagination.hasMore = !historyPagination.total || historyList.value.length >= historyPagination.total\r\n      historyPagination.pageNo += 1\r\n    } else {\r\n      ElMessage.error(res.desc || '获取历史通话失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取历史通话失败', error)\r\n  } finally {\r\n    historyPagination.loading = false\r\n  }\r\n}\r\n\r\n// 统一会话处理函数\r\nconst sessionChange = (item) => {\r\n  console.log('sessionChange called with item:', item)\r\n\r\n  let prevSessionId = activeSessionId.value\r\n  let session = item\r\n\r\n  // 1. 如果没有传入会话对象或找不到传入的会话对象，从列表中找第一个\r\n  if (!session) {\r\n    // 首先尝试从现有的activeSessionId查找对话对象\r\n    if (activeSessionId.value) {\r\n      // 先从实时会话列表查找\r\n      session = sessionList.value.find((s) => s.CALL_ID === activeSessionId.value)\r\n\r\n      // 如果在实时会话中没找到，再从历史列表查找\r\n      if (!session) {\r\n        session = historyList.value.find((s) => s.CALL_ID === activeSessionId.value)\r\n      }\r\n    }\r\n\r\n    // 如果仍然没有找到会话对象，则从列表中选择第一个可用的\r\n    if (!session) {\r\n      // 优先选择通话中的会话\r\n      if (sessionList.value.length > 0) {\r\n        session = sessionList.value[0]\r\n      }\r\n      // 其次选择历史会话\r\n      else if (historyList.value.length > 0) {\r\n        session = historyList.value[0]\r\n      }\r\n    }\r\n  }\r\n\r\n  // 如果没有可用的会话，清空当前会话并返回\r\n  if (!session) {\r\n    handleClear()\r\n    return\r\n  }\r\n\r\n  // 2. 设置会话ID、类型和电话号码\r\n  const isRealTimeSession = sessionList.value.some((s) => s.CALL_ID === session.CALL_ID)\r\n  activeSessionType.value = isRealTimeSession ? '0' : '1'\r\n  activeSessionId.value = session.CALL_ID\r\n  activeSessionPhone.value = session.CUST_PHONE\r\n\r\n  // 3. 如果会话ID变化或者是正在通话中的会话，则更新会话内容\r\n  if (prevSessionId !== session.CALL_ID || isRealTimeSession) {\r\n    // 获取工单详情\r\n    getOrderDetail(session)\r\n\r\n    try {\r\n      // 清空旧数据\r\n      if (prevSessionId !== session.CALL_ID) {\r\n        chatInfo.startTime = ''\r\n        chatInfo.endTime = ''\r\n        chatInfo.chatTime = ''\r\n        chatList.value = []\r\n        audioSrc.value = ''\r\n      }\r\n\r\n      // 尝试解析通话内容\r\n      if (session.CALL_CONTENT) {\r\n        let callContent = []\r\n        try {\r\n          callContent = JSON.parse(session.CALL_CONTENT)\r\n        } catch (e) {\r\n          console.error('解析通话内容失败', e)\r\n        }\r\n\r\n        // 设置会话信息\r\n        chatInfo.startTime = session.START_TIME\r\n        chatInfo.endTime = session.END_TIME\r\n        chatInfo.chatTime = session.CALL_TIME || ''\r\n\r\n        // 转换通话内容\r\n        chatList.value = callContent.map((element) => {\r\n          return {\r\n            role: element.clientId, // 角色，1-用户；2-机器人；\r\n            timestamp: formatDate(element.timestamp),\r\n            content: element.txt,\r\n          }\r\n        })\r\n\r\n        // 设置录音地址\r\n        if (session.RECORD_PATH) {\r\n          let url = new URL('/aiamgr/record/play.do', window.location.origin)\r\n          const query = {\r\n            recordePath: session.RECORD_PATH,\r\n            sessionId: session.CALL_ID,\r\n          }\r\n          url.search = new URLSearchParams(query).toString()\r\n          audioSrc.value = url.toString()\r\n        } else {\r\n          audioSrc.value = null\r\n        }\r\n\r\n        scrollToBottom()\r\n      }\r\n    } catch (error) {\r\n      console.error('处理会话详情失败', error)\r\n    }\r\n  }\r\n}\r\n\r\n// 清空会话内容\r\nconst handleClear = () => {\r\n  activeSessionId.value = ''\r\n  activeSessionPhone.value = ''\r\n  chatInfo.startTime = ''\r\n  chatInfo.endTime = ''\r\n  chatInfo.chatTime = ''\r\n  chatList.value = []\r\n  audioSrc.value = ''\r\n}\r\n\r\n// 筛选条件重置\r\nconst handleFilterReset = () => {\r\n  filterForm.keyWord = ''\r\n  filterForm.startTime = null\r\n  filterForm.endTime = null\r\n}\r\n\r\n// 处理点击外部隐藏popover\r\nconst handleClickOutside = () => {\r\n  console.log('handleClickOutside')\r\n  filterVisible.value = false\r\n}\r\n\r\n// 筛选条件验证\r\nconst filterFlag = () => {\r\n  if (filterForm.startTime !== null && filterForm.endTime === null) {\r\n    ElMessage.warning('请选择结束时间')\r\n    return false\r\n  }\r\n  if (filterForm.startTime === null && filterForm.endTime !== null) {\r\n    ElMessage.warning('请选择开始时间')\r\n    return false\r\n  }\r\n  if (filterForm.startTime !== null && filterForm.endTime !== null) {\r\n    const time1 = new Date(filterForm.startTime).getTime()\r\n    const time2 = new Date(filterForm.endTime).getTime()\r\n    if (time1 > time2) {\r\n      ElMessage.warning('请选择正确的时间范围')\r\n      return false\r\n    }\r\n  }\r\n  return true\r\n}\r\n\r\n// 格式化时间\r\nconst formatDate = (timestamp) => {\r\n  if (!timestamp) return ''\r\n\r\n  // 处理字符串时间戳\r\n  if (typeof timestamp === 'string' && !timestamp.includes('-')) {\r\n    timestamp = Number(timestamp)\r\n  }\r\n\r\n  // 如果已经是格式化的时间字符串，直接返回\r\n  if (typeof timestamp === 'string' && timestamp.includes('-')) {\r\n    return timestamp\r\n  }\r\n\r\n  const date = new Date(timestamp)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  const seconds = String(date.getSeconds()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n}\r\n\r\n// 导出\r\nconst handleExport = debounce(\r\n  () => {\r\n    ElMessage({\r\n      message: '导出成功',\r\n      type: 'success',\r\n    })\r\n\r\n    window.open('/cx-monitordata-12345/servlet/export?action=WorkMonitorRecord&callId=' + activeSessionId.value, '_blank')\r\n  },\r\n  1000,\r\n  true\r\n)\r\n\r\n// 播放单句转写文本\r\nconst handleChatPlay = (item) => {\r\n  if (audioPlayer.value && typeof item.start !== 'undefined') {\r\n    audioPlayer.value.playAudio(item.start)\r\n  }\r\n}\r\n\r\n// 替换HTML\r\nconst replaceHtml = (html) => {\r\n  if (!html) return ''\r\n  let newText = html.replace(/\\n/g, '<br>')\r\n  return newText\r\n}\r\n\r\nconst chatContent = ref(null) // 聊天内容DOM引用\r\nconst historyListContainer = ref(null) // 历史通话列表容器DOM引用\r\n\r\n// 聊天内容滚动到底部\r\nconst scrollToBottom = () => {\r\n  nextTick(() => {\r\n    let chatEl = chatContent.value\r\n    chatEl && chatEl.scrollTo({ top: chatEl.scrollHeight })\r\n  })\r\n}\r\n\r\n// 历史通话列表滚动监听\r\nconst handleHistoryScroll = debounce(() => {\r\n  const container = historyListContainer.value\r\n  if (!container) return\r\n\r\n  const { scrollTop, scrollHeight, clientHeight } = container\r\n  // 当滚动到距离底部50px时触发加载更多\r\n  if (scrollTop + clientHeight >= scrollHeight - 50) {\r\n    getHistorySessionList(true) // 加载更多\r\n  }\r\n}, 200)\r\n\r\n// 定时器引用\r\nlet timer = null\r\n\r\n// 启动数据拉取定时器\r\nconst startDataPolling = () => {\r\n  if (timer) {\r\n    clearInterval(timer)\r\n  }\r\n  timer = setInterval(() => {\r\n    getSessionStat(false) // 只获取统计数据和实时会话，不拉取历史数据\r\n  }, 10000)\r\n}\r\n\r\n// 停止数据拉取定时器\r\nconst stopDataPolling = () => {\r\n  if (timer) {\r\n    clearInterval(timer)\r\n    timer = null\r\n  }\r\n}\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  getSessionStat(true) // 获取统计数据和会话列表，包括历史数据\r\n  getWorkGroupData() // 获取班组和成员数据\r\n\r\n  // 启动定时器\r\n  startDataPolling()\r\n})\r\n\r\n// 组件激活时（从keep-alive缓存中激活）\r\nonActivated(() => {\r\n  getSessionStat(false) // 立即获取一次数据，不包括历史数据\r\n  startDataPolling() // 启动定时器\r\n})\r\n\r\n// 组件失活时（被keep-alive缓存）\r\nonDeactivated(() => {\r\n  stopDataPolling() // 停止定时器\r\n})\r\n\r\n// 组件卸载时清除定时器\r\nonUnmounted(() => {\r\n  stopDataPolling()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  border-radius: 4px;\r\n  color: #fff;\r\n  line-height: 22px;\r\n  display: flex;\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  background: #112756;\r\n  .containerLeft {\r\n    width: 332px;\r\n    background: linear-gradient(180deg, rgba(0, 85, 255, 0.16) 0%, rgba(0, 85, 255, 0.08) 98%);\r\n    border-width: 0px 1px 0px 0px;\r\n    border-style: solid;\r\n    border-color: rgba(0, 85, 255, 0.8);\r\n    display: flex;\r\n    flex-direction: column;\r\n    .staff-info {\r\n      display: flex;\r\n      gap: 16px;\r\n      padding: 24px;\r\n\r\n      .el-avatar {\r\n        --el-avatar-size: 56px !important;\r\n        flex: 0 0 56px !important;\r\n      }\r\n\r\n      .info {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        align-items: flex-start;\r\n\r\n        .name {\r\n          font-size: 20px;\r\n          font-weight: bold;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .id {\r\n          font-size: 14px;\r\n          color: rgba(255, 255, 255, 0.5);\r\n        }\r\n\r\n        .arrow-down {\r\n          width: 20px;\r\n          height: 20px;\r\n          vertical-align: middle;\r\n        }\r\n      }\r\n    }\r\n    .realTimeContent {\r\n      flex: 0 0 max-content;\r\n    }\r\n    .historyContent {\r\n      flex: 1;\r\n      display: flex;\r\n      overflow: hidden;\r\n    }\r\n    .historyContent {\r\n      flex-direction: column;\r\n    }\r\n  }\r\n  .containerRight {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px;\r\n    gap: 16px;\r\n    .statistics {\r\n      display: flex;\r\n      gap: 16px;\r\n    }\r\n    .mainContent {\r\n      flex: 1;\r\n      display: flex;\r\n      gap: 16px;\r\n      overflow: hidden;\r\n      .panel {\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg, rgba(0, 85, 255, 0.16) 0%, rgba(0, 85, 255, 0.08) 98%);\r\n        border-width: 0px 1px 0px 0px;\r\n        border-style: solid;\r\n        border-color: rgba(0, 85, 255, 0.4);\r\n        display: flex;\r\n        flex-direction: column;\r\n        .panelHead {\r\n          background: url('#{$imgUrl2}/bg_title.svg') no-repeat left center;\r\n          background-size: auto 100%;\r\n          padding: 8px 24px 8px 44px;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          .title {\r\n            font-size: 20px;\r\n            font-weight: bold;\r\n            line-height: 32px;\r\n            background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n            text-fill-color: transparent;\r\n            letter-spacing: 1px;\r\n            .tel {\r\n              letter-spacing: 0;\r\n            }\r\n          }\r\n          .el-button {\r\n            height: 32px;\r\n            line-height: 32px;\r\n            padding: 0 15px;\r\n          }\r\n          .btnExport {\r\n            height: 32px;\r\n            line-height: 32px;\r\n            border-radius: 4px;\r\n            background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0.3) 100%);\r\n            border: 1px solid rgba(0, 128, 255, 0.2);\r\n            .icon {\r\n              display: inline-block;\r\n              vertical-align: middle;\r\n              margin: -1px 4px 0 0;\r\n              width: 16px;\r\n              height: 16px;\r\n              background: url('#{$imgUrl2}/icon_export.svg') no-repeat center center;\r\n              background-size: 100% 100%;\r\n            }\r\n            .txt {\r\n              display: inline-block;\r\n              vertical-align: middle;\r\n              background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);\r\n              -webkit-background-clip: text;\r\n              -webkit-text-fill-color: transparent;\r\n              background-clip: text;\r\n              text-fill-color: transparent;\r\n            }\r\n          }\r\n        }\r\n        .panelBody {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.callList {\r\n  flex: 1;\r\n  padding: 0 24px;\r\n  overflow: auto;\r\n  li {\r\n    padding-bottom: 16px;\r\n    position: relative;\r\n    .liContent {\r\n      background: url('#{$imgUrl2}/bg_callList.png') no-repeat center center;\r\n      background-size: 100% 100%;\r\n      padding: 16px 16px 16px 80px;\r\n      position: relative;\r\n      cursor: pointer;\r\n    }\r\n    &.active .liContent {\r\n      background: url('#{$imgUrl2}/bg_callListActive.png') no-repeat center center;\r\n      background-size: 100% 100%;\r\n    }\r\n    .iconCall {\r\n      width: 48px;\r\n      height: 48px;\r\n      background: url('#{$imgUrl2}/icon_call.svg') no-repeat center center;\r\n      background-size: 100% 100%;\r\n      position: absolute;\r\n      left: 16px;\r\n      top: 16px;\r\n    }\r\n    .tel {\r\n      font-size: 18px;\r\n      line-height: 28px;\r\n      background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n      letter-spacing: 1px;\r\n      display: flex;\r\n      .label {\r\n        margin: 2px 0 0 8px;\r\n        padding: 0 8px;\r\n        height: 22px;\r\n        background: rgba(0, 220, 85, 0.2);\r\n        box-shadow: inset 0px 0px 2px 0px rgba(0, 220, 85, 0.5);\r\n        border: 1px solid rgba(0, 220, 85, 0.3);\r\n        border-radius: 2px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-items: center;\r\n        .txt {\r\n          font-size: 12px;\r\n          background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n          background-clip: text;\r\n          text-fill-color: transparent;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n    }\r\n    .callTime {\r\n      color: rgba(255, 255, 255, 0.5);\r\n      line-height: 22px;\r\n    }\r\n    .callType {\r\n      color: rgba(255, 255, 255, 0.5);\r\n      line-height: 22px;\r\n      .iconArrow {\r\n        display: inline-block;\r\n        vertical-align: middle;\r\n        margin: -2px 5px 0 0;\r\n        width: 14px;\r\n        height: 14px;\r\n        background: url('#{$imgUrl2}/icon_arrow.svg') no-repeat center center;\r\n        background-size: 100% 100%;\r\n        opacity: 0.5;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 加载状态样式\r\n.loading-item,\r\n.no-more-item {\r\n  padding: 16px 24px;\r\n  text-align: center;\r\n\r\n  .loading-content,\r\n  .no-more-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 8px;\r\n    color: rgba(255, 255, 255, 0.5);\r\n    font-size: 14px;\r\n  }\r\n\r\n  .loading-icon {\r\n    width: 16px;\r\n    height: 16px;\r\n    border: 2px solid rgba(255, 255, 255, 0.3);\r\n    border-top: 2px solid #00ffff;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.historyContent {\r\n  .historyHead {\r\n    padding: 16px 24px 12px;\r\n    position: relative;\r\n    .iconTitle {\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n      margin: -1px 8px 0 0;\r\n      width: 28px;\r\n      height: 28px;\r\n      background: url('#{$imgUrl2}/icon_title.svg') no-repeat center center;\r\n      background-size: 100% 100%;\r\n    }\r\n    .title {\r\n      vertical-align: middle;\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      line-height: 28px;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n    }\r\n    .filterContent {\r\n      .filterBtn {\r\n        position: absolute;\r\n        right: 24px;\r\n        top: 16px;\r\n        width: 32px;\r\n        height: 32px;\r\n        background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0.3) 100%);\r\n        border: 1px solid rgba(0, 128, 255, 0.3);\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        &:active {\r\n          background: linear-gradient(180deg, rgba(0, 170, 255, 0.7) 0%, rgba(0, 170, 255, 0.2) 100%);\r\n          border: 1px solid #00aaff;\r\n        }\r\n        .iconFilter {\r\n          width: 32px;\r\n          height: 32px;\r\n          background: url('#{$imgUrl2}/icon_filter.svg') no-repeat center center;\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .historyCallTime {\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    margin-bottom: 4px;\r\n  }\r\n  .callList {\r\n    padding-top: 0;\r\n    li {\r\n      padding-left: 24px;\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        width: 0;\r\n        border-left: 1px dashed #00aaff;\r\n        position: absolute;\r\n        left: 8px;\r\n        top: 0;\r\n        bottom: 0;\r\n      }\r\n      &::after {\r\n        content: '';\r\n        display: block;\r\n        width: 16px;\r\n        height: 16px;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 4px;\r\n        background: url('#{$imgUrl2}/icon_point.svg') no-repeat center center;\r\n        background-size: 100% 100%;\r\n      }\r\n      &:first-child::before {\r\n        top: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.statistics {\r\n  .statisticsList {\r\n    flex: 1;\r\n    display: flex;\r\n    gap: 16px;\r\n    .infoItem {\r\n      flex: 1;\r\n      display: flex;\r\n      gap: 8px;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: url('#{$imgUrl2}/bg_statistics.png') no-repeat center bottom;\r\n      background-size: 100% auto;\r\n      padding: 8px 0;\r\n      .icon {\r\n        padding: 2px;\r\n        width: 56px;\r\n        height: 56px;\r\n      }\r\n      .info {\r\n        .num {\r\n          font-size: 20px;\r\n          line-height: 1.5;\r\n          font-weight: bold;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n    }\r\n    .infoItem1 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics1.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #00ffff 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n    .infoItem2 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics2.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #00aaff 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n    .infoItem3 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics3.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n    .infoItem4 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics4.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #ff8c00 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n    .infoItem5 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics5.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n    .infoItem6 {\r\n      .icon {\r\n        background: url('#{$imgUrl2}/icon_statistics6.svg') no-repeat center center/contain;\r\n      }\r\n      .num {\r\n        background: linear-gradient(180deg, #ffffff 50%, #ffdc00 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n  }\r\n  .fullScreen {\r\n    width: 80px;\r\n    padding-top: 8px;\r\n    display: flex;\r\n    .fullscreen-container {\r\n      flex: 1;\r\n      display: flex;\r\n    }\r\n    .fullScreenBtn {\r\n      flex: 1;\r\n      background: url('#{$imgUrl2}/bg_fullScreen.svg') no-repeat center center;\r\n      background-size: 100% 100%;\r\n      cursor: pointer;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      padding-top: 16px;\r\n      &:hover {\r\n        opacity: 0.8;\r\n      }\r\n      .iconFullScreen {\r\n        width: 16px;\r\n        height: 16px;\r\n        background: url('#{$imgUrl2}/bg_return_btn.svg') no-repeat center center;\r\n        // background: url('#{$imgUrl2}/icon_fullScreen.svg') no-repeat center center;\r\n        background-size: 100% 100%;\r\n        margin-right: 4px;\r\n      }\r\n      .iconFullScreen2 {\r\n        background: url('#{$imgUrl2}/icon_fullScreen2.svg') no-repeat center center;\r\n        background-size: 100% 100%;\r\n      }\r\n      .txt {\r\n        background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n.callInfo {\r\n  flex: 0.57 1 0;\r\n  min-width: 0;\r\n  .panelBody {\r\n    overflow: hidden;\r\n  }\r\n  .timeInfo {\r\n    border-top: 1px solid rgba(0, 85, 255, 0.1);\r\n    border-bottom: 1px solid rgba(0, 85, 255, 0.3);\r\n    background: linear-gradient(180deg, rgba(0, 85, 255, 0) 0%, rgba(0, 85, 255, 0.2) 100%);\r\n    display: flex;\r\n    .timeItem {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: center;\r\n      padding: 16px 8px;\r\n      position: relative;\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        width: 1px;\r\n        height: 16px;\r\n        background: rgba(0, 85, 255, 0.3);\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        margin-top: -8px;\r\n      }\r\n      &:first-child::before {\r\n        display: none;\r\n      }\r\n      .attr {\r\n        color: rgba(255, 255, 255, 0.5);\r\n        white-space: nowrap;\r\n      }\r\n      .time {\r\n        background: linear-gradient(180deg, #ffffff 50%, #00ffff 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n        font-weight: bold;\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n  .chatContent {\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding: 16px 24px 8px;\r\n  }\r\n}\r\n.chatList {\r\n  li {\r\n    padding: 0 62px 24px;\r\n    position: relative;\r\n    .icon {\r\n      position: absolute;\r\n      top: 0;\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n    .time {\r\n      font-size: 12px;\r\n      line-height: 18px;\r\n      margin-bottom: 4px;\r\n    }\r\n    .msgLine {\r\n      .msgContent {\r\n        display: flex;\r\n        justify-content: left;\r\n        .txt {\r\n          display: inline-block;\r\n          border-radius: 4px;\r\n          padding: 9px 16px;\r\n          position: relative;\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            width: 7px;\r\n            height: 8px;\r\n            position: absolute;\r\n            top: 9px;\r\n          }\r\n          &::after {\r\n            content: '';\r\n            display: block;\r\n            width: 1px;\r\n            height: 8px;\r\n            position: absolute;\r\n            top: 9px;\r\n          }\r\n        }\r\n        .playContent {\r\n          white-space: nowrap;\r\n          padding: 8px 5px;\r\n          .iconPlay {\r\n            display: inline-block;\r\n            vertical-align: middle;\r\n            margin: -2px 4px 0 4px;\r\n            width: 20px;\r\n            height: 20px;\r\n            background: url('#{$imgUrl2}/icon_playBtn.svg') no-repeat center center;\r\n            background-size: 80% 80%;\r\n            cursor: pointer;\r\n            &:hover {\r\n              opacity: 0.8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .msgLiLeft {\r\n    .icon {\r\n      left: 0;\r\n      background: url('#{$imgUrl2}/icon_user.svg') no-repeat left center;\r\n      background-size: auto 100%;\r\n    }\r\n    .msgLine {\r\n      .msgContent {\r\n        .txt {\r\n          background: url('#{$imgUrl2}/bg_msgLeft.png') no-repeat center center;\r\n          background-size: 100% 100%;\r\n          &::before {\r\n            left: -6px;\r\n            background: url('#{$imgUrl2}/icon_triangleleft.png') no-repeat left center;\r\n            background-size: auto 100%;\r\n          }\r\n          &::after {\r\n            left: 0;\r\n            background: #0041a0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .msgLiRight {\r\n    .icon {\r\n      right: 0;\r\n      background: url('#{$imgUrl2}/icon_kefu.svg') no-repeat left center;\r\n      background-size: auto 100%;\r\n    }\r\n    .time {\r\n      text-align: right;\r\n    }\r\n    .msgLine {\r\n      .msgContent {\r\n        justify-content: right;\r\n        flex-direction: row-reverse;\r\n        .playContent {\r\n          padding-right: 10px;\r\n        }\r\n        .txt {\r\n          background: url('#{$imgUrl2}/bg_msgRight.png') no-repeat center center;\r\n          background-size: 100% 100%;\r\n          &::before {\r\n            right: -6px;\r\n            background: url('#{$imgUrl2}/icon_triangleRight.png') no-repeat left center;\r\n            background-size: auto 100%;\r\n          }\r\n          &::after {\r\n            right: 0;\r\n            background: #00699d;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.orderInfo {\r\n  flex: 0.43 1 0;\r\n  min-width: 0;\r\n  .panelBody {\r\n    padding: 16px 24px 5px;\r\n    overflow: auto;\r\n  }\r\n  .orderTitle {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    line-height: 24px;\r\n    background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    text-fill-color: transparent;\r\n    padding-left: 12px;\r\n    margin-bottom: 16px;\r\n    position: relative;\r\n    &::before {\r\n      content: '';\r\n      display: block;\r\n      width: 4px;\r\n      height: 16px;\r\n      background: #00ffff;\r\n      box-shadow: 0px 0px 6px 0px rgba(0, 255, 255, 0.6);\r\n      position: absolute;\r\n      left: 0;\r\n      top: 4px;\r\n    }\r\n  }\r\n  .customTable {\r\n    border-collapse: collapse;\r\n    margin-bottom: 24px;\r\n    td {\r\n      border: 1px solid rgba(0, 85, 255, 0.4);\r\n      padding: 14px 15px;\r\n      font-size: 14px;\r\n    }\r\n    .td1 {\r\n      width: 160px;\r\n      background: rgba(0, 85, 255, 0.24);\r\n      vertical-align: top;\r\n    }\r\n    .td2 {\r\n      background: rgba(0, 85, 255, 0.1);\r\n    }\r\n  }\r\n}\r\n@media screen and (max-width: 1500px) {\r\n  .container .containerRight .statistics {\r\n    gap: 8px;\r\n  }\r\n  .statistics .statisticsList {\r\n    gap: 8px;\r\n    .infoItem {\r\n      .icon {\r\n        width: 46px;\r\n        height: 46px;\r\n      }\r\n      .info {\r\n        .attr {\r\n          font-size: 13px;\r\n        }\r\n        .num {\r\n          font-size: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.filterPopoverContent {\r\n  padding: 6px;\r\n  min-width: 330px;\r\n  .filterPopoverItem {\r\n    display: flex;\r\n    margin-bottom: 16px;\r\n    .attr {\r\n      line-height: 32px;\r\n      padding-right: 8px;\r\n      background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n    }\r\n    .el-input {\r\n      flex: 1;\r\n\r\n      .el-input__prefix,\r\n      .el-input__suffix {\r\n        color: #fff !important;\r\n      }\r\n\r\n      .el-input__prefix,\r\n      .el-input__inner {\r\n        background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n        text-fill-color: transparent;\r\n      }\r\n    }\r\n  }\r\n  .el-button {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    border-radius: 4px;\r\n    padding: 0 16px 0 12px;\r\n    margin-left: 8px;\r\n    .iconReset,\r\n    .iconSearch {\r\n      display: inline-block;\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: middle;\r\n      margin: -2px 4px 0 0;\r\n    }\r\n    .iconReset {\r\n      background: url('#{$imgUrl2}/icon_reset.svg') no-repeat center center;\r\n      background-size: 100% 100%;\r\n    }\r\n    .iconSearch {\r\n      background: url('#{$imgUrl2}/icon_search.svg') no-repeat center center;\r\n      background-size: 100% 100%;\r\n    }\r\n  }\r\n  .btnReset,\r\n  .btnReset:hover,\r\n  .btnReset:focus {\r\n    background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);\r\n    border: 1px solid #0080ff;\r\n    .txt {\r\n      background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n    }\r\n  }\r\n  .btnSearch,\r\n  .btnSearch:hover,\r\n  .btnSearch:focus {\r\n    background: url('#{$imgUrl2}/bg_searchBtn.png') no-repeat center center;\r\n    background-size: 100% 100%;\r\n    border: none;\r\n    border-radius: 0;\r\n    .txt {\r\n      background: linear-gradient(180deg, #ffffff 50%, #00ffff 79%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n    }\r\n  }\r\n  .btnReset:hover,\r\n  .btnSearch:hover {\r\n    opacity: 0.8;\r\n  }\r\n  .el-input__inner {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    border-radius: 4px;\r\n    border: none;\r\n    outline: none;\r\n  }\r\n  .el-input__prefix .el-icon-time {\r\n    font-size: 0;\r\n    background: url('#{$imgUrl2}/icon_time.svg') no-repeat center center;\r\n    background-size: 50% 50%;\r\n  }\r\n  .el-date-editor .el-input__icon {\r\n    line-height: 32px;\r\n  }\r\n}\r\nbody .el-popper.el-picker-panel-custom {\r\n  color: #fff;\r\n  background: #112756;\r\n  border-color: #16cbff;\r\n\r\n  .el-picker-panel {\r\n    color: #fff;\r\n  }\r\n  .el-date-picker__header-label,\r\n  .el-date-table th {\r\n    color: #fff;\r\n  }\r\n  .el-picker-panel__footer {\r\n    border-color: #16cbff;\r\n\r\n    .el-button.is-text {\r\n      color: #fff;\r\n    }\r\n\r\n    .el-button.is-text:hover,\r\n    .el-button.is-plain {\r\n      background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);\r\n      border: 1px solid #0080ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n  &.el-popper[x-placement^='top'] .popper__arrow {\r\n    border-top-color: #16cbff;\r\n    &::after {\r\n      border-top-color: #112756;\r\n    }\r\n  }\r\n  &.el-popper[x-placement^='bottom'] .popper__arrow {\r\n    border-bottom-color: #16cbff;\r\n    &::after {\r\n      border-bottom-color: #112756;\r\n    }\r\n  }\r\n  .el-date-picker__time-header,\r\n  .el-picker-panel__content tr {\r\n    border-color: #16cbff;\r\n  }\r\n  .el-input__inner {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    border-radius: 4px;\r\n    // background: rgba(0, 128, 255, 0.2);\r\n    border: none;\r\n    outline: none;\r\n  }\r\n  .el-picker-panel__footer .el-button--default {\r\n    background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);\r\n    border: 1px solid #0080ff;\r\n    color: #fff;\r\n  }\r\n  .el-picker-panel__icon-btn {\r\n    color: #fff;\r\n  }\r\n  .el-time-panel {\r\n    background: #112756;\r\n    border-color: #16cbff;\r\n    .el-time-panel__btn {\r\n      color: #fff;\r\n    }\r\n    .el-time-spinner__item.is-active:not(.disabled) {\r\n      color: #fff;\r\n    }\r\n    .el-time-spinner__item:hover:not(.disabled):not(.is-active) {\r\n      background: none;\r\n    }\r\n    .el-time-panel__footer {\r\n      border-color: #16cbff;\r\n    }\r\n  }\r\n}\r\n\r\n.agent-dropdown {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.audio-container {\r\n  padding: 0 24px;\r\n  border-radius: 0px 0px 4px 4px;\r\n  background: linear-gradient(180deg, rgba(0, 85, 255, 0.08) 0%, rgba(0, 85, 255, 0.03) 100%);\r\n  border: 1px solid rgba(0, 85, 255, 0.32);\r\n}\r\n</style>\r\n", "import script from \"./index.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./index.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./index.vue?vue&type=style&index=0&id=279fb171&lang=scss&scoped=true\"\nimport \"./index.vue?vue&type=style&index=1&id=279fb171&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+comp_dfa19381716d55f869eff48e4b9623d4/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-279fb171\"]])\n\nexport default __exports__"], "names": ["props", "__props", "audioContent", "ref", "btnContent", "canvas", "audio", "audioContext", "analyser", "buffer", "isPlaying", "currentTime", "duration", "waveformData", "canvasHeight", "initAudio", "async", "value", "window", "AudioContext", "webkitAudioContext", "create<PERSON><PERSON>yser", "fftSize", "nextTick", "handleResize", "error", "console", "loadAudio", "audioSrc", "warn", "log", "response", "fetch", "ok", "Error", "status", "arrayBuffer", "state", "resume", "decodeAudioData", "generateWaveformData", "channelData", "getChannelData", "numberOfBars", "Math", "floor", "width", "samplesPerBar", "length", "newWaveformData", "i", "start", "end", "min", "max", "j", "push", "drawWaveform", "ctx", "getContext", "clearRect", "height", "margin", "left", "right", "centerY", "progressX", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "barSpacing", "maxBarHeight", "cornerRadius", "maxAmplitude", "map", "point", "abs", "for<PERSON>ach", "x", "amplitude", "normalizedHeight", "barHeight", "isPlayed", "fillStyle", "y", "beginPath", "moveTo", "lineTo", "arc", "PI", "closePath", "fill", "lineGradient", "createLinearGradient", "addColorStop", "strokeStyle", "lineWidth", "markerY", "stroke", "save", "clip", "bgColor1", "restore", "togglePlay", "pauseAudio", "playAudio", "time", "src", "play", "catch", "e", "pause", "updateTime", "onAudioEnd", "handleCanvasClick", "event", "rect", "getBoundingClientRect", "clientX", "percent", "canvasWidth", "clientWidth", "watch", "newSrc", "onMounted", "addEventListener", "onBeforeUnmount", "close", "removeEventListener", "router", "useRouter", "route", "useRoute", "id", "params", "staffInfo", "name", "query", "avatar", "decodeURI", "type", "isIframe", "useIframe", "sessionList", "activeSessionId", "activeSessionPhone", "activeSessionType", "filterVisible", "filterForm", "reactive", "key<PERSON>ord", "startTime", "dayjs", "startOf", "format", "endTime", "historyList", "historyPagination", "pageNo", "pageSize", "hasMore", "loading", "total", "statistics", "AGENTID", "CALL_IN_COUNT_ALL", "AVG_CALL_IN_TIME", "AVG_ARRANGE_TIME", "LOGIN_TIME", "CALL_IN_TIME_ALL", "GOOD_PERCENT", "chatInfo", "chatTime", "chatList", "orderDetail", "audioPlayer", "workGroups", "allMembers", "getWorkGroupData", "res", "getWorkGroup", "result", "data", "members", "group", "userList", "user", "groupName", "workGroupName", "ElMessage", "desc", "switchToAgent", "agent", "path", "NAME", "IMG_URL", "getSessionStat", "includeHistory", "getAgentCallStat", "Object", "assign", "getActiveCallData", "getHistorySessionList", "getCallData", "CALL_ID", "<PERSON><PERSON><PERSON><PERSON>", "getOrderDetail", "getWorkOrderDetail", "session", "ORDER_ID", "isLoadMore", "filterFlag", "getHistoryCall", "newData", "totalRow", "item", "prevSessionId", "find", "s", "handleClear", "isRealTimeSession", "some", "CUST_PHONE", "CALL_CONTENT", "callContent", "JSON", "parse", "START_TIME", "END_TIME", "CALL_TIME", "element", "role", "clientId", "timestamp", "formatDate", "content", "txt", "RECORD_PATH", "url", "URL", "location", "origin", "recorde<PERSON><PERSON>", "sessionId", "search", "URLSearchParams", "toString", "scrollToBottom", "handleFilterReset", "handleClickOutside", "warning", "time1", "Date", "getTime", "time2", "includes", "Number", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "handleExport", "debounce", "message", "open", "replaceHtml", "html", "newText", "replace", "chatContent", "historyList<PERSON><PERSON><PERSON>", "chatEl", "scrollTo", "top", "scrollHeight", "handleHistoryScroll", "container", "scrollTop", "clientHeight", "timer", "startDataPolling", "clearInterval", "setInterval", "stopDataPolling", "onActivated", "onDeactivated", "onUnmounted"], "sourceRoot": ""}