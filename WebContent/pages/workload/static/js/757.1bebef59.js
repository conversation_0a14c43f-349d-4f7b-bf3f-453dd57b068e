"use strict";(self["webpackChunkoverall_monitordata_vue"]=self["webpackChunkoverall_monitordata_vue"]||[]).push([[757],{4757:function(e,a,l){l.r(a),l.d(a,{default:function(){return je}});l(1484),l(6961),l(7354);var t=l(9304),s=l(2681),n=l(4104),o=l(1318),r=l(4199),i=l(482),u=l(5034);l(9370),l(2807),l(8747);function c(e={}){const{itemsPerRow:a=8,initialLoadRows:l=1,loadMoreRows:s=1,rootMargin:o="50px",threshold:r=.1}=e;let i=null;const u=new Map,c=(0,n.KR)(null),v=e=>{e.visibleCount||(e.visibleCount=Math.min(e.userList?.length||0,a*l))},d=e=>(v(e),e.expand?e.userList.slice(0,e.visibleCount):e.userList.slice(0,Math.min(a,e.visibleCount))),p=e=>(v(e),e.expand?e.visibleCount<e.userList.length:e.userList.length>a&&e.visibleCount<a),L=(e,a)=>{e&&a&&u.set(e,a)},C=(e,s)=>{e.expand=!e.expand,e.expand?e.visibleCount<e.userList.length&&(e.visibleCount=Math.max(e.visibleCount,a*l),(0,t.dY)((()=>{m(s)}))):e.visibleCount=Math.min(e.userList.length,a)},k=(e,l)=>{const n=e.expand?e.userList.length:a,o=Math.min(e.visibleCount+a*s,n);o>e.visibleCount&&(e.visibleCount=o,(0,t.dY)((()=>{m(l)})))},_=e=>{i&&i.disconnect(),i=new IntersectionObserver((a=>{a.forEach((a=>{if(a.isIntersecting){const l=u.get(a.target);l&&(i.unobserve(a.target),k(l,e))}}))}),{root:c.value,rootMargin:o,threshold:r})},m=e=>{i&&(u.clear(),(0,t.dY)((()=>{const a=document.querySelectorAll(".lazy-trigger");a.forEach(((a,l)=>{const t=a.getAttribute("data-group-id"),s=e.value.find((e=>e.workGroupId===t));s&&(u.set(a,s),i.observe(a))}))})))},E=(e,a)=>{c.value=a?.value,(0,t.dY)((()=>{_(e),setTimeout((()=>{m(e)}),100)}))},y=e=>e.map((e=>{const t=Math.min(e.userList?.length||0,a*l);return{...e,expand:!1,visibleCount:t}})),b=e=>e.value.reduce(((e,a)=>(e[a.workGroupId]={expand:a.expand,visibleCount:a.visibleCount},e)),{}),h=(e,a)=>{e.value.forEach((e=>{const l=a[e.workGroupId];l&&(e.expand=l.expand,e.visibleCount=l.visibleCount)}))},f=()=>{i&&(i.disconnect(),i=null),u.clear()};return{scrollContainer:c,getVisibleItems:d,shouldShowLoadMore:p,setLazyTrigger:L,toggleGroupExpand:C,initLazyLoad:E,initializeGroupData:y,saveGroupStates:b,restoreGroupStates:h,cleanup:f,itemsPerRow:a,initialLoadRows:l,loadMoreRows:s}}const v={class:"switch-btn"},d=["onClick"],p={class:"switch-btn-item-text"};var L={__name:"SwitchBtn",props:{options:{type:[Array,Object],default:()=>[]},modelValue:{type:String,default:""}},emits:["update:modelValue","change"],setup(e,{emit:a}){const l=e,n=a;(0,t.wB)((()=>l.modelValue),(e=>{n("change",e)}));const o=e=>{n("update:modelValue",e)};return(a,l)=>((0,t.uX)(),(0,t.CE)("div",v,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.options,((a,l)=>((0,t.uX)(),(0,t.CE)("div",{key:l,class:(0,s.C4)(["switch-btn-item",{active:e.modelValue===l}]),onClick:e=>o(l)},[(0,t.Lk)("div",p,(0,s.v_)(a),1)],10,d)))),128))]))}},C=l(6762);const k=(0,C.A)(L,[["__scopeId","data-v-13dba46a"]]);var _=k;const m=e=>((0,t.Qi)("data-v-5113c6e5"),e=e(),(0,t.jt)(),e),E={class:"ranking-list scroll-container"},y=["data","onMouseover"],b=m((()=>(0,t.Lk)("div",{class:"index"},null,-1))),h={class:"name fontStyle"},f={class:"bar"},g={class:"count"};function A(e,a,l,n,o,r){return(0,t.uX)(),(0,t.CE)("div",E,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(l.data,((a,n)=>((0,t.uX)(),(0,t.CE)("div",{class:(0,s.C4)(["rank-item",{active:l.activeItem===a.AGENT_NO}]),data:a,key:a.AGENT_NO||n,onMouseover:l=>e.$emit("active",a)},[b,(0,t.Lk)("p",h,(0,s.v_)(a.NAME),1),(0,t.Lk)("div",f,[(0,t.Lk)("span",{class:"bar-fill",style:(0,s.Tr)(r.getBarStyle(a.ALL_SCORE))},null,4)]),(0,t.Lk)("p",g,(0,s.v_)(a.ALL_SCORE),1)],42,y)))),128))])}var N={components:{},props:{data:{type:Array,default:()=>[]},activeItem:{type:String,default:""}},data(){return{}},computed:{sortedData(){return[...this.data].sort(((e,a)=>Number(a.ALL_SCORE)-Number(e.ALL_SCORE)))},extreme(){return 0===this.sortedData.length?0:Number(this.sortedData[0].ALL_SCORE)||0}},watch:{},methods:{getBarStyle(e){return e&&0!==this.extreme?{width:Number(e)/this.extreme*100+"%"}:{width:"0%"}}}};const S=(0,C.A)(N,[["render",A],["__scopeId","data-v-5113c6e5"]]);var R=S;const T={class:"radar-chart"},O={key:0,class:"title"};var I={__name:"RadarChart",props:{data:{type:Object,required:!0},title:{type:String,default:""}},setup(e){const a=e,l={busiNumberScore:"业务量得分",qualityScore:"质检得分",monthlyExamScore:"月考得分",afterLongScore:"话后处理得分",attendanceScore:"出勤得分"},n=(0,t.EW)((()=>({radar:{indicator:Object.keys(l).map((e=>({name:e,max:5}))),center:["50%","60%"],radius:"50%",nameGap:5,axisName:{formatter:e=>`{labelStyle|${l[e]}} {valueStyle|${a.data[e]}}`,rich:{labelStyle:{fontSize:(0,u.Q4)(12),color:"#FFFFFF",verticalAlign:"middle"},valueStyle:{fontFamily:"zcoolqingkehuangyouti",fontSize:(0,u.Q4)(14),color:"#00FFFF",verticalAlign:"middle"}}},splitArea:{areaStyle:{color:"transparent"}},axisLine:{lineStyle:{color:"rgba(0, 128, 255, 0.3)"}},splitLine:{lineStyle:{color:"rgba(0, 128, 255, 0.3)"}}},series:[{name:"得分",type:"radar",data:[{name:"得分",value:Object.keys(l).map((e=>a.data[e+"Leven"])),itemStyle:{color:"#00FFFF"},areaStyle:{color:"rgba(51, 250, 253, 0.3)"}}]}]}))),{chartRef:o,chart:r}=(0,u.uy)(n);return(a,l)=>((0,t.uX)(),(0,t.CE)("div",T,[e.title?((0,t.uX)(),(0,t.CE)("div",O,(0,s.v_)(e.title),1)):(0,t.Q3)("",!0),(0,t.Lk)("div",{class:"chart-container",ref_key:"chartRef",ref:o},null,512)]))}};const F=(0,C.A)(I,[["__scopeId","data-v-06e06084"]]);var x=F,w=l(4225),M=l(8074);const G=e=>((0,t.Qi)("data-v-4dbf9c78"),e=e(),(0,t.jt)(),e),P={class:"body"},U={class:"area"},X={class:"item"},K=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"当天接线量",-1))),V={class:"value"},W={class:"item"},z=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"回访总量",-1))),D={class:"value"},Q={class:"item"},j=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"网络总量",-1))),H={class:"value"},q={class:"area"},Y={class:"title-secondary"},$=G((()=>(0,t.Lk)("h3",{class:"fontStyle"},"上月综合排名",-1))),B={class:"search-bar"},Z={class:"active-panel"},J={class:"part"},ee={class:"item"},ae=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"总分",-1))),le={class:"value f-24"},te={class:"sub-part"},se={class:"item"},ne=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"奖励分",-1))),oe={class:"value"},re={class:"item"},ie=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"扣减分",-1))),ue={class:"value"},ce={class:"part"},ve={class:"item"},de=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"月度排名",-1))),pe={class:"value"},Le={class:"part"},Ce={class:"item"},ke=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"挂机满意度",-1))),_e={class:"value"},me={class:"area"},Ee=G((()=>(0,t.Lk)("div",{class:"title-secondary"},[(0,t.Lk)("h3",{class:"fontStyle"},"人员职场")],-1))),ye={class:"total-data"},be={class:"item"},he=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"接线量占比",-1))),fe={class:"value"},ge=G((()=>(0,t.Lk)("span",{class:"divider"},null,-1))),Ae={class:"item"},Ne=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"回访量占比",-1))),Se={class:"value"},Re=G((()=>(0,t.Lk)("span",{class:"divider"},null,-1))),Te={class:"item"},Oe=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"网络量占比",-1))),Ie={class:"value"},Fe={class:"bar"},xe={class:"name fontStyle"},we={class:"label fontStyle"},Me={class:"value"},Ge=["onClick"],Pe=["onClick"],Ue={class:"name fontStyle"},Xe=G((()=>(0,t.Lk)("span",{class:"label fontStyle"},"工作量",-1))),Ke={class:"value"},Ve=["data-group-id"],We=G((()=>(0,t.Lk)("div",{class:"loading-placeholder"},[(0,t.Lk)("div",{class:"loading-text"},"加载中")],-1))),ze=[We];var De=Object.assign({name:"HomePage"},{__name:"index",setup(e){const a=(0,r.rd)(),{isIframe:l}=(0,u.u)(),{scrollContainer:v,getVisibleItems:d,shouldShowLoadMore:p,setLazyTrigger:L,toggleGroupExpand:C,initLazyLoad:k,initializeGroupData:m,saveGroupStates:E,restoreGroupStates:y,cleanup:b}=c({itemsPerRow:8,initialLoadRows:1,loadMoreRows:1,rootMargin:"50px",threshold:.1}),h=(0,n.KR)({ACCEPTANCE_COUNT:"0",ACCEPTANCE_FLOW_UP:"0",ACCEPTANCE_CALL_COUNT:"0",ACCEPTANCE_COUNT_PERCENT:"0%",ACCEPTANCE_FLOW_UP_PERCENT:"0%",ACCEPTANCE_CALL_COUNT_PERCENT:"0%"}),f=(0,n.KR)("agent"),g={agent:"坐席",work:"班组"},A=(0,n.KR)(""),N=(0,n.KR)([]),S=(0,n.KR)(null),T=(0,n.KR)({}),O=(0,n.KR)({}),I=(0,n.KR)([]),F=(0,n.KR)(0),G=e=>{C(e,I)},We=async()=>{try{const e=await(0,M.M1)();"000"===e.result&&(h.value=e.data||{})}catch(e){console.error("获取总体数据失败",e)}},De=async()=>{try{const e=await(0,M.H2)(f.value,A.value);"000"===e.result&&(N.value=e.data||[],N.value.length>0?Qe(N.value[0]):(S.value=null,T.value={},O.value={}))}catch(e){console.error("获取排行榜数据失败",e)}},Qe=e=>{console.log("handleActiveChange",e),e.AGENT_NO!==S.value&&(S.value=e.AGENT_NO,T.value=N.value.find((a=>a.AGENT_NO===e.AGENT_NO))||{},T.value.AGENT_NO&&T.value.MONTH_ID&&(O.value={...T.value}))},je=async()=>{try{const e=await(0,M.Zc)();"000"===e.result&&(console.log("获取班组数据成功，数据数量:",e.data?.length),I.value=m(e.data||[]),k(I,v))}catch(e){console.error("获取班组数据失败",e)}},He=(0,t.EW)((()=>{if(!O.value.LAST_MONTH_SCORE_FIVE)return{};const{LAST_MONTH_SCORE_FIVE:e}=O.value;return e})),qe=()=>{A.value="",De()},Ye=e=>{De()},$e=(e,l)=>{a.push({path:`/call-monitor/${e.AGENTID}`,query:{name:e.NAME,avatar:e.IMG_URL,type:l.businessType}})},Be=()=>{const e=v.value||document.querySelector(".scroll-container");e&&(F.value=e.scrollTop)},Ze=()=>{const e=v.value||document.querySelector(".scroll-container");e&&F.value>0&&setTimeout((()=>{e.scrollTop=F.value}),100)};let Je=null;return(0,t.sV)((()=>{We(),De(),je(),Je=setInterval((()=>{We();const e=E(I);je().then((()=>{y(I,e)}))}),1e4)})),(0,t.xo)((()=>{Je&&(clearInterval(Je),Je=null),b()})),(0,t.n)((()=>{(0,t.dY)((()=>{Ze()}))})),(0,r.JZ)(((e,a,l)=>{Be(),l()})),(e,a)=>{const r=(0,t.g2)("el-input"),u=(0,t.g2)("el-form-item"),c=(0,t.g2)("el-button"),C=(0,t.g2)("el-icon"),k=(0,t.g2)("el-avatar");return(0,t.uX)(),(0,t.CE)("div",{class:(0,s.C4)(["home",{"iframe-mode":(0,n.R1)(l)}])},[(0,t.Lk)("div",P,[(0,t.Lk)("div",U,[(0,t.Lk)("p",X,[K,(0,t.Lk)("span",V,(0,s.v_)((0,n.R1)(i.Mo)(h.value.ACCEPTANCE_COUNT)),1)]),(0,t.Lk)("p",W,[z,(0,t.Lk)("span",D,(0,s.v_)((0,n.R1)(i.Mo)(h.value.ACCEPTANCE_FLOW_UP)),1)]),(0,t.Lk)("p",Q,[j,(0,t.Lk)("span",H,(0,s.v_)((0,n.R1)(i.Mo)(h.value.ACCEPTANCE_CALL_COUNT)),1)])]),(0,t.Lk)("div",q,[(0,t.Lk)("div",Y,[$,(0,t.bF)(_,{modelValue:f.value,"onUpdate:modelValue":a[0]||(a[0]=e=>f.value=e),options:g,onChange:Ye},null,8,["modelValue"])]),(0,t.Lk)("div",B,[(0,t.bF)(u,{label:"关键字"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:A.value,"onUpdate:modelValue":a[1]||(a[1]=e=>A.value=e),placeholder:"请输入",clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(c,{onClick:qe,icon:(0,n.R1)(o.C42)},{default:(0,t.k6)((()=>[(0,t.eW)(" 重置 ")])),_:1},8,["icon"]),(0,t.bF)(c,{type:"primary",onClick:De,icon:(0,n.R1)(o.vji),style:{margin:"0"}},{default:(0,t.k6)((()=>[(0,t.eW)(" 搜索 ")])),_:1},8,["icon"])]),(0,t.bF)(R,{data:N.value,activeItem:S.value,onActive:Qe,style:{}},null,8,["data","activeItem"]),(0,t.Lk)("div",Z,[(0,t.Lk)("div",J,[(0,t.Lk)("p",ee,[ae,(0,t.Lk)("span",le,(0,s.v_)(O.value.ALL_SCORE||"-"),1)]),(0,t.Lk)("div",te,[(0,t.Lk)("p",se,[ne,(0,t.Lk)("span",oe,(0,s.v_)(O.value.REWARD||"-"),1)]),(0,t.Lk)("p",re,[ie,(0,t.Lk)("span",ue,(0,s.v_)(O.value.DEDUCTION||"-"),1)])])]),(0,t.Lk)("div",ce,[(0,t.Lk)("p",ve,[de,(0,t.Lk)("span",pe,(0,s.v_)(O.value.MONTHLY_RANKING||"-"),1)])]),(0,t.Lk)("div",Le,[(0,t.Lk)("p",Ce,[ke,(0,t.Lk)("span",_e,(0,s.v_)(O.value.ON_HOOK_SATISFACTION||"-")+"%",1)])])]),O.value.LAST_MONTH_SCORE_FIVE?((0,t.uX)(),(0,t.Wv)(x,{key:0,data:He.value,title:T.value.NAME+"-五维图"},null,8,["data","title"])):(0,t.Q3)("",!0)]),(0,t.Lk)("div",me,[Ee,(0,t.Lk)("div",ye,[(0,t.Lk)("p",be,[he,(0,t.Lk)("span",fe,(0,s.v_)(h.value.ACCEPTANCE_COUNT_PERCENT||"0%"),1)]),ge,(0,t.Lk)("p",Ae,[Ne,(0,t.Lk)("span",Se,(0,s.v_)(h.value.ACCEPTANCE_FLOW_UP_PERCENT||"0%"),1)]),Re,(0,t.Lk)("p",Te,[Oe,(0,t.Lk)("span",Ie,(0,s.v_)(h.value.ACCEPTANCE_CALL_COUNT_PERCENT||"0%"),1)])]),(0,t.Lk)("div",{class:"scroll-container",ref_key:"scrollContainer",ref:v},[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(I.value,(e=>((0,t.uX)(),(0,t.CE)("div",{key:e.workGroupId,class:"group-container"},[(0,t.Lk)("div",Fe,[(0,t.Lk)("div",xe,(0,s.v_)(e.workGroupName),1),(0,t.Lk)("span",we,(0,s.v_)(e.businessType),1),(0,t.Lk)("span",Me,(0,s.v_)(e.workGroupWorkCount),1),e.userList.length>8?((0,t.uX)(),(0,t.CE)("div",{key:0,class:"btn fontStyle",onClick:a=>G(e)},[(0,t.eW)((0,s.v_)(e.expand?"收起班组":"展开班组")+" ",1),e.expand?((0,t.uX)(),(0,t.Wv)(C,{key:0},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(o.DoI))])),_:1})):((0,t.uX)(),(0,t.Wv)(C,{key:1},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(o.yd$))])),_:1}))],8,Ge)):(0,t.Q3)("",!0)]),e.userList.length>0?((0,t.uX)(),(0,t.CE)("div",{key:0,class:(0,s.C4)(["content",e.expand?"":"not-expand"])},[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)((0,n.R1)(d)(e),(a=>((0,t.uX)(),(0,t.CE)("div",{key:a.AGENTID,onClick:l=>$e(a,e),class:"staff"},[(0,t.bF)(k,{size:56,src:a.IMG_URL||(0,n.R1)(w),onError:e=>a.IMG_URL=(0,n.R1)(w),style:{flex:"0 0 56px",border:"1px solid #00ffff"}},null,8,["src","onError"]),(0,t.Lk)("div",Ue,(0,s.v_)(a.NAME),1),(0,t.Lk)("p",null,[Xe,(0,t.Lk)("span",Ke,(0,s.v_)(a.WORK_COUNT),1)])],8,Pe)))),128)),(0,n.R1)(p)(e)?((0,t.uX)(),(0,t.CE)("div",{key:0,ref_for:!0,ref:a=>(0,n.R1)(L)(a,e),"data-group-id":e.workGroupId,class:"lazy-trigger"},ze,8,Ve)):(0,t.Q3)("",!0)],2)):(0,t.Q3)("",!0)])))),128))],512)])])],2)}}});const Qe=(0,C.A)(De,[["__scopeId","data-v-4dbf9c78"]]);var je=Qe}}]);
//# sourceMappingURL=757.1bebef59.js.map