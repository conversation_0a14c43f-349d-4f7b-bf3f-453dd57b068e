<!DOCTYPE html>
<html lang="zh-CN" style="height: 100%">
  <head>
    <meta charset="UTF-8" />
    <title>质检分类管理</title>
    <link rel="stylesheet" href="/yc-qualityZn/static/element-ui/index.css">
    <link rel="stylesheet" href="/yc-qualityZn/static/css/common.css" />
    <!-- 引入样式 -->
    <style>
      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
      }
      #app {
        height: 100%;
      }
      .el-container {
        height: 100%;
      }
      .el-main {
        padding: 15px;
        height: 100%;
        box-sizing: border-box;
        background-color: #f0f2f5;
      }
      .el-card {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .el-card__body {
        flex: 1;
        overflow: auto;
        padding: 20px;
        display: flex;
        flex-direction: column;
      }
      .header-title {
        margin-bottom: 20px;
      }
      .search-area {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      .search-form {
        display: flex;
        align-items: center;
      }
      .el-form-item {
        margin-bottom: 0;
      }
      .button-group {
        white-space: nowrap;
      }
      .el-table {
        margin-top: 20px;
        flex: 1;
      }
      .form-container {
        padding: 20px;
      }
      .help-icon {
        margin-left: 4px;
        color: #909399;
        cursor: pointer;
      }
      .dialog-footer {
        text-align: right;
        padding-top: 20px;
      }
      .el-dialog__body {
        padding: 0;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <el-container>
        <el-main>
          <el-card>
            <div class="header-title">
              <h3><i class="el-icon-list"></i> 智能质检模型管理</h3>
            </div>

            <div class="search-area">
              <el-form
                :inline="true"
                :model="searchForm"
                ref="searchForm"
                class="search-form"
              >
                <el-form-item label="智能质检模型名称">
                  <el-input
                    v-model="searchForm.className"
                    placeholder="请输入模型名称"
                    size="small"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small" @click="loadData">
                    <i class="el-icon-search"></i> 搜索
                  </el-button>
                </el-form-item>
              </el-form>

              <div class="button-group">
                <el-button type="success" size="small" @click="showEditDialog()"
                  >手动新增</el-button
                >
                <!-- <el-button type="primary" size="small" @click="sysnData">同步新增</el-button> -->
              </div>
            </div>

            <el-table :data="tableData" border style="width: 100%">
              <el-table-column
                prop="ZN_CLASS_ID"
                label="质检模型ID"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="ZN_CLASS_NAME"
                label="质检模型名称"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME1"
                label="质检项1"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME2"
                label="质检项2"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME3"
                label="质检项3"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME4"
                label="质检项4"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME5"
                label="质检项5"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME6"
                label="质检项6"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME7"
                label="质检项7"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME8"
                label="质检项8"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME9"
                label="质检项9"
              ></el-table-column>
              <el-table-column
                prop="QC_ITEM_NAME10"
                label="质检项10"
              ></el-table-column>
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="showEditDialog(scope.row.ZN_CLASS_ID)"
                    >编辑</el-button
                  >
                  <el-button type="text" @click="delData(scope.row.ZN_CLASS_ID)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container" style="margin-top: 20px">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pagination.currentPage"
                :page-sizes="[10, 15, 20, 50, 100, 200]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
              >
              </el-pagination>
            </div>

            <!-- 编辑弹窗 -->
            <el-dialog
              :title="editForm.znClassId ? '编辑质检模型' : '新增质检模型'"
              :visible.sync="dialogVisible"
              width="40%"
              :close-on-click-modal="false"
              :destroy-on-close="true"
            >
              <el-form
                ref="editForm"
                :model="editForm"
                :rules="rules"
                label-width="120px"
                size="small"
                class="form-container"
              >
                <!-- 基本信息 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label="质检模型ID"
                      prop="ZN_CLASS_ID"
                      required
                    >
                      <el-input
                        v-model="editForm.ZN_CLASS_ID"
                        :disabled="!!editForm.znClassId"
                        placeholder="请输入质检模型ID"
                      >
                        <el-tooltip
                          slot="append"
                          content="质检模型ID必须使用智能质检的任务ID"
                          placement="top"
                        >
                          <i class="el-icon-question help-icon"></i>
                        </el-tooltip>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="质检模型名称"
                      prop="ZN_CLASS_NAME"
                      required
                    >
                      <el-input
                        v-model="editForm.ZN_CLASS_NAME"
                        placeholder="请输入质检模型名称"
                      >
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 质检项列表 -->
                <div v-for="i in 10" :key="i">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item
                        :label="'质检项ID' + i"
                        :prop="'QC_ITEM_ID' + i"
                      >
                        <el-input
                          v-model="editForm['QC_ITEM_ID' + i]"
                          :placeholder="'请输入质检项ID' + i"
                        >
                          <el-tooltip
                            v-if="i === 1"
                            slot="append"
                            content="质检项ID必须使用智能质检任务所关联的质检项ID，该字段用于统计，如果不写，表示不用统计，统计字段最多支持10个字段"
                            placement="top"
                          >
                            <i class="el-icon-question help-icon"></i>
                          </el-tooltip>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        :label="'质检项名称' + i"
                        :prop="'QC_ITEM_NAME' + i"
                      >
                        <el-input
                          v-model="editForm['QC_ITEM_NAME' + i]"
                          :placeholder="'请输入质检项名称' + i"
                        >
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
              </div>
            </el-dialog>
          </el-card>
        </el-main>
      </el-container>
    </div>

    <!-- 引入Vue、Element UI和Axios -->
    <script src="/yc-qualityZn/static/vue/vue.min.js"></script>
    <script src="/yc-qualityZn/static/element-ui/index.js"></script>
    <script src="/yc-qualityZn/static/axios/axios.min.js"></script>
    <script src="/yc-qualityZn/static/js/axios-config.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            searchForm: {
              className: "",
            },
            tableData: [],
            pagination: {
              currentPage: 1,
              pageSize: 15,
              total: 0,
            },
            dialogVisible: false,
            editForm: {
              znClassId: "",
              ZN_CLASS_ID: "",
              ZN_CLASS_NAME: "",
              QC_ITEM_ID1: "",
              QC_ITEM_NAME1: "",
              QC_ITEM_ID2: "",
              QC_ITEM_NAME2: "",
              QC_ITEM_ID3: "",
              QC_ITEM_NAME3: "",
              QC_ITEM_ID4: "",
              QC_ITEM_NAME4: "",
              QC_ITEM_ID5: "",
              QC_ITEM_NAME5: "",
              QC_ITEM_ID6: "",
              QC_ITEM_NAME6: "",
              QC_ITEM_ID7: "",
              QC_ITEM_NAME7: "",
              QC_ITEM_ID8: "",
              QC_ITEM_NAME8: "",
              QC_ITEM_ID9: "",
              QC_ITEM_NAME9: "",
              QC_ITEM_ID10: "",
              QC_ITEM_NAME10: "",
            },
            rules: {
              ZN_CLASS_ID: [
                {
                  required: true,
                  message: "请输入质检模型ID",
                  trigger: "blur",
                },
              ],
              ZN_CLASS_NAME: [
                {
                  required: true,
                  message: "请输入质检模型名称",
                  trigger: "blur",
                },
              ],
            },
          };
        },
        created() {
          this.loadData();
        },
        methods: {
          loadData() {
            axios
              .post("/yc-qualityZn/webcall", {
                data: {
                  params: {
                    className: this.searchForm.className,
                    pageIndex: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize,
                    pageType: 3,
                  },
                  controls: ["ZnClassDao.classList"],
                },
              })
              .then((response) => {
                console.log(response.data["ZnClassDao.classList"]);
                if (response.data["ZnClassDao.classList"].state == 1) {
                  this.tableData = response.data["ZnClassDao.classList"].data;
                  this.pagination.total =
                    response.data["ZnClassDao.classList"].totalRow *
                    response.data["ZnClassDao.classList"].totalPage;
                } else {
                  this.$message.error(response.data.msg);
                }
              });
          },
          showEditDialog(znClassId = "") {
            this.editForm = {
              znClassId: znClassId,
              ZN_CLASS_ID: "",
              ZN_CLASS_NAME: "",
              QC_ITEM_ID1: "",
              QC_ITEM_NAME1: "",
              QC_ITEM_ID2: "",
              QC_ITEM_NAME2: "",
              QC_ITEM_ID3: "",
              QC_ITEM_NAME3: "",
              QC_ITEM_ID4: "",
              QC_ITEM_NAME4: "",
              QC_ITEM_ID5: "",
              QC_ITEM_NAME5: "",
              QC_ITEM_ID6: "",
              QC_ITEM_NAME6: "",
              QC_ITEM_ID7: "",
              QC_ITEM_NAME7: "",
              QC_ITEM_ID8: "",
              QC_ITEM_NAME8: "",
              QC_ITEM_ID9: "",
              QC_ITEM_NAME9: "",
              QC_ITEM_ID10: "",
              QC_ITEM_NAME10: "",
            };

            if (znClassId) {
              // 构建请求参数
              const params = {};
              Object.keys(this.editForm).forEach((key) => {
                if (key !== "znClassId") {
                  params[`qcClassEdit.${key}`] = this.editForm[key];
                }
              });

              axios
                .post("/yc-qualityZn/webcall", {
                  data: {
                    params: {
                      ...params,
                      marsPrefix: "qcClassEdit.",
                      pk: znClassId,
                      mars: "ZnClassDao.classRecord",
                    },
                    controls: ["ZnClassDao.classRecord"],
                  },
                })
                .then((response) => {
                  if (
                    response.data["ZnClassDao.classRecord"] &&
                    response.data["ZnClassDao.classRecord"].data
                  ) {
                    this.editForm = {
                      ...this.editForm,
                      ...response.data["ZnClassDao.classRecord"].data,
                    };
                  }
                });
            }

            this.dialogVisible = true;
            this.$nextTick(() => {
              this.$refs.editForm && this.$refs.editForm.clearValidate();
            });
          },
          submitForm() {
            this.$refs.editForm.validate((valid) => {
              if (valid) {
                const url = this.editForm.znClassId
                  ? "/yc-qualityZn/servlet/zn/class?action=update"
                  : "/yc-qualityZn/servlet/zn/class?action=add";

                // 构建请求参数
                const params = {};
                Object.keys(this.editForm).forEach((key) => {
                  if (key !== "znClassId") {
                    params[`qcClassEdit.${key}`] = this.editForm[key] || ""; // 确保空值为空字符串
                  }
                });

                axios
                  .post(url, {
                    data: params,
                  })
                  .then((response) => {
                    if (response.data.state === 1) {
                      this.$message.success(response.data.msg);
                      this.dialogVisible = false;
                      this.loadData();
                    } else {
                      this.$message.error(response.data.msg || "操作失败");
                    }
                  });
              }
            });
          },
          delData(znClassId) {
            this.$confirm("是否确定删除当前质检任务？", "删除提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              axios
                .post("/yc-qualityZn/servlet/zn/class?action=delete", {
                  data: {
                    znClassId: znClassId,
                  },
                })
                .then((response) => {
                  if (response.data.state === 1) {
                    this.$message.success(response.data.msg);
                    this.loadData();
                  } else {
                    this.$message.error(response.data.msg);
                  }
                });
            });
          },
          sysnData() {
            this.$confirm("是否确定同步数据？", "同步提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              axios
                .post("/yc-qualityZn/servlet/zn/class?action=sysn")
                .then((response) => {
                  if (response.data.state === 1) {
                    this.$message.success(response.data.msg);
                    this.loadData();
                  } else {
                    this.$message.error(response.data.msg);
                  }
                });
            });
          },
          handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.loadData();
          },
          handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.loadData();
          },
        },
      });
    </script>
  </body>
</html>
