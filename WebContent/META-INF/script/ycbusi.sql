-- 2025-05-30
CREATE TABLE `cx_agent_alarm_12345` (
    `MSG_ID` varchar(64) NOT NULL COMMENT '消息id',
    `ALARM_TYPE` varchar(10) NOT NULL COMMENT '告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求',
    `DATE_ID` varchar(20) DEFAULT NULL COMMENT '日期id yyyy-MM-dd',
    `AGENT_ID` varchar(32) DEFAULT NULL COMMENT '告警坐席id',
    `AGENT_NAME` varchar(32) DEFAULT NULL COMMENT '告警坐席名称',
    `AGENT_PHONE` varchar(32) DEFAULT NULL COMMENT '告警坐席话机号',
    `ALARM_TIME` varchar(32) DEFAULT NULL COMMENT '告警时间',
    `IS_ALARM` varchar(10) DEFAULT '1' COMMENT '是否告警1 告警 2不告警',
    `ALARM_COUNT` varchar(32) NOT NULL COMMENT '告警次数，针对次数类的告警为对应通话记录中的告警次数，对于超长通话、话后超时最大1',
    `HANDLE_STATUS` varchar(32) DEFAULT '2' COMMENT '1.已处理 2未处理',
    `HANDLE_USER` varchar(64) DEFAULT NULL COMMENT '处理人id',
    `HANDLE_USER_ACCT` varchar(64) DEFAULT NULL COMMENT '处理人工号',
    `HANDLE_TIME` varchar(32) DEFAULT NULL COMMENT '处理时间',
    `HANDLE_TEMP` varchar(64) DEFAULT NULL COMMENT '处理通知模版',
    `AUDIT_LEVEN` varchar(10) DEFAULT NULL COMMENT '审核级别',
    PRIMARY KEY (`MSG_ID`,`ALARM_TYPE`,`ALARM_COUNT`) USING BTREE,
    KEY `INDEX_ALARM_TIME` (`ALARM_TIME`)
) COMMENT='坐席告警表';

-- 2025-06-05
CREATE TABLE `cx_agent_score_config_12345` (
    `MONTH_ID` varchar(32) NOT NULL COMMENT '月份id',
    `AGENT_NO` varchar(32) NOT NULL COMMENT '坐席工号',
    `AGENT_NAME` varchar(64) DEFAULT NULL COMMENT '坐席名称',
    `WORK_GROUP` varchar(64) DEFAULT NULL COMMENT '班组',
    `BUSI_NUMBER_SCORE` varchar(10) DEFAULT NULL COMMENT '业务量得分',
    `QUALITY_SCORE` varchar(10) DEFAULT NULL COMMENT '质检得分',
    `ATTENDANCE_SCORE` varchar(10) DEFAULT NULL COMMENT '出勤得分',
    `MONTHLY_EXAM_SCORE` varchar(10) DEFAULT NULL COMMENT '月考得分',
    `AFTER_LONG_SCORE` varchar(10) DEFAULT NULL COMMENT '话后处理得分',
    `DEDUCTION` varchar(10) DEFAULT NULL COMMENT '扣减',
    `REWARD` varchar(10) DEFAULT NULL COMMENT '奖励',
    `MONTHLY_RANKING` varchar(10) DEFAULT NULL COMMENT '月度排名',
    `ON_HOOK_SATISFACTION` varchar(10) DEFAULT NULL COMMENT '挂机满意度',
    `ALL_SCORE` varchar(10) DEFAULT NULL COMMENT '总分',
    `HONOR1` varchar(20) DEFAULT NULL COMMENT '个人荣誉1',
    `HONOR2` varchar(20) DEFAULT NULL COMMENT '个人荣誉2',
    `HONOR3` varchar(20) DEFAULT NULL COMMENT '个人荣誉2',
    `CREATE_TIME` varchar(20) DEFAULT NULL COMMENT '创建时间',
    `CREATE_NAME` varchar(30) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`MONTH_ID`,`AGENT_NO`)
) COMMENT='坐席月度评分导入配置';

CREATE TABLE `cx_work_score_config_12345` (
      `MONTH_ID` varchar(32) NOT NULL COMMENT '月份id',
      `WORK_GROUP` varchar(64) NOT NULL COMMENT '班组',
      `BUSI_NUMBER_SCORE` varchar(10) DEFAULT NULL COMMENT '业务量得分',
      `QUALITY_SCORE` varchar(10) DEFAULT NULL COMMENT '质检得分',
      `ATTENDANCE_SCORE` varchar(10) DEFAULT NULL COMMENT '出勤得分',
      `MONTHLY_EXAM_SCORE` varchar(10) DEFAULT NULL COMMENT '月考得分',
      `AFTER_LONG_SCORE` varchar(10) DEFAULT NULL COMMENT '话后处理得分',
      `DEDUCTION` varchar(10) DEFAULT NULL COMMENT '扣减',
      `REWARD` varchar(10) DEFAULT NULL COMMENT '奖励',
      `MONTHLY_RANKING` varchar(10) DEFAULT NULL COMMENT '月度排名',
      `ON_HOOK_SATISFACTION` varchar(10) DEFAULT NULL COMMENT '挂机满意度',
      `ALL_SCORE` varchar(10) DEFAULT NULL COMMENT '总分',
      `HONOR1` varchar(20) DEFAULT NULL COMMENT '个人荣誉1',
      `HONOR2` varchar(20) DEFAULT NULL COMMENT '个人荣誉2',
      `HONOR3` varchar(20) DEFAULT NULL COMMENT '个人荣誉2',
      `CREATE_TIME` varchar(20) DEFAULT NULL COMMENT '创建时间',
      `CREATE_NAME` varchar(30) DEFAULT NULL COMMENT '创建人',
      PRIMARY KEY (`MONTH_ID`,`WORK_GROUP`)
) COMMENT='班组月度评分导入配置';

-- 2025-06-06
CREATE TABLE `cx_12345_config` (
   `DATA_ID` varchar(32) NOT NULL COMMENT '数据id',
   `WORK_NUMBER_CONFIG` text COMMENT '工作量五维图配置字段',
   PRIMARY KEY (`DATA_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='12345配置表';


CREATE TABLE `cx_appeal_record` (
    `APPEAL_ID` varchar(64) NOT NULL COMMENT '主键id',
    `APPEAL_NAME` varchar(255) DEFAULT NULL COMMENT '诉求名称',
    `APPEAL_TYPE` varchar(10) DEFAULT NULL COMMENT '诉求类型',
    `APPEAL_CONTENT` text COMMENT '诉求内容',
    `APPEAL_SUBMIT_TIME` varchar(20) DEFAULT NULL COMMENT '诉求提交时间',
    `HW_AGENT_ID` varchar(10) DEFAULT NULL COMMENT '华为坐席工号',
    `SKILL_GROUP_ID` varchar(64) DEFAULT NULL COMMENT '班组id',
    `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户id',
    `APPEAL_RESULT` varchar(10) DEFAULT NULL COMMENT '审核结果 1通过 2不通过 3待审核',
    `APPEAL_STATE` varchar(10) DEFAULT NULL COMMENT '诉求状态 1 完成 2 执行中',
    `CREATE_TIME` varchar(20) DEFAULT NULL COMMENT '诉求创建时间',
    PRIMARY KEY (`APPEAL_ID`)
) COMMENT='首发诉求记录表';

CREATE TABLE `cx_appeal_audit_record` (
      `ID` varchar(64) NOT NULL COMMENT '主键id',
      `APPEAL_ID` varchar(64) DEFAULT NULL COMMENT '首发诉求id',
      `AUDIT_RESULT` varchar(10) DEFAULT NULL COMMENT '审核结果 1通过 2不通过 3待审核',
      `HANDLING_OPINION` text COMMENT '处理意见',
      `AUDIT_TIME` varchar(20) DEFAULT NULL COMMENT '审核时间',
      `AUDIT_LEVEL` varchar(10) DEFAULT NULL COMMENT '审核级别 1、班长 2、值班长 3......',
      `AUDIT_USER_ID` varchar(64) DEFAULT NULL COMMENT '审核人id，京办app没有这个id',
      `AUDIT_USER_NAME` varchar(64) DEFAULT NULL COMMENT '审核名称 如三级 二级 一级 部门',
      PRIMARY KEY (`ID`)
)  COMMENT='首发诉求审核记录表';
CREATE INDEX idx_appeal_id_audit_time ON cx_appeal_audit_record (APPEAL_ID, AUDIT_TIME);

-- 2025-06-09新增
CREATE TABLE `cx_agent_order_day_stat` (
   `AGENT_NO` varchar(32)  NOT NULL COMMENT '坐席工号',
   `DATE_ID` varchar(20)  NOT NULL COMMENT '时间 yyyy-MM-dd',
   `AGENT_NAME` varchar(64)  DEFAULT NULL COMMENT '坐席名称',
   `WORK_GROUP` varchar(64)  DEFAULT NULL COMMENT '工作组名称',
   `WORK_GROUP_ID` varchar(32)  DEFAULT NULL COMMENT '工作组id',
   `ORDER_COUNT` varchar(10)  DEFAULT NULL COMMENT '工单数量',
   PRIMARY KEY (`AGENT_NO`,`DATE_ID`)
) COMMENT='坐席工单量日统计表';
CREATE TABLE `cx_skill_work_relation` (
  `ID` varchar(32) NOT NULL COMMENT '主键',
  `SKILL_GROUP_ID` varchar(64) DEFAULT NULL COMMENT '部门id',
  `WORK_GROUP_ID` varchar(64) DEFAULT NULL COMMENT '华为工作组id',
  `CREATE_TIME` varchar(32) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`)
) COMMENT='华为班组和系统部门的关系表';


CREATE TABLE CX_NOTICE_TASK(
`TASK_ID` VARCHAR(32) NOT NULL  COMMENT '任务号' ,
`ENT_ID` VARCHAR(32) NOT NULL  COMMENT '企业ID' ,
`BUSI_ORDER_ID` VARCHAR(32) NOT NULL  COMMENT '订购ID' ,
`NOTICE_TYPE` VARCHAR(2) NOT NULL  COMMENT '通知类型;0-助手消息;1-短信通知;2-外呼通知' ,
`TASK_STATE` VARCHAR(2)   COMMENT '任务状态;0-未发送;1-已发送;2-发送中' ,
`TEMLPATE_ID` VARCHAR(32)   COMMENT '模版ID' ,
`SEND_TYPE` VARCHAR(2) NOT NULL  COMMENT '发送方式;1-立即发送;2-指定时间' ,
`CREATED_BY` VARCHAR(50) NOT NULL  COMMENT '创建人' ,
`CREATED_TIME` VARCHAR(19) NOT NULL  COMMENT '创建时间' ,
PRIMARY KEY (TASK_ID)
)  COMMENT = '通知任务表';


CREATE TABLE CX_NOTICE_TEMPLATE(
   `ID` VARCHAR(32) NOT NULL  COMMENT 'ID' ,
   `TEMPLATE_TYPE` VARCHAR(1)   COMMENT '模版类型;0-助手消息模版;1-短信通知模版;2-外呼通知模版' ,
   `TEMPLATE_NAME` VARCHAR(100)   COMMENT '模版名称' ,
   `TEMPLATE_SUBJECT` VARCHAR(255)   COMMENT '模版主题' ,
   `SMS_TEMPLATE_ID` VARCHAR(32)   COMMENT '短信模版ID;仅短信通知模板用，其他类型可为空' ,
   `DETAIL` TEXT   COMMENT '详细信息;支持参数化' ,
   `MESSAGE_TYPE` VARCHAR(1)   COMMENT '消息类型;仅助手消息模板用，其他类型可为空' ,
   `CREATED_BY` VARCHAR(32) NOT NULL  COMMENT '创建人' ,
   `CREATED_TIME` VARCHAR(19) NOT NULL  COMMENT '创建时间' ,
   `UPDATED_BY` VARCHAR(32) NOT NULL  COMMENT '更新人' ,
   `UPDATED_TIME` VARCHAR(19) NOT NULL  COMMENT '更新时间' ,
   PRIMARY KEY (ID)
)  COMMENT = '消息通知模版';

CREATE TABLE CX_SMS_TASK_OBJ(
`OBJ_ID` VARCHAR(32) NOT NULL  COMMENT '名单ID' ,
`ENT_ID` VARCHAR(32) NOT NULL  COMMENT '企业ID' ,
`BUSI_ORDER_ID` VARCHAR(32) NOT NULL  COMMENT '订购ID' ,
`MONTH_ID` INT NOT NULL  COMMENT '月份ID' ,
`DATE_ID` INT NOT NULL  COMMENT '日期ID' ,
`TASK_ID` VARCHAR(32) NOT NULL  COMMENT '任务ID' ,
`CUST_NAME` VARCHAR(100) NOT NULL  COMMENT '客户名称' ,
`PHONE` VARCHAR(100) NOT NULL  COMMENT '手机号' ,
`CONTENT` TEXT(255) NOT NULL  COMMENT '消息内容' ,
`AGENT_PHONE` VARCHAR(20) NOT NULL  COMMENT '坐席工号' ,
`NEXT_RUN_TIME` INT NOT NULL DEFAULT 0 COMMENT '下次执行时间;立即发送时为0，指定时间发送填指定时间的时间戳' ,
`TASK_STATE` VARCHAR(2)   COMMENT '名单状态' ,
`SEND_STATE` VARCHAR(2) NOT NULL  COMMENT '发送状态;0-待发送、1-发送中、2-发送成功、3-发送失败' ,
`RECEIPT_STATE` VARCHAR(2) NOT NULL DEFAULT 2 COMMENT '回执状态;短信回执状态  0 失败  1成功 2未知' ,
`CREATED_BY` VARCHAR(32) NOT NULL  COMMENT '创建人' ,
`CREATED_TIME` VARCHAR(19) NOT NULL  COMMENT '创建时间' ,
`WORK_GROUP_ID` VARCHAR(255) NOT NULL  COMMENT '班组ID' ,
`WORK_GROUP_NAME` VARCHAR(255) NOT NULL  COMMENT '班组名称' ,
PRIMARY KEY (OBJ_ID)
)  COMMENT = '短信通知名单表';

CREATE TABLE CX_CALL_TASK_OBJ(
 `OBJ_ID` VARCHAR(32) NOT NULL  COMMENT '名单ID' ,
 `ENT_ID` VARCHAR(32) NOT NULL  COMMENT '企业ID' ,
 `BUSI_ORDER_ID` VARCHAR(32) NOT NULL  COMMENT '订购ID' ,
 `MONTH_ID` INT NOT NULL  COMMENT '月份ID' ,
 `DATE_ID` INT NOT NULL  COMMENT '日期ID' ,
 `TASK_ID` VARCHAR(32) NOT NULL  COMMENT '任务ID' ,
 `CUST_NAME` VARCHAR(100) NOT NULL  COMMENT '客户名称' ,
 `PHONE` VARCHAR(100) NOT NULL  COMMENT '手机号' ,
 `CONTENT` TEXT NOT NULL  COMMENT '消息内容' ,
 `AGENT_PHONE` VARCHAR(20) NOT NULL  COMMENT '坐席工号' ,
 `NEXT_RUN_TIME` INT NOT NULL  COMMENT '下次执行时间' ,
 `CALL_RESULT` VARCHAR(3)   COMMENT '呼叫结果;0-成功；1-无人应答；2-用户忙；3-用户挂机；4-网络忙；5-空号；6-用户拒绝；7-关机；8-暂停服务；9-不在服务区；10-传真机；11-用户欠费；99-系统错误；100-其他错误；111-三方会议失败；112-转移失败；113-呼坐席话机失败；114-外呼失败；115-无效的企业；116-外呼时坐席已签出；117-被系统签出；999-未定义错误' ,
 `START_TIME` VARCHAR(19)   COMMENT '外呼开始时间' ,
 `END_TIME` VARCHAR(19)   COMMENT '外呼结束时间' ,
 `CALL_DURATION` VARCHAR(10)   COMMENT '通话时长' ,
 `CREATED_BY` VARCHAR(100) NOT NULL  COMMENT '创建人' ,
 `CREATED_TIME` VARCHAR(19) NOT NULL  COMMENT '创建时间' ,
 `WORK_GROUP_ID` VARCHAR(255) NOT NULL  COMMENT '班组ID' ,
 `WORK_GROUP_NAME` VARCHAR(255) NOT NULL  COMMENT '班组名称' ,
 `TASK_STATE` VARCHAR(1) NOT NULL DEFAULT '0' COMMENT '名单状态;0-未提交；1-已提交' ,
 PRIMARY KEY (OBJ_ID)
)  COMMENT = '外呼通知名单表';

CREATE TABLE `cx_agent_help_distribute_table` (
  `DISTRIBUTE_ID` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '派发id',
  `MSG_ID` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息id',
  `DISTRIBUTE_TIME` varchar(32) DEFAULT NULL COMMENT '派发时间',
  `DISTRIBUTE_AGENT_ID` varchar(32) DEFAULT NULL COMMENT '派发人id',
  `DISTRIBUTE_AGENT_ACC` varchar(64) DEFAULT NULL COMMENT '派发人账号',
  PRIMARY KEY (`DISTRIBUTE_ID`),
  KEY `INDEX_MSG_ID` (`MSG_ID`),
  KEY `INDEX_DISTRIBUTE_TIME` (`DISTRIBUTE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='坐席求助处理记录派发表';

CREATE TABLE `CX_CTI_CALL_DATA` (
    `DATE_ID` VARCHAR(32)   NOT NULL     COMMENT '日期id yyyy-MM-dd' ,
    `HOUR_ID` VARCHAR(32)   NOT NULL     COMMENT '小时id HH:mm' ,
    `CALL_RATE` VARCHAR(32)   NULL     COMMENT '接通率' ,
    PRIMARY KEY  (`DATE_ID`,`HOUR_ID`)
) COMMENT='华为CTI接口接口数据表';

-- 20250717新增
ALTER TABLE  cx_agent_alarm_12345 ADD HELP_TYPE varchar(10) COMMENT '求助类型';
ALTER TABLE  cx_agent_alarm_12345 ADD HANDEL_USER_ROLE varchar(10) COMMENT '处理人角色 1值班长 2班长';
ALTER TABLE  cx_agent_alarm_12345 ADD WORK_GROUP_ID varchar(10) COMMENT '班组id';
ALTER TABLE  cx_agent_alarm_12345 ADD MONTH_ID varchar(10) COMMENT '月份id';
CREATE INDEX WORK_GROUP_ID_INDEX on cx_agent_alarm_12345 (WORK_GROUP_ID);


-- 20250717 通知任务表新增月份id、日期id，方便报表统计
ALTER TABLE CX_NOTICE_TASK ADD MONTH_ID INT NOT NULL  COMMENT '月份ID';
ALTER TABLE CX_NOTICE_TASK ADD DATE_ID INT NOT NULL  COMMENT '日期ID';