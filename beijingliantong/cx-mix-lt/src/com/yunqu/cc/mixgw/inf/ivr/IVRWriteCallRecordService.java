package com.yunqu.cc.mixgw.inf.ivr;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.thread.ExecutorProcessPool;

import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;


/**
 * 本接口供ivr调用，用于外线坐席，当该"外线坐席"在外工作需要直接回访用户时，通话记录中没有写坐席名称、坐席id、话机号、号码归属地、技能组
 * <AUTHOR>
 *
 */

public class IVRWriteCallRecordService extends IService {
	private static Logger logger = CommonLogger.getLogger("callOut");
	private ExecutorProcessPool pool = ExecutorProcessPool.getInstance();

	@Override
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
		logger.info("[IVRWriteCallRecordService] << " + jsonObject.toJSONString());
		JSONObject value=(JSONObject) JSONPath.eval(jsonObject, "$.data.value");
		JSONObject resultObject = new JSONObject();
		
		EasyQuery query = QueryFactory.getReadQuery();
		pool.execute(new Thread(() -> {
			runWrite(query,value);
		}));
		resultObject.put("respCode", "000");
		resultObject.put("desc", "成功");
		return result(resultObject);
	}
	
	//执行写入方法
	private void runWrite(EasyQuery query,JSONObject value) {
		try {
			String caller = value.getString("caller");//主叫
			String serialId = value.getString("serialId");//话单id
			if(StringUtils.isBlank(serialId)) {
				logger.info("serialId is not null");
				return;
			}
			EasySQL sql=new EasySQL("select USER_ID from cc_user") ;
			sql.append("where ent_id in (select  ENT_ID from CC_ENT where ENT_STATE=0)");
			sql.appendLike(caller,"and USER_STATE=0 and ADMIN_FLAG=0 and F_PHONE_NUM like ?");
			List<EasyRow> list=query.queryForList(sql.getSQL(),sql.getParams());
			String userId=list.get(0).getColumnValue("USER_ID");
			EasySQL userSql=new EasySQL("select t1.PREFIX_NUM,t1.ENT_ID,t1.AGENT_NAME,t2.USER_ACCT,t2.F_PHONE_NUM from "+Constants.LT_BUSI_DB+".cc_busi_user t1 "
					+ "inner join cc_user t2 on t1.USER_ID=t2.USER_ID ") ;
			userSql.append(userId,"where t1.USER_ID =  ? ");
			List<EasyRow> userList=query.queryForList(userSql.getSQL(),userSql.getParams());
			EasyRecord record = new EasyRecord(Constants.LT_BUSI_DB+".cc_call_record","SERIAL_ID");
			record.set("SERIAL_ID", serialId);
			//坐席名称
			record.set("AGENT_NAME",userList.get(0).getColumnValue("AGENT_NAME"));
			//坐席工号
			record.set("AGENT_PHONE",userList.get(0).getColumnValue("USER_ACCT"));
			//坐席工号
			record.set("AGENT_ID",userId);
			//话机号码
			record.set("PHONE_NUM",userList.get(0).getColumnValue("F_PHONE_NUM"));
			//获取技能组编码
			userSql=new EasySQL("SELECT SKILL_GROUP_ID FROM "+Constants.LT_BUSI_DB+".cc_skill_group_user");
			userSql.append(userId,"where USER_ID =  ? ");
			String groupId = query.queryForString(userSql.getSQL(), userSql.getParams());
			//技能组编码
			record.set("GROUP_ID",groupId);
			//H码
			String called = value.getString("phone");//被叫
			if(StringUtils.isNotBlank(called)) {
				String code = "";
				if("0".equals(called.substring(0,1)) && called.length()>10) {
					code = called.substring(1,8);
				}else {
					code = called.substring(0,7);
				}
				if(StringUtils.isNotBlank(code) ) {
					logger.info("code:"+code);
					String hcode = CacheUtil.get("M_HCODE_"+code);
					if(StringUtils.isNotBlank(hcode)) {
						JSONObject json = JSONObject.parseObject(hcode);
						if(json!=null) {
							logger.info("hCode:"+json.toJSONString());
							record.set("AREA_CODE",json.getString("area"));
						}
					}
				}
			}
			query.update(record);
		} catch (Exception ex) {
			logger.error("IVRKeyService execute sql error,cause:"+ex.getMessage(), ex);
		}
	}
	
	private JSONObject result(JSONObject obj){
		
		logger.info("obj:"+obj.toJSONString());
		JSONObject json=new JSONObject();
		json.put("data",obj);
		return json;
	}

	public static void main(String[] args) {
		JSONObject type=new JSONObject();
		type.put("2","1");
		System.out.print(new JSONObject().put("date",type));
	}
	
}