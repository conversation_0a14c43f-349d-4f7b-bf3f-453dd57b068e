package com.yunqu.cc.mixgw.dao.report.call;


import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.StatDaoContext;
import com.yunqu.cc.mixgw.service.StatistcServiceSql;


@WebObject(name="CallStatDao")
public class CallStatDao extends StatDaoContext {

	/**
	 * 1、坐席话务量统计报表
	 * @return
	 */
	@WebControl(name="agentCallStatList", type=Types.LIST)
	public JSONObject agentCallStatList() {
		String limitDate = param.getString("limitDate");
		String starDate="";
		String endDate = "";
		if(StringUtils.notBlank(limitDate)) {
			String[] split = limitDate.split("~");
			starDate=split[0].trim().replace("-", "");
			endDate=split[1].trim().replace("-", "");
		}
		String agentId = "";
		if(StringUtils.isNotBlank(param.getString("agentId"))){
			JSONArray jsonArray = param.getJSONArray("agentId");
			for (int i = 0; i < jsonArray.size(); i++) {
				agentId += ",'"+jsonArray.getString(i)+"'";
			}
			agentId = agentId.substring(1);
		}
		String stType = param.getString("stType");
		
		String ifNull = "IFNULL";
		DBTypes types = this.getQuery().getTypes();
		if(types == DBTypes.MYSQL){
			ifNull = "IFNULL";
		}else if(types == DBTypes.ORACLE){
			ifNull = "NVL";
		}else if(types == DBTypes.PostgreSql){
			ifNull = "COALESCE";
		}

		String skillId = getSkillIdByRole(param.getString("skillId"));
		
		EasySQL sql = StatistcServiceSql.getService().agentCallStatListSql(this.getDbName(), this.getEntId(), this.getBusiOrderId(), starDate, endDate, skillId, stType, agentId, ifNull);
        
		CommonLogger.logger.info("sql:" + sql.getSQL() + ",param:" + JSONObject.toJSONString(sql.getParams()));
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		//添加一个数据更新时间
		result.put("updateTime", getYcstatTableByTaget("CC_RPT_CALL_STAT").get("UPDATE_TIME"));
		result.put("statisticType", stType);
		return result;
	}
	
	/**
	 * 4、技能组话务量统计报表
	 * @return
	 */
	@WebControl(name="groupCallStatList", type=Types.LIST)
	public JSONObject groupCallStatList() {
		//日期
		String starDate=" ";
		String endDate=" ";
		String limitDate = param.getString("limitDate");
		if(StringUtils.notBlank(limitDate)) {
			String[] split = limitDate.split("~");
			starDate=split[0].replace("-", "");
			endDate=split[1].replace("-", "");
		}
		
		String ifNull = "IFNULL";
		DBTypes types = this.getQuery().getTypes();
		if(types == DBTypes.MYSQL){
			ifNull = "IFNULL";
		}else if(types == DBTypes.ORACLE){
			ifNull = "NVL";
		}else if(types == DBTypes.PostgreSql){
			ifNull = "COALESCE";
		}
		
		String skillId = getSkillIdByRole(param.getString("skillId"));
		
		EasySQL sql = null;
		sql = StatistcServiceSql.getService().groupCallStatListSql(getDbName(),getEntId(),getBusiOrderId(),starDate, endDate, skillId, ifNull);
		
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams());
		return result;
	}

}
