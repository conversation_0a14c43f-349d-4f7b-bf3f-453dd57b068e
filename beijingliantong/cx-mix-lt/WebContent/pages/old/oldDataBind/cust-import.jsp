<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传数据</title>
	<style>
		 #importDataList-body td:nth-child(even) {  
			 display: none;
   		 }  
	    #importDataList-body td:nth-child(odd) {  
	        background: White;  
	    } 
	    #autoAddZeroTr{display: none!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="custUpload" method="post" enctype="multipart/form-data">
				
				  <table class="table table-vzebra">
	                    <tbody>
		                     <tr>
			                        <td class="required">用户信息</td>
			                        <td>
			                        	 <input type="file" id="file" name="file" onchange="upload(this)" class="hidden" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/>
			                        	 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()"> 点击上传  </button>
					                 	 <a class="btn btn-sm btn-link" href="${ctxPath}/template/oldImportTemp.xlsx " download="用户信息模板.xlsx" target="view_window">下载模板</a>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c">
			                        	读取结果
			                        </td>
			                        <td id="import_result">
			                         	
			                        </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this);parent.oldDataBindList.loadData()">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	 jQuery.namespace("custUpload");
	
	function upload(file){
		if (file.files && file.files[0]) {
        	console.log('你选择的文件大小' + (file.files[0].size / 1024).toFixed(0) + "kb");
        	if(file.files[0].size > 5*1024*1024){//文件大小不能超过5M
        		layer.msg("导入文件大小不能超过5M!",{icon:7,time:3000});
        		return;
        	}
		}
		var formData = new FormData($("#custUpload")[0]); 
		$.ajax({  
	          url: '${ctxPath}/servlet/import?action=custUpload',  
	          type: 'POST',  
	          data: formData,async: true,cache: false,contentType: false,processData: false,  
	          success: function (result) {
	        	  layer.closeAll('dialog');
	        	  console.info(result);
	        	  if(result.state==1){
	        		  var results=result.data;
	        		  for (var i=0;i<results.length;i++)
	        		  { 
	        			  $("#import_result").append(results[i] + "<br>");
	        		  }
		        
	        	  }else{
		        	  layer.closeAll('dialog');
	        		  layer.alert(result.msg);
	        	  }
	        	  $("#file").val("");
	          },error:function(){
	        	  layer.closeAll('dialog');
	          },beforeSend:function(){
	        	  	layer.msg('数据解析中...', {icon: 16 ,shade: 0.01,time:0,offset:'180px'});
	          } 
	     }); 
	}



	 

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>