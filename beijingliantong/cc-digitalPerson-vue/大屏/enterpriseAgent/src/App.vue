<template>
  <div id="app">
    <scale-box :width="7680" :height="2160" bgc="transparent" :delay="100" :isFlat="false">
      <router-view/>
    </scale-box>
  </div>
</template>

<script>
import ScaleBox from "vue2-scale-box";

export default {
  name: 'App',
  components: {ScaleBox},
  data() {
    return {}
  },
  mounted() {

  },
  methods: {}
}
</script>

<style lang="scss">
#app {
  background: #000000;
  height: 100%;
}
</style>
