import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const createRouter = () => new Router({
    scrollBehavior: () => ({y: 0}),
    routes: [
        {
            path: '/',
            component: () => import('@/views/index/index'),
            meta: {title: '企业坐席监控'},
            hidden: true,
        }
    ]
})

const router = createRouter()

export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

export default router
  