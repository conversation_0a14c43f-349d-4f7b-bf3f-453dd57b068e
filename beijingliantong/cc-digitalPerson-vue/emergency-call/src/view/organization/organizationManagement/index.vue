<template>
  <div class="flex flex-gap" style="flex-wrap: nowrap;align-items: normal" @click="hideMenu">
    <card-slot class="left">
      <div class="title line" style="display: flex;justify-content: space-between;">
        <span>{{ $t('组织架构管理') }}</span>
      </div>
      <el-input v-model="filterText" class="mg-b16" size="small" suffix-icon="el-icon-search"
        :placeholder="$t('请输入关键字搜索')" clearable @input="inputData">
      </el-input>
      <div class="treeBox">
        <Orgtree ref="Orgtree" @getList="getTree" @clickNode="clickNode"></Orgtree>
      </div>
    </card-slot>
    <div style="width:calc(100% - 248px);">
      <card-slot class="flex-column">
        <div class="flex-space-between flex-gap" style="overflow: hidden">
          <div class="title titelFlex">
            <!-- <div>{{ $t('热线中心') }}</div> -->
            <!-- <el-button style="text-align: right;display: flex;" size="small" @click="config">
              <span style="display: flex;">
                <i class="el-icon-ognSetting" />
                {{ $t('组织类型配置') }}
              </span>
            </el-button> -->
          </div>
        </div>
        <div class="label-panel mg-t16">
          <div class="label blue">
            <div style="display: flex; align-items: center;">
              <div class="leftImg" style="background-color: #ccdbeb;">
                <div class="numberBox">123</div>
              </div>
              <div style="margin-left:16px;color: #252525;">编号</div>
            </div>
            <!-- <countTo :endVal='66666' class="rightCount"></countTo> -->
            <div class="rightCount">{{ SKILL_GROUP_ID || '-' }}</div>
          </div>
          <div class="label orange">
            <div style="display: flex; align-items: center;">
              <div class="leftImg" style="background-color: #feecc9;">
                <el-image style="width: 30px; height: 30px" :src="groupImg" fit="cover"></el-image>
              </div>
              <div style="margin-left:16px;color: #252525;">部门名称</div>
            </div>
            <div class="rightCount">{{ SKILL_GROUP_NAME || '-' }}</div>
          </div>
          <div class="label poolBlue">
            <div style="display: flex; align-items: center;">
              <div class="leftImg" style="background-color: #cff0fd;">
                <el-image style="width: 30px; height: 30px" :src="memberImg" fit="cover"></el-image>
              </div>
              <div style="margin-left:16px;color: #252525;">成员人数</div>
            </div>
            <countTo :endVal='AGENT_COUNT' class="rightCount"></countTo>
          </div>
        </div>
      </card-slot>
      <card-slot class="flex-column mg-t16">
        <div class="title titelFlex">
          <div>{{ $t('成员列表') }}</div>
          <div style="display: flex;">
            <div class="searchBox">
              <div class="item">关键字：</div>
              <el-input v-model="table.key" size="small" placeholder="请输入(账号、姓名)"></el-input>
            </div>
            <el-button style="text-align: right;display: flex;" size="small" icon="el-icon-search"
              @click="getDeptRecordList">
              {{ $t('搜索') }}
            </el-button>
            <el-button style="text-align: right;display: flex;" size="small" @click="addMembers">
              <span style="display: flex;">
                <i class="el-icon-addSetting" />
                {{ $t('新增成员') }}
              </span>
            </el-button>
          </div>
        </div>
        <base-pagination-table @pageChange="handlePageChange"
          :pageNav="{ pageIndex: pageNav.page, pageSize: pageNav.size, totalRow: pageNav.totalRow, layout: 'prev, pager, next, jumper' }"
          class="mg-t16">
          <template #table="slotPros">
            <el-table :data="table.data" v-loading="table.loading" border :height="slotPros.tableHeight" stripe>
              <el-table-column :label="$t('序号')" width="60px" type="index"></el-table-column>
              <el-table-column :label="$t('姓名')" min-width="200px" prop="USERNAME"></el-table-column>
              <el-table-column :label="$t('登录账号')" min-width="200px" prop="USER_ACCT"></el-table-column>
              <el-table-column :label="$t('部门')" min-width="200px" prop="SKILL_GROUP_NAME"></el-table-column>
              <el-table-column :label="$t('所属地方')" min-width="200px" prop="AREA_NAME"></el-table-column>
              <el-table-column :label="$t('角色')" min-width="100px" prop="ROLE_NAME"></el-table-column>
              <el-table-column :label="$t('是否启用')" min-width="100px" prop="CENTER_USER_STATE">
                <template slot-scope="scope">
                  <el-switch v-model="scope.row.CENTER_USER_STATE" inactive-value="1" active-value="0" active-color="#1B5FA4" inactive-color="#E3E5EE"
                    @change="changeUserStatus(scope.row)"></el-switch>
                </template>
              </el-table-column>
              <el-table-column :label="$t('操作')" width="80px" fixed="right">
                <template slot-scope="scope">
                  <el-link type="warning" @click="delPerson(scope.row)">移除</el-link>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </base-pagination-table>
      </card-slot>
    </div>
    <addMember ref="addMember" @getUserList="getDeptRecordList"></addMember>
  </div>
</template>

<script>
import Orgtree from '@/view/organization/components/Orgtree'
import addMember from '../components/addMember.vue';
import { getDeptTree, getDeptInfo, getDeptRecordList, delGroupUser, UpdateUserStatus } from '@/api/base/organization'
export default {
  components: { Orgtree, addMember },
  data() {
    return {
      groupImg: require('../../../assets/images/organiza/groupImg.png'),
      memberImg: require('../../../assets/images/organiza/member.png'),
      filterText: '',
      pageNav: {
        page: 1,
        size: 10,
        totalRow: 0,
      },
      table: {
        key: '',
        alldata: [],
        data: [],
        loading: false
      },
      SKILL_GROUP_ID: '',
      AGENT_COUNT: 0,
      GROUP_TYPE_NAME: '',
      SKILL_GROUP_NAME: '',
      pk: '',
      timer: 0,
      fullTree:[]
    };
  },
  methods: {
    changeUserStatus(item) {
      let dt = {
        userId: item.USER_ID,
        status: item.CENTER_USER_STATE
      }
      UpdateUserStatus(dt).then(res => {
        if (res.data.state == 1) {
          this.$message.success(res.data.msg)
        } else {
          this.$message.error(res.data.msg)
        }
      }).finally(res => {
        this.getDeptRecordList()
      })
    },
    inputData(value) {
      if (this.timer === 0) {
        this.timer = setTimeout(() => {
          this.filterTree(this.fullTree, value,)
        }, 1000)
      } else {
        clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.filterTree(this.fullTree, value,)
        }, 1000)
      }
    },
    filterTree(treeData, inputValue, key) {
      let newTreeData = [];
      treeData.forEach(item => {
        if (item.name.indexOf(inputValue) > -1) {
          newTreeData.push(item)
        } else {
          if (item.children && item.children.length > 0) {
            // 父级不匹配，继续向下查询子级是否匹配
            const arr = this.filterTree(item.children, inputValue, key);
            // 如果子级匹配，将符合条件的子级和父级push到目标数组中
            if (arr && arr.length > 0) {
              const obj = {
                ...item,
                children: arr
              };
              newTreeData.push(obj);
            }
          }
        }
      });
      this.$refs.Orgtree.setTree(newTreeData)
      return newTreeData;
    },
    clickNode(dt) {
      this.table.loading = true;
      this.pk = dt.id
      let data = {
        pk: dt.id
      }
      getDeptInfo(data).then(res => {
        if (res.data.data) {
          this.SKILL_GROUP_ID = res.data.data.SKILL_GROUP_ID || ''
          this.GROUP_TYPE_NAME = res.data.data.GROUP_TYPE_NAME || ''
          this.SKILL_GROUP_NAME = res.data.data.SKILL_GROUP_NAME || ''
          this.AGENT_COUNT = Number(res.data.data.AGENT_COUNT) || 0
        }
      })
      this.getDeptRecordList()
    },
    getDeptRecordList() {
      this.table.loading = true;
      let param = {
        pageType: '3',
        pageIndex: this.pageNav.page,
        pageSize: this.pageNav.size,
        pk: this.pk,
        agentName: this.table.key
      }
      getDeptRecordList(param).then(res => {
        if (res.data.state == 1) {
          this.pageNav.totalRow = res.data.totalRow
          this.table.data = res.data.data
        }
      }).finally(res => {
        this.table.loading = false;
      })
    },
    hideMenu() {
      this.$refs.Orgtree.hideMenu()
    },
    handlePageChange(val) {
      this.pageNav.page = val
      this.getDeptRecordList()
    },
    addMembers() {
      this.$refs.addMember.open(this.pk)
    },
    config() {
      try {
        top.popup.openTab({
          url: '/yc-emgnotify/pages/emerg/index.html#/organization/typeConfig',
          id: 'typeConfig',
          title: '组织类型配置',
        })
      } catch {
        this.$router.push({ path: '/organization/typeConfig' })
      }
    },
    delPerson(item) {
      this.$confirm('此操作将移除该角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          userId: item.USER_ID,
          skillGroupId: item.SKILL_GROUP_ID,
        }
        delGroupUser(data).then(res => {
          if (res.data.state == 1) {
            this.$message({
              type: 'success',
              message: '移除成功!'
            });
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            });
          }
        }).finally(res => {
          this.getDeptRecordList()
        })

      }).catch(() => {
      });
    },
    getTree() {
      getDeptTree().then(res => {
        if (res.data.state == 1) {
          this.fullTree = [res.data.data]
          this.$refs.Orgtree.setTree([res.data.data])
        }
      }).finally(res => {
        this.getDeptRecordList()
      })
    }
  },
  mounted() {
    this.getTree()
  }
};
</script>
<style lang="scss" scoped>
.el-link.el-link--warning {
  color: #F4664A !important;
}

.searchBox {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .item {
    color: #707070;
    font-weight: normal;
  }
}

.el-icon-ognSetting {
  background: url('../../../assets/images/organiza/user-settings-line.png') no-repeat;
  background-size: cover;
  width: 14px;
  height: 14px;
}

.el-icon-addSetting {
  background: url('../../../assets/images/organiza/user-add-line.png') no-repeat;
  background-size: cover;
  width: 14px;
  height: 14px;
}

.title {
  font-size: 16px;
  color: #252525;
  font-weight: bold;
  white-space: nowrap;


  &.line {
    border-bottom: 1px solid #E3E5EE;
    margin: 5px -24px 16px -24px;
    padding: 0 24px 16px 24px;
  }
}

.titelFlex {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.left {
  width: 228px !important;
  height: calc(100vh - 32px) !important;
  flex-shrink: 0;
  overflow: hidden;

  .treeBox {
    margin: 0 -24px;
    height: calc(100% - 90px);
    overflow: scroll;

    .allTree {
      padding: 0 24px;
      color: #606266;
      height: 46px;
      line-height: 46px;
      cursor: pointer;
      user-select: none;

      &:hover {
        background: #f8f8f8;
      }
    }
  }

  ::v-deep .el-tree {
    width: fit-content;
    min-width: -webkit-fill-available;

    &-node__content {
      height: 46px;
      padding-right: 16px;
      box-sizing: border-box;

      >.el-tree-node__expand-icon {
        margin: 0 3px 0 16px;
      }
    }

    &--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      border-left: 3px solid #1B5FA4;

    }
  }
}

.label {
  font-size: 14px;
  color: #707070;
  white-space: nowrap;
}

::v-deep .el-input__suffix .el-input__icon {
  line-height: 32px !important;
}

.tableBox {
  height: calc(100vh - 160px);

  .btnText {
    cursor: pointer;
    color: #1B5FA4;
    font-size: 14px;
    user-select: none;

    &:active {
      color: #2689eb;
    }

    &:last-child {
      color: #F4664A;
    }
  }

  .tag {
    display: inline-block;
    padding: 1px 8px;
    border-radius: 4px;
    color: #707070;

    &.green {
      background: rgba(82, 196, 26, 0.1);
      color: #52C41A;
    }

    &.grey {
      background: rgba(37, 37, 37, 0.1);
    }
  }
}

.label-panel {
  width: 100%;
  display: flex;

  .label {
    padding: 13px 24px;
    width: 100%;
    margin-right: 16px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:nth-child(3) {
      margin-right: 0;
    }

    // display: flex;
    // justify-content: space-between;

    .leftImg {
      width: 62px;
      height: 62px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .numberBox {
        width: 28px;
        height: 20px;
        color: #fff;
        background-color: #1b5fa4;
        border-radius: 2px;
        font-size: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
      }


    }

    .rightCount {
      font-size: 24px;
      font-weight: bold;
    }

  }

  .blue {
    background-color: #eff4f9;
    color: #1B5FA4;
  }

  .orange {
    background-color: #fff9ee;
    color: #FAAD14;
  }

  .poolBlue {
    background-color: #f0fafe;
    color: #2BBDF7;
  }
}
</style>