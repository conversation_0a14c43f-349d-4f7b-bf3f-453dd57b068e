<template>
    <div class="list"  v-loading="loadding">
        <card-slot class="mg-b16">
            <div class="flex">
                <div class="time">时间：</div>
                <el-date-picker
                    v-model="params.dataList"
                    type="daterange"
                    format="yyyy 年 MM 月 dd 日"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-input style="flex: 1; margin-left: 8px" :placeholder="'请输入事务名称'" v-model="params.value">
                    <el-button slot="append" @click="doSearch">{{ $t("检索") }}</el-button>
                    <!-- <template slot="append"><span @click="doSearch" style="cursor: pointer;">检索</span></template> -->
                </el-input>
            </div>
        </card-slot>
        <card-slot class="mg-b16">
            <base-item
                title="事务分类："
                class="mg-b16"
                :list="JY_TRANS_TYPE"
                @handleToDelect="selectTransaction"
                active=""></base-item>
            <base-item
                title="任务状态："
                class="mg-b16"
                :list="JY_TASK_STATUS_OTHER"
                @handleToDelect="selectQuest"
                active=""></base-item>
            <div class="flex">
                <span style="padding-right: 8px; color: #707070">测试事件：</span>
                <el-switch
                    v-model="params.operSwitch"
                    active-value="Y"
                    inactive-value="N"
                    active-color="#1B5FA4"
                    @change="() => {pageNav.pageIndex=1;doSearch()}"
                    inactive-color="#E3E5EE">
                </el-switch>
            </div>
        </card-slot>
        <div v-if="list.length > 0" >
            <card-with-content-slot :list="list" :hasState="false">
                <template #border-img>
                    <img src="@/assets/images/common/transaction.png" alt="" />
                </template>
                <template #type="{ itemObj }">
                    <div>{{ "事务分类：" + JY_TRANS_TYPE[itemObj.SECOND_TYPE] }}</div>
                </template>
                <template #auditState="{ itemObj }">
                    <div
                        v-if="JY_TASK_STATUS_OPTIONS[itemObj.TASK_STATUS]"
                        class="audit"
                        :style="{
                            color: JY_TASK_STATUS_OPTIONS[itemObj.TASK_STATUS].color,
                            background: JY_TASK_STATUS_OPTIONS[itemObj.TASK_STATUS].bgColor,
                        }">
                        {{ JY_TASK_STATUS[itemObj.TASK_STATUS] }}
                    </div>
                </template>
                <template #content="{ itemObj }">
                    <div class="content">
                        <div class="card-item" v-for="item in labelList" :key="item.value">
                            <div class="label" v-if="item.value == 'place'">
                                <span v-if="hasCustType(itemObj.CUST_TYPE, 1)">区县：</span>
                                <span v-else-if="hasCustType(itemObj.CUST_TYPE, 2)">经纬度：</span>
                                <span v-else-if="hasCustType(itemObj.CUST_TYPE, 3)">工作组：</span>
                            </div>
                            <div class="label" v-else>{{ item.label }}</div>
                            <div class="value" v-if="item.value == 'place'">
                                {{ setPlaceName(itemObj.CUST_JSON, itemObj.CUST_TYPE) }}
                            </div>
                            <div class="value" v-else>{{ itemObj[item.value] || "-" }}</div>
                        </div>
                        <div class="card-item">
                            <div class="label">通知渠道：</div>
                            <!-- :notificationChannelList="JY_NOTICE_CHANNEL" -->
                            <notificationChannelBox
                                :itemObj="itemObj">
                            </notificationChannelBox>
                        </div>
                        <div class="card-item">
                            <div class="label">审核结果：</div>
                            <div class="value" :style="{ color: setColor(itemObj.CHECK_RESULT) }">
                                {{ JY_CHECK_RESULT[itemObj.CHECK_RESULT] || "-" }}
                            </div>
                        </div>
                        <div class="card-item">
                            <div class="label">执行完成比例：</div>
                            <div class="value" style="color: #1b5fa4; font-weight: bold">
                                {{ itemObj.radio || "-" }}
                            </div>
                        </div>
                    </div>
                </template>
                <template #total="{ itemObj }">
                    <div class="flex">
                        <div style="color: #707070; margin-right: 16px">通知人数</div>
                        <countTo
                            :endVal="itemObj.notify"
                            style="color: #1b5fa4; font-weight: bold; font-size: 18px"
                            v-if="itemObj.notify != null"></countTo>
                        <div style="color: #1b5fa4; font-weight: bold; font-size: 18px" v-else>-</div>
                    </div>
                </template>
                <template #operate="{ itemObj }">
                    <el-button @click="toEdit(itemObj)" size="small" v-if="itemObj.TASK_STATUS == 1">{{
                        $t("编辑")
                    }}</el-button>
                    <el-button @click="toDel(itemObj)" size="small" v-if="itemObj.TASK_STATUS == 1">{{
                        $t("关闭")
                    }}</el-button>
                    <!-- <el-button @click="toDel(itemObj)" size="small">{{ $t("删除") }}</el-button> -->
                    <el-button @click="toDetail(itemObj)" size="small" type="primary">{{ $t("查看详情") }}</el-button>
                </template>
            </card-with-content-slot>
            <el-pagination
                class="flex-end mg-t16"
                background
                @current-change="handlePageChange"
                @size-change="handleSizeChange"
                :current-page.sync="pageNav.pageIndex"
                :page-size="pageNav.pageSize"
                :total="pageNav.totalRow">
            </el-pagination>
        </div>
        <div class="no-data" v-else>
            <img src="@/assets/images/common/no-data.svg" alt="">
            <div class="label">暂无数据</div>
        </div>
    </div>
</template>

<script>
import CardWithContentSlot from "@/components/common/CardWithContentSlot";
import { getQuestList, getDict, delTransQuest, getGroupList } from "@/api/base/eventManager.js";
import notificationChannelBox from "@/view/components/notificationChannelBox";
import disasterEventMixin from "@/mixin/disasterEvent";

export default {
    name: "index",
    mixins: [disasterEventMixin],
    components: { CardWithContentSlot, notificationChannelBox },
    data() {
        return {
            formList: {},
            list: [],
            labelList: [
                { label: "事务时间：", value: "CREATE_TIME" },
                { label: "区县：", value: "place" },
                { label: "通知时间：", value: "TIMING_SEND" },
                // { label: "通知渠道：", value: "isNot" },
                // { label: "审核结果：", value: "result" },
                // { label: "完成比例:", value: "rate" },
            ],
            // 1-智能外呼 2-短信通知  3-应急广播通知 4-微信通知
            notificationChannelList: {
                '1': '智能外呼',
                '2': '短信通知',
                '3': '应急广播通知',
                '4': '微信通知'
            },
            params: {
                value: "", //关键字
                transaction: "", //事务分类
                quest: "", //任务状态
                operSwitch: "N", //测试事件
                dataList: [], //时间范围
            },
            pageNav: {
                pageType: 3,
                pageIndex: 1,
                pageSize: 9,
                totalRow: 0,
            },
            loadding: false,
            groupList: [],
        };
    },
    methods: {
        handlePageChange(val) {
            this.pageNav.pageIndex = val;
            this.doSearch();
        },
        handleSizeChange(val) {
            this.pageNav.pageSize = val;
            this.doSearch();
        },
        // 旧数据的custJson是数组，而新数据的cusJson是对象
        hasCustType(type, val) {
            let cust_type = JSON.parse(type)
            if (typeof cust_type == 'number') 
                return cust_type == val
            else if (typeof cust_type == 'object' && cust_type.length) 
                return cust_type.indexOf(val + '') > -1
        },
        setPlaceName(jsonStr, typeStr) {
            let json = JSON.parse(jsonStr);
            let type = JSON.parse(typeStr);
            if (typeof type == 'number') {
                if (type == 1 && json.custJson.length) {
                    return json.custJson[0].locationName;
                } else if (type == 2 && json.custJson.length) {
                    return (
                        "东经：" + json.custJson[0].detail[0].lon + "，" + "北纬：" + json.custJson[0].detail[0].lat + "；"
                    );
                } else if (type == 3 && json.custJson.length) {
                    let re = [];
                    json.custJson.forEach((item) => {
                        re.push(
                            this.groupList.map((tem) => {
                                if (tem.ID == item) return tem.GROUP_NAME;
                            })
                        );
                    });
                    return re.toString().replaceAll(",", " ");
                }
            } else if (typeof type == 'object') {
                if (type.indexOf('1') > -1 && json.custJson.areas && json.custJson.areas.length) {
                    return json.custJson.areas[0].locationName;
                } else if (type.indexOf('2') > -1 && json.custJson.areas && json.custJson.areas.length) {
                    return (
                        "东经：" + json.custJson.areas[0].detail[0].lon + "，" + "北纬：" + json.custJson.areas[0].detail[0].lat + "；"
                    );
                } else if (type.indexOf('3') > -1 && json.custJson.groups && json.custJson.groups.length) {
                    let re = [];
                    json.custJson.groups.forEach((item) => {
                        re.push(
                            this.groupList.map((tem) => {
                                if (tem.ID == item) return tem.GROUP_NAME;
                            })
                        );
                    });
                    return re.toString().replaceAll(",", " ");
                }
            }
            
        },
        setColor(level) {
            switch (level) {
                case "01":
                    return "#1b5fa4";
                case "02":
                    return "#52C41A";
                case "03":
                    return "#F03838";
                case "04":
                    break;
            }
        },
        toDetail(item) {
            try {
                // 打开新页面
                // url 链接
                // title 标题
                // data 参数
                top.popup.openTab({
                    url: "/yc-emgnotify/pages/emerg/index.html#/eventManage/calledTask/detail",
                    id: "detail" + item.TASK_ID,
                    title: "事务叫应任务详情",
                    data: { taskId: item.TASK_ID, CHECK_RESULT: item.CHECK_RESULT, title: item.TASK_NAME, role: "2" },
                });
            } catch {
                this.$router.push({
                    path: "/eventManage/calledTask/detail",
                    query: { taskId: item.TASK_ID, CHECK_RESULT: item.CHECK_RESULT, title: item.TASK_NAME, role: "2" },
                });
            }
        },
        toEdit(item) {
            try {
                // 打开新页面
                // url 链接
                // title 标题
                // data 参数
                top.popup.openTab({
                    url: "/yc-emgnotify/pages/emerg/index.html#/eventManage/newCallTask",
                    id: "newCallTask",
                    title: "新建事务叫应任务",
                    data: { ID: item.TASK_ID },
                });
            } catch {
                this.$router.push({ path: "/eventManage/newCallTask", query: { ID: item.TASK_ID } });
            }
        },
        toDel(item) {
            this.$confirm("此操作将关闭该任务, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let data = {
                        TASK_ID: item.TASK_ID,
                        TASK_STATUS: '8'
                    };
                    delTransQuest(data)
                        .then((res) => {
                            if (res.data.state == 1) {
                                this.$message({
                                    type: "success",
                                    message: "关闭成功!",
                                });
                            } else {
                                this.$message({
                                    type: "success",
                                    message: "关闭失败！",
                                });
                            }
                        })
                        .finally((res) => {
                            this.doSearch();
                        });
                })
                .catch(() => {});
        },
        selectTransaction(value) {
            // 选择事务分类
            console.log(value);
            this.params.transaction = value;
            this.doSearch();
        },
        selectQuest(value) {
            // 选择任务状态
            this.params.quest = value;
            this.doSearch();
        },
        doSearch() {
            this.loadding = true;
            // 检索
            let data = {
                pageType: 3,
                pageIndex: this.pageNav.pageIndex,
                pageSize: this.pageNav.pageSize,
                S_CREATE_TIME: this.params.dataList ? this.params.dataList[0] : "",
                E_CREATE_TIME: this.params.dataList ? this.params.dataList[1] : "",
                IS_TEST: this.params.operSwitch,
                TASK_STATUS: this.params.quest,
                TASK_NAME: this.params.value,
                SECOND_TYPE: this.params.transaction,
            };
            this.list = [];
            getQuestList(data)
                .then((res) => {
                    if (res.data.state == 1) {
                        let dt = res.data.data;
                        this.pageNav.totalRow = res.data.totalRow;
                        dt.forEach((item) => {
                            item.disasterName = item.TASK_NAME;
                            item.classification =
                                "事务分类：" + (item.SECOND_TYPE ? this.JY_TRANS_TYPE[item.SECOND_TYPE] : "未知");
                            item.notify = Number(item.NOTICE_PERSON_NUM);
                            item.radio = Math.floor(item.NOTICE_COMPLETION_RATE * 100) + "%";
                        });
                        this.list = dt;
                    }
                })
                .finally((res) => {
                    this.loadding = false;
                    this.$forceUpdate();
                });
        },
    },
    mounted() {
        this.params.dataList = [
            this.$day().subtract(30, "day").startOf("date").format("YYYY-MM-DD HH:mm:ss"),
            this.$day(new Date()).endOf("date").format("YYYY-MM-DD HH:mm:ss"),
        ];
        this.doSearch();
        getGroupList().then((res) => {
            if (res.data.state == 1) {
                this.groupList = res.data.data;
            }
        });
    },
};
</script>

<style scoped lang="scss">
.list {
    .time {
        color: #707070;
        margin-right: 8px;
    }

    .content {
        border-radius: 4px;
        width: 100%;
        background: rgba(240, 244, 247, 0.5);
        padding: 16px;
        margin: 16px 0;
        font-size: 14px;
        color: #252525;

        .card-item {
            display: flex;

            &:not(:last-child) {
                margin-bottom: 8px;
            }

            .label {
                color: #707070;
                min-width: 106px;
                margin-right: 16px;
                white-space: nowrap;
            }

            .value {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .special {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 8px;

                .li {
                    border-radius: 4px;
                    padding: 1px 8px;
                    background: rgba(37, 37, 37, 0.05);
                }
            }
        }
    }
}

.audit {
    text-align: center;
    position: absolute;
    right: -34px;
    top: 12px;
    width: 120px;
    height: 32px;
    line-height: 32px;
    transform: rotate(45deg);
}
</style>
