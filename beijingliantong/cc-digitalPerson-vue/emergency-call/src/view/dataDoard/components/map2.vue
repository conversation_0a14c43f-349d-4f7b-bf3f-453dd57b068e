<template>
    <div class="mapbox">
        <div ref="map" class="map"></div>
        <div class="legend">
            <div class="row">
                <div class="round" @click="setValue(1000)"></div>
                1000
            </div>
            <div class="row">
                <div class="round" @click="setValue(5000)"></div>
                5000
            </div>
            <div class="row">
                <div class="round" @click="setValue(10000)"></div>
                10000
            </div>
            <div class="row">
                <div class="round" @click="setValue(50000)"></div>
                50000+
            </div>
            <div class="line"></div>
        </div>
    </div>
</template>

<script>
import { getMapJson } from '@/api/base/dataDoard'
import * as echarts from "echarts";
import provinceMap from './json.json'

export default {
    name: '',
    props: ['data'],
    watch: {
        data: {
            handler(newVal) {
                this.setMap(newVal)
            },
            deep: true
        }
    },
    data() {
        return {
            mapCode: '',
            myChart: null,
            option: {
                title: {
                    show: false
                },
                tooltip: {
                    trigger: 'item',
                    show: true,
                    formatter: function (params) {
                        if(typeof(params.value) == "undefined"){
                        return params.name + ' : ' + 0;
                        }else{
                        return params.name + ' : ' + params.value[2];
                        }
                    }
                },
                grid: {
                    left: '0',
                    right: '0',
                    bottom: '0',
                    top: '0',
                    containLabel: true
                },
                geo: [
                    {
                        map:'map',
                        roam:false,
                        zoom:1,
                        zlevel:2,
                        z:1,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: true,
                                color: '#1B5FA4'
                            },
                            emphasis: {
                                show: true,
                                color: '#FFFFFF'
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#fff',
                                borderWidth:1,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                }
            
                            },
                            emphasis: {
                                borderWidth:1,
                                areaColor: '#1B5FA4',
                                borderColor:'#FFFFFF'
                            }
                        },
                    },
                    {
                        map:'map',
                        roam:false,
                        zoom:1,
                        zlevel:0,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#639fdb',
                                borderWidth:1.5,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                }
            
                            },
                            emphasis: {
                                borderWidth:1.5,
                                areaColor: '#003b65',
                                borderColor:'#639fdb'
                            }
                        }
                    },
                    {
                        map:'map',
                        roam:false,
                        zlevel:1,
                        zoom:1,
                        z:1,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#FFFFFF',
                                borderWidth:1,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                },
                                shadowColor: '#1E62A7',
                                shadowBlur: 0,
                                shadowOffsetX:0,
                                shadowOffsetY:10
            
                            },
                            emphasis: {
                                borderWidth:3,
                                areaColor: '#1B5FA4',
                                borderColor:'#FFFFFF'
                            }
                        }
                    },
                    {
                        map:'map',
                        roam:false,
                        zlevel:1,
                        zoom:1,
                        z:1,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#FFFFFF',
                                borderWidth:1,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                },
                                shadowColor: '#1E62A7',
                                shadowBlur: 0,
                                shadowOffsetX:-1,
                                shadowOffsetY:10
            
                            },
                            emphasis: {
                                borderWidth:3,
                                areaColor: '#1B5FA4',
                                borderColor:'#FFFFFF'
                            }
                        }
                    },
                    {
                        map:'map',
                        roam:false,
                        zoom:1,
                        zlevel:1,
                        z:1,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#FFFFFF',
                                borderWidth:1,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                },
                                shadowColor: '#1E62A7',
                                shadowBlur: 0,
                                shadowOffsetX:-1,
                                shadowOffsetY:14
            
                            },
                            emphasis: {
                                borderWidth:3,
                                areaColor: '#1B5FA4',
                                borderColor:'#FFFFFF'
                            }
                        }
                    },
                    {
                        map:'map',
                        roam:false,
                        zoom:1,
                        zlevel:1,
                        z:1,
                        aspectScale:1,
                        layoutCenter: ["50%", "40%"],
                        layoutSize: '100%',
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                borderColor:'#FFFFFF',
                                borderWidth:1,
                                areaColor: {
                                    type: 'linear',
                                    x: 0,
                                    y: 1500,
                                    x2: 1000,
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0.5,
                                        color: '#9EC4EC' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: '#B6D5F4' // 100% 处的颜色
                                    }],
                                    global: true // 缺省为 false
                                },
                                shadowColor: '#1E62A7',
                                shadowBlur: 0,
                                shadowOffsetX:-1,
                                shadowOffsetY:21
            
                            },
                            emphasis: {
                                borderWidth:3,
                                areaColor: '#1B5FA4',
                                borderColor:'#FFFFFF'
                            }
                        }
                    },
            
                ],
                series: [
                    {
                        type: 'scatter',
                        name: '叫应次数',
                        coordinateSystem: 'geo',
                        nameProperty: 'name',
                        data: [
                            // {
                            //     name: '110114',
                            //     value: [116.235906,40.218085, 100]
                            // },
                            // {
                            //     name: '110105',
                            //     value: [116.486409,39.921489, 300000]
                            // }
                        ],
                        symbol: 'circle',
                        symbolSize: (value,params) => {
                            return value[2] >= 50000 ? 80 : (value[2] >= 10000 ? 40 : (value[2] >= 5000 ? 25 : 13))
                        },
                        itemStyle:{
                            color:'#6698CB',
                            borderColor:'#1B5FA4',
                            borderWidth: 1
                        },        
                        label:{
                            show: false
                        },
                        emphasis: {
                            label:{
                                show: false
                            },
                        },
                        zlevel: 3
                    }
                ]
            },
            mapJson: {},
            mapData: [],
            value: ''
        }
    },
    methods: {
        setMap(data) {
            let list = []
            data.forEach(item => {
                let center = []
                let name = ''
                this.mapJson.forEach(c => {
                    if (c.properties.adcode == item.CITY) {
                        center = c.properties.center
                        name = c.properties.name
                    }
                })
                let obj = {
                    name: name,
                    value: [...center, item.S_COUNT || 0]
                }
                list.push(obj)
            })
            // list.push({
            //     value: [0,0,0]
            // })
            this.mapData = JSON.parse(JSON.stringify(list))
            this.option.series[0].data = list
            // this.option.series[1].data = list
            this.myChart.setOption(this.option)
        },
        setValue(data) {
            this.value = this.value == data ? '' : data
            let list = []
            if (!this.value) {
                list = JSON.parse(JSON.stringify(this.data))
            } else if (this.value == 1000) {
                list = this.data.filter(item => item.S_COUNT <= 1000)
            } else if (this.value == 5000) {
                list = this.data.filter(item => item.S_COUNT <= 5000 && item.S_COUNT > 1000)
            } else if (this.value == 10000) {
                list = this.data.filter(item => item.S_COUNT <= 10000 && item.S_COUNT > 5000)
            } else {
                list = this.data.filter(item => item.S_COUNT >= 50000)
            }
            this.setMap(list)
        },
        getMapJson() {
            return getMapJson(this.mapCode).then(res => {
                return res.data
            })
        },
        getNanHai() {
            return getMapJson('nanhai').then(res => {
                return res.data
            })
        }
    },
    async mounted() {
        let info = localStorage.getItem('emerg_user_info') || "{}"
        this.mapCode = (JSON.parse(info).defProvinceCode || '11') + '0000'
        this.$emit('getName', provinceMap[this.mapCode])
        let json = await this.getMapJson()
        let nanhai = await this.getNanHai()
        this.mapJson = json.features
    //     let canvas = document.createElement("canvas");
    //     let ctx = canvas.getContext('2d');
    //     canvas.width = 100;
    //     canvas.height = 100;
    //     //绘制颜色偏向
    //    var Grd = ctx.createLinearGradient(100, 0, 0, 100);
    //     Grd.addColorStop(0, "#2467AB");
    //     Grd.addColorStop(1, "#9BC3EB");
    //     ctx.fillStyle = Grd;
    //     ctx.fillRect(0, 0, 100, 100);
        this.$echarts.registerMap('map', json)
        this.$echarts.registerMap('map2', json)
        // this.$echarts.registerMap('nanhai', nanhai)
        this.myChart = this.$echarts.init(this.$refs.map);
        // this.option.series[1].realisticMaterial.detailTexture = ctx.canvas.toDataURL()
        this.myChart.setOption(this.option)
        window.addEventListener('resize', () => {
            this.myChart.resize()
        })
    },
}
</script>

<style lang="scss" scoped>
.mapbox {
    position: relative;
    .map {
        width: 100%;
        height: 100%;
    }
    .legend {
        position: absolute;
        left: -50px;
        bottom: 0;
        display: flex;
        flex-direction: column;
        .row {
            display: flex;
            align-items: center;
            color: #707070;
            font-size: 14px;
            margin-bottom: 10px;
            &:nth-child(1) {
                .round {
                    width: 13px;
                    height: 13px;
                    margin: 0 44px 0 34px;
                }
            }
            &:nth-child(2) {
                .round {
                    width: 25px;
                    height: 25px;
                    margin: 0 38px 0 28px;
                }
            }
            &:nth-child(3) {
                .round {
                    width: 41px;
                    height: 41px;
                    margin: 0 30px 0 20px;
                }
            }
            &:nth-child(4) {
                margin-bottom: 0;
                .round {
                    width: 81px;
                    height: 81px;
                    margin: 0 10px 0 0;
                }
            }
        }
        .round {
            background: rgba(27, 95, 164, 0.5);
            box-sizing: border-box;
            border: 2px solid #1B5FA4;
            border-radius: 50%;
            cursor: pointer;
        }
        .line {
            position: absolute;
            left: 40.5px;
            top: 6.5px;
            width: 0;
            height: 143px;
            border-left: 1px dashed #1B5FA4;
        }
    }
}
</style>