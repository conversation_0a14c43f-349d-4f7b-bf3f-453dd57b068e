<template>
    <div class="box">
        <div class="echart" ref="echart" :style="{zoom: zooms}"></div>
        <div class="title">{{$t('短信发送成功率')}}</div>
    </div>
</template>

<script>
export default {
    name: '',
    props: ['data', 'zooms'],
    watch: {
        data: {
            handler(newVal) {
                this.option.series[0].data[0].value = Number(newVal.S_MSG_SUCC)
                this.option.series[0].data[1].value = Number(newVal.S_MSG) - Number(newVal.S_MSG_SUCC)
                this.option.title.text = Number(newVal.S_MSG) == 0 ? '0.00%' : (Number(newVal.S_MSG_SUCC)/Number(newVal.S_MSG)).toFixed(2) * 100 + '%'
                this.myChart.setOption(this.option)
                this.myChart.resize()
            },
            deep: true
        },
        zooms: {
            handler() {
                if (!this.myChart) return;
                this.$nextTick(() => {
                    this.myChart.resize()
                })
            },
            deep: true
        }
    },
    data() {
        return {
            myChart: null,
            option: {
                color: ['#1B5FA4', '#F2F5F5'],
                title: {
                    top: '32%',
                    left: 'center',
                    text: '0.00%',
                    textStyle: {
                        color: '#252525',
                        fontSize: 20
                    }
                },
                tooltip: {
                    show: true
                },
                legend: {
                    show: false
                },
                series: [
                    {
                    name: '短信发送成功率',
                    type: 'pie',
                    radius: ['60%', '80%'],
                    center: ['50%', '40%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        disabled: true,
                        scale: false,
                        label: {
                            show: false
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 0, name: '成功' },
                        { value: 0, name: '失败' }
                    ]
                    }
                ]
            }
        }
    },
    mounted() {
        this.myChart = this.$echarts.init(this.$refs.echart);
        this.myChart.setOption(this.option)
        window.addEventListener('resize', () => {
            this.myChart.resize()
        })
    },
}
</script>

<style lang="scss" scoped>
.box {
    position: relative;
    width: 100%;
    height: 100%;
    .title {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        color: #707070;
        font-size: 14px;
        line-height: 22px;
        white-space: nowrap;
    }
}
.echart {
    width: 100%;
    height: 100%;
}
</style>