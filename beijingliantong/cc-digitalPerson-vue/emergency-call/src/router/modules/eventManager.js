export default function (router) {
    router.push({
        path: '/eventManage/newCallTask',
        name: '新建事务叫应任务',
        component: () => import('@/view/eventManage/newCallTask/index')
    });
  router.push({
    path: '/eventManage/calledTask',
    name: '事务叫应任务',
    component: () => import('@/view/eventManage/calledTask/index')
  });
  router.push({
    path: '/eventManage/calledTask/detail',
    name: '事务叫应任务详情',
    component: () => import('@/view/eventManage/calledTask/detail')
  });
  router.push({
    path: '/eventManage/templateManage',
    name: '事务通知模板管理',
    component: () => import('@/view/eventManage/templateManage/index')
  });
  router.push({
    path: '/eventManage/templateManage/detail',
    name: '事务通知模板管理详情',
    component: () => import('@/view/eventManage/templateManage/detail')
  });
  router.push({
    path: '/eventManage/templateManage/newAdd',
    name: '新建事务通知模板管理',
    component: () => import('@/view/eventManage/templateManage/newAdd')
  });
}
