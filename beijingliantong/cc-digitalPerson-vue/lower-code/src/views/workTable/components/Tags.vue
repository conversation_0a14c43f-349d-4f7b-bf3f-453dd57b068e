<template>
  <div class="work-tags flex-space-between">
    <!--      <div class="left">编辑页面</div>-->
    <el-tabs
      type="card"
      class="tab-view flex"
      v-model="active"
      @tab-click="handleToClickTab"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.viewId"
        :label="tag.viewName"
        :name="tag.viewId"
      >
        <div slot="label">
          <span>{{ tag.viewName }}</span>
          <i class="el-icon-close"></i>
        </div>
      </el-tab-pane>
      <div class="add" @click="addTag">
        <i class="iconfont icon-tianjia"></i>
      </div>
    </el-tabs>
    <div class="right flex">
      <div class="flex">
        <div class="left-item item">
          <i class="iconfont icon-chexiaoshangyibu"></i>
        </div>
        <div class="right-item item">
          <i class="iconfont icon-chexiaoyibu"></i>
        </div>
      </div>
      <div class="toggle flex mg-l16">
        <div class="left-item item active">
          <i class="iconfont icon-pcduan"></i>
        </div>
        <div class="right-item item">
          <i class="iconfont icon-yidongduan"></i>
        </div>
      </div>
      <div class="line"></div>
      <el-button size="small" plain type="danger" icon="iconfont icon-tuichu">{{
        $t("预览")
      }}</el-button>
      <el-button size="small" plain type="primary" icon="iconfont icon-yulan">{{
        $t("预览")
      }}</el-button>
      <el-button
        size="small"
        plain
        type="primary"
        icon="iconfont icon-baocun"
        @click="handleToSave"
        >{{ $t("保存") }}</el-button
      >
      <el-button
        size="small"
        type="primary"
        icon="iconfont icon-shengchengjson"
        >{{ $t("生成模板") }}</el-button
      >
    </div>
  </div>
</template>

<script>
import { EventBus } from "@/utils/eventBus";
export default {
  name: "TagsView",
  props: {
    visitedViews: {
      type: Array,
      default: () => [],
    },
    activeName: {
      type: String,
      default: "",
    },
  },
  watch: {
    activeName: {
      handler(newVal) {
        this.active = newVal;
      },
    },
  },
  data() {
    return {
      active: "",
      // visitedViews: [
      //   {viewName: "视图1", viewId: "1"},
      //   {viewName: "视图2", viewId: "2"},
      //   {viewName: "视图3", viewId: "3"},
      // ]
    };
  },
  methods: {
    addTag() {
      this.$emit("handleToAddTag");
    },
    handleToClickTab(val) {
      this.$emit("handleToClickTab", this.active);
    },
    handleToSave() {
      EventBus.$emit("handleToSaveTableConfig", "table");
    },
  },
};
</script>

<style scoped lang="scss">
@import "~@/styles/variables.scss";

.work-tags {
  height: 32px;
  width: 100%;
  margin-bottom: 16px;

  .left {
    font-size: 16px;
    font-weight: bold;
    color: #262626;
  }

  .right {
    .toggle .item {
      background: #ffffff;

      &.active {
        background: #e5edfa;
        color: #0555ce;
      }
    }

    .item {
      width: 50px;
      height: 32px;
      color: #c5c5c5;
      background: #e5edfa;
      text-align: center;
      line-height: 32px;

      &.left-item {
        border-radius: 4px 0px 0px 4px;
        margin-right: 1px;
      }

      &.right-item {
        border-radius: 0px 4px 4px 0px;
      }

      &:hover {
        cursor: pointer;
        color: #0555ce;
      }
    }

    .line {
      width: 0.5px;
      height: 24px;
      background: #c5c5c5;
      margin: 0 24px;
    }
  }

  ::v-deep .el-tabs {
    .add {
      width: 32px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      border-radius: 0px 4px 4px 0px;
      background: #e5edfa;
      border-color: rgba(255, 255, 255, 0.5);

      i {
        font-size: 14px;
        color: #ffffff;
        border-radius: 50%;
        background: #0555ce;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tabs__header {
      margin-bottom: 0;
      border-bottom: 0;
    }

    .el-tabs__nav {
      display: flex;
      flex-wrap: nowrap;
      border: 0;
      gap: 5px;
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 30px;
      color: #333333;
    }

    .el-tabs__item {
      line-height: normal;
      height: auto;
      padding: 5px 16px !important;
      color: #333333;
      border: 0;
      background: #ffffff;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }

      &.is-active {
        background: $blue;
        color: #ffffff !important;
      }

      &:hover {
        background: $blue;
        color: #ffffff !important;
        transition: none;
      }

      .el-icon-close:hover {
        background: rgba(255, 255, 255, 0.5);
        color: $blue;
      }
    }

    .el-icon-close {
      color: #ffffff;
      background: #c5c5c5;
      width: 14px !important;
      line-height: 14px !important;
    }
  }
}
</style>
