<template>
  <base-drawer
    v-bind="$attrs"
    v-on="$listeners"
    :confirm-loading="isLoading"
    :title="selRow&&selRow.id?'编辑'+selRow.name:'新增插件'"
    @drawerSave="drawerSave"
    @drawerCancel="drawerCancel"
    @drawerOnClose="drawerCancel"
  >
    <template #content>
      <el-form label-width="76px" ref="ruleForm" :model="formList" :rules="rules">
        <el-form-item :label="$t('名称')" prop="name" :rules="rules.isRequired">
          <el-input maxlength="20" show-word-limit placeholder="请输入" v-model="formList.name"></el-input>
        </el-form-item>
        <el-form-item :label="$t('类型')" prop="pluginType" :rules="rules.isRequired">
          <el-select @change="changePluginType" style="width: 100%" filterable v-model="formList.pluginType"
                     placeholder="请选择"
                     clearable>
            <el-option v-for="(label, value) in CODEGLUE_PLUGIN_TYPE"
                       :key="value"
                       :label="label"
                       :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <template v-if="formList.pluginType=='p_stDate'">
          <!--          <el-form-item :label="$t('数据源')">-->
          <!--          <el-cascader :props="props" :options="dataSourceTree"></el-cascader>-->
          <!--          </el-form-item>-->
          <el-form-item :label="$t('数据源')">
            <el-select filterable @change="changeSource" style="width: 100%" v-model="formList.pluginData.dsCode"
                       clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in dataSourceTree"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('数据库')">
            <el-select filterable @change="changeArchive" style="width: 100%" v-model="formList.pluginData.dbName"
                       clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in dataSheet"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('数据表')">
            <el-select filterable @change="changeSheet" style="width: 100%" v-model="formList.pluginData.tableName"
                       clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in schemaList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('日期字段')">
            <el-select filterable style="width: 100%" v-model="formList.pluginData.fieldDay" clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in fieldList"
                :key="item.fieldName"
                :label="item.fieldText+'('+item.fieldName+')'"
                :value="item.fieldName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('星期字段')">
            <el-select filterable style="width: 100%" v-model="formList.pluginData.fieldWeek" clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in fieldList"
                :key="item.fieldName"
                :label="item.fieldText+'('+item.fieldName+')'"
                :value="item.fieldName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('月份字段')">
            <el-select filterable style="width: 100%" v-model="formList.pluginData.fieldMonth" clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in fieldList"
                :key="item.fieldName"
                :label="item.fieldText+'('+item.fieldName+')'"
                :value="item.fieldName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('季度字段')">
            <el-select filterable style="width: 100%" v-model="formList.pluginData.fieldQuarter" clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in fieldList"
                :key="item.fieldName"
                :label="item.fieldText+ '('+item.fieldName+')'"
                :value="item.fieldName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('年度字段')">
            <el-select filterable style="width: 100%" v-model="formList.pluginData.fieldYear" clearable
                       :placeholder="$t('请选择')">
              <el-option
                v-for="item in fieldList"
                :key="item.fieldName"
                :label="item.fieldText+'('+item.fieldName+')'"
                :value="item.fieldName">
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item :label="$t('版本号')" prop="versionNo">
          <el-input placeholder="请输入" v-model="formList.versionNo"></el-input>
        </el-form-item>
        <el-form-item :label="$t('序号')" prop="sortOrder">
          <el-input-number style="width: 100%" v-model="formList.sortOrder" :controls="false"></el-input-number>
          <!--          <el-input placeholder="请输入"  v-model="formList.sortOrder"></el-input>-->
        </el-form-item>
        <el-form-item :label="$t('备注')" prop="bakup">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="formList.bakup">
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('启用状态')" prop="enableStatus" :rules="rules.isRequired">
          <el-select style="width: 100%" v-model="formList.enableStatus" placeholder="">
            <el-option v-for="(label, value) in ENABLE_STATUS"
                       :key="value"
                       :label="label"
                       :value="value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

    </template>
  </base-drawer>
</template>

<script>
import BaseDrawer from "@/components/base/BaseDrawer";
import {pluginInfo, addOrUpdate} from "@/api/plugin";
import {getDict} from "@/api/base";
import {getDataSourceTree, getFields} from "@/api/data";


export default {
  name: "CooperativeScheduling",
  components: {BaseDrawer},
  props: {
    selRow: {
      type: Object,
      default: () => {
        return {};
      },
    },
    title: {
      type: String,
      default: "",
    },
    classType: {
      type: String,
      default: "",
    },

  },
  data() {
    return {
      dataSourceId: "",
      schema: "",
      fieldList: [],
      dataSourceTree: [],
      dataSheet: [],
      dataArchive: [],
      schemaList: [],
      options: [],
      isLoading: false,
      isEdit: false,
      formList: {
        id: "",
        enableStatus: "01",
        sortOrder: 1,
        versionNo: "01",
        pluginData: {
          dsCode: "",
          dbName: "",
          tableName: "",
          fieldDay: "",
          fieldWeek: "",
          fieldMonth: "",
          fieldQuarter: "",
          fieldYear: "",
        },
      },
      ENABLE_STATUS: {},
      CODEGLUE_PLUGIN_TYPE: {},
      //校验规则
      rules: {
        isRequired: [
          {required: true, message: '不能为空', trigger: 'blur'},
          {required: true, message: '不能为空', trigger: 'change'}
        ],
      },
    }
  },

  watch: {
    selRow: {
      deep: true,
      async handler(newVal) {
        if (newVal && newVal.id) {
          await this.pluginInfo(newVal.id)
          await this.changePluginType(this.formList.pluginType)
          await this.changeSource(this.formList.pluginData.dsCode, false)
          await this.changeArchive(this.formList.pluginData.dbName, false)
          this.changeSheet(this.formList.pluginData.tableName, false)
        }
      }
    },

  },

  mounted() {
    this.getDict()
  },
  methods: {
    getDict() {
      let data = {
        "params": {},
        controls: ["common.getDict('ENABLE_STATUS')", "common.getDict('CODEGLUE_PLUGIN_TYPE')"]
      }
      getDict(data).then(res => {
        this.ENABLE_STATUS = res.data["common.getDict('ENABLE_STATUS')"].data
        this.CODEGLUE_PLUGIN_TYPE = res.data["common.getDict('CODEGLUE_PLUGIN_TYPE')"].data

      })
    },
    //获取数据源
    async changePluginType(val) {
      if (val == 'p_stDate') {
        let data = {
          dataSourceId: '',
          type: 'datasource',
          schema: ''
        }
        let callBack = (res) => {
          this.dataSourceTree = res.data.data || []
        }
        await this.getDataSourceTree(data, callBack)
      }
    },
    //获取数据库
    async changeSource(val, flag = true) {
      if (flag) {
        this.formList.pluginData.tableName = ""
        this.formList.pluginData.dbName = ""
        this.formList.pluginData = {
          ...this.formList.pluginData,
          fieldDay: "",
          fieldWeek: "",
          fieldMonth: "",
          fieldQuarter: "",
          fieldYear: "",
        };
      }

      this.dataSourceId = val;
      let data = {
        dataSourceId: val,
        type: 'schema',
        schema: ''
      }
      let callBack = (res) => {
        this.dataSheet = res.data.data || []
      }
      await this.getDataSourceTree(data, callBack)
    },
    //获取数据表
    async changeArchive(val, flag = true) {
      if (flag) {
        this.formList.pluginData.tableName = ""
        this.formList.pluginData = {
          ...this.formList.pluginData,
          fieldDay: "",
          fieldWeek: "",
          fieldMonth: "",
          fieldQuarter: "",
          fieldYear: "",
        };
      }

      this.schema = val;
      let data = {
        dataSourceId: this.dataSourceId || "",
        type: 'table',
        schema: val
      }
      let callBack = (res) => {
        this.schemaList = res.data.data || []
      }
      await this.getDataSourceTree(data, callBack)
    },
    changeSheet(val, flag = true) {
      if (flag) {
        this.formList.pluginData = {
          ...this.formList.pluginData,
          fieldDay: "",
          fieldWeek: "",
          fieldMonth: "",
          fieldQuarter: "",
          fieldYear: "",
        };
      }
      let query = {
        data: {
          dataSourceId: this.dataSourceId || "",
          table: val,
          schema: this.schema || ""
        }

      }
      getFields(query).then(res => {
        if (res.succeed) {
          this.fieldList = res.data.data || []
        }
      })
    },
    async getDataSourceTree(data, callBack) {
      let query = {
        data: {
          ...data
        }
      }
      await getDataSourceTree(query).then(res => {
        callBack && callBack(res)
      })
    },
    //编辑
    async pluginInfo(id) {
      let data = {
        data: {
          pluginId: id || ""
        }
      };
      await pluginInfo(data)
        .then((res) => {
          if (res.succeed) {
            this.formList = {...this.formList, ...res.data.data, pluginData: JSON.parse(res.data.data.pluginData)}
          } else {
            this.$message.error(res.data.msg);
          }
        })

    },

    //新增
    addOrUpdate() {
      this.isLoading = true;
      let data = {
        data: {
          ...this.formList,
          pluginData: {
            ...this.formList.pluginData,

          }
        }
      };
      addOrUpdate(data)
        .then((res) => {
          if (res.succeed) {
            this.$message.success(this.$t(this.formList.id ? '编辑成功' : "添加成功"));
            this.$emit("successCallback")
            this.resetForm()
          } else {
            this.$message.error(res.data.msg);
          }
        }).finally(() => {
        this.isLoading = false;
      })

    },
    drawerSave() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.addOrUpdate()
        }
      });
    },

    drawerCancel() {
      this.resetForm()
    },
    //重置
    resetForm(formName = "ruleForm") {
      this.formList.id = ""
      this.formList.pluginData = {
        dsCode: "",
        tableName: "",
        fieldDay: "",
        fieldWeek: "",
        fieldMonth: "",
        fieldQuarter: "",
        fieldYear: "",
      }
      this.$refs[formName]?.resetFields();
      this.$emit("update:drawerVisible", false);
      this.$emit("update:selRow", null);
      this.isEdit = false;
    },
  }
};
</script>

<style scoped lang="scss"></style>
