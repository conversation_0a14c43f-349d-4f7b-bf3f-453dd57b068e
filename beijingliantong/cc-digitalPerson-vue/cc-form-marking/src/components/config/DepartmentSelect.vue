<template>
  <el-cascader
    v-model="value"
    :options="options"
	   clearable
    :props="{ checkStrictly: true }"
    @change="handleChange"
  ></el-cascader>
</template>

<script>
import DataServer from "../../service/DataServer.js";

export default {
  components: {},
  props: ["dataModel"],
  data() {
    return {
      maxOptionLength: 100, //最多一个级联能选几级
      lazyLoad: false, // 是否开启懒加载
      haveFather: false,
      Enforcement: false,
      value: [],
      options: [],
      reload: true,
      reloadParams: "",
      reloadParamsUsed: false,
      fatherData: "",
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    handleChange(value) {
      this.value = value;
      // console.log(this.value)
      if(this.value.length){
		this.$emit("update:dataModel", this.value[this.value.length - 1]);
      }
    },
    renderSelect(pId) {},
    clearSelect(pId) {},
    loadData(pCode) {
      const commonTreePromise = DataServer.getDepartmentCascade({
        lazyLoad: this.lazyLoad,
        pCode: pCode || "",
        level: "",
      });
      commonTreePromise.then(
        (resp) => {
          if (resp.data.state == 1 && resp.data.data.length > 0) {
            if (!pCode) {
              this.options = DataServer.commonDataHandle.handleData(
                resp.data.data,
                "",
                "",
                resp.data.data[0].DEPT_CODE.length,
                this.maxOptionLength
              );
            } else {
              let arr = resp.data.data;
              this.options = DataServer.commonDataHandle.handleData(
                resp.data.data,
                "",
                "",
                arr[0].DEPT_CODE.length,
                this.maxOptionLength
              );
            }
          }
        },
        (error) => {
          console.log("请求异常", error);
        }
      );
    },
  },
  watch: {
    value: function (newVal, oldVal) {
      if (!newVal || newVal.length==0) {
		this.$emit("update:dataModel", '');
	  }
    },
    dataModel: {
      handler(newVal, oldVal) {
        // console.log("this.mymodel", newVal, oldVal);
        this.value = newVal;
      },
      immediate: true,  
      deep: true  
    },
  },
};
</script>

<style scoped>
</style>
