<template>
    <el-dialog :visible.sync="visible" :title="title" :width="isMobile() ? '90%' : '50%'" @close="handleClose">
		<div id="video"></div>
	</el-dialog>
</template>

<script>
import { isMobile } from '@/util'
import Player from 'xgplayer';

export default {
    name: '',
    data() {
        return {
            visible: false,
            player: null,
            title: ''
        }
    },
    methods: {
        isMobile,
        open(data) {
            let { url, name, type, id } = data
            this.url = url
            this.title = name
            this.visible = true
            this.$nextTick(() => {
                this.player = new Player({
                    id: 'video',
                    autoplay: true,
                    url: '/cc-base/servlet/attachment?action=download&fileId=' + id
                })
            })
        },
        handleClose() {
            this.player.destroy()
        }
    }
}
</script>

<style lang="scss" scoped>
#video {
    width: 100% !important;
    height: 360px !important;
}
::v-deep .el-dialog__title {
    max-width: 80%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: inline-block;
}
::v-deep .el-dialog__body {
    padding-top: 0 !important;
}
</style>