@import "./colorConfig.scss";

$primary-color: #409eff;
$primary-background-color: #ecf5ff;

*,
:after,
:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.fa-icon {
	width: auto;
	height: 1em; /* 或任意其它字体大小相对值 */

	/* 要在 Safari 中正常工作，需要再引入如下两行代码 */
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}

.iconfont {
	font-size: 16px !important;
	color: $iconColor;
	&.danger {
		color: $dangerColor;
	}
	&.theme {
		color: $themeColor;
	}
	&.white {
		color: #fff;
	}
}

.fm2-container {
	height: 100%;
	background-color: $bgColor;
	.header {
		height: auto !important;
		margin-bottom: 16px;
		padding: 0;
	}
	.fm2-main {
		position: relative;

		& > .el-container {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
		}

		.components-list-container {
			transition: 0.1s;
			position: relative;

			.components-list {
				width: 100%;
				height: 100%;
				overflow: auto;
				background-color: #fff;

				.widget-cate {
					color: $textColor;
					padding: 8px 12px;
					font-size: 16px;
					font-weight: bold;
				}

				ul {
					position: relative;
					overflow: hidden;
					padding: 0 10px 10px;
					margin: 0;
				}

				.form-edit-widget-label {
					font-size: 14px;
					display: block;
					width: 46%;
					height: 40px;
					line-height: 40px;
					position: relative;
					float: left;
					left: 0;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					margin: 2%;
					color: $textColor;
					// border: 1px solid $bgColor;
					border-radius: 4px;
					&:hover {
						color: $themeColor;
						// border: 1px dashed $themeColor;
					}

					& > a {
						display: block;
						cursor: move;
						background: $bgColor;
						// border: 1px solid $bgColor;

						.icon {
							margin-right: 6px;
							margin-left: 12px;
							font-size: 14px;
							display: inline-block;
							vertical-align: middle;
						}

						span {
							display: inline-block;
							vertical-align: middle;
						}
					}
				}
			}
		}

		.center-container {
			display: flex;
			align-items: center;
			margin: 0 16px;
			.el-main {
				padding: 0;
				position: relative;
			}
		}

		.widget-config-container {
			transition: 0.1s;
			position: relative;
			background-color: #fff;
			box-sizing: border-box;

			.configContent {
				width: 100%;
				height: 100%;
			}

			.el-header {
				border-bottom: 1px solid #e4e7ed;
				padding: 0 5px;
			}

			.config-tab {
				height: 52px;
				line-height: 48px;
				display: inline-block;
				width: 50%;
				text-align: center;
				font-size: 14px;
				font-weight: 500;
				position: relative;
				cursor: pointer;

				&.active {
					span {
						display: inline-block;
						border-bottom: 4px solid $themeColor;
					}
				}
			}

			.config-content-main {
				padding: 16px 24px;
				
				.el-button--info {
					padding: 12px 20px
				}

				.el-form-item__label {
					padding: 0;
					font-weight: 500;
				}

				// .el-form-item {
				//   border-bottom: solid 1px #e1e1e1;
				//   padding-bottom: 10px;
				// }
				.form-config-container {
					li {
						list-style: none;
						margin-bottom: 10px;
					}
					.el-input-number {
						width: 100%;
					}
				}

				.widgetConfig {
					.fieldForm {
						.el-form-item {
							margin-bottom: 16px !important;
						}
						.widget-cate {
							font-size: 14px;
							font-weight: bold;
						}
					}
				}
			}

			.ghost {
				background: #fff;
				border: 1px dashed $primary-color;

				&::after {
					background: #fff;
					display: block;
					content: "";
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
				}
			}

			ul {
				margin: 0;
				padding: 0;
			}

			li.ghost {
				list-style: none;
				font-size: 0;
				display: block;
				position: relative;
			}
		}

		.foldIcon {
			position: absolute;
			width: 12px;
			height: 50px;
			display: flex;
			justify-content: center;
			align-items: center;
			top: 50%;
			background-color: #fff;
			transform: translateY(-50%);
			&:hover {
				cursor: pointer;
			}
			&.left {
				right: -14px;
				border-radius: 0 15px 15px 0;
			}
			&.right {
				left: -14px;
				border-radius: 15px 0 0 15px;
			}
		}
	}
	main {
		padding: 0;
	}

	footer {
		height: 30px;
		line-height: 30px;
		border-top: 1px solid #e0e0e0;
		font-size: 12px;
		text-align: right;
		color: $primary-color;
		background: #fafafa;

		a {
			color: $primary-color;
		}
	}
}

.form-name {
	font-size: 16px;
	line-height: 24px;
	font-weight: bold;
	color: $textColor;
	margin-bottom: 16px;
}

.widget-form-container {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 16px 24px;
	background-image: url("~@/assets/containerBg.png");
	overflow: auto;

	.form-empty {
		// position: absolute;
		// text-align: center;
		// width: 300px;
		// font-size: 20px;
		// top: 200px;
		// left: 50%;
		// margin-left: -150px;
		// color: #ccc;

		position: absolute;
		top: 200px;
		display: flex;
		width: calc(100% - 32px);
		flex-direction: column;
		align-items: center;
		color: #868686;
	}

	.el-form {
		height: calc(100% - 40px);
		// margin-top: 16px;
		.draggable {
			height: 100%;
		}
	}

	.widget-form-list {
		// background: #fff;
		border: 1px dashed #c5c5c5;
		min-height: 100%;
		border-radius: 4px;

		.widget-col-list {
			min-height: 50px;
			border: 1px dashed #c5c5c5;
		}

		.widget-col-list {
			.widget-view {
				border-bottom: none;
			}
		}

		.widget-view {
			padding: 16px;
			position: relative;
			// border: 1px dashed #c5c5c5;
			border-bottom: 1px dashed #c5c5c5;
			// background-color: rgba(236, 245, 255, 0.3);
			// margin: 2px;
			margin: 0;

			.el-form-item__content {
				position: unset;
			}

			&.is_req {
				.el-form-item__label::before {
					content: "*";
					color: #f56c6c;
					margin-right: 4px;
				}
			}

			.widget-view-description {
				height: 15px;
				line-height: 15px;
				font-size: 13px;
				margin-top: 6px;
				color: #909399;
			}

			.widget-view-action {
				position: absolute;
				right: 0;
				bottom: 0;
				height: 20px;
				line-height: 20px;
				display: flex;
				z-index: 9;

				i {
					display: inline-block;
					width: 20px;
					text-align: center;
					font-size: 14px;
					border-radius: 2px;
					color: #fff;
					cursor: pointer;
					background: $themeColor;
				}
			}

			.widget-view-drag {
				position: absolute;
				left: 0;
				top: 0;
				// bottom: -18px;
				height: 24px;
				line-height: 24px;
				background: $themeColor;
				z-index: 9;
				// display: none;

				i {
					font-size: 14px;
					color: #fff;
					margin: 0 5px;
					cursor: move;
				}
			}

			&:after {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				display: block;
			}

			&:hover {
				// background: $primary-background-color;
				outline: 1px solid $themeColor;
				outline-offset: 0px;

				// &.active {
				// 	outline: 1px solid $themeColor;
				// 	border: 1px solid $themeColor;
				// 	outline-offset: 0;
				// }

				.widget-view-drag {
					display: block;
				}
			}

			&.active {
				outline: 1px solid $themeColor;
				border: 1px solid $themeColor;
			}

			&.ghost {
				background: #f56c6c;
				border: 2px solid #f56c6c;
				outline-width: 0;
				height: 3px;
				box-sizing: border-box;
				font-size: 0;
				content: "";
				overflow: hidden;
				padding: 0;
			}
		}

		.widget-table {
			padding-bottom: 0;
			padding: 5px;
			background-color: rgba(253, 246, 236, 0.3);

			.widget-table-wrapper {
				min-height: 50px;
				background: #fff;
				display: flex;
				justify-content: flex-start;

				.widget-table-row {
					td {
						border-bottom: 0;
					}
				}

				.widget-table-left {
					width: 51px;
					border-left: 1px solid #ebeef5;
					border-right: 1px solid #ebeef5;
					border-top: 1px solid #ebeef5;
					flex: none;
				}

				.widget-table-view {
					border: 1px solid #ebeef5;
					width: 200px;
					float: left;
					height: 100%;
					position: relative;
					display: block;

					.el-table {
						height: 100%;
					}

					&.is_req {
						.el-form-item__label::before {
							content: "*";
							color: #f56c6c;
							margin-right: 4px;
						}
					}

					.widget-view-description {
						height: 15px;
						line-height: 15px;
						font-size: 13px;
						margin-top: 6px;
						color: #909399;
					}

					.widget-view-action {
						position: absolute;
						right: 0;
						bottom: 0;
						height: 28px;
						line-height: 28px;
						background: $themeColor;
						z-index: 9;

						i {
							font-size: 14px;
							color: #fff;
							margin: 0 5px;
							cursor: pointer;
						}
					}

					.widget-view-drag {
						position: absolute;
						left: 0;
						top: 0;
						// bottom: -18px;
						height: 24px;
						line-height: 24px;
						background: $themeColor;
						z-index: 9;
						// display: none;

						i {
							font-size: 14px;
							color: #fff;
							margin: 0 5px;
							cursor: move;
						}
					}

					&::after {
						position: absolute;
						left: 0;
						right: 0;
						top: 0;
						bottom: 0;
						display: block;
						content: "";
					}

					&::before {
						display: none;
					}

					&:hover {
						background: $primary-background-color;
						outline: 1px solid $primary-color;
						outline-offset: -1px;

						&.active {
							// outline: 1px solid $primary-color;
							border: 1px solid $primary-color;
							outline: 1px solid $primary-color;
							outline-offset: -1px;
						}

						.widget-view-drag {
							display: block;
						}
					}

					&.active {
						outline: 1px solid $primary-color;
						border: 1px solid $primary-color;
						outline-offset: -1px;
					}

					&.ghost {
						background: #f56c6c;
						outline-width: 0;
						width: 5px !important;
						box-sizing: border-box;
						font-size: 0;
						content: "";
						overflow: hidden;
						padding: 0;
						position: relative;
						outline: none !important;
						border: 0 !important;

						&::after {
							background: #f56c6c;
							position: absolute;
							top: 0;
							left: 0;
							bottom: 0;
							right: 0;
							z-index: 9999;
							content: "";
							outline: none;
						}
					}
				}

				.widget-table-content {
					width: 100%;
					// border: 1px dashed #ccc;
					outline: 1px dashed #ccc;
					background: #fff;
					flex: 1;
					margin: 0 1px;
					overflow: auto;

					& > div {
						height: 100%;
					}

					.widget-table-col {
						height: 100%;

						.ghost {
							background: #f56c6c;
							// border: 2px solid #F56C6C;
							position: relative;
							content: "";
							float: left;
							height: 100%;
							width: 5px !important;
							list-style: none;
							font-size: 0;
							overflow: hidden;
							outline: none;

							&::after {
								background: #f56c6c;
								position: absolute;
								top: 0;
								left: 0;
								bottom: 0;
								right: 0;
								z-index: 9999;
								content: "";
								outline: none;
							}
						}
					}
				}
			}

			&.active {
				outline: 2px solid #e6a23c;
				border: 1px solid #e6a23c;
			}

			&:hover {
				background: #fdf6ec;
				outline: 1px solid #e6a23c;
				outline-offset: 0px;

				&.active {
					outline: 2px solid #e6a23c;
					border: 1px solid #e6a23c;
					outline-offset: 0;
				}
			}

			.widget-view-action.widget-col-action {
				// background: #e6a23c;
			}

			.widget-view-drag.widget-col-drag {
				// background: #e6a23c;
			}

			&::after {
				display: none;
			}

			&.ghost {
				background: #f56c6c;
				outline-width: 0;
				height: 5px;
				box-sizing: border-box;
				font-size: 0;
				content: "";
				overflow: hidden;
				padding: 0;
				position: relative;
				outline: none;
				border: 0;

				&::after {
					background: #f56c6c;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					content: "";
					display: block;
					z-index: 999;
				}
			}
		}

		.widget-col {
			padding-bottom: 0;
			padding: 5px;
			// background-color: rgba(253, 246, 236, 0.3);

			&.active {
				outline: 1px solid $themeColor;
				border: 1px solid $themeColor;
			}

			&:hover {
				// background: #fdf6ec;
				outline: 1px solid $themeColor;
				outline-offset: 0px;

				&.active {
					outline: 1px solid $themeColor;
					border: 1px solid $themeColor;
					outline-offset: 0;
				}
			}

			.el-col {
				min-height: 50px;
			}

			&.ghost {
				background: #f56c6c;
				border: 2px solid #f56c6c;
				outline-width: 0;
				height: 3px;
				box-sizing: border-box;
				font-size: 0;
				content: "";
				overflow: hidden;
				padding: 0;
			}

			.widget-view-action.widget-col-action {
				// background: #e6a23c;
			}

			.widget-view-drag.widget-col-drag {
				// background: #e6a23c;
			}

			&::after {
				display: none;
			}
		}

		.ghost {
			background: #f56c6c;
			border: 2px solid #f56c6c;
			outline-width: 0;
			height: 3px;
			box-sizing: border-box;
			font-size: 0;
			content: "";
			overflow: hidden;
			padding: 0;
		}
	}

	.ghost {
		background: #f56c6c;
		border: 2px solid #f56c6c;
		position: relative;

		&::after {
			background: #f56c6c;
		}
	}

	li.ghost {
		height: 5px;
		list-style: none;
		font-size: 0;
		overflow: hidden;
	}

	.widget-grid {
		background: #f4f6fc;
		position: relative;
		border-left: 5px solid transparent;
		padding: 5px;
		margin: 0 !important;

		&.active {
			border-left: 5px solid $primary-color;
			background: #b3d8ff;
		}
	}

	.widget-grid-container {
		&.ghost {
			background: #f56c6c;
			border: 2px solid #f56c6c;
			outline-width: 0;
			height: 3px;
			box-sizing: border-box;
			font-size: 0;
			content: "";
			overflow: hidden;
			padding: 0;
		}
	}

	.ghost {
		background: #f56c6c;
		border: 2px solid #f56c6c;
		position: relative;

		&::after {
			background: #f56c6c;
		}
	}

	li.ghost {
		height: 5px;
		list-style: none;
		font-size: 0;
		overflow: hidden;
	}

	.ghostEmmm {
		background: #f56c6c;
		border: 2px solid #f56c6c;
		position: relative;

		&::after {
			background: #f56c6c;
		}
	}
	li.ghostEmmm {
		height: 100%;
		width: 1px;
		list-style: none;
		font-size: 0;
		overflow: hidden;
	}
}

.viewer-container {
	z-index: 99999 !important;
}

.flex {
	display: flex;
	align-items: center;
}
.flex-space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.flex-wrap {
	display: flex;
	flex-wrap: wrap;
}

.divider {
	width: 1px;
	height: 24px;
	background-color: #c5c5c5;
	margin: 0 24px;
}

.themeTextColor {
	color: $themeColor;
}

.removeBtn {
	color: $dangerColor;
	&:hover {
		color: $dangerColor;
	}
}

.dangerBg {
	background-color: $dangerColor !important;
}

.colorPicker {
	height: 40px;
	padding: 0;
	margin: 0;
	border: 1px solid $bgColor;
	border-radius: 4px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-left: 12px;
	.el-color-picker {
		height: 24px;
		.el-color-picker__trigger {
			height: 24px;
			width: 24px;
			padding: 0;
			border: none;
			.el-color-picker__color {
				border-radius: 4px;
				overflow: hidden;
				border-color: $bgColor;
			}
			.el-color-picker__icon {
				display: none;
			}
		}
	}
	.value {
		margin-left: 12px;
	}
	.percent {
		padding: 0 12px;
		border-left: 1px solid $bgColor;
	}
}

@media print {
	#app {
		-webkit-print-color-adjust: exact;
	}
}
