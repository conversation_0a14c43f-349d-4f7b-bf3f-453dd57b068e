{"name": "monitor-screen", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^4.1.1", "axios": "^1.2.1", "chroma-js": "^2.4.2", "core-js": "^3.8.3", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-ui": "2.15.13", "sass": "^1.71.1", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-i18n": "^6.1.3", "vue-router": "3", "vue2-scale-box": "^0.1.7"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass-loader": "^11.1.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-unused-vars": 0, "vue/multi-word-component-names": "off", "no-debugger": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}