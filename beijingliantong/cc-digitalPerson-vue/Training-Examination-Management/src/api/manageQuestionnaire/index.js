import xhr from "@/api/xhr/index";
import qs from "qs";

const headers = {
  "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
};
export default {
  // 问卷分析-问卷分析-数据分析
  queryQuestionnaireList(data) {
    return xhr({
      url: "/cc-ai-learn/webcall?action=questionnaireDao.queryQuestionnaireList",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问卷分析-问卷分析-数据分析-查询单人试卷填写情况
  questionnaireAnswer(data) {
    return xhr({
      url: "/cc-ai-learn/webcall?action=questionnaireDao.questionnaireAnswer",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问卷作答记录查询
  queryAnswerRecord(data) {
    return xhr({
      url: "/cc-ai-learn/webcall?action=questionnaireDao.queryAnswerRecord",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 编辑问卷查询
  queryQuestionnaire(data) {
    return xhr({
      url: "/cc-ai-learn/questionnaire?action=queryQuestionnaire",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问卷数据统计分析
  queryQuestionnaireDataAnalysis(data) {
    return xhr({
      url: "/cc-ai-learn/servlet/questionnaireServlet?action=queryQuestionnaireDataAnalysis",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问卷管理查询
  queryQuestionnaireMagInfos(data) {
    return xhr({
      url: "/cc-ai-learn/webcall?action=questionnaireDao.queryQuestionnaireMagInfos",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 删除问卷
  deleteQuestionnaireMagById(data) {
    return xhr({
      url: "/cc-ai-learn/servlet/questionnaireServlet?action=deleteQuestionnaireMagById",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问卷分析-题目分析
  queryQuestionAnalysis(data) {
    return xhr({
      url: "/cc-ai-learn/servlet/questionnaireServlet?action=queryQuestionAnalysis",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 问答型问题结果查询
  queryQuestionInfosById(data) {
    return xhr({
      url: "/cc-ai-learn/webcall?action=questionnaireDao.queryQuestionInfosById",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 发布
  handlePublish(data){
    return xhr({
      url: "/cc-ai-learn/servlet/questionnaireServlet?action=GetQsUrl",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 导出
  handleExport(data){
    return xhr({
      url: "/cc-ai-learn/servlet/export?action=exportQuestion",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
      responseType: 'blob'
    });
  },
  // 查询问卷明细配置
  handleQueryQuestionConfig(data){
    return xhr({
      url: "/cc-ai-learn/webcall?action=QuestionConfigDao.list",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 编辑问卷明细
  handleUpdateQuestionConfig(data){
    return xhr({
      url: "/cc-ai-learn/servlet/questionConfigServlet?action=UpdateQuestionConfig",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 查询问卷明细的搜索条件
  handleQuestionSearch(data){
    return xhr({
      url: "/cc-ai-learn/webcall?action=QuestionConfigDao.config",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
  // 查询问卷明细
  handleQuestionConfigDao(data){
    return xhr({
      url: "/cc-ai-learn/webcall?action=QuestionConfigDao.detail",
      method: "post",
      data: qs.stringify({
        data: JSON.stringify(data),
      }),
      headers,
    });
  },
    // 导出
    handleDownload(data){
      return xhr({
        url: "/cc-ai-learn/servlet/questionConfigServlet?action=ExportQuestionList",
        method: "post",
        data: qs.stringify({
          data: JSON.stringify(data),
        }),
        headers,
        responseType: 'blob',
      });
    },
};
