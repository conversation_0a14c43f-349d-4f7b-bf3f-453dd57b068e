<template>
  <div class="detail">
    <div class="header">
      <div class="header-bg">
        <img src="@/assets/content.png" alt="" />
        <div class="mg-t16 mg-b16">
          <van-tag color="#b2caf1" text-color="#0555CE" v-if="form.orderType=='咨询'">{{ form.orderType }}</van-tag>
          <van-tag color="#FDEBEB" text-color="#F03838" v-if="form.orderType=='投诉'">{{ form.orderType }}</van-tag>
          <van-tag color="#FDEBEB" text-color="#F03838" v-if="form.orderType=='举报'">{{ form.orderType }}</van-tag>
          <van-tag color="#b2caf1" text-color="#0555CE" v-if="form.orderType=='建议'">{{ form.orderType }}</van-tag>
          <van-tag color="#FEF4E5" text-color="#FA9904" v-if="form.orderType=='求助'">{{ form.orderType }}</van-tag>
          <van-tag color="#FEF4E5" text-color="#FA9904" v-if="form.orderType=='其他'">{{ form.orderType }}</van-tag>
          <van-tag color="#EDF9E8" text-color="#52C41A" v-if="form.orderType=='表扬'">{{ form.orderType }}</van-tag>

          <van-tag class="mg-l16" color="#b2caf1" text-color="#0555CE" v-if="form.orderStatus=='待受理'">{{ form.orderStatus }}</van-tag>
          <van-tag class="mg-l16" color="#FDEBEB" text-color="#F03838" v-if="form.orderStatus=='待完善'">{{ form.orderStatus }}</van-tag>
          <van-tag class="mg-l16" color="#FEF4E5" text-color="#a5a5a5" v-if="form.orderStatus=='已失效'">{{ form.orderStatus }}</van-tag>
          <van-tag class="mg-l16" color="#b2caf1" text-color="#0555ce" v-if="form.orderStatus=='处理中'">{{ form.orderStatus }}</van-tag>
          <van-tag class="mg-l16" color="#FEF4E5" text-color="#a5a5a5" v-if="form.orderStatus=='已关闭'">{{ form.orderStatus }}</van-tag>
          <van-tag class="mg-l16" color="#EDF9E8" text-color="#52C41A" v-if="form.orderStatus=='处理完成'">{{ form.orderStatus }}</van-tag>

          
        </div>
        <div class="header-title">{{ form.requestTopic }}</div>
      </div>
      <div class="header-content">
        {{ form.requestContent }}
      </div>
    </div>

    <div class="footer">
      <div class="footer-content">
        <span id="sp">事项编号</span>
        <span>{{ form.instId }}</span>
      </div>
      <div class="footer-content">
        <span id="sp">事发地址</span>
        <span id="sp2"
          >{{ form.requestLocaition }}</span
        >
      </div>
      <!-- <div class="footer-content">
        <span id="sp" >附件</span>
        <div class="flex">
          <div v-for="item in attachs" :key="item.id">
            <img
              v-if="item.type == 'image'"
              class="footer-content-img"
              :src="item.F_URL"
              alt=""
              @click="viewImage(item)"
            />
            <video v-if="item.type == 'video'" :src="item.F_URL" style="width: 100%; height: 100%">

            </video>
          </div>
      </div>
      </div> -->
      <div class="footer-content">
        <div id="sp" class="attment2" >
          附件
        </div>
        <div class="attment2-list" >
          <div v-for="item in attachs" :key="item.fId" class="list-item" @click="handleDownLoad(item)">
            {{ item.fName }}
          </div>
          
          
        </div>
       

      </div>
      

    </div>
    <!-- <div class="evaluate" v-if="LAST_SATISFY">
      <img src="@/assets/evaluateResult.png" alt="" />
      <div class="evaluate-content">
        <div class="evaluate-content-list">
          <div class="text1">满意评价</div>
          <div class="text2">{{ LAST_SATISFY }}</div>
        </div>
        <div class="evaluate-content-list">
          <div class="text1">评价意见</div>
          <div class="text2">{{ LAST_SATISFY_REMARK }}</div>
        </div>
        <div class="evaluate-content-list">
          <div class="text1">评价时间</div>
          <div class="text2">{{ LAST_SATISFY_TIME }}</div>
        </div>
      </div>
    </div>
    <div class="evaluate2" v-else>
      <img src="@/assets/evaluateResult.png" alt="" />
      <div class="evaluate-content">
        <div class="evaluate-content-list">
          <div class="text1">暂无评价</div>
        </div>
      </div>
    </div> -->
    <van-image-preview v-model="showImage" :images="currentImage" />
  </div>
</template>

<script>
import { appealDetail,getOrderAttach,downloadAttach } from "@/api";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      form:{
        orderType:"",//工单类型
        requestTopic:"",//诉求标题
        requestContent:"",//诉求内容
        instId:"",//诉求编号
        orderStatus:"",//工单状态
        requestLocaition:"",//地址
      },
      attachs: [], //附件
      LAST_SATISFY: "", //满意评价
      LAST_SATISFY_REMARK: "", //评价意见内容
      LAST_SATISFY_TIME: "", //评价时间
      currentImage: [],
      showImage: false,
      viewVideo: false,
      currentVideo: "",
      token:"",

    };
  },
  computed: {
    ...mapGetters(["ORDER_TYPE", "evaluate"]),
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem("token"));
    this.getDetailList();
    this.getOrderAttach()
  },
  methods: {
    getDetailList() {
      let query = {
        order:{
          instId: this.$route.query.instId,
          contactTel:localStorage.getItem('phone') || ""
        }
      };
      appealDetail(this.token,query)
        .then((res) => {
          if(res.code =='0010'){
            this.form = res.data

          }else{
            this.$toast.fail(res.msg)

          }
        })
        .catch((err) => {
          this.$toast.fail(res)
        });
    },
    getOrderAttach(){
      let query = {
        order:{
          instId: this.$route.query.instId,
        }
      };
      getOrderAttach(this.token,query)
        .then((res) => {
          console.log(res)
          if(res.code =='0010'){
            this.attachs = res.data

          }else{
            this.$toast.fail(res.msg)

          }
        })
        .catch((err) => {
          this.$toast.fail(res)
        });

    },
    viewImage(raw) {
      var list = this.attachs.filter((item) => item.type == "image");
      if (list.length) {
        this.currentImage = list.map((item) => item.F_URL);
        this.showImage = true;
      }
    },
    // 下载图片
    handleDownLoad(item){
      let query = {
        attach:{
          fId: item.fId,
        }
      };
      downloadAttach(this.token,query)
        .then((res) => {
          console.log('下载图片',res)
          const blob = new Blob([res]);
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = item.fName +'.png';
        link.click();

        })


    }
  },
};
</script>

<style lang="scss" scoped>
.detail {
  padding: 24px;
  min-height: 100vh;
  background: #f2f4f7;
  box-sizing: border-box;
  .header {
    width: 702px;
    border-radius: 16px;
    background: #ffffff;
    .header-bg {
      padding: 32px 32px 0px 32px;
      border-radius: 16px;
      background: linear-gradient(
        180deg,
        #a5d0ff 0%,
        rgba(236, 243, 245, 0) 100%
      );
    }
    .header-title {
      font-family: Alibaba PuHuiTi;
      font-size: 32px;
      font-weight: bold;
      line-height: 48px;
      color: #262626;
      // word-break: keep-all;
      // white-space: nowrap;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
  }
  .header-content {
    padding: 32px;
    font-family: SourceHanSansCN-Regular;
    font-size: 28px;
    line-height: 44px;
    color: #868686;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 3; //想要的行数
    // -webkit-box-orient: vertical;
  }
}
.footer {
  margin-top: 24px;
  padding-bottom: 32px;
  border-radius: 16px;
  background: #ffffff;
  #sp {
    color: #868686;
    white-space:nowrap;
  }
  #sp2 {
    text-align: right;
    width: 400px;
  }
  .footer-content {
    padding: 32px 32px 0px 32px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-family: SourceHanSansCN-Regular;
    font-size: 28px;
    font-weight: normal;
    line-height: 40px;
    letter-spacing: 0em;
    .footer-content-sp {
      margin-top: 40px;
      letter-spacing: 2em;
    }
    .footer-content-img {
      width: 108px;
      height: 108px;
      margin-left: 12px;
    }
  }
  .attment{
    height: 108px;
  }
  
}
.evaluate {
  margin-top: 24px;
  width: 702px;
  height: 388px;
  background: linear-gradient(
    180deg,
    #a5d0ff 0%,
    rgba(236, 243, 245, 0) 50%,
    #ffffff 100%
  );
  border-radius: 16px;
  img {
    margin-top: 32px;
    margin-left: 32px;
  }
}
.evaluate-content {
  margin-top: 76px;
  .evaluate-content-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 32px 32px 40px;
    div {
      font-family: SourceHanSansCN-Regular;
      font-size: 28px;
      font-weight: normal;
      line-height: 40px;
    }
    .text1 {
      color: #868686;
    }
    .text2 {
      color: #262626;
    }
  }
}
.evaluate2 {
  margin-top: 24px;
  width: 702px;
  height: 216px;
  background: linear-gradient(180deg, #a5d0ff 0%, rgba(236, 243, 245, 0) 100%);
  border-radius: 16px;
  img {
    margin-top: 32px;
    margin-left: 32px;
  }
}
.attment2{
    width:110px;
  }
  .attment2-list{
    flex:1;
    overflow: hidden;
    text-align: end;
    .list-item{
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
     
    }
  }
</style>