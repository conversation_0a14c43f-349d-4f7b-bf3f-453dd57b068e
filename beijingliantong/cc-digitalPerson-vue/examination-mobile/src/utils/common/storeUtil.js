function setStr(key, str) {
  window.localStorage.setItem(key, str);
}

function getStr(key) {
  return window.localStorage.getItem(key);
}

function setObj(key, obj) {
  const jsonStr = JSON.stringify(obj);
  setStr(key, jsonStr);
}

function getObj(key) {
  const jsonStr = getStr(key);
  if (jsonStr) {
    return JSON.parse(jsonStr);
  }
  return null;
}

function remove(key) {
  window.localStorage.removeItem(key);
}

export default {
  setStr,
  getStr,
  setObj,
  getObj,
  remove,
};
