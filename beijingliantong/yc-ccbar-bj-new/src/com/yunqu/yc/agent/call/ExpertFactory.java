package com.yunqu.yc.agent.call;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.easitline.common.core.context.ServerContext;

import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.util.CacheUtil;

/**
 * 外线专家管理工厂
 * <AUTHOR>
 *
 */
public class ExpertFactory {
	
	private static ExpertFactory  factory  = null;
	
	/**
	 * 保存当前的外线专家列表。
	 */
	private Map<String,Expert>  expertMap = new HashMap<String,Expert>();
	
	private List<Expert>  expertList = new ArrayList<Expert>();

	/**
	 * 保存当前最后一个外呼外线坐席，每次执行一次外呼外线，则从池里面获取一个外线坐席进行匹配。
	 */
	private  int  order = 0 ;
	
	private String entCode;
	
	public static synchronized ExpertFactory getInstance(){
		if(factory == null){
			factory = new ExpertFactory();
			factory.initCode();
			factory.initExpert();
		}
		return factory;
	}
	
	private void initExpert(){
		for(int i = 8000;i<8100;i++){
			Expert expert = new Expert(i+"@"+entCode);
			expertMap.put(expert.getExpertId(),expert);
			expertList.add(expert);
		}
	}
	
	private void initCode(){
		
		try {
			String cacheIndex = CacheUtil.hget("#expert",ServerContext.getServerName());
			if(StringUtils.isNotBlank(cacheIndex)){
				this.entCode = "out"+cacheIndex;
				return;
			}
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("ExpertFactory.initCode() error,cause:"+ex.getMessage(),ex); 
		}
		
		int index = 1000;
		try {
			Map<String,String> map = CacheUtil.hgetAll("#expert");
			Set<String> keys = map.keySet();
			for(String key:keys){
				int _temp = Integer.parseInt(map.get(key));
				if(_temp>=index){
					index = _temp;
				}
			}
			index++;
			this.entCode = "out"+index;
			CacheUtil.hset("#expert",ServerContext.getServerName(),index+"");
			return;
		} catch (Exception e) {
			// TODO: handle exception
		}
		index = new Random().nextInt(999999);
		this.entCode = "out"+index;
		return;
	}
	

	public Expert getExpert(String expertId){
		Expert expert =  this.expertMap.get(expertId);
		//CcbarLogger.getLogger().warn("ExpertFactory.getExpert("+expertId+")->"+expert);
		return expert;
	}
	
	/**
	 * 执行外线专家的批量
	 */
	public synchronized Expert getNextExpert(){
		CcbarLogger.getLogger().info("getNextExpert(index:"+order+"),experts.size()->"+expertList.size());
		Expert expert =  expertList.get(order);
		CcbarLogger.getLogger().info("getNextExpert(index:"+order+")->"+expert);
		order++;  
		if(order == 100)   order = 0 ;
		return expert;
		
	}
	
//	public static void main(String[] args) {
//		int index = new Random().nextInt(999999);
//		System.out.println(index);
//	}

}
