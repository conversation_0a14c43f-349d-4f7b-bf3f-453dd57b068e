<link rel="stylesheet" href="/yc-ccbar/ccbar/ccbar-sdk.css">
<link rel="stylesheet" href="css/theme.css">
<link rel="stylesheet" href="fonts/iconfont/iconfont.css">


<!-- 第一种 -->
<div id="ccbarWebrctTheme1" class="ccbar-webrtc-panel theme1" >
	<div class="ccbar-webrtc-panel-toggle"></div>

	<div class="ccbar-webrtc-panel-main">
		<div class="ccbar-call"></div>
		<!-- 视频展示 -->
		<div class="ccbar-webrtc-videos">
			<div class="flex-row" style="height:100%">
				<div class="ccbar-video-show flex-item miniView"></div>
				<div class="ccbar-video-show flex-item mainiView"></div>
			</div>
		</div>
		<!-- 操作按钮 -->
		<div class="ccbar-webrtc-btns">
			<a data-ccbartype="clearcall" href="#" class="ccbar-webrtc-btn">结束</a>
			<a data-ccbar-webrtc-share="screen" href="#" class="ccbar-webrtc-btn">分享</a>
		</div>
	</div>
</div>

<!-- 第二 -->
<div id="ccbarWebrctTheme2" class="ccbar-webrtc-panel theme2"  style="display:none">
	<div class="ccbar-webrtc-panel-toggle"></div>

	<div class="ccbar-webrtc-panel-main">
		
		<!-- 操作按钮 -->
		<div class="ccbar-webrtc-btns">
			<a data-ccbartype="clearcall" href="#" class="ccbar-webrtc-btn">结束</a>
			<a data-ccbar-webrtc-share="screen" href="#" class="ccbar-webrtc-btn">分享</a>
			<a data-ccbar-webrtc="toggle" href="#" class="ccbar-webrtc-btn">切换</a>
		</div>
		<!-- 视频展示 -->
		<div class="ccbar-webrtc-videos">
			<div class="ccbar-video-show miniView"><video src=""></video></div>
			<div class="ccbar-video-show mainiView"></div>
		</div>
		<div class="ccbar-webrtc-info">
			<span style="float:right">13112341234</span>
			<span>通话中</span>
			<span>00:32</span>
		</div>
	</div>
</div>

<!-- 第3 -->
<div id="ccbarWebrctTheme3" class="ccbar-webrtc-panel theme2 theme3" style="display:none" >
	<div class="ccbar-webrtc-panel-toggle"></div>

	<div class="ccbar-webrtc-panel-main">
		
		<!-- 操作按钮 -->
		<div class="ccbar-webrtc-btns">
			<!-- <a data-ccbartype="clearcall" href="#" class="ccbar-webrtc-btn">结束</a>
			<a data-ccbar-webrtc-share="screen" href="#" class="ccbar-webrtc-btn">分享</a>
			<a data-ccbar-webrtc-share="screen" href="#" class="ccbar-webrtc-btn">切换</a> -->
			<!-- <button data-ccbartype="logon" class="function-btn"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agentready"></i><span class="func-btn-label">签入</span></button> -->
			<button class="function-btn disabled" style="float: right;"><i class="func-btn-icon _ccbar_icon _ccbar_icon-logoff"></i><span class="func-btn-label">隐藏</span></button>

			<button data-ccbartype="clearcall" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-clearcall"></i><span class="func-btn-label">挂机</span></button>

			<button data-ccbartype="agentnotready" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agentnotready"></i><span class="func-btn-label">置忙</span></button>

			<button data-ccbartype="agentready" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agent"></i><span class="func-btn-label">置闲</span></button>

			<button data-ccbartype="mutecall" class="function-btn funcbtn-hide disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-holdcall"></i> <span class="func-btn-label">麦克风</span></button>

			<button data-ccbartype="mutecall" class="function-btn funcbtn-hide disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-wendang"></i> <span class="func-btn-label">摄像头</span></button>

			<button data-ccbar-webrtc-share="screen" class="function-btn funcbtn-hide disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-monitor"></i> <span class="func-btn-label">屏幕共享</span></button>
		</div>
		<div class="ccbar-webrtc-info">
			<span style="float:right">13112341234</span>
			<span>通话中</span>
			<span>00:32</span>
		</div>
		<!-- 视频展示 -->
		<div class="ccbar-webrtc-videos">
			<div class="ccbar-video-show miniView"><video src=""></video></div>
			<div class="ccbar-video-show mainiView"></div>
		</div>
		
	</div>
</div>

<!-- 第四 -->
<div id="theme4" class="ccbar-webrtc-panel theme2 theme4"  style="height: 482px; display: none;">
	<div class="ccbar-webrtc-panel-toggle"></div>

	<div class="ccbar-webrtc-panel-main">
		
		<!-- 操作按钮 -->
		<!-- <div class="ccbar-webrtc-btns">
			<a data-ccbartype="clearcall" href="#" class="ccbar-webrtc-btn">结束</a>
			<a data-ccbar-webrtc-share="screen" href="#" class="ccbar-webrtc-btn">分享</a>
			<a data-ccbar-webrtc="toggle" href="#" class="ccbar-webrtc-btn">切换</a>
		</div> -->
		<!-- 视频展示 -->
		<div class="ccbar-webrtc-videos">
			<div class="ccbar-video-show miniView"><video src=""></video></div>
			<div class="ccbar-video-show mainiView"></div>
		</div>
		<!-- <div class="ccbar-webrtc-info">
			<span style="float:right">13112341234</span>
			<span>通话中</span>
			<span>00:32</span>
		</div> -->
	</div>
</div>

<!-- 新的七版本 -->
<div id="theme7" class="ccbar-webrtc-panel theme7" style="display: none;">
	<div class="ccbar-webrtc-panel-header">
		<a href="javascript:;" class="ccbar-webrtc-panel-min"><i class="ccbar-webrtc-icon icon-min"></i></a>
		<div data-ccbar-sip-state="online" id="yqMark" class="ccbar-webrtc-sip-state"></div>
		<span class="sipinfo">话机-<span id="yqCallerNum">12345</span></span>
		<span class="calling" style="display:none">
			<span class="ccbar-webrtc-text">视频通话中</span>
			<span data-ccbar-webrtc-text="custphone" class="ccbar-webrtc-text">13112341234</span>
		</span>
	</div>
	<div id="ccbarVoiceView" class="ccbar-webrtc-panel-main">
		<div class="flex" style="background: #f2f2f2;">
			<div class="ccbar-webrtc-panel-keyboard-number">
				<div class="flex-row">
					<div class="flex-item"><input id="ccbarWebrtcInput" readonly="" autocomplete="off" type="text" autofocus=""></div>
				</div>
			</div>
			<div class="flex-item">
				<div class="ccbar-webrtc-panel-keyboard">
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="1">1</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="2">2</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="3">3</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="4">4</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="5">5</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="6">6</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="7">7</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="8">8</div>
					</div>
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="9">9</div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="*">*</div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="0">0</div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key" data-ccbar-webrtc-keyboard="3">#</div>
					</div>
				</div>
				
			</div>
			<div class="ccbar-webrtc-panel-keyboard-btns">
					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key"><i class="ccbar-webrtc-icon icon-voice"></i></div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item yes">
						<div class="ccbar-webrtc-panel-keyboard-key">
							<i class="ccbar-webrtc-icon icon-camera"></i><br><span class="ccbar-webrtc-panel-keyboard-text">视频</span>
						</div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item yes">
						<div class="ccbar-webrtc-panel-keyboard-key">
							<i class="_ccbar_icon _ccbar_icon-answercall2"></i><br><span class="ccbar-webrtc-panel-keyboard-text">音频</span>
						</div>
					</div>

					<div class="ccbar-webrtc-panel-keyboard-item">
						<div class="ccbar-webrtc-panel-keyboard-key">
							<i class="ccbar-webrtc-icon icon-clearcall"></i>
						</div>
					</div>
				</div>
		</div>
	</div>
	<div id="ccbarVideoView" class="ccbar-webrtc-panel-main" style="display:none">
		<div class="ccbar-webrtc-videos">
			<div class="ccbar-video-show miniView">
				<div class="ccbar-video-signal"><i class="ccbar-webrtc-icon icon-xinhao"></i></div>
				<div class="ccbar-video" data-ccbar-webrtc-video="local"></div>
			</div>
			<div class="ccbar-video-show mainiView">
				<div class="ccbar-video-signal"><i class="ccbar-webrtc-icon icon-xinhao"></i></div>
				<div class="ccbar-video" data-ccbar-webrtc-video="remote"></div>
			</div>

			<div class="ccbar-webrtc-controls">
				<a href="javascript:;" title="截图" data-ccbar-webrtc-share="screenshot"  class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-screenshot"></i></a>
				<!-- <a href="javascript:;" title="媒体分享" data-ccbar-webrtc-share="resource" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-media"></i></a> -->
				<a href="javascript:;" title="屏幕分享" data-ccbar-webrtc-share="screen" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-wendang"></i></a>
				<a href="javascript:;" title="麦克风开关" data-ccbar-webrtc-microphone="toggle" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-voice"></i></a>
				<a href="javascript:;" title="切换麦克风" data-ccbar-webrtc-microphone="switch" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-voiceswitch"></i></a>
				<a href="javascript:;" title="摄像头开关" data-ccbar-webrtc-camera="toggle" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-camera"></i></a>
				<a href="javascript:;" title="切换摄像头" data-ccbar-webrtc-camera="switch" class="ccbar-webrtc-btn"><i class="ccbar-webrtc-icon icon-cameraswitch"></i></a>
				<a href="javascript:;" data-ccbartype="clearcall" title="挂机" class="ccbar-webrtc-btn clearcall"><i class="ccbar-webrtc-icon icon-clearcall"></i></a>
			</div>
		</div>
	</div>

	<div class="ccbar-webrtc-panel-footer"></div>

</div>