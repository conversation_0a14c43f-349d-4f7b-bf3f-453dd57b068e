<?xml version="1.0" encoding="UTF-8"?>
<project name="cc-notes" default="makewar" basedir=".">

	<property name="SrcDir" location="src" />
	<property name="DestDir" location="" />
	<property name="WebDir" location="WebContent" />
	
	<target name="cleanDir" description="清除文件">
				
	</target>
	
	<target name="makewar" description="Create a war for the module">
		<!-- 备份代码 -->
		<delete dir="${WebDir}/WEB-INF/bakup/file.zip"/>
		<zip destfile="${WebDir}/WEB-INF/bakup/file.zip" basedir="${SrcDir}"/>
		<jar jarfile="${DestDir}/cc-notes.war" basedir="${WebDir}" />
	</target>
	
</project>
