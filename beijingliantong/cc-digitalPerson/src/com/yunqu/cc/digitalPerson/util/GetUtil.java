package com.yunqu.cc.digitalPerson.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import com.yq.busi.common.util.DateUtil;
import org.apache.http.Header;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.http.HttpResp;
import com.yunqu.cc.digitalPerson.base.Constants;

public class GetUtil {
	
    public final static String APP_ID = AppContext.getContext(Constants.APP_NAME).getProperty("APP_ID", "");
	
	public final static String TOKEN_APP_ID = AppContext.getContext(Constants.APP_NAME).getProperty("TOKEN_APP_ID", "");
	
	public final static String SECRET = AppContext.getContext(Constants.APP_NAME).getProperty("SECRET", "");
	
	public final static String CVTE_URL = AppContext.getContext(Constants.APP_NAME).getProperty("CVTE_URL", "");
	/**
	 * 向指定URL发送GET方法的请求
	 * 
	 * @param url
	 *            发送请求的URL
	 * @param param
	 *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
	 * @return URL 所代表远程资源的响应结果
	 */
	public static HttpResp sendGet(String url, String param, String charset,List<Header> headers) {
		if (StringUtils.isBlank(charset)) {
			charset = GWConstants.ENCODE_UTF8;
		}
		HttpResp resp = new HttpResp();
		if (StringUtils.isBlank(url)) {
			resp.setResult("请求URL不能为空!");
			return resp;
		}
		

		String result = "";
		try {
			String urlNameString = joinGetUrl(url, param);

			URL realUrl = new URL(urlNameString);
			// 打开和URL之间的连接
			HttpURLConnection connection = (HttpURLConnection)realUrl.openConnection();
			// 设置通用的请求属性
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			connection.setRequestProperty("Content-Type", "text/html;charset=" + charset);
			connection.setConnectTimeout(60000);
			connection.setReadTimeout(60000);
			if(headers!=null)
				for(int i=0;i<headers.size();i++){
					Header header = headers.get(i);
					connection.setRequestProperty(header.getName(), header.getValue());
				}		
			// 建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			resp.setCode(connection.getResponseCode());
			//获取响应
			result = readStrByCode(connection.getInputStream(), charset);
			resp.setResult(result);
		} catch (Exception e) {
			CommonCoreLogger.logger.error("[HttpUtil.sendGet] 发送GET请求出错:"+e.getMessage(),e);
			System.out.println("发送GET请求出现异常！" + e);
			e.printStackTrace();
		}finally {
			
		}
		return resp;
	}
	

	/**
	 * 将get请求的url和param拼接起来
	 * 
	 * @param url
	 * @param param
	 * @return
	 */
	private static String joinGetUrl(String url, String param) {

		String urlNameString = url;

		if (StringUtils.isNotBlank(param)) {
			if (url.indexOf("?") == -1 && !param.startsWith("&")) {
				urlNameString = url + "?" + param;
			} else if (url.indexOf("?") != -1 && !param.startsWith("&")) {
				urlNameString = url + "&" + param;
			}
		}

		return urlNameString;
	}
	
	/**
	 * 从输入流里按指定字符集获取响应内容
	 * @param is
	 * @param charset
	 * @return
	 */
	public static String readStrByCode(InputStream is, String charset) {
		StringBuilder builder = new StringBuilder();
		BufferedReader reader = null;
		String line = "";
		try {
			reader = new BufferedReader(new InputStreamReader(is, charset));
			while ((line = reader.readLine()) != null) {
				builder.append(line);
			}
		} catch (Exception e) {
			e.printStackTrace();
			try {
				reader.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if(reader!=null){
					reader.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return builder.toString();
	}
	
	public static String getIP(HttpServletRequest request) {
	    String ip = request.getHeader("x-forwarded-for");
	    if (!checkIP(ip)) {
	        ip = request.getHeader("Proxy-Client-IP");
	    }
	    if (!checkIP(ip)) {
	        ip = request.getHeader("WL-Proxy-Client-IP");
	    }
	    if (!checkIP(ip)) {
	        ip = request.getRemoteAddr();
	    }
	    if(!checkIP(ip)){
	    	try {
	    		ip = InetAddress.getLocalHost().getHostAddress().toString();
			} catch (UnknownHostException e) {}
	    }
	    return ip;
	}
	

	public static boolean checkIP(String ip) {
	    if (ip == null || ip.length() == 0 || "unkown".equalsIgnoreCase(ip)
	            || ip.split(".").length != 4) {
	        return false;
	    }
	    return true;
	}
	
	public static String getDomainName(HttpServletRequest request){
		StringBuffer url = request.getRequestURL();
		String tempContextUrl = url.delete(url.length() - request.getRequestURI().length(), url.length()).toString();
        return tempContextUrl;
	}

	/**
	 * 将16进制颜色编码换成BGR格式（字幕合成时使用，python程序需要BRG格式编码）
	 * @param rgb
	 * @return
	 */
	public static String RgbToBrg(String rgb) {
		int red = Integer.parseInt(rgb.substring(0, 2), 16);
		int green = Integer.parseInt(rgb.substring(2, 4), 16);
		int blue = Integer.parseInt(rgb.substring(4, 6), 16);
		String buleInt = Integer.toHexString(blue);
		String greenInt = Integer.toHexString(green);
		String redInt = Integer.toHexString(red);
		StringBuilder bgrHexBuilder = new StringBuilder();
		bgrHexBuilder.append(buleInt.length()==1 ? "0" + buleInt : buleInt);
		bgrHexBuilder.append(greenInt.length()==1 ? "0" + greenInt : greenInt);
		bgrHexBuilder.append(redInt.length()==1 ? "0" + redInt : redInt);
		return bgrHexBuilder.toString();
	}

	/**
	 * 校验时间格式：HH:mm:ss
	 * @param time
	 * @return
	 */
	public static boolean checkTime(String time){
		String regex = "^\\d{2}:\\d{2}:\\d{2}$";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(time);
		if (matcher.matches()) {
			if (DateUtil.getDate("HH:mm:ss", time) != null) {
				return true;
			}
		}
		return false;
	}
}
