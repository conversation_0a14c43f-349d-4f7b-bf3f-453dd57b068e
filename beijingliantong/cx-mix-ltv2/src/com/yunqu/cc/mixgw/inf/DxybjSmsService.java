package com.yunqu.cc.mixgw.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.EntContext;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.HttpResp;
import com.yunqu.cc.mixgw.util.HttpUtil;
import com.yunqu.cc.mixgw.util.MD5Util;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

/**
 * 大兴医保局短信渠道接口
 * <AUTHOR>
 * @date 2024年5月21日i下午3:06:27
 *
 */
public class DxybjSmsService extends IService {

    protected Logger logger = CommonLogger.getLogger("dxybjsms");

    @Override
    public JSONObject invoke(JSONObject json) {
    	logger.info(CommonUtil.getClassNameAndMethod(this) + " 发送短信收到参数,json:" + json.toJSONString());
        JSONObject rtJson = new JSONObject();
        String command = json.getString("command");
        if("smsSendEx".equals(command)) {
        	String phoneNum =json.getString("receiver");	//客户的手机号码
    		String content =json.getString("content");	//发送内容
    		String smsId = json.getString("smsId");	//发送内容
    		if(StringUtils.isBlank(phoneNum)){
    			logger.info(CommonUtil.getClassNameAndMethod(this) + " 手机号码不能为空");
    			rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "手机号码不能为空");
    			return rtJson;
    		}
    		if(StringUtils.isBlank(content)){
    			logger.info(CommonUtil.getClassNameAndMethod(this) + " 内容不能为空");
    			rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "内容不能为空");
    			return rtJson;
    		}
    		JSONObject exJson = json.getJSONObject("exJson");
    		String TemplateCode = "";
    		if(exJson!=null) {
    			TemplateCode = exJson.getString("TemplateCode");
    		}
    		if(StringUtils.isBlank(TemplateCode)) {
    			TemplateCode = getTempCode(content,json.getString("schema"));
    		}
    		if(StringUtils.isBlank(TemplateCode)){
    			logger.info(CommonUtil.getClassNameAndMethod(this) + " 未获取到工单模版");
    			rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "未获取到工单模版");
    			return rtJson;
    		}
    		json.put("TemplateCode", TemplateCode);
    		JSONObject resultFlag = new JSONObject();
    		resultFlag = this.sendSmsCodeList(json);
    		logger.info(resultFlag);
    		if(resultFlag==null || !"1".equals(resultFlag.getString("code"))){
    			rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "发送短信失败");
    			//return rtJson;
    		}else{
    			json.put("status", "2");
    			rtJson.put("respCode", GWConstants.RET_CODE_SUCCESS);
    			rtJson.put("respDesc", "发送短信成功");
    		}
    		resultFlag = resultFlag.getJSONObject("data");
    		if(resultFlag==null) {
    			rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "未获取到返回值！");
    			return rtJson;
    		}
    		String batch = resultFlag.getString("batch");
    		EasySQL usql = new EasySQL();
    		usql.append("UPDATE " + json.getString("schema") + ".C_SMS_SEND_RECORD set SEND_STATUS= ? ,SMS_SEND_ID=? where ID=?");
    		try {
    			QueryFactory.getWriteQuery().execute(usql.getSQL(),new Object[] {3,batch,smsId} );
    		} catch (SQLException e) {
    			// TODO Auto-generated catch block
    			e.printStackTrace();
    		}
    		//发送成功后，调用接口，通过状态status回传，通知服务接口，已推送到运营商
//    		IService service;
//    		try {
//    			service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
//    			JSONObject param = new JSONObject();
//    			param.put("command", "saveSmsReceipt");
//    			param.put("smsMsgId", json.getString("smsId"));
//    			param.put("type", "3");
//    			param.put("schema", json.getString("schema"));
//    			param.put("epCode", json.getString("epCode"));
//    			param.put("receiver", json.getString("receiver"));
//    			param.put("content", resultFlag.getString("msg"));
//    			param.put("status", resultFlag.getString("msg").indexOf("发送成功")>=0?"0":"1");
//    			param.put("channleId", json.getString("CODE"));
//    			logger.info(CommonUtil.getClassNameAndMethod(this) + ",rtJson:" + param.toJSONString());
//    			service.invoke(param);
//    		} catch (ServiceException e) {
//    			e.printStackTrace();
//    		}
    		
        }else if("smsReceiptEx".equals(command)){
        	logger.info("进入更新短信状态接口！");
        	String id = json.getString("id");//短信批次号
        	String st = json.getString("st");//短信状态报告，stat=DELIVRD表示用户成功接收短信。
        	if(StringUtils.isBlank(id)) {
        		rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "ID不能为空");
    			return rtJson;	
        	}
        	String entId = Constants.getAppConfigProperty("DXYBJ_ENT_ID", "");
        	if(StringUtils.isBlank(entId)) {
        		logger.error("短信回执获取失败，未获取到企业id");
        		rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "短信回执获取失败，未获取到企业id");
    			return rtJson;
        	}
        	EntContext context = EntContext.getContext(entId);
        	try {
        		List<JSONObject> receipts = QueryFactory.getReadQuery().queryForList("select ID,SMS_SEND_ID,RECEIVER,CHANNEL_ID from "
				+context.getSchemaId()+".c_sms_send_record where SMS_SEND_ID=?", new Object[] {id},new JSONMapperImpl());
        		logger.info(JSON.toJSONString(receipts));
        		if(receipts.size()==0) {
        			rtJson.put("respCode", GWConstants.RET_CODE_SUCCESS);
        			rtJson.put("respDesc", "处理成功");
        		}
				for (JSONObject jsonObject : receipts) {
					IService service;
		    		try {
		    			service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
		    			JSONObject param = new JSONObject();
		    			param.put("command", "saveSmsReceipt");
		    			param.put("smsMsgId", jsonObject.getString("ID"));
		    			param.put("type", "3");
		    			param.put("schema", context.getSchemaId());
		    			param.put("epCode", entId);
		    			param.put("receiver", jsonObject.getString("RECEIVER"));
		    			param.put("content",  st);
		    			param.put("status",  st.indexOf("DELIVRD")>=0?"0":"1");
		    			param.put("channleId", jsonObject.getString("CHANNEL_ID"));
		    			logger.info(CommonUtil.getClassNameAndMethod(this) + ",rtJson:" + param.toJSONString());
		    			rtJson = service.invoke(param);
		    			if("000".equals(rtJson.getString("respCode"))) {
		    				String status = st.indexOf("DELIVRD")>=0?"5":"4";
		    				EasySQL usql = new EasySQL();
		    				usql.append("UPDATE " + context.getSchemaId() + ".C_SMS_SEND_RECORD set SEND_STATUS= ? ,SEND_RESULT_DESC=? where ID=?");
		    				
		    				//3.5#20240520-1 根据回执状态，写入短信发送状态
		    				Object[] params = new Object[] { status, st.indexOf("DELIVRD")>=0?"发送成功":st, jsonObject.getString("ID") };
		    				QueryFactory.getReadQuery().execute(usql.getSQL(),params );
		    			}
		    		} catch (Exception e) {
		    			e.printStackTrace();
		    			logger.error(e.getMessage(),e);
						rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		    			rtJson.put("respDesc", "处理失败");
		    		}
				}
        	} catch (Exception e) {
				e.printStackTrace();
				logger.error(e.getMessage(),e);
				rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
    			rtJson.put("respDesc", "处理失败");
			}
        }
        logger.info(rtJson);
        return rtJson;
    }
    
    
    public JSONObject sendSmsCodeList(JSONObject json){
		JSONObject smsChannel=json.getJSONObject("smsChannel");
		String account = smsChannel.getString("ACCOUNT");
		String paswd = smsChannel.getString("PASSWD");
		if(StringUtils.isBlank(account)||StringUtils.isBlank(paswd)){
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 发送短信失败，账号或者密码为空!");
			return null;
		}
		try {
			String phoneNum=json.getString("receiver");
			if(StringUtils.isBlank(phoneNum)) {
				return null;
			}
			try {
				JSONObject param = new JSONObject();
				String token = MD5Util.MD5(phoneNum+account);
				param.put("token", token.toLowerCase());
				param.put("templateid", json.getString("TemplateCode"));
				param.put("phone", phoneNum);
				logger.info(param.toJSONString());
				HttpResp resp = HttpUtil.sendPost(Constants.getDxybjSmsUrl()+"/api/news/sendingApi", param.toJSONString(), HttpUtil.TYPE_JSON, "UTF-8", "token", token);
				logger.info(CommonUtil.getClassNameAndMethod(this) + "发送短信接口是否成功code：:" + resp.getCode()+"  Result:"+resp.getResult());
				return JSONObject.parseObject(resp.getResult());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
		
	}
    
    /**
     * 获取模版编码
     * @param content
     * @return
     */
    public String getTempCode(String content,String schema) {
    	String tempCode = "";
    	try {
    		tempCode = QueryFactory.getReadQuery().queryForString("select MSG_CODE from "+schema+".c_sms_template where CONTENT=?", new Object[] {content});
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
    	return tempCode;
    }
}
