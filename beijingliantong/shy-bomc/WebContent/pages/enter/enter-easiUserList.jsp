<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>地市分配</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm">
       			<input type="hidden" name="experience" value="true"/>
             	<div class="ibox">
             		<div class="ibox-title clearfix">
             		      <div class="form-group">
             		         <h5><span class="glyphicon glyphicon-user"></span> 地市分配</h5>
             		         <div class="input-group input-group-sm">
									<span class="input-group-addon">姓名</span>	
									<input type="text" name="userName" class="form-control input-sm" style="width:200px" placeholder="姓名" maxlength="100">
							 </div>	
							 <div class="input-group input-group-sm" style="width:200px">
									<span class="input-group-addon">地市</span>	
									  <select class="form-control"   name="city"  data-mars="enter.cityList" style="display:inline-block;width: 100%;" >
	                         				 <option value=''>请选择</option>
	                                  </select>
	                         </div>		
							 <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="Enter.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
             			</div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="addrBook.easiUserList">
                             <thead>
	                         	 <tr>
								      <th class="text-l" style="min-width: 100px;">姓名</th>
								      <th class="text-l" style="min-width: 100px;">登录账号</th>
								      <th class="text-l" style="min-width: 100px;">性别</th>
								      <th class="text-l" style="min-width: 100px;">移动电话</th>
								      <th class="text-l" style="min-width: 100px;">邮箱</th>
								      <th class="text-l" style="min-width: 100px;">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr class="text-l">
											<td>{{:USERNAME}}</td>
											<td>{{:USER_ACCT}}</td>
											<td>{{:SEX}}</td>
											<td>{{:MOBILE}}</td>
											<td>{{:EMAIL}}</td>
											<td> 
												<a href="javascript:void(0)" onclick="Enter.assignCity('{{:USER_ID}}')">分配地市</a>
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script src="${ctxPath}/static/js/bootstrap-select.js"></script>
	<link rel="stylesheet" href="${ctxPath}/static/css/bootstrap-select.css">
	<script type="text/javascript">
	    var currEntId;
		jQuery.namespace("Enter");
		$(function(){
			var render=new Render({el:"#searchForm"});
		});
		Enter.searchData=function(){
			searchData();
		}
	
		$.views.converters("addTimeFun", function(val) {
			if(!isNaN(val)){
				var d = new Date(val);
				var year = d.getFullYear();
				var month = d.getMonth() + 1;
				month = month > 9 ? month.toString() : '0' + month;
				var date = d.getDate() > 9 ? d.getDate().toString() : '0'
						+ d.getDate();
				var hour = d.getHours() > 9 ? d.getHours().toString() : '0'
						+ d.getHours();
				var minute = d.getMinutes() > 9 ? d.getMinutes().toString() : '0'
						+ d.getMinutes();
				var second = d.getSeconds() > 9 ? d.getSeconds().toString() : '0'
						+ d.getSeconds();
				return year + "-" + month + "-" + date + "   " + hour + ":"
						+ minute + ":" + second;
			}else{
				if(val.length>19)
					return val.substring(0,19);
				return val;
			}
		});
		Enter.assignCity=function(userId){
		    popup.layerShow({type:1,title:'分配地市',offset:'20px',area:['500px','250px']},"${ctxPath}/pages/enter/enter-assignCity.jsp?userId="+userId,null);
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>