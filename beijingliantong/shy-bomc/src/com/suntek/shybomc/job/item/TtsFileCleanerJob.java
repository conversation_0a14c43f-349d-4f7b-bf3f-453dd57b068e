package com.suntek.shybomc.job.item;

import java.io.File;
import java.time.Duration;
import java.time.Instant;

import com.suntek.shybomc.util.RedisUtils;
import net.javacrumbs.shedlock.core.DefaultLockingTaskExecutor;
import net.javacrumbs.shedlock.core.LockConfiguration;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.core.LockingTaskExecutor;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

import com.suntek.shybomc.base.Constants;

public class TtsFileCleanerJob implements Runnable {

	private final static Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);

	@Override
	public void run() {
		try {
			LockProvider lockProvider = RedisUtils.getLockProvider();
			LockingTaskExecutor executor = new DefaultLockingTaskExecutor(lockProvider);
			LockConfiguration lockConfiguration = new LockConfiguration(Instant.now(), "ttsFileCleanerJob",
					Duration.ofMinutes(30), Duration.ofMinutes(1));
			executor.executeWithLock(new Runnable() {
				@Override
				public void run() {
					long start = System.currentTimeMillis();
					String voiceFileRootDir = Constants.IVRMENU_FILEPATH;
					File rootDir = new File(voiceFileRootDir);
					File[] files = rootDir.listFiles();
					if (files != null) {
						for (File file : files) {
							if (file.isDirectory()) {
								traverseFile(file);
							}
						}
					}

					File tmpRootDir = new File(Constants.WEB_TTSGW_DIR);
					files = tmpRootDir.listFiles();
					if (files != null) {
						for (File file : files) {
							if (file.getName().contains("_temp_tts")) {
								long lastModified = file.lastModified();
								long time = System.currentTimeMillis() - lastModified;
								if (time > 24 * 60 * 60 * 1000) {
									file.delete();
									logger.info("删除文件:" + file.getName());
								}
							}
						}
					}

					long end = System.currentTimeMillis();
					logger.info(String.format("耗时为:%d毫秒", end - start));
				}
			}, lockConfiguration);
		} catch (Throwable e) {
			logger.error(e.getMessage(), e);
		}
	}

	private void traverseFile(File dir) {
		File[] files = dir.listFiles();
		if (files == null) {
			return;
		}
		for (File file : files) {
			if (file.isFile() && file.getName().startsWith("conf_")) {
				long lastModified = file.lastModified();
				long time = System.currentTimeMillis() - lastModified;
				if (time > 24 * 60 * 60 * 1000 * 2) {
					file.delete();
					logger.info("删除文件:" + file.getName());
				}
			}
			if (file.isDirectory()) {
				traverseFile(file);
			}
		}
	}
}
