package com.suntek.shybomc.vo;

public class UserModel {
	private String userCode;
	private String userName;
	private String deptName;
	private String bingdingPhone;
	private String extNo;
	private String userId;
	private String enterId;
	private String userType;
	private String sipAccount;
	private String sipHardAccount;
	private int onlyInnerCall;

	/**
	 * @return the userCode
	 */
	public String getUserCode() {
		return userCode;
	}

	/**
	 * @param userCode the userCode to set
	 */
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	/**
	 * @return the userName
	 */
	public String getUserName() {
		return userName;
	}

	/**
	 * @param userName the userName to set
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * @return the deptName
	 */
	public String getDeptName() {
		return deptName;
	}

	/**
	 * @param deptName the deptName to set
	 */
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	/**
	 * @return the bingdingPhone
	 */
	public String getBingdingPhone() {
		return bingdingPhone;
	}

	/**
	 * @param bingdingPhone the bingdingPhone to set
	 */
	public void setBingdingPhone(String bingdingPhone) {
		this.bingdingPhone = bingdingPhone;
	}

	/**
	 * @return the extNo
	 */
	public String getExtNo() {
		return extNo;
	}

	/**
	 * @param extNo the extNo to set
	 */
	public void setExtNo(String extNo) {
		this.extNo = extNo;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the enterId
	 */
	public String getEnterId() {
		return enterId;
	}

	/**
	 * @param enterId the enterId to set
	 */
	public void setEnterId(String enterId) {
		this.enterId = enterId;
	}

	/**
	 * @return the userType
	 */
	public String getUserType() {
		return userType;
	}

	/**
	 * @param userType the userType to set
	 */
	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getSipAccount() {
		return sipAccount;
	}

	public void setSipAccount(String sipAccount) {
		this.sipAccount = sipAccount;
	}

	public String getSipHardAccount() {
		return sipHardAccount;
	}

	public void setSipHardAccount(String sipHardAccount) {
		this.sipHardAccount = sipHardAccount;
	}

	public int getOnlyInnerCall() {
		return onlyInnerCall;
	}

	public void setOnlyInnerCall(int onlyInnerCall) {
		this.onlyInnerCall = onlyInnerCall;
	}

}
