package com.suntek.shybomc.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.suntek.shybomc.base.AppBaseDao;
import com.suntek.shybomc.model.ProductType;
import com.suntek.shybomc.vo.CrmBillRowMapper;
import com.suntek.shybomc.vo.FuncRowMapper;
import com.suntek.shybomc.vo.TreeModel;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;

@WebObject(name = "enter")
public class EnterDao extends AppBaseDao {

    @WebControl(name = "tree", type = Types.TREE)
    public JSONObject tree() throws SQLException {
        Map<String, List<TreeModel>> treeMap = new HashMap<>();// 存放树里边的各个关系

        // key是父节点id
        // ，value是TreeModel的list对象
        // 得到功能菜单树
        String roleSql;
        Object[] params = new Object[]{this.param.getString("entId")};
        roleSql = "select t3.ROLE_ID from cc_enterprise t1 inner join cc_user t2 on t1.ENT_ID = t2.ENT_ID and t1.PBX_PHONE = t2.BINDING_PHONE inner join cc_user_role_group t3 on t2.USER_ID = t3.USER_ID where t2.USER_TYPE=0 and t1.ENT_ID=?";
        String roleId = this.getQuery().queryForString(roleSql, params);
        String sql = "select t1.*,(select FUNC_ID from cc_role_func_group t2 where t2.ROLE_ID=? and t1.FUNC_ID=t2.FUNC_ID) as CHECK_FUNCID from CC_FUNLIST t1 order by t1.PARENT_ID,t1.INDEX_NUM asc";
        List<TreeModel> list = this.getQuery().queryForList(sql, new Object[]{roleId}, new FuncRowMapper());
        for (int i = 0; i < list.size(); i++) {
            TreeModel model = list.get(i);
            String pid = model.getpId();
            if (treeMap.get(pid) == null) {
                List<TreeModel> childList = new ArrayList<>();
                childList.add(model);
                treeMap.put(pid, childList);
            } else {
                List<TreeModel> childList = treeMap.get(pid);
                childList.add(model);
            }
        }
        String pId = "0";// 第一层
        List<TreeModel> treeList = setTree(treeMap, pId);
        JSONArray jsonObject = JSONArray.parseArray(JSON.toJSONString(treeList));
        return getTree(jsonObject);
    }

    private List<TreeModel> setTree(Map<String, List<TreeModel>> treeMap, String pid) {
        if (treeMap.get(pid) == null)
            return new ArrayList<TreeModel>();
        List<TreeModel> childList = treeMap.get(pid);
        for (int i = 0; i < childList.size(); i++) {
            TreeModel model = (TreeModel) childList.get(i);
            model.setChildren(setTree(treeMap, model.getId()));
        }
        return childList;
    }

    @WebControl(name = "twoWeeks", type = Types.TEXT)
    public JSONObject twoWeeks() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -14);
        return this.getText(EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd"));
    }

    @WebControl(name = "today", type = Types.TEXT)
    public JSONObject today() {
        Calendar cal = Calendar.getInstance();
        return this.getText(EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd"));
    }

    @WebControl(name = "today_ym", type = Types.TEXT)
    public JSONObject today_ym() {
        Calendar cal = Calendar.getInstance();
        return this.getText(EasyDate.dateToString(cal.getTime(), "yyyy-MM"));
    }

    @WebControl(name = "feeList", type = Types.LIST)
    public JSONObject feeList() {
        String startDate = this.param.getString("startDate");
        if (StringUtils.isBlank(startDate)) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            startDate = EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd");
        }
        String startTime = startDate + " 00:00:00";
        String endDate = this.param.getString("endDate");
        if (StringUtils.isBlank(endDate)) {
            Calendar cal = Calendar.getInstance();
            endDate = EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd");
        }
        String endTime = endDate + " 23:59:59";
        EasySQL sql = this.getEasySQL(
                "select t1.*,t2.ENT_NAME from CC_BILL_LIST t1 left join CC_ENTERPRISE t2 on t1.ENT_ID =t2.ENT_ID where ");
        sql.append(startTime, " START_TIME >=? ");
        sql.append(endTime, " and START_TIME <= ? ");
        sql.appendLike(this.param.getString("entName"), " and t2.ENT_NAME like ? ");
        String caller = this.param.getString("caller");// 分机号
        if (StringUtils.isNotBlank(caller)) {
            sql.appendLike(caller.trim(), " and t1.CALLER like ? ");
        }
        String called = this.param.getString("called");
        if (StringUtils.isNotBlank(called)) {
            sql.appendLike(called.trim(), " and t1.CALLED like ? ");
        }
        String callFlag = this.param.getString("callFlag");// 呼叫标识
        if (StringUtils.isNotBlank(callFlag)) {
            sql.append(callFlag, " and t1.CALL_FLAG = ? ");
        }
        String callResult = this.param.getString("callResult");// 呼叫结果
        if (StringUtils.isNotBlank(callResult)) {
            sql.append(callResult, " and t1.CALL_RESULT = ? ");
        }
        sql.append(" order by START_TIME desc");
        this.info("[EnterDao.feeList()]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")", null);
        return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
    }


    @WebControl(name = "feeListBNumber", type = Types.LIST)
    public JSONObject feeListBNumber() {
        String startDate = this.param.getString("startDate");
        if (StringUtils.isBlank(startDate)) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -1);
            startDate = EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd");
        }
        String startTime = startDate + " 00:00:00";
        String endDate = this.param.getString("endDate");
        if (StringUtils.isBlank(endDate)) {
            Calendar cal = Calendar.getInstance();
            endDate = EasyDate.dateToString(cal.getTime(), "yyyy-MM-dd");
        }
        String endTime = endDate + " 23:59:59";
        EasySQL sql = this.getEasySQL(
                "select t1.*,t2.ENT_NAME from cc_b_statistics t1 left join CC_ENTERPRISE t2 on t1.ENT_ID =t2.ENT_ID where ");
        sql.append(startTime, " START_TIME >=? ");
        sql.append(endTime, " and START_TIME <= ? ");
        sql.appendLike(this.param.getString("entName"), " and t2.ENT_NAME like ? ");
        String caller = this.param.getString("caller");// 分机号
        if (StringUtils.isNotBlank(caller)) {
            sql.appendLike(caller.trim(), " and t1.CALLER like ? ");
        }
        String called = this.param.getString("called");
        if (StringUtils.isNotBlank(called)) {
            sql.appendLike(called.trim(), " and t1.CALLED like ? ");
        }
        sql.append(" order by START_TIME desc");
        this.info("[EnterDao.feeList()]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")", null);
        return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
    }

    /**
     * 得到语音菜单
     *
     * @return
     * @throws SQLException
     */
    @WebControl(name = "getIvrMenuObject", type = Types.RECORD)
    public JSONObject getIvrMenuObject() throws SQLException {
        String enterId = param.getString("enterId");
        String parentKey = param.getString("parentKey");
        String[] pkArray = new String[]{"ENT_ID", "PARENT_KEY"};
        EasyRecord record = new EasyRecord("PBX_IVR_MENU", pkArray).setPrimaryValues(enterId, parentKey);
        Map<String, String> obj2 = this.getQuery().findById(record); // 相当于obj2
        for (int i = 0; i < 10; i++) {
            String key = obj2.get("KEY" + i);
            if (!StringUtils.isBlank(key) && key.contains("@queue")) {
                String queueId = key.substring(key.lastIndexOf(":") + 1);
                String queueName = getQueueNameById(queueId);
                obj2.put("NAME" + i, "转分机队列(" + queueName + ")");
            }
        }
        JSONObject obj = new JSONObject();
        obj.put("data", obj2);
        return obj;
    }

    private String getQueueNameById(String queueId) throws SQLException {
        return this.getQuery().queryForString("select QUEUE_NAME from PBX_EXT_QUEUE where  QUEUE_ID = ?",
                new Object[]{queueId});
    }

    /**
     * 得到企业信息
     *
     * @return
     * @throws SQLException
     */

    @WebControl(name = "getEnterObject", type = Types.RECORD)
    public JSONObject getEnterObject() throws SQLException {
        String enterId = param.getString("pk");
        EasyRecord record = new EasyRecord("CC_ENTERPRISE", "ENT_ID").setPrimaryValues(enterId);

        JSONObject obj = getRecord(record);
        @SuppressWarnings("unchecked")
        HashMap<String, String> obj2 = (HashMap<String, String>) obj.get("data");
        String areaId = obj2.get("AREA_CODE").toString();

        EasyRow entInfo = this.getQuery().queryForRow(
                "select IN_CALL_SHOW_TYPE,OUT_CALL_SHOW_TYPE from pbx_ent_info where ENT_ID=?",
                new Object[]{enterId});
        if (entInfo != null) {
            obj2.put("IN_CALL_SHOW_TYPE", entInfo.getColumnValue("IN_CALL_SHOW_TYPE"));
            obj2.put("OUT_CALL_SHOW_TYPE", entInfo.getColumnValue("OUT_CALL_SHOW_TYPE"));
        }
        String sql = "select PARENT_AREA_ID from CC_AREA_INFO where AREA_ID=?";
        String[] params = {areaId};
        String PROVICE_ID = this.getQuery().queryForString(sql, params);

        obj2.put("PROVICE_ID", PROVICE_ID);
        obj2.put("AREA_CODE", areaId);
        String entLogo = obj2.get("ENT_LOGO");
        if (!StringUtils.isBlank(entLogo)) {
            obj2.put("ENT_LOGO", "/shy-bomc/enterlogo/" + entLogo);
        }

        String pkgCode = obj2.get("PKG_CODE").toString();
        if (pkgCode == null || "".equals(pkgCode)) {
            obj2.put("PKG_NAME", "");
            obj2.put("PKG_DESC", "");
        } else {
            sql = "select * from CC_BILL_PACKAGE where PKG_CODE=?";
            String[] params2 = {pkgCode};
            EasyRow row = this.getQuery().queryForRow(sql, params2);
            if (row != null) {
                obj2.put("PKG_NAME", row.getColumnValue("PKG_NAME"));
                StringBuffer desc = new StringBuffer();
                desc.append("套餐名称：").append(row.getColumnValue("PKG_NAME")).append("\n是否计费：")
                        .append("0".equals(row.getColumnValue("FEE_FLAG")) ? "否" : "是").append("\n计费方式：")
                        .append("1".equals(row.getColumnValue("PKG_NAME")) ? "按次计费" : "按月计费").append("\n功能费：")
                        .append((Integer.parseInt(row.getColumnValue("FEE")) / 1000) + "元").append("\n产品ID：")
                        .append(row.getColumnValue("PRODUCT_ID"));

                obj2.put("PKG_DESC", desc.toString());
            } else {
                obj2.put("PKG_NAME", "");
                obj2.put("PKG_DESC", "");
            }

        }
        obj.put("data", obj2);
        return obj;
    }

    @WebControl(name = "enterListSel", type = Types.DICT)
    public JSONObject enterListSel() {
        return this.getDictByQuery("select ENT_ID,concat('(',PBX_PHONE,')',ENT_NAME) from CC_ENTERPRISE",
                new Object[]{});
    }

    @WebControl(name = "enterList", type = Types.LIST)
    public JSONObject enterList() {
        EasySQL sql = this.getEasySQL(
                "select t1.ENT_NAME,t1.ENT_CODE,t1.PBX_PHONE,t1.ADD_TIME,t1.BILL_PHONE,t1.ENT_ID,t1.IS_SINGLE,t1.USER_COUNT,t1.FEE,t1.PBX_STATUS,"
                        + "t1.LINK_PHONE,t2.PASS_THROUGH_CALLER,t2.OPEN_APP,t2.LOCATION_PREFIX,"
                        + "(select t2.AREA_NAME from CC_AREA_INFO t2 where t1.AREA_CODE=t2.AREA_ID) as AREA_NAME from CC_ENTERPRISE t1 "
                        + "left join pbx_ent_info t2 on t1.ENT_ID=t2.ENT_ID where 1=1 ");
        sql.appendLike(this.param.getString("entName"), " and t1.ENT_NAME like ?");
        sql.appendLike(this.param.getString("pbxPhone"), " and t1.PBX_PHONE like ? ");
        if (this.param.getBoolean("experience") != null && this.param.getBoolean("experience")) {
            sql.append(" and BNET_ID is null");
        }
        this.info("[EnterDao.enterList()]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")",
                null);
        return this.queryForPageList(sql.getSQL() + " order by t1.ADD_TIME desc", sql.getParams(), null);
    }

    @WebControl(name = "proviceList", type = Types.DICT)
    public JSONObject proviceList() {
        return this.getDictByQuery(
                "select AREA_ID ,AREA_NAME   from CC_AREA_INFO where AREA_LEVEL=0 order by SHOW_NUM asc",
                new Object[]{});
    }

    @WebControl(name = "areaList", type = Types.DICT)
    public JSONObject areaList() {
        return this.getDictByQuery(
                "select AREA_ID ,AREA_NAME  from CC_AREA_INFO where AREA_LEVEL=1 and PARENT_AREA_ID=? order by SHOW_NUM asc",
                new Object[]{this.param.getString("entName")});
    }

    /**
     * 选择分机数
     *
     * @return
     * @throws SQLException
     */
    @WebControl(name = "phoneRootTree", type = Types.TREE)
    public JSONObject phoneRootTree() throws SQLException {

        String enterId = this.param.getString("enterId");

        String keyValue = this.param.getString("keyValue");

        // 查询企业下面是否以后部门或者用户
        String sql = "select count(1) from CC_FRAMEWORK where ENT_ID=? and PARENT_CODE='0' ";
        int deptNum = this.getQuery().queryForInt(sql, new Object[]{this.param.getString("enterId")});
        boolean entIsparent = false;
        if (deptNum > 0)
            entIsparent = true;
        else {
            sql = "select count(1) from CC_USER t1 where t1.ENT_ID=? and not exists (select 1 from CC_FRAMEWORK_USER t2 where t1.ENT_ID=t2.ENT_ID and t2.USER_ID=t1.USER_ID) ";
            int userNum = this.getQuery().queryForInt(sql, new Object[]{this.param.getString("enterId")});
            if (userNum > 0)
                entIsparent = true;
        }

        List<Map<String, Object>> treeList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("id", "0");
        map.put("name", "企业分机电话");
        map.put("pId", "0");
        map.put("enterId", enterId);
        map.put("keyValue", keyValue);
        map.put("isParent", entIsparent);
        map.put("nocheck", true);

        treeList.add(map);

        Map<String, Object> map3 = new HashMap<>();
        map3.put("id", keyValue);
        map3.put("name", "转按键" + keyValue + "下层子菜单(按键值" + keyValue + ")");
        map3.put("pId", "0");
        map3.put("enterId", enterId);
        map3.put("keyValue", keyValue);
        map3.put("nocheck", false);
        map3.put("isParent", false);
        treeList.add(map3);

        Map<String, Object> map2 = new HashMap<>();
        map2.put("id", "@asr");
        map2.put("name", "转智能语音找人(功能按键：@asr)");
        map2.put("pId", "0");
        map2.put("enterId", enterId);
        map2.put("keyValue", keyValue);
        map2.put("nocheck", false);
        map2.put("isParent", false);
        treeList.add(map2);

        String getQueueSql = "select QUEUE_ID,QUEUE_NAME from PBX_EXT_QUEUE where  ENT_ID = ?";
        List<EasyRow> dataList = this.getQuery().queryForList(getQueueSql, new Object[]{enterId});
        for (EasyRow easyRow : dataList) {
            String queueId = easyRow.getColumnValue("QUEUE_ID");
            String queueName = easyRow.getColumnValue("QUEUE_NAME");
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("id", "@queue:" + queueId);
            tempMap.put("name", "转分机队列(" + queueName + ")");
            tempMap.put("pId", "0");
            tempMap.put("enterId", enterId);
            tempMap.put("keyValue", keyValue);
            tempMap.put("nocheck", false);
            tempMap.put("isParent", false);
            treeList.add(tempMap);
        }
        JSONArray jsonObject = JSONArray.parseArray(JSON.toJSONString(treeList));
        return getTree(jsonObject);

    }

    /**
     * 语音导航树
     *
     * @return
     * @throws SQLException
     */
    @WebControl(name = "getIvrMenuRootTree", type = Types.TREE)
    public JSONObject getIvrMenuRootTree() throws SQLException {

        String enterId = this.param.getString("enterId");
        String enterName = this.param.getString("enterName");

        List<Map<String, Object>> treeList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("id", "0");
        map.put("name", enterName + "语音导航");
        map.put("pId", "");
        map.put("enterId", enterId);

        // 得到功能菜单树
        String sql = "select count(1) from PBX_IVR_MENU where ENT_ID=? and PARENT_KEY='0' and (KEY1='1' or KEY2='2' or KEY3='3' or KEY4='4'"
                + " or KEY5='5' or KEY6='5' or KEY7='5' or KEY8='5' or KEY9='5' or KEY0='0' )";
        int childCount = this.getQuery().queryForInt(sql, new Object[]{this.param.getString("enterId")});

        boolean isParent = false;
        if (childCount > 0)
            isParent = true;
        map.put("isParent", isParent);

        treeList.add(map);

        JSONArray jsonObject = JSONArray.parseArray(JSON.toJSONString(treeList));
        return getTree(jsonObject);

    }

    @WebControl(name = "blackPhoneList", type = Types.LIST)
    public JSONObject blackPhoneList() throws SQLException {
        String sql = "select  BLACK_TYPE,PHONE_NUMBER,ADD_TIME,USERNAME from CC_SYS_BLACK_PHONE csb";
        String phoneNumber = this.param.getString("PHONE_NUMBER");
        if (StringUtils.isBlank(phoneNumber)) {
            sql += " left join EASI_USER usr on csb.USER_ID = usr.USER_ID order by ADD_TIME desc";
            return this.queryForPageList(sql, null, null);
        } else {
            phoneNumber = "%" + phoneNumber + "%";
            sql += " left join EASI_USER usr on csb.USER_ID = usr.USER_ID where PHONE_NUMBER like ? order by ADD_TIME desc";
            JSONObject pageList = this.queryForPageList(sql, new Object[]{phoneNumber}, null);
            JSONArray datArray = pageList.getJSONArray("data");
            for (Object object : datArray) {
                JSONObject jObject = (JSONObject) object;
                jObject.put("ROLE_STYLE", getUserPrincipal().isRole("SYS_OPERATOR") ? false : true);
            }
            pageList.put("data", datArray);
            return pageList;
        }

    }

    @WebControl(name = "extPhoneList", type = Types.LIST)
    public JSONObject extPhoneList() throws SQLException {
        String bindPhone = this.param.getString("BINDING_PHONE");
        String entName = this.param.getString("entName");
        Integer useStatus = this.param.getInteger("USE_STATUS");
        String areaCode = this.param.getString("areaCode");
        String sql;
        List<Object> params = new ArrayList<>();
        sql = "select EXT_ID,BINDING_PHONE,SIP_PHONE,SIP_ACCOUNT,SIP_PWD,SIP_HARD_PHONE,SIP_HARD_ACCOUNT,SIP_HARD_PWD,EXT_STATUS,"
                + "pbx_ext_phone.IS_SINGLE,pbx_ext_phone.ADD_TIME,ENT_NAME,RECORD_FLAG,EXT_STOP,cc_enterprise.PBX_PHONE from pbx_ext_phone "
                + "left join cc_enterprise on pbx_ext_phone.ENT_ID = cc_enterprise.ENT_ID where 1=1";
        if (StringUtils.isNoneEmpty(bindPhone)) {
            sql += " and pbx_ext_phone.BINDING_PHONE like ?";
            params.add(bindPhone);
        }
        if (StringUtils.isNotBlank(entName)) {
            sql += " and ENT_NAME like ?";
            params.add("%" + entName + "%");
        }
        if (StringUtils.isNotBlank(areaCode)) {
            sql += " and cc_enterprise.AREA_CODE in (SELECT AREA_ID FROM cc_area_info WHERE PARENT_AREA_ID = ?)";
            params.add(areaCode);
        }
        if (useStatus != null) {
            sql += " and EXT_STATUS=?";
            params.add(useStatus);
        }
        sql += " order by ADD_TIME desc";
        //this.info("企业分机SQL -----> " + sql + ",PARAMS -----> " + params, null);
        JSONObject pageList = this.queryForPageList(sql, params.toArray(), null);
        JSONArray datArray = pageList.getJSONArray("data");
        for (Object object : datArray) {
            @SuppressWarnings("unchecked")
            Map<String, Object> jObject = (Map<String, Object>) object;
            jObject.put("ROLE_STYLE", getUserPrincipal().isRole("SYS_OPERATOR") ? false : true);
        }
        pageList.put("data", datArray);
        return pageList;
    }

    @WebControl(name = "extPhone", type = Types.DICT)
    public JSONObject extPhone() throws SQLException {
        String entId = this.param.getString("ENT_ID");
        String sql;
        sql = "select EXT_ID,BINDING_PHONE from pbx_ext_phone pei where ENT_ID = ? and not exists(select u.USER_ID from cc_user u where u.ENT_ID = pei.ENT_ID and u.BINDING_PHONE = pei.BINDING_PHONE)";
        return this.getDictByQuery(sql, new Object[]{entId});
    }

    @WebControl(name = "enterPbxPackageList", type = Types.LIST)
    public JSONObject entPbxPackageList() throws SQLException {
        EasySQL sql = this
                .getEasySQL("select t1.ENT_ID,t1.ENT_NAME,t1.PBX_PHONE,t1.ADD_TIME from CC_ENTERPRISE t1  where 1=1 ");
        sql.appendLike(this.param.getString("entName"), " and t1.ENT_NAME like ? ");
        sql.appendLike(this.param.getString("pbxPhone"), " and t1.PBX_PHONE like ? ");
        this.info("[EnterDao.enterList()]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")",
                null);
        JSONObject dataJson = this.queryForPageList(sql.getSQL() + " order by t1.ADD_TIME desc", sql.getParams(), null);
        JSONArray data = dataJson.getJSONArray("data");
        EasyQuery query = this.getQuery();
        for (int i = 0; i < data.size(); i++) {
            JSONObject enter = data.getJSONObject(i);
            String enterId = enter.getString("ENT_ID");
            EasyRow enterPackageRate = query.queryForRow(
                    "select PACKAGE_RATE from cc_package_customer where ENT_ID=? and PACKAGE_TYPE=" + ProductType.PBX,
                    new Object[]{enterId});
            String enterRate = "未订购";
            if (enterPackageRate != null) {
                enterRate = enterPackageRate.getColumnValue("PACKAGE_RATE");
            }
            enter.put("ENTER_RATE", enterRate);
            EasyRow queuePackageRate = query
                    .queryForRow("select PACKAGE_RATE from cc_package_customer where ENT_ID=? and PACKAGE_TYPE="
                            + ProductType.PBX_QUEUQ, new Object[]{enterId});
            String queueRate = "未订购";
            if (queuePackageRate != null) {
                queueRate = queuePackageRate.getColumnValue("PACKAGE_RATE");
            }
            enter.put("QUEUE_RATE", queueRate);
            EasyRow ivrPackageRate = query
                    .queryForRow("select PACKAGE_RATE from cc_package_customer where ENT_ID=? and PACKAGE_TYPE="
                            + ProductType.PBX_IVR, new Object[]{enterId});
            String ivrRate = "未订购";
            if (ivrPackageRate != null) {
                ivrRate = ivrPackageRate.getColumnValue("PACKAGE_RATE");
            }
            enter.put("IVR_RATE", ivrRate);
        }
        dataJson.put("data", data);
        return dataJson;
    }

    @WebControl(name = "enterExtPackageList", type = Types.LIST)
    public JSONObject entExtPackageList() throws SQLException {
        String enterId = this.param.getString("entId");
        String sql = "select BINDING_PHONE,MEMBER_ID,ADD_TIME from pbx_ext_phone where ENT_ID=? order by ADD_TIME desc";

        JSONObject dataJson = this.queryForPageList(sql, new Object[]{enterId});
        EasyQuery query = this.getQuery();
        JSONArray data = dataJson.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject extPhone = data.getJSONObject(i);
            String userId = extPhone.getString("MEMBER_ID");

            EasyRow extPackageRate = query.queryForRow(
                    "select PACKAGE_RATE from cc_package_customer where ENT_ID=? and USER_ID=? and PACKAGE_TYPE="
                            + ProductType.EXTENSION,
                    new Object[]{enterId, userId});
            String extRate = "未订购";
            if (extPackageRate != null) {
                extRate = extPackageRate.getColumnValue("PACKAGE_RATE");
            }
            extPhone.put("EXTENDSION_RATE", extRate);
            EasyRow queuePackageRate = query.queryForRow(
                    "select PACKAGE_RATE from cc_package_customer where ENT_ID=? and USER_ID=? and PACKAGE_TYPE="
                            + ProductType.VOICE,
                    new Object[]{enterId, userId});
            String queueRate = "未订购";
            if (queuePackageRate != null) {
                queueRate = queuePackageRate.getColumnValue("PACKAGE_RATE");
            }
            extPhone.put("VOICE_RATE", queueRate);
        }
        dataJson.put("data", data);
        return dataJson;
    }

    @WebControl(name = "enterPbxPackageRateList", type = Types.LIST)
    public JSONObject enterPbxPackageRateList() throws SQLException {
        EasySQL sql = this
                .getEasySQL("select t1.ENT_ID,t1.ENT_NAME,t1.PBX_PHONE,t1.ADD_TIME from CC_ENTERPRISE t1  where 1=1 ");
        sql.append(this.param.getString("entId"), " and t1.ENT_ID = ? ");
        sql.appendLike(this.param.getString("pbxPhone"), " and t1.PBX_PHONE like ? ");
        this.info("[EnterDao.enterList()]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")",
                null);
        JSONObject dataJson = this.queryForPageList(sql.getSQL() + " order by t1.ADD_TIME desc", sql.getParams(), null);
        JSONArray data = dataJson.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject enter = data.getJSONObject(i);
            String enterId = enter.getString("ENT_ID");
            String rateSql = "select sum(PACKAGE_RATE) from cc_package_customer where ENT_ID=?";
            int totalRate = this.getQuery().queryForInt(rateSql, new Object[]{enterId});
            enter.put("TOTAL_RATE", totalRate);
        }
        dataJson.put("data", data);
        return dataJson;
    }

    @WebControl(name = "sbcList", type = Types.LIST)
    public JSONObject sbcList() throws SQLException {
        String sbcName = this.param.getString("sbcName");
        String sql = "select SBC_ID,SBC_NAME,SIP_SERVER,SIP_PROXY,SIP_DOMAIN,MASTER_FLAG,AVAILABLE_STATUS from pbx_sbc_info";
        if (StringUtils.isBlank(sbcName)) {
            sql += " order by SBC_NAME";
            return this.queryForPageList(sql, new Object[]{});
        } else {
            sql += " where SBC_NAME like concat('%',?,'%') order by SBC_NAME";
            return this.queryForPageList(sql, new Object[]{sbcName});
        }
    }

    @WebControl(name = "voiceList", type = Types.LIST)
    public JSONObject voiceList() throws SQLException {
        String enterName = this.param.getString("enterName");
        String addTime = this.param.getString("addTime");
        String validateTime = this.param.getString("validateTime");
        String auditStatus = this.param.getString("auditStatus");
        String validateTime2 = this.param.getString("validateTime2");
        String auditStatus2 = this.param.getString("auditStatus2");
        EasySQL sql = this.getEasySQL("select t1.SERIAL_ID,t2.ENT_NAME,t1.TTS_CONTENT,t1.VOICE_TYPE,"
                + "t1.ADD_TIME,t1.VALIDATE_TIME,t1.AUDIT_STATUS,t1.REJECT_RESON,t1.AUDIT_USER_ID,"
                + "t1.VOICE_FILE,t1.VOICE_FILE_DURATION,t1.VALIDATE_TIME2,t1.AUDIT_STATUS2,t1.AUDIT_USER_ID2,t1.REJECT_RESON2 "
                + " from pbx_voice t1 left join cc_enterprise t2 on t1.ENT_ID=t2.ENT_ID where 1=1");
        sql.appendLike(enterName, " and t2.ENT_NAME like ?");
        sql.append(addTime, " and DATE_FORMAT(t1.ADD_TIME,'%Y-%m-%d')=?");
        sql.append(validateTime, " and DATE_FORMAT(t1.VALIDATE_TIME,'%Y-%m-%d')=? ");
        sql.append(auditStatus, " and AUDIT_STATUS=?");
        sql.append(validateTime2, " and DATE_FORMAT(t1.VALIDATE_TIME2,'%Y-%m-%d')=? ");
        sql.append(auditStatus2, " and AUDIT_STATUS2=?");
        sql.append(" order by t1.ADD_TIME desc,t1.VOICE_TYPE,t1.AUDIT_STATUS");
        JSONObject pageList = this.queryForPageList(sql.getSQL(), sql.getParams());
        JSONArray data = pageList.getJSONArray("data");

        String userSql = "select USERNAME from easi_user where USER_ID=?";
        for (Object object : data) {
            JSONObject obj = JSONObject.parseObject(JSON.toJSONString(object));
            String auditUserId = obj.getString("AUDIT_USER_ID");
            String auditUserId2 = obj.getString("AUDIT_USER_ID2");
            if (!StringUtils.isBlank(auditUserId)) {
                JSONObject user1 = this.queryForRecord(userSql, new Object[]{auditUserId}, null);
                obj.put("USERNAME", user1.getJSONObject("data").getString("USERNAME"));
            }
            if (!StringUtils.isBlank(auditUserId2)) {
                JSONObject user2 = this.queryForRecord(userSql, new Object[]{auditUserId2}, null);
                obj.put("USERNAME2", user2.getJSONObject("data").getString("USERNAME"));
            }
            obj.put("ROLE_STYLE", getUserPrincipal().isRole("SYS_OPERATOR") ? false : true);
        }
        pageList.put("data", data);
        return pageList;
    }

    @WebControl(name = "voiceList2", type = Types.LIST)
    public JSONObject voiceList2() throws SQLException {
        String enterName = this.param.getString("enterName");
        String addTime = this.param.getString("addTime");
        String validateTime = this.param.getString("validateTime");
        String auditStatus = this.param.getString("auditStatus");
        EasySQL sql = this.getEasySQL("select t1.SERIAL_ID,t2.ENT_NAME,t1.TTS_CONTENT,t1.VOICE_TYPE,"
                + "t1.ADD_TIME,t3.USERNAME,t1.VOICE_FILE,t1.VOICE_FILE_DURATION,t1.VALIDATE_TIME2,t1.AUDIT_STATUS2,t1.REJECT_RESON2 "
                + " from pbx_voice t1 left join cc_enterprise t2 on t1.ENT_ID=t2.ENT_ID left join easi_user t3 on t1.AUDIT_USER_ID2=t3.USER_ID where t1.AUDIT_STATUS=1");
        sql.appendLike(enterName, " and t2.ENT_NAME like ?");
        sql.append(addTime, " and DATE_FORMAT(t1.ADD_TIME,'%Y-%m-%d')=?");
        sql.append(validateTime, " and DATE_FORMAT(t1.VALIDATE_TIME2,'%Y-%m-%d')=? ");
        sql.append(auditStatus, " and AUDIT_STATUS2=?");
        sql.append(" order by t1.ADD_TIME desc,t1.VOICE_TYPE,t1.AUDIT_STATUS2");
        return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "uccnapplyList", type = Types.LIST)
    public JSONObject uccnapplyList() throws SQLException {
        String enterName = this.param.getString("entName");
        EasySQL sql = this.getEasySQL("select APPLY_ID,APPLY_COMPANY,APPLY_NAME,APPLY_TIME,APPLY_PHONE,APPLY_STATUS,"
                + "APPLY_NOTE,USERNAME,APPLY_OPERATE_TIME from cc_uccnapply t1 left join easi_user t2 on t1.APPLY_OPERATOR = t2.USER_ID where 1=1");
        sql.appendLike(enterName, " and APPLY_COMPANY like ?");
        sql.append(" order by APPLY_TIME desc");
        return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "crmOrderList", type = Types.LIST)
    public JSONObject crmOrderList() throws SQLException {
        String accnbr = this.param.getString("ACCNBR");
        String orderStatus = this.param.getString("ORDER_STATUS");
        String startDate = this.param.getString("startDate");
        String operType = this.param.getString("OPER_TYPE");
        String entName = this.param.getString("ENT_NAME");
        String areaCode = this.param.getString("areaCode");
        if (!StringUtils.isBlank(startDate)) {
            startDate = startDate + " 00:00:00";
        }

        String endDate = this.param.getString("endDate");
        if (!StringUtils.isBlank(endDate)) {
            endDate = endDate + " 23:59:59";
        }
        StringBuffer sb = new StringBuffer(
                "select CRM_NO,CRM_TYPE,PBX_PHONE,PHONE,PHONE_TYPE,PHONE_FEE,CRM_RECORD,RECORD_FEE,LINKMAN,LINKPHONE,"
                        + "ADDR,ACCEPT_TIME,ORDER_STATUS,ADD_TIME,ACTION_DESC,ENT_NAME "
                        + "from cc_order_log where 1=1");
        List<Object> params = new ArrayList<Object>();
        if ("finish".equals(orderStatus)) {
            sb.append(" and ORDER_STATUS = 1 ");
        }
        if ("fail".equals(orderStatus)) {
            sb.append(" and ORDER_STATUS = -1 ");
        }

        if ("newProd".equals(operType)) {
            sb.append(" and CRM_TYPE = 'add' ");
        }

        if ("cancelProd".equals(operType)) {
            sb.append(" and CRM_TYPE = 'delete' ");
        }

        if ("modifyProdAttribute".equals(operType)) {
            sb.append(" and CRM_TYPE = 'modify' ");
        }

        if (StringUtils.isNotBlank(accnbr)) {
            sb.append(" and PHONE like concat('%',?,'%') ");
            params.add(accnbr);
        }
        if (!StringUtils.isBlank(startDate) && !StringUtils.isBlank(endDate)) {
            sb.append(" and ADD_TIME between ? and ?");
            params.add(startDate);
            params.add(endDate);
        }
        if (StringUtils.isNotBlank(entName)) {
            sb.append(" and ENT_NAME like concat('%',?,'%') ");
            params.add(entName);
        }
        if (StringUtils.isNotBlank(areaCode)) {
            sb.append(" and AREA_CODE = ? ");
            params.add(areaCode + "1");
        }
        sb.append(" order by ADD_TIME desc ");
        return this.queryForPageList(sb.toString(), params.toArray());
    }

    @WebControl(name = "crmOrderDetail", type = Types.RECORD)
    public JSONObject crmOderDetail() throws SQLException {
        String orderId = this.param.getString("orderId");
        String sql = "select CRM_NO,CRM_TYPE,PBX_PHONE,PHONE,PHONE_TYPE,PHONE_FEE,CRM_RECORD,RECORD_FEE,LINKMAN,LINKPHONE,ADDR,ACCEPT_TIME,ORDER_STATUS,ADD_TIME from cc_order_log where CRM_NO=?";
        JSONObject record = this.queryForRecord(sql, new Object[]{orderId}, null);
        JSONObject data = record.getJSONObject("data");
        String crmType = data.getString("CRM_TYPE");
        if ("add".equals(crmType)) {
            data.put("CRM_TYPE", "添加");
        } else if ("modify".equals(crmType)) {
            data.put("CRM_TYPE", "修改");
        } else if ("delete".equals(crmType)) {
            data.put("CRM_TYPE", "删除");
        }
        String phoneType = data.getString("PHONE_TYPE");
        if ("pbx".equals(phoneType)) {
            data.put("PHONE_TYPE", "总机");
        } else if ("ext".equals(phoneType)) {
            data.put("PHONE_TYPE", "分机");
        }
        String addTime = data.getString("ADD_TIME");
        if (StringUtils.isNotBlank(addTime)) {
            data.put("ADD_TIME", addTime.subSequence(0, 19));
        }
        String orderStatus = data.getString("ORDER_STATUS");
        if ("1".equals(orderStatus)) {
            data.put("ORDER_STATUS", "成功");
        } else if ("-1".equals(orderStatus)) {
            data.put("ORDER_STATUS", "失败");
        }
        record.put("data", data);
        return record;
    }

    @WebControl(name = "crmBill", type = Types.LIST)
    public JSONObject crmBill() throws SQLException {
        String entName = this.param.getString("entName");
        String billDate = this.param.getString("billDate");
        if (!StringUtils.isBlank(billDate)) {
            billDate += "-01";
        }
        EasySQL sql = this.getEasySQL("select * from cc_crm_bill_stat where 1=1 ");
        sql.appendLike(entName, " and ENT_NAME like ?");
        sql.append(billDate, " and BILL_DATE = ?");
        sql.append("order by BILL_DATE desc");
        return this.queryForPageList(sql.getSQL(), sql.getParams(), new CrmBillRowMapper());
    }

    /**
     * 增加企业计费查询
     *
     * @return
     * @throws SQLException
     */
    @WebControl(name = "entBill", type = Types.LIST)
    public JSONObject entBill() {
        // 查询企业信息表,构造企业信息
        JSONObject jsonObject = new JSONObject();
        try {
            long start = System.currentTimeMillis();
            int page = StringUtils.isEmpty(this.param.getString("pageIndex")) ? 1
                    : Integer.parseInt(this.param.getString("pageIndex")) == -1 ? 1
                    : Integer.parseInt(this.param.getString("pageIndex"));
            int pageSize = StringUtils.isEmpty(this.param.getString("pageSize")) ? 6
                    : Integer.parseInt(this.param.getString("pageSize"));
            String id = this.param.getString("entid"); // 企业id
            String billDate = this.param.getString("startDate");
            StringBuffer strBuffer = new StringBuffer();
            strBuffer.append(
                    "select ENT_ID as ENT_ID,PBX_PHONE as PBX_PHONE,ZJ_FREE as ZJ_COST,FJ_FREE as FJ_COST,RC_FREE as RC_COST,"
                            + "FUN_BILL as FUN_FREE,FUN_RATE as FUN_RATE,CALL_FREE as CALL_COST_FREE,CALL_BILL as TEL_FREE,TEL_RATE as TEL_RATE, "
                            + "TOTAL_FREE as TOTAL_FREE from cc_enterprise_hb_bill where 1=1 ");
            if (!StringUtils.isEmpty(id)) {
                strBuffer.append("and ENT_ID='" + id + "'");
            }
            if (!StringUtils.isEmpty(billDate)) {
                strBuffer.append("and MONTH_DAY='" + billDate + "'");
            }
            this.info("[EnterDao.entBill]查询sql:" + strBuffer.toString(), null);
            JSONObject obj = this.queryForList(strBuffer.toString(), new Object[]{});
            List<Map<String, Object>> list = (List<Map<String, Object>>) obj.get("data");
            int total = list.size();
            int totalPageNum = (total + pageSize - 1) / pageSize;
            List<Map<String, Object>> resultList = list.subList(pageSize * (page - 1),
                    ((pageSize * page) > total ? total : (pageSize * page)));

            jsonObject.put("totalRow", total);
            jsonObject.put("total", total);
            jsonObject.put("pageNumber", page == 1 ? -1 : page);
            jsonObject.put("data", resultList);
            jsonObject.put("totalPage", totalPageNum);
            jsonObject.put("type", "LIST");
            jsonObject.put("pageSize", pageSize);

            /* resultJsonObject.put("enter.entBill", jsonObject); */
            this.info("[EnterDao.entBill--返回查询集合--]" + jsonObject, null);
            long end = System.currentTimeMillis();
            long duration = end - start;
            this.info("[EnterDao.entBill--查询计费--]总耗时为:" + duration + "毫秒", null);
        } catch (Exception e) {
            this.error("[EnterDao.entBill--异常信息--]", e);
        }
        return jsonObject;
    }

    @WebControl(name = "enterCallConfList", type = Types.LIST)
    public JSONObject enterCallConfList() {
        String id = this.param.getString("entid");
        EasySQL sql = this.getEasySQL("select ENT_ID,ENT_NAME,CALL_HOURS,CALL_DAY,CALL_MONTH,"
                + "EXT_CALL_HOURS,EXT_CALL_DAY,EXT_CALL_MONTH from PBX_CALL_CONFIG t1 where 1=1");
        if (!StringUtils.isEmpty(id)) {
            sql.append(" and ENT_ID='" + id + "'");
        }
        this.info("[EnterDao.enterCallConfList]" + sql.getSQL() + ",参数(" + StringUtils.join(sql.getParams(), ",") + ")",
                null);
        return this.queryForPageList(sql.getSQL() + " order by t1.CREATE_TIME desc", sql.getParams(), null);
    }

    @WebControl(name = "cityList", type = Types.DICT)
    public JSONObject cityList() {
        String sql = "select AREA_ID,AREA_NAME from CC_AREA_INFO where AREA_LEVEL=1";
        sql = sql + " order by SHOW_NUM asc";
        return this.getDictByQuery(sql, new Object[]{});
    }

    @WebControl(name = "phoneList", type = Types.LIST)
    public JSONObject phoneList() throws SQLException {
        String bindPhone = this.param.getString("PHONE");
        String sql;
        Object[] params = null;
        if (StringUtils.isBlank(bindPhone)) {
            sql = "select * from pbx_phone_pool  order by ADD_TIME desc";
        } else {
            sql = "select * from pbx_phone_pool" + " where PHONE like ? order by ADD_TIME desc";
            params = new Object[]{"%" + bindPhone + "%"};
        }
        return this.queryForPageList(sql, params, null);
    }

    @WebControl(name = "fenJiList", type = Types.LIST)
    public JSONObject fenJiList() throws SQLException {
        String accountId = this.param.getString("accountId");
        String phone = this.param.getString("PHONE");
        String sql;
        Object[] params = null;
        if (StringUtils.isBlank(phone)) {
            sql = "select * from pbx_ext_phone_pool  where ACCOUNT_ID=? order by ADD_TIME desc";
            params = new Object[]{accountId};
        } else {
            sql = "select * from pbx_ext_phone_pool" + " where ACCOUNT_ID=? and PHONE like ? order by ADD_TIME desc";
            params = new Object[]{accountId, "%" + phone + "%"};
        }
        return this.queryForPageList(sql, params, null);
    }

    @WebControl(name = "experimentEntList", type = Types.LIST)
    public JSONObject experimentEntList() {
        EasySQL sql = this.getEasySQL("select t1.* from PBX_EXPERIMENT_ENT t1 where 1=1  ");
        String status = this.param.getString("status");
        if (status != null && !"".equals(status)) {
            sql.append(status.trim(), " and t1.ENT_STATUS = ? order by ADD_TIME desc");
        }
        return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
    }

    @WebControl(name = "applicationList", type = Types.LIST)
    public JSONObject applicationList() {
        String sql = "select * from pbx_application";
        return this.queryForPageList(sql, new Object[]{});
    }
}
