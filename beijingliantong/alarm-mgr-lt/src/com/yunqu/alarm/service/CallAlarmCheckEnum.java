package com.yunqu.alarm.service;

import com.yunqu.alarm.service.handler.*;

/**
 * <p>
 * 外呼相关业务监控数据字典
 * </p>
 *
 * <AUTHOR>
 * @version v1.0
 * @ClassName CallAlarmCheckEnum
 * @Description 外呼相关业务监控数据字典
 * @Copyright Copyright (c) 2022
 * @Company 广州云趣信息科技有限公司
 * @since create in 2022/4/21 15:37
 */
public enum CallAlarmCheckEnum {
	/**
	 * SDR:话单数据量监控
	 */
	SDR_CHECK(new SdrAlarmCheckHandler()),
	/**
	 * CDR:cdr话单数据量监控
	 */
	CDR_CHECK(new CdrAlarmCheckHandler()),
	/**
	 * BILLTIME:外呼时长监控
	 */
	BILLTIME_CHECK(new BillTimeAlarmCheckHandler()),

	/**
	 * SBCCOUNT_CHECK:中继资源数告警监控
	 */
	SBCCOUNT_CHECK(new SbcCountAlarmCheckHandler());

	private IAlarmCheckHandler alarmCheckHandler;

	CallAlarmCheckEnum(IAlarmCheckHandler alarmCheckHandler) {
		this.alarmCheckHandler = alarmCheckHandler;
	}

	public IAlarmCheckHandler getAlarmCheckHandler() {
		return alarmCheckHandler;
	}

	public void setAlarmCheckHandler(IAlarmCheckHandler alarmCheckHandler) {
		this.alarmCheckHandler = alarmCheckHandler;
	}
}
