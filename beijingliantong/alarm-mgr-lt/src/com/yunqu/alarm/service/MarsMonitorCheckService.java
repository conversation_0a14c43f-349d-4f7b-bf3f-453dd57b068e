package com.yunqu.alarm.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.alarm.base.Constants;
import com.yunqu.alarm.listener.GlobalContextListener;
import com.yunqu.alarm.log.AlarmLogger;
import com.yunqu.alarm.support.MarsMonitorCheckContainer;
import com.yunqu.alarm.util.MarsMonitorThreadPoolManager;
import com.yunqu.alarm.util.RedisUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.CacheTime;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;

/**
 * <p>
 * 磐石资源使用监控检测服务类
 * </p>
 *
 * @ClassName MarsMonitorCheckService
 * <AUTHOR> Copy This Tag)
 * @Description 用于检测每个minuteId周期保持缓存中的数据都是该阶段的峰值，保证echart图展现的都为峰值数据
 * @Since create in 2022/9/9 17:35
 * @Version v1.0
 * @Copyright Copyright (c) 2022
 * @Company 广州云趣信息科技有限公司
 */
public class MarsMonitorCheckService implements Runnable {

	/**
	 * 内存初始话空对象
	 *
	 * @date 2022/9/13 9:37
	 */
	private static final JSONObject EMPTYJSON = new JSONObject();

	/**
	 * 企业资源使用情况redis缓存前缀
	 *
	 * @date 2022/9/13 9:38
	 */
	private static final String MONITOR_KEY_PREFIX = "MARS_RES_MONITOR_";

	/**
	 * 线程启动入口
	 *
	 * <AUTHOR>
	 * @date 2022/9/13 9:39
	 */
	@Override
	public void run() {
		while (GlobalContextListener.runState) {
			checkMonitorData();
		}
	}


	/**
	 * 校验监控数据
	 *
	 * <AUTHOR>
	 * @date 2022/9/9 17:46
	 */
	public void checkMonitorData() {
		if (!Constants.isDaemon()) {
			//如果不是配置了服务，则不做处理。
			try {
				Thread.sleep(60 * 1000);
			} catch (Exception ignored) {
			}
		} else {
			AlarmLogger.getLogger("ent-monitor").info("check cache data begin");
			excueteCheck();
			AlarmLogger.getLogger("ent-monitor").info("check cache data over");
		}
	}

	/**
	 * 执行监控数据校验
	 *
	 * <AUTHOR>
	 * @date 2022/9/9 17:46
	 */
	public void excueteCheck() {
		try {
			List<String> allEnts = getAllEnts();
			int dateId = getDateId();
			int timeId = getTimeId();
			int minuteId = getMinuteId();
			String timeKey = dateId + "" + timeId + "" + minuteId;
			String monitorDataKey = "MARS_MONITOR_RES";
			String petraResKey = "PETRA_RES";

			//1：检测是否进入下一周期，如果进入下一周期则清空内存数据
			if (!MarsMonitorCheckContainer.MONITOR_DATA_CONTAINER.containsKey(timeKey)) {
				Map<String, JSONObject> tmpMap = new HashMap<>(MarsMonitorCheckContainer.MONITOR_DATA_CONTAINER);
				Map<String, JSONObject> dataMap = new HashMap<>(MarsMonitorCheckContainer.DATA_CONTAINER);
				MarsMonitorCheckContainer.MONITOR_DATA_CONTAINER.clear();
				MarsMonitorCheckContainer.DATA_CONTAINER.clear();
				MarsMonitorCheckContainer.MONITOR_DATA_CONTAINER.put(timeKey, EMPTYJSON);
				MarsMonitorThreadPoolManager.getInstance().execute(new com.yunqu.alarm.service.v2.AlarmMonitorService(tmpMap, dataMap));
			}

			Set<Callable<String>> taskSet = new HashSet<>();
			if (Constants.isMonitorByEntInfo()) {
				allEnts.stream().filter(StringUtils::isNotBlank).forEach(entId -> {
					//2：检测缓存中的监控数据和内存中的监控数据，已达到缓存中的数据为每个minuteId的峰值
					taskSet.add(() -> {
						String monitorKey = MONITOR_KEY_PREFIX + entId;
						Map<String, String> cacheData = RedisUtil.hgetAll(monitorKey);
						if (!MarsMonitorCheckContainer.DATA_CONTAINER.containsKey(entId)) {
							//3：该周期第一次轮询则将缓存中的数据直接放入内存中
							MarsMonitorCheckContainer.DATA_CONTAINER.put(entId, JSONObject.parseObject(JSONObject.toJSONString(cacheData)));
						} else {
							JSONObject localData = MarsMonitorCheckContainer.DATA_CONTAINER.get(entId);
							AlarmLogger.getLogger("ent-monitor").info("<" + entId + ">monitor cache data is >>>" + JSONObject.toJSONString(cacheData));
							AlarmLogger.getLogger("ent-monitor").info("<" + entId + ">monitor local data is >>>" + JSONObject.toJSONString(localData));
							String timestamp = cacheData.remove("timestamp");
							localData.put("timestamp", timestamp);
							//4：轮询缓存中的数据，比较内存中的数据，将更大的数据惊醒数据存储（内存+缓存）
							cacheData.forEach((key, val) -> {
								int cacheVal = Integer.parseInt(val);
								int localVal = localData.getIntValue(key);
								if (cacheVal > localVal) {
									localData.put(key, cacheVal);
								}
							});
							AlarmLogger.getLogger("ent-monitor").info("<" + entId + ">monitor to cache data is >>>" + JSONObject.toJSONString(localData));
							MarsMonitorCheckContainer.DATA_CONTAINER.put(entId, localData);
						}
						return null;
					});
				});
			}

			taskSet.add(() -> {
				Map<String, String> petraRes = RedisUtil.hgetAll("#PETRA_RES");
				for (String key : petraRes.keySet()) {
					String val = petraRes.get(key);
					JSONObject valJson = JSONObject.parseObject(val);
					if (!MarsMonitorCheckContainer.DATA_CONTAINER.containsKey(key)) {
						//3：该周期第一次轮询则将缓存中的数据直接放入内存中
						MarsMonitorCheckContainer.DATA_CONTAINER.put(key, valJson);
					} else {
						JSONArray result = new JSONArray();
						JSONArray res = valJson.getJSONArray("res");
						JSONArray localRes = MarsMonitorCheckContainer.DATA_CONTAINER.get(key).getJSONArray("res");
						for (int i = 0; i < res.size(); i++) {
							JSONObject resObj = res.getJSONObject(i);
							String type = resObj.getString("type");
							int used = resObj.getIntValue("used");
							Object obj = localRes.stream().filter(o -> {
								JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(o));
								return StringUtils.equals(type, jsonObject.getString("type"));
							}).findFirst().get();
							JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
							if(used>jsonObject.getIntValue("used")) {
								result.add(resObj);
							} else {
								result.add(jsonObject);
							}
						}
						valJson.put("res", result);
						MarsMonitorCheckContainer.DATA_CONTAINER.put(key, valJson);
					}
				}
				return null;
			});

			//5：使用线程池执行检测任务
			MarsMonitorThreadPoolManager.getInstance().invokeAll(taskSet);
		} catch (Exception e) {
			AlarmLogger.getLogger("ent-monitor").error(e.getMessage(), e);
		} finally {
			try {
				Thread.sleep(2 * 1000);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}


	/**
	 * 获取所有的企业ID
	 *
	 * @return java.util.List<java.lang.String>
	 * <AUTHOR>
	 * @date 2022/9/9 17:45
	 */
	private List<String> getAllEnts() throws SQLException {
		String allEntCacheKey = "ENT_CACHE:ALL";
		EasyCache cache = CacheManager.getMemcache();
		List<String> allEntIds = cache.get(allEntCacheKey);
		if (CollectionUtils.isEmpty(allEntIds)) {
			String sql = "select ENT_ID from CC_ENT";
			allEntIds = getQuery().queryForList(sql, new Object[]{}, new EasyRowMapper<String>() {

				@Override
				public String mapRow(ResultSet rs, int i) {
					try {
						return rs.getString("ENT_ID");
					} catch (SQLException e) {
						AlarmLogger.getLogger().error(e.getMessage(), e);
						return "";
					}
				}
			});
			cache.put(allEntCacheKey, allEntIds, CacheTime.THREE_MINUTES * 3);
		}
		return allEntIds;
	}

	private EasyQuery getQuery() {
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.READ_DS_NAME);
	}

	/**
	 * 获取日期ID
	 *
	 * @return int
	 * <AUTHOR>
	 * @date 2022/9/9 17:49
	 */
	private static int getDateId() {
		EasyCalendar cal = EasyCalendar.newInstance();
		return cal.getDateInt();
	}

	/**
	 * 获取分钟ID
	 *
	 * @return int
	 * <AUTHOR>
	 * @date 2022/9/9 17:49
	 */
	private static int getMinuteId() {
		EasyCalendar cal = EasyCalendar.newInstance();
		int minute = cal.getCalendar().get(Calendar.MINUTE);
		return (minute / 5) + 1;
	}

	/**
	 * 获取
	 *
	 * @return int
	 * <AUTHOR>
	 * @date 2022/9/9 17:49
	 */
	private static int getTimeId() {
		EasyCalendar cal = EasyCalendar.newInstance();
		int hour = cal.getCalendar().get(Calendar.HOUR_OF_DAY);
		int minute = cal.getCalendar().get(Calendar.MINUTE);
		return hour * 2 + (minute / 30 + 1);
	}

	public static void main(String[] args) {
		System.out.println(getTimeId());
	}

}
