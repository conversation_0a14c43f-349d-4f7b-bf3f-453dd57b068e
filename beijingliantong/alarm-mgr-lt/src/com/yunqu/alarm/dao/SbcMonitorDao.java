package com.yunqu.alarm.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.alarm.base.AppDaoContext;
import com.yunqu.alarm.base.Constants;
import com.yunqu.alarm.base.QueryFactory;
import com.yunqu.alarm.log.AlarmLogger;
import com.yunqu.alarm.log.SbcMonitorLogger;
import com.yunqu.alarm.util.RedisUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

/**
 * @ClassName: SbcMonitorDao
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/10/19
 * @Version 1.0
 */
@WebObject(name = "SbcMonitorDao")
public class SbcMonitorDao extends AppDaoContext {

    /**
	 * 企业中继资源监控
	 * @return
	 */
	@WebControl(name="sbcMonitorList",type= Types.LIST)
	public JSONObject sbcMonitorList() {

		try {
			//1:获取系统默认告警阈值
			int threshold = getThreshold("MARS-00061");
			if (threshold <= 0) {
				threshold = 10;
			}

			EasySQL sql = new EasySQL("select t1.CALL_LICENSE,t3.*,t4.CONFIG_JSON, (select 1 from CC_ENT_THIRD_SBC_ACCOUNT t2 where t1.ENT_ID = t2.ENT_ID) as THRID_FLAG ");
			sql.append(" from CC_ENT_RES t1 ");
			sql.append(" left join CC_ENT t3 on t1.ENT_ID = t3.ENT_ID ");
			sql.append(" left join CC_ENT_SBC_MONITOR_CONFIG t4 on t1.ENT_ID = t4.ENT_ID ");
			sql.append(" where t3.ENT_STATE=0");
			sql.appendLike(param.getString("entName"), " and t3.ENT_NAME like ?");
			sql.append(" order by t1.ENT_ID");
			SbcMonitorLogger.getLogger().info("企业中继资源监控--->sql:"+sql.getSQL()+"，param:"+JSONObject.toJSONString(sql.getParams()));

			JSONObject queryForPageObj = queryForPageList2(sql.getSQL(), sql.getParams());
			JSONArray ents = queryForPageObj.getJSONArray("data");
			for (int i = 0; i < ents.size(); i++) {
				int warnSbcCount = threshold;
				JSONObject entInfo = ents.getJSONObject(i);
				String entId = entInfo.getString("ENT_ID");
				String entName = entInfo.getString("ENT_NAME");
				String thridFlag = entInfo.getString("THRID_FLAG");
				String configJson = entInfo.getString("CONFIG_JSON");

				//检查是否配置了企业中继资源监控告警阀值
				if (StringUtils.isNotBlank(configJson)) {
					JSONObject confObj = JSONObject.parseObject(configJson);
					if (confObj != null) {
						warnSbcCount = confObj.getIntValue("warnSbcCount");
					}
				}
				entInfo.put("warnSbcCount", warnSbcCount);

				//加载普通企业中继资源占用数
				if (!StringUtils.equals("1", thridFlag)) {
					String entResKey = Constants.RES_KEY_PREFIX + entId;
					String monitorKey = Constants.MONITOR_KEY_PREFIX + entId;
					for (String control : Constants.CONTROL_ARR) {
						entInfo.put(control + "_total", Constants.getResVal(entResKey, control));
						entInfo.put(control + "_used", Constants.getResVal(monitorKey, control));
					}
					entInfo.put("updateTime", RedisUtil.hget(monitorKey, "timestamp"));
					continue;
				}

				//加载第三方下挂企业中继资源占用数
				String ipGroupName = Constants.THIRD_IP_GROUP_NAME_PREFIX + entId;
				String confKey = Constants.SET_THIRD_IP_PREFIX + ipGroupName;
				entInfo.put("ipt_total", RedisUtil.hget(ipGroupName, "maxCall"));//支持最大并发数
				entInfo.put("ipt_used", RedisUtil.hget(ipGroupName, "currentCall"));//目前并发数
				entInfo.put("updateTime", RedisUtil.hget(ipGroupName, "timeStamp"));//最后更新的时间戳
				entInfo.put("iplist", RedisUtil.hget(confKey, "iplist"));//已认证的IP地址
			}
			queryForPageObj.put("updateTime", EasyCalendar.newInstance().getDateTime("-"));
			return queryForPageObj;
		}catch (Exception e){
			SbcMonitorLogger.getLogger().error(e.getMessage(),e);
		}
		return getJsonResult(null);
	}
	/**
	 * 从数据库获取下相关告警阈值
	 *
	 * @param eventCode 告警类型
	 * @return int
	 * <AUTHOR>
	 * @date 2022/4/20 19:29
	 */
	private int getThreshold(String eventCode) {
		int threshold = 0;
		try {
			String sql = "select THRESHOLD from CC_ALARM_BUSI_NOTIFY_CONF where event_code=? and use_flag=1";
			String str = QueryFactory.getQuery().queryForString(sql, eventCode);
			if (StringUtils.isNotBlank(str)) {
				threshold = Integer.parseInt(str);
			}
		} catch (Exception e) {
			AlarmLogger.getLogger().error(e.getMessage(), e);
		}
		return threshold;
	}


}
