package com.yunqu.cc.mixgw.servlet;

import java.io.DataInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.Clip;

import org.apache.log4j.Logger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.util.VideoUtil;

@WebServlet("/recordSum/*")
public class RecordSumServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.getLogger("recordSum");
	private static Map<String, String> fileMap = new HashMap<String, String>();
	private static String fileDir = "/data/audio/";
	//private static String fileDir = "E:\\file";

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		logger.info("我进来了！");
		fileMap = new HashMap<String, String>();
		int contLength = req.getContentLength();
		JSONObject jsonObject = null;
		if (contLength > 0) {

			byte[] content = new byte[req.getContentLength()];

			try {
				DataInputStream dis = new DataInputStream(req.getInputStream());
				dis.readFully(content);
				logger.info("<< Request[" + req.getRemoteAddr() + "," + req.getRemotePort() + "],body->"
						+ new String(content));
				// String str=URLDecoder.decode(new String(content), "UTF-8");
				String str = new String(content);
				try {
					jsonObject = JSON.parseObject(str);
				} catch (Exception e) {
					jsonObject = parseParamToJson(str);
				}
				if (jsonObject == null) {
					resp.getWriter().write("Fail,JSON format error!");
					return;
				}
			} catch (Exception ex) {
				logger.error(ex, ex);
				resp.setCharacterEncoding("UTF-8");
				resp.getWriter().write(ex.getMessage());
				return;
			}
		} else {
			// contLength小于等于0时看是否request是否有值
			jsonObject = new JSONObject();
			Enumeration enu = req.getParameterNames();
			while (enu.hasMoreElements()) {
				String paraName = (String) enu.nextElement();
				jsonObject.put(paraName, req.getParameter(paraName));
			}
			if (StringUtils.isBlank(jsonObject.getString("command"))) {
				resp.getWriter().write("Fail,content is null");
				return;
			}
		}
		try {
			
			String debug = jsonObject.getString("debug");
			findFiles(fileDir);
			logger.info("读取所有文件，文件数量为：" + fileMap.size());
			if("1".equals(debug)) {
				return;
			}
			Long sumTime = 0L;
			logger.info("正在读取中！！！");
			for (Map.Entry<String, String> entry : fileMap.entrySet()) {
				String mapValue = entry.getValue();
				//获取录音文件播放时长 单位秒
				Long time = VideoUtil.getDuration(fileDir+mapValue);
				sumTime = time+sumTime;
			}
			logger.info("推送文件总时长为："+sumTime+"秒");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("出现错误：" + e.getMessage());
		}
	}


    public static void main(String[] args) {   	
    	
    	try {
			System.out.println(VideoUtil.getDuration("d:\\微信\\WeChat Files\\wxid_24d7nj016vi122\\FileStorage\\File\\2022-07\\1246501.wav"));
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
    
	/**
	 * 递归查找文件
	 * 
	 * @param baseDirName    查找的文件夹路径
	 * @param targetFileName 需要查找的文件名
	 * @param fileList       查找到的文件集合
	 */
	public static void findFiles(String baseDirName) {

		File baseDir = new File(baseDirName); // 创建一个File对象
		if (!baseDir.exists() || !baseDir.isDirectory()) { // 判断目录是否存在
			System.out.println("文件查找失败：" + baseDirName + "不是一个目录！");
		}
		String tempName = null;
		// 判断目录是否存在
		File tempFile;
		File[] files = baseDir.listFiles();
		for (int i = 0; i < files.length; i++) {
			tempFile = files[i];
			if (tempFile.isDirectory()) {
				findFiles(tempFile.getAbsolutePath());
			} else if (tempFile.isFile()) {
				tempName = tempFile.getName();
				if (".wav".equals(tempName.substring(tempName.lastIndexOf(".")))) {
					tempName = tempName.substring(0, tempName.length() - 4);
					fileMap.put(tempName, tempFile.getAbsolutePath().replace(fileDir, ""));
				}

			}
		}
	}
	/**
	 * 解析请求参数为json格式
	 * @param str
	 * @return
	 */
	public static JSONObject parseParamToJson(String str){
		JSONObject jsonObject = null;
		String[] parameters = str.split("&");
		if(parameters.length > 0){
			jsonObject = new JSONObject();
			for (String string : parameters) {
				String[] param = string.split("=");
				if(param.length!=2){
					continue;
				}
				String key = param[0];
				String val = param[1];
				int indexOf = key.indexOf("[");
				if(indexOf > 0){
					String key2 = key.substring(0, indexOf);
					String key3 = key.substring(indexOf+1, key.length()-1);
					JSONObject data = jsonObject.getJSONObject(key2);
					if(data == null){
						data = new JSONObject();
					}
					data.put(key3, val);
					jsonObject.put(key2, data);
				}else{
					jsonObject.put(key, val);
				}
			}
		}
		return jsonObject;
	}
}
