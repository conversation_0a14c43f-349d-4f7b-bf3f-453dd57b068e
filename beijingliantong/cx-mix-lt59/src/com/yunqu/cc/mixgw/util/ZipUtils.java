package com.yunqu.cc.mixgw.util;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.apache.log4j.Logger;

import com.yunqu.cc.mixgw.base.CommonLogger;
import org.apache.poi.util.IOUtils;

public class ZipUtils {
	
	public static Logger logger = CommonLogger.logger;
     /**
     * 压缩文件
     *
     * @param sourceFilePath 源文件路径
     * @param zipFilePath    压缩后文件存储路径
     * @param zipFilename    压缩文件名
     */
    public static boolean compressToZip(String sourceFilePath, String zipFilePath, String zipFilename) {
    	boolean flag = true;
        File sourceFile = new File(sourceFilePath);
        File zipPath = new File(zipFilePath);
        if (!zipPath.exists()) {
            zipPath.mkdirs();
        }
        File zipFile = new File(zipPath + File.separator + zipFilename);
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
        	flag = writeZip(sourceFile, "", zos);
            //文件压缩完成后，删除被压缩文件
            //boolean flag = deleteDir(sourceFile);
        	zos.close();
        } catch (Exception e) {
            flag = false;
            logger.info("文件压缩失败！"+e.getMessage());
            return flag;
        }
        return flag;
    }
    
    /**
     * 遍历所有文件，压缩
     *
     * @param file       源文件目录
     * @param parentPath 压缩文件目录
     * @param zos        文件流
     */
    public static boolean writeZip(File file, String parentPath, ZipOutputStream zos) {
    	boolean flag = true;
        if (file.isDirectory()) {
            //目录
            parentPath += file.getName() + File.separator;
            File[] files = file.listFiles();
            for (File f : files) {
                writeZip(f, parentPath, zos);
            }
        } else {
            //文件
            try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
                //指定zip文件夹
                ZipEntry zipEntry = new ZipEntry(parentPath + file.getName());
                zos.putNextEntry(zipEntry);
                int len;
                byte[] buffer = new byte[1024 * 10];
                while ((len = bis.read(buffer, 0, buffer.length)) != -1) {
                    zos.write(buffer, 0, len);
                    zos.flush();
                }
                bis.close();
            } catch (Exception e) {
                flag = false;
                logger.info("文件压缩失败！"+e.getMessage());
                return flag;
            }
        }
        return flag;
    }
    /**
     * 删除文件夹
     *
     * @param dir
     * @return
     */
    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        //删除空文件夹
        return dir.delete();
    }
    
    
    /**
     * 压缩文件cb相关文件
     *
     * @param sourceFilePath 源文件路径
     * @param zipFilePath    压缩后文件存储路径
     * @param zipFilename    压缩文件名
     */
    public static boolean compressCBToZip(String sourceFilePath, String zipFilePath, String zipFilename) {
    	boolean flag = true;
        File sourceFile = new File(sourceFilePath);
        File zipPath = new File(zipFilePath);
        if (!zipPath.exists()) {
            zipPath.mkdirs();
        }
        File zipFile = new File(zipPath + File.separator + zipFilename);
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
        	flag = writeZipCB(sourceFile, "", zos);
            //文件压缩完成后，删除被压缩文件
            //boolean flag = deleteDir(sourceFile);
        	zos.close();
        } catch (Exception e) {
            flag = false;
            logger.info("文件压缩失败！"+e.getMessage());
            return flag;
        }
        return flag;
    }
    
    /**
     * 遍历所有文件，压缩
     *
     * @param file       源文件目录
     * @param parentPath 压缩文件目录
     * @param zos        文件流
     */
    public static boolean writeZipCB(File file, String parentPath, ZipOutputStream zos) {
    	boolean flag = true;
        if (file.isDirectory()) {
            //目录
            parentPath += file.getName() + File.separator;
            File[] files = file.listFiles();
            for (File f : files) {
                writeZip(f, parentPath, zos);
            }
        } else {
        	//只压缩xlsx格式
        	if(file.getName().contains(".xlsx")) {
	            //文件
	            try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
	                //指定zip文件夹
	                ZipEntry zipEntry = new ZipEntry(parentPath + file.getName());
	                zos.putNextEntry(zipEntry);
	                int len;
	                byte[] buffer = new byte[1024 * 10];
	                while ((len = bis.read(buffer, 0, buffer.length)) != -1) {
	                    zos.write(buffer, 0, len);
	                    zos.flush();
	                }
	                bis.close();
	            } catch (Exception e) {
	                flag = false;
	                logger.info("文件压缩失败！"+e.getMessage());
	                return flag;
	            }
        	}
        }
        return flag;
    }
    
    /**
     * 解压缩ZIP文件
     * @param zipFile ZIP文件
     * @param destDir 目标路径
     */
    public static void zipDecompress(InputStream zipStream, File destDir) {
        byte[] buffer = new byte[1024];
        try (ZipInputStream zis = new ZipInputStream(zipStream, Charset.forName("GBK"))) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                File file = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                } else {
                    File parent = file.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                entry = zis.getNextEntry();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        	try {
				if(zipStream!=null) {
					zipStream.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
    }
    
    /**
  	 * 在某个文件夹里，查询出所有指定后缀的文件
  	 * @param downloadSuffix
  	 * @return
  	 */
  	public static List<File> listFileBySuffix(File dic,String downloadSuffix) {
  		List<File> list = new ArrayList<File>();
  		if(dic.isDirectory()){
  			for(File f :dic.listFiles()){
  				if(f.isFile()){
  					if(f.getAbsolutePath().endsWith(downloadSuffix)){
  						list.add(f);
  						continue;
  					}
  				}else if(f.isDirectory()){
  					list.addAll(listFileBySuffix(f,downloadSuffix)) ;
  				}
  				
  			}
  		}
  		return list;
  	}

    /**
     * 解压ZIP文件
     * @param zipStream ZIP文件输入流
     * @param targetDir 目标目录
     */
    public static void unzipFiles(InputStream zipStream, File targetDir) {
        try (ZipInputStream zis = new ZipInputStream(
                new BufferedInputStream(zipStream),
                Charset.forName("GBK"))) {

            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }

            ZipEntry entry;
            byte[] buffer = new byte[8192];

            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();
                File newFile = new File(targetDir, entryName);

                // 如果是目录，创建目录
                if (entry.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    // 确保父目录存在
                    File parent = newFile.getParentFile();
                    if (parent != null) {
                        parent.mkdirs();
                    }

                    // 写入文件
                    try (FileOutputStream fos = new FileOutputStream(newFile);
                         BufferedOutputStream bos = new BufferedOutputStream(fos)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                    }
                }
                zis.closeEntry();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}