package com.yunqu.cc.mixgw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.cc.mixgw.base.Constants;

@WebListener
public class InterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		ServiceResource  resource7 = new ServiceResource();
		resource7.appName = Constants.APP_NAME; //服务所在的WAR应用名   
		resource7.className = "com.yunqu.cc.mixgw.inf.PublicCRUDService";//服务实现类，类必须实现IService接口
		resource7.description = "cx-mix公共写入/修改/查询";//服务描述
		resource7.serviceId = "CX_MIX_PUBLICCRUD";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource7.serviceName = "cx-mix公共写入/修改/查询";//服务名称
		list.add(resource7);
		
		ServiceResource resource9 = new ServiceResource();
		resource9.appName = Constants.APP_NAME;
		resource9.className = "com.yunqu.cc.mixgw.inf.SmsSverificationService";
		resource9.description = "短信验证码接口";
		resource9.serviceId = "YC-SMS-SEND-SMS-SERVICE";
		resource9.serviceName = "短信验证码接口";
		list.add(resource9);

		ServiceResource  resource1 = new ServiceResource();
		resource1.appName     =  Constants.APP_NAME;
		resource1.className   =  "com.yunqu.cc.mixgw.inf.OrigCallService";
		resource1.description =  "全媒体呼叫中心平台B号";
		resource1.serviceId   =  "GET_ORIGCALL_SERVICE";
		resource1.serviceName =  "全媒体呼叫中心平台B号";
		list.add(resource1);
		
		
		ServiceResource  resource2 = new ServiceResource();
		resource2.appName     =  Constants.APP_NAME;
		resource2.className   =  "com.yunqu.cc.mixgw.inf.EntAlarmService";
		resource2.description =  "企业告警接口";
		resource2.serviceId   =  "CX_MIX_ENT_ALARM";
		resource2.serviceName =  "企业告警接口";
		list.add(resource2);
		
		return list;
	}

}
