<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>全媒体智能呼叫中心话务资源使用总量统计表</title>
	<style type="text/css">
	    th{
	        text-align:center;/** 设置水平方向居中 */
	        vertical-align:middle/** 设置垂直方向居中 */
	    }
	    a:link{ color:#00adff;}
	    
	    ::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		
		.container-fluid{
		    height: 100%;
		}
		
		#searchForm{
			height:100%;
		}
		
		#searchForm .layui-table-view {
		    height: 100%;
		}
		
		#searchForm .layui-table-box {
		    height: 93%;
		}
		
		.ibox {
		    height: 100%;
		}
		
		.ibox-content {
		    height: 100%;
		}


		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.btn{
			margin-right: 10px;
		}
		#order_card{
			height: 100%;
			overflow-x: auto;
			/* overflow-y: hidden; */
			-webkit-box-flex: 1;
		    -ms-flex: 1;
		    flex: 1;
		    overflow: auto;
		    position: relative;
		}
		.custIcon {
			color: #31b0d5;
			font-size: 17px;
			cursor:pointer;
		}
		.out_a {
			position: absolute;
		    top: 50%;
		    right: 15px;
		    z-index: 333;
		}
		.flex-row {
		    width: 100%;
		    display: -webkit-box;
		    display: -ms-flexbox;
		    display: flex;
		    -webkit-box-orient: horizontal;
		    -webkit-box-direction: normal;
		    -ms-flex-direction: row;
		    flex-direction: row;
		    -webkit-box-pack: justify;
		    -ms-flex-pack: justify;
		    justify-content: space-between;
		    -webkit-box-sizing: border-box;
		    box-sizing: border-box;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<input type="hidden" id="sendTimeBeginSet">
	<input type="hidden" id="sendTimeEndSet">
	<div class="flex-row" style="height: 100%">
		<div id="order_card" >
			<div class="saasTitleMsg" id="saasTitleMsg" style="display: none;">
				<div style="background:#ffffff;color:#676A6C;padding: 5px;width: 540px;font-size: 14px;">
					<p><span>---</span></p>
				</div>
			</div> 	
			<form  name="searchForm" class="form-inline" id="searchForm" >
		   		<div class="ibox" >
		  			<div class="ibox-title clearfix">
		    			<div class="form-group">
		      				<h5>全媒体智能呼叫中心话务资源使用总量统计表<i class="layui-icon layui-icon-about tips" id="saasTitle" style="color: #1E9FFF;" title=""></i></h5>
		      				<div class="input-group pull-right">
					   			<button type="button" class="btn btn-sm btn-info btn-outline " onclick="resZlStat.exportDetail()">导出</button>
							</div>
						</div>
						<hr style="margin: 5px -15px">
						<div class="form-group" id="divId">
						  	<div class="input-group">
					 			<span class="input-group-addon">统计时间</span>
					        	<input type="text" class="form-control" id="beginTime" name="beginTime"data-mars-top="true" autocomplete="off" style="width:142px" > 
		                		<input type="text" class="form-control" id="endTime" name="endTime"data-mars-top="true" autocomplete="off" style="width:142px" > 
		                	</div>
						   	<div class="input-group ">
								<button type="button" class="btn btn-sm btn-default" onclick="resZlStat.searchData('1')"><span class="glyphicon glyphicon-search"></span><span>查询</span></button>
							</div>
						 	
						</div>
		     	    </div>  
		           	<div class="table-responsive" style="background-color: #fff;">
		   	     		<table id="dataList"></table>
		           	</div> 
				</div>
			</form>
		</div>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/cc-sms/static/js/time.js"></script>
	<script type="text/javascript">
		jQuery.namespace("resZlStat");
		$(function() {
			$("#searchForm").render({success:function(){
				requreLib.setplugs('layui',function(){
					//加载时间控件
					 layui.use('laydate', function(){
		   			  	var laydate = layui.laydate;
			   			laydate.render({elem: '#beginTime',type: 'month',value: dateFormat(new Date(), "yyyy-MM"),btns: ['clear', 'confirm']});
			   			laydate.render({elem: '#endTime',type: 'month',value: dateFormat(new Date(), "yyyy-MM"),btns: ['clear', 'confirm']});
			   			$("#saasTitle").hover(function() {
							openSaasTitleMsg();
						}, function() {
							//layer.close(subtips);
						});
		   			}) 
		   			resZlStat.loadData();
		   		})
				
			}})	
			//自动填充
			$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
		});
		function openSaasTitleMsg() {
			var msg = $("#saasTitleMsg").html();
			layer.tips("<span style='color:#676A6C'>"+msg+"</span>", '#saasTitle',{tips:[1,'#ffffff'],closeBtn:1,time:2000,area: ['auto', 'auto']});
		}
		//计算两个中文时间的时间差
		function  getDaysBetween(dateString1,dateString2){
		    var  startDate = Date.parse(dateString1);
		    var  endDate = Date.parse(dateString2);
		    if (startDate>endDate){
		        return 0;
		    }
		    if (startDate==endDate){
		        return 1;
		    }
		    var days=(endDate - startDate)/(1*24*60*60*1000);
		    return  days;
		}
		
		//获得时间
		function getBeforeDate(n) {
			var date = new Date();
			var year, month, day;
			date.setDate(date.getDate() + n);
			year = date.getFullYear();
			month = date.getMonth() + 1;
			day = date.getDate();
			s = year + '-' + (month < 10 ? ('0' + month) : month) + '-' + (day < 10 ? ('0' + day) : day);
			return s;
		}
	
		//初始化加载数据
		resZlStat.loadData=function(){
			
			$("#searchForm").initTable({
				mars:"resStatDao.zl",
				id:"dataList",
				limit:'15',
				height : 'full-180',
				limits:[15,25,50,100,200],
				cols: [
					[
						{width:100,type:'numbers', title: '序号',rowspan: 2},
						{width:130,field:'date',title:'月份',align:'center',rowspan: 2},
						{width:130,field:'name',title:'系统名称',align:'center',rowspan: 2},
						
						{field:'',title:'坐席资源',align:'center',colspan: 3},
						{field:'',title:'IVR资源',align:'center',colspan: 3},
						{field:'',title:'SIP资源',align:'center',colspan: 3},
						{field:'',title:'ASR资源',align:'center',colspan: 3},
						{field:'',title:'TTS资源',align:'center',colspan: 3},
						{field:'',title:'会议资源',align:'center',colspan: 3},
						{field:'',title:'智能外呼资源',align:'center',colspan: 3},
						{field:'',title:'录音资源',align:'center',colspan: 3}
					],
					[
						{width:130,field:'agentCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'agentPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'agentPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'ivrCallCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'ivrCallPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'ivrCallPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'sipCallCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'sipCallPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'sipCallPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'asrCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'asrPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'asrPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'ttsCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'ttsPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'ttsPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'confCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'confPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'confPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'ocsCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'ocsPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'ocsPercentage',title:'资源占用率',align:'center'},
						
						{width:130,field:'recordCountMax',title:'月峰值',align:'center'},					
						{width:130,field:'recordPeak',title:'月均峰值',align:'center'},					
						{width:130,field:'recordPercentage',title:'资源占用率',align:'center'}
					]
				]
			});
		}
		resZlStat.searchData=function(flag){
			$("#searchForm").queryData({id:'dataList'});
		};
		
		//导出
		resZlStat.exportDetail = function(){
			layer.confirm("是否导出全媒体智能呼叫中心话务资源使用总量统计报表",{icon: 3, title:"导出提示",btn:["确定","取消"],offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportResZlStat&"+$("#searchForm").serialize();
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>