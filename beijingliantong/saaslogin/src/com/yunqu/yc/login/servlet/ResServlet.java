package com.yunqu.yc.login.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.login.base.Constants;
import com.yunqu.yc.login.base.QueryFactory;
import com.yunqu.yc.login.logger.AppLogger;
import com.yunqu.yc.login.utils.CacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * Servlet implementation class AccessTokenServlet
 */
@WebServlet("/res")
public class ResServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public ResServlet() {
        super();
        // TODO Auto-generated constructor stub
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
    	try {
			this.doservice(request, response);
		} catch (Exception ex) {
			response.getWriter().println(ex.getMessage());
		}
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doservice(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		response.setHeader("Pragma", "no-cache");	// HTTP/1.0 caches might not implement Cache-Control and might only implement Pragma: no-cache
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", 0);
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");	// 与 contentType 分开设置
		
		String  token = request.getParameter("token");
		String  redirect_uri = request.getParameter("redirect_uri");
		String  busiId = request.getParameter("busiId");
		
		AppLogger.getLogger().info("["+request.getRemoteAddr()+","+request.getRequestURI()+"] << token:"+token+",redirect_uri:"+redirect_uri);
		
		if(StringUtils.isAllBlank(token,redirect_uri)){
			response.getWriter().write("Invalid request param!");
			return ;
		}
		
		String username = CacheUtil.get(token);
		if(StringUtils.isBlank(username)){
			response.getWriter().write("token已失效，请重新登录!");
			return ;
		}
		
		String password = "";
		try {
			String sql = "select USER_PWD from cc_user where USER_ACCT = ?   and  USER_STATE = 0 ";
			password = QueryFactory.getQuery().queryForString(sql, new Object[] { username });
			if(StringUtils.isBlank(password)){
				response.getWriter().write("请求访问资源失败，原因：无效登录账号！");
				return;
			}
			
			YCUserPrincipal  principal = (YCUserPrincipal)request.getUserPrincipal();
			if(principal==null){
				request.login(username, password);
				principal = (YCUserPrincipal)request.getUserPrincipal();
			}
			
			if(principal == null){
				response.getWriter().write("请求访问资源失败，原因：无效登录账号！");
			}
			if(StringUtils.isBlank(busiId)) {
				principal.setBusiId(Constants.getBusiId());
			}else {
				principal.setBusiId(busiId);
			}
			
			if(principal.isAdmin()){
				principal.setRoleType(1);
			}
			if(StringUtils.isNotBlank(redirect_uri)){
				response.sendRedirect(redirect_uri);
				return;
			}
		} catch (Exception ex) {
			AppLogger.getLogger().error(ex,ex);
			response.getWriter().write("请求访问资源失败，原因："+ex.getMessage());
			return ;
		}
		
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doGet(request, response);
	}

}
