package com.yunqu.yc.notify.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


/**
 * 请求数据模型
 * <AUTHOR>
 */
public class RequestData{
	
	//企业ID
	String entId;

	//32消息流水号，全局唯一
	String serialId;
	
	String busiSerialId;

	String busiId;
	
	// 时间戳
	long timestamp = 0;
	
	//被通知客户号码
	String called;
	
	//JSON对象，随路数据
	String userData = "" ;
	
	//通知结果回调ＵＲＬ，云呼平台执行完成语音通知，把通知结果进行返回，当该字段为空是，不通知。
	String callbackUrl;

	String sysnbackUrl;
	
	JSONObject params;

	String taskId;

	String taskName;
	
	String batchId;
	
	//取消类型（task:任务所有名单；batch:批次所有名单；obj:具体某个名单）
	String cancelType;

	JSONArray custList;

	public String getSysnbackUrl() {
		return sysnbackUrl;
	}

	public void setSysnbackUrl(String sysnbackUrl) {
		this.sysnbackUrl = sysnbackUrl;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public JSONArray getCustList() {
		return custList;
	}

	public void setCustList(JSONArray custList) {
		this.custList = custList;
	}

	public JSONObject getParams() {
		return params;
	}

	public void setParams(JSONObject params) {
		this.params = params;
	}

	public String getBusiSerialId() {
		return busiSerialId;
	}

	public void setBusiSerialId(String busiSerialId) {
		this.busiSerialId = busiSerialId;
	}

	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getSerialId() {
		return serialId;
	}

	public void setSerialId(String serialId) {
		this.serialId = serialId;
	}


	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String[] parseCalledList(){
		return called.split("&");
	}

	public String getCalled() {
		return called;
	}

	public void setCalled(String called) {
		this.called = called;
	}

	public String getUserData() {
		return userData;
	}

	public void setUserData(String userData) {
		this.userData = userData;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}
	
	public String getTaskName() {
		return taskName;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	
	public String getCancelType() {
		return cancelType;
	}

	public void setCancelType(String cancelType) {
		this.cancelType = cancelType;
	}

	public String getBusiId() {
		return busiId;
	}

	public void setBusiId(String busiId) {
		this.busiId = busiId;
	}

	@Override
	public String toString(){
		return JSON.toJSONString(this);
	}
	
}
