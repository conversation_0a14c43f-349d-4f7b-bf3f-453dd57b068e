package com.yunqu.yc.notify.servlet.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.notify.base.EntContext;
import com.yunqu.yc.notify.base.QueryFactory;
import com.yunqu.yc.notify.base.TaskConstants;
import com.yunqu.yc.notify.model.RequestData;
import com.yunqu.yc.notify.servlet.AbstractServlet;
import com.yunqu.yc.notify.util.AssertUtils;
import com.yunqu.yc.notify.util.CacheUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务修改接口
 */
@WebServlet("/task/edit")
public class TaskEditServlet extends AbstractServlet {
    @Override
    protected String getName() {
        return "taskEdit";
    }

    @Override
    protected String proxy(RequestData requestData) throws Exception {
        try {
            EasyQuery query = QueryFactory.getQuery();
            String taskId = requestData.getTaskId();
            AssertUtils.isNotBlank(taskId, "任务ID不能为空");
            String entId = requestData.getEntId();
            JSONObject params = requestData.getParams();
            String busiId = requestData.getBusiId();
            EntContext context = EntContext.getContext(entId);
            String busiOrderId = context.getBusiOrderId(busiId);

            JSONObject task = getTask(entId, taskId, busiOrderId);
            //先查找出任务
            AssertUtils.isNotNull(task, "任务不存在");

            Integer taskState = task.getInteger("TASK_STATE");
            AssertUtils.isTrue(!taskState.equals(TaskConstants.TASK_STATE_005), "当前状态不可修改");

            EasyRecord record = new EasyRecord(context.getTableName("cc_task"), "TASK_ID");
            record.setPrimaryValues(task.getString("TASK_ID"));
            //扩展字段
            setExtConf(record, task, params);
            //其他字段
            setOtherColumn(record, params);

            //时间(传开始时间则是要修改时间，不传则不修改)
            if (StringUtils.isNotBlank(params.getString("startDate"))) {
                AssertUtils.isNotBlank(params.getString("endDate"), "结束时间不能为空");
                Date startDate = EasyDate.stringToDate("yyyy-MM-dd", params.getString("startDate"));
                Date endDate = EasyDate.stringToDate("yyyy-MM-dd", params.getString("endDate"));
                AssertUtils.isTrue(startDate.before(endDate), "开始时间不能大于结束时间");
                record.set("START_DATE", params.getString("startDate"));
                record.set("END_DATE", params.getString("endDate"));
            }

            String skillGroupId = params.getString("skillGroupId");
            JSONArray agentList = params.getJSONArray("agentList");

            if (StringUtils.isNotBlank(skillGroupId)){
                //技能组
                EasySQL sql = new EasySQL("select count(1) FROM " + EntContext.getContext(entId).getTableName("cc_skill_group") + " where 1=1 ");
                sql.append(skillGroupId, "and SKILL_GROUP_ID = ? ");
                sql.append(entId, "and ENT_ID = ? ");
                sql.append(busiOrderId,"and BUSI_ORDER_ID = ?");
                boolean exist = query.queryForExist(sql.getSQL(), sql.getParams());
                AssertUtils.isTrue(exist, "技能组【" + skillGroupId + "】不存在");
                //任务组管理，把呼叫任务分配到具体的技能组
                createTaskSkillGroup(record.getPrimaryValue().toString(), skillGroupId, entId, busiOrderId);
                //查找技能组坐席
                List<String> skillGroupAgentList = getSkillGroupAgentList(skillGroupId, entId);
                //通知坐席
                notifyAgent(skillGroupAgentList,entId,busiId);
            } else if (agentList != null && agentList.size() > 0) {
                //坐席列表创建技能组
                createTaskSkillGroupAgentList(record, task.getString("TASK_NAME"), agentList, entId, busiOrderId);
                //通知坐席
                notifyAgent(agentList,entId,busiId);
            }

            if (task.getInteger("TASK_TYPE").equals(TaskConstants.TASK_TYPE_IVR) && StringUtils.isNotBlank(params.getString("robotId"))) {
                String robotId = params.getString("robotId");
                AssertUtils.isNotBlank(robotId, "机器人ID不能为空");
                boolean b = query.queryForExist("select count(1) from CC_ENT_ROBOT_MODEL where ROBOT_ID = ? and ENT_ID = ?", new Object[]{robotId, entId});
                AssertUtils.isTrue(b, "机器人【" + robotId + "】不存在");
                record.put("IVR_FLOW_NAME", robotId);
            }

            //外呼号码 没传则用企业的所有外显号码
            List<String> prefixList = getPrefixList(params, entId);
            JSONArray prefixNumList = params.getJSONArray("prefixNumList");
            if (prefixNumList != null && prefixNumList.size() > 0) {
                createPrefix(prefixList, record, entId);
            }

            this.changeToInt(record, "TASK_NUM", "STATE_TIME", "TASK_SOURCE", "CALL_RATE_FACTOR", "TASK_STATE", "TASK_TYPE", "IS_BATCH_ALLOCATION",
                    "MAX_QUEUE_TIME", "MAX_QUEUE_COUNT", "FAIL_CALL_COUNT", "CALL_INTERVAL", "IVR_ORDER_FLAG", "IVR_ORDER_FLAG",
                    "RES_COUNT", "IVR_ORDER_CONFIRM", "IVR_ORDER", "ALLOCATION_TYPE", "OCS_CALLTIME_LIMIT", "PRIORITY", "MAX_ALTERING_TIME");
            query.update(record);
            return getSuccResult("成功").toJSONString();
        } catch (SQLException s) {
            logger.error(s);
            return getErrorResult(s.getMessage()).toJSONString();
        }
    }

    private void createTaskSkillGroupAgentList(EasyRecord taskRecord, String taskName,JSONArray agentList, String entId,String busiOrderId) throws Exception {
        AssertUtils.isTrue(agentList!=null&&agentList.size() > 0,"坐席列表不能为空");
        EasyQuery query = QueryFactory.getQuery();
        List<String> agentIdList = new ArrayList<>();
        for (int i = 0; i < agentList.size(); i++) {
            String agentName = agentList.getString(i);
            AssertUtils.isNotBlank(agentName,"坐席工号不能为空");
            JSONObject user = query.queryForRow("select USER_ID from CC_USER where USER_ACCT = ? and ENT_ID = ?", new Object[]{agentName, entId},new JSONMapperImpl());
            AssertUtils.isNotNull(user,"坐席【"+agentName+"】不存在");
            agentIdList.add(user.getString("USER_ID"));
        }

        //创建技能组
        EasyRecord record = new EasyRecord(EntContext.getContext(entId).getTableName("CC_SKILL_GROUP"),"SKILL_GROUP_ID");
        record.setPrimaryValues(this.nextSeq("CC_SKILL_GROUP"));
        record.put("BUSI_ORDER_ID",busiOrderId);
        record.put("ENT_ID",entId);
        record.put("SKILL_GROUP_NAME","任务【"+taskName+"】技能组");
        if (StringUtils.isNotBlank(taskRecord.getString("PREFIX_GROUP_ID"))){
            record.put("PREFIX_GROUP_ID",taskRecord.getString("PREFIX_GROUP_ID"));
        }else{
            record.put("PREFIX_NUM",taskRecord.getString("PREFIX_NUM"));
        }
        record.put("QUEUE_STRATEGY",1);
        record.put("CREATOR",getCreator());
        record.put("CREATE_TIME","");
        record.set("QUEUE_STRATEGY", 1);
        record.set("IDX_ORDER", 99);
        record.set("AGENT_COUNT", agentList.size());
        record.set("GROUP_STATE",2);
        record.set("WORK_NOT_READY",0);
        record.set("CREATE_TIME",EasyDate.getCurrentDateString());
        query.save(record);

        //将坐席分配到技能组
        for (int i = 0; i < agentIdList.size(); i++) {
            String agentId = agentIdList.get(i);
            EasyRecord userGroup=new EasyRecord(EntContext.getContext(entId).getTableName("cc_skill_group_user"),new String[]{"SKILL_GROUP_ID","USER_ID"});
            userGroup.set("IDX_ORDER", 99);
            userGroup.set("BUSI_ORDER_ID", busiOrderId);
            userGroup.set("ENT_ID", entId);
            userGroup.setPrimaryValues(record.getPrimaryValue().toString(),agentId);
            query.save(userGroup);
        }

        //任务组管理，把呼叫任务分配到具体的技能组
        createTaskSkillGroup(taskRecord.getPrimaryValue().toString(), record.getPrimaryValue().toString(), entId,busiOrderId);
    }

    private List<String> getSkillGroupAgentList(String skillGroupId,String entId) {
        EasyQuery query = QueryFactory.getQuery();
        EntContext context = EntContext.getContext(entId);
        EasySQL easySQL = new EasySQL("select t3.USER_ACCT FROM "+ context.getTableName("cc_skill_group") +" t1" );
        easySQL.append("INNER JOIN "+context.getTableName("cc_skill_group_user")+" t2 on t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID ");
        easySQL.append("INNER JOIN cc_user t3 on t2.USER_ID = t3.USER_ID ");
        easySQL.append(skillGroupId,"WHERE t1.SKILL_GROUP_ID = ? ",false);
        try {
            List<JSONObject> objects = query.queryForList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
            if (objects!=null && objects.size() > 0) {
                return objects.stream().map(jsonObject -> jsonObject.getString("USER_ACCT")).collect(Collectors.toList());
            }
        }catch (SQLException s){
            logger.error(s);
        }
        return Collections.emptyList();
    }

    private void createTaskSkillGroup(String taskId, String skillGroupId, String entId, String busiOrderId) throws SQLException {
        if (QueryFactory.getQuery(entId).queryForExist("select count(1) from " + EntContext.getContext(entId).getTableName("CC_TASK_GROUP") + " where TASK_ID = ? and SKILL_GROUP_ID = ?", new Object[]{taskId, skillGroupId})) {
            return;
        }
        EasyRecord taskGroup = new EasyRecord(EntContext.getContext(entId).getTableName("CC_TASK_GROUP"), new String[]{"TASK_GROUP_ID"});
        taskGroup.setPrimaryValues(RandomKit.randomStr());
        taskGroup.set("SKILL_GROUP_ID", skillGroupId);
        taskGroup.set("TASK_ID", taskId);
        taskGroup.set("ENT_ID", entId);
        taskGroup.set("ALLOC_STATE", 0);//坐席未分配
        taskGroup.set("OBJ_USE_COUNT", 0);
        taskGroup.set("OBJ_COUNT", 0);
        taskGroup.set("ASIGN_COUNT", 0);
        taskGroup.set("TASK_STATE", 0);//待执行
        taskGroup.set("BUSI_ORDER_ID", busiOrderId);
        taskGroup.set("CREATE_TIME", EasyDate.getCurrentDateString());
        taskGroup.set("CREATOR", getCreator());
        QueryFactory.getQuery(entId).save(taskGroup);
    }

    private void setExtConf(EasyRecord taskRecord, JSONObject data, JSONObject params) {
        JSONObject conf = data.getJSONObject("EXT_CONF");
        if (StringUtils.isNotBlank(params.getString("specialStat"))) {
            conf.put("specialStat", conf.getString("specialStat"));
        }
        if (StringUtils.isNotBlank(params.getString("videoCall"))) {
            conf.put("videoCall", conf.getString("videoCall"));
        }
        if (StringUtils.isNotBlank(params.getString("sms_xx"))) {
            conf.put("sms_xx", conf.getString("sms_xx"));
        }
        if (StringUtils.isNotBlank(params.getString("filterRepeatNum"))) {
            conf.put("filterRepeatNum", conf.getString("filterRepeatNum"));
        }
        if (StringUtils.isNotBlank(params.getString("sms_gj"))) {
            conf.put("sms_gj", conf.getString("sms_gj"));
        }
        if (StringUtils.isNotBlank(params.getString("callType"))) {
            conf.put("callType", conf.getString("callType"));
        }
        if (StringUtils.isNotBlank(params.getString("evedayCall"))) {
            conf.put("evedayCall", conf.getString("evedayCall"));
        }
        if (StringUtils.isNotBlank(params.getString("filterRunDate"))) {
            conf.put("filterRunDate", conf.getString("filterRunDate"));
        }
        if (StringUtils.isNotBlank(params.getString("callBackUrl"))) {
            conf.put("callBackUrl", conf.getString("callBackUrl"));
        }
        if (StringUtils.isNotBlank(params.getString("sysnbackUrl"))) {
            conf.put("sysnbackUrl", conf.getString("sysnbackUrl"));
        }
        taskRecord.set("EXT_CONF", conf.toJSONString());
    }


    private List<String> getPrefixList(JSONObject params, String entId) throws Exception {
        JSONArray prefixNums = params.getJSONArray("prefixNumList");
        List<String> prefixNumList = new ArrayList<>();
        if (prefixNums == null || prefixNums.size() == 0) {
            List<JSONObject> prefixNum = QueryFactory.getQuery().queryForList("select PREFIX_NUM from CC_PREFIX where ENT_ID = ?", new Object[]{entId}, new JSONMapperImpl());
            AssertUtils.isNotEmpty(prefixNum, "该企业未配置外显号码");
            for (JSONObject object : prefixNum) {
                prefixNumList.add(object.getString("PREFIX_NUM"));
            }
        } else {
            for (int i = 0; i < prefixNums.size(); i++) {
                String num = prefixNums.getString(i);
                if (!QueryFactory.getQuery().queryForExist("select count(1) from cc_prefix where PREFIX_NUM = ? and ENT_ID = ?", new Object[]{num, entId})) {
                    throw new Exception("号码【" + num + "】不存在");
                }
                prefixNumList.add(num);
            }
        }
        return prefixNumList;
    }

    private void createPrefix(List<String> prefixNumList, EasyRecord record, String entId) throws Exception {
        //超过1一个号码则生成外呼号码组
        if (prefixNumList.size() > 1) {
            EasyRecord prefixGroupRecord = new EasyRecord("cc_prefix_group", "PREFIX_GROUP_ID");
            prefixGroupRecord.setPrimaryValues(RandomKit.uniqueStr());
            prefixGroupRecord.set("ENT_ID", entId);
            prefixGroupRecord.set("PREFIX_GROUP_NAME", "外呼组");
            prefixGroupRecord.set("CREATOR", getCreator());
            prefixGroupRecord.set("CREATE_TIME", EasyDate.getCurrentDateString());
            List list = new ArrayList();
            for (int i = 0; i < prefixNumList.size(); i++) {
                list.add(new Object[]{
                        entId, record.getPrimaryValue(),
                        prefixNumList.get(i), entId, EasyDate.getCurrentDateString()
                });
            }
            String sql = "INSERT INTO cc_prefix_group_prefix (ENT_ID,PREFIX_GROUP_ID,PREFIX_NUM,CREATOR,CREATE_TIME) VALUES(?,?,?,?,?)";
            QueryFactory.getQuery().save(prefixGroupRecord);
            QueryFactory.getQuery().executeBatch(sql, list);

            record.put("PREFIX_NUM", "");
            record.put("PREFIX_GROUP_ID", prefixGroupRecord.getPrimaryValue());
        } else {
            record.put("PREFIX_GROUP_ID", "");
            record.set("PREFIX_NUM", prefixNumList.get(0));
        }
    }

    private void setOtherColumn(EasyRecord record, JSONObject params) throws Exception {
        //呼叫比
        Float callRate = params.getFloat("callRate");
        if (callRate != null) {
            AssertUtils.isTrue(callRate >= 0 && callRate < 10, "呼叫比取值范围0-9");
            record.put("CALL_RATE", callRate);
        }

        //呼叫失败重呼次数
        Integer failCallCount = params.getInteger("failCallCount");
        if (failCallCount != null) {
            AssertUtils.isTrue(failCallCount >= 0, "呼叫失败重呼次数取值范围需大于或等于0");
            record.put("FAIL_CALL_COUNT", failCallCount);
        }

        //重呼间隔
        Integer callInterval = params.getInteger("callInterval");
        if (callInterval != null) {
            AssertUtils.isTrue(callInterval >= 0, "重呼间隔取值范围需大于或等于0");
            record.put("CALL_INTERVAL", callInterval * 60); //转分钟
        }

        //外呼通道数
        Integer resCount = params.getInteger("resCount");
        if (resCount != null) {
            AssertUtils.isTrue(resCount >= 0, "外呼通道数取值范围需大于或等于0");
            record.put("RES_COUNT", resCount);
        }

    }

    protected synchronized int nextSeq(String tableName) {
        EasyQuery query = QueryFactory.getQuery();
        tableName = tableName.toUpperCase();
        String sql = "select SEQ_ID from CC_SEQ  where TABLE_ID = ?";
        int seq = 0;
        try {
            String seqIdStr = query.queryForString(sql, new Object[]{tableName});
            if (StringUtils.isNotBlank(seqIdStr)) {
                seq = Integer.parseInt(seqIdStr);
            }
        } catch (Exception ex) {
            logger.error("AppBaseServlet.nextSeq() error->cause:" + ex.getMessage(), ex);
        }
        if (seq <= 0) {
            try {
                sql = "insert into CC_SEQ(TABLE_ID,SEQ_ID) values (?,?)";
                query.execute(sql, new Object[]{tableName, 1});
            } catch (Exception ex) {
                logger.error("AppBaseServlet.nextSeq() error->cause:" + ex.getMessage(), ex);
            }
        }
        seq = seq + 1;
        sql = "update CC_SEQ set SEQ_ID = ? where TABLE_ID = ?  ";
        try {
            query.executeUpdate(sql, new Object[]{seq, tableName});
        } catch (Exception ex) {
            logger.error("AppBaseServlet.nextSeq() error->cause:" + ex.getMessage(), ex);
        }
        return seq;
    }

    public void notifyAgent(List<?> agentList,String entId,String busiId){
        agentList.forEach(agent -> {
            JSONObject value = new JSONObject();
            value.put("agentId",agent.toString());
            value.put("entId",entId);
            value.put("busiId",busiId);
            if(CacheUtil.llen("#update-agent-group") < 1000) {
                CacheUtil.lpush("#update-agent-group", value.toJSONString());
            }
        });
    }

    private void createTemp(JSONObject params, EasyRecord taskRecord, String entId, String busiOrderId) throws Exception {
        JSONArray custTemp = params.getJSONArray("custTempList");
        String custTempTelNum = params.getString("custTempTelNum");
        AssertUtils.isTrue(custTemp != null && custTemp.size() > 0, "客户资料表头【custTempList】不能为空");
        AssertUtils.isTrue(custTemp.size() <= 21, "客户资料表头限制21个");
        AssertUtils.isNotBlank(custTempTelNum, "客户号码字段【custTempTelNum】不能为空");
        AssertUtils.isTrue(custTemp.contains(custTempTelNum), "客户模板不存在【" + custTempTelNum + "】字段");

        EasyRecord record = new EasyRecord(EntContext.getContext(entId).getTableName("cc_cust_temp"), "TEMP_ID");
        record.setPrimaryValues(RandomKit.randomStr());
        Integer fIndex = 0;
        for (int i = 0; i < custTemp.size(); i++) {
            String tempName = custTemp.getString(i);
            AssertUtils.isNotBlank(tempName, "表头名称不能为空");
            JSONObject tempParams = new JSONObject();
            tempParams.put("readOnly", "0"); //不能修改 默认：否
            tempParams.put("dateType", "0");
            tempParams.put("name", tempName);
            tempParams.put("inuse", "1"); //是否启用 默认：是
            if (tempName.equals(custTempTelNum)) {
                record.set("TEL_NUM1", tempParams.toJSONString());
            } else {
                fIndex++;
                record.set("F" + fIndex, tempParams.toJSONString());
            }
        }

        record.set("BUSI_ORDER_ID", busiOrderId);
        record.set("ENT_ID", entId);
        record.set("TEMP_NAME", "模板");
        record.set("CREATE_TIME", EasyDate.getCurrentDateString());
        record.set("CREATOR", getCreator());
        record.set("CREATE_RES", "1");
        QueryFactory.getQuery().save(record);
        taskRecord.set("TEMP_ID", record.getPrimaryValue());
    }
}
