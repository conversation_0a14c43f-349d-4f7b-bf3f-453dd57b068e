package com.yunqu.yc.notify.util;

import com.yunqu.yc.notify.base.Constants;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class IpUtils {

    private static Set<String> whiteIpSet;

    static {
        whiteIpSet = Constants.getWhiteIp();
    }

    public static String getIp(HttpServletRequest request){
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    /**
     * 判断是否在白名单ip里面
     * @param ip
     * @return
     */
    public static boolean isWhiteIp(String ip){
        if (StringUtils.isBlank(ip)){
            return false;
        }
        return whiteIpSet.contains(ip);
    }

    public static boolean isWhiteIp(HttpServletRequest request){
        String ip = getIp(request);
        return isWhiteIp(ip);
    }

}
