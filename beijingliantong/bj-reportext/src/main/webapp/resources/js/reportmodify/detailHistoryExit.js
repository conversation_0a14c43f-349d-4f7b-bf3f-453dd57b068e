layui.use('form', function () {
    let form = layui.form;
});

function initData(data){
    $("#recId").val(data.REC_ID)
    $("#insertTime").val(data.INSERT_TIME)
    $("#relationFlag").val(data.RELATION_FLAG)
    $("#finishFlag").val(data.FINISH_FLAG)
    $("#satisfyFlag").val(data.SATISFY_FLAG)
    var form = layui.form;
    form.render("select");
}

$(document).ready(function () {
    $.ajax({
        url: basePath + '/report/getSessionRecordById.do',
        type: 'POST',
        cache: false,
        async: false,
        data: { sessionId: $("#sessionId").val() },
        success: function (data) {
            if (data.code == '0') {
                var textArray = data.textArray;
                textArray = textArray.replace(/[\r\n]/g, "<br>");
                var text = eval('(' + textArray + ')');
                for (var i = 0; i < text.length; i++) {
                    var div = '';
                    if (text[i].TYPE == "1") {
                        div = "<div class='historyRight msgBox'><input type='hidden' value=\'" + JSON.stringify(text[i]) + "\' name = 'liData' /><div class='time'>" + getdate(text[i].TIMESTAMP) + "</div><div class='txt' contenteditable='true'>" + text[i].CONTENT + "</div></div > ";
                    } else {
                        div = "<div class='historyLeft msgBox'><input type='hidden' value=\'" + JSON.stringify(text[i]) + "\' name='liData'/><div class='time'>" + getdate(text[i].TIMESTAMP) + "</div><div class='txt' contenteditable='true'>" + text[i].CONTENT + "</div ></div > ";
                    }
                    $(".detailHistory").append(div);
                }
            } else {
                console.log('获取通话内容异常！');
            }
        }
    });
})

function saveEdit() {
    let arr = []
    $(".detailHistory .msgBox").each(function () {
        let txt = $(this).find(".txt").text()
        let liData = JSON.parse($(this).find("input[name=liData]").val())
        liData.CONTENT = txt
        arr.push(liData)
    })
    let sessionId=$("#sessionId").val()
    $.ajax({
        type: 'post',
        url: basePath + '/report/modifysessionInfoById.do',
        data:{
            textarry:JSON.stringify(arr),
            sessionId:sessionId
        },
        dataType: 'json',
        success: function (data) {
            if (data.code === "0") {
                parent.reloadt()
                layer.msg("保存成功");
            }else{
                layer.msg("保存失败");
            }
        }, error: function (error) {
             layer.msg("保存失败");
        }
    });
}

/*时间戳转时间*/
function getdate(timestamp) {
    timestamp = Number(timestamp);
    var now = new Date(timestamp),
        y = now.getFullYear(),
        m = now.getMonth() + 1,
        d = now.getDate();
    return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " " + now.toTimeString().substr(0, 8);
}

function formSave() {
    let recId=$("#recId").val();
    let insertTime=$("#insertTime").val();
    let relationFlag=$("#relationFlag").val();
    let finishFlag=$("#finishFlag").val();
    let satisfyFlag=$("#satisfyFlag").val();
    $.ajax({
        type: 'post',
        url: basePath + '/report/modifytikcetInfoById.do',
        data:{
            recId:recId,
            insertTime:insertTime,
            relationFlag:relationFlag,
            finishFlag:finishFlag,
            satisfyFlag:satisfyFlag,
        },
        dataType: 'json',
        success: function (data) {
            if (data.code === "0") {
                parent.reloadt()
                layer.msg("保存成功");
            }else{
                layer.msg("保存失败");
            }
        }, error: function (error) {
             layer.msg("保存失败");
        }
    });
}
