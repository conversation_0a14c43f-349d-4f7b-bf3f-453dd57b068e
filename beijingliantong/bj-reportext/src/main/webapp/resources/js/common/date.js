function getNowDate(){
	var date = new Date();
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	month = month < 10 ? "0" + month: month;
	var day = date.getDate();
	day = day < 10 ? "0" + day: day;
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	//alert(year + '-' + month + '-' + day  + ' ' + hour + ':' + minute + ':' + second);
	return year + '-' + month + '-' + day ;
}


function getInitDate(){
	var date = new Date();
	var year = date.getFullYear()+10;
	var month = date.getMonth() + 1;
	month = month < 10 ? "0" + month: month;
	var day = date.getDate();
	day = day < 10 ? "0" + day: day;
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	//alert(year + '-' + month + '-' + day  + ' ' + hour + ':' + minute + ':' + second);
	return year + '-' + month + '-' + day ;
}

function getAfterWeek(){
	var now = new Date();
	var date = new Date(now.getTime() - 7 * 24 * 3600 * 1000);
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	month = month < 10 ? "0" + month: month;
	var day = date.getDate();
	day = day < 10 ? "0" + day: day;
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	//alert(year + '-' + month + '-' + day  + ' ' + hour + ':' + minute + ':' + second);
	return year + '-' + month + '-' + day ;
}

function getAfterTime(number, isEnd) {
	var date = new Date();
	date.setTime(date.getTime() + (number == undefined || isNaN(parseInt(number)) ? 0 : parseInt(number)) * 86400000);
	var y = date.getFullYear();
	var m = date.getMonth() + 1;
	var d = date.getDate();
	return y + "-" + (m < 10 ? ('0' + m) : m) + "-" + (d < 10 ? ('0' + d) : d)+" "+(isEnd==0?"00:00:00":"23:59:59");
}

function getAfterDay(number, isEnd) {
	var date = new Date();
	date.setTime(date.getTime() + (number == undefined || isNaN(parseInt(number)) ? 0 : parseInt(number)) * 86400000);
	var y = date.getFullYear();
	var m = date.getMonth() + 1;
	var d = date.getDate();
	return y + "-" + (m < 10 ? ('0' + m) : m) + "-" + (d < 10 ? ('0' + d) : d);
}

//获取当前日期后number天的日期
function getAfter(number)
{
	var date = new Date();
	date.setTime(date.getTime() + (number == undefined || isNaN(parseInt(number)) ? 0 : parseInt(number)) * 86400000);
	var y = date.getFullYear();
	var m = date.getMonth() + 1;
	var d = date.getDate();
	return y + "-" + (m < 10 ? ('0' + m) : m) + "-" + (d < 10 ? ('0' + d) : d);
}
//根据业务类型和关键字模糊匹配业务列表
function getBusilistByLikeKey(busiType,busiName,callback){
	var data = new Object();
	data.busiType = busiType;
	data.bbusiName = busiName;
	
	$.ajax({ 
		type: 'POST',
		url: "/qualitycheckmgr/busiAnalyse/getBusiListByLikeKey.do", 
		data:data,
		success: function(respData){
			var busiData = respData.data;
			callback&&callback(busiData);
        },
        error: function(respData){
			var busiData = respData.data;
			callback&&callback(busiData);
        }
	});
}