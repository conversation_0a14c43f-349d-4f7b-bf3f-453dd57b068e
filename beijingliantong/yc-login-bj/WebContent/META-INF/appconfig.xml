<?xml version="1.0" encoding="UTF-8"?>
<config>

	 <param key="platformName" name="平台名称" type="string" description="" value="云呼平台" index="1"/>
	<!--  <param key="bgImgUrl" name="登录页面背景图片" type="string" description="" value="/yc-login/static/images/bg.jpg" index="-2"/>
	 <param key="leftImgUrl" name="登录页面左侧图片" type="string" description="" value="/yc-login/static/images/logo.png" index="-1"/> -->
	 <param key="copyInfo" name="底部自定义html内容" type="textarea" description="" value="" index="2"/>
	 <param key="loginCss" name="登录自定义样式" type="textarea" description="" value="" index="3"/>
	 <param key="noticeMsg" name="登录公告"  type="string" description="" value="" index="4"/>
	 <param key="langSelect" name="多语言选择"  type="radio" items="0:关闭,1:默认中文,2:默认英文"  description="0:关闭,1:默认中文,2:默认英文"  value="0" index="5"/>
	 
	 <param key="LOGIN_DYNAMIC_KEY" name="登录动态加密KEY" type="radio" items="0:关闭,1:开启" description="1：开启  0:关闭" value="1" index="10"/>
	 <param key="CHECK_BUSI_DATE" name="校验订购有效期"  type="radio" items="0:关闭,1:开启" description="1：开启  0:关闭" value="0" index="10"/>
	 
	 <param key="SSO_LOGIN_FLAG" name="单点登录接口"  type="radio" items="0:关闭,1:开启,2:盐加密开启"  description="0关闭 1开启 2 盐加密开启"  value="1" index="11"/>
	 
	 <param key="LOGIN_PAGE_URL" name="登录URL" type="string" description="登录URL,填写外链切换其他系统登录" value="" index="20"></param>
	 <param key="LOGIN_PAGE_PATH" name="登录页面路径" type="string" description="yc-login内置登录页面名称" value="sso.jsp" index="30"></param>
	 <param key="UPDATE_PWD_PAGE_URL" name="修改密码URL" type="string" description="默认yc-login内置修改密码页面" value="" index="40"></param>
	 <param key="LOGIN_SUCCESS_URL" name="登录成功跳转链接" type="string" description="登录成功跳转链接,例如：/cc-portal" value="" index="50"/>
	 <param key="LOGOUT_PAGE_URL" name="退出跳转链接" type="string" description="退出跳转链接" value="" index="60"/>
	 <param key="smsLoginMustHasMobile" name="是否支持双因子开启后短信号码为空" type="radio" items="0:否,1:是"  description="开启企业双因子后是否需支持不配置短信号码" index="70" value="0"></param> 
	 <param key="SEND_SMS_EXPIRATION" name="短信验证码有效期" type="string" description="短信验证码有效期，单位分钟，默认10分钟" value="10" index="80"/>
	 <param key="SESSION_EHCACHE_SHARE" name="启用Redis存储会话" type="radio" items="false:关闭,true:开启" description="true：开启 false:关闭" value="false" index="90"/>
	 <param key="IS_ENT_CENTER" name="多角色登录"  type="radio" items="0:否,1:是" description="1：是 0:否" value="0" index="100"/>
	 <param key="USER_LIST" name="需限制IP的账号" type="string" description="逗号隔开" value="" index="110"/>
	 <param key="LOGIN_IP" name="限制IP集合" type="string" description="IP地址,逗号隔开" value="" index="120"/>
	 <param key="ipLoginAuth" name="企业限制IP登录" type="textarea" description="格式:{entCode1:'ip',entCode2:'172.16.*,************'}" value="" index="130"/>
	 <param key="UNLIMITED_ACOUNT" name="登录IP无限制账号" type="textarea" description="登录账号,逗号隔开" value="" index="140"/>
	 
	 
</config>
