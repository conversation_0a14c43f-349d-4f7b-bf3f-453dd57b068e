package com.yunqu.yc.callproxy.customer.impl;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.callproxy.customer.Message;
import com.yunqu.yc.callproxy.model.CustomerRequestData;

/**
 * 4.2.12.咨询接口
 * <AUTHOR>
 *
 */
public class ConsultCallMessage extends Message {

	private String callType = "1";
	
	public ConsultCallMessage() {
		// TODO Auto-generated constructor stub
	}

	public ConsultCallMessage(String callType) {
		this.callType = callType;
	}
	

	@Override
	public JSONObject proxy(CustomerRequestData requestData) {
		JSONObject data = requestData.getData();
		JSONObject json = new JSONObject();
		json.put("messageId", "cmdConsultCall");
		json.put("called", data.getString("called"));
		json.put("userData", data.getString("userData"));
		json.put("displayNumber", data.getString("displayNumber"));
		if(StringUtils.isNotBlank(callType)){
			json.put("callType", callType);
		}
		if(data.get("userData") != null){
			json.put("userData", data.getJSONObject("userData"));
		}
		return json;
	}

}
