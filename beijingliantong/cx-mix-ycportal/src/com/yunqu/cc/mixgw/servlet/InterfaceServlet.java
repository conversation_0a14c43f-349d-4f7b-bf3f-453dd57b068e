package com.yunqu.cc.mixgw.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.util.EasyQueryUtil;
import org.apache.commons.collections.bag.SynchronizedSortedBag;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 *接口监听请求Servlet
 */
@WebServlet("/post/interface")
public class InterfaceServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;
	private EasyCache cache = CacheManager.getMemcache();
	protected Logger logger = CommonLogger.logger;
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		JSONObject res = new JSONObject();
		JSONObject json= JSON.parseObject(getRequestStr(req));
		String command=json.getString("command");
		if (StringUtils.isBlank(command)) {
			res.put("respCode", "003");
			res.put("respDesc", "请求参数错误");
			resp.setContentType("text/html; charset=UTF-8");
			resp.getWriter().print(res);
			return;
		}
		if ("recordCommand".equals(command)) { //通话记录查询数据备份到报表。含12345
			listRecordQuery(json);
		} else if ("isFirstCommand".equals(command)) {
		} else if ("keyCommand".equals(command)) {
		}
		resp.setContentType("text/html; charset=UTF-8");
		resp.getWriter().print(res);
	}
	/* 1.查出通话记录基本数据
	 * 2.根据通话记录主叫被叫区分本网异网，本地异地
	 * 3.入库需要字段：本网异网，本地异地，呼入呼出，手机号码，企业id，通话时长
	 */
	public List<EasyRow> listRecordQuery(JSONObject obj) {
		try {
			//获取该平台所有企业
			EasySQL sqlEnt=new EasySQL("select ENT_ID from CC_ENT");
			List<EasyRow> listEnt = EasyQueryUtil.getBusiQuery().queryForList(sqlEnt.toString());
			if (listIsNotNull(listEnt)) {
				listEnt.forEach((EasyRow rowEnt)->{
					String entId=rowEnt.getColumnValue("ENT_ID");
					logger.info("打印企业id："+entId);
					EasySQL sql = new EasySQL();
					sql.append(" SELECT *  FROM ycbusi_cc.cc_call_record ");
					sql.append(obj.getString("MONTH_ID")," WHERE  MONTH_ID = ?  ",false);
					sql.append(entId," WHERE  ENT_ID = ?  ",false);
					try {
						List<EasyRow> list  = EasyQueryUtil.getBusiQuery().queryForList(sql.toString());
						logger.info("cc_call_record数据entID："+entId+"size"+list.size());
						if (listIsNotNull(list)) {
							JSONArray array = new JSONArray();
							List<String> param=new ArrayList<String>();
							list.forEach((EasyRow row)->{
								//先区分呼入还是呼出，获取手机号码
								String paramSql="";
								String createCause=row.getColumnValue("CREATE_CAUSE");
								String monthID=row.getColumnValue("MONTH_ID");//月份
								String billTime=row.getColumnValue("BILL_TIME");//月份
								JSONObject jsOb=new JSONObject();
								String phone="";
								String cause="";//呼叫方式 01 呼入，02呼出，03席间
								if("6".equals(createCause)||"8".equals(createCause)){//呼出
									phone=row.getColumnValue("CALLED");
									jsOb=isTelBJ(phone);//被叫
									cause="02";
								}else if("1".equals(createCause)||"2".equals(createCause)||"4".equals(createCause)){//呼入
									phone=row.getColumnValue("CALLED");
									jsOb=isTelBJ(phone);//主叫
									cause="01";
								}else{//席间呼叫
									String caller=row.getColumnValue("CALLER");
									if(caller.length()>=13){//如果主叫等于13位手机号码证明是用户，否则席间呼叫本网异网，本地异地默认0
										jsOb=isTelBJ(row.getColumnValue("CALLER"));
									}
									phone=caller;
									cause="03";
								}
								//获取手机号码后，区分本网异网，本地异地
								paramSql="INSERT INTO LT_REPORT(ID,CREATE_CAUSE,REGION_PHONE,NETWORK_PHONE,ENT_ID,PHONE,CALL_TIME,DATA_TIME)" +
										" VALUES('"+ RandomKit.randomStr()+"','"+cause+"','"+jsOb.getString("REGION_PHONE")+"','"+jsOb.getString("NETWORK_PHONE")+"','"+entId+"','"+phone+"','"+billTime+"','"+monthID+"')";
								param.add(paramSql);//写完后写入该方法
								try {
									EasyQueryUtil.getBusiQuery().executeBatch(param);
								} catch (SQLException e) {
									logger.error("异常entID："+entId+"size"+list.size());
									e.printStackTrace();
								}
							});
						}
					} catch (SQLException e) {
						e.printStackTrace();
					}
				});
			}
		} catch (Exception e) {
		}
		return null;

	}

	/**
	 * 判断电话号码 本地、异地，本网、异网
	 * 本地01，异地02
	 * 01本网，02异网
	 * @param tel
	 * @return
	 */
	public JSONObject isTelBJ(Object tel) {
		JSONObject json=new JSONObject();
		String telStr = String.valueOf(tel);
		String hcode = telStr.substring(0, 7);
		String hcodeStr = cache.get("M_HCODE_" + hcode);
		//从缓存中查询地区编码
		JSONObject jsonObject = JSONObject.parseObject(hcodeStr);
		if (jsonObject != null) {
			String provinceCode = jsonObject.getString("provinceCode");
			if ("110000".equals(provinceCode)) {//本地（北京号码）
				json.put("LOCAL_NET","01");
			}else{//异地
				json.put("LOCAL_NET","02");
			}
			String vendor = jsonObject.getString("vendor");
			if ("2".equals(provinceCode)) {//本网（联通号码）
				json.put("LOCAL_NET","01");
			}else{//异地
				json.put("LOCAL_NET","02");
			}
		}
		return json;
	}
	public static void  main(String[] args) {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object=new JSONObject();
		object.put("123","2qweqeqe");
		object.put("1123","21");
		list.add(object);
		JSONObject object1=new JSONObject();
		object1.put("123","23111");
		object1.put("1123","21");
		list.add(object1);
		list.forEach((JSONObject)->{
				System.out.println(JSONObject.getString("123"));
			}
		);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
	}

	/**
	 * 判断该List是否为空
	 * @param list
	 * @return
	 */
	public static boolean listIsNull(List<?> list) {
		if(list == null || list.size()<1){
			return true;
		}
		return false;
	}
	/**
	 * 判断该List是否 不为�?
	 * @param list
	 * @return
	 */
	public static boolean listIsNotNull(List<?> list) {
		return !listIsNull(list);
	}

	/**
	 * 从请求体里获取数据
	 * @param request
	 * @return 请求json
	 */
	public String getRequestStr(HttpServletRequest request){
		StringBuilder dataBui = new StringBuilder(); //
		String line = null;
		BufferedReader reader = null;

		try{
			reader = request.getReader();
			while ((line = reader.readLine()) != null){
				dataBui.append(line);
			}
			//获取数据
			StringBuilder logBui = new StringBuilder();
			logBui.append("[MainServlet.getRequestStr] 获取请求数据").append(dataBui);
//		    CommonLogger.logger.info(logBui.toString());
		}catch(Exception e ){
//			CommonLogger.logger.error("[MainServlet.getRequestStr] error:"+e.getMessage(),e);
			return null;
		}finally{
			try {
				reader.close();
			} catch (IOException e) {
			}
			reader = null;
		}
		return dataBui.toString();
	}

}
