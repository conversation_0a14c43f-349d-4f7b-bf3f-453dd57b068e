package com.yunqu.cc.mixgw.base;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1"; //默认数据源名称(写)

	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2"; //默认数据源名称(写)

	public final static String DS_READ_NAME = "yc-read-ds"; //默认数据源名称(读)

	public final static String APP_NAME = "cx-mix-ycportal"; //应用

	private static AppContext context = AppContext.getContext("yc-api");

	/**
	 * 管理员
	 */
	public final static int ROLE_TYPE_1=1;
	
	/**
	 * 班长
	 */
	public final static int ROLE_TYPE_2=2;
	
	/**
	 * 坐席
	 */
	public final static int ROLE_TYPE_3=3;
	/**
	 * 图片类型
	 */
	public final static String FILE_TYPE_IMG = "1";
	/**
	 * 其他文件类型
	 */
	public final static String FILE_TYPE_OTHER = "2";

	/**
	 * 统计库名称
	 */
	public static String getStatSchema() {
		return context.getProperty("STAT_DB", "stat");
	}

	/**
	 * 管理员
	 */
	public final static int ROLE_TYPE_MANAGER = 1;

	/**
	 * 坐席
	 */
	public final static int ROLE_TYPE_AGENT = 3;
	/**
	 * 班长
	 */
	public final static int ROLE_TYPE_MONITOR = 2;
	/**
	 * 按键数据库
	 */
	public static final String RECIVE_KEY_DB ="t41_xfhlw_ycbusi1";
	
	/**
	 *	限制下载速度单位KB
	 */
	public static int getMaxRate() {
		return Integer.valueOf(AppContext.getContext(APP_NAME).getProperty("YC_PORTAL_MAXRATE", "1024"));
	}

	/**
	 * 导出数据量限制
	 * @return 默认3000
	 */
	public static int getLimitExportCount(){
		String count = AppContext.getContext(APP_NAME).getProperty("LIMIT_EXPORT_COUNT", "3000");
		if(StringUtils.isBlank(count)){
			return 3000;
		}
		try {
			return Integer.parseInt(count);
		} catch (Exception e) {
		}
		return 3000;
	}


	/**
	 * 离线导出最大长度
	 * @return 默认150000
	 */
	public static int getOffLineExportMaxCount(){
		String count = AppContext.getContext(APP_NAME).getProperty("OFF_LINE_EXPORT_MAX_COUNT", "150000");
		if(StringUtils.isBlank(count)){
			return 150000;
		}
		try {
			return Integer.parseInt(count);
		} catch (Exception e) {
		}
		return 150000;
	}
}
