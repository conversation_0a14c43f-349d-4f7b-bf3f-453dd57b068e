package com.yunqu.cc.mixgw.dao;


import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppDaoContext;

@WebObject(name="task")

public class TaskDao extends AppDaoContext {
	
	/**
	 * 查询人工外呼任务列表
	 * @return
	 */
	@WebControl(name="agentList",type=Types.DICT)
	public JSONObject agentList(){
		EasySQL sql=this.getEasySQL("select task_id,task_name from ").append(getTableName("CC_TASK")).append(" where 1=1 ");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
		sql.append(getBusiOrderId()," and TASK_TYPE = 1 ");
		sql.append("order by CREATE_TIME DESC");
		EasyQuery query=this.getQuery();
		query.setMaxRow(120);
		this.setQuery(query);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}	
	/**
	 * 查询外呼任务列表
	 * @return
	 */
	@WebControl(name="list",type=Types.DICT)
	public JSONObject list(){
		EasySQL sql=this.getEasySQL("select task_id,task_name from ").append(getTableName("CC_TASK")).append(" where 1=1 ");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID = ? ");
		sql.append("order by CREATE_TIME DESC");
		EasyQuery query=this.getQuery();
		query.setMaxRow(120);
		this.setQuery(query);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}	
}




