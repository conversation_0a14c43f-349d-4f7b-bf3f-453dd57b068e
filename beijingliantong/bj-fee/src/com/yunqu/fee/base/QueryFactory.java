package com.yunqu.fee.base;

import org.easitline.common.db.EasyQuery;

public class QueryFactory {

	/**
	 * 数据据源1
	 */
	private static EasyQuery writeQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WRITE_NAME);

	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);

	static{
		writeQuery.setLogger(LoggerFactory.getLogger());
		readQuery.setLogger(LoggerFactory.getLogger());
	}
	
	/**
	 * 获取数据源
	 * @param entId
	 * @return
	 */
	public static EasyQuery getWriteQuery(){
//		EasyQuery query = null;// EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WRITE_NAME);
//		if(query == null){
//			String url = Constants.getProperty("WRITE_URL", ""); //"******************************************";//132.90.465.230:807
//			String dbuser = Constants.getProperty("WRITE_USER", "");//"root";
//			String password = Constants.getProperty("WRITE_PWD", "");//"suntek";
//			String driverName = "com.mysql.cj.jdbc.Driver";
//			query = EasyQuery.getQuery(driverName, url, dbuser, password);
//			query.setLogger(LoggerFactory.getLogger());
//		}
//		return query;
		return writeQuery;
	}
	
	public static EasyQuery getReadQuery(){
		return readQuery;
	}
}
