body,html{
    position: relative;
    overflow: hidden;
    height: 100%;
}
.outer{
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.tree_outer{
    position: absolute;
    top: 0;
    left: 0;
    /* width: 420px; */
    height: 400px;
    display: none;
}
.txtInput{
    position: absolute;
    top: 0;
    left: 74px;
    padding-right: 4px;
    text-indent: 4px;
    width: 182px;
    height: 34px;
    /*text-align: center;*/
    line-height: 34px;
    border-radius: 4px;
    border: 1px solid #ccc;
    text-overflow:ellipsis;
    -o-text-overflow:ellipsis; 
    overflow:hidden;
    white-space:nowrap;
}
.tree_content{
    position: absolute;
    top: 0;
    left: 0;
    width: 420px;
    height: 360px;
    background: #fff;
    box-shadow:rgba(0,0,0,.05) 0px 0px 10px 1px; 
    
}

.layui-table-grid-down {
    position: absolute;
    top: 0;
    right: 0;
    width: 26px;
    height: 100%;
    padding: 5px 0;
    border-width: 0 0 0 1px;
    text-align: center;
    background-color: #fff;
    color: #999;
    cursor: pointer;
    display: none;
}
.layui-table-grid-down .layui-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -8px 0 0 -8px;
}
.layui-table-body td:hover .layui-table-grid-down{ display: block !important; }
.iconItem {
    float: left;
    margin-right: 10px;
    margin-top: 8px;
}
.iconItem .icon_conditionSet {
    display: block;
    width: 32px;
    height: 32px;
    border-radius: 32px;
    background: url(../../../images/icon_conditionSet.png) #35bc9b no-repeat center;
    cursor: pointer;
}