$(document).ready(function(){
	layui.use(['form','laydate'], function(){
		var form = layui.form;
		var laydate = layui.laydate;
		
		laydate.render({
			elem: '#startTime',
			value:getAfterTime2(-2,0)
		});	
		laydate.render({
			elem: '#endTime',
			value:getAfterTime2(0,1)
		});

	});

	/* 渲染任务名称下拉 */
	$.ajax({
		type:'post',  
		url:basePath+'/taskmanage/getTaskListForOption',
		data:{},
		cache:false,  
		async: false,
		dataType:'json',  
		success:function(data){  
			if(data.code==0){
				var taskList = data.taskList;
				let html = '<option value="">请选择任务名称</option>'
				for (let i = 0;i<taskList.length;i++) {
					html += `<option value="${taskList[i].TASK_ID}">${taskList[i].TASK_NAME}</option>`
				}
				$("#taskId").html(html);
			}
		}
	});

	intTable();
})

/* 初始化表格 */
function intTable(){
	layui.use('table', function(){
	var table = layui.table;
		table.render({
			elem: '#dataTable',
			url:basePath + '/mark/getMarkStatInfo',
			height:'full-100',
			cols: [[ //表头
				{field: 'STAT_DAY', title: '日期', width: "20%",align:'center'},
				{field: 'TASK_NAME', title: '任务名称', width: "20%",align:'center'},
				{field: 'ORIGINAL_TEXT', title: '原始文本', width: "20%",align:'center'},
				{field: 'MARK_TEXT', title: '标注文本', width: "20%",align:'center'},
				{field: 'MARK_NUM', title: '次数', width: "20%",align:'center', sort: true, templet:function(d){
	            	let link = `<span class="tableLink" onclick="dumpItem('${d.STAT_DAY}','${d.RECORD_ID}','${d.ORIGINAL_TEXT}','${d.MARK_TEXT}')">${d.MARK_NUM}</span>`
					return link;
	            }}
			]],
			page:true,
			id: 'dataTable',
			where: {
				startTime: getAfterTime2(-2,0).replaceAll("-",""),
				endTime: getAfterTime2(0,1).replaceAll("-","")
			}
		});
	});
}

/* 跳转质检明细 */
function dumpItem(statDay,recordId,originalText,markText){
	statDay = statDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")

	layer.open({
		type: 2,
		id: 'fullIframe',
		title: false,
		closeBtn: false,
		area: [$(window).width() + "px", $(window).height() + "px"],
		content: basePath + '/pages/checkmgr/markDetail/markDetail.jsp?recordId='+recordId + '&statDay=' + statDay + '&originalText=' + originalText + '&markText=' + markText,
		success: function (layero, index) {}
	});
}

/* 搜索表格 */
function reload(){
	if(!comparisonTime()) return false;

	//执行重载
	layui.table.reload('dataTable', {
		page: {
			curr: 1 //重新从第 1 页开始
		},
		where: {
			taskId: $("#taskId").val(),
			originalText: $("#originalText").val().trim(),
			markText: $("#markText").val().trim(),
			startTime: $("#startTime").val().replaceAll("-",""),
			endTime: $("#endTime").val().replaceAll("-","")
		}
	});
}

/* 导出 */
function exportItem(){
	if(!comparisonTime()) return false;

	let taskId = $("#taskId").val();
	let originalText = $("#originalText").val().trim();
	let markText = $("#markText").val().trim();
	let startTime = $("#startTime").val().replaceAll("-","");
	let endTime = $("#endTime").val().replaceAll("-","");

	window.open(basePath + '/mark/exportMarkStatInfo?taskId=' + taskId + "&originalText=" + originalText + "&markText=" + markText + "&startTime=" + startTime + "&endTime=" + endTime);
}

/* 时间比较 */
function comparisonTime() {
	var startTime = new Date($("#startTime").val());
	var endTime = new Date($("#endTime").val());
	var timeFlag = true;

	if($("#startTime").val() == '') {
		layui.layer.msg("请选择开始时间");
		timeFlag = false;
	}else if($("#endTime").val() == ''){
		layui.layer.msg("请选择结束时间");
		timeFlag = false;
	}else if(startTime.getTime() > endTime.getTime()) {
		layui.layer.msg("开始时间不能比结束时间大");
		timeFlag = false;
	}

	return timeFlag;
}