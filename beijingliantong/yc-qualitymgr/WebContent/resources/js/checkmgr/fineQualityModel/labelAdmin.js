$(function () {
	initLabel();
	layui.use('form', function () {
		var form = layui.form;
		form.render('checkbox');
	});
});

/**
 * 初始化标签信息
 * @returns
 */
function initLabel() {
	$.ajax({
		type: 'post',
		url: basePath + '/model/getModelLabelByModelId',
		data: {
			modelId: $("#modelId").val()
		},
		cache: false,
		async: false,
		dataType: 'json',
		success: function (data) {
			if (data.code === "0") {
				var result = data.result;
				if (result.length > 0) {
					$('#labelBody').html('');
					for (var i = 0; i < result.length; i++) {
						if (result[i].LABEL_NAME == "E") {
							$('#labelBody').append('<tr><td class="forbidCheckebox"><input type="checkbox" name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox" disabled=""></td><td><input type="hidden" name="labelName" value="E"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td id="endScore">X=0</td></tr>');
						} else if (result[i].LABEL_NAME == "F") {
							$('#labelBody').append('<tr><td class="forbidCheckebox"><input type="checkbox" name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox" disabled=""></td><td><input type="hidden" name="labelName" value="F"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td>一票否决(默认为F)</td></tr>');
						} else if (result[i].LABEL_NAME == "D") {
							if (result[i].PASS_TYPE == "1") {
								$('#labelBody').append('<tr><td><input type="checkbox"  checked=""  name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox"></td><td><input type="hidden" name="labelName" value="D"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td><input type="text" class="layui-input input_min" id="startScore" value="' + result[i].START_SCORE + '" onkeyup="onBlur(this)"> &lt; X &lt; <input type="text" id="endScore" class="layui-input input_max" value="' + result[i].END_SCORE + '" onkeyup="onBlur(this)"></td></tr>');
							} else {
								$('#labelBody').append('<tr><td><input type="checkbox" name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox"></td><td><input type="hidden" name="labelName" value="D"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td><input type="text" class="layui-input input_min" id="startScore" value="' + result[i].START_SCORE + '" onkeyup="onBlur(this)"> &lt; X &lt; <input type="text" id="endScore" class="layui-input input_max" value="' + result[i].END_SCORE + '" onkeyup="onBlur(this)"></td></tr>');
							}
						} else {
							if (result[i].PASS_TYPE == "1") {
								$('#labelBody').append('<tr><td><input type="checkbox" checked="" name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox"></td><td><input type="hidden" name="labelName" value="'+result[i].LABEL_NAME+'"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td><input type="text" class="layui-input input_min" id="startScore" value="' + result[i].START_SCORE + '" onkeyup="onBlur(this)"> &le; X &lt; <input type="text" id="endScore" class="layui-input input_max" value="' + result[i].END_SCORE + '" onkeyup="onBlur(this)"></td></tr>');
							} else {
								$('#labelBody').append('<tr><td><input type="checkbox" name="tableChoose" lay-skin="primary" lay-filter="tableCheckbox"></td><td><input type="hidden" name="labelName" value="'+result[i].LABEL_NAME+'"><input type="text" name="labelNameTxt" class="layui-input labelNameTxt" autocomplete="off" value="'+result[i].LABEL_CONFIG_NAME+'"></td><td><input type="text" class="layui-input input_min" id="startScore" value="' + result[i].START_SCORE + '" onkeyup="onBlur(this)"> &le; X &lt; <input type="text" id="endScore" class="layui-input input_max" value="' + result[i].END_SCORE + '" onkeyup="onBlur(this)"></td></tr>');
							}

						}
					}
				}
			}
		}
	});
}

/**
 * 保存更新标签信息
 * @returns
 */
function saveOrupdate() {
	var data = {};
	data.modelId = modelId;
	data.labelList = [];
	var list = $("#labelBody").find("tr");
	var lableConfigList=[];//分类名称数据
	for (var i = 0; i < list.length; i++) {
		var label = list[i];
		var startScore = $(label).find("#startScore").val();
		if (startScore == '' || typeof (startScore) == "undefined") {
			startScore = $(label).find("#startScore").text();
		}

		startScore = startScore.replace('=', '');
		startScore = startScore.replace('X', '');
		var endScore = $(label).find("#endScore").val();
		if (endScore == '' || typeof (endScore) == "undefined") {
			endScore = $(label).find("#endScore").text();
		}
		endScore = endScore.replace('=', '');
		endScore = endScore.replace('X', '');

		var labelName = $(label).find("input[name=labelName]").val();
		var lableConfigName= $(label).find("input[name=labelNameTxt]").val();
		var check = $(label).find("input[type=checkbox]").prop('checked');

		if($.trim(lableConfigName)===""){
			layer.msg("请输入分类名称");
			return;
		}
		if(lableConfigList.indexOf(lableConfigName)!==-1){
			layer.msg("分类名称不能重复");
			return;
		}else{
			lableConfigList.push(lableConfigName);
		}

		if (labelName !== "E" && labelName !== "F") {
			if (labelName == 'A') {
				if ($.trim(startScore) == "") {
					layer.msg("等分范围不能为空");
					return;
				}
				if ($.trim(startScore) != '100') {
					if ($.trim(endScore) == "") {
						layer.msg("等分范围不能为空");
						return;
					}
					if (parseInt(startScore) > parseInt(endScore)) {
						layer.msg("请输入正确的等分范围");
						return;
					}
				}
			} else {
				if ($.trim(startScore) == "" || $.trim(endScore) == "") {
					layer.msg("等分范围不能为空");
					return;
				}
				if (parseInt(startScore) > parseInt(endScore)) {
					layer.msg("请输入正确的等分范围");
					return;
				}
			}

		}

		var passType = check ? '1' : '0';
		data.labelList.push({
			labelName: labelName,
			startScore: startScore,
			endScore: endScore,
			passType: passType,
			lableConfigName:lableConfigName
		});
	}

	$.ajax({
		type: 'post',
		url: basePath + "/model/saveMdoellabel",
		contentType: "application/json",
		data: JSON.stringify(data),
		cache: false,
		dataType: 'json',
		success: function (res) {
			if (res.code === 0) {
				layer.msg("保存成功");
			} else {
				layer.msg(res.msg);
			}
		}
	});
	var index1 = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
	parent.layer.close(index1);
}

function onBlur(ob) {
	if ($(ob).is(':focus')) {
		var re = /[0-9]$/;
		if (ob.value != "") {
			if (!re.test(ob.value)) {
				layer.msg("只能输入正整数");
				ob.value = "";
				ob.focus();
			}
		}
	}
}