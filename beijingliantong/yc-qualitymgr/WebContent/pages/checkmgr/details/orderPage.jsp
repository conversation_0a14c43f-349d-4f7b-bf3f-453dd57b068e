<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@include file="/pages/common/taglibs.jsp"%>
<!-- 导入配置文件 -->
<%@include file="/pages/common/config.jsp"%>
<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport"
		content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<title>工单明细</title>
	<link rel="stylesheet" href="${ctx}/resources/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="${ctx}/resources/css/checkmgr/detail/orderPage.css">
	<script type="text/javascript" src="${ctx}/resources/js/common/jquery-3.3.1.min.js"></script>
	<script src="${ctx}/resources/layui/layui.js" charset="utf-8"></script>
</head>

<body>
	<input type="hidden" id="serialId" name="serialId" value="${param.serialId}">
	<div class="mainBox">
		<div class="panelBox">
			<div class="panelHead">来电工单信息</div>
			<div class="panelContent clearfix">
				<div class="formBox formBox2 clearfix">
					<div class="formItem">
						<div class="attr">工单编号</div>
						<div class="content" id="SERIAL_ID"></div>
					</div>
					<div class="formItem">
						<div class="attr">工单标题</div>
						<div class="content" id="ORDER_TITLE"></div>
					</div>
				</div>
				<div class="formBox formBox2 clearfix">
					<div class="formItem">
						<div class="attr">工单类型</div>
						<div class="content" id="ORDER_TYPE"></div>
					</div>
					<div class="formItem">
						<div class="attr">工单问题分类</div>
						<div class="content" id="QUESTION_WAY"></div>
					</div>
				</div>
				<div class="formBox formBox2 clearfix">
					<div class="formItem">
						<div class="attr">来电号码</div>
						<div class="content" id="USER_PHONE"></div>
					</div>
					<div class="formItem">
						<div class="attr">来电时间</div>
						<div class="content" id="CALL_TIME"></div>
					</div>
				</div>
				<div class="formBox formBox2 clearfix">
				<div class="formItem">
						<div class="attr">来电处理方式</div>
						<div class="content" id="DISPOSAL_TYPE"></div>
					</div>
					<div class="formItem">
						<div class="attr">坐席工号</div>
						<div class="content" id="AGNET_ID"></div>
					</div>
				</div>
				<div class="formBox formBox2 clearfix">
				<div class="formItem">
						<div class="attr">坐席姓名</div>
						<div class="content" id="AGNET_NAME"></div>
					</div>
					<div class="formItem">
						<div class="attr">所属班组</div>
						<div class="content" id="GROUP_NAME"></div>
					</div>
					</div>
				<div class="formBox formBoxTextarea clearfix">
					<div class="formItem">
						<div class="attr">工单主要内容</div>
						<div class="content" id="MAJOR_CONTENT"></div>
					</div>
				</div>
				<div class="formBox formBoxTextarea clearfix">
					<div class="formItem">
						<div class="attr">工单处理结果</div>
						<div class="content" id="HANDLE_RESULT"></div>
					</div>
				</div>
			</div>
		</div>
		<div class="panelBox">
			<div class="panelHead">回访工单信息</div>
			<div class="panelContent clearfix">
				<div class="formBox clearfix">
					<div class="formItem">
						<div class="attr"><span class="txt">回访是否成功</span></div>
						<div class="content" id="IS_VALID_VISIT"></div>
					</div>
				</div>
				<div id="visitSuccss" style="display:none;">
					<div class="formBox formBox2 clearfix">
						<div class="formItem">
							<div class="attr">是否联系</div>
							<div class="content" id="IS_CONTACT"></div>
						</div>
						<div class="formItem" style="padding-left: 160px;">
							<div class="attr" style="width: 160px;">是否联系（机器识别）</div>
							<div class="content" id="IS_CONTACT_ROB"></div>
						</div>
					</div>
					<div class="formBox formBox2 clearfix">
						<div class="formItem">
							<div class="attr">是否解决</div>
							<div class="content" id="IS_SOLVED"></div>
						</div>
						<div class="formItem" style="padding-left: 160px;">
							<div class="attr" style="width: 160px;">是否解决（机器识别）</div>
							<div class="content" id="IS_SOLVED_ROB"></div>
						</div>
					</div>
					<div class="formBox formBox2 clearfix">
						<div class="formItem">
							<div class="attr">是否满意</div>
							<div class="content" id="IS_SATISFACTION"></div>
						</div>
						<div class="formItem" style="padding-left: 160px;">
							<div class="attr" style="width: 160px;">是否满意（机器识别）</div>
							<div class="content" id="IS_SATISFACTION_ROB"></div>
						</div>
					</div>
					<div class="formBox formBoxTextarea clearfix">
						<div class="formItem">
							<div class="attr">不满意原因</div>
							<div class="content wordItemBox" id="SATISFACTION_REASON_KEY"></div>
						</div>
					</div>
					<div class="formBox formBoxTextarea clearfix">
						<div class="formItem">
							<div class="attr">不满意原因<br>（机器识别）</div>
							<div class="content wordItemBox" id="SATISFACTION_REASON_KEY_ROB"></div>
						</div>
					</div>
				</div>
				<div id="visitFail" style="display:none;">
					<div class="formBox formBoxTextarea clearfix">
						<div class="formItem">
							<div class="attr">未回访成功原因</div>
							<div class="content" id="INVALID_REASON"></div>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="panelBox">
			<div class="panelHead">大模型信息</div>
			<div class="panelContent clearfix">
				<div class="formBox clearfix">
					<div class="formItem">
						<div class="attr"><span class="txt">标题</span></div>
						<div class="content" id="BOT_TITLE"></div>
					</div>
				</div>
				<div class="formBox formBoxTextarea clearfix">
					<div class="formItem">
						<div class="attr">摘要</div>
						<div class="content" id="BOT_ABSTRACT"></div>
					</div>
				</div>
				
			</div>
		</div>
	</div>
</body>

<script>
layui.use('form', function(){
	var form = layui.form;
});
$(document).ready(function(){
	$(window).resize(function(){
		setTxtTitle();
	})
	initData();//加载数据
})

/* 超出一行加title */
function setTxtTitle(){
	$(".formBox:not(.formBoxTextarea) .content").each(function(){
		let txt=$(this).text();
		let width=$(this).width()+"px";
		$("body").append('<div class="testBox" id="testBox" style="width:'+width+'">'+txt+'</div>');
		if($("#testBox").height()>40){
			$(this).prop("title",txt);
		}
		$("#testBox").remove();
	})
}

/* 接收父页面加载数据 */
function initData(){
	$.ajax({
    type: 'post',
    url: basePath + '/details/queryAgentAssistantOrderInfo',
    data: {
			serialId: $("#serialId").val()
		},
    dataType: 'json',
    success: function (res) {
      if (res.code === "1") {
        let data = res.data;
				for(let key in data){
					/* 不满意原因、不满意原因（机器识别）由#号分开多个内容显示 */
					if(key==="SATISFACTION_REASON_KEY" || key==="SATISFACTION_REASON_KEY_ROB"){
						if(data[key]!==""){
							let list=data[key].split("#");
							for(let i=0;i<list.length;i++){
								$("#"+key).append('<div class="wordItem">'+list[i]+'</div>');
							}
						}
					}else{
						$("#"+key).text(data[key]);
					}
				}
				if(data.IS_VALID_VISIT==="是"){
					$("#visitSuccss").show();
					$("#visitFail").hide();

				}else{
					$("#visitSuccss").hide();
					$("#visitFail").show();
				}
				setTxtTitle();
      }else{
				layui.use('form', function(){
					var form = layui.form;
					layer.msg(res.msg);
				});
			}
    }
  });
}

</script>

</html>