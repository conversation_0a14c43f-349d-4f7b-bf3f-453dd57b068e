<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
<%@include file="/pages/common/taglibs.jsp"%>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>模型配置</title>
<link rel="stylesheet" href="${ctx}/resources/css/common/reset.css">
<link rel="stylesheet" href="${ctx}/resources/layui/css/layui.css"
	media="all">
<link rel="stylesheet"
	href="${ctx}/resources/css/checkmgr/analysismodel/addQItems.css">
<link rel="stylesheet"
	href="${ctx}/resources/css/checkmgr/model/addParagraphColor.css">
<script type="text/javascript"
	src="${ctx}/resources/js/common/jquery-3.3.1.min.js"></script>
<script type="text/javascript"
	src="${ctx}/resources/js/common/jquery.nicescroll.min.js"></script>
</head>
<body>
	<!-- secondFlag配置项里获取，用于新增质检项时“出现枪话现象”条件时抢话时间单位，0：秒；1：毫秒； -->
	<input type="hidden" id="secondFlag" value="${param.secondFlag}">
	<div class="outer theme_fresh">
		<form action="" class="layui-form" onsubmit=" return false;"
			style="height: 100%; display: block;">
			<div class="inner" id='outer' style="height: 100%;">
				<ul class="layui-timeline">
					<li class="layui-timeline-item"><i
						class="layui-icon layui-timeline-axis leftIcons">1</i>
						<div class="layui-timeline-content">
							<div class="inner_block">
								<p class="inner_title" style="display: inline-block;">分析项名称：</p>
								<input type="text" class="txtScore txtInput" id="itemName"
									name="itemName" placeholder="请输入质检项名称（最多20个字符）">
								<p class="inner_title title_theme">绑定主题：</p>
								<div class="selectBox">
									<select id="themeBind"></select>
								</div>
							</div>
						</div></li>
					<li class="layui-timeline-item" style="margin-top: 6px;"><i
						class="layui-icon layui-timeline-axis leftIcons">2</i>
						<div class="layui-timeline-content condition_content">
							<div class="inner_block">
								<p class="inner_title">定义条件</p>
								<input type="button" class="btnAdd btns theme_fresh_addNormal"
									value="添加条件">
							</div>
						</div></li>
					<li class="layui-timeline-item"><i
						class="layui-icon layui-timeline-axis leftIcons">3</i>
						<div class="layui-timeline-content">
							<div class="inner_block">
								<p class="inner_title">条件之间的逻辑关系</p>
							</div>
							<div class="inner_block layui-form-item">
								<div class="layui-inline">
									<input type="radio" name="condRelationType"
										lay-filter="condition" value="0" title="满足所有条件" checked="">
									<input type="radio" name="condRelationType"
										lay-filter="condition" value="1" title="满足所有条件之一"> <input
										type="radio" name="condRelationType" lay-filter="condition"
										value="2" title="不满足所有条件"> <input type="radio"
										name="condRelationType" lay-filter="condition" value="3"
										title="不满足条件之一即可">
								</div>
							</div>
						</div></li>
					<li class="layui-timeline-item"><i
						class="layui-icon layui-timeline-axis leftIcons">4</i>
						<div class="layui-timeline-content">
							<div class="inner_block">
								<p class="inner_title">是否检索条件</p>
							</div>
							<!-- <div class="inner_block layui-form-item">
                                <div class="layui-inline">
                                    <p class="inner_bottom_title inner_title_frist">加减分值：</p>
                                    <input type="radio" name="scoringType" lay-filter="points" value="0" title="加分" checked="">
                                    <input type="radio" name="scoringType" lay-filter="points" value="1" title="减分" >
                                    <input type="text" class="txtScore" placeholder="请输入分值" style="margin-top:2px" name="score">
                                </div>
                            </div> -->
							<div class="inner_block layui-form-item">
								<div class="layui-inline">
									<input type="radio" name="queryFlag" lay-filter="queryFlag"
										value="1" title="是" checked=""> <input type="radio"
										name="queryFlag" lay-filter="queryFlag" value="0" title="否">
								</div>
							</div>
						</div></li>
				</ul>
				<!-- <div style="position: fixed;bottom: 20;width: 30%;right:2%; text-align:right;">
                                <div class="layui-input-block" >
                                  <button class="layui-btn btnSave1" lay-submit="" lay-filter="add">提交</button>
                                  <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                </div> -->
			</div>
			<div class="inner_block">
				<input type="button" class="txtInput btnSave" value="保存"> <input
					type="button" class="txtInput btnReset" value="重置">
			</div>
		</form>
	</div>
	<script src="${ctx}/resources/layui/layui.js" charset="utf-8"></script>
	<!-- 关键字测试加高亮 -->
	<script type="text/javascript"
		src="${ctx}/resources/js/checkmgr/model/jquery.textSearch-1.0.js"></script>

	<script type="text/javascript"
		src="${ctx}/resources/js/checkmgr/customized/zjg/labelAdmin.js"></script>


</body>
</html>