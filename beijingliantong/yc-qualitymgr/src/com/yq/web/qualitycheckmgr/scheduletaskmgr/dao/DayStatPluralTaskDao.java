package com.yq.web.qualitycheckmgr.scheduletaskmgr.dao;

import java.util.List;
import java.util.Map;

public interface DayStatPluralTaskDao {

	/**
	 * 统计时间范围内的关键词命中日结数据
	 * @param range 时间范围
	 */
	void getKeywordDayStat(int range);

	/**
	 * 批量插入关键词命中日结统计数据
	 * @param schema 业务库
	 * @param list 要插入的数据集
	 */
	void addBatchKeywordDayStat(String schema, List<Map<String, Object>> list);

	/**
	 * 统计时间范围内的主题命中日结数据
	 * @param range 时间范围
	 */
	void getThemeDayStat(int range);
	
	/**
	 * 批量插入主题命中日结统计数据
	 * @param schema 业务库
	 * @param list 要插入的数据集
	 */
	void addBatchThemeDayStat(String schema, List<Map<String, Object>> list);

	/**
	 * 统计时间范围内的分数段标签命中日结数据
	 * @param range
	 */
	void getLabelDayStat(int range);
	
	/**
	 *  批量插入分数段标签命中日结统计数据
	 * @param schema 业务库
	 * @param list 要插入的数据集
	 */
	void addBatchLabelDayStat(String schema, List<Map<String, Object>> list);
	
	/**
	 * 统计时间范围内的各分数段质检量日结数据
	 * @param range 时间范围
	 */
	void getScoreDayStat(int range);
	
	/**
	 * 批量插入各分数段质检量日结统计数据
	 * @param schema 业务库
	 * @param list 要插入的数据集
	 */
	void addBatchScoreDayStat(String schema, List<Map<String, Object>> list);

	/**
	 * 统计时间范围内的各质检项命中数日结数据
	 * @param range
	 */
	void getQcItemDayStat(int range);
	
	/**
	 *  批量插入各质检项命中数日结统计数据
	 * @param schema 业务库
	 * @param list 要插入的数据集
	 */
	void addBatchQcItemDayStat(String schema, List<Map<String, Object>> list);
	
}
