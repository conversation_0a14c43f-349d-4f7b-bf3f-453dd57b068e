package com.yq.web.qualitycheckmgr.qualitymgr.model.service;

import java.util.List;
import java.util.Map;

import org.springframework.ui.ModelMap;

import com.alibaba.fastjson.JSONArray;
import com.yq.web.qualitycheckmgr.qualitymgr.entity.bean.EntityQueryDto;
import com.yq.web.qualitycheckmgr.qualitymgr.exparam.bean.ExParamQueryDto;
import com.yq.web.qualitycheckmgr.qualitymgr.model.bean.ItemInfoExportData;
import com.yq.web.qualitycheckmgr.qualitymgr.model.bean.QueryModelConfigDto;
import com.yq.web.qualitycheckmgr.qualitymgr.model.bean.WordOrSentenceExportData;

/**
 * 质检模型管理接口
 * 
 * <AUTHOR>
 *
 */
public interface ModelService {

	/**
	 * 根据企业id获取企业的所有质检模型信息
	 * 
	 * @param busiId
	 * @return
	 */
	public List<Map<String, Object>> getModelListAll(String busiId);

	/**
	 * 根据企业id获取质检模型
	 * 
	 * @param busiId
	 * @return
	 */
	public Map<String, Object> getModelByBusiId(String busiId, String entId);

	/**
	 * 保存质检模型
	 * 
	 * @param params
	 * @return
	 */
	public boolean saveAIModel(Map<String, Object> params);

	/**
	 * 更新质检模型
	 * 
	 * @param params
	 * @return
	 */
	public boolean updateAIModel(Map<String, Object> params);

	/**
	 * 删除 质检模型
	 * 
	 * @param params
	 * @return
	 */
	public boolean deleteAIModel(Map<String, Object> params);

	/**
	 * 查询记录数
	 */
	public int getAIModelNum(Map<String, Object> params);

	/**
	 * 查询质检模型列表
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getAIModelList(Map<String, Object> params);

	/**
	 * 获取质检模型明细信息
	 * 
	 * @param params
	 * @return
	 */
	public Map<String, Object> getChModelInfoById(Map<String, Object> params);

	/**
	 * 更新质检模型状态
	 * 
	 * @param params
	 * @return
	 */
	public boolean updateAIModelStatus(Map<String, Object> params);

	/**
	 * 查询是否存在访质检模型名称
	 * 
	 * @param params
	 * @return
	 */
	public boolean queryAIModelByName(Map<String, Object> params);

	/**
	 * 复制质检模型
	 * 
	 * @param params
	 * @return
	 */
	public boolean copyAIModel(Map<String, Object> params);

	/**
	 * 导出质检模型
	 * 
	 * @param modelIds
	 * @param entId
	 * @return
	 */
	public Map<String, Object> exportAIModel(Map<String, Object> params, ExParamQueryDto dto, EntityQueryDto queryDto,
			Map<String, String> paramsTypeMap, Map<String, String> entityTypeMap, Map<String, String> entityMap);

	/**
	 * 初始化标准质检模型
	 * 
	 * @param jsonArray
	 * @param model
	 * @return
	 */
	public boolean initModel(JSONArray jsonArray, ModelMap model);

	/**
	 * 导入质检模型
	 * 
	 * @param model
	 * @return
	 */
	public boolean importAIModel(Map<String, Object> model);

	/**
	 * 根据模型ID获取标签详情
	 * 
	 * @param modelId
	 * @param entId
	 * @return
	 */
	public List<Map<String, Object>> getModelLabelByModelId(String modelId, String entId);

	/**
	 * 保存更新质检模型标签
	 * 
	 * @param params
	 * @return
	 */
	public boolean saveMdoellabel(Map<String, Object> params);

	/**
	 * 根据企业ID获取参数key
	 * 
	 * @param params
	 * @return
	 */
	public Map<String, Object> getParamKeyByEntCode(String entCode);

	/**
	 * 获取模型列表作为下拉
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getModelListForOption(Map<String, Object> params);

	public Integer queryAIModelByModelId(Map<String, Object> params);

	/**
	 * 根据任务id渲染标签配置作为下拉
	 * 
	 * @param modelMap
	 * @return
	 */
	public List<Map<String, Object>> getLableInfoByTaskIdForSelect(String taskId, String entId);

	/**
	 * 获取模型配置的关键词或者话术信息
	 * 
	 * @param dto
	 * @return
	 */
	public WordOrSentenceExportData getWordOrSentenceInfo(QueryModelConfigDto dto);

	/**
	 * 导出质检项/分析项详情
	 * 
	 * @param dto
	 * @return
	 */
	public ItemInfoExportData exportItemInfo(QueryModelConfigDto dto);

	/**
	 * 通过模型id获取质检项列表作为下拉
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getItemInfoByModelId(Map<String, Object> params);

}
