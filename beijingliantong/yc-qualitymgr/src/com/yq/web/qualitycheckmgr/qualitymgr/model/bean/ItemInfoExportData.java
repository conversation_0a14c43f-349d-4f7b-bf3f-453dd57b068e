/**
@Title: ItemInfoExportData.java
@Description: TODO
<AUTHOR>
@date 2023-07-11 11:41:47
*/
package com.yq.web.qualitycheckmgr.qualitymgr.model.bean;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.fastjson.JSONArray;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: 质检项，分析项导出实体类
 * @Description: TODO
 * <AUTHOR>
 * @date 2023-07-11 11:41:47
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(25)
@AllArgsConstructor
@NoArgsConstructor
public class ItemInfoExportData implements Serializable {
	private static final long serialVersionUID = -1724149595972171378L;

	/**
	 * 导出质检项/分析项集合
	 */
	@ExcelProperty(index = 0)
	private String exportItemJson;

}
