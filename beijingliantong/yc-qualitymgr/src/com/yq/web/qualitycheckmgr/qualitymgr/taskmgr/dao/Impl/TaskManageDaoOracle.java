package com.yq.web.qualitycheckmgr.qualitymgr.taskmgr.dao.Impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

@Repository("taskManageDaoOracle")
public class TaskManageDaoOracle extends TaskManageDaoImpl {

//	@Override
//	public List<Map<String, Object>> getTaskList(Map<String, Object> params) {
//		
//		List<Object> paramList=new ArrayList<Object>();
//		
//		StringBuffer sql=new StringBuffer();
//		
//		StringBuffer wheresql = new StringBuffer();
//		
//		String page=(String)params.get("page");
//		String limit=(String)params.get("limit");
//		int start=(Integer.parseInt(page)-1)*Integer.parseInt(limit);
//		int end=Integer.parseInt(page)*Integer.parseInt(limit);
//		
//		String entId=(String)params.get("entId");
//		
//		if(entId!=null&&!"".equals(entId))
//		{
//			paramList.add(entId);
//			wheresql.append(" and t3.CORP_KEY = ?");
//		}
//		
//		String busId = (String) params.get("busId");
//		
//		if(busId!=null&&!"".equals(busId))
//		{
//			paramList.add(busId);
//			wheresql.append(" and t3.BUSI_ID = ?");
//		}
//		
//		String status = (String) params.get("status");
//		if (status != null && !"".equals(status)) {
//			paramList.add(status);
//			wheresql.append(" and t3.TASK_STATUS = ?");
//		}
//		String taskId = (String) params.get("taskId");
//		if (taskId != null && !"".equals(taskId)) {
//			paramList.add(taskId);
//			wheresql.append(" and t3.TASK_ID = ?");
//		}
//		
//		wheresql.append(" ORDER BY t3.TASK_ID DESC");
//		
//		
//		sql.append("SELECT t1.TASK_ID,t2.*,t1.ALL_NUM,t1.SUCCESS_NUM,t1.CHECK_NUM,t2.TASK_STATUS,t2.TASK_TYPE FROM (");
//		sql.append("SELECT b.TASK_ID,COUNT(q.BATCH_ID) ALL_NUM,");
//		sql.append("SUM(CASE WHEN ( (q.ASR_FLAG = 2 AND (q.AI_TASK_EXE_FLAG = 1 OR q.AI_TASK_EXE_FLAG = 2) ) OR q.ASR_FLAG = 1 ) THEN 1 ELSE 0 END) CHECK_NUM,");
//		sql.append("SUM( CASE WHEN q.AI_TASK_EXE_FLAG = 1 THEN 1 ELSE 0 END ) SUCCESS_NUM");
//		sql.append(" FROM (SELECT * FROM (SELECT t2.*,ROWNUM rn FROM (SELECT t3.TASK_ID FROM IQC_QC_BATCHTASK t3 WHERE 1=1"+wheresql+")t2 WHERE ROWNUM <= "+end+") WHERE rn>"+start);
//		sql.append(" )" + "b LEFT JOIN VIEW_IQC_QC_TASK q ON b.TASK_ID = q.BATCH_ID ");
//		sql.append(" GROUP BY b.TASK_ID) t1 LEFT JOIN IQC_QC_BATCHTASK t2 ON t1.TASK_ID = t2.TASK_ID WHERE 1=1");
//		
//		sql.append(" ORDER BY t2.TASK_ID DESC");
//		
//		String excesql = sql.toString().replace("IQC_QC_BATCHTASK", getTableName(entId, "IQC_QC_BATCHTASK"));
//		excesql = excesql.replace("VIEW_IQC_QC_TASK", getTableName(entId, "VIEW_IQC_QC_TASK"));
//		return queryForList(excesql,paramList.toArray());
//	}

	@Override
	public List<Map<String, Object>> getAllTaskList(Map<String, Object> params) {

		List<Object> paramList = new ArrayList<Object>();

		StringBuffer sql = new StringBuffer();

		String page = (String) params.get("page");
		String limit = (String) params.get("limit");

		String entId = (String) params.get("entId");
		sql.append(
				"SELECT * FROM (SELECT * FROM (SELECT t2.*,ROWNUM rn FROM (SELECT TASK_ID,TASK_NAME,TASK_STATUS,TASK_TYPE,MODEL_ID,AL_MODEL_ID,START_TIME,END_TIME,"
						+ "GROUP_ID,CALLED,CALLER,CALL_TYPE,AFTER_HOUR,BUSI_TYPE,DAY_START_TIME,DAY_END_TIME,JOB_NUMBER,DYNAMIC_HEADER"
						+ ",CREATE_TIME FROM IQC_QC_BATCHTASK t1 WHERE 1 = 1");

		if (entId != null && !"".equals(entId)) {
			paramList.add(entId);
			sql.append(" and t1.CORP_KEY = ?");
		}

		// 模型id
		String modelId = (String) params.get("modelId");
		if (modelId != null && !"".equals(modelId)) {
			paramList.add(modelId);
			sql.append(" and t1.MODEL_ID = ?");
		}

		String busId = (String) params.get("busId");

		if (busId != null && !"".equals(busId)) {
			paramList.add(busId);
			sql.append(" and t1.BUSI_ID = ?");
		}

		String status = (String) params.get("status");
		if (status != null && !"".equals(status)) {
			paramList.add(status);
			sql.append(" and t1.TASK_STATUS = ?");
		}
		String taskId = (String) params.get("taskId");
		if (taskId != null && !"".equals(taskId)) {
			paramList.add(taskId);
			sql.append(" and t1.TASK_ID = ?");
		}

		sql.append(" ORDER BY t1.TASK_ID DESC");

		if ((page != null && !"".equals(page)) && (limit != null && !"".equals(limit))) {
			int start = (Integer.parseInt(page) - 1) * Integer.parseInt(limit);
			int end = Integer.parseInt(page) * Integer.parseInt(limit);
			sql.append(")t2 WHERE ROWNUM <= " + end + ") WHERE rn >" + start + ") t3");
		} else {
			sql.append(" ) t2)) t3");
		}

		sql.append(
				" LEFT JOIN  (SELECT TASK_ID AS D_TASK_ID,SUM(TASK_ALL_NUM) as ALL_NUM,SUM(QC_NUM) as CHECK_NUM,SUM(QC_SUCCESS_NUM) as SUCCESS_NUM"
						+ " FROM IQC_STAT_TASK_INFO GROUP BY TASK_ID ORDER BY TASK_ID DESC) q ON t3.TASK_ID  = q.D_TASK_ID ORDER BY t3.TASK_ID DESC");
		String exsql = sql.toString().replace("IQC_STAT_TASK_INFO", getTableName(entId, "IQC_STAT_TASK_INFO"))
				.replaceAll("IQC_QC_BATCHTASK", getTableName(entId, "IQC_QC_BATCHTASK"));
		logger.info("func[TaskManageDaoOracle.getAllTaskList]sql-->" + exsql);
		logger.info("func[TaskManageDaoOracle.getAllTaskList]params-->" + paramList.toString());
		return queryForList(exsql, paramList.toArray());
	}

}
