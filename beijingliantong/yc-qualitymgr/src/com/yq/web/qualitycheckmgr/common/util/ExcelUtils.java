package com.yq.web.qualitycheckmgr.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.yq.web.qualitycheckmgr.common.log.CommonLogger;

/**
 * 
 * Excel工具类
 *
 */
public class ExcelUtils {

	public static Logger logger = CommonLogger.getLogger();

	public static final DataFormatter dataFormatter = new DataFormatter();

	/**
	 * 导出Excel
	 * 
	 * @param workbook  工作簿对象
	 * @param titles    列标题
	 * @param list      数据List
	 * @param sheetName Excel sheet名称
	 */
	public static void export(HSSFWorkbook workbook, String[] titles, List<Map<String, Object>> list,
			String sheetName) {
		try {
			// 在webbook中添加一个sheet,对应Excel文件中的sheet
			HSSFSheet hssfSheet = workbook.createSheet(sheetName);
			// 在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
			HSSFRow row = hssfSheet.createRow(0);
			int rowNum = 1;
			HSSFCell hssfCell = null;

			for (int i = 0; i < titles.length; i++) {
				// 列索引从0开始
				hssfCell = row.createCell(i);
				// 列名
				hssfCell.setCellValue(titles[i]);
			}

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum);
					rowNum++;
					for (int j = 0; j < titles.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(titles[j]) != null ? (String) map.get(titles[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
		} catch (Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}

	/**
	 * 导出Excel
	 * 
	 * @param workbook  工作簿对象
	 * @param titles    表格列标题
	 * @param keys      map取值的key
	 * @param list      数据List
	 * @param sheetName Excel sheet名称
	 */
	public static void export(HSSFWorkbook workbook, String[] titles, String[] keys, List<Map<String, Object>> list,
			String sheetName) {
		try {
			// 在webbook中添加一个sheet,对应Excel文件中的sheet
			HSSFSheet hssfSheet = workbook.createSheet(sheetName);
			// 在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
			HSSFRow row = hssfSheet.createRow(0);
			int rowNum = 1;
			HSSFCell hssfCell = null;

			for (int i = 0; i < titles.length; i++) {
				// 列索引从0开始
				hssfCell = row.createCell(i);
				// 列名
				hssfCell.setCellValue(titles[i]);
			}

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum);
					rowNum++;
					for (int j = 0; j < keys.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(keys[j]) != null ? (String) map.get(keys[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
		} catch (Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}

	/**
	 * 将导入的Excel的对应sheet解析为List对象
	 * 
	 * @param workbook  工作簿对象
	 * @param titles    列标题
	 * @param sheetName Excel sheet名称
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static List<Map<String, Object>> analysisExcel(Workbook workbook, String[] titles, String sheetName) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map = null;

		Sheet sheet = workbook.getSheet(sheetName);

		if (StringUtils.isEmpty(sheet)) {
			// 适配2.0升3.0 无标签配置表头的情况
			return list;
		}

		int rownum = sheet.getLastRowNum();

		for (int i = 1; i <= rownum; i++) {
			map = new HashMap<String, Object>();

			Row row = sheet.getRow(i);
			if (row != null) {
				for (int j = 0; j < titles.length; j++) {
					if (row.getCell(j) != null) {
//						row.getCell(j).setCellType(HSSFCell.CELL_TYPE_STRING);
//						map.put(titles[j], row.getCell(j).getStringCellValue());
						// 强转为字符串保存在map中
						map.put(titles[j], dataFormatter.formatCellValue(row.getCell(j)));
					}
				}

//				list.add(map);
				if (!map.isEmpty()) {
					list.add(map);
				}
			}
		}

		return list;
	}

	public static List<Map<String, Object>> analysisExcel(Workbook workbook, String[] keys, int sheetnum) {
		Sheet sheet = workbook.getSheetAt(sheetnum);
		return analysisExcel(workbook, keys, sheet.getSheetName());
	}

	public static List<Map<String, Object>> analysisExcel(Workbook workbook, List<String> keyList, int sheetnum) {
		String[] keys = new String[keyList.size()];
		for (int i = 0; i < keyList.size(); i++)
			keys[i] = keyList.get(i);
		return analysisExcel(workbook, keys, sheetnum);
	}

	public static List<Object> analysisExcelTitle(Workbook workbook, int sheetnum) {
		List<Object> list = new ArrayList();
		Sheet sheet = workbook.getSheetAt(sheetnum);
		Row row = sheet.getRow(0);
		if (row != null)
			for (int i = 0; i < row.getLastCellNum(); i++) {
				if (row.getCell(i) != null)
					list.add(dataFormatter.formatCellValue(row.getCell(i)));
			}
		return list;
	}

	/**
	 * 导出Excel
	 * 
	 * @param workbook  工作簿对象
	 * @param titles    表格列标题
	 * @param keys      map取值的key
	 * @param list      数据List
	 * @param sheetName Excel sheet名称
	 */
	public static void exportBySXSSFWorkbook(SXSSFWorkbook workbook, String[] titles, String[] keys,
			List<Map<String, Object>> list, String sheetName) {
		try {
			// 在webbook中添加一个sheet,对应Excel文件中的sheet
			SXSSFSheet hssfSheet = workbook.createSheet(sheetName);
			// 在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
			SXSSFRow row = hssfSheet.createRow(0);
			int rowNum = 1;
			SXSSFCell hssfCell = null;

			for (int i = 0; i < titles.length; i++) {
				// 列索引从0开始
				hssfCell = row.createCell(i);
				// 列名
				hssfCell.setCellValue(titles[i]);
			}

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum);
					rowNum++;
					for (int j = 0; j < keys.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(keys[j]) != null ? (String) map.get(keys[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
		} catch (Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}

	/**
	 * 合并单元格导出Excel
	 * 
	 * @param workbook  工作簿对象
	 * @param titles    表格列标题
	 * @param keys      map取值的key
	 * @param list      数据List
	 * @param sheetName Excel sheet名称
	 */
	public static void exportMergeBySXSSFWorkbook(SXSSFWorkbook workbook, List<String[]> titles, String[] keys,
			List<Map<String, Object>> list, String sheetName, int mergeIndex) {
		try {
			// 在webbook中添加一个sheet,对应Excel文件中的sheet
			SXSSFSheet hssfSheet = workbook.createSheet(sheetName);

			// Create a CellStyle object
			CellStyle style = workbook.createCellStyle();

			// Set horizontal alignment to center
			style.setAlignment(HorizontalAlignment.CENTER);

			// Set vertical alignment to center
			style.setVerticalAlignment(VerticalAlignment.CENTER);

			// 有关段落信息的行表头
			SXSSFRow phaseRow = hssfSheet.createRow(0);
			// 在sheet中添加表头第1行,注意老版本poi对Excel的行数列数有限制short
			SXSSFRow row = hssfSheet.createRow(1);
			int rowNum = 2;
			SXSSFCell hssfCell = null;
			for (int i = 0; i < titles.size(); i++) {
				String[] title = titles.get(i);
				for (int k = 0; k < title.length; k++) {
					// 列索引从0开始
					hssfCell = i == 0 ? phaseRow.createCell(k) : row.createCell(k);
					// 列名
					hssfCell.setCellValue(title[k]);
					hssfCell.setCellStyle(style);
				}
			}

			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum);
					rowNum++;
					for (int j = 0; j < keys.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(keys[j]) != null ? (String) map.get(keys[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
			// 合并单元格
			String[] phaseTitle = titles.get(0);
			// 记录每个段落的最开始的下标和上一个段落名称
			int phaseIndex = mergeIndex;
			String lastPhaseName = "";
			for (int i = 0; i < phaseTitle.length; i++) {
				if (i < mergeIndex) {
					// 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
					// 合并页面展示的表头
					hssfSheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
				} else {
					String phaseName = phaseTitle[i];
					if (!StringUtils.isEmpty(lastPhaseName) && !lastPhaseName.equals(phaseName)) {
						// 合并段落单元格 1 个段落下只有个质检项的不需要被合并
						if (phaseIndex != (i - 1)) {
							hssfSheet.addMergedRegion(new CellRangeAddress(0, 0, phaseIndex, i - 1));
						}
						phaseIndex = i;
					}
					lastPhaseName = phaseName;

					// 合并最后一个段落
					if (lastPhaseName.equals(phaseTitle[phaseTitle.length - 1]) && (phaseIndex + 1) != phaseTitle.length
							&& i == (phaseTitle.length - 1)) {
						hssfSheet.addMergedRegion(new CellRangeAddress(0, 0, phaseIndex, phaseTitle.length - 1));
					}
				}

			}
		} catch (Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}


	/**
	 * 合并单元格导出Excel,分批导出
	 *
	 * @param workbook  工作簿对象
	 * @param titles    表格列标题
	 * @param keys      map取值的key
	 * @param list      数据List
	 */
	public static void exportMergeBySXSSFWorkbook(SXSSFWorkbook workbook, List<String[]> titles, String[] keys,
												  List<Map<String, Object>> list, SXSSFSheet hssfSheet, int beginIndex) {
		try {

			// Create a CellStyle object
			CellStyle style = workbook.createCellStyle();

			// Set horizontal alignment to center
			style.setAlignment(HorizontalAlignment.CENTER);

			// Set vertical alignment to center
			style.setVerticalAlignment(VerticalAlignment.CENTER);
			int rowNum = 2;
			SXSSFRow row;

			if (beginIndex == 0) {
				// 有关段落信息的行表头
				SXSSFRow phaseRow = hssfSheet.createRow(0);
				// 在sheet中添加表头第1行,注意老版本poi对Excel的行数列数有限制short
				row = hssfSheet.createRow(1);
				SXSSFCell hssfCell = null;
				for (int i = 0; i < titles.size(); i++) {
					String[] title = titles.get(i);
					for (int k = 0; k < title.length; k++) {
						// 列索引从0开始
						hssfCell = i == 0 ? phaseRow.createCell(k) : row.createCell(k);
						// 列名
						hssfCell.setCellValue(title[k]);
						hssfCell.setCellStyle(style);
					}
				}
			}


			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum + beginIndex);
					rowNum++;
					for (int j = 0; j < keys.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(keys[j]) != null ? (String) map.get(keys[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
			hssfSheet.flushRows();

		} catch (Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}

	/**
	 * 合并单元格
	 * @param titles
	 * @param mergeIndex
	 * @param hssfSheet
	 */
	public static void mergerSheet(List<String[]> titles, int mergeIndex, SXSSFSheet hssfSheet) {
		// 合并单元格
		String[] phaseTitle = titles.get(0);
		// 记录每个段落的最开始的下标和上一个段落名称
		int phaseIndex = mergeIndex;
		String lastPhaseName = "";
		for (int i = 0; i < phaseTitle.length; i++) {
			if (i < mergeIndex) {
				// 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
				// 合并页面展示的表头
				hssfSheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
			} else {
				String phaseName = phaseTitle[i];
				if (!StringUtils.isEmpty(lastPhaseName) && !lastPhaseName.equals(phaseName)) {
					// 合并段落单元格 1 个段落下只有个质检项的不需要被合并
					if (phaseIndex != (i - 1)) {
						hssfSheet.addMergedRegion(new CellRangeAddress(0, 0, phaseIndex, i - 1));
					}
					phaseIndex = i;
				}
				lastPhaseName = phaseName;

				// 合并最后一个段落
				if (lastPhaseName.equals(phaseTitle[phaseTitle.length - 1]) && (phaseIndex + 1) != phaseTitle.length
						&& i == (phaseTitle.length - 1)) {
					hssfSheet.addMergedRegion(new CellRangeAddress(0, 0, phaseIndex, phaseTitle.length - 1));
				}
			}

		}
	}



	/**
	 * 导出Excel
	 */
	public static void exportBySXSSFWorkbook(String[] titles, String[] keys, List<Map<String, Object>> list, SXSSFSheet hssfSheet, int beginRow) {
		try {
			SXSSFRow row;
			int rowNum = 1 + beginRow;
			if (beginRow == 0) {
				// 在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
				row = hssfSheet.createRow(0);
				SXSSFCell hssfCell = null;

				for (int i = 0; i < titles.length; i++) {
					// 列索引从0开始
					hssfCell = row.createCell(i);
					// 列名
					hssfCell.setCellValue(titles[i]);
				}
			}

			for (int i = beginRow; i < list.size() + beginRow; i++) {
				Map<String, Object> map = list.get(i - beginRow);
				if (!map.isEmpty()) {
					row = hssfSheet.createRow(rowNum);
					rowNum++;
					for (int j = 0; j < keys.length; j++) {
						// 创建单元格，并设置值
						String cellValue = map.get(keys[j]) != null ? (String)map.get(keys[j]) : "";
						row.createCell(j).setCellValue(cellValue);
					}
				}
			}
			hssfSheet.flushRows();
		}
		catch(Exception e) {
			logger.error("ExcelUtils.export-->excel表空间创建失败" + e.getMessage(), e);
		}
	}

	/**
	 * 合并单元格
	 * 
	 * @param workbook
	 * @param mergeRowBegin 开始合并的行
	 * @param columns       需要合并的列
	 */
	public static <T> void mergeCells(List<T> obj, HSSFWorkbook workbook, int mergeRowBegin, Integer... columns) {
		HSSFSheet sheet = workbook.getSheet("sheet1");
		String[] str = new String[obj.size()];
		for (Integer column : columns) {
			int rowBegin = mergeRowBegin;
			// 获取指定列，不同行的单元格值
			try {
				for (int i = 0; i < obj.size(); i++) { // 遍历行
					HSSFRow row = sheet.getRow(rowBegin);
					HSSFCell cell = row.getCell(column);
					String value = cell.getStringCellValue();
					str[i] = value;
					rowBegin++;
				}
			} catch (Exception e) {
				logger.error("获取单元格值失败:" + e);
			}
			// 合并
			try {
				int index = 0;// 计数器
				for (int i = 0; i < str.length - 1; i++) {
					index++;// 1 2 3
					// 判断单元格值是否相同
					if (!str[i].equals(str[i + 1])) {
						if (1 - index != 0) { // 如果只有一个单元格，不合并
							CellRangeAddress cellAddresses = new CellRangeAddress(i + 1 - index + mergeRowBegin,
									i + mergeRowBegin, column, column);
							sheet.addMergedRegion(cellAddresses);
							index = 0;
						}

					}
					// 判断是否循环到最后一行
					if (i + 1 == str.length - 1) {
						if (index != 0) {// 如果只有一个单元格，不合并
							CellRangeAddress cellAddresses = new CellRangeAddress(i + 1 - index + mergeRowBegin,
									i + 1 + mergeRowBegin, column, column);
							sheet.addMergedRegion(cellAddresses);
						}
					}
				}
			} catch (Exception e) {
				logger.error("合并单元格失败:" + e);
			}
		}
	}

	/**
	 * 添加维度值
	 * 
	 * @param obj
	 * @param workbook
	 * @param rowBegin 行
	 * @param map      key:维度类型 value：需要合并的列
	 * @param <T>
	 * @return
	 */
//	public static <T> HSSFWorkbook replaceValue(List<T> obj, HSSFWorkbook workbook, int rowBegin,
//			Map<String, Integer> map) {
//		HSSFSheet sheet = workbook.getSheet("sheet1");
//		Set<Map.Entry<String, Integer>> entries = map.entrySet();
//		for (Map.Entry<String, Integer> entry : entries) {
//			int r = rowBegin;
//			// 医疗类别('0':住院，'1':门诊，'2':门特) MED_TYPE
//			if (AllDimension.MedType.getName().equals(entry.getKey())) {
//				for (int i = 0; i < obj.size(); i++) {
//					HSSFRow row = sheet.getRow(r);
//					HSSFCell cell = row.getCell(entry.getValue());
//					String value = cell.getStringCellValue();
//					cell.setCellValue("0".equals(value) ? MedType.MedType0.getName()
//							: "1".equals(value) ? MedType.MedType1.getName() : MedType.MedType2.getName());
//					r++;
//				}
//			}
//		}
//	}

}
