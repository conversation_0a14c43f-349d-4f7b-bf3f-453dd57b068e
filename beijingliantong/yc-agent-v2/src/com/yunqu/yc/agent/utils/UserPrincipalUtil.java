package com.yunqu.yc.agent.utils;

import java.lang.reflect.Method;
import java.security.Principal;

import javax.servlet.http.HttpServletRequest;
import javax.websocket.Session;

import org.easitline.common.core.sso.UserPrincipal;

import com.yunqu.yc.agent.base.AgentLogger;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

public class UserPrincipalUtil {

	public static YCUserPrincipal getUserPrincipal(HttpServletRequest request){
		YCUserPrincipal userPrincipal = null;
		Principal principal = request.getUserPrincipal();
	    if ((principal instanceof UserPrincipal)){
	    	userPrincipal = (YCUserPrincipal)principal;
	    }else{
	    	try {
	    		Class<?> c = Class.forName("org.easitline.common.core.sso.UserPrincipalFactory");
	    		Method[] methods = c.getMethods();
	    		for (Method method : methods) {
	    			if(method.getName().equals("getUserPrincipal")){
	    				return (YCUserPrincipal)method.invoke(c, principal);
	    			}
				}
	    		userPrincipal = (YCUserPrincipal)principal;
			} catch (Exception e) {
				AgentLogger.getLogger().error(e.getMessage(), e);
			}
	    }
	    return userPrincipal;
	}


	public static YCUserPrincipal getUserPrincipal(Session session){
		YCUserPrincipal userPrincipal = null;
		Principal principal = session.getUserPrincipal();
	    if ((principal instanceof UserPrincipal)){
	    	userPrincipal = (YCUserPrincipal)principal;
	    }else{
	    	try {
	    		Class<?> c = Class.forName("org.easitline.common.core.sso.UserPrincipalFactory");
	    		Method[] methods = c.getMethods();
	    		for (Method method : methods) {
	    			if(method.getName().equals("getUserPrincipal")){
	    				return (YCUserPrincipal)method.invoke(c, principal);
	    			}
				}
			} catch (Exception e) {
//				AgentLogger.getLogger().error(e.getMessage(), e);
			}
    		userPrincipal = (YCUserPrincipal)principal;
	    }
	    return userPrincipal;
	}
}
