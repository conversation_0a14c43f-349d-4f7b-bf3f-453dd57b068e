package com.yunqu.yc.agent.easyexcel.export;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.yunqu.yc.agent.base.Constants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.easitline.common.core.log.LogEngine;

/**
 * 数据处理对象
 * <AUTHOR>
 *
 */
public abstract class BaseRowHandler {
	private String language = "cn";
	/**
	 * EXCEL表头名
	 * @return
	 */
	public abstract String getName();
	
	/**
	 * EXCEL字段表头
	 * @return
	 */
	public abstract List<String> getHead();
	
	/**
	 * 解析数据
	 * @param map
	 * @return
	 */
	public abstract List<String> parseRow(Map<String, String> map);

	/**
	 * 开始执行
	 */
	public abstract void start();

	/**
	 * 导出完成
	 */
	public abstract void done();
	
	/**
	 * 处理数据
	 */
	public List<String> excuteData(Map<String, String> data) {
		List<String> list = null;
		try {
			list = parseRow(data);
		}catch (Exception e){
			this.getLogger().error(e.getMessage(),e);
		}
		return list;
	}

	public RowWriteHandler getRowWriteHandler() {

		RowWriteHandler rowWriteHandler = new RowWriteHandler() {


			private Map<String, Object> dictMap;

			@Override
			public void beforeRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Integer rowIndex,
					Integer relativeRowIndex, Boolean isHead) {

			}

			@Override
			public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
					Integer relativeRowIndex, Boolean isHead) {

			}

			@Override
			@SuppressWarnings("unchecked")
			public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
					Integer relativeRowIndex, Boolean isHead) {

				if (!isHead && dictMap != null) {
					for (Entry<String, Object> dict : dictMap.entrySet()) {
						Map<String, String> map = (Map<String, String>) dict.getValue();
						String value = row.getCell(Integer.parseInt(dict.getKey())).getStringCellValue();
						row.getCell(Integer.parseInt(dict.getKey())).setCellValue(map.get(value));
					}
				}

			}

			public Map<String, Object> getDictMap() {
				return dictMap;
			}

			public void setDictMap(Map<String, Object> dictMap) {
				this.dictMap = dictMap;
			}
		};
		return rowWriteHandler;
	}

	public AbstractColumnWidthStyleStrategy getColumnWidthStyleStrategy() {
		return null;
	}

	public HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
		// 头的策略
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		// 背景设置为红色
		headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

		// 字体
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setFontName("微软雅黑");// 设置字体名字
		headWriteFont.setFontHeightInPoints((short) 10);// 设置字体大小
		headWriteFont.setBold(true);// 字体加粗
		headWriteCellStyle.setWriteFont(headWriteFont); // 在样式用应用设置的字体;
		headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
//				headWriteCellStyle.setBorderTop(BorderStyle.NONE);
//				headWriteCellStyle.setBorderLeft(BorderStyle.NONE);
//				headWriteCellStyle.setBorderRight(BorderStyle.NONE);
		headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headWriteCellStyle.setShrinkToFit(true);
		// 内容的策略
		WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
		// 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
		// 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
		/*
		 * contentWriteCellStyle.setFillPatternType(FillPatternType.
		 * SOLID_FOREGROUND); // 背景
		 * contentWriteCellStyle.setFillForegroundColor(IndexedColors.
		 * GREY_40_PERCENT.getIndex());
		 * contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.
		 * CENTER);
		 * contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		 * //边框 contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
		 */
		// 文字
		WriteFont contentWriteFont = new WriteFont();
		// 字体大小
		contentWriteFont.setFontHeightInPoints((short) 10);
		contentWriteFont.setFontName("微软雅黑");// 设置字体名字
		contentWriteCellStyle.setWriteFont(contentWriteFont);
		// 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
		return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
	}
	
	/**
	 * 格式化表头
	 * @return
	 */
	public List<List<String>> getHeader() {
		List<List<String>> header = new ArrayList<List<String>>();
		for (String field : getHead()) {
			List<String> list = new ArrayList<String>();
			list.add(getName());
			list.add(getName());
			list.add(field);
			header.add(list);
		}
		return header;
	}

	public Logger getLogger(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);
	}

	public void setLanguage(String language){
		this.language = language;
	}

	public String getLang() {
		return language;
	}
	
}
