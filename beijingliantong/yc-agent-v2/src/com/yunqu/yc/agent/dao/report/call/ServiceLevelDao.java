package com.yunqu.yc.agent.dao.report.call;

import java.util.Map;

import com.yunqu.yc.agent.base.AgentLogger;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.StatDaoContext;
import com.yunqu.yc.agent.service.ServiceLevelSql;

@WebObject(name="serviceLevel")
public class ServiceLevelDao extends StatDaoContext{

		
	/**
	 * 整体服务水平报表
	 * @return
	 */
	
	@PreAuthorize(resId={"yc_reprot_v2_call_serviceLevel","yc_agent_report_serviceLevel"})
	@WebControl(name="serviceLevelsql", type=Types.LIST)
	public JSONObject ivrCallInList() {
//		isRes("");// 20220713注释，原因管理员角色查询，坐席服务水平报表无数据，suntek@xxx超级管理员查看正常.
		Map<String, String> CC_RPT_CALL_STAT = getYcstatTableByTaget("CC_RPT_CALL_STAT");

		String dateType = param.getString("dateType");
		//日期
		String startDate=" ";
		String endDate=" ";
		String limitDate;
		if (StringUtils.isBlank(dateType) || dateType.equals("day")){
			//按日统计
			limitDate = param.getString("date");
			if(StringUtils.notBlank(limitDate)) {
				String[] split = limitDate.split("~");
				startDate=split[0].trim().replace("-", "");
				endDate=split[1].trim().replace("-", "");
			}
		}else {
			//按月统计
			limitDate = param.getString("dateMonth");
			if(StringUtils.notBlank(limitDate)) {
				String[] split = limitDate.split("~");
				startDate=split[0].trim().replace("-", "")+"01";
				endDate=split[1].trim().replace("-", "")+"31";
			}
		}



		String ifNull = this.getNullMethod();
		
//		EasySQL sql = ServiceLevelSql.getService().serviceLevelList(startDate,endDate,this.param.getString("stType"),getEntId(),getBusiOrderId(), ifNull);
		try {
			ServiceLevelSql service = new ServiceLevelSql(this.getUserPrincipal());
			EasySQL sql = service.serviceLevelList2(startDate,endDate,dateType,ifNull);
			JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams());

			//return this.queryForPageList(sql.getSQL(), sql.getParams());

			//添加一个数据更新时间
			result.put("updateTime", CC_RPT_CALL_STAT.get("UPDATE_TIME"));
			return result;
		}catch (Exception e){
			AgentLogger.getLogger().error(e.getMessage(),e);
		}

		return new JSONObject();
	}
	
	
	
}