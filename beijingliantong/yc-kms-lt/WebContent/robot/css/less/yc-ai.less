.onfocus-item{border:1px solid #a5c7fe!important}.container{transform:none;transition:none;height:auto}.container.animate{transition:left .3s;transform:translate3d(0,0,0)}[data-region-selector].tc-15-dropdown-in-hd .tc-15-dropdown-menu{max-height:none}@-moz-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@-webkit-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@-o-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@keyframes nodeInserted{from{opacity:.99}to{opacity:1}}embed,object{animation-duration:.001s;-ms-animation-duration:.001s;-moz-animation-duration:.001s;-webkit-animation-duration:.001s;-o-animation-duration:.001s;animation-name:nodeInserted;-ms-animation-name:nodeInserted;-moz-animation-name:nodeInserted;-webkit-animation-name:nodeInserted;-o-animation-name:nodeInserted}@keyframes fade-in{0%{opacity:0}}@-webkit-keyframes fade-in{0%{opacity:0}}@keyframes fade-out{100%{opacity:0}}@-webkit-keyframes fade-out{100%{opacity:0}}@keyframes modal-in{0%{opacity:0;transform:scale(0.8,0.8);-moz-transform:scale(0.8,0.8);-webkit-transform:scale(0.8,0.8);-ms-transform:scale(0.8,0.8)}}@-webkit-keyframes modal-in{0%{opacity:0;transform:scale(0.8,0.8);-moz-transform:scale(0.8,0.8);-webkit-transform:scale(0.8,0.8);-ms-transform:scale(0.8,0.8)}}@keyframes modal-out{100%{transform:scale(0.8,0.8);-moz-transform:scale(0.8,0.8);-webkit-transform:scale(0.8,0.8);-ms-transform:scale(0.8,0.8);opacity:0}}@-webkit-keyframes modal-out{100%{transform:scale(0.8,0.8);-moz-transform:scale(0.8,0.8);-webkit-transform:scale(0.8,0.8);-ms-transform:scale(0.8,0.8);opacity:0}}.mask-in{animation:fade-in .2s;-webkit-animation:fade-in .2s}.mask-out{animation:fade-out .2s;-webkit-animation:fade-out .2s}.modal-in{animation:modal-in .15s;-webkit-animation:modal-in .15s}.modal-out{animation:modal-out .15s;-webkit-animation:modal-out .15s}.fade-in{animation:fade-in .3s;-webkit-animation:fade-in .3s}.fade-out{animation:fade-out .3s;-webkit-animation:fade-out .3s}.error-page{width:800px;text-align:center;margin:100px auto 0;padding:50px;font-size:18px;line-height:1.5;color:#999;border:1px solid #e0e0e0;background-color:#fff}.error-page h2{font-size:40px;font-weight:bold}@-moz-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@-webkit-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@-o-keyframes nodeInserted{from{opacity:.99}to{opacity:1}}@keyframes nodeInserted{from{opacity:.99}to{opacity:1}}embed,object{animation-duration:.001s;-ms-animation-duration:.001s;-moz-animation-duration:.001s;-webkit-animation-duration:.001s;-o-animation-duration:.001s;animation-name:nodeInserted;-ms-animation-name:nodeInserted;-moz-animation-name:nodeInserted;-webkit-animation-name:nodeInserted;-o-animation-name:nodeInserted}

.container{top: 10px;}
.qc-header-nav{}
.chat-section{
	max-width: 1220px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
        box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
}
.section-footer{
	position: absolute;
	z-index: 2;
	bottom: 1px;
	right: 1px;
	left: 1px;
	height: 30px;
}
.chat-section .section-header{
	background-color: #2C8FF3;height: 60px; 
	
    position: relative;

    .section-header-logo{
    	position: absolute;
    	display: inline-block;
    	left: 0;bottom: 0;
    }
}
.chat-section .section-content{height: calc( 100% - 60px)!important;}
.left-area{
	width: calc( 100% - 318px);
}
.right-area{
    box-sizing: border-box;
}
.container-smarty{background: none;}
.container-smarty .main{
        // background: linear-gradient(#12C9E9, #106fec);
}
.chat-section{background: none}

.ai-chat-btns{
	position: absolute;
	height: 30px;
	top:15px;
	right: 10px;
	z-index: 2;
	display: inline-block;
	.ai-chat-btn{
		margin-left: 5px;
		display: inline-block;
		width: 30px;
		height: 30px;
		// border:1px solid #ddd;
		border-radius: 50%;
		// background-color: #2277da;
		background-repeat: no-repeat;
		background-position: center;
        // box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.3);
		
		&:hover{ background-color: #006ba2;}

		&.resetchat{ background-image: url('../images/reset.png') }
		&.closechat{ background-image: url('../images/window_close.png') }
	}
}

.icon-helpless,.icon-helpful{vertical-align: middle!important;}

.question-todo{
	position: absolute;
	bottom: 101%;
	left: 2px;
	right: 2px;
	display: inline-block;
	border: 1px solid #ddd;
	border-radius: 2px;
	z-index: 2;
	background-color: #fff;
	ul{
		list-style: none;
		margin: 0;
		padding: 0;
		li{
			padding: 5px 10px;
			cursor: pointer;
			color:#333;
			a{
				color:#333;
				text-decoration: none;
			}
			&:nth-child(odd){
				background-color: #f5f5f5;
			}
			&:hover{
				a{color: #006eff;}
			}
		}
	}
}
.feedback-wrap .seporator{margin-top: 14px;}
.relative-warp{
    border-top: 1px solid #ddd;
    margin-top: 10px;
    padding-top: 4px;
}