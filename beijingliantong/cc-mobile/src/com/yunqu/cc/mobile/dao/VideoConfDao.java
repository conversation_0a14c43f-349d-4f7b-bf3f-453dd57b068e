package com.yunqu.cc.mobile.dao;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mobile.base.AppDaoContext;
import com.yunqu.cc.mobile.base.CommonLogger;

@WebObject(name = "videoConfDao")
public class VideoConfDao extends AppDaoContext {
	private Logger logger = CommonLogger.logger;

	@WebControl(name = "videoConfList", type = Types.LIST)
	public JSONObject videoConfList() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT T1.*");
		sql.append("FROM " + this.getTableName("CC_MEDIA_VIDEO_RECORD ") + " T1");
		sql.append("WHERE 1 = 1");
		sql.append(this.getEntId(), "AND T1.ENT_ID = ?");
		sql.append(this.getBusiOrderId(), "AND T1.BUSI_ORDER_ID = ?");
		//sql.appendLike(this.param.getString("userAcc"), " AND T1.CREATE_ACC LIKE ?");
		if(StringUtils.isNotBlank(this.param.getString("dateRange"))) {
			String [] dateArr = this.param.getString("dateRange").split(" ~ ");
			sql.append(dateArr[0].replaceAll("-", ""), "AND T1.DATE_ID >= ?");
			sql.append(dateArr[1].replaceAll("-", ""), "AND T1.DATE_ID <= ?");
		}
		sql.append("ORDER BY T1.BEGIN_TIME DESC");
		logger.info("[videoConfDao]->videoConfList sql:"+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name = "confFileList", type = Types.LIST)
	public JSONObject confFileList() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT T1.ID,T1.FILE_TYPE,T1.FILE_NAME,T1.START_TIME,T1.END_TIME,T1.LENS,T1.VIDEO_ACCT");
		sql.append("FROM " + this.getTableName("C_VIDEO_FILE") + " T1");
		sql.append("WHERE 1 = 1");
		sql.append(this.param.getString("confId"), "AND T1.VIDEO_CALL_RECORD = ?", false);
		sql.append("ORDER BY T1.START_TIME DESC");
		logger.info("[videoConfDao]->videoConfList sql:"+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
		return this.queryForList(sql.getSQL(), sql.getParams(),null);
	}
	
	
	@WebControl(name = "findVideo", type = Types.LIST)
    public JSONObject findVideo(){
		String chatSessionId = param.getString("chatSessionId");
		
		EasySQL sql = getEasySQL("select T1.CHANNEL_NAME,t1.AGENT_NAME,t3.ID,t3.CALL_TIME,t3.BEGIN_TIME,t3.END_TIME,t3.TOTAL_TIME,");
		sql.append("t3.CLEAR_CAUSE,t3.CALL_TYPE,t3.QC_STATE,VIDEO_PATH1 URL,VIDEO_PATH2 AGENT_URL,");
		sql.append("t3.START_TAG_TIME,t3.STOP_TAG_TIME from " + this.getTableName("CC_MEDIA_RECORD") + "  t1 ");
		sql.append("left join " + this.getTableName("CC_MEDIA_VIDEO_RECORD") + "   t3 on t1.SERIAL_ID  = t3.CHAT_SESSION_ID");
		sql.append("where t3.TOTAL_TIME>0");
		sql.append("and t3.CONF_ID is not null");
		sql.append(chatSessionId,"and t1.SERIAL_ID = ?",false);
		logger.info("[videoConfDao]->videoConfList sql:"+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
	    return queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}
	
}
