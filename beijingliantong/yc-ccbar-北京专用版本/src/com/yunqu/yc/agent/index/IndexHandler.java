package com.yunqu.yc.agent.index;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.listener.GlobalContextListener;
import com.yunqu.yc.agent.log.IndexLogger;
import com.yunqu.yc.agent.model.AgentEventModel;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.msg.notify.AbstractNotify;
import com.yunqu.yc.agent.msg.notify.CallEventNotify;

/**
 * 坐席工作量采集数据更新
 * <AUTHOR>
 *
 */

public class IndexHandler  implements Runnable {
	
	private static BlockingQueue<JSONObject> events0 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events1 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events2 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events3 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events4 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events5 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events6 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events7 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events8 = new LinkedBlockingQueue<JSONObject>();
	private static BlockingQueue<JSONObject> events9 = new LinkedBlockingQueue<JSONObject>();
	
	private BlockingQueue<JSONObject> events = null;
	private int index = 0;
	
	public  IndexHandler(int index){
		this.index = index;
		if(index == 1) events = events1;
		if(index == 2) events = events2;
		if(index == 3) events = events3;
		if(index == 4) events = events4;
		if(index == 5) events = events5;
		if(index == 6) events = events6;
		if(index == 7) events = events7;
		if(index == 8) events = events8;
		if(index == 9) events = events9;
		if(index == 0) events = events0;
	} 
	
	public void run(){
		IndexLogger.getLogger().info("[IndexHandler] init handler thread["+index+"]");
		try {
			Thread.sleep(30*1000);
		} catch (Exception ex) {
			IndexLogger.getLogger().error(ex,ex);
		}
		long _size = 0;
		while(GlobalContextListener.runState){
			try {
				JSONObject iccsObject = events.poll(20, TimeUnit.SECONDS);
				if(iccsObject == null) {
					continue;
				}
				_size++;
				if(_size > Integer.MAX_VALUE) _size = 0;
				if(_size%100==0)  IndexLogger.getLogger().info("[IndexHandler] Handler thread["+index+"] events size:"+events.size());
				String messageId = iccsObject.getString("messageId");
				if(messageId.startsWith("resp")){
					this.doRespEvent(iccsObject);
				}else{
					this.doEvtEvent(iccsObject);
				}
			} catch (Exception ex) {
				IndexLogger.getLogger().error(ex,ex);
				try {
					Thread.sleep(1000);
				} catch (Exception e) {
					// TODO: handle exception
				}
			}
		}
		IndexLogger.getLogger().info("[IndexHandler] destroy handler thread["+index+"]");
	}
	
	
	private void doEvtEvent(JSONObject iccsObject){
		
		String messageId = iccsObject.getString("messageId");
		String entId = iccsObject.getString("entId");
		String agentId = iccsObject.getString("agentId");
		
		IndexLogger.getLogger().info("[doEvtEvent]["+messageId+"] << "+iccsObject);
		
//		if("evtAltering".equalsIgnoreCase(messageId)){
//			if("1".equals(iccsObject.getString("createCause")) || "2".equals(iccsObject.getString("createCause"))){
//				this.getIndexModel(entId, agentId).alertingService();
//			}
//		}
		
		if("evtConnected".equalsIgnoreCase(messageId)){
			this.getIndexModel(entId, agentId).startService();
		}
		
		if("evtDisConnected".equalsIgnoreCase(messageId)){
			this.getIndexModel(entId,agentId).finishHold();
			AgentEventModel eventModel 	= AbstractNotify.getAgentEvent(iccsObject); 
			this.getIndexModel(entId, agentId).finishService(eventModel.getClearCause() ,eventModel.getCreateCause(),getBillTime(eventModel));
		}
		
		if("evtWorkNotReady".equalsIgnoreCase(messageId)){  //进入话后整理
			this.getIndexModel(entId, agentId).startAcw();
		}
		
		if("evtWorkReady".equalsIgnoreCase(messageId)){  //结束通话
			this.getIndexModel(entId,agentId).finishAcw(iccsObject.getString("createCause"));
		}
	}
	
	private IndexModel getIndexModel(String entId,String agentId){
		while(GlobalContextListener.runState){
			try {
				return IndexModel.getIndexModel(entId,agentId);
			} catch (Exception ex) {
				IndexLogger.getLogger().info("IndexModel.getIndexModel("+entId+","+agentId+") fail,cause:"+ex.getMessage(),ex);
				try {
					Thread.sleep(1000);
				} catch (Exception ex1) {
					// TODO: handle exception
				}
				continue;
			}
		}
		return null;
	}
	
	private IndexModel getIndexModel(EasyCalendar cal,String entId,String agentId){
		while(GlobalContextListener.runState){
			try {
				return IndexModel.getIndexModel(cal,entId,agentId);
			} catch (Exception ex) {
				IndexLogger.getLogger().info("IndexModel.getIndexModel("+entId+","+agentId+") fail,cause:"+ex.getMessage(),ex);
				try {
					Thread.sleep(1000);
				} catch (Exception ex1) {
					// TODO: handle exception
				}
				continue;
			}
		}
		return null;
	}
	
	private void doRespEvent(JSONObject iccsObject){
		
		String bizType = iccsObject.getString("bizType");
		String messageId = iccsObject.getString("messageId");
		String result = iccsObject.getString("result");
		
		IndexLogger.getLogger().info("[doRespEvent]["+messageId+"] << "+iccsObject);
		
		String entId = iccsObject.getString("entId");
		String agentId = iccsObject.getString("agentId");
		
		AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
		
		if("respCheckpoint".equalsIgnoreCase(messageId)){
			EasyCalendar cal = EasyCalendar.newInstance();
			cal.add(EasyCalendar.HOUR, -10);
			String readyMode = iccsObject.getString("readyMode");
			if(StringUtils.isBlank(readyMode)){
				readyMode = agentModel.getReadyMode();
			}
			if(StringUtils.isBlank(readyMode)){
				readyMode =  "notReady";
			}
			
			
			IndexModel newDayIndexModel = this.getIndexModel(cal,entId, agentId);
			
			//先完成昨日的计算
			this.getIndexModel(cal,entId, agentId).finishLogout(true);
			
			newDayIndexModel.reset();
			
			
			
//			//由于respCheckpoint的日期为前一天，需要更新后一天的签出时间
//			EasyCalendar newCal = EasyCalendar.newInstance();
//			newCal.add(EasyCalendar.HOUR, +10);
//			
//			//执行签入
//			this.getIndexModel(newCal,entId, agentId).startLogin(true);
//			//设置示忙模式
//			if("notReady".equals(readyMode)){
//				this.getIndexModel(entId, agentId).startNotReady(agentModel.getNotReadyReasonId());
//			}else{
//				this.getIndexModel(entId, agentId).startReady();
//			}
			return ;
		}
		
		
		
		// 如果登录成功，则把坐席信息缓存
		if ("000".equals(result) || "117".equals(result)  || "105".equals(result)) {
			
			//登出成功，则清理缓存。
			if("respLogout".equalsIgnoreCase(messageId)){
				this.getIndexModel(entId, agentId).finishLogout(false);
			}
			//签入成功，则缓存当前坐席信息，为30秒。
			if("respLogin".equalsIgnoreCase(messageId)){
				if("000".equals(result)){
					this.getIndexModel(entId, agentId).startLogin(false);
				}
			}
			
//			if ("respMakeCall".equalsIgnoreCase(messageId)) { // 如果是makecall回调通知的，就生产话单
//				this.getIndexModel(entId,agentId).startCall(iccsObject.getString("createCause"));
//			}
			
			//无界面坐席不处理状态相关的时间统计
			if(!"withBiz".equalsIgnoreCase(bizType)){
				IndexLogger.getLogger().info("[putEvent]["+messageId+"]["+agentId+"]["+bizType+"][index:"+index+"] -> 无界面坐席不做坐席工作量统计");
				return ;
			}
			
			if ("respChangeWorkMode".equalsIgnoreCase(messageId) || "respLogin".equalsIgnoreCase(messageId)) {
				this.getIndexModel(entId, agentId).startWorkMode(iccsObject.getString("workMode"));
			}

			if ("respReady".equalsIgnoreCase(messageId)) {
				this.getIndexModel(entId, agentId).startReady();
			}

			if ("respNotReady".equalsIgnoreCase(messageId)) {
				this.getIndexModel(entId, agentId).startNotReady(agentModel.getNotReadyReasonId());
			}
			
//			if ("respWorkReady".equalsIgnoreCase(messageId)) {
//				this.getIndexModel(entId, agentId).finishAcw(agentModel.getIccsObject().getString("createCause"));
//			}
			
			//保持成功
			if ("respHoldCall".equalsIgnoreCase(messageId)) {
				this.getIndexModel(entId, agentId).startHold();
			}
			//恢复
			if ("respRetrieveCall".equalsIgnoreCase(messageId)) {
				this.getIndexModel(entId, agentId).finishHold();
			}
			
//			if ("respTransferCall".equalsIgnoreCase(messageId)) {
//				this.getIndexModel(entId, agentId).startTransfer(iccsObject.getString("callType"));
//			}

		}
	}
	
	/**
	 * 
	 * @param iccsObject
	 * @return
	 */
	private static int getBillTime(AgentEventModel eventModel){
		EasyCalendar calendar 	= EasyCalendar.newInstance();
		//计费时长，从通话开始到通话结束
		int billTime = 0;
		if(CallEventNotify.isBlankDateTime(eventModel.getBillBeginTime()) || CallEventNotify.isBlankDateTime(eventModel.getEndTime()) ){
			//
		}else{
			try {//总的通话时间=接通进入IVR时间-坐席释放时间
				billTime = calendar.diff(eventModel.getBillBeginTime(),eventModel.getEndTime(), "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
			} catch (Exception ex) {
			}
		}
		
		if(billTime<0){
			billTime = 0 ;
		}
		return billTime;
	}
	
	public  static void putEvent(JSONObject iccsObject){
		String messageId = iccsObject.getString("messageId");
		String agentId = iccsObject.getString("agentId");
	    String bizType = iccsObject.getString("bizType");
		
		if(StringUtils.isBlank(messageId)) return;
		if(StringUtils.isBlank(agentId)) return;
		if("evtAgentState".equals(messageId)) return ;
		if("respQueryAgentInfo".equalsIgnoreCase(messageId)){
			return ;
		}

		int _index = Math.abs(agentId.hashCode())%9;
		IndexLogger.getLogger().info("[putEvent]["+messageId+"]["+agentId+"]["+bizType+"][index:"+_index+"] << "+iccsObject);
		
		try {
			if(_index == 0) events0.put(iccsObject);
			if(_index == 1) events1.put(iccsObject);
			if(_index == 2) events2.put(iccsObject);
			if(_index == 3) events3.put(iccsObject);
			if(_index == 4) events4.put(iccsObject);
			if(_index == 5) events5.put(iccsObject);
			if(_index == 6) events6.put(iccsObject);
			if(_index == 7) events7.put(iccsObject);
			if(_index == 8) events8.put(iccsObject);
			if(_index == 9) events9.put(iccsObject);
		} catch (Exception ex) {
			IndexLogger.getLogger().error(ex,ex);
		}
	}
	
	public static void main(String[] args) {
		EasyCalendar cal = EasyCalendar.newInstance();
		cal.add(EasyCalendar.HOUR, 10);
		System.out.println(cal.getDateTime("-"));
	}
	
}
