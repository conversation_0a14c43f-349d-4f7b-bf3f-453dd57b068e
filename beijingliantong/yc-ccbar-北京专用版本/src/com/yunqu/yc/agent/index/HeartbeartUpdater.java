package com.yunqu.yc.agent.index;

import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.listener.GlobalContextListener;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.ICCSLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.model.RequestDataV1;
import com.yunqu.yc.agent.server.CcbarSocket;
import com.yunqu.yc.agent.util.CacheUtil;

/**
 *  心跳线程
 *  
 *  心跳增加当前话机状态：
 *  
 *  使用哈希类型存放号码数据

	key  --ibac_电话号码
	
	field定义：
	
	passWord  --  密码
	
	routeAddr  --  ip:port
	
	status -- online|offline|ringing|talking    （判断是否online状态，需结合lastActiveTime字段，如果lastActiveTime超时，则是offline状态） 
	
	lastActiveTime  -- 最近一次注册时间戳  
	
	regCallID  -- 注册callID
	
	preRegCallID --记录旧的注册callid，以";"隔开 ，累积长度超过200字节会删最前面的callid
	
	isLock   --   是否多次注册失败后被锁定 1|0
	
	lockTime -- 锁定时间戳
 *  
 * <AUTHOR>
 *
 */

public class HeartbeartUpdater implements Runnable{

		@Override
		public void run() {
			
		    try {
		    	Thread.sleep(15*1000);
			} catch (Exception ex) {
				ICCSLogger.getLogger().error("<Heartbeart-thread>WorkerThread thread sleep error,cause:"+ex.getMessage(),ex);
			}
		    
			while(GlobalContextListener.runState){
				long timer = System.currentTimeMillis();
			    try {
					Set<String> agents = CcbarSocket.getOnlineAgents();
					if(agents.size()>0){
						CcbarLogger.getLogger().info("[DEBUG] 发送websocket连接坐席心跳,当前ccbar在线坐席数 -> "+agents.size());
						for(String agentId:agents){
							CcbarSocket socket = CcbarSocket.getSocket(agentId);
							if(socket == null) continue;
							socket.sendHeartbeat();
						}
					}
				} catch (Exception ex) {
					ICCSLogger.getLogger().error("<Heartbeart-thread> sendHeartbeat error,cause:"+ex.getMessage(),ex);
					CcbarLogger.getLogger().error("<Heartbeart-thread> sendHeartbeat error,cause:"+ex.getMessage(),ex);
				}
			    timer = System.currentTimeMillis() - timer;
			    if(timer>5*1000){
			    	CcbarLogger.getLogger().error("<Heartbeart-thread> sendHeartbeat over time("+timer+"ms)");
			    	ICCSLogger.getLogger().error("<Heartbeart-thread> sendHeartbeat over time("+timer+"ms)");
			    }
			    long sleepTime = 10*1000 - timer;
			    if(sleepTime>0){
			    	 try {
					    Thread.sleep(sleepTime);
					} catch (Exception ex) {
						CcbarLogger.getLogger().error("<Heartbeart-thread> WorkerThread thread sleep error,cause:"+ex.getMessage(),ex);
					}
			    }
			}
		}
}
