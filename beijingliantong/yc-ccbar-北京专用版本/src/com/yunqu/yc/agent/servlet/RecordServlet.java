package com.yunqu.yc.agent.servlet;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;

import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.EntContext;
import com.yunqu.yc.agent.log.CcbarLogger;

/**
 * 云趣话机上传本地录音文件，URL参数为file，格式：录音文件时间&&企业ID&&坐席工号&&话机号码&&录音文件名
 */
@WebServlet("/record")
public class RecordServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public RecordServlet() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		return;
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		String file = request.getParameter("file");
		CcbarLogger.getLogger().info("[record] << "+file);
		if(StringUtils.isBlank(file)){
			response.setStatus(404);
			response.getWriter().println("param[file] not found!");
			return;
		}
		
		String[] parts = StringUtils.split(file, "_");
		if(parts.length < 5){
			CcbarLogger.getLogger().info("[record] >> 参数[file]格式错误，参数分割[_]的个数少于5,file->"+file);
			response.setStatus(403);
			response.getWriter().println("param[file] format error!");
			return;
		}
		
		String path = Constants.getLocalRecordPath();
		if(StringUtils.isBlank(path)){
			CcbarLogger.getLogger().info("[record] >> 未配置话机上传录音文件存储路径，不处理话机上传的路径文件，file->"+file);
			return;
		}
		
		if(!path.endsWith("/")) path = path+"/";
		
		String datetime = parts[0];
		if(datetime.length()!=14){
			CcbarLogger.getLogger().info("[record] >> 录音文件时间["+datetime+"]格式错误,正确格式为：yyyyMMddHHmmss，file->"+file);
			return;
		}
		String dateId = datetime.substring(0,8);
		String entId = parts[1];
		EntContext context = EntContext.getContext(entId);
		if(StringUtils.isBlank(context.getEntName())){
			CcbarLogger.getLogger().info("[record] >> 无效的企业ID["+entId+"]，file->"+file);
			return;
		}
		path = path + File.separator + entId + File.separator + dateId + File.separator;
		
		File dir = new File(path);
		if(!dir.isDirectory()) dir.mkdirs();
		path = path + file;
		File targetFile = new File(path);
		FileOutputStream fos = new FileOutputStream(targetFile);
		try {
			InputStream is = request.getInputStream();
			int i = 0 ;
			byte[] arr = new byte[10240];
			while(true){
				i = is.read(arr);
				if(i == -1) break;
				fos.write(arr,0,i);
			}
			
		} catch (Exception ex) {
			CcbarLogger.getLogger().error(ex,ex);
		}
		try {
			fos.close();
		} catch (Exception ex) {
			CcbarLogger.getLogger().error(ex,ex);
		}
		
	}
	public static void main(String[] args) {
		String  file = "20220606155055$$企业ID$$8011$$115020170018003$$63dc56d1cf8e4382aeb96b63b7d44b71.wav";
		String[] parts = StringUtils.split(file, "$$");
		System.out.println(parts.length);
		for(int i = 0 ;i<parts.length;i++){
			System.out.println(parts[i]);
		}
		System.out.print("20220606155055".substring(0,8));
	}

}
