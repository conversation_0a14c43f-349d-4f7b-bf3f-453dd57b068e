<link rel="stylesheet" href="/yc-ccbar/voicehelper/v2/css/ccbar-vh.css">
<!-- <link rel="stylesheet" href="css/ccbar-vh.css"> -->
<style>
	.voicehelper-box{z-index: 1221;right: 38px;}
	.vh-popup{
		position: fixed;
		background-color: #fff;
		width: 80px;
		height: auto;
		border: 1px solid #ddd;
		text-align: center;
		z-index: 33;
	}

	.vh-popup-item{
		padding: 5px 10px;
		border-bottom: 1px solid #ddd;
	}

	.vh-switch.clicking:before{
        content: '';
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }

    .vh-showRecomSceneList{
    	border-bottom: 1px solid #ddd;
    	padding: 5px;
    }
    .vh-showRecomSceneList-item{
    	display: inline-block;
    	font-size: 14px;
    	color: #333;
    	padding: 3px 7px;
    	line-height: 1;
    	border: 1px solid #1379DE;
    	border-left: 3px solid #1379DE;
    	margin: 3px;
    	cursor: pointer;
    }

    #scenelist{
    	width: 100%;
    height: 30px;
    }

    .voicehelper-box .vh-navs-panel .vh-navs-result{
    	overflow: auto;
    }

    .vh-scene-nav-clear{
    	position: absolute;
    	z-index: 2;
    	background-color: red;
    	color: #fff;
    	font-size: 12px;
    	padding: 3px 7px;
    	display: none;
    	bottom: 0;
    	right: 0;
    	cursor: pointer;
    }

.voicehelper-box .vh-navs-panel{position: relative;}
.voicehelper-box .vh-navs-panel:hover .vh-scene-nav-clear{
	display: block;
}
</style>
<div id="voicehelperApp" class="voicehelper-box" @click="popup.top=0;popup.left=0;" >
	<div class="vh-popup" v-show="popup.top && popup.left" :style="{top:popup.top+'px',left:popup.left+'px'}" style="display: none;">
		<div class="vh-popup-item" @click="popupCommand('copy')">复制</div>
		<div class="vh-popup-item" @click="popupCommand('km')">知识库</div>
		<!-- <div class="vh-popup-item"  @click="popupCommand('order')">填单</div> -->
	</div>
	<div @click.stop="toggle" class="vh-switch" :class="{isInit:isInit}"></div>
	<div v-show="show"  class="voicehelper-box-content" style="display:none">
			<div v-if="navList.length>0 || isTreeInit" v-show="showTab" class="vh-tabs-panel">
			<div class="vh-tabs">
				<div class="vh-tabs-titles">
					<div v-if="isTreeInit" @click.stop="activeTab='navtree'" class="vh-tabs-title" :class="{active:activeTab == 'navtree'}">导航</div>
					<div @click.stop="activeTab=item.id" class="vh-tabs-title" v-for="item,index in navList" v-text="item.title" :class="{active:activeTab == item.id}"></div>
				</div>
				<div class="vh-tabs-contents">
					<div v-for="item,index in navList" :class="{active:activeTab == item.id}" class="vh-tabs-content" style="overflow:hidden"><iframe :src="item.url" frameborder="0"></iframe></div>
					<div v-if="isTreeInit" class="vh-tabs-content " :class="{active:activeTab == 'navtree'}">
						<div class="vh-navs-panel">
							<!-- <div @click="sceneLst = []" class="vh-scene-nav-clear">清空</div> -->
							<div class="vh-tree-scenelist">
								<select v-show="scene.list.length>0" @change="changeSceneSelect" v-model="scene.current" name="scenelist" id="scenelist">
									<option value="">清选择流程</option>
									<option v-for="item,index in scene.list" :value="item.sceneId">{{item.sceneName}}</option>
								</select>
							</div>
							<div v-show="scene.recomSceneList.length>0" class="vh-showRecomSceneList">
								<div v-for="item,index in scene.recomSceneList" @click="changeScene(item.sceneId)" class="vh-showRecomSceneList-item">{{item.sceneName}}</div>
							</div>
							<div class="vh-navs-tree">
	                        	<ul id="ccbarTreeBox" class="ztree"></ul>
							</div>
							<div ref="vhResult" id="ccbarVoiceHelperNavList" class="vh-navs-result">
								<!-- <div v-for="item,index in sceneLst">
									<div class="vh-sence-title">{{item.sceneName}}</div>
									<div v-for="navItem,navIndex in formatNavList(item.navigationList)" class="vh-sence" v-html="navItem"></div>	
								</div> -->
								
								<div v-show="item.labelContent && (index == 0 || (index-1>=0 && item.naviId != sceneLst[index-1].naviId)) " v-for="item,index in sceneLst">
									<div class="vh-sence-title" :style="{color:item.labelColor}">{{item.naviName}}</div>
									<div  class="vh-sence" v-html="item.labelContent"></div>	
								</div>
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</div>
		<div class="vh-asr-box">
			<!-- <div @click="showTab=!showTab"  class="vh-tab-switch"></div> -->
			<div class="vh-asr-panel">
				<!-- 面板切换 -->
				<div v-if="navList.length>0 || isTreeInit" @click="showTab=!showTab" :class="{active:showTab}" class="vh-toggle"></div>

				<div class="vh-p-header">
					<div class="vh-p-search">
						<input type="search" v-model="searchText" class="vh-p-search-input"/>
						<input type="button" class="vh-p-search-btn" value="搜索"></input>
					</div>
					<div class="vh-p-title">坐席助手</div>
				</div>
				
				<div class="vh-p-tools">
					<div class="flex-row">
						<label id="vhLangSwitch" class="vh-checkbox" style="display:none">
							<a href="javascript:;" class="voicehelper-switch-lang">
		                        <span>语种▽</span>
		                        <ul class="voicehelper-switch-lang-list" style="width:6em">
		                            <li data-voicehelper-lang="mandarin">普通话</li>
		                            <li data-voicehelper-lang="cantonese">粤语</li>
		                            <li data-voicehelper-lang="english">英语</li>
		                        </ul>
		                    </a>
						</label>
						<div class="flex-item" style="border-right:1px solid #ddd">
							
							<label class="vh-checkbox"><input type="checkbox" v-model="config.lock">锁定</label>
							<label class="vh-checkbox"><input type="checkbox" v-model="config.custom">只看客户</label>
							<label class="vh-checkbox"><input type="checkbox" v-model="config.order">实时填单</label>
							<label class="vh-checkbox"><input type="checkbox" v-model="config.fllow">知识跟随</label>
							<label class="vh-checkbox"><input type="checkbox" v-model="config.wave">播放器</label>
						</div>
						<a href="javascript:;" @click="msgList=[]" class="vh-tool-clear">清空</a>

					</div>
				</div>
				
				<div class="vh-p-info">
					<div class="vh-p-info-box">
						<div class="vh-p-info-val">{{callInfo.speedFlag || '--'}}</div>
						<div class="vh-p-info-text">语速过快</div>
					</div>
					<div class="vh-p-info-box">
						<div class="vh-p-info-val">{{callInfo.feelingFlag || '--'}}</div>
						<div class="vh-p-info-text">情绪异常</div>
					</div>
					<div class="vh-p-info-box">
						<div class="vh-p-info-val">{{callInfo.muteFlag || '--'}}</div>
						<div class="vh-p-info-text">静音</div>
					</div>
					<div class="vh-p-info-box">
						<div class="vh-p-info-val">{{callInfo.foreStallFlag || '--'}}</div>
						<div class="vh-p-info-text">抢话</div>
					</div>
					<div class="vh-p-info-box">
						<div class="vh-p-info-val">{{callInfo.outlineWord || '--'}}</div>
						<div class="vh-p-info-text">违规词</div>
					</div>
				</div>
				<div ref="msgBox" class="vh-p-content" :class="{onlyCust:config.custom}">
					
					<!-- 测试数据 -->
					<template v-for="item,index in msgList">
						<!-- 消息tips -->
						<div v-if="item.msgType=='tips'" data-msg-type="tips" class="vh-msg">
							<div class="vh-msg-content">{{item.data}}</div>
						</div>
						<!-- 呼叫信息 -->
						<div v-else-if="item.msgType=='connect'" data-msg-type="call" class="vh-msg">
							<div  class="vh-msg-call">
								<span data-type="start" class="vh-span vh-createcause">{{createCauseName(item.data.createCause)}}</span>|
								<span class="vh-span">{{item.data.caller}}<b>→</b>{{item.data.called}}</span>|
								<span class="vh-span">{{item.msgTime}}</span>
							</div>
						</div>
						<div v-else-if="item.msgType=='disconnect'" data-msg-type="call" class="vh-msg">
							<div class="vh-msg-call">
								<span data-type="end" class="vh-span vh-createcause">{{createCauseName(item.data.createCause)}}</span>|
								<span class="vh-span">{{item.data.caller}}<b>→</b>{{item.data.called}}</span>|
								<span class="vh-span">{{item.msgTime}}</span>
							</div>
						</div>
						<!-- 转写 --> 
						<div v-else-if="item.msgType=='asr' || item.msgType=='msg'" data-msg-type="asr" :sender="getSender(item.data.clientid)" class="vh-msg">
							<div class="vh-msg-time">{{item.data.chatTime}}</div>
							<div v-if="item.data.start && item.data.end && item.data.clientid!=1" @click="playVoice(item)" class="vh-msg-content-voice"><span class="play-btn">{{item|recordTime}}"</span></div>
							<div  class="vh-msg-content">
								<div  @contextmenu.prevent="rightClick(item.data.content)" @dblclick.stop="copyText(item.data.content)" class="vh-msg-content-text" v-html="asrContent(item.data)"></div>
								
							</div>
							<div v-if="item.data.start && item.data.end && item.data.clientid==1" @click="playVoice(item)" class="vh-msg-content-voice"><span class="play-btn">{{item|recordTime}}"</span></div>
							<!-- 槽位 -->
							<div class="v-h-msg-tags">
								<span v-for="slotItem,slotIndex in item.data.slotList" @contextmenu.prevent="rightClick(slotItem.slotValue)" @dblclick.stop="copyText(slotItem.slotValue)" class="vh-msg-tag">{{slotKeyMap(slotItem.slotKey)}}:{{slotItem.slotValue}}</span>
							</div>

							<!-- 违规词 -->
							<div v-if="item.data.outlineWord" class="vh-msg-outlineWord">{{item.data.outlineWord}}</div>
							<!-- 语速 情绪 等标签 -->
							<div class="v-h-msg-tags">
								<!-- 语速 -->
								<span data-type="speedFlag" :data-flag="item.data.wordSpeed" class="vh-msg-tag">{{formatSpeedFlag(item.data.speedFlag)}}</span>
								<!-- 情绪 -->
								<span v-if="item.data.feelingFlag == 1" data-type="feelingFlag" :data-flag="item.data.feelingFlag" class="vh-msg-tag">情绪异常</span>
							</div>
						</div>

						<!-- 知识导航 -->
						<div :sender="item.data ? getSender(item.data.clientid):'0'" style="text-align: center;">
							<div v-if="item.data.infoKey" class="vh-p-ex-box">
								<div class="vh-p-ex-box-title">知识跟随</div>
									<div></div>
								<div class="vh-p-ex-box-content" @contextmenu.prevent="rightClick(item.data.infoKey)" @dblclick.stop="copyText(item.data.infoKey)" v-html="item.data.infoKey"></div>
							</div>

							<div v-if="item.data.talkInfo" class="vh-p-ex-box">
								<div class="vh-p-ex-box-title">话术推荐</div>
									<div></div>
								<div class="vh-p-ex-box-content" @contextmenu.prevent="rightClick(item.data.talkInfo)" @dblclick.stop="copyText(item.data.talkInfo)" v-html="item.data.talkInfo"></div>
							</div>

							<div v-if="item.data.goldTalk && item.data.goldTalk!='[]'" class="vh-p-ex-box">
								<div class="vh-p-ex-box-title">金牌话术</div>
									<div></div>
								<div class="vh-p-ex-box-content" @contextmenu.prevent="rightClick(item.data.goldTalk)" @dblclick.stop="copyText(item.data.goldTalk)" v-html="item.data.goldTalk"></div>
							</div>
						</div>
					</template>
				</div>

				<div v-show="config.wave " class="vh-p-wave" style="border-top:1px solid #ddd;">
					<div class="flex-row">
						<div class="waveform-btns ">
							<a href="javascript:;" @click="wavePlay(waveState)" :data-type="waveState" class="waveform-btn waveform-btn-toggle abs-center"></a>
							<div v-show="waveLoading" class="waveform-loading abs-center">{{loadingPresent}}%</div>
						</div>
						<div id="waveform" class="flex-item" style="position: relative;">
							<div id="progress" class="progress loading">
						      <div class="progress-bar progress-bar-info progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
						        
						      </div>
						    </div>
						</div>
					</div>
					<div id="wave-timeline" style="margin-left:72px"></div>
				</div>
			</div>

		</div>	
	</div>
	
</div>

<!-- <script src="https://gw.yunqu-info.com/easitline-static/js/jquery.min.js"></script>
<script src="https://gw.yunqu-info.com/yc-ccbar/ccbar/ccbar.js"></script>
<script src="https://gw.yunqu-info.com/yc-ccbar/voicehelper/js/ccbar-voicehelper.v2.js"></script> -->
<script src="/yc-base/static/lib/wavesurfer/wavesurfer.js"></script>
<script src="/yc-base/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
<script src="/yc-ccbar/voicehelper/v2/js/vue.min.js"></script>
<script src="/yc-ccbar/voicehelper/v2/js/main.js"></script>
<!-- <script src="js/main.js"></script>