<template>
  <div class="flash_screen">
    <div class="header" :style="{backgroundImage: 'url('+banner+')',backgroundSize: 'cover'}">
      <div class="header_top">
        <img :src="logo" class="header_logo" />
        <div class="header_countdown" @click="skipHandle">{{ count }} 跳过</div>
      </div>
      <div class="title">
        <span style="color: #FBDE99">直达</span>您的资金需求
      </div>
    </div>
    <div>
      <img :src="logo2" class="footer" />

    </div>
  </div>
</template>

<script>
export default {
  name: "home",
  data() {
    return {
      count: "", //倒计时
      timer: null,
      banner:"",
      logo:"",
      logo2:"",
    };
  },
  mounted() {
    this.threeGo()
    this.banner = `/cc-video/apiConfVerify?action=toImg&entId=${this.$route.query.entId}&imgType=01`
    this.logo = `/cc-video/apiConfVerify?action=toImg&entId=${this.$route.query.entId}&imgType=02`
    this.logo2 = `/cc-video/apiConfVerify?action=toImg&entId=${this.$route.query.entId}&imgType=03`
  },
  methods: {
    threeGo() {
      const TIME_COUNT = 5;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            clearInterval(this.timer);
            this.timer = null;
            //跳转的页面写在此处
            this.$router.push({
              path: `/viewReview?entId=${this.$route.query.entId}`,
            });
          }
        }, 1000);
      }
    },
    skipHandle() {
      this.$router.push({
        path: `/viewReview?entId=${this.$route.query.entId}`,
      });
    },
  },
};
</script>

<style scoped lang="less">
.flash_screen {
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: hidden;
  .header {
    width: 375px;
    height: 520px;
    // background: url(https://kftest.df-finance.com.cn/cc-video/apiConfVerify?action=toImg&entId=2001&imgType=01) no-repeat;
    // background-size: cover;
    .header_top {
      display: flex;
      .header_logo {
        width: 100px;
        height: 25.56px;
        margin-left: 20px;
        margin-top: 20px;
      }
      .header_countdown {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 56px;
        height: 28px;
        border-radius: 1000px;
        background: rgba(0, 0, 0, 0.5);
        font-family: Roboto;
        font-size: 14px;
        font-weight: normal;
        line-height: 14px;
        letter-spacing: 0em;
        margin-top: 20px;
        margin-left: 180px;
        color: #ffffff;
      }
    }
    .title {
      width: 237px;
      height: 112px;
      margin-top: 47px;
      margin-left: 69px;
      font-family: YouSheBiaoTiHei;
      font-size: 64px;
      font-weight: normal;
      line-height: 56px;
      text-align: center;
      letter-spacing: 0em;
      text-shadow: 0px 2px 16px 0px rgba(0, 51, 128, 0.4);
      color: #ffffff;
    }
  }
  
}
</style>
