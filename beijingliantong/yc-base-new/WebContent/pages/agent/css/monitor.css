* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: #262626;
  font-size: 14px;
}
html,
body,
.agent-page {
  height: 100%;
  width: 100%;
}
.agent-page > .flex-row {
  height: 100%;
}
html {
  background: #F2F4F7;
}
body {
  padding: 20px;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.abs-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.flex {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.flex-row {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.flex-item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow: auto;
}
.agent-panel {
  background: #fff;
  margin-bottom: 20px;
}
.agent-panel .agent-panel-header {
  line-height: 48px;
  padding: 0 24px;
}
.agent-panel .agent-panel-title label input {
  vertical-align: middle;
  zoom: 1.3;
}
.agent-panel .agent-panel-title a {
  text-decoration: none;
  color: #708FF7;
}
.agent-panel .agent-panel-content {
  padding: 0 16px 16px 16px;
}
.agent-panel .agent-total {
  font-size: 32px;
  font-weight: bold;
  line-height: 48px;
  letter-spacing: 0px;
  /* 中性色/高强调 */
  color: #262626;
}
.agent-panel .agent-total .grid5 {
  padding: 24px;
}
.agent-panel .agent-total h5 {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  /* 中性色/高强调 */
}
.agent-panel .agent-total span {
  font-size: 14px;
}
.agent-panel .agent-box-detail {
  text-align: center;
}
.agent-panel .agent-box-detail .state-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  background: #E4E4E4;
  margin: 0 auto 5px;
}
.agent-panel .agent-box-detail .state-txt {
  height: 18px;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;
  /* 中性色/中强调 */
  color: #868686;
}
.agent-panel .agent-box-detail .state-txt2 {
  height: 22px;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  /* 中性色/高强调 */
  color: #262626;
}
.agent-panel .agent-box-info {
  margin-bottom: 20px;
  height: 48px;
}
.agent-panel .agent-box-info .agent-avatar {
  position: relative;
  border-radius: 50%;
  border: 1px solid #ddd;
  width: 48px;
  height: 48px;
  float: left;
  margin-right: 15px;
  background: linear-gradient(180deg, #868686 0%, rgba(134, 134, 134, 0.5) 100%);
}
.agent-panel .agent-box-info .agent-name {
  line-height: 24px;
  font-size: 16px;
}
.agent-panel .agent-box-info .agent-id {
  color: #868686;
  font-size: 12px;
  line-height: 18px;
}
.agent-panel .agent-box-info .agent-state {
  display: inline-block;
  border-radius: 4px;
  padding: 2px 8px;
  background: #868686;
  color: #fff;
  font-size: 12px;
  line-height: 18px;
  margin-left: 10px;
}
.agent-panel .agent-box-info .state-icon {
  height: 20px;
  font-size: 20px;
}
.agent-panel .agent-box-info .state-txt {
  width: 20px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;
  /* 中性色/中强调 */
  color: #868686;
}
.agent-panel .agent-box-info .state-txt2 {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  /* 中性色/高强调 */
  color: #262626;
}
.agent-panel .grid {
  height: 118px;
  border-radius: 4px;
  background: #EAF4FD url(../images/t-total.png) no-repeat right bottom;
  background-size: 100px 118px;
  margin: 0 8px;
}
.agent-panel .grid[data-type="online"] {
  background: #F0F3FE;
  background: #F0F3FE url(../images/t-online.png) no-repeat right bottom;
  background-size: 100px 118px;
}
.agent-panel .grid[data-type="idle"] {
  background: #EDF9E8;
  background: #EDF9E8 url(../images/t-idle.png) no-repeat right bottom;
  background-size: 100px 118px;
}
.agent-panel .grid[data-type="altering"] {
  background: #FEF4E5;
  background: #FEF4E5 url(../images/t-altering.png) no-repeat right bottom;
  background-size: 100px 118px;
}
.agent-panel .grid[data-type="talk"] {
  background: #FDEBEB;
  background: #FDEBEB url(../images/t-talk.png) no-repeat right bottom;
  background-size: 100px 118px;
}
.agent-panel .grid .grid-txt {
  font-size: 32px;
  font-weight: bold;
  line-height: 48px;
}
.agent-panel .agent-box {
  float: left;
  margin: 8px;
  padding: 20px;
  width: calc(20% - 16px);
    min-width: 280px;
  
  height: 184px;
  border-radius: 8px;
  opacity: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(0deg, rgba(134, 134, 134, 0) 0%, rgba(134, 134, 134, 0.3) 99%);
}
.agent-panel .agent-box .agent-avatar:after {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: url(../images/user.png) no-repeat center center;
}
.agent-panel .agent-box[data-state="online"] {
  background: #F0F3FE;
}
.agent-panel .agent-box[data-state="idle"] {
  background: linear-gradient(0deg, rgba(82, 196, 26, 0) 0%, rgba(82, 196, 26, 0.3) 99%);
}
.agent-panel .agent-box[data-state="idle"] .agent-avatar {
  background: linear-gradient(0deg, rgba(82, 196, 26, 0.5) 2%, #52C41A 100%);
}
.agent-panel .agent-box[data-state="idle"] .agent-avatar:after {
  background: url(../images/idle.png) no-repeat center center;
}
.agent-panel .agent-box[data-state="idle"] .state-icon {
  background: #D9F2CE;
}
.agent-panel .agent-box[data-state="idle"] .agent-state {
  background: #52C41A;
}
.agent-panel .agent-box[data-state="altering"] {
  background: linear-gradient(0deg, rgba(250, 153, 4, 0) 0%, rgba(250, 153, 4, 0.3) 99%);
}
.agent-panel .agent-box[data-state="altering"] .agent-avatar {
  background: linear-gradient(0deg, rgba(250, 153, 4, 0.5) 0%, #FA9904 100%);
}
.agent-panel .agent-box[data-state="altering"] .agent-avatar:after {
  background: url(../images/altering.png) no-repeat center center;
}
.agent-panel .agent-box[data-state="altering"] .state-icon {
  background: #FDE8C8;
}
.agent-panel .agent-box[data-state="altering"] .agent-state {
  background: #FA9904;
}
.agent-panel .agent-box[data-state="talk"] {
  background: linear-gradient(0deg, rgba(240, 56, 56, 0) 1%, rgba(240, 56, 56, 0.3) 98%);
}
.agent-panel .agent-box[data-state="talk"] .agent-avatar {
  background: linear-gradient(180deg, #F03838 0%, rgba(240, 56, 56, 0.5) 99%);
}
.agent-panel .agent-box[data-state="talk"] .agent-avatar:after {
  background: url(../images/talk.png) no-repeat center center;
}
.agent-panel .agent-box[data-state="talk"] .state-icon {
  background: #FBD5D5;
}
.agent-panel .agent-box[data-state="talk"] .agent-state {
  background: #F03838;
}
.agent-panel .label-checkbox {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
  width: 60px;
  height: 24px;
  border-radius: 4px;
  opacity: 1;
  color: #868686;
  background: rgba(134, 134, 134, 0.1);
  box-sizing: border-box;
  /* 中性色/中强调 */
  border: 1px solid #868686;
  text-align: center;
  font-size: 14px;
  line-height: 22px;
  overflow: hidden;
}
.agent-panel .label-checkbox input {
  display: none;
}
.agent-panel .label-checkbox:after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #868686;
  right: -15px;
  top: -15px;
  border-radius: 50%;
}
.agent-panel .label-checkbox:before {
  content: '√';
  position: absolute;
  width: 12px;
  height: 12px;
  font-size: 12px;
  right: 0;
  top: -4px;
  z-index: 2;
  color: #fff;
}
.agent-panel .label-checkbox[data-state="idle"] {
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid #52C41A;
  color: #52C41A;
}
.agent-panel .label-checkbox[data-state="idle"]:after {
  background-color: #52C41A;
}
.agent-panel .label-checkbox[data-state="altering"] {
  background: rgba(250, 153, 4, 0.1);
  border: 1px solid #FA9904;
  color: #FA9904;
}
.agent-panel .label-checkbox[data-state="altering"]:after {
  background-color: #FA9904;
}
.agent-panel .label-checkbox[data-state="talk"] {
  background: rgba(240, 56, 56, 0.1);
  border: 1px solid #F03838;
  color: #F03838;
}
.agent-panel .label-checkbox[data-state="talk"]:after {
  background-color: #F03838;
}
.agent-panel .label-checkbox.not-slt {
  color: #868686;
  background: #F2F2F2;
  border-color: #F2F2F2;
}
.agent-panel .label-checkbox.not-slt:after {
  display: none;
}
#layout-content {
  height: calc(100vh - 290px);
  overflow: auto;
}
