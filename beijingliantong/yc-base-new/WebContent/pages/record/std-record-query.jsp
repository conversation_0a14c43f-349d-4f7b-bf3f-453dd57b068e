<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>优秀录音查询</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" name="searchForm" id="searchForm"  data-toggle="render">
		<div class="ibox">
			<div class="ibox-title">
				<div class="form-group">
						<h5><span class="glyphicon glyphicon-search"></span> 优秀录音查询</h5>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">开始日期</span> 
							<input  name="startDate" onClick="WdatePicker({})" id="startDate" size = "12" class="form-control input-sm Wdate">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">结束日期</span> 
							<input  name="endDate" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\',{d:0});}',maxDate:'#F{$dp.$D(\'startDate\',{d:14});}'})" size = "12"  class="form-control input-sm Wdate">
						</div>
					 <div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="StdRecord.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				    </div>
				 </div>
			</div>
			<div class="ibox-content">
				<table  data-mars="agent.stdRecordList" data-auto-fill="10" class="table table-auto  table-bordered table-hover table-condensed text-c"  id="tableHead">
					<thead>
						<tr>
							<th class="text-c">日期</th>
							<th class="text-c">坐席工号</th>
							<th class="text-c">坐席姓名</th>
							<th class="text-c">通话时长</th>
							<th class="text-c">计费时长（分钟）</th>
							<th class="text-c">标准话术</th>
							<th class="text-c">语言表达能力</th>
							<th class="text-c">营销能力</th>
							<th class="text-c">备注</th>
							<th class="text-c">录音</th>
							<th class="text-c">所属任务</th>
						</tr> 
					</thead>
					<tbody id="dataList"></tbody>
				</table>
				<div class="row paginate">
	                <jsp:include page="/pages/common/pagination_more.jsp"/>
	            </div> 
			</div>
		</div>
	</form>
					<script id="list-template" type="text/x-jsrender">
					{{for list}}
							<tr>
							<td>{{cutText:CREATE_TIME 12 ''}}</td>
							<td>{{:AGENT_PHONE}}</td>
							<td>{{:AGENT_NAME}}</td>
							<td>{{clock:BILL_TIME}} </td>
							<td>{{:FEE_TIME_60}} </td>
							<td>{{:SPEECHCRAFT_SCORE}}分</td>				
							<td>{{:LANG_SCORE}}分</td>
							<td>{{:SALE_SCORE}}分 </td>
							<td title="{{:RECODE_DESC}}">{{cutText:RECODE_DESC 20}}</td>		
							<td>
                                {{if RECORD_FILE}}
                                    <a href="javascript:void(0)" onclick="StdRecord.recoredListener('{{:RECORD_ID}}')">播放录音</a>
                                {{/if}}
                            </td>
							<td title="{{:TASK_NAME}}">{{cutText:TASK_NAME 20}}</td>		
							</tr>
						{{/for}}					 
					 </script>
</EasyTag:override>

<EasyTag:override name="script">

<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript">
	
		jQuery.namespace("StdRecord");
	
		StdRecord.loadData=function(){
			$("#searchForm").searchData();
		}
		
		StdRecord.recoredListener = function(serialId) {
		    popup.layerShow({type:2,title:'播放录音',offset:'20px',area:['700px','380px']},"${ctxPath}/pages/record/record-play.jsp",{serialId:serialId});
		}
		$.views.converters('clock',function(time){
			return formatClock(time);
			function formatClock(time) {
				if(time == undefined || time == null || time=='') return "0s";
				time = parseInt(time);
				var h = Math.floor(time/3600);
				var m = Math.floor(time%3600/60);
				var s = time%60;
				m = m<10?'0'+m:m;
				s = s<10?'0'+s:s;

				return h+":"+m+":"+s;
			}
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>