define(['jquery'], function($) {
	var skinControl = {
		color:['#0d4782','#3cf','#2cc','#4ca'],
		init:function(key,callback){
			var _color =getLS('SKIN');
			if(_color){
				setBG(_color);
			}
			var temp = '';

			for (var i = 0; i < skinControl.color.length; i++) {
				temp+= '<label data-color="'+skinControl.color[i]+'" class="skin-color-picker" style="display:inline-block;width:20px; height:20px; margin:5px; background:'+skinControl.color[i]+'"></label>'
			};
			$('#_skin').html(temp);
			$(document).on('click','.skin-color-picker',function(event){
				var color= $(this).data('color');
				setLS('SKIN',color);
				setBG(color);
			});
		},
		removeEvent:function(key){

		}
	};

	function setBG(color){
		$("#portal-header .navbar-default").css({
			'background-color': color
		});
	}

	function getLS(key){

		var strStoreDate = window.localStorage? localStorage.getItem(key): Cookie.read(key);
		return strStoreDate;
	}
	var Cookie = {
		read:function(key){},
		write:function(key,value){}
	}
	function setLS(key,value){
		if (window.localStorage) {
		    localStorage.setItem(key,value);	
		} else {
		    Cookie.write(key,value);	
		}
	}

	return {
		skinControl : skinControl
	}
});