<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>折线统计图</title>
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
  </head>

  <body>
    <div id="sdaa">
      <el-date-picker
        v-model="value1"
        type="date"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        size="small"
        @change="getData"
      >
      </el-date-picker>
      <div id="main" style="width: 100%; height: 90%"></div>
    </div>
  </body>
  <script>
    var app = new Vue({
      el: "#sdaa",
      data: {
        myChart: "",
        xData: [],
        yData: [],
        value1: "",
      },
      methods: {
        getData() {
          let _this = this;
          yq.remoteCall(
            "/cx-mix-base/webcall?action=statistic.getAppCallResStat",
            { date: _this.value1 }
          ).then((res) => {
            if (res.data?.length) {
              res.data.map((item) => {
                _this.xData.push(item.HOUR_ID);
                _this.yData.push(item.IVR_RES_COUNT);
              });
            } else {
              _this.xData = [""];
              _this.yData = [""];
            }
            let option = {
              title: {
                text: "话路并发量趋势图",
                itemGap: 15,
              },
              tooltip: {
                trigger: "axis",
              },
              grid: {
                top: "14%",
                left: "3%",
                right: "4%",
                bottom: "1%",
                containLabel: true,
              },
              xAxis: {
                type: "category",
                name: "小时",
                data: _this.xData,
              },
              yAxis: {
                type: "value",
                name: "占用数",
              },
              series: [
                {
                  data: _this.yData,
                  type: "line",
                  smooth: true,
                },
              ],
            };
            _this.myChart.setOption(option);
          });
        },

        formattedDate(timestamp) {
          const date = new Date(timestamp);
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, "0");
          const day = date.getDate().toString().padStart(2, "0");
          const formattedDate = `${year}-${month}-${day}`;
          return formattedDate;
        },
      },
      mounted() {
    	  this.$nextTick(()=>{
    	        this.myChart = echarts.init(document.getElementById("main"));
    	        // 使用刚指定的配置项和数据显示图表。
    	        this.value1 = this.formattedDate(new Date());
    	        this.getData();
    	  })
      },
    });
  </script>
  <style>
    #sdaa {
      padding-top: 20px;
      width:100%;
      height: 100%;
    }
    .el-date-editor {
      position: absolute;
      right: 20px;
      z-index: 999;
    }
  </style>
</html>
