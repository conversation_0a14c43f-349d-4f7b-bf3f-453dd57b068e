<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>修改</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="userForm" data-mars="" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
		<input type="hidden" name="id" value="${param.id}"/>
		<input type="hidden" name="phone" value="${param.phone}"/>
		<input type="hidden" name="startDate" value="${param.startDate}"/>
		<table class="table  table-edit table-vzebra mt-10">
			<tbody>
				<tr>
					<td class="required" width="130px">呼入呼出状态</td>
					<td>
						<select id="createCause" name="createCause" class="form-control input-sm">
							<option value='01' selected="selected">呼入</option>
							<option value='02' >呼出</option>
							<option value='03' >席间</option>
						</select>
					</td>
				</tr>
				<tr>
					<td class="required" width="130px">本地异地</td>
					<td>
						<select id="regionPhone" name="regionPhone" class="form-control input-sm">
							<option value='0' selected="selected">无</option>
							<option value='01'>本地</option>
							<option value='02' >异地</option>
						</select>
					</td>
				</tr>
				<tr>
					<td class="required" width="130px">本网异网</td>
					<td>
						<select id="networkPhone" name="networkPhone" class="form-control input-sm">
							<option value='0' selected="selected">无</option>
							<option value='01'>本网</option>
							<option value='02' >异网</option>
						</select>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="userForm.ajaxSubmitForm()">保存</button>
			<button class="btn btn-sm btn-default ml-20" type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("userForm");
		
		$(function(){
			$("#createCause").find("option[value='${param.createCause}']").attr("selected",'selected');
			$("#regionPhone").find("option[value='${param.regionPhone}']").attr("selected",'selected');
			$("#networkPhone").find("option[value='${param.networkPhone}']").attr("selected",'selected');
		});
		userForm.ajaxSubmitForm = function(){
			var data = form.getJSONObject("userForm");
			ajax.remoteCall("${ctxPath}/servlet/export?action=update",data,function(result) {
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
					layer.closeAll();
					parent.red.initData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		 }
		 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>