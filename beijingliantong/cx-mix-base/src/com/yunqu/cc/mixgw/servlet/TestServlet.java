package com.yunqu.cc.mixgw.servlet;

import java.io.BufferedReader;
import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;

@WebServlet("/test")
public class TestServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	protected Logger logger = CommonLogger.getLogger();
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}
	
	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
			try {
				String json = getRequestStr(req);
				logger.info("接收原始报文数据==>>" + json);
			} catch (Exception e) {
				e.printStackTrace();
			}
	}
	
	public String getRequestStr(HttpServletRequest request) {
		StringBuilder dataBui = new StringBuilder(); //
		String line = null;
		BufferedReader reader = null;
		try {
			reader = request.getReader();
			while ((line = reader.readLine()) != null) {
				dataBui.append(line);
			}
			//获取数据
			StringBuilder logBui = new StringBuilder();
			logBui.append("[MainServlet.getRequestStr] 获取请求数据").append(dataBui);
		} catch (Exception e) {
			return null;
		} finally {
			try {
				reader.close();
			} catch (IOException e) {
			}
			reader = null;
		}
		return dataBui.toString();
	}

}
