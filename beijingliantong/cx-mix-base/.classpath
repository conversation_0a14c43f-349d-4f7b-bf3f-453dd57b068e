<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/mars"/>
	<classpathentry combineaccessrules="false" kind="src" path="/cc-commonext"/>
	<classpathentry kind="con" path="org.eclipse.jdt.junit.JUNIT_CONTAINER/4"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/bcprov-jdk16-143.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jsch-0.1.54.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/hutool-all-5.3.7.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
