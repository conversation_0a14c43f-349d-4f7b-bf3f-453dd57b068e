package com.yunqu.ccportal.servlet;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.base.Constants;

@WebServlet("/index/*")
public class IndexServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForIndex() {		
		if (getRequest().getRemoteUser() == null) {
			String logoutType = this.getPara("logoutType");
			String loginUrl=appContext.getProperty("LOGIN_URL", "");
			if(StringUtils.isBlank(loginUrl)){
				loginUrl=appContext.getProperty("LOGIN_URL", "");
			}
			
			loginUrl = joinUrl(loginUrl,logoutType);
			
			if(StringUtils.isNotBlank(loginUrl) && loginUrl.indexOf("cc-portal") == -1){
				try {
					if(ServerContext.isDebug()){
						CommonCoreLogger.getUserLogger().info("通过cc-portal登录,直接重定向到: "+ loginUrl);
					}
					this.getResponse().sendRedirect(loginUrl);
					
					return;
				} catch (Exception e) {
					CommonCoreLogger.getUserLogger().info("通过cc-portal登录,直接重定向到: "+ loginUrl+",异常:"+e.getMessage(),e);
				}
			}
			String loginVersion = Constants.LOGIN_VERSION;
			if ("1".equals(loginVersion)) {
				loginUrl = joinUrl("/login-new.jsp", logoutType);
				CommonCoreLogger.getUserLogger().info("通过cc-portal登录-1: "+ loginUrl);
				forward(loginUrl);
			} else if (loginVersion.indexOf(".jsp") > 0) {
				loginUrl = joinUrl(loginVersion, logoutType);
				CommonCoreLogger.getUserLogger().info("通过cc-portal登录-2: "+ loginUrl);

				redirect(loginVersion);
			} else {
				loginUrl = joinUrl("/login" + loginVersion + ".jsp", logoutType);
				CommonCoreLogger.getUserLogger().info("通过cc-portal登录-3: "+ loginUrl);

				forward(loginUrl);
			}
		} else {
			redirect("/cc-portal/workbench");
		}
	}
	
	private String joinUrl(String loginUrl, String logoutType) {
		if(loginUrl.indexOf("?") != -1){
			loginUrl = loginUrl + "&logoutType="+logoutType;
		}else{
			loginUrl = loginUrl + "?logoutType="+logoutType;
		}
		return loginUrl;
	}

	/**
	 * 进入到工作台首页界面
	 */
	public void actionForWelcome() {
		
		String welcomeUrl = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(),"cc-base", "WELCOME_PAGE_URL");
		if(StringUtils.isBlank(welcomeUrl)){
			welcomeUrl = Constants.WELCOME_PAGE_URL;
		}
		redirect(welcomeUrl);
		
	}

}
