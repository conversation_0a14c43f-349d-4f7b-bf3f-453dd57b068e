package com.yunqu.ccportal.servlet;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpSession;

import org.easitline.common.core.web.EasyResult;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.ResModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.UserPosition;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.user.UserMgr;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppBaseServlet;

@WebServlet("/servlet/userRes")
public class UserResServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	
	/**
	 * 导航
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public  String actionForUserMenu(){
		List<String> list1 = SchemaService.findAllEntSchema();
		String schema="";
		for (String string : list1) {
			schema=string;
		}
		UserModel  user =UserUtil.getUser(this.getRequest());
		String acc = user.getUserAcc();
		String endId=user.getEpCode();
		HttpSession session = this.getRequest().getSession();
		//先从session中取
		List<ResModel> userRessList = (List<ResModel>)session.getAttribute("USER-MENU-LIST");
		List<List<ResModel>> userRessLists= (List<List<ResModel>>)session.getAttribute("USER-MENU-LIST2");
		//取不到，就从数据库里查询
		if(userRessList==null){
			userRessList = UserMgr.getUserRessList(acc,schema, "2000", true,true,endId);
			userRessLists=new ArrayList<List<ResModel>>() ;
			
			 List<ResModel> list=new ArrayList<ResModel>();//临时存储
			 if(userRessList!=null){
				 for(int i=0;i<userRessList.size();i++){
					 list.add(userRessList.get(i));
					 if((i+1)%5==0||i==userRessList.size()-1){//每5个位一个或者最后一个List<ResModel>存入userRessLists
						 userRessLists.add(list);
						 list=new ArrayList<ResModel>();
					 }
				 }
			 }
			 session.setAttribute("USER-MENU-LIST", userRessList);
			 session.setAttribute("USER-MENU-LIST2", userRessLists);
		}
		
		this.setAttr("userRessLists", userRessLists);
		this.setAttr("userRessList", userRessList);
		return "/pages/menu-navigation.jsp";
	}
	
	/**
	 * 个人资料
	 * @return
	 */
	public JSONObject actionForGetUserInfo(){
		JSONObject json = new JSONObject();
		UserModel user = UserUtil.getUser(this.getRequest());
		String userAcc = user.getUserAcc();
		//姓名
		String userName = user.getUserName();
		//部门
		String deptName = user.getDeptName();
		//工号
		String userNo = user.getUserNo();
		//职位
		List<UserPosition> positions = user.getPositions();
		String userPosition = "";
		for(int i=0;positions!=null&&positions.size()>0&&i<positions.size()-1;i++){
			userPosition += positions.get(i).getName()+'、';
		}
		if(positions!=null&&positions.size()>0){
			userPosition += positions.get(positions.size()-1).getName();
		}
		json.put("userAcc", userAcc);
		json.put("userName", userName);
		json.put("deptName", deptName);
		json.put("userNo", userNo);
		json.put("entId", user.getEpCode());
		json.put("busiOrderId", user.getBusiOrderId());
		json.put("userPosition", userPosition);
		
		JSONObject extConf = user.getExtConf();
		if (extConf!=null) {
			json.put("signNames", user.getExtConf().getString("SIGNATURE"));
		}else{
			json.put("signNames", "");
		}
		
		return EasyResult.ok(json.toString(), "");
	}
}
