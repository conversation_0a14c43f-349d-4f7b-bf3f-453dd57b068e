package com.yunqu.ccportal.servlet;

import java.lang.reflect.Array;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppBaseServlet;

/**
 * 部门类型
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/groupType")
public class GroupTypeServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	/**
	 * 新增
	 */
	public EasyResult actionForAdd() {
		JSONObject jsonObject = this.getJSONObject("groupType");
		try {
//			String groupType = this.getChildGroupType(this.getJsonPara("pGroupType"));
//			//最大99个子节点
//			if(!groupType.startsWith(this.getJsonPara("pGroupType"))){
//				return EasyResult.fail("已经到达最大子节点数99");
//			}
//			if(StringUtils.isBlank(jsonObject.getString("MAX_NODE_COUNT"))){
//				jsonObject.put("MAX_NODE_COUNT", 0);
//			}
			EasyRecord record = new EasyRecord(getTableName("CC_GROUP_TYPE"), "GROUP_TYPE");
			record.setColumns(jsonObject);
			//record.setPrimaryValues(groupType);
			this.getQuery().save(record);
			return EasyResult.ok(null, "添加成功！");
		} catch (Exception ex) {
			this.error("添加失败功，原因：" + ex.getMessage(), ex);
			return EasyResult.fail("添加失败，原因：" + ex.getMessage());
		}
	}
	
	/**
	 * 修改
	 */
	public EasyResult actionForUpdate() {
		JSONObject jsonObject = this.getJSONObject("groupType");
		try {
			if(StringUtils.isBlank(jsonObject.getString("MAX_NODE_COUNT"))){
				jsonObject.put("MAX_NODE_COUNT", 0);
			}
			EasyRecord easyRecord = new EasyRecord(getTableName("CC_GROUP_TYPE"), "GROUP_TYPE");
			easyRecord.setColumns(jsonObject);
			this.getQuery().update(easyRecord);
			return EasyResult.ok(null, "修改成功！");
		} catch (SQLException ex) {
			return EasyResult.fail("修改失败，原因：" + ex.getMessage());
		}
	}

	/**
	 * 删除
	 */
	public EasyResult actionForDelete() {
		JSONObject jsonObject = this.getJSONObject();
		Object[] params = {jsonObject.getString("groupType") };
		EasyQuery query = this.getQuery();
		try {
			if(query.queryForExist("select count(1) from "+getTableName("CC_SKILL_GROUP")+" where GROUP_TYPE = ?", params)){
				return EasyResult.fail("该类型正在使用，不能删除！");
			}
			query.execute("delete from "+getTableName("CC_GROUP_TYPE")+" where GROUP_TYPE = ?", params);
			return EasyResult.ok(null, "删除成功！");
		} catch (Exception ex) {
			return EasyResult.fail("删除失败，原因：" + ex.getMessage());
		}
	}
	
	/**
	 * 判断是否存在子节点
	 * @return
	 */
	public EasyResult actionForChildNode(){
		try {
			if(this.getQuery().queryForExist("select count(1) from "+getTableName("CC_SKILL_GROUP")+" where GROUP_TYPE like '"+this.getJsonPara("groupType")+"__'", new Object[]{})){
				return EasyResult.fail();
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	/**
	 * 获取下一个节点
	 * @param pGroupType
	 * @return
	 * @throws SQLException
	 */
	private String getChildGroupType(String pGroupType) throws SQLException{
		String lastType = this.getQuery().queryForString("select max(GROUP_TYPE) GROUP_TYPE from "+getTableName("cc_group_type")+" where GROUP_TYPE like '"+pGroupType+"__'", new Object[]{});
		if(StringUtils.isBlank(lastType)){
			//初始节点
			if(StringUtils.isBlank(pGroupType)){
				return "01";
			}
			return pGroupType + "01";
		}
		return addNumber(lastType, 1);
	}
	
	/**
	 * 子节点递增
	 * @param str
	 * @param step
	 * @return
	 */
	private String addNumber(String str, int step) {
	    String strchg = null;
	    int idef = 0,
	        icur = 0,
	        addValue = 0,
	        byteValue = 0,
	        icarry = 0,
	        len = 0,
	        istep = 0;
	    istep = step;
	    byte[] b = str.getBytes();
	    len = Array.getLength(b);
	    for (int i = 0; i < len; i++) {
	        if (istep == 0)
	            break;
	        byteValue = (new Byte(b[len - i - 1])).intValue(); //取最后一位的值
	        addValue = byteValue + istep; //当前字母的ASCLL值加步长
	        if ((idef = addValue - 57) > 0) { //idef为所在位的值加步长后与单前最大值的ASCLL的差值。
	            if ((idef % 10) > 0)
	                istep = idef / 10 + 1; //取得进位值 如果取模大于0时加1
	            else
	                istep = idef / 10;
	            if ((icarry = idef % 10) > 0)
	                icur = icarry + 47;
	            else
	                icur = 57; //当取模为0时，当前所在位数的值为最大值9（9的排序为10）
	        } else {
	            icur = addValue;
	            istep = 0;
	        }
	
	        b[len - i - 1] = (byte) icur;
	
	    }
	    if (istep > 0)
	        strchg = null;
	    else
	        strchg = new String(b);
	
	    return strchg;
	}

}
