/**
 * 
 */
package com.yunqu.ccportal.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.model.SkillGroup;



/**
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/skillGroup/*")
public class SkillGroupServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		EasyResult result = new EasyResult();
		try {
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			
			String groupType = skillGroup.getString("GROUP_TYPE");
			JSONObject typeJson = this.getQuery().queryForRow("select * from "+getTableName("CC_GROUP_TYPE")+" where GROUP_TYPE = ?", new Object[]{groupType}, new JSONMapperImpl());
			int count = this.getQuery().queryForInt("select count(1) from "+getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and GROUP_TYPE = ?", new Object[]{this.getEntId(),this.getBusiOrderId(),groupType});
			/*
			 * 判断技能组类型的最大节点数是否超标
			 */
			if(typeJson.getIntValue("MAX_NODE_COUNT") != 0 && count >= typeJson.getIntValue("MAX_NODE_COUNT")){
				return EasyResult.fail("添加失败，部门类型\""+typeJson.getString("GROUP_TYPE_NAME")+"\"最大节点数"+typeJson.getIntValue("MAX_NODE_COUNT")+"已经用完，不能再添加该类型。");
			}

			skillGroup.set("SKILL_GROUP_CODE", this.getGroupCode(skillGroup.getString("P_GROUP_ID")));
			skillGroup.setPrimaryValues(this.nextSeq("CC_SKILL_GROUP"));
			skillGroup.set("ENT_ID", getEntId());
			skillGroup.set("BUSI_ORDER_ID", this.getBusiOrderId());
			skillGroup.set("AGENT_COUNT", 0);
			skillGroup.addCreateTime();
			skillGroup.setCreator(getUserPrincipal().getUserId());
			this.getQuery().save(skillGroup);

			result.setMsg("添加成功！");
			/*
			 * 判断该技能组是否后有话务权限，
			 * 如果为1，表示有，并同步数据
			 */
			if(typeJson.getIntValue("CALL_FLAG") == 1){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"add");
				this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 获取部门编码
	 * @param busiType
	 * @param pId
	 * @return
	 * @throws SQLException
	 */
	private String getGroupCode(String pId) throws SQLException{
		String lastCode = this.getQuery().queryForString("select max(SKILL_GROUP_CODE) GROUP_CODE from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? and BUSI_ORDER_ID = ? and P_GROUP_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId(),pId});
		if(StringUtils.isBlank(lastCode)){
			String pCode = this.getQuery().queryForString("select SKILL_GROUP_CODE from "+getTableName("CC_SKILL_GROUP")+" where SKILL_GROUP_ID = ?", new Object[]{pId});
			if(StringUtils.isBlank(pCode)){
				//不是初始节点，但是父节点没有部门编码
				if(!"0".equals(pId)){
					return "";
				}
				return "500";
			}
			return pCode + "500";
		}
		String code = lastCode.substring(lastCode.length() - 3);
		String preCode = lastCode.substring(0,lastCode.length() - 3);
		code = (Integer.parseInt(code) + 1) + "";
		return preCode + code;
	}
	
	/**
	 * 更新技能组
	 * @return
	 */
	public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject("skillGroup");
		try {
			String sql="select count(1) from " + getTableName("CC_SKILL_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and SKILL_GROUP_ID <> ? and SKILL_GROUP_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), jsonObject.getString("SKILL_GROUP_ID"), StringUtils.trim(jsonObject.getString("SKILL_GROUP_NAME"))})) {
				result.addFail("该技能组名称已经存在！");
				return result;
			}
			SkillGroup skillGroup=getModel(SkillGroup.class, "skillGroup");
			skillGroup.remove("GROUP_TYPE_NAME"); 
			this.getQuery().update(skillGroup);
			updateUserGroupList(jsonObject.getString("SKILL_GROUP_NAME"), jsonObject.getString("SKILL_GROUP_ID"));
			result.setMsg("修改成功！");
			
			if(this.getSkillCallFalg(skillGroup.getString("SKILL_GROUP_ID"))){
				this.syncSkillCache(skillGroup.getString("SKILL_GROUP_ID"));
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"update");
				if(syncGroupInfo != null){
					result.addFail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 添加部门用户
	 * @return
	 */
	public EasyResult actionForAddUser(){
		JSONObject param = this.getJSONObject();
		JSONArray userIds = param.getJSONArray("userIds");
		String groupId = param.getString("groupId");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		
		if(userIds!=null && userIds.size()>0){
			ArrayList<Object[]> list = new ArrayList<>();
			
			for(int i = 0; i < userIds.size(); i++){
				String userId = userIds.getString(i);
				if(StringUtils.isNotBlank(userId)){
					list.add(new Object[]{groupId,userId,busiOrderId,entId,i});
				}
			}

			EasyQuery query = this.getQuery();
			try {
				query.begin();
				query.executeBatch("insert into "+getTableName("CC_SKILL_GROUP_USER")+" (SKILL_GROUP_ID,USER_ID,BUSI_ORDER_ID,ENT_ID,IDX_ORDER) values(?,?,?,?,?)", list);
				query.commit();
				this.updateAllUserGroupList();
				this.updateSkillUserCount();//更新技能组坐席数
				//话务能力
				if(this.getSkillCallFalg(groupId)){
					this.syncSkillCache(groupId);
					String syncGroupInfo = this.syncGroupInfo(this.getEntId(),groupId,"update");
					if(syncGroupInfo != null){
						return EasyResult.error(501, syncGroupInfo);
					}
				}
			} catch (SQLException e) {
				try {
					query.roolback();
				} catch (SQLException e1) {
					this.error(e1.getMessage(), e1);
				}
				this.error(e.getMessage(), e);
				return EasyResult.fail("添加失败，失败原因："+e.getMessage());
			}
		}
		
		return EasyResult.ok();
	}
	
	/**
	 * 删除技能组
	 * @return
	 */
	public EasyResult actionForDelete(){
		try {
			String skillGroupId = getJsonPara("pk");
			if(getQuery().queryForExist("select count(1) from " + getTableName("CC_SKILL_GROUP") + " where P_GROUP_ID = ?", skillGroupId)){
				return EasyResult.fail("存在下级班组，不能删除！");
			}
			if(getQuery().queryForExist("select count(1) from " + getTableName("cc_skill_group_user") + " where SKILL_GROUP_ID = ?", new Object[]{skillGroupId})) {
				return EasyResult.fail("存在所属成员，不能删除！");
			}
			SkillGroup skillGroup=new SkillGroup(getDbName());
			skillGroup.setPrimaryValues(skillGroupId);
			this.getQuery().deleteById(skillGroup);
			
			//话务能力
			if(this.getSkillCallFalg(skillGroupId)){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroup.getString("SKILL_GROUP_ID"),"delete");
				String reloadKey = "RELOAD_SKILLGROUP_" + skillGroupId;
				cache.delete(reloadKey);
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
			return EasyResult.ok(null,"删除成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 删除用户
	 * @return
	 */
	public EasyResult actionForDelGroupUser(){
		EasyQuery query = getQuery();
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		String userId = jsonObject.getString("userId");
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			int updateCount=query.executeUpdate("delete from " + getTableName("CC_SKILL_GROUP_USER") + " where SKILL_GROUP_ID = ? and USER_ID = ? ", new Object[]{skillGroupId,userId});
			if(updateCount>0){
				this.updateUserGroupList(query, userId);
				this.updateSkillUserCount();
			}
			result.setMsg("移除成功！");
			this.updateSkillUserCount();//更新技能组坐席数
			
			//话务能力
			if(this.getSkillCallFalg(skillGroupId)){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
				this.syncSkillCache(skillGroupId);
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("移除失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 批量删除
	 * @return
	 */
	public EasyResult actionForBatchDelUser(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject=getJSONObject();
		EasyQuery query = getQuery();
		JSONArray userIds=jsonObject.getJSONArray("userIds");
		String skillGroupId = jsonObject.getString("skillGroupId");
		try {
			query.begin();
			if(userIds!=null&&userIds.size()>0){
				EasyRecord easyRecord = new EasyRecord(getTableName("CC_SKILL_GROUP_USER"), new String[]{"SKILL_GROUP_ID","USER_ID"});
				for(int i = 0; i < userIds.size(); i ++){
					String id = userIds.getString(i);
						easyRecord.setPrimaryValues(new Object[]{skillGroupId,id});
						query.deleteById(easyRecord);
						this.updateUserGroupList(query, id);
				}
				result.setMsg(userIds.size()+"个坐席移除成功！");
			}else{
				result.addFail("请先选择坐席！");
			}
			query.commit();
			this.updateSkillUserCount();//更新技能组坐席数
			this.updateSkillUserCount();
			
			//话务能力
			if(this.getSkillCallFalg(skillGroupId)){
				String syncGroupInfo = this.syncGroupInfo(this.getEntId(),skillGroupId,"update");
				this.syncSkillCache(skillGroupId);
				if(syncGroupInfo != null){
					return EasyResult.fail(syncGroupInfo);
				}
			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("移除坐席失败，原因："+e1.getMessage(),e1);
				result.addFail("移除坐席失败，原因："+e1.getMessage());
			}
			this.error("移除坐席失败，原因："+e.getMessage(),e);
			result.addFail("移除坐席失败，原因："+e.getMessage());
		}
		return result;
	}

	@Override
	protected String getResId() {
		return null;
	}

	/**
	 * 修改技能组下的所有坐席信息
	 * @param groupName
	 * @param groupId
	 * @throws SQLException 
	 */
	private void updateUserGroupList(String groupName, String groupId) throws SQLException{
		String sql = "update " + getTableName("CC_BUSI_USER") + " set GROUP_LIST = ? where BUSI_ORDER_ID = ? and ENT_ID = ? and USER_ID in (select USER_ID from "
				+getTableName("CC_SKILL_GROUP_USER")+" where SKILL_GROUP_ID = ?)";
		this.getQuery().execute(sql, new Object[]{groupName,getBusiOrderId(),getEntId(),groupId});
	}
	
	private void updateAllUserGroupList(){
		try {
			EasyQuery query = this.getQuery();
			List<EasyRow> list = query.queryForList("select USER_ID from "+getTableName("CC_BUSI_USER")+" where ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId()});
			if(list!=null&&list.size()>0){
				for (EasyRow easyRow : list) {
					this.updateUserGroupList(query, easyRow.getColumnValue("USER_ID"));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 跟新坐席技能组名称
	 * @param userId
	 * @throws SQLException
	 */
	private void updateUserGroupList(EasyQuery query, String userId) throws SQLException{
		String grouName = "";
		List<EasyRow> list = query.queryForList("select t1.SKILL_GROUP_NAME from "+getTableName("CC_SKILL_GROUP")+" t1,"+getTableName("CC_SKILL_GROUP_USER")+" t2 where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t2.BUSI_ORDER_ID = ? and t2.USER_ID = ?", new Object[]{this.getBusiOrderId(), userId});
		if(list != null){
			for (EasyRow easyRow : list) {
				grouName += "," + easyRow.getColumnValue("SKILL_GROUP_NAME");
			}
			if(grouName.length() > 1){
				grouName = grouName.substring(1);
			}
		}
		query.execute("update "+getTableName("CC_BUSI_USER")+ " set GROUP_LIST = ? where BUSI_ORDER_ID = ? and USER_ID = ?", new Object[]{grouName,this.getBusiOrderId(), userId});
	}
	
	/**
	 * 获取技能组Id
	 * @param tableName
	 * @return
	 */
	private synchronized int nextSeq(String tableName){
		tableName = tableName.toUpperCase();
		String sql = "select SEQ_ID from CC_SEQ  where TABLE_ID = ?";
		int seq = 0;
		try {
			String seqIdStr = this.getQuery().queryForString(sql, new Object[]{tableName});
			if(StringUtils.isNotBlank(seqIdStr)){
				seq = Integer.parseInt(seqIdStr);
			}
		} catch (Exception ex) {
			 this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
		}
		if(seq <=0){
			try {
				sql = "insert into CC_SEQ(TABLE_ID,SEQ_ID) values (?,?)";
				this.getQuery().execute(sql, new Object[]{tableName,1});
			} catch (Exception ex) {
				this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
			}
		}
		seq = seq +1;
		sql = "update CC_SEQ set SEQ_ID = ? where TABLE_ID = ?  ";
		try {
			this.getQuery().executeUpdate(sql, new Object[]{seq,tableName});
		} catch (Exception ex) {
			this.error("AppBaseServlet.nextSeq() error->cause:"+ex.getMessage(), ex);
		}
		return seq;
	}
	
	/**
	 * 判断技能组是否属于外呼权限
	 * @param skillId
	 * @return
	 */
	private boolean getSkillCallFalg(String skillId){
		try {
			return this.getQuery().queryForExist("select count(1) from "+getTableName("CC_SKILL_GROUP t1,")+getTableName("CC_GROUP_TYPE t2")+" where t1.GROUP_TYPE = t2.GROUP_TYPE and t2.CALL_FLAG = 1 and t1.SKILL_GROUP_ID = ?", new Object[]{skillId});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return false;
	}
}
