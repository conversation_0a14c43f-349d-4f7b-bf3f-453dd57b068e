package com.yunqu.ccportal.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.easitline.common.utils.string.StringUtils;

public class StrUtils {
	
	public static String delHTMLTag(String htmlStr){ 
        String regEx_script="<script[^>]*?>[\\s\\S]*?<\\/script>"; //定义script的正则表达式 
        String regEx_style="<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式 
        String regEx_html="<[^>]+>"; //定义HTML标签的正则表达式 
         
        Pattern p_script=Pattern.compile(regEx_script,Pattern.CASE_INSENSITIVE); 
        Matcher m_script=p_script.matcher(htmlStr); 
        htmlStr=m_script.replaceAll(""); //过滤script标签 
         
        Pattern p_style=Pattern.compile(regEx_style,Pattern.CASE_INSENSITIVE); 
        Matcher m_style=p_style.matcher(htmlStr); 
        htmlStr=m_style.replaceAll(""); //过滤style标签 
         
        Pattern p_html=Pattern.compile(regEx_html,Pattern.CASE_INSENSITIVE); 
        Matcher m_html=p_html.matcher(htmlStr); 
        htmlStr=m_html.replaceAll(""); //过滤html标签 

        return htmlStr.trim(); //返回文本字符串 
        
    }
	
	/**
	 * 给字符值添加chars字符，默认是单引号  1,2,3,c===> '1','2','3','c'
	 * @param val
	 * @param chars
	 * @return
	 */
	public static String appendChar(String val, String chars) {
		while (val.indexOf(",,") != -1) {
			val = val.trim();
			val = val.replaceAll(",,", ",");
			if (val.startsWith(",")) {
				val = val.substring(1, val.length());
			}
			if (val.endsWith(",")) {
				val = val.substring(0, val.length() - 1);
			}
		}
		if (StringUtils.isBlank(chars)) {
			chars = "'";
		}
		val = chars + val + chars;
		val = val.replaceAll(",", chars + "," + chars);
		return val;
	}
}
