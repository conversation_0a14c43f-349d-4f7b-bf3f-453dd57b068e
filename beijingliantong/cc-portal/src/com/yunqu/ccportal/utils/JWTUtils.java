package com.yunqu.ccportal.utils;

import java.util.Date;

import org.easitline.common.utils.crypt.MD5Util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator.Builder;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.yq.busi.common.model.UserModel;

public class JWTUtils {

    // token过期时间1min (过期会自动刷新续命 目的是避免一直都是同一个token )
    private static final long EXPIRE_TIME = 1 * 60 * 1000;
    // 登录间隔时间10min 超过这个时间强制重新登录
    private static long Login_Interval;

    public static void verifySign(Algorithm algorithm, String token) {
        DecodedJWT decode = JWT.decode(token);
        algorithm.verify(decode);
    }

    public static boolean needRefresh(String token) {
        Date exp = JWTUtils.getExp(token);
        return new Date().getTime() >= exp.getTime();
    }

    public static Date getExp(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("exp").asDate();
        } catch (JWTDecodeException e) {
            e.printStackTrace();
            return null;
        }
    }
    
    public static void main(String[] args) {
		UserModel userModel = new UserModel();
		userModel.setUserAcc("8001@cc");
		userModel.setUserName("8001");
		String token = sign(userModel, "AF43B5B2A8C0550319076A2E129728DA");
		System.out.println(token);
		tokenInfoByToken(token);
		String oldToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyQWNjIjoiODAwMUBjYyIsImV4cCI6MTY0ODg4NTA0OCwidXNlcm5hbWUiOiI4MDAxIn0.Vb8fIaeZ1nGjhAvnvUDAt5KKtqKZhWVDG-E5arDirrI";
		System.out.println(needRefresh(oldToken));
		System.out.println(getExp(oldToken));
	}

    /**
     * 生成签名,5min后过期
     *
     * @param tokenInfo 用户信息
     * @param secret    用户的密码
     * @return 加密的token
     */
    public static String sign(UserModel user, String secret) {
        try {
            Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            long currentTimeMillis = System.currentTimeMillis();
            Builder builder = JWT.create()
                    .withClaim("username", user.getUserName())
                    .withClaim("userAcc", user.getUserAcc())
                    .withClaim("timestamp", currentTimeMillis)
                    .withClaim("entId", user.getEpCode())
                    .withClaim("busiOrderId", user.getBusiOrderId())
                    // md5（md5（用户账号）+时间戳）
                    .withClaim("sign", MD5Util.getHexMD5(MD5Util.getHexMD5(user.getUserAcc())+currentTimeMillis));
            return builder.withExpiresAt(date).sign(algorithm);
        } catch (Exception e) {
            return null;
        }
    }

    public static String signLink(String resourceId, Long userId, String secret) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        if (userId == null) {
            return JWT.create().withClaim("resourceId", resourceId).sign(algorithm);
        } else {
            return JWT.create().withClaim("resourceId", resourceId).withClaim("userId", userId).sign(algorithm);
        }
    }

    public static boolean verifyLink(String token, String resourceId, Long userId, String secret) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        JWTVerifier verifier;
        if (userId == null) {
            verifier = JWT.require(algorithm).withClaim("resourceId", resourceId).build();
        } else {
            verifier = JWT.require(algorithm).withClaim("resourceId", resourceId).withClaim("userId", userId).build();
        }

        try {
            verifier.verify(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户名
     */
    public static void tokenInfoByToken(String token) {
        DecodedJWT jwt = JWT.decode(token);
        String username = jwt.getClaim("username").asString();
        String userId = jwt.getClaim("userAcc").asString();
        System.out.println(username + ", " + userId);
    }

    /**
     * 获取当前token上次操作时间
     *
     * @param token
     * @return
     */
    public static Long tokenLastOperateTime(String token) {
        DecodedJWT jwt = JWT.decode(token);
        Date expiresAt = jwt.getExpiresAt();
        return expiresAt.getTime();
    }

}
