package com.yunqu.ccportal.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;

public class UserResourceRowMapper implements EasyRowMapper<UserResource>{

	@SuppressWarnings("unchecked")
	@Override
	public UserResource mapRow(ResultSet rs, int rownum) {
		UserResource ur = new UserResource();
		try {
			ur.setResId(rs.getString("RES_ID"));
			ur.setPresId(rs.getString("P_RES_ID"));
			ur.setResName(rs.getString("RES_NAME"));
			//ur.setResTarget(rs.getString("RES_TARGET"));
			ur.setResUrl(rs.getString("RES_URL"));
			ur.setResIcon(rs.getString("RES_ICON"));
			ur.setResType(rs.getInt("RES_TYPE"));
			ur.setIdxOrder(rs.getInt("IDX_ORDER"));
			ur.setResCode(rs.getInt("RES_CODE"));
			ur.setSons(rs.getInt("SONS"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return ur;
		
	}

}
