package com.yunqu.ccportal.listener;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

import org.apache.log4j.Logger;

import com.yunqu.ccportal.base.CommonLogger;

/**
 * Application Lifecycle Listener implementation class UserSessionListener
 *
 */   
@WebListener
public class UserSessionListener implements HttpSessionListener {

	private Logger logger = CommonLogger.logger;

	/**
	 * Session被创建时
     * @see HttpSessionListener#sessionCreated(HttpSessionEvent)
     */
    public void sessionCreated(HttpSessionEvent event)  { 
//    	String sessionId = event.getSession().getId();
//    	if(ServerContext.isDebug()){
//    		logger.info(CommonUtil.getClassNameAndMethod(this)+" 用户登录,sessionId="+sessionId);
//    	}
    }

	/**
	 * Session被删除时
     * @see HttpSessionListener#sessionDestroyed(HttpSessionEvent)
     */
    public void sessionDestroyed(HttpSessionEvent event)  { 
//    	String sessionId = event.getSession().getId();
//    	
    	
    }
	
}
