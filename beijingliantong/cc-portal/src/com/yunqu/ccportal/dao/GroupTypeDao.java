package com.yunqu.ccportal.dao;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;

/**
 * 部门类型
 * <AUTHOR>
 *
 */
@WebObject(name="GroupTypeDao")
public class GroupTypeDao extends AppDaoContext{

	/**
	 * 获取部门类型对象
	 * @return
	 */
	@WebControl(name="record",type=Types.RECORD)
	public  JSONObject getObject(){
		EasySQL sql = this.getEasySQL("select * from");
		sql.append(getTableName("CC_GROUP_TYPE"));
		sql.append("where 1=1");
		sql.append(param.getString("groupType.NODE_TYPE"), "and GROUP_TYPE = ?", false);
		return queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
	
	/**
	 * 部门类型架构
	 */
	@WebControl(name="groupTypeTree", type=Types.TREE)
	public JSONObject groupTypeTree() {
    	JSONObject root = new JSONObject();
		root.put("name","组织架构");
		root.put("id", 0);
		root.put("open", true);
	    try {
	    	String sql = "select * from " + getTableName("CC_GROUP_TYPE");
	    	List<JSONObject> nodes = getQuery().queryForList(sql, null, new JSONMapperImpl());
	    	
			root.put("children", this.nextNode(1, "", nodes));
		} catch (SQLException e) {
			e.printStackTrace();
		}
	    return getTree(root);
	}

	/**
	 * 获取下一个节点
	 * @param pId
	 * @param skillList
	 * @return
	 */
	private JSONArray nextNode(int level, String pId, List<JSONObject> nodesList){
		JSONArray childArray = new JSONArray();
		for (JSONObject node : nodesList) {
			String groupType = node.getString("GROUP_TYPE");
			if(groupType.length() == level * 2 && groupType.startsWith(pId)){
				JSONObject child = new JSONObject();
				child.put("name", node.getString("GROUP_TYPE_NAME"));
				child.put("id", groupType);
				child.put("open", true);
				child.put("children", nextNode(level+1, groupType, nodesList));
				childArray.add(child);
			}
		}
		return childArray;
	}
	
	/**
	 * 获取部门类型列表
	 * @return
	 */
	@WebControl(name="groupTypeList",type=Types.LIST)
	public  JSONObject groupTypeList(){
		EasySQL sql = this.getEasySQL("SELECT * from");
		sql.append(getTableName("CC_GROUP_TYPE"));
		sql.append("where 1=1");
		if(StringUtils.isNotBlank(param.getString("groupType"))&&!"0".equals(param.getString("groupType"))){
			sql.append("and GROUP_TYPE like '"+param.getString("groupType")+"__'");
		}
		sql.appendLike(param.getString("groupTypeName"), "and GROUP_TYPE_NAME like ?");
		return this.queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 获取部门类型字典
	 * @return
	 */
	@WebControl(name="groupTypeDict", type=Types.DICT)
	public JSONObject treeDict(){
		String pk = param.getString("pGroupType");
		if(StringUtils.isBlank(pk)){
			pk = "";
		}
		EasySQL sql = this.getEasySQL("select GROUP_TYPE,CONCAT(GROUP_TYPE_NAME,CASE CALL_FLAG when 1 then '(话务权限)' else '(无话务权限)' end) GROUP_TYPE_NAME from");
		sql.append(getTableName("CC_GROUP_TYPE"));
		sql.append("where 1=1");
		sql.append("and GROUP_TYPE like '"+pk+"__'");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
}
