package com.yunqu.ccportal.dao;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;
import com.yunqu.ccportal.utils.PhoneCryptor;
/**
 * 录音文件
 */
@WebObject(name="RecordDao")
public class RecordDao extends AppDaoContext {
	
	/**
	 * 获取通话记录
	 * @return
	 */
	@WebControl(name="getRecord",type=Types.TEMPLATE)
	public JSONObject getRecord(){
		JSONObject result = null;
		if(StringUtils.notBlank(param.getString("serialId"))){
			String sql="select * from "+getTableName("CC_CALL_RECORD")+" where SERIAL_ID = ? ";
			result = queryForRecord(sql, new Object[]{param.getString("serialId")},new MapRowMapperImpl());
		}else{
			String sql="select * from "+getTableName("CC_CALL_RECORD")+" where OBJ_ID = ? and BILL_TIME > 0 ";
			result = queryForRecord(sql, new Object[]{param.getString("objId")},new MapRowMapperImpl());
		}
		JSONObject data = result.getJSONObject("data");
		//判断该对象是否存在录音文件
		if(result != null){
			String recordFile = data.getString("RECORD_FILE");
			if(StringUtils.isNotBlank(recordFile)){
				 recordFile =getRecordUrlPrefix() + recordFile;
			}
			result.put("url", recordFile);
			result.put("state", 1);
		}
		return result;
	}
	
	/**
	 * 我的通话记录
	 * @return
	 */
	@WebControl(name="myList",type=Types.LIST)
	public  JSONObject myCallRecord(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		String[] dateRange=param.getString("dateRange").replaceAll("-","").split(" 到 ");
		EasySQL sql = this.getEasySQL("select * from ");
		sql.append(this.getTableName("CC_CALL_RECORD t1"));
		sql.append(" where 1=1 ");
		sql.append(this.getEntId()," and t1.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");
		sql.append(this.getUserPrincipal().getUserId()," and t1.AGENT_ID = ? ");
		sql.append(this.param.getString("agentPhone"), " and t1.AGENT_PHONE = ? ");
		sql.append(dateRange[0], " and t1.DATE_ID >= ? ");
		sql.append(dateRange[1], " and t1.DATE_ID <= ? ");
		sql.append(this.param.getString("clearCause"), " and t1.CLEAR_CAUSE  = ? ");
		sql.append(this.param.getString("satisfId"), " and t1.SATISF_ID = ?");
		String clearCause=this.param.getString("clearCause");
		if(StringUtils.notBlank(clearCause)){
			sql.append(clearCause, " and t1.CLEAR_CAUSE  = ? ");
		}
		switch(param.getIntValue("billTime")){
		case 1://全部有效时间
			sql.append(0, " and t1.BILL_TIME > 0");
			break;
		case 2://0到30秒
			sql.append(0, " and t1.BILL_TIME > ?");
			sql.append(30, " and t1.BILL_TIME <= ?");
			break;
		case 3://30到60秒
			sql.append(30, " and t1.BILL_TIME > ?");
			sql.append(60, " and t1.BILL_TIME <= ?");
			break;
		case 4://60到120秒
			sql.append(60, " and t1.BILL_TIME > ?");
			sql.append(120, " and t1.BILL_TIME <= ?");
			break;
		case 5://120到300秒
			sql.append(120, " and t1.BILL_TIME > ?");
			sql.append(300, " and t1.BILL_TIME <= ?");
			break;
		case 6://300秒以上
			sql.append(300, " and t1.BILL_TIME > ?");
			break;
		}
		
		//加密号码查询，不支持模糊查询
		String custPhone = this.param.getString("custPhone");
		if(StringUtils.isNotBlank(custPhone)){
			sql.append("and t1.CUST_PHONE in('"+cryptor.encrypt(custPhone)+"','"+custPhone+"')");
		}
		
		sql.append(" order by t1.SERIAL_ID ");
		//sql.appendSort(param.getString("sortName"),param.getString("sortType"));
		JSONObject result=this.queryForPageList(sql.getSQL(), sql.getParams());
		result.put("data", cryptor.decrypt(result.getJSONArray("data"),new String[]{"CALLER","CALLED","CUST_PHONE"},false));
		return result;
	}
	
	/**
	 * 通话记录查询
	 * @return
	 */
	@WebControl(name="queryList",type=Types.LIST)
	public  JSONObject list(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		
		EasySQL sql = this.getEasySQL("select t1.* from ");
		sql.append(this.getTableName("CC_CALL_RECORD t1"));
		sql.append(" where 1=1 ");
		sql.append(this.getEntId()," and t1.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");
		
		sql.append(this.getSkillRes("t1.GROUP_ID", param.getString("groupId")));
	
		sql.append(this.param.getString("startDate").replaceAll("-", ""), " and t1.DATE_ID >= ? ");
		sql.append(this.param.getString("endDate").replaceAll("-", ""), " and t1.DATE_ID <= ? ");
		sql.appendLike(this.param.getString("agentName"), " and t1.AGENT_NAME like ? ");
		sql.append(this.param.getString("taskId"), " and t1.TASK_ID  = ? ");
		sql.append(this.param.getString("satisfId"), " and t1.SATISF_ID = ?");
		String clearCause=this.param.getString("clearCause");
		if(StringUtils.notBlank(clearCause)){
			sql.append(clearCause, " and t1.CLEAR_CAUSE  = ? ");
		}
		
		switch(param.getIntValue("billTime")){
		case 1://全部有效时间
			sql.append(0, " and t1.BILL_TIME > 0");
			break;
		case 2://0到30秒
			sql.append(0, " and t1.BILL_TIME > ?");
			sql.append(30, " and t1.BILL_TIME <= ?");
			break;
		case 3://30到60秒
			sql.append(30, " and t1.BILL_TIME > ?");
			sql.append(60, " and t1.BILL_TIME <= ?");
			break;
		case 4://60到120秒
			sql.append(60, " and t1.BILL_TIME > ?");
			sql.append(120, " and t1.BILL_TIME <= ?");
			break;
		case 5://120到300秒
			sql.append(120, " and t1.BILL_TIME > ?");
			sql.append(300, " and t1.BILL_TIME <= ?");
			break;
		case 6://300秒以上
			sql.append(300, " and t1.BILL_TIME > ?");
			break;
		}

		//加密号码查询，不支持模糊查询
		String custPhone = this.param.getString("custPhone");
		if(StringUtils.isNotBlank(custPhone)){
			sql.append("and t1.CUST_PHONE in('"+cryptor.encrypt(custPhone)+"','"+custPhone+"')");
		}
		
		sql.appendSort(param.getString("sortName"),param.getString("sortType"),"t1.SERIAL_ID ");
		JSONObject result=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		result.put("data", cryptor.decrypt(result.getJSONArray("data"), new String[]{"CALLER","CALLED","CUST_PHONE"},false));
		return result;
	}

	/**
	 * 未接来电查询列表
	 * @return
	 */
	@WebControl(name="noAnswerList",type=Types.LIST)
	public JSONObject noAnswerList(){
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		EasySQL sql = this.getEasySQL("select * from ").append(getTableName("CC_CALL_NOANSWER")).append("where 1=1");
		sql.append(getEntId(),"and ENT_ID = ?");
		sql.append(getBusiOrderId(),"and BUSI_ORDER_ID =?");
		sql.append(param.getString("beginTime"), "and ALERING_TIME >= ?");
		sql.append(param.getString("endTime"), "and ALERING_TIME <= ?");
		sql.append(param.getString("state")," and STATE = ?");
		
		sql.append(this.getSkillRes("GROUP_ID", param.getString("groupId")));

		//加密号码查询，不支持模糊查询
		String called = this.param.getString("called");
		String caller = this.param.getString("caller");
		if(StringUtils.isNotBlank(called)){
			sql.append("and CALLED in('"+cryptor.encrypt(called)+"','"+called+"')");
		}
		if(StringUtils.isNotBlank(caller)){
			sql.append("and CALLER in('"+cryptor.encrypt(caller)+"','"+caller+"')");
		}
		
		JSONArray agentArray = param.getJSONArray("agentId");
        if(agentArray != null){
        	String agentId = agentArray.toJSONString().replaceAll("\"", "'").replace("[", "(").replace("]", ")");
        	sql.append("and AGENT_ID in " + agentId);
        }
		sql.appendSort(param.getString("sortName"), param.getString("sortType"), "ALERING_TIME");
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		result.put("data", cryptor.decrypt(result.getJSONArray("data"), new String[]{"CALLER","CALLED"}, false));
		return result;
	}
	
	/**
	 * 获取通话路由前缀
	 * @return
	 */
	private String getRecordUrlPrefix(){
		String entId=getEntId();
		String url=CacheManager.getMemcache().get("RecordUrlPrefix_"+entId);
		   try {
			   if(StringUtils.isBlank(url)){
				   String sql = "select  t2.RECORD_FILE_URL   from CC_ENT_RES  t1  , CC_PETRA_RES t2 where t1.PETRA_ID = t2.PETRA_ID    and t1.ENT_ID =  ?";
				    url=this.getQuery().queryForString(sql, new Object[]{this.getEntId()});
				    if(StringUtils.isNotBlank(url)){
				    	if(url.lastIndexOf("/")==-1){
				    		url=url+"/";
				    	}
				    }
				    CacheManager.getMemcache().put("RecordUrlPrefix_"+entId, url);
			   }
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return url;
	}
}
