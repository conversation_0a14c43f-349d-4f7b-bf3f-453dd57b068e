package com.yunqu.ccportal.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppDaoContext;
import com.yunqu.ccportal.base.CommonLogger;

/**
 * Title:公告及备忘录消息
 * Description:获取公告及备忘录消息操作
 * <AUTHOR>
 */
@WebObject(name="messageView")
public class MessageViewDao extends AppDaoContext{
	
	private Logger logger = CommonLogger.logger;
	/**
	 * 获取紧急公告消息提醒
	 * @return
	 */
	@WebControl(name="getAffiche",type=Types.LIST)
	public JSONObject getAffiche() {
		String agent = UserUtil.getUser(request).getUserAcc();
		String current = EasyDate.getCurrentDateString();
		EasySQL sql = new EasySQL("SELECT t1.NOTICE_ID,t1.NOTICE_TITLE,t1.PUBLISH_TIME FROM");
		sql.append(this.getTableName("c_notes_info t1")+" LEFT JOIN");
		sql.append(this.getTableName("c_notes_recv t2")+" ON t1.NOTICE_ID = t2.NOTICE_ID");
		sql.append("WHERE 1=1");
		sql.append(3,"AND T1.notes_status =?");
		sql.append(0,"AND IS_READ = ?");
		sql.append(1,"AND IS_IMPORTANT = ?");
		sql.append("AND IS_RECALL = '0'");
		sql.append("AND t1.IS_DEL = '0'");
		sql.append(agent,"AND AGENT_ID = ?");
		sql.append(getEntId(),"and t1.EP_CODE=?");
		sql.append(getBusiOrderId(),"and t1.BUSI_ORDER_ID=?");
		sql.append(getEntId(),"and t2.EP_CODE=?");
		sql.append(getBusiOrderId(),"and t2.BUSI_ORDER_ID=?");
		sql.append(current,"AND INVALID_TIME >= ?");
		sql.append(current,"AND PUBLISH_TIME <= ?");
		sql.append("ORDER BY PUBLISH_TIME DESC");
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this)+">>sql:"+sql+";params>>"+ param);
		}		
		return this.queryForList(sql.getSQL(),sql.getParams());
	}
	/**
	 * 获取备忘录消息提醒
	 * @return
	 */
	@WebControl(name="getMemo",type=Types.LIST)
	public JSONObject getMemo() {
		String agent = UserUtil.getUser(request).getUserAcc();
		String current = EasyDate.getCurrentDateString();
		EasySQL sql = new EasySQL("SELECT ID,TITLE,CONTENT,REMIND_TIME FROM "+getTableName("C_CF_AGENT_MEMOS"));
		sql.append("WHERE 1=1");
		sql.append("AND STATUS='Y'");
		sql.append(agent,"AND USER_ACC =?");
		sql.append(current,"AND REMIND_TIME>=?");
		sql.append("ORDER BY REMIND_TIME DESC");
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this)+">>sql:"+sql+";params>>"+ param);
		}		
		
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
}
