package com.yunqu.ccportal.model;

import org.easitline.common.utils.excel.annotation.ExcelField;

public class AgentWorkStat {
	@ExcelField(title = "团队", order = 1)
	private String teamName;
	@ExcelField(title = "班组", order = 2)
	private String groupName;
	@ExcelField(title = "业务员", order = 3)
	private String agent;
//	@ExcelField(title = "登录时长", order = 3)
//	private String onlineTime;
	@ExcelField(title = "工作时长", order = 4)
	private String workTime;
	@ExcelField(title = "小休次数", order = 5)
	private String notReadyCount1;
    @ExcelField(title = "会议次数）", order = 6)
    private String notReadyCount2;
    @ExcelField(title = "培训次数", order = 7)
    private String notReadyCount3;
	@ExcelField(title = "小休时长（秒）", order = 8)
	private String notReadyTime1;
	@ExcelField(title = "会议时长（秒） ", order = 9)
	private String notReadyTime2;
	@ExcelField(title = "培训时长（秒）", order = 10)
	private String notReadyTime3;
	@ExcelField(title = "呼出状态空闲时长", order = 11)
	private String outboundStateTime;
	@ExcelField(title = "有效服务时长", order = 12)
	private String serviceTime;
	@ExcelField(title = "接话次数", order = 13)
	private String inSuccCount;
	@ExcelField(title = "呼出次数", order = 14)
	private String outSuccCount;
	@ExcelField(title = "通话时长", order = 15)
	private String totalCallTime;
	@ExcelField(title = "工时利用率", order = 16)
	private String servicePercent;
	
	public AgentWorkStat(){
		
	}

	public String getTeamName() {
		return teamName;
	}

	public void setTeamName(String teamName) {
		this.teamName = teamName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getAgent() {
		return agent;
	}

	public void setAgent(String agent) {
		this.agent = agent;
	}
/*
	public String getOnlineTime() {
		return onlineTime;
	}

	public void setOnlineTime(String onlineTime) {
		this.onlineTime = onlineTime;
	}
*/
	public String getWorkTime() {
		return workTime;
	}

	public void setWorkTime(String workTime) {
		this.workTime = workTime;
	}

	public String getNotReadyCount1() {
		return notReadyCount1;
	}

	public void setNotReadyCount1(String notReadyCount1) {
		this.notReadyCount1 = notReadyCount1;
	}

	public String getNotReadyCount2() {
		return notReadyCount2;
	}

	public void setNotReadyCount2(String notReadyCount2) {
		this.notReadyCount2 = notReadyCount2;
	}

	public String getNotReadyCount3() {
		return notReadyCount3;
	}

	public void setNotReadyCount3(String notReadyCount3) {
		this.notReadyCount3 = notReadyCount3;
	}

	public String getNotReadyTime1() {
		return notReadyTime1;
	}

	public void setNotReadyTime1(String notReadyTime1) {
		this.notReadyTime1 = notReadyTime1;
	}

	public String getNotReadyTime2() {
		return notReadyTime2;
	}

	public void setNotReadyTime2(String notReadyTime2) {
		this.notReadyTime2 = notReadyTime2;
	}

	public String getNotReadyTime3() {
		return notReadyTime3;
	}

	public void setNotReadyTime3(String notReadyTime3) {
		this.notReadyTime3 = notReadyTime3;
	}

	public String getServiceTime() {
		return serviceTime;
	}

	public void setServiceTime(String serviceTime) {
		this.serviceTime = serviceTime;
	}

	public String getInSuccCount() {
		return inSuccCount;
	}

	public void setInSuccCount(String inSuccCount) {
		this.inSuccCount = inSuccCount;
	}

	public String getOutSuccCount() {
		return outSuccCount;
	}

	public void setOutSuccCount(String outSuccCount) {
		this.outSuccCount = outSuccCount;
	}

	public String getTotalCallTime() {
		return totalCallTime;
	}

	public void setTotalCallTime(String totalCallTime) {
		this.totalCallTime = totalCallTime;
	}

	public String getServicePercent() {
		return servicePercent;
	}

	public void setServicePercent(String servicePercent) {
		this.servicePercent = servicePercent;
	}

	public String getOutboundStateTime() {
		return outboundStateTime;
	}

	public void setOutboundStateTime(String outboundStateTime) {
		this.outboundStateTime = outboundStateTime;
	}
	
}
