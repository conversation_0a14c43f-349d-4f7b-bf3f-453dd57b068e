package com.yunqu.ccportal.base;

import org.easitline.common.core.context.AppContext;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String VERSION_NAME="2.2.1";
	
	public final static String VERSION_DATE="2019-01-14";
	
	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)
	
	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)
	
	public final static String DS_READ_NAME= "yc-read-ds";     //默认数据源名称(读)
	
	public final static String APP_NAME = "cc-portal";     //应用
	
	private static AppContext context = AppContext.getContext("yc-api");
	
	public static final AppContext ccbase_context = AppContext.getContext("cc-base");
	
	/**
	 * 是否开启密码复杂程度
	 */
	public static final String setPwdRule = ccbase_context.getProperty("setPwdRule", "1");
	
	/**
	 * 服务接口PASSWORD
	 */
	public static String getServiceInterfacePwd() {
		String SERVICE_INTERFACE_PWD = ccbase_context.getProperty("SERVICE_INTERFACE_PWD", "yq_85521717");
		return SERVICE_INTERFACE_PWD;
	}
	
	
	/**
	 * 统计库名称
	 */
	public static String getStatSchema(){
		return context.getProperty("STAT_DB","stat");
	}
	
	public static final String OPEN_CALL_URL = ConfigUtil.getString(APP_NAME,"OPEN_CALL_URL", "N");
	
	/**
	 * 登陆版本
	 */
	public static final String LOGIN_VERSION = ConfigUtil.getString(APP_NAME,"LOGIN_VERSION", "1");
	public static final String LOGIN_PWD_LIMIT = ConfigUtil.getString(APP_NAME,"LOGIN_PWD_LIMIT", "N");
	/**
	 * 登陆页面LOGO
	 */
	public static final String LOGIN_LOGO = ConfigUtil.getString(APP_NAME,"LOGIN_LOGO", "/cc-portal/static/images/logo.png");
	/**
	 * 企业备案
	 */
	public static final String COPYRIGHT = ConfigUtil.getString(APP_NAME,"COPYRIGHT", "云趣科技版权所有");

	
	/**
	 * 登录后的首页，默认为 /cc-workbench/pages/portal.jsp
	 */
	public static final String WELCOME_PAGE_URL = ConfigUtil.getString(APP_NAME,"WELCOME_PAGE_URL", "/cc-workbench/pages/portal.jsp");
	
	public final static String  BUSI_ID = "007";
	
	/**
	 * 管理员
	 */
	public final static int ROLE_TYPE_MANAGER=1;
	
	/**
	 * 班长
	 */
	public final static int ROLE_TYPE_MONITOR=2;
	
	/**
	 * 坐席
	 */
	public final static int ROLE_TYPE_AGENT=3;
	/**
	 * 自定义角色
	 */
	public final static int ROLE_TYPE_CUSTOM =9;
	
	/**
	 * 任务执行状态    0:未执行
	 */
	public final static int TASK_OBJ_STATE_NO_EXECUTE = 0;       
	/**
	 * 任务执行状态    １:等待执行
	 */
	public final static int TASK_OBJ_STATE_WAIT_EXECUTE = 1;     
	
	/**
	 * 任务执行状态　２:执行中
	 */
	public final static int TASK_OBJ_STATE_EXECUTE_ING = 2; 
	
	/**
	 * 任务执行状态　5:预约中
	 */
	public final static int TASK_OBJ_STATE_EXECUTE_VISIT = 5;    
	/**
	 * 任务执行状态　9:执行完成'
	 */
	public final static int TASK_OBJ_STATE_EXECUTE_FINISH = 9;    
	
	/**
	 * 呼叫标志    1:呼叫成功 
	 */
	public final static int CALL_FLAG_SUCCESS = 1;       
	/**
	 * 呼叫标志    2:呼叫失败
	 */
	public final static int CALL_FLAG_FAIL = 2;          
	
	/**
	 * 执行结果  1 营销成功  
	 */
	public final static int EXEC_RESULT_SUCCESS = 1;  
	/**
	 * 执行结果  2 营销失败  
	 */
	public final static int EXEC_RESULT_FAIL = 2;     
	/**
	 * 执行结果  3 取消执行
	 */
	public final static int EXEC_RESULT_CANCEL = 3;   
	
	/**
	 * 预约完成状态     0：待处理 
	 */
	public final static int HANDLE_STATE_WAIT = 0;        
	/**
	 * 预约完成状态     １：已执行 
	 */
	public final static int HANDLE_STATE_SUCCESS = 1;     
	
	//工单状态，０：待处理　１：退回　２：已办结
	/**
	 * 工单状态，０：待处理
	 */
	public final static int ORDER_STATE_NO_DEAL = 0;
	/**
	 * 工单状态，１：退回
	 */
	public final static int ORDER_STATE_RETURN = 1;
	/**
	 * 工单状态，２：已办结
	 */
	public final static int ORDER_STATE_DEAL_FINISH = 2;
	
	/**
	 * 质检状态    1：待质检  
	 */
	public final static int QC_STATE_WAIT = 1;
	/**
	 * 质检状态     2：质检通过
	 */
	public final static int QC_STATE_PASS = 2;
	/**
	 * 质检状态      3：质检不通过
	 */
	public final static int QC_STATE_NO_PASS = 3;
	
	/**
	 * 客户满意度    1 满意 
	 */
	public final static int CUST_SATISFY_NICE = 1;
	/**
	 * 客户满意度    2 一般  
	 */
	public final static int CUST_SATISFY_NO_BAD = 2;
	/**
	 * 客户态度   3 不满意',
	 */
	public final static int CUST_SATISFY_BAD = 3;
	
	/**
	 * 数据准备标志，0 未申请
	 */
	public final static int READY_FLAG_NO = 0;
	/**
	 * 数据准备标志，1 申请中 
	 */
	public final static int READY_FLAG_WAIT = 1;
	/**
	 * 数据准备标志， 2 数据已准备
	 */
	public final static int READY_FLAG_FINISH = 2;
	
	/**
	 * 任务来源,1 云呼系统人工创建
	 */
	public final static int TASK_SOURCE_SYSTEM = 1;
	/**
	 * 任务来源， 2 接口创建，包括大数据活动创建接口
	 */
	public final static int TASK_SOURCE_BIGDATA = 2;
	
	/**
	 * 短信类型：1、任务营销短信 
	 */
	public final static int SMS_TYPE_TASK = 1;
	/**
	 * 短信类型：2、通知短信
	 */
	public final static int SMS_TYPE_NOTICE = 2;
	
	/**
	 * 短信模板状态，0 正常
	 */
	public final static int TEMP_STATE_NORMAL = 0;
	/**
	 * 短信模板状态，1 停用
	 */
	public final static int TEMP_STATE_STOP = 1;
	/**
	 * 短信模板状态，9 待审批
	 */
	public final static int TEMP_STATE_WAIT = 9;
	/**
	 * 短信模板审批状态，1 待审批 
	 */
	public final static int TEMP_AUTH_STATE_WAIT = 1;
	/**
	 * 短信模板审批状态，2 审批通过
	 */
	public final static int TEMP_AUTH_STATE_PASS = 2;
	/**
	 * 短信模板审批状态，3 审批不通过
	 */
	public final static int TEMP_AUTH_STATE_NO_PASS = 3;
	/**
	 * 大数据风控管理查询计费配置，BD_001 身份验证查询次数
	 */
	public final static String ENT_FEE_CONF_IDENTITY = "BD_001";
	/**
	 * 大数据风控管理查询计费配置，BD_002 还款风险查询次数
	 */
	public final static String ENT_FEE_CONF_REPAYMENT = "BD_002";
	/**
	 * 大数据风控管理查询计费配置，BD_003 信用报告查询次数
	 */
	public final static String ENT_FEE_CONF_CREDIT = "BD_003";

	/**
	 * 未分配任务对象
	 */
	public final static String TASK_AGENT_READY = "0";
	/**
	 * 分配任务对象到任务公海
	 */
	public final static String TASK_AGENT_POOL = "5";
	/**
	 * 封锁任务对象
	 */
	public final static String TASK_AGENT_LOCK = "6";
	
	
	/**
	 * 外呼号码状态，0 正常
	 */
	public final static int PREFIX_STATE_USE = 0;
	/**
	 * 外呼号码状态，1 停用
	 */
	public final static int PREFIX_STATE_STOP = 0;

	/**
	 * 门户标题
	 */
	public static final String PORTAL_TITLE = ConfigUtil.getString(APP_NAME,"PORTAL_TITLE", "");
	
	/**
	 * 门户logo
	 */
	public static final String PORTAL_LOGO = ConfigUtil.getString(APP_NAME,"PORTAL_LOGO", "/cc-portal/portal/images/logo.png");;
	/**
	 * 从ccbase模块获取是否开启修改密码界面配置项
	 */
	public static final String IS_SHOW_EDIT_PASSWORD = ccbase_context.getProperty("IS_SHOW_EDIT_PASSWORD", "");

	/**
	 * 从ccbase模块里获取 是否开启监控客户来电次数功能
	 */
	public static final String MONITOR_CALL_TIME = ccbase_context.getProperty("MONITOR_CALL_TIME", "N");;
	
	/**
	 * 个性化
	 */
	public static String PORTAL_INDEX_CSS=ConfigUtil.getString(APP_NAME, "PORTAL_INDEX_CSS", "");
	
	/**
	 * 个性化
	 */
	public static String PORTAL_FAVICON_ICO=ConfigUtil.getString(APP_NAME, "PORTAL_FAVICON_ICO", "/cc-portal/favicon.ico");
	
	public static String PORTAL_RES_CODE = ConfigUtil.getString(APP_NAME, "PORTAL_RES_CODE", "N");
	
	/**
	 * 开启坐席助手,Y-开启,N-不开启
	 */
	public static String AGENT_ASSISTANT = ConfigUtil.getString(APP_NAME, "AGENT_ASSISTANT", "N");
	
	/**
	 * 超时自动退出时间，单位（分钟），为0，不自动退出
	 */
	public static final int AUTO_LOGOUT_TIME_OUT = CommonUtil.parseInt(ConfigUtil.getString(APP_NAME,"AUTO_LOGOUT_TIME_OUT", "0"));
	
	
	/**
	 * 万和退出监听
	 * @return String
	 */
	public static String getLouginOutListener(){
		AppContext context = AppContext.getContext("cx-mix");
		return context.getProperty("LOUGINOUT_LISTENER","N");
	}
	
	/**
	 * 是否开启密码修改提醒 Y-开启 N-不开启
	 * @return
	 */
	public static String getPwdUpdateFlag(){
		return ConfigUtil.getString(APP_NAME,"PWD_UPDATE_FLAG", "N");
	}
	
	/**
	 * 门户界面是否显示常用通知
	 * @return
	 */
	public static String showNotes(){
		return ConfigUtil.getString(APP_NAME,"SHOW_NOTES", "N");
	}
	
	/**
	 * 开启密码修改提醒后，密码修改X天后要求用户修改密码
	 * @return
	 */
	public static int getPwdUpdateDays(){
		return CommonUtil.parseInt(ConfigUtil.getString(APP_NAME,"PWD_UPDATE_DAYS", "90"));
	}

	/**
	 * 缓存最近接听客户电话的坐席的时长  3.1#20211019-1
	 * @return
	 */
	public static int getSaveCustLatestAgentTime() {
		return CommonUtil.parseInt(ConfigUtil.getString(APP_NAME,"SAVE_CUST_LATESTAGENT_TIMES", "50"));
	}

	public static String getYcportalUrl() {
		// TODO Auto-generated method stub
		 String defaultUrl = "/yc-portal/pages/task/my-task-execute.jsp?from=myAutoTask&objId=#objId#&taskId=#taskId#";
		 return ConfigUtil.getString(APP_NAME,"YC_PORTAL_CALLOUT_URL", defaultUrl);
	}

	public static String  getDbStateConfig(){
		return ConfigUtil.getString(APP_NAME,"DB_ISOK", "Y");
	}
}
