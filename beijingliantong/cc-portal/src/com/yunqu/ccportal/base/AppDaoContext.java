package com.yunqu.ccportal.base;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
/**
 * dao base class
 * <AUTHOR>
 */

public class AppDaoContext extends DaoContext {
	
	protected EasyCache cache = CacheManager.getMemcache();
	
	private EasyQuery query=null;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_WIRTE_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected boolean loginCheck() {
		return true;
	}
	
	@Override
	protected void setQuery(EasyQuery query) {
		this.query=query;
	}

	@Override
	public EasyQuery getQuery(){
		String entId=getEntId();
		int _entId=Integer.valueOf(entId);
		EasyQuery easyQuery=null;
		if(query!=null){
			easyQuery=this.query;
		}else if(_entId%2==0){
			easyQuery=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		}else{
			easyQuery=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		easyQuery.setLogger(getLogger());
		return easyQuery;
	}
	protected String getTableName(String tableName){
		String dbName=getDbName();
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	protected String getTableName(String dbName,String tableName){
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	protected String getEntId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getEntId();
	}
	
	protected String getDbName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getSchemaName();
	}
	
	protected String getBusiOrderId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getBusiOrderId();
	}
	protected YCUserPrincipal getUserPrincipal(){ 
		return (YCUserPrincipal)request.getUserPrincipal();
	}
	//用户数据库ID 
	protected String getUserId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getUserId();
	}
	
	protected String getBusiId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getBusiId();
	}
	
	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	/**
	 * 获取技能组Id
	 */
	protected String[] getSkillGroupIds(){
		Object attribute = this.getUserPrincipal().getAttribute("skillGroupIds");

		try{
			String [] s = (String[]) attribute;
			this.error("skillGroupIds:"+s[0], null);
		}catch(Exception e){
		}
		if(attribute==null)return null;
		return (String[]) attribute;
	}
	
	/**
	 * 获取技能组Id
	 */
	protected String[] getSkillGroupIdChilds(){
		Object attribute = this.getUserPrincipal().getAttribute("skillGroupIdChilds");
		try{
			String [] s = (String[]) attribute;
			this.error("skillGroupIds:"+s[0], null);
		}catch(Exception e){
		}
		if(attribute==null)return null;
		return (String[]) attribute;
	}

	/**
	 * 获取角色类型
	 * @return
	 */
	protected int getRoleType(){
		YCUserPrincipal user = this.getUserPrincipal();
		return user.getRoleType();
	}
	
	/**
	 * 获取班组下得坐席
	 * @param groupId
	 * @param type
	 * @return
	 */
	protected String getSkillUserSql(String groupId, String type){
		if(StringUtils.isBlank(groupId)){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		if("3".equals(type)){
			sql.append("select t22.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t22 where 1=1");
			sql.append(" and t22.ENT_ID = '"+this.getEntId()+"'");
			sql.append(" and t22.BUSI_ORDER_ID = '"+getBusiOrderId()+"'");
			sql.append(" and t22.SKILL_GROUP_ID = '"+groupId+"'");
		}else{
			sql.append("select t22.USER_ID from ").append(getTableName("CC_SKILL_GROUP_USER t22"));
			sql.append(" left join ").append(getTableName("CC_SKILL_GROUP t33")).append(" on t22.SKILL_GROUP_ID = t33.SKILL_GROUP_ID");
			sql.append(" where t22.P_GROUP_ID = '"+groupId+"'");
			sql.append(" or t22.SKILL_GROUP_ID = '"+groupId+"')");
		}
		return sql.toString();
	}
	
	/**
	 * 获取权限sql,
	 * @param sqlKey	sql查询条件
	 * @return
	 */
	protected String getUserResSql(String sqlKey){
		String sql = "";
		YCUserPrincipal user = this.getUserPrincipal();
		if(user.getRoleType() == Constants.ROLE_TYPE_AGENT){				//座席
			sql = " and "+sqlKey+" = '"+user.getUserId()+"'";
		}else if(user.getRoleType() == Constants.ROLE_TYPE_MONITOR){			//班长
			String[] skillGroupIds = getSkillGroupIdChilds();
			if(skillGroupIds != null){					//存在技能组，获取该团队
				sql = " and "+sqlKey+" in ( select t5.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t5 where t5.ENT_ID = '"+user.getEntId()+"' and t5.BUSI_ORDER_ID = '"+user.getBusiOrderId()+"'";
					sql +=" and t5.SKILL_GROUP_ID '"+StringUtils.joinSql(skillGroupIds)+"')";
			}else{														//不存在技能组，获取个人
				sql +=" and "+sqlKey+" = '"+user.getUserId()+"'";
			}
		}
		return sql;
	}
	
	/**
	 * 获取技能组权限
	 * @param sqlKey
	 * @param pSkillId
	 * @return
	 */
	protected String getSkillRes(String sqlKey, String pSkillId){
		String sql = "";
		int roleType = this.getRoleType();
		String[] skillGroupIds = null;
		if(StringUtils.isNotBlank(pSkillId) && !"0".equals(pSkillId)){
			skillGroupIds = this.getChildSkill(pSkillId);
		}else if(roleType != Constants.ROLE_TYPE_MANAGER){
			skillGroupIds = getSkillGroupIdChilds();
			if(skillGroupIds != null && skillGroupIds.length == 1){
				skillGroupIds = this.getChildSkill(getSkillGroupIdChilds()[0]);
			}
			sql = " and "+sqlKey+" = -1";
		}
		if(skillGroupIds != null){					//存在技能组，获取该团队
			sql = " and "+sqlKey+" "+StringUtils.joinSql(skillGroupIds)+"";
		}
		return sql;
	}
	
	/**
	 * 获取部门下所有技能组
	 * @param pId
	 * @param reflash	是否刷新缓存
	 * @return
	 */
	private String[] getChildSkill(String pId){
		JSONArray array = null;
		String cacheKey = "SKILL_ID_FAMILY_"+pId;
		Object obj = cache.get(cacheKey);
		if(obj == null){
			array = new JSONArray();
			array.add(pId);
			try {
				List<JSONObject> list = this.getQuery().queryForList("select SKILL_GROUP_ID,P_GROUP_ID from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId()}, new JSONMapperImpl());
				if(list != null && list.size() > 0){
					this.addSkill(pId, array, list);
					cache.put(cacheKey, array, 3600);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		} else{
			array = (JSONArray)obj;
		}
		return JSONArray.toJavaObject(array, String[].class);
	}
	
	/**
	 * 获取子部门
	 * @param pId
	 * @param array
	 * @param list
	 */
	private void addSkill(String pId, JSONArray array, List<JSONObject> list ){
		for(JSONObject row : list){
			if(pId.equals(row.getString("P_GROUP_ID"))){
				array.add(row.getString("SKILL_GROUP_ID"));
				addSkill(row.getString("SKILL_GROUP_ID"), array, list);
			}
		}
	}
	
	protected JSONObject getTask(String taskId) {  
		try {
			if(StringUtils.isBlank(taskId))return new JSONObject();
			String taskObjeString = cache.get("TASK_"+taskId);
			if(StringUtils.isNotBlank(taskObjeString)){
				JSONObject taskObj =  JSONObject.parseObject(taskObjeString);
				return taskObj;
			}
			String sql = "select * from "+this.getTableName("CC_TASK")+"  where  TASK_ID = ? ";
			JSONObject taskObj = this.getQuery().queryForRow(sql , new Object[]{taskId},new JSONMapperImpl());
			cache.put("TASK_"+taskId,taskObj.toJSONString(),300); //300秒更新一次缓存
			return taskObj;
		} catch (Exception ex) {
			this.error("getTask() error,cause:"+ex.getMessage(), ex);
		}
		return new JSONObject();
		
	}
	protected String getSkillGroupId(){
		String[] skillGroupIds =(String[])this.getUserPrincipal().getAttribute("skillGroupIds");
		if(skillGroupIds!=null&&skillGroupIds.length>0){
			return skillGroupIds[0];
		}else{
			return "";
		}
	}
}
