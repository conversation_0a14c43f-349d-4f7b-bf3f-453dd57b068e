package com.yunqu.ccportal.service;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.ccportal.base.Constants;

/**
 * 任务来电弹屏服务
 * 
 * //	event.eventId //呼叫id，全局唯一
//		event.eventName //呼叫id，全局唯一
//		event.callSerialId //呼叫id，全局唯一
//		event.busiOrderId //业务订购ID，全局唯一
//		event.entId //企业Id，全局唯一
//		event.custId  //客户ID
//		event.createCause  //呼叫创建原因
 * <AUTHOR>
 *
 */
public class CallOutUrlService extends BaseCallUrlService {

	/**
	 * JSONObject eventInfo = new JSONObject();
		eventInfo.put("eventId", getEvent(messageId));  //事件
		eventInfo.put("eventName", getEventName(messageId));  //事件名称
		eventInfo.put("caller", iccsObject.getString("caller"));  //主叫
		eventInfo.put("called", data.getString("called"));  //被叫
		eventInfo.put("station", iccsObject.getString("telephone"));  //分机号
		eventInfo.put("callSerialId", userData.getString("callSerialId"));  //呼叫序列ID
		eventInfo.put("busiOrderId", userData.getString("busiOrderId"));  //业务订购ID
		if(userData.containsKey("taskId"))	eventInfo.put("taskId", userData.getString("taskId"));  //任务ID
		if(userData.containsKey("objId")){
			eventInfo.put("objId", userData.getString("objId"));  //任务对象ID
			eventInfo.put("custId", getCustId(entId,userData.getString("objId")));
		}
		eventInfo.put("createCause", iccsObject.getString("createCause"));  //呼叫创建原因
		eventInfo.put("busiId", userData.getString("busiId"));
		eventInfo.put("custTempId", userData.getString("custTempId"));
		eventInfo.put("agentId",iccsObject.getString("agentId"));
		eventInfo.put("entId", entId);
		
		
		JSONObject  showUrlParams = new JSONObject();
		showUrlParams.put("entId", entId);
		showUrlParams.put("custPhone", userData.getString("custPhone"));
		showUrlParams.put("skillId", agentModel.getSkillGroupId());
		showUrlParams.put("caller", iccsObject.getString("caller"));  //主叫
		showUrlParams.put("called", data.getString("called"));  //被叫
		showUrlParams.put("callSerialId", userData.getString("callSerialId"));  //呼叫序列ID
		showUrlParams.put("busiId", userData.getString("busiId"));
		showUrlParams.put("busiOrderId", userData.getString("busiOrderId")); //
		showUrlParams.put("agentId", iccsObject.getString("agentId")); //
		showUrlParams.put("userData", userData); //
	 
	    JSONArray urls = new JSONArray();
		JSONObject url1 = new JSONObject();
		url1.put("id", "myAutoTask");
		url1.put("title", "来电弹屏");
		url1.put("type", "1");
		url1.put("url", "www.sina.com.cn");
		urls.add(url1);
		
		请求json：
		{
		    "agentId":"8001@ekf",
		    "userData":{
		        "callSerialId":"83707465687098639471513",
		        "workCallbackUrl":"http://10.10.1.135:9059/yc-api/interface/callback",
		        "workVox":"83761635946029996996731",
		        "makeCallType":"agentManualCall",
		        "busiOrderId":"83761635946029996996731",
		        "custPhone":"17304037276",
		        "command":"respWork",
		        "realCalled":"17304037276"
		    },
		    "called":"17304037276",
		    "callSerialId":"83707465687098639471513",
		    "entId":"1000",
		    "busiId":"007",
		    "custPhone":"17304037276",
		    "createCause":"6",
		    "skillId":"9",
		    "caller":"88819286",
		    "busiOrderId":"83761635946029996996731",
		    "displayCustPhone":"17304037276"
		}
	 */
	@Override
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求外呼弹屏 params->"+jsonObject);
		
		if (jsonObject == null){
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求外呼弹屏参数为空,直接返回. ");
			return null;
		}
/*		
		String caller = jsonObject.getString("displayCustPhone");
		if(StringUtils.isBlank(caller)){
			caller = jsonObject.getString("caller");
		}
		jsonObject.put("caller", caller);*/
		
		String caller = jsonObject.getString("caller");
		String called = jsonObject.getString("called");
		String entId = jsonObject.getString("entId");
		String busiOrderId = jsonObject.getString("busiOrderId");
		String schema = SchemaService.findSchemaByEntId(entId);
		
		if(StringUtils.isBlank(busiOrderId)){
			busiOrderId = SchemaService.getCcBusiOrderIdByEntId(entId);
		}
		
		//3.1#20211027-1 对于非本门户的弹屏请求，直接返回空；ccbar会调用所有门户的弹屏服务，需要门户里控制是否返回；cc-portal支持的业务在cc-base里配置
		String busiId = jsonObject.getString("busiId");
		if(StringUtils.isBlank(busiId)){
			busiId = "0";
		}
		String portalBusiId = ConfigUtil.getString(Constants.APP_NAME,"PORTAL_BUSI_ID", "007");
		if(portalBusiId.indexOf(busiId) == -1){
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 非本门户的弹屏请求,直接返回. ");
			return null;
		}
		
		//获取该客户近X小时呼出该客户的次数
		int callOutTimes = getCallOutTimes(jsonObject,called,entId,busiOrderId,schema,true);
		jsonObject.put("callOutTimes", callOutTimes);
		jsonObject.put("callTimes", getCallTimes(jsonObject,called,entId,busiOrderId,schema,false)); //该客户的呼入次数，此时无需更新呼入次数
		
		//3.1#20210628-1 在来电弹屏时增加 servicePhone 显示平台号码
		jsonObject.put("servicePhone", caller);
		
		//设置客户号码
		jsonObject.put("custPhone", called);
		jsonObject.put("callTime", DateUtil.getCurrentDateStr());
		
		if(!"Y".equals(Constants.OPEN_CALL_URL)){
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 系统未开启弹屏,直接返回. ");
			return null;
		}
		try {
			// 缓存URL,从9060企业配置界面，订购的企业呼叫中心业务里，读取配置的url
			String incomingCall = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-base", "INCOMING_CALL");
			//等于0(不开启)，并且等于呼出的直接返回
			if(StringUtils.equals("0", incomingCall)){
				logger.info(CommonUtil.getClassNameAndMethod(this)+"  系统未开启呼出弹屏,直接返回 ");
				return null;
			}
			String type = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-base", "CALL_OPEN");
			JSONArray urls = new JSONArray();
			if(StringUtils.equals("0", type)) {
				String skillId = jsonObject.getString("skillId");
				try {
					JSONObject row = getCustCallUrl(entId, busiOrderId, schema, caller, skillId);
					if(ServerContext.isDebug()){
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求外呼自定义弹屏 busiInfo->"+row);
					}
					JSONObject urlInfo  = new JSONObject();
					if(row != null){
						//{"title":"百度搜索","url":"/yc-agent/pages/log/agent-work-log.jsp?caller=$caller&called=$called","type":"1","unique":"2"}
						String url = initParam(called,caller,"02",row.getString("CALLURL"),jsonObject);
						urlInfo.put("title", row.getString("NAME"));
						urlInfo.put("type", row.getIntValue("OPEN_TYPE"));
						
						int unique = 2;
						if(StringUtils.equals("Y", row.getString("IS_UNIQUE"))) {
							unique = 1;
						}
						urlInfo.put("unique", unique);
						urlInfo.put("id", getCallUrlPageId(unique,entId));
						//自定义弹屏添加类型openBusiType,和openBusiId
						if(url.indexOf("?")!=-1){
							url += "&openBusiType=02&openBusiId="+row.getString("ID");
						}else{
							url += "?openBusiType=02&openBusiId="+row.getString("ID");
						}
						urlInfo.put("url", url);
						urls.add(urlInfo);
					}else{
						logger.error("非唯一弹屏，查询弹屏记录为空.");
					}
					
				}catch (Exception e) {
					logger.error(e,e);
				}
			}else {
				//系统唯一弹屏
				try {
					JSONObject row = getSystemUniqueCallurl(entId, busiOrderId, schema);
					
					if(ServerContext.isDebug()){
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求外呼弹屏 busiInfo->"+row);
					}
					if(row!=null){
						JSONObject urlInfo  = row.getJSONObject("CALL_URL");
						if(urlInfo != null){
							JSONObject urlJson = row.getJSONObject("CALL_URL");		//{"title":"标题","url":"https://pz.xmhtwy.com/addview.jsp?callid=$callSerialId&callee=$called&caller=$caller","type":"1","unique":"1"}
							String url = initParam(called,caller,"02",urlJson.getString("url"),jsonObject);
							urlInfo.put("title", urlJson.getString("title"));
							urlInfo.put("type", urlJson.getIntValue("type"));
							
							int unique = urlJson.getIntValue("unique");
							urlInfo.put("unique", unique);
							urlInfo.put("id", getCallUrlPageId(unique,entId));
							
							urlInfo.put("url", url);
							urls.add(urlInfo);
						}
					}else{
						logger.error("唯一弹屏，查询弹屏记录为空.");
					}
					
				} catch (Exception ex) {
					logger.error(ex,ex);
				}
			}
			//9059没有配置企业呼叫中心的弹屏时，默认使用yc-agent的弹屏url，目前仅仅是兼容东风鸿泰的场景
			if(urls.size()<1){
				JSONObject taskUrl = new JSONObject();
				taskUrl.put("id", "voiceCallIn");
				taskUrl.put("title", "客服外呼弹屏");
				taskUrl.put("type", "1");
				taskUrl.put("url", getUrl(jsonObject));
				urls.add(taskUrl);
			}
			
			
			JSONObject retObj = new JSONObject();
			retObj.put("urls", urls);
			
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回外呼弹屏信息,urls->"+retObj);
			}
			
			return retObj;
			
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 处理外呼弹屏异常:"+e.getMessage(),e);
		}
		
		return null;
	}
	


	
	/**
	 * ccbar在接通客户电话时，会将随路数据传给来电弹屏url处理
	 * @param custPhone    客户号码
	 * @param servicePhone 平台号码
	 * @param callType     呼叫类型  01-呼入 02-话出
	 * @param url
	 * @param jsonObject
	 * @return
	 */
	private String initParam(String custPhone,String servicePhone,String callType,String url,JSONObject jsonObject){
		
		//客户第一次接入时，保持userData；转移后将第一次userData的部分数据写入到第二次的userData里
		String caller = jsonObject.getString("caller");
		JSONObject userData = jsonObject.getJSONObject("userData");
		if(userData!=null){
			String consultAgent = userData.getString("consultAgent");
			//属于坐席第一次接入，此时userData有较全的数据，要写入到缓存里
			if(StringUtils.isBlank(consultAgent)){
				CacheUtil.put("CC-CALL-USER-DATA-"+caller, userData.toJSONString());
				if(ServerContext.isDebug()){
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户是通话中的第一次接入，随路数据存入缓存,userData->"+userData);
				}
			}else{//属于第二次转移接入，此时需要将第一次的随路数据合并到本次的随路数据中去
				if(ServerContext.isDebug()){
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户是通话中的转移操作...");
				}
				String oldUserDataStr = CacheUtil.get("CC-CALL-USER-DATA-"+caller);
				if(StringUtils.isNotBlank(oldUserDataStr)){
					if(ServerContext.isDebug()){
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户是通话中的转移操作,获取第一次随路数据,oldUserData->"+oldUserDataStr);
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户是通话中的转移操作,替换后的随路数据,userData->"+userData);
					}
					JSONObject oldUserData = JSON.parseObject(oldUserDataStr);
					for(String key : oldUserData.keySet()){
						if(!userData.containsKey(key)){
							//内容里不包含json时才加入到userData
							String val = String.valueOf(oldUserData.get(key));
							if(StringUtils.isNotBlank(val) && val.indexOf("{") == -1){
								userData.put(key, val);
							}
						}
					}
					if(ServerContext.isDebug()){
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户是通话中的转移操作,替换后的随路数据,userData->"+userData);
					}
					jsonObject.put("userData", userData);
				}
			}
		}
		
		url = addUrlParamFromJson(custPhone,servicePhone,callType,url,jsonObject);
		
		
		return url;
	}
	
}
