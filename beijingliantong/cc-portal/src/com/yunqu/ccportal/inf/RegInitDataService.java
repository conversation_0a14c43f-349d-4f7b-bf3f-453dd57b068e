package com.yunqu.ccportal.inf;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.service.BaseRegInitDataService;
import com.yunqu.ccportal.base.CommonLogger;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.base.QueryFactory;
/**
 * 提供本模块需要初始化的数据，如数据字典、定时任务等
 * 提供的数据由相关管理模块通过服务类接口采集
 */
public class RegInitDataService extends BaseRegInitDataService{
	
	@Override
	public String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	public Logger getLogger() {
		return CommonLogger.logger;
	}

	@Override
	public EasyQuery getEasyQuery() {
		return QueryFactory.getWriteQuery();
	}


	/**
	 * 获取通知对象配置数据，需要被具体模块重写
	 * @return
	 */
	public JSONArray getNoticeInfo() {
		// TODO Auto-generated method stub
		return null;
	}

	/**
	 * 获取该模块里的定时任务,需要被具体模块重写
	 * @return
	 */
	public JSONArray getInitJobs() {
		return null;
	}
	
	/**
	 * 获取i18n数据,需要被各模块重写
	 * @return
	 */
	public JSONArray getI18n(){
		return null;
	}
	

	/**
	 * 获取本模块所需的数据字段集合，需要子类重写
	 * @return
	 */
	public JSONArray getInitDict() {
		
		return null;
	}
	
}
