var newMessageTips = {
	time: 0,
	title: document.title,
	timer: null,
	// 显示新消息提示执行方法  
	show: function() {
		var title = newMessageTips.title;
		// 定时器，设置消息切换频率闪烁效果就此产生  
		newMessageTips.timer = setTimeout(function() {
			newMessageTips.time++;
			newMessageTips.show();
			if (newMessageTips.time % 2 == 0) {
				document.title = "【新消息】" + title
			} else {
				document.title = "【　　　】" + title
			};
		}, 600);
		return [newMessageTips.timer, newMessageTips.title];
	},
	// 取消新消息提示方法  
	clear: function() {
		clearTimeout(newMessageTips.timer);
		document.title = newMessageTips.title;
	}
};
if (typeof(yconfig) == "undefined") yconfig = {}
var agentListFromCcbar = [];
var _ccbarDebug = yconfig.debugerMode; //开启debugger模式
var notification;

//全媒体接入人数变化
top.mediaUserListListener = function(size) {
	top.userChatSize = size;
}
//状态名更
ccbarEvent.addEvent('agentStateSync', function(agentState) {
	$(".curstatus").text(getI18nValue(agentState.stateDesc));

	myCCbar.agentId = agentState.agentId;
	console.log("更改状态", agentState);
	
	var state = agentState.state;
	// console.log('isShowVideoBtn',isShowVideoBtn)
	if ((state == "BUSY" || state == "IDLE" || state == "OCCUPY" || state=="ALERTING" || state== "TALK") && isShowVideoBtn) {
	// if (isShowVideoBtn) {
		$('#videoBtn').show()
	}else{
		$('#videoBtn').hide()
	}
	
	if (state == 'TALK') {
		$('#videoTransfer_Father').show()
		$('#videoConsultation').show()
	}else{
		$('#videoTransfer_Father').hide()
		$('#videoConsultation').hide()
	}
	

	//20201019 由于平台签出时返回的掩码为3，此时logoff可用，应该是不可用，只能在portal里自行处理
	var logoff = false;
	if (agentState.funcMask) {
		logoff = agentState.funcMask.logoff;
	}
	$('#ccbar').attr('state', state)
	if ("LOGOFF" == state && logoff) {
		$('#agentLogoff').css('display', 'none');
	} else {
		$('#agentLogoff').css('display', '');
	}

	if (state == 'TALK') {
		// $('[data-ccbartype=agentnotready]').addClass('disabled'); 3.1#20211230-1 坐席通话过程中允许示忙
	}

    if (top.showPhoneDirBtn == "1") {
        $('#phoneDirBtn').show()
    } else {
        $('#phoneDirBtn').hide()
    }

    if ($('#addressWin').length) {
        var message = {
            ccbarState: state
        }
        $('#addressWin').find('iframe')[0].contentWindow.postMessage(message, '/')
    }
});
ccbarEvent.addEvent('agentStateChange', function(msg) {
	var userData = msg.userData || {};
	// console.log("userData",userData)
	if (typeof userData == 'string') userData = JSON.parse(userData);
	if (top.portalListener) top.portalListener(msg.cmddata, userData);
}); 

/*调用子框架*/
function callChildrenFunc(funcName, iframeId, args) {
	document.getElementById(iframeId).contentWindow[funcName](args);
}

function changeAuto(val) {
	CallControl.autoAnswer(val, function(result) {
		if (result.state) {
			$("#autoanswercall").prop('checked', val);
			localStorage.setItem('isAutoAnswer', val);
		} else {
			$("#autoanswercall").prop('checked', !val);

		}

	});
}

var isShowVideoBtn = false,number_Video = 0;
function controlVideoBtn() {
   setTimeout(function () {
   	if(top.showVideoBtn == '1'){
   		isShowVideoBtn = true;
   	}else{
   		$('#videoBtn').css('display','none')
   		isShowVideoBtn = false;
		if(number_Video < 5){
			number_Video++;
			controlVideoBtn()
		}
   	}
   },1000)
}
$(function() {
	//var agent_phone = "8009";//话机号码
	ccbar_plugin.init('', '', 'false');
	audio_player.init();
	initAgentSel(); //初始化坐席选择

	//自动应答
	$("#autoanswercall").on("change", function() {
		var val = $(this).prop('checked');
		changeAuto(val)
	});
	 
	controlVideoBtn()
	
	
	//视频客服
	$("#spkf").on("mouseenter", function() {
		$('#spkf_btn').show()
	});
	$("#spkf").on("mouseleave", function() {
		$('#spkf_btn').hide()
	});
	//视频外呼
	$("#videoMakeCall").on("click", function() {
		CallControl.videoMakeCall($("#customCalledInput").val())
		
		// $('#videoTransfer_Father').show()
		// $('#videoConsultation').show()
	});
	//视频转移
	$("#ccbar_agent_sel3").on("click", function() {
		CallControl.videoTransfer($("#ccbar_select_groups_agent2").val(), $("#customCalledInput").val(),'',1)
	});
	//视频咨询
	$("#ccbar_agent_sel4").on("click", function() {
		CallControl.videoConsultation($("#ccbar_select_groups_agent2").val(), $("#customCalledInput").val(),'',1)
	});

    $('#phoneDirBtn').on('click', function() {
        top.popup.layerShow({
            id: 'addressWin',
            title: getI18nValue('通讯录'),
            type: 2,
            offset: '100px',
            area: ['60%', '60%'],
            success: function(layero, index) {
            }
        }, '/cc-base/pages/basis/addressBook.jsp', {ccbarState: $('#ccbar').attr('state')})
    })

	//记录工作模式

	//坐席信息初始化
	CallControl.ccbarInit(function(result) {
		var agentList = result.data.agentPhones;
		agentListFromCcbar = agentList;
		if (agentList.length == 0) {
			$("#ccbar-phone-select").hide();
		} else if (agentList.length == 1) {
			$("#ccbar-phone-input").val(agentList[0]);
			$("#ccbar-phone-select").hide();
		} else {
			$("#ccbar-phone-input").hide();
			var html = '';
			for (var i = 0; i < agentList.length; i++) {
				html += '<option value="' + agentList[i] + '">' + agentList[i] + '</option>';
			}
			$("#ccbar-phone-select").html(html);
		}
		//不需要输入或者选择工号
		if (CallControl.agentLogin == 'auto') {
			$("#ccbar-phone-input,#ccbar-phone-select").hide();
		}

		if (result.data.voiceAgent == 'off') {
			$("#voiceAgent").hide();
			$("#voiceSwitch").prop("checked", false);
		}
		if (result.data.multiMediaAgent == 'off') {
			$("#multiMediaAgent").hide();
			$("#mediaIframeBtn").hide();
			$("#multiMediaSwitch").prop("checked", false);

		}

		//置忙原因
		var busyTypes = result.data.busyTypes;
		var html = $.templates('#ccbarNotReadyBtns-template').render(result.data)
		$('#ccbarNotReadyBtns').html(html)

		//国际化加载置忙原因
		execI18n();

	});

	$("#ccbarCustomPhone").remove();

	$(document).on("keydown", function(e) {
		if (e.keyCode == 116 && CallControl.isLogoned) {
			var flag = confirm(getI18nValue('坐席已签入,是否刷新页面?'));
			if (flag) {
				CallControl.logoff();
				location.reload();
			} else {
				return false;
			}
		}
	});

	//  if(localStorage){
	// 	var phone = localStorage.getItem('phone');
	// 	$("#ccbar-phone-input").val(phone);
	// }

	/*全媒体相关*/
	//传入消息到全媒体
	CallControl.onRespMediaEvent = function(mediaEvent) {
		console.log('收到在线客服消息', mediaEvent);
		callChildrenFunc('handelMediaEvent', 'agentChatIframe', mediaEvent);
		newMessageTips.show();
	}

	$('body').on("click", newMessageTips.clear)

	/*挂机*/
	CallControl.onEventSessionTimeout = function(data) {
		layer.alert(data.content, function() {
			location.reload();
		})
	}

	/*签出回调*/
	ccbarEvent.addEvent('logoff', function(event) {
		try {
			callChildrenFunc('handelMediaLogoff', 'agentChatIframe', '')
		} catch (err) {
			console.log("全媒体页面可能未打开或未加载")
		}
		$('#agentsel2').fadeOut();
		$(".needLogonFunc").removeClass("logoned");
		//$(".needLogonFunc").addClass("multiMediaAgent");
		$("#agent").show();

		//$("#agent").addClass("logoff");
		//$("#agent>span").text("签入");
		//清除坐席外显号码
		$("#ccbar_phoneCallerList").html("");
		$(".ccbarVoiceCtrl").hide();
		if ($("#multiMediaSwitch").prop('checked')) {
			top.CallControl.ws.disconnect();
		}
		window.onbeforeunload = function() {
			return getI18nValue("确认离开当前页面吗？如话机已签入请先签出!");
		}
	});


	/*外呼*/
	$('#customCalledInput').on("keyup", function(event) {
		if (event.keyCode == "13") { //keyCode=13是回车键)
			event.preventDefault()
			agentMakeCall();
		}
	});

	$("#customMakeCallBtn").on("click", function() {
		agentMakeCall();
		return false;
	});

	function agentMakeCall() {
		var makecall = CallControl.getFunc("makecall");
		if (makecall) {
			var num = $("#customCalledInput").val();
			var test1 = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+$/
			var reg = /(^((\d{11})|(0)(\d{11})|(\d{7,8})|(\d{4}|\d{3})(\d{7,8}))$)/;
			var reg2 = /(^((1)(\d{2,5})|(9)(\d{2,5}))$)/; //紧急号码或服务电话
			// if(new RegExp(reg).test(num)||new RegExp(reg2).test(num)||new RegExp(test1).test(num)){

			// }else{
			//     tipsMsg("请输入正确的电话号码")
			//     return;
			// }
			ccbar_plugin.callControl.makeCall(num, {
				makeCallType: 'agentManualCall'
			});
		} else {
			tipsMsg(getI18nValue("当前不可外呼!"))
		}
	}


	/*右上角的按钮*/
	$('#ccbar-phone-input').on("keyup", function(event) {
		if (event.keyCode == "13") { //keyCode=13是回车键)
			event.preventDefault();
			//var num = $('#ccbar-phone-input').val();
			agentLogonSwitch();
		}
	});
	$('#login-input .login-btn').on("click", function(event) {

		event.preventDefault();
		//var num = $('#ccbar-phone-input').val();
		agentLogonSelect();

	});

	$("#agent").on("click", agentBtn);

	function agentBtn() {
		var state = CallControl.getState();
		if (state == null || state == 'LOGOFF') {
			//语音坐席,非多媒体坐席,只有一个话机号码
			if (CallControl.voiceAgent == 'on' &&
				CallControl.multiMediaAgent == 'off' &&
				agentListFromCcbar.length == 1) {
				agentLogonSelect();
				//多媒体坐席
			} else if (CallControl.voiceAgent == 'off' &&
				CallControl.multiMediaAgent == 'on') {
				agentLogonSelect()
			}
			$("#login-input").toggle();

		} else {
			agentLogoff();
		}
	}

	function agentLogonSwitch() {
		var type = $("input[name=agenttype]:checked").val();
		if (type == "voiceSwitch") {
			var num = $('#ccbar-phone-input').val();
			agentLogonByPhonenum(num);
		} else if (type == "multiMediaSwitch") {
			multiMediaLogon();
		}
	}

	function agentLogonSelect() {
		var multiMediaSwitch = $("#multiMediaSwitch").prop('checked') ? 'on' : 'off';
		var voiceSwitch = $("#voiceSwitch").prop('checked') ? 'on' : 'off';
		if (ccbarReadymode == "1") {
			var data = {
				'multiMediaSwitch': multiMediaSwitch,
				'voiceSwitch': voiceSwitch,
				readyMode: 'notReady',
				state: '1'
			};
		} else {
			var data = {
				'multiMediaSwitch': multiMediaSwitch,
				'voiceSwitch': voiceSwitch,
				readyMode: 'ready',
				state: '2'
			};
		}
		if (voiceSwitch == 'on') {
			var phoneNum = '';
			if (agentListFromCcbar.length > 1) {
				phoneNum = $('#ccbar-phone-select').val();
			} else {
				phoneNum = $('#ccbar-phone-input').val();
			}
			if (phoneNum == "" && CallControl.agentLogin != 'auto') {
				ccbar_popup.msg(getI18nValue("请输入话机号码"));
				return;
			} else {
				data.phoneNum = phoneNum;
			}
		}

		CallControl.isMultiMedia = voiceSwitch == 'off' && multiMediaSwitch == 'on' ? true : false;
		CallControl.logon(data.phoneNum, function(result) {
			if (result.data.code == 'succ') {
				CallControl.sbcAddr = result.data.result.sbcAddr;
				CallControl.phoneType = result.data.result.phoneType;
				CallControl.inbound = result.data.result.inbound;
				CallControl.outbound = result.data.result.outbound;
				if (!CallControl.isMultiMedia) {
					callerphone = function(rs) {
						if (localStorage) {
							localStorage.setItem('phone', phoneNum)
						}

						if (rs.resultCode == '000') {
							CallControl.wakeupSIP();
						}
					}
				} else {
					callerphone = function() {}
				}

				if (result.data.result.sbcAddr) {
					CallControl.sbcAddr = result.data.result.sbcAddr;
				} else {
					CallControl.sbcAddr = "";
				}
			} else {
				tipsMsg(result.data.content);
			}
		}, data);
	}

	/*只需要话机号码的签入方式*/
	function agentLogonByPhonenum(agent_phone) {
		if (agent_phone == "") {
			ccbar_popup.msg(getI18nValue("请输入话机号码"));
			return;
		}
		CallControl.isMultiMedia = false;

		CallControl.logon(agent_phone, function(result) {
			if (result.data.code == 'succ') {
				callerphone = function(result) {
					if (localStorage) {
						localStorage.setItem('phone', agent_phone)
					}
					if (result.data.result.phoneType != '4' && result.data.result.sbcAddr) {
						oprIfm.location.href = result.data.result.sbcAddr; //改写拉起链接
					}

				}
			} else {
				ccbar_popup.msg(result.data.content);
			}
		})
	}

	function multiMediaLogon() {
		CallControl.multiMediaLogon(function(result) {
			if (result.data.code == 'succ') {
				callerphone = function() {
					//$(".needLogonFunc").addClass("multiMediaAgent");
				}
			} else {
				ccbar_popup.msg(result.data.content);
			}
		})
	}

	/*全媒体签入*/
	CallControl.multiMediaLogon = function(callback) {
		CallControl.isMultiMedia = true;
		var data = {
			'multiMediaSwitch': 'on',
			'voiceSwitch': 'off'
		};
		$.ajax({
			dataType: 'jsonp',
			jsonp: "callbackFunc",
			jsonpCallback: "jsonpCallbackLogon",
			contentType: "application/x-www-form-urlencoded; charset=UTF-8",
			url: this.getContextPath() + '/yc-ccbar/AgentEvent?action=login',
			data: {
				'cmdJson': JSON.stringify(data)
			},
			timeout: 5000, //5秒超时
			success: function(result) {
				if ($.isFunction(callback)) {
					callback(result);
				}
				if (result.data.code == 'succ') {
					CallControl.longPolling('logon');
					if (result.data.result.sbcAddr) {
						CallControl.sbcAddr = result.data.result.sbcAddr;
					} else {
						CallControl.sbcAddr = "";
					}
				} else {

					console.log("签入失败")
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {}
		});
	}

	/*需要各项配置的的SDK签入方式*/
	function agentLogon() {
		ccbarsdk.logon(agent_agentId, $.md5(agent_token), agent_phone, function(result) {
			if (result.data.code == 'succ' && result.data.result.sbcAddr) {
				callerphone = function() {
					console.log("拉起话机");
					oprIfm.location.href = result.data.result.sbcAddr; //改写拉起链接
				}
			}
		})
	}

	/*签出*/
	function agentLogoff() {
		if (CallControl.getFunc('logoff')) {
			CallControl.logoff(function(result) {
				try {
					if (result.state != '1') {
						layer.msg(result.msg, {
							icon: 7
						})
					}
				} catch (e) {}

			});
		}
	}
});

function initAgentSel() {
	$("select[name='ccbar_select_groups2']").off('change').on('change', function(e) {
		var groupId = $(this).val();
		if (groupId == '') {
			$("#ccbar_select_groups_agent2").html('');
			return
		};

		CallControl.agentList(groupId, function(result) {
			if (result.state) {
				var groups = result.data.result.groups;
				var agents = result.data.result.agents;

				var agentsHtml = '<option value="">' + getI18nValue('请选择') + '</option>';
				for (var j = 0; j < agents.length; j++) {
					agentsHtml += '<option value="' + agents[j].AGENT_ID + '">' + agents[j].AGENT_NAME +
						'</option>'
				}
				$("#ccbar_select_groups_agent2").html(agentsHtml);
			}
		});
	});

	$("#ccbar_agent_sel2").off('click').on('click', function() {
		var val = $("#ccbar_select_groups_agent2").val();
		if (val != '') {
			$("#customCalledInput").val(val);
			$('#agentsel2').fadeOut()
		}
	});
}

function openAgentSel(type) {
	if (type == 1) {
		$('#ccbar_agent_sel2').hide()
		$('#ccbar_agent_sel3').show()
		$('#ccbar_agent_sel4').hide()
		
	}else if(type == 2) {
		$('#ccbar_agent_sel3').hide()
		$('#ccbar_agent_sel2').hide()
		$('#ccbar_agent_sel4').show()
	}else {
		$('#ccbar_agent_sel3').hide()
		$('#ccbar_agent_sel2').show()
		$('#ccbar_agent_sel4').hide()
	}
	
	
	if (CallControl.getFunc('makecall')  || type == 1 || type == 2) {
		$("#ccbar_select_agents2").html('');
	
		CallControl.agentList('', function(result) {
			if (result.state) {
				var groups = result.data.result.groups;
				var agents = result.data.result.agents;
				var groupsHtml = '<option value="">' + getI18nValue('请选择技能组') + '</option>';
				for (var i = 0; i < groups.length; i++) {
					groupsHtml += '<option value="' + groups[i].SKILL_GROUP_ID + '">' + groups[i]
						.SKILL_GROUP_NAME + '</option>'
				}
	
				$("#ccbar_select_groups2").html(groupsHtml);
				$("#ccbar_select_agents2").html('');
				$("#agentsel2").fadeIn();
			} else {
				tipsMsg(result.data.content);
			}
		});
	} else {
		tipsMsg(getI18nValue('当前不允许外呼'))
	}
}

function showCallBtn() {
	$("#agentCallIframeBtn").show();
}

function hideCallBtn() {
	$("#agentCallIframeBtn").hide();
}


function closeCallPanel() {
	if (parent.CallControl.getFunc('clearcall')) {
		parent.ccbar_plugin.callControl.clearCall();
		hideCallBtn();
	}
}

function showMediaBtn() {
	$("#mediaIframeBtn").show();
}

function hideMediaBtn() {
	$("#mediaIframeBtn").hide();
}


/*发起多媒体*/
function openMediaCenter() {
	showMediaBtn();
	$("#mediaIframeBtn>a").click();
}


function toggleWorkMode(workMode) {
	var workModeType = workMode || CallControl.workModeType;
	if (workModeType == 'inbound') {
		CallControl.workMode('outbound', function(result) {
			if (result.state) {
				CallControl.workModeType = 'outbound';
				tipsMsg(getI18nValue('已切换到呼出模式'));
				localStorage && localStorage.setItem('rememberWorkMode', 'outbound');
			} else {
				tipsMsg(result.data.content)
			}
		});

	} else if (workModeType == 'all' || workModeType == 'undefined') {
		CallControl.workMode('inbound', function(result) {
			if (result.state) {
				tipsMsg(getI18nValue('已切换到呼入模式'));
				CallControl.workModeType = 'inbound';
				localStorage && localStorage.setItem('rememberWorkMode', 'inbound');
			} else {
				tipsMsg(result.data.content)
			}
		});
	} else if (workModeType == 'outbound') {
		CallControl.workMode('all', function(result) {
			if (result.state) {
				tipsMsg(getI18nValue('已切换到自动模式'));
				localStorage && localStorage.setItem('rememberWorkMode', 'all');
			} else {
				tipsMsg(result.data.content)
			}
		});
	}
}

if (entId == undefined) var entId = yconfig.entId;

function agentLogout() {
	if (CallControl.isLogoned) {
		if (CallControl.getFunc('logoff')) {
			CallControl.logoff();
			CallControl.closeSIP();
			location.href = '/cc-portal/workbench?action=loginPage';
		} else {
			tipsMsg(getI18nValue('当前不允许退出'));
		}
	} else {
		CallControl.closeSIP();
		location.href = '/cc-portal/workbench?action=loginPage';
	}
}

/*来电相关*/
function closeInboundPopup() {
	if (CallControl.getFunc('clearcall')) {
		CallControl.clearcall();
	}
	$("#answercallpanel").fadeOut();
}

/**注册状态变更事件**/
ccbarEvent.addEvent('agentStateSync', function(agentState) {
	//自动应答状态
	if (agentState.autoAnswer != undefined) {
		var flag = agentState.autoAnswer == 'true' ? true : false;
		$("#autoanswercall").prop('checked', flag);
	}
	$('#ccbar').attr('state', agentState.state)
});

/*来电弹屏*/
ccbarEvent.addEvent('evtConnected', function(callEvent) {

	console.log('来电', callEvent);
	//来电号码写入浏览器，便于其他地方获取来电号码
	if (callEvent.event.caller) {
		window.sessionStorage.setItem("cc-latest-caller", callEvent.event.caller);
	}
	if (callEvent.userData && callEvent.userData.makeCallType && callEvent.userData.makeCallType ==
		'agentManualCall') {
		return false;
	}
	if (callEvent.urls && callEvent.urls.length > 0) {
		for (var i = 0; i < callEvent.urls.length; i++) {
			var url = callEvent.urls[i].url;
			if (callEvent.custInfo) {
				if (callEvent) {}
				var prarms = 'custInfo=' + JSON.stringify(callEvent.custInfo);
				if (url.indexOf("?") > -1) {
					url = url + '&' + prarms
				} else {
					url = url + '?' + prarms
				}
			}
			if (callEvent.callSerialId) {
				var prarms = 'callSerialId=' + callEvent.callSerialId;
				if (url.indexOf("?") > -1) {
					url = url + '&' + prarms
				} else {
					url = url + '?' + prarms
				}
			}
			var type = callEvent.urls[i].type;
			if (url.indexOf("?") > -1) {
				url = url + '&parentTabId=' + callEvent.urls[i].id
			} else {
				url = url + '?parentTabId=' + callEvent.urls[i].id
			}
			if (type == '1') {
				_createPage({
					url: url,
					title: callEvent.urls[i].title,
					id: callEvent.urls[i].id
				});
			} else if (type == '2') {
				window.open(url, callEvent.urls[i].title);
			} else {
				linkOpenWindow(url, callEvent.urls[i].title);
			}
		}
	}
});

function linkOpenWindow(url, title) {
	var id = new Date().getTime() + 'templink';
	var html = '<a style="display:none;" target="_blank" href="' + url + '" id="' + id + '"></a>'
	$("body").append(html)
	document.getElementById(id).click();
	$("#" + id).remove();
}

var myCCbar = {
	agentId: '',
}

/*CallControl.callerList = function(callback) {
    var data = {
        "event": "callerList"
    };
    ccbarRequestSend(this.getContextPath() + "/cc-base/servlet/phone?action=CallerList", cmdJson(data), function(result) {
        ccbarDebugger(result.msg);
        if (result.data.result.callers) {
            CallControl.callers = result.data.result.callers
        }
        if ($.isFunction(callback)) {
            callback(result);
        }
    }, {
        jsonpCallback: 'JSONcallbackCallerList'
    });
}*/

/*签入回调*/
ccbarEvent.addEvent('logon', function(event) {
	if (event.result == 'Fail') {
		return
	}
	//记录自动应答
	var _autoAnswer = localStorage.getItem('isAutoAnswer');
	if (_autoAnswer == null) {
		_autoAnswer = yconfig.getAutoAnswerFlag();
	}
	_autoAnswer = _autoAnswer == 'true' ? true : false;
	setTimeout(function() {
		changeAuto(_autoAnswer)
	}, 500)

	// $("#autoanswercall").prop('checked',_autoAnswer);
	myCCbar.agentId = event.agentId;
	/*var workMode = localStorage&&localStorage.getItem('rememberWorkMode');
	if(workMode==null){
		workMode=yconfig.getWorkMode();
	}
	if(workMode&&$("#voiceSwitch").prop('checked')){
		setTimeout(function(){
			CallControl.workMode(workMode);
		},1000)
	}*/
	try {
		callChildrenFunc('handelMediaLogon', 'agentChatIframe', '')
	} catch (err) {
		//console.log("全媒体页面可能未打开或未加载")
	}

	if (CallControl.isMultiMedia) {
		openMediaCenter();
	}


	//新建websocket
	if ($("#multiMediaSwitch").prop('checked')) {
		$(".page-link[data-id='agentChatIframe']").click();

		if (WebSocket && top.CallControl.ws) {
			var tempHost = location.origin;
			var wshost = tempHost.replace('http', 'ws');
			wshost = wshost + '/yc-ccbar/websocket/ws';

			top.CallControl.ws.ws && top.CallControl.ws.ws.readyState === WebSocket.OPEN ? '' : top.CallControl
				.ws.connect(wshost);

			ccbarWs = setInterval(function() {
				top.CallControl.ws.send('heartbeat', 'sendMessage', {
					'cmdJson': JSON.stringify({
						msgType: "heartbeat"
					})
				});
			}, 10000);
		} else {
			layer.alert('您的浏览器不支持Websocket,全媒体或无法正常使用');
		}
	}
	//callChildrenFunc( 'handelMediaLogon','agentChatIframe' ,null)

	callerphone(event);
	$("#login-input").hide();
	// $("#agent").removeClass("logoff");
	$("#agent").hide();
	//$("#agent>span").text("签出");
	$(".needLogonFunc").addClass("logoned");
	$("#voiceSwitch").prop('checked') ? $(".ccbarVoiceCtrl").show() : $(".ccbarVoiceCtrl").hide();
	//获取坐席外显号码
	if ($("#voiceSwitch").prop('checked')) {
		CallControl.callerList(function(result) {
			console.log("外显号码发起呼叫!!!!!!!!!")
			var tempOptions = '';
			if (result.data.result.callers) {
				for (var caller of result.data.result.callers) {
					tempOptions += '<option value="' + caller + '">' + caller + '</option>'
				}
				if (result.data.result.callers.length > 1) {
					$(".ccbar-department").removeClass("onlyone");
				} else {
					$(".ccbar-department").addClass("onlyone");
				}

			} else {
				tipsMsg(getI18nValue('无外显号码,不允许呼叫.'));
			}
			$("#ccbar_phoneCallerList").html(tempOptions);
			$("#ccbar_select_caller2").html(tempOptions);
			console.log('外呼号码1234',$("#ccbar_select_caller2"),tempOptions)
		});
	}

});

ccbarEvent.addEvent('evtAltering', function(event) {
	if (evtNetWorkReachedClock) {
		clearTimeout(evtNetWorkReachedClock)

	}
	$('#agentsel2').fadeOut();
	if (event.userData && event.userData.skillGroupName) {
		$('.ccbar-skillinfo').html('<span style="color:blue;">' + event.userData.skillGroupName + '</span>');
	}
	if (event.event.createCause == 6 || event.event.createCause == 8 || event.event.createCause == 29) {
		$("#answercallpanel").fadeOut();

		return;
	}
	if ($("#autoanswercall").prop('checked')) {
		CallControl.answerCall();
		audio_player.play(yconfig.getCallBeginUrl(), false);
		//3.1#20211019 来电振铃弹屏，优先显示队列名称
		var skillGroupName = event.event.queueName || event.event.skillGroupName || '';
		var html = "客户号码：" + event.event.custPhone + "</br>";
		html += "技能组：" + skillGroupName + "</br>";
		top.layer.msg(html, {
			offset: 'rt',
			closeBtn: 1,
			time: 12000
		});
		$("#answercallpanel").fadeOut();
	} else {
		$("#createCauseName").text(ccbar_config['createCause'][event.event.createCause]);
		var provinceName = event.event.areaInfo && event.event.areaInfo.provinceName ? event.event.areaInfo
			.provinceName + '  ' : '';
		var areaName = event.event.areaInfo && event.event.areaInfo.name ? event.event.areaInfo.name + '  ' :
		'';
		$("#createCauseArea").text(provinceName + "-" + areaName);
		$("#createCauseCustPhone").text();
		$("#createCauseCustPhone").text(event.event.custPhone);
		$("#createCauseCustPhone").text();
		$('#ccbar-labels').text('');

		var labels = '';
		if (event.labels) {
			for (var i = 0; i < event.labels.length; i++) {
				for (var key in event.labels[i]) {
					labels += event.labels[i][key]
				}
			}
		}
		$('#ccbar-labels').text(labels);
		//3.1#20211019 来电振铃弹屏，优先显示队列名称
		var skillGroupName = event.event.queueName || event.event.skillGroupName || '';
		$("#createSkillGroupName").text(skillGroupName);
		var url = localStorage.getItem('evtAlteringUrl')
		if (url) {
			var param = {
				custPhone: event.event.custPhone,
				skillGroupName: skillGroupName,
				areaName: provinceName + '-' + areaName
			}
			url = formatUrl(url, param)
			popup.openTab(url, getI18nValue('振铃弹屏'))
		} else {
			$('.answercallpanel-btns').show();
			$("#answercallpanel").fadeIn();
		}
        if (Notification.permission == "granted") {
            notification = new Notification(getI18nValue("Hi，您好："), {
                body:  getI18nValue('客户号码') + event.event.custPhone + getI18nValue('来电了，点击接听！'),
                icon: '/cc-portal/static/images/notify.png'
            });
            notification.onclick = function() {
                top.CallControl.answerCall(function() {
                    $("#answercallpanel").fadeOut();
                    if (url) {
                        top.$('[lay-id]li').each(function() {
                            if ($(this).attr('lay-id').indexOf(url) > -1) {
                                top.popup.closeTab({
                                    id: $(this).attr('lay-id'),
                                    url: $(this).attr('lay-id')
                                });
                            }
                        })
                    }
                })
                notification.close();    
            };
        }
		audio_player.play(yconfig.getCallBeginUrl(), true);

	}
});

var evtNetWorkReachedClock = null;
ccbarEvent.addEvent('evtNetWorkReached', function(event) {
	if ($("#autoanswercall").prop('checked')) {
		var createCauseName = ccbar_config['createCause'][event.event.createCause] || '';
		var displayCustPhone = event.event.displayCustPhone || event.event.custPhone || event.event.caller ||
		'';
		var skillGroupName = event.event.skillGroupName || '';
		var labels = '';
		if (event.labels) {
			for (var i = 0; i < event.labels.length; i++) {
				labels += event.labels[i].labels + ' '
			}
		}
		var areaName = event.event.areaInfo && event.event.areaInfo.name ? event.event.areaInfo.name + '  ' :
		'';
		if (typeof(numOrigin) != 'undefined') areaName += numOrigin(event.event.userData.custPhone);

		$("#createCauseName").text(createCauseName)
		$("#createCauseArea").text(areaName);
		$('#ccbar-labels').text('');
		var labels = '';
		if (event.labels) {
			for (var i = 0; i < event.labels.length; i++) {
				for (var key in event.labels[i]) {
					labels += event.labels[i][key]
				}
			}
		}
		$('#ccbar-labels').text(labels);
		$("#createCauseCustPhone").text(displayCustPhone);
		$("#createSkillGroupName").text(skillGroupName);
		$('.answercallpanel-btns').hide();

		var url = localStorage.getItem('evtAlteringUrl')
		if (url) {
			var param = {
				custPhone: event.event.custPhone,
				skillGroupName: skillGroupName,
				areaName: provinceName + '-' + areaName
			}
			url = formatUrl(url, param)
			popup.openTab(url, getI18nValue('振铃弹屏'))
		} else {
			$("#answercallpanel").fadeIn();
			evtNetWorkReachedClock = setTimeout(function() {
				$("#answercallpanel").fadeOut();
			}, 60000)
		}
        if (Notification.permission == "granted") {
            notification = new Notification(getI18nValue("Hi，您好："), {
                body:  getI18nValue('客户号码') + event.event.custPhone + getI18nValue('来电了，点击接听！'),
                icon: '/cc-portal/static/images/notify.png'
            });
            notification.onclick = function() {
                top.CallControl.answerCall(function() {
                    $("#answercallpanel").fadeOut();
                    if (url) {
                        top.$('[lay-id]li').each(function() {
                            if ($(this).attr('lay-id').indexOf(url) > -1) {
                                top.popup.closeTab({
                                    id: $(this).attr('lay-id'),
                                    url: $(this).attr('lay-id')
                                });
                            }
                        })
                    }
                })
                notification.close();    
            };
        }
	}
})

var latestCall = null;
ccbarEvent.addEvent('evtConnected', function(event) {
	if (evtNetWorkReachedClock) {
		clearTimeout(evtNetWorkReachedClock)
	}
	var caller = event.event.caller;
	var called = event.event.called;
	var custPhone = event.event.custPhone;
	var displayCustPhone = event.event.displayCustPhone || event.event.custPhone;
	if (custPhone == caller) {
		caller = displayCustPhone
	} else {
		called = displayCustPhone
	}
	$('#call-caller').text(caller);
	$('#call-called').text(called);
	$('.ccbar-call').hide()
	$('#cur-call-info').show();

	latestCall = event;
	$("#answercallpanel").fadeOut();
	audio_player.stop();

});

ccbarEvent.addEvent('evtConsultedBegin', function(event) {
	$("#answercallpanel").fadeOut();
	audio_player.stop();
});

ccbarEvent.addEvent('evtDisConnected', function(event) {
	if (evtNetWorkReachedClock) {
		clearTimeout(evtNetWorkReachedClock)
	}
	$('.ccbar-call').show()
	$('#cur-call-info').hide();

	$("#answercallpanel").fadeOut();
	$('.ccbar-skillinfo').html('');
    notification && notification.close(); 
	var url = localStorage.getItem('evtAlteringUrl')
	if (url) {
		top.$('[lay-id]li').each(function() {
			if ($(this).attr('lay-id').indexOf(url) > -1) {
				top.popup.closeTab({
					id: $(this).attr('lay-id'),
					url: $(this).attr('lay-id')
				});
			}
		})
	}
	audio_player.stop();
	audio_player.play(yconfig.getCallEndUrl(), false)
});


ccbarEvent.addEvent('clearcall', function(event) {
	$("#answercallpanel").fadeOut();
	audio_player.stop();
});
ccbarEvent.addEvent('setready', function(event) {
	tipsMsg(getI18nValue('坐席已置闲'));
});

ccbarEvent.addEvent('setnotready', function(event) {
	tipsMsg(getI18nValue('坐席已置忙'));
});

//当前排队总数
ccbarEvent.addEvent('monitor', function(event) {
	$('#currentCCbarQueueCount').text(event.totalQueueCount)
	if (event.totalQueueCount > 0) {
		$('#currentCCbarQueueCount').show()
	} else {
		$('#currentCCbarQueueCount').hide()
	}
});


function dump(url, title, id) {
	popup.openTab({
		id: id,
		url: url,
		title: title
	});
}


/*注册全媒体新消息显示*/
window.addEventListener('message', function(event) {
	//console.log('event',event)
	var targetItem = $('.layui-tab-title [lay-pid="ycmeidaent2"]')
	var data = event.data
	if (data.source && data.source == 'yc-media' && data.type) {
		switch (data.type) {
			case 'newMsg':
				targetItem.css({
					'backgroundColor': '#f2eaa2'
				})
				break;
			case 'readMsg':
				targetItem.removeAttr('style')
				break;
			case 'wsOpen':
				targetItem.css({
					'borderLeft': '0 solid red'
				})
				break;
			case 'wsClose':
				targetItem.css({
					'borderLeft': '3px solid red'
				})
				break;
			case 'unreadCount':
				if (targetItem.find('.layui-badge').length == 0) {
					targetItem.append('<span class="layui-badge"></span>')
				}
				var count = data.count > 0 ? (data.count > 99 ? '99+' : data.count) : '';
				targetItem.find('.layui-badge').text(count)
				break;
		}
	}
})

function formatUrl(str, param) {
	if (str.includes('?')) {
		return str + '&' + $.param(param)
	} else {
		return str + '?' + $.param(param)
	}
}

//情绪异常
ccbarEvent.addEvent('asrFeeling', function(event) {
	console.log('asrFeeling')
	console.log(event)
	var clientid = '';
	if (event.msg && event.msg.clientid) {
		clientid = event.msg.clientid;
	}

	var data = { 
		'content': event.content,
		'custPhone': top.latestCall.event.custPhone,
		'clientid':clientid,
		'keyWord': event.feelingWord
	};
	$.ajax({
		dataType: 'jsonp',
		contentType: "application/x-www-form-urlencoded; charset=UTF-8",
		url:  '/cc-base/api/spec?action=AgentMood',
		data: {
			'cmdJson': JSON.stringify(data)
		},
		timeout: 5000, //5秒超时
		success: function(result) {
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {}
	});
});
//话术引导
ccbarEvent.addEvent('asrTalkInfo', function(event) {
	console.log('asrTalkInfo')
	console.log(event)
	var clientid = '';
	if (event.msg && event.msg.clientid) {
		clientid = event.msg.clientid;
	}
	var data = {
		'content': event.content,
		'clientid':clientid,
		'custPhone': top.latestCall.event.custPhone,
		'keyWord': event.talkInfo
	};
	$.ajax({
		dataType: 'jsonp',
		contentType: "application/x-www-form-urlencoded; charset=UTF-8",
		url:  '/cc-base/api/spec?action=TalkInfo',
		data: {
			'cmdJson': JSON.stringify(data)
		},
		timeout: 5000, //5秒超时
		success: function(result) {
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {}
	});
});