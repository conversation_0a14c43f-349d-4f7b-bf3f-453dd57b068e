<!DOCTYPE html>
<html lang="en" class="bg-white">

<head>
    <title>设置</title>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="../module/admin.css?v=311" />
    <!--[if lt IE 9]>
    <script src=""></script>
    <script src=""></script>
    <![endif]-->

    <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
    <script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
    <script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
    <script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
    <style>
        body {
            overflow-x: hidden;
        }
        
        .layui-card-body {
            padding: 0;
        }
        
        .theme-div {
            padding-left: 15px;
            padding-top: 20px;
            margin-bottom: 10px;
        }
        
        .btnTheme {
            display: inline-block;
            margin: 0 6px 15px 0;
            padding: 4px;
            border: 1px solid #fff;
        }
        
        .btnTheme img {
            width: 80px;
            height: 50px;
            border: 1px solid #f2f2f2;
            background: #F2F2F2;
            cursor: pointer;
        }
        
        .btnTheme:hover,
        .btnTheme.active {
            border-color: #5FB878;
        }
        
        .more-menu-item {
            display: block;
            height: 50px;
            line-height: 50px;
            font-size: 16px;
            border-bottom: 1px solid #e8e8e8;
            color: #333;
            padding: 0px 25px;
            font-style: normal;
        }
        
        .more-menu-item:first-child {
            border-top: 1px solid #e8e8e8;
        }
        
        .more-menu-item:hover {
            background: #F2F2F2;
            color: #333;
        }
        
        .more-menu-item .layui-icon {
            padding-right: 10px;
            font-size: 18px;
        }
        
        .more-menu-item:after {
            content: "\e602";
            font-family: layui-icon !important;
            position: absolute;
            right: 16px;
            color: #999;
        }
        
        .more-menu-item.no-icon:after {
            content: "";
        }
        /** 设置表单样式 */
        
        .set-item-label {
            display: inline-block;
            height: 38px;
            line-height: 38px;
            padding-left: 20px;
            color: #333333;
        }
        
        .set-item-ctrl {
            display: inline-block;
            height: 38px;
            line-height: 38px;
        }
        
        .set-item-ctrl>* {
            margin: 0;
        }
    </style>


</head>

<body>
    <div class="page-loading">
        <div class="ball-loader">
            <span></span><span></span><span></span><span></span>
        </div>
    </div>
    <div class="layui-card-header" i18n-content="设置主题"></div>
    <div class="layui-card-body">
        <!-- 主题列表 -->
        <div class="theme-div"></div>

        <!-- 导航 -->

        <!-- 控制开关 -->
        <div class="layui-form" style="margin: 25px 0;">
            <div class="layui-form-item">
                <label class="set-item-label" i18n-content="页脚："></label>
                <div class="set-item-ctrl" style="margin-right: 15px;">
                    <input id="setFooter" lay-filter="setFooter" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
                <label class="set-item-label" style="width: auto;" i18n-content="Tab记忆："></label>
                <div class="set-item-ctrl">
                    <input id="setTab" lay-filter="setTab" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="set-item-label" style="width: auto;" i18n-content="多标签："></label>
                <div class="set-item-ctrl">
                    <input id="setMoreTab" lay-filter="setMoreTab" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
                <label class="set-item-label" i18n-content="切换刷新："></label>
                <div class="set-item-ctrl">
                    <input id="setRefresh" lay-filter="setRefresh" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
            </div>

            <div class="layui-form-item">
                <!-- <label class="set-item-label">切换tab刷新：</label>
            <div class="set-item-ctrl">
                <input id="setTabRefresh" lay-filter="setTabRefresh" type="checkbox" lay-skin="switch" lay-text="开启|关闭">
            </div> -->
                <label class="set-item-label" i18n-content="展开侧栏："></label>
                <div class="set-item-ctrl">
                    <input id="setFlexible" lay-filter="setFlexible" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="set-item-label" i18n-content="导航小三角："></label>
                <div class="set-item-ctrl">
                    <input lay-filter="navArrow" type="radio" value="" i18n-title="默认" name="navArrow">
                    <input lay-filter="navArrow" type="radio" value="arrow2" i18n-title="箭头" name="navArrow">
                    <input lay-filter="navArrow" type="radio" value="arrow3" i18n-title="加号" name="navArrow">
                </div>
            </div>
            <!-- <div class="layui-form-item">
	            <label class="set-item-label"   i18n-content="语言："></label>
	            <div class="set-item-ctrl">
	                 <select name="userLang" id="userLang" lay-verify="required" lay-filter="userLangChange">
				        <option value="CN">中文1</option>
		 				<option value="EN">English</option>
				      </select>
	            </div>
	        </div> -->

            <div class="layui-form-item">

                <label class="set-item-label" i18n-content="护眼模式："></label>
                <div class="set-item-ctrl">
                    <input id="isUseMask" lay-filter="isUse" type="checkbox" lay-skin="switch" i18n-lay-text="开启|关闭">
                </div>
                <div style="text-align: center;padding: 10px 0">
                    <i class="layui-icon layui-icon-rate-solid"></i><span class="icon-dark" i18n-content="暗"></span>
                    <input id="maskVal" type="range" min="0" max="100" style="display:inline-block;vertical-align: middle; width: 60%;margin: 0 10px" />
                    <span class="icon-light" i18n-content="亮"></span><i class="layui-icon layui-icon-rate"></i></div>
            </div>
        </div>

    </div>
    <script type="text/javascript" src="/cc-portal/static/js/my_i18n.js"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
    <script type="text/javascript" src="/cc-portal/portal/js/common.js?v=311"></script>
    <script>
	    layui.config({
	   	  	base: '/cc-portal/portal/module/'
	   	}).extend({
	   	  	admin: 'admin'
	   	});
        layui.use(['layer', 'form', 'admin'], function() {
            var $ = layui.jquery;
            var layer = layui.layer;
            var form = layui.form;
            var admin = layui.admin;
            var leftNav = '.layui-layout-admin>.layui-side>.layui-side-scroll>.layui-nav';
            var mainTab = '.layui-body>.layui-tab[lay-filter="admin-pagetabs"]';

            var themes = [{
                title: getI18nValue('黑白主题'),
                theme: 'admin'
            }, {
                title: getI18nValue('黑色主题'),
                theme: 'black'
            }, {
                title: getI18nValue('蓝色主题'),
                theme: 'blue'
            }, {
                title: getI18nValue('藏青主题'),
                theme: 'cyan'
            }, {
                title: getI18nValue('黄色主题'),
                theme: 'yellow'
            }, {
                title: getI18nValue('绿色主题'),
                theme: 'green'
            }, {
                title: getI18nValue('粉红主题'),
                theme: 'pink'
            }, {
                title: getI18nValue('紫白主题'),
                theme: 'purple-white'
            }, {
                title: getI18nValue('紫色主题'),
                theme: 'purple'
            }, {
                title: getI18nValue('白色主题'),
                theme: 'white'
            }, {
                title: getI18nValue('红白主题'),
                theme: 'red-white'
            }, {
                title: getI18nValue('红色主题'),
                theme: 'red'
            }];
            for (var i = 0; i < themes.length; i++) {
                var str = '<div class="btnTheme" theme="theme-' + themes[i].theme + '" title="' + themes[i].title + '">';
                str += '	<img src="../module/theme/img/theme-' + themes[i].theme + '.png">';
                str += '</div>';
                $('.theme-div').append(str)
            }

            // 切换主题
            var mTheme = layui.data(admin.tableName).theme;
            $('.btnTheme[theme=' + (mTheme ? mTheme : admin.defaultTheme) + ']').addClass('active');
            $('.btnTheme').click(function() {
                $('.btnTheme').removeClass('active');
                $(this).addClass('active');
                admin.changeTheme($(this).attr('theme'));
            });

            // 关闭/开启页脚
            var openFooter = layui.data(admin.tableName).openFooter;
            $('#setFooter').prop('checked', openFooter == undefined ? false : openFooter); // edit by cc 默认设置页脚关闭
            form.on('switch(setFooter)', function(data) {
                var checked = data.elem.checked;
                layui.data(admin.tableName, {
                    key: 'openFooter',
                    value: checked
                });
                checked ? top.layui.jquery('body.layui-layout-body').removeClass('close-footer') : top.layui.jquery('body.layui-layout-body').addClass('close-footer');
            });

            // 关闭/开启Tab记忆功能
            $('#setTab').prop('checked', top.layui.index.cacheTab || false);
            form.on('switch(setTab)', function(data) {
                top.layui.index.setTabCache(data.elem.checked);
            });

            // 切换Tab自动刷新
            var tabAutoRefresh = layui.data(admin.tableName).tabAutoRefresh || false; //edit by cc false改为true
            $('#setRefresh').prop('checked', tabAutoRefresh == undefined ? false : tabAutoRefresh);
            form.on('switch(setRefresh)', function(data) {
                var checked = data.elem.checked;
                layui.data(admin.tableName, {
                    key: 'tabAutoRefresh',
                    value: checked
                });
                checked ? top.layui.jquery('.layui-body>.layui-tab[lay-filter="admin-pagetabs"]').attr('lay-autoRefresh', 'true') : top.layui.jquery('.layui-body>.layui-tab[lay-filter="admin-pagetabs"]').removeAttr('lay-autoRefresh');
            });

            // 关闭/开启多标签
            var openTab = layui.data(admin.tableName).openTab;
            $('#setMoreTab').prop('checked', openTab == undefined ? top.layui.index.pageTabs : openTab);
            form.on('switch(setMoreTab)', function(data) {
                var checked = data.elem.checked;
                layui.data(admin.tableName, {
                    key: 'openTab',
                    value: checked
                });
                admin.putTempData('indexTabs', undefined); // 清除缓存的Tab
                top.location.reload();
            });

            // 关闭/开启面板折叠 edit by cc
            var flexible = layui.data(admin.tableName).cacheFlexible;
            $('#setFlexible').prop('checked', flexible == undefined ? top.layui.index.flexible : flexible);
            form.render('checkbox');
            form.on('switch(setFlexible)', function(data) {
                var checked = data.elem.checked;
                layui.data(admin.tableName, {
                    key: 'cacheFlexible',
                    value: checked
                });
            });

            //护眼模式设置 edit by cc
            var _isUseMask = localStorage.getItem('_isUseMask') || false;
            var _maskOpacity = localStorage.getItem('_maskOpacity') || 50;
            _isUseMask = 'true' || true ? true : false;
            $('#maskVal').val(_maskOpacity);
            $('#isUseMask').prop('checked', _isUseMask);

            form.on('switch(isUse)', function(data) {
                $('#isUseMask').prop('checked', data.elem.checked);
                $('#isUseMask').change()

            });

            $('body').on('change', '#isUseMask,#maskVal', function(e) {
                var _val = $('#maskVal').val();
                var _isUse = $('#isUseMask').prop('checked');

                top.layui.index.setMask.set(_isUse, _val);

                localStorage.setItem('_isUseMask', _isUse)
                localStorage.setItem('_maskOpacity', _val)
            })


            const Storage = {};

            Storage.get = function(name) {
                return localStorage.getItem(name);
            }

            Storage.set = function(name, val) {
                localStorage.setItem(name, val);
            }

            var lastData = Storage.get("NavigationIcon");
            if (lastData) {
                var data = JSON.parse(lastData)
                layui.data(admin.tableName, {
                    key: 'navArrow',
                    value: data.value
                });
                top.layui.jquery(leftNav).removeClass('arrow2 arrow3');
                data.value && top.layui.jquery(leftNav).addClass(data.value);
            }

            // 导航小三角
            var navArrow = layui.data(admin.tableName).navArrow;
            $('[name="navArrow"][value="' + (navArrow ? navArrow : '') + '"]').prop('checked', 'true');
            form.on('radio(navArrow)', function(data) {
                console.log(data)
                Storage.set("NavigationIcon", JSON.stringify(data));

                layui.data(admin.tableName, {
                    key: 'navArrow',
                    value: data.value
                });
                top.layui.jquery(leftNav).removeClass('arrow2 arrow3');
                data.value && top.layui.jquery(leftNav).addClass(data.value);
            });

            /**语言
            var multiLang = localStorage.getItem("MULTI-LANG-KEY");
            $('[name="multiLang"][value="' + (multiLang ? multiLang : 'CN') + '"]').prop('checked', 'true');
            form.on('radio(multiLang)', function(data) {
                localStorage.setItem("MULTI-LANG-KEY", data.value)
                top.layui.jquery(leftNav).removeClass('arrow2 arrow3');
                data.value && top.layui.jquery(leftNav).addClass(data.value);
                ajax.remoteCall("/cc-portal/workbench?action=setLang", {
                    "lang": data.value
                }, function(result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {
                            icon: 1
                        });
                    } else {
                        layer.alert(result.msg, {
                            icon: 5
                        });
                    }
                });

            });
            */

            // form.on('select(userLangChange)', function(data) {
            //     localStorage.setItem("MULTI-LANG-KEY", data.value);
            //     ajax.remoteCall("/cc-portal/workbench?action=setLang", {
            //         "lang": data.value
            //     }, function(result) {
            //         if (result.state == 1) {
            //             layer.msg(result.msg, {
            //                 icon: 1
            //             });
            //         } else {
            //             layer.alert(result.msg, {
            //                 icon: 5
            //             });
            //         }
            //     });

            // });

            form.render('radio');
            form.render('checkbox');

            // ajax.remoteCall("/cc-base/api/spec?action=getAllLang", {}, function(result) {
            //     var list = result.data;
            //     var select = document.getElementById('userLang');
            //     if (list != null || list.size() > 0) {
            //         $("#userLang").find("option").remove();
            //         for (var c in list) {
            //             var option = document.createElement("option");
            //             option.setAttribute("value", list[c].LANG_CODE);
            //             option.innerText = list[c].LANG_NAME;
            //             select.appendChild(option)
            //         }

            //         //设置默认的语言
            //         var multiLang = localStorage.getItem("MULTI-LANG-KEY");
            //         if (!multiLang) {
            //             multiLang = "CN";
            //         }
            //         $("#userLang").val(multiLang);

            //         form.render('select');

            //     };
            // });

        });
    </script>
</body>

</html>