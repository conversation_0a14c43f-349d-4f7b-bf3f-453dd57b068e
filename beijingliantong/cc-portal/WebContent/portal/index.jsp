<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<%@page import="com.yunqu.ccportal.base.Constants" %>
<c:set var="isShowEditPassword" value="<%=Constants.IS_SHOW_EDIT_PASSWORD%>" />
<c:set var="userData" value="<%=request.getUserPrincipal()%>" />
<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link href="${portalIconUrl}" rel="icon">
		<title>${userData.entName } - ${userData.loginAcct }</title>
		<link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css" />
		<link rel="stylesheet" href="${ctxPath}/portal/module/admin.css?v=0805" />

		<!-- ccbar -->
		<link rel="stylesheet" href="/yc-ccbar/ccbar/ccbar.css?v=1546572904630">
		<link rel="stylesheet" href="${ctxPath}/ccbar/ccbar_agent.css?v=1117">
		<link rel="stylesheet" href="/yc-ccbar/ccbar/font/iconfont.css?v=20180803">
		<link rel="stylesheet" href="${ctxPath}/portal/css/font_1049336_pstto6h887j.css">

		<link rel="stylesheet" href="${ctxPath}/static/css/indexSystem.css?v=20191119">
		<link rel="stylesheet" href="${ctxPath}/static/css/chat.css?v=20191119">
		<link rel="stylesheet" href="${ctxPath}/static/css/icons.css?v=20191119">

		<link rel="stylesheet" href="/cc-portal/static/font/iconfont.css?20220125">

		<link rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css">


		<!-- ccbar end -->
		<style>
			.layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a {
				text-align: center;
				text-align: center;
				height: inherit;
				min-height: 45px;
				padding: 5px 10px;

			}

			.layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a>i {
				margin-left: 10px;
				margin-right: 6px;
				font-size: 18px;
			}

			.layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a>cite {
				display: block !important;
				line-height: 20px;
				margin-top: -10px;
				font-size: 13px;
			}

			@media screen and (max-width: 750px) {
				.layui-layout-body .layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a>cite {
					display: inline-block !important;
				}

				.layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a {
					text-align: left !important
				}

				.admin-nav-mini.layui-layout-admin .layui-side .layui-nav .layui-nav-item .layui-nav-child a {
					padding-left: 40px;
				}
			}

			.layui-layout-admin.admin-nav-mini .layui-side .layui-nav-tree>.layui-nav-item>a>cite {
				margin: 0 auto;
				margin-top: -10px;
				font-size: 13px;
				max-width: 2em;
				overflow: hidden;
			}

			.layui-layout-admin .layui-body>.layui-tab>.layui-tab-title li {
				font-size: 13px;
			}

			.layui-layout-admin .layui-side,
			.layui-layout-admin .layui-header .layui-logo {
				width: 210px;
			}

			.layui-layout-admin .layui-side .layui-nav .layui-nav-more {
				right: 30px;
			}

			.clearfix:after {
				content: "";
				/*设置内容为空*/
				height: 0;
				/*高度为0*/
				line-height: 0;
				/*行高为0*/
				display: block;
				/*将文本转为块级元素*/
				visibility: hidden;
				/*将元素隐藏*/
				clear: both;
				/*清除浮动*/
			}

			.layui-layout-admin .layui-body {
				left: 210px;
			}

			#box p {
				width: 100%;
				margin: 0;
				padding: 0;
				padding: 10px 0;
				padding-left: 15px;
				box-sizing: border-box;
			}

			#box .layui-input {
				height: 28px;
			}

			#box .layui-form-select dl {
				padding: 0;
				top: 30px;
			}

			#departmentDiv {
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				display: none;
			}

			#department:hover #departmentDiv {
				display: block;
			}

			.departmentItem {
				position: relative;
				cursor: pointer;
				width: 100px;
				text-align: left;
				padding-left: 10px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				padding-right: 30px;
			}

			.departmentItem:hover {
				background-color: rgba(0, 0, 0, 0.1);
			}

			.isSelected {
				color: seagreen;
				position: absolute;
				right: 10px;
				top: -1px;
			}

			#box .layui-edge {
				display: none;
			}

			#box .layui-anim {
				width: 190px;
			}

			#ccbar_phoneCallerList option {
				color: #333 !important
			}

			.rtTips_li {
				width: 92%;
				/* height: 100px; */
				padding: 12px;
				/* border-bottom: 1px solid rgba(0, 0, 0, 0.4); */
				border-radius: 5px;
				background-color: #409EFF;
				display: flex;
				flex-direction: column;
				color: #fff;
				margin-bottom: 10px;
				position: relative;

				animation-name: fadeInRight;
				-webkit-animation: fadeInRight;
				animation-duration: 1s;
			}

			/*从右到左进入*/
			@keyframes fadeInRight {
				from {
					opacity: 0;
					-webkit-transform: translate(1000px, 0);
					transform: stranslate(1000px, 0);
				}

				to {
					opacity: 1;
					-webkit-transform: translate(10px, 0);
					transform: stranslate(10px, 0);
				}
			}

			@-webkit-keyframes fadeInRight {
				from {
					opacity: 0;
					-webkit-transform: translate(1000px, 0);
					transform: stranslate(1000px, 0);
				}

				to {
					opacity: 1;
					-webkit-transform: translate(10px, 0);
					transform: stranslate(10px, 0);
				}
			}

			.rtTips_p2 {
				flex: 1;
				line-height: 30.5px;
				text-indent: 2em;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 330px;
			}

			.side-toolbar {
				position: fixed;
				right: 35px;
				bottom: 110px;
				width: 48px;
				min-height: 48px;
				border-radius: 24px;
				background: #fff;
				box-shadow: 0 2px 15px 0 rgba(0, 0, 0, .1);
				z-index: 999 !important;
			}

			.side-toolbar .toolbar-item {
				margin: 6px;
				width: 36px;
				height: 36px;
				line-height: 36px;
				color: #3d3d3d;
				text-align: center;
				cursor: pointer;
			}

			.side-toolbar .iconfont-toolbar {
				color: #3d3d3d;
				width: 20px;
				height: 20px;
				vertical-align: middle;
			}

			.toolbar-item-active {
				background: #f9f9f9;
				border-radius: 18px;
			}

			.toolbar-item-active .iconfont-toolbar {
				color: #ff6a00;
			}

			.side-toolbar .side-toolbar-border {
				margin: 0 auto;
				width: 20px;
				border-top: 1px solid #ededec;
			}

			.side-toolbar-btn {
				position: fixed;
				right: -15px;
				bottom: 100px;
				width: 48px;
				height: 48px;
				/* border-radius: 24px;
				line-height: 48px;
				background: #fff;
				box-shadow: 0 2px 15px 0 rgba(0,0,0,.1); */
				z-index: 999 !important;
				text-align: center;
				/* color: #ff6a00; */
				cursor: pointer;
			}

			.wifi-wrapper {
				width: 16px;
				height: 16px;
				position: relative;
			}

			.wifi-line {
				width: 2px;
				bottom: 0;
				background: #ccc;
				position: absolute;
			}

			.wifi-line.first-line {
				position: absolute;
				left: 0;
				height: 4px;
			}

			.wifi-line.second-line {
				left: 4px;
				height: 8px;
			}

			.wifi-line.third-line {
				left: 8px;
				height: 12px;
			}

			.wifi-line.four-line {
				left: 12px;
				height: 16px;
			}

			.menu-item {
				display: flex;
				align-items: center;
				line-height: 2;
				cursor: pointer;
			}

			.menu-item:hover {
				color: #ff6a00;
			}

			/*标签计数*/
			.layui-tab-title .layui-badge {
				position: absolute;
				left: inherit;
				right: 0;
				top: 0;
			}

			.layui-tab-title .layui-badge:empty {
				display: none;
			}


			/*  * {
				margin:0;
				padding:0;
				border:none
			}
			body,html {
				height:100%;
				width:100%;
			} */
			.drag-box {
				user-select: none;
				/* background-color: #FFF6EA; */
				z-index: 198910;
				position: fixed;
				bottom: 10px;
				left: 74px;
				/* width:200px; */
				border-radius: 15px;
				width: auto;
				height: 30px;
				line-height: 30px;
				overflow: hidden;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			}

			#dragBoxBar {
				/* align-items:center; */
				/* display:flex; */
				/* justify-content:space-between; */
				/* background:#fff; */
				/* width:100%; */
				height: 30px;
				cursor: move;
				user-select: none;


				padding: 20px 0;

				/* position: fixed; */
				/* left: 100px; */
				/* top: 100px; */
			}

			.no-select {
				user-select: none;
			}

			.pointer-events {
				pointer-events: none;
			}

			.no-border {
				border: none;
			}

			/* #injectedBox {
				height:160px;
				display:flex;
				align-items:center;
				justify-content:center;
				font-size:2rem;
				background:#eee;
			} */
			#activeNoticeMsg {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}


			.rightBottomMsg {
				cursor: pointer;
				display: flex;
				justify-content: space-between;
				margin-top: 10px;
			}

			.rightBottomMsg:first-child {
				margin-top: 0;
			}

			.rightBottomMsg:hover {
				color: #2F77F1;
			}

			.rbleftSpan {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: inline-block;
				flex: 1;
			}

			.rbrightSpan {
				width: 12rem;
				text-align: right;
			}

			.layui-carousel>[carousel-item]:before {
				display: none;
			}
		</style>
		<link rel="stylesheet" href="${Constants.PORTAL_INDEX_CSS}?v=20191119">
	</head>

	<body class="layui-layout-body">
		<ul id="rtTips" style="position: fixed;z-index: 9999;right: 15px;top: 100px;width: 400px;height: auto;">

		</ul>
		<div class="layui-layout layui-layout-admin">
			<input type="hidden" id="isOpenWebIM" value="${isOpenWebIM}">
			<!-- 头部 -->
			<div class="layui-header">
				<div class="layui-logo">
					<img src="${portalLogo}" />
					<cite id='portalTitle'>&nbsp;${portalTitle}&emsp;</cite>
				</div>
				<ul class="layui-nav layui-layout-left">
					<li class="layui-nav-item" lay-unselect id="stretch">
						<a ew-event="flexible" i18n-title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
					</li>
					<li class="layui-nav-item" lay-unselect>
						<a ew-event="refresh" i18n-title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
					</li>
					<jsp:include page="head.jsp"></jsp:include>
					<li style="display: inline-block;
					background-color: #4695e4;
					position: relative;
					top: 6px;
					left: 6px;
					border-radius: 9px;"
					id="romoteBtn">
						<a href="" i18n-content="远程" style="color: #FFF;
						display: inline-block;
						padding: 5px 10px;"></a>
					</li>
				</ul>
				<ul class="layui-nav layui-layout-right">
					<li class="layui-nav-item" lay-unselect style="height: 50px;">
						<div class="layui-form-item" style="height: 50px;cursor: pointer;">
							<!-- <label class="set-item-label" i18n-content="语言" style="color: #000;"></label> -->
							<!-- <img src="${ctxPath}/static/images/多语言.png" style="margin-right: 10px; width: 24px;" alt="语言"> -->
							<span id="currentLang"
								style="margin-right: 10px; margin-right: 10px; color: #333333; border: 1px solid #333333; padding: 1px 5px;">中文</span>
							<!-- <div class="set-item-ctrl">
                                        <select name="userLang" id="userLang" onchange="choseLang()">
                                        </select>
                                    </div> -->

							<dl class="layui-nav-child" id="userLang" style="overflow: visible;left: -20px;">

							</dl>
						</div>
					</li>
					<li class="layui-nav-item" lay-unselect>
						<a ew-event="message" i18n-title="消息">
							<span class="layui-badge-dot" style="display:none;"></span>
							<!--小红点-->
							<i class="layui-icon layui-icon-notice"></i>
						</a>
					</li>
					<li class="layui-nav-item" lay-unselect style="display: none">
						<a ew-event="note" i18n-title="便签"><i class="layui-icon layui-icon-note"></i></a>
					</li>
					<li class="layui-nav-item layui-hide-xs" lay-unselect>
						<a ew-event="fullScreen" i18n-title="全屏"><i class="layui-icon layui-icon-screen-full"></i></a>
					</li>
					<li class="layui-nav-item" lay-unselect>
						<a href="javascript:;">
							<div class="wifi-wrapper" i18n-title="正在获取网络响应时长">
								<div class="wifi-line first-line"></div>
								<div class="wifi-line second-line"></div>
								<div class="wifi-line third-line"></div>
								<div class="wifi-line four-line"></div>
							</div>
						</a>
					</li>
					<li class="layui-nav-item" lay-unselect>
						<a>
							<img src="${userIcon }" class="layui-nav-img">
							<cite>${userData.nickName}</cite>
						</a>
						<dl class="layui-nav-child" style="overflow: visible;">
							<dd lay-unselect>
								<a title="下载话机" href="/yc-ccbar/ccbar/download.jsp" target="_blank"
									i18n-content="下载话机"></a>
							</dd>
							<dd lay-unselect id="department" style="position: relative;overflow: visible;">
								<a title="部门选择" href="#" i18n-content="部门选择"></a>
								<div id="departmentDiv"
									style="position: absolute;left: -140px;top: 5px;background-color: #fff;color: #000;">
									<ul id="departmentSelect">
									</ul>
								</div>
							</dd>
							<c:if test="${isShowEditPassword=='Y'}">
								<EasyTag:res resId="cc-portal-btnauth-xgmm">
									<dd lay-unselect>
										<a id="setPsw" i18n-content="修改密码"></a>
									</dd>
								</EasyTag:res>
							</c:if>
							<input type="hidden" id="satisKey" value="${satisKey}">
							<dd lay-unselect>
								<a ew-event="note" data-url="/cc-portal/portal/tpl/tpl-bookMark.jsp" i18n-title="收藏夹"
									i18n-content="收藏夹"></a>
							</dd>
							<dd lay-unselect>
								<a ew-event="memo" data-url="/cc-workbench/pages/memorandum-more.jsp" i18n-title="备忘录"
									i18n-content="备忘录"></a>
							</dd>
							<dd lay-unselect>
								<a ew-event="sysDocument" data-url="/cc-base/pages/systemDocumentation/list.jsp"
									i18n-title="系统文档" i18n-content="系统文档"></a>
							</dd>
							<hr>
							<dd lay-unselect>
								<a id="btnLogout" i18n-content="退出"></a>
							</dd>
						</dl>
					</li>
					<li class="layui-nav-item" lay-unselect>
						<a ew-event="theme" i18n-title="主题"><i class="layui-icon layui-icon-more-vertical"></i></a>
					</li>
				</ul>


			</div>

			<!-- 侧边栏 -->
			<div class="layui-side">
				<div class="layui-side-scroll">
					<ul class="layui-nav layui-nav-tree arrow2" lay-filter="admin-side-nav" lay-accordion="true"
						style="margin: 15px 0;">
					</ul>
				</div>
			</div>

			<!-- 主体部分 -->
			<div class="layui-body">
				<div class="layui-tab" lay-allowClose="true" lay-filter="admin-pagetabs">
					<ul class="layui-tab-title">
					</ul>
					<div class="layui-tab-content">

					</div>
				</div>
				<div class="layui-icon admin-tabs-control layui-icon-prev" ew-event="leftPage"></div>
				<div class="layui-icon admin-tabs-control layui-icon-next" ew-event="rightPage"></div>
				<div class="layui-icon admin-tabs-control layui-icon-down">
					<ul class="layui-nav admin-tabs-select" lay-filter="admin-pagetabs-nav">
						<li class="layui-nav-item" lay-unselect>
							<a href="javascript:;"></a>
							<dl class="layui-nav-child layui-anim-fadein">
								<dd ew-event="closeThisTabs" lay-unselect>
									<a href="javascript:;" i18n-content="关闭当前标签页"></a>
								</dd>
								<dd ew-event="closeOtherTabs" lay-unselect>
									<a href="javascript:;" i18n-content="关闭其它标签页"></a>
								</dd>
								<dd ew-event="closeAllTabs" lay-unselect>
									<a href="javascript:;" i18n-content="关闭全部标签页"></a>
								</dd>
							</dl>
						</li>
					</ul>
				</div>
			</div>

			<!-- 底部 -->
			<div class="layui-footer">
				Copyright © <span id="year"></span>
				<a href="javascript:void(0)" i18n-content="呼叫中心"></a> All rights reserved.
				<span class="pull-right">Version 2.5.3</span>
			</div>

			<!-- 手机屏幕遮罩层 -->
			<div class="site-mobile-shade"></div>

			<!-- 工具栏按钮 -->
			<div class="side-toolbar-btn">
				<!-- <i class="iconfont-toolbar icon-caidanguanli" style="font-size: 24px;display: block;"></i>
				<i class="iconfont-toolbar icon-zhankai" style="font-size: 24px;display: none;"></i> -->
				<img src="/cc-portal/static/images/left.png" alt="" class="leftBtn">
			</div>
			<!-- 右侧工具栏 -->
			<div class="side-toolbar" style="display: none;">
				<!-- 备忘录 -->
				<div class="toolbar-item memorandum-tool">
					<i class="iconfont-toolbar icon-shoucangbiaoqianbeiwanglu"></i>
				</div>
				<div class="side-toolbar-border"></div>
				<!-- FAQ查询 -->
				<EasyTag:res resId="cc-base-sys-faq">
					<div class="toolbar-item faq-tool">
						<i class="iconfont-toolbar icon-faq"></i>
					</div>
					<div class="side-toolbar-border"></div>
				</EasyTag:res>
				<!-- 主动保障 -->
				<EasyTag:res resId="cc-base-sys-submit-question">
					<div class="toolbar-item support-tool">
						<i class="iconfont-toolbar icon-bianji"></i>
					</div>
					<div class="side-toolbar-border"></div>
				</EasyTag:res>
				<!-- 保障列表 -->
				<!-- <EasyTag:res resId="cc-base-sys-handle-question">
					<div class="toolbar-item list-tool">
						<i class="iconfont-toolbar icon-chakanliebiao"></i>
					</div>
					<div class="side-toolbar-border"></div>
				</EasyTag:res> -->
				<!-- 更多 -->
				<EasyTag:res resId="cc-base-sys-right">
					<div class="toolbar-item more-tool">
						<i class="iconfont-toolbar icon-gengduo"></i>
					</div>
					<div class="side-toolbar-border"></div>
				</EasyTag:res>
			</div>

		</div>

		<div class="page-loading">
			<div class="ball-loader">
				<span></span><span></span><span></span><span></span>
			</div>
		</div>
		<div id="mydiv"  style="display: none;"></div>
		<script>
			//设置当前用户的图像
			var userIcon = '${userIcon }';
			localStorage.setItem("userIcon", userIcon);

			localStorage.setItem("autoLogoutTimeOut", '${autoLogoutTimeOut}');

			
		</script>





		<!-- <div style="position: fixed;z-index: 999999;"></div> -->
		<dragbox id="dragBox" class="drag-box">
			<dragboxbar style="display: flex;background-color: #FFF6EA;">
				<div style="width: auto;transform: translateY(-20px);" class=" no-select" id="dragBoxBar">
					<img src="/cc-portal/portal/images/notice_red.png"
						style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;display: none;"
						onclick="changeNoticeShow(1)" id="red_activeNoticeImg">
					<img src="/cc-portal/portal/images/notice_nor.png"
						style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;"
						onclick="changeNoticeShow(2)" id="no_activeNoticeImg">
					<img src="/cc-portal/portal/images/notice_select.png"
						style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;display: none;"
						onclick="changeNoticeShow(3)" id="activeNoticeImg" class="activeNoticeShow">
					<span style="display: none;vertical-align: middle;margin-left: 5px;color: #DE7D00;"
						i18n-content="系统公告" class="activeNoticeShow " id="drag"></span>
				</div>
				<div style="flex: 1;margin-left: 5px;padding-right: 10px;display: none;"
					class="layui-carousel activeNoticeShow" id="activeNoticeMsg">
					<div carousel-item id="lbt" style="height: 30px;line-height: 33px;">
					</div>
				</div>
				<!-- <div class="layui-carousel" id="test1"
					style="height: 30px;display: inline-block;color: #1296db !important;">
					<div carousel-item id="lbt" style="height: 30px;">
					</div>
				</div> -->
			</dragboxbar>
			<!-- <injectedbox id="injectedBox">CONTENT</injectedbox> -->
		</dragbox>



		<script id="nav-template" type="text/x-jsrender">
			<div class="layui-nav-item clearfix" style="padding: 0 4px;width:200px;white-space:nowrap;text-overflow:ellipsis;margin-top: 20px;position:relative;" id="box">
                            <form class="layui-form" style="width: 95%;float: left;margin-left: 5px;height:18px;position: relative;">
                                <select name="city" lay-verify="required" lay-search id="mainSearchInpt" lay-filter="mainInpt">
								</select>
                            </form>
                            <i class="layui-icon layui-icon-search" style="position: absolute;z-index: 9999; right: 16px;top: 6px;"></i>
                            <button type="button" class="layui-btn" style="display: none;width: 51px;padding: 0;position:absolute;left:150px;top:0px;height:28px;line-height: 28px;" id="mainSearchBtn" ew-event="search"><i class="layui-icon layui-icon-search"></i></button>
                        </div>

                        <ul class="layui-nav layui-nav-tree" lay-accordion="true" lay-filter="admin-side-nav" style="margin-top: 15px;">
                            {{for data}}
                            <li class="layui-nav-item">
                                <a  {{if url && url.indexOf('/')==0}}lay-href="{{:url}}"{{/if}} 
									{{if url && url.indexOf('/')!=0}}onclick="openOutLink('{{:url}}', '{{:id}}', '{{:title}}')"{{/if}}
									href="javascript:;" data-id="{{:id}}" data-pid="{{:pid}}" ><i class="layui-icon layui-icon-unlink {{:icon}}"></i>&emsp;<cite>{{:title}}</cite></a>
								{{if nav && nav.length>0}}
                                <dl class="layui-nav-child">
                                    {{for nav}}
                                    <dd>
                                        <a  {{if url && url.indexOf('/')==0}}lay-href="{{:url}}"{{/if}} 
											{{if url && url.indexOf('/')!=0}}onclick="openOutLink('{{:url}}', '{{:id}}', '{{:title}}')"{{/if}}
											 data-id="{{:id}}" href="javascript:;">{{:title}}</a> {{if nav && nav.length>0}}
                                        <dl class="layui-nav-child">
                                            {{for nav}}
                                            <dd>
                                                <a  {{if url && url.indexOf('/')==0}}lay-href="{{:url}}"{{/if}} 
													{{if url && url.indexOf('/')!=0}}onclick="openOutLink('{{:url}}', '{{:id}}', '{{:title}}')"{{/if}}
												 	 data-id="{{:id}}" href="javascript:;">{{:title}}</a> {{if nav && nav.length>0}}
                                                <dl class="layui-nav-child">
                                                    {{for nav}}
                                                    <dd>
                                                        <a  {{if url && url.indexOf('/')==0}}lay-href="{{:url}}"{{/if}} 
															{{if url && url.indexOf('/')!=0}}onclick="openOutLink('{{:url}}', '{{:id}}', '{{:title}}')"{{/if}}
															 data-id="{{:id}}" href="javascript:;">{{:title}}</a> {{if nav && nav.length>0}}
                                                        <dl class="layui-nav-child">
                                                            {{for nav}}
                                                            <dd><a  {{if url && url.indexOf('/')==0}}lay-href="{{:url}}"{{/if}} 
																	{{if url && url.indexOf('/')!=0}}onclick="openOutLink('{{:url}}', '{{:id}}', '{{:title}}')"{{/if}}
																	 data-id="{{:id}}" href="javascript:;">{{:title}}</a></dd>
                                                            {{/for}}
                                                        </dl>
                                                        {{/if}}
                                                    </dd>
                                                    {{/for}}
                                                </dl>
                                                {{/if}}
                                            </dd>
                                            {{/for}}
                                        </dl>
                                        {{/if}}
                                    </dd>
                                    {{/for}}
                                </dl>
                                {{/if}}
                            </li>
                            {{/for}}

                        </ul>
                    </script>

		<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
		<!-- jsrender -->
		<script src="/easitline-static/js/jsrender.min.js" charset="utf-8"></script>

		<!-- js部分 -->
		<script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layui/layui.js" charset="utf-8"></script>
		<script type="text/javascript" src="${ctxPath}/portal/js/common.js?v=1204" charset="utf-8"></script>

		<script type="text/javascript" src="${ctxPath}/portal/js/remote.js?v=20220929" charset="utf-8"></script>

		<script type="text/javascript" src="${ctxPath}/portal/js/index.core.js?v=20191203" charset="utf-8"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20190625" charset="utf-8">
		</script>
		<script type="text/javascript" src="${ctxPath}/static/js/my_i18n.js?20211103" charset="utf-8"></script>
		<script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711" charset="utf-8"></script>

		<script>
			var currisShow = false;
			function changeNoticeShow(val) {
				// currisShow = isShow;
				if (!currisShow) {
					$('.activeNoticeShow').show()
					$('#no_activeNoticeImg').hide()
					$('.drag-box').css('width', '32.35%')
					$('.drag-box').css('border-radius', '15px 5px 5px 15px')
				} else {
					$('#no_activeNoticeImg').show()
					$('.activeNoticeShow').hide()
					$('.drag-box').css('width', 'auto')
					$('.drag-box').css('border-radius', '15px')
				}
				
				if(val == '1'){
					$('#red_activeNoticeImg').hide()
					$('#activeNoticeImg').show()
					$('#no_activeNoticeImg').hide()
				}
				
				
				currisShow = !currisShow;
			}
			var layer;
			var notice;
			layui.config({
				base: '${ctxPath}/portal/module/'
			}).extend({
				admin: 'admin',
				notice: 'notice/notice',
				dropdown: 'dropdown/dropdown',
				index: 'index'
			});
			layui.use(['layer', 'element', 'admin', 'index', 'dropdown', 'notice', 'form'], function() {
				var $ = layui.jquery;
				var admin = layui.admin;

				var isShowEditPassword = '${isShowEditPassword}';
				var needUpdatePwd = '${needUpdatePwd}';
				console.log('是否允许修改密码:' + isShowEditPassword + ",是否需要修改密码:" + needUpdatePwd);
				//20210408 密码过期后自动弹出密码修改界面
				if (isShowEditPassword == "Y" && needUpdatePwd == "Y") {
					layer.confirm(getI18nValue('密码过期提示'), {
						skin: 'layui-layer-admin',
						title: getI18nValue('提示'),
						btn: [getI18nValue('确定'), getI18nValue('取消')]
					}, function(index) {
						layer.close(index);
						modifyPwd();
					});

				} else {
					console.log("密码未过期，不需要修改密码");
				}

				$('#year').text(new Date().getFullYear())
				//加载设定好的导航栏小图标
				$('#mydiv').load("${ctxPath}/portal/tpl/tpl-theme.html");

				setTimeout(function() {
					var leftNav = '.layui-layout-admin>.layui-side>.layui-side-scroll>.layui-nav';
					// 导航小三角
					var navArrow = layui.data(admin.tableName).navArrow;
					const Storage = {};
					Storage.get = function(name) {
						return localStorage.getItem(name);
					}
					Storage.set = function(name, val) {
						localStorage.setItem(name, val);
					}
					var lastData = Storage.get("NavigationIcon");
					if (lastData) {
						var data = JSON.parse(lastData)
						layui.data(admin.tableName, {
							key: 'navArrow',
							value: data.value
						});
						top.layui.jquery(leftNav).removeClass('arrow2 arrow3');
						data.value && top.layui.jquery(leftNav).addClass(data.value);
					}
				}, 1000);

				layer = layui.layer;
				notice = layui.notice;
				var dropdown = layui.dropdown;
				var form = layui.form;
				var index = layui.index;
				var element = layui.element;
				var welcomeUrl = '/cc-portal/index?action=welcome';

				// 默认加载主页
				index.loadHome({
					menuPath: '${welcomeUrl}',
					menuName: '<i class="layui-icon layui-icon-home"></i>'
				});

				//获取部门列表 并设置默认选中
				$.ajax({
					url: '/cc-portal/workbench?action=userDepts',
					data: {},
					type: 'get',
					dataType: 'json',
					success: function(res) {
						var str = '';
						for (var i = 0; i < res.data.length; i++) {
							if (res.deptCode == res.data[i].deptCode) {
								str += '<li id="' + res.data[i].deptCode + '" class="departmentItem">' + res
									.data[i].deptName +
									'<i class="layui-icon layui-icon-ok isSelected"></i></li>'
							} else {
								str += '<li id="' + res.data[i].deptCode + '" class="departmentItem">' + res
									.data[i].deptName + '</li>'
							}
						}
						$('#departmentSelect').html(str)
						$('#departmentSelect').click(function(e) {
							$.ajax({
								url: '/cc-portal/workbench?action=setDefaultDept',
								data: {
									deptCode: e.target.id
								},
								type: 'get',
								dataType: 'json',
								success: function(res) {
									layer.msg(res.msg)
									setTimeout(function() {
										window.location.reload()
									}, 1500);
								}
							});
						})
					}
				});

				// 修改密码点击事件
				$('#setPsw').click(function() {
					modifyPwd();
				});

				// 退出登录点击事件
				$('#btnLogout').click(function() {
					layer.confirm(getI18nValue('确定退出登录'), {
						skin: 'layui-layer-admin',
						title: getI18nValue('提示'),
						btn: [getI18nValue('确定'), getI18nValue('取消')]
					}, function() {
						logout();
					});
				});

				// 点击远程
				$('#romoteBtn').on('click',function(e) {
					e.preventDefault()
					popUp()
				})
				// 读远程开启配置
				var remoteSwitch = localStorage.getItem('remoteSwitch')
				if (remoteSwitch == '0') {
					$('#romoteBtn').css('display','none')
				} 

				$.ajax({
					url: "${ctxPath}/workbench?query=menu",
					type: "post",
					cache: false,
					dataType: "json",
					async: true,
					success: function(data) {
						getNavHtml(data);

						var form = layui.form;
						// 递归查找
						var allItem = []

						function find(arr) {
							if (arr == null) {
								return null
							};
							arr.forEach(function(item, index, arr) {
								if (item.search && !item.nav) {
									allItem.push(item.search);
								} else {
									find(item.nav);
								}
							})
							return allItem;
						}
						var keywords;

						var alldata = data.data;
						var obj = find(alldata);
						keywords = obj;
						var html = '<option value=""> </option>';
						for (var i in keywords) {
							html += '<option value="' + keywords[i] + '">' + keywords[i] + '</option>';
						}
						$('#mainSearchInpt').html(html)

						form.on('select(mainInpt)', function(data) {
							$('#mainSearchInpt').val(data.value)
							$('#mainSearchBtn').click()
							$('#mainSearchInpt').val('')
							if ($('#mask')) {
								$('#mask').hide()
							}
							if ($('.layui-form-selected dl')) {
								$('.layui-form-selected dl').hide()
							}
						});
						form.render();

						// 判断当前侧栏是否展开
						var a = $('.layui-layout-admin').hasClass('admin-nav-mini')
						if (a == true) {
							$('#box').hide();
							// $('#box #mainSearchBtn').css('left', '3px');
						} else {
							$('#box').show();
							// $('#box #mainSearchBtn').css('left', '150px');
						}
						//透明遮盖层   点击关闭选择框
						var mask = document.createElement('div')
						mask.style = 'position:fixed;width:100%;height:100%;z-index:666;display:none;';
						mask.id = 'mask';
						document.getElementById("box").appendChild(mask);
						$('#box .layui-input').click(function() {
							$('#mask').show()
							// $('.layui-form-selected dl').show()
							try {
								$('#box .layui-anim').style.display = 'block'
							} catch (err) {}
						})
						$('#mask').click(function(e) {
							$('#mask').hide()
							try {
								$('#box .layui-anim').style.display = 'none'
							} catch (err) {}
						})
					}
				});

				//加载导航
				function getNavHtml(data) {
					var markup = document.getElementById("nav-template").innerHTML;
					var template = $.templates(markup);
					var html = template.render(data);
					$('.layui-side-scroll').html(html);
					$('.layui-side-scroll ul').prepend('<li class="layui-nav-item"><a lay-href="' + welcomeUrl +
						'" data-id="home" data-pid="2000" href="javascript:;"><i class="layui-icon layui-icon-home"></i>&emsp;<cite>' +
						getI18nValue("首页") + '</cite></a></li>'
					);
					element.render('nav');

				}
				/* 			//获取消息提示的红点
				            ajax.daoCall({
				                controls: ['messageView.getAffiche'],
				                params: {}
				            }, function (result) {
				                if (result['messageView.getAffiche'].data.length > 0) {
				                    noticeTip("block");
				                }
				            }) */

				ajax.remoteCall("/cc-base/api/spec?action=getAllLang", { timeout: 1000 }, function(result) {
					var list = unique(result.data);
					var select = document.getElementById('userLang');
					if (list != null || list.size() > 0) {
						// $("#userLang").find("option").remove();
						var str = '';
						for (var c in list) {
							str += '<dd lay-unselect data-code="' + list[c].LANG_CODE + '"  data-name="' + list[
									c].LANG_NAME + '"  onClick="choseLang(this,1)"><a href="#">' + list[c]
								.LANG_NAME; + '</a></dd>'
						}
						$("#userLang").html(str);

						//设置默认的语言
						var multiLang = localStorage.getItem("MULTI-LANG-KEY");
						var multiLang_msg = localStorage.getItem("MULTI-LANG-KEYNAME");
						if (!multiLang) {
							multiLang = "CN";
							localStorage.setItem('language', 'zh-CN')
							choseLang(multiLang, '中文', 2)
						} else {
							// 给IVR的
							switch (multiLang) {
								case 'EN':
									localStorage.setItem('language', 'en-US')
									break;
								case 'CN':
									localStorage.setItem('language', 'zh-CN')
									break;
								case 'ja':
									localStorage.setItem('language', 'ja')
									break;
								default:
									break;
							}

						}
						if (multiLang_msg) {
							$('#currentLang').text(multiLang_msg)
						}
					};
				});

				var isOpen = false
				var isMoreOpen = false
				$('.side-toolbar-btn').on({
					'mouseenter': function() {
						isOpen = true
						$('.side-toolbar').show()
					},
					'mouseleave': function() {
						isOpen = false
						setTimeout(function() {
							if (!isOpen && !moreShow && !isMoreOpen) {
								isOpen = false
								$('.side-toolbar').hide()
							}
						}, 500)
					}
				})

				$('.side-toolbar').on({
					'mouseleave': function() {
						isOpen = false
						if (!moreShow) {
							$(this).hide()
						}
					},
					'mouseenter': function() {
						isOpen = true
					}
				})

				$('body').on({
					'mouseleave': function() {
						isMoreOpen = false
						$(this).hide()
						isOpen = false
						setTimeout(function() {
							if (!isOpen) {
								$('.side-toolbar').hide()
							}
						}, 500)
					},
					'mouseenter': function() {
						isMoreOpen = true
					}
				}, '.more-menu-wrapper')

				$('body').on({
					'click': function(e) {
						var url = $(e.currentTarget).data('url')
						var name = $(e.currentTarget).data('name')
						popup.openTab(url, getI18nValue(name))
					}
				}, '.menu-item')

				// 监听侧边工具栏事件
				var toolbarIndex = null
				var moreShow = false
				$('.side-toolbar .toolbar-item').on({
					'mouseenter': function(e) {
						$(e.currentTarget).toggleClass('toolbar-item-active')
						var className = $(e.currentTarget).attr('class')
						var text = '',
							ele = '';
						if (className.indexOf('memorandum-tool') > -1) {
							text = getI18nValue('备忘录')
							ele = '.memorandum-tool'
						}
						if (className.indexOf('faq-tool') > -1) {
							text = getI18nValue('常见问题')
							ele = '.faq-tool'
						}
						if (className.indexOf('support-tool') > -1) {
							text = getI18nValue('系统建议')
							ele = '.support-tool'
						}
						if (className.indexOf('list-tool') > -1) {
							text = getI18nValue('系统建议管理')
							ele = '.list-tool'
						}
						if (className.indexOf('more-tool') > -1) {
							$.ajax({
								url: '/cc-base/servlet/menu?action=MenuList&pResId=cc-base-sys-right',
								success: function(res) {
									if (res.state == 1) {
										var html = ''
										for (var i = 0; i < res.data.length; i++) {
											html += '<div class="menu-item" data-name="' + res
												.data[i].RES_NAME + '" data-url="' + res.data[i]
												.RES_URL + '" >' +
												'<i class="layui-icon layui-icon-release" style="margin-right: 10px;"></i>' +
												getI18nValue(res.data[i].RES_NAME) + '</div>'
										}
										toolbarIndex = layer.tips(html, '.more-tool', {
											tips: 4,
											offset: [0, '20px'],
											time: 0,
											success: function(layerTips, index) {
												layerTips.addClass(
													'more-menu-wrapper')
												var left = layerTips.css('left')
												left = left.substring(0, left
													.indexOf('px'));
												layerTips.css("left", (left - 10) +
													'px');
												layerTips.find('.layui-layer-TipsL')
													.hide()
											}
										})
									} else {
										layer.msg(getI18nValue(res.msg), {
											icon: 5
										});
									}
								},
								error: function(err) {
									layer.msg(getI18nValue('出现网络故障,请稍后再试!'), {
										icon: 5
									});
								}
							})
						}
						if (className.indexOf('more-tool') < 0) {
							toolbarIndex = layer.tips(text, ele, {
								tips: 4,
								offset: [0, '20px'],
								time: 0,
								success: function(layerTips, index) {
									var left = layerTips.css('left')
									left = left.substring(0, left.indexOf('px'));
									layerTips.css("left", (left - 10) + 'px');
								}
							})
						}
					},
					'mouseleave': function(e) {
						$(e.currentTarget).toggleClass('toolbar-item-active')
						var className = $(e.currentTarget).attr('class')
						if (className.indexOf('more-tool') > -1) {
							moreShow = true
							isMoreOpen = false
							setTimeout(function() {
								if (!isMoreOpen && !isOpen) {
									$('.side-toolbar, .more-menu-wrapper').hide()
								}
							}, 500)
						} else {
							moreShow = false
							layer.close(toolbarIndex)
						}
					},
					'click': function(e) {
						var className = $(e.currentTarget).attr('class')
						if (className.indexOf('memorandum-tool') > -1) {
							popup.openTab('/cc-workbench/pages/memorandum-more.jsp', getI18nValue('备忘录'))
						}
						if (className.indexOf('faq-tool') > -1) {
							var title = getI18nValue('常见问题')
							var url = '/cc-base/pages/faq/faq-index.html'
							var height = parseInt(document.body.clientHeight - 90)
							var width = parseInt(document.body.clientWidth * 0.3)
							popup.layerShow({
								type: 2,
								title: title,
								offset: ['90px', width + 'px'],
								area: ['70%', height + 'px']
							}, "/cc-base/pages/faq/faq-index.html");
						}
						if (className.indexOf('support-tool') > -1) {
							var height = parseInt(document.body.clientHeight * 0.8)
							var width = parseInt(document.body.clientWidth * 0.8)
							var left = parseInt(document.body.clientWidth * 0.1)
							var top = parseInt(document.body.clientHeight * 0.1)
							popup.layerShow({
								type: 2,
								title: getI18nValue('系统建议'),
								area: [width + 'px', height + 'px'],
								offset: [top + 'px', left + 'px'],
							}, "/cc-base/pages/faq/fault-edit.html");
						}
						if (className.indexOf('list-tool') > -1) {
							popup.openTab('/cc-base/pages/faq/reportFaultList.jsp', getI18nValue('系统建议管理'))
						}
					}
				})

				
				//获取元素。
			    let demo = document.querySelector('#dragBox')
			    let drag = document.querySelector('#drag')
			    let canMove = false
			    let x = 0, y = 0
			    //鼠标按下事件
			    drag.onmousedown = function (e) {
			        x = e.pageX - demo.offsetLeft
			        y = e.pageY - demo.offsetTop
			        canMove = true
			    }
			    // console.log(demo.offsetLeft);
			    
			    //demo自定义右键窗口
			    demo.oncontextmenu = function (e) {
			        e.preventDefault()//阻止默认行为
			        // console.log('右键了');
			    }
			    //鼠标松开事件
			    window.onmouseup = function () {
			        canMove = false
			    }
			    //当前窗口失去焦点。
			    window.onblur = function () {
			        canMove = false
			    }
			    //鼠标移动事件
			    window.onmousemove = function (e) {
			        if (canMove) {
						e.preventDefault(); //阻止默认行为
			            let left = e.pageX - x
			            let top = e.pageY - y
			            if (left < 0) left = 0
			            if (top < 0) top = 0
			            let maxLeft = window.innerWidth - demo.offsetWidth
			            let maxTop = window.innerHeight - demo.offsetHeight
			            if (left > maxLeft) left = maxLeft
			            if (top > maxTop) top = maxTop
			            demo.style.left = left + "px"
			            demo.style.top = top + 'px'
			        }
			    }
			    

				//获取系统公告
				if("Y" == '${showNotes}'){
				    
					//获取系统公告
					getNotes()
					setInterval(function() {
						getNotes()
					}, 180000)
					
				} else {
					$("#dragBox").css("display","none");				
	
				}
				

				function getNotes() {
					$.ajax({
						//请求方式
						type: "POST",
						//请求的媒体类型
						contentType: "application/json;charset=UTF-8",
						//请求地址
						url: "/cc-portal/workbench?action=getNotes",
						timeout: 3000,
						//数据，json字符串
						data: {},
						//请求成功
						success: function(result) {
							let str2 = '';
							let res = JSON.parse(result.data).noticeList;
							let reg = /<\/?.+?\/?>/g;
							if (!res || res.length == 0) {
								$('#lbt').html('<p style="text-align: center;">' + getI18nValue('暂无数据') +
									'</p>')
								return
							}
							// <img src="/cc-portal/portal/images/notice_red.png"
							// 	style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;display: none;"
							// 	onclick="changeNoticeShow(true)" id="red_activeNoticeImg">
							// <img src="/cc-portal/portal/images/notice_nor.png"
							// 	style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;"
							// 	onclick="changeNoticeShow(true)" id="no_activeNoticeImg">
							// <img src="/cc-portal/portal/images/notice_select.png"
							// 	style="width: 30px;height: 30px;vertical-align: middle;cursor: pointer;display: none;"
							// 	onclick="changeNoticeShow(false)" id="activeNoticeImg" class="activeNoticeShow">
							for (let i = 0; i < res.length; i++) {
								// var val = res[i].NOTICE_CONTENT.replace(reg, '');


								if (res[i].IS_READ == '0') {
									$('#red_activeNoticeImg').show()
									$('#no_activeNoticeImg').hide()
									$('#activeNoticeImg').hide()
								}


								let val = res[i].NOTICE_TITLE;
								let notesObjJson = JSON.stringify(res[i]).replace(/\"/g, "'");

								if (res[i].IS_IMPORTANT == '1') {

									str2 +=
										'<p  style="width:auto!important;height: 30px;margin-top:0;" class="rightBottomMsg" onclick="notesDetils(' +
										notesObjJson + ')" ><span  class="rbleftSpan"> ' + val + '</span>';
								} else {
									str2 +=
										'<p  style="width:auto!important;height: 30px;margin-top:0;" class="rightBottomMsg"  onclick="notesDetils(' +
										notesObjJson + ')"><span class="rbleftSpan"> ' + val + '</span>';
								}




								// if (res[i].IS_READ == '0') {
								// 	str2 +=
								// 		'<span class="layui-badge-dot" style="transform:translateY(-15px);display:inline-block;margin-left:3px;"></span>'
								// }

								str2 += '</p>';
							}
							// $('#msgBox').html(str)
							$('#lbt').html(str2)

							layui.use('carousel', function() {
								var carousel = layui.carousel;
								carousel.render({
									elem: '#activeNoticeMsg',
									width: '80%',
									height: '30px',
									arrow: 'none',
									anim: 'updown',
									indicator: 'none',
									interval: 5000
								});
							});
						},
						//请求失败，包含具体的错误信息
						error: function(e) {
							// $('#msgBox').html(e.responseText)
							console.log(e.status);
							console.log(e.responseText);
						}
					});
				}
			});

			//查看公告详情
			function notesDetils(notesObj) {
				if (typeof(notesObj.IS_READ) == "undefined" || notesObj.IS_READ == "") {
					insertRecv(notesObj);
				}
				var noticeId = notesObj.NOTICE_ID;
				ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=read&noticeId=" + noticeId, {}, function(result) {
					if (result.state == 1) {
						popup.layerShow({
							type: 2,
							title: getI18nValue("查看公告"),
							offset: 'r',
							area: ['800px', '100%'],
							url: "/cc-workbench/pages/noticeInfoMain.jsp",
							data: {
								noticeId: noticeId,
								artificial: "Y"
							}
						});

						setTimeout(function() {
							getNotes()
						}, 3000);
					}
				})
			}
			// 打开外部链接（不为本域名的地址）
			function openOutLink(url, id, title) {
				$.ajax({
					url: '/cc-portal/workbench?query=GetToken',
					type: 'post',
					dataType: 'JSON',
					success: function(res) {
						if (res && res.state == 1) {
							if (url.indexOf('?') == -1) {
								url += '?';
							} else {
								url += '&';
							}
							url = url + 'token=' + res.data;
						}
						popup.openTab({
							id: id,
							title: getI18nValue(title),
							url: url
						});
					},
					error: function(e) {
						console.log('query token error: ' + e, e);
						popup.openTab({
							id: id,
							title: getI18nValue(title),
							url: url
						});
					}
				})
			}

			// 上报网络延迟
			function upLoadStatus(time) {
				$.ajax({
					url: '/cc-base/api/check?action=upLoadStatus&reply=' + time,
					timeout: 1000,
					success: function(res) {}
				})
			}

			if (window.Notification) {
				if (Notification.permission == "granted") {
					console.log('浏览器允许Notification发送通知');
				} else {
					Notification.requestPermission(function(permission) {
						console.log('Notification发起通知申请');
					});
				}
			} else {
				console.log('浏览器不支持Notification');
			}

			// 设置信号状态
			function setWifi(respTime) {
				if (respTime >= 0 && respTime < 30) {
					$('.wifi-line').css('background', '#000')
				} else if (respTime >= 30 && respTime < 100) {
					$('.wifi-line').css('background', '#000')
					$('.four-line').css('background', '#ccc')
				} else if (respTime >= 100 && respTime < 300) {
					$('.wifi-line').css('background', '#000')
					$('.four-line,.third-line').css('background', '#ccc')
				} else if (respTime >= 300 && respTime < 500) {
					$('.wifi-line').css('background', '#000')
					$('.four-line,.third-line,.second-line').css('background', '#ccc')
				} else {
					$('.wifi-line').css('background', '#ccc')
				}
				if (respTime > 2000) {
					upLoadStatus(respTime)
					var text = getI18nValue('您的网络不太稳定,网络响应时长：') + respTime + 'ms'
					if ($('#wifiNotice')[0]) notice.hide({}, $('#wifiNotice')[0]);
					notice.error({
						id: 'wifiNotice',
						title: getI18nValue('网络延迟'),
						message: text,
						timeout: 3000
					});
				}
				$('.wifi-wrapper').attr('title', getI18nValue('网络响应时长：') + respTime + 'ms')
			}

			function choseLang(val, num) {
				// 如果num为1，则是点选语言模式；如果为2，则是js触发改变语言
				var name = $(val).data('name');
				if (num == 1) {
					var code = $(val).data('code');
					localStorage.setItem("MULTI-LANG-KEY", code);
					localStorage.setItem("MULTI-LANG-KEYNAME", name);
					ajax.remoteCall("/cc-portal/workbench?action=setLang", {
						"lang": code
					}, function(result) {
						if (result.state == 1) {
							layer.msg(result.msg, {
								icon: 1
							});
							window.location.reload()
						} else {
							layer.alert(result.msg, {
								icon: 5
							});
						}
					});
				} else if (num == 2) {
					var data = {};
					data.value = val || $('#userLang').val();
					localStorage.setItem("MULTI-LANG-KEY", data.value);
					localStorage.setItem("MULTI-LANG-KEYNAME", name);
					ajax.remoteCall("/cc-portal/workbench?action=setLang", {
						"lang": data.value
					}, function(result) {
						if (result.state == 1) {
							layer.msg(result.msg, {
								icon: 1
							});
						} else {
							layer.alert(result.msg, {
								icon: 5
							});
						}
					});
				}
			}

			function unique(arr) {
				var result = [];
				var obj = {};
				for (var i = 0; i < arr.length; i++) {
					if (!obj[arr[i].LANG_CODE]) {
						result.push(arr[i]);
						obj[arr[i].LANG_CODE] = true;
					}
				}
				return result;
			}

			function modifyPwd() {
				popup.layerShow({
					type: 1,
					title: getI18nValue('修改密码'),
					area: ['400px', '300px'],
					offset: '100px'
				}, "${ctxPath}/pages/entmgr/password-modify.jsp");
			}

			//控制消息提醒小红点的显示
			var noticeTip = function(style) {
				$(".layui-badge-dot").css("display", style);
			}

			var ctxPath = '${ctxPath}';
			var isAgentUser = '${isAgentUser}';
			var entId = '${userData.entId}';
			var iscrypt = '${showcrypt}';
			localStorage.setItem("iscrypt", iscrypt);
			/** ******** 不处理这种定制内容
			if (entId == 3000) {
			    $('#portalTitle').html('&nbsp;小润招聘助手&emsp;');
			}
			*/
			localStorage.setItem("userEntId", "${userData.entId}");
			localStorage.setItem("userAccount", "${userData.loginAcct}");
			localStorage.setItem("userName", "${userData.userName}");
			localStorage.setItem("userEntName", "${userData.entName}");
			localStorage.setItem("userId", "${userData.userId}");
			localStorage.setItem("busiId", "${userData.busiId}");
			localStorage.setItem("userBusiId", "${userData.busiId}");
			localStorage.setItem("busiOrderId", "${busiOrderId}");
			localStorage.setItem("userNickName", "${userNickName}");
			localStorage.setItem("userAgentPhone", "${userAgentPhone}");
			localStorage.setItem("evtAlteringUrl", "${evtAlteringUrl}");
			localStorage.setItem("remoteSwitch", "${remoteSwitch}");
			//3.1#********-1 设置振铃弹屏url
			if ("${evtAlteringUrl}" == "") {
				localStorage.removeItem("evtAlteringUrl");
			} else {
				localStorage.setItem("evtAlteringUrl", "${evtAlteringUrl}");
			}

			var refreshFollowMiniutes = "${refreshFollowMiniutes}";
			if (!refreshFollowMiniutes || refreshFollowMiniutes == "" || refreshFollowMiniutes == "undefined") {
				refreshFollowMiniutes = 0;
			}
			localStorage.setItem("refreshFollowMiniutes", "${refreshFollowMiniutes}");


			//初始化定时器：用于查询通知
			console.log('启动定时查询通知:61秒查询一次');
			if (window.getNotice) window.getNotice('/cc-base/NoticeServlet?action=searchUserNotice',
				'/cc-base/NoticeServlet?action=answerUserNotice', 120);

			//定时加载在线会话待跟进记录
			if (refreshFollowMiniutes > 0) {
				console.log('启动定时加载在线会话待跟进记录:' + (refreshFollowMiniutes * 60) + '秒查询一次');
				setInterval(function() {
					getFollowList();
					//}, 5000);
				}, refreshFollowMiniutes * 60 * 1000);
			} else {
				console.log('不启动定时加载在线会话待跟进记录,企业参数配置REFRESH_FOLLOW_MINIUTES为空或为0');
			}
			
			if("${remoteSwitch}" == "1"){
				console.log('已配置为开启远程协助功能，准备拉起远程主控端...');
			}
			
		</script>
		<c:if test="${isAgentUser=='true'}">
			<!-- ccbar -->
			<script type="text/javascript" src="/yc-res/config.js"></script>
			<script src="/yc-ccbar/ccbar/ccbar.js?v=1546828141708"></script>
			<script src="/yc-ccbar/ccbar/ccbar_sdk.js?v=1546828141708"></script>
			<script src="${ctxPath}/ccbar/ccbar_agent.js?v=20220809-03"></script>
			<!-- ccbar end -->
			<script>
				var ccbarWorkmode = "0"; //是否显示工作模式            1-是 0-否
				var showAutoAnswer = "0"; //是否显示自动应答勾选框  1-是 0-否
				var showVideoBtn = "0"; //是否显示视频客服按钮      1-是 0-否
				var showPhoneDirBtn = "0"; //是否显示通讯录按钮  1-是  0-否
				$.ajax({
					url: '/cc-portal/workbench?query=isCcbrWorkmode',
					data: {},
					type: 'get',
					dataType: 'json',
					success: function(res) {
						if (res.workModeCode == "1") {
							ccbarWorkmode = res.workModeCode;
						}
						if (res.showAutoAnswer == "1") {
							showAutoAnswer = res.showAutoAnswer;
						}
						if (res.showVideoBtn == "1") {
							showVideoBtn = res.showVideoBtn;
						}
						if (res.showPhoneDirBtn == "1") {
							showPhoneDirBtn = res.showPhoneDirBtn;
						}
					}
				});

				ccbarEvent.addEvent('agentStateSync', function(e) {
					var satisKey = $('#satisKey').val();
					if (e.funcMask.sstransfer == true && (typeof(satisKey) != 'undefined' && satisKey)) {
						$('#ivrBtn').show()
					} else {
						$('#ivrBtn').hide()
					}
					if (ccbarWorkmode == "1") {
						$("#workModeSpan").hide();
					} else {
						$("#workModeSpan").show();
					}
					if (showAutoAnswer == "1") {
						$("#autoAnswerSpan").hide();
					} else {
						$("#autoAnswerSpan").show();
					}
				})
				$('#ivrBtn').on('click', function(e) {
					var satisKey = $('#satisKey').val();

					if (CallControl.getFunc('sstransfer') && (typeof(satisKey) != 'undefined' && satisKey)) {
						CallControl.sstransfer(satisKey, latestCall.event.custPhone, '', '2', {
							busiOrderId: latestCall.event.busiOrderId,
							callSerialId: latestCall.event.callSerialId,
							entId: latestCall.event.entId,
							command: "respSatisf"
						}, function(result) {
							console.log('满意度:', result);
							tipsMsg(getI18nValue('已发起满意度'));
						});
					}
				});
			</script>
		</c:if>
		<script src="${ctxPath}/portal/module/pageControl.js"></script>

		<!-- 聊天界面相关 -->
		<!-- 右下消息提示 -->
		<script src="${ctxPath}/static/js/chatWinLayerContent.js"></script>
		<script type="text/javascript" id="chat-script">
			/*悬浮框*/
			var _askPanel = null;
			var _askPanel_show = false

			function toggleChatPanel(obj) {
				$(obj).find("i").toggleClass("mMessageWhite mClose2")
				if (!_askPanel) {
					openChatWin();
					_askPanel = true;
					_askPanel_show = true;
				} else {
					$(top.document.body).find('#chatWinLayerContent').parents('.layui-layer').toggle();
					if (_askPanel_show) {
						_askPanel_show = false;
						$("#_askPanel_").addClass('hideExpertsAsk')
						$("#_askPanel_").parent().css({
							'zIndex': -1,
							'opacity': 0
						});
					} else {
						_askPanel_show = true;
						$("#_askPanel_").removeClass('hideExpertsAsk')
						$("#_askPanel_").parent().css({
							'zIndex': 221,
							'opacity': 1
						});
					}
				}
			}
			var ccbarReadymode = "0";
			$(function() {
				$.ajax({
					url: '/cc-portal/workbench?query=ccbarReadymode',
					data: {},
					type: 'get',
					dataType: 'json',
					success: function(res) {
						if (res.ccbarReadymode == "1") {
							ccbarReadymode = res.ccbarReadymode;
						}
					}
				});
				$.ajax({
					url: '/cc-portal/workbench?query=isWatemaek',
					data: {},
					type: 'get',
					dataType: 'json',
					success: function(res) {
						if (res.labelCode == "1") {
							var longinAcct = '${userData.userName}';
							var portalTitle = "";
							var time = "";
							if (res.bodyCode == "0") {
								var portalTitle = '${userData.entName }';
								var time = new Date();
								watermark(portalTitle, longinAcct, "", time.toLocaleString(), 200, 60, 40,
									245, 150);
							} else {
								watermark(portalTitle, longinAcct, "", time.toLocaleString(), 60, 10, 22,
									245, 120);
							}
						}
					}
				});
				var isOpenWebIM = $("#isOpenWebIM").val();
				var isCommunication = '${isCommunication}';
				if ("Y" == isOpenWebIM && "1" == isCommunication) {
					var chatHtml = '<div class="chat-bottom-tips">' +
						'<span id="expertsAsk-badge" class="chat-badge"></span>' +
						'<a href="javascript:;" class="chat-open-btn" style="height: 28px;">' +
						'<i class="mIcon mMessageWhite"></i>' +
						'</a>' +
						'</div>';

					//var chatHtml = $('#chat-bottom').text();
					$('body', document).append(chatHtml);
					var btn = $(document).find('a.chat-open-btn');
					if (btn.length > 0) {
						btn[0].onclick = function() {
							toggleChatPanel(this);
						}
					}
					setTimeout(function() {
						openChatWin(true);

					}, 1000);
				}
			})
			//显示未读消息数
			function showUnReadNum(num) {
				if (!num || num <= 0 || num == "0") {
					$("#expertsAsk-badge").text("");
					return;
				}
				if (num > 99) {
					$("#expertsAsk-badge").text("99+");
					return;
				}
				$("#expertsAsk-badge").text(num);
				//$(".chat-badge").text(num);
			}

			function setIsFixedChatPanel(obj) {
				var btn = $(document).find('a.chat-open-btn');
				$(btn).find("i").toggleClass("mMessageWhite mClose2")
				_askPanel_show = false;
				$("#_askPanel_").addClass('hideExpertsAsk')
				$("#_askPanel_").parent().css({
					'zIndex': -1,
					'opacity': 0
				});
			}

			function watermark(name, userAcc, ip, now, x_spac, y_spac, angle, width, height) {
				//默认设置
				var defaultSettings = {
					//		        watermark_txt: "text",
					watermark_x: 100, //水印起始位置x轴坐标
					watermark_y: 120, //水印起始位置Y轴坐标
					watermark_rows: 20, //水印行数
					watermark_cols: 20, //水印列数
					watermark_x_space: x_spac, //水印x轴间隔
					watermark_y_space: y_spac, //水印y轴间隔
					watermark_color: '#aaa', //水印字体颜色
					watermark_alpha: 0.6, //水印透明度
					watermark_fontsize: '18.7px', //水印字体大小
					watermark_font: '微软雅黑', //水印字体
					watermark_width: width, //水印宽度
					watermark_height: height, //水印长度
					watermark_angle: angle //水印倾斜度数
				};
				if (arguments.length === 1 && typeof arguments[0] === "object") {
					var src = arguments[0] || {};
					for (key in src) {
						if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) continue;
						else if (src[key]) defaultSettings[key] = src[key];
					}
				}
				var oTemp = document.createDocumentFragment();
				//获取页面最大宽度
				var page_width = Math.max(document.body.scrollWidth, document.body.clientWidth);
				var cutWidth = page_width * 0.0150;
				var page_width = page_width - cutWidth;
				//获取页面最大高度
				var page_height = Math.max(document.body.scrollHeight, document.body.clientHeight) + 450;
				page_height = Math.max(page_height, window.innerHeight - 30);
				//如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
				if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings
						.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (
							defaultSettings.watermark_cols - 1)) > page_width)) {
					defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings
						.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
					defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings
						.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
				}
				//如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
				if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings
						.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (
							defaultSettings.watermark_rows - 1)) > page_height)) {
					defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings
						.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
					defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings
						.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
				}
				$(".mask_div").remove();
				var x;
				var y;
				for (var i = 0; i < defaultSettings.watermark_rows; i++) {
					y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
					if (i % 2 == 0) {
						for (var j = 0; j < defaultSettings.watermark_cols; j++) {
							x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings
								.watermark_x_space) * j;

							var mask_div = document.createElement('div');
							mask_div.id = 'mask_div' + i + j;
							mask_div.className = 'mask_div';

							var mask_div1 = document.createElement('div');
							mask_div1.appendChild(document.createTextNode(name));
							var mask_div2 = document.createElement('div');
							mask_div2.appendChild(document.createTextNode(userAcc));
							var mask_div3 = document.createElement('div');
							mask_div3.appendChild(document.createTextNode(ip));
							var mask_div4 = document.createElement('div');
							mask_div4.appendChild(document.createTextNode(now));
							mask_div.appendChild(mask_div1);
							mask_div.appendChild(mask_div2);
							mask_div.appendChild(mask_div3);
							mask_div.appendChild(mask_div4);


							//设置水印div倾斜显示
							mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.visibility = "";
							mask_div.style.position = "absolute";
							mask_div.style.left = x + 'px';
							mask_div.style.top = y + 'px';
							mask_div.style.overflow = "hidden";
							mask_div.style.zIndex = "9999";
							//让水印不遮挡页面的点击事件
							mask_div.style.pointerEvents = 'none';
							mask_div.style.opacity = defaultSettings.watermark_alpha;
							mask_div.style.fontSize = defaultSettings.watermark_fontsize;
							mask_div.style.fontFamily = defaultSettings.watermark_font;
							mask_div.style.color = defaultSettings.watermark_color;
							//		            mask_div.style.textAlign = "center";
							mask_div.style.width = defaultSettings.watermark_width + 'px';
							mask_div.style.height = defaultSettings.watermark_height + 'px';
							mask_div.style.display = "block";
							oTemp.appendChild(mask_div);
						};

					} else {
						for (var j = 0; j < defaultSettings.watermark_cols; j++) {
							x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings
								.watermark_x_space) * j + (defaultSettings.watermark_x_space * 2.0);
							var mask_div = document.createElement('div');
							mask_div.id = 'mask_div' + i + j;
							mask_div.className = 'mask_div';

							var mask_div1 = document.createElement('div');
							mask_div1.appendChild(document.createTextNode(name));
							var mask_div2 = document.createElement('div');
							mask_div2.appendChild(document.createTextNode(userAcc));
							var mask_div3 = document.createElement('div');
							mask_div3.appendChild(document.createTextNode(ip));
							var mask_div4 = document.createElement('div');
							mask_div4.appendChild(document.createTextNode(now));
							mask_div.appendChild(mask_div1);
							mask_div.appendChild(mask_div2);
							mask_div.appendChild(mask_div3);
							mask_div.appendChild(mask_div4);

							//设置水印div倾斜显示
							mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
							mask_div.style.visibility = "";
							mask_div.style.position = "absolute";
							mask_div.style.left = x + 'px';
							mask_div.style.top = y + 'px';
							mask_div.style.overflow = "hidden";
							mask_div.style.zIndex = "9999";
							//让水印不遮挡页面的点击事件
							mask_div.style.pointerEvents = 'none';
							mask_div.style.opacity = defaultSettings.watermark_alpha;
							mask_div.style.fontSize = defaultSettings.watermark_fontsize;
							mask_div.style.fontFamily = defaultSettings.watermark_font;
							mask_div.style.color = defaultSettings.watermark_color;
							//		            mask_div.style.textAlign = "center";
							mask_div.style.width = defaultSettings.watermark_width + 'px';
							mask_div.style.height = defaultSettings.watermark_height + 'px';
							mask_div.style.display = "block";
							oTemp.appendChild(mask_div);
						};

					}

				};
				document.body.appendChild(oTemp);
			}





			function closeItSelf(event) {
				event.stopPropagation()
				$(event.target).parent().remove();
			}


			var div = document.createElement('div');

			function showTipsMsg(msg) {
				// var div = document.createElement('div');
				var e = window.event;
				$(div).css('display', 'block')
				$(div).css('border', '1px solid #000')
				$(div).css('padding', '10px')
				$(div).css('wordWrap', 'break-word')
				$(div).css('width', '200px');
				$(div).css('backgroundColor', '#f8f8f8')
				$(div).css('borderRadius', '6px')
				$(div).css('position', 'fixed')
				$(div).css('zIndex', '99999')
				$(div).css('left', e.clientX - 200)
				$(div).css('top', e.clientY + 20)
				$(div).html(msg)
				document.body.append(div)
			}

			function hideTipsMsg(params) {
				$(div).css('display', 'none')
			}

			function openLink(event, params, params2) {
				event.stopPropagation()
				$(event.target).parent().parent().remove();
				popup.openTab({
					title: params ? params : '新消息',
					url: params2,
					id: 'myTips' + randomNum(0, 10000)
				})
			}

			//生成从minNum到maxNum的随机数
			function randomNum(minNum, maxNum) {
				switch (arguments.length) {
					case 1:
						return parseInt(Math.random() * minNum + 1, 10);
						break;
					case 2:
						return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
						break;
					default:
						return 0;
						break;
				}
			}


			//打开页面的方式
			function openPageType(currentOpenType, noticeUrl, title) {
				// 01-弹出层 02-独立页签 03-新窗口

				if (currentOpenType == '01') {
					layer.open({ //弹出层
						type: 2,
						title: title,
						content: [noticeUrl, 'no'],
						area: ['50%', '50%']
					});
				} else if (currentOpenType == '02') {
					openUrl(noticeUrl, title); //独立页签
				} else if (currentOpenType == '03') {
					window.open(noticeUrl, title);
				} else { //默认01
					layer.open({ //弹出层
						type: 2,
						title: title,
						content: [noticeUrl, 'no'],
						area: ['50%', '50%']
					});
				}
			}




			// 内部消息websocket
			var preTime = 0; // 记录心跳发送时时间戳
			var respTime = 0; // 记录心跳返回时反应时间
			var heart = null; // 心跳定时器
			var wsLoginNotice = true; //是否进行异地登录提醒
			var agentChatFun = {
				ws: null,
				ip: '',
				port: '',
				lastMsg: '',
				host: '',
				sessionId: '',
				account: '',
				userInfo: '',
				userName: '',
				sendContent: '',
				init: function(ip, port, callback) {
					console.info('连接ws - agentChat : init.')
					var tempHost = location.origin;
					var wshost = tempHost.replace('http', 'ws');
					agentChatFun.host = wshost + '/cc-base/agentChat';
					ccbarWs = setInterval(function() {
						agentChatFun.heartbeat();
					}, 60000);
				},
				connect: function() {
					$.ajax({
						url: '/cc-base/servlet/agentChat?action=checkUserLoginStatus',
						data: {},
						type: 'get',
						dataType: 'json',
						timeout: 1000,
						success: function(res) {
							if (res.state == 1) {
								if (res.data && res.data.loginOut && res.data.loginOut == true) {
									console.error('无法连接ws - agentChat : 账号已在其它地方登录,' + res.data.loginOutDesc)
									layer.alert(res.data.loginOutDesc, {
										icon: 5,
										offset: 'rb'
									}, function(index) {
										logout();
									});
								} else {
									if (res.data && res.data.loginOutDesc) {
										console.error('连接ws - agentChat :,' + res.data.loginOutDesc)
										if (wsLoginNotice) {
											/**
											notice.error({
												id: 'wsLoginNotice',
												title: getI18nValue('异地登录'),
												message: res.data.loginOutDesc,
												timeout: 3000,
												onclick: function(){
													
												}
											});
											*/
											layer.msg(res.data.loginOutDesc, {
												icon: 5,
												time: 5000,
												offset: 'rb',
												btn: [getI18nValue('我知道了')],
												yes: function(index) {
													wsLoginNotice = false;
													layer.close(index);
												}
											});
										}
									}
									var tempHost = location.origin;
									var wshost = tempHost.replace('http', 'ws');
									var host = wshost + '/cc-base/agentChat';
									agentChatFun.ws = new WebSocket(host);
									agentChatFun.ws.onmessage = agentChatFun.handlerMessage;
									agentChatFun.ws.onopen = agentChatFun.onopen;
									agentChatFun.ws.onclose = agentChatFun.onclose;
									return true;
								}
							}
							if (res.state == 505) {
								console.error('无法连接ws - agentChat : 登录超时.')
								layer.alert(getI18nValue("登录超时"), {
									icon: 5
								}, function(index) {
									logout();
								});
							}
							if (res.state == 500) {
								console.error('无法连接ws - agentChat : 操作失败')
								layer.msg(getI18nValue("操作失败"), {
									icon: 5
								}, function() {
									console.error('无法连接ws - agentChat : 准备重连...')
									agentChatFun.onclose();
								});
							}

						}
					});
				},
				disconnect: function() {
					try {
						agentChatFun.ws.close();
					} catch (e) {
						console.log('ws关闭失败,', e);
					}
				},
				send: function(event, data, callback) {
					var _data = {
						event: event
					}
					_data = $.extend({}, _data, data);
					_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
					if (agentChatFun.ws.readyState === WebSocket.OPEN) {
						agentChatFun.ws.send(_data);
					}
				},
				sendMsg: function(event, data, callback) {
					var _data = {
						event: event
					}
					if (event == "sendChat") {
						_data.cmdJson = data;
						_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data);
					} else if (event == "withdraw") {
						_data.cmdJson = data;
						_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
					} else if (event == "readMsg") {
						_data.cmdJson = data;
						_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
					} else {
						_data = $.extend({}, _data, data);
						_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
					}
					if (agentChatFun.ws.readyState === WebSocket.OPEN) {
						agentChatFun.ws.send(_data);
					}
				},
				handlerMessage: function(message) {
					var messageData = JSON.parse(message.data);
					if (messageData.msgType == "agentNotice") { //实时通知
						// layer.msg(messageData.msgContent);
						var li = document.createElement('li');
						li.className = 'rtTips_li';

						var str = '';
						if (messageData.chat.type == 'text') {
							str =
								'<p style="margin:0;height:23px;line-height:21px;font-weight:700;"><i style="display:inline-block;margin-right:10px;transform: translateY(15px);" class="layui-icon layui-icon-notice"></i>' +
								getI18nValue('您有一条新的消息！') + '</p>';
							str += '<p class="rtTips_p2" onmousemove="showTipsMsg(' + messageData.chat.content +
								')"   onmouseleave="hideTipsMsg()">';
							str += messageData.chat.content;
						} else if (messageData.chat.type == 'link') {
							str =
								'<p style="margin:0;height:23px;line-height:21px;font-weight:700;"><i style="display:inline-block;margin-right:10px;transform: translateY(15px);" class="layui-icon layui-icon-notice"></i>' +
								(messageData.chat.title ? messageData.chat.title : '新消息提醒') + '</p>';
							str += '<p class="rtTips_p2" onmousemove="showTipsMsg(' + messageData.chat.content +
								')"   onmouseleave="hideTipsMsg()">';
							str += '<a style="color:#fff;text-decoration: underline;" href="javascirpt:void(0)" > ' +
								messageData.chat.content + '</a>';
						}
						str += '</p>'
						str +=
							'<div onclick="closeItSelf(event)" style="background-color: #409EFF;width: 30px;font-size:30px;position:absolute;border-radius:4px;right: 10px;top: 50%;transform: translateY(-50%);" class="layui-btn layui-btn-xs  layui-btn-normal">×</div>';

						li.innerHTML = str;




						var noticeJson = messageData.noticeJson;
						// console.log('-------------------noticeJson:::', noticeJson)
						var noticeUrl = noticeJson.url;

						var currentOpenType = noticeJson.urlOpenType;
						if (noticeJson) {
							var noticeUrl = noticeJson.url;
							if (noticeUrl && noticeUrl != '' && noticeUrl != '..') {
								li.onclick = function() {
									$(li).remove();
									// openUrl(noticeUrl, getI18nValue('查看'))
									openPageType(currentOpenType, noticeUrl, getI18nValue('查看'))
								};
							} else {
								if (noticeJson.openType == '02') { // 点击弹层layer.open
									li.onclick = function() {
										layer.open({
											type: 2,
											title: '审批来自【' + noticeJson.createUserAcc + '】的置忙请求',
											content: [noticeUrl, 'no'],
											area: ['450px', '335px']
										});
									};
								} else { //点击弹窗openUrl
									var noticeUrl = noticeJson.url;
									if (noticeUrl && noticeUrl != '' && noticeUrl != '..') {
										li.onclick = function() {
											$(li).remove();
											// openUrl(noticeUrl, getI18nValue('查看'))
											openPageType(currentOpenType, noticeUrl, getI18nValue('查看'))
										};
									} else {
										li.onclick = function() {
											$(li).remove();

											openPageType('02',
												'/cc-portal/pages/config/sys-config2.jsp?resId=cc-base-sys-notice',
												getI18nValue('系统通知'))
										};
									}
								}
							}
						}



						$('#rtTips').append(li);
						var closeTime = 30000;
						if (messageData.chat.showTime) {
							closeTime = Number(messageData.chat.showTime) * 1000;
						}
						var dsq = setTimeout(function() {
							$(li).remove()
						}, closeTime)

						$(li).mouseenter(function(params) {
							if (dsq) {
								clearTimeout(dsq)
							}
						})

						try {
							var mp3 = new Audio("/cc-portal/static/plugin/notice.wav");
							mp3.play(); //播放 mp3这个音频对象
						} catch (e) {
							console.error(e);
						}
					}

					// if (messageData.msgType != "heartbeat") {
					// console.log("handlerMessage", messageData)
					// // $("iframe[src^='/cc-base/pages/tpl/tpl-message.jsp']")[0].contentWindow.insideMessage();
					// }

					var isOpenMain = null;
					$.each($(".layui-layer-iframe"), function(index, value) {
						if ($(value).find('iframe').attr("src").indexOf('tpl-message.jsp') > 0) {
							isOpenMain = true;
						}
					})


					// $($("iframe[src='/cc-base/pages/tpl/tpl-message.jsp']")[0]).contents().find('#test').click()

					if (messageData.msgType == "createChat") {
						if (isOpenMain) { //打开了首页消息右侧侧边栏
							// console.log(1)

							var childNode = $("iframe[src='/cc-base/pages/tpl/tpl-message.jsp']").contents()
								.find(
									"#insideList");
							if (childNode.find('.message-list-item').length > 0) {
								childNode.append(
									'<div class="message-list-item" href="javascript:;" id="record' +
									messageData.chatRecord.ID +
									'" onclick="notification.sendPerson(this,&quot;' +
									messageData.chatRecord.ID + '&quot;,&quot;' + messageData.chatRecord
									.USER_NAME +
									'&quot;)"><div class="message-item-box"><div class="item-box-left"><img class="item-box-icon" src="/cc-base/static/images/user.png" ></div><div class="item-box-right"><div class="item-box-header">' +
									messageData.chatRecord.USER_NAME + '（' + messageData.chatRecord
									.USER_ACC + '）' +
									messageData.chatRecord.USER_DEPT_NAME +
									'</div><div class="item-box-bottom"><div class="item-content-left"><!-- <div class="message-item-content">{{:BAKUP}}</div> --> <!-- 待确认 --><div class="message-item-content">您有会话消息，请回复！</div><div class="message-item-text">' +
									messageData.chatRecord.BEGIN_TIME +
									'</div></div><div class="item-content-right"><div class="message-item-child"></div></div></div></div></div></div>'
								)
							}
						} else {
							noticeTip("block");
						}
					} else if (messageData.msgType == "agentChat") {
						if (isOpenMain) { //打开了首页消息右侧侧边栏

							var isOpenSon = null;
							$.each($(".layui-layer-iframe"), function(index, value) {
								if ($(value).find('iframe').attr("src").indexOf('send-person.jsp') >
									0) {
									console.log($(".layui-layer-iframe")[index])
									isOpenSon = true;
								}
							})
							// console.log(2, isOpenSon)
							if (isOpenSon) { //打开聊天窗口
								// console.log(2.1)
								$("iframe[src^='/cc-base/pages/tpl/send-person.jsp']")[0].contentWindow
									.receiveInfo(JSON.stringify(messageData));
							} else {
								// console.log(2.2)
								// sessionStorage.setItem('handleMsg', 0)
								// $($("iframe[src='/cc-base/pages/tpl/tpl-message.jsp']")[0]).contents().find('#test')[0].click(0)
								// $("iframe[src='/cc-base/pages/tpl/tpl-message.jsp']")[0].contentWindow.updateRecord(JSON.stringify(messageData))
								$("iframe[src^='/cc-base/pages/tpl/tpl-message.jsp']")[0].contentWindow
									.insideMessage();
							}

						} else {
							noticeTip("block");
						}


					} else if (messageData.msgType == "sendChat") {
						// console.log(3)
						var isOpenSon = null;
						$.each($(".layui-layer-iframe"), function(index, value) {
							if ($(value).find('iframe').attr("src").indexOf('send-person.jsp') > 0) {
								isOpenSon = true;
							}
						})
						if (isOpenSon) { //聊天窗口已打开
							if (messageData.state) $("iframe[src^='/cc-base/pages/tpl/send-person.jsp']")[0]
								.contentWindow.receiveInfo(JSON.stringify(messageData));
							return;
						}
					} else if (messageData.msgType == "withdraw") {
						// console.log(4)
						if (messageData.state) {
							$('#record' + agentChatFun.withdrawId).remove();
						} else {
							layer.msg(messageData.msg);
						}
					} else if (messageData.msgType == "heartbeat") {
						if (preTime > 0) {
							respTime = new Date().getTime() - preTime;
							setWifi(respTime)
						}
					}
				},
				onopen: function(event) {
					// console.log('WebSocket opened'); 
					heart = setInterval(function() {
						agentChatFun.heartbeat();
					}, 10000);
					console.info('建立ws心跳,每10s发送一次')
				},
				onclose: function(event) {
					// console.log('index:::WebSocket closed', event);
					preTime = 0;
					if (heart) {
						clearInterval(heart);
						heart = null;
						console.info('断开ws连接心跳')
					}
					/**
					setTimeout(() => {
						console.error('断开重连...可能是因为多台电脑，重复登陆一个账号导致')
						agentChatFun.connect();
					}, 60000);
					*/
					//console.error('断开重连...可能是因为多台电脑，重复登陆一个账号导致')
					layer.msg("无法连接到通知服务器,暂时无法接收实时通知提醒,可能是账号在其他地方登录、或网络限制，如需重试，稍后刷新界面再试...", {
						icon: 5,
						time: 5000,
						offset: 'rb',
						btn: [getI18nValue('我知道了')],
						yes: function(index) {
							layer.close(index);
						}
					});
					
				},
				onmessage: function(message) {
					// var data = JSON.parse(message.data);
					// console.log(data);
				},
				heartbeat: function() {
					// console.log('heartbeat')
					preTime = new Date().getTime();
					agentChatFun.send('heartbeat', {
						'cmdJson': JSON.stringify({
							msgType: "heartbeat"
						})
					});
				},
			};

			setTimeout(function() {
				agentChatFun.connect();
			}, 3000);


			function updataTql(a) {
				$("iframe[src^='/cc-base/pages/tpl/tpl-message.jsp']")[0].contentWindow.insideMessage();
			}

			function sendInside(event, data, callback) {
				var _data = {
					event: event
				}
				if (event == "sendChat") {
					_data.cmdJson = data;
					_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data);
				} else if (event == "withdraw") {
					_data.cmdJson = data;
					_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
				} else if (event == "readMsg") {
					_data.cmdJson = data;
					_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
				} else {
					_data = $.extend({}, _data, data);
					_data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
				}
				if (agentChatFun.ws.readyState === WebSocket.OPEN) {
					agentChatFun.ws.send(_data);
				}
			}
		</script>
		<script src="/yc-ccbar/voicehelper/js/ccbar-voicehelper.v2.js?v=01"></script>
		<script>
			var agentAssistant = '${agentAssistant}';
			var slotKeys = '${slotKeys}';
			var purposeKey = '${purposeKey}';
			var assistantExpandUrl = '${assistantExpandUrl}';
			if ("" == slotKeys || slotKeys == null) {
				slotKeys =
					"requestLocal,requestTopic,7d62009afd527967e053bc03a8c04c3b,02_FORM1_EX2,02_FORM1_EX7,02_FORM1_EX8,02_FORM1_EX9]";
			}
			if ("" == purposeKey || purposeKey == null) {
				purposeKey = "41966dd1a7b111e981d0005056975975";
			}
			if (agentAssistant && "1" == agentAssistant) {
				$(function() {
					CallControl.voiceHelper && CallControl.voiceHelper.init({
						slotKeys: slotKeys.split(","), //槽位key
						purposeKey: purposeKey, //意图key 
						slotKeysMap: {
							'7d62009afd4f7967e053bc03a8c04c3b': 'userId' //非标准槽位翻译 

						},
						tabs:[{id:'assistantExpandUrl',url:assistantExpandUrl,title:'知识库'}]
					});
					
					/*  setTimeout(function() {
						$('.yc-voicehelper-toggle').click();
						$('.vh-switch').click();
					}, 1500)  */
				});
			}


			function openUrl(URL, title) {

				popup.openTab({
					url: URL,
					title: getI18nValue(title),
					id: "notice-info-list",
					data: {}
				});
			}
		</script>
	</body>


</html>
