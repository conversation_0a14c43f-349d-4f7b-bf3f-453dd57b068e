<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<script type="text/javascript" src="static/js/my_i18n.js?2020071711"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711"></script>

<style>
	.cur-workMode[data-curworkmode='outbound']:before {
		font-family: "_ccbar_icon" !important;
		content: "\e63b";
		font-size: 16px;
	}

	.cur-workMode[data-curworkmode='inbound']:before {
		font-family: "_ccbar_icon" !important;
		content: "\e631";
		font-size: 16px;
	}

	.ccbar-notready-reason {
		position: absolute;
		top: 95%;
		display: none;
		background: #fff;
		min-width: 120px;
		border: 1px solid #ddd
	}

	.ccbar-notready-reason li {
		padding: 5px 10px
	}

	.ccbar-notready-reason-box:not(.disabled):hover .ccbar-notready-reason {
		display: block
	}
</style>
<style>
	#ccbar.ccbar-box {
		line-height: 20px;
		color: #333
	}

	#ccbar.ccbar-box>ul>li,
	#ccbar.ccbar-box .ccbar-btn {
		display: inline-block;
		vertical-align: middle;
	}

	#ccbar.ccbar-box .ccbar-btn.btn-hide-disabled.disabled,
	#ccbar.ccbar-box .needLogonFunc {
		display: none;
	}

	.ccbar-box .ccbar-btn {
		line-height: 24px;
	}

	.ccbr-login-input .mediaSwitch {
		color: #333;
	}

	#ccbar.ccbar-box .needLogonFunc.logoned {
		display: inline-block;
	}

	#ccbar.ccbar-box .login-btn {
		color: #fff;
	}

	.ccbarbtn_close {
		padding: 0 !important
	}

	.ccbar_panel_login {
		color: #333
	}

	.agent-btn-group {
		vertical-align: middle;
	}

	.videoBtns {
		border-radius: 10px;
		padding: 0 15px;
		color: #fff;
		padding: 4px 10px;
		display: inline-block;
		transform: translateY(2px);
		cursor: pointer;
		display: inline-block;
		margin-bottom: 5px;
	}

	#videoTransfer_Father:hover #videoTransfer {
		display: block;
	}

	.ccbar-form-input-group .select2-selection--single {
		height: 30px;
		border-radius: 0;
	}

	#videoBtn:hover #spkf_btn {
		display: block !important;
	}

	#setbusyReason {
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	}
</style>
<!-- ccbar -->
<li style="display: inline-block;vertical-align: middle;">
	<!--话务弹出层 begin -->
	<div id="agentsel2" class="ccbar_panel_login" style="height: 160px;display: none;">
		<a href="javascript:void(0)" onclick="$('#agentsel2').fadeOut()" class="ccbarbtn_close"></a>
		<form class="ccbar-form">
			<div style="min-height: 80px;">
				<div class="ccbar-form-input-group" style="display: table;">
					<span class="input-group-addon" i18n-content="外显">外显</span>
					<select name="ccbar_select_caller2" id="ccbar_select_caller2" class="form-control"></select>
				</div>
				<div class="ccbar-form-input-group" style="display: table;">
					<span class="input-group-addon" i18n-content="技能组">技能组</span>
					<select name="ccbar_select_groups2" id="ccbar_select_groups2" class="form-control"></select>
				</div>
				<div class="ccbar-form-input-group" style="display: table;">
					<span class="input-group-addon" i18n-content="坐席">坐席</span>
					<select name="ccbar_select_groups_agent2" id="ccbar_select_groups_agent2"
						class="form-control"></select>
				</div>

				<div class="ccbar-form-input-group" style="display: table;">
					<span class="input-group-addon" i18n-content="外线">外线</span>
					<input name="ccbar_select_called" id="ccbar_select_called" class="form-control">
					<span onclick="toOutCalled()" class="input-group-addon" i18n-content="发起">发起</span>

				</div>
			</div>
			<div style="text-align:center;padding-top:10px">
				<div style="text-align:center;padding-top:10px;width: auto;display: inline-block;"
					onclick="closeAgentsel2()">
					<button type="button" style="width:120px" id="ccbar_agent_sel2" data-placement="bottom"
						class="ccbar_btn_login" i18n-content="呼叫">呼叫</button>

					<button type="button" style="width:120px" id="ccbar_agent_sel3" data-placement="bottom"
						class="ccbar_btn_login " i18n-content="转移">转移</button>

					<button type="button" style="width:120px" id="ccbar_agent_sel4" data-placement="bottom"
						class="ccbar_btn_login" i18n-content="咨询">咨询</button>
				</div>
			</div>
		</form>
	</div>

	<div id="answercallpanel" class="ccbar_panel_login" style="display: none;height: auto;border-radius: 10px;">
		<!-- <a href="javascript:void(0)" onclick="closeInboundPopup();" class="ccbarbtn_close"></a> -->
		<div class="ccbar-form">
			<!-- <div class="ccbar-form-input-group"><span class="input-group-addon">主叫号码</span>
                  <div class="form-control"><span data-ccbaragentinfo="caller"></span></div>
              </div> -->
			<div id="createSkillGroupName" class="text-center"></div>
			<div class="ccbar-inbound-caller text-center" id="createCauseCustPhone" style="font-size: 20px"></div>
			<div class="ccbar-inbound-area text-center" style="margin-bottom: 10px;">
				<span id="createCauseArea"></span>
				<span id="createCauseName"></span>
			</div>
			<div class="text-center" style="margin-bottom: 20px;">
				<span data-ccbar-text="clock"></span>
			</div>
		</div>
		<div style="text-align:center;margin-top:-15px;margin-bottom: 20px;">
			<button type="button" style="width:100px" id="" data-ccbartype="answercall" data-placement="bottom"
				class="ccbar_btn_login answercall disabled">接听</button>&nbsp;&nbsp;
			<button type="button" style="width:100px" id="" data-ccbartype="clearcall" data-placement="bottom"
				class="ccbar_btn_login clearcall disabled">挂机</button>
		</div>
	</div>
	<script>
		function videoMakeCall() {
			var num = $.trim($('#customCalledInput').val())
			if (!num) {
				tipsmsg('请输入电话号码')
			}
			CallControl.videoMakeCall(num)
		}

		function openAgentSel(type) {
			if (type == 1) {
				$('#ccbar_agent_sel2').hide()
				$('#ccbar_agent_sel3').show()
				$('#ccbar_agent_sel4').hide()

			} else if (type == 2) {
				$('#ccbar_agent_sel3').hide()
				$('#ccbar_agent_sel2').hide()
				$('#ccbar_agent_sel4').show()
			} else {
				$('#ccbar_agent_sel3').hide()
				$('#ccbar_agent_sel2').show()
				$('#ccbar_agent_sel4').hide()
			}



			$("#ccbar_select_agents2").html('');

			CallControl.agentList('', function(result) {
				if (result.state) {
					var groups = result.data.result.groups;
					var agents = result.data.result.agents;
					var groupsHtml = '<option value="">' + '请选择技能组' + '</option>';
					for (var i = 0; i < groups.length; i++) {
						groupsHtml += '<option value="' + groups[i].SKILL_GROUP_ID + '">' + groups[i]
							.SKILL_GROUP_NAME + '</option>'
					}

					$("#ccbar_select_groups2").html(groupsHtml);
					$("#ccbar_select_agents2").html('');
					$("#agentsel2").fadeIn();
				} else {
					tipsMsg(result.data.content);
				}
			});

		}

		$(function() {
			//视频客服 
			$("#spkf").on("mouseenter", function() {
				$('#spkf_btn').show()
			});
			$("#spkf").on("mouseleave", function() {
				$('#spkf_btn').hide()
			});
			//视频外呼 
			$("#videoMakeCall").on("click", function() {
				CallControl.videoMakeCall($("#customCalledInput").val())
			});
			//视频转移 
			$("#ccbar_agent_sel3").on("click", function() {
				CallControl.videoTransfer($("#ccbar_select_groups_agent2").val(), $('#ccbar_select_caller2')
					.val(), '', 1)
			});
			//视频咨询 
			$("#ccbar_agent_sel4").on("click", function() {
				CallControl.videoConsultation($("#ccbar_select_groups_agent2").val(), $(
					'#ccbar_select_caller2').val(), '', 1)
			});
		})

		function toOutCalled() {
			if ($('#ccbar_agent_sel3:visible').length == 1) {
				CallControl.videoTransfer($("#ccbar_select_called").val(), $('#ccbar_select_caller2').val(), '', 3)
			} else if ($('#ccbar_agent_sel4:visible').length == 1) {
				CallControl.videoConsultation($("#ccbar_select_called").val(), $('#ccbar_select_caller2').val(), '', 3)
			}
		}
	</script>
	<!--话务弹出层 end -->
	<div id="ccbar" class="ccbar-box" style="margin-top: 0;vertical-align: middle;">
		<ul>
			<li class="ccbar-skillinfo needLogonFunc ccbarVoiceCtrl" i18n-title="技能组信息"></li>
			<li class="ccbar-call ccbar-department needLogonFunc ccbarVoiceCtrl" i18n-title="外显号码">
				<i class="_ccbar_icon _ccbar_icon-agent3"></i>
				<select name="" id="ccbar_phoneCallerList" class="ccbar-select">
					<option value="">------</option>
				</select>
			</li>
			<li id="ccbar_callinfo" class="ccbar-call ccbar-form needLogonFunc ccbarVoiceCtrl">
				<i class="_ccbar_icon _ccbar_icon-answercall2"></i>
				<input type="text" id="customCalledInput" i18n-placeholder="外呼号码" class="ccbar-input">
				<a onclick="openAgentSel()"
					style="padding: 0 5px;text-decoration: none;cursor: pointer;font-size: 12px; display: inline-block;">|
					<span i18n-content="选择坐席"></span> <b class="caret"></b></a>
			</li>
			<li class="needLogonFunc ccbarVoiceCtrl">
				<a href="javascript:void(0)" id="customMakeCallBtn" i18n-title="外呼" data-ccbartype="makecall"
					class="ccbar-btn btn-hide-disabled btn-makecall disabled">
					<i class="_ccbar_icon _ccbar_icon-answercall2"></i> <span i18n-content="外呼"></span>
				</a>
				<a href="javascript:void(0)" i18n-title="挂机" data-ccbartype="clearcall"
					class="ccbar-btn btn-clearcall disabled">
					<i class="_ccbar_icon _ccbar_icon-clearcall"></i> <span i18n-content="挂机"></span>
				</a>
				<c:if test="${ addressStatus == 'Y' }">
					<a href="javascript:void(0)" id="addressStatus" i18n-title="归属地" class="ccbar-btn btn-agentready">
						<span id="areaName" i18n-content="归属地"></span>
					</a>
				</c:if>
				<a href="javascript:void(0)" i18n-title="咨询" data-ccbartype="consultationcall"
					class="ccbar-btn btn-makecall btn-hide-disabled disabled">
					<i class="_ccbar_icon _ccbar_icon-consultationcall"></i> <span i18n-content="咨询"></span>
				</a>

				<a href="javascript:void(0)" i18n-title="转移" data-ccbartype="sstransfer"
					class="ccbar-btn btn-makecall btn-hide-disabled disabled">
					<i class="_ccbar_icon _ccbar_icon-sstransfer"></i> <span i18n-content="转移"></span>
				</a>



				<a href="javascript:void(0)" id="ivrBtn" i18n-title="满意度" data-ccbartype="sstransfer" data-tag="true"
					class="ccbar-btn btn-makecall btn-hide-disabled disabled">
					<i class="_ccbar_icon _ccbar_icon-sstransfer"></i> <span i18n-content="满意度"></span>
				</a>





				<a href="javascript:void(0)" i18n-title="三方通话" data-ccbartype="conferencecall"
					class="ccbar-btn btn-makecall btn-hide-disabled disabled">
					<i class="_ccbar_icon _ccbar_icon-conferencecall"></i> <span i18n-content="三方"></span>
				</a>

				<a href="javascript:void(0)" i18n-title="保持" data-ccbartype="holdcall"
					class="ccbar-btn btn-clearcall btn-hide-disabled disabled" id="holdCallBtn">
					<i class="_ccbar_icon _ccbar_icon-holdcall"></i> <span i18n-content="保持"></span>
				</a>

				<a href="javascript:void(0)" i18n-title="恢复" data-ccbartype="unholdcall"
					class="ccbar-btn btn-makecall btn-hide-disabled disabled" id="unholdCallBtn">
					<i class="_ccbar_icon _ccbar_icon-unholdcall"></i> <span i18n-content="恢复"></span>
				</a>

				<a href="javascript:void(0)" i18n-title="静音" data-ccbartype="mutecall"
					class="ccbar-btn btn-clearcall btn-hide-disabled disabled" id="unholdCallBtn">
					<i class="_ccbar_icon _ccbar_icon-mutecall"></i> <span i18n-content="静音"></span>
				</a>

				<a href="javascript:void(0)" i18n-title="通讯录" data-ccbartype="phoneDir" id="phoneDirBtn"
					style="display: none;">
					<i class="iconfont-toolbar icon-tongxunlu _ccbar_icon"
						style="font-size: 24px;vertical-align: middle;"></i>
				</a>
			</li>

			<li class="needLogonFunc ccbarVoiceCtrl" id="videoBtn">
				<div style="background-color: #30ABF9;position: relative;transform: translateY(1px);margin-bottom: 0;"
					class="videoBtns" id="spkf">
					视频客服
					<div id="spkf_btn" style="display: none;padding: 5px 10px;border-radius: 5px;position: absolute;background-color: #fff;width: 100px;left: -11px;top: 25px; 
                box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%); 
                -moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2); 
                -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%);">
						<div id="videoMakeCall" style="background-color: #EFB43C;" class="videoBtns btn-hide-disabled"
							data-tag="true" data-ccbartype="makecall">
							视频外呼
						</div>
						<div id="videoTransfer_Father" onclick="openAgentSel(1)" data-tag="true"
							data-ccbartype="sstransfer" style="background-color: #1AA034;position: relative;"
							class="videoBtns btn-hide-disabled">
							视频转移
						</div>
						<div id="videoConsultation" onclick="openAgentSel(2)" data-tag="true"
							data-ccbartype="consultationcall" style="background-color: #DD4F43;"
							class="videoBtns btn-hide-disabled">
							视频咨询
						</div>
					</div>
				</div>
			</li>


			<li id="cur-call-info" class="ccbar-split ccbar-info" style="display: none">
				<label class="ccbar-label"><span i18n-content="主叫"></span>：<span id="call-caller"
						style="width: inherit;min-width: 7em"></span></label>
				<label class="ccbar-label"><span i18n-content="被叫"></span>：<span id="call-called"
						style="width: inherit;min-width: 7em"></span></label>
			</li>

			<c:if test="${ voiceSKillCount > 0 }">
				<li class="ccbar-split ccbar-info">
					<label class="ccbar-label"><span i18n-content="状态"></span>:<span data-agentinfo="curstatus"
							class="curstatus cur-agentstate" style="text-align: center;"
							i18n-content="未签入"></span></label>
					<label class="ccbar-label"><span i18n-content="时长"></span>:<span id=""
							data-ccbar-text="clock">00:00:00</span></label>
				</li>

				<li class="needLogonFunc" style="position: relative;padding-top: 4px">
					<span class="tag" data-ccbar-text1="totalQueueCount" i18n-title="当前排队数" data-toggle="tooltip"
						data-placement="left" onclick="$('#ccbarMonitorInfo').toggle()"
						style="border: 1px solid #3cf;background: #3cf; color: #fff; padding: 3px 7px; border-radius: 4px">
						<font i18n-title="排队" i18n-content="排队"></font>
						<span id="currentCCbarQueueCount"
							style="position:absolute;top: -5px;background: red;right: -5px;line-height: 1;padding: 0 3px;border-radius: 3px;">1</span>
					</span>
					<div id="ccbarMonitorInfo" class="ccbr-login-input"
						style="left: inherit;width: 420px;padding: 10px;right: -25px;">
						<div data-ccbar-info="groupMonitors"></div>
					</div>
				</li>
				<li class="ccbar-split-no">
					<div id="login-input" class="ccbr-login-input">
						<div id="voiceAgent" class="ccbr-login-agenttype" style="margin-bottom: 5px">
							<label for="voiceSwitch" class="mediaSwitch">
								<input type="radio" id="voiceSwitch" checked name="agenttype" value="voiceSwitch" />
								<i class="radio-icon"></i>
								<span i18n-content="语音坐席"></span>
							</label>
							<input id="ccbar-phone-input" i18n-placeholder="话机号码" type="text" class="login-input">

							<select id="ccbar-phone-select" i18n-placeholder="话机号码" type="text"
								class="login-input"></select>
						</div>

						<!-- <div id="multiMediaAgent" class="ccbr-login-agenttype">
			                          <label for="multiMediaSwitch" class="mediaSwitch">
			                              <input type="radio" id="multiMediaSwitch" name="agenttype" value="multiMediaSwitch"/>
			                              <i class="radio-icon"></i>
			                              <span>全媒体坐席</span>
			                          </label>
			                          
			                      </div> -->
						<a href="javascript:;" class="login-btn" i18n-content="签入" id="addIn"></a>
					</div>


					<a href="javascript:void(0)" id="agent" class="ccbar-btn btn-agent">
						<i class="_ccbar_icon _ccbar_icon-online"></i> <span i18n-content="签入"></span>
					</a>

			</c:if>


			<a href="javascript:void(0)" i18n-title="结束监听" data-ccbartype="stopMonitor"
				class="ccbar-btn btn-agentready btn-hide-disabled disabled">
				<span i18n-content="结束监听"></span>
			</a>
			<a href="javascript:void(0)" i18n-title="结束强插" data-ccbartype="stopInvent"
				class="ccbar-btn btn-agentready btn-hide-disabled disabled">
				<span i18n-content="结束强插"></span>
			</a>
			<a href="javascript:void(0)" i18n-title="结束整理" data-ccbartype="workReady"
				class="ccbar-btn btn-agentready btn-hide-disabled disabled">
				<span i18n-content="结束整理"></span>
			</a>

			<a href="javascript:void(0)" i18n-title="结束密语" onclick="CallControl.stopSecretlyTalk()" data-tag="true"
				data-ccbartype="stopSecretlyTalk" class="ccbar-btn btn-agentready btn-hide-disabled disabled">
				<span i18n-content="结束密语"></span>
			</a>


			<div class="agent-btn-group needLogonFunc">
				<a href="javascript:void(0)" i18n-title="坐席置闲" data-ccbartype="agentready"
					class="ccbar-btn btn-agentready disabled">
					<i class="_ccbar_icon _ccbar_icon-agentready"></i> <span i18n-content="置闲"></span>
				</a>

				<div href="javascript:void(0)" data-tag="true" i18n-title="坐席置忙" data-anchor="top-center"
					data-ccbartype="agentnotready"
					class="ccbar-btn btn-agentnotready disabled ccbar-notready-reason-box"
					style="border-radius: 0 10px 10px 0!important;">
					<i class="_ccbar_icon _ccbar_icon-agentnotready"></i> <span i18n-content="置忙"></span> <i
						class="layui-icon layui-icon-triangle-d"></i>

					<ul class="dropdown-menu-nav ccbar-notready-reason" id="ccbarNotReadyBtns" onclick="getBusyApplication()">
						<li class="busyLi" id="busytype1">
							<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="1"
								style="padding:10px 15px;color: #333"><span
									style="display: inline-block;width: 30px;"><i
										class="icon _ccbar_icon _ccbar_icon-xiuxi"></i></span> <span
									i18n-content="小休"></span></a>
						</li>
						<li class="busyLi" id="busytype2">
							<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="2"
								style="padding:10px 15px;color: #333"><span
									style="display: inline-block;width: 30px;"><i
										class="icon _ccbar_icon _ccbar_icon-huiyi"></i></span> <span
									i18n-content="会议"></span></a>
						</li>
						<li class="busyLi" id="busytype3">
							<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="3"
								style="padding:10px 15px;color: #333"><span
									style="display: inline-block;width: 30px;"><i
										class="icon _ccbar_icon _ccbar_icon-peixun"></i></span> <span
									i18n-content="培训"></span></a>
						</li>
					</ul>
				</div>

			</div>

			<a href="javascript:void(0)" id="agentLogoff" data-ccbartype="logoff"
				class="ccbar-btn btn-hide-disabled btn-agent disabled">
				<i class="_ccbar_icon _ccbar_icon-offline"></i> <span i18n-content="签出"></span>
			</a>


			<span id="workModeSpan" class="needLogonFunc ccbarVoiceCtrl" style="vertical-align: middle;">
				<a href="javascript:toggleWorkMode()" i18n-title="切换工作模式" style="text-decoration: none;"><span
						class="color-white text-xs block"><span data-curworkmode="" class="cur-workMode"></span> <i
							class="_ccbar_icon _ccbar_icon-switch" style="margin-left: -3px"></i></span></a>
			</span>

			<label id="autoAnswerSpan" class="checkbox checkbox-success needLogonFunc checkbox-inline ccbarVoiceCtrl"
				style="vertical-align: middle;">
				<input id="autoanswercall" type="checkbox" name="autoanswercall" style="vertical-align: middle;"> <span
					i18n-content="自动应答"> </span>
			</label>





			<div id="setbusyReason"
				style="position: fixed;left: 50%;top: 30%;transform: translate(-50%,-50%);width: 400px;height: 285px;padding: 10px;z-index: 99999;background-color: #fff;display: none;">
				<p style="padding: 5px 0;position: relative;">
					<span style="color: red;">*</span>
					<span i18n-content="请输入【置忙原因】"></span>
					<span style="position: absolute;right: 0px;top: 5px;font-size: 28px;cursor: pointer;"
						onclick="cannelBuzyReason()">×</span>
				</p>
				<textarea id="buzyReason" oninput="buzyReasonInp()"
					style="width: 100%;height: 200px;resize: none;border: 1px solid #c6c6c6;padding: 10px;"></textarea>
				<span id="maxLengthSpan" style="position: absolute;right: 80px;bottom: 8px;">
					0/100
				</span>
				<div style="text-align: right;margin-top: 3px;">
					<button type="button" onclick="toSetbusyReason()"
						style="background-color: #008CFF;color: #fff;border-radius: 5px;padding: 5px 10px;border: none;cursor: pointer;"
						i18n-content="提交"></button>
				</div>
			</div>
</li>

</ul>
</div>
</li>
<!-- ccbar end -->

<!-- 置忙原因的模板 -->
<script id="ccbarNotReadyBtns-template" type="text/x-jsrender">
	{{for busyTypes}}
	<li class="busyLi" data-busytype="{{:busyType}}" id="busytype{{:busyType}}">
		<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="{{:busyType}}"
			style="padding:10px 15px;color: #333"><span style="display: inline-block;width: 30px;"><i
					class="icon _ccbar_icon _ccbar_icon-xiuxi"></i></span> <span
				i18n-content="{{:busyTypeName}}"></span></a>
	</li>
	{{/for}}
</script>
<script type="text/javascript">
	var maxStrLimit = 100,
		currentBusyType = 1;

	function getBusyApplication(params) {
		// console.log('话务条触发置忙') //  busyType [示忙类型 1:小休,2:会议,3:培训]
		let event = window.event;
		event.stopPropagation();
		// let isShowTips = localStorage.getItem('notReadyAudit') || ''; //配置项：是否启用置忙申请
		currentBusyType = $(event.target).parents('.busyLi').data('busytype') || 1;
		// if (isShowTips == "Y") {
		ajax.remoteCall("/cc-base/servlet/buybusyApply?action=isApply", {
			"buybusyTypeId": currentBusyType,
		}, function(res) {
			console.log('是否需要置忙申请--接口返回', res)
			if (res.allow == "N") {
				//allow=Y的就不需要发通知，允许置忙
				//allow=N的才拦截掉
				console.log('currentBusyType:::', currentBusyType)
				$('#setbusyReason').show()
			} else {
				top.CallControl.agentNotReady(currentBusyType)
			}
		});

		// }
	}

	function buzyReasonInp() {
		let length = $('#buzyReason').val().length;
		// console.log(length)
		$('#maxLengthSpan').html(length + '/' + maxStrLimit)

		if (length > maxStrLimit) {
			$('#maxLengthSpan').css('color', 'red')
			return
		} else {
			$('#maxLengthSpan').css('color', '#000')
		}
	}

	function cannelBuzyReason() {
		$('#buzyReason').val('');
		$('#setbusyReason').hide();
		$('#maxLengthSpan').html(0 + '/' + maxStrLimit)
	}

	function toSetbusyReason() {
		let msg = $('#buzyReason').val()
		if (msg.trim() == '') {
			layer.msg(getI18nValue('提交内容为空'), {
				icon: 5
			})
			return
		} else {
			if (msg.length > maxStrLimit) {
				layer.msg(getI18nValue('超过字数限制'), {
					icon: 5
				})
				return
			}
			console.log('提交时候的currentBusyType', currentBusyType)
			ajax.remoteCall("/cc-base/servlet/buybusyApply?action=Apply", {
				"buybusyTypeId": currentBusyType,
				"applyContent": msg
			}, function(res) {
				console.log('置忙原因提交--接口返回', res)
				if (res.state == 1) {
					layer.msg(getI18nValue('发起置忙请求成功,请等待审核'), {
						icon: 1
					})
					$('#buzyReason').val('')
					$('#maxLengthSpan').html(0 + '/' + maxStrLimit)
					$('#setbusyReason').hide()
				} else {
					layer.msg(res.msg, {
						icon: 5
					})
				}
			});
		}
	}

	var ajax = {
		remoteCall: function(url, postdata, successCallback, option) {
			!!option || (option = {});
			var loading = true;
			if (option.loading != undefined) loading = option.loading;
			var dataStr = JSON.stringify(postdata);
			if (typeof(filterXSS) != "undefined") {
				dataStr = filterXSS(dataStr);
			}
			$.ajax($.extend({
				url: url,
				type: 'post',
				cache: false,
				dataType: 'json',
				contentType: "application/x-www-form-urlencoded; charset=UTF-8",
				data: {
					data: dataStr
				},
				success: function(result) {
					layer.closeAll('loading');
					successCallback && successCallback(result);
				},
				error: function(jqXHR, textStatus, errorThrown) {
					layer.closeAll('loading');
					console.error("HTTP Status " + jqXHR.status + " - " + textStatus + " - " +
						url + "");
					layer.alert("出现网络故障,请稍后再试!", {
						icon: 7,
						time: 15000
					});
				},
				complete: function() {
					layer.closeAll('loading');
				},
				beforeSend: function(xhr) {
					if (loading) {
						layer.load(1, {
							shade: [0.1, '#fff'],
							time: 1000 * 60 * 3
						})
					}
				},
				headers: {
					'token': 'bWFyc0AyMDE5'
				}
			}, option)).then(function(result) {
				option.then && option.then(result);
			});
		}
	}


	var Thefirst = 1;

	function event1() {
		Thefirst = 1;
	}
	setTimeout(function() {
		$('#ccbar-phone-input').focus(function(params) {
			$(document).keydown(function(event) {
				if (event.keyCode == 13 && Thefirst == 1) {
					$('.login-btn').trigger('click');
					Thefirst = 2;
					$("#agentLogoff").bind("click", event1);
				}
			});
		})
	}, 1000);


	function evtAltering() {
		top.ccbarsdk.registMessageListener('evtAltering', function(data) {
			var areaInfo = data.event.areaInfo;
			if (areaInfo.name) {
				$("#areaName").text(areaInfo.name);
			} else {
				$("#areaName").text('未知');
			}
		});
	}

	function evtConnect() {
		top.ccbarsdk.registMessageListener('evtConnected', function(data) {
			top.currentData = data;
		});
	}

	function evtDisconnect() {
		top.ccbarsdk.registMessageListener('evtDisConnected', function(data) {
			top.currentData = null;
		});
	}

	function monitor() {
		top.ccbarsdk.registMessageListener('monitor', function(event) {
			$('[data-ccbar-text="totalQueueCount"]').text(getI18nValue('排队') + '[' + event.totalQueueCount + ']');
			var html = '';
			for (var i = 0; i < event.groupMonitors.length; i++) {
				var data = event.groupMonitors[i];
				html += getTd(data);
			}
			var thead = '<tr><th data-type="skillGroupName" style="width: 100px">' + getI18nValue('技能组') +
				'</th><th data-type="logonAgentCount">' + getI18nValue('在线') +
				'</th><th data-type="idleAgentCount">' + getI18nValue('闲') +
				'</th><th data-type="busyAgentCount">' + getI18nValue('忙') +
				'</th><th data-type="talkAgentCount">' + getI18nValue('通话') +
				'</th><th data-type="workNotReadyAgentCount">' + getI18nValue('话后') +
				'</th><th data-type="queueCallCount">' + getI18nValue('排队') + '</th><th data-type="aveQueueLen">' +
				getI18nValue('排队均长') + '</th></tr>'
			var table = '<table data-ccbar-info="groupMonitors" border="1px" class="monitor-table"><thead>' +
				thead + '</thead><tbody>' + html + '</tbody></table>'
			$('[data-ccbar-info="groupMonitors"]').html(table);

			function getTd(data) {
				var html = '';
				var list = ['skillGroupName', 'logonAgentCount', 'idleAgentCount', 'busyAgentCount',
					'talkAgentCount', 'workNotReadyAgentCount', 'queueCallCount', 'aveQueueLen'
				];
				for (var j = 0; j < list.length; j++) {
					if (list[j] == 'aveQueueLen') {
						data[list[j]] = parseInt(data[list[j]]) + 's';
					}
					html += '<td data-type="' + list[j] + '">' + data[list[j]] + '</td>';
				}
				return '<tr>' + html + '</tr>';
			}
		});
	}

	setTimeout(function() {
		evtAltering();
		evtConnect();
		evtDisconnect();
		monitor();

		$('#maxLengthSpan').html(0 + '/' + maxStrLimit)
	}, 1000);


	function closeAgentsel2(params) {
		$('#agentsel2').hide()
	}
</script>
