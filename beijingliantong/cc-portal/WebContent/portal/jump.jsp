<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
<title></title>
</EasyTag:override>
<EasyTag:override name="content">
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		var jumpType = "${param.jumpType}";
		var cData = "${param.data}";
		var cDeptCode = "${param.deptCode}";
		/**
		 * 初始化验证核身情况
		 */
		var jump = function() {
			$.post("/cc-portal/workbench?action=getUserInfo",{},function(result){
				if(result.state == 1) {
					var data = result.data;
					localStorage.setItem("userNickName", data.userNickName);
					localStorage.setItem("userEntName", data.entName);
					localStorage.setItem("userAgentPhone", data.userAgentPhone);
					localStorage.setItem("userEntId", data.entId);
					localStorage.setItem("userBusiId", data.busiId);
					localStorage.setItem("busiId", data.busiId);
					localStorage.setItem("userAccount", data.userAccount);
					localStorage.setItem("userName", data.userName);
					localStorage.setItem("busiOrderId", data.busiOrderId);
					localStorage.setItem("userId", data.userId);
					
					if(jumpType == '1') {
						location.href = "/yc-media/pages/index.jsp";
					}
				}
			},'json');
		}

		$(function(){
			jump();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>
