// 以下代码是配置layui扩展模块的目录，每个页面都需要引入
layui.config({
	version:'20190812',
    base: getProjectUrl() + 'portal/module/'
}).extend({
    notice: 'notice/notice',
    dropdownm:'dropdownm/dropdownm',
    elementV:'element'
}).use(['layer', 'admin'], function () {
	 var $ = layui.jquery;
     var layer = layui.layer;
     var admin = layui.admin;

     // 移除loading动画
     setTimeout(function () {
        admin.removeLoading();
     }, window === top ? 300 : 0);

});

// 获取当前项目的根路径，通过获取layui.js全路径截取assets之前的地址
function getProjectUrl() {
    var layuiDir = layui.cache.dir;
    if (!layuiDir) {
        var js = document.scripts, last = js.length - 1, src;
        for (var i = last; i > 0; i--) {
            if (js[i].readyState === 'interactive') {
                src = js[i].src;
                break;
            }
        }
        var jsPath = src || js[last].src;
        layuiDir = jsPath.substring(0, jsPath.lastIndexOf('/') + 1);
    }
    return layuiDir.substring(0, layuiDir.indexOf('portal'));
}


$(function(){
	
	autoLogoutTimeOut();
    
});

var oldTime = new Date().getTime();
var newTime = new Date().getTime();
var outTime = 720 * 60 * 1000; //设置超时时间：720分钟
var timeTask;

/**
 * 门户前端控制超时自动退出处理
 */
function autoLogoutTimeOut(){
	//如果已开启了监听定时任务，则先删除
	clearTask();
	
	//读取配置的超时小时数，当大于0时开启监控，否则不开启
	var autoLogoutTimeOut = localStorage.getItem("autoLogoutTimeOut");
	if(autoLogoutTimeOut>0){
		
		console.log("开启自动退出监听,"+autoLogoutTimeOut+"分钟不操作则自动退出.");
		
		//设置超时时间（单位：毫秒）
		outTime = autoLogoutTimeOut * 60 * 1000;
		
		/* 鼠标移动事件 */
	    $(document).mouseover(function(){
	        oldTime = new Date().getTime(); //鼠标移入重置停留的时间
	        //console.log("更新时间,"+oldTime);
	    });
	    
	    /* 定时器  判断每60秒是否长时间未进行页面操作 */
	    timeTask = window.setInterval(OutTime, 1000*60);
	    
	    console.log("开启自动退出监听,每分钟检测一次");
	    
	}else{
		console.log("不开启自动退出监听,cc-portal配置autoLogoutTimeOut="+autoLogoutTimeOut);
	}
    
}
function OutTime(){
    newTime = new Date().getTime(); //更新未进行操作的当前时间
    if(newTime - oldTime > outTime){ //判断是否超时不操作
        console.log("长时间未操作,自动退出登录");
        /** */
		setTimeout(function(){
			logout();
		},10000);
		
		/**
		layer.alert(outTime+'毫秒无操作,您已被系统强制退出.',{title:'系统提醒',icon:7,closeBtn:false},function(){
			logout();
		});
		*/
    }else{
    	console.log("开始检测是否需要自动退出:未到时间," + (newTime - oldTime)+ "/" + outTime);
    }
}

function clearTask(){
	if(timeTask!=null){
		console.log("停止自动退出监听");
		clearInterval(timeTask);
	}
}


/**
 * 获取系统通知
 * searchUrl：查询通知url
 * answerUrl：应答通知url，应答后的通知不再显示
 * seconds：通知轮询秒数
 */
window.getNotice=function(searchUrl,answerUrl,seconds){
	if(seconds==null || seconds=="" || seconds<=30){
		seconds = 30;
	}
	seconds = seconds*1000;
	
	console.log('启动查询通知结果：'+seconds);
	
	//第一次5s秒执行
	setTimeout(function(){ 
		 searchUserNotice(searchUrl,answerUrl);
    }, 5000);
	
	setInterval(function(){
		searchUserNotice(searchUrl,answerUrl);
	}, seconds);
};

/**
 * 查询系统通知
 * searchUrl：查询通知url
 * answerUrl：应答通知url，应答后的通知不再显示
 */
function searchUserNotice(searchUrl,answerUrl){
	$.ajax({
		url:searchUrl,
		timeout: 2000,
		success:function(res){
			var data=res;
			if(typeof res=='string'){
				data=JSON.parse(data);
			}
			if(data.data){
				//console.log('通知查询结果：',data.data);
				if(data.data.notice){
					//data={"msg":"","data":{respDesc: "查询成功", noticeNum: 2, serialId: "153451030321600060", respCode: "000", notice: "您有如下通知:<br/>紧急公告:1<br/>通知:4<br/>"},"state":1};
					var noticeNum = data.data.noticeNum;
					var notice = data.data.notice;
					var content = notice.split(":");
					notice = notice.replace(content[0],getI18nValue(content[0]));
					if(noticeNum>0){
						if(parent.noticeTip) {
							parent.noticeTip("block");
						}
						try {
							var mp3 = new Audio("/cc-portal/static/plugin/notice.wav");
							mp3.play(); //播放 mp3这个音频对象
						} catch (e) {
							console.error(e);
						}
			    		top.notice.info({
			                title: getI18nValue('消息通知'),
			                message: notice,
			                buttons: [['<button>'+getI18nValue('我知道了')+'</button>', function () {
			                	answerUserNotice(answerUrl);
			                	top.notice.destroy(this);
			                }],
			                ['<button>'+getI18nValue('所有消息')+'</button>',function () {
			                	top.notice.destroy(this);
			        			top.layui.admin.popupRight({
			        				id: 'layer-notice',
			        				type: 2,
			        				content: '/cc-base/pages/tpl/tpl-message.jsp'
			        			});
			                }],
							['<button>'+getI18nValue('快速复制')+'</button>', function() {
								var text = notice
								if (text.substr(-5) == '<br/>') {
									if (noticeNum>1) {
										text = text.substr(0, text.length-5)
									}
									if (noticeNum==1) {
										text = text.split(":")[1]
										text = text.substr(0, text.length-5)
									}
								}
								clipboard(text)
							}]]
			            });
					}
				}
				
				if(data.data.popNotices){
					//top.popup.layerClose();
					var popNotices = data.data.popNotices;
					var len = popNotices.length;
					for(var i =0 ;i<len; i++){
						top.popup.layerShow({type:2,title:popNotices[i].title,time:60000,offset:'r',area:['800px','100%']},popNotices[i].url,{});
					}
				}
				
				//3.1#20210615-1
				if(data.data.respCode=="000"){
					var deptCode = data.data.deptCode;
					if(deptCode=="" || deptCode==undefined){
						layer.alert(getI18nValue("您没有所属部门,使用系统会出现错误,请联系管理员为您设置所属部门!"), {
	                        icon: 5
	                    });
					}
				}
				
				//3.1#20211110-1 如果loginOut为true，则要踢出当前登录用户
				var loginOut = data.data.loginOut;
				if(loginOut){
					layer.alert(data.data.loginOutDesc, {
                        	icon: 5
	                    },
	                    function (index){
	                    	logout();
	                    }
					);
				}
			}
			
			
		},
		error:function(er){
			console.log('通知查询请求失败',er);
		}
	})
	
	/**
	 * 应答系统通知
	 * answerUrl：应答通知url，应答后的通知不再显示
	 */
	function answerUserNotice(url){
		$.ajax({
			url:url,
			timeout: 2000,
			success:function(res){
				
			},
			error:function(er){
				console.log('通知应答请求失败',er);
			}
		})
	}
};

//查看跟进历史
function getFollowList() {
	ajax.remoteCall("/cc-workbench/servlet/sessionHistory?action=sessionList", '', function(result) {
		if (result.data) {
			
			top.layer.open({
				type: 2,
                title: getI18nValue("超时待跟进会话"),
                offset: 'rt', 
                shade: 0,
                //shadeClose:false,
                area: ['600px', '90%'],   	
                //content: '${ctxPath}'+result.data,
                content: '/cc-workbench'+result.data,
                id:'getFollowList'
				});
			
			/* top.popup.layerShow({
                type: 2,
                title: getI18nValue("超时待跟进会话"),
                offset: 'r', 
                shade: 0,
                //shadeClose:false,
                area: ['600px', '80%'],   	
                url: '${ctxPath}'+result.data,
                id:'getFollowList'
            }); */
        }
    });
}

function commandCopy(text) {
	var textArea = document.createElement("textarea");
	textArea.value = text;
	
	textArea.style.top = "0";
	textArea.style.left = "0";
	textArea.style.position = "fixed";
  
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
  
	try {
	  var successful = document.execCommand('copy');
	  var msg = successful ? 'successful' : 'unsuccessful';
	} catch (err) {
	  console.error(err);
	}
  
	document.body.removeChild(textArea);
}

// 复制通知
function clipboard(text) {
	if (!navigator.clipboard) {
		commandCopy(text);
	  return;
	}
	navigator.clipboard.writeText(text).then(function() {
	}, function(err) {
	  console.error(err);
	});
}