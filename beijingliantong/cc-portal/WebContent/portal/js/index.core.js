function pageZoom(){
		  var ratio = 0,
		  screen = window.screen,
		  ua = navigator.userAgent.toLowerCase();
		   
		  if(window.devicePixelRatio !== undefined) {
		      ratio = window.devicePixelRatio;
		  }else if(~ua.indexOf('msie')) {
		    if (screen.deviceXDPI && screen.logicalXDPI) {
		      ratio = screen.deviceXDPI / screen.logicalXDPI;
		    }
		  }else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
		    ratio = window.outerWidth / window.innerWidth;
		  }
		  if(ratio){
		   	 ratio = Math.round(ratio * 100);
		  }
		  return ratio;
};
function getAgentConfig(key){
	if(key==''){
		return '';
	}
	var agentSetting=localStorage.getItem("agentSetting");
	if(agentSetting){
		var json=eval('(' + agentSetting + ')');
		return json[key];
	}
	return '';
}
function screenAuto(){
		var w=screen.width;
		var zoom=pageZoom();
		if(w>1400&&zoom!=100){
			console.log("你的浏览器目前处于缩放"+zoom+"%状态，页面可能会出现错位现象，建议100%大小显示。",{offset:'rb',icon:7,time:6000});
		}else if(w==1366||w==1360){
			document.getElementsByTagName('html')[0].style.zoom=0.9;
		}else if(w==1280){
			document.getElementsByTagName('body')[0].style.zoom=0.8;
		}else if(w<1280&&w>=800){
			document.getElementsByTagName('body')[0].style.zoom=0.7;
		}
}
function getCtxPath(){
	var pathName = window.document.location.pathname;
	var contextPath=pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return contextPath;
}
function logout(){
	if(typeof(CallControl) !='undefined'){
		agentLogout();
	}else{
		top.location.href = '/cc-portal/workbench?action=loginPage';
	}
}
$(function(){
	screenAuto();
});
function zeroPadding(num, digit) {
    var zero = '';
    for(var i = 0; i < digit; i++) {
        zero += '0';
    }
    return (zero + num).slice(-digit);
}

