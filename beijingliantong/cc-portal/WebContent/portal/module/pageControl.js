var pageControl = (function(){
	var pageMap = {};
	return {
		openPage:function(options){
			if (options&&options.callback) options.end = options.callback;
			var defaultOpts = {
				id:'',
				title:'',
				url:'',
				newPage:false,
				reload:false,
				end:null
			}
			var opts = $.extend({},defaultOpts,options);

			if(opts.data){
				var urlData = opts.data;
                var url=String(opts.url);
                if(url.indexOf("?")>=0){
                    url=url+"&"+jQuery.param(urlData);
                }else{
                    url=url+"?"+jQuery.param(urlData);
                }
                opts.url=url;
            }

			//判断是否已存在页面
			var flag = false;
			$('.layui-layout-admin>.layui-body .layui-tab>.layui-tab-title>li').each(function () {
				if (opts.url && $(this).attr('lay-id') === opts.url) {
                    flag = true;
                    return false;
                }
                else if(opts.id && $(this).attr('lay-pid') === opts.id){
                    flag = true;
                	if(opts.url && opts.url!=$(this).attr('lay-pid')){
                    	flag = false;
                		$(this).attr('lay-id',opts.url);
                		$('.layui-tab iframe').filter('#'+opts.id).attr('lay-id',opts.url);
                		$('.layui-tab iframe').filter('#'+opts.id).attr('src',opts.url);
                	}else{
                		opts.url = $(this).attr('lay-id')
                	}
                	if(opts.title){
						var icon = $(this).find('i');
						icon.appendTo('body');
						$(this).text(opts.title);
						$(this).append(icon);
					}
                    return false;
                }
			});
			
			top.layui.index.openTab(opts);
			top.layui.index.openTab(opts);
			//判断是否需要刷新
			if(flag && (opts.reload || layui.data(layui.admin.tableName).tabAutoRefresh)){
				top.layui.admin.refresh();
			}

		},
		closePage:function(options){
			var urlId = $('.layui-tab-title>.layui-this').attr('lay-id');//当前页面的链接
			//没传参数,关闭的是当前页面
			if(typeof(options)=='undefined' || (typeof(options)=='object' && !options.url&& !options.id)){
				options&&options.callback&&options.callback();
				
				top.layui.index.closeTab(urlId);
			}else if(typeof(options)=='object'){
				

				if(options.url){
					if(urlId == options.url){
						options&&options.callback&&options.callback();
					}else{
						setTimeout(function(){options&&options.callback&&options.callback();},0);
					}
					top.layui.index.closeTab(options.url);
				}
				if(options.id){
					$('.layui-tab-title>li[lay-pid='+options.id+']').each(function(){
						var id = $(this).attr('lay-id');
						if(urlId == id){
							options&&options.callback&&options.callback();
						}else{
							setTimeout(function(){options&&options.callback&&options.callback();},0);
						}
						top.layui.index.closeTab(id);
					});
				}
				
			}
			//新增open参数,用于关闭页面后打开新页面
			if(options && options.open){
				pageControl.openPage(options.open)
			}
		},
		jumpPage:function(options){
			if(typeof(options)=='undefined'){
				console.warn('jumpPage false,no options');
			}else if(typeof(options)=='object'){
				if(options.id){
					var data = {
						id:options.id
					}
					data.url = options.url?options.url:$('.layui-tab-title>li[lay-pid='+options.id+']').attr('lay-id');
					top.layui.index.openTab(data);

				}else if(options.url){
					top.layui.index.openTab(options);
				}else{
					console.warn('jumpPage false,no match options');
					return false;
				}
				options.callback&&options.callback();
			}
		},
		flushPage:function(options){
			var url = '';
			if(typeof(options)=='string'){
				url = options;
			}else if(typeof(options)=='object'){
				if(options.url){
					url = options.url;
				}else if(options.id){
					url = pageControl.getUrlById(options.id);
					if(url==''){console.warn('jumpPage false,unfind target'); return false;}
				}
			}
			top.layui.admin.refresh(url);
			options&&options.callback&&options.callback()
		},
		updatePage:function(options){
			if(typeof(options)=='object'){
				//根据options.id判断获取目标页面还是当前页面
				var isTargetHasId = true;
				var target = options.id ? $('.layui-layout-admin>.layui-body .layui-tab>.layui-tab-title>li[lay-pid="'+options.id+'"]'):$('.layui-layout-admin>.layui-body .layui-tab>.layui-tab-title>li.layui-this');
				// var id =target.attr('lay-pid')||target.attr('lay-id');
				if(target.length>0){
					if(options.title){
						var icon = target.find('i');
						icon.appendTo('body');
						target.text(options.title);
						target.append(icon);
					}
					if(options.url){
						target.attr('lay-id',options.url);
						var index = target.index()
						var page = $('.layui-tab-content .layui-tab-item').eq(index).find('iframe');
                		page.attr('lay-id',options.url);
                		page.attr('src',options.url);
					}
					if(options.replaceId){
						var index = target.index();
						var page = $('.layui-tab-content .layui-tab-item').eq(index).find('iframe');
			            page.attr('id',options.replaceId);
			            target.attr('lay-pid',options.replaceId);
					}
				}else{
					if(options.url) this.openPage(options);
				}
			}
		},
		getUrlById:function(id){
			return $('.layui-tab-title>li[lay-pid='+id+']').attr('lay-id')||''
		},
		getPageContent:function(options){
			if(!options || (!options.id&&!options.url)){
				return null;
			}else{
				var windowContent = null,_page;
				if(options.id){
					_page = $('iframe#'+options.id);
				}else if(options.url){
					_page = $('iframe[src="'+options.url+'"]');
				}
				if(_page.length >0){ windowContent = _page[0].contentWindow}
				return windowContent;
			}
		},
		closePageByFilter:function(options){
			var d = {type:'id',start:'',end:''} //type为id或url
			var o = $.extend({},d,options);
			var filterString = '';
			var type = o.type == 'id' ?'lay-pid':'lay-id';
			if(o.start){filterString += '['+type+'^="'+o.start+'"]'}
			if(o.end){filterString += '['+type+'$="'+o.end+'"]'}
			var list = $('.layui-tab>.layui-tab-title>li').filter(filterString);
			list.each(function(e){
				var url = $(this).attr('lay-id');
				layui.index.closeTab(url);
			});
	   }
	}
})();


/*旧接口适配*/
window._openPage =  function(options){pageControl.openPage(options)};
window._createPage =  function(options){
    if($.isPlainObject(options)){
         pageControl.openPage(options);
     }else{
        var numargs = arguments.length; 
         var d = {
            url:options,
            title: arguments[1]
         }
         if(numargs == 3){
            var data = arguments[2];

            if(data!=undefined && $.isPlainObject(data)){
                d.data = data;
            }
        }
         pageControl.openPage(d);
     }
};
window._removePage =  function(options){pageControl.closePage(options)};
window._jumpPage =  function(options){pageControl.jumpPage(options)};