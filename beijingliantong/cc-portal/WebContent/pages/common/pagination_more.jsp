<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<div style="display: inline-block;position: relative;margin-left: 15px;">
		<input type="hidden" name="pageType"  value="2">
		<input type="hidden" class="pageIndexV" name="pageIndex" value="1">
		<c:set var="pageSize" value="${param.pageSize}"></c:set>
		<c:choose>
			<c:when test="${empty param.pageSizes}">
				<c:set var="pageSizes" value="5,10,15,25,50,100,200"></c:set>
				<c:if test="${empty param.pageSize}">
					<c:set var="pageSize" value="15"></c:set>
				</c:if>
			</c:when>
			<c:otherwise>
				<c:set var="pageSizes" value="${param.pageSizes}"></c:set>
			</c:otherwise>
		</c:choose>
 		<select name="pageSize" class="form-control input-sm" style="width: 92px;display: inline-block;height: 28px;padding: 2px 5px">
			<c:forEach items="${fn:split(pageSizes,',')}"  var="val" varStatus="vs"  >
                         <option value="${val}" <c:if test="${pageSize==val}">selected="selected"</c:if>>${val} 条/页</option>
                  </c:forEach>
		</select>
		<button type="button" class="btn btn-default btn-sm pageMore previous" data-page-num="-1">上一页</button>
		<button type="button" class="btn btn-default btn-sm pageMore next" data-page-num="1">下一页</button>
</div>

