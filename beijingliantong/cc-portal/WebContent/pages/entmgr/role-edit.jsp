<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>角色编辑</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="role.record" data-pk="${param.roleId}"  method="post"  autocomplete="off" data-mars-prefix="role.">
				  <input type="hidden" name="role.ROLE_ID" class="form-control input-sm" value="${param.roleId}">
				  <table class="table table-edit table-vzebra mt-10">
	                    <tbody>
		                     <tr>
			                        <td class="required" width="30px" >角色名称</td>
			                        <td><input type="text" name="role.ROLE_NAME" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     
		                     <tr <c:if test="${!empty param.roleId}"> class="hidden"</c:if>>
		                            <td class="required">角色类型</td>
		                            <td>
		                                <select name="role.ROLE_TYPE" data-rules="required" class="form-control input-sm">
		                                    <option value="">请选择</option>
		                                    <option value="1">管理员</option>
		                                    <option value="2">班长</option>
		                                    <option value="3">坐席</option>
		                                    <option value="9">自定义</option>
		                                </select>
		                            </td>
		                     </tr>
		                     
		                     <tr>
			                        <td>角色描述</td>
			                        <td>
			                           <textarea rows="3" class="form-control input-sm" name="role.ROLE_DESC"></textarea>
			                        </td>
		                      </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="RoleEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("RoleEdit");
	
	RoleEdit.roleId='${param.roleId}';
	
	$(function(){
		$("#easyform").render();  
	});
	RoleEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 if(RoleEdit.roleId==''){
				 RoleEdit.insertData(); 
			 }else{
				 RoleEdit.updateData(); 
			 }
		 };
	}
	RoleEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			ajax.remoteCall("${ctxPath}/servlet/role?action=add",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					CCRole.loadData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
	RoleEdit.updateData = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/role?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1},function(){
					CCRole.loadData();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg);
			}
		  }
		);
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>