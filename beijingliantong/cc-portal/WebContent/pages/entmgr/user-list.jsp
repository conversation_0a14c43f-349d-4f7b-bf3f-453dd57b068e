<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>业务员管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
       		<input type="hidden" name="agentConfig" value="allocCust,checkBao,poolAlloc" placeholder="显示企业参数配置key，多参数使用','隔开">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">姓名</span>	
									  <input type="text" name="agentName" class="form-control input-sm" style="width:90px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">工号</span>	
									  <input type="text" name="agentPhone" class="form-control input-sm" style="width:90px">
							   </div>
							    <div class="input-group input-group-sm">
								      <span class="input-group-addon">角色</span>	
									  <select class="form-control input-sm" name="roleId" onchange="CCUser.loadData()" data-mars="role.roleNameDict" style="width:100px">
			                       		 		<option value=""> 请选择 </option>	
			                      	</select>
							   </div>
							    <div class="input-group input-group-sm">
								      <span class="input-group-addon">状态</span>	
									  <select class="form-control input-sm" name="userState" onchange="CCUser.loadData()"  style="width:80px">
			                       		 		<option value="0">启用</option>	
			                       		 		<option value="1">停用</option>	
			                      	</select>
							   </div>
							    <div class="input-group input-group-sm">
								      <span class="input-group-addon">部门</span>	
			                      	  <input type="text" name="agentGroup" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" data-event='enter' class="btn btn-sm btn-default" onclick="CCUser.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
	              		<table id="main">
	              	    </table>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		requreLib.setplugs("layui",function(){
			CCUser.loadData();
	    });
	
		var CCUser={
			loadData:function(data){
				$("#searchForm").initTable({
					 mars:'user.agentList'
					,totalRow:false//显示汇总行
					,limit:15
					,page:true
					,toolbar:false
					,cols: [
						[
				         {width:80, title: '序号',type:'numbers'}
				         ,{minWidth:90,field:'AGENT_NAME', title: '姓名', templet:function(row){
				        	 return '<a  href="javascript:void(0)" onclick="CCUser.editData(\''+row.USER_ID+'\',\''+row.USER_ACCT+'\',\''+row.AGENT_PHONE+'\',\''+row.SKILL_GROUP_NAME+'\')">'+row.AGENT_NAME+'</a>';
				         }}
				         ,{minWidth:90,field:'USER_ACCT', title: '登录帐号'}
				         ,{minWidth:90,field:'AGENT_PHONE', title: '工号'}
				         ,{minWidth:120,field:'GROUP_LIST', title: '部门'}
				         ,{minWidth:120,field:'ROLE_LIST', title: '角色'}
				         ,{minWidth:100,field:'PREFIX_NUM', title: '外显号码'}
				         ,{minWidth:100,field:'MOBILE', title: '手机号码'}
				         ,{width:80,field:'USER_STATE', title: '状态', templet:function(row){
				        	 if(row.CENTER_USER_STATE == 1){
				        		 return '暂停';
				        	 }
				        	 return userStatusFn(row.USER_STATE);
				         }}
				         ,{width:156,field:'LOGIN_TIME', title: '最近登录时间'}
				         ]
					]
				});
			},
			
			editData:function(userId,userAcct,agentPhone,skillId){
			    popup.layerShow({type:1,title:'编辑',offset:'rb',area:['420px','100%']},"${ctxPath}/pages/entmgr/user-edit.jsp",{userId:userId,userAcct:userAcct,skillId:skillId});
			}
		}
		
		var userStatusFn = function(val) {
			if(val==0){
				return "启用";
			}else if(val==1){
				return "<span class='label label-warning'>暂停</span>";
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>