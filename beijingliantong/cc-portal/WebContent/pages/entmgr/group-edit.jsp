<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>部门</title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="skillGroup.record"  method="post" data-pk="${param.skillGroupId}" data-mars-prefix="skillGroup.">
				  <input type="hidden" name="skillGroup.SKILL_GROUP_ID" value="${param.skillGroupId}">
				  <input type="hidden" name="skillGroup.P_GROUP_ID" value="${param.pGroupId }">
				  <input type="hidden" name="pGroupType" value="${param.groupType }">
				  <table class="table table-edit table-vzebra">
	                    <tbody>

		                     <tr>
			                        <td class="required" width="100px">部门名称</td>
			                        <td><input type="text" name="skillGroup.SKILL_GROUP_NAME" autocomplete="off" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     <c:if test="${empty param.skillGroupId}">
		                     <tr>
			                        <td class="required">部门类型</td>
			                        <td>
			                        	<select name="skillGroup.GROUP_TYPE" data-rules="required" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
			                        		<option value="">请选择</option>
			                        	</select>
			                        </td>
		                     </tr>
		                     </c:if>
		                     <c:if test="${!empty param.skillGroupId}">
		                     <tr>
			                        <td class="required">部门类型</td>
			                        <td><input type="text" name="skillGroup.GROUP_TYPE_NAME" readonly="readonly" class="form-control input-sm"></td>
		                     </tr>
		                     </c:if>
		                     
		                     <tr>
			                        <td>外显号码</td>
			                        <td>
			                            <select class="form-control input-sm" id="prefixSelect2" name="skillGroup.PREFIX_NUM" data-mars="common.prefixNumDic">
				                        	<option value="">请选择</option>	
				                        </select>
				                    </td>
		                     </tr>
		                     <tr>
			                        <td>外显号码组</td>
			                        <td>
			                            <select name="skillGroup.PREFIX_GROUP_ID" class="form-control input-sm" data-mars="common.prefixGroupDic">
			                                <option value="">请选择</option>
			                            </select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="required">优先排序</td>
			                        <td><input type="number" name="skillGroup.IDX_ORDER"  data-rules="required|digits" class="form-control input-sm" value="99"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="SkillGroupEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("SkillGroupEdit");
	
	SkillGroupEdit.skillGroupId='${param.skillGroupId}';
	
	$(function(){
		$("#easyform").render({success:function(result){
			requreLib.setplugs('select2',function(){
				 $("#prefixSelect2").select2({theme: "bootstrap",openOnEnter:false});
			});
		}});
	});
	
	SkillGroupEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 var groupType = $("select[name='skillGroup.GROUP_TYPE']").find("option:selected").text();
			 if(groupType.indexOf('(话务权限)')>0){
				 var groupId = $("select[name='skillGroup.PREFIX_GROUP_ID'] option:selected").val();
				 var num = $("select[name='skillGroup.PREFIX_NUM'] option:selected").val();
				 if(groupId == num){
					 layer.msg("来显号码或外呼号码组不能为空!",{icon: 5,offset:'20px'});
					 return;
				 }
			 }
			 if(SkillGroupEdit.skillGroupId==''){
				 SkillGroupEdit.insertData(); 
			 }else{
				 SkillGroupEdit.updateData(); 
			 }
		 };
	}
	SkillGroupEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=add",data,function(result) { 
				if(result.state == 1){
					SkillGroup.loadData();
					popup.layerClose("#easyform");
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
	SkillGroupEdit.updateData = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					SkillGroup.loadData();
					popup.layerClose("#easyform");
				});
			}else{
			      layer.alert(result.msg);
			}
		  }
		);
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>