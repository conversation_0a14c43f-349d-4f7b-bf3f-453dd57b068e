<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>组织架构</title>
	<style type="text/css">
		 div#rMenu {position:absolute; visibility:hidden; top:0;text-align: left;padding: 2px;}
		.dropdown-menu>li>a{font-size: 13px;}
	    .ibox-header{margin-bottom:10px}
	    .ibox-header-content{background-color: #ffffff;padding: 10px 20px;border-color: #e7eaec;border-style: solid solid none;border-width: 1px 0;}
	    .ibox-header-content h4{font-weight:600;font-size:16px;margin:8px 6px 0px}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" id="searchForm" name="searchForm" class="form-inline">
			<input type="hidden" name="pk" id="skillId">
			<input type="hidden" name="groupType" id="groupType">
			<select class="hidden" id="prefixGroup" data-mars="prefixGroup.prefixGroupDict"></select>
			<select class="hidden" name="roleId" data-mars="role.roleNameDict"></select>
			<div class="row">
					<div  style="background-color: #fff;margin-left: 15px;width: 18%;float: left;height: 100%">
						<div style="border-bottom: 1px solid #eee;height: 52px;line-height: 52px;padding: 0px 15px"> 组织架构<span class="f-12 text-info pull-right">右键菜单操作</span></div>
						<div class="ztree" data-mars="skillGroup.groupTree" id="ztree" data-setting="{callback: {onClick: zTreeOnClick,onRightClick: onRightClick}}" style="max-height: 530px;overflow:auto; padding: 15px;max-height: 520px;"></div>
					</div>
					<div style="height: 100%;width: 78%;float: left;margin-left: 15px;">
						<div class="ibox">
							<div class="ibox-header" data-mars="skillGroup.record" id="groupInfo">
			             		<div class="ibox-title" style="border-bottom: none;">
									 <div class="form-group">
				             		     <h5 id="SKILL_GROUP_NAME"> 团队信息</h5>
										 <div class="input-group input-group-sm pull-right">
										     <button type="button" class="btn btn-sm btn-info btn-outline" onclick="SkillGroup.editData()" id="editDataBtn">编辑 </button>
										 </div>
									 </div>
			             	    </div>
		             	        <div class="ibox-header-content">
		             	            <div class="row">
		             	                <div class="col-xs-2">
		             	                    <small>编号</small>
		             	                    <h4 class="f-16" id="SKILL_GROUP_ID">0</h4>
		             	                </div>
		             	                <div class="col-xs-2">
		             	                    <small>部门类型</small>
		             	                    <h4 class="f-16" id="GROUP_TYPE_NAME"></h4>
		             	                </div>
		             	                <div class="col-xs-2">
		             	                    <small>成员人数</small>
		             	                    <h4 id="AGENT_COUNT">0</h4>
		             	                </div>
		             	                <div class="col-xs-2" id="prefixNum">
		             	                    <small>外呼号码</small>
		             	                    <h4 id="PREFIX_NUM">0</h4>
		             	                 </div>
		             	                 <div class="col-xs-2" id="prefixGroup">
		             	                    <small>外呼号码组</small>
		             	                    <h4 id="PREFIX_GROUP_ID" data-fn="getPrefixGroup">0</h4>
		             	                 </div>
		             	                 <div class="col-xs-2" id="prefixGroup">
		             	                    <small>话务标志</small>
		             	                    <h4 id="CALL_FLAG" data-fn="getCallFlag"></h4>
		             	                 </div>
		             	            </div>
		             	        </div>
		             	    </div>
		             	    <div class="ibox-header">
			             		<div class="ibox-title">
									 <div class="form-group">
				             		      <h5> 成员列表 </h5>
			             		          <div class="input-group input-group-sm">
											      <span class="input-group-addon">姓名</span>	
												  <input type="text" name="agentName" class="form-control input-sm" style="width:100px">
										   </div>
										   <div class="input-group input-group-sm">
												<button type="button" class="btn btn-sm btn-default" onclick="GroupUser.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
										   </div>
										   <div class="input-group input-group-sm pull-right">
										     	<button type="button" class="btn btn-sm btn-info btn-outline" onclick="SkillGroup.addUser()">新增成员 </button>
										   </div>
									  </div>
			             	    </div>
			              	    <div class="ibox-content">
				              	    <table id="main">
		              	    		</table>
								 </div>
		              	    </div>
						</div>
					</div>
		</div>
	
	</form>
	<div id="rMenu">
		<ul class="dropdown-menu" role="menu">
			<li class="add_data"><a href="javascript:void(0)" onclick="SkillGroup.addData();"><span class="glyphicon glyphicon-plus"></span><span class="item-name" id="addDataLi"> 添加</span></a></li>
			<li class="edit_data"><a href="javascript:void(0)" onclick="SkillGroup.editData();"><span class="glyphicon glyphicon-edit"></span><span class="item-name"id="editDataLi"> 修改</span></a></li>
			<li class="del_data"><a href="javascript:void(0)" onclick="SkillGroup.delData();"><span class="glyphicon glyphicon-trash"></span><span class="item-name"id="delDataLi"> 删除</span></a></li>
			<li class="add_user"><a href="javascript:void(0)" onclick="SkillGroup.addUser();"><span class="glyphicon glyphicon-plus"></span><span class="item-name"> 新增成员</span></a></li>
		</ul>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		$("#prefixGroup").hide();
		$("#groupInfo").hide()
		jQuery.namespace("SkillGroup");
		var zTree, rMenu,skillName;
		var height=$(window).height();
		requreLib.setplugs('slimscroll,ztree,layui',function(){
			$('#ztree').slimScroll({  
	                height: height-100,
	                color: '#ddd'
	        });
			SkillGroup.loadData();
		});
		
		SkillGroup.loadData = function(){
		  	$("#searchForm").render();
			$("#searchForm").initTable({
				 mars:'skillGroup.groupUserList'
				,totalRow:false//显示汇总行
				,limit:15
				,page:true
				,toolbar:false
				,cols: [
					[
			         {width:90, title: '序号',type:'numbers'}
			         ,{minWidth:120,field:'AGENT_NAME', title: '姓名', templet:function(row){
			        	 return '<a  href="javascript:void(0)" onclick="SkillGroup.editUser(\''+row.USER_ID+'\',\''+row.USER_ACCT+'\',\''+row.AGENT_PHONE+'\',\''+row.SKILL_GROUP_NAME+'\')">'+row.AGENT_NAME+'</a>';
			         }}
			         ,{minWidth:120,field:'USER_ACCT', title: '登录帐号'}
			         ,{field:'AGENT_PHONE', title: '工号'}
			         ,{minWidth:120,field:'ROLE_LIST', title: '角色'}
			         ,{minWidth:90,field:'PREFIX_NUM', title: '外显号码'}
			         ,{minWidth:90,field:'MOBILE', title: '手机号码'}
			         ,{width:80,field:'USER_STATE', title: '状态', templet:function(row){
			        	 if(row.CENTER_USER_STATE == 1){
			        		 return '暂停';
			        	 }
			        	 return userStatusFn(row.USER_STATE);
			         }}
			         ,{minWidth:160,field:'SKILL_GROUP_NAME', title: '所属'}
			         ,{width:80,field:'ROLE_ID', title: '操作', templet:function(row){
			        	 return '<a href="javascript:void(0)" onclick="SkillGroup.delUser(\''+row.USER_ID+'\',\''+row.AGENT_PHONE+'\')">移除</a>';
			         }}
			         ]
				]
			});
		}
		
		function onRightClick(event, treeId, treeNode) {
			zTree = $.fn.zTree.getZTreeObj("ztree");
			rMenu = $("#rMenu");
			zTree.selectNode(treeNode);
			var nodes = zTree.getSelectedNodes();
			if(nodes&&nodes.length>0){
				$(".add_data").removeClass("hidden");
				$(".edit_data").removeClass("hidden");
				$(".del_data").removeClass("hidden");
				$(".add_user").removeClass("hidden");
				
				if(nodes[0].id == 0){
					$(".edit_data").addClass("hidden");
					$(".del_data").addClass("hidden");
					$(".add_user").addClass("hidden");
				}
				
				$("#skillId").val(nodes[0].id);
				$("#groupType").val(nodes[0].groupType);
				skillName = nodes[0].name;
		        showRMenu("node", event.clientX, event.clientY);
			}
		}
		function showRMenu(type, x, y) {
			$("#rMenu ul").show();
			if (type=="root") {
				$("#m_del").hide();
				$("#m_check").hide();
				$("#m_unCheck").hide();
			} else {
				$("#m_del").show();
				$("#m_check").show();
				$("#m_unCheck").show();
			}
			rMenu.css({"top":y+"px", "left":x+"px", "visibility":"visible"});
			$("body").bind("mousedown", onBodyMouseDown);
		}
		function hideRMenu() {
			if (rMenu) rMenu.css({"visibility": "hidden"});
			$("body").unbind("mousedown", onBodyMouseDown);
		}
		function onBodyMouseDown(event){
			if (!(event.target.id == "rMenu" || $(event.target).parents("#rMenu").length>0)) {
				rMenu.css({"visibility" : "hidden"});
			}
		}
		
		function zTreeOnClick(event, treeId, treeNode){
			if(treeNode.id == 0){
				$("#groupInfo").hide();
			}else{
				$("#groupInfo").show();
			}
			$("#skillId").val(treeNode.id);
			$("#groupType").val(treeNode.groupType);
			SkillGroup.loadData();
		}
		
		function getPrefixGroup(val){
			if(val&&val != undefined&&val!=''){
				var count=$("#prefixGroup option").length;
			    for(var i=0;i<count;i++) {           
			    	var obj = $("#prefixGroup").get(0).options[i];
			    	if(obj.value == val) {
			    		return obj.text;
			        }
		        }
			}else{
				return '';
			}
		}
		
		function getCallFlag(val){
			if(val == 1){
				return '话务权限';
			}
			return '无话务权限';
		}
	  	
	  	SkillGroup.addData = function(){
		  	hideRMenu();
		  	var groupType = $("#groupType").val();
			popup.layerShow({type:1,title:'新增',offset:'20px',area:['400px','400px']},"${ctxPath}/pages/entmgr/group-edit.jsp",{groupType:groupType,pGroupId:$("#skillId").val()});
	  	}
	  	
	  	SkillGroup.editData = function(){
		  	hideRMenu();
		  	var groupType = $("#groupType").val();
		    popup.layerShow({type:1,title:'编辑',offset:'20px',area:['400px','400px']},"${ctxPath}/pages/entmgr/group-edit.jsp",{groupType:groupType,skillGroupId:$("#skillId").val()});
	   	}
	  	
	  	SkillGroup.delData = function(resId,resName){
		   	$("#rMenu ul").show();
			var id =$("#skillId").val();
			if(id == "0" || id == null){
				layer.msg("无法删除根节点！");
				return ;
			}
			layer.confirm('是否确定删除？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=delete", {pk:id}, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	$("#skillId").val('0');
					    	$("#groupType").val('1');
					    	SkillGroup.loadData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
						SkillGroup.loadData();
					}
	  			});
			});
		}
	  	
	  	SkillGroup.addUser = function(){
		  	hideRMenu();
		  	var skillId =$("#skillId").val();
			if(skillId == "0" || skillId==null ||skillId==""){
				layer.msg("请选择一个组织架构级别！");
				return ;
			}
			var title = '';
			var groupType = $("#groupType").val();
			popup.layerShow({type:2,title:title,offset:'20px',area:['800px','700px']},"${ctxPath}/pages/entmgr/group-user-selector.jsp",{skillGroupId:skillId,groupName:skillName,groupType:groupType});
	  	}
	  
	  	SkillGroup.delUser=function(userId,userName,flag){
			layer.confirm('确定移除吗?',{icon: 3, title:'提示'}, function(){
				var data = {userId:userId,skillGroupId:$("#skillId").val(),flag:flag,groupName:skillName};
				ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=delGroupUser",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
							SkillGroup.loadData();
					    });
					}else if(result.state == 2){
						layer.confirm(result.msg,function(index){
							layer.close(index);
							SkillGroup.delUser(userId,userName,1);
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});   
	  	}
	  	
	  	SkillGroup.editUser = function(userId,userAcct,agentPhone,skillId){
		    popup.layerShow({type:1,title:'编辑',offset:'rb',area:['480px','100%']},"${ctxPath}/pages/entmgr/user-edit.jsp",{userId:userId,userAcct:userAcct,skillId:skillId,groupShow:1});
		}
	  
	  	var GroupUser = {
	  			loadData:function(){
	  				SkillGroup.loadData();
	  			}
	  	}
	  	var CCUser = {loadData:function(v){SkillGroup.loadData();}}

		var userStatusFn = function(val) {
			if(val==0){
				return "启用";
			}else if(val==1){
				return "<span class='label label-warning'>暂停</span>";
			}
		}
	</script>


</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>