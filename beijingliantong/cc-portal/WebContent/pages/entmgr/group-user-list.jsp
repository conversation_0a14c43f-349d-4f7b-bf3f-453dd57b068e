<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>业务员管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post"  class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off">
                <input type="hidden" value="${param.skillGroupId }" name="skillGroupId">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 班组业务员 </h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">业务员工号</span>	
									  <input type="text" name="condition" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group input-group-sm hidden">
								    <span class="input-group-addon">角色</span>	
									<select class="form-control input-sm" name="roleId" data-mars="role.roleNameDict">
			                      	</select>
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="GroupUser.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
							   <div class="input-group input-group-sm pull-right btn-group">
							       <button type="button" class="btn btn-sm btn-success" onclick="GroupUser.assignAgent()">+添加业务员 </button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="skillGroup.groupUserList">
                             <thead>
	                         	 <tr>
								      <th class="text-c" width="40px;"><label class="checkbox checkbox-success"><input type="checkbox" id="ids"/><span></span></label></th>
								      <th class="text-c">序号</th>
								      <th class="text-c">姓名</th>
								      <th class="text-c">登录账号</th>
								      <th class="text-c">业务员工号</th>
								      <th class="text-c">角色</th>
								      <th class="text-c">状态</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><label class="checkbox checkbox-success"><input type="checkbox" name="userIds" value="{{:USER_ID}}"/><span></span></label></td>
									   	    <td>{{:#index+1}}</td>
									   	    <td>{{:AGENT_NAME}}</td>
											<td>{{:USER_ACCT}}</td>
											<td>{{:AGENT_PHONE}}</td>                                         
											<td>{{getText:ROLE_ID 'roleId'}}</td>                                           
											<td>{{if CENTER_USER_STATE == 1}}暂停{{else}}{{userStatusFn:USER_STATE}}{{/if}}</td>                                         
											<td><a href="javascript:void(0)" onclick="GroupUser.delUser('{{:USER_ID}}','{{:AGENT_PHONE}}')">移除</a></td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("GroupUser");

		$(function(){
			$("#ids").click(function(){
				$("input[name='userIds']").prop("checked", $(this).is(':checked'));
			});
			$("#searchForm").render({complete:function(){
				//GroupUser.assignAgent();
			}});
		});
		GroupUser.loadData=function(){
			$("#searchForm").searchData();
		}
		GroupUser.skillGroupId = '${param.skillGroupId}';
		GroupUser.bussOrderId = '${param.bussOrderId}';
		GroupUser.groupName = '${param.groupName}';
		
		$.views.converters("userStatusFn", function(val) {
			if(val==0){
				return "启用中";
			}else if(val==1){
				return "<span class='label label-warning'>已暂停</span>";
			}
		});
		GroupUser.assignAgent=function(){
		    popup.layerShow({type:1,title:'分配业务员',offset:'20px',area:['550px','450px']},"${ctxPath}/pages/entmgr/group-user-selector.jsp",{skillGroupId:GroupUser.skillGroupId,bussOrderId:GroupUser.bussOrderId,groupName:GroupUser.groupName});
		}
		GroupUser.delUser = function(userId,userName,flag){
			layer.confirm("是否确定移除?",function(){
				var data = {userId:userId,skillGroupId:GroupUser.skillGroupId,flag:flag,groupName:GroupUser.groupName};
				ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=delGroupUser",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
							GroupUser.loadData();
					    });
					}else if(result.state == 2){
						layer.confirm(result.msg,function(index){
							layer.close(index);
							GroupUser.delUser(userId,userName,1);
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		GroupUser.batchDel=function(){
			var data=$.extend(form.getJSONObject("#searchForm"),{skillGroupId:GroupUser.skillGroupId,groupName:GroupUser.groupName});
	  		ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=batchDelUser", data, function(result) {
	  			if(result.state == 1){
				    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
				    	GroupUser.loadData();
				    });
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>