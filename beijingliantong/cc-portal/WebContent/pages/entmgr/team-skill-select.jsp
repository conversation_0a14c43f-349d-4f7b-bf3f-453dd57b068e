<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择班组</title>
	<style>
		#dataList2 tr{cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="skillGroupSelectForm" style="margin-bottom: 65px">
       		<input type="hidden" name="userId" value="${param.userId }">
             	<div class="ibox">
	              	<div class="ibox-content" style="padding: 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="6" data-container="dataList2" data-template="list-template2" data-auto-fill="5" data-mars="skillGroup.teamList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">选择</th>
								      <th>班组名称</th>
								      <th>坐席数</th>
								      <th>创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList2">
                             </tbody>
		                 </table>
                        	 <script id="list-template2" type="text/x-jsrender">
								   {{for  list}}
										<tr class="{{:SKILL_GROUP_ID}}">
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" {{if USER_GROUP_ID}}checked{{/if}} name="skillIds" value="{{:SKILL_GROUP_ID}}_{{:SKILL_GROUP_NAME}}"/><span></span></label></td>
											<td>{{:SKILL_GROUP_NAME}}</td>                                         
											<td>{{:AGENT_COUNT}}人</td>                                         
											<td>{{cutText:CREATE_TIME 12 ''}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
	              	</div> 
	              	<div class="layer-foot text-c" style="position: fixed;">
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="SkillGroupSelect.saveData()">确定</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("SkillGroupSelect");
		
		SkillGroupSelect.userId = "${param.userId}";
		
		$(function(){
			$("#skillGroupSelectForm").render({success:function(result){
				$("#dataList2 tr").on('click',function(event){
					if(event.target.type == 'radio'|| event.target.type == 'checkbox') { event.stopPropagation(); return;}
					var val=$(this).find("input").prop("checked");
					$(this).find("input").prop("checked",!val);
				});
			}});
		});
		
		SkillGroupSelect.saveData=function(){
			var data = form.getJSONObject("#skillGroupSelectForm");
			ajax.remoteCall("${ctxPath}/servlet/user?action=updateTeam",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
						popup.layerClose();
						CCUser.loadData({jumpPage:false});
					});
				}else{
					layer.alert(result.msg);
				}
			  }
			);
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>