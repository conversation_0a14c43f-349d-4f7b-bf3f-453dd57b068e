<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>角色用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
       			<input type="hidden" name="roleId" value="${param.roleId }">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      	<h5> 角色用户</h5>
             		          	<div class="input-group input-group-sm">
								      	<span class="input-group-addon">姓名</span>	
									  	<input type="text" name="agentName" autocomplete="off" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width:100px">
							   	</div>
							   	<div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="RoleUser.loadData()">
										<span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
							   	<div class="input-group input-group-sm pull-right btn-group">
							       		<button type="button" class="btn btn-sm btn-success btn-outline" onclick="RoleUser.selectUser()">+新增角色</button>
							   	</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
	              		<table id="main">
	              	    </table>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("RoleUser");
		
		
		
		requreLib.setplugs("layui",function(){
			RoleUser.loadData();
        });
		
		RoleUser.loadData = function(){
			$("#searchForm").initTable({
				 mars:'role.roleUserList'
				,totalRow:false//显示汇总行
				,limit:15
				,page:true
				,toolbar:false
				,cols: [
					[
			         {width:80, title: '序号', type:'numbers'}
			         ,{field:'AGENT_NAME', title: '姓名'}
			         ,{field:'AGENT_PHONE', title: '工号'}
			         ,{field:'GROUP_LIST', title: '部门'}
			         ,{width:90,field:'USER_ID', title: '操作',templet:function(row){
			        	 return '<a href="javascript:void(0)" onclick="RoleUser.delUser(\''+row.USER_ID+'\',\''+row.ROLE_ID+'\')">移除</a>';
			         }}
			         ]
				]
			});
		}
		
		RoleUser.selectUser = function(){
			popup.layerShow({type:2,title:'选择用户',offset:'20px',area:['800px','700px']},"${ctxPath}/pages/entmgr/role-user-selector.jsp",{roleId:'${param.roleId}'});
		}
		
		RoleUser.delUser = function(userId,roleId){
			var data = {roleId:roleId, userId:userId};
			ajax.remoteCall("${ctxPath}/servlet/role?action=deleteUser",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
						RoleUser.loadData();
					});
				}else{
					layer.alert(result.msg);
				}
			});
		}
		
		RoleUser.delUsers = function(){
			var data = form.getJSONObject("#searchForm");
			ajax.remoteCall("${ctxPath}/servlet/role?action=deleteUser",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
						RoleUser.loadData();
					});
				}else{
					layer.alert(result.msg);
				}
			});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>