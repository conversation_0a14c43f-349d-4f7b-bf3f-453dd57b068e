<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>配置信息</title>
	<style type="text/css">
		.container-fluid {
			height: 100%;
		}

		.labelListDiv {
			padding: 15px 10px;
			color: #76838F;
    		font-size: 15px;
			/* border: 1px solid; */
		    /* border-color: #8ba99f33; */
		    border-radius: 3px;
		    margin: 2px;
		}

		a:link {
			color: #00adff;
		}

		#leftBox a:link {
			color: inherit !important;
			text-decoration: none !important;
		}

		.labelListDiv:hover {
			color: #fff !important;
			background-color: rgba(27, 168, 237, 0.6);
			cursor: pointer;
		}

		.labelListDivClick {
			color: #fff !important;
			background-color: #1BA8ED;
		}

		#tagManage {
			position: absolute;
			bottom: 30px;
			left: 50%;
			transform: translateX(-50%);
		}

		td[data-field="ID"] .layui-table-cell {
			overflow: inherit;
		}

		.layui-table-view .layui-table {
			position: relative;
			width: 100%;
			margin: 0px;
		}

		.layui-table-cell,
		.layui-table-tool-panel li {
			overflow: hidden;
		}

		.layui-table-header .layui-table-cell,
		.layui-table-tool-panel li {
			overflow: inherit !important;
		}
	</style>
	<style>
	    /* .layui-layer-content{
	        overflow-x: hidden !important;
	    } */
	    
	    .container-fluid {
		    padding-right: 0px;
		    padding-left: 0px;
		    margin-right: 0px;
		    margin-left: 0px;
		    padding: 0px;
		}
	    
	    .layui-tab-item{
			height: 100% !important;
		}
	</style>
	 <style type="text/css">
        html{           /* 解决页面会出现双滚动条问题。overflow: hidden; 溢出隐藏，给一个元素中设置overflow: hidden，那么该元素的内容若超出了给定的宽度和高度属性，那么超出的部分将会被隐藏，不占位。 */
            overflow: hidden;
        }
        body{           /* 解决页面上下左右会出现空白问题。让外框的外边距和内边距都为0 */
            margin: 0;
            padding: 0;
        }
        #iframepage{
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
<div style="background:#FFFFFF; color:#000000 ;height: 100%;width: 100%;border:0px;margin:0px;padding:1px" id="box">
	<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief" style="height: 100% !important;">
	  <ul class="layui-tab-title" >
	  </ul>
	  <div class="layui-tab-content" style="height: calc(100% - 50px);width: 100%;margin:0px;padding:0px">
	  </div>
	</div>
</div>

</EasyTag:override>

<EasyTag:override name="script">

<script>
	var urlFrame = {};
    var menuList = []
	//注意：选项卡 依赖 element 模块，否则无法进行功能性操作
	var element ;

	$(function(){
		layui.use('element', function () {
			var $ = layui.jquery,
			element = layui.element;
			element.on('tab(docDemoTabBrief)', function (data) {
				var li = data.elem.context;
			});
			initMenu();
		});
	});
	
	function initMenu(){
		ajax.remoteCall("/cc-portal/workbench?query=menu",{resId:'${param.resId }'},function(result) { 
			getMenuHtml(result);
	    	//initIframe();
		});
	}

	$('.layui-tab-title').on('click', 'li', function(e) {
		if (e.target == $('.layui-this .layui-icon-refresh')[0]) {
			var id = $(e.currentTarget).attr('lay-id')
			document.getElementById(id).contentWindow.location.reload()
			return
		}
		$('.layui-tab-title li .layui-icon-refresh').hide()
		$('.layui-icon-refresh', e.target).show()
	})
	
	//加//载导航
	function getMenuHtml(data){
	   	var firstId = "";
        menuList = data.data
	    if(data.data && data.data.length>0){
	    	for(var i=0;i<data.data.length;i++){
	    		var d = data.data[i];
	    		var id = d.id;
	    		var title = d.title;
	    		var url = d.url;
	    		if(firstId==""){
	    			firstId = id;
	    			// url = d.url;
	    		}
    			//缓存起来
    			urlFrame[id] = (i != 0);
    			var iconText = '<i class="layui-icon layui-icon-refresh" style="margin: 0 5px;display: none;"></i> '
    			layui.element.tabAdd('docDemoTabBrief', {
	    			 title: title + iconText,
	    			 content: '<iframe scrolling="yes" id="' + id + '" frameborder="0" src="'+(i == 0 ? url : '')+'" style="width:100%;height:100%;"></iframe>',
	    			 id: id
	    		});
    			setIframeHeight(document.getElementById(firstId));
	    	}
	    	layui.element.tabChange('docDemoTabBrief', firstId);
			$('.layui-tab-title .layui-icon-refresh').first().show()
	    	//监听tab点击
	    	layui.element.on('tab(docDemoTabBrief)', function(data){
	    		var id = $(data.elem.context).attr("lay-id");
	    		// $("#"+id).attr("src",urlFrame[id]);
				if (urlFrame[id]) {
                    if (document.getElementById(id).getAttribute('src')) {
                        document.getElementById(id).contentWindow.location.reload()
                    } else {
                        document.getElementById(id).src = menuList.find(item => item.id == id).url || ''
                    }
					urlFrame[id] = false
				}
	    	})
	    }
	}
	
	function setIframeHeight(iframe) {
		if (iframe) {
			var iframeWin = iframe.contentWindow || iframe.contentDocument.parentWindow;
			if (iframeWin.document.body) {
				iframe.height = iframeWin.document.documentElement.scrollHeight-100 || iframeWin.document.body.scrollHeight-100;
			}
		}
	};
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>