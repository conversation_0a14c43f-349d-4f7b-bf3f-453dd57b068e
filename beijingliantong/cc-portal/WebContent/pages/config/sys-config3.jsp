<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>配置信息</title>
	<style type="text/css">
		.gray-bg {
            background: #F6F7F9;
        }
        .container-fluid {
            height: 100%;
            padding: 0;
        }
        #box {
            height: 100%;
            padding: 0.1rem;
            box-sizing: border-box;
            overflow: hidden;
        }
        .tab-box {
            position: relative;
            height: 0.48rem;
            line-height: 0.48rem;
            background: #FFFFFF;
            border-radius: 0.05rem;
            display: flex;
            margin-bottom: 0.1rem;
        }
        .line {
            position: absolute;
            height: 1px;
            background: #E5E9EF;
            bottom: 0.1rem;
            left: 0;
            right: 0;
        }
        .tab-item {
            position: relative;
            color: #929292;
            font-size: 0.14rem;
            margin: 0 0.2rem 0 0.12rem;
            font-family: Microsoft YaHei;
            cursor: pointer;
        }
        .tab-active {
            color: #1389FF;
        }
        .tab-active::after {
            position: absolute;
            content: '';
            bottom: 0.1rem;
            left: 0;
            right: 0;
            margin: auto;
            background: #1389FF;
            width: 80%;
            height: 2px;
            z-index: 1;
        }
        .iframe-box {
            height: calc(100% - 0.63rem);
        }
        .iframe-item {
            width: 100%;
            height: 100%;
        }
        .hide {
            display: none;
        }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<div id="box">
	<div class="tab-box">
        <div class="line"></div>
    </div>
    <div class="iframe-box">
        <iframe src="" frameborder="0" class="iframe-item"></iframe>
    </div>
</div>

</EasyTag:override>

<EasyTag:override name="script">

<script>
	var urlFrame = {};
    var menuList = [];
	var element ;

	$(function(){
        initMenu()
	});
	
	function initMenu(){
		ajax.remoteCall("/cc-portal/workbench?query=menu",{resId:'${param.resId }'},function(result) { 
			getMenuHtml(result);
		});
	}
	
	//加//载导航
	function getMenuHtml(data){
	   	var firstId = "";
        menuList = data.data
	    if(data.data && data.data.length>0){
            var tabHtml = '', iframeHtml = '';
	    	for(var i=0;i<data.data.length;i++){
	    		var d = data.data[i];
	    		var id = d.id;
	    		var title = d.title;
	    		var url = d.url;
	    		if(firstId==""){
	    			firstId = id;
	    			// url = d.url;
	    		}
    			//缓存起来
    			urlFrame[id] = (i != 0);
                tabHtml += '<div class="tab-item" data-id="' + id + '">' + title + '</div>';
                iframeHtml += '<iframe src="' + (i == 0 ? url : '') + '" frameborder="0" class="iframe-item' + (i == 0 ? '' : ' hide') + '" id="' + id + '"></iframe>'
	    	}
            $('.tab-box').prepend(tabHtml)
            $('.tab-box .tab-item').first().click()
            $('.iframe-box').html(iframeHtml)
	    }
	}

    $(document).on('click', '.tab-item', function() {
        if ($(this).hasClass('tab-active')) return;
        $(this).addClass('tab-active').siblings().removeClass('tab-active')
        var id = $(this).data('id')
        $('.iframe-item').addClass('hide')
        $('#'+id).removeClass('hide')
        if (urlFrame[id]) {
            document.getElementById(id).src = menuList.find(item => item.id == id).url || ''
            urlFrame[id] = false
        }
    })

    function resize() {
        var width = top.document.getElementsByTagName('body')[0].clientWidth
        document.documentElement.style.fontSize = (width / 1920) * 100 + 'px'
        document.body.style.fontSize = (width / 1920) * 100 + 'px'
    }

    document.addEventListener('resize', resize())
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>