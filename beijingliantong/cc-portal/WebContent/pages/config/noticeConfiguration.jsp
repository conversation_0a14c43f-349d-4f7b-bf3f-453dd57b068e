<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>配置中心</title>
	<style>
		*::-webkit-scrollbar-thumb {
		    background: #3DB6A4;
		    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent);
		}
		*::-webkit-scrollbar {
		    width: 6px;
		    overflow-y: scroll;
		}
		*::-webkit-scrollbar-track {
		    background-color: #eee;
		}
		*::-webkit-scrollbar-track-piece {
		    background-color: #eee;
		}
		body,html{width: 100%;height: 100%}
    	.layui-bg-white.layui-nav{background-color: transparent; color: #fff;width: 100%;}
        .layui-bg-white.layui-nav-tree .layui-nav-item a{color:#333;}
        .layui-bg-white.layui-nav .layui-nav-more{border-color: #aaa transparent transparent;}
        .layui-bg-white .layui-nav-itemed>a, .layui-bg-white.layui-nav-tree .layui-nav-title a, .layui-bg-white.layui-nav-tree .layui-nav-title a:hover{color:#333!important;}
        .layui-bg-white .layui-nav-itemed>.layui-nav-child{ background-color: transparent!important; }
        .layui-bg-white .layui-nav-itemed>.layui-nav-child dd a{padding-left: 30px;}
        .layui-bg-white .layui-nav .layui-nav-mored,.layui-bg-white .layui-nav-itemed>a .layui-nav-more{border-color: transparent transparent #aaa;}
        .layui-bg-white.layui-nav-tree .layui-nav-item a:hover{background-color:#f8f8f8;}
        .layui-bg-white.layui-nav-tree .layui-nav-child dd.layui-this, .layui-bg-white.layui-nav-tree .layui-nav-child dd.layui-this a, .layui-bg-white.layui-nav-tree .layui-this, .layui-bg-white.layui-nav-tree .layui-this>a, .layui-bg-white.layui-nav-tree .layui-this>a:hover{
            background-color: #f4f4f4;
            color:#2196f3;
        }
        .box{width: 100%;height: 100%; background: #fff;}
        .left-side{width: 210px;height: 100%;overflow: auto; background: #ffffff;float: left;}
        .right-side{margin-left: 210px;height: 100%;overflow: auto;border-left: 1px solid #ddd }
        .layui-bg-white.layui-nav-tree .layui-nav-item a{text-decoration: none;}
        
        .gray-bg{background-color: #f8f8f8;}
        .container-fluid{width: 100%;height: 100%;padding: 0px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="box">
		<div class="left-side">
		</div>
		<div class="right-side">
		</div>
	</div>
	
<script id="menu-template" type="text/x-jsrender">
	<ul class="layui-nav layui-nav-tree layui-bg-white layui-inline">
		{{for data}}
	  		<li class="layui-nav-item layui-nav-itemed">
				{{if url}}
					<a data-url="{{:url}}" href="javascript:;" class="menu">{{:title}}</a>
				{{else}}
					<a href="javascript:;">{{:title}}</a>
				{{/if}}
				{{if nav && nav.length>0}}
					<dl class="layui-nav-child">
						{{for nav}}
							<dd><a data-url="{{:url}}" href="javascript:;" class="menu">{{:title}}</a></dd>
						{{/for}}
					</dl>
				{{/if}}
			</li>
		{{/for}}
    </ul>
</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		$(function(){
			initMenu();
		});
		
		function initMenu(){
			ajax.remoteCall("${ctxPath}/workbench?query=menu",{resId:'${param.resId }'},function(result) { 
				getMenuHtml(result);
            	initIframe();
			});
		}
		
		function initIframe(){
			layui.use('element', function(){
				  var element = layui.element;
		     });
			$("[data-url]").click(function(){
				var t=$(this);
				var url=t.data("url");
				//console.info(url);
				$(".right-side").html("<iframe src='"+url+"' frameborder='0' style='height:98%;width:100%'></iframe>");
			});
			$(".left-side .menu").first().click();
			$(".left-side .menu").first().addClass("layui-this");
		}
		
		//加载导航
        function getMenuHtml(data){
            var markup = document.getElementById("menu-template").innerHTML;
            var template = $.templates(markup);
            var html = template.render(data);
            $('.left-side').html(html);
        }
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp" %>