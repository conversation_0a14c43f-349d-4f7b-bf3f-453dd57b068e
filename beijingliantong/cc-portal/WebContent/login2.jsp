<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib prefix="c"   uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="x"   uri="http://www.easitline.org/cc/portal/tags/function-tags.tld" %>
<c:set var="contextPath" value="${pageContext.request.contextPath}" />
<%
    request.logout();
	request.getSession().invalidate();
%>
<!DOCTYPE html>
<html>
<head>
    <title>智能客服系统 - 登录</title>
    <meta charset="utf-8"/>
    <link href="${Constants.PORTAL_FAVICON_ICO}" rel="icon">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css"/>
    <link rel="stylesheet" href="portal/css/login.css" media="all">
    <meta name="renderer" content="webkit"/>
    <style>
        .login-header {
            color: rgba(0, 0, 0, .85) !important;
        }
        .login-footer {
            color: rgba(0, 0, 0, 0.7) !important;
        }

        .login-footer a {
            color: rgba(0, 0, 0, 0.7) !important;
        }

        .login-footer a:hover {
            color: rgba(0, 0, 0, 0.4) !important;
        }
        .layui-form-item .layui-form-checkbox[lay-skin=primary]{margin-top: 0px!important;}
        input:-webkit-autofill{-webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;}
		input:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white inset;}
		
		.login-header{background-color: #fff;}
		.login-header .pull-right{font-weight: normal;font-size: 12px;}
		.login-header .pull-right div{display:inline-block;width: 30px;height: 24px;border: 1px solid #eee;cursor: pointer;}
    </style>


    <!-- 登录 -->
    <style>
        .loginpage{width:100%;height:100%;background-color:#f2f2f2;background-position:center;background-repeat:no-repeat;background-size:cover}.loginpage .loginImage{width:100%;height:100%;background-color:#f2f2f2;left:0;top:0;background-position:center;background-repeat:no-repeat;background-size:cover;position:absolute}.login-panel{z-index:2;margin:40px auto 0;width:820px;height:420px;border-radius:8px;overflow:hidden}.login-panel .login-left,.login-panel .login-right{float:left;height:100%;position:relative}.login-panel .login-left{background-color:#17a6f0;width:420px}.login-panel .login-right{width:400px;background-color:#fff}.login-panel .login-right .login-box{padding:40px 50px}.login-panel .login-right .login-box .login-title{
        line-height:1;font-size:20px;font-weight:bold;text-align:center;letter-spacing:1px;margin-bottom:12px; margin-top: -15px; color:#17a6f0}.login-form .form-input-box{border-radius:2px;border:1px solid #e5e5e5;position:relative;margin-bottom:20px;box-sizing:border-box}.login-form .form-input-box.hasIcon{padding-left:40px}.login-form .form-input-box.code-input{float:left;width:170px}.login-form .form-input-box input{font-size:14px;line-height:24px;height:44px;border:0;outline:none;width:100%;box-sizing:border-box;background:none;color:#999;display:block;padding:10px}.login-form .form-input-box .form-input-box-icon{position:absolute;left:0;top:0;width:40px;height:100%;background-repeat:no-repeat;background-position:16px center}
        .login-form .form-input-box .form-input-box-icon.username{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVEMjM0RUFDNEEyQzExRTlCMDVCRTI1OTMwRUJDNjQzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVEMjM0RUFENEEyQzExRTlCMDVCRTI1OTMwRUJDNjQzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NUQyMzRFQUE0QTJDMTFFOUIwNUJFMjU5MzBFQkM2NDMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NUQyMzRFQUI0QTJDMTFFOUIwNUJFMjU5MzBFQkM2NDMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5jK2SFAAABzUlEQVR42pyUSyhEYRSA/zF3CgkRiig7z/KYsiQlycajTEhWklASylZZoNiIjbJimilFKSV5FVIiUTZsbchrwcJjfIdfjTvG3JlTX2fmv+f/7j/3nDs2n8+nbDabMkex0xlN6oJ2yIMouAQPTJ0cHT2Y93y5/hIiyyCtQQpMwy68QRn0gANqkZ6HFCIzSAdaIJvuTDeLIS1CKRRw/clfGKUCoxlyodEsk2DthdSqv/aZr/8lbAAvG69VkODaM2kO6q0IM+FChQ6pybIilJ+ZZkEoNfdWhNLRBh6+I4TQBTtWhLOQCGPBTNysV4/QhPlasDmsJi3DKozQhDO9nk0agg6B9XlLg603F5MmoQIe4R2SHIZxY7fbuw729pYsvSnNTU2GfuXcbq/3FrF0PV8eT3pqamZ8XFy37u6gjA41vqBCZAmkFSiBOoo3A6b++4Yy0KP6kbRR9xwgpDCWtS1IhmqKrv5rMfVlWngKNdS/ml+9af1TKkLJJKg5JFXqbo//6nKLy1XO522oonBDhRGcVObRDc5Fj+f454T9sB6uTJ9U/h/3Ydh/sOXoCyrykHmU2VWGXviAQo5fFKEwR5rsL+yEGRiIUHivHepTgAEAR1y9F5aNvFAAAAAASUVORK5CYII=)}
        .login-form .form-input-box .form-input-box-icon.code{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjk1NjlDRDJFNEEyQzExRTk4OTRFOTBEQjcyQjE2NDhFIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjk1NjlDRDJGNEEyQzExRTk4OTRFOTBEQjcyQjE2NDhFIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6OTU2OUNEMkM0QTJDMTFFOTg5NEU5MERCNzJCMTY0OEUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6OTU2OUNEMkQ0QTJDMTFFOTg5NEU5MERCNzJCMTY0OEUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7OUju9AAABwklEQVR42qyVzyvDYRzHfbe1iAO5SooyS8na/gBKSRRZa4uDCwc/yslhyUFiibg5LDclGyfKjxwcnGzMAYmdSJzkIkV8vR4907fHdzazp169n+/zPJ/3np+fabquF+S1CMMU6Uqj290O19CRyUczGmmaphrVIuPghyh0S51KxOOXqqEoNsVgUfhCObjAAbvgweCU/gbqM3BB/QqNwyN8wOjXpIwzdHk84mMDbkAE7GOUNNmCGqQJ6qBSzPwkFtN+7CEDdaj6yxmI8SIu5WHJ5SADPp8d/GZ9lhwvxzxMY1r8b0NMAki/2LfVSORZ7belWxKyDrMEHRranUgYhmhPZLtkO4Nf0TPYw6RZmpXIH4nSv2wYXyjONp3hLdSLCkFBZBK2MGtDhckbDCoxThlnargNPakPTEPIBGxCq9y3FyXGJ+NMDZegk3vlNJjOIcPQRz2p3EGHfI7h7+ervmUGrVG18kK8GS60FTmAe8b6TN+yLCNQlOVdrIYuY2PabMMMSpEBEcgM3g3t4kotQC+00HdkzDa/GYrMsgMPcjbnINrG5Aq8mB2r6StTPixDgjIfVsAdrEAIsyezfKjl+y/gU4ABACnIyyN2/phXAAAAAElFTkSuQmCC)}
        .login-form .form-input-box .form-input-box-icon.psw{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjdDNDU0NkFGNEEyQzExRTk5RTEwRjRDNDhDRTEzQUQ0IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjdDNDU0NkIwNEEyQzExRTk5RTEwRjRDNDhDRTEzQUQ0Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6N0M0NTQ2QUQ0QTJDMTFFOTlFMTBGNEM0OENFMTNBRDQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6N0M0NTQ2QUU0QTJDMTFFOTlFMTBGNEM0OENFMTNBRDQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6iTQhKAAABdUlEQVR42qyUO0gDQRCGcyEIis/KUm0tcwFrG0Ewoo19EI2ggoVgq6V2ChZaWFgIViKCiqnT5cTCwkbQwiI2CUETFOX8FnZDbr29R8jCxySZ2f/mdbFc10108qTUB8uy/jnTtj2LWYcJ6IMaFOHg3nFu9XiRXNL0JMT2MRfwBsswCStQgWv8e74XhapeNsEL8APThodl4Rfmo2a4CmeUdePn5PcrzDms6T6TYBoKIf0vyDjPsVS5aiiUMSj7lIe7AMEpOIIhMq6qkpuCdiYzgDmFrNCPsSmuHF7OKZVqqRbHoSxhBl5kYCNAqBt6YRiOQWxFrjllSv2CxXaWmXt5+BQ6rRl2wbPhguhXD9Tpl19fy9LvEQw6oqQReIXRoMBkosMnquCGZsP/HMQ+wYch7kGz4YJqOQ2nDpfSRhNkkqLZZYQbPu/uO2Yubg8fYanNWYxBVe/hCeyQ6Tf2KYbYOGzL+x7BTfm67UJ/DMGKFNsSX/4EGAD3CH46yZfjkQAAAABJRU5ErkJggg==)}.login-form .form-submit{
         border-radius:2px;border:0;outline:none;background:#17a6f0;font-size:16px;line-height:24px;padding:10px;color:#fff;display:block;width:100%;letter-spacing:8px;
         background-image: -moz-linear-gradient( 0deg, rgb(111,178,255) 0%, rgb(60,137,255) 58%, rgb(9,95,255) 100%);
         background-image: -webkit-linear-gradient( 0deg, rgb(111,178,255) 0%, rgb(60,137,255) 58%, rgb(9,95,255) 100%);
         background-image: -ms-linear-gradient( 0deg, rgb(111,178,255) 0%, rgb(60,137,255) 58%, rgb(9,95,255) 100%);
         }.login-form .get-code-box{float:right;width:120px;height:46px}.login-form .get-code-box img{border-radius:2px;display:block;width:100%;height:100%}.row-code{margin-bottom:20px}.copy-info{font-size:14px;color:#f5f5f5;width:100%;text-align:center;position:absolute;bottom:40px;left:0;line-height: 24px;}@media (max-width:1000px){.login-panel{width:760px}.login-panel .login-left{width:360px}}@media (max-width:765px){.login-panel{width:400px}.login-panel .login-left{display:none}}@media (max-width:420px){.login-panel{width:90%}.login-panel .login-right{width:100%}.login-form .form-input-box.code-input{width:65%}.login-form .get-code-box{width:30%;height:36px}.login-panel .login-right .login-box{padding:30px}.login-form .form-input-box input{padding:5px 10px;height:34px}}
        .form-submit:hover{background-color: #3276b1}
        
        .login-panel{width:400px}
        .login-form .form-input-box{background: #fff;}
        .login-form .form-input-box input{}
       
        .login-footer{color: #444!important;}
        .login-footer a{color: #444!important;}
        .login-panel .login-right{background-color: #fff;} 
        
         body,html{
	          padding: 0;
	          min-height: 600px;
	          overflow-y: auto;
        }
       .form-input::-webkit-input-placeholder{color: #999}
       .copy-info {
	        font-size: 14px;
	        color: #b2b2b2;
	        width: 100%;
	        text-align: center;
	        position: absolute;
	        bottom: 20px;
	        left: 0;
	    }
	    .login-title img{height: 60px;display: block; margin:0 auto;}
	    .form-submit{cursor: pointer;}
	    .form-submit:hover{opacity:0.7;}
    </style>

    <script type="text/javascript">
        if (window != top)
            top.location.replace(location.href);
    </script>
</head>

<body class="loginpage loginImage" style="background-image: url(${contextPath }/static/images/bg.png)">
 <div class="login-wrapper">
    <div id="loginpanel" class="login-panel">
      <div class="login-right">
        <div class="login-box">
          <div class="login-title" style="line-height: 80px;height: 80px">
            	<img alt="" src=" ${x:getValue('loginLogoUrl','/cc-portal/static/images/logo.png')}">
          </div>
          <div class="login-content">
              <div class="content-box">
                 <form class="login-form" autocomplete="off">
                    <input id="ctxPath" type="hidden" value="/cc-portal"/>
                    <input id="busiId" type="hidden" value="007"/>
                    <!-- <input id="suffix" type="hidden" value="@zbc"/> -->
                    <div class="form-input-box hasIcon">
                      <div class="form-input-box-icon username"></div>
                        <input id="j_username" type="text" lay-verify="required" placeholder="账号" autocomplete="off" class="form-input">
                    </div>
                    <div class="form-input-box hasIcon ">
                      <div class="form-input-box-icon psw"></div>
                      <input id="j_password" type="password" lay-verify="required" placeholder="密码" autocomplete="off" class="form-input">
                    </div>
                    <div class="clear row-code">
                      <div class="form-input-box code-input hasIcon">
                          <div class="form-input-box-icon code"></div>
                          <input type="text" lay-verify="required" placeholder="验证码" id="j_imagecode" class="form-input">
                      </div>
                      <div class="get-code-box">
                          <img id="imageCode" style="cursor: pointer;" onclick="reloadImageCode();" title="点击刷新验证码" src="/yc-login/login?login=ImageCode&st=<%=System.currentTimeMillis()%>" alt="">
                      </div>
                    </div>
                    <input type="button" lay-filter="login-submit" class="form-submit"  lay-submit value="登录">
                 </form>
              </div>
          </div>
        </div>
      </div>
    </div>

    <div class="login-footer">
        <p>© 2019 云趣科技 <a href="" target="_blank">版权所有</a></p>
        <p>
        	 浏览器推荐使用：谷歌(Chrome)、火狐(Firefox)、360(极速模式)、Microsoft Edge、IE10(以上)版本,大于1440X900分辩率！
        </p>
    </div>
</div>

<script type="text/javascript" src="/yc-login/static/js/jquery.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js"></script>
<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.base64.min.js"></script>
<script type="text/javascript" src="/yc-login/static/js/login.js?v=201900828"></script>
<script>
    layui.use(['form'], function () {
        var form = layui.form;
        // 表单提交
        form.on('submit(login-submit)', function (obj) {
            //submitCheck();
            smsVerifyCheck();
            return false;
        });
        // 图形验证码
        $('.login-captcha').click(function () {
            this.src = this.src + '&t=' + (new Date).getTime();
        });
    });
</script>
</body>
</html>