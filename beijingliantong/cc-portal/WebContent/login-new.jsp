<%@page import="com.yunqu.ccportal.base.Constants"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib prefix="c"   uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="x"   uri="http://www.easitline.org/cc/portal/tags/function-tags.tld" %>
<c:set var="contextPath" value="${pageContext.request.contextPath}" />
<%
    request.logout();
	request.getSession().invalidate();
%>

<!DOCTYPE html>

<html>
<head>
    <title>智能客服系统 - 登录</title>
    <meta charset="utf-8"/>
    <link href="${Constants.PORTAL_FAVICON_ICO}" rel="icon">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css"/>
    <link rel="stylesheet" href="portal/css/login.css" media="all">
    <meta name="renderer" content="webkit"/>
    

    <script type="text/javascript">
        if (window != top)
            top.location.replace(location.href);
    </script>
</head>
<style>

</style>
<body class="page-login">
 <div class="login-wrapper">
	 <div class="pull-right">
	 	<select id="userLang"  onchange="changeLang(this)">
	 		<option value="CN">中文</option>
	 		<option value="EN">English</option>
	 	</select>
	 	<!-- <a href="javascript:;" class="userLang"  i18n-content="中文" ></a> -->
    </div>
    <div class="login-panel">
    	<div class="login-right">
			<div class="login-box">
			<div class="login-title" style="line-height: 80px;height: 80px">
	            	<img alt="" src="${x:getValue('LOGIN_LOGO','/cc-portal/static/images/logo.png')}">
	          </div>
		      
		      <div class="login-content">
		          <div class="content-box">
		            <form class="login-form" autocomplete="off">
	                    <input id="ctxPath" type="hidden" value="/cc-portal"/>
	                    <input id="busiId" type="hidden" value="007"/>
	                    <!-- <input id="suffix" type="hidden" value="@zbc"/> -->
	                    <div class="form-input-box hasIcon">
	                      <div class="form-input-box-icon username"></div>
	                        <input id="j_username" type="text" lay-verify="required" i18n-placeholder="账号" autocomplete="off" class="form-input">
	                    </div>
	                    <div class="form-input-box hasIcon ">
	                      <div class="form-input-box-icon psw"></div>
	                      <input id="j_password" type="password" lay-verify="required"  i18n-placeholder="密码" autocomplete="off" class="form-input">
	                    </div>
	                    <div class="clear row-code">
	                      <div class="form-input-box code-input hasIcon">
	                          <div class="form-input-box-icon code"></div>
	                          <input type="text" lay-verify="required"  i18n-placeholder="验证码" id="j_imagecode" class="form-input">
	                      </div>
	                      <div class="get-code-box">
	                          <img id="imageCode" style="cursor: pointer;" onclick="reloadImageCode();" i18n-title="点击刷新验证码" src="/yc-login/login?login=ImageCode&st=<%=System.currentTimeMillis()%>" alt="">
	                      </div>
	                    </div>
	                    <input type="button" lay-filter="login-submit" class="form-submit"  lay-submit i18n-value="登录">
	                 </form>
		          </div>
		      </div>
              <div class="scan" style="display: none;">
                  <img src="/yc-ssologin/qrCode/qrCodeLogin?action=getQrCode" alt="" class="qrcode">
                  <div class="qrcode-status" style="display: none;">
                      <p class="text"></p>
                      <div class="refreshBtn" i18n-content="刷新" style="display: none;"></div>
                      <img src="/cc-portal/static/images/correct.png" alt="" style="display: none;" class="correct">
                  </div>
                  <div class="tips">
                        <img src="/cc-portal/static/images/scan.png" alt="">
                        <p i18n-content="打开"></p>&nbsp;
                        <p i18n-content="移动坐席APP" class="scanName"></p>&nbsp;
                        <p i18n-content="扫描二维码"></p>
                    </div>
              </div>
              <div class="modeText" i18n-content="扫码登录"></div>
		    </div>
	 	 </div>
	</div>


    <div class="login-footer" style="color:#999 !important;">
        <p i18n-content="${x:getValue('COPYRIGHT','云趣科技版权所有')}" onclick="onclickRecord()" style="cursor:pointer"></p>
        <p  i18n-content="浏览器推荐使用"></p>
    </div>
</div>

<link rel="stylesheet" href="/cc-portal/static/css/login.css?v=20220721">
<script type="text/javascript" src="/yc-login/static/js/jquery.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js"></script>
<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.md5.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.base64.min.js"></script>
<script type="text/javascript" src="/yc-login/static/js/login.js?v=201900828"></script>

<script type="text/javascript" src="static/js/my_i18n.js?2020071711"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711"></script>
<script>
    layui.use(['form'], function () {
        var form = layui.form;
        // 表单提交
        form.on('submit(login-submit)', function (obj) {
        	console.log("登录前通知服务端设置语言")
        	ajax.remoteCall("/cc-base/api/spec?action=setUserLang",{"userAcc":$("#j_username").val(),"lang":$("#userLang").val()},function(result) { 
                
        	});
            //submitCheck();
            smsVerifyCheck();
            return false;
        });
        
        // 图形验证码
        $('.login-captcha').click(function () {
            this.src = this.src + '&t=' + (new Date).getTime();
        });
    });
    
    var isScan = false;
    var timer = null;
    $('.modeText').on('click', function() {
        isScan = !isScan
        $(this).html(getI18nValue(isScan ? '密码登录' : '扫码登录'))
        if (isScan) {
            $('.login-content').hide()
            $('.scan').show()
            $('.qrcode').attr('src', '/yc-ssologin/qrCode/qrCodeLogin?action=getQrCode' + '&t=' + new Date().getTime())
            timer = setInterval(getQrCodeLoginResult, 2000)
        } else {
            $('.login-content').show()
            $('.scan').hide()
            $('.qrcode-status').hide()
            $('.refreshBtn').hide()
            clearInterval(timer)
        }
    })

    $('.refreshBtn').on('click', function() {
        $('.correct').hide()
        $('.qrcode-status').hide()
        $('.refreshBtn').hide()
        $('.qrcode').attr('src', '/yc-ssologin/qrCode/qrCodeLogin?action=getQrCode' + '&t=' + new Date().getTime())
        timer = setInterval(getQrCodeLoginResult, 2000)
    })

    function getQrcode() {
        $.ajax({
            url: '/yc-ssologin/qrCode/qrCodeLogin?action=getQrCode',
            success: function(res) {
                console.log(res);
            }
        })
    }

    function getQrCodeLoginResult() {
        $.ajax({
            url: '/yc-ssologin/qrCode/qrCodeLogin?action=getQrCodeLoginResult&loginToken=',
            type: 'POST',
            cache:false,
            dataType : 'json',
            contentType : "application/x-www-form-urlencoded; charset=UTF-8",
            data: {
                data: {}
            },
            success: function(result) {
                var text = ''
                if (result.state == 1) {
                    $('.qrcode-status').show()
                    $('.refreshBtn').hide()
                    $('.qrcode-status .text').hide()
                    $('.correct').show()
                    setTimeout(function() {
                        location.replace(result.data)
                    }, 1000);
                } else if (result.state == 500 || result.state == 501) {
                    clearInterval(timer)
                    $('.correct').hide()
                    $('.qrcode-status').show()
                    text = '系统错误'
                    $('.qrcode-status .text').text(getI18nValue(text))
                } else if (result.state == 502) {
                    clearInterval(timer)
                    $('.correct').hide()
                    $('.qrcode-status').show()
                    $('.refreshBtn').show()
                    text = '二维码已失效'
                    $('.qrcode-status .text').text(getI18nValue(text))
                } else {
                    $('.qrcode-status').hide()
                    $('.refreshBtn').hide()
                    $('.correct').hide()
                }
            },
            error: function() {
                // clearInterval(timer)
                // $('.qrcode-status').show()
                // $('.qrcode-status .text').text(getI18nValue('系统错误'))
            }
        })
    }
    
    //切换语言
    function changeLang(obj){
    	var userLang = obj.value;
    	localStorage.setItem("MULTI-LANG-KEY",userLang);
    	execI18n();
    }
    
    function onclickRecord(){
    	window.open("https://beian.miit.gov.cn/#/Integrated/index");
    }
    
    $(function(){
    	 ajax.remoteCall("/cc-base/api/spec?action=getAllLang",{},function(result) { 
    		 var list = result.data;
             var select = document.getElementById('userLang');
             if (list != null || list.size() > 0) {
            	 $("#userLang").find("option").remove(); 
                for (var c in list) {
                    var option = document.createElement("option");
                    option.setAttribute("value", list[c].LANG_CODE);
                    option.innerText = list[c].LANG_NAME;
                    select.appendChild(option)
                }
                
              //设置默认的语言
           	    var multiLang = localStorage.getItem("MULTI-LANG-KEY");
           	    if(!multiLang){
           	    	multiLang = "CN";
           	    }
           	    $("#userLang").val(multiLang);
             };
             
    	 });
    	
    	
   	    
    	//ajax.remoteCall("/cc-portal/workbench?action=setLang",{"lang": multiLang},function(result) { 
		  //  alert(result)
      	//});
    	
    	window.onbeforeunload = function(event) { 
    		  
    		
    	}
    })
    var logoutType = "${param.logoutType}";
    if("2"==logoutType){
    	layer.alert(getI18nValue("账号已在其它地方登录"), {
        	icon: 5
        },
        function (index){
        	window.location.href = funcUrlDel("logoutType");
        }
	);
    }
    

    function getQueryString(name) {  
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");  
        var r = window.location.search.substr(1).match(reg);  
        if (r != null) return unescape(r[2]);  
        return null;  
    }  
    
    /**
     * 删除当前url中指定参数
     * @param names 数组或字符串
     * @returns {string}
     */
    function funcUrlDel(names) {
        if(typeof(names)=='string'){
            names = [names];
        }
        var loca = window.location;
        var obj = {}
        var arr = loca.search.substr(1).split("&");
        //获取参数转换为object
        for(var i = 0; i < arr.length; i++) {
            arr[i] = arr[i].split("=");
            obj[arr[i][0]] = arr[i][1];
        };
        //删除指定参数
        for(var i = 0; i < names.length; i++) {
             delete obj[names[i]];
        }
        //重新拼接url
        var url = loca.origin + loca.pathname + "?" + JSON.stringify(obj).replace(/[\"\{\}]/g, "").replace(/\:/g, "=").replace(/\,/g, "&");
        return url;
    }

</script>
</body>
</html>