<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta name="wap-font-scale" content="no" />
	<LINK rel="Bookmark" href="/yc-login/favicon.ico" />
	<LINK rel="Shortcut Icon" href="/yc-login/favicon.ico" />
	<title>登录</title>
	<meta name="viewport" content="initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/cc-portal/static/css/login.css">
	<!-- 初始化 -->
	<style>
		
	</style>
	<!-- 登录样式 -->
</head>
<body class="page-login">
	<div class="login-panel"><div class="login-right">
		<div class="login-box">
	      <div class="login-title"> 企业呼叫中心  </div>
	      <div class="login-content">
	          <div class="content-box">
	             <form class="login-form" autocomplete="off">
	                  <div class="form-input-box hasIcon">
	                      <div class="form-input-box-icon username"></div>
	                      <input type="text" style="display: none;">
	                      <input type="text" id="j_username" onfocus="this.removeAttribute('readonly')" placeholder="用户名" autocomplete="off" class="form-input">
	                  </div>
	                  <div class="form-input-box hasIcon ">
	                      <div class="form-input-box-icon psw"></div>
	                       <input type="password" style="display: none;">
	                      <input type="text" id="j_password" onfocus="this.type='password'" placeholder="密码" autocomplete="off" class="form-input">
	                  </div>
	                  <div class="clear row-code">
	                      <div class="form-input-box code-input hasIcon">
	                          <div class="form-input-box-icon code"></div>
	                          <input type="text" placeholder="验证码" id="j_imagecode" class="form-input">
	                      </div>
	                      <div class="get-code-box">
	                          <img id="imageCode" style="cursor: pointer;" onclick="reloadImageCode();" title="点击刷新验证码" src="/yc-login/login?login=ImageCode&amp;ts1583916436147" alt="">
	                      </div>
	                  </div>
	                  <input type="button" class="form-submit" onclick="submitCheck();" value="登录">
	             </form>
	          </div>
	      </div>
	    </div>
	  </div>
	</div>
</body>
</html>