<?xml version="1.0" encoding="UTF-8"?>
<config>
<param key="OPEN_CALL_URL" 		name="是否开启来电弹屏" type="radio" items="{Y:开启,N:关闭}"  description="是否开启来电弹屏,Y-开启,N-不开启" value="N" index="1"></param>
<param key="LOGIN_URL" 			name="登录URL" type="string" description="重定向的登录URL" value="/cc-portal/login.jsp" index="2"></param>
<param key="WELCOME_PAGE_URL" 	name="登录后的首页URL" type="string" description="登录后的首页URL" value="/cc-workbench/pages/portal.jsp" index="3"></param>
<param key="loginLogoUrl" 		name="登录界面logo" type="string" description="" value="" index="4"/>
<param key="LOGOUT_URL" 		name="退出跳转URL" type="string" description="退出跳转URL优先取cc-portal，然后再去yc-login" value="" index="2"></param>

<param key="PORTAL_TITLE" 		name="门户首页标题" type="string" description="" value="企业呼叫中心" index="5" />
<param key="PORTAL_LOGO"  		name="门户首页LOGO" type="string" description="" value="/cc-portal/portal/images/logo.png" index="6" />
<param key="PORTAL_FAVICON_ICO" name="FAVICON_ICO图标" type="string" description="" value="" index="7" />

<param key="LOGIN_VERSION" 		name="v3版本登陆页面版本" type="text" description="登陆页面版本 ，默认1" value="1"  index="9" />
<param key="LOGIN_LOGO" 		name="v3版本登陆页LOGO" type="text" description="v3版本登陆页LOGO ，默认/cc-portal/static/images/logo.png" value="/cc-portal/static/images/logo.png"   index="10" />
<param key="COPYRIGHT" 			name="企业备案" type="text" description="企业备案" value="云趣科技版权所有"   index="11" />
<param key="PORTAL_RES_CODE" 	name="菜单编码开关" type="radio" items="{Y:开启,N:关闭}"  description="是否显示菜单编码,Y-开启,N-不开启" value="N"  index="12" ></param>
<param key="AUTO_LOGOUT_TIME_OUT" 	name="前端超时自动退出时间" type="int"   description="设置后,如果客户超过该时间未在系统里进行任何操作则自动退出,默认为0,不开启超时检查,单位:分钟" value="0" index="15"></param>

<param key="PWD_UPDATE_FLAG" 	name="是否开启密码修改提醒" type="radio" items="{Y:开启,N:关闭}"  description="开启后，如需用户修改密码，在首页会弹出密码修改框,前提是cc-base里开启了允许用户修改密码，修改后无需重载" value="N" index="16"></param>
<param key="PWD_UPDATE_DAYS" 	name="密码修改超时提醒天数" type="int"   description="开启密码提醒后，用户在首次登录或者上次修改密码距离今天超过配置的天数后会提醒修改密码,修改后无需重载" value="0" index="17"></param>

<param key="PORTAL_BUSI_ID" 	name="支持弹屏的服务ID" type="string"   description="支持弹屏里的服务，如007-企业呼叫中心，12345-12345客服，003-云客服；该门户的弹屏功能仅仅针对配置的服务生效" value="007" index="17"></param>


<param key="SAVE_CUST_LATESTAGENT_TIMES" 	name="缓存客户最近接待坐席的时间" type="string" description="坐席接听客户来电时，系统会缓存客户最近服务坐席，时长默认为50小时(注意：该配置需要结合队列配置-路由-排队优先级配置里的优先分配给上次接待坐席开关使用，由IVR在客户接入时，通过mars request接口获取上次接待坐席。)" index="20" value="50" />
<param key="YC_PORTAL_CALLOUT_URL" 	name="预测式外呼弹屏地址" type="string" description="当挂载云电销的菜单到企业客服时，收到预测式外呼时，如果配置了url则弹屏到指定url，否则弹屏企业客服的界面" index="20" value="/yc-portal/pages/task/my-task-execute.jsp?from=myAutoTask&amp;objId=#objId#&amp;taskId=#taskId#" />
<param key="DB_ISOK" 	name="数据库连接是否正常" type="radio" items="{Y:正常,N:异常}"  description="数据库连接是否正常，设置为异常后，模块将不再连接数据库；仅用于模拟测试，生产中必须设置为Y，如数据库异常，系统会自动管理" index="21" value="Y" />
<param key="SHOW_NOTES" 	name="门户界面上是否长驻公告通知信息" type="radio" items="{Y:显示,N:隐藏}"  description="设置为显示后，界面上会显示一个弹出框，滚动显示最新的公告信息等" index="22" value="N" />

<param key="PORTAL_INDEX_CSS" 	name="PORTAL_INDEX个性化css文件" type="string" description="PORTAL_INDEX个性化css文件" index="9999" value="" />

</config>
