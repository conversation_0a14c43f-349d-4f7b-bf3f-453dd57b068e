<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
	<resource id="cc-base-system" name="系统管理" url="/cc-portal/pages/config/sys-config.jsp?resId=cc-base-system" type="2" portal="" state="0" order="4" icon="iconfont icon-setting" entId ="0" index = "80">
		<resource id="cc-base-system-btnauth" name="通用按钮权限"  type="2" icon="" portal="" state="0" entId ="0"  index="99">
			<resource id="cc-portal-btnauth-bookMark-add" name="门户显示添加收藏夹按钮" url="" type="3" icon="" portal="" state="0" entId="0" index="21"/>
			<resource id="cc-portal-btnauth-xgmm" name="修改密码功能" url="" type="3" icon="" portal="" state="0" entId="0" index="22"/>
		</resource>
	</resource>
</resources>