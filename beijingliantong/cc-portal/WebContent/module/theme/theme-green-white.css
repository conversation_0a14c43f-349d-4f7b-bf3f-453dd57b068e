/** 透明侧边栏导航 */
.layui-layout-admin .layui-side .layui-nav {
    background-color: transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(255,255,255,.03);
}

/** logo部分样式 */
.layui-layout-admin .layui-header .layui-logo {
    background-color: #00675d;
    color: #eee;
}

/** header样式 */
.layui-layout-admin .layui-header {
    background-color: #ffffff;
}

.layui-layout-admin .layui-header a {
    color: #333;
}

.layui-layout-admin .layui-header a:hover {
    color: #333;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #333 transparent transparent;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #333;
}

/** 导航栏下面的线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #333;
}

/** 侧边栏样式 */
.layui-layout-admin .layui-side {
    background-color: #00675d !important;
}

.layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a, .layui-nav-tree .layui-this > a:hover {
    background-color: #00675d;
}

.layui-nav-tree .layui-nav-bar {
    background-color: #00675d;
}

/** 主题颜色 */

/** 按钮 */
.layui-btn:not(.layui-btn-primary):not(.layui-btn-normal):not(.layui-btn-warm):not(.layui-btn-danger):not(.layui-btn-disabled) {
    background-color: #00675d;
}

.layui-btn.layui-btn-primary:hover {
    border-color: #00675d;
}

/** 开关 */
.layui-form-onswitch {
    border-color: #00675d;
    background-color: #00675d;
}

/** 分页插件 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #00675d;
}

.layui-table-page .layui-laypage input:focus {
    border-color: #00675d !important;
}

.layui-table-view select:focus {
    border-color: #00675d !important;
}

.layui-table-page .layui-laypage a:hover {
    color: #00675d;
}

/** 单选按钮 */
.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #00675d;
}

/** 下拉条目选中 */
.layui-form-select dl dd.layui-this {
    background-color: #00675d;
}

/** 选项卡 */
.layui-tab-brief > .layui-tab-title .layui-this {
    color: #00675d;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-color: #00675d !important;
}

/** 面包屑导航 */
.layui-breadcrumb a:hover {
    color: #00675d !important;
}

/** 主体标题 */
.layui-body-header-title {
    border-left-color: #00675d;
}

/** 日期选择器按钮 */
.laydate-footer-btns span:hover {
    color: #00675d !important;
}

/** 时间轴 */
.layui-timeline-axis {
    color: #00675d;
}

/** 主题切换 */
.btnTheme:hover, .btnTheme.active {
    border-color: #00675d;
}

/** 侧边栏文字颜色 */
.layui-side .layui-nav .layui-nav-item a {
    color: rgba(238,238,238,.7);
}

.layui-side .layui-nav-itemed > a, .layui-nav-tree .layui-nav-title a, .layui-nav-tree .layui-nav-title a:hover {
    color: #eee !important;
}

/** header线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #00675d;
}

/** tab下划线 */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    background-color: #00675d;
    top: 0px;
}

/** 复选框 */
.layui-form-checked[lay-skin=primary] i {
    border-color: #00675d;
    background-color: #00675d;
}

.layui-form-checkbox[lay-skin=primary] i:hover {
    border-color: #00675d;
}

/** PC端折叠鼠标经过样式 */
.layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item.admin-nav-hover > .layui-nav-child {
    background: #00675d !important;
}

/** 移动设备样式 */
@media screen and (max-width: 750px) {
    /** 去掉PC端折叠鼠标经过样式 */
    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item.admin-nav-hover > .layui-nav-child {
        background-color: rgba(0, 0, 0, .3) !important;
    }
}

/** admin风格弹窗样式 */
.layui-layer.layui-layer-admin .layui-layer-title {
    background-color: #00675d;
    color: #ffffff;
}

/** 按钮颜色 */
.layui-layer.layui-layer-admin .layui-layer-setwin a {
    color: #ffffff;
}

/* 最小化按钮 */
.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-min cite {
    background-color: #dddddd;
}

/** 弹窗确定按钮 */
 .layui-layer.layui-layer-admin .layui-layer-btn .layui-layer-btn0 {
     border-color: #00675d;
     background-color: #00675d;
 }

/* 圆形按钮 */
.btn-circle {
    background: #00675d;
}