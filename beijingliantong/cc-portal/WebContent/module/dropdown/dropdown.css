/**
 * 下拉菜单模块
 * date:2019-06-02   License By http://easyweb.vip
 */

.dropdown-no-scroll {
    overflow: hidden;
}

/** 遮罩层 */
.dropdown-menu-shade {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9997;
    background-color: rgba(0, 0, 0, .1);
}

.dropdown-menu-shade.no-shade {
    background-color: transparent;
}

/** 夜间主题 */

.dropdown-menu-nav.dark {
    background: #32363F;
    border-color: #484e58;
}

.dropdown-menu-nav.dark > li > a {
    color: #a1a8b8;
}

.dropdown-menu-nav.dark > li > a:hover {
    background-color: #272b34;
}

.dropdown-menu-nav.dark > li.disabled > a, .dropdown-menu-nav > li.disabled > a:hover {
    color: #7a8191;
}

.dropdown-menu-nav.dark hr {
    background-color: #484e58;
}

.dropdown-menu-nav.dark > li.title {
    color: #868b9a;
}

.dropdown-menu-nav.dark .dropdown-anchor {
    border-color: #484e58;
}

.dropdown-menu-nav.dark .dropdown-anchor::after {
    border-color: #32363F;
}

/** 白色主题 */
.dropdown-menu {
    position: relative;
    display: inline-block;
}

.dropdown-menu-nav {
    position: absolute;
    padding: 5px 0;
    margin: 0;
    overflow: visible;
    min-width: 110px;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
    border: 1px solid #d2d2d2;
    text-align: left;
    z-index: 9998;
}

.dropdown-menu .dropdown-menu-nav {
    display: none;
}

.dropdown-menu.dropdown-open .dropdown-menu-nav {
    display: block;
}

.dropdown-menu-nav.fixed {
    position: fixed;
}

.dropdown-menu-nav > li {
    padding: 0;
    margin: 0;
    line-height: 18px;
    user-select: none;
    list-style: none;
}

.dropdown-menu-nav > li > a {
    display: block;
    color: #555;
    font-size: 14px;
    padding: 10px 15px;
    text-decoration: none;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
}

.dropdown-menu-nav > li > a:hover {
    background-color: #eeeeee;
}

.dropdown-menu-nav > li .layui-icon {
    font-size: 14px;
    margin-right: 5px;
}

.dropdown-menu-nav > hr {
    height: 1px;
    margin: 3px 0;
    background-color: #e6e6e6;
}

/** 禁用样式 */
.dropdown-menu-nav > li.disabled {
    cursor: not-allowed;
}

.dropdown-menu-nav > li.disabled > a, .dropdown-menu-nav > li.disabled > a:hover {
    color: #999;
    cursor: not-allowed;
    pointer-events: none;
    background-color: transparent;
}

/** 标题样式 */
.dropdown-menu-nav > li.title {
    color: #999;
    font-size: 12px;
    padding: 3px 15px;
}

/** 小箭头 */
.dropdown-menu-nav .dropdown-anchor, .dropdown-menu-nav .dropdown-anchor:after {
    border: 8px solid #d2d2d2;
    position: absolute;
    display: inline-block;
}

.dropdown-menu-nav .dropdown-anchor:after {
    content: '';
    border: 7px solid #fff;
}

/** 下左位置 */
.dropdown-menu-nav.dropdown-bottom-left {
    top: 100%;
}

.dropdown-menu-nav.dropdown-bottom-left {
    margin-top: 8px;
}

.dropdown-menu-nav.dropdown-bottom-left .dropdown-anchor, .dropdown-menu-nav.dropdown-bottom-left .dropdown-anchor:after {
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    top: -16px;
}

.dropdown-menu-nav.dropdown-bottom-left .dropdown-anchor:after {
    top: -6px;
    left: -7px;
}

.dropdown-menu-nav.dropdown-bottom-left .dropdown-anchor {
    left: 12px;
}

/** 下右位置 */
.dropdown-menu-nav.dropdown-bottom-right {
    right: 0;
}

.dropdown-menu-nav.dropdown-bottom-right {
    margin-top: 8px;
}

.dropdown-menu-nav.dropdown-bottom-right .dropdown-anchor, .dropdown-menu-nav.dropdown-bottom-right .dropdown-anchor:after {
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    top: -16px;
}

.dropdown-menu-nav.dropdown-bottom-right .dropdown-anchor:after {
    top: -6px;
    left: -7px;
}

.dropdown-menu-nav.dropdown-bottom-right .dropdown-anchor {
    right: 12px;
}

/** 下中位置 */
.dropdown-menu-nav.dropdown-bottom-center {
    left: 50%;
    /*transform: translateX(-50%);*/
}

.dropdown-menu-nav.dropdown-bottom-center {
    margin-top: 8px;
}

.dropdown-menu-nav.dropdown-bottom-center .dropdown-anchor, .dropdown-menu-nav.dropdown-bottom-center .dropdown-anchor:after {
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    top: -16px;
}

.dropdown-menu-nav.dropdown-bottom-center .dropdown-anchor:after {
    top: -6px;
    left: -7px;
}

.dropdown-menu-nav.dropdown-bottom-center .dropdown-anchor {
    left: calc(50% - 8px);
}

/** 上左位置 */
.dropdown-menu-nav.dropdown-top-left {
    bottom: 100%;
}

.dropdown-menu-nav.dropdown-top-left {
    margin-bottom: 8px;
}

.dropdown-menu-nav.dropdown-top-left .dropdown-anchor, .dropdown-menu-nav.dropdown-top-left .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    bottom: -16px;
}

.dropdown-menu-nav.dropdown-top-left .dropdown-anchor:after {
    top: -8px;
    left: -7px;
    bottom: auto;
}

.dropdown-menu-nav.dropdown-top-left .dropdown-anchor {
    left: 12px;
}

/** 上右位置 */
.dropdown-menu-nav.dropdown-top-right {
    bottom: 100%;
    right: 0;
}

.dropdown-menu-nav.dropdown-top-right {
    margin-bottom: 8px;
}

.dropdown-menu-nav.dropdown-top-right .dropdown-anchor, .dropdown-menu-nav.dropdown-top-right .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    bottom: -16px;
}

.dropdown-menu-nav.dropdown-top-right .dropdown-anchor:after {
    top: -8px;
    left: -7px;
    bottom: auto;
}

.dropdown-menu-nav.dropdown-top-right .dropdown-anchor {
    right: 12px;
}

/** 上中位置 */
.dropdown-menu-nav.dropdown-top-center {
    bottom: 100%;
    left: 50%;
    /*transform: translateX(-50%);*/
}

.dropdown-menu-nav.dropdown-top-center {
    margin-bottom: 8px;
}

.dropdown-menu-nav.dropdown-top-center .dropdown-anchor, .dropdown-menu-nav.dropdown-top-center .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    bottom: -16px;
}

.dropdown-menu-nav.dropdown-top-center .dropdown-anchor:after {
    top: -8px;
    left: -7px;
    bottom: auto;
}

.dropdown-menu-nav.dropdown-top-center .dropdown-anchor {
    left: calc(50% - 8px);
}

/** 左上位置 */
.dropdown-menu-nav.dropdown-left-top {
    right: 100%;
    bottom: 0;
}

.dropdown-menu-nav.dropdown-left-top {
    margin-right: 8px;
}

.dropdown-menu-nav.dropdown-left-top .dropdown-anchor, .dropdown-menu-nav.dropdown-left-top .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent;
    right: -16px;
}

.dropdown-menu-nav.dropdown-left-top .dropdown-anchor:after {
    top: -7px;
    left: -8px;
    right: auto;
}

.dropdown-menu-nav.dropdown-left-top .dropdown-anchor {
    bottom: 12px;
}

/** 左下位置 */
.dropdown-menu-nav.dropdown-left-bottom {
    right: 100%;
    top: 0;
}

.dropdown-menu-nav.dropdown-left-bottom {
    margin-right: 8px;
}

.dropdown-menu-nav.dropdown-left-bottom .dropdown-anchor, .dropdown-menu-nav.dropdown-left-bottom .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent;
    right: -16px;
}

.dropdown-menu-nav.dropdown-left-bottom .dropdown-anchor:after {
    top: -7px;
    left: -8px;
    right: auto;
}

.dropdown-menu-nav.dropdown-left-bottom .dropdown-anchor {
    top: 12px;
}

/** 左中位置 */
.dropdown-menu-nav.dropdown-left-center {
    right: 100%;
    top: 50%;
    /*transform: translateY(-50%);*/
}

.dropdown-menu-nav.dropdown-left-center {
    margin-right: 8px;
}

.dropdown-menu-nav.dropdown-left-center .dropdown-anchor, .dropdown-menu-nav.dropdown-left-center .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent;
    right: -16px;
}

.dropdown-menu-nav.dropdown-left-center .dropdown-anchor:after {
    top: -7px;
    left: -8px;
    right: auto;
}

.dropdown-menu-nav.dropdown-left-center .dropdown-anchor {
    top: calc(50% - 8px);
}

/** 右上位置 */
.dropdown-menu-nav.dropdown-right-top {
    left: 100%;
    bottom: 0;
}

.dropdown-menu-nav.dropdown-right-top {
    margin-left: 8px;
}

.dropdown-menu-nav.dropdown-right-top .dropdown-anchor, .dropdown-menu-nav.dropdown-right-top .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-top-color: transparent;
    left: -16px;
}

.dropdown-menu-nav.dropdown-right-top .dropdown-anchor:after {
    top: -7px;
    left: -6px;
}

.dropdown-menu-nav.dropdown-right-top .dropdown-anchor {
    bottom: 12px;
}

/** 右下位置 */
.dropdown-menu-nav.dropdown-right-bottom {
    left: 100%;
    top: 0;
}

.dropdown-menu-nav.dropdown-right-bottom {
    margin-left: 8px;
}

.dropdown-menu-nav.dropdown-right-bottom .dropdown-anchor, .dropdown-menu-nav.dropdown-right-bottom .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-top-color: transparent;
    left: -16px;
}

.dropdown-menu-nav.dropdown-right-bottom .dropdown-anchor:after {
    top: -7px;
    left: -6px;
}

.dropdown-menu-nav.dropdown-right-bottom .dropdown-anchor {
    top: 12px;
}

/** 右中位置 */
.dropdown-menu-nav.dropdown-right-center {
    left: 100%;
    top: 50%;
    /*transform: translateY(-50%);*/
}

.dropdown-menu-nav.dropdown-right-center {
    margin-left: 8px;
}

.dropdown-menu-nav.dropdown-right-center .dropdown-anchor, .dropdown-menu-nav.dropdown-right-center .dropdown-anchor:after {
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-top-color: transparent;
    left: -16px;
}

.dropdown-menu-nav.dropdown-right-center .dropdown-anchor:after {
    top: -7px;
    left: -6px;
}

.dropdown-menu-nav.dropdown-right-center .dropdown-anchor {
    top: calc(50% - 8px);
}

/** 按钮里面三角形样式 */
.icon-btn .layui-icon-drop {
    margin-right: 0;
    font-size: 14px;
}

.layui-icon-drop:before {
    content: "\e625";
}

.layui-icon-drop.top {
    transform: rotate(180deg);
    display: inline-table;
}

.layui-icon-drop.left {
    transform: rotate(90deg);
    display: inline-table;
}

.layui-icon-drop.right {
    transform: rotate(-90deg);
    display: inline-table;
}

.dropdown-menu + .dropdown-menu, .layui-btn + .dropdown-menu, .dropdown-menu + .layui-btn {
    margin-left: 10px;
}