@import url(minAlert.css);
.ul-reset {
  margin: 0;
  padding: 0;
  list-style: none;
}
.d-flex {
  width: 100%;
  height: 100%;
  display: table;
  table-layout: fixed;
}
.d-flex .d-flex-left,
.d-flex .d-flex-middle,
.d-flex .d-flex-right {
  position: relative;
  display: table-cell;
}
.d-flex .d-flex-left {
  background-color: #f2f2f2;
  width: 200px;
}
.d-flex .d-flex-left .drop {
  right: -5px;
  z-index: 2;
}
.d-flex .d-flex-right {
  background-color: #aaa;
  width: 300px;
}
.d-flex .d-flex-right .drop {
  left: -5px;
  z-index: 2;
}
.drop {
  position: absolute;
  cursor: e-resize;
  width: 10px;
  height: 100%;
  top: 0;
  background-color: #3cd;
}
.flexbox {
  display: table;
  width: 100%;
  height: 100%;
}
.mb-20 {
  margin-bottom: 20px;
}
.full-page,
.full-page body {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.panel-fixed {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.panel-fixed .panel-fixed-title,
.panel-fixed .panel-fixed-header,
.panel-fixed .panel-fixed-footer {
  position: relative;
}
.panel-fixed .panel-fixed-content {
  position: relative;
  flex: 1;
}
.chat-user-list-title {
  padding: 10px 15px;
  line-height: 30px;
  border-bottom: 1px solid #e1e3e5;
}
.chat-user-list-title h3 {
  margin: 0;
  padding: 0;
}
.chat-user-list-title label {
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 0;
}
.chat-user-list-title .num {
  color: #008cee;
}

.row-flex {
  display: table;
  width: 100%;
  height: 100%;
  position: relative;
}
.row-flex > .col-flex {
  display: table-cell;
  height: 100%;
  position: relative;
}
.row-flex .col-flex-1 {
  flex: 1;
}
/* 闁瑰瓨鍨瑰▓鎴濐啅閵夈倗绋婇柛娆欐嫹 */
.my-info-panel {
  position: relative;
  height: 70px;
  overflow: hidden;
  border-bottom: 1px solid #e1e3e5;
}
.my-info-panel label .user-tag{
	border:1px solid #999;
	color:#999;
	font-size:10px;
	font-weight:normal;
	padding:1px;
	margin-left:4px;
	border-radius:3px;
}
.my-info-panel .avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  position: absolute;
  left: 7px;
  top: 7px;
}
.my-info-panel .info {
  padding: 8px 20px;
  margin-top: 4px;
  height: 50px;
  padding-left: 63px;
}
.my-info-panel .info p {
  color: #aaa;
  font-size: 12px;
}
.my-info-panel .system-btns {
  font-size: 0;
  display: inline-block;
  height: 70px;
}
.my-info-panel .system-btns .function-btn {
  box-sizing: border-box;
  height: 100%;
  float: left;
  background: none;
  text-align: center;
  outline: none;
  margin: 0;
  padding: 10px 15px;
  cursor: pointer;
  color: #17a6f0;
}
.my-info-panel .system-btns .function-btn .func-btn-icon {
  font-size: 24px;
  display: block;
}
.my-info-panel .system-btns .function-btn .func-btn-label {
  font-size: 13px;
  line-height: 18px;
  color: #aaa;
}
.my-info-panel .system-btns .function-btn:hover {
  background: #f3f4f6;
  text-decoration: none;
}
.my-info-panel .system-btns .function-btn.disabled {
  color: #aaa;
  cursor: not-allowed;
}
.my-info-panel .system-btns .function-btn.disabled:hover {
  background: none!important;
  border-color: transparent!important;
}
.tran-area {
	position:absolute;
	top: 70px;
	display: none;
	right: 10px;
	z-index: 10;
	background: #fff;
	padding: 15px;
	border: 1px solid #e1e3e5;
	width:300px;
	border-radius: 2px;
	min-height: 460px;
	border-top: none
}
.chat-record ul{
	font-size: 13px;
	list-style: none;
	padding-left: 0px;
	color:#555
}
.chat-record ul .msg-box{
	margin-top: 15px;
	position: relative;
}
.chat-record .msg-box div{
	padding: 5px 10px;
	border: 1px solid #e1e3e5;
	display: table;
	border-radius: 2px;
	position: relative;
}

.chat-record .msg-box .arrow,.chat-record .msg-box .arrow:after{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
}

.chat-record  .msg-reply .arrow{
    left:-7px;
    top:7px;
    border-top:8px solid transparent; 
    border-right: 8px solid #e1e3e5; 
    border-bottom: 8px solid transparent; 
}

.chat-record  .msg-reply .arrow:after{
	top: -7px;
	content: "";
	margin-left:1px;
    border-top:7px solid transparent; 
    border-right: 8px solid #fff; 
    border-bottom: 8px solid transparent; 
    z-index:10
}

.chat-record  .msg-send .arrow{
    right:-7px;
    top:7px;
    border-top:8px solid transparent; 
    border-left: 8px solid #5bc0de; 
    border-bottom: 8px solid transparent; 
}

.chat-record  .msg-send div{
	float: right;
	background-color: #5bc0de;
    border: 1px solid #5bc0de;
}
.tran-area .collapse-btn{
	height: 20px;
	background-color: #f3f4f6;
	border-top:1px solid #e1e3e5;
	margin-left: -15px;
	margin-right: -15px;
	position: absolute;
	bottom: 0px;
	width: 100%;
	text-align: center;
	cursor: pointer;
}
/* 闁哄啫鐖煎Λ鎸庢姜閿燂拷 */
.timeline-box {
  background-color: #fff;
}
.timeline-box .timeline-year {
  position: relative;
}
.timeline-box .timeline-year .timeline-header {
  padding: 10px 15px;
  font-size: 14px;
  line-height: 20px;
  border-bottom: 1px solid #ddd;
}
.timeline-box .timeline-year .timeline-content {
  position: relative;
  border-bottom: 1px solid #ddd;
  overflow: hidden;
}
.timeline-box .timeline-year .timeline-content:after {
  content: '';
  background: #dde7f0;
  width: 2px;
  height: 100%;
  left: 72px;
  top: 30px;
  position: absolute;
}
.timeline-box .timeline-year .timeline-content .timeline-date {
  position: absolute;
  top: 30px;
  left: 15px;
  font-weight: normal;
}
.timeline-box .timeline-year .timeline-content .timeline-date span{
	display: block;
}
.timeline-box .timeline-year .timeline-content .timeline-date span.date{
	color: #666
}
.timeline-box .timeline-year .timeline-content .timeline-date span.time{
	color: #999;
	font-size: 12px;
	padding-left: 4px
}
.timeline-box .timeline-year .timeline-content .timeline-month {
  position: relative;
  padding: 20px 15px 0 100px;
}
.timeline-box .timeline-year .timeline-content .timeline-month:after {
  content: '';
  background: #0e8eef;
  height: 12px;
  width: 12px;
  border: 3px solid #fff;
  border-radius: 50%;
  left: 67px;
  top: 32px;
  z-index: 2;
  position: absolute;
}
.timeline-box .timeline-year .timeline-content .timeline-month:before {
  content: '';
  background: #0e8eef;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  left: 65px;
  top: 30px;
  z-index: 2;
  position: absolute;
}
.timeline-box .timeline-year .timeline-content .timeline {
  list-style: none;
  margin: 0;
  padding: 0;
}
.timeline-box .timeline-year .timeline-content .timeline li {
  background-color: #f6f6f6;
  border-radius: 5px;
  position: relative;
  padding: 10px;
  margin-bottom: 15px;
}
.timeline-box .timeline-year .timeline-content .timeline li:after {
  content: '';
  position: absolute;
  left: -20px;
  top: 13px;
  width: 0;
  height: 0;
  border-style: dashed solid dashed dashed   ;
  border-color: transparent #f6f6f6 transparent transparent   ;
  overflow: hidden;
  border-width: 10px;
}
/* 闂傚牄鍨哄姗�宕楅幎鑺ワ紨 */
/*.panel-closebtn {
  position: absolute;
  width: 20px;
  height: 40px;
  top: 100px;
  right: 0;
  text-decoration: none;
  display: block;
  background: url(../images/panel_close.png) no-repeat center;
  z-index: 2;
}
.panel-closebtn.open {
  background: url(../images/panel_open.png) no-repeat center;
}*/
/* tab闂傚牄鍨哄锟� */
/* 闁瑰瓨鍨瑰▓鎴濐啅閵夈倗绋婇柛娆欐嫹--闁兼寧绮屽畷锟� */
.item-row {
  margin-bottom:20px
}
.contents-row {
  background-color: #f5f6f7;
}
.menu-panel .menu-tabs {
  list-style: none;
  padding: 0;
  text-align: center;
  position: relative;
  width: 1170px;
  margin: auto;
  overflow: hidden;
}
.menu-panel .menu-tabs .menu-tab {
  float: left;
  padding: 15px 39px;
  width: 20%;
  box-sizing: border-box;
  position: relative;
}
.menu-panel .menu-tabs .menu-tab.cur:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: dashed  dashed solid dashed   ;
  border-color: transparent transparent #f5f6f7 transparent   ;
  overflow: hidden;
  border-width: 10px;
  bottom: 0;
  left: 50%;
  margin-left: -7px;
}
.menu-panel .menu-tabs .menu-tab a {
  display: block;
  text-decoration: none;
  color: #373d41;
}
.menu-panel .menu-tabs .menu-tab .menu-tab-image {
  width: 50px;
  height: 50px;
  margin: auto;
  margin-bottom: 15px;
  background-repeat: no-repeat;
  background-size: contain;
}
.menu-panel .menu-tabs .menu-tab .menu-tab-image img {
  display: block;
  width: 100%;
  height: 100%;
}
.menu-panel .menu-tabs .menu-tab .menu-tab-text {
  font-size: 14px;
  line-height: 1.5;
}
.menu-panel .menu-tabs-contents {
  list-style: none;
  position: relative;
  width: 990px;
  margin: 0;
  padding: 0;
  margin: 0 auto;
  overflow: hidden;
}
.menu-panel .menu-tabs-contents .menu-tabs-content {
  padding: 20px 0;
  display: none;
  transition: all 0.3s ease-in-out;
}
.menu-panel .menu-tabs-contents .menu-tabs-content:after {
  content: '';
  display: block;
  clear: both;
}
.menu-panel .menu-tabs-contents .menu-tabs-content.cur {
  display: block;
  transition: all 0.3s ease-in-out;
}
.menu-panel .menu-tabs-contents .menu-tabs-content .product-item {
  text-decoration: none;
  width: 300px;
  padding: 12px 20px;
  margin-left: 10px;
  margin-right: 25px;
  box-sizing: border-box;
  float: left;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease-in-out;
  font-size: 14px;
  color: #373d41;
  line-height: 24px;
}
.menu-panel .menu-tabs-contents .menu-tabs-content .product-item:nth-child(3n) {
  margin-right: 0;
}

.menu-panel .menu-tabs-contents .menu-tabs-content .product-item:hover {
  background: #fff;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.15);
}

.menu-panel .menu-tabs-contents .menu-tabs-content .product-item .menu-level-3{
	padding-left:0
}

.menu-panel .menu-tabs-contents .menu-tabs-content .product-item .menu-level-3 li{
	list-style:none;
	display:inline-block;
	margin-right:15px;
}
.menu-panel .menu-tabs-contents .menu-tabs-content .product-item .menu-level-3 li a{
  font-size: 12px;
  color: #9b9ea0;
}

.menu-panel .menu-tabs-contents .menu-tabs-content .product-item .menu-level-3 li a:hover{
	text-decoration:none
}

.abs-panel {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
/* 濡絾鐗犻妴澶愭閵忊剝绶� */
.index-panel {
  display: table;
  width: 100%;
  margin-bottom: 20px;
}
.index-panel > div {
  display: table-cell;
  padding-top: 15px;
  vertical-align: top;
}
.index-panel .side-left {
  width: 320px;
}
.index-panel .side-center {
  padding: 15px;
}
.index-panel .side-right {
  width: 380px;
}
/* 濞戞搩浜欏Ч澶嬬┍閳╁啩绱� */
.index-user-info {
  background: #fff;
  padding: 20px 20px 20px 140px;
  position: relative;
}
.index-user-info .useravatar {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 20px;
  left: 20px;
  border-radius: 5px;
  overflow: hidden;
}
.index-user-info .userinfo label {
  margin-bottom: 10px;
  line-height: 30px;
  font-size: 18px;
  color: #2e3133;
  font-weight: 500;
}
.index-user-info .userinfo p {
  font-size: 14px;
  color: #919699;
}
.index-menu {
  background-color: #fff;
  margin-bottom: 20px;
}
.index-menu ul {
  border-top: 1px solid #ddd;
  list-style: none;
  margin: 0;
  padding: 0;
}
.index-menu ul li {
  /*border-bottom: 1px solid #ddd;*/
  display: block;
  color: #5c6266;
  padding: 15px;
  line-height: 20px;
  font-size: 14px;
}
.index-menu ul li a{
	color: #5c6266
}
.index-menu ul li a:hover{
	text-decoration: none;
}
/* 闂侇偅姘ㄩ弫銈夋閵忊剝绶� */
.index-box {
  background-color: #fff;
}
.index-box .index-box-header {
  padding: 15px;
  line-height: 20px;
  color: #2e3133;
  font-size: 16px;
  border-bottom: 1px solid #ddd;
}
.index-box .index-box-header i.iconfont {
  font-size: 16px;
}
.index-box .index-box-header .label {
	padding-top:0.3em
}
.index-box .index-box-header .more a {
	color: #919699;
	font-size:14px
}
.index-box .index-box-header .more a:hover {
	text-decoration: none;
}
/* 缂佺媴鎷烽柡鍕偆ab闁告帒娲﹀畷锟� */
.ul-tab {
	padding-left: 0px;
	margin-bottom: 15px;
}
.ul-tab li {
	display: inline-block;
	padding-right: 15px;
	border-right: 1px solid #ccc;
	font-weight: 600;	
	cursor: pointer;
}
.ul-tab li.active{
	color: #337ab7
}
.ul-tab li:last-child {
	border-right: none;
}
.ul-tab li+li {
	padding-left: 15px
}
/* 闊浂鍋婇敓鐣屽枎閸欏棝宕ｉ敓锟� */
.list-fastenter {
  margin: 0;
  padding: 15px;
  list-style: none;
}
.list-fastenter:after {
  content: '';
  display: block;
  clear: both;
}
.list-fastenter li {
  display: block;
  width: 33.333%;
  float: left;
  margin: 10px 0;
}
.list-fastenter li > a {
  padding: 10px;
  text-decoration: none;
  color: #5c6266;
}
.list-fastenter li > a:hover {
  color: #008cee;
}
/* 閻㈩垱鐡曢～鍡涙⒒椤曪拷椤ｏ拷 */
.list-question {
  margin: 0;
  padding: 0 15px;
  list-style: none;
}
.list-question li {
  display: block;
  padding: 15px 0;
  border-bottom: 1px  dashed #ddd;
}
.list-question li > a {
  text-decoration: none;
  color: #5c6266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.list-question li > a:hover {
  color: #008cee;
}
.bigger {
  font-size: 22px;
}
.color-red {
  color: #f24949;
}
.list-callinfo {
  list-style: none;
  margin: 0;
  padding: 0;
}
.list-callinfo:after {
  content: '';
  display: block;
  clear: both;
}
.list-callinfo li {
  box-sizing: border-box;
  display: block;
  padding: 15px;
  width: 33.333%;
  float: left;
}
.list-callinfo li > .box {
  border: 1px solid #ddd;
  border-left: 5px solid #40AFFF;
  border-radius: 5px;
  color: #5c6266;
  padding: 20px 15px;
}
.list-callinfo li > .box > span {
  line-height: 30px;
}
/* 鐎规悶鍎卞畷鐔稿緞閸曨厽鍊� */
.list-gongdan {
  line-height: 40px;
  padding: 30px 0;
}
.list-gongdan span {
  display: inline-block;
}
.list-gongdan img {
  margin: 0 15px;
}
.list-gongdan i.mIcon {
  margin: 0 15px;
}
/* 闁稿浚鍓欓幉锟� */
.list-notice {
  padding: 0;
  margin: 0 20px;
  list-style: none;
}
.list-notice li {
  display: block;
  min-height: 30px;
  padding: 10px 0 ;
  border-bottom: 1px  dashed #ddd;
  position: relative;
  cursor: pointer;
}
.list-notice li:hover {
  background-color: #f5f6f7;
}
.list-notice li .notice-icon {
  position: absolute;
  top: 50%;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}
.list-notice li .notice-info {
  margin-left: 40px;
  font-size: 14px;
  line-height: 20px;
  color: #919699;
}
.list-notice li .notice-info .notice-date {
  float: right;
}
.list-notice li .notice-info p {
  color: #2e3133;
  margin-bottom: 0;
}
.list-notice li .notice-info h5 {
  margin: 5px 0;
}
/* 濠㈣泛娲ょ换鏇°亹閿燂拷 */
.list-memorandum {
  padding: 0;
  margin: 0 20px;
  list-style: none;
}
.list-memorandum li {
  display: block;
  min-height: 20px;
  padding: 10px 0 ;
  border-bottom: 1px  dashed #ddd;
  position: relative;
  cursor: pointer;
}
.list-memorandum li:hover {
  background-color: #f5f6f7;
}
.list-memorandum li .remove-icon{
	display:none;
	float:right;
	margin-left:10px
}
.list-memorandum li:hover .remove-icon{
	display:inline
}
.list-memorandum li a {
  font-size: 14px;
  line-height: 20px;
  color: #919699;
}
.list-memorandum li a:after {
  content: '';
  display: block;
  clear: both;
}
.list-memorandum li a ._title {
  float: left;
}
.list-memorandum li a .notice-date {
  float: right;
}
.w-33 {
  width: 33.33333% !important;
}
.w-25 {
  width: 25% !important;
}
.w-40 {
  width: 40% !important;
}
.w-100 {
  width: 100% !important;
}
/* 闁硅鎷烽柤宕囨櫕缁拷 */
.m-panel {
  border: 1px solid #ddd;
}
.m-panel .m-panel-header {
  background-color: #f3f4f6;
  color: #333;
  padding: 8px 10px;
  line-height: 20px;
}
.m-panel .m-panel-header .panel-title {
  font-weight: bold;
}
.list-tabel {
  margin: 15px 0;
  padding: 0;
  display: table;
  table-layout: fixed;
  width: 100%;
}
.list-tabel li {
  display: table-cell;
  border-right: 1px solid #ccc;
}
.list-tabel li p {
  margin: 0;
}
.list-tabel li font {
  margin-right: 3px;
}
.list-tabel li:nth-last-child(1) {
  border-right: 0;
}
/* 閺夌儐鍓涗簺闁圭顦甸幐锟� */
.btn-tran {
  background: url(images/tran.png) no-repeat center !important;
  width: 50px;
  height: 60px;
}
.btn-tran:hover,
.btn-tran.cur {
  background: url(images/tran_a.png) no-repeat center !important;
}
.portal-body,
.portal-body body {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}
.portal-body body {
  background-color: #f2f4f5;
  min-width: 1280px;
}
.portal-panel {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.portal-panel .portal-header,
.portal-panel .portal-sidebar,
.portal-panel .portal-main {
  position: absolute;
}
.portal-panel .portal-header {
  z-index: 5;
  top: 0;
  left: 50px;
  height: 50px;
  right: 0;
  background-color: #fff;
}
.portal-panel .portal-header:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #e1e4e5;
}
.portal-panel .portal-header .logo {
  float: left;
  height: 100%;
}
.portal-panel .portal-header .logo img {
  height: 100%;
}
.portal-panel .portal-sidebar {
  left: 0;
  top: 0;
  bottom: 0;
  width: 50px;
  background-color: #0092d8;
  background-image: -moz-linear-gradient(90deg, #0092d8 0%, #00aeff 100%);
  background-image: -webkit-linear-gradient(90deg, #0092d8 0%, #00aeff 100%);
  background-image: -ms-linear-gradient(90deg, #0092d8 0%, #00aeff 100%);
}
.portal-panel .portal-sidebar i.iconfont {
  font-size: 20px;
}
.portal-panel .portal-sidebar .sidebar-userinfo {
  height: 50px;
}
.portal-panel .portal-main {
  left: 50px;
  top: 50px;
  right: 0;
  bottom: 0;
}
.portal-panel .navbar-left-nav {
  float: left;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  letter-spacing: 1px;
}
.portal-panel .navbar-right-nav {
  float: right;
  padding: 10px 20px;
}
.portal-panel .navbar-right-nav > ul {
  list-style: none;
  font-size: 0;
  margin: 0;
  padding: 0;
}
.portal-panel .navbar-right-nav > ul > li {
  padding: 0 10px;
  display: inline-block;
  font-size: 14px;
  color: #fff;
  line-height: 20px;
}
.portal-panel .navbar-right-nav > ul > li > a {
  color: #fff;
  text-decoration: none;
}
.portal-panel .navbar-userinfo {
  padding-left: 10px;
}
.portal-panel .user-avatar {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid #ddd;
}
.module-group {
  position: relative;
  width: 100%;
  height: 100%;
}
.module-group .module {
  width: 100%;
  height: 100%;
  display: none;
}
.module-group .module .page-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}
.module-group .module.cur {
  display: block;
}
.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
}
.sidebar-nav > li {
  display: block;
  text-align: center;
  position: relative;
}
.sidebar-nav > li:hover {
  background-color: #00aeff;
  color: #fff;
}
.sidebar-nav > li:hover > a {
  color: #fff;
}
.sidebar-nav > li.cur {
  background-color: rgba(255, 255, 255, 0.3);
}
.sidebar-nav > li.cur > a {
  color: #fff;
}
.sidebar-nav > li > a {
  color: #fff;
  display: block;
  line-height: 50px;
  text-decoration: none;
}

.sidebar-nav > li > a 
.sidebar-nav > li > a:hover {
  color: #fff;
}
.hasChild {
  position: relative;
}
.hasChild:hover > .nav-children {
  display: block;
}
.hasChild .nav-children {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 100%;
  z-index: 2;
  border: 1px solid #ccc;
  width: 150px;
  display: none;
}
.hasChild .nav-children li {
  background: #fff;
  display: block;
  padding: 5px 10px;
  line-height: 20px;
  font-size: 12px;
  color: #333;
  border-bottom: 1px solid #f2f2f2;
}
.hasChild .nav-children li:hover {
  background-color: #e6ecf2;
}
.hasChild .nav-children li a {
  line-height: 20px;
}
.hasChild .nav-children li a:hover {
  color: #666;
}
.pages-group-tabs-box {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.pages-group-tabs-box .pages-group-tabs-header {
  position: absolute;
  width: 100%;
  height: 35px;
  top: 0;
  left: 0;
  border-bottom: 1px solid #e1e4e5;
  z-index: 2;
  background-color: #fff;
}
.pages-group-tabs-box .pages-group-tabs-header .tabs-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
}
.pages-group-tabs-box .pages-group-tabs-content {
  position: absolute;
  top: 35px;
  left: 0;
  width: 100%;
  bottom: 0;
}
.pages-group-tabs-box .page-container-list,
.pages-group-tabs-box .page-tab-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.pages-group-tabs-box .page-tab-list {
  font-size: 0;
  position: absolute;
  top: 0;
  overflow: hidden;
}
.pages-group-tabs-box .page-tab-list li {
  font-size: 14px;
  display: inline-block;
  float: left;
  position: relative;
  padding: 3px 25px 3px 15px;
  line-height: 29px;
  box-sizing: border-box;
  cursor: pointer;
  white-space: nowrap;
  color: #919799;
  border-right: 1px solid #e1e4e5;
}
.pages-group-tabs-box .page-tab-list li .tabs-close {
  position: absolute;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  right: 5px;
  top: 50%;
  margin-top: -10px;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.pages-group-tabs-box .page-tab-list li .tabs-close:after {
  content: '闁告鎷�';
  font-style: normal;
  font-size: 12px;
  font-family: 'song';
}
.pages-group-tabs-box .page-tab-list li .tabs-close:hover {
  color: #ed4c4c;
}
.pages-group-tabs-box .page-tab-list li:hover {
  color: #5c6366;
  background-color: #f2f4f5;
}
.pages-group-tabs-box .page-tab-list li.active {
  color: #5c6366;
  background-color: #f2f4f5;
}
.pages-group-tabs-box .page-container-list {
  position: relative;
  width: 100%;
  height: 100%;
}
.pages-group-tabs-box .page-container-list li {
  display: none;
  position: relative;
  width: 100%;
  height: 100%;
}
.pages-group-tabs-box .page-container-list li iframe {
  width: 100%;
  height: 100%;
  border: 0;
}
.pages-group-tabs-box .page-container-list li.active {
  display: block;
}
/* ccbar闁哄秴鍢茬槐锟� */
.ccbar-box {
  height: 30px;
  display: inline-block;
}
.ccbar-box > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.ccbar-box > ul > li {
  display: inline-block;
  position: relative;
  padding: 0 5px;
}
.ccbar-box .ccbar-select {
  border: 0;
  outline: none;
  background: none;
}
.ccbar-box .ccbar-btn {
  display: inline-block;
  line-height: 28px;
  height: 28px;
  text-decoration: none;
  padding: 0 12px;
  margin-left: 5px;
  color: #919799;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -ms-border-radius: 12px;
  -o-border-radius: 12px;
  border-radius: 12px;
}
.ccbar-box .ccbar-btn.disabled {
  cursor: not-allowed;
}
.ccbar-box .ccbar-btn.btn-makecall,
.ccbar-box .ccbar-btn.btn-clearcall {
  color: #fff;
}
.ccbar-box .ccbar-btn.btn-makecall {
  background-color: #20d99b;
}
.ccbar-box .ccbar-btn.btn-clearcall {
  background-color: #ff6e61;
}
.ccbar-box .ccbar-btn.btn-agentnotready {
  background-color: #ff6e61;
  color: #fff;
}
.ccbar-box .ccbar-btn.btn-agentready {
  background-color: #20d99b;
  color: #fff;
}
.ccbar-box .ccbar-btn.btn-workready {
  color: #fff;
  background-color: #20d99b;
}
.ccbar-box .ccbar-label {
  line-height: 30px;
  margin-bottom: 0;
  color: #919799;
}
.ccbar-box .ccbar-label span {
  color: #0092d8;
}
.ccbar-box .ccbar-label span.cur-agentstate {
  display: inline-block;
  min-width: 4em;
}
.ccbar-box .ccbar-call {
  line-height: 28px;
  border: 1px solid #e1e4e5;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  border-radius: 10px;
}
.ccbar-box .ccbar-call .iconfont {
  color: #33beff;
}
.ccbar-box .ccbar-clock {
  float: right;
  color: #919799;
  margin-right: 5px;
  font-size: 12px;
}
.ccbar-box .ccbar-split {
  padding-right: 10px;
}
.ccbar-box .ccbar-split:after {
  position: absolute;
  background-color: #e1e4e5;
  content: '';
  right: 0;
  top: 50%;
  width: 1px;
  opacity: 0.9;
  height: 20px;
  margin-top: -10px;
}
.ccbar-box .ccbar-form {
  width: 190px;
}
.ccbar-box .ccbar-form input {
  border: 0;
  outline: none;
  background: none;
  width: 90px;
  line-height: 28px;
  height: 28px;
  color: #5c6366;
  font-size: 14px;
}
.ccbar-box .ccbar-department {
  width: 160px;
  position: relative;
}
.ccbar-box .ccbar-department > a {
  text-decoration: none;
}
.ccbar-box .ccbar-department .cur-department {
  color: #5c6366;
  font-size: 14px;
  display: inline-block;
  width: 110px;
}
.ccbar-box .ccbar-department .caret {
  position: absolute;
  right: 6px;
  top: 50%;
  margin-top: -2px;
}
.ccbar-box .ccbar-form,
.ccbar-box .ccbar-department {
  padding: 0 5px;
  height: 30px;
  box-sizing: border-box;
}

.new-tabs {
	padding-left: 0px;
	list-style:none;
}
.new-tabs li {
	float:left;
	width:33.333%;
	padding: 10px 26px;
	background:#f5f6f7
}
.new-tabs li a{
	color: #555;
	font-weight: 600
}
.new-tabs li a:hover{
	text-decoration: none;
}
.new-tabs li.active{
	background:#fff
}

.new-tab-content .new-tab-pane{
	display:none;
}

.new-tab-content .new-tab-pane.active{
	display:block;
}

/* 濞撴皜鍕焿闂佸墽鍋撶敮鎾閵忊剝绶� */
.sidebar-links-box {
  position: absolute;
  left: 100%;
  background: #fff;
  border: 1px solid #f2f2f2;
}
.hasMeunBox:hover .list-column-main {
  display: block;
}
.list-column-main {
  background-color: #fff;
  border-top: #e9e9e9 1px solid;
  border-right: #e9e9e9 1px solid;
  border-bottom: #e9e9e9 1px solid;
  padding: 8px 16px 8px 16px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  position: absolute;
  min-width: 460px;
  left: 100%;
  text-align: left;
  top: 0px;
  z-index: 3;
  display:none
}

.list-column-main .list-column .level-1-menu{
	color:#222
}
.list-column-main .list-column{
	margin-bottom:10px
}
.list-column-main .list-column .menu{
	padding:0;
	margin:4px 0 0
}
.list-column-main .list-column a:hover{
	text-decoration:none
}
.list-column .menu li{
	display:inline-block;
	list-style:none;
	padding:0 0 0 10px
}
.list-column .menu li:first-child{
	padding:0
}
.list-column .menu .level-2-menu{
	color:#999;
}
.list-column .level-2-menu:after{
	content:">";
	display:inline-block;
	margin-left:4px;
}
.list-column .level-3-menu:after{
	content:"|";
	display:inline-block;
	margin-left:10px;
	color:#999;
	margin-top: -2px;
}
.list-column .level-3-menu:last-child:after{
	display:none
}
/* tabs */
.contabs {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1980;
  display: none;
  height: 40px;
  line-height: 40px;
  background: #fff;
  border-bottom: solid 1px #e4eaec;
}
.contabs.contabs-open {
  display: block;
}
.contabs .dropdown-toggle:hover {
  background-color: #f2f2f2!important;
}
.contabs .dropdown-toggle:hover .caret {
  color: #333;
}
.contabs .open .dropdown-toggle {
  background-color: #f2f2f2!important;
}
.contabs .open .dropdown-toggle .caret {
  color: #333;
}
.contabs .btn {
  height: 38px;
  background: #fff;
  border: none;
  border-radius: 0;
}
.contabs .contabs-scroll {
  position: relative;
  width: calc(100% - 39px);
  height: 40px;
  overflow: hidden;
}
.contabs .contabs-scroll .con-tabs {
  position: absolute;
  width: auto;
}
.contabs .contabs-scroll .con-tabs > li {
  float: left;
  width: 9.5em;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.contabs .contabs-scroll .con-tabs > li:after {
  content: '';
  position: absolute;
  width: 1px;
  top: 0;
  right: 0;
  height: 100%;
  background: #f2f2f2;
}
.contabs .contabs-scroll .con-tabs > li > a {
  position: relative;
  display: block;
  padding: 0 25px 0 9px;
  overflow: hidden;
  font-size: 13px;
  color: #76838f;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contabs .contabs-scroll .con-tabs > li.noclose >a {
  padding: 0 9px
}

.contabs .contabs-scroll .con-tabs > li > a > .icon {
  position: absolute;
  top: 13px;
  right: 9px;
  font-size: 14px;
  line-height: 14px;
}
.contabs .contabs-scroll .con-tabs > li > a > .icon:before {
  color: #a3afb7;
}
.contabs .contabs-scroll .con-tabs > li > a > .icon:hover:before {
  color: #fff;
  background: #f96868;
  border-radius: 50%;
}
.contabs .contabs-scroll .con-tabs > li.active {
  background: #f1f4f5;
  position: relative;
}
.contabs .contabs-scroll .con-tabs > li.active:before{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  z-index: 2;
  background-color: #0092d8;
}
.contabs .contabs-scroll .con-tabs > li.active > a {
  background: 0 0!important;
  border-right: solid 1px #e4eaec;
  border-left: solid 1px #e4eaec;
      color: #333;  
    font-weight: 500;
}
.contabs .btn + .contabs-scroll.contabs-scroll {
  width: calc(100% - 105px);
}
.loader-page {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  display: none;
}
.loader-page.active {
  display: block;
}
.loader-page iframe {
  border: 0;
  width: 100%;
  height: 100%;
}
.loader-page,
.module {
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  -o-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.loader-page.cur,
.module.cur,
.loader-page.active,
.module.active {
  opacity: 100;
  filter: alpha(opacity=100);
}
/**
	濞戞挸顑嗘刊娲嚕濠婂啫绀嬮柡宥呭槻缁憋拷
*/
.dropdown-icon > li > .addon {
  position: absolute;
  width: 2em;
  top: 0;
  margin-top: -0.5em;
  text-align: center;
  left: 0;
  z-index: 2;
  vertical-align: middle;
  height: 100%;
}
.dropdown-icon > li {
  position: relative;
  color: #888;
  cursor: pointer;
}
.dropdown-icon::after {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 2em;
  height: 100%;
  border-right: 1px solid #eee;
  background-color: #f8f8f8;
  z-index: -1;
}
.dropdown-icon > li > a {
  color: #333;
  padding-left: 3em!important;
  font-size: 13px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* scrioll */
#sidebar-scroll-wrap .scroll-wrapper {
  left: 0;
  width: 100%;
  height: calc(100% - 100px);
  top: 50px;
}
.scroll-wrapper {
  position: absolute;
}
.scroll-wrapper .scroller {
  position: absolute;
  z-index: 1;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  width: 100%;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-text-size-adjust: none;
  -moz-text-size-adjust: none;
  -ms-text-size-adjust: none;
  -o-text-size-adjust: none;
  text-size-adjust: none;
}