package com.yunqu.yc.agent.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;


/**
 * 操作流水
 * <AUTHOR>
 *
 */
public class LogUtils {
	private static Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME + "-addLog");
	public static final int EVENT_TYPE_ORDER = 70;//工单
	public static final int EVENT_TYPE_CUST = 80;//客户资料
	public static final int EVENT_TYPE_CALL = 90;//呼叫记录


	public static final int OPER_TYPE_ADD = 10;
	public static final int OPER_TYPE_IMPORT = 11;
	public static final int OPER_TYPE_EXPORT = 12;
	public static final int OPER_TYPE_EDIT = 20;
	public static final int OPER_TYPE_DEL = 40;

	public static final String LOG_SERVICE_ID = "NODE_ADD_LOG_SERVICE";
	private LogUtils(){
	}

	/**
	 * 针对不开启流程记录的日志
	 * @param entId
	 * @param entName
	 * @param eventTypeUser
	 * @param oper
	 * @param userId
	 * @param userName
	 * @param desc
	 */
	public static void log(String entId,String entName,int eventTypeUser,int oper,String userId,String userName,String desc,String busiId){
		LogUtils.log(entId,entName,eventTypeUser,oper,userId,userName,desc,"","", busiId);
	}

	/**
	 * 针对开启流程记录的日志
	 * @param entId
	 * @param entName
	 * @param eventTypeUser
	 * @param operType
	 * @param userId
	 * @param userName
	 * @param desc
	 * @param isFlow
	 * @param flowNumber
	 */
	public static void log(String entId,String entName,int eventTypeUser,int operType,String userId,String userName,String desc,String isFlow,String flowNumber,String busiId){
		if(!"1".equals(AppContext.getContext("yc-base").getProperty("ENT_LOG", "0"))){//是否开启记录
			return;
		}
		try {
			JSONObject params = new JSONObject();
			params.put("entId",entId);
			params.put("entName",entName);
			params.put("eventTypeUser",eventTypeUser);
			params.put("operType",operType);
			params.put("userId",userId);
			params.put("userName",userName);
			params.put("desc",desc);
			params.put("isFlow",isFlow);
			params.put("flowId",flowNumber);
			params.put("busiId",busiId);
			IService service = ServiceContext.getService(LOG_SERVICE_ID);
			JSONObject invoke = service.invoke(params);
		}catch (Exception e){
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}

	}

	public static void logCallEnt(String entId,int oper,String desc,UserPrincipal principal){
		LogUtils.logEnt(entId,  LogUtils.EVENT_TYPE_CALL, oper, desc, principal);
	}
	public static void logCustEnt(String entId,int oper,String desc,UserPrincipal principal){
		LogUtils.logEnt(entId,  LogUtils.EVENT_TYPE_CUST, oper, desc, principal);
	}
	public static void logOrderEnt(String entId,int oper,String desc,UserPrincipal principal){
		LogUtils.logEnt(entId,  LogUtils.EVENT_TYPE_ORDER, oper, desc, principal);
	}

	public static void logEnt(String entId,int type,int oper,String desc,UserPrincipal principal){
		YCUserPrincipal user = (YCUserPrincipal) principal;
		String entName = getEntName(entId);
		LogUtils.log(entId, entName, type, oper, user.getUserId(), user.getLoginAcct(), desc,user.getBusiId());
	}

	/**
	 * 获取企业名称
	 * @param entId
	 * @return
	 */
	protected static String getEntName(String entId){
		String entName = CacheUtil.get("ENT_NAME_"+entId);
		if(StringUtils.isBlank(entName)){
			try {
				entId = StringUtils.parseInt(entId)+"";//为了防止企业ID不是数字
				entName = QueryFactory.getQuery(entId).queryForString("select ENT_NAME from CC_ENT where ENT_ID = ?", new Object[]{entId});
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
		if(StringUtils.isNotBlank(entName)){
			CacheUtil.put("ENT_NAME_"+entId, entName);
		}
		return entName;
	}
	/**
	 create table cc_ent_log(
	 LOG_ID varchar(32) NOT NULL COMMENT '流水ID',
	 ENT_ID varchar(32) NOT NULL COMMENT '企业ID',
	 ENT_NAME varchar(100) COMMENT '企业名称',
	 EVENT_TYPE int COMMENT '修改类型，10：企业信息，20：坐席信息，30：话机信息，',
	 OPER_TYPE int COMMENT '操作类型，10：新增，20：修改，30：查询，40：删除，21：销户，22:暂停，23：恢复正常，24:解锁',
	 USER_ID varchar(32) NOT NULL COMMENT '操作人ID',
	 USERNAME VARCHAR(50) COMMENT '操作人姓名',
	 LOG_DESC varchar(500) COMMENT '操作详情',
	 CREATE_DATE int DEFAULT NULL COMMENT '创建日期',
	 CREATE_TIME varchar(19) DEFAULT NULL COMMENT '创建时间',
	 PRIMARY KEY (LOG_ID),
	 KEY IDX_cc_ent_log_1(ENT_ID,CREATE_DATE),
	 KEY IDX_cc_ent_log_2(USER_ID,EVENT_TYPE)
	 )


	 ALTER table cc_ent_log add IS_FLOW VARCHAR(10) default 'N' COMMENT '是否需要流程';
	 ALTER table cc_ent_log add FLOW_NUMBER int COMMENT '流程顺序1-N';
	 */
	
}
