package com.yunqu.yc.agent.dao.order;

import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentLogger;
import com.yunqu.yc.agent.base.AppDaoContext;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.OrderTempFactory;
import com.yunqu.yc.agent.service.OrderTempService;

/**
 * <AUTHOR>
 */
@WebObject(name = "agentOrder")
public class OrderDao extends AppDaoContext {

	/**
	 * 待处理工单
	 * 
	 * @return
	 */
	@WebControl(name = "onDealList", type = Types.LIST)
	public JSONObject onDealList() {


		// 当前登录人ID
		String userId = this.getUserPrincipal().getUserId();
		String tempId = this.param.getString("tempId");
		String custPhone = this.param.getString("custPhone");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		String limitDate = param.getString("limitDate");
		if(StringUtils.isNotBlank(limitDate) && StringUtils.isBlank(startDate)){
			String[] split = limitDate.split("~");
			startDate = StringUtils.trim(split[0]);
			endDate = split.length>1?StringUtils.trim(split[1]):"";
		}
		if(StringUtils.isBlank(startDate)){
			startDate = "1970-01-01 00:00:00";
		}
		EasySQL sql = getEasySQL("");

		// 查找符合当前登录人的[处理人 20]类型的工单
		sql.append("select t1.ORDER_ID,t1.CUST_PHONE,t1.CUST_NAME,t1.ORDER_TIME,t2.ORDER_STEP_NAME,t3.TMP_NAME, t1.URGENCY_DEGREE from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER t1,"));
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" where t1.ORDER_STATE = 1 "); // 处理中的工单
		sql.append(" and t1.HANDLER_TYTPE=20 "); // 经办类型为 处理人
		sql.append(" and t1.ORDER_STEP_ID=t2.ORDER_STEP_ID ");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(userId, " and t1.HANDLER_ID=?");// 处理人匹配
		sql.append(tempId, " and t1.TMP_ID=?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql.appendRLike(custPhone, " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		sql.append(endDate, " and t1.ORDER_TIME <= ?");
		sql.append(param.getInteger("chnlType"), "and t1.CHNL_TYPE = ?");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" union ");
		// 查找符合当前登录人的[经办组 30]类型的工单
		sql.append("select t1.ORDER_ID,t1.CUST_PHONE,t1.CUST_NAME,t1.ORDER_TIME,t3.ORDER_STEP_NAME,t4.TMP_NAME,t1.URGENCY_DEGREE from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1,"));
		sql.append(getTableName("CC_AGENT_ORDER_GROUPUSER t2,"));
		sql.append(getTableName("CC_AGENT_ORDER_STEP t3"));
		sql.append(" where t1.ORDER_STATE = 1 "); // 处理中的工单
		sql.append(" and t1.HANDLER_TYTPE=30 "); // 动作经办类型为 经办组
		sql.append(" and t1.ORDER_STEP_ID=t3.ORDER_STEP_ID ");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(" and t1.HANDLER_ID = t2.ORDER_GROUP_ID");
//		sql.append(" and ((t1.HANDLER_ID = t2.ORDER_GROUP_ID and (t1.RESERVE1 is null or t1.RESERVE1=''))");
//		sql.append(userId," or (t1.RESERVE1 = ?))");
		sql.append(userId, " and t2.USER_ID=?");// 处理人匹配
		sql.append(tempId, " and t1.TMP_ID=?");
		sql.append(" and t1.TMP_ID = t4.TMP_ID");
		sql.appendRLike(custPhone, " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		sql.append(endDate, " and t1.ORDER_TIME <= ?");
		sql.append(param.getInteger("chnlType"), "and t1.CHNL_TYPE = ?");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" union ");
		// 查找符合当前登录人的[角色 10]类型的工单
		sql.append("select t1.ORDER_ID,t1.CUST_PHONE,t1.CUST_NAME,t1.ORDER_TIME,t3.ORDER_STEP_NAME,t4.TMP_NAME,t1.URGENCY_DEGREE from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1,"));
		sql.append(getTableName("CC_AGENT_ORDER_GROUPUSER t2,"));
		sql.append(getTableName("CC_AGENT_ORDER_STEP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER_GROUP_REF t5"));
		sql.append(" where t1.ORDER_STATE = 1 "); // 处理中的工单
		sql.append(" and t1.HANDLER_TYTPE=10 "); // 动作经办类型为 经办组
		sql.append(" and t1.ORDER_STEP_ID=t3.ORDER_STEP_ID ");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(" and t1.HANDLER_ID = t2.ORDER_ROLE_ID");
//		sql.append(" and ((t1.HANDLER_ID = t2.ORDER_ROLE_ID and (t1.RESERVE1 is null or t1.RESERVE1=''))");
//		sql.append(userId," or (t1.RESERVE1 = ?))");
		//sql.append(" and t1.HANDLER_ID = t2.ORDER_ROLE_ID");// 角色匹配
		sql.append(" and t5.ORDER_GROUP_ID = t2.ORDER_GROUP_ID"); // 经办组匹配
		sql.append("  and t5.TMP_ID=t4.TMP_ID"); // 经办组匹配
		sql.append(userId, " and t2.USER_ID=?");// 处理人匹配
		sql.append(tempId, " and t1.TMP_ID=?");
		sql.append(" and t1.TMP_ID = t4.TMP_ID");
		sql.appendRLike(custPhone, " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.appendLike(param.getString("reqCode"), " and t1.REQ_CODE like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		sql.append(endDate, " and t1.ORDER_TIME <= ?");
		sql.append(param.getInteger("chnlType"), "and t1.CHNL_TYPE = ?");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" order by ORDER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 查询工单
	 * 
	 * @return
	 */
	@WebControl(name = "list", type = Types.LIST)
	public JSONObject list() {

		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = getEasySQL("select "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE," +
				"t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_CONTENT,t1.ORDER_TIME,t1.ORDER_STATE,t1.ORDER_LOG,t1.BUSI_ORDER_ID,t1.ORDER_ID," +
				"t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t1.HANDLE_TIME, t1.URGENCY_DEGREE from ");
		String orderRange = this.param.getString("orderRange");//话单范围
		if ("1".equals(orderRange)) {
			sql.append(getTableName("CC_AGENT_ORDER_HIS t1"));
		}else{
			sql.append(getTableName("CC_AGENT_ORDER t1"));
		}
		if (this.getUserPrincipal().getRoleType() == Constants.ROLE_TYPE_MONITOR) { // 班长
			String[] skillGroupId = getSkillGroupId();
			if (skillGroupId != null) { // 判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
				sql.append(" inner join "+ getTableName("CC_SKILL_GROUP_USER")+" t2 on t1.AGENT_ID = t2.USER_ID");
				sql.append(getEntId(), "and t2.ENT_ID = ?");
				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
				if (skillGroupId.length == 1) {
					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?");
				} else {
					sql.append("and t2.SKILL_GROUP_ID in (" + StringUtils.join(skillGroupId, ",") + ")");
				}
			}
		}
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
		sql.append(param.getInteger("orderLevel"), " and t1.ORDER_LEVEL = ?");
		sql.appendRLike(param.getString("custPhone"), " and t1.CUST_PHONE like ?");
		sql.append(param.getString("startDate"), " and t1.ORDER_TIME >= ?");
		if (StringUtils.isNotBlank(param.getString("endDate"))) {
			sql.append(param.getString("endDate"), " and t1.ORDER_TIME <= ?");
		}
		sql.append(param.getInteger("chnlType"), " and t1.CHNL_TYPE = ?");
		sql.append(param.getString("urgencyDegree"), " and t1.URGENCY_DEGREE = ?");
		
		if (this.getUserPrincipal().getRoleType() == Constants.ROLE_TYPE_MONITOR) { // 班长
//			String[] skillGroupId = getSkillGroupId();
//			if (skillGroupId != null) { // 判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
//				sql.append("and t1.AGENT_ID in ( select t2.USER_ID from " + getTableName("CC_SKILL_GROUP_USER")
//						+ " t2 where 1=1");
//				sql.append(getEntId(), "and t2.ENT_ID = ?");
//				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
//				if (skillGroupId.length == 1) {
//					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?)");
//				} else {
//					sql.append("and t2.SKILL_GROUP_ID in (" + StringUtils.join(skillGroupId, ",") + "))");
//				}
//			}
		} else if (this.getUserPrincipal().getRoleType() != Constants.ROLE_TYPE_MANAGER) { // 如果不是管理员,工单经办人
//			sql.append(this.getUserId(), " and exists (select 1 from " + getTableName("CC_AGENT_ORDER_GROUPUSER t2,")
//					+ getTableName("CC_AGENT_ORDER_TMP t3")
//					+ " where t2.ORDER_GROUP_ID = t3.ORDER_GROUP_ID and t3.TMP_ID = t1.TMP_ID and t2.USER_ID = ?)");
			sql.append("and t1.ORDER_ID in (select t1.ORDER_ID from ");
			sql.append(getTableName("CC_AGENT_ORDER t1,"));
			sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
			sql.append(" where 1 = 1 "); // 处理中的工单
			sql.append(" and t1.HANDLER_TYTPE=20 "); // 经办类型为 处理人
			sql.append(" and t1.ORDER_STEP_ID=t2.ORDER_STEP_ID ");
			sql.append(getEntId(), " and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.append(this.getUserId(), " and t1.HANDLER_ID=?");// 处理人匹配
			sql.append(param.getString("startDate"), " and t1.ORDER_TIME >= ?");
			if (StringUtils.isNotBlank(param.getString("endDate"))) {
				sql.append(param.getString("endDate"), " and t1.ORDER_TIME <= ?");
			}
			sql.append(" union ");
			// 查找符合当前登录人的[经办组 30]类型的工单
			sql.append("select t1.ORDER_ID from ");
			if ("1".equals(orderRange)) {//判断是否选择了历史工单
				sql.append(getTableName("CC_AGENT_ORDER_HIS t1,"));
			}else{
				sql.append(getTableName("CC_AGENT_ORDER t1,"));
			}
			sql.append(getTableName("CC_AGENT_ORDER_GROUPUSER t2 "));
			sql.append(" where 1 = 1 "); // 处理中的工单
			sql.append(getEntId(), " and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.append(" and t1.HANDLER_ID = t2.ORDER_GROUP_ID");
			sql.append(this.getUserId(), " and t2.USER_ID=?");// 处理人匹配
			sql.append(param.getString("startDate"), " and t1.ORDER_TIME >= ?");
			if (StringUtils.isNotBlank(param.getString("endDate"))) {
				sql.append(param.getString("endDate"), " and t1.ORDER_TIME <= ?");
			}
			sql.append(" union ");
			// 查找符合当前登录人的[角色 10]类型的工单
			sql.append("select t1.ORDER_ID from ");
			if ("1".equals(orderRange)) {//判断是否选择了历史工单
				sql.append(getTableName("CC_AGENT_ORDER_HIS t1,"));
			}else{
				sql.append(getTableName("CC_AGENT_ORDER t1,"));
			}
			sql.append(getTableName("CC_AGENT_ORDER_GROUPUSER t2 "));
			sql.append(" where 1 = 1 "); // 处理中的工单
			sql.append(" and t1.HANDLER_TYTPE=10 "); // 动作经办类型为 经办组=
			sql.append(getEntId(), " and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.append(" and t1.HANDLER_ID = t2.ORDER_ROLE_ID");
			sql.append(this.getUserId(), " and t2.USER_ID=?");// 处理人匹配
			sql.append(param.getString("startDate"), " and t1.ORDER_TIME >= ?");
			if (StringUtils.isNotBlank(param.getString("endDate"))) {
				sql.append(param.getString("endDate"), " and t1.ORDER_TIME <= ?");
			}
			sql.append(" union ");
			sql.append("select t1.ORDER_ID from ");
			sql.append(getTableName("CC_AGENT_ORDER_LOG t3,"));
			if ("1".equals(orderRange)) {//判断是否选择了历史工单
				sql.append(getTableName("CC_AGENT_ORDER_HIS t1"));
			}else{
				sql.append(getTableName("CC_AGENT_ORDER t1"));
			}
			sql.append(" where 1=1");
			sql.append(getEntId(), " and t1.ENT_ID = ?");
			sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			sql.append(getUserId(), "and t3.OPER_ID = ?");
			sql.append(param.getString("startDate"), " and t1.ORDER_TIME >= ?");
			if (StringUtils.isNotBlank(param.getString("endDate"))) {
				sql.append(param.getString("endDate"), " and t1.ORDER_TIME <= ?");
			}
			sql.append( "and t1.ORDER_ID = t3.ORDER_ID)");
		}
		sql.append(	param.getInteger("orderState"), "and t1.ORDER_STATE = ?");
		sql.appendLike(param.getString("reqCode"), " and t1.REQ_CODE like ?");
		sql.append(param.getString("tempId"), "and t1.TMP_ID = ?");
		sql = this.appendSqlTempQuery(sql);

		sql.append(" order by t1.ORDER_TIME desc");
		AgentLogger.getLogger().info(sql.getSQL()+"[param]"+JSON.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 我的工單
	 * 
	 * @return
	 */
	@WebControl(name = "myList", type = Types.LIST)
	public JSONObject myList() {
		
		String limitDate = param.getString("limitDate");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		if(StringUtils.isNotBlank(limitDate) && StringUtils.isBlank(startDate)){
			String[] split = limitDate.split("~");
			startDate = StringUtils.trim(split[0]);
			endDate = split.length>1?StringUtils.trim(split[1]):"";
		}
		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = getEasySQL("select "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE,t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_CONTENT,t1.ORDER_TIME,t1.ORDER_STATE,t1.ORDER_LOG,t1.BUSI_ORDER_ID,t1.ORDER_ID,t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t2.ORDER_STEP_NAME,t3.TMP_NAME from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(getUserId(), " and t1.AGENT_ID = ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql.append(param.getInteger("orderLevel"), " and t1.ORDER_LEVEL = ?");
		sql.appendRLike(param.getString("custPhone"), " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate, " and t1.ORDER_TIME <= ?");
		}
		sql.append(	param.getInteger("orderState"), "and t1.ORDER_STATE = ?");
		sql.append(param.getString("tempId"), "and t1.TMP_ID = ?");
		sql.append(param.getInteger("chnlType"), "and t1.CHNL_TYPE = ?");
		sql.appendLike(param.getString("reqCode"), " and t1.REQ_CODE like ?");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" order by t1.ORDER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}
	

	/**
	 * 我参与的工单
	 * 
	 * @return
	 */
	@WebControl(name = "myPartList", type = Types.LIST)
	public JSONObject myPartList() {
		String limitDate = param.getString("limitDate");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		if(StringUtils.isNotBlank(limitDate) && StringUtils.isBlank(startDate)){
			String[] split = limitDate.split("~");
			startDate = StringUtils.trim(split[0]);
			endDate = split.length>1?StringUtils.trim(split[1]):"";
		}
		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = getEasySQL("select distinct "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE,t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_TIME,t1.ORDER_STATE,t1.BUSI_ORDER_ID,t1.ORDER_ID,t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t2.ORDER_STEP_NAME,t4.TMP_NAME from ");
		sql.append(getTableName("CC_AGENT_ORDER_LOG t3,"));
		sql.append(getTableName("CC_AGENT_ORDER_TMP t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(param.getInteger("orderLevel"), " and t1.ORDER_LEVEL = ?");
		sql.appendRLike(param.getString("custPhone"), " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate, " and t1.ORDER_TIME <= ?");
		}
		sql.append(	param.getInteger("orderState"), "and t1.ORDER_STATE = ?");
		sql.append(param.getString("tempId"), "and t1.TMP_ID = ?");
		sql.append(getUserId(), "and t3.OPER_ID = ?");
		sql.append( "and t1.ORDER_ID = t3.ORDER_ID");
		sql.append(param.getInteger("chnlType"), "and t1.CHNL_TYPE = ?");
		sql.append( "and t4.TMP_ID = t1.TMP_ID");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" order by t1.ORDER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}
	

	@WebControl(name = "getOrder", type = Types.TEMPLATE)
	public JSONObject getOrder() {
		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = this.getEasySQL("select "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE,t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_CONTENT,t1.ORDER_TIME,t1.ORDER_STATE,t1.ORDER_LOG,t1.BUSI_ORDER_ID,t1.ORDER_ID,t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t2.EXAM_GROUP_NAME,t3.ORDER_STEP_NAME,t3.STEP_TYPE,t4.TMP_NAME,t1.URGENCY_DEGREE from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t4,"));
		String orderRange = this.param.getString("orderRange");//话单范围
		if ("1".equals(orderRange)) {
			sql.append(getTableName("CC_AGENT_ORDER_HIS t1"));
		}else{
			sql.append(getTableName("CC_AGENT_ORDER t1"));
		}
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_GROUP t2"));
		sql.append(" on t1.ORDER_GROUP_ID = t2.ORDER_GROUP_ID ");
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t3"));
		sql.append(" on t1.ORDER_STEP_ID = t3.ORDER_STEP_ID ");
		sql.append(" where 1=1");
		sql.append(param.getString("orderId"), " and t1.ORDER_ID = ?");
		sql.append(" and t1.TMP_ID = t4.TMP_ID");
		
		JSONObject result = queryForRecord(sql.getSQL(), sql.getParams(), null);
		return result;
	}

	/**
	 * 获取暂存工单
	 * 
	 * @return
	 */
	@WebControl(name = "getTemporaryOrder", type = Types.LIST)
	public JSONObject getTemporaryOrder() {
		String limitDate = param.getString("limitDate");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		if(StringUtils.isNotBlank(limitDate) && StringUtils.isBlank(startDate)){
			String[] split = limitDate.split("~");
			startDate = StringUtils.trim(split[0]);
			endDate = split.length>1?StringUtils.trim(split[1]):"";
		}
		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = this.getEasySQL("select "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE,t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_CONTENT,t1.ORDER_TIME,t1.ORDER_STATE,t1.ORDER_LOG,t1.BUSI_ORDER_ID,t1.ORDER_ID,t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t2.ORDER_STEP_NAME,t3.TMP_NAME, t1.URGENCY_DEGREE from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER t1 force INDEX(IDX_CC_AGENT_ORDER_1)"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where t1.ORDER_STATE = 3");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(getUserId(),  " and t1.AGENT_ID = ?");
		sql.appendRLike(param.getString("custPhone"), " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		}
		sql.append(param.getString("tempId"), "and t1.TMP_ID = ?");
		sql.append(param.getInteger("chnlType"), " and t1.CHNL_TYPE = ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" order by t1.ORDER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}
	

	/**
	 * 获取我创建的已完成工单
	 * 
	 * @return
	 */
	@WebControl(name = "getMyFinishedOrder", type = Types.LIST)
	public JSONObject getMyFinishedOrder() {
		String limitDate = param.getString("limitDate");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		if(StringUtils.isNotBlank(limitDate) && StringUtils.isBlank(startDate)){
			String[] split = limitDate.split("~");
			startDate = StringUtils.trim(split[0]);
			endDate = split.length>1?StringUtils.trim(split[1]):"";
		}
		String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), "t1");
		EasySQL sql = this.getEasySQL("select DISTINCT "+filedInfo+",t1.AGENT_ID,t1.AGENT_NAME,t1.CHNL_TYPE,t1.CUST_NAME,t1.CUST_OBJ_ID,t1.CUST_PHONE,t1.TMP_ID,t1.SERIAL_ID,t1.REQ_CODE,t1.ORDER_TIME,t1.ORDER_STATE,t1.BUSI_ORDER_ID,t1.ORDER_ID,t1.HANDLER_TYTPE,t1.HANDLER_NAME,t1.ORDER_STEP_ID,t2.ORDER_STEP_NAME,t3.TMP_NAME from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER_LOG t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where t1.ORDER_STATE = 2");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		//sql.append(getUserId(),  " and t1.AGENT_ID = ?");
		sql.appendRLike(param.getString("custPhone"), " and t1.CUST_PHONE like ?");
		sql.appendLike(param.getString("custName"), " and t1.CUST_NAME like ?");
		sql.append(startDate, " and t1.ORDER_TIME >= ?");
		if (StringUtils.isNotBlank(endDate)) {
			sql.append(endDate, " and t1.ORDER_TIME <= ?");
		}
		sql.append(param.getString("tempId"), " and t1.TMP_ID = ?");
		sql.append(param.getInteger("chnlType"), " and t1.CHNL_TYPE = ?");
		sql.append( "and t1.ORDER_ID = t4.ORDER_ID");
		sql.append(getUserId(), "and t4.OPER_ID = ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql = this.appendSqlTempQuery(sql);// 添加工单模板查询
		sql.append(" order by t1.ORDER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}


	/**
	 * 操作流程
	 * 
	 * @return
	 */
	@WebControl(name = "logList", type = Types.TEMPLATE)
	public JSONObject logList() {
		JSONObject resultJson = new JSONObject();
		try {
			String orderLog = this.getQuery().queryForString(
					"select ORDER_LOG from " + getTableName("CC_AGENT_ORDER") + " where ORDER_ID = ?",
					new Object[] { param.getString("orderId") });
			if (orderLog != null) {
				JSONArray array = JSONArray.parseArray(orderLog);
				resultJson.put("total", array.size());
				resultJson.put("data", getJsonResult(array));
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return resultJson;
	}

	/**
	 * 工单排行榜
	 */
	@WebControl(name = "orderTopList", type = Types.LIST)
	public JSONObject orderTopList() {
		EasySQL sql = this.getEasySQL("select COUNT(AGENT_ID) ORDER_COUNT,AGENT_NAME OPER_NAME from ");
		sql.append(getTableName("CC_AGENT_ORDER"));
		sql.append(" where 1=1 and ORDER_STATE = 2 ");
		String date = param.getString("date");
		if (StringUtils.isNotBlank(date)) {
			String startTime = "";
			if ("today".equals(date)) {
				EasyCalendar newInstance = EasyCalendar.newInstance();
				startTime = newInstance.getDateTime("-");
			} else if ("week".equals(date)) {
				Calendar cal = Calendar.getInstance();
				int weekDay = cal.get(Calendar.DAY_OF_WEEK) - 1;
				if (weekDay == 0) {
					weekDay = 7;
				}
				cal.add(Calendar.DATE, 1 - weekDay);
				EasyCalendar newInstance = EasyCalendar.newInstance(cal.getTime());
				startTime = newInstance.getDateTime("-");
			} else if ("month".equals(date)) {
				EasyCalendar newInstance = EasyCalendar.newInstance();
				EasyCalendar monthFirstDay = newInstance.getMonthFirstDay();
				startTime = monthFirstDay.getDateTime("-");
			}
			startTime = startTime.substring(0, 10);
			sql.append(startTime, " and ORDER_TIME >= ?");
		}
		sql.append(getEntId(), " and ENT_ID >= ? ");
		sql.append(getBusiOrderId(), " and BUSI_ORDER_ID >= ? ");
		sql.append("group by AGENT_ID,AGENT_NAME order by ORDER_COUNT desc");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 获取客户模板
	 */
	@WebControl(name = "custTemp", type = Types.TEMPLATE)
	public JSONObject custTemp() {
		EasySQL sql = this.getEasySQL("select * from");
		sql.append(getTableName("CC_CUST_TEMP"));
		sql.append(" where 1=1");
		sql.append(param.getString("custTempId"), "and TEMP_ID = ?");
		sql.append(param.getString("tel"), " and TEL_NUM1 = ?");
		sql.append(getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(getEntId(), " and ENT_ID = ?");
		return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 获取工单基本信息
	 */
	@WebControl(name = "orderBaseInfo", type = Types.TEMPLATE)
	public JSONObject orderBaseInfo() {
		JSONObject row = null;
		if (StringUtils.isBlank(param.getString("orderId"))) {
			JSONObject jsonObject = new JSONObject();
			//jsonObject.put("AGENT_NAME", getUserPrincipal().getUserName()); 原逻辑直接从cc_user中取值
			//刘稳修改从cc_busi_user表取坐席名称
			String userId = getUserPrincipal().getUserId();
			try {
				row = this.getQuery().queryForRow("select AGENT_NAME from " + getTableName("cc_busi_user") + " where USER_ID = ? and ent_ID=? and BUSI_ORDER_ID=?",
						new Object[] { userId,getEntId(),getBusiOrderId() }, new JSONMapperImpl());
				if(row!=null){
					jsonObject.put("AGENT_NAME", row.getString("AGENT_NAME"));
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				e.printStackTrace();
			}
			jsonObject.put("REQ_CODE", RandomKit.orderId());
			String callTime = param.getString("callTime");
			if (StringUtils.isBlank(callTime)) {
				callTime = EasyDate.getCurrentDateString();
			}
			jsonObject.put("ORDER_TIME", callTime);
			jsonObject.put("CUST_OBJ_ID", param.getString("custObjId"));
			return getTemplate(jsonObject);
		}
		
		try {
			String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(param.getString("tempId"), null);
			row = this.getQuery().queryForRow("select "+filedInfo+",AGENT_ID,AGENT_NAME,CHNL_TYPE,CUST_NAME,CUST_OBJ_ID,CUST_PHONE,TMP_ID,SERIAL_ID,REQ_CODE,ORDER_CONTENT,ORDER_TIME,ORDER_ID,ORDER_STEP_ID from " + getTableName("CC_AGENT_ORDER") + " where ORDER_ID = ?",
					new Object[] { param.getString("orderId") }, new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(row);
	}

	/**
	 * 获取工单客户资料
	 */
	@WebControl(name = "orderCust", type = Types.TEMPLATE)
	public JSONObject orderCust() {
		JSONArray list = new JSONArray();
		EasyQuery query = this.getQuery();
		String custObjId = param.getString("custObjId");
		String telNum = param.getString("telNum");
		String orderId = param.getString("orderId");
		String custId = param.getString("custId");
		try {
			Map<String, String> custRow = null;
			// 根据custObjId、custId、telNum获取客户资料
			if (StringUtils.isNotBlank(custObjId) || StringUtils.isNotBlank(telNum) || StringUtils.isNotBlank(custId)) {
				EasySQL custSql = this.getEasySQL("select * from ").append(getTableName("CC_ENT_CUST"))
						.append(" where 1=1");
				custSql.append(custObjId, " and CUST_OBJ_ID = ?");
				if (StringUtils.isBlank(custObjId) && StringUtils.isNotBlank(telNum)) {
					//custSql.append("and TEL_NUM1 in ("+parseTelNum(telNum)+")");
					custSql.append("and TEL_NUM1 = '"+telNum+"'");//现在平台没有前面加0的
					custSql.append(getEntId(), "and ENT_ID = ?");
					custSql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?");
				}
				if (StringUtils.isBlank(custObjId) && StringUtils.isNotBlank(custId) && !"null".equals(custId)) {
					custSql.append(custId, "and CUST_ID = ?");
				}
				custSql.append(param.getString("custTempId"), "and TEMP_ID = ?");
				custRow = query.queryForRow(custSql.getSQL(), custSql.getParams(), new MapRowMapperImpl());
			}
			// 如果数据库有该资料
			if (custRow != null && StringUtils.isNotBlank(custRow.get("TEMP_ID"))) {
				JSONObject tempRow = getCustTemplate(custRow.get("TEMP_ID"));
				list.add(getFieldObj("姓名", "", "CUST_NAME", 0));
				for (Entry<String, Object> entry : tempRow.entrySet()) {
					JSONObject resultJson = new JSONObject(true);
					Object key = entry.getKey(); // 字段名称
					Object val = entry.getValue(); // 字段定义json
					if (val == null || StringUtils.isBlank(val.toString())) {
						continue;
					}
					JSONObject jsonObject = JSONObject.parseObject(val.toString());
					if (jsonObject.getIntValue("inuse") == 1) {
						resultJson.put("val", custRow.get(key));
						resultJson.put("readonly", jsonObject.getIntValue("readonly"));
						resultJson.put("name", jsonObject.getString("name"));
						resultJson.put("field", key.equals("TEL_NUM1") ? "CUST_PHONE" : key);
						if (key.equals("CUST_NAME"))
							list.remove(0);
						list.add(resultJson);
					}
				}
			} else {// 数据库没有该客户资料
					// 如果有orderId，从工单中获取客户基本信息
				String custName = "";
				if (StringUtils.isNotBlank(orderId)) {
					EasyRow row = this.getQuery().queryForRow("select CUST_NAME,CUST_PHONE from "
							+ getTableName("CC_AGENT_ORDER") + " where ORDER_ID = ?", new Object[] { orderId });
					custName = row.getColumnValue("CUST_NAME");
					telNum = row.getColumnValue("CUST_PHONE");
				}
				list.add(getFieldObj("联系电话", telNum, "CUST_PHONE", 0));
				list.add(getFieldObj("姓名", custName, "CUST_NAME", 0));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(list);
	}

	private JSONObject getFieldObj(String name, String value, String fieldName, int readonly) {
		JSONObject map = new JSONObject();
		map.put("name", name);
		map.put("val", value);
		map.put("readonly", readonly);
		map.put("field", fieldName);
		return map;
	}

	/**
	 * 获取工单资料
	 */
	@WebControl(name = "orderTempInfo", type = Types.TEMPLATE)
	public JSONObject orderTempInfo() {
		JSONArray list = new JSONArray();
		EasyQuery query = this.getQuery();
		String orderId = param.getString("orderId");
		try {
			OrderTempService service = OrderTempFactory.get(this.getEntId(), this.getDbName());
			String filedInfo = service.getDataFiled(param.getString("tempId"), null);
			EasySQL custSql = this.getEasySQL("select "+filedInfo+",AGENT_ID,AGENT_NAME,CHNL_TYPE,CUST_NAME,CUST_OBJ_ID,CUST_PHONE,TMP_ID,SERIAL_ID,REQ_CODE,ORDER_CONTENT,ORDER_TIME,ORDER_STATE,ORDER_LOG,BUSI_ORDER_ID,HANDLER_TYTPE,HANDLER_NAME,ORDER_STEP_ID from ").append(getTableName("CC_AGENT_ORDER"))
					.append(" where 1=1");
			custSql.append(orderId, " and ORDER_ID = ?");
			Map<String, String> custRow = query.queryForRow(custSql.getSQL(), custSql.getParams(),
					new MapRowMapperImpl());

			if (custRow != null && StringUtils.isNotBlank(custRow.get("TMP_ID"))) {
				JSONObject tempRow = service.getOrderTemplate(custRow.get("TMP_ID"));
				for (Entry<String, Object> entry : tempRow.entrySet()) {
					Object key = entry.getKey(); // 字段名称
					Object val = entry.getValue(); // 字段定义json
					if (val == null || StringUtils.isBlank(val.toString())) {
						continue;
					}
					JSONObject jsonObject = JSONObject.parseObject(val.toString());
					if (jsonObject.getIntValue("state") == 1) {
						Map<String, String> map = new HashMap<>();
						map.put("KEY", jsonObject.getString("name"));
						map.put("VALUE", custRow.get(key));
						list.add(map);
					}
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		JSONObject resultJson = new JSONObject();
		resultJson.put("total", list.size());
		resultJson.put("data", list);
		return resultJson;
	}

	/**
	 * 获取该客户的工单记录
	 */
	@WebControl(name = "orderListByCust", type = Types.TEMPLATE)
	public JSONObject orderListByCust() {
		String telNum = param.getString("telNum");
		String custId = param.getString("custId");
		String custObjId = param.getString("custObjId");
		EasySQL sql = new EasySQL();
		sql.append("select ORDER_TIME,REQ_CODE,AGENT_NAME,ORDER_CONTENT,CUST_PHONE,CUST_NAME,ORDER_ID,CUST_OBJ_ID,TMP_ID from");
		sql.append(getTableName("CC_AGENT_ORDER"));
		sql.append("where 1=1");
		sql.append(getEntId(), "and ENT_ID = ?");
		sql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?");

		if (StringUtils.isNotBlank(telNum)) {
			//sql.append("and CUST_PHONE in ("+parseTelNum(telNum)+")");
			sql.append("and CUST_PHONE = '"+telNum+"'");//现在平台没有前面加0的
		} else if (StringUtils.isNotBlank(custId)) {// 客户编号
			sql.append("and CUST_OBJ_ID in (select distinct CUST_OBJ_ID from").append(getTableName("CC_ENT_CUST"))
					.append("where 1=1");
			sql.append(custId, "and CUST_ID = ?");
			sql.append(getEntId(), "and ENT_ID = ?");
			sql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?)");
		} else if (StringUtils.notBlank(custObjId)) {
			sql.append(custObjId, "and CUST_OBJ_ID = ?");
		} else {
			return getJsonResult(new ArrayList<>());
		}
		sql.append(" union all");
		sql.append("select ORDER_TIME,REQ_CODE,AGENT_NAME,ORDER_CONTENT,CUST_PHONE,CUST_NAME,ORDER_ID,CUST_OBJ_ID,TMP_ID from");
		sql.append(getTableName("CC_AGENT_ORDER_HIS"));
		sql.append("where 1=1");
		sql.append(getEntId(), "and ENT_ID = ?");
		sql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?");

		if (StringUtils.isNotBlank(telNum)) {
			//sql.append("and CUST_PHONE in ("+parseTelNum(telNum)+")");
			sql.append("and CUST_PHONE = '"+telNum+"'");//现在平台没有前面加0的
		} else if (StringUtils.isNotBlank(custId)) {// 客户编号
			sql.append("and CUST_OBJ_ID in (select distinct CUST_OBJ_ID from").append(getTableName("CC_ENT_CUST"))
					.append("where 1=1");
			sql.append(custId, "and CUST_ID = ?");
			sql.append(getEntId(), "and ENT_ID = ?");
			sql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?)");
		} else if (StringUtils.notBlank(custObjId)) {
			sql.append(custObjId, "and CUST_OBJ_ID = ?");
		} else {
			return getJsonResult(new ArrayList<>());
		}
		sql.append("order by ORDER_TIME desc ");
		param.put("pageType", 1);
		param.put("pageIndex", 1);
		param.put("pageSize", 15);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 获取客户模板
	 * 
	 * @param tempId
	 * @return
	 */
	private JSONObject getCustTemplate(String tempId) {
		String cacheKey = "CUST_TEMP_" + tempId;
		JSONObject jsonObject = cache.get(cacheKey);
		if (jsonObject == null) {
			try {
				jsonObject = this.getQuery().queryForRow(
						"select CUST_ID,CUST_NAME,CUST_GROUP_NAME,TEL_NUM1,TEL_NUM2,TEL_NUM3,TEL_NUM4,F1,F2,F3,F4,F5,F6,F7,F8,F9,F10,F11,F12,F13,F14,F15,F16,F17,F18,F19,F20 from "
								+ getTableName("cc_cust_temp") + " where TEMP_ID = ?",
						new Object[] { tempId }, new JSONMapperImpl());
				cache.put(cacheKey, jsonObject);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return jsonObject;
	}

	/**
	 * 添加列表模板查询条件
	 */
	private EasySQL appendSqlTempQuery(EasySQL sql) {
		for (String key : param.keySet()) {
			if (key.contains("DATA")) {
				if (key.startsWith("startDate")) {
					sql.append(param.getString(key), "and t1." + key.substring(9) + " >= ?");
				} else if (key.startsWith("endDate")) {
					sql.append(param.getString(key), "and t1." + key.substring(7) + " <= ?");
				} else if (key.startsWith("input")) {
					sql.appendLike(param.getString(key), "and t1." + key.substring(5) + " like ?");
				}else if (key.startsWith("checkbox") ) {
					sql.appendLike(param.getString(key), "and t1." + key.substring(8) + " like ?");
				}else if (key.startsWith("cascader") ) {
					sql.appendLike(param.getString(key), "and t1." + key.substring(8) + " like ?");
				}
				else { // select,radio
					sql.append(param.getString(key), "and t1." + key + " = ?");
				}
			}
		}
		return sql;
	}

	/**
	 * 获取步骤关联的所有动作
	 */
	@WebControl(name = "getStepActionList", type = Types.TEMPLATE)
	public JSONObject getStepActionList() {
		// 获取步骤关联的所有动作
		String stepId = param.getString("stepId");
		EasySQL sql = getEasySQL("select t1.*,t2.STEP_TYPE as NEXT_STEP_TYPE from ");
		sql.append(getTableName("CC_AGENT_ORDER_ACTION t1,"));
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(stepId, " where t1.ORDER_STEP_ID = ?");
		sql.append(" and t1.NEXT_STEP_ID=t2.ORDER_STEP_ID");
		JSONObject list = this.queryForList(sql.getSQL(), sql.getParams(), null);
		return list;
	}
	
	/**
	 * 获取工单流转日志
	 */
	@WebControl(name = "getOrderLog", type = Types.LIST)
	public JSONObject getOrderLog() {
		// 获取步骤关联的所有动作
		String orderId = param.getString("orderId");
		EasySQL sql = getEasySQL("select t1.* from ");
		sql.append(getTableName("CC_AGENT_ORDER_LOG t1"));
		sql.append(orderId, " where t1.ORDER_ID = ?");
		sql.append( " order by t1.OPER_TIME desc");
		return this.queryForList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 统计标统
	 * @return
	 */
	@WebControl(name = "statHeadList", type = Types.TEMPLATE)
	public JSONObject statHeadList() {
		String tempId = this.getTempIdByName(param.getString("tempName"));
		if("-1".equals(tempId)){
			return this.getTemplate(null);
		}
		String cacheKey = "statHeadList_"+tempId;
		String cacheVal = cache.get(cacheKey);
		if(StringUtils.isNotBlank(cacheVal)){
			return this.getTemplate(JSONArray.parseArray(cacheVal));
		}
		String statTime = param.getString("statTime");
		String statName = "日期";
		if(StringUtils.isBlank(statTime)){
			statTime = "DATE_ID";
		}
		String listName = param.getString("listName");
		String rowName = param.getString("rowName");

		JSONArray listArray = getFileData(tempId, listName);
		JSONArray rowArray = getFileData(tempId, rowName);

		JSONArray array = new JSONArray();

		JSONObject obj = new JSONObject();
		obj.put("title", statName);
		obj.put("field", statTime);
		obj.put("align","center");
		array.add(obj);
		//统计维度
		for(int i = 0; i < listArray.size(); i++){
			JSONObject row = listArray.getJSONObject(i);
			obj = new JSONObject();
			obj.put("title", row.getString("name"));
			obj.put("field", row.getString("key"));
			obj.put("align","center");
			obj.put("totalRow","true");
			array.add(obj);
		}
		//统计数据
		for(int i = 0; i < rowArray.size(); i++){
			JSONObject row = listArray.getJSONObject(i);
			List<JSONObject> levelList = (List<JSONObject>)row.get("list");
			if(levelList != null){
				for(JSONObject level : levelList){
					obj = new JSONObject();
					obj.put("title", level.getString("NAME"));
					obj.put("field", level.getString("ID"));
					obj.put("align","center");
					obj.put("totalRow","true");
					array.add(obj);
				}
			}
		}

		obj.put("title", "总数");
		obj.put("field", "ALL_COUNT");
		obj.put("align","center");
		obj.put("totalRow","true");
		array.add(obj);
		cache.put(cacheKey, array.toJSONString());
		return this.getTemplate(array);
	}
	
	/**
	 * 统计数据
	 * @return
	 */
	@WebControl(name = "statDataList", type = Types.LIST)
	public JSONObject statDataList() {
		String tempId = this.getTempIdByName(param.getString("tempName"));
		if("-1".equals(tempId)){
			return this.getTemplate(null);
		}
		String statTime = param.getString("statTime");
		if(StringUtils.isBlank(statTime)){
			statTime = "DATE_ID";
		}
		String listName = param.getString("listName");
		String rowName = param.getString("rowName");

		JSONArray listArray = getFileData(tempId, listName);
		JSONArray rowArray = getFileData(tempId, rowName);



		//String orderId = param.getString("orderId");
		EasySQL sql = getEasySQL("select ");
		//统计维度
		sql.append(statTime);
		for(int i = 0; i < listArray.size(); i++){
			String key = listArray.getJSONObject(i).getString("key");
			sql.append(",").append("SUBSTRING_INDEX("+key+",',',1) as "+key);
		}
		//统计数据
		for(int i = 0; i < rowArray.size(); i++){
			JSONObject row = listArray.getJSONObject(i);
			List<JSONObject> levelList = (List<JSONObject>)row.get("list");
			if(levelList != null){
				for(JSONObject level : levelList){
					String levelId = level.getString("ID");
					sql.append(",sum(case when data3 like '"+levelId+"%' then 1 else 0 end) as "+levelId);
				}
			}
		}
		sql.append(",").append("count(1) as ALL_COUNT");
		sql.append(getTableName("CC_AGENT_ORDER"));
		sql.append("where 1=1");
		sql.append(this.getEntId(), "and ENT_ID = ?");
		sql.append(param.getString("startDate"), "and DATE_ID >= ?");
		sql.append(param.getString("endDate"), "and DATE_ID <= ?");

		//group by
		sql.append("group by ");
		sql.append(statTime);
		for(int i = 0; i < listArray.size(); i++){
			String key = listArray.getJSONObject(i).getString("key");
			sql.append(",").append("SUBSTRING_INDEX("+key+",',',1) as "+key);
		}
		return this.queryForList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 根据模板名称获取模板ID
	 * @param tempName
	 * @return
	 */
	private String getTempIdByName(String tempName){
		String cacheKey = "ORDER_AGENT_TEMP_ID_"+tempName;
		String tempId = cache.get(cacheKey);
		if(StringUtils.isNotBlank(tempId)){
			return tempId;
		}
		try {

			JSONObject row = this.getQuery().queryForRow("SELECT * from "+getTableName("CC_AGENT_ORDER_TMP")+" where TMP_NAME = ?", new Object[]{tempName}, new JSONMapperImpl());
			if(row == null){
				tempId = "-1";
			}
			cache.put(cacheKey, tempId, 600);
			cache.put(tempId+"_json", row, 600);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return tempId;
	}

	/**
	 * 
	 * @param tempId
	 * @param names
	 * @return
	 */
	private JSONArray getFileData(String tempId, String names){
		JSONArray array = new JSONArray();

		String str = cache.get(tempId + "_json");
		if(StringUtils.isBlank(str)){
			return array;
		}
		JSONObject temp = JSONObject.parseObject(str);

		if(names == null){
			return array;
		}
		String[] split = names.split(",");
		for(String name : split){

			for(int i = 1; i<=20;i++){
				String key = "DATA" + i;
				String val = temp.getString(key);
				if(StringUtils.isBlank(val)){
					continue;
				}
				if(val.indexOf(name)<0){
					continue;
				}
				JSONObject data = JSONObject.parseObject(val);
				if(name.equals(data.getString("name"))){//找到key
					if(StringUtils.isBlank(data.getString("id"))){
						continue;
					}
					List<JSONObject> levelList = getOrderSelList(data.getString("id"));
					if(levelList == null){
						continue;
					}

					JSONObject row = new JSONObject();
					row.put("key",key);//
					row.put("name",name);
					row.put("list", levelList);

					array.add(row);
					break;
				}
			}
		}

		return array;
	}

	/**
	 * 获取级联一级节点
	 * @param pId
	 * @return
	 */
	private List<JSONObject> getOrderSelList(String pId){
		try{
			return this.getQuery().queryForList("SELECT ID,NAME from " + getTableName("CC_AGENT_ORDER_SEL_CONF") + " where PARENT_ID = ? and STATUS = 1 order by IDX_ORDER", new Object[]{pId}, new JSONMapperImpl());
		}catch(Exception e){
			this.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 获取首页数据
	 * @param 
	 * @return
	 */
	@WebControl(name = "getdata", type = Types.RECORD)
	public JSONObject getdata(){
		String startDate=EasyDate.dateToString(new Date(), "yyyy-MM-dd");
		String endDate=EasyDate.dateToString(new Date(), "yyyy-MM-dd");
		EasySQL sql = getEasySQL("select * from ");
		sql.append("(");
		sql.append("select count(1) as ALLORDER from ");
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(startDate + " 00:00:00", " and t1.ORDER_TIME >= ?");
		sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		sql.append(") t1,");
		sql.append("(");
		sql.append("select count(DISTINCT t1.ORDER_ID)  as MYPARTORDER  from");
		sql.append(getTableName("CC_AGENT_ORDER_LOG t3,"));
		sql.append(getTableName("CC_AGENT_ORDER_TMP t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(startDate + " 00:00:00", " and t1.ORDER_TIME >= ?");
		sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		sql.append(getUserId(), "and t3.OPER_ID = ?");
		sql.append( "and t1.ORDER_ID = t3.ORDER_ID");
		sql.append( "and t4.TMP_ID = t1.TMP_ID");
		sql.append(") t2,");
		sql.append("(");
		sql.append("select count(DISTINCT t1.ORDER_ID) as myfinishOrder from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER_LOG t4,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where t1.ORDER_STATE = 2");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(startDate + " 00:00:00", " and t1.ORDER_TIME >= ?");
		sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		sql.append( "and t1.ORDER_ID = t4.ORDER_ID");
		sql.append(getUserId(), "and t4.OPER_ID = ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql.append(") t3,");
		sql.append("(");
		sql.append("select count(1) as TemporaryOrder from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where t1.ORDER_STATE = 3");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(getUserId(),  " and t1.AGENT_ID = ?");
		sql.append(startDate + " 00:00:00", " and t1.ORDER_TIME >= ?");
		sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql.append(") t4,");
		sql.append("(");
		sql.append("select count(1) as myCreatOrder from ");
		sql.append(getTableName("CC_AGENT_ORDER_TMP t3,"));
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		sql.append(" left join ");
		sql.append(getTableName("CC_AGENT_ORDER_STEP t2"));
		sql.append(" on t1.ORDER_STEP_ID = t2.ORDER_STEP_ID");
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		sql.append(getUserId(), " and t1.AGENT_ID = ?");
		sql.append(" and t1.TMP_ID = t3.TMP_ID");
		sql.append(startDate + " 00:00:00", " and t1.ORDER_TIME >= ?");
		sql.append(endDate + " 23:59:59", " and t1.ORDER_TIME <= ?");
		sql.append(") t5");
		JSONObject record = this.queryForRecord(sql.getSQL(), sql.getParams());
		JSONObject data = record.getJSONObject("data");
		data.put("UPDATE_TIME",  EasyDate.getCurrentDateString());
		return record;
		
	}
	
	
	/**
	 * 获取工单新增趋势
	 */
	@WebControl(name = "getOrderTrend", type = Types.DICT)
	public JSONObject getOrderTrend() {
		EasySQL sql= new EasySQL("select t1.fdate, count(t2.ORDER_ID) as CREATE_COUNT FROM");
		sql.append("(");
		sql.append("SELECT @s :=@s + 1 as findex, DATE_FORMAT(DATE(DATE_SUB(CURRENT_DATE, INTERVAL @s DAY)),'%Y%m%d') AS fdate");
		sql.append(" from mysql.help_topic,(SELECT @s := -1) temp");
		sql.append(" WHERE @s < 6");
		sql.append("ORDER BY fdate) t1 ");
		sql.append("left join");
		sql.append(getTableName("cc_agent_order")+" t2 ON t1.fdate=t2.DATE_ID");
		sql.append("GROUP BY t1.fdate");
		
		JSONObject query = this.queryForList(sql.getSQL(), sql.getParams());
		JSONArray data = query.getJSONArray("data");
		List<String> days = new ArrayList<>();
		List<String> creates = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			JSONObject json = data.getJSONObject(i);
			days.add(json.getString("FDATE"));
			creates.add(json.getString("CREATE_COUNT"));
		}
		JSONObject result= new JSONObject();
		result.put("days", days);
		result.put("creates", creates);
		return result;
	}
	
	/**
	 * 获取当前用户能否查看当前工单
	 */
	@WebControl(name = "getOrderAuthority", type = Types.RECORD)
	public JSONObject getOrderAauthority() {
		EasySQL sql= new EasySQL("select count(1) FROM "+getTableName("cc_agent_order") +" where 1=1 ");
		sql.append(param.getString("orderId")," and ORDER_ID=?",false);
		if (this.getUserPrincipal().getRoleType() == Constants.ROLE_TYPE_MONITOR) { // 班长
			String[] skillGroupId = getSkillGroupId();
			if (skillGroupId != null) { // 判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
				sql.append("and AGENT_ID in ( select t2.USER_ID from " + getTableName("CC_SKILL_GROUP_USER")
						+ " t2 where 1=1");
				sql.append(getEntId(), "and t2.ENT_ID = ?");
				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
				if (skillGroupId.length == 1) {
					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?)");
				} else {
					sql.append("and t2.SKILL_GROUP_ID in (" + StringUtils.join(skillGroupId, ",") + "))");
				}
			}
		}else if(this.getUserPrincipal().getRoleType() == Constants.ROLE_TYPE_MANAGER || this.getUserPrincipal().isAdmin()) {
			//不需要做处理
		}else {
			//其他角色只能查看自己的工单
			sql.append(getUserId(),"and AGENT_ID=?");
		}
		boolean isAuthority = false;
		try {
			isAuthority = this.getQuery().queryForExist(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			e.printStackTrace();
		}
		JSONObject result= new JSONObject();
		result.put("isAuthority", isAuthority);
		return result;
	}
	
	/**
	 * 获取短信模板字典
	 * @return
	 */
	@WebControl(name="smsChnDict", type=Types.DICT)
	public JSONObject smsChnDict(){
		return getDictByQuery("select SMS_CHN_ID,SMS_TEMP_NAME from CC_SMS_CHN where ENT_ID = ?", new Object[]{getEntId()});
	}
	
}
