package com.yunqu.yc.agent.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.agent.utils.CacheUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;

/**
 * mars前端统一新增日志服务接口
 */
public class AddLogService extends IService {

	public EasyCache cache = CacheManager.getMemcache();
	private static Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME + "-addLog");


	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		logger.info("[AddLogService] << " + json.toJSONString());
		String entId = json.getString("entId");
		String entName = getEntName(entId);
		String eventTypeUser = json.getString("eventTypeUser");
		String operType = json.getString("operType");
		String userId = json.getString("userId");
		String userName = json.getString("userName");
		String desc = json.getString("desc");
		if(StringUtils.isAnyBlank(entId,eventTypeUser,operType,userId)){
			return EasyResult.fail("缺少必填参数！");
		}
		String flowId = json.getString("flowId");
		String isFlow = json.getString("isFlow");
		String busiId = json.getString("busiId");
		EasyRecord record = new EasyRecord("CC_ENT_LOG", "LOG_ID");
		record.setPrimaryValues(RandomKit.orderId());
		record.set("ENT_ID", entId);
		record.set("ENT_NAME", entName);
		record.set("EVENT_TYPE", eventTypeUser);
		record.set("OPER_TYPE", operType);
		record.set("USER_ID", userId);
		record.set("USERNAME", userName);
		record.set("LOG_DESC", desc);
		record.set("CREATE_DATE", EasyDate.getCurrentDateString("yyyyMMdd"));
		record.set("CREATE_TIME", EasyDate.getCurrentDateString());
		record.set("IS_FLOW", isFlow);
		record.set("FLOW_ID", flowId);
		record.set("BUSI_ID", busiId);
		try {
			entId = StringUtils.parseInt(entId)+"";//为了防止企业ID不是数字
			QueryFactory.getQuery(entId).save(record);
		}catch(Exception e){
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return EasyResult.ok();
	}
	protected static String getEntName(String entId){
		String entName = CacheUtil.get("ENT_NAME_"+entId);
		if(StringUtils.isBlank(entName)){
			try {
				entName = QueryFactory.getReadQuery().queryForString("select ENT_NAME from CC_ENT where ENT_ID = ?", new Object[]{entId});
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
		if(StringUtils.isNotBlank(entName)){
			CacheUtil.put("ENT_NAME_"+entId, entName);
		}
		return entName;
	}

}