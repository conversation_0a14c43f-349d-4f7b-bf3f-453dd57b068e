package com.yunqu.yc.agent.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.yc.agent.base.AppBaseServlet;

@WebServlet("/servlet/callNoanswer/*")
public class CallNoAnswerServlet extends AppBaseServlet {

	private static final long serialVersionUID = -1864094683991960092L;
	
	//跟新未接来电
	public EasyResult actionForUpdateNoAnswer(){
		try {
			this.getQuery().execute("update " + getTableName("CC_CALL_NOANSWER") + " set STATE = 1,HANDLE_TIME=?,USERNAME=?,USER_ID=? where SERIAL_ID = ?",
					new Object[]{EasyDate.getCurrentDateString(),this.getUserPrincipal().getUserName(),getUserId(),getJsonPara("serialId")});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("操作失败");
		}
		return EasyResult.ok(null, "操作成功");
	}
	
	//跟新留言
	public EasyResult actionForUpdateWord(){
		try {
			this.getQuery().execute("update " + getTableName("CC_CALL_WORD") + " set STATE = 1,HANDLE_TIME=?,USERNAME=?,USER_ID=? where SERIAL_ID = ?",
					new Object[]{EasyDate.getCurrentDateString(),this.getUserPrincipal().getUserName(),getUserId(),getJsonPara("serialId")});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("操作失败");
		}
		return EasyResult.ok(null, "操作成功");
	}
		
	
	
	//删除留言-删除的同时，把未接来电另存为其它表数据
	public EasyResult actionForDeleteReply(){
		EasyResult result = new EasyResult();
		try {
			EasySQL sql = new EasySQL("INSERT INTO "+getTableName("CC_CALL_NOANSWER_SECOND")+"(SERIAL_ID,ENT_ID,DATE_ID,CALLER,CALLED,BUSI_ORDER_ID,QUEUE_TIME,ALERING_TIME,END_TIME,IVR_STAY_TIME,QUEUE_STAY_TIME,AGENT_STAY_TIME,AGENT_ID,AGENT_NAME,GROUP_ID,GROUP_NAME,STATE,USER_ID,USERNAME,"
					+ "HANDLE_TIME,RECALL_TIME,CALL_SERIAL_ID,BEGIN_TIME,TOTAL_TIME,AGENT_RELEASE,QUEUE_ID,QUEUE_NAME,KEY_CODE,SIP_CALL_ID,AREA_CODE,PHONE_NUM,DEPT_CODE,HANDLE_DESC,ORIG_CALLER,ORIG_CALLED,CREATE_TIME,CREATE_ACC)");
			sql.append("select SERIAL_ID,ENT_ID,DATE_ID,CALLER,CALLED,BUSI_ORDER_ID,QUEUE_TIME,ALERING_TIME,END_TIME,IVR_STAY_TIME,QUEUE_STAY_TIME,AGENT_STAY_TIME,AGENT_ID,AGENT_NAME,GROUP_ID,GROUP_NAME,STATE,USER_ID,USERNAME,"
					+ "HANDLE_TIME,RECALL_TIME,CALL_SERIAL_ID,BEGIN_TIME,TOTAL_TIME,AGENT_RELEASE,QUEUE_ID,QUEUE_NAME,KEY_CODE,SIP_CALL_ID,AREA_CODE,PHONE_NUM,DEPT_CODE,HANDLE_DESC,ORIG_CALLER,ORIG_CALLED,'"+EasyDate.getCurrentDateString()+"' CREATE_TIME,'"+this.getUserId()+"' CREATE_ACC ");
			sql.append("from "+getTableName("CC_CALL_NOANSWER")+" where 1=1 ");
			sql.append(getJsonPara("serialId"), "and SERIAL_ID = ?");
			this.getQuery().execute(sql.getSQL(), sql.getParams());//存放到隔壁表，避免删除后没有相关记录，并且得同步更新统计
			this.getQuery().execute("delete from " + getTableName("CC_CALL_NOANSWER") + " where SERIAL_ID = ?", new Object[]{getJsonPara("serialId")});
			result.setMsg("删除成功！");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("删除失败！");
		}
		return result;
	}
	
		
	//跟新漏话
	public EasyResult actionForUpdateMiscall(){
		try {
			EasySQL sql = new EasySQL("select  USER_ID, AGENT_NAME from "+getTableName("CC_BUSI_USER"));
			sql.append(getUserId(),"where USER_ID=?");
			sql.append(getBusiOrderId()," and BUSI_ORDER_ID=?");
			sql.append(getEntId()," and ENT_ID=?");
			EasyRow row = this.getQuery().queryForRow(sql.getSQL(), sql.getParams());
			if(row!=null) {
				this.getQuery().execute("update " + getTableName("CC_CALL_MISCALL") + " set STATE = 1,HANDLE_TIME=?,USERNAME=?,USER_ID=? where SERIAL_ID = ?",
						new Object[]{EasyDate.getCurrentDateString(),row.getColumnValue("AGENT_NAME"),getUserId(),getJsonPara("serialId")});
			}
//			this.getQuery().execute("update " + getTableName("CC_CALL_MISCALL") + " set STATE = 1,HANDLE_TIME=?,USERNAME=?,USER_ID=? where SERIAL_ID = ?",
//					new Object[]{EasyDate.getCurrentDateString(),this.getUserPrincipal().getUserName(),getUserId(),getJsonPara("serialId")});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("操作失败");
		}
		return EasyResult.ok(null, "操作成功");
	}

	@Override
	protected String getResId() {
		return null;
	}
}
