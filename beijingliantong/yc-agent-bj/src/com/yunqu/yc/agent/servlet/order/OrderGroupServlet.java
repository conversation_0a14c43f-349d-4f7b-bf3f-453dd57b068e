package com.yunqu.yc.agent.servlet.order;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import com.yunqu.yc.agent.utils.LogUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AppBaseServlet;
import com.yunqu.yc.agent.utils.CacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * 审批组servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/orderGroup")
public class OrderGroupServlet extends AppBaseServlet {

	private static final long serialVersionUID = 7738338623822931016L;

	/**
	 * 添加审批组
	 */
	public EasyResult actionForAddGroup(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_GROUP"), "ORDER_GROUP_ID");
			record.setColumns(getJSONObject("orderGroup"));
			String sql="select count(1) from " + getTableName("CC_AGENT_ORDER_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and EXAM_GROUP_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), StringUtils.trimToEmpty(record.getString("EXAM_GROUP_NAME"))})) {
				result.addFail("该审批组名称已经存在！");
				return result;
			}
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			record.setPrimaryValues(RandomKit.randomStr());
			record.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			record.set("CREATOR", getUserName());
			this.getQuery().save(record);
			String key = "CX_ORDER_GROUP_NAME_"+record.getString("ORDER_GROUP_ID");//缓存key
			cache.put(key, record.getString("EXAM_GROUP_NAME"));//存储工单审批组名称的缓存
			result.setMsg("添加成功！");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_ADD,"【新增工单审批组】新增"
					+record.getString("EXAM_GROUP_NAME")+"名称工单审批组，id是"+record.getString("ORDER_GROUP_ID")+"。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 更新审批组
	 */
	public EasyResult actionForUpdateGroup(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_GROUP"), "ORDER_GROUP_ID");
			record.setColumns(getJSONObject("orderGroup"));
			String sql="select count(1) from " + getTableName("CC_AGENT_ORDER_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and EXAM_GROUP_NAME = ? and ORDER_GROUP_ID <> ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), StringUtils.trimToEmpty(record.getString("EXAM_GROUP_NAME")), record.getString("ORDER_GROUP_ID")})) {
				result.addFail("该审批组名称已经存在！");
				return result;
			}
			this.getQuery().update(record);
			String key = "CX_ORDER_GROUP_NAME_"+record.getString("ORDER_GROUP_ID");//缓存key
			cache.put(key, record.getString("EXAM_GROUP_NAME"));//存储工单审批组名称的缓存
			result.setMsg("修改成功！");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_EDIT,"【修改工单审批组】修改"
					+record.getString("EXAM_GROUP_NAME")+"名称工单审批组，id是"+record.getString("ORDER_GROUP_ID")+"。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 删除审批组
	 */
	public EasyResult actionForDeleteGroup(){
		try {
			EasyQuery query = this.getQuery();
			String groupId = getJsonPara("groupId");
			Object[] param = new Object[]{groupId};
			if(query.queryForExist("select count(1) from " + getTableName("CC_AGENT_ORDER_TMP ")+" where ORDER_GROUP_ID = ?", param)){
				return EasyResult.fail("该审批组已经使用，不能删除！");
			}
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_GROUPUSER" + " WHERE ORDER_GROUP_ID = ?"), param);
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_GROUP") + " where ORDER_GROUP_ID = ?", param);
			String key = "CX_ORDER_GROUP_NAME_"+groupId;//缓存key
			cache.delete(key);//存储工单审批组名称的缓存
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_DEL,"【删除工单审批组】删除"
					+"id是"+groupId+"工单审批组。",this.getUserPrincipal());
			return EasyResult.ok(null,"删除成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("删除失败"+e.getMessage());
		}
	}
	
	/**
	 * 添加审批组坐席
	 * @return
	 */
	public EasyResult actionForAddGroupUser() {
		EasyQuery  easyQuery = this.getQuery();
		try {
			YCUserPrincipal userPrincipal = this.getUserPrincipal();
			String entId = userPrincipal.getEntId();
			String busiOrderId = userPrincipal.getBusiOrderId();
			String userName = userPrincipal.getUserName();
			String createTime = EasyCalendar.newInstance().getDateTime("-");
			String groupId=getJSONObject().getString("groupId");
			JSONArray array=getJSONObject().getJSONArray("userIds");
			if(array!=null && !array.isEmpty()){
				for(Object object:array){
					EasyRecord userGroup=new EasyRecord(getTableName("CC_AGENT_ORDER_GROUPUSER"),"GROUP_USER_ID");
					userGroup.set("ORDER_GROUP_ID", groupId);
					userGroup.set("BUSI_ORDER_ID", busiOrderId);
					userGroup.set("ENT_ID", entId);
					userGroup.setPrimaryValues(RandomKit.randomStr());
					userGroup.set("USER_ID", object);
					userGroup.set("CREATE_TIME", createTime);
					userGroup.set("CREATOR", userName);
					easyQuery.save(userGroup);
				}
			}
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_ADD,"【新增工单审批组内坐席】新增"
					+array+"id坐席到"+groupId+"id工作组中。",this.getUserPrincipal());
			return  EasyResult.ok(null,"分配成功!");
		} catch (SQLException ex) {
			this.error("分配失败，原因："+ex.getMessage(),ex);
			return EasyResult.error(501, "分配失败，原因："+ex.getMessage());
		}
	}	
	
	/**
	 * 单独移除审批组坐席
	 */
	public EasyResult actionForDelGroupUser(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject = getJSONObject();
		String groupUserId = jsonObject.getString("groupUserId");
		String userId = jsonObject.getString("userId");
		try {
			if(this.getQuery().queryForExist("select count(1) from " + getTableName("CC_AGENT_ORDER ")+" where ORDER_STATE = 1 and HANDLER_TYTPE = 1 and HANDLER_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{userId,getEntId(),getBusiOrderId()})){
				return EasyResult.fail("该坐席存在工单未审批，不能删除！");
			}
			getQuery().executeUpdate("delete from " + getTableName("CC_AGENT_ORDER_GROUPUSER") + " where GROUP_USER_ID = ?", new Object[]{groupUserId});
			CacheUtil.getOrderRoleList(groupUserId, getDbName(), true, "2");//存储缓存
			result.setMsg("移除成功！");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_DEL,"【移除工单审批组内坐席】移除"
					+userId+"id坐席从"+groupUserId+"id工作组中。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("移除失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 给审批组人员设置角色
	 * @return
	 */
	public EasyResult actionForAssignRole(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_GROUPUSER"), "GROUP_USER_ID");
			record.setColumns(getJSONObject("groupUser"));
			this.getQuery().update(record);
			CacheUtil.getOrderRoleList(record.getString("ORDER_ROLE_ID"), getDbName(), true, "1");//存储缓存
			result.setMsg("修改成功！");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_EDIT,"【修改工单审批组内坐席角色】修改"
					+record.getString("GROUP_USER_ID")+"id坐席角色id为"+record.getString("ORDER_ROLE_ID")+"。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	/**
	 * 给审批组人员设置角色-批量
	 * @return
	 */
	public EasyResult actionForAssignRoleBatch(){
		JSONObject jsonObject = this.getJSONObject();
		String roleId = jsonObject.getString("roleId");
		JSONArray jsonArray = jsonObject.getJSONArray("groupUserIds");
		if(StringUtils.isBlank(roleId)){
			return EasyResult.fail("角色不能为空");
		}
		if(jsonArray == null || jsonArray.size()<=0){
			return EasyResult.fail("请先选择坐席");
		}
		List<Object[]> params = new ArrayList<Object[]>();
		for(int i = 0; i < jsonArray.size(); i++){
			params.add(new Object[]{
					roleId,jsonArray.getString(i)
			});
		}
		String sql = "update "+getTableName("CC_AGENT_ORDER_GROUPUSER")+" set ORDER_ROLE_ID = ? where GROUP_USER_ID = ?";
		EasyResult result = new EasyResult();
		try {
			this.getQuery().executeBatch(sql, params);
			CacheUtil.getOrderRoleList(roleId, getDbName(), true, "1");//存储缓存
			result.setMsg("修改成功！");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_EDIT,"【批量修改工单审批组内坐席角色】批量修改"
					+jsonArray.toJSONString()+"id坐席角色id为"+roleId+"。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 批量移除审批组坐席
	 */
	public EasyResult actionForBatchDelUser(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject=getJSONObject();
		EasyQuery query = getQuery();
		int deleteCount = 0;
		JSONArray groupUserIds=jsonObject.getJSONArray("groupUserIds");
		String groupId = jsonObject.getString("groupId");
		try {
			JSONArray resultArray = new JSONArray();
			List<EasyRow> list = query.queryForList("select t2.GROUP_USER_ID from "+getTableName("CC_AGENT_ORDER t1,")+getTableName("CC_AGENT_ORDER_GROUPUSER t2")+" where t1.HANDLER_ID = t2.USER_ID and t1.HANDLER_TYTPE = 1 and t1.ORDER_STATE = 1 and t2.ORDER_GROUP_ID = ? GROUP BY t2.GROUP_USER_ID", new Object[]{groupId});
			Set<String> set = new HashSet<>();
			for (EasyRow row : list) {
				set.add(row.getColumnValue("GROUP_USER_ID"));
			}
			if(groupUserIds!=null && groupUserIds.size()>0){
				for (int i = 0; i < groupUserIds.size(); i++) {
					if(set.contains(groupUserIds.getString(i))){
						resultArray.add(groupUserIds.getString(i));
					}else{
						deleteCount += query.executeUpdate("delete from " + getTableName("CC_AGENT_ORDER_GROUPUSER") + " where GROUP_USER_ID = ?", new Object[]{groupUserIds.getString(i)});
					}
				}
			}else{
				result.addFail("请先选择坐席！");
			}
			if(resultArray.size() > 0){
				result.setMsg("成功移除" + deleteCount + "个坐席！坐席：" + resultArray.toJSONString()+"存在工单未审批，不能删除！");
			}else{
				result.setMsg("成功移除" + deleteCount + "个坐席！");
			}
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_DEL,"【移除工单审批组内坐席】批量移除"
					+groupUserIds.toJSONString()+"id坐席的坐席从id为"+groupId+"的组中。",this.getUserPrincipal());
			CacheUtil.getOrderRoleList(groupId, getDbName(), true, "2");//存储缓存
		} catch (SQLException e) {
			this.error("移除坐席失败，原因："+e.getMessage(),e);
			result.addFail("移除坐席失败，原因："+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 选择器更新用户
	 * @return
	 */
	public EasyResult actionForUpdateGroupUser(){
		EasyQuery  easyQuery = this.getQuery();
		YCUserPrincipal userPrincipal = this.getUserPrincipal();
		String entId = userPrincipal.getEntId();
		String busiOrderId = userPrincipal.getBusiOrderId();
		String userName = userPrincipal.getUserName();
		String createTime = EasyCalendar.newInstance().getDateTime("-");
		String groupId=getJSONObject().getString("groupId");
		JSONArray array=getJSONObject().getJSONArray("userIds");
		try {
			if(array!=null && !array.isEmpty()){
				// 先查出该审批组有已有的审批人员
				List<JSONObject> oldList = easyQuery.queryForList("select * from " + getTableName("CC_AGENT_ORDER_GROUPUSER") +" where ORDER_GROUP_ID=?",new Object[] {groupId}, new JSONMapperImpl());
				List<JSONObject> newList = new ArrayList<>();
				for(int i=0;i<array.size();i++){
					String userId = array.getString(i);
					boolean flag = false;
					for (JSONObject row : oldList) {
						if(userId.equals(row.getString("USER_ID"))){
							newList.add(row);
							flag = true;
							break;
						}
					}
					if(!flag){
						JSONObject userGroup = new JSONObject();
						userGroup.put("GROUP_USER_ID", RandomKit.randomStr());
						userGroup.put("ORDER_GROUP_ID", groupId);
						userGroup.put("BUSI_ORDER_ID", busiOrderId);
						userGroup.put("ENT_ID", entId);
						userGroup.put("USER_ID", userId);
						userGroup.put("CREATE_TIME", createTime);
						userGroup.put("CREATOR", userName);
						newList.add(userGroup);
					}
				}
				
				easyQuery.executeUpdate("delete from " + getTableName("CC_AGENT_ORDER_GROUPUSER") +" where ORDER_GROUP_ID=?", new Object[]{groupId});
				
				for(JSONObject row : newList){
					EasyRecord userGroup=new EasyRecord(getTableName("CC_AGENT_ORDER_GROUPUSER"),"GROUP_USER_ID");
					userGroup.putAll(row);
					easyQuery.save(userGroup);
				}
			}else{
				easyQuery.executeUpdate("delete from " + getTableName("CC_AGENT_ORDER_GROUPUSER") +" where ORDER_GROUP_ID=?", new Object[]{groupId});
			}
			CacheUtil.getOrderRoleList(groupId, getDbName(), true, "2");//存储缓存
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_ADD,"【添加工单审批组内坐席】添加"
					+array.toJSONString()+"id坐席的坐席从id为"+groupId+"的组中。",this.getUserPrincipal());
			return  EasyResult.ok(null,"分配成功!");
		} catch (SQLException ex) {
			this.error("分配失败，原因："+ex.getMessage(),ex);
			return EasyResult.error(501, "分配失败，原因："+ex.getMessage());
		}
	}
	
	@Override
	protected String getResId() {
		// TODO Auto-generated method stub
		return null;
	}

}
