package com.yunqu.yc.agent.servlet.order;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import com.yunqu.yc.agent.utils.LogUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AppBaseServlet;
import com.yunqu.yc.agent.base.OrderTempFactory;
import com.yunqu.yc.agent.service.OrderTempService;

@WebServlet("/servlet/orderTemp")
public class OrderTempServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 8235213574177683968L;
	
	/**
	 * 添加模板
	 */
	public EasyResult actionForAdd(){
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		try {
			EasyRecord record=new EasyRecord(getTableName("CC_AGENT_ORDER_TMP"),"TMP_ID");
			record.setColumns(this.parseParam());
			record.setPrimaryValues(RandomKit.randomStr());
			
			// 获取模板关联的审批组ID
			String groupIds= record.getString("ORDER_GROUP_ID");
			record.remove("ORDER_GROUP_ID");
			if(StringUtils.isNoneBlank(groupIds)) {
				String[] groupIdArray = groupIds.split(",");
				for (int i = 0; i < groupIdArray.length; i++) {
					String groupId = groupIdArray[i];
					// 添加模板与审批组的关联关系
					EasyRecord refRecord = new EasyRecord(getTableName("CC_AGENT_ORDER_GROUP_REF"),"ORDER_GROUP_ID","TMP_ID");
					refRecord.setPrimaryValues(groupId,record.getPrimaryValue());
					query.save(refRecord);
				}
			}
			
			//保存级联于模板之间的关系 nh
//			String selectId= record.getString("SELECT_ID");
//			record.remove("SELECT_ID");
//			if(StringUtils.isNoneBlank(selectId)) {
//				EasyRecord refRecord = new EasyRecord(getTableName("cc_agent_order_sel_ref"),"SELECT_CONF_ID","TMP_ID");
//				refRecord.setPrimaryValues(selectId,record.getPrimaryValue());
//				query.save(refRecord);
//			}
			
			
			// 为模板添加三个默认的步骤
			addDefaultSteps(query,record.getPrimaryValue());
			
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID",getBusiOrderId());
			record.set("TMP_STATE", 0);
			record.set("CREATOR",getUserName());
			record.set("CREATE_TIME",EasyDate.getCurrentDateString(null));
			setIntType(record, "TMP_ORDER", "TMP_STATE", "APPROVAL_FLAG");
			query.save(record);

			OrderTempFactory.get(this.getEntId(), this.getDbName()).updateTemp(record.getString("TMP_ID"));
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_ADD,"【新增工单模版】新增"
					+record.getString("TMP_NAME")+"名称工单模版。",this.getUserPrincipal());
			result.setMsg("添加成功！");
			
		} catch (SQLException e) {
			this.error(e.getMessage(),e);
			result.addFail("添加失败，失败原因："+e.getMessage());
		}
		return result;
	}

	/**
	 * 添加默认的模板步骤 
	 * 	新建工单 
	 * 	工单完成
	 * 	工单取消
	 * @param query
	 * @param primaryValue
	 * @throws SQLException 
	 */
	private void addDefaultSteps(EasyQuery query, Object tempId) throws SQLException {
		// 添加 新建工单 步骤
		EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_STEP"),"ORDER_STEP_ID");
		record.setPrimaryValues(RandomKit.randomStr());
		record.set("TMP_ID", tempId);
		record.set("STEP_TYPE", 10);
		record.set("ORDER_STEP_NAME","新建工单");
		record.set("IDX_ORDER",1);
		query.save(record);
		// 添加 工单完成 步骤
		record = new EasyRecord(getTableName("CC_AGENT_ORDER_STEP"),"ORDER_STEP_ID");
		record.setPrimaryValues(RandomKit.randomStr());
		record.set("TMP_ID", tempId);
		record.set("STEP_TYPE", 20);
		record.set("ORDER_STEP_NAME","工单完成");
		record.set("IDX_ORDER",100);
		query.save(record);
		// 添加 工单取消 步骤
		record = new EasyRecord(getTableName("CC_AGENT_ORDER_STEP"),"ORDER_STEP_ID");
		record.setPrimaryValues(RandomKit.randomStr());
		record.set("TMP_ID", tempId);
		record.set("STEP_TYPE", 30);
		record.set("ORDER_STEP_NAME","工单取消");
		record.set("IDX_ORDER",99);
		query.save(record);
	}

	/**
	 * 修改模板
	 */
	public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		try {
			EasyRecord record=new EasyRecord(getTableName("CC_AGENT_ORDER_TMP"),"TMP_ID");
			record.setColumns(this.parseParam());
			
			// 维护模板与审批组关联关系
			// 先把之前的关系全删掉再更新
			String groupIds= record.getString("ORDER_GROUP_ID");
			record.remove("ORDER_GROUP_ID");
			if(StringUtils.isNoneBlank(groupIds)) {
				query.execute("delete from " + getTableName("CC_AGENT_ORDER_GROUP_REF") + " where TMP_ID = ? ", record.getPrimaryValue() );
				// 获取模板关联的审批组ID
				String[] groupIdArray = groupIds.split(",");
				for (int i = 0; i < groupIdArray.length; i++) {
					String groupId = groupIdArray[i];
					// 添加模板与审批组的关联关系
					EasyRecord refRecord = new EasyRecord(getTableName("CC_AGENT_ORDER_GROUP_REF"),"ORDER_GROUP_ID","TMP_ID");
					refRecord.setPrimaryValues(groupId,record.getPrimaryValue());
					query.save(refRecord);
				}
			}
			//修改模板与级联配置的关系
//			String selectId= record.getString("SELECT_ID");
//			record.remove("SELECT_ID");
//			if(StringUtils.isNoneBlank(selectId)) {
//				
//				query.execute("delete from " + getTableName("cc_agent_order_sel_ref") + " where TMP_ID = ? ", record.getPrimaryValue() );
//
//				EasyRecord refRecord = new EasyRecord(getTableName("cc_agent_order_sel_ref"),"SELECT_CONF_ID","TMP_ID");
//				refRecord.setPrimaryValues(selectId,record.getPrimaryValue());
//				query.save(refRecord);
//			}

			setIntType(record, "TMP_ORDER", "TMP_STATE", "APPROVAL_FLAG");
			query.update(record);
			// cache.delete("ORDER_TEMP_"+record.getPrimaryValue().toString());
			
			OrderTempFactory.get(this.getEntId(), this.getDbName()).updateTemp(record.getString("TMP_ID"));
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_EDIT,"【修改工单模版】修改"
					+record.getString("TMP_NAME")+"名称工单模版，模版id是"+record.getString("TMP_ID")+"。",this.getUserPrincipal());
			result.setMsg("修改成功");
		} catch (SQLException e) {
			this.error(e.getMessage(),e);
			result.addFail("修改失败，失败原因："+e.getMessage());
		}
		return result;
	}
	

	/**
	 * 保存步骤字段
	 */
	public EasyResult actionForSaveStepFields(){
		EasyResult result = new EasyResult();
		EasyRecord record=new EasyRecord(getTableName("CC_AGENT_ORDER_STEP"),"ORDER_STEP_ID");
		record.set("ORDER_STEP_ID", getJsonPara("stepId"));
		String fieldsJson = getJsonPara("fieldsJson");
		record.set("REF_DATA_FIELDS", fieldsJson);
		try {
			setIntType(record, "STEP_TYPE", "TIME_LIMIT", "IDX_ORDER");
			this.getQuery().update(record);
			result.setMsg("保存成功");
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_EDIT,"【保存工单模版步骤】保存"
					+record.getString("TMP_ID")+"模版id的"+record.getString("ORDER_STEP_NAME")+"名称的步骤。",this.getUserPrincipal());
		} catch (SQLException e) {
			this.error(e.getMessage(),e);
			result.addFail("保存失败，失败原因："+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 删除模板
	 */
	public EasyResult actionForDelete(){
		EasyQuery query = this.getQuery();
		try {
			Object[] param = new Object[]{getJsonPara("tempId")};
			if(query.queryForExist("select count(1) from " + getTableName("CC_AGENT_ORDER") + " where TMP_ID = ?", param)){
				return EasyResult.fail("该模板已经使用，不能删除");
			}
			// 删除与审批组的关联关系
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_GROUP_REF") + " where TMP_ID = ? ", param );
			// 删除与步骤的关联关系
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_STEP") + " where TMP_ID = ? ", param);
			// 删除模板
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_TMP") + " where TMP_ID = ? ", param);
			LogUtils.logOrderEnt(getEntId(),LogUtils.OPER_TYPE_DEL,"【删除工单模版】删除"
					+getJsonPara("tempId")+"的工单模版。",this.getUserPrincipal());
			return EasyResult.ok(null, "删除成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 判断模板是否使用
	 */
	public EasyResult actionForHasUse(){
		try {
			Object[] param = new Object[]{getJsonPara("tempId")};
			if(this.getQuery().queryForExist("select LENGTH(ORDER_ID) from " + getTableName("CC_AGENT_ORDER") 
				+ " where TMP_ID = ? limit 1", param)){//原来是 select count(1) from table where tmp_id=? 这样的话数据太多全表查询了，查不出来
				return EasyResult.fail("该模板已经在工单中使用，请谨慎修改！是否继续？");
			}
			return EasyResult.ok(null, "没有使用");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 获取模板的字段和步骤关联的字段
	 * @return
	 */
	public EasyResult actionForGetTemplateData(){
		try {
			OrderTempService orderTempService = OrderTempFactory.get(this.getEntId(), this.getDbName());
			if(orderTempService == null){
				return EasyResult.ok(new HashMap<String, Object>());
			}
			String filedInfo = orderTempService.getDataFiledDef();
			if(StringUtils.isBlank(filedInfo)){
				return EasyResult.ok(new HashMap<String, Object>());
			}
			Map<String, String> templateData = this.getQuery().queryForRow("select "+filedInfo+" from " + getTableName("CC_AGENT_ORDER_TMP") +" where TMP_ID = ?",new Object[]{getJsonPara("tempId")}, new MapRowMapperImpl());
			// 获取模板关联的所有步骤的关联字段，在前端防止重复使用字段
			List<Map<String, String>> usedFieldList = this.getQuery().queryForList("select REF_DATA_FIELDS from " +getTableName("CC_AGENT_ORDER_STEP") + " where TMP_ID=?", new Object[]{getJsonPara("tempId")}, new MapRowMapperImpl());
			Map<String, String> unUsedFields = new HashMap<String, String>();
			// 已使用的字段
			List<String> usedFields = new ArrayList<String>();
			for (Map<String, String> usedFieldMap : usedFieldList) {
				String fieldsJson = usedFieldMap.get("REF_DATA_FIELDS");
				if(StringUtils.isNotBlank(fieldsJson)) {
					JSONArray fieldsJsonArray = JSON.parseArray(fieldsJson);
					if(fieldsJsonArray!=null && !fieldsJsonArray.isEmpty()) {
						for (int i = 0; i < fieldsJsonArray.size(); i++) {
							JSONArray jsonArray = fieldsJsonArray.getJSONObject(i).getJSONArray("cols");
							for (int j = 0; j < jsonArray.size(); j++) {
								String field = jsonArray.getJSONObject(j).getString("refData");
								usedFields.add(field);
							}
						}
					}
				}
			}
			// 过滤出未使用的字段
			for (Map.Entry<String, String> entry : templateData.entrySet()) {
				String key = entry.getKey();
				String value = entry.getValue();
				if(!usedFields.contains(key)) {
					unUsedFields.put(key, value);
				}
			}
			
			EasySQL sql = new EasySQL("select REF_DATA_FIELDS from ").append(getTableName("CC_AGENT_ORDER_STEP")).append("where 1=1");
			sql.append(getJsonPara("tempId"), "and TMP_ID = ?");
			sql.append(getJsonPara("stepId"), "and ORDER_STEP_ID = ?");
			sql.append(getJsonParaInt("stepType"), "and STEP_TYPE = ?");
			
			String refDataFields = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			//查询当前模板是否关联的了级联下拉框的配置项
			//JSONObject jsonSel = this.getQuery().queryForRow("select * from "+getTableName("cc_agent_order_sel_ref")+"  where  TMP_ID=? ", new Object[]{getJsonPara("tempId")},new JSONMapperImpl());
			Map<String, Object> result = new HashMap<String, Object>(2);
			result.put("templateData", templateData);
			result.put("refDataFields", refDataFields);
			result.put("unUsedFields", unUsedFields);
//			if(jsonSel != null) {
//				//jsonSel.getString("SELECT_CONF_ID");
//				result.put("selectId", jsonSel.getString("SELECT_CONF_ID"));
//			}
			
			return EasyResult.ok(result);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 获取模板的字段和步骤关联的字段
	 * @return
	 */
	public EasyResult actionForGetTemplateListData(){
		try {
			String filedInfo = OrderTempFactory.get(this.getEntId(), this.getDbName()).getDataFiled(getJsonPara("tempId"), null);
			if(filedInfo.length()==0) {
				//当模板字段为空时直接返回
				return EasyResult.ok();
			}
			Map<String, String> templateData = this.getQuery().queryForRow("select "+filedInfo+" from " + getTableName("CC_AGENT_ORDER_TMP") +" where TMP_ID = ?",new Object[]{getJsonPara("tempId")}, new MapRowMapperImpl());
			// 获取模板关联的所有步骤的关联字段，在前端防止重复使用字段
			List<Map<String, String>> usedFieldList = this.getQuery().queryForList("select REF_DATA_FIELDS from " +getTableName("CC_AGENT_ORDER_STEP") + " where TMP_ID=? and STEP_TYPE in(10,40)", new Object[]{getJsonPara("tempId")}, new MapRowMapperImpl());
			Map<String, String> unUsedFields = new HashMap<String, String>();
			// 已使用的字段
			List<String> usedFields = new ArrayList<String>();
			for (Map<String, String> usedFieldMap : usedFieldList) {
				String fieldsJson = usedFieldMap.get("REF_DATA_FIELDS");
				if(StringUtils.isNotBlank(fieldsJson)) {
					JSONArray fieldsJsonArray = JSON.parseArray(fieldsJson);
					if(fieldsJsonArray!=null && !fieldsJsonArray.isEmpty()) {
						for (int i = 0; i < fieldsJsonArray.size(); i++) {
							JSONArray jsonArray = fieldsJsonArray.getJSONObject(i).getJSONArray("cols");
							for (int j = 0; j < jsonArray.size(); j++) {
								String field = jsonArray.getJSONObject(j).getString("refData");
								usedFields.add(field);
							}
						}
					}
				}
			}
			// 过滤出未使用的字段
			if(templateData!=null) {
				for (Map.Entry<String, String> entry : templateData.entrySet()) {
					String key = entry.getKey();
					String value = entry.getValue();
					if(!usedFields.contains(key)) {
						unUsedFields.put(key, value);
					}
				}
			}
			String stepId = getJsonPara("stepId");
			String dealFlag = getJsonPara("dealFlag");
			
			//工单业务信息
			EasySQL sql = new EasySQL("select ORDER_STEP_ID,STEP_TYPE,ORDER_STEP_NAME,REF_DATA_FIELDS from ").append(getTableName("CC_AGENT_ORDER_STEP")).append("where 1=1");
			sql.append(getJsonPara("tempId"), "and TMP_ID = ?");
			sql.append("and STEP_TYPE = 10 ");
			sql.append("order by STEP_TYPE,IDX_ORDER");
			
			JSONArray refDataFields = new JSONArray();
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(list != null){
				for (JSONObject row : list) {
					try {
						if(StringUtils.isNotBlank(row.getString("REF_DATA_FIELDS")))
							refDataFields.addAll(JSONArray.parseArray(row.getString("REF_DATA_FIELDS")));
					} catch (Exception e) {
					}
				}
			}
			
			//String refDataFields = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			//查询当前模板是否关联的了级联下拉框的配置项
//			JSONObject jsonSel = this.getQuery().queryForRow("select * from "+getTableName("cc_agent_order_sel_ref")+"  where  TMP_ID=? ", new Object[]{getJsonPara("tempId")},new JSONMapperImpl());
			
			Map<String, Object> result = new HashMap<String, Object>(2);
			result.put("templateData", templateData);
			result.put("refDataFields", refDataFields.toJSONString());
			result.put("unUsedFields", unUsedFields);
//			if(jsonSel != null) {
//				//jsonSel.getString("SELECT_CONF_ID");
//				result.put("selectId", jsonSel.getString("SELECT_CONF_ID"));
//			}
			
			//待处理工单信息
			if(StringUtils.isNotBlank(stepId)){
				sql = new EasySQL("select REF_DATA_FIELDS from ").append(getTableName("CC_AGENT_ORDER_STEP")).append("where 1=1");
				sql.append(getJsonPara("tempId"), "and TMP_ID = ?");
				sql.append(getJsonPara("stepId"), "and ORDER_STEP_ID = ?");
				sql.append(getJsonPara("stepType"), "and STEP_TYPE = ?");
				String colsDealData = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
				result.put("colsDealData", colsDealData);
			}
			
			//处理工单信息
			if("info".equals(dealFlag)){
				sql = new EasySQL("select ORDER_STEP_ID,STEP_TYPE,ORDER_STEP_NAME,REF_DATA_FIELDS from ").append(getTableName("CC_AGENT_ORDER_STEP")).append("where 1=1");
				sql.append(getJsonPara("tempId"), "and TMP_ID = ?");
				sql.append("and STEP_TYPE = 40 ORDER BY IDX_ORDER");
				list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				result.put("colsInfoList", list);
			}
			return EasyResult.ok(result);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	/**
	 * 格式化数据
	 * @param orderTemp
	 * @return
	 */
	private JSONObject parseParam(){
		JSONObject record = new JSONObject();
		JSONObject jsonObject=getJSONObject();
		JSONObject orderTemp = getJSONObject("orderTemp");
		record.put("TMP_ID", orderTemp.get("TMP_ID"));
		record.put("TMP_NAME", orderTemp.get("TMP_NAME"));
		record.put("ORDER_GROUP_ID", orderTemp.get("ORDER_GROUP_ID"));
		record.put("ORDER_GROUP_LIST", orderTemp.get("ORDER_GROUP_LIST"));
		record.put("TMP_STATE", orderTemp.get("TMP_STATE"));
		record.put("APPROVAL_FLAG", orderTemp.get("APPROVAL_FLAG"));
		record.put("TMP_ORDER", orderTemp.get("TMP_ORDER"));
		record.put("GROUP_ID", orderTemp.get("GROUP_ID"));
//		record.put("SELECT_ID", orderTemp.get("SELECT_ID"));
		Iterator<String> sIterator = orderTemp.keySet().iterator();
		while (sIterator.hasNext()) {
			String key =  sIterator.next();
			if(key.startsWith("DATA")){
				if(orderTemp.get(key)!=null && StringUtils.notBlank(orderTemp.get(key).toString())){
					JSONObject colums=new JSONObject();
					colums.put("state", StringUtils.isNotBlank(jsonObject.getString(key + ".inuse")) ? 1 : 0);//是否启用
					colums.put("name", orderTemp.getString(key));//名称
					colums.put("required", StringUtils.isNotBlank(jsonObject.getString(key + ".required")) ? 1 : 0);//是否必填
					colums.put("show", StringUtils.isNotBlank(jsonObject.getString(key + ".show")) ? 1 : 0);		//是否显示在列表
					colums.put("query", StringUtils.isNotBlank(jsonObject.getString(key + ".query")) ? 1 : 0);		//是否作为查询条件
					String dataType = jsonObject.getString(key + ".dataType");//类型
					colums.put("dataType", dataType);
					if("text".equals(dataType) || "date".equals(dataType) || "time".equals(dataType) || "number".equals(dataType) || "textArea".equals(dataType) || "cascader".equals(dataType)){
						//如果是文本、日期、时间、数字类型，级联下拉框类型

						if("text".equals(dataType) || "textArea".equals(dataType)){
							JSONArray array = jsonObject.getJSONArray(key + ".text");
							if(array!=null && array.size()>0){
								colums.put("value", array.get(0));
							}

						}else{
							colums.put("value", jsonObject.getString(key + "." + dataType));
						}
						colums.put("id", jsonObject.getString(key + "." + "id"));
					}else if("select".equals(dataType) || "radio".equals(dataType) || "checkbox".equals(dataType)){
						//如果是下拉框、单选框、多选框类型
						JSONArray values = new JSONArray();
						String option = jsonObject.getString(key + ".select");
						if(StringUtils.isNotBlank(option)){
							for (String op : option.split(",")) {
								values.add(op);
							}
						}
						colums.put("value", values);
						//如果是下拉框、单选框、多选框类型
						JSONArray defs = new JSONArray();
						String def = jsonObject.getString(key + ".defaultStr");
						if(StringUtils.isNotBlank(def)){
							for (String op : def.split(",")) {
								defs.add(op);
							}
						}
						colums.put("defaultStr", defs);
					}
					record.put(key, JSONObject.toJSONString(colums));
				}else{
					record.put(key, null);
				}
			}
		}
		return record;
	}
	
	
	@Override
	protected String getResId() {
		return null;
	}

}
