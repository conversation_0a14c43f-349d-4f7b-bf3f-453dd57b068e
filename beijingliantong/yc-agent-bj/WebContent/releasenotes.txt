版本号:  3.3.0#20250716-1
修改日期: 2025-07-16
修改人:   lw
版本修改内容
    1.我的通话记录中，呼叫结果100改为其它呼叫失败
    2.我的待办工单增加紧急程度标识
影响范围：
	云客服报表的导出功能
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250709-1
修改日期: 2025-07-09
修改人:   lw
版本修改内容
    1.修改云客服工单，保存修改为"转派-指派组"、工单催办内容增加工单编号、催办工单列表为空、切换“指派组”的时候 指派人框未清空
    2.导出时工单级联最大5000条导致导出不全
    3.工单级联写入缓存
影响范围：
	云客服报表的导出功能
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250626-1
修改日期: 2025-06-26
修改人:   lw
版本修改内容
    1.修改云客服工单，暂存后再次提交生成了两张一样id的工单
    2.修复"/out/attachment接口未授权访问漏洞，只有图片才可以未授权访问
影响范围：
	云客服报表的导出功能
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250609-2
修改日期: 2025-06-09
修改人:   wpy
版本修改内容
    1.修改云客服报表导出，全部改为走后台模板导出
    修改界面：pages/report/call/报表界面
影响范围：
	云客服报表的导出功能
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250609-1
修改日期: 2025-06-09
修改人:   lw
版本修改内容
    1、针对云客服工单新增老化表
    2、我得工单里面时间周期与全部工单时间周期同步
    3、工单bug处理
    4、操作日志的新增
影响范围：
	无
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250514-1
修改日期: 2025-05-14
修改人:   wd
版本修改内容
    1、新增GlobalFilter过滤器，过滤上传下载限制功能。
影响范围：
	无
升级脚本：
    无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250415-1
修改日期: 2025-04-15
修改人:   ocj
版本修改内容
    1.提交代码冲突部分，本次提交内容与以下修改内容一致
    版本号:  3.3.0#20250207-1
	修改日期: 2025-02-07
    来电接听时，新增配置，支持是否允许显示挂机按钮，默认允许，IS_DISPLAY_HANGING=true允许，false不允许，需在9059企业配置进行配置
影响范围：
	无
升级脚本：

---------------------------------------------------------------------------------------
版本号:  3.3.0#20250310-1
修改日期: 2025-03-10
修改人:   lw
版本修改内容
    1.处理班长用户登录云客服后首页的统计数据是个慢sql问题
    2.新建工单时，如果当前流程有多个流转流程，则将多个按键调整为一个按键“保存”，每个按键指派人调整为下拉框选择
    3.来电弹屏，通过最初用户的按键拉起对应的工单模版
影响范围：
	无
升级脚本：
	mysql.sql
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250208-2
修改日期: 2025-02-08
修改人:   lw
版本修改内容
    1.新增工单催办功能，可以催办工单进度，如果选择短信则可以发送短信通知
    2.新增工单撤销功能，直接关闭当前工单
    3.新增工单撤回功能，工单撤回到上一步
    4.工单删除增加角色控制，不能所有人都可以删除工单
    5.新建工单增加发送短信功能，在工单模版中配置开启发送短信
影响范围：
	1.业务工单所有位置
升级脚本：
	mysql.sql
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250208-1
修改日期: 2025-02-08
修改人:   chenzhiwei
版本修改内容
    1.通话记录和我的通话记录导出功能新增导出pdf格式
    2.工单增加紧急程度标识
    3.新增催办工单列表查询功能和导出功能
影响范围：
	1.通话记录、我的通话记录中导出功能
	2.业务工单中新增紧急程度字段，全部工单列表中导出功能
升级脚本：
	alter table CC_AGENT_ORDER add URGENCY_DEGREE varchar(2) COMMENT '紧急程度：0-一般，1-紧急，2-加急'
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250207-1
修改日期: 2025-02-07
修改人:   ocj
版本修改内容
    1.来电接听时，新增配置，支持是否允许显示挂机按钮，默认允许，IS_DISPLAY_HANGING，需在9059企业配置进行配置
    2.话务报表-坐席话务统计，分页支持选择500条/页
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250121-1
修改日期: 2025-01-21
修改人:   wd
版本修改内容
    1.我的通话记录、通话记录列表新增排队时长、挂机方向字段，查询条件新增挂机方向和排队时长条件。导出也同步新增排队时长、挂机方向字段。
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20250114-1
修改日期: 2024-12-14
修改人:   lw
版本修改内容
    1.解决坐席工作日志根据坐席id导出不了的bug
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241213-1
修改日期: 2024-12-13
修改人:   lw
版本修改内容
    1.在大屏右上角新增技能组筛选条件
    2.处理修改模版时候的慢sql问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241114-1
修改日期: 2024-11-14
修改人:   lw
版本修改内容
    1.修改心跳检测为8s
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241101-1
修改日期: 2024-11-01
修改人:   lty
版本修改内容
    1.技能组多选和非多选直接切换兼容问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241031-2
修改日期: 2024-10-31
修改人:   lty
版本修改内容
    1.修改技能组配置项不开启的情况下UI无感知
	2.增加多选技能组参数传递
	3.修改签入按钮在配置项为true的情况下为点击弹出
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241031-1
修改日期: 2024-10-31
修改人:   ocj
版本修改内容
    1.增加按技能组选择签入功能、如需开启，在企业-配置里面自定义新增LOGIN_SELECT_GROUP=true开启，否则不开启，不影响原有客户，不过需要测试一下签入签出、接打电话
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241030-1
修改日期: 2024-10-10
修改人:   lty
版本修改内容
    1.修改签入逻辑，根据接口返回loginSelectGroup字段判断是否需要多选
	2.修改签入窗口UI，取消划出窗口，窗口消失改为点击取消窗口消失
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241010-2
修改日期: 2024-10-10
修改人:   ocj
版本修改内容
    1.优化user-list重置密码，注释了新方法，新方法的版本升级需要跟yc-base一起升级
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20241010-1
修改日期: 2024-10-10
修改人:   lw
版本修改内容
    1.处理下述菜单的导出变为easyExcel方式
    客户资料数据导出	/yc-agent/pages/report/cust/cust-export.jsp
	离线导出工单	/yc-agent/pages/order2/order/order-query-list.jsp
	离线导出通话记录	/yc-agent/pages/record/cdr-query.jsp
	离线导出漏话记录	/yc-agent/pages/record/call-miscall-list.jsp
	坐席工作日志	/yc-agent/pages/log/agent-work-log.jsp
	全媒体接入记录	/yc-agent/pages/media/record/media-access-list.jsp
	全媒体人工会话记录	/yc-agent/pages/media/record/media-chat-list.jsp
	工单管理V2	/yc-agent/pages/order2/order/order-query-list.jsp
	质检结果	/yc-agent/pages/qc/qc-result-list.jsp
	漏话列表	/yc-agent/pages/record/call-miscall-list.jsp
	我的通话记录	/yc-agent/pages/record/my-cdr.jsp
	通话记录查询	/yc-agent/pages/record/cdr-query.jsp
	通话记录查询	/yc-agent/pages/record/cdr-query-video.jsp
	IVR轨迹列表	/yc-agent/pages/record/ivr-trace-list.jsp
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240923-1
修改日期: 2024-09-23
修改人:   ocj
版本修改内容
    1.处理通话记录离线导出时无法根据用户账号条件导出
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240903-1
修改日期: 2024-09-03
修改人:   ocj
版本修改内容
    1.通话记录增加老化数据表查询条件
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240814-1
修改日期: 2024-08-14
修改人:   ocj
版本修改内容
    1.前端bug，switch case 忘加 break;导致往下走，低级问题！
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240809-2
修改日期: 2024-08-09
修改人:   刘稳
版本修改内容
    1.对接助手转写，实现自动填单，修改了voice_portal_v2.jsp和form-creater.js
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240809-1
修改日期: 2024-08-09
修改人:   ocj
版本修改内容
    1.解决SDK对接无转写问题index.jsp
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240806-1
修改日期: 2024-08-06
修改人:   ocj
版本修改内容
    1.优化我的通话记录查询平台号问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240719-1
修改日期: 2024-07-19
修改人:   刘稳
版本修改内容
    1.优化工单统计中的一个慢sql，使用了STRAIGHT_JOIN选择索引
    2.优化了工单暂存的一个人慢sql 使用了force index指定索引，这个只支持有IDX_CC_AGENT_ORDER_1工单表索引的，如果不存在请增加索引
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240715-1
修改日期: 2024-07-15
修改人:   ocj
版本修改内容
	1、解决easitline-static版本bug，导致WdatePicker()获取不到时间问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240712-1
修改日期: 2024-07-12
修改人:   ocj
版本修改内容
	1、增加按企业开启科大助手功能
	2、科大助手sdk对接，需要同步升级cx-mix-base
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240710-1
修改日期: 2024-07-10
修改人:   ocj
版本修改内容
	1.增加按企业开启助手功能，需同步升级20240710版本后的cx-mix-base
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240521-1
修改日期: 2024-05-21
修改人:   ocj
版本修改内容
	1、兼容视频客服环境，增加相关配置项，视频客服环境开启
	2、通话记录、录音播放增加视频客服相关定制化cdr-query-video.jsp、record-play-video.jsp
    3、首页根据配置项判断是否开启网络视频客服功能
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240521-1
修改日期: 2024-05-21
修改人:   ocj
版本修改内容
	1、兼容视频客服环境，增加相关配置项，视频客服环境开启
	2、通话记录、录音播放增加视频客服相关定制化cdr-query-video.jsp、record-play-video.jsp
    3、首页根据配置项判断是否开启网络视频客服功能
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240510-1
修改日期: 2024-05-10
修改人:   刘稳
版本修改内容
	1、工单给下拉框、单选框、多选框设置默认值
	2、通话记录 我的通话记录增加听完录音的颜色标识
	3、通话记录、我的通话记录 固定搜索条件

影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240422-1
修改日期: 2024-04-22
修改人:   刘稳
版本修改内容
	1、工单给开始时间和结束时间设置默认值
	2、我的通话记录界面新增补单功能
	3、通话记录和我的通话记录新增选择框，可以选择某几条导出
	4、漏话记录、通话记录界面新增离线导出
	5、留言新增离线转写功能

影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240412-1
修改日期: 2024-04-12
修改人:   刘稳
版本修改内容
	1、工单列表和通话记录列表新增离线导出功能。

影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240410-1
修改日期: 2024-04-10
修改人:   ocj
版本修改内容
	1、优化来电弹屏业务、支持通过9059配置设置是否来电覆盖弹屏
	2、解决偶发情况下多次弹屏bug

影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240408-1
修改日期: 2024-04-08
修改人:   刘稳
版本修改内容
	1、调整工单列表的时间，都调整为yyyy-MM-dd HH:mm:ss格式
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240312-1
修改日期: 2024-03-12
修改人:   刘稳
版本修改内容
	1、云客服工单普通角色（除管理员和班长）目前查看全部工单显示为空，需要做权限控制
	2、云客服工单级联模版导入存在问题，现在进行迁移优化
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240117-1
修改日期: 2024-01-17
修改人:   刘稳
版本修改内容
	1、在首页面新增个人信息功能按钮，点击后展示个人信息
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20240104-1
修改日期: 2024-01-04
修改人:   王丹
版本修改内容
	1、兼容话单表中满意度字段默认值为-1的情况。（-1是未邀约评价，后期要区分满意度类型时，把下面隐藏去掉，改为未邀约评价）涉及cdr-query.jsp、my-cdr.jsp俩个页面
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231228-1
修改日期: 2023-12-28
修改人:   ocj
版本修改内容
	1、增加通话记录查询日志、质检详情sql日志
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231226-2
修改日期: 2023-12-26
修改人:   ocj
版本修改内容
	1、修改管理员重置密码功能，需要同步升级yc-base
	2、增加界面web版坐席助手
影响范围：
	使用yc-agent及管理员重置密码功能
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231226-1
修改日期: 2023-12-26
修改人:   刘稳
版本修改内容
	1、添加部分接口xss拦截
	2、修改发送公告拦截，新增拦截黑名单，黑名单内的内容无法提交到公告中
影响范围：
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231207-1
修改日期: 2023-12-07
修改人:   王鹏远
版本修改内容
	1、添加部分接口xss拦截
影响范围：
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231117-1
修改日期: 2023-11-30
修改人:   王鹏远
版本修改内容
	1、话务条呼入弹窗适配中国广电
	2.修复yc-agent部分漏洞
影响范围：
	话务条呼入弹窗的运营商显示
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231117-1
修改日期: 2023-11-17
修改人:   ocj
版本修改内容
	1、通话记录、我的通话记录增加外呼15话机异常标识。
影响范围：
	通话记录、我的通话记。
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231103-1
修改日期: 2023-11-03
修改人:   王丹
版本修改内容
	1、通话记录sql添加查询字段，t1.CREATE_TIME,t1.AGENT_STAY_TIME。导出通话记录方法新增判断是否导出振铃时长字段。
影响范围：
	通话记录查询，导出。
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20231102-1
修改日期: 2023-11-02
修改人:   王鹏远
版本修改内容
	1、大兴社保，工单导出中默认添加当前步骤字段
影响范围：
	工单导出功能
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230817-2
修改日期: 2023-08-17
修改人:   刘稳
版本修改内容
	1、修改工单浏览权限漏洞
	2、修改回显配置的登录界面logo时后台查询不到业务库问题
影响范围：
	弹屏公告和统计坐席数功能验证
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230817-1
修改日期: 2023-08-17
修改人:   ocj
版本修改内容
	1、首页faq编辑+faq查询鼠标点击变成小手形状
	2、faq编辑只允许管理员权限查看
影响范围：
	弹屏公告和统计坐席数功能验证
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230712-2
修改日期: 2023-07-12
修改人:   王鹏远
版本修改内容
	1.ws修改为定时ajax方式请求
影响范围：
	弹屏公告和统计坐席数功能验证
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230710-2
修改日期: 2023-07-10
修改人:   ou
版本修改内容
	1.增加未接来电删除功能、支持移除后报表数据重新统计
	2.增加移除记录表，避免删除后记录找回等相关问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230614-2
修改日期: 2023-06-14
修改人:   刘稳
版本修改内容
	1.修改前台获取logo没有验证漏洞
	2.修改未登录可以访问portal下的文件漏洞
	3.修改云客服工单指派人偶发出现展示出两个下拉框漏洞
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230614-1
修改日期: 2023-06-14
修改人:   王鹏远
版本修改内容
	1.优化统计登录用户信息功能，支持https时wss连接
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230530-1
修改日期: 2023-05-11
修改人:   欧诚剑
版本修改内容
	1.优化通话记录、我的通话记录查询主被叫号码查询，根据后台配置是否加密判断是否增加加密验证
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230511-1
修改日期: 2023-05-11
修改人:   欧诚剑
版本修改内容
	1.优化漏话技能组查询条件问题、导出问题
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230505-1
修改日期: 2023-05-05
修改人:   欧诚剑
版本修改内容
	1.增加当前登录用户所在服务器，沿用WebSocket请求，30秒一次，缓存有效期2分钟一次
	2.如果要查询当前所在服务器web端使用用户有多少，在redis客户端通过 KEYS CC-BASE-USER-ONLINE-STATE-174\175(这个根据配置项配置而定)*，避免在代码里面这样模糊搜索，影响redis性能
影响范围：
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230504-2
修改日期: 2023-05-04
修改人:   刘稳
版本修改内容
	1.修改每个企业可以配置不同的侧边栏logo
影响范围： 
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230504-1
修改日期: 2023-05-04
修改人:   欧诚剑
版本修改内容
	1.增加logo配置功能、支持上传、同时，该上传方法调用的cc-base
影响范围： 
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230428-1
修改日期: 2023-04-28
修改人:   王鹏远
版本修改内容
	1.添加首页系统公告弹屏功能，发公告界面在lt59包中
影响范围： 
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230427-1
修改日期: 2023-04-27
修改人:   王鹏远
版本修改内容
	1.添加跳转首页可配置功能，可根据企业跳转不同界面，需要参数配置中配置
	2.定制疾控中心首页界面
影响范围： 
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230228-1
修改日期: 2023-02-28
修改人:   王鹏远
版本修改内容
	1.将坐席管理界面移过来，修改界面添加提示，后台还是走yc-base
	 界面地址：yc-agent/pages/entmgr/user-list.jsp?isPhonNum=前缀提示，同标准版
	2.提交之前工单级联树配置代码
影响范围： 
	无
升级脚本：
	无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20230131-1
修改日期: 2023-01-31
修改人:   欧诚剑
版本修改内容
	1.注释席间交流相关功能
影响范围： 
	修改密码
升级脚本：
	 无
---------------------------------------------------------------------------------------
版本号:  3.3.0#20221228-1
修改日期: 2022-12-28
修改人:   欧诚剑
版本修改内容
	1.修改密码界面指向yc-login
影响范围： 
	修改密码
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：3.3.0#20220805-1
版本号:  3.3.0#20220805-1
修改日期: 2022-08-05
修改人:   丁泉铭
版本修改内容
	1.首页[话务设置]中新增是否自定义来电振铃音选项，音频文件为localStorage有效；
影响范围： 
	1.来电振铃音
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：3.3.0#20220721-1
版本号:  3.3.0#20220721-1
修改日期: 2022-07-01
修改人:   刘稳
版本修改内容
	1.在坐席工作日志界面新增导出功能【agent-work-log.jsp】
影响范围：
	1.坐席工作日志
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：3.3.0#20220713-1
版本号:  3.3.0#20220713-1
修改日期: 2022-07-13
修改人:   王丹
版本修改内容
	1.解决管理员角色查询，坐席服务水平报表无数据问题。只注释了一行代码。com.yunqu.yc.agent.dao.report.call.ServiceLevelDao.ivrCallInList.29行
影响范围： 
	1.工单
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：3.2.1#20220620-1
版本号:  3.2.1#20220620-1
修改日期: 2022-06-20
修改人:   刘稳
版本修改内容
	1.当工单模板未配置字段后新建完工单进入已完成工单或我参与工单界面会报错 现已修改
影响范围： 
	1.工单
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220424-1
版本号:  2.8.1#20220424-1
修改日期: 2022-04-24
修改人:   刘稳
版本修改内容
	1.修改工单排行榜查看了所有企业的数据
	2.修改工单查询列表中，已完结的工单，“完结时间”显示为空
	3.存量客户管理页面添加客户资料时 标识“客户编号”必填项（样式）
影响范围： 
	1.工单查询、客户资料管理
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220411-1
版本号:  2.8.1#20220411-1
修改日期: 2022-04-11
修改人:   刘稳
版本修改内容
	1.修改工单查询 业务工单→工单管理→已完成的列表中，无“完结时间” 修改文件【OrderDao.java】【OrderServlet.java】
影响范围： 
	1.工单新增、工单管理中全部工单和已完成工单
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220407-1
版本号:  2.8.1#20220407-1
修改日期: 2021-04-07
修改人:   刘稳
版本修改内容
	1.修改外线坐席没有振铃弹屏 修改文件【ccbar_agent.js】
影响范围： 
	1.振铃弹屏
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220330-1
版本号:  2.8.1#20220330-1
修改日期: 2022-03-30
修改人:   刘稳
版本修改内容
	1.振铃弹屏没有显示出被叫号码问题 修改文件【ccbar_agent.js】
影响范围： 
	1.振铃弹屏
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220316-1
版本号:  2.8.1#20220316-1
修改日期: 2022-03-16
修改人:   刘稳
版本修改内容
	1.修改工单查询全部界面 根据模板的配置选择导出列表 修改文件【ExportServlet.java】
影响范围： 
	1.工单管理下的导出全部工单
升级脚本：
	 无
---------------------------------------------------------------------------------------
升级版本说明：2.8.1#20220303-1
版本号:  2.8.1#20220303-1
修改日期: 2022-03-03
修改人:   刘稳
版本修改内容
	1.修改外呼弹屏不回显客户资料bug，修改文件【CallUrlService.java】
影响范围： 
	1.开启外呼来电弹屏的用户
升级脚本：
	 无
---------------------------------------------------------------------------------------
模块名：yc-agent
版本号:  1.0#20211108-1
修改日期: 2021-11-08
修改人:  刘稳
说明:
	1、修改已完成工单不可更改在order-core.js的OrderCore.orderInfo方法中进行判断完成的工单隐藏保存按钮和不可更改工单内容
---------------------------------------------------------------------------------------
