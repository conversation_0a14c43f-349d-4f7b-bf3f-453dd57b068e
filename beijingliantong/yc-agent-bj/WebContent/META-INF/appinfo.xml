<?xml version="1.0" encoding="UTF-8"?>
<application id="yc-agent" name="云客服标准版-v3" resTable="ycBusiRes#003" version="3.3.0#20250716-1" >

    <datasources>
        <datasource name="yc-wirte-ds-1" description="云客服数据源(写1)" isnull="true" /> 
        <datasource name="yc-wirte-ds-2" description="云客服数据源(写2)" isnull="true" /> 
        <datasource name="yc-read-ds" description="云客服数据源(读)" isnull="true" /> 
    </datasources>
    <description>
		3.3.0#20250716-1
		1.我的通话记录中，呼叫结果100改为其它呼叫失败
		2.我的待办工单增加紧急程度标识
		3.3.0#20250709-1
		1.修改云客服工单，保存修改为"转派-指派组"、工单催办内容增加工单编号、催办工单列表为空、切换“指派组”的时候 指派人框未清空
		2.导出时工单级联最大5000条导致导出不全
		3.3.0#20250626-1
		1.修改云客服工单，暂存后再次提交生成了两张一样id的工单
		2.修复"/out/attachment接口未授权访问漏洞，只有图片才可以未授权访问
		3.3.0#20250623-1
		1.人社部定制满意度;
		3.3.0#20250603-1
		1.新增9059定向公告对接；
		3.3.0#20250310-1
		1.新建工单时，如果当前流程有多个流转流程，则将多个按键调整为一个按键“保存”，每个按键指派人调整为下拉框选择
		2.来电弹屏，通过最初用户的按键拉起对应的工单模版
		3.新增工单催办功能，可以催办工单进度，如果选择短信则可以发送短信通知
		4.新增工单撤销功能，直接关闭当前工单
		5.新增工单撤回功能，工单撤回到上一步
		6.工单删除增加角色控制，不能所有人都可以删除工单
		7.新建工单增加发送短信功能，在工单模版中配置开启发送短信
		3.3.0#20250219-1
		1.处理班长用户登录云客服后首页的统计数据是个慢sql问题,因为要升级就不改版本号
		3.3.0#20250208-1
		1.通话记录和我的通话记录导出功能新增导出pdf格式
		2.工单增加紧急程度标识
		3.新增催办工单列表查询功能和导出功能
		3.3.0#20250207-1
		1.来电接听时，新增配置，支持是否允许显示挂机按钮。2.话务报表-坐席话务统计，分页支持选择500条/页
		3.3.0#20250121-1
		1.我的通话记录、通话记录列表新增排队时长、挂机方向字段，查询条件新增挂机方向和排队时长条件。导出也同步新增排队时长、挂机方向字段。
    	3.3.0#20250114-1
    	1.解决坐席工作日志根据坐席id导出不了的bug
    	3.3.0#20241213-1
    	1.在大屏右上角新增技能组筛选条件
    	2.处理修改模版时候的慢sql问题
		3.3.0#20241031-1
		1.增加按技能组选择签入功能、如需开启，在企业-配置里面自定义新增LOGIN_SELECT_GROUP=true开启，否则不开启，不影响原有客户，不过需要测试一下签入签出、接打电话
		3.3.0#20241010-1
    	处理下述菜单的导出变为easyExcel方式
	    客户资料数据导出	/yc-agent/pages/report/cust/cust-export.jsp
		离线导出工单	/yc-agent/pages/order2/order/order-query-list.jsp
		离线导出通话记录	/yc-agent/pages/record/cdr-query.jsp
		离线导出漏话记录	/yc-agent/pages/record/call-miscall-list.jsp
		坐席工作日志	/yc-agent/pages/log/agent-work-log.jsp
		全媒体接入记录	/yc-agent/pages/media/record/media-access-list.jsp
		全媒体人工会话记录	/yc-agent/pages/media/record/media-chat-list.jsp
		工单管理V2	/yc-agent/pages/order2/order/order-query-list.jsp
		质检结果	/yc-agent/pages/qc/qc-result-list.jsp
		漏话列表	/yc-agent/pages/record/call-miscall-list.jsp
		我的通话记录	/yc-agent/pages/record/my-cdr.jsp
		通话记录查询	/yc-agent/pages/record/cdr-query.jsp
		通话记录查询	/yc-agent/pages/record/cdr-query-video.jsp
		IVR轨迹列表	/yc-agent/pages/record/ivr-trace-list.jsp
    	3.3.0#20240809-2
    	1.对接助手转写，实现自动填单
		3.3.0#20240806-1
		1、优化我的通话记录取值问题
    	3.3.0#20240719-1
    	1.优化工单统计中的一个慢sql，使用了STRAIGHT_JOIN选择索引
    	2.优化了工单暂存的一个人慢sql 使用了force index指定索引，这个只支持有IDX_CC_AGENT_ORDER_1工单表索引的，如果不存在请增加索引
		3.3.0#20240712-1
		1.增加按企业开启科大助手功能，需同步升级20240710版本后的cx-mix-base
		3.3.0#20240710-1
			1.增加按企业开启助手功能，需同步升级20240710版本后的cx-mix-base
		3.3.0#20240524-1
			1.兼容视频客服环境yc-agent，避免视频客户环境定制化yc-agent
			2.增加视频客服配置项功能，在视频客服环境开启即可，其余环境默认关闭
		3.3.0#20240422-1 edit by dqm 2024-04-22 11:50:00
			1.系统参数配置增加是否开始话务设置全部权限;
			2.来电振铃音增加两个预设,自定义限制500kb,优化相关逻辑;
		3.3.0#20240410-1
			1、优化来电弹屏业务、支持通过9059配置设置是否来电覆盖弹屏，解决偶发情况下多次弹屏bug
    	3.3.0#20240408-1
    		1、调整工单列表的时间，都调整为yyyy-MM-dd HH:mm:ss格式
    	3.3.0#20240117-1
    		1、在首页面新增个人信息功能按钮，点击后展示个人信息
		3.3.0#20231226-2
    		1、修改管理员重置密码功能，需要同步升级yc-base
			2、增加界面web版坐席助手
    	3.3.0#20231226-1
    		1、添加部分接口xss拦截
			2、修改发送公告拦截，新增拦截黑名单，黑名单内的内容无法提交到公告中
		3.3.0#20231117-1
			1.通话记录、我的通话记录增加15话机异常标识
			2.话务条呼入弹窗适配中国广电
			3.修复yc-agent部分漏洞
		3.3.0#20231103-1
			1.通话记录sql添加查询字段，t1.CREATE_TIME,t1.AGENT_STAY_TIME。导出通话记录方法新增判断是否导出振铃时长字段。
		3.3.0#20231102-1
			1.工单导出添加当前步骤字段
    	3.3.0#20230720-1
    		1.优化请求，websocket改为ajax
   		3.3.0#20230614-1
    		1.优化统计登录用户信息
    	3.3.0#20230530-1
    		1.优化通话记录、我的通话记录查询主被叫号码查询，根据后台配置是否加密判断是否增加加密验证
    	3.3.0#20230505-1
    		1.增加logo上传功能
			2.增加所在服务器web端登录用户信息
   		3.3.0#20230428-1
    		1.添加首页公告弹屏功能
   		3.3.0#20230427-1
    		1.添加跳转首页可配置功能，可根据企业跳转不同界面，需要参数配置中配置
			2.定制疾控中心首页界面
    	3.3.0#20230307-1
    		1.修改工单中有两个以上的复选框报错
    	3.3.0#20230228-1
    		1、迁移坐席管理界面到yc-agent
    	3.3.0#20221025-1
    		1、修改每日运营统计呼出平均通话时长计算错误问题；
		3.3.0#20220929-1
			1、修改工单侧边栏sql注入的问题
			2、工单页面侧边栏手机号信息兼容优化
		3.3.0#20220927-1
			1、修改工单侧边栏显示为根据企业ID显示
    	3.3.0#20220926-1
    		1、修改通话记录问题；
    		2、修改工单侧边栏问题；
    	3.3.0#20220924-1
    		1、修复分时统计问题；
    		2、兼容云客服挂载云电销，支持自动任务来电弹屏；
    		3、解决工作组坐席绑定不能清空问题；
    		4、修改工单回显来电主被；
		3.3.0#20220922-1
			1、新增工单侧边栏配置 2、新增cc_agent_order_tabs工单侧边栏表
	    3.3.0#20220920-1
	     	1、改造修改密码方式；2、优化导出当前页录音方式；
    	3.3.0#20220916-1
    		1、简单工单添加回显主被叫功能；
    		2、简单工单导出功能优化；
    	3.3.0#20220915-1
    		1、修改大屏问题；
    		2、添加工单ivr订购功能；
		3.3.0#20220914-1
			1.全媒体菜单增加“坐席辅助功能权限”菜单。
    	3.3.0#20220913-1
    		1、修改坐席工作日志导出问题；
    	3.3.0#20220907-1
    		1、限制通知长度；
    		2、解决报表查找多坐席问题；
		3.3.0#20220830-1
			1、优化全媒体配置-满意度指标配置
		3.3.0#20220829-1
			1、优化全媒体相关配置
    </description>
	<versionHistory>
		3.3.0#20220822-1
			1、去除所有事务处理；
			2、调整postgreSql语法；
			3、调整快速入口bug;
		3.2.8#20220811-1
			1、优化录音文件读取的逻辑，增加录音文件路径拓展，兼容多台录音文件服务器。
			2、优化全媒体渠道按键配置，增加按键是否显示，需执行脚本 ALTER TABLE ycmain.CC_CHANNEL_KEY ADD IS_SHOW INT DEFAULT 1 COMMENT '按键是否显示，0不显示，1显示';
		3.2.7#20220811-1
			1、删除isRes判断权限；
			2、修改菜单拦截器；
		3.2.6#20220801-1
			1、坐席工作情况添加示忙其他统计，满足接口对接的情况
		3.2.5#20220805-1
			1.首页[话务设置]中新增是否自定义来电振铃音选项，音频文件为localStorage有效；
		3.2.5#20220728-1
			1、新增ivr轨迹查询列表，关联表【CC_IVR_TRACE】；
			2、修复为创建工单模板是，来电弹屏报错问题；
			3、新增坐席状态日志列表，关联表【CC_AGENT_STATE_LOG】；
			4、修改在oracle环境上，工单不能distinct【ORDER_LOG】大字段；
			5、添加企业拓展配置信息到localStorage，使用方式，调用getEntExtConfig(key)；
			6、考虑到很多环境用不到ivr语音播报，默认隐藏，如果需要使用，在企业拓展配置添加【IVR_PALY:1】；
		3.2.4#20220721-1
			1.在坐席工作日志界面新增导出功能【agent-work-log.jsp】
		3.2.3#20220706-1
			1、添加IVR按键配置，添加表【CC_IVR_KEY】，路径/yc-agent/pages/entmgr/ivr-conf-list.jsp；
			2、增加IVR节点统计，路径/yc-agent/pages/report/call/ivrTraceStat.jsp
		3.2.2#20220624-1
			1、修改菜单查询方式；
			2、未配置工单模板时来电弹屏报错；
		3.2.1#20220620-1
			1.当工单模板未配置字段后新建完工单进入已完成工单或我参与工单界面会报错 现已修改
		3.2.1#20220601-1
			1.JSP页面提交增加token校验，用于防止跨站请求伪造（CSRF）攻击.
		3.2.1#20220519-1
			1.优化渠道配置-按键配置，按键可关联队列，不需要在技能组管理中配置服务渠道
		3.2.1#20220519-1
			1、添加菜单拦截器，通过yc-base配置启动，默认不启动；
		3.2.0#20220516-1
			1、通话记录、我的通话记录界面，通话时长查询条件可支持手填查询
			2、解决漏话时间选择bug。
			3、通话记录、我的通话记录界面、漏话 新增可以配置查询天数功能（如该界面地址后面加：?exportMaxDay=120），默认31天
			
		2.8.1#20220424-1
			1、解决通话记录查询页面无法查询到数据，SQL执行超时。
			2、解决通话记录的录音播放不了的问题。
			 1.修改工单排行榜查看了所有企业的数据
			 2.修改工单查询列表中，已完结的工单，“完结时间”显示为空
			 3.存量客户管理页面添加客户资料时 标识“客户编号”必填项（样式）
						 
		2.8.1#20220422-1 1.话务条新增静音功能，得同步升级callserver、iccs
		2.8.1#20220411-1 1.修改工单查询 业务工单→工单管理→已完成的列表中，无“完结时间” 修改文件【OrderDao.java】【OrderServlet.java】
	  	2.8.1#20220407-1 1.修改外线坐席没有振铃弹屏 修改文件【ccbar_agent.js】
	    2.8.1#20220330-1 1.振铃弹屏没有显示出被叫号码问题 修改文件【ccbar_agent.js】
		2.8.1#20220316-2
			1、解决我的通话记录 通话录音bug
    	2、大屏监控数据库权限问题
	    2.8.1#20220316-1 修改工单查询全部界面 根据模板的配置选择导出列表 修改文件【ExportServlet.java】
		2.8.1#20220314-1 1.首页界面的快速入口模块改为可配。添加数据库表【cc_agent_quick_entry】脚本。
						 2.优化工单提醒，添加心跳机制。
		2.8.1#20220303-1 修改外呼弹屏不回显客户资料bug，修改文件【CallUrlService.java】
	    2.8.1#20220228-1 新增振铃弹屏、外呼弹屏功能  需要在9059弹屏配置 进行配置
		2.8.1#20220218-1 修改指派人的bug 修改文件【order-new.jsp】
		2.8.1#20220217-1 关闭新建工单时左上角的框 修改文件【order-new.jsp】
		2.8.1#20220214-1 1.将工单提醒在公告中独立出来 
						 修改文件【NoticeDao.java、PortalCommonDao.java、ReceiveMessageService.java、NoticeAlertServlet.java
							     NoticeServlet.java、notice-list.jsp、portal_common.js、portal_template.jsp、portal-agent.jsp、system.css】
						 添加文件【notice-alert-detail.jsp、notice-alert-list.jsp】
						 添加表  【CC_NOTICE_ALERT】
		2.8.1#20220209-1 1.修改漏话查看规则 管理员可以查看所有漏话 修改文件【CallNoAnswerDao.java】
		2.8.0#20220208-1 1.导出数据量由配置统一控制
		2.7.9#20220128-1 1.优化工单提醒 修改文件OrderServlet.java
		2.7.9#20220127-1 1.优化工单提醒并添加催办类型 修改文件【Constants.java、ReceiveMessageService.java、NoticeAlertServlet.java、NoticeServlet.java
							portal-agent.jsp、portal-engineer.jsp】
		2.7.9#20220124-1 1.通话记录查询适配原来的分页栏 【例：/yc-agent/pages/record/cdr-query.jsp?allExport=true则为显示原来的分页栏】
					     2.修改通话记录查询中客户号码筛选条件不可用bug 修改文件【cdr-query.jsp、my-cdr.jsp】
		2.7.9#20220124-1 1.漏话、未接、留言只留时间统计维度；2.工单级联查询类型问题
		2.7.9#20220121-2 1.修改历史工单一开始加载很多录音文件问题，2修改工单历史录音兼容不了wav格式问题,3导出坐席多选问题
		2.7.9#20220121-2 1.在工单来电弹屏上新增被叫号码修改文件【order-new.jsp】
		2.7.9#20220121-1 1.修改暂存中发起工单数据不回显 修改文件【order-new.jsp】
		2.7.8#20220120-2 1.代办工单查询效率低，添加默认时间，创建索引，提高查询效率
		2.7.8#20220120-1 1.优化工单配置中发送通知和指定人按钮样式 修改文件【template-flow-config.jsp】
						 2.修改bug云客服工单配置好的级联，必选项失效，没选还是能提交 修改文件【form-creater.js】
		2.7.7#20220119-1 优化sql并将RESERVE1字段存储下一级指派人改为HANDLER_ID字段存储并更新HANDLER_TYTPE状态为20 修改文件【order-my-deal.jsp】
						【order-new.jsp】【OrderServlet.java】【OrderDao.java】
		2.7.6#20220117-1 新增指派人功能 在工单模板配置中、每个模板下的每个流程的步骤动作中新增是否开启指派人选项，选择是后此动作中开启指派人，不选默认工单
						 发送全部人，选择人后单独只给这一个人，修改文件【OrderTempDao.java】【OrderTempDao.java】【OrderServlet.java】【CustDataServlet.java】
						 【order-new.jsp】【order-my-deal.jsp】【temp.jsp】【template-flow-config.jsp】【order-core.js】新增在cc_agent_order_action表中新增
						 字段ASSIGN_PERSON功能是'是否开启指派人'，使用cc_agent_order中的RESERVE1保留字段作为存储下一级指派人ID
		2.7.5#20220112-1 新增满意度评价按钮，在后台企业控制，跟据表CC_ENT_VOX字段VOX_TYPE为8并且不存在为4
		2.7.4#20220107-1 修改来电弹屏有一个加载状态 修改位置【voice_portal_v2.jsp】
		2.7.4#20220106-1 修改通话记录，修改呼叫方向、主被叫查询条件和分页方式
		2.7.4#20211231-1
			1.漏话列表点击处理时，处理人读取业务库中的坐席姓名。 修改文件：CallNoAnswerServlet.java
			2.修复分时话务统计中，不选技能组数据都为空。 修改文件:StatistcServiceSql.java
			3.报表统计中可以加导出不仅只有界面显示的数据，界面须传入参数allExport=1。
				修改文件：agentCallStat.jsp entCallStat.jsp groupCallStat.jsp hourCallStat.jsp serviceLevelReport.jsp wholeOperate.jsp
		2.7.4#20211229-2  
			1.修改企业未配置工单模板时，点击【新建工单】，页面提示【出现网路故障，请稍后再试】 修改文件 order-new.jsp
		2.7.4#20211229-1
			1.坐席来电未接示忙时弹窗提醒，是否重新示闲。 修改文件为ccbar_agent.js ccbar-agent-setting.jsp
			2.工单级联管理修改切换级联树表格控件。 修改文件为order-select-config.jsp treeTable.css
		2.7.4#20211224-1
			1.现场管理”→“座席工作情况”→“导出”，发现接话次数列，系统页面是0的，导出的报表单元格部分为空 修改文件为ExportServlet.java
		2.7.4#20211220-2
			1.修改bug通话录音管理→通话记录查询，筛选座席工号后，导出通话记录，400 – Bad Request  修改文件：cdr-query.jsp、ExportServlet.java
		2.7.4#20211220-1
			1.修改点击【工单完成】弹窗报Column 'DATA1' in field list is ambiguous的问题 修改文件OrderServlet.java。
			2.修改已完成的工单不能修改、未完成的工单修改保存后数据消失问题 修改文件order-info.jsp、form-creater.js、order-core.js、template-edit.jsp。
			3.修改从cc_busi_user表取坐席名称,原逻辑是从cc_user中取值 修改文件OrderDao.java
		2.7.4#20211216-1
			1.webcall权限控制
		2.7.4#20211215-1
			1.通话记录新增号码归属地字段
		2.7.4#20211124-1 
			1、修改工单管理导出工单时级联数据为ID的问题，修改的文件有ExportServlet.java
		2.7.4#20211123-1 
			1、将工单管理工单查询中级联条件为input输入框改为cascader级联框，修改的文件为order-query-list.jsp、order-query-finished-list.jsp、order-query-dealing-list.jsp、order-query-temp-list.jsp和OrderDao.java
			2、修改同一模板不同级联下拉框使用同一个级联树是产生的id重复bug，修改的文件为temp.jsp和form-creater.js
			3、修改完成工单查看时点击不了呼叫问题，修改文件custInfo.jsp
		2.7.4#20211118-3 1、优化流程流转配置下的步骤动作中增加是否发送消息单选框的代码，修改的位置有template-flow-config.jsp、OrderServlet.java
			2、在数据库cc_agent_order_action新增字段SEND_MSG 作用是标识是否发送信息
		2.7.4#20211118-2 
			1、添加工程师工单首页。
			2、添加配置项IS_ORDER_INDEX
			3、修改Constants.java OrderDao.java WorkbenchServlet.java portal_common.js 
			4、新增 portal-engineer.jsp echarts.min.js
			5、修改工单关联级联模板后，列表显示问题
		2.7.4#20211118-1 1、修改工单多级下拉问题；2、修改工单关联模板问题；3、新增录音批量导出；
		2.7.3#20211112-2 1、在流程流转配置下的步骤动作中增加是否发送消息单选框，勾选后流程执行到下一步时发送消息进行提醒
			修改的内容有template-flow-config.jsp、OrderServlet.java、Constants.java
		2.7.2#20211112-1
			1、首页新增右下角新消息弹窗提醒。
			2、提供消息弹窗信息接口,可指定用户接收消息。
			3、修改NoticeDao.java PortalCommonDao.java GlobalContextListener.java NoticeServlet.java
				notice-list.jsp notice-mgr.jsp portal_common.js portal-agent.jsp
			4、新增 ReceiveMessageService.java NoticeAlertServlet.java
			5、新增表CC_NOTICE_USER
		2.7.3#20211108-1 1、修改已完成工单不可更改在order-core.js的OrderCore.orderInfo方法中进行判断完成的工单隐藏保存按钮和不可更改工单内容
		2.7.2#20211027-1 1、工单查询导出限制；2、我的工单处理脚本优化；
		2.7.2#20210609-1
			1、工单模板配置添加级联下拉框。
			2、新增页面级联配置管理。
			3、修改OrderServlet.java OrderTempFlowServlet.java OrderTempServlet.java Orderdao.java
			order-my-temporary.jsp order-new.jsp form-create.jsp temp.jsp template-edit.jsp
			template-flow-config.jsp template-list.jsp charts.jsp mycharts.js form-creater.js order-core.js
			4、新增OrderRoleServlet.java OrderSelectConfigServlet.java OrderSelectConfigDao.java 
			order-servlet-config.jsp order-selectConf-edit.jsp cascader-select.jsp 
    	2.1.1#20181019
    		1、
    		2、
    	2.1.1#20181010
    		1、
    		2、
    	2.1.2#20181102
    		1、修改坐席工作日志bug
    	2.1.2#20181108
    		1、修改通话记录中，无法查询呼出的客户号码
   		2.0#20190218
    		1、增加全媒体统计报表
    	2.0#20190226
    		1、解决全媒体报表导出结果与查询不符合的问题。
    	2.0#20190521
    		1、全媒体服务记录中增加服务小结字段，SERVICE_RESULT
    		alter table CC_MEDIA_RECORD add SERVICE_RESULT varchar2(1000);
			comment on column CC_MEDIA_RECORD.SERVICE_RESULT is '服务小结';
    	2.0#20191120
    		1、添加话务报表
    		2、添加全媒体报表
    </versionHistory>
</application>
