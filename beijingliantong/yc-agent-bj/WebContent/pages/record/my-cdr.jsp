<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>我的通话记录</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" name="searchForm" id="searchForm" >
		<div class="ibox">
			<div class="ibox-title"  id="divTitle" style="position: fixed;width:98%;z-index:1000;top: -1vh;margin-top: 1vh;">
				<div class="form-group">
						<h5><span class="glyphicon glyphicon-earphone"></span> 我的通话记录</h5>
						<div class="input-group input-group-sm pull-right btn-group">
				   			<a class="btn btn-sm btn-success"  href="javascript:void(0)" onclick="Report.normalExport()"><i class="glyphicon glyphicon-export"></i> 批量导出 </a>
				   			<a class="btn btn-sm btn-info"  href="javascript:void(0)" onclick="Report.exportRepotFiles()"><i class="glyphicon glyphicon-import"></i> 下载录音 </a>
				    	</div>
			    </div>
			    <hr style="margin: 5px -15px">
				<div class="form-group">
						<div class="input-group input-group-sm">
							<span class="input-group-addon">呼叫方向</span>
               				<select name="callType" class="form-control" onchange="Report.loadData()">
								<option value="0">全部</option>
								<option value="1">呼出</option>
								<option value="2">呼入</option>
								<option value="3">席间呼叫</option>
								<option value="4">呼转外线</option>
								<option value="9">其他</option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">开始时间</span> 
							<input  name="startTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="startTime" data-mars-reload="false" id="startDate" data-mars-top="true"  data-mars="common.todayFirstTime"  class="form-control input-sm Wdate">
						</div>
						
						<div class="input-group input-group-sm">
							<span class="input-group-addon">结束时间</span> 
							<input  name="endTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="endTime" data-mars-reload="false" data-mars-top="true"  data-mars="common.todayEndTime" class="form-control input-sm Wdate">
						</div>
						<!-- <div class="input-group input-group-sm for-hiddenSys hidden">
							<span class="input-group-addon">主叫</span> 
							<input  name="caller"  size = "12"  class="form-control">
						</div>
						<div class="input-group input-group-sm for-hiddenSys hidden">
							<span class="input-group-addon">被叫</span> 
							<input  name="called"  size = "12"  class="form-control">
						</div> -->
						<div class="input-group input-group-sm">
							<span class="input-group-addon">
							<select style="border: none;background-color: #fafafa;height: 18px;cursor: pointer;" id="callFlag" name="callFlag" >
							<option value="1">主叫号码</option>
							<option value="2">被叫号码</option>
	             		    </select>
	             		    </span> 
							<input id="callPhone" size = "11"  class="form-control" name="callPhone">	
					 </div>
						<div class="input-group input-group-sm hidden">
							<span class="input-group-addon">满意度</span> 
							<select name="satisfId" id="satisfId" class="form-control">
								<option value="">请选择</option>
								<option value="0">未评价</option>
								<option value="1">非常满意</option>
								<option value="2">满意</option>
								<option value="3">一般</option>
								<option value="4" data-class="label label-warning">不满意</option>
								<%-- 2024-01-04 兼容 话单表中满意度字段默认值为-1的情况。（-1是未邀约评价，后期要区分满意度类型时，把下面隐藏去掉，改为未邀约评价） --%>
								<option value="-1" style="display: none" >未评价</option>
							</select>
						</div>
						<div class="input-group input-group-sm hidden">
							<span class="input-group-addon">呼叫结果</span> 
							<select name="clearCause" id="clearCause" class="form-control">
								<option value="">请选择</option>
								<option value="0">成功</option>
								<option value="1">无人应答</option>
								<option value="2">用户忙</option>
								<option value="3">用户挂机</option>
								<option value="4">网络忙</option>
								<option value="5">空号</option>
								<option value="6">拒接</option>
								<option value="7">关机</option>
								<option value="8">停机</option>
								<option value="9">不在服务区</option>
								<option value="10">传真机</option>
								<option value="11">欠费</option>
								<option value="12">重复号码</option>
								<option value="13">电话总机</option>
								<option value="14">久叫不应</option>
								<option value="15">话机异常</option>
								<option value="50">回铃音反馈-关机</option>
								<option value="51">回铃音反馈-空号</option>
								<option value="52">回铃音反馈-停机</option>
								<option value="53">回铃音反馈-用户拒接</option>
								<option value="54">回铃音反馈-用户忙</option>
								<option value="55">回铃音反馈-不在服务区</option>
								<option value="56">回铃音反馈-无应答</option>
								<option value="98">坐席挂机</option>
								<option value="99">系统错误</option>
								<option value="100">其它呼叫失败</option>
					  	   </select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">通话时长(s)</span> 
						<input id="startBillTime" name="startBillTime" size="2" class="form-control" autocomplete="off" type="number" onkeypress="return event.charCode >= 48" min="0" max="60" oninput="if (value < 0) value = 0;if(value>36000)value=36000"> 
						<span class="input-group-addon">-</span>
						<input id="endBillTime" name="endBillTime" size="2" class="form-control" autocomplete="off" type="number" onkeypress="return event.charCode >= 48" min="0" max="60" oninput="if (value < 0) value = 0;if(value>36000)value=36000">
						<span class="input-group-addon">-</span> 
						<select name="billTime" class="form-control" onchange="Report.loadData()">
							<option value="0">全部</option>
							<option value="1">有效通话</option>
							<option value="2">0~30秒</option>
							<option value="3">30秒~1分钟</option>
							<option value="4">1分钟~2分钟</option>
							<option value="5">2分钟~5分钟</option>
							<option value="6">5分钟以上</option>
						</select>
						
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">话单范围</span>
						<select name="billRange" id="billRange" class="form-control" onchange="Report.loadData()">
							<option value="">请选择</option>
							<option value="1">近期话单</option>
							<option value="2">历史话单</option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">挂机方向</span>
						<select name="agentRelease" class="form-control" onchange="Report.loadData()">
							<option value="">请选择</option>
							<option value="1">用户挂断</option>
							<option value="2">坐席挂断</option>
							<option value="3">系统挂断</option>
						</select>
					</div>
					 <div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" id="searchBut" onclick="Report.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				    </div>
				 </div>
			</div>
			<div class="ibox-content"  id="divTable" style="position: absolute;width: 98%;">
				<table  data-mars="report.myCallRecord" data-auto-fill="10" class="table table-auto table-striped table-bordered table-hover table-condensed text-c"  id="tableHead">
					<thead>
						<tr>
							<th class="text-c" width="40px;"><label class="checkbox checkbox-success"><input type="checkbox" id="ids"/><span></span></label></th>							
							<th class="text-c" data-sort="t1.DATE_ID">日期</th>
							<th class="text-c" data-sort="t1.BILL_BEGIN_TIME">开始时间</th>
							<th class="text-c" data-sort="t1.BILL_END_TIME">结束时间</th>
							<th class="text-c" data-sort="t1.BILL_TIME">通话时长</th>
							<th class="text-c" data-sort="t1.FEE_TIME_60" title="单位:分钟">计费时长</th>
							<th class="text-c" data-sort="t1.QUEUE_STAY_TIME">排队时长</th>
							<th class="text-c">坐席</th>
							<th class="text-c">话机号码</th>
							<th class="text-c">主叫</th>							
							<th class="text-c for-hiddenSys">被叫</th>
							<th class="text-c">号码归属地</th>	
							<th class="text-c">呼叫方向</th>	
							<th class="text-c">挂机方向</th>
							<th class="text-c">呼叫结果</th>
							<th class="text-c">满意度</th>
							<!-- <th class="text-c for-hiddenSys">质检</th> -->
							<th class="text-c">操作</th>
						</tr> 
					</thead>
					<tbody id="dataList"></tbody>
				</table>
				<div class="row paginate">
                   	<c:choose>
					      <c:when test="${param.allExport == 'true'}">
					         <jsp:include page="/pages/common/pagination.jsp"/>
					      </c:when>
					      <c:otherwise>
					          <jsp:include page="/pages/common/pagination_more.jsp"/>
					      </c:otherwise>
					</c:choose> 
	            </div> 
			</div>
		</div>
	</form>
	<!-- <a href="javascript:void(0)" onclick="Report.ivrListener('{{:SERIAL_ID}}','{{call:CUST_PHONE _CUST_PHONE  fn='getPhone'}}')">IVR播放</a> -->
					<script id="list-template" type="text/x-jsrender">
					{{for list}}
							{{if READ_PLAY!='Y'}}
								<tr>
							{{else}} 
								<tr style="background-color: rgba(255,255,0,0.2);">
							{{/if}}
							<td class="text-c"><label class="checkbox checkbox-success"><input type="checkbox" name="serialIdName" value="{{:SERIAL_ID}}"/><span></span></label></td>
							<td>{{:DATE_ID}}</td>
							<td>{{dateToTime:BILL_BEGIN_TIME}}</td>
							<td>{{dateToTime:BILL_END_TIME}}</td>
							<td>{{clock:BILL_TIME}} </td>
							<td>{{:FEE_TIME_60}} </td>
							<td>{{clock:QUEUE_STAY_TIME}} </td>
							<td>{{:AGENT_NAME}}{{if AGENT_PHONE}}-{{:AGENT_PHONE}}{{/if}}</td> 
							<td>{{:PHONE_NUM}}</td>
							<td>
							{{call:CALLER _CALLER fn='getPhone'}}
							<a title="拨打电话{{call:CALLER _CALLER fn='getPhone'}}" href="javascript:void(0)" onclick="Report.callPhone('{{call:CALLER _CALLER fn='getPhone'}}')"><i class="glyphicon glyphicon-earphone" style="float:right;line-height:25px;"></i></a>
							</td>				
							<td class="for-hiddenSys">
							{{call:CALLED _CALLED fn='getPhone'}}
							<a title="拨打电话{{call:CALLED _CALLED fn='getPhone'}}" href="javascript:void(0)" onclick="Report.callPhone('{{call:CALLED _CALLED fn='getPhone'}}')"><i class="glyphicon glyphicon-earphone" style="float:right;line-height:25px;"></i></a>
							</td>
							<td>{{:AREA_NAME}}</td>
							<td>{{call:CREATE_CAUSE fn='createCause'}}</td>	
							<td>{{call:AGENT_RELEASE fn='agentRelease'}}</td>	
							<td>{{getText:CLEAR_CAUSE 'clearCause'}} </td>
							<td>{{if SATISF_ID}} {{getText:SATISF_ID '#satisfId'}} {{else}}未评价{{/if}} </td>
							<!-- <td class="text-c for-hiddenSys">{{if QC_STATE == 1}}已质检{{else}}未质检{{/if}}</td> -->
							<td>
                                {{if RECORD_FILE}}<input type="hidden" name="files" value="{{:SERIAL_ID}}">
                                    <a href="javascript:void(0)" onclick="Report.recoredListener('{{:SERIAL_ID}}')">在线播放</a>
									{{call: fn='fnIvrPaly'}}
                                {{/if}}
								{{if IF_ORDER_MEND=='Y'}}
									 {{if REQ_CODE}}<a href="javascript:void(0)" onclick="Report.showOrderDetail('{{:ORDER_ID}}')">工单详情</a>
									 {{else}} <a href="javascript:void(0)" style="color:red" onclick="Report.orderMead('{{:SERIAL_ID}}','{{:CREATE_CAUSE}}','{{:CALLER}}','{{:CALLED}}')">工单补录</a>{{/if}}
                                {{/if}}
                            </td>
							</tr>
						{{/for}}					 
					 </script>
</EasyTag:override>

<EasyTag:override name="script">

<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript">
		var userAcct = localStorage.getItem("userAccount");
		var ivrPaly = getEntExtConfig('IVR_PALY');//企业拓展配置，播放ivr音乐，1表示需要
		var ifOrderMend = "";
		jQuery.namespace("Report");
		$(function(){
			$("#searchForm").render({success:function(result){
				if(result["report.myCallRecord"]){
					if(result["report.myCallRecord"].ifOrderMend=='Y'){
						$(".reqCode").show();
					}else{
						$(".reqCode").hide();
					}
				}else{
					$(".reqCode").hide();
				}
				$("#searchForm").searchData({success:function(result){
					addClass();
				}});
				$("#ids").click(function(){
					$("input[name='serialIdName']").prop("checked", $(this).is(':checked'));
					layer.msg("请注意，此全选为全选此界面通话记录！");
				});
				var firstRegionHeight =document.getElementById('divTitle').offsetHeight;
				var heightWithUnit = (firstRegionHeight) + 'px';
				$('#divTable').css('top', heightWithUnit);
			}});
		});

		
		Report.showOrderDetail = function(orderId,custObjId,tmpId) {
			popup.layerShow({type:2,title:'工单详情',offset:'rb',area:['100%','100%'],maxmin:true},"/yc-agent/pages/order2/order/my/order-info.jsp",{orderId:orderId});
		}
		Report.loadData=function(){
			/* var data = {}
			var custPhone = $("#custPhone").val();
			$("input[name='called']").val('');
			$("input[name='caller']").val('');
			if(custPhone){
				var callType = $("select[name='callType']").val();
				if(callType == 0){
					layer.msg("请先选择呼叫方式");
					return;
				}else if(callType == 1){
					$("input[name='called']").val(custPhone);
				}else{
					$("input[name='caller']").val(custPhone);
				}
			} */
			var exportMaxDay='${param.exportMaxDay}';
			var data = form.getJSONObject("#searchForm");
			var startDate = data["startTime"];
			var endDate = data["endTime"];
			var diyTime=diy_time(startDate,endDate)+1;
			if(exportMaxDay==''){
				exportMaxDay=31;
			}
			if(startDate==''||endDate==''){
				return layer.msg("开始日期或结束日期不能为空!");
			}
			if(diyTime>exportMaxDay){
				layer.alert("通话日期的开始日期和结束日期间距不能超过"+exportMaxDay+"天",{icon: 7});
				return;
			}
			$("#searchBut").prop("disabled", true); 
			$("#searchForm").searchData({success:function(result){
				addClass();
				$("#searchBut").prop("disabled", false); 
			}});
		}
		Report.orderMead = function(serialId,createCause,caller,callee) {
			var custPhone = caller;
			if(createCause == 6 || createCause == 8){
				custPhone = callee;
			}
			popup.openTab({url:"/yc-agent/pages/portal/voice_portal_v2.jsp",title:"新建工单",reload:true,data:{operType:"add",SERIAL_ID:serialId,caller:caller,callee:callee,custPhone:custPhone,serialId:serialId}});
		}
		Report.recoredListener = function(serialId) {
			addClass(serialId);
		    popup.layerShow({type:2,title:'播放录音',offset:'20px',area:['700px','380px'],shadeClose:false},"/yc-base/pages/record/record-play.jsp",{serialId:serialId});
		}
		
        Report.changeTask = function(){
			Report.loadData();
		}

		var exportIndex;
		// 点击导出按钮弹框提示 请选择导出格式，格式有excel和pdf
		Report.normalExport = function(){
			// 页面层
			exportIndex = layer.open({
				type: 1,
				area: ['300px', '180px'], // 宽高
				title: '批量导出', // 弹出层标提
				content: `
                <div style="padding: 11px;">
                    <p style="margin-bottom: 20px"><i class="layui-icon layui-icon-face-smile" style="font-size: 30px; color: #1E9FFF;"></i> 请选择导出方式：</p>
                    <div class="button-container">
                        <button class="layui-btn layui-bg-blue" style="width: 130px" onclick="Report.exportEntRepotList()">
							<i class="layui-icon layui-icon-table"></i> 导出EXCEL
						</button>
                        <button class="layui-btn layui-btn-warm" style="width: 130px" onclick="Report.exportCallByPDF()">
							<i class="layui-icon layui-icon-file"></i> 导出PDF
						</button>
                    </div>
                </div>
            ` // 弹出层的内容
			});
		}

        Report.exportEntRepotList = function(){
			layer.close(exportIndex);
			var data = form.getJSONObject("#searchForm");
			var exportMaxDay='${param.exportMaxDay}';
			var startDate = data["startTime"];
			var endDate = data["endTime"];
			
			var diyTime=diy_time(startDate,endDate)+1;
			if(exportMaxDay==''){
				exportMaxDay=1;
			}
			if(startDate==''||endDate==''){
				layer.msg("开始日期或结束日期不能为空!");
				return;
			}
			if(diyTime>exportMaxDay){
				layer.alert("通话日期的开始日期和结束日期间距不能超过"+exportMaxDay+"天",{icon: 7});
				return;
			}
			var serialIds = "";
			if(data.serialIdName){
				//var agentIds = JSON.stringify(data.agentId);
				for(var i = 0;i < data.serialIdName.length;i++){
					serialIds = serialIds+data.serialIdName[i]+",";
				}
				serialIds = serialIds.substring(0, serialIds.length-1);
				if(data.serialIdName.length>30){
					layer.alert('最多允许勾选30条',{title:"提示",btn:["确定"]});
					return;
				}
			}
			
			layer.confirm('是否导出通话记录？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportMyRecordList&"+$("#searchForm").serialize()+"&serialIds="+serialIds;
			});
		}

		Report.exportCallByPDF = function(){
			layer.close(exportIndex);
			var data = form.getJSONObject("#searchForm");
			var exportMaxDay='${param.exportMaxDay}';
			var startDate = data["startTime"];
			var endDate = data["endTime"];

			var diyTime=diy_time(startDate,endDate)+1;
			if(exportMaxDay==''){
				exportMaxDay=1;
			}
			if(startDate==''||endDate==''){
				layer.msg("开始日期或结束日期不能为空!");
				return;
			}
			if(diyTime>exportMaxDay){
				layer.alert("通话日期的开始日期和结束日期间距不能超过"+exportMaxDay+"天",{icon: 7});
				return;
			}
			var serialIds = "";
			if(data.serialIdName){
				//var agentIds = JSON.stringify(data.agentId);
				for(var i = 0;i < data.serialIdName.length;i++){
					serialIds = serialIds+data.serialIdName[i]+",";
				}
				serialIds = serialIds.substring(0, serialIds.length-1);
				if(data.serialIdName.length>30){
					layer.alert('最多允许勾选30条',{title:"提示",btn:["确定"]});
					return;
				}
			}

			layer.confirm('是否导出通话记录？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportMyRecordListByPDF&"+$("#searchForm").serialize()+"&serialIds="+serialIds;
			});
		}
        
        Report.exportRepotFiles = function(){
			var files = '';
			$("input[name='files']").each(function(){
				var val = $(this).val();
				files += ',' + val;
			});
			if(files.length > 1){
				files = files.substring(1);
			}
			layer.confirm('是否下载当前页面的通话录音？',{icon: 3, title:'下载提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportRepotFiles&files="+files;			
			});
		}
        Report.callPhone = function(num){
			if(parent.parent.CallControl.getFunc('makecall')){
				parent.parent.ccbar_plugin.callControl.makeCall(num);
			} else{
				layer.alert('当前话务状态不可用',{title:"提示",btn:["确定"]});
			}
		}
		Report.ivrListener = function(serialId,called){
			if(parent.CallControl&&parent.CallControl.getState() == 'TALK'){
				var callType = 2;
				var source = 'play';
				var userData ={
						serialId:serialId
				}
				parent.CallControl.consultIVR(called, source, userData, function(data){console.info(data)})
			}else{
				layer.alert('非【通话中】状态，不可IVR播放。');
			}
		}
        
		$.views.converters('dateToTime',function(datetime){
			if(!datetime) return "";
			return datetime.toString().substr(11)
		});
		
		$.views.converters('clock',function(time){
			
			return formatClock(time);
			function formatClock(time) {
				if(time == undefined || time == null || time =='') return "0s";
				time = parseInt(time);
				var h = Math.floor(time/3600);
				var m = Math.floor(time%3600/60);
				var s = time%60;
				m = m<10?'0'+m:m;
				s = s<10?'0'+s:s;

				return h+":"+m+":"+s;
			}
		});
		
		function diy_time(time1,time2){
		    time1 = Date.parse(new Date(time1));
		    time2 = Date.parse(new Date(time2));
		    return time3 = Math.abs(parseInt((time2 - time1)/1000/3600/24));
		}

		function createCause(createCause){
			if(createCause == 6 || createCause == 8){//呼入
				return '呼出';
			}else if(createCause == 2||createCause == 1){//呼出
				return '呼入';
			}else if(createCause == 3||createCause == 4||createCause == 5||createCause == 14||createCause == 29){//席间呼叫
				return '席间呼叫';
			}else if(createCause == 99){//呼转外线
				return '呼转外线';
			}else if(createCause == 19){//强插
				return '强插';
			}
			return '其他';
		}
		function agentRelease(agentRelease){
			if(agentRelease == 1){
				return '用户挂断';
			}else if(agentRelease == 2){
				return '坐席挂断';
			}else if(agentRelease == 3){//席间呼叫
				return '系统挂断';
			}else if(agentRelease == 4){//呼转外线
				return '转移挂机';
			}else if(agentRelease == 5){//强插
				return '强拆挂机';
			}
			return '其他挂机';
		}
		//播放ivr录音
		function fnIvrPaly(row){
			if(ivrPaly&&ivrPaly=='1'){
				return '<a class="IVR_PALY" href="javascript:void(0)" onclick="Report.ivrListener(\''+row.SERIAL_ID+'\',\''+row.CUST_PHONE+'\',\''+row._CUST_PHONE+'\')">IVR播放</a>';
			}else{
				return '';
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>