<%@ page language="java" contentType="text/html;charset=UTF-8"%>
	<div class="list-order" id="contactHistoryDiv"></div>
		
     <script id="recordHis-template" type="text/x-jsrender">
						{{for data}}
							<div class="list-order-item">
					             <div class="list-order-item-header">
									{{:#index+1}} .
						            {{call:BILL_BEGIN_TIME fn='diaplayTime'}}
									{{if CALL_TYPE == 1}}
										<span style="color:red">呼出 </span>
									{{else}}
										<span style="color:blue">呼入 </span>
                   					{{/if}}
									<span class="time">{{:AGENT_NAME}}{{if AGENT_PHONE}}-{{:AGENT_PHONE}}{{/if}}</span>
					             </div>
					             <div class="list-order-item-content">
					             	<p class="text">主叫：{{call:CALLER _CALLER fn='getPhone'}} <span class="pull-right">被叫：{{call:CALLED _CALLED fn='getPhone'}}</span></p>
					             	<p class="text">开始时间：{{:BILL_BEGIN_TIME}}</p>
					             	<p class="text">结束时间：{{:BILL_END_TIME}}</p>
					             	<p class="text">通话时长：{{clock:BILL_TIME}}</p>
									{{if RECORD_FILE}}
					             	<p class="text" id="{{:SERIAL_ID}}">
										<button class="btn btn-sm btn-primary"  type="button" onclick="audition('{{:SERIAL_ID}}')"><span class="glyphicon glyphicon-play-circle"></span> 试听</button>
									</p>
									{{/if}}
					             </div>
					         </div>
						{{/for}}
						{{if data.length==0}}
							<p class="text-c mt-20">
								<img src="/easitline-static/images/nodata.png"/>
							</p>
						{{/if}}
		</script>
