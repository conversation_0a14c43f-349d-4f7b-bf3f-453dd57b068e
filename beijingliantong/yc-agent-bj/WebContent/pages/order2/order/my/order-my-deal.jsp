<%@ include file="/pages/common/global.jsp"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的待办工单</title>
	
	<style>
		.layui-layer-loading.layer-skin .layui-layer-content{overflow: unset !important;}

		/*折叠样式*/
		.collapse-panel{position: relative;}
		.collapse-panel .layui-tab-title{padding-left: 40px;}
		.collapse-btn{
			position: absolute;
			left: 5px;
			top: 10px;
			z-index: 5;
			text-decoration: none;
			cursor: pointer;
			padding: 5px 10px;
		}
		.cred{color: red;}
		.verifyTruthRow{display: none;}
		.collapse-btn:hover,.collapse-btn:focus{text-decoration: none;}
		.lastFollowDesc{position: absolute;margin-left: 150px;top: 13px;font-size: 12px;color: #999;}
		#advancedQueryCondition{
			display:block;
			word-break:keep-all; 
			white-space:nowrap; 
			overflow:hidden; 
			text-overflow:ellipsis; 
		}
		
		.layui-form-label{
		 	width: 90px !important;
		 	font-size: 12px !important;
		}
		.layui-input-block{
			margin-left: 95px !important;
		}
		.checkbox-radio-group{
			padding-top: 10px !important;
		}
		
		/* 工单紧急程度样式 */
		.active-urgent {
			border-left: 5px solid red !important;
		}
		.active-important {
			border-left: 5px solid orange !important;
		}
		.active-urgent.active {
			background-color: #ffebeb !important;
		}
		.active-important.active {
			background-color: #fff5eb !important;
		}
	</style>
	
</EasyTag:override>
<EasyTag:override name="content">
<link href="${ctxPath}/static/css/order.css" rel="stylesheet">
		<div class="ibox">
			<div class="ibox-content  pd-0">
				<div class="ibox-flex">
					<div class="left-side">
						<div class="ibox-flex-vertical">
							<div class="left-side-header" style="padding-left:10px;">
								 <div class="input-group input-group-sm pb-10">
								    <span class="input-group-addon">受理时间</span>	
									<input data-mars="common.limitHMonthTime" id="limitDate" type="text" name="limitDate" class="form-control input-sm" style="width: 215px;">	
							     </div>
								 <div class="input-group input-group-sm">
								    <span class="input-group-addon">客户名称</span>	
									<input type="text" name="custName" class="form-control input-sm" style="width: 122px;">	
									 <span class="input-group-btn">
						                 <button type="button" class="btn btn-sm btn-default" onclick="OrderMyDeal.simpleQuery()">
						                	 <span class="glyphicon glyphicon-search"></span> 搜索
						                 </button>
						                 <button type="button" class="btn btn-sm btn-default" onclick="OrderMyDeal.showAdvancedQuery()" title="高级查询">
						                	 <span class="glyphicon glyphicon-filter"></span>
						                 </button>
						             </span>	
						              			  
							     </div>
							</div>
							
							<div class="left-side-header" style="padding-left:10px;">
								<span class="ml-20">第&nbsp;<span class="pageNumber" style="color:green;color: #f0ad4e;font-weight: 600;">1</span>&nbsp;页</span>
								<span class="ml-20">共&nbsp;<span class="totalPage" style="color:green;color: #f0ad4e;font-weight: 600;">0</span>&nbsp;页</span>
								<span class="ml-20">当前页&nbsp;<span class="totalRow" style="color:green;color: #f0ad4e;font-weight: 600;">0</span>&nbsp;条</span>
							</div>
							<div class="left-side-header" style="padding-left:10px; display: none;" id="advancedQueryCondition">
							</div>
							<div class="left-side-content ibox-flex-item" id="order-title">
								<div style="overflow:auto" id="todo-content">
									<ul class="todoList">

									</ul>
								</div>
							</div>
							<div class="left-side-footer text-center">
								  <ul class="pager" style="margin: 0 0 7px 0;">
								    <li class="prev" style="float:left;"><a href="javascript:void(0)" id="prePage">上一页</a></li>
								    <li class="next" style="float:right;"><a href="javascript:void(0)" id="nextPage">下一页</a></li>
								  </ul>
							</div>
						</div>
					</div>
					
				<form autocomplete="off" onsubmit="return false;" class="form-inline" id="orderForm" style="width: 100%;">
					<input name="orderId" id="orderId" type="hidden"/>
					<input name="order.TMP_ID" id="tmpId" type="hidden"/>
					<input name="order.ORDER_STEP_ID" id="stepId" type="hidden"/>
					<input id="dealFlag" value="deal" type="hidden">
					<div class="ibox-flex-item" id="order-content">
						<div style="padding: 0px 30px;">
							<div id="row1" class="row">
								<div class="layui-tab layui-tab-brief collapse-panel">
									<button id="custInfoSaveBtn" type="button" style="z-index: 9;position: relative;" onclick="OrderCore.saveOrderInfo('custInfo-content');" class="btn btn-sm btn-default btn-outline pull-right mt-5">保存</button>
									<a href="javascript:;" title="折叠面板" class="collapse-btn" data-toggle="collapse" data-target="#custInfo-content"><i class="layui-icon layui-icon-down"></i></a>
									<ul class="layui-tab-title">
										<li id="custInfoLi" onclick="OrderCore.switchBtnClick('custInfo')" class="layui-this">客户信息</li>
										<li onclick="OrderCore.switchBtnClick('custDetailInfo')">客户详情</li>
										<li id="orderLogInfoLi" onclick="OrderCore.switchBtnClick('')">流转历史</li>
										<li onclick="OrderCore.switchBtnClick('')">联系历史</li>
										<li onclick="OrderCore.switchBtnClick('')">历史工单</li>
									</ul>
									<div id="custInfo-content" class="layui-tab-content mt-15 in" style="min-height: 60px;">
										<div class="layui-tab-item layui-show ">
											<jsp:include page="module/custInfo.jsp"/> 
										</div>
										<div class="layui-tab-item">
											<jsp:include page="module/custDetailInfo.jsp"/> 
										</div>
										<div  class="layui-tab-item">
											<jsp:include page="module/orderLogInfo.jsp"/> 
										</div>
										<div class="layui-tab-item">
											<jsp:include page="module/contactHistory.jsp"/>
										</div>
										<div class="layui-tab-item">
											<jsp:include page="module/orderHistory.jsp"/>
										</div>
									</div>
								</div>
							</div>
							 <div id="row2" class="row" >
								<div class="layui-tab layui-tab-brief collapse-panel">
									<!-- <button type="button" style="z-index: 9;position: relative;" onclick="saveOrderInfo('orderInfo-content');" class="btn btn-sm btn-default btn-outline pull-right mt-5">保存</button> -->
									<a href="javascript:;" title="折叠面板" class="collapse-btn" data-toggle="collapse" data-target="#orderInfo-content"><i class="layui-icon layui-icon-down"></i></a>
									<ul class="layui-tab-title">
										<li class="layui-this">工单基本信息</li>
									</ul>
									<div id="orderInfo-content" class="layui-tab-content mt-15 in" style="min-height: 60px;">
										<div class="layui-tab-item layui-show">
											<jsp:include page="module/orderInfo.jsp"/> 
										</div>
									</div>
								</div>
							</div> 
							<div id="row3" class="row" style="margin-top:10px">
								<div class="layui-tab layui-tab-brief detailCustInfo collapse-panel">
									<button type="button" style="z-index: 9;position: relative;" onclick="OrderCore.saveOrderInfo('orderBusiInfo-content');" class="btn btn-sm btn-default btn-outline pull-right mt-5">保存</button>
									<a href="javascript:;" title="折叠面板" class="collapse-btn" data-toggle="collapse" data-target="#orderBusiInfo-content"><i class="layui-icon layui-icon-down"></i></a>

									<ul class="layui-tab-title">
										<li class="layui-this">工单业务信息</li>
									</ul>
									<div id="orderBusiInfo-content" class="layui-tab-content mt-15 in" style="min-height: 60px;">
										<div class="layui-tab-item layui-show" id="flowFormCreate">
											<template v-for="colItem,colIndex in formData.colsData">
								                 <div class="layui-row">
								                     <div v-if="!isEdit" v-for="refItem,refIndex in colItem.cols" :class="getRowClass(colItem.colNum)">
								                         <component :is="getTemplateName(refItem.refData)" :name="refItem.refData" :data="getTemplateData(refItem.refData)"></component>
								                     </div>
								                 </div>
								             </template>
										</div>
									</div>
										
								</div>
							</div>
							
							<div id="row5" class="row" style="margin-top:10px">
								<div class="layui-tab layui-tab-brief collapse-panel">
									<a href="javascript:;" title="折叠面板" class="collapse-btn" data-toggle="collapse" data-target="#orderDeal-content"><i class="layui-icon layui-icon-down"></i></a>
									<ul class="layui-tab-title">
										<li class="layui-this">处理工单[<span id="orderStepName"></span>]</li>
									</ul>
									<div id="orderDeal-content" class="layui-tab-content mt-15 in" style="min-height: 60px;">
										<div class="layui-tab-item layui-show" id="flowFormCreateDeal">
											<template v-for="colItem,colIndex in formData.colsDealData">
								                 <div class="layui-row">
								                     <div v-if="!isEdit" v-for="refItem,refIndex in colItem.cols" :class="getRowClass(colItem.colNum)">
								                         <component :is="getTemplateName(refItem.refData)" :name="refItem.refData" :data="getTemplateData(refItem.refData)"></component>
								                     </div>
								                 </div>
								             </template>
										</div>
										<div class="layui-row" >
										<div class="layui-col-md6" id="assignPersonDiv" style="display: none;"> 
				  							<div class="layui-form-item">
					  							<label class="layui-form-label">指派人</label> 
												<div class="layui-input-block"> 
					  								<select class="form-control" id="assignPerson" name="order.HANDLER_ID" data-mars="orderTemp.agentOrderSssignPerson" data-mars-top="true" style="width:100%;">
					  								<option value="">请选择</option>
					  								</select>
					  							</div>
				  							</div>
				  						</div>
									</div>	
									</div>
									<div style="margin-left: 35px;" id="actionListDiv" data-mars="agentOrder.getStepActionList" data-template="actionList-template" data-container="#actionListDiv">
										
									</div>
								</div>
							</div>
							</div>
						</div>
						<div id="nodataImageDiv" style="text-align:center;display: none;"><img style="margin-top: 200px;" src="/easitline-static/images/nodata.png"/></div>
						</form>
					</div>
				</div>
			</div>
 <jsp:include page="/pages/order2/template/temp.jsp"></jsp:include>
 
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/order-core.js"></script>
	<script>
		jQuery.namespace("OrderMyDeal");
	
		$(function(){
			OrderCore.setTag("DEAL");
			$('body').on('click','.collapse-btn',function(){
				$(this).find('i').toggleClass('layui-icon-down layui-icon-right')
			})
			
			requreLib.setplugs("layui",function(){
	        	$("#limitDate").render({success:function(result){
					layui.use(['element','laydate'], function(){
		    			var laydate = layui.laydate;
		    			laydate.render({ elem: '#limitDate',range: '~' ,type : 'datetime',btns: ['confirm'] });
						OrderMyDeal.initEvent();
						OrderMyDeal.simpleQuery();
				   });
	        	}});
	    	});
		});
		
		//初始化指派人
		OrderMyDeal.initAssignPerson = function(stepId){
			ajax.daoCall({controls:['orderTemp.getTempAction'],params:{stepId:stepId}},function(result){
				if(result["orderTemp.getTempAction"]!=null && result["orderTemp.getTempAction"]!=undefined 
						&& result["orderTemp.getTempAction"].data!=null && result["orderTemp.getTempAction"].data!=undefined
						&& result["orderTemp.getTempAction"].data.length>0){
					
						var tempId = "";
						var actionId = "";
						for(var i = 0;i < result["orderTemp.getTempAction"].data.length;i++){
							tempId = tempId + result["orderTemp.getTempAction"].data[i].TMP_ID+",";
							actionId = actionId + result["orderTemp.getTempAction"].data[i].ORDER_ACTION_ID+",";
						}
						tempId= tempId.substr(0,tempId.length-1);
						actionId = actionId.substr(0,actionId.length-1);
						ajax.daoCall({controls:['orderTemp.groupAssingPerson'],params:{tmpId:tempId,actionId:actionId}},function(result){
							if(result["orderTemp.groupAssingPerson"]!=null && result["orderTemp.groupAssingPerson"]!=undefined 
									&& result["orderTemp.groupAssingPerson"].data!=null && result["orderTemp.groupAssingPerson"].data!=undefined
									&& result["orderTemp.groupAssingPerson"].data.length>0 && result["orderTemp.groupAssingPerson"].data[0].ASSIGN_PERSON!=undefined
									&& result["orderTemp.groupAssingPerson"].data[0].ASSIGN_PERSON =="Y"){
								$("#assignPersonDiv").show();
							}else{
								$("#assignPersonDiv").hide();
							}
						})
						requreLib.setplugs("select2", function () {
					        $("#assignPerson").select2({theme: "bootstrap",placeholder:'请选择',width:'100%'});
					        $("#assignPersonDiv").render({data:{tmpId:tempId,actionId:actionId},success:function(result){
					        	if($(".select2-container--bootstrap")){
					        		if($(".select2-container--bootstrap").length>1){
					        			$(".select2-container--bootstrap").eq(1).hide();
					        		}
					        	}
					        }})
						});
				}
			})
			
		}
		
		// 查询参数
		OrderMyDeal.queryParams = {};
		// 高级查询的条件文本
		OrderMyDeal.conditonContent = "";
		OrderMyDeal.pageIndex = -1,
		
		//加载列表  param：代表搜索条件    isStarted：代表是否从头开始，因为一点击搜索就是要从第一页开始 content:高级查询的条件内容
		OrderMyDeal.loadTodolist = function(param,isStarted,content){

			var formParam = {};
			if(param){
				formParam = param;
				// 记录查询参数，以便上下翻页的时候使用
				OrderMyDeal.queryParams = param;
				if(isStarted){
					OrderMyDeal.pageIndex = -1;
				}
			}
			formParam.pageIndex = OrderMyDeal.pageIndex;
			formParam.pageSize = 15;
			ajax.daoCall({controls:['agentOrder.onDealList'],params:formParam},function(templdateResult){
				var data = templdateResult['agentOrder.onDealList']?templdateResult['agentOrder.onDealList']:{};
				var totalRow=data.data.length;
				var pageNumber= data.pageNumber;
				var totalPage = data.totalPage;
				var pageSize=data.pageSize;
				if(pageNumber < 0){
					pageNumber = 1;
					OrderMyDeal.pageIndex = pageNumber;
				}
				$('.pageNumber').text(pageNumber);
				if(totalRow != pageSize){	//最后一页
					$('#nextPage').css('pointer-events','none');
					$('#nextPage').css('opacity','.65');
				}else{
					$('#nextPage').css('pointer-events','');
					$('#nextPage').css('opacity','');
				}
				if(pageNumber == 1){	//第一页
					$('#prePage').css('pointer-events','none');
					$('#prePage').css('opacity','.65');
				}else{
					$('#prePage').css('pointer-events','');
					$('#prePage').css('opacity','');
				}
				$(".totalRow").text(totalRow);
				// 仅第一次搜索时会返回正确的页面数量
				if(totalPage != 0){
					$(".totalPage").text(totalPage);
				}
				// 显示高级查询的条件内容
				if(content && content != ''){
					OrderMyDeal.conditonContent = content;
					$("#advancedQueryCondition").html(content);
					$("#advancedQueryCondition").attr("title",content);
					$("#advancedQueryCondition").show();
				}else{
					$("#advancedQueryCondition").empty();
					OrderMyDeal.conditonContent = "";
					$("#advancedQueryCondition").hide();
					$("#advancedQueryCondition").removeAttr("title");
				}
				// 当有内容时显示内容，没内容时显示无数据的图片
				if(data.data.length==0){
					$("#nodataImageDiv").show();
					$("#order-content").hide();
					$("#order-btn-list").hide();
					$(".todoList").html('<div style="text-align:center;width:100%;"><img src="/easitline-static/images/nodata.png"/></div>');
					$(".totalPage").text(0);
					return;
				}else{
					$("#order-content").show();
					$("#order-btn-list").show();
					$("#nodataImageDiv").hide();
				}
				var tmp = $.templates("#order-list-template");
				var html=tmp.render({data:data.data});
				$(".todoList").html(html);
				$(".todoList li").first().click();
				
			
			});
		}
		
		OrderMyDeal.initEvent = function(){
			//左侧高度随window高度变化而变化
			$('#order-title').height($(window).height()-117);
			$('#order-content').height($(window).height()-70);
			$('.left-side').height($(window).height()-22);
			$(window).resize(function(){
				$('#order-title').height($(window).height()-117);
				$('#order-content').height($(window).height()-70);
				$('.left-side').height($(window).height()-22);
			});
			$('#order-content').scroll(function(){
				var left = $('#order-content').offset().left+26+'px';
				
				var ll=$('#row1').outerHeight()+$('#row2').outerHeight()+$('#row3').outerHeight()+$('#row4').outerHeight()+$('#row5').outerHeight();
				if($('#order-content').scrollTop()>ll){
					if($('#fixed-temp').length == 0 ){
						$('#fixed-content').after($('<div id="fixed-temp" style="height:40px;"></div>'));
					}
					$('#fixed-content').css({position: 'fixed',top: '10px',left: left,background: '#fff',right: '48px','z-index': '100'});
				}else{
					$('#fixed-temp').remove();
					$('#fixed-content').css({position: 'relative',top: '',left: '',background: '',right: '','z-index': ''});
				}
			});
			
			OrderMyDeal.bindBtn();//绑定上下页事件
		}
		
		/**
		 * 绑定上下一页的事件
		 */
		 OrderMyDeal.bindBtn = function(){
			$('#nextPage').click(function(){
				OrderMyDeal.pageIndex = OrderMyDeal.pageIndex+1;
				OrderMyDeal.loadTodolist(OrderMyDeal.queryParams,false,OrderMyDeal.conditonContent);
			});
			
			$('#prePage').click(function(){
				OrderMyDeal.pageIndex = OrderMyDeal.pageIndex-1;
				OrderMyDeal.loadTodolist(OrderMyDeal.queryParams,false,OrderMyDeal.conditonContent);
			});
		}

		//显示高级查询表单
		OrderMyDeal.showAdvancedQuery = function(){
			popup.layerShow({type:1,title:'高级查询',offset:'lt',area:['40%','300px']},"${ctxPath}/pages/order2/order/my/order-advanced-query.jsp",{nameSpace:'OrderMyDeal',queryParams:JSON.stringify(OrderMyDeal.queryParams)});
		}
		
		// 简单查询，只根据客户名称查询
		OrderMyDeal.simpleQuery = function(){
			var custName = $("input[name='custName']").val();
			var limitDate = $("#limitDate").val();
			OrderMyDeal.loadTodolist({custName:custName,limitDate:limitDate},true);
		}
		// 处理工单
		OrderMyDeal.handleOrder = function(){
			var orderId = $("#orderId").val();
			var tmpId = $("#tmpId").val();
			var stepId = $("#stepId").val();
			popup.layerShow({type:1,title:'处理工单',offset:'20px',area:['80%','90%'],maxmin:true,shadeClose:false},"${ctxPath}/pages/order2/order/my/order-deal.jsp",{orderId:orderId,tmpId:tmpId,stepId:stepId});
		}
		
		function getActionBtn(stepType){
			// 工单完成
			if(stepType == '20'){
				return "btn-success";
			}
			// 工单取消
			if(stepType == '30'){
				return "btn-danger";
			}
			return "btn-primary";
		}

		jQuery.namespace("OrderEdit");
		
		
		OrderEdit.ajaxSubmitForm = function(actionId){
			console.info(OrderCore.dealData);
			/* if(form.validate("#easyform")){
				OrderEdit.handleOrder(actionId);
			} */
			
			var param = {};
			var formData = form.getJSONObject("#orderForm");
			for (var i in formData) {
				$('#flowFormCreateDeal').find('[name]').each(function(){
					var name = $(this).attr('name');
					if(i == name){
						param[i] = formData[i];
					}
				});
			}
			param['orderId'] = $("#orderId").val();
			param['stepId'] = $("#stepId").val();
			param['actionId'] = actionId;
			//将指派人id传入后台
			param['HANDLER_ID'] = $("#assignPerson").val();
			OrderEdit.param = param;
			if(form.validate('#flowFormCreateDeal')){
				OrderEdit.handleOrder(actionId);
			}
		}
		
		// 处理工单
		OrderEdit.handleOrder = function(actionId,endStepId){
			
			var data = OrderEdit.param;
			data['actionId'] = actionId;
			// 如果是一键办结或者一键取消，则需要查询一下当前模板是否存在多个工单完成类型或者工单取消类型的步骤，让客户选择一个进行一键办结或者一键取消
			if((actionId == 'Finish' || actionId == 'Cancel') && endStepId == undefined){
				var type;
				if(actionId == 'Finish'){
					type = 20
				}
				if(actionId == 'Cancel'){
					type = 30
				}
				ajax.daoCall({controls:['orderTemp.getStepListByType'],params:{tmpId:OrderEdit.tmpId,type:type}},function(result){
					var res=result['orderTemp.getStepListByType'];
					var stepList = res['data'];
					if(stepList){
						// 如果返回的步骤列表不止一个，则让客户选择一个
						if(stepList.length > 1){
							var html = '<div style="text-align:center;">';
							for(var i=0;i<stepList.length;i++){
								var step = stepList[i];
								if(i == 0){
									html += '<label class="radio radio-inline radio-success" style="margin-right:10px;"><input type="radio" name="choosedStep" value="'+step.ORDER_STEP_ID+'" checked="checked"> <span>'+step.ORDER_STEP_NAME+'</span></label>';
								}else{
									html += '<label class="radio radio-inline radio-success" style="margin-right:10px;"><input type="radio" name="choosedStep" value="'+step.ORDER_STEP_ID+'"> <span>'+step.ORDER_STEP_NAME+'</span></label>';
								}
							}
							html += '</div>'; 
							layer.open({
								 title: '选择最终步骤',
								 content: html,
								 yes: function(index){
									 // 选中后的最终步骤
									 var stepId = $("input[name='choosedStep']:checked").val();
									 OrderEdit.handleOrder(actionId,stepId);
								 }
								 
							}); 
						}else if(stepList.length == 1){
							 OrderEdit.handleOrder(actionId, stepList[0].ORDER_STEP_ID);
						}else{
							layer.alert("当前工单模板配置出现问题，请联系管理员",{icon: 5});
						}
					}else{
						layer.alert("当前工单模板配置出现问题，请联系管理员",{icon: 5});
					}
				});
				
			}else{
				data['endStepId'] = endStepId;
				ajax.remoteCall("${ctxPath}/servlet/order?action=handle",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1},function(){
							layer.closeAll();
							OrderMyDeal.loadTodolist(OrderMyDeal.queryParams,false,OrderMyDeal.conditonContent);
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp"%>