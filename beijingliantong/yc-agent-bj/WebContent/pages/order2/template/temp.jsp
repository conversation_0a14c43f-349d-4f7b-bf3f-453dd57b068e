<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	
	<script id="orderCust-template" type="text/x-jsrender">
			{{for data}}
				{{if #index % 2 == 0}} 
				<div class="layui-form-item" >
					<div class="layui-row" >{{/if}}
		  				<div class="layui-col-md6"> 
							 <label class="layui-form-label">{{:name}}</label> 
							 <div class="layui-input-block"> 
		                         <input {{if field == 'CUST_PHONE' || field == 'CUST_NAME'}}name="order.{{:field}}"{{/if}} type="text" value="{{:val}}" {{if field!='CUST_NAME'&&field!='CUST_PHONE'}}readonly="readonly"{{/if}} class="form-control" style="width:100%;" autocomplete="off"/>
							 </div> 
		  				</div>		
				{{if #index % 2 == 1}}</div></div>{{/if}}
			{{/for}}
	</script>
	
	<script id="actionList-template" type="text/x-jsrender">
			{{for data}}
				<button type="button" class="btn btn-sm {{call:NEXT_STEP_TYPE fn='getActionBtn'}} {{if #getIndex()!=0}} ml-20{{/if}}" onclick="OrderEdit.ajaxSubmitForm('{{:ORDER_ACTION_ID}}');this.disabled='disabled'">{{:ORDER_ACTION_NAME}}</button>
			{{/for}}
			<button type="button" class="btn btn-sm btn-success hide {{if data && data.length}} ml-20{{/if}}" onclick="OrderEdit.ajaxSubmitForm('Finish');this.disabled='disabled'">一键办结</button>
			<button type="button" class="btn btn-sm btn-danger hide ml-20" onclick="OrderEdit.ajaxSubmitForm('Cancel');this.disabled='disabled'">一键取消</button>
	</script>
	
	<script id="order-list-template" type="text/x-jsrender">
			{{for data}}
			  <li style="cursor:pointer" onclick="OrderCore.showOrderObj('{{:ORDER_ID}}')" data-tel="{{:CUST_PHONE}}" class="todo todo_{{:ORDER_ID}}  {{if #index ==0}}active{{/if}} {{if URGENCY_DEGREE=='2'}}active-urgent{{else URGENCY_DEGREE=='1'}}active-important{{/if}}">
					<p>{{:#index+1}}.<b>{{:CUST_NAME}}</b>
					{{if URGENCY_DEGREE=='2'}}<span style="color:red;font-weight:bold;">【加急】</span>{{else URGENCY_DEGREE=='1'}}<span style="color:orange;font-weight:bold;">【紧急】</span>{{else}}<span style="color:black;">【一般】</span>{{/if}}
					<span class="status">
						<span class="pull-right layui-badge layui-bg-blue" style="margin-top:2px">{{:TMP_NAME}}</span> 
					</span>
					</p>
					<p>
						<div>
							<span>当前步骤</span>
							<span style="color:green">【{{if ORDER_STEP_NAME}}{{:ORDER_STEP_NAME}}{{else}}暂存{{/if}}】</span>
						</div>  
					</p>
					<p>
						<div>
							<span>{{:AGENT_NAME}} 创建于 {{:ORDER_TIME}}</span>
						</div>  
					</p>
				</li>
			{{/for}}
	</script>
	
	<script id="stepAction-template" type="text/x-jsrender">
		{{if admin }}
			<button type="button" class="btn btn-primary" onclick="OrderNew.ajaxSubmitForm()" id="saveBtn">提交工单</button>
		{{else}}
			{{for list}}
				<button type="button" class="layui-btn {{call:NEXT_STEP_TYPE fn='getActionBtn'}} {{if #getIndex()!=0}} ml-20{{/if}}" onclick="OrderNew.addData('{{:ORDER_ACTION_ID}}')">{{:ORDER_ACTION_NAME}}</button>
			{{/for}}
				<button type="button" class="layui-btn layui-btn-primary {{if list && list.length}} ml-20{{/if}}" onclick="OrderNew.temporarySave()">暂存</button>
		{{/if}}
			
	</script>
	
	<script id="orderInit-template" type="text/x-jsrender">
		<template v-for="colItem,colIndex in formData.colsData">
    		<div class="layui-row">
        	<div v-if="!isEdit" v-for="refItem,refIndex in colItem.cols" :class="getRowClass(colItem.colNum)">
            	<component :is="getTemplateName(refItem.refData)" :name="refItem.refData" :data="getTemplateData(refItem.refData)"></component>
        	</div>
    		</div>
		</template>
	</script>
	
	<script id="orderDealInit-template" type="text/x-jsrender">
		<template v-for="colItem,colIndex in formData.colsDealData">
			 <div class="layui-row">
				 <div v-if="!isEdit" v-for="refItem,refIndex in colItem.cols" :class="getRowClass(colItem.colNum)">
					 <component :is="getTemplateName(refItem.refData)" :name="refItem.refData" :data="getTemplateData(refItem.refData)"></component>
				 </div>
			 </div>
		 </template>
	</script>
	
	<script id="template-drag" type="x-template">
        <div class="tmpl-drag">
            <div class="tmpl-btns">
                <div class="layui-btn-group">
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe654;</i>
                  </button>
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe642;</i>
                  </button>
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe640;</i>
                  </button>
                </div>
            </div>
            <div class="tmpl-detail"></div>
        </div>
    </script>
    <script id="template-text" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <input type="text" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请输入文本" autocomplete="off" class="form-control" style="width:100%;"/>
            </div>
      </div>    
    </script>
    <script id="template-date" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <input readonly type="text" onclick="WdatePicker({dateFmt: 'yyyy-MM-dd'})" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请选择日期" autocomplete="off" class="form-control wDate" style="width:100%;background:#fff url(/easitline-static/lib/My97DatePicker/skin/date.png) no-repeat right;" />
            </div>
      </div>    
    </script>
    <script id="template-datetime" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <input readonly type="text" onclick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请选择时间" autocomplete="off" class="form-control wDate" style="width:100%;background:#fff url(/easitline-static/lib/My97DatePicker/skin/date.png) no-repeat right;" />
            </div>
      </div>    
    </script>
    <script id="template-number" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <input type="number" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请输入数字" autocomplete="off" class="form-control" style="width:100%;" />
            </div>
      </div>    
    </script>
    <script id="template-select" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <select :name="'order.'+name" id="" lay-ignore class="form-control" :data-rules="data.required == 1?'required':''" style="width:100%;">
                    <option value="">请选择{{data.name}}</option>
					<template v-for="optionItem,optionIndex in data.value">
						<option :value="optionItem" selected="selected" v-if="optionItem === data.selected">{{optionItem}}</option>
						<option :value="optionItem" v-else>{{optionItem}}</option>
					</template>
                </select>
            </div>
      </div>    
    </script>
    <script id="template-radio" type="x-template">checked="checked"
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <div class="checkbox-radio-group" style="padding-top: 6px;">
                    <label v-for="optionItem,optionIndex in data.value" class="radio radio-info radio-inline">
                        <input type="radio" :name="'order.'+name" :value="optionItem" v-if="optionItem === data.checked" checked="checked">
                        <input type="radio" :name="'order.'+name" :value="optionItem" v-else>
                        <span>{{optionItem}}</span>
                    </label>
                </div>
            </div>
      </div>    
    </script>
    <script id="template-checkbox" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
                <div class="checkbox-radio-group" style="padding-top: 8px;">
					<input type='hidden' name='checkbox' :value="name" />
                    <label v-for="optionItem,optionIndex in data.value" class="checkbox checkbox-info checkbox-inline">
                        <input type="checkbox" :name="'order.'+name" :value="optionItem" :checked="data.checked && data.checked.indexOf(optionItem) != -1">
                        <span>{{optionItem}}</span>
                    </label>
                </div>
            </div>
      </div>    
    </script>
    
    <script id="template-textArea" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
				<textarea class="form-control" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请输入文本" style="width:100%;" rows="3"></textarea>
            </div>
      </div>    
    </script>
    
     <script id="template-cascader" type="x-template">
		<div  class="layui-form-item" id="cascaderDiv">
    		<label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
   		 	<div class="layui-input-block">
				   <input type="text" :id="data.id+data.name"  :name="'order.'+name" :value="data.value" class="layui-input" readonly="readonly" placeholder="请选择">
			</div>
 		 </div>
    </script>
    
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script src="/yc-agent/static/js/charts/vue.js" charset="utf-8"></script>
    <script src="/yc-agent/static/js/form-creater.js" charset="utf-8"></script>
    
    <script type="text/javascript">


    // 过滤字段空数据
    function filterData(dataJson,orderInfo){//selectId
    	var tempDataJson = {};
    	for(key in dataJson){
    		var field = dataJson[key];
    		if(field && field!=''){
    			var row = JSON.parse(field);
    			if(orderInfo&&orderInfo!=undefined){
        			var val = orderInfo[key];
        			if(val&&val!=undefined){
        				if(row.dataType == 'select'){
        					row['selected'] = val;
            			}else if(row.dataType == 'radio'){
            				row['checked'] = val;
            			}else if(row.dataType == 'checkbox'){
            				row['checked'] = val;
            			}else if(row.dataType == 'cascader'){
            				row['value'] = val;
            			}else{
                			row['value'] = val;
            			}
        			}/* else{
        				if(row.dataType == 'radio'){
        					if(row.value){
                				row['checked'] = row.value[0];
        					}
            			}
        			}	 */
    			}
    			/* if("cascader" == row.dataType){
    				row.selectId = selectId;
    			}
 */
    			tempDataJson[key] = row;
    			
    		}
    	}
    	return tempDataJson;
    }
    
    // 过滤空数据
    function filterColData(refDataFields){
    	if(refDataFields && refDataFields!=''){
    		return JSON.parse(refDataFields);
    	}else{
    		return [];
    	}
    }

 	// 过滤字段空数据
    function filterUnUsedData(unUsedFields){
    	var result = {};
    	for(key in unUsedFields){
    		var field = unUsedFields[key];
    		if(field && field!=''){
    			result[key] = JSON.parse(field);
    		}
    	}
    	return result;
    }
 	
    function filterStepData(list){
    	if(list){
    		var listData = [];
    		for(var i = 0; i < list.length; i++){
    			var data = {};
    			data.id = list[i]['ORDER_STEP_ID'];
    			data.name = list[i]['ORDER_STEP_NAME'];
    			data.colsData = filterColData(list[i]['REF_DATA_FIELDS']);
    			listData.push(data);
    		}
    		return listData;
    	}
    	return [];
    }
	</script>
</html>