<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>坐席话务分析报表</title>
    <style>
    	.ztreeDiv {
			display:none;position: absolute;border:1px solid rgb(170,170,170);min-width: 150px;max-width: 300px; 
			max-height: 200px;z-index:10;overflow: auto;background-color: #F0F0F0
			}
		.pagination pagination-sm pageNumV{float:right}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form class="form-inline" id="searchForm" data-page-hide='true'>
        <div class="ibox">
            <div class="ibox-title clearfix" id="divId">
                <div class="form-group">
                    <h5> 坐席话务分析报表 <span id="titleAndTime"> </span></h5>
                    <div class="input-group input-group-sm pull-right btn-group">
                        <button type="button" class="btn btn-sm btn-info btn-outline"  onclick="agentCallStat.exportAgentCallStat()">
                          		导 出
                        </button>
                    </div>
                </div>
                <hr style="margin: 3px -15px">
                <div class="form-group">
					<div class="input-group input-group-sm">
						<span class="input-group-addon">统计类型</span> <select class="form-control" id="stType" name="stType">
							<option value="skillStat" selected="selected">按技能组统计</option>
							<option value="dayStat">按天统计</option>
						</select>
					</div>
                    <div class="input-group input-group-sm ml-20">
						<div class="input-group input-group-sm">
							<span class="input-group-addon">统计日期</span>
		     				 <div class="layui-input-inline">
		    			   		<input data-mars="common.limitToday" type="text" class="layui-input" id="limitDate" autocomplete="off" style="height:30px;width:190px" name="limitDate"> 
		   			  		 </div>
		   				</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">-</span> 
							<select class="form-control input-sm" name="dateRange" onchange="agentCallStat.onCasecade($(this))">
								<option value="">请选择</option>
								<option value="today">今天</option>
								<option value="yesterday">昨天</option>
								<option value="thisWeek">本周</option>
								<option value="RecentlyOneMonth">近一个月</option>
								<option value="RecentlyThreeMonth">近三个月</option>
							</select>
						</div>
					</div>
					
				  	<div class="input-group input-group-sm" id="skill_select" style="position: inherit">
          		         <span class="input-group-addon">技能组</span>	
          		         <input name="skillName"  id="skillName" readonly="readonly"  onclick="showMenu(this,'consultTypeZtree')" class="form-control input-sm">
              			 <input name="skillId" id="skillId" readonly="readonly" class="hidden" >
              			 <div class="ztreeDiv" id="consultTypeZtree" >
            				<div class="ztree"   data-mars="groupTree.groupTreeVoice" 
								id="ztree" data-setting="{callback: {onClick: zTreeOnClick,onCheck: zTreeOnCheck},check: {enable: true,chkStyle: 'checkbox',chkboxType: { 'Y': 's', 'N': 'ps' }}}"
								style="max-height: 530px; overflow: auto; padding: 15px;">
							</div>
                		</div>
					</div>
					<div class="input-group input-group-sm" id="clean_skill_btn">
                        <button type="button" class="btn btn-sm btn-default" onclick="agentCallStat.cleanSkillGroup()"><span
                                class="glyphicon glyphicon-refresh"></span> 清空技能组
                        </button>
                    </div>
          		    <div class="input-group input-group-sm" id="agentDiv">
        		         <span class="input-group-addon">坐席</span>
        		         <select name="agentId" id="agentId" data-mars="common.userDict" data-mars-top="true" multiple="multiple" size="1">
        		         </select>
				    </div>
					<div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="agentCallStat.searchData('1')"><span class="glyphicon glyphicon-search"></span> 查询 </button>
                    </div>
                </div>
            </div>
            <div class="ibox-content mt-15">
                <table id="tree-table">
                </table>
           </div>
			<div class="stat-desc ibox-content mt-15">
				<fieldset class="content-title">
		  			<legend>统计口径</legend>
			    </fieldset>
			呼入话务:<br>
			1、振铃数：来电振铃数(包含坐席接通数+坐席未接数)，转坐席数<br>
			2、坐席接通数：系统分配给坐席、且坐席接通的通话数<br>
			3、坐席接通率：(坐席接通数/转坐席数)*100%<br>
			4、15s内接通率：客户振铃后，15s内接通的电话数占振铃数的百分比，（(15s接通数/转坐席数)*100%）<br>
			5、服务总时长：坐席接通电话的通话总时长<br>
			6、呼入平均通话时长：坐席接通电话的通话评价时长，服务总时长/坐席接通数<br>
			7、振铃时长：来电振铃总时长，单位：秒<br>
			8、平均振铃时长：振铃总时长/转坐席数，单位：秒<br>
			9、话后整理时长：来电话后整理总时长，单位：秒<br>
			10、平均话后整理时长：话后整理总时长/坐席接通数<br>
			11.参评率：客户拨打电话后参与满意度评价的百分比，(参与评价量/坐席接通数) *100%<br>
			12.满意率：客户拨打电话后满意度调查给出满意的百分比，(评价为非常满意、满意的通话数/参与评价量) *100%<br>
			呼出话务：<br>
			1、呼出总数：坐席外呼的总数(包含接通、未接通的)<br>
			2、客户接通数：坐席外呼客户，客户接通的通话数<br>
			3、客户接通率：(客户接通数/坐席呼出数)*100%<br>
			4、呼出总时长：坐席外呼客户的通话总时长<br>
			5、呼出平均通话时长：呼出总时长/坐席接通数<br>
			席间话务：<br>
			1、席间呼入数：坐席接听来自坐席的电话的次数<br>
			2、席间呼出数：坐席拨打坐席的电话的次数<br>
	            	</div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript">

        jQuery.namespace("agentCallStat");
      	//重置
		agentCallStat.reset=function(){
			$("#divId select").val("");
			$("#limitDate").val(getThisMonthStartDate() + " ~ " + getThisMonthEndDate());
	    	$("#stType").val("skillStat");
	    	//复选框重置
			$("#agentId").multiselect("destroy");
       		requreLib.setplugs('multiselect',function(){
				$("#agentId").multiselect({
					 enableFiltering: true,
					 maxHeight: 200,
					 includeSelectAllOption: true,
					 selectAllText:'全选',
					 nonSelectedText: '请选择'
				});
			});
        		
		};
        
        requreLib.setplugs("layui",function(){
        	$("#searchForm").render({success:function(result){
	  			if(result['common.limitToday']){
                	agentCallStat.loadData();
        		}
        	}});
        	layui.use('laydate', function(){
    			  var laydate = layui.laydate;
    			  laydate.render({ elem: '#limitDate',range: '~' ,format: 'yyyy-MM-dd',btns: ['confirm'] });
    		})
    	});
        
        agentCallStat.searchData=function(flag){
        	$("#searchForm").queryData();
		}
        
		var zTree, rMenu;
		requreLib.setplugs('slimscroll,ztree', function() {
			$('#ztree').slimScroll({
				   height: '430px',
	                color: '#ddd'
			});
		});
		
		function zTreeOnClick(event, treeId, treeNode) {
			zTreeGetNodes();
			hideMenu('consultTypeZtree');
		}

		function zTreeOnCheck(event,treeId,treeNode){
			zTreeGetNodes();
		}

		function zTreeGetNodes(){
			var ids = '';
			var names = '';
			var nodes = $.fn.zTree.getZTreeObj("ztree").getCheckedNodes(true);
			if(nodes){
				for(var i = 0; i<nodes.length; i++){
					if(nodes[i].id != undefined){
						ids += ','+nodes[i].id;
						names += ','+nodes[i].name;
					}
				}
			}

			$("#skillName").val(names.substring(1));
			$("#skillId").val(ids.substring(1));
			var skillId = ids.substring(1);
			
			$("#agentDiv").render({data:{groupId:skillId},success: function (data) {
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect("destroy").multiselect({
						 enableFiltering: true,
						 maxHeight: 200,
						 includeSelectAllOption: true,
						 selectAllText:'全选',
						 nonSelectedText: '请选择'
					});
				});
			}});
		}
		
		//显示菜单
		function showMenu(obj,treeId) {
			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).css({ left: leftPx, top: topPx+heightPx }).slideDown("fast");
		    	if(treeId=="consultTypeZtree"){
			    	$("body").bind("mousedown", onBodyDownPc);
		    	}else{
			    	$("body").bind("mousedown", onBodyDownPc);
		    	}
		}
		//隐藏菜单
		function hideMenu(divId) {
		    $("#"+divId).fadeOut("fast");
		    	$("body").unbind("mousedown", onBodyDownPc);
		}
		//全局点击事件
		function onBodyDownPc(event) {
		    if (!( event.target.id == "consultTypeZtree" || event.target.id == "proCodeShow" || $(event.target).parents("#ztree").length > 0)) {
		        hideMenu('consultTypeZtree');
		  	}
		}
        
        var formatJson = {
				type:'bar',
				valueKey:[],
				legendData:[],
				xAxisKey:'AGENT_NAME',
				seriesName:'话务量',
				echarTile:'坐席话务量统计',
				stack : true
				}

		var resultJson = {}
        
        $(function () {
            $("#searchForm").render({
                success: function (data) {

                	requreLib.setplugs('multiselect',function(){
    					$("#agentId").multiselect({
       					 	 enableFiltering:true,
   						 	 maxHeight: 400,
   							 includeSelectAllOption: true,
   							 selectAllText:'全选',
   							 nonSelectedText: '请选择'
   						});
        			});
                    var statData = data['CallStatDao.agentCallStatList'];
    				resultJson = statData;
                    if (statData) {
                        var updateTime = statData.updateTime;
                        if (updateTime && updateTime != "") {
                            $("#titleAndTime").html("<font color='#5cb85c'>(数据更新时间:" + updateTime + ")</font>");
                        }
                    }
                }
            });
          	//监听radio改变事件
			$("#stType").change(function(e){
				$("#stType").val();
				
				agentCallStat.loadData();
	        });
        });
        
        var exData = []

        agentCallStat.loadData = function () {
        	$("#searchForm").initTable({
            	url:'${ctxPath}/webcall?action=CallStatDao.agentCallStatList',
				page:true,
            	limit:15,	
				limits:[10,15,20,30,50,100,200,500],
            	loading:true,
            	totalRow: true,
				title:'坐席话务分析报表',
            	cellMinWidth:100,
            	id:'tree-table',
            	cols: [[
            		{align: 'center', title: '',colspan: 2,totalRowText: '汇总'},
					{align: 'center', title: '呼入话务', colspan: 12},
					{align: 'center', title: '呼出话务', colspan: 5},
					{align: 'center', title: '席间话务', colspan: 2}
				],[
				{
					field: 'SKILL_GROUP_NAME',
					title: '统计维度',
					align: 'center',fixed:'left',
					minWidth:120,
					templet:function(row){
						if(row.DATE_VALUE && row.DATE_VALUE != undefined){
							return row.DATE_VALUE;
						}
						if(row.SKILL_GROUP_NAME && row.SKILL_GROUP_NAME != undefined){
							return row.SKILL_GROUP_NAME;
						}
						return '';
			        }
				},
				{
					field: 'AGENT_NAME',
					title: '坐席',
					align: 'center',fixed:'left',
					totalRowText: '汇总',
					minWidth:150,
					templet:function(row){
						return  row.AGENT_PHONE+ '-' + row.AGENT_NAME;
			        }
				},
				{
					field: 'AGENT_COUNT',
					title: '振铃数',
					align: 'center',
					sort: 'true',
					totalRow: true,
					minWidth:110
				},
				{
					field: 'IN_CONN_SUCC_COUNT',
					title: '坐席接通数',
					align: 'center',
					sort: 'true',
					totalRow: true,
					minWidth:110
				},
				{
					field: 'IN_CONN_SUCC_PER',
					title: '坐席接通率',
					align: 'center',
					minWidth:110,
					totalRow: true,
					templet:function(row){
						return formatPerse(row.IN_CONN_SUCC_COUNT/row.AGENT_COUNT) + '%';
			        }
				},
				{
					field: 'IN_CALL_LESS_FIFTEEN_COUNT_PER',
					title: '15s接通率',
					align: 'center',
					minWidth:110,
					totalRow: true,
					templet:function(row){
						return formatPerse(row.IN_CALL_LESS_FIFTEEN_COUNT/row.AGENT_COUNT) + '%';
			        }
				},
				{
					field: 'IN_TOTAL_TIME',
					title: '服务总时长',
					align: 'center',
					minWidth:110,
					totalRow: true,
					templet:function(row){
						return formatSeconds(row.IN_TOTAL_TIME);
			        }
				},
				{
					field: 'IN_TOTAL_TIME_AVG',
					title: '呼入平均通话时长',
					align: 'center',
					minWidth:150,
					totalRow: true,
					templet:function(row){
						return formatSeconds(formatInt(row.IN_TOTAL_TIME,row.IN_CONN_SUCC_COUNT));
			        }
				},
				{
					field: 'IN_ALERTING_TIME',
					title: '振铃时长',
					align: 'center',
					sort: 'true',
					totalRow: true,
					minWidth:130
				},
				{
					field: 'IN_ALERTING_TIME_AVG',
					title: '平均振铃时长',
					align: 'center',
					sort: 'true',
					totalRow: true,
					minWidth:130,
					templet:function(row){
		        		return formatInt(row.IN_ALERTING_TIME,row.AGENT_COUNT);
			        }
				},
				{
					field: 'IN_WORKREADY_TIME',
					title: '话后整理时长',
					align: 'center',
					totalRow: true,
					minWidth:150
				},
				{
					field: 'IN_WORKREADY_TIME_AVG',
					title: '平均话后整理时长',
					align: 'center',
					totalRow: true,
					minWidth:150,
					templet:function(row){
		        		return formatInt(row.IN_WORKREADY_TIME,row.IN_CONN_SUCC_COUNT);
			        }
				},
		         {
					align:'center',
					field:'SATISF_COUNT_PER',
					totalRow: true,
					title:  '参评率'
					,templet:function(row){
						return formatPerse(row.SATISF_COUNT/row.IN_CONN_SUCC_COUNT) + '%';
		         	}
				},
		         {
					align:'center',
					field:'SATISF_GOOD_COUNT_PER', 
					totalRow: true,
					minWidth:110,
					title:  '满意率',
					templet:function(row){
						return formatPerse(row.SATISF_GOOD_COUNT/row.SATISF_COUNT) + '%';
		         	}
				},
				
				{
					field: 'OUT_CALL_COUNT',
					title: '呼出总数',
					align: 'center',
					totalRow: true,
					sort: 'true',
					minWidth:110
				},
				{
					field: 'OUT_CONN_SUCC_COUNT',
					title: '客户接通数',
					align: 'center',
					totalRow: true,
					sort: 'true',
					minWidth:110
				},
				{
					field: 'OUT_CONN_SUCC_RATIO',
					title: '客户接通率',
					align: 'center',
					totalRow: true,
					minWidth:110,
					templet:function(row){
						return formatPerse(row.OUT_CONN_SUCC_COUNT/row.OUT_CALL_COUNT) + '%';
			        }
				},
				{
					field: 'OUT_TOTAL_TIME',
					title: '呼出总时长',
					align: 'center',
					minWidth:110,
					totalRow: true,
					templet:function(row){
						return formatSeconds(row.OUT_TOTAL_TIME);
			        }
				},
				{
					field: 'OUT_TOTAL_TIME_AVG',
					title: '呼出平均通话时长',
					align: 'center',
					sort: 'true',
					minWidth:150,
					totalRow: true,
					templet:function(row){
						return formatSeconds(formatInt(row.OUT_TOTAL_TIME,row.OUT_CONN_SUCC_COUNT));
			        }
				},
				
				{
					field: 'AGENT_IN_CALL_COUNT',
					title: '席间呼入数',
					align: 'center',
					sort: 'true',
					totalRow: true,
					minWidth:110
				},
				{
					field: 'AGENT_OUT_CALL_COUNT',
					title: '席间呼出数',
					align: 'center',
					totalRow: true,
					sort: 'true',
					minWidth:110
				}
				
				]],
				done:function(res,curr,count){
					var stType = $("#stType").val();
					if('dayStat'==stType){
						$("[data-field='SKILL_GROUP_NAME']").find('div').find('span').html('日期');
					}else {
						$("[data-field='SKILL_GROUP_NAME']").find('div').find('span').html('技能组');
					}
					
					if(res.data&&res.data.length>0){
						exData = res.data;
						
		        		var IN_CONN_SUCC_COUNT = 0;
		        		var AGENT_COUNT = 0;
		        		var IN_CALL_LESS_FIFTEEN_COUNT = 0;
		        		var IN_CALL_COUNT = 0;
		        		var IN_TOTAL_TIME = 0;
		        		var IN_ALERTING_TIME = 0;
		        		var IN_WORKREADY_TIME = 0;
		        		var SATISF_COUNT = 0;
		        		var SATISF_GOOD_COUNT = 0;
	
		        		var OUT_CONN_SUCC_COUNT = 0;
		        		var OUT_CALL_COUNT = 0;
		        		var OUT_TOTAL_TIME = 0;

		        		var AGENT_IN_CALL_COUNT = 0;
		        		var AGENT_OUT_CALL_COUNT = 0;
		        		
		        		layui.each(res.data,function(index,d){
		        			IN_CONN_SUCC_COUNT+=Number(d.IN_CONN_SUCC_COUNT);
		        			AGENT_COUNT+=Number(d.AGENT_COUNT);
		        			IN_CALL_LESS_FIFTEEN_COUNT+=Number(d.IN_CALL_LESS_FIFTEEN_COUNT);
		        			IN_CALL_COUNT +=Number(d.IN_CALL_COUNT);
		        			IN_TOTAL_TIME +=Number(d.IN_TOTAL_TIME);
		        			IN_ALERTING_TIME+=Number(d.IN_ALERTING_TIME);
		        			IN_WORKREADY_TIME+=Number(d.IN_WORKREADY_TIME);
		        			SATISF_COUNT+=Number(d.SATISF_COUNT);
		        			SATISF_GOOD_COUNT+=Number(d.SATISF_GOOD_COUNT);
	
		        			OUT_CONN_SUCC_COUNT += Number(d.OUT_CONN_SUCC_COUNT);
		        			OUT_CALL_COUNT +=Number(d.OUT_CALL_COUNT);
		        			OUT_TOTAL_TIME +=Number(d.OUT_TOTAL_TIME);

		        			AGENT_IN_CALL_COUNT+=Number(d.AGENT_IN_CALL_COUNT);
		        			AGENT_OUT_CALL_COUNT+=Number(d.AGENT_OUT_CALL_COUNT);
		        		})
		        		//汇总数据
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_COUNT"] .layui-table-cell').text(AGENT_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CONN_SUCC_COUNT"] .layui-table-cell').text(IN_CONN_SUCC_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CALL_COUNT"] .layui-table-cell').text(OUT_CALL_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CONN_SUCC_COUNT"] .layui-table-cell').text(OUT_CONN_SUCC_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_IN_CALL_COUNT"] .layui-table-cell').text(AGENT_IN_CALL_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_OUT_CALL_COUNT"] .layui-table-cell').text(AGENT_OUT_CALL_COUNT);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CONN_SUCC_PER"] .layui-table-cell').text(formatPerse(IN_CONN_SUCC_COUNT/AGENT_COUNT) + '%');
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CALL_LESS_FIFTEEN_COUNT_PER"] .layui-table-cell').text(formatPerse(IN_CALL_LESS_FIFTEEN_COUNT/AGENT_COUNT)+"%");
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_TOTAL_TIME"] .layui-table-cell').text(formatSeconds(IN_TOTAL_TIME));
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_TOTAL_TIME_AVG"] .layui-table-cell').text(formatSeconds(formatInt(IN_TOTAL_TIME,IN_CONN_SUCC_COUNT)));
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_ALERTING_TIME_AVG"] .layui-table-cell').text(formatInt(IN_ALERTING_TIME,AGENT_COUNT));
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_WORKREADY_TIME_AVG"] .layui-table-cell').text(formatInt(IN_WORKREADY_TIME,IN_CONN_SUCC_COUNT));
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_ALERTING_TIME"] .layui-table-cell').text(IN_ALERTING_TIME);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_WORKREADY_TIME"] .layui-table-cell').text(IN_WORKREADY_TIME);
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="SATISF_COUNT_PER"] .layui-table-cell').text(formatPerse(SATISF_COUNT/IN_CONN_SUCC_COUNT)+"%");
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="SATISF_GOOD_COUNT_PER"] .layui-table-cell').text(formatInt(SATISF_GOOD_COUNT/SATISF_COUNT)+"%");
		        		
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CONN_SUCC_RATIO"] .layui-table-cell').text(formatPerse(OUT_CONN_SUCC_COUNT/OUT_CALL_COUNT)+"%");
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_TOTAL_TIME"] .layui-table-cell').text(formatSeconds(OUT_TOTAL_TIME));
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_TOTAL_TIME_AVG"] .layui-table-cell').text(formatSeconds(formatInt(OUT_TOTAL_TIME,OUT_CONN_SUCC_COUNT)));
		        		
		        		//汇总数据表述
		        		$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_COUNT"] .layui-table-cell').attr('title','呼入振铃数');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CONN_SUCC_COUNT"] .layui-table-cell').attr('title','呼入坐席接通数');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CONN_SUCC_PER"] .layui-table-cell').attr('title','呼入坐席接通率');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_CALL_LESS_FIFTEEN_COUNT_PER"] .layui-table-cell').attr('title','呼入15s接通率');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_TOTAL_TIME"] .layui-table-cell').attr('title','呼入服务总时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_TOTAL_TIME_AVG"] .layui-table-cell').attr('title','呼入平均通话时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_ALERTING_TIME_AVG"] .layui-table-cell').attr('title','呼入平均振铃时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_WORKREADY_TIME_AVG"] .layui-table-cell').attr('title','呼入平均话后整理时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_ALERTING_TIME"] .layui-table-cell').attr('title','呼入振铃时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="IN_WORKREADY_TIME"] .layui-table-cell').attr('title','呼入话后整理时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="SATISF_COUNT_PER"] .layui-table-cell').attr('title','呼入参评率');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="SATISF_GOOD_COUNT_PER"] .layui-table-cell').attr('title','呼入满意率');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CALL_COUNT"] .layui-table-cell').attr('title','呼出总数');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CONN_SUCC_COUNT"] .layui-table-cell').attr('title','呼出客户接通数');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_CONN_SUCC_RATIO"] .layui-table-cell').attr('title','呼出客户接通率');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_TOTAL_TIME"] .layui-table-cell').attr('title','呼出总时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="OUT_TOTAL_TIME_AVG"] .layui-table-cell').attr('title','呼出平均通话时长');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_IN_CALL_COUNT"] .layui-table-cell').attr('title','席间呼入数');
						$("div[lay-id=tree-table]").find('.layui-table-total td[data-field="AGENT_OUT_CALL_COUNT"] .layui-table-cell').attr('title','席间呼出数');
		        		
		        		var sumJson = {
		        				'AGENT_PHONE':'汇总',
		        				'AGENT_NAME':' ',	
		        				'DATE_VALUE':'',
		        				'SKILL_GROUP_NAME':'',
		        				'IN_CONN_SUCC_COUNT':IN_CONN_SUCC_COUNT,
		        				'AGENT_COUNT':AGENT_COUNT,
		        				'IN_CALL_LESS_FIFTEEN_COUNT':IN_CALL_LESS_FIFTEEN_COUNT,
		        				'IN_CALL_COUNT':IN_CALL_COUNT,
		        				'IN_TOTAL_TIME':IN_TOTAL_TIME,
		        				'IN_ALERTING_TIME':IN_ALERTING_TIME,
		        				'IN_WORKREADY_TIME':IN_WORKREADY_TIME,
		        				'SATISF_COUNT':SATISF_COUNT,
		        				'SATISF_GOOD_COUNT':SATISF_GOOD_COUNT,
		        				'OUT_CONN_SUCC_COUNT':OUT_CONN_SUCC_COUNT,
		        				'OUT_CALL_COUNT':OUT_CALL_COUNT,
		        				'OUT_TOTAL_TIME':OUT_TOTAL_TIME,
		        				'AGENT_IN_CALL_COUNT':AGENT_IN_CALL_COUNT,
		        				'AGENT_OUT_CALL_COUNT':AGENT_OUT_CALL_COUNT
		        		};
		        		exData.push(sumJson);
		        	}
					
		        }
            });
        }

         agentCallStat.exportAgentCallStat = function () {
        	var isExportAll = "${param.allExport}";
        	console.log(isExportAll);
        	if(isExportAll!=""&&isExportAll=='1'){
        		agentCallStat.exportAll();
        	}else{
        		 layui.use('table', function(){
	           		  	var table = layui.table;
	           		  	table.exportFile('tree-table',exData ,'xls');
	              })
        	}
        } 
        
         agentCallStat.cleanSkillGroup = function () {
        	 $("#skillName").val("");
        	 $("#skillId").val("");
        	 $("#skill_select").render();
         } 
        

        //合并纵向单元格
        function portaitRowSpan() {
            var rowText = "";
            var rowNum = 1;
            var rowObj;
            var selectTd = $(".row-span");
            var len = selectTd.length;
            $(".row-span").each(function (i) {
                var thisText = $(this).text();
                if (!rowObj || i == 0) {
                    rowText = thisText;
                    rowObj = $(this);
                } else {
                    if (thisText == rowText) {
                        rowNum++;
                        $(this).removeAttr("class");
                        $(this).remove();
                        if (i == len - 1) {
                            rowObj.attr("rowspan", rowNum);
                        }
                    } else {
                        rowObj.attr("rowspan", rowNum);
                        rowText = thisText;
                        rowNum = 1;
                        rowObj = $(this);
                    }
                }
            });
        }

        /*
        //自定义模板的格式化方法,可直接使用{{time:XXXX}}
        $.views.converters("time", function(val) {
            return formatSeconds(val);
        }); */

        //格式化秒数
        function formatSeconds(value) {
        	if(value==''||value==undefined){
        		return '00:00:00';
        	}
            if (parseInt(value).toString() == 'NaN') {
                return "00:00:00";
            }
            var theTime = parseInt(value);// 秒
            var theTime1 = 0;// 分
            var theTime2 = 0;// 小时
            if (theTime > 60) {
                theTime1 = parseInt(theTime / 60);
                theTime = parseInt(theTime % 60);
                if (theTime1 > 60) {
                    theTime2 = parseInt(theTime1 / 60);
                    theTime1 = parseInt(theTime1 % 60);
                }
            }
            var result = theTime;
            if (theTime < 10) {
                result = "0" + theTime;
            }
            if (theTime1 > 0) {
                if (theTime1 < 10) {
                    result = "0" + theTime1 + ":" + result;
                } else {
                    result = "" + theTime1 + ":" + result;
                }
            } else {
                result = "00:" + result;
            }
            if (theTime2 > 0) {
                if (theTime2 < 10) {
                    result = "0" + theTime2 + ":" + result;
                } else {
                    result = "" + theTime2 + ":" + result;
                }
            } else {
                result = "00:" + result;
            }
            return result;
        }
		function formatInt(v1,v2){
			if(isNaN(v1)||isNaN(v2)){
		    	return 0;
		    }
			if(parseInt(v2)==0){
				return 0;
			}
			var value = parseInt(v1)/parseInt(v2);
			if(isNaN(value)){
		    	return 0;
		    }
            return Math.round(value);
		}
		
        function formatPerse(x) {
        	if(x==undefined || x == Infinity || x == ''){
        		return 0;
        	}
            var f = parseFloat(x);
            if (isNaN(f)) {
                return 0;
            }
            f = Math.round(x * 10000) / 100;
            return f + "";
        }

        function formatPerse2() {
            var editors = arguments;
            var v1 = editors[0];
            var v2 = editors[1];
            var x = v2 - v1;
            var f = parseFloat(x / v2);
            if (isNaN(f)) {
                return 0;
            }
            f = Math.round(f * 10000) / 100;
            return f + "";
        }
        
        function divOper() {
            var editors = arguments;
            var v1 = editors[0];
            var v2 = editors[1];
            var f = parseFloat(v1 / v2);
            if (isNaN(f)) {
                return 0;
            }
            //f = Math.round(f * 10000) / 100;
            return f + "";
        }
        

        function formatToMinute(seconds) {
            var f = parseFloat(seconds);
            if (isNaN(f) || f == 0) {
                return 0;
            }
            return (Math.round(parseFloat(f / 60) * 100)) / 100;
        }

        function onEcharts(oper){
			formatJson.legendData = ['呼入数','呼入接通数','呼出数','呼出接通数','席间呼入数','席间呼出数'];
			formatJson.valueKey = ['IN_CALL_COUNT','IN_CONN_SUCC_COUNT','OUT_CALL_COUNT','OUT_CONN_SUCC_COUNT','AGENT_IN_CALL_COUNT','AGENT_OUT_CALL_COUNT'];
			if(resultJson&&resultJson.total>0){
				popup.layerShow({type:1,title:'坐席话务量统计图',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/stat/echarts.jsp",{});
			}else{
				layer.open({title: '提示',content: '无数据显示！'});    
			}

		}
      //设置时间
		agentCallStat.onCasecade = function(p){
      	var dateRange = p.val();
         	if(dateRange == "today") {
         		$("#limitDate").val(getTodayDate() + " ~ " + getTodayDate());
         	}else if(dateRange == "yesterday") {
      		$("#limitDate").val(getYesterDayDate() + " ~ " + getYesterDayDate());
      	}else if(dateRange == "thisWeek") {
         		$("#limitDate").val(getThisWeekStartDate() + " ~ " + getThisWeekEndDate());
         	}else if(dateRange == "RecentlyOneMonth") {
         		$("#limitDate").val(getThisMonthStartDate() + " ~ " + getThisMonthEndDate());
         	}else if(dateRange == "RecentlyThreeMonth") {
      		$("#limitDate").val(getRecentlyThreeMonthStartDate() + " ~ " + getTodayDate());
      	}
      }
      
		agentCallStat.exportAll = function(){
			var data = form.getJSONObject("#searchForm");
			data["pageIndex"]= 1;
			data["pageSize"]= 10000;
			var exData1 = [];
			ajax.remoteCall("${ctxPath}/webcall?action=CallStatDao.agentCallStatList",data, function (res) {
			if(res.data&&res.data.length>0){
				exData1 = res.data;
        		var IN_CONN_SUCC_COUNT = 0;
        		var AGENT_COUNT = 0;
        		var IN_CALL_LESS_FIFTEEN_COUNT = 0;
        		var IN_CALL_COUNT = 0;
        		var IN_TOTAL_TIME = 0;
        		var IN_ALERTING_TIME = 0;
        		var IN_WORKREADY_TIME = 0;
        		var SATISF_COUNT = 0;
        		var SATISF_GOOD_COUNT = 0;
        		var OUT_CONN_SUCC_COUNT = 0;
        		var OUT_CALL_COUNT = 0;
        		var OUT_TOTAL_TIME = 0;
        		var AGENT_IN_CALL_COUNT = 0;
        		var AGENT_OUT_CALL_COUNT = 0;
        		layui.each(res.data,function(index,d){
        			IN_CONN_SUCC_COUNT+=Number(d.IN_CONN_SUCC_COUNT);
        			AGENT_COUNT+=Number(d.AGENT_COUNT);
        			IN_CALL_LESS_FIFTEEN_COUNT+=Number(d.IN_CALL_LESS_FIFTEEN_COUNT);
        			IN_CALL_COUNT +=Number(d.IN_CALL_COUNT);
        			IN_TOTAL_TIME +=Number(d.IN_TOTAL_TIME);
        			IN_ALERTING_TIME+=Number(d.IN_ALERTING_TIME);
        			IN_WORKREADY_TIME+=Number(d.IN_WORKREADY_TIME);
        			SATISF_COUNT+=Number(d.SATISF_COUNT);
        			SATISF_GOOD_COUNT+=Number(d.SATISF_GOOD_COUNT);
        			OUT_CONN_SUCC_COUNT += Number(d.OUT_CONN_SUCC_COUNT);
        			OUT_CALL_COUNT +=Number(d.OUT_CALL_COUNT);
        			OUT_TOTAL_TIME +=Number(d.OUT_TOTAL_TIME);
        			AGENT_IN_CALL_COUNT+=Number(d.AGENT_IN_CALL_COUNT);
        			AGENT_OUT_CALL_COUNT+=Number(d.AGENT_OUT_CALL_COUNT);
        		})
        		//汇总数据
        		var sumJson = {
        				'AGENT_PHONE':'汇总',
        				'AGENT_NAME':' ',	
        				'DATE_VALUE':'',
        				'SKILL_GROUP_NAME':'',
        				'IN_CONN_SUCC_COUNT':IN_CONN_SUCC_COUNT,
        				'AGENT_COUNT':AGENT_COUNT,
        				'IN_CALL_LESS_FIFTEEN_COUNT':IN_CALL_LESS_FIFTEEN_COUNT,
        				'IN_CALL_COUNT':IN_CALL_COUNT,
        				'IN_TOTAL_TIME':IN_TOTAL_TIME,
        				'IN_ALERTING_TIME':IN_ALERTING_TIME,
        				'IN_WORKREADY_TIME':IN_WORKREADY_TIME,
        				'SATISF_COUNT':SATISF_COUNT,
        				'SATISF_GOOD_COUNT':SATISF_GOOD_COUNT,
        				'OUT_CONN_SUCC_COUNT':OUT_CONN_SUCC_COUNT,
        				'OUT_CALL_COUNT':OUT_CALL_COUNT,
        				'OUT_TOTAL_TIME':OUT_TOTAL_TIME,
        				'AGENT_IN_CALL_COUNT':AGENT_IN_CALL_COUNT,
        				'AGENT_OUT_CALL_COUNT':AGENT_OUT_CALL_COUNT
        		};
        		exData1.push(sumJson);
        		layui.use('table', function(){
           		  	var table = layui.table;
           		  	table.exportFile('tree-table',exData1 ,'xls');
              	})
        	}
		  });
		}
    </script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list_v2.jsp" %>
