<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>呼入呼出服务明细</title>
	<style type="text/css">
th {
	text-align: center; /** 设置水平方向居中 */
	vertical-align: middle /** 设置垂直方向居中 */
}

a:link {
	color: #00adff;
}

.input-width {
	width: 170px !important;
}

.radio-inline {
	line-height: 20px;
}
</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form name="searchForm" class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span>呼入呼出服务明细</span><span id="titleAndTime"></span><span id="sub"></span>
					</h5>

					<div class="form-group">
						<div class="input-group">
							<span class="input-group-addon">坐席名称</span> 
							<input type="text"
								name="AGENT_NAME" class="form-control input-sm"
								style="width: 150px !important;">
						</div>
						<div class="input-group">
							<span class="input-group-addon">坐席工号</span> 
							<input type="text"
								name="AGENT_ACC" class="form-control input-sm"
								style="width: 150px !important;">
						</div>
						<div class="input-group">
							<span class="input-group-addon">坐席号码</span> 
							<input type="text"
								name="AGENT_PHONE" class="form-control input-sm"
								style="width: 150px !important;">
						</div>
						<div class="input-group">
							<span class="input-group-addon">客户号码</span> 
							<input type="text"
								name="USER_PHONE" class="form-control input-sm"
								style="width: 150px !important;">
						</div>
						<div class="input-group">
							<span class="input-group-addon">挂机原因</span> 
							<input type="text"
								name="CLEAR_CAUSE" class="form-control input-sm"
								style="width: 150px !important;">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon" >开始时间</span>
							<input type="text" class="form-control input-sm" id="startDate" name="startDate"
							 style="width:150px"  autocomplete="off">
							<span class="input-group-addon">-</span>	
							<input type="text" class="form-control input-sm" id="endDate" name="endDate"
							 style="width:150px"autocomplete="off">
                    	</div>
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default"
								onclick="callInCallOutDetail.loadData()">
								<span class="glyphicon glyphicon-search"></span> 搜索
							</button>
						</div>


						<div class="input-group input-group-sm pull-right" style="margin-left: 10px;">
							<button type="button" class="btn btn-sm btn-success btn-outline glyphicon glyphicon-import"
								onclick="callInCallOutDetail.importData()">导入</button>
								<button type="button" class="btn btn-sm btn-info btn-outline glyphicon glyphicon-export"
									onclick="callInCallOutDetail.exportDetail()" >导出
								</button>
						</div>
					</div>

				</div>

			</div>

			<div class="ibox-content table-responsive">
				<table id="dataList"></table>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("callInCallOutDetail");

		$(function() {
			$("#searchForm").render({
				success : function() {
					requreLib.setplugs('select2',function(){
						$("#POST_CODE").select2({theme: "bootstrap"});
					});
					
					requreLib.setplugs('layui', function() {
						$("#startDate").val(getTodayStartTime());
				    	$("#endDate").val(getTodayEndTime());
						layui.use('laydate', function(){
					  		var laydate = layui.laydate;
					  		laydate.render({ elem: '#startDate' ,type: 'datetime'});
					  		laydate.render({ elem: '#endDate' ,type: 'datetime'});
						});
						callInCallOutDetail.loadData();
					})
				}

			});
		});

		//初始化加载数据
		callInCallOutDetail.loadData = function() {
			if(!checkDate('${param.exportMaxDay}')){
            	return;
            }
			$("#searchForm")
					.initTable(
							{
								mars : "callDetail.callInAndCallOutDetailList",
								id : "dataList",
								limit : '15',
								height : 'full-180',
								limits : [ 15, 25, 50, 100, 200 ],
								title : '呼入呼出服务明细',
								cols : [ [
										{
											width : 60,
											type : 'numbers',
											title : '序号'
										},
										{
											field : 'SERIAL_ID',
											title : '流水号',
											align : 'center'
										},
										
										{
											field : 'AGENT_ACC',
											title : '坐席工号',
											align : 'center'
										},
										{
											field : 'AGENT_NAME',
											title : '坐席名称',
											align : 'center'
										},
										{
											field : 'AGENT_PHONE',
											title : '坐席号码',
											align : 'center'
										},
										{
											field : 'USER_PHONE',
											title : '客户号码',
											align : 'center'
										},
										{
											field : 'BEGIN_TIME',
											title : '开始时间',
											align : 'center'
										},
										{
											field : 'END_TIME',
											title : '结束时间',
											align : 'center'
										},
										{
											field : 'RING_TIME',
											title : '振铃时长',
											align : 'center'
										},
										{
											field : 'RECORD_TIME',
											title : '通话时长',
											align : 'center'
										},
										{
											field : 'WORKREADY_TIME',
											title : '话后整理时长',
											align : 'center'
										},
										{
											field : 'CALL_TIME',
											title : '总时长',
											align : 'center'
										},
										{
											field : 'CLEAR_CAUSE',
											title : '挂机原因',
											align : 'center'
										},{
											field : 'CALL_TYPE',
											title : '呼叫类型',
											align : 'center'
										}] ],

							});
		}

		
		callInCallOutDetail.importData=function() {
			popup.layerShow({
				type : 2,
				title : "导入",
				offset : '20px',
				area : [ '420px', '200px' ]
			}, "${ctxPath}/pages/report/cx-call-detail-import.jsp", {type:"callin-callout-detail"});
		}

		//导出
		callInCallOutDetail.exportDetail = function(){
			if(!checkDate('${param.exportMaxDay}')){
            	return;
            }
			layer.confirm("是否导出呼入呼出服务明细",{icon: 3, title:"导出提示",btn:["确定","取消"],offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/callDetail?action=exportCallInCallOutDetail&"+$("#searchForm").serialize();
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>