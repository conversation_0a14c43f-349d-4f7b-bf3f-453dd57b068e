{{for}}
	{{if type == "02"}}
	<div class="input-group input-group-sm" >
		<span class="input-group-addon" style="width:82px;">{{:FIELD_TEXT}}</span>
		<select name="{{:FIELD_TABLE}}.extSearch.normal.{{:FIELD_NAME}}"  id="{{:FIELD_NAME}}" data-mars="common.getDict({{:dictCode}})"  class="form-control input-sm" style="width:{{:searchWidth || 150}}px;">
			<option value="">请选择</option>
		</select>
	</div>
	{{else type == "03"}}
	 	<div class="input-group input-group-sm" >
	 		{{if type == "03"&&groups == "Y"}}
			<span class="input-group-addon">{{:FIELD_TEXT}}</span>
			<input type="text" name="{{:FIELD_TABLE}}.extSearch.normal.{{:FIELD_NAME}}StartTime" id="{{:FIELD_NAME}}StartTime" class="form-control input-sm" style="width:{{:searchWidth || 152}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}StartDatebegin">
			<span class="input-group-addon">~</span>	
			<input type="text" id="{{:FIELD_NAME}}EndTime" name="{{:FIELD_TABLE}}.extSearch.normal.{{:FIELD_NAME}}EndTime" class="form-control input-sm" style="width: {{:searchWidth || 150}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}EndDateend">
			{{else vague == "02"}}
			<span class="input-group-addon">{{:FIELD_TEXT}}</span>
			<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}RightLike" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 152}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}StartDatebegin"> 
		 	{{else vague == "03"}}
			<span class="input-group-addon">{{:FIELD_TEXT}}</span>
			<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}LeftLike" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 152}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}StartDatebegin"> 
		 	{{else vague == "04"}}
			<span class="input-group-addon">{{:FIELD_TEXT}}</span>
			<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}AllLIke" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 152}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}StartDatebegin"> 
		 	{{else}}
			<span class="input-group-addon">{{:FIELD_TEXT}}</span>
			<input type="text" name="{{:FIELD_TABLE}}.extSearch.normal.{{:FIELD_NAME}}" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 152}}px;" autocomplete="off" data-format="{{:dateFormatter}}" data-type-date="{{:FIELD_NAME}}StartDatebegin"> 
			{{/if}}
	 	</div>
	{{else}}
	<div class="input-group input-group-sm" >
		{{if vague == "02"}}
		<span class="input-group-addon">{{:FIELD_TEXT}}</span>
		<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}RightLike" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 150}}px;" autocomplete="off">
		{{else vague == "03"}}
		<span class="input-group-addon">{{:FIELD_TEXT}}</span>
		<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}LeftLike" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 150}}px;" autocomplete="off">
		{{else vague == "04"}}
		<span class="input-group-addon">{{:FIELD_TEXT}}</span>
		<input type="text" name="{{:FIELD_TABLE}}.extSearch.like.{{:FIELD_NAME}}AllLIke" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 150}}px;" autocomplete="off">
		{{else}}
		<span class="input-group-addon">{{:FIELD_TEXT}}</span>
		<input type="text" name="{{:FIELD_TABLE}}.extSearch.normal.{{:FIELD_NAME}}" id="{{:FIELD_NAME}}" class="form-control input-sm" style="width:{{:searchWidth || 150}}px;" autocomplete="off">
		{{/if}}
	</div>
	{{/if}}
{{/for}}
