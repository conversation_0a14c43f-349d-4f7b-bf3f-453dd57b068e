package com.yq.web.taskgw.listener;

import java.util.List;
import java.util.Map;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;

import com.yq.web.taskgw.common.cache.TaskCache;
import com.yq.web.taskgw.common.log.CommonLogger;
import com.yq.web.taskgw.common.utils.Constants;
import com.yq.web.taskgw.dao.TaskDao;

/**
 * 获取任务监听器
 *
 */
//@WebListener
public class GetTaskListener implements ServletContextListener {

	public Logger logger = CommonLogger.getLogger();
	private ScheduledExecutorService service = null;

	/**
	 * Default constructor.
	 */
	public GetTaskListener() {
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see ServletContextListener#contextDestroyed(ServletContextEvent)
	 */
	public void contextDestroyed(ServletContextEvent arg0) {
		// TODO Auto-generated method stub
		if (service != null) {
			service.shutdown();
		}
		TaskCache.flag=false;
	}

	/**
	 * @see ServletContextListener#contextInitialized(ServletContextEvent)
	 */
	public void contextInitialized(ServletContextEvent arg0) {
		// TODO Auto-generated method stub
		try {
			String timespan = Constants.getAppConfigProperty("GET_TASK_TIME");
			if (timespan == null || "".equals(timespan)) {
				timespan = "10";
			}
			
			int time = Integer.parseInt(timespan);
			service = Executors.newScheduledThreadPool(1);

			logger.info(">>>启动获取任务定时器, 周期为"+time+"分");

			service.scheduleAtFixedRate(new TimerTask() {
				@Override
				public void run() {
					logger.info("开始扫描规则信息");
					try
					{
						if(TaskCache.getSize()==0)
						{
							List<String> schamesList=TaskDao.getInstance().schemasList;
							for(int i=0;i<schamesList.size();i++)
							{
								List<Map<String,Object>> taskList=TaskDao.getInstance().getTasks(schamesList.get(i));
								TaskCache.put(taskList);
							}
						}
						
						
					}
					catch(Exception e)
					{
						logger.error("扫描规则定时器出现异常1:"+e.getMessage(), e);
					}
					logger.info("扫描规则完成");
				}
			}, 2 * 60 * 1000, time * 60 * 1000, TimeUnit.MILLISECONDS);
			// }
		} catch (Exception e) {
			logger.error("扫描规则定时器出现异常:"+e.getMessage(), e);
		}

	}

}
