<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>未接来电列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off" >
       		<input type="hidden" name="type" value="all">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 未接来电列表</h5>
             		          <div class="input-group input-group-sm">
									<span class="input-group-addon">开始日期</span> 
									<input  name="beginTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="begintime" class="form-control input-sm Wdate">
							  </div>
							  <div class="input-group input-group-sm">
									<span class="input-group-addon">结束日期</span> 
									<input  name="endTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'begintime\',{d:0});}',maxDate:'#F{$dp.$D(\'begintime\',{d:14});}'})" class="form-control input-sm Wdate">
							  </div>
             		          <div class="input-group input-group-sm">
								        <span class="input-group-addon">挂机类型</span>	
									    <select class="form-control input-sm" name="callStepId" style="width:122px">
			                      			<option value=""> 请选择 </option>
			                      			<option value="1">IVR挂机</option>
			                      			<option value="2">排队挂机</option>
			                      			<option value="3">留言挂机</option>
			                      		</select>
							  </div>
             		          <div class="input-group input-group-sm">
								        <span class="input-group-addon">记录类型</span>	
									    <select class="form-control input-sm" name="recordType" style="width:122px">
			                      			<option value=""> 请选择 </option>
			                      			<option value="1">未接来电</option>
			                      			<option value="2">留言</option>
			                      		</select>
							   </div>
             		           <div class="input-group input-group-sm">
								        <span class="input-group-addon">处理状态</span>	
									    <select class="form-control input-sm" name="state">
			                      			<option value=""> 请选择</option>
			                      			<option value="0">待处理</option>
			                      			<option value="1">已处理</option>
			                      		</select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="NoAnswer.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="CallNoAnswerDao.list">
                             <thead>
	                         	 <tr>
	                         	 	  <th class="text-c">日期 </th>
								      <th class="text-c">主叫</th>
								      <th class="text-c">被叫</th>
								      <th class="text-c">IVR时间</th>
								      <th class="text-c">排队时间</th>
								      <th class="text-c">挂机时间</th>
								      <th class="text-c">排队停留时间</th>
								      <th class="text-c">挂机原因</th>
								      <th class="text-c">记录类型</th>
								      <th class="text-c">处理状态</th>
								      <th class="text-c">处理人</th>
								      <th class="text-c">处理时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td> {{:DATE_ID}}</td> 
											<td> {{call:CALLER _CALLER fn='getPhone'}}</td>  
											<td> {{call:CALLED _CALLED fn='getPhone'}}</td>  
											<td> {{call:BEGIN_TIME fn='fnTime'}}</td>  
											<td> {{call:QUEUE_TIME fn='fnTime'}}</td>  
											<td> {{call:END_TIME fn='fnTime'}}</td>  
											<td> {{call:QUEUE_STAY_TIME fn='formatSeconds'}}</td>  
											<td> {{getText:CALL_STEP_ID 'callStepId'}}</td>  
											<td> {{getText:RECORD_TYPE 'recordType'}}</td>  
											<td> {{getText:STATE 'state'}}</td>  
											<td> {{if HANDLER}}{{:HANDLER}}{{else}}-{{/if}}</td>  
											<td> {{if HANDLE_TIME}}{{:HANDLE_TIME}}{{else}}-{{/if}}</td>
											<td width = "160px">
											    {{if STATE == 0}}
									            	<a  href="javascript:void(0)" onclick="NoAnswer.updateData('{{:SERIAL_ID}}')">标记处理</a>&nbsp;&nbsp;
												{{/if}} 
												{{if RECORD_FILE}}{{if RECORD_FILE < 3}}{{else}}
									            	<a  href="javascript:void(0)" onclick="NoAnswer.recoredListener('{{:SERIAL_ID}}','{{:RECORD_FILE}}')">播放录音</a>
												{{/if}}{{/if}}
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("NoAnswer");
		$(function(){
			$("#searchForm").render({success:function(result){
			}});
		});
		NoAnswer.loadData=function(){
			$("#searchForm").render({success:function(result){
			}});
		}
		NoAnswer.updateData = function(serialId){
			ajax.remoteCall("${ctxPath}/servlet/callNoanswer?action=updateState", {serialId:serialId}, function(result) {
	  			if(result.state == 1){
	  				NoAnswer.loadData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}
		NoAnswer.recoredListener = function(serialId,recordFile) {

			ajax.remoteCall("${ctxPath}/servlet/call?action=getNoAnswerRecordFile",{serialId:serialId,recordFile:recordFile},function(result) {
				if(result.state == 0 ||!result.url|| result.url==''){
					layer.msg(result.msg,{icon: 7,offset:'70px'},function(index){
						layer.close(index);
					});
					return;
				}
				recordFile = result.url;
				popup.layerShow({type:1,title:'播放录音',offset:'20px',area:['485px','186px']},"${ctxPath}/pages/record/record-play-wav.jsp",{recordFile:recordFile});
  			});


		}
		function fnTime(time){
			return time.substring(11);
		}
		function formatSeconds(value) {
		    if(parseInt(value).toString() == 'NaN'){
		    	return "00小时00分00秒";
		    }
		    var theTime = parseInt(value);// 秒
		    var theTime1 = 0;// 分
		    var theTime2 = 0;// 小时
		    if(theTime > 60) {
		        theTime1 = parseInt(theTime/60);
		        theTime = parseInt(theTime%60);
		            if(theTime1 > 60) {
		            theTime2 = parseInt(theTime1/60);
		            theTime1 = parseInt(theTime1%60);
		            }
		    }
		        var result = ""+parseInt(theTime)+"秒";
		        if(theTime1 > 0) {
		        result = ""+parseInt(theTime1)+"分"+result;
		        }
		        if(theTime2 > 0) {
		        result = ""+parseInt(theTime2)+"小时"+result;
		        }
		    return result;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>