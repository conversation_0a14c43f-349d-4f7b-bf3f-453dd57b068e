<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>短信记录查询</title>
	<style>
    	.td-overflow{
			white-space: nowrap !important;
		    text-overflow: ellipsis !important;
		    overflow-x: hidden;
		    max-width: 133px;
		}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" name="searchForm" id="searchForm" >
		<div class="ibox">
			<div class="ibox-title clearfix">
 				<div class="form-group">
 					<h5> 短信发送统计</h5>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">开始日期</span> 
						<input onClick="WdatePicker({maxDate : '#F{$dp.$D(\'endDate\')||\'%y-%M-%d\'}',dateFmt: 'yyyy-MM-dd'})" id="startDate" name="starSendTime" data-mars="common.weekAgoDate" data-mars-top="true" size = "20" class="form-control input-sm Wdate" autocomplete="off">
					</div>
					
					<div class="input-group input-group-sm">
						<span class="input-group-addon">结束日期</span> 
						<input onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',dateFmt: 'yyyy-MM-dd'})" id="endDate" name="endSendTime" size = "20" data-mars="common.todayDate" data-mars-top="true" class="form-control input-sm Wdate" autocomplete="off">
					</div>
					
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="RecordList.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				   	</div>
 				</div>
			</div>
			<div class="ibox-content">
				<table  data-mars="SmsDao.statDay" class="table table-auto table-striped table-bordered table-hover table-condensed text-c" data-auto-fill="10">
					<thead>
						<tr>
							<th>日期</th>
							<th class="text-c">坐席工号</th>
							<th class="text-c">短信模板</th>
							<th class="text-c">发送数</th>
							<th class="text-c">成功数</th>							
							<th class="text-c">失败数</th>
						</tr> 
					</thead>
					<tbody id="dataList"></tbody>
				</table>
				<div class="row paginate">
                   	<jsp:include page="/pages/common/pagination.jsp"/>
	            </div> 
				<script id="list-template" type="text/x-jsrender">
					{{for list}}
						<tr>
							<td>{{:DATE_ID}}</td>
							<td>{{:AGENT_ID}}</td>
							<td>{{:SMS_TEMP_NAME}}</td>
							<td>{{:SMS_COUNT}}</td>
							<td>{{:SUCC_COUNT}}</td>
							<td>{{:FAIL_COUNT}}</td>
						</tr>
					{{/for}}
				</script>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("RecordList");
		
		$(function(){
			$("#searchForm").render();
		});
		
		RecordList.loadData=function(){
			$("#searchForm").searchData();
		}
		
		//短信状态， 10 待发送  20 已经发送   30 发送成功   90 发送失败
		function sendStateFn(state){
			if(state == 10){
				return '待发送';
			}
			if(state == 20){
				return '已经发送';
			}
			if(state == 30){
				return '发送成功';
			}
			if(state == 40){
				return '发送失败';
			}
			return '发送失败';
		}
		
		//回执状态，0 未收到  1 已收到
		function receiptState(state){
			if(state == 0){
				return '未收到';
			}
			if(state == 1){
				return '已收到';
			}
		}
		
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>