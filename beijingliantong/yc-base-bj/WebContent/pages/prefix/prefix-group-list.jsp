<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>号码组管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form  class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off">
       			<input name="prefixType" type="hidden" value="1" title="呼出"/>
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> <span data-i18n="外呼号码组管理"></span></h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon"><span data-i18n="号码组名称"></span></span>
									  <input type="text" name="groupName" class="form-control input-sm" style="width:140px">
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="PrefixGroup.searchData()"><span class="glyphicon glyphicon-search"></span> <span data-i18n="搜索"></span></button>
								</div>
							   <div class="input-group input-group-sm pull-right btn-group">
							       <button type="button" class="btn btn-sm btn-success" onclick="PrefixGroup.addData()">+<span data-i18n="新增号码组"></span></button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="prefixGroup.list">
                             <thead>
	                         	 <tr>
								      <th><span data-i18n="号码组名称"></span></th>
								      <th><span data-i18n="创建时间"></span></th>
								      <th><span data-i18n="操作"></span></th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td><a href="javascript:void(0)" onclick="PrefixGroup.editData('{{:PREFIX_GROUP_ID}}')" > {{:PREFIX_GROUP_NAME}}</a></td>
											<td>{{:CREATE_TIME}}</td>
                                            <td>
                                                <a href="javascript:void(0)" onclick="PrefixGroup.delData('{{:PREFIX_GROUP_ID}}','{{:PREFIX_GROUP_NAME}}')" > <span data-i18n="删除"></span></a> -
                                                <a href="javascript:void(0)" onclick="PrefixGroup.modPrefix('{{:PREFIX_GROUP_ID}}','{{:PREFIX_GROUP_NAME}}')" > <span data-i18n="号码维护"></span></a>
                                            </td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/language/i18-ent.js"></script>
	<script type="text/javascript">
		jQuery.namespace("PrefixGroup");
		
		PrefixGroup.searchData=function(){
			$("#searchForm").render({
				success:function() {
					loadPageI18n();
				}
			});
		}
		PrefixGroup.prefixList=function(){
		    popup.layerShow({type:2,title:getI18nText('号码管理'),offset:'20px',area:['50%','72%']},"${ctxPath}/pages/prefix/prefix-list.jsp",{});
		}
		PrefixGroup.addData=function(){
		    popup.layerShow({type:1,title:getI18nText('号码组新增'),offset:'20px',area:['350px','200px']},"${ctxPath}/pages/prefix/prefix-group-edit.jsp",{});
		}
		PrefixGroup.editData=function(groupId,groupName){
		    popup.layerShow({type:1,title:getI18nText('号码组修改'),offset:'20px',area:['350px','200px']},"${ctxPath}/pages/prefix/prefix-group-edit.jsp",{groupId:groupId});
		}
		PrefixGroup.modPrefix = function(groupId,groupName){
			var count = PrefixGroup.getSelectText(groupId);
			popup.openTab('${ctxPath}/pages/prefix/group-prefix.jsp',getI18nText('号码维护')+'('+groupName+')',{groupId:groupId,count:count})
		}
		PrefixGroup.delData = function(groupId,groupName){
			layer.confirm(getI18nText('groupDelConfirm','当前号码组将要被删除，是否继续？'),{icon: 3, title:getI18nText('删除提示'),offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/servlet/prefixGroup?action=delete", {groupId:groupId}, function(result) {
		  			if(result.state == 1){
					    layer.msg(getI18nText('Success',result.msg),{icon: 1,time:1200,offset:'40px'},function(){
					    	PrefixGroup.searchData();
					    });
					}else{
						layer.alert(getI18nText('Fail',result.msg),{icon: 5});
					}
	  			});
			});
		}
		PrefixGroup.getSelectText = function(id){
			var text = '0';
			$('#prefixCount option').each(function(){
				   if( $(this).val() == id){
				        text = $(this).text();
				   }
			});
			return text;
		}
		$(function(){
			$("#searchForm").render({
				success:function() {
					loadPageI18n();
				}
			});
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>