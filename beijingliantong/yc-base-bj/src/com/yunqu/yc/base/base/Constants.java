package com.yunqu.yc.base.base;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     //默认数据源名称(写)
	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";     //默认数据源名称(写)
	
	public final static String DS_READ_NAME = "yc-read-ds";     //默认数据源名称(读)
	
	public final static String MQ_LOGGER_NAME = "yc-mq";
	
	private final static AppContext context = AppContext.getContext("yc-api");
	
	public final static String VERSION_NAME="3.2.0";
	
	public final static String VERSION_DATE="2022-03-11";
	
	/**
	 * 统计库名称
	 */
	public static String getStatSchema(){
		return context.getProperty("STAT_DB","stat");
	}
	
	public final static String APP_NAME = "yc-base";     //应用
	
	/**
	 * 电销基础版本
	 */
	public final static String  BUSI_ID = "001";
	
	/**
	 * 管理员
	 */
	public final static int ROLE_TYPE_1=1;
	
	/**
	 * 班长
	 */
	public final static int ROLE_TYPE_2=2;
	
	/**
	 * 坐席
	 */
	public final static int ROLE_TYPE_3=3;
	/**
	 * 自定义角色
	 */
	public final static int ROLE_TYPE_9=9;
	
	/**
	 * 任务状态：启动中
	 */
	public final static int TASK_STATE_005=5;
	
	/**
	 * 外呼号码状态，0 正常
	 */
	public final static int PREFIX_STATE_USE = 0;
	/**
	 * 外呼号码状态，1 停用
	 */
	public final static int PREFIX_STATE_STOP = 0;
	
	/**
	 * 外呼类型，6:人工外呼
	 */
	public final static int CALL_TYPE_MANUAL = 6;
	
	/**
	 * 外呼类型，8:自动外呼
	 */
	public final static int CALL_TYPE_AUTO = 8;
	
	/**
	 * 通话记录类型
	 */
	public final static String RECORD_TYPE ="1";

	/**
	 * 未接来电记录类型
	 */
	public final static String NO_ANSWER_TYPE ="2";
	
	/**
	 * 语音留言类型
	 */
	public final static String WORK_TYPE ="4";
	/**
	 * 机器人
	 */
	public static final String ROBOT_TYPE = "5";

	/**
	 * 通话记录视频类型
	 */
	public final static String RECORD_VIDEO ="10";

	public static AppContext getContext(){
		return AppContext.getContext(APP_NAME);
	}

	public static AppContext getYcPortalContext(){
		return AppContext.getContext("yc-portal");
	}

	/**
	 * 启用页面菜单拦截
	 * @return
	 */
	public static boolean openFilterUrl(){
		return "1".equals(getContext().getProperty("openFilterUrl","0"));
	}

	/**
	 * 最大导出行数
	 * @return
	 */
	public static int maxExpCount(){
		return Integer.parseInt(getContext().getProperty("maxExpCount","100000"));
	}

	/**
	 * 启用校验Referer
	 * @return
	 */
	public static boolean checkReferer(){
		return "1".equals(getContext().getProperty("CHECK_REFERER","0")) && StringUtils.isNotBlank(getOrigin());
	}
	/**
	 * 获取源地址
	 * @return
	 */
	public static String getOrigin(){
		return getContext().getProperty("ORIGIN","");
	}
	
	/**
	 * 获取ccbar版本号
	 * @return
	 */
	public static int getCcbarVersion(){
		try {
			String property = getContext().getProperty("CCBAR_VERSION", "4");
			return Integer.parseInt(property);
		} catch (Exception e) {
		}
		return 4;
	}

	private static String contextPath = null;

	private static String portalContextPath = null;

	private static String contextPrefix = null;
	public static String getContextPath() {
		if(contextPath==null) {
			String prefix = getContextPrefix();
			contextPath = prefix+"/"+APP_NAME;
		}
		return contextPath;
	}

	public static String getContextPrefix() {
		if(contextPrefix==null) {
			contextPrefix = System.getProperty("mars.app.context.path", "");
			return contextPrefix;
		}
		return contextPrefix;
	}
	
	/**
	 * 获取平台easyExcel的依赖包版本号
	 * @return
	 */
	public static int getAliExcelType(){
		int type =0;
		if(StringUtils.isNotBlank(getContext().getProperty("ALI_EXCEL_TYPE", "0"))){
			type = Integer.parseInt(getContext().getProperty("ALI_EXCEL_TYPE", "0"));
		}
		return type;
	}
}

