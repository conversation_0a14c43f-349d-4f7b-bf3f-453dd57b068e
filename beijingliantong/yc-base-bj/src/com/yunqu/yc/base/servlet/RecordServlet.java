package com.yunqu.yc.base.servlet;

import com.yunqu.yc.base.base.AppBaseServlet;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;

/**
 * 获取录音路径
 *
 * <AUTHOR>
 */
//@WebServlet("/record")
@Deprecated
public class RecordServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    public void actionForIndex() {
        HttpServletRequest req = this.getRequest();
        HttpServletResponse resp = this.getResponse();
        String serialId = req.getParameter("serialId");
        String path = req.getParameter("path");
        String entId = req.getParameter("entId");
        ServletOutputStream out = null;
        try {
            resp.setHeader("Content-Type", "audio/mpeg");
            out = resp.getOutputStream();
            readStream(entId, serialId, path, out);
            out.flush();
            out.close();

        } catch (Exception e) {
            this.getLogger().error(e.getMessage(), e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                this.getLogger().error(e.getMessage(), e);
            }
        }
    }

    /**
     * 输出流
     *
     * @param entId
     * @param serialId
     * @param ext
     * @param out
     */
    private void readStream(String entId, String serialId, String path, ServletOutputStream out) {
        FileInputStream fis = null;
        try {
            String recordUrlPrefix = this.getRecordUrlPrefix(entId);
            if (path.startsWith(recordUrlPrefix)) {
                recordUrlPrefix = "/";
            }

            File file = getRecordFile(recordUrlPrefix, path);
            if (file == null) {
                //录音文件路径扩展中查找
                String recordUrlPrefixExt = this.getRecordUrlPrefixExt(entId);
                file = getRecordFileByExt(recordUrlPrefixExt, path);
                if (file == null) {
                    this.getResponse().sendError(404, "file not exist.");
                    return;
                }
            }

            byte[] buf = new byte[2048];
            int leng = 0;
            fis = new FileInputStream(file);
            while ((leng = fis.read(buf)) != -1) {
                out.write(buf, 0, leng);
            }

        } catch (Exception e) {
            getLogger().error(e.getMessage(), e);
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (Exception ignored) {
            }

        }
    }
}
