// JavaScript Document
$.fn.extend({
	onPage:function(fn){
		this[0].pageCallback=fn;
	}
});
function initMainMenu(){
	var _c=$('#container'),_m=$('#mainMenu');
	
	$('#mainMenu > .item').bind({
		click:function(e){
			var o=$(this),index=o.index(),chs=_c.find('.menuPageItem');
			chs.hide();
			chs.eq(index).show();
			if(_m[0].pageCallback) _m[0].pageCallback(index,o.children('.tip').text(),this);
		}
	});
	_c.children('.menuPageItem').eq(0).show();
}
$(function(){
	initMainMenu();	
});