.clearfix {

    &:before,
    &:after {
        content: " ";
        display: table;
    }

    &:after {
        clear: both;
    }
}

.abs-center {
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    /* IE 9 */
    -webkit-transform: translate(-50%, -50%);
    /* Safari and Chrome */
    -o-transform: translate(-50%, -50%);
    /* Opera */
    -moz-transform: translate(-50%, -50%);
    /* Firefox */
    transform: translate(-50%, -50%);
}

.flex {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    /*justify-content: space-between;*/
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-row {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-item {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: auto;
}

.monitor-table {
    width: 100%;
    border: 1px solid #66aaf1;
    border-radius: 4px;
    overflow: hidden;
    border-spacing: 0;
    font-size: 14px;

    th,
    td {
        padding: 7px 10px;
        line-height: 20px;
        border: 0;
        border-right: 1px solid #1158b9;

        a {
            color: #2aaffc;
        }

        &:nth-last-child(1) {
            border-right: 0
        }
    }

    thead,
    .th {
        background: rgba(14, 84, 171, .16);
        color: #9fc9ff;
    }

    tbody {
        td {
            border-bottom: 1px solid #1158b9;
            border-left: 0
        }
    }

    &[data-layout="fixed"] {
        table-layout: fixed;
    }

    &[data-align="left"] {

        td,
        th {
            text-align: left!important;
        }
    }
    
    &[data-type="full"] {
        height: 100%;

        th {
            line-height: 20px;
            padding: 5px 10px;
            text-align: center;
            border-bottom: 1px solid #1158b9;
            height: 30px;
        }

        td {
            position: relative;
        }

        .th-mix {
            position: relative;

            .th1 {
                padding: 5px;
                position: absolute;
                right: 0;
                top: 0;
                display: inline-block;
                text-align: right;
            }

            .th2 {
                padding: 5px;
                position: absolute;
                left: 0;
                bottom: 0;
                display: inline-block;
                text-align: left;
            }

        }
    }

    &[data-border="none"]{
        border:0;
        th,td{border: 0;}
    }

    &[data-zebra]{
        thead th,tbody tr:nth-child(even){background-color: rgba(5, 34, 95 ,0.7)}
        tbody tr:nth-child(odd){background-color: rgba(11, 41, 107 ,0.9)}
    }
}


