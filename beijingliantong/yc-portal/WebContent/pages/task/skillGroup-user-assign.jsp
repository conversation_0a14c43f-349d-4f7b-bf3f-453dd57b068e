<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>分配到坐席</title>
	<style>
		label{font-weight: normal;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="groupUserAssignForm">
             	<div class="ibox">
	              	<div class="ibox-content">
		           	    <div>
		              		<p class="stat" style="margin-top: -15px;"></p>
		           	    	<p>
		           	    		每个坐席分配数 <input id="assigAgent" onchange="assigAgentFn()" type="number" style="width: 80px;" value="0" onkeyup="this.value=this.value.replace(/\D/g,'')" class="form-control input-sm ml-10"/>
		           	    		<label class="checkbox checkbox-success ml-20 hidden"><input type="checkbox" name="runDate" id="dayMaxCountCheck" value="1"><span>每天最大执行数</span></label><input name="dayMaxCount" id="dayMaxCount" value="0" type="number" onkeyup="this.value=this.value.replace(/\D/g,'')" style="width: 70px;display: none;" class="form-control input-sm ml-10"/>
		           	    	</p>
		           	    </div>
		           	    <hr style="margin: 15px 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-container="#groupUserAssignTbody" data-template="groupUserAssign-template" data-mars="task.taskGroupUser">
                             <thead>
	                         	 <tr>
								      <th class="text-c" style="width: 100px"><label class="checkbox checkbox-success"><input type="checkbox" id="all_1" checked="checked"><span></span></label></th>
								      <th class="text-c">坐席工号</th>
								      <th class="text-c">坐席名</th>
								      <th class="text-c">已分配数</th>
								      <th class="text-c">分配数 </th>
		   						 </tr>
                             </thead>
                             <tbody id="groupUserAssignTbody">
                             </tbody>
		                 </table>
                        	 <script id="groupUserAssign-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
									   	    <td><label class="checkbox checkbox-success"><input type="checkbox" checked="checked" name="userIds" value="{{:USER_ID}}"/><span></span></label></td>
											<td><input type="hidden" value="{{call:USER_ID fn='getTaskAgentId'}}" name="{{:USER_ID}}"/>{{:AGENT_PHONE}}</td>                                         
									   	    <td>{{replace:AGENT_NAME 'null' '--'}}</td>
									   	    <td>{{call:USER_ID fn='getRecordCount'}}</td>
											<td class="text-c"><input type="number" onchange="getAssignSum()" name="userAssinCount.{{:USER_ID}}" onkeyup="this.value=this.value.replace(/\D/g,'')" value="0" class="userIds form-control input-sm" style="width:70px"/></td>
									    </tr>
								   {{/for}}					         
							 </script>
	              	</div> 
                </div>
                <div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="GroupUser.save()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("GroupUser");
		GroupUser.count=${param.assignSum};
		var m1={};
		var m2={};
		var datas={};
		$(function(){
			$(".stat").html("待分配数："+GroupUser.count+"，已分配： 0。");
			$('[data-toggle="tooltip"]').tooltip();
		    datas={taskGroupId:'${param.taskGroupId}',taskId:'${param.taskId }',skillGroupId:'${param.skillGroupId}',assignSum: '${param.assignSum }'};
			ajax.daoCall({params:datas,controls:['task.taskGroupAgent']},function(result){
				var data=result['task.taskGroupAgent'].data;
				if(data.length>0){
					for(var o in data){
						m1[data[o].AGENT_ID]=data[o].OBJ_COUNT;
						m2[data[o].AGENT_ID]=data[o].TASK_AGENT_ID;
					}
				}
				$("#groupUserAssignForm").render({data:datas});
			});
			$("#all_1").click(function(){
				$("input[name='userIds']").prop("checked", $(this).is(':checked'));
				getAssignSum();
			});
			$("#dayMaxCountCheck").click(function(){
				if($(this).is(':checked')){
					$("#dayMaxCount").show();
				}else{
					$("#dayMaxCount").hide();
				}
			});
			//平均数分配
			$("#all_2").click(function(){
				var obj=$("#groupUserAssignForm .userIds");
				if($(this).is(':checked')){
						var length=obj.length;
						var count=0;
						obj.each(function(){
							count= count+ parseInt(GroupUser.count/length);
							$(this).val(parseInt(GroupUser.count/length));
						});
						var firstVal=parseInt(obj.first().val());
						obj.first().val(firstVal+(GroupUser.count-count));
				}else{
					obj.each(function(){
						$(this).val(0);
					});
				}
				getAssignSum();
			});
		});
		
		
		function assigAgentFn(){
			var assigAgent=$("#assigAgent").val();
			$("#groupUserAssignForm .userIds").each(function(){
				$(this).val(assigAgent);
			});
			getAssignSum();
		}
		
		function getRecordCount(val){
			if(m1[val]==null){
				return "0";
			}else{
				return m1[val];
			}
		}
		function getTaskAgentId(val){
			var agentTaskId = m2[val];
			if(agentTaskId && agentTaskId != undefined){
				return m2[val];
			}
			return '';
		}
		function getAssignSum(){
			var sum=0;
			$("#groupUserAssignForm .userIds").each(function(){
				var val=$(this).val();
				if(val!=''&&val>0){
					sum=sum+parseInt(val);
				}
			});
			$(".stat").html("待分配数："+GroupUser.count+"，已分配："+sum+"。");
		}
		GroupUser.save = function(){
				var count=GroupUser.count;//可分配数
				var sum=0;
				$("#groupUserAssignForm .userIds").each(function(){
					sum=sum+parseInt($(this).val());
				});
				if(sum <= 0){
					layer.msg("无分配数!");
					return;
				}
				if($("#dayMaxCountCheck:checked").val()){
					if($("#dayMaxCount").val()<1){
						layer.msg("每天最大执行数必须大于0！");
						return;
					}
				}
				if(sum>count){
					//layer.msg("分配数不能大于："+count);
					//return;
				}
				ajax.remoteCall("${ctxPath}/servlet/task/customer?action=saveGroupUserAssign",$.extend(datas,form.getJSONObject("#groupUserAssignForm")),function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
							Task.loadData();
							layer.closeAll();
					    });
					}else{
						layer.alert(result.msg,{icon: 7});
					}
				});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>