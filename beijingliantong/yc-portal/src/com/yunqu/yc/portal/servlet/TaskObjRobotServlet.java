package com.yunqu.yc.portal.servlet;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.base.AppBaseServlet;
import com.yunqu.yc.portal.base.Constants;
import com.yunqu.yc.portal.base.TaskConstants;
import com.yunqu.yc.portal.utils.PhoneCryptor;
import com.yunqu.yc.portal.utils.PostUtils;
import com.yunqu.yc.portal.utils.paramUtils;

@SuppressWarnings("serial")
@MultipartConfig
@WebServlet("/servlet/taskObjRobot")
public class TaskObjRobotServlet extends AppBaseServlet{
	public void actionForImport(){
		EasyResult result=new EasyResult();
		long begin=System.currentTimeMillis();
		long end=0;
		EasyQuery query=this.getQuery();
		try {
			JSONObject jsonObject=getJSONObject();
			String taskId=jsonObject.getString("taskId");
			String robotTaskId=jsonObject.getString("robotTaskId");
			JSONObject taskObj = query.queryForRow("SELECT TASK_TYPE,TASK_STATE FROM "+getTableName("CC_TASK")+" WHERE TASK_ID=?", new Object[]{taskId}, new JSONMapperImpl());
			Integer taskType=taskObj.getInteger("TASK_TYPE");
			String batchName=jsonObject.getString("batchName");
			Integer state=taskObj.getInteger("TASK_STATE");
			String batchId=RandomKit.uniqueStr();
			String entId=getEntId();
			String busiOrderId=getBusiOrderId();
			String monthId=EasyDate.getCurrentDateString("yyyyMM");
			boolean bl=PhoneCryptor.getInstance().iscrypt(entId);
			int sum=0;
			JSONObject groupObj=null;
			String skillGroupId=null;
			String taskGroupId=null;
			
			
			if(taskType==TaskConstants.TASK_TYPE_AUTO){
				String sql="select SKILL_GROUP_ID,TASK_GROUP_ID from "+getTableName("cc_task_group")+" where task_id = ?";
				groupObj=query.queryForRow(sql,new Object[]{taskId},new JSONMapperImpl());
				if(jsonObject.isEmpty()){
					return;
				}
				skillGroupId=groupObj.getString("SKILL_GROUP_ID");
				taskGroupId=groupObj.getString("TASK_GROUP_ID");
			}
			EasyQuery tempQuery=this.getQuery();
			tempQuery.begin();
			EasySQL robotObjSql = new EasySQL();
			robotObjSql.append("SELECT T1.* FROM "+getTableName("CC_TASK_OBJ")+" T1 LEFT JOIN "+getTableName("CC_TASK_OBJ_ROBOT")+" T2 ON T1.OBJ_ID=T2.OBJ_ID WHERE 1=1");
			robotObjSql.append(robotTaskId,"AND T1.TASK_ID=?");
			JSONArray purpose = jsonObject.getJSONArray("purpose");
			if(purpose!=null) {
				robotObjSql.append(" and ( t2.STD_CLASS in(");
				for(int i=0;i<purpose.size();i++) {
					if(i!=0) {
						robotObjSql.append(",");
					}
					robotObjSql.append(purpose.get(i),"?");
				}
				robotObjSql.append(")");
				robotObjSql.append(jsonObject.getString("purpose-null")," or t2.STD_CLASS is null or t2.STD_CLASS = ''");
				robotObjSql.append(")");
			}
			List<JSONObject> robotObjList = query.queryForList(robotObjSql.getSQL(), robotObjSql.getParams(), new JSONMapperImpl());
			if(robotObjList!=null&&robotObjList.size()>0){
				for(int i=0;i<robotObjList.size();i++){
					try {
						JSONObject object=robotObjList.get(i);
						EasyRecord record=new EasyRecord(getTableName("CC_TASK_OBJ"),"OBJ_ID");
						record.set("ROBOT_OBJ_ID", object.getString("OBJ_ID"));
						if(bl){
							String[] fields={"TEL_NUM1","TEL_NUM2","TEL_NUM3","TEL_NUM4"};
							for(String key:fields){
								String value = object.getString(key);    
								if(StringUtils.notBlank(value)){
									object.put(key, PhoneCryptor.getInstance().encrypt(value));
								}
							}
						}
						delEmptyKey(object);
						record.setColumns(object);
						record.set("MONTH_ID",monthId);
						if(!StringUtils.isBlank(record.getString("TEL_NUM1"))){
							record.set("ENT_ID", entId);
							record.setPrimaryValues(RandomKit.randomStr());
							record.set("BUSI_ORDER_ID",busiOrderId );
							record.set("TASK_ID", taskId);
							record.set("TASK_STATE", Constants.TASK_OBJ_STATE_NO_EXECUTE);
							if(taskType==TaskConstants.TASK_TYPE_AUTO){
								record.set("GROUP_ID", skillGroupId);
								record.set("TASK_GROUP_ID",taskGroupId);
								record.set("ALLOC_GROUP_TIME",EasyDate.getCurrentDateString());
							}else{
								record.set("GROUP_ID","0");
								record.set("TASK_GROUP_ID", "0");
							}
							if(taskType == TaskConstants.TASK_TYPE_IVR){
								record.set("IVR_SALE_RESULT", 0);//IVR确认标志，0 未确认
							}
							record.set("AGENT_ID", "0");
							record.set("TASK_AGENT_ID", "0");
							record.set("BATCH_ID", batchId);
							if(tempQuery.save(record)){
								sum++;
							}
							end=System.currentTimeMillis();
						}
					} catch (SQLException e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			tempQuery.commit();
			//重新计算下导入总数
			sum=this.getQuery().queryForInt("select count(1) from "+getTableName("cc_task_obj")+" where task_id = ? and batch_id = ?", new Object[]{taskId,batchId});
			
			if(sum>0){
				EasyRecord batchRecord=new EasyRecord(getTableName("CC_TASK_BATCH"),"BATCH_ID");
				batchRecord.setPrimaryValues(batchId);
				batchRecord.set("TASK_ID",taskId);
				batchRecord.set("BATCH_NAME",batchName);
				batchRecord.set("OBJ_COUNT",sum);
				batchRecord.set("CALL_SUCCESS_COUNT",0).set("SALE_SUCCESS_COUNT",0).set("SALE_FAIL_COUNT", 0);
				batchRecord.set("CREATE_TIME",EasyDate.getCurrentDateString());
				batchRecord.set("CREATOR",getUserId());
				query.save(batchRecord);
				
				
				if(taskType.equals(TaskConstants.TASK_TYPE_AUTO) || taskType.equals(TaskConstants.TASK_TYPE_IVR)){
					if(state < TaskConstants.TASK_STATE_005){
						state=TaskConstants.TASK_STATE_004;
					}
					query.executeUpdate("update "+getTableName("cc_task_group")+" set OBJ_COUNT=OBJ_COUNT+?,ASIGN_COUNT=ASIGN_COUNT+? where TASK_GROUP_ID= ? ",new Object[]{sum,sum,taskGroupId});
					this.addTaskLog("10",taskId, setOperDesc("任务技能组分配",1000,1));
				}else{
					if(state < TaskConstants.TASK_STATE_002){
						state=TaskConstants.TASK_STATE_002;
					}
				}
				String sql="update "+getTableName("CC_TASK")+" set ASIGN_COUNT = ASIGN_COUNT + ? ,OBJ_COUNT = OBJ_COUNT + ?,TASK_STATE = ?  where TASK_ID = ?";
				query.execute(sql, new Object[]{sum,sum,state,taskId});
			}else{
				result.addFail("导入失败！");
			}
			end=System.currentTimeMillis();
			long time=(end-begin)/1000;
			if(time==0)time=1;
			this.addTaskLog("9",taskId,setOperDesc("导入数据"+sum+"条,总耗时:"+time+"秒",end-begin,sum));
			String msg="成功导入"+sum+"条数据,耗时:"+time+"秒！";
			this.getLogger().info(msg);
			result.setMsg(msg);
			renderJson(result);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			renderJson(EasyResult.fail("导入失败:"+e.getMessage()));
		}
	}
	
	/**
	 * 去掉JSONObject中的空值
	 * @param obj
	 */
	private void delEmptyKey(JSONObject obj) {
		Iterator<String> it = obj.keySet().iterator();
		while(it.hasNext()) {
			if(StringUtils.isEmpty(obj.getString(it.next()))) {
				it.remove();
			}
		}
	}
	
	/**
	 * 修改param参数信息
	 * @return
	 */
	public EasyResult actionForUpdateParam() {
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_ROBOT_PARAM"),"TASK_ID");
			JSONObject json = getJSONObject();
			String taskId = json.getString("taskId");
			JSONArray arr = json.getJSONArray("paramList");
			JSONObject data = new JSONObject();
			if(arr!=null&&!arr.isEmpty()) {
				for(int i=0;i<arr.size();i++) {
					String param = arr.getString(i);
					JSONObject paramJson = new JSONObject();
					paramJson.put("text", json.getString(param+".text"));
					String optionList=paramUtils.EncodeOption(json.getString(param+".options"));
					if(StringUtils.isNotBlank(optionList)) {
						JSONArray options = JSONArray.parseArray(optionList);
						paramJson.put("options", options);
					}
					paramJson.put("value", json.getString(param+".value"));
					data.put(param, paramJson.toJSONString());
				}
			}
			record.setColumns(data);
			record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			record.setPrimaryValues(taskId);
			if(!this.getQuery().update(record)){
				this.getQuery().save(record);
			}
			
			setTaskConf(taskId);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 启用机器人参数配置
	 * @param taskId
	 */
	private void setTaskConf(String taskId){
		try {
			JSONObject extConf = null;
			String extConfStr = this.getQuery().queryForString("select EXT_CONF from "+getTableName("cc_task")+" where TASK_ID = ?", new Object[]{taskId});
			if(StringUtils.isBlank(extConfStr)){
				extConf = new JSONObject();
			}else{
				extConf = JSONObject.parseObject(extConfStr);
			}
			extConf.put("confRobotParam", 1);
			this.getQuery().execute("update "+getTableName("CC_TASK")+" set EXT_CONF = ? where TASK_ID = ?", new Object[]{extConf.toJSONString(), taskId});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	/**
	 * 同步机器人参数
	 * @return
	 */
	public EasyResult actionForSynsRobotParam() {
		try {
			JSONObject json = getJSONObject();
			String taskId = json.getString("taskId");
			
			JSONObject row = this.getQuery().queryForRow("SELECT T1.ROBOT_MODEL_ID,T2.* FROM "+getTableName("CC_TASK")+" T1,"+getTableName("CC_TASK_ROBOT_PARAM")+" T2,,CC_ROBOT_MODEL T3 WHERE T1.TASK_ID = T2.TASK_ID AND T1.IVR_FLOW_NAME = t3.ROBOT_ID AND T1.TASK_ID = ?", new Object[]{taskId}, new JSONMapperImpl());
			
			if(row == null || StringUtils.isAnyBlank(row.getString("ROBOT_MODEL_ID"),row.getString("PARAM1"))){
				return EasyResult.ok();
			}
			JSONObject newParamData = getRobotParamData(row.getString("ROBOT_MODEL_ID"));
			if(newParamData == null){
				return EasyResult.ok();
			}
			JSONObject data = new JSONObject();
			for(String key : row.keySet()){
				if(!key.startsWith("PARAM") || StringUtils.isBlank(row.getString(key))){
					continue;
				}
				data.put(key, newParamData.getString(JSONObject.parseObject(row.getString(key)).getString("value")));
			}
			
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_ROBOT_PARAM"),"TASK_ID");
			record.setColumns(data);
			record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			record.setPrimaryValues(taskId);
			this.getQuery().update(record);
			
			setTaskConf(taskId);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage());
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 请求接口获取数据
	 * @param robotId
	 * @return
	 */
	private JSONObject getRobotParamData(String robotId){
		JSONObject param = new JSONObject();
		param.put("robotId", robotId);
		String urlStr = Constants.getSysnRobotParamPath();
		if(StringUtils.isBlank(urlStr)){//没有配置地址，不同步
			return null;
		}
		String res = PostUtils.formSend(urlStr, param);
		if(StringUtils.isBlank(res)){
			return null;
		}
		JSONObject result = JSONObject.parseObject(res);
		if(!"0".equals(result.getString("code"))){
			return null;
		}
		JSONObject json = new JSONObject();
		JSONArray data = result.getJSONArray("data");
		for(int i=0; i<data.size(); i++){
			JSONObject row = new JSONObject();
			JSONArray array = new JSONArray();
			JSONObject obj = data.getJSONObject(i);
			String fieldName = obj.getString("fieldName");
			String key = obj.getString("key");
			JSONArray dictList = obj.getJSONArray("dictList");
			if(dictList!=null){
				for(int j=0;j<dictList.size(); j++){
					JSONObject option = new JSONObject();
					option.put(dictList.getJSONObject(j).getString("valueKey"), dictList.getJSONObject(j).getString("valueDesc"));
					array.add(option);
				}
			}
			row.put("text", fieldName);
			row.put("value", key);
			row.put("options", array);
			json.put(key, row.toJSONString());
		}
		return json;
	}
}
