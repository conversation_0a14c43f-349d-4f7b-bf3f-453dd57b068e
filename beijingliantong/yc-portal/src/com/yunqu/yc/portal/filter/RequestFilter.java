package com.yunqu.yc.portal.filter;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.base.Constants;
import com.yunqu.yc.portal.model.PortalTokenManager;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.ContentType;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashSet;
import java.util.Set;

public class RequestFilter implements Filter {
private static Logger logger = LogEngine.getLogger("PortalTokenLogger");
	@Override
	public void destroy() {
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		// TODO Auto-generated method stub

	}
	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest)servletRequest;
		HttpServletResponse response = (HttpServletResponse)servletResponse;
		YCUserPrincipal principal  = (YCUserPrincipal)request.getUserPrincipal();
		long thid = Thread.currentThread().getId();
		String userId = principal.getUserId();
		String requestURI = request.getRequestURI();
		request.setAttribute("showConfIvrOrderBtn", Constants.getContext().getProperty("showConfIvrOrderBtn","0"));
		//jsp页面请求设置token
		if(requestURI.endsWith(".jsp")||requestURI.contains("/workbench")){
			PortalTokenManager.createToken(request,userId);
			chain.doFilter(servletRequest, servletResponse);
			return;
		}

		if(requestURI.endsWith("/yc-portal/")){
			chain.doFilter(servletRequest, servletResponse);
			return;
		}

		if(!PortalTokenManager.validateToken(request,userId)){
			logger.error("Thread["+thid+"] 非法请求！请求路径："+requestURI+" token无效！！！");
			this.renderErrorResult(request,response,"非法请求：token无效！！！");
			return;
		}

		chain.doFilter(servletRequest, servletResponse);
	}

	private void renderErrorResult(HttpServletRequest request, HttpServletResponse response,String errorMsg){
		this.renderJson(request,response,EasyResult.fail(errorMsg).toJSONString());
	}

	private void renderJson(HttpServletRequest request, HttpServletResponse response,String jsonString){
		PrintWriter writer = null;
		try {
			response.setHeader("Pragma", "no-cache");	// HTTP/1.0 caches might not implement Cache-Control and might only implement Pragma: no-cache
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);

			response.setContentType(ContentType.JSON.value());
			response.setCharacterEncoding("UTF-8");	// 与 contentType 分开设置
			writer = response.getWriter();
			writer.write(jsonString);
			writer.flush();
		} catch (Exception ex) {
			logger.error(ex.getMessage(),ex);
		}
	}
}
