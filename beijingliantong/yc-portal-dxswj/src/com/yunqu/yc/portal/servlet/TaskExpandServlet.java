package com.yunqu.yc.portal.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

//import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.portal.base.AppBaseServlet;

/**
 * 任务额外拓展功能
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/task/expand")
public class TaskExpandServlet extends AppBaseServlet{

	private static final long serialVersionUID = 1L;
	private static final String DEL_MANU_RES = "del_task_data_manu";
	private static final String DEL_AUTO_RES = "del_task_data_auto";
	private static final String DEL_IVR_RES = "del_task_data_ivr";
	
	/**
	 * 删除任务历史记录信息，针对开始试用阶段，只有超级管理员才有权限
	 * @return
	 */
	//@PreAuthorize(resId = {DEL_MANU_RES})
	public EasyResult actionForDelTaskDataManu(){
		return delTaskData(DEL_MANU_RES);
	}
	
	/**
	 * 删除自动任务数据
	 * @return
	 */
	//@PreAuthorize(resId = {DEL_AUTO_RES})
	public EasyResult actionForDelTaskDataAuto(){
		return delTaskData(DEL_AUTO_RES);
	}
	
	/**
	 * 删除机器人任务数据
	 * @return
	 */
	//@PreAuthorize(resId = {DEL_IVR_RES})
	public EasyResult actionForDelTaskDataIvr(){
		return delTaskData(DEL_IVR_RES);
	}
	
	/**
	 * 删除数据处理逻辑
	 * @param taskType
	 * @return
	 */
	private EasyResult delTaskData(String resId){
		if(!this.isSuperUser()){
			return EasyResult.fail("不是超管无权操作");
		}
		
		String taskId = this.getJsonPara("taskId");
		if(StringUtils.isBlank(taskId)){
			return EasyResult.fail("非法操作");
		}
		try {
			if(!this.getQuery().queryForExist("SELECT count(1) from CC_ENT_BUSI_RES where RES_ID = ?", new Object[]{})){
				return EasyResult.fail("无权操作，请联系管理员");
			}
			
			Object[] param = new Object[]{taskId};
			this.delTaskObj(param);
			
			if(DEL_IVR_RES.equals(resId)){
				this.delTaskRobot(param);
			}
			this.addTaskLog("7", taskId, "清空此任务历史数据，包括导入批次、任务名单、预约记录、回访结果及任务通话记录");
			this.updateTaskStatInfo(taskId);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.error();
		}
	}
	
	/**
	 * 删除任务数据
	 * @param param
	 * @throws SQLException
	 */
	private void delTaskObj(Object[] param) throws SQLException{
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_HIS")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_ROBOT")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_ROBOT_HIS")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_TIMER")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_WORD")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_REDO_LIST")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_BATCH")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_AGENT")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_CALL_RECORD")+" where TASK_ID = ?", param);
	}
	
	/**
	 * 删除机器人任务数据
	 * @param param
	 * @throws SQLException
	 */
	private void delTaskRobot(Object[] param) throws SQLException{
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_ROBOT")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_ROBOT_HIS")+" where TASK_ID = ?", param);
		this.getQuery().execute("delete from "+getTableName("CC_TASK_OBJ_ROBOT_PARAM")+" where TASK_ID = ?", param);
	}

}
