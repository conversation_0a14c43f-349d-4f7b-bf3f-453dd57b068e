package com.yunqu.yc.portal.servlet;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.context.AppContext;
//import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.base.AppBaseServlet;
import com.yunqu.yc.portal.base.Constants;
import com.yunqu.yc.portal.base.TaskConstants;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

@SuppressWarnings("serial")
@WebServlet("/servlet/taskObj")
public class TaskObjServlet extends AppBaseServlet{


//	@PreAuthorize(resId = {"yc_task_mylist","yc_task_result_query"})
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord(getTableName("CC_TASK_OBJ"),"OBJ_ID");
		try {
			record.setColumns(getJSONObject("obj"));
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	/**
	 * 修改回访结果
	 * @return
	 */
	public EasyResult actionForUpdateSalesResult(){
		JSONObject jsonObject=getJSONObject();
		try {
			String objId=jsonObject.getString("objId");
			String taskId=jsonObject.getString("taskId");
			String custName=jsonObject.getString("custName");
			String telNum=jsonObject.getString("telNum");
			String result=jsonObject.getString("resultIds");
			String resultId=result.substring(0, result.indexOf("_"));
			String saleResultName=result.substring(result.indexOf("_")+1);
			String remark=jsonObject.getString("remark");
			String sql="update "+getTableName("CC_TASK_OBJ")+" set result_id = ? where OBJ_ID = ?";
			EasyRecord objHis=getObjHis();
			objHis.set("OBJ_MEMO", "[修改"+Constants.getPortalName()+"结果]"+saleResultName+","+remark);
			remark = StringUtils.trim(remark);
			sql="update "+getTableName("CC_TASK_OBJ")+" set RESULT_ID = ?,OBJ_MEMO = ? where OBJ_ID = ?";
			this.getQuery().executeUpdate(sql, new Object[]{resultId,remark,objId});
			objHis.set("TEL_NUM", telNum);
			objHis.set("CUST_NAME", custName);
			objHis.set("TASK_ID", taskId);
			objHis.set("OBJ_ID", objId);
			this.getQuery().save(objHis);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 营销完成
	 * @return
	 */
	public EasyResult actionForFinishTask() throws SQLException {
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		JSONObject jsonObject = getJSONObject();
		
		String objId=jsonObject.getString("objId");
		int taskTyp = jsonObject.getIntValue("taskTyp");
		String allowNoCallSubmit=appContext.getProperty("allowNoCallSubmit", "1");
		
		if("0".equals(allowNoCallSubmit)){
			String sql="select count(1) from " + getTableName("CC_CALL_RECORD") + " where OBJ_ID = ?";
			if(!query.queryForExist(sql, new Object[]{objId})){
				return EasyResult.fail("没呼叫客户号码不能保存！");
			}
		}
		
		YCUserPrincipal userPrincipal=getUserPrincipal();
		Map<String,String> taskObj=getQuery().queryForRow("select CUST_NAME,TASK_ID,TASK_GROUP_ID,AGENT_ID from "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_OBJ")+" where OBJ_ID = ?", new Object[]{objId},new MapRowMapperImpl());
		
		
		String taskId = taskObj.get("TASK_ID");
		String taskGroupId = taskObj.get("TASK_GROUP_ID");
		String agentId = taskObj.get("AGENT_ID");
		String saleResult=jsonObject.getString("resultId");
		String taskAgentId=jsonObject.getString("taskAgentId");
		String saleResultId=saleResult.split("_")[0];//营销结果Id
		String saleResultFlag=saleResult.split("_")[1];//营销结果(成功或失败标识)
		String saleResultName=saleResult.split("_")[2];//结果名称
		String objMemo=jsonObject.getString("objMemo");
		int custSatisfy=jsonObject.getIntValue("custSatisfy");
		String telNum=jsonObject.getString("telNum");
		String ivrSaleResult=jsonObject.getString("ivrSaleResult");
		try {
			query.begin();
			
			EasySQL taskSql=new EasySQL("update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK")+" set ");
			
			EasySQL taskGroupSql=new EasySQL("update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_GROUP")+" set ");
			
			EasySQL taskAgentSql=new EasySQL(" update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_AGENT")+" set ");
			
			
			EasyRecord obj = new EasyRecord(getTableName(userPrincipal.getSchemaName(),"CC_TASK_OBJ"), "OBJ_ID");
			obj.setPrimaryValues(objId);
			obj.set("SALE_FINISH_TIME", EasyDate.getCurrentDateString());
			obj.set("RUN_DATE", EasyCalendar.newInstance().getDateInt());
			obj.set("TASK_STATE", Constants.TASK_OBJ_STATE_EXECUTE_FINISH);
			//obj.set("AGENT_FLAG","1");//手动外呼无需转坐席，是否转坐席，交给ccbar判断
			obj.set("RESULT_ID", saleResultId);
			obj.set("OBJ_MEMO", objMemo);
			obj.set("CUST_SATISFY", custSatisfy);
			obj.set("AGENT_ID", getUserId());
			
			String orderProdList=jsonObject.getString("orderProdList");
			if(StringUtils.notBlank(orderProdList)){
				obj.set("ORDER_PROD_LIST",orderProdList);
			}
			if(StringUtils.notBlank(ivrSaleResult)){
				obj.set("IVR_SALE_RESULT", this.parseInteger(ivrSaleResult));//未确认
			}
			//判断是否销售成功
			if("1".equals(saleResultFlag)) {
				taskSql.append("SALE_SUCCESS_COUNT = SALE_SUCCESS_COUNT + 1");
				taskGroupSql.append("SALE_SUCCESS_COUNT=SALE_SUCCESS_COUNT+1");
				taskAgentSql.append("SALE_SUCCESS_COUNT=SALE_SUCCESS_COUNT+1");
			}else{
				taskSql.append("SALE_FAIL_COUNT = SALE_FAIL_COUNT + 1");
				taskGroupSql.append("SALE_FAIL_COUNT=SALE_FAIL_COUNT+1");
				taskAgentSql.append("SALE_FAIL_COUNT=SALE_FAIL_COUNT+1");
			}
			//判断是否呼叫成功
			//query.queryForRow("select count(1), from " + getTableName("CC_CALL_RECORD") + " where OBJ_ID = ? and ENT_ID = ? and TASK_ID = ?";
			if(query.queryForExist("select count(1) from " + getTableName("CC_CALL_RECORD") + " where OBJ_ID = ? and ENT_ID = ? and TASK_ID = ?", new Object[]{objId,this.getEntId(),taskId,})){
				obj.set("CALL_FLAG", Constants.CALL_FLAG_SUCCESS);
			}else {
				obj.set("CALL_FLAG", Constants.CALL_FLAG_FAIL);
			}
			//obj.set("CALL_RESULT", "0");//呼叫结果
			
			//判断是否执行的是预约任务
			if("myVisit".equals(jsonObject.getString("from"))){ 
				String timeCallId=jsonObject.getString("timeCallId");
				query.execute("update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_TIMER")+" set HANDLE_STATE = ?,AGENT_ID = ? where TIME_CALL_ID = ?",new Object[]{ Constants.HANDLE_STATE_SUCCESS,getUserId(),timeCallId});
				taskAgentSql.append(",TIMER_COUNT=TIMER_COUNT-1");
				//返单
				obj.set("QC_STATE", TaskConstants.QC_WIAT);
				query.execute("update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_REDO_LIST")+" set STATE = ? , FINISH_TIME = ? where OBJ_ID = ?",new Object[]{ Constants.HANDLE_STATE_SUCCESS,EasyDate.getCurrentDateString(),objId});
				cache.delete("Timer_"+getUserId());
			}
			
			if("myRedo".equals(jsonObject.getString("from"))){
				obj.set("QC_STATE", TaskConstants.QC_WIAT);
				query.execute("update "+getTableName(userPrincipal.getSchemaName(),"CC_TASK_REDO_LIST")+" set STATE = ? , FINISH_TIME = ? where OBJ_ID = ?",new Object[]{ Constants.HANDLE_STATE_SUCCESS,EasyDate.getCurrentDateString(),objId});
			}
			if("myAutoTask".equals(jsonObject.getString("from"))){
				
			}
			
			taskSql.append(",OBJ_USE_COUNT=OBJ_USE_COUNT + 1");//已完成记录数
			taskSql.append(taskId," where TASK_ID = ?");
			//query.execute(taskSql.getSQL(), taskSql.getParams());//更新任务表
			
			
			taskGroupSql.append(",OBJ_USE_COUNT=OBJ_USE_COUNT+1");
			taskGroupSql.append(taskGroupId,"where TASK_GROUP_ID = ?");//更新任务技能组
			//query.execute(taskGroupSql.getSQL(),taskGroupSql.getParams());
			
			
			
			taskAgentSql.append(",OBJ_USE_COUNT = OBJ_USE_COUNT + 1");
			
			if(StringUtils.notBlank(taskAgentId)){
				taskAgentSql.append(taskAgentId," where TASK_AGENT_ID = ?");
			}else{
				taskAgentSql.append(agentId," where AGENT_ID = ?");
				taskAgentSql.append(taskId," and  TASK_ID = ?");
				taskAgentSql.append(taskGroupId," and  TASK_GROUP_ID = ?");
			}
			
			if("myVisit".equals(jsonObject.getString("from"))){
				query.execute(taskAgentSql.getSQL(),taskAgentSql.getParams());//更新任务坐席
			}
			
			
			EasyRecord objHis = getObjHis();
			objHis.set("CUST_NAME", taskObj.get("CUST_NAME"));
			objHis.set("TEL_NUM", telNum);
			if(StringUtils.notBlank(objMemo)){
				objHis.set("OBJ_MEMO", "["+Constants.getPortalName()+"结果]"+saleResultName+","+objMemo);
				if(StringUtils.notBlank(orderProdList)){
					objHis.set("OBJ_MEMO", "["+Constants.getPortalName()+"结果]"+saleResultName+","+objMemo+",[订购产品]"+orderProdList);
				}
			}else{
				objHis.set("OBJ_MEMO", saleResultName);
			}
			objHis.set("TASK_ID", taskId);
			objHis.set("OBJ_ID", objId);
			objHis.set("PHONE_NUM","");//话机号码
			this.changeToInt(objHis);
			query.save(objHis);
			String idInfo = UpdatetaskAgent(taskTyp,objId,taskId,taskGroupId);
			if (taskTyp==TaskConstants.TASK_TYPE_AUTO) {
				obj.set("TASK_AGENT_ID", idInfo);
			}
			query.update(obj);
			query.commit();
			result.setMsg("保存成功！");
			
			
			//判断坐席的任务是否已完成，已完成则刷新任务数据
//			if(checkAgentTaskIsFinish(taskId, null)) {
//				this.updateTaskStatInfo(taskId);
//			}
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(e1.getMessage(), e1);
				result.addFail("保存失败！失败原因：" + e1.getMessage());
			}
			this.error(e.getMessage(), e);
			result.addFail("保存失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	//更新任务坐席分配表信息
	private String UpdatetaskAgent(int taskTyp,String objId,String taskId,String taskGroupId){
		String id="";
		if(taskTyp==TaskConstants.TASK_TYPE_AUTO) {//自动外呼任务时，往任务坐席(cc_task_agent)表中插入具体信息数据
			try {
				id=getQuery().queryForString("SELECT TASK_AGENT_ID FROM "+getTableName("CC_TASK_AGENT")+" WHERE TASK_ID = ? AND ENT_ID = ? AND AGENT_ID = ? ", new Object[] {taskId,getEntId(),getUserId()});
				if(StringUtils.isNotBlank(id)) {
					EasySQL agentSql=new EasySQL("UPDATE "+getTableName("CC_TASK_AGENT")+" SET OBJ_USE_COUNT = OBJ_USE_COUNT + 1 ");
					agentSql.append(",OBJ_COUNT = OBJ_COUNT + 1");
					agentSql.append(getEntId()," WHERE ENT_ID = ?");
					agentSql.append(taskId," AND TASK_ID = ?");
					agentSql.append(getUserId()," AND AGENT_ID = ?");
					getQuery().execute(agentSql.getSQL(), agentSql.getParams());
				}else {
					EasyRecord taskAgent=new EasyRecord(getTableName("CC_TASK_AGENT"),"TASK_AGENT_ID");
					id= RandomKit.uniqueStr();
					taskAgent.setPrimaryValues(id);
					taskAgent.set("ENT_ID",getEntId());
					taskAgent.set("BUSI_ORDER_ID",getBusiOrderId());
					taskAgent.set("TASK_ID",taskId);
					taskAgent.set("AGENT_ID", getUserId());
					taskAgent.set("OBJ_COUNT", 1);
					taskAgent.set("OBJ_USE_COUNT", 1);
					taskAgent.set("TASK_GROUP_ID", taskGroupId);
					taskAgent.set("TIMER_COUNT", 0);//预约数
					taskAgent.set("TASK_STATE", 0);//待执行
					taskAgent.set("CREATOR",getUserPrincipal().getUserId());
					taskAgent.set("CREATE_TIME",EasyDate.getCurrentDateString());
					getQuery().save(taskAgent);
				}
			} catch (SQLException e) {
				getLogger().info(e.getMessage());
			}
		}
		return id;
	}
	private EasyRecord getObjHis(){
		EasyRecord objHis = new EasyRecord(getTableName("CC_TASK_OBJ_HIS"), "OBJ_HIS_ID").setPrimaryValues(RandomKit.randomStr());
		String agentPhone=getUserPrincipal().getAgentPhone();
		if(StringUtils.notBlank(agentPhone)){
			objHis.set("CREATE_USER", getNickName()+"-"+agentPhone);
		}else{
			objHis.set("CREATE_USER", getNickName());
		}
		objHis.set("CREATE_TIME", EasyDate.getCurrentDateString());
		objHis.set("MONTH_ID", Integer.valueOf(EasyCalendar.newInstance().getFullMonth()));
		return objHis;
	}
	
	public EasyResult actionForSynDataStat() {
		String taskId=getJsonPara("taskId");
		if(StringUtils.isBlank(taskId)){
			taskId=getPara("taskId");
		}
		if(StringUtils.notBlank(taskId)){
			updateTaskStatInfo(taskId);
		}
		return EasyResult.ok();
	}
	
	
	/**
	 * 刷新坐席任务列表
	 * 5分钟内不能再次刷新
	 * @return
	 */
//	@PreAuthorize(resId = {"yc_task_mylist"})
	public EasyResult actionForRefreshMyTaskList() {
		
		String agentId = this.getUserPrincipal().getUserId();
		
		String key = "refreshMyTaskList_"+agentId;
		
		Object cacheTimes = cache.get(key);
		long thisTime = System.currentTimeMillis();
		
		if(cacheTimes!=null) {
			long time = (long) cacheTimes;
			long doneTime = thisTime - time;
			//5分钟内不能再次刷新
			long surplusTime = 5*60*1000 - doneTime;
			surplusTime = surplusTime/1000;
			this.getLogger().debug("<actionForRefreshMyTaskList> 刷新任务数据过于频繁，请在"+surplusTime+"秒后重试");
			return EasyResult.fail("刷新数据过于频繁，请在"+surplusTime+"秒后重试");
		}
		
		EasySQL sql = new EasySQL("select t1.TASK_ID from ");
		sql.append(getTableName("cc_task_agent t1 "));
		sql.append("left join "+getTableName("cc_task t2 on t1.TASK_ID = t2.TASK_ID"));
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ? ");
		sql.append(agentId, " and t1.AGENT_ID = ?");
		sql.append(TaskConstants.TASK_STATE_005, " and t2.TASK_STATE = ?");
		
		try {
			
			List<EasyRow> agentTaskList = this.getQuery().queryForList(sql.getSQL(), sql.getParams());
			
			if(agentTaskList!=null&&agentTaskList.size()>0) {
				
				for (int i = 0; i < agentTaskList.size(); i++) {
					
					EasyRow easyRow = agentTaskList.get(i);
					String taskId = easyRow.getColumnValue("TASK_ID");
					Object object = cache.get("taskStatInfo_"+taskId);
					if(object==null) {
						this.updateTaskStatInfo(taskId);
						cache.put("taskStatInfo_"+taskId,System.currentTimeMillis(), 5*60);
					}else {
						this.getLogger().debug("<actionForRefreshMyTaskList> 刷新任务["+taskId+"]数据过于频繁");
					}
					
				}
				
			}
				
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
			return EasyResult.fail("刷新过于频繁，请稍后再试");
		}
		
		cache.put(key, thisTime,5*60);
		
		return EasyResult.ok();
	}
	
	/**
	 * IVR任务页面刷新坐席任务列表
	 * 5分钟内不能再次刷新
	 * @return
	 */
	public EasyResult actionForRefreshAuditTaskList() {
		String agentId = this.getUserPrincipal().getUserId();
		String key = "AuditTaskList_"+agentId;
		Object cacheTimes = cache.get(key);
		long thisTime = System.currentTimeMillis();
		if(cacheTimes!=null) {
			long time = (long) cacheTimes;
			long doneTime = thisTime - time;
			//5分钟内不能再次刷新
			long surplusTime = 5*60*1000 - doneTime;
			surplusTime = surplusTime/1000;
			this.getLogger().debug("<actionForRefreshMyTaskList> 刷新任务数据过于频繁，请在"+surplusTime+"秒后重试");
			return EasyResult.fail("刷新数据过于频繁，请在"+surplusTime+"秒后重试");
		}
		EasySQL sql = new EasySQL("select t1.TASK_ID from ");
		sql.append(getTableName("cc_task t1 "));
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ? ");
		sql.append(TaskConstants.TASK_STATE_005, " and t1.TASK_STATE = ?");
		try {
			List<EasyRow> agentTaskList = this.getQuery().queryForList(sql.getSQL(), sql.getParams());
			if(agentTaskList!=null&&agentTaskList.size()>0) {
				for (int i = 0; i < agentTaskList.size(); i++) {
					EasyRow easyRow = agentTaskList.get(i);
					String taskId = easyRow.getColumnValue("TASK_ID");
					Object object = cache.get("taskStatInfo_"+taskId);
					if(object==null) {
						this.updateTaskStatInfo(taskId);
						cache.put("taskStatInfo_"+taskId,System.currentTimeMillis(), 5*60);
					}else {
						this.getLogger().debug("<actionForRefreshMyTaskList> 刷新任务["+taskId+"]数据过于频繁");
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
			return EasyResult.fail("刷新过于频繁，请稍后再试");
		}
		cache.put(key, thisTime,5*60);
		return EasyResult.ok();
	}
	
	/**
	 * get next taskobj
	 * @return
	 */
//	@PreAuthorize(resId = {"yc_task_mylist"})
	public EasyResult actionForNextTask() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = this.getJSONObject();
		String objId = jsonObject.getString("objId");
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_OBJ"), "OBJ_ID").setPrimaryValues(objId);
			record.set("TASK_ORDER", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			result.setSuccess(true, "更新成功！");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("更新失败！失败原因：" + e.getMessage());
		}
		return result;
	}
//	@PreAuthorize(resId = {"yc_task_mylist"})
	public EasyResult actionForUpdateIvrResult() {
		EasyResult result = new EasyResult();
		JSONObject jsonObject = this.getJSONObject();
		String objId = jsonObject.getString("objId");
		String ivrSaleResult=jsonObject.getString("ivrSaleResult");
		String selectKey=jsonObject.getString("selectKey");
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_OBJ"), "OBJ_ID").setPrimaryValues(objId);
			String msg="确认订购成功!";
			
			if(StringUtils.notBlank(ivrSaleResult)){
				if("1".equals(ivrSaleResult)){//确认订购
					ivrSaleResult="1";
				}else if("2".equals(ivrSaleResult)){//取消订购
					ivrSaleResult="2";
					msg="用户取消订购！";
				}else if("0".equals(ivrSaleResult)){//未确认
					ivrSaleResult="0";
					msg="用户未确认订购！";
				}else if("3".equals(ivrSaleResult)){//用户按键错误
					ivrSaleResult="3";
					msg="用户按键错误！";
				}else if("4".equals(ivrSaleResult)){//已人工确认
					ivrSaleResult="4";
					msg="IVR已人工确认！";
				}else{
					ivrSaleResult="0";//未确认
					msg="用户未确认订购！";
				}
			}
			record.set("IVR_SALE_RESULT", Integer.valueOf(ivrSaleResult));
			if(StringUtils.isNotBlank(selectKey)){
				record.set("IVR_SALE_SELECT_KEY", selectKey);//ALTER TABLE cc_task_obj ADD IVR_SALE_SELECT_KEY VARCHAR(20) COMMENT '发起IVR订购时，保存用户输入的按键值';
			}
			this.getQuery().update(record);
			result.setSuccess(true, msg);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("执行失败：" + e.getMessage());
		}
		return result;
	}

//	@PreAuthorize(resId = {"yc_task_mylist"})
	public EasyResult actionForDelAppointTask(){
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			String pk=getJsonPara("pk");
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_TIMER"),"TIME_CALL_ID");
			record.setPrimaryValues(pk);
			
			String objId=getJsonPara("objId");
			
			EasyRecord taskObj=new EasyRecord(getTableName("CC_TASK_OBJ"),"OBJ_ID").setPrimaryValues(objId);
			taskObj.set("TASK_STATE", Constants.TASK_OBJ_STATE_NO_EXECUTE);
			query.update(taskObj);
			
			Map<String,String> taskObjMap=this.getQuery().findById(taskObj);
			String taskAgentId=taskObjMap.get("TASK_AGENT_ID");
			if(StringUtils.notBlank(taskAgentId)&&!"0".equals(taskAgentId)){
				query.execute("update " + getTableName("CC_TASK_AGENT") + " set TIMER_COUNT = TIMER_COUNT - 1 where TASK_AGENT_ID = ?", new Object[]{taskAgentId});
			}
			query.deleteById(record);
			
			query.commit();
		} catch (SQLException e1) {
			try {
				query.roolback();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			e1.printStackTrace();
			return EasyResult.fail("撤销失败!");
		}
		return EasyResult.ok("取消成功!");
	}
	
	/**
	 * 预约任务
	 * @return
	 */
	public EasyResult actionForAddAppointTask() {
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			JSONObject jsonObject=getJSONObject();
			EasyRecord record = new EasyRecord(getTableName("CC_TASK_TIMER"),"TIME_CALL_ID");
			record.setColumns(JsonKit.getJSONObject(jsonObject, "timer"));
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			record.set("TASK_ID", jsonObject.getString("taskId"));
			record.set("AGENT_ID", this.getUserPrincipal().getUserId());
			record.set("HANDLE_STATE", Constants.HANDLE_STATE_WAIT);
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			
			String timeCallId=record.getString("TIME_CALL_ID");
			String objId=record.getString("OBJ_ID");
			
			EasyRecord taskObjRecord=new EasyRecord(getTableName("CC_TASK_OBJ"),"OBJ_ID").setPrimaryValues(objId);
			taskObjRecord.set("TASK_STATE",Constants.TASK_OBJ_STATE_EXECUTE_VISIT);
			query.update(taskObjRecord);
			
			
			if(StringUtils.notBlank(timeCallId)){
				query.update(record);
			}else{
				record.setPrimaryValues(RandomKit.uniqueStr());
				query.save(record);
				String taskAgentId=jsonObject.getString("taskAgentId");
				if(StringUtils.notBlank(taskAgentId)&&!"0".equals(taskAgentId)){
					query.execute("update " + getTableName("CC_TASK_AGENT") + " set TIMER_COUNT = TIMER_COUNT + 1 where TASK_AGENT_ID = ?", new Object[]{taskAgentId});
				}
			}
		
			//添加-接触历史
			EasyRecord objHis =getObjHis();
			objHis.set("CUST_NAME", jsonObject.getString("CUST_NAME"));
			objHis.set("TASK_ID", jsonObject.getString("taskId"));
			objHis.set("OBJ_ID", record.getString("OBJ_ID"));
			objHis.set("TEL_NUM", record.getString("PHONE_NUM"));
			objHis.set("OBJ_MEMO", "[预约]"+record.getString("TIME_MEMO"));
			objHis.set("PHONE_NUM","");//话机号码
			objHis.set("SHOW_CALLER","");//外呼号码
			query.save(objHis);
			
			query.commit();
			result.setMsg("预约成功！");
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(e.getMessage(), e1);
				result.addFail("更新失败！失败原因：" + e1.getMessage());
			}
			this.error(e.getMessage(), e);
			result.addFail("更新失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 根据任务ID，任务对象ID，客户号码，查询话单表是否存在该记录
	 * @return
	 */
	public EasyResult actionForHasCall(){
		EasyResult result = new EasyResult();
		JSONObject jsonObject = this.getJSONObject();
		try {
			String serialId = this.getQuery().queryForString("select SERIAL_ID from " + getTableName("CC_CALL_RECORD") + " where TASK_ID = ? and OBJ_ID = ? and CALLED = ?",new Object[]{jsonObject.getString("taskId"), jsonObject.getString("objId"), jsonObject.getString("called")});
			if(StringUtils.isNotBlank(serialId)){
				result.setSuccess(serialId, "存在话单！");
			}else {
				result.addFail("请先拨打客户号码！");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("话单查询失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	
	
	/**
	 * 二次回滚
	 * @return
	 */
	public EasyResult actionForTwoMarketing(){
		long beginTime=System.currentTimeMillis();
		EasyResult result = new EasyResult();
		JSONObject object = this.getJSONObject();
		String taskId=object.getString("taskId");
		String endDate = object.getString("endDate");
		JSONArray resultIdArray = object.getJSONArray("resultId");
		int taskType=object.getIntValue("taskType");
		JSONArray callResultArray = object.getJSONArray("callResult");
		EasyQuery query=this.getQuery();
		try {

			EasySQL sql=new EasySQL(" update "+getTableName("CC_TASK_OBJ")+" set ");
			sql.append("SEC_SALE_TIMES = SEC_SALE_TIMES +1,RUN_DATE = 0, ");
			sql.append(TaskConstants.QC_WIAT, "QC_STATE = ?, ");
			sql.append(Constants.TASK_OBJ_STATE_NO_EXECUTE, "TASK_STATE = ?");
			sql.append(" where 1=1");
			sql.append(taskId, " and TASK_ID = ?",false);
			sql.append(Constants.TASK_OBJ_STATE_EXECUTE_FINISH," and TASK_STATE = ?");
			sql.append(object.getString("startDate"), " and SALE_FINISH_TIME >= ?");
			sql.append(object.getString("agentId"), " and AGENT_ID = ?");
			sql.append(object.getString("batchId")," and BATCH_ID = ?");
			sql.append(object.getInteger("custSatisfy"), " and CUST_SATISFY = ?");
			sql.append(object.getInteger("qcState"), " and QC_STATE = ?");

			EasySQL sql2=new EasySQL(" update "+getTableName("CC_TASK_OBJ")+" set ");
			sql2.append(0, "PUSH_BPS = ?");
			sql2.append(" where 1=1");
			sql2.append(taskId, " and TASK_ID = ?",false);
			sql2.append(Constants.TASK_OBJ_STATE_EXECUTE_FINISH," and TASK_STATE = ?");
			sql2.append(object.getString("startDate"), " and SALE_FINISH_TIME >= ?");
			sql2.append(object.getString("agentId"), " and AGENT_ID = ?");
			sql2.append(object.getString("batchId")," and BATCH_ID = ?");
			sql2.append(object.getInteger("custSatisfy"), " and CUST_SATISFY = ?");
			sql2.append(object.getInteger("qcState"), " and QC_STATE = ?");

			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " and SALE_FINISH_TIME <= ?");
				sql2.append(endDate+" 23:59:59", " and SALE_FINISH_TIME <= ?");
			}
			YCUserPrincipal principal=getUserPrincipal();
			if(principal.getRoleType()==Constants.ROLE_TYPE_MONITOR){
				sql.append(getSkillGroupId(),"and task_id in (SELECT TASK_ID from "+getTableName("cc_task_group")+" where SKILL_GROUP_ID = ?)");
				sql2.append(getSkillGroupId(),"and task_id in (SELECT TASK_ID from "+getTableName("cc_task_group")+" where SKILL_GROUP_ID = ?)");
			}
			if(principal.getRoleType()==Constants.ROLE_TYPE_AGENT){
				sql.append(principal.getUserId(), " and AGENT_ID = ?");
				sql2.append(principal.getUserId(), " and AGENT_ID = ?");
			}

			//人工任务
			StringBuffer resultStr=new StringBuffer("(");
			if(resultIdArray != null && resultIdArray.size() >0){
				String sqlIn=StringUtils.joinSql(JSONObject.toJavaObject(resultIdArray, String[].class));
				sql.append(" and RESULT_ID "+sqlIn);
				sql2.append(" and RESULT_ID "+sqlIn);
				for(Object resultId:resultIdArray){
					resultStr.append(object.get(resultId)+",");
				}
			}
			resultStr=new StringBuffer(resultStr.substring(0, resultStr.length()-1));
			resultStr.append(")");
			
			//自动任务
			if(taskType==2){
				sql.append("and RESULT_ID is null");
				sql2.append("and RESULT_ID is null");
				resultStr=new StringBuffer("");
			}
			if(callResultArray!=null && callResultArray.size()>0){
				String sqlIn=StringUtils.joinSql(JSONObject.toJavaObject(callResultArray, int[].class));
				sql.append(" and CALL_RESULT "+sqlIn);
				sql2.append(" and CALL_RESULT "+sqlIn);
			}

//			this.getLogger().info("actionForTwoMarketing--->sql:"+sql.getSQL()+",param:"+JSONObject.toJSONString(sql.getParams()));
			//执行
			int executeUpdate=query.executeUpdate(sql.getSQL(), sql.getParams());
			if(executeUpdate>0){

				try{
					query.executeUpdate(sql2.getSQL(), sql2.getParams());
				}catch (Exception e){
					this.error(e.getMessage(), e);
				}
				this.updateTaskStatInfo(taskId);
				long endTime=System.currentTimeMillis();
				this.addTaskLog("12", taskId, setOperDesc("成功回滚"+resultStr.toString()+" " + executeUpdate + " 条数据！", endTime-beginTime, executeUpdate));
				result.setMsg("成功回滚 " + executeUpdate + " 条数据！");
			}else{
				result.addFail("没有符合要回滚条件的数据!");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("回滚失败！失败原因："+ e.getMessage());
		}
		return result;
	}
	
	public EasyResult actionForSendSms(){
		String entId=getEntId();
		JSONObject jsonObject=getJSONObject();
		IService service;
		JSONObject  jsonIn = null;
		try {
			service = ServiceContext.getService("YC-SMS-SEND-SERVICE");
			jsonIn= new JSONObject();
			
			jsonIn.put("smsId", jsonObject.getString("objId"));
			jsonIn.put("entId", entId);
			jsonIn.put("account", getUserAccount());
			jsonIn.put("username", getNickName());
			jsonIn.put("mobile", jsonObject.getString("telNum"));
			jsonIn.put("content", jsonObject.getString("smsContent"));
			JSONObject  result = service.invoke(jsonIn);
			this.info("syncEntInfo success >> entId:"+entId+",result:"+result.toJSONString(),null);
			
			EasyRecord objHis=getObjHis();
			objHis.set("OBJ_ID", jsonObject.getString("objId"));
			objHis.set("TEL_NUM", jsonObject.getString("telNum"));
			objHis.set("OBJ_MEMO", "[短信]"+jsonObject.getString("smsContent"));
		    this.getQuery().save(objHis);
		} catch (ServiceException ex) {
			this.error("syncEntInfo error >> entId:"+entId+",cause:"+ex.getMessage(), ex);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok(jsonIn,"发送成功！");
	}
	
	/**
	 * 检查坐席任务是否已完成
	 * @param taskId
	 * @param agentId
	 * @return
	 * @throws SQLException
	 */
	public boolean checkAgentTaskIsFinish(String taskId,String agentId){
		EasySQL sql = new EasySQL("select count(1) from ");
		sql.append(getTableName("CC_TASK_OBJ t1 "));
		sql.append(" where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID = ? ");
		sql.append(taskId," and t1.TASK_ID = ?");
		sql.append(Constants.TASK_OBJ_STATE_EXECUTE_FINISH," and t1.TASK_STATE <> ?");
		if(StringUtils.notBlank(agentId)){
			sql.append(agentId, " and t1.AGENT_ID = ?");
		}else{
			sql.append(this.getUserPrincipal().getUserId(), " and t1.AGENT_ID = ?");
		}
		
		
		int agentTaskCount;
		try {
			agentTaskCount = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			if(agentTaskCount==0) {
				return true;
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
		return false;
	}
	/**
	 * 获取任务配额配置
	 */
	public EasyResult actionForTaskPaperConf() {
		String taskId = getJSONObject().getString("taskId");
		String objId = this.getJsonPara("objId");
		try {
			if(StringUtils.isBlank(taskId) && StringUtils.isNotBlank(objId)){
				taskId = this.getQuery().queryForString("select TASK_ID from "+getTableName("CC_TASK_OBJ")+" where OBJ_ID = ?", new Object[]{objId});
			}
			if(StringUtils.isBlank(taskId)){
				return EasyResult.fail();
			}
			if("1".equals(Constants.getProperty("wjUse", ""))){//使用问卷
				String qtId = this.getQuery().queryForString("select QT_ID from "+getTableName("CC_TASK")+" where TASK_ID = ?", new Object[]{taskId});
				if(StringUtils.isNotBlank(qtId)){//绑定问卷
					JSONObject json = new JSONObject();
					json.put("wjExcType", Constants.getProperty("wjExcType", "0"));
					String showType = this.getQuery().queryForString("select SHOW_TYPE from "+getTableName("QT_QUESTIONNAIRE")+" where QT_ID = ?", new Object[]{qtId});
					json.put("showType", showType);
					String quotaType = this.getQuery().queryForString("SELECT QUOTA_TYPE FROM "+getTableName("QT_TASK_PAPER")+" WHERE TASK_ID = ?", new Object[] {taskId});
					json.put("quotaType", quotaType);
					List<JSONObject> result= this.getQuery().queryForList("SELECT SUCC_COUNT,QUOTA_COUNT FROM "+getTableName("QT_TASK_PAPER_CONF")+" WHERE 1=1 AND TASK_ID = ?",new Object[] {taskId},new JSONMapperImpl());
					json.put("result", result);
					this.info(json , null);
					return EasyResult.ok(json);
				}
			}
			return EasyResult.fail();
		} catch (Exception e) {
			return EasyResult.fail();
		}
	}
	
	/**
	 * 获取任务转工单路径
	 * @return
	 */
	public EasyResult actionForGetOrderUrl(){
		String url = "";
		String busiId = this.getUserPrincipal().getBusiId();
		if(Constants.BUSI_ID.equals(busiId)){
			url = "/yc-portal/pages/order/order-new.jsp";
		}else if("003".equals(busiId)){
			if("v2".equals(AppContext.getContext("yc-agent").getProperty("orderVersion", "v1"))){
				url = "/yc-agent/pages/portal/voice_portal_v2.jsp";
			}else{
				url = "/yc-agent/pages/portal/voice_portal.jsp";
			}
		}else if("007".equals(busiId)){
			url = "/cc-eorder/pages/eorder/handle/send-order.jsp";
		}
		return EasyResult.ok(url); 
	}
}
