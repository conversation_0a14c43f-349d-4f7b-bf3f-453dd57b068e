package com.yunqu.yc.portal.model;

import java.text.DecimalFormat;
import java.util.Map;

import org.easitline.common.utils.excel.annotation.ExcelField;

public class TaskRobotlStat {
	@ExcelField(title = "任务名称/批次", order = 1)
	private String statName;
	@ExcelField(title = "总名单量", order = 2)
	private String custCount;
	@ExcelField(title = "外呼名单量", order = 3)
	private String callCount;
	@ExcelField(title = "接通量", order = 4)
	private String connCount;
	@ExcelField(title = "接通率", order = 5)
	private String connRate;
	@ExcelField(title = "转坐席量", order = 6)
	private String agentCount;
	@ExcelField(title = "通话总时长（秒）", order = 7)
	private String totalTime;
	@ExcelField(title = "客户意向A", order = 8)
	private String aCount;
	@ExcelField(title = "客户意向B", order = 9)
	private String bCount;
	@ExcelField(title = "客户意向C", order = 10)
	private String cCount;
	@ExcelField(title = "客户意向D", order = 11)
	private String dCount;
	@ExcelField(title = "客户意向E", order = 12)
	private String eCount;
	@ExcelField(title = "客户意向F", order = 13)
	private String fCount;
	
	public TaskRobotlStat() {
		
	}
	
	public TaskRobotlStat(Map<String, String> row) {
		this.statName = row.get("STAT_NAME");
		this.custCount = row.get("CUST_COUNT");
		this.callCount = row.get("CALL_COUNT");
		this.connCount = row.get("CONN_COUNT");
		this.agentCount = row.get("AGENT_COUNT");
		this.totalTime = row.get("TOTAL_TIME");
		this.aCount = row.get("A_COUNT");
		this.bCount = row.get("B_COUNT");
		this.cCount = row.get("C_COUNT");
		this.dCount = row.get("D_COUNT");
		this.eCount = row.get("E_COUNT");
		this.fCount = row.get("F_COUNT");
	}

	public String getStatName() {
		return statName;
	}

	public void setStatName(String statName) {
		this.statName = statName;
	}

	public String getCustCount() {
		return custCount;
	}

	public void setCustCount(String custCount) {
		this.custCount = custCount;
	}

	public String getCallCount() {
		return callCount;
	}

	public void setCallCount(String callCount) {
		this.callCount = callCount;
	}

	public String getConnCount() {
		return connCount;
	}

	public void setConnCount(String connCount) {
		this.connCount = connCount;
	}

	public String getConnRate() {
		if(this.connCount == null || this.callCount == null){
			return "0%";
		}
		int conn = Integer.parseInt(this.connCount);
		int call = Integer.parseInt(this.callCount);
		if(conn == 0 || call == 0){
			return "0%";
		}
		float rate = conn / (float)call * 100;
		DecimalFormat df = new DecimalFormat("###.00");
		return df.format(rate) + "%";
	}

	public void setConnRate(String connRate) {
		this.connRate = connRate;
	}

	public String getAgentCount() {
		return agentCount;
	}

	public void setAgentCount(String agentCount) {
		this.agentCount = agentCount;
	}

	public String getaCount() {
		return aCount;
	}

	public void setaCount(String aCount) {
		this.aCount = aCount;
	}

	public String getbCount() {
		return bCount;
	}

	public void setbCount(String bCount) {
		this.bCount = bCount;
	}

	public String getcCount() {
		return cCount;
	}

	public void setcCount(String cCount) {
		this.cCount = cCount;
	}

	public String getdCount() {
		return dCount;
	}

	public void setdCount(String dCount) {
		this.dCount = dCount;
	}

	public String geteCount() {
		return eCount;
	}

	public void seteCount(String eCount) {
		this.eCount = eCount;
	}

	public String getfCount() {
		return fCount;
	}

	public void setfCount(String fCount) {
		this.fCount = fCount;
	}

	public String getTotalTime() {
		return totalTime;
	}

	public void setTotalTime(String totalTime) {
		this.totalTime = totalTime;
	}
	
}
