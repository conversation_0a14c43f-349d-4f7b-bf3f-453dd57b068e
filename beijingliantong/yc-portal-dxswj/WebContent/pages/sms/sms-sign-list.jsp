<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业短信签名管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 短信签名管理</h5>
	             		      <div class="input-group input-group-sm pull-right">
							       <button type="button" class="btn btn-sm btn-success" onclick="SmsSign.addData()">+申请签名</button>
							   </div>
			        		   <div class="input-group input-group-sm">
									<span class="input-group-addon">签名状态</span>	
									<select class="form-control input-sm" name="signState" onchange="SmsSign.searchData()" >
										<option value="">请选择</option>
									    <option value="0"> 正常</option>
									    <option value="1" data-class="label label-warning"> 停用</option>
									    <option value="9"> 待审批</option>
									</select>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="smsSign.list">
                             <thead>
	                         	 <tr>
								      <th>短信签名</th>
								      <th class="text-c">签名状态</th>
								      <th class="text-c">创建时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:SIGN_NAME}}</td>
                                            <td class="text-c">{{getText:SIGN_STATE 'signState'}}</td>
											<th class="text-c">{{:CREATE_TIME}}</th>
											<td class="text-c">
												<a href="javascript:void(0)" onclick="SmsSign.editData('{{:SIGN_ID}}')" > 查看</a> 
												{{if SIGN_STATE!=9}}
													<a href="javascript:void(0)" onclick="SmsSign.delData('{{:SIGN_ID}}')" > 移除</a>
												{{/if}}
                                            </td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("SmsSign");
		
		SmsSign.searchData=function(){
			$("#searchForm").searchData();
		}
		
		SmsSign.addData=function(){
		    popup.layerShow({type:1,title:'企业短信签名申请',offset:'20px',area:['550px','480px']},"${ctxPath}/pages/sms/sms-sign-edit.jsp",{});
		}
		SmsSign.editData=function(signId){
		    popup.layerShow({type:1,title:'企业短信签名详情',offset:'20px',area:['550px','580px']},"${ctxPath}/pages/sms/sms-sign-edit.jsp",{signId:signId});
		}
		SmsSign.delData=function(signId){
			layer.confirm('当前短信模板将要被注销，是否继续？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		    	var data = {signId:signId};
		  		ajax.remoteCall("${ctxPath}/servlet/smsSign?action=delete", data, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	SmsSign.searchData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>