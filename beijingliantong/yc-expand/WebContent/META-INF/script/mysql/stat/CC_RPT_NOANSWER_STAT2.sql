/*==============================================================*/
/* Table: CC_RPT_NOANSWER_STAT2                                 */
/*==============================================================*/
create table CC_RPT_NOANSWER_STAT2
(
   ENT_ID               varchar(32) comment '企业ID',
   DATE_ID              int comment '日期',
   HOUR_ID              int comment '小时',
   AGENT_ID             varchar(32) comment '坐席ID',
   GROUP_ID             varchar(32) comment '技能组ID',
   MONTH_ID             int comment '月份',
   AREA_CODE            varchar(30) comment '客户来电地区',
   DEPT_CODE            varchar(30) comment '坐席所在部门编号',
   CALLED               varchar(30) comment '主叫号码',
   NOANSWER_COUNT       int comment '通话时长',
   CALLBACK_COUNT       int comment '回拨数据',
   LOAD_DATE            varchar(32) comment '数据更新时间'
);

alter table CC_RPT_NOANSWER_STAT2 comment '未接来电统计2';

/*==============================================================*/
/* Index: IDX_CC_RPT_NOANSWER_STAT2_1                           */
/*==============================================================*/
create index IDX_CC_RPT_NOANSWER_STAT2_1 on CC_RPT_NOANSWER_STAT2
(
   DATE_ID
);

/*==============================================================*/
/* Index: IDX_CC_RPT_NOANSWER_STAT2_2                           */
/*==============================================================*/
create index IDX_CC_RPT_NOANSWER_STAT2_2 on CC_RPT_NOANSWER_STAT2
(
   CALLED
);

/*==============================================================*/
/* Index: IDX_CC_RPT_NOANSWER_STAT2_3                           */
/*==============================================================*/
create index IDX_CC_RPT_NOANSWER_STAT2_3 on CC_RPT_NOANSWER_STAT2
(
   GROUP_ID,
   DATE_ID
);

/*==============================================================*/
/* Index: IDX_CC_RPT_NOANSWER_STAT2_4                           */
/*==============================================================*/
create index IDX_CC_RPT_NOANSWER_STAT2_4 on CC_RPT_NOANSWER_STAT2
(
   AGENT_ID,
   DATE_ID
);
