/*==============================================================*/
/* Table: CC_TASK_AGENT                                         */
/*==============================================================*/
create table CC_TASK_AGENT
(
   TASK_AGENT_ID        varchar(32) not null comment '任务坐席ID',
   ENT_ID               varchar(32) comment '企业ID',
   BUSI_ORDER_ID        varchar(32) comment '订购ID',
   TASK_ID              varchar(32) comment '任务ID',
   AGENT_ID             varchar(32) comment '技能组ID',
   TASK_GROUP_ID        varchar(32) comment '任务组ID',
   OBJ_COUNT            int default 0 comment '名单总量',
   OBJ_USE_COUNT        int default 0 comment '已使用名单量',
   CALL_COUNT           int default 0 comment '外呼次数',
   CALL_SUCCESS_COUNT   int default 0 comment '接通名单量',
   CALL_NOANSWER_COUNT  int default 0 comment '无人应答名单量',
   AGENT_COUNT          int default 0 comment '转坐席数',
   SALE_SUCCESS_COUNT   int default 0 comment '营销成功数',
   SALE_FAIL_COUNT      int default 0 comment '营销失败数',
   TIMER_COUNT          int default 0 comment '预约任务数',
   TASK_STATE           int default 0 comment '任务执行状态，0:待执行，１:执行中 ,　２:已完成',
   CREATOR              varchar(50) comment '创建人',
   CREATE_TIME          varchar(32) comment '创建时间',
   primary key (TASK_AGENT_ID)
);

alter table CC_TASK_AGENT comment '任务坐席，把呼叫任务分配到具体的坐席';

/*==============================================================*/
/* Index: IDX_CC_TASK_AGENT_1                                   */
/*==============================================================*/
create index IDX_CC_TASK_AGENT_1 on CC_TASK_AGENT
(
   AGENT_ID,
   BUSI_ORDER_ID
);

/*==============================================================*/
/* Index: IDX_CC_TASK_AGENT_2                                   */
/*==============================================================*/
create index IDX_CC_TASK_AGENT_2 on CC_TASK_AGENT
(
   ENT_ID,
   BUSI_ORDER_ID,
   TASK_ID
);

/*==============================================================*/
/* Index: IDX_CC_TASK_AGENT_3                                   */
/*==============================================================*/
create index IDX_CC_TASK_AGENT_3 on CC_TASK_AGENT
(
   TASK_GROUP_ID
);
