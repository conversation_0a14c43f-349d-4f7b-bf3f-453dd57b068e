/*==============================================================*/
/* Table: CC_TASK_OBJ_ROBOT_HIS                                 */
/*==============================================================*/
create table CC_TASK_OBJ_ROBOT_HIS
(
   CALL_OBJ_ID          varchar(32) not null comment '呼叫ID',
   DATE_ID              int comment '日期',
   OBJ_ID               varchar(32) not null comment '对象ID',
   ENT_ID               varchar(32) comment '企业ID',
   TASK_ID              varchar(32) comment '任务ID',
   BATCH_ID             varchar(32) comment '批次ID',
   MONTH_ID             int comment '月份',
   CUST_ID              varchar(50) comment '客户编号',
   CUST_NAME            varchar(50) comment '客户姓名',
   CUST_PHONE           varchar(50) comment '客户号码',
   STD_CLASS            varchar(10) comment '客户意向  A  B  C  D  E F ',
   STD_CLASS_CAUSE      varchar(50) comment '标准分类描述',
   SCORE                int comment '分值 0~100',
   LABELS               varchar(500) comment '标签，JSON数组',
   SERIAL_ID            varchar(32) comment '通话记录ID',
   BEGIN_TIME           varchar(19) comment '外呼开始时间，格式：yyyy-mm-dd hh:MM:ss',
   END_TIME             varchar(19) comment '外呼结束时间，格式：yyyy-mm-dd hh:MM:ss',
   CALL_TIME            int comment '通话时长',
   CLEAR_CAUSE          int comment '外呼标志，0  否  1 是',
   RECORD_FILE          varchar(255) comment '录音文件路径',
   READ_FLAG            int comment '已读状态，0 未读  1 已读',
   AGENT_FLAG           int comment '转坐席标志，0 未转坐席  1 转坐席',
   RESULT_ID            varchar(32) comment '营销结果ID',
   AGENT_ID             varchar(32) comment '坐席ID',
   GROUP_ID             varchar(32) comment '技能组ID，０,代表未分配',
   PARAMS               varchar(500) comment '提参信息，JSON数组',
   primary key (CALL_OBJ_ID)
);

alter table CC_TASK_OBJ_ROBOT_HIS comment '机器人营销结果历史';

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_3                           */
/*==============================================================*/
create index IDX_CC_TASK_OBJ_ROBOT_HIS_3 on CC_TASK_OBJ_ROBOT_HIS
(
   TASK_ID,
   DATE_ID,
   STD_CLASS
);

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_2                           */
/*==============================================================*/
create index IDX_CC_TASK_OBJ_ROBOT_HIS_2 on CC_TASK_OBJ_ROBOT_HIS
(
   CUST_PHONE
);

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_1                           */
/*==============================================================*/
create index IDX_CC_TASK_OBJ_ROBOT_HIS_1 on CC_TASK_OBJ_ROBOT_HIS
(
   TASK_ID,
   BATCH_ID,
   STD_CLASS
);

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_4                           */
/*==============================================================*/
create index IDX_CC_TASK_OBJ_ROBOT_HIS_4 on CC_TASK_OBJ_ROBOT_HIS
(
   CUST_ID
);

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_5                           */
/*==============================================================*/
create index IDX_CC_TASK_OBJ_ROBOT_HIS_5 on CC_TASK_OBJ_ROBOT_HIS
(
   TASK_ID,
   CALL_TIME,
   STD_CLASS
);

/*==============================================================*/
/* Index: IDX_CC_TASK_OBJ_ROBOT_HIS_6                           */
/*==============================================================*/
create unique index IDX_CC_TASK_OBJ_ROBOT_HIS_6 on CC_TASK_OBJ_ROBOT_HIS
(
   DATE_ID,
   OBJ_ID
);
