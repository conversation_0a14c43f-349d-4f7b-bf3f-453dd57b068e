/*==============================================================*/
/* Table: CC_VOX_GROUP_CUST                                     */
/*==============================================================*/
create table CC_VOX_GROUP_CUST
(
   GROUP_CUST_ID        varchar(32) not null comment '组用户ID',
   ENT_ID               varchar(32) comment '企业ID',
   GROUP_ID             varchar(32) comment '分组ID',
   INITIALS             varchar(10) comment '首字母',
   CUST_NAME            varchar(30) comment '姓名',
   CUST_PHONE           varchar(20) comment '电话号码',
   primary key (GROUP_CUST_ID)
);

alter table CC_VOX_GROUP_CUST comment '分组名单';

/*==============================================================*/
/* Index: IDX_CC_VOX_GROUP_CUST_1                               */
/*==============================================================*/
create index IDX_CC_VOX_GROUP_CUST_1 on CC_VOX_GROUP_CUST
(
   GROUP_ID,
   INITIALS
);

/*==============================================================*/
/* Index: IDX_CC_VOX_GROUP_CUST_2                               */
/*==============================================================*/
create index IDX_CC_VOX_GROUP_CUST_2 on CC_VOX_GROUP_CUST
(
   CUST_PHONE,
   ENT_ID,
   GROUP_ID
);
