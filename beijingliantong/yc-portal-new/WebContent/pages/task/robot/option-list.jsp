<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>编辑模板</title>
	<style type="text/css">
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="optionForm"  method="post">
				  <input type="hidden" value='${param.val }' name="values">
				  <table class="table table-auto table-bordered table-hover table-condensed" data-mars="taskObjRobot.optionList" data-template = "option-template" data-container="optionList">
				  		<thead>
				  			<tr>
				  				<th>选项值</th>
				  				<th>选项文本</th>
				  				<th>操作</th>
				  			</tr>
				  		</thead>
                        <tbody id="optionList">
                        </tbody>
	              </table>
                      	<script id="option-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td style="width:80px"><input name="key" value="{{:key}}" class="form-control input-sm" type="text"/></td>
											<td><input name="value" value="{{:value}}" class="form-control input-sm" type="text"/></td>                                        
											<td style="width:50px"> 
												<a  href="javascript:void(0)" onclick="OptionEdit.delOption(this)">移除</a>&nbsp;
											</td>
									    </tr>
								    {{/for}}					         
						</script>
                   <a style="float: right;" class="f-12" href="javascript:void(0);" onclick="OptionEdit.apendOption()">+添加选项</a>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="OptionEdit.saveForm()"> 保存 </button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	jQuery.namespace("OptionEdit");
	var data;
	$(function(){
		$("#optionForm").render({success:function(result){
			data=result['taskObjRobot.optionList'].data
		}});
	});
	/* 保存选项 */
	OptionEdit.saveForm = function(){
		let data = []
		var arr=[];
		var msg = "";
		$("#optionList").find('tr').each(function(){
			var json={};
			var key = $(this).find("[name='key']").val();
			var val = $(this).find("[name='value']").val();
			if(arr.indexOf(key) < 0){
				if(key&&key!=null&&key!=""){
					json[key]=val
					data.push(json);
					arr.push(key)
				}
			}else{
				msg = msg + "“" + key + "”";
			}
		});
		if(msg.length > 0){
			layer.msg(msg+"存在重复选项值，请再次确认！")
			return;
		}
		paramsConf.callbackOption(data);
		popup.layerClose("#optionForm");
	}
	
	OptionEdit.apendOption = function(){
		if(data==null||data==""){
			$("#optionList").find('tr').remove();
			data.push(0)
		}
		var html ="<tr><td style='width:80px'><input name='key' class='form-control input-sm' type='text'/></td>"+"<td><input name='value' class='form-control input-sm' type='text'/></td>"
		+"<td style='width:50px'><a href='javascript:void(0)' onclick='OptionEdit.delOption(this)'>移除</a></td></tr>";
		$("#optionList").append(html);
	}
	
	OptionEdit.delOption = function(e){
		$(e).parent().parent().remove();
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>