<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务状态详细</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="ibox-content" id="stateInfo" style="width: 100%;" data-mars="task.taskStatInfo" method="post" >
		<input type="hidden" name="taskId" value="${param.taskId }">
		  <table class="table table-vzebra">
               <tbody>
                    <tr>
	                    <td>任务状态</td>
	                    <td>${param.taskState }</td>
                    </tr>
               </tbody>
               <tbody id="stateData">
               </tbody>
          </table>
          <script id="state-template" type="text/x-jsrender">
			{{if state == 3 && workTime == 1}}
                    <tr>
	                    <td>执行状态</td>
	                    <td>执行中</td>
                    </tr>
                    <tr>
	                    <td>空闲坐席数</td>
	                    <td>{{:idleAgentCount}}</td>
                    </tr>
                    <tr>
	                    <td>当前执行外呼名单数</td>
	                    <td>{{:callCount}}</td>
                    </tr>
                    <tr>
	                    <td>等待外呼名单数</td>
	                    <td>{{:objCount}}</td>
                    </tr>
                    <tr>
	                    <td>接通率</td>
	                    <td>{{if jtRate > 0}}{{:jtRate*100}}{{else}}0{{/if}}%</td>
                    </tr>
                    <tr>
	                    <td>今天最大中继占用数</td>
	                    <td>{{:maxOccupyCount}}</td>
                    </tr>
                    <tr>
	                    <td>最近发起外呼时间</td>
	                    <td>{{if lastCallTime == '1970-01-01 00:00:00'}}未外呼{{else}}{{:lastCallTime}}{{/if}}</td>
                    </tr>
                    <tr>
	                    <td>最近获取外呼名单时间</td>
	                    <td>{{:lastGetTaskTime}}</td>
                    </tr>
			{{else}}
                    <tr>
	                    <td>执行状态</td>
	                    <td>{{if state == 2}}暂停中{{else}}-{{/if}}</td>
                    </tr>
                    <tr>
	                    <td>原因</td>
	                    <td>
						{{if stateReason == 1}}
	                    	无空闲坐席
						{{else stateReason == 2}}
	                   		等待外呼数为0
						{{else stateReason == 11}}
	                   		监控长时间未通知
						{{else workTime == 9}}
	                    	非工作时间
						{{else}}
							任务状态【${param.taskState }】
						{{/if}}
						</td>
                    </tr>
			{{/if}}
			
		 </script>
	</form>	
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		$(function(){
			$("#stateInfo").render({success:function(result){
				var html = $.templates("#state-template").render(result['task.taskStatInfo'].data);
				$("#stateData").html(html);
			}});
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>