<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>外呼任务表</title>
	<style>
		.dropdown-icon>li{margin: -4px 0px}
		h5>span:last-child{letter-spacing: 0px;color: #5cb85c}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" data-task-type="1" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 预览式外呼任务<span title="每隔十分钟统计" class="f-12">(数据统计时间:<span  data-mars="statistic.getTaskStatUpdateTime"></span>)</span></h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">任务名称</span>	
									  <input type="text" name="taskName" class="form-control input-sm" style="width:122px">
							   </div>
             		          <div class="input-group input-group-sm">
									<span class="input-group-addon">状态</span>
									<select class="form-control input-sm" name="state" onchange="Task.loadData()">
										<option value="">全部</option>
										<option value="1">待导入客户</option>
										<option value="2" data-class="label label-info">待分配</option>
										<option value="3">待启动(未到时间)</option>
										<option value="4" data-class="label label-info">待启动</option>
										<option value="5" data-class="label label-success">启动中</option>
										<option value="6" data-class="label label-danger">已暂停</option>
										<option value="7" data-class="label label-warning">已关闭</option>
										<option value="8">已完成</option>
									 </select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="Task.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
							   <div class="input-group input-group-sm pull-right btn-group">
							       <button type="button" class="btn btn-sm btn-success" onclick="Task.addData()">+新增任务</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content table-responsive">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-nowrap" data-auto-fill="10" id="tableHead" data-mars="task.list">
                             <thead>
	                         	 <tr>
								      <th>任务名称</th>
								      <th class="text-c" data-sort="TASK_STATE">状态</th>
								      <th class="text-c" data-sort="OBJ_COUNT" class="text-c">名单总量</th>
								      <th  class="text-c">已使用名单量</th>
								      <th class="text-c" class="text-c">待分配数</th>
								      <th data-sort="OBJ_USE_COUNT" class="text-c">可使用名单量</th>
								      <th data-sort="CALL_SUCCESS_COUNT" class="text-c">接通名单量</th>
								      <th class="text-c">接通率 </th>
								      <th data-sort="SALE_SUCCESS_COUNT" class="text-c"><span class="portal-name">营销</span>成功数</th>
								      <th data-sort="SALE_FAIL_COUNT" class="text-c"><span class="portal-name">营销</span>失败数</th>
								      <th class="text-c">成功率</th>
								      <th class="text-c">操作</th>
								      <th data-sort="CREATE_TIME">创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>
												<a  href="javascript:void(0)" title="{{:TASK_NAME}}" onclick="Task.detailData('{{:TASK_ID}}','{{:TASK_STATE}}')">{{call: fn='getTaskType'}}&nbsp;{{cutText:TASK_NAME 40}}</a> 
											</td>                                         
											<td class="text-c"> {{call:(END_DATE) (TASK_STATE) fn='excuteTime'}}</td>
											<td class="text-c"> <span class="text-info">{{:OBJ_COUNT}}</span></td> 
											<td class="text-c"> {{call: fn='useCount'}} </td>                                        
											<td class="text-c"> {{if ASIGN_COUNT!=0}} <span class="label label-info label-outline">{{:ASIGN_COUNT}}</span>{{else}}<span class="text-primary">{{:ASIGN_COUNT}}</span>{{/if}}</td>                                         
											<td class="text-c"> {{if OBJ_USE_COUNT==OBJ_COUNT}}<span class="text-success">{{:OBJ_COUNT-OBJ_USE_COUNT}}</span> {{else}} <span class="label label-info label-outline">{{:OBJ_COUNT-OBJ_USE_COUNT}}</span>{{/if}}</td>                                           
											<td class="text-c"> <span class="text-success">{{:CALL_SUCCESS_COUNT}}</span></td>                                         
											<td class="text-c"> <span class="text-warning">{{call: fn='callCvr'}}</span></td>                                         
											<td class="text-c"> <span class="text-warning">{{:SALE_SUCCESS_COUNT}}</span></td>                                         
											<td class="text-c"><span class="text-danger"> {{:SALE_FAIL_COUNT}}</span></td>                                         
											<td class="text-c"><span class="text-danger"> {{call: fn='saleCvr'}}</span></td>                                         
											<td class="text-c">
												  <div class="btn-group btn-group-xs dropdown ml-10">
													<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
														操作  <span class="caret"></span>
													</button>
													<ul class="dropdown-menu dropdown-icon" role="menu">
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-home"></i>
																</div>
																<a  href="javascript:void(0)" style="color:#5cb85c;" onclick="Task.detailData('{{:TASK_ID}}',{{:TASK_STATE}})">任务总览</a>
															</li>
														   {{if TASK_STATE!=7}}
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-edit"></i>
																</div>
																<a  href="javascript:void(0)" onclick="Task.editData('{{:TASK_ID}}','{{:TEMP_ID}}')">编辑任务</a>
															</li>
															{{/if}}
															{{if TASK_STATE < 7 && TASK_SOURCE ==1}}	
																	<li><div class="addon">
																		<i class="glyphicon glyphicon-import"></i>
																	</div><a  href="javascript:void(0)" onclick="Task.custImport('{{:TASK_ID}}','{{:TEMP_ID}}',{{:TASK_STATE}})">导入客户资料</a></li>
															{{/if}}
															{{if TASK_STATE < 7}}
															 <li><div class="addon">
																<i class="glyphicon glyphicon-import"></i>
															 </div><a  href="javascript:void(0)" onclick="Task.blackImport('{{:TASK_ID}}')">导入黑名单</a></li>
															{{/if}}
															{{if TASK_STATE==2}}
																{{!--<li><a  href="javascript:void(0)" onclick="Task.assignSkillGroup('{{:TASK_ID}}',{{:TASK_TYPE}})">分配技能组</a></li>--}}
															{{/if}}
															{{if TASK_STATE < 7}}
																<li><a  href="javascript:void(0)" onclick="Task.saleResult('{{:TASK_ID}}')">{{call:'营销' fn='parsePortalName'}}结果配置</a></li>
															{{/if}}
															{{if ((TASK_STATE<7 && TASK_STATE>1) && ASIGN_COUNT > 0)&& ALLOCATION_TYPE!=1}}
																<li><a  href="javascript:void(0)" id="taskAssign_{{:TASK_ID}}" onclick="Task.taskAssign('{{:TASK_ID}}','{{:TASK_STATE}}','{{:TASK_TYPE}}','{{:IS_BATCH_ALLOCATION}}','{{:ASIGN_COUNT}}')">任务分配({{:ASIGN_COUNT}})</a></li>
															{{/if}}
															<li role="separator" class="divider"></li>
															{{if TASK_STATE==3  || TASK_STATE ==4 || TASK_STATE ==6|| TASK_STATE ==8}}
																<li><div class="addon">
																		<i class="glyphicon glyphicon-play"></i>
																	</div><a  href="javascript:void(0)" onclick="Task.beignTask('{{:TASK_ID}}')">启动任务</a></li>
															{{/if}}
															{{if TASK_STATE ==5}}
																	<li><div class="addon">
																		<i class="glyphicon glyphicon-pause">
																	</i>
																	</div><a  href="javascript:void(0)" onclick="Task.pauseTask('{{:TASK_ID}}')">暂停任务</a></li>
															{{/if}}
															{{if TASK_STATE >= 5 &&  TASK_STATE < 8 && TASK_STATE!=7}}
																<li>
																	<div class="addon">
																		<i class="glyphicon glyphicon-ok"></i>
																	</div>
																	<a  href="javascript:void(0)" onclick="Task.finishTask('{{:TASK_ID}}',{{:TASK_STATE}})">任务已执行完成</a>
																</li>
																<li>
																	<div class="addon">
																		<i class="glyphicon glyphicon-list-alt"></i>
																	</div>
																	<a  href="javascript:void(0)" onclick="Task.twoSales('{{:TASK_ID}}','{{:TASK_NAME}}')">二次{{call:'营销' fn='parsePortalName'}}</a>
																</li>
															{{/if}}
															{{if TASK_STATE !=7 && TASK_STATE >= 3}}
																<li><div class="addon">
																		<i class="glyphicon glyphicon-stop"></i>
																	</div><a  href="javascript:void(0)" onclick="Task.cannelTask('{{:TASK_ID}}',{{:TASK_STATE}})">关闭任务</a></li>
															{{/if}}
															{{if TASK_STATE < 3}}
																<li><div class="addon">
																		<i class="glyphicon glyphicon-remove"></i>
																	</div><a  href="javascript:void(0)" onclick="Task.deleteTask('{{:TASK_ID}}')">删除任务</a></li>
															{{/if}}
															{{if TASK_STATE == 5 || TASK_STATE == 6}}
																	<li role="separator" class="divider"></li>
																	<li>
																		<div class="addon">
																			<i class="glyphicon glyphicon-refresh">
																			</i>
																		</div>
																		<a  href="javascript:void(0)" onclick="Task.updateTaskData('{{:TASK_ID}}')">刷新统计数据</a>
																	</li>
																    <li>
																       <div class="addon">
																		  <i class="glyphicon glyphicon-trash"></i>
																        </div>
																        <a  href="javascript:void(0)" onclick="Task.taskRecovery('{{:TASK_ID}}',{{:TASK_STATE}},'{{:TASK_NAME}}')">任务回收</a>
																	</li>
																<li><a  href="javascript:void(0)" onclick="Task.resultList('{{:TASK_ID}}','{{:TASK_NAME}}')">查看{{call:'营销' fn='parsePortalName'}}数据</a></li>
															{{/if}}
															<li>
																<div class="addon">
																	<i class="glyphicon glyphicon-pencil"></i>
																</div>
																<a  href="javascript:void(0)" onclick="Task.log('{{:TASK_ID}}')">操作日志</a>
															</li>
													</ul>
												</div>
											</td>
											<td>{{cutText:CREATE_TIME 12 ''}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp">
	                     		<jsp:param value="10,20,50,100" name="pageSizes"/>
	                     		<jsp:param value="20" name="pageSize"/>
	                     	</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Task");
		$(function(){
			$("#searchForm").render();
		});
		Task.loadData=function(){
			$("#searchForm").searchData();
		}
		Task.addData=function(){
			popup.layerShow({type:1,shadeClose:false,title:'新增外呼任务',offset:'20px',area:['630px','500px']},"${ctxPath}/pages/task/manual-task-edit.jsp",null);
		}
		Task.editData=function(taskId,tempId){
		    popup.layerShow({type:1,shadeClose:false,title:'编辑任务',offset:'20px',area:['620px','500px']},"${ctxPath}/pages/task/manual-task-edit.jsp",{taskId:taskId,tempId:tempId});
		}
		Task.detailData=function(taskId,state){
			popup.openTab("${ctxPath}/pages/task/task-info-stat.jsp","任务总览",{taskId:taskId,taskState:state,type:1});
		    //popup.layerShow({type:1,title:'查看外呼任务',offset:'20px',area:['80%','90%']},"${ctxPath}/pages/task/task-detail.jsp",{taskId:taskId,type:1});
		}
		Task.saleResult=function(taskId){
		    popup.layerShow({type:1,title:'任务'+portalName+'结果配置',offset:'20px',area:['600px','450px']},"${ctxPath}/pages/task/task-sales-result.jsp",{taskId:taskId});
		}
		Task.log=function(taskId){
		    popup.layerShow({type:1,title:'操作日志',offset:'20px',area:['600px','450px']},"${ctxPath}/pages/task/task-log.jsp",{taskId:taskId});
		}
		Task.taskRecovery=function(taskId,taskState,taskName){
			if(taskState!=6){
				layer.alert("非任务暂停状态不能操作!",{icon:7});
				return
			}
		    popup.layerShow({type:1,title:'任务数据回收',offset:'20px',area:['550px','450px']},"${ctxPath}/pages/task/task-recovery.jsp",{taskId:taskId,taskName:taskName});
		}
		Task.updateTaskData=function(taskId){
			ajax.remoteCall("${ctxPath}/servlet/taskObj?action=synDataStat",{taskId:taskId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						Task.loadData();
					});
				}else{
				      layer.alert(result.msg,{icon: 7});
				}
			  }
			);
		}
		function saleCvr(data){
			var total=parseInt(data.SALE_SUCCESS_COUNT)+parseInt(data.SALE_FAIL_COUNT);
			var num =data.SALE_SUCCESS_COUNT;
			if (isNaN(num) || isNaN(total)) { 
				return "-"; 
			} 
			var t=Math.round(num / total * 10000) / 100.00;
			if(t>100)t=100;
			return total <= 0 ? "0%" : (t+ "%"); 
		}
		Task.blackImport=function(taskId){
			popup.openTab("${ctxPath}/pages/task/black/black-list.jsp","任务黑名单管理",{taskId:taskId});

			popup.layerShow({type:1,title:'导入黑名单',offset:'20px',area:['420px','310px'],shadeClose:false},"${ctxPath}/pages/task/black-import.jsp",{taskId:taskId});
		}
		function callCvr(data){
			//var total=parseInt(data.CALL_SUCCESS_COUNT)+parseInt(data.CALL_NOANSWER_COUNT);
			var total=parseInt(data.OBJ_USE_COUNT);
			var num =data.CALL_SUCCESS_COUNT;
			if (isNaN(num) || isNaN(total)) { 
				return "-"; 
			} 
			var t=Math.round(num / total * 10000) / 100.00;
			if(t>100)t=100;
			return total <= 0 ? "0%" : (t + "%"); 
		}
		
		function useCvr(data){
			var total=parseInt(data.OBJ_COUNT);
			var num =data.OBJ_USE_COUNT;
			if (isNaN(num) || isNaN(total)) { 
				return "-"; 
			} 
			var rate=total <= 0 ? "0%" : (Math.round(num / total * 10000) / 100.00 + "%");
			var msg="已执行："+rate+"；待执行数："+(total-num);
			return msg; 
		}

		function useCount(data){
			var html = '';
			if(parseInt(data.OBJ_USE_COUNT)>=parseInt(data.OBJ_COUNT)){
				html = '<span class="lable lable-success">'+data.OBJ_COUNT+'</span> ';
			}else {
				html = '<span class="label label-info label-outline" data-toggle="tooltip" title="'+useCvr(data)+'">'+data.OBJ_USE_COUNT+'</span> ';
			}
			return html;
		}
		Task.twoSales = function(taskId,taskName){
			popup.layerShow({type:1,title:'二次'+portalName,offset:'20px',area:['630px','428px']},"${ctxPath}/pages/task/task-result-two-sales.jsp",{taskId:taskId,taskName:taskName});
		}
		
		function twoSalesCallback(){
			Task.loadData();
		}
		
		Task.beignTask=function(taskId){
			ajax.remoteCall("${ctxPath}/servlet/task?action=beignTask",{taskId:taskId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1});
					Task.loadData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
		Task.deleteTask=function(taskId){
			layer.confirm("确定要删除此任务？删除后不可恢复",{offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/task?action=deleteTask",{taskId:taskId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1});
							Task.loadData();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					  }
					);
			});
		}
		Task.deleteTaskData=function(taskId){
			layer.confirm("确定要清空此任务历史数据？包括导入批次、任务名单、预约记录、回访结果及任务通话记录！操作后不可恢复！",{offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/task/expand?action=delTaskDataAuto",{taskId:taskId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1});
						Task.loadData();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				  }
				);
			});
		}
		Task.pauseTask=function(taskId){
			layer.confirm("确定要暂停此任务吗?",{offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/task?action=pauseTask",{taskId:taskId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1});
							Task.loadData();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					  }
					);
			});
		}
		
		Date.prototype.format = function(format) {
		    var date = {
		       "M+": this.getMonth() + 1,
		       "d+": this.getDate(),
		       "h+": this.getHours(),
		       "m+": this.getMinutes(),
		       "s+": this.getSeconds(),
		       "q+": Math.floor((this.getMonth() + 3) / 3),
		       "S+": this.getMilliseconds()
		    };
		    if (/(y+)/i.test(format)) {
		       format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
		    }
		    for (var k in date) {
		       if (new RegExp("(" + k + ")").test(format)) {
		           format = format.replace(RegExp.$1, RegExp.$1.length == 1
		              ? date[k] : ("00" + date[k]).substr(("" + date[k]).length));
		       }
		    }
		    return format;
		}
		function excuteTime(date,state){
			var currentDate=new Date();
			if(currentDate.format('yyyy-MM-dd') > date && state==5){
				return '<span class="label label-warning ml-5">任务已过期</span>';
			};
			return transValueToText('state',state);
			
		}
		function  getTaskType(data){
			if(data.ALLOCATION_TYPE==1){
				return '<label class="label label-info label-outline">公海</label>';
			}
			if(data.TASK_SOURCE==2){
				return '<label class="label label-info label-outline">大数据</label>';
			}
			return '';
		}
		Task.cannelTask=function(taskId,taskState){
			if(taskState==5){
				layer.msg("请先暂停再关闭任务！",{icon:7,offset:'20px'});
				return;
			}
			layer.confirm("确定要关闭此任务吗?",{offset:'20px',icon:7},function(){
				layer.confirm("关闭任务后不能再恢复,除"+portalName+"成功的客户数据和通话录音数据外将会被清理,确认关闭吗?",{offset:'20px',icon:7},function(){
					ajax.remoteCall("${ctxPath}/servlet/task?action=cannelTask",{taskId:taskId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1});
							Task.loadData();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					  }
					);
				});
			});
		}
		Task.finishTask=function(taskId,taskState){
			if(taskState==5){
				layer.msg("请先暂停再操作！",{icon:7,offset:'20px'});
				return;
			}
			ajax.remoteCall("${ctxPath}/servlet/task?action=finishTask",{taskId:taskId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1});
					Task.loadData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
		Task.assignSkillGroup=function(taskId,type){
		    popup.layerShow({type:2,title:'分配技能组',offset:'20px',area:['650px','430px']},"${ctxPathPrefix}/yc-base/pages/skillGroup/select-skill-group.jsp",{taskId:taskId,type:type});
		}
		//任务分配
		Task.taskAssign=function(taskId,taskState,type,isBatchAssign,count){
			//先判断是否分配技能组
			ajax.remoteCall("${ctxPath}/servlet/task?action=hasTaskGroup",{taskId:taskId},function(result) { 
				if(result.state == 1){
					//是否按批次分配
					if(isBatchAssign==1){
					    popup.layerShow({type:1,title:'可分配数：'+count,maxmin:true,offset:'20px',area:['80%','93%']},"${ctxPath}/pages/task/task-assign-for-batch.jsp",{taskId:taskId,taskState:taskState,count:count});
					}else{
					    popup.layerShow({type:1,title:'可分配数：'+count,maxmin:true,offset:'20px',area:['600px','400px']},"${ctxPath}/pages/task/task-assign.jsp",{taskId:taskId,taskState:taskState,count:count});
					}
				}else{
					layer.alert(result.msg,{icon: 5},function(index){
						layer.close(index);
						Task.assignSkillGroup(taskId,type);
					});
				}
			  }
			); 
		}
		Task.custData=function(taskId,tempId){
			 popup.openTab("${ctxPath}/pages/task/task-cust-list.jsp","客户数据",{taskId:taskId,tempId:tempId,taskType:2,type:2});
		}
		Task.custImport=function(taskId,tempId,state){
			if(tempId==''){
				layer.msg('请先设置模板!');
				return;
			}
		    popup.layerShow({type:1,title:'导入客户资料',offset:'20px',area:['500px','400px'],shadeClose:false},"${ctxPath}/pages/cust/cust-import.jsp",{taskId:taskId,tempId:tempId,taskType:1,state:state});
		}
		Task.resultList=function(taskId,taskName){
		    popup.openTab("${ctxPath}/pages/task/task-result-search.jsp",portalName+"结果查询",{taskId:taskId,taskName:taskName});
		}
		Task.cdrList=function(taskId){
		    popup.openTab("${ctxPathPrefix}/yc-base/pages/record/cdr-query.jsp","通话记录查询",{taskId:taskId});
		}
	</script>
	
	
	
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
