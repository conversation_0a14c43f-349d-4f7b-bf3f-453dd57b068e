var task;
$(function(){
		if(window !=top){  
		    top.location.href=location.href;  
		}  
		$(".personInfo").mouseover(function(){
			$(this).addClass("open");
		});
		$(".personInfo").mouseout(function(){
			$(this).removeClass("open");
		});
		if(agentPhone==''||agentPhone==null){
			$(".isAdmin").css("display","none");
		}else{
			$(".isAgent").css("display","none");
		}
		ccbar_plugin.init('', '',true);
		ccbarEvent.addEvent('logon', function(event) {
			if(event.result == 'Fail'){return}
			pushMessage();
			$(".ccbar-need-logon").show()
			$(".agentLogoff").removeAttr('style');
			if(CallControl.ccbarMode == 1){
				$("#workMode").hide();
			}else{
				$("#workMode").show();
			}
			task=setInterval(pushMessage,120000);
			window.onbeforeunload = function() { 
			    return "确认离开当前页面吗？如话机已签入请先签出!";
			 } 
		});
		ccbarEvent.addEvent('logoff', function(event) {
			clearInterval(task);
			$(".ccbar-need-logon").hide()
			window.onbeforeunload=null;

		});
		ccbarEvent.addEvent('setready', function(event) {
			pushMessage();
		});

		/*增加*/
		ccbarEvent.addEvent('evtAltering',function(){
			$(".ccbar-need-connect").show()
		});

		ccbarEvent.addEvent('evtDisConnected',function(){
			$(".ccbar-need-connect").hide()
		});
		
		$(".portalLogo").attr("src",getCtxPathPrefix()+"/yc-res/"+entId+"/images/logo-portal.png");
		
	});

	function getCtxPathPrefix(){
		return localStorage.getItem("ctxPathPrefix");
	}
		function pushMessage(){
			/*ajax.remoteCall("/yc-portal/workbench?action=pushMessage",{},function(result) { 
				if(result.state==0){
					window.location.reload();
				}else{
					if(result.data){
					  if(CallControl.getState()=='IDLE'||CallControl.getState()=='BUSY'||CallControl.getState()==null||CallControl.getState()=='LOGOFF'){
						if(result.data.length==1){
								popup.openTab({url:"/yc-portal/pages/task/my-task-execute.jsp?from=myVisit",data:{timeCallId:result.data[0].TIME_CALL_ID,objId:result.data[0].OBJ_ID},title:"我的预约任务",id:'myVisit'});
								clearInterval(task);
								task=setInterval(pushMessage,1000*300);
						}else{
							layer.alert(result.msg,{icon:7,time:80000,offset:'rb',shadeClose:false,shade :0},function(index){
								layer.close(index);
								clearInterval(task);
								task=setInterval(pushMessage,1000*300);
								popup.openTab({url:"/yc-portal/pages/task/my-task-appoint-list.jsp",title:"我的预约任务",id:'yc_task_mytimerlist'});
							});
						}
					  }
					}
				}
			},{loading:false,error:function(){
				clearInterval(task);
			}});*/
			
		}
		 function agentTodayStat(){
	    	popup.layerShow({type:2,title:'我的今日工作统计',offset:'100px',area:['400px','220px']},getCtxPathPrefix()+"/yc-base/pages/agent/agent-today-stat.jsp",null);
		}
		 function systemlogout(){
			if(typeof(CallControl) !='undefined'){
				if($("[data-ccbartype='logoff']")&&$("[data-ccbartype='logoff']").hasClass('disabled')){}else if(top.CallControl&&top.CallControl.getFunc('logoff')){ccbar_plugin.callControl.logoff()};
			}
			setTimeout(function(){
				location.href = getCtxPathPrefix()+'/yc-login?ctxPath=/yc-portal&busiId=002';
			},600)
		}
	 	var errorCount=1;
		function errorImg(img) {
			if(errorCount==1){
				img.src = getCtxPathPrefix()+"/yc-res/default/images/logo-portal.png";
			}
			if(errorCount >= 2){
				img.src = getCtxPathPrefix()+"/yc-login/static/images/portal_logo.png";
				img.onerror = null;
			}
			errorCount++;
		}
		function callerphone(){
			CallControl.getActivateSipPhoneUrl( CallControl.myPhoneNum, '' );
		}