package com.yunqu.yc.portal.utils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.HashSet;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.base.QueryFactory;

/**
 * 表字段校验，用于升级时，有时缺少对应字段报错，如果新增字段要求必须使用该类先进一步判断
 * 拓展可以校验字段类型
 * <AUTHOR>
 *
 */
public class TableDataUtil {

	private static Logger logger = LogEngine.getLogger("TABLE-WARN");
	
	private static JSONObject tableField = new JSONObject();

	/**
	 * 查询表结构
	 * @param tableName
	 * @return
	 */
	private static JSONObject getTableInfo(String tableName){
		int indexOf = tableName.indexOf(".");
		if(indexOf>0){
			tableName = tableName.substring(0, indexOf) + tableName.substring(indexOf).toLowerCase();
		}else{
			tableName = tableName.toLowerCase();
		}
		JSONObject table = null;
		if(tableField.containsKey(tableName)){
			table = tableField.getJSONObject(tableName);
		}else{
			table = queryTableInfo(tableName);
		}
		return table;
	}
	
	/**
	 * 查询表结构
	 * @param tableName
	 * @return
	 */
	private synchronized static JSONObject queryTableInfo(String tableName){
		JSONObject table = null;
		if(tableField.containsKey(tableName)){
			table = tableField.getJSONObject(tableName);
		}else{
			table = new JSONObject();
			EasyQuery query = QueryFactory.getReadQuery();
			String sql = "SELECT * FROM "+tableName;
			if(query.getTypes() == DBTypes.MYSQL){
				sql += " limit 1";
			}else if(query.getTypes() == DBTypes.ORACLE){
				sql += " WHERE ROWNUM <= 1";
			}else if(query.getTypes() == DBTypes.SQLSERVER){
				sql += " TOP 1";
			}
			try(Connection conn = query.getConnection();
				Statement stmt = conn.createStatement();
			    ResultSet rs = stmt.executeQuery(sql)) {  

			    ResultSetMetaData metaData = rs.getMetaData();  
			    int columnCount = metaData.getColumnCount();  

			    for (int i = 1; i <= columnCount; i++) {  
			        String columnName = metaData.getColumnName(i);  
			        String columnType = metaData.getColumnTypeName(i);
			        table.put(columnName.toUpperCase(), columnType);
			    }
			    logger.info("[table init]tableName:"+tableName+","+table);
			} catch (Exception e) {
				logger.warn("[table no find]tableName:"+tableName);
				logger.error(e.getMessage(), e);
			}
			tableField.put(tableName, table);
		}
		return table;
	}
	
	/**
	 * 判断表字段是否存在
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	public static boolean exitTableField(String tableName, String columnName){
		boolean exit = false;
		JSONObject tableInfo = getTableInfo(tableName);
		exit = tableInfo.containsKey(columnName.toUpperCase());
		if(!exit){
			logger.warn("[column no find]tableName:"+tableName+",columnName:"+columnName);
		}
		return exit;
	}
	
	/**
	 * 脚本写入数据
	 * @param record
	 * @return
	 */
	public static EasyRecord checkRecord(EasyRecord record){
		String tableName = record.getTableName();
		for (String columnName : new HashSet<>(record.keySet())) {
			if(!exitTableField(tableName, columnName)){
				record.remove(columnName);
			}
		}
		return record;
	}

}
