package com.yunqu.cc.mixgw.base;

import java.io.File;
import java.sql.SQLException;

import org.easitline.common.core.Globals;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;


import org.easitline.common.utils.string.StringUtils;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.util.StringUtil;
/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constants {

    public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1"; //默认数据源名称(写)

    public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2"; //默认数据源名称(写)

    public final static String DS_READ_NAME = "yc-read-ds"; //默认数据源名称(读)


    public final static String APP_NAME = "cx-mix-jkzx";

    private static AppContext context = AppContext.getContext("yc-api");

    private static AppContext congmixlt = AppContext.getContext("cx-mix-lt");
    /**
     * 从cx-mix-lt模块获取平台配置项-云客服业务库
     */
    public static final String LT_BUSI_DB = congmixlt.getProperty("LT_BUSI_DB", "");
    /**
     * 管理员
     */
    public final static int ROLE_TYPE_MANAGER = 1;

    /**
     * 坐席
     */
    public final static int ROLE_TYPE_AGENT = 3;
    /**
     * 班长
     */
    public final static int ROLE_TYPE_MONITOR = 2;


    /**
     * 统计库名称
     */
    public static String getStatSchema() {
        return context.getProperty("DB_STAT_NAME", "stat");
    }

    /**
     * 获取配置常量,如果没有配置常量,赋予一个默认值
     *
     * @param key          配置的常量id
     * @param defaultValue 默认值
     * @return
     */
    public static String getAppConfigProperty(String key, String defaultValue) {
        AppContext appContext = AppContext.getContext(APP_NAME);
        String value = "";
        if (null != appContext) {
            value = appContext.getProperty(key, defaultValue);
        } else {
            value = defaultValue;
        }
        return value;
    }
    
    /**
     * 获取失败发送短信的发送号码
     * @return
     */
    public static String getSendSmsPhone() {
    	return AppContext.getContext(APP_NAME).getProperty("JKZF_SEND_SMS_PHONE", "15903243544");
    }
    
    /**
     * 获取疾控中心企业id
     * @return
     */
    public static String getEntId() {
    	return AppContext.getContext(APP_NAME).getProperty("JKZF_ENT_ID", "1000");
    }
    
    /**
	 * 获取正确的录音后缀
	 * @return
	 */
	public static String getCorrectFileSuffix(String recordFile, String entId){
		String prefix = getRecordUrlPrefix(entId); //老版本是用 /recordfile，新版在ROOT里；新版本是配置的文件绝对路径，如/home/<USER>
		String filePath = "";
		if(prefix.startsWith("/recordfile")){
			filePath = getFileBasePath() + prefix + recordFile;
		}else{
			filePath = prefix + recordFile;
		}
		for(String suffix : getFileSuffix().split(";")) {
			File file = new File(filePath + suffix);
			if(file.exists()) {
				return suffix;
			}
		}
		CommonLogger.logger.error("找不到录音文件的后缀名recordFile=" + filePath);
		return "";
	}
    
	
	/**
	 * 录音文件前缀
	 * @return
	 */
	public static String getRecordUrlPrefix(String entId){
		String url=CacheManager.getMemcache().get("RecordUrlPrefix_"+entId);
		   try {
			   if(StringUtils.isBlank(url)){
				   String sql = " select RECORD_FILE_URL from CC_PETRA_RES t  JOIN CC_ENT_RES t1 on t.PETRA_ID = t1.PETRA_ID WHERE t1.ENT_ID in (SELECT (CASE WHEN t2.P_ENT_ID='0' THEN t2.ENT_ID ELSE t2.P_ENT_ID END) ENT_ID FROM CC_ENT t2 where t2.ENT_ID = ? )";
				    url=QueryFactory.getReadQuery().queryForString(sql, new Object[]{entId});
				    if(StringUtils.isNotBlank(url)){
				    	if(url.lastIndexOf("/")==-1){
				    		url=url+"/";
				    	}
				    }
				    CacheManager.getMemcache().put("RecordUrlPrefix_"+entId, url);
			   }
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return url;
	}
	
	
    /**
	 * 获取录音相对路径
	 * @return
	 */
	public static String getRecordFileRelativePath(String path){
		String dir = getFileBasePath();
		if(!StringUtil.startsWith(path, "/")) {
			dir += File.separator;
		}
		for(String suffix : getFileSuffix().split(";")) {
			if(ServerContext.isDebug()) {
				CommonLogger.logger.info(" 通话录音文件路径recordFile=" + dir + path + suffix);
			}
			File file = new File(dir + path + suffix);
			if(file.exists()) {
				return path + suffix;
			}
		}
		return path;
	}
	
	/**
	 * 获取录音后缀配置
	 * @return
	 */
	public static String getFileSuffix(){
		 return AppContext.getContext(APP_NAME).getProperty("FILE_SUFFIX", "");
	}

	
	public static String getFileBasePath() {
		String basePath = AppContext.getContext(APP_NAME).getProperty("FILE_BASE_PATH", "");
		if(CommonUtil.isNotBlank(basePath)) {
			return basePath;
		}else {
			basePath = Globals.WEBAPPS_DIR + File.separator + "ROOT";
			return basePath.replaceAll("\\\\", "/");
		}
	}
    
    
    
	/**
	 * 定时任务所在机器开启即可，同时需要在企业呼叫中心定时任务开启该任务[每十分钟把录音文件转写成文本]
	 * <AUTHOR> @date  2023/4/20 10:38
	 * @return boolean
	 */
	public static boolean asrTransferData() {
		return "open".equals(AppContext.getContext(APP_NAME).getProperty("JKZX_ASRTRANSFER_DATA","close"));
	}
	
	/**
     * 转写时间 01-3天 02-7天 03-10天 04-30天
     */
    public final static String dateType = "02";
    
    
    /**
	 *语言转文本接口url
	 */
	public static String getSmsSendUrl() {
		return AppContext.getContext(APP_NAME).getProperty("AUDIO_TO_TEXT_URL", "http://127.0.0.1:9060");
	}
	
	
	/**
	 *最大抽取数
	 */
	public static String getCreateMax() {
		return AppContext.getContext(APP_NAME).getProperty("JKZX_CREATE_MAX", "10");
	}

	/**
	 * 导出数据量限制
	 * @return 默认3000
	 */
	public static int getLimitExportCount(){
		String count = AppContext.getContext(APP_NAME).getProperty("LIMIT_EXPORT_COUNT", "3000");
		if(StringUtils.isBlank(count)){
			return 3000;
		}
		try {
			return Integer.parseInt(count);
		} catch (Exception e) {
		}
		return 3000;
	}


	/**
	 * 离线导出最大长度
	 * @return 默认150000
	 */
	public static int getOffLineExportMaxCount(){
		String count = AppContext.getContext(APP_NAME).getProperty("OFF_LINE_EXPORT_MAX_COUNT", "150000");
		if(StringUtils.isBlank(count)){
			return 150000;
		}
		try {
			return Integer.parseInt(count);
		} catch (Exception e) {
		}
		return 150000;
	}
}
