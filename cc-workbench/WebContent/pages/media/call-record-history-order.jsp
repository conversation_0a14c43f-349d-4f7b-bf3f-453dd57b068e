<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>通话记录</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<form id="easyform"  method="post"  class="layui-form" style="color: #000000;padding-top: 5px;">
<!-- 全媒体页面路径参数 -->
<input type="hidden" name="sessionId" value="${param.sessionId }" />
<input type="hidden" name="custId" id="custId" value="${param.custId }" />
<input type="hidden" name="channelKey" value="${param.channelKey }" />
<input type="hidden" name="channelType" value="${param.channelType }" />
<input type="hidden" name="account" value="${param.account }" />
<input type="hidden" name="nickname" value="${param.nickname }" />
<input type="hidden" name="chatSessionId" value="${param.chatSessionId }" />
<input type="hidden" name="serialId" id="serialId" value="${param.serialId }" />
<input type="hidden" name="SESSION_RECORD_ID" value="${param.chatSessionId }" />
<input type="hidden" name="customerMobilePhone" value="${param.customerMobilePhone }" />
<input type="hidden" name="custPhone" value="${param.custPhone }" />
<input type="hidden" name="SOURCE" value="media" />
<div id="timeline" class="timeline-box">
</div>

</form>	
	
</EasyTag:override>
<EasyTag:override name="script">

<script id="chat-his-template" type="text/x-jsrender">
	{{for data}}
		<li class="layui-timeline-item">
    		<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
    		<div class="layui-timeline-content layui-text">
			{{if TYPE=='2' }}
				<h3 class="layui-timeline-title">电话</h3>
      			{{if CREATE_CAUSE=='1' || CREATE_CAUSE=='2'}}<p>来源：<span>语音热线(呼入)</p>
					 {{else CREATE_CAUSE=='6' || CREATE_CAUSE=='8'}}<p>来源：<span>语音热线(呼出)</p>
					 {{else }}<p>来源：<span>语音热线(席间呼叫)</p>
				{{/if}}
				<p>手机号码：{{:CUST_PHONE}}</p>
				<p>受理坐席：{{:AGENT_NAME}}</p>
				<p>开始时间：{{:BEGIN_TIME}}</p>
				<p>结束时间：{{:END_TIME}}</p>
				<p>录音记录：<a class="record-history" data-serialid="{{:SERIAL_ID}}"  data-agentname="{{:AGENT_NAME}}" data-custname="{{:CUST_NAME}}" >查看</a>
				<p>通话小结： {{:SUMMARY_FULL_PATH}}
			{{else  TYPE=='4'}}
				<h3 class="layui-timeline-title">未接来电</h3>
      			<p>来源：<span>未接来电</p>
				<p>手机号码：{{:CUST_PHONE}}</p>
				<p>受理坐席：{{:AGENT_NAME}}</p>
				<p>开始时间：{{:BEGIN_TIME}}</p>
				<p>结束时间：{{:END_TIME}}</p>
			{{else  TYPE=='5'}}
				<h3 class="layui-timeline-title">漏话</h3>
      			<p>来源：<span>语音漏话</p>
				<p>手机号码：{{:CUST_PHONE}}</p>
				<p>开始时间：{{:BEGIN_TIME}}</p>
				<p>结束时间：{{:END_TIME}}</p>
			{{else}}
				<h3 class="layui-timeline-title">留言</h3>
      			<p>来源：<span>语音留言</p>
				<p>手机号码：{{:CUST_PHONE}}</p>
				<p>开始时间：{{:BEGIN_TIME}}</p>
				<p>结束时间：{{:END_TIME}}</p>
				<p>录音记录：<a class="record-history" data-serialid="{{:SERIAL_ID}}"  data-agentname="{{:AGENT_NAME}}" data-custname="{{:CUST_NAME}}" >查看</a>
				<p>通话小结： {{:SUMMARY_FULL_PATH}}
				{{if STATE==0 }}
					<p>处理状态: <a href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-danger call_word" data-serialid="{{:SERIAL_ID}}">标记处理</a>
				{{else STATE==1 }}
					<p>处理状态: <span class="layui-btn layui-btn-xs label-success">已经处理</span></p>
				{{/if}}
			{{/if}}
			
    		</div>
  		</li>
	{{/for}}
	{{if data.length==0}}
		<p class="text-center">暂无记录</p>
	{{/if}}
 
</script>


<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.js"></script>
<script src="${ctxPath}/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>

<script type="text/javascript">

var sessionId='${param.sessionId}';
var custId='${param.custId}';
var customerMobilePhone='${param.customerMobilePhone}';
var channelKey='${param.channelKey}';
var custPhone='${param.custPhone}';

$(function(){
	
	loadData();
 	
	$('body').on("click",".record-history",function(){
		var t = $(this);
		var serialId = t.data('serialid');
		var custName = t.data('custname');
		var agentName = t.data('agentname');
		chatRecord2(serialId,custName,agentName);
		
	});
	
	$('body').on("click", ".huihua-xiaojie", function() {
		var t = $(this);
		var serialId = t.data('serialid');
		popup.openTab("/cc-summary/servlet/summaryConfig?action=ToAdd", "小结", {refId: serialId, type:"1"});
	});
	
	$('body').on("click", ".call_word", function() {
		var t = $(this);
		var serialId = t.data('serialid');
		
		layer.confirm(getI18nValue('确定标记为已处理'),{
			btn : [ getI18nValue('确定'), getI18nValue('取消')] ,offset:'40px'},
			function(index, layero) {
				layer.close(index);
				ajax.remoteCall("/cc-callloss/servlet/callNoanswer?action=updateWord", {serialId:serialId}, function(result) {
		  			if(result.state == 1){
		  				loadData();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			},function(index){
				layer.close(index);
			});
	})
});

function loadData() {	
	ajax.daoCall({"params":{sessionId:sessionId,custId:custId,customerMobilePhone:customerMobilePhone,channelKey:channelKey,custPhone:custPhone},"controls":['callRecordHis.list']}, function (result){
		var data = result['callRecordHis.list']  ? result['callRecordHis.list']  : [];
		var callList = result['callRecordHis.list']['data'] ? result['callRecordHis.list']['data'] : [];
		var tmp = $.templates("#chat-his-template");
		
		if(data){
			var html = '<ul class="layui-timeline">';
			html += tmp.render(data);
			html += '</ul>';
		}else{
			var html = tmp.render(data);
		}
		
		$("#timeline").html(html);
	});
}
 

function chatRecord2(serialId){
	var data = {serialId:serialId};
	top.parent.popup.layerShow({type:2,title:'详情',area:['80%','80%'],maxmin:true,full:false,shadeClose:false,moveOut:true,end:function(){
		if(typeof(wavesurfer) != 'undefined' && wavesurfer){
			wavesurfer.destroy()
		}
	}},'${ctxPath}/pages/media/qc-znVoiceResult-info-order.jsp',data);
	
}


	
</script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>