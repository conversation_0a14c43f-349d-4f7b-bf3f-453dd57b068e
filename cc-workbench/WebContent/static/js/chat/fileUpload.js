var fileUploader = (function(){

	function checkfile(fileObj,uploadSetting){
				//校验文件类型
		var filename=fileObj.name.toLowerCase(); 
		var fileType=uploadSetting.fileType;
		if(fileType!=null&&fileType!=undefined&&fileType!=''){
			var fileExtension = filename.substring(filename.lastIndexOf('.') + 1);
			fileExtension=fileExtension.toLowerCase();
			if(fileType.indexOf(fileExtension)==-1){
				alert("上传仅支持格式："+fileType);
				return false;
			}
		}
		//校验文件大小
		var fileMaxSize=uploadSetting.fileMaxSize;
		if(fileMaxSize!=null&&fileMaxSize!=undefined&&fileMaxSize!=''){
			return checkFileSize(fileObj,fileMaxSize,uploadSetting);
		}
		return true;
	}
		
	var isIE = /msie/i.test(navigator.userAgent) && !window.opera; 
	function checkFileSize(target,fileMaxSize,uploadSetting) { 
		var fileSize = 0; 
		if (isIE && !target.files) { 
			target.select(); target.blur();
			var filePath = document.selection.createRange().text;
			var fileSystem = new ActiveXObject("Scripting.FileSystemObject"); 
			if(!fileSystem.FileExists(filePath)){ 
				alert("附件不存在，请重新输入！"); 
				return false; 
			} 
			var file = fileSystem.GetFile(filePath); 
			fileSize = file.Size; 
		} else { 
			fileSize = target.size; 
		}  
		var size = fileSize / 1024; 
		if(size>fileMaxSize){ 
			if(fileMaxSize>=1024){
				alert("文件大小限制"+(fileMaxSize/1024).toFixed(2)+"M内！"); 
			}else{
				alert("文件大小限制"+fileMaxSize+"k内！"); 
			}
			target.value =""; 
			return false; 
		} 
		return true;
	} 

	return {
		count:0,
		init:function(el,uploadSetting,callbackFn){
			var _self = this;
			if(!$.isFunction(callbackFn)) alert('没有绑定上传回调方法');

			var defaultOpts = {
				event:'click',
				callbackFn:''
			}
			var opts = $.extend({}, defaultOpts);
			
			$("body").on(opts.event,el,function(){
				_self.addNewForm(uploadSetting,callbackFn)
			});
		},
		addNewForm:function(uploadSetting,callbackFn){
			var _self = this;

			_self.count++;
			var id = new Date().getTime()+_self.count;
			var html = '<form id="fileForm'+id+'" enctype="multipart/form-data"  method="post" style="display: none"><input id="localfile'+id+'" name="localfile'+id+'" class="filestyle" data-icon="true" data-buttonText="请选择文件"  data-size="sm"	type="file"  accept=""  multiple/></form>';
			$('body').append(html);
			$("#localfile"+id).on('change', function(event) {
				event.preventDefault();
				_self.upload(id,uploadSetting,callbackFn)
			});

			$("#localfile"+id).click();
		},
		upload:function(id,uploadSetting,callbackFn,confirm){
			var fileList = document.getElementById("localfile"+id).files;
			if(fileList.length<=0){
		    	alert("请选择上传的文件!")
		    	return;
			}
			for(var i=0;i<fileList.length;i++){
				var fileObj=fileList[i];
				if(!checkfile(fileObj,uploadSetting)){
					return;
				}
			}
			var formData = new FormData($("#fileForm"+id)[0]); 
			var data=$.extend({},uploadSetting);
			var paramStr=jQuery.param(data);
			var sessionId = $(".chat-box.active").data('sessionId');
			$.ajax({  
		          url: '/online/servlet/fileUpload?action=upload&'+paramStr,  
		          type: 'POST',  
		          data: formData,async: false,cache: false,contentType: false,processData: false,  
		          success: function (result) {
				    	 layer.msg(result.msg||'上传成功',{time:1500,icon:1},function(){
				    		 layer.closeAll('dialog');
					    	 if(result.errno  == 0){
					    		 //popup.layerClose("#fileForm"+id);
							 	 // window[uploadSetting.callback]&&window[uploadSetting.callback](result.data,uploadSetting.args);
							 	 callbackFn&&callbackFn(result.data,sessionId);
					    	 }
				    	 });
		          },error: function (returndata) {  
			             layer.msg('上传失败!'); 
			             layer.closeAll('dialog');
		          },beforeSend:function(){
		        	    layer.msg('正在上传', {icon: 16,time:1000*100,offset:'30px'});
		        	 	// $("#fileForm #backbut,#fileUploadSaveBtn").attr("disabled", true);	
		          },complete:function(){
		        	    layer.closeAll('loading');
		        	    $("fileForm"+id).remove();
				    	//$("#fileForm #backbut,#fileUploadSaveBtn").attr("disabled", false);
		          }  
		     }); 
		}
	}
})();
	