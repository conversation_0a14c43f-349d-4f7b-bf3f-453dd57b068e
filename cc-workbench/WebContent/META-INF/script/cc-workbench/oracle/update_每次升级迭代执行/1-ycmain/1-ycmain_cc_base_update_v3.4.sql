-- 20230317
create table C_KB_PLATEFORM
(
ID varchar(64) not null ,
CODE varchar(100) ,
NAME varchar(200) ,
DESC_ varchar(500) ,
KM_TYPE varchar(50) ,
INF_TYPE varchar(30) ,
INF_IP varchar(30) ,
INF_PORT int default 8080 ,
REQ_ENCODE varchar(20) default 'UTF-8' ,
RESP_ENCODE varchar(20) default 'UTF-8' ,
INF_PARAM varchar(500) ,
EX_JSON varchar(2000) ,
IS_DEFAULT varchar(10) default 'N' ,
UPDATE_ACC varchar(30) ,
UPDATE_TIME varchar(19) ,
CREATE_ACC varchar(30) ,
CREATE_NAME varchar(50) ,
CREATE_DEPT varchar(30) ,
CREATE_DEPT_NAME varchar(100) ,
CREATE_TIME varchar(19) ,
ENT_ID varchar(30) ,
BUSI_ORDER_ID varchar(64) ,
primary key (ID)
);

COMMENT on table C_KB_PLATEFORM is '知识平台配置，配置对接的知识库的平台配置信息';

COMMENT on column C_KB_PLATEFORM.ID is 'ID';
COMMENT on column C_KB_PLATEFORM.CODE is '平台编码';
COMMENT on column C_KB_PLATEFORM.NAME is '平台名称';
COMMENT on column C_KB_PLATEFORM.DESC_ is '平台说明';
COMMENT on column C_KB_PLATEFORM.KM_TYPE is '知识平台类型，cc-km 企业知识库；ai-km 智能知识库；sr-km 视嵘知识库';
COMMENT on column C_KB_PLATEFORM.INF_TYPE is '接口协议，http、https、iservice';
COMMENT on column C_KB_PLATEFORM.INF_IP is '接口IP';
COMMENT on column C_KB_PLATEFORM.INF_PORT is '接口端口';
COMMENT on column C_KB_PLATEFORM.REQ_ENCODE is '接口请求编码，UTF-8，GBK';
COMMENT on column C_KB_PLATEFORM.RESP_ENCODE is '接口响应编码，UTF-8，GBK';
COMMENT on column C_KB_PLATEFORM.INF_PARAM is '接口固定参数';
COMMENT on column C_KB_PLATEFORM.EX_JSON is '扩展配置数据，json';
COMMENT on column C_KB_PLATEFORM.IS_DEFAULT is '是否默认，同一企业，只允许默认一个';
COMMENT on column C_KB_PLATEFORM.UPDATE_ACC is '修改人';
COMMENT on column C_KB_PLATEFORM.UPDATE_TIME is '修改时间';
COMMENT on column C_KB_PLATEFORM.CREATE_ACC is '创建人账号';
COMMENT on column C_KB_PLATEFORM.CREATE_NAME is '创建人名称';
COMMENT on column C_KB_PLATEFORM.CREATE_DEPT is '创建人部门编号';
COMMENT on column C_KB_PLATEFORM.CREATE_DEPT_NAME is '创建人部门名称';
COMMENT on column C_KB_PLATEFORM.CREATE_TIME is '创建时间';
COMMENT on column C_KB_PLATEFORM.ENT_ID is '企业ID';
COMMENT on column C_KB_PLATEFORM.BUSI_ORDER_ID is '企业业务订购ID';

create index IDX_C_KB_PLATEFORM1 on C_KB_PLATEFORM
(
CODE,
ENT_ID
);

create index IDX_C_KB_PLATEFORM2 on C_KB_PLATEFORM
(
ENT_ID,
IS_DEFAULT
);


create table C_KB_CMS_INFO
(
ID varchar(64) not null ,
CLIENT_TYPE varchar(30) ,
CONTENT_TYPE varchar(30) ,
TITLE varchar(100) ,
TITLE2 varchar(100) ,
CONTENT CLOB ,
KEYWORD varchar(500) ,
BAKUP varchar(200) ,
IDX_ORDER int default 1 ,
PIC_URL varchar(200) ,
LINK_URL varchar(200),
CREATE_ACC varchar(30) ,
CREATE_NAME varchar(50) ,
CREATE_DEPT varchar(30) ,
CREATE_DEPT_NAME varchar(100) ,
CREATE_TIME varchar(19),
ENT_ID varchar(30) ,
BUSI_ORDER_ID varchar(64) ,
ENABLE_STATUS varchar(10) default '01' ,
P_ID varchar(64) default '2000' ,
P_ID2 varchar(64) default '2000' ,
P_ID3 varchar(64) default '2000' ,
EX_JSON varchar(2000),
primary key (ID)
);

COMMENT on table C_KB_CMS_INFO is '知识客户端配置，配置知识库客户端展示内容';

COMMENT on column C_KB_CMS_INFO.ID is 'ID';
COMMENT on column C_KB_CMS_INFO.CLIENT_TYPE is '适用的客户端类型 1-12345市民端 2-知识客户端 3-工单市民端';
COMMENT on column C_KB_CMS_INFO.CONTENT_TYPE is '栏目类型 1-市民端核心诉求栏 2-市民端热门服务 3-市民端便民专区 4-市民端诉求说明指南 5-市民端热点知识预览图 6-市民端自助知识、7-市民端核心知识 8-市民端通知公告';
COMMENT on column C_KB_CMS_INFO.TITLE is '名称，标题';
COMMENT on column C_KB_CMS_INFO.TITLE2 is '副标题、非必填';
COMMENT on column C_KB_CMS_INFO.CONTENT is '内容、非必填';
COMMENT on column C_KB_CMS_INFO.KEYWORD is '关键字、非必填';
COMMENT on column C_KB_CMS_INFO.BAKUP is '备注';
COMMENT on column C_KB_CMS_INFO.IDX_ORDER is '序号';
COMMENT on column C_KB_CMS_INFO.PIC_URL is '问题图片URL';
COMMENT on column C_KB_CMS_INFO.LINK_URL is '链接URL';
COMMENT on column C_KB_CMS_INFO.CREATE_ACC is '创建人账号';
COMMENT on column C_KB_CMS_INFO.CREATE_NAME is '创建人名称';
COMMENT on column C_KB_CMS_INFO.CREATE_DEPT is '创建人部门编号';
COMMENT on column C_KB_CMS_INFO.CREATE_DEPT_NAME is '创建人部门名称';
COMMENT on column C_KB_CMS_INFO.CREATE_TIME is '创建时间';
COMMENT on column C_KB_CMS_INFO.ENT_ID is '企业ID';
COMMENT on column C_KB_CMS_INFO.BUSI_ORDER_ID is '企业业务订购ID';
COMMENT on column C_KB_CMS_INFO.ENABLE_STATUS is '启用状态, ENABLE_STATUS 01-启用 02-禁用';
COMMENT on column C_KB_CMS_INFO.P_ID is '父ID一级，预留';
COMMENT on column C_KB_CMS_INFO.P_ID2 is '父ID二级，预留';
COMMENT on column C_KB_CMS_INFO.P_ID3 is '父ID三级，预留';
COMMENT on column C_KB_CMS_INFO.EX_JSON is '扩展参数';


create index IDX_C_KB_CMS_INFO1 on C_KB_CMS_INFO
(
ENT_ID,
CLIENT_TYPE,
CONTENT_TYPE
);


create index IDX_C_KB_CMS_INFO2 on C_KB_CMS_INFO
(
P_ID
);