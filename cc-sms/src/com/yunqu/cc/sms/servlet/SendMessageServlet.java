package com.yunqu.cc.sms.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSON;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.sms.base.CommonLogger;
import com.yunqu.cc.sms.base.QueryFactory;
import com.yunqu.cc.sms.model.NodeTree;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * Title:发送信息类
 * Description:处理信息的发送
 * Company:云趣科技
 * <AUTHOR>
 *
 */
public class SendMessageServlet extends HttpServlet{

	private Logger logger = CommonLogger.getLogger();
	/**
	 * doGet!
	 */
	private static final long serialVersionUID = 1L;
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String id=req.getParameter("id");
		String sql="SELECT GROUP_ID ID,GROUP_NAME NAME,PARENT_ID PID, '0' TYPE FROM " + UserUtil.getRequestUserSchema(req) + ".C_SMS_GROUP WHERE PARENT_ID=? "
				+ " AND EP_CODE = ? AND BUSI_ORDER_ID = ? "
				+ "UNION "
				+ "SELECT ALARM_MEMBER_ID GROUP_ID ,A.NAME,ALARM_GROUP_ID PID ,'1' TYPE FROM " + UserUtil.getRequestUserSchema(req) + ".C_SMS_MEMBER A LEFT JOIN "
				+ "" + UserUtil.getRequestUserSchema(req) + ".C_SMS_MEMBER_GROUP B ON B.ALARM_MEMBER_ID=A.MEMBER_ID WHERE ALARM_GROUP_ID= ? "
				+ " AND A.EP_CODE = ? AND A.BUSI_ORDER_ID = ? ";
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this)+" sql=" + sql.toString() + "");
		}
		EasyQuery query= QueryFactory.getReadQuery();
		try {
			YCUserPrincipal userPrincipal = UserUtil.getUserPrincipal(req);
			String entId = userPrincipal.getEntId();
			String busiOrderId = userPrincipal.getBusiOrderId();
			List<EasyRow> acList = query.queryForList(sql, new Object[]{id,entId,busiOrderId,id,entId,busiOrderId});
			List<NodeTree> res = new ArrayList<NodeTree>();	
			for(EasyRow frame : acList){
				NodeTree tree = new NodeTree();
				tree.setId(frame.getColumnValue("ID"));
				tree.setName(frame.getColumnValue("NAME"));
				tree.setpId(frame.getColumnValue("pid")==null?"":frame.getColumnValue("pid"));
				if("1".equals(frame.getColumnValue("TYPE"))){
					tree.setIsParent("false");
					tree.setOpen("false");
					tree.setType("1");
					tree.setChildren(new ArrayList<NodeTree>());
				}else{
					tree.setType("0");
					tree.setIsParent("true");
					tree.setOpen("false");
					tree.setChildren(new ArrayList<NodeTree>(0));
				}
				
				res.add(tree);
			}
			String str=JSON.toJSONString(res);
			resp.setContentType("text/html; charset=utf-8");

			resp.getWriter().write(str);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"查询失败"+e.getMessage(), e);
		}
		
	}
	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doGet(req, resp);
	}
}
