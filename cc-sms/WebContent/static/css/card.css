.ibox-card-body,
.ibox-card-header,
.ibox-form-label,
.ibox-form-mid,
.ibox-form-select,
.ibox-input-block,
.ibox-input-inline,
.ibox-textarea {
    position: relative
}
*::-webkit-scrollbar {

  width: 6px;

  height: 14px;
}
*::-webkit-scrollbar-thumb {

  border-radius: 2px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.1);
}
*::-webkit-scrollbar-track {

  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
ul{list-style: none;margin: 0;padding: 0;}
.ibox-index{padding: 5px 10px;}
.ibox-card {
    margin-bottom: 15px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0,.ibox-index .05)
}

.ibox-card:last-child {
    /*margin-bottom: 0*/
}

.ibox-card-header {
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px solid #f6f6f6;
    color: #333;
    border-radius: 2px 2px 0 0;
    font-size: 14px
}

.ibox-bg-black,
.ibox-bg-blue,
.ibox-bg-cyan,
.ibox-bg-green,
.ibox-bg-orange,
.ibox-bg-red {
    color: #fff !important
}

.ibox-card-body {
    padding: 10px 15px;
    line-height: 24px;
    overflow: auto;
}

.ibox-card-body .ibox-table {
    margin: 5px 0
}

.ibox-card .ibox-tab {
    margin: 0
}

.ibox-card .swiper-container{
    /*overflow-y: inherit;*/
}
.ibox-card .swiper-slide{overflow: auto;}
.ibox-card .swiper-pagination{
    position: absolute;
    right: 10px;
    bottom: 0px;
}

.ibox-card .ibox-card-header .swiper-pagination-bullets .swiper-pagination-bullet{
    margin: 0 4px;
    width: 10px;height: 10px;
}


.ibox-card-badge,.ibox-index .ibox-card-btn-group,.ibox-index .ibox-card-span-color,.ibox-index .ibox-card-span-icon {
    position: absolute;
    right: 15px;
    top:11px;
}
.ibox-card-span-icon{
    line-height: initial;
    position: absolute;
    right: 15px;
    top: 50%;
    margin-top: 1px;
}
/* tab */
.ibox-card-header .nav-tabs{
    border-bottom: 0;
}
.ibox-card-header .nav-tabs>li {
    float: left;
    margin-bottom: 0;
}

.ibox-card-header .nav-tabs>li>a:hover {
    border-color: #eee #eee #ddd;
    background-color: transparent;
}

.ibox-card-header .nav-tabs>li.active>a,.ibox-index .ibox-card-header .nav-tabs>li.active>a:focus,.ibox-index .ibox-card-header .nav-tabs>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 0; 
    border-bottom: 2px solid transparent;
    border-bottom-color: #3cf;
}

.ibox-card-header .nav-tabs>li>a{
    border: 0; 
    border-bottom: 2px solid transparent;
}

.ibox-card-backlog{

}

.ibox-card-backlog-body:hover {
    background-color: #f2f2f2;
    color: #888;
}

.ibox-card-backlog .ibox-card-backlog-body {
    display: block;
    padding: 10px 15px;
    background-color: #f8f8f8;
    color: #999;
    border-radius: 2px;
    transition: all .3s;
    -webkit-transition: all .3s;
    text-decoration: none;
    cursor: pointer;
}

.ibox-card-backlog-body h3 {
    padding-bottom: 10px;
    font-size: 12px;
    margin: 0;
}

.ibox-card-backlog-body p cite {
    font-style: normal;
    font-size: 30px;
    font-weight: 300;
    color: #337ab7;
}

.col-space10>* {
    padding: 5px;
}
.ibox-card-shortcut{text-align: center;}
.ibox-card-shortcut>.row,.ibox-card-backlog>.row{
    margin: 0 -7px;
}
.ibox-card-shortcut li:hover .ibox-card-icon {
    background-color: #f2f2f2;
}
.ibox-card-shortcut li .ibox-card-icon {
    display: inline-block;
    width: 100%;
    height: 55px;
    line-height: 55px;
    text-align: center;
    border-radius: 2px;
    font-size: 30px;
    background-color: #F8F8F8;
    color: #333;
    transition: all .3s;
    -webkit-transition: all .3s;
}
.ibox-card-shortcut li a{text-decoration: none;cursor: pointer;}
.ibox-card-shortcut li cite {
    position: relative;
    top: 2px;
    display: block;
    color: #666;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    list-style: none;
    font-style: normal;
}

.ibox-index .col-lg-1,.ibox-index .col-lg-10,.ibox-index .col-lg-11,.ibox-index .col-lg-12,.ibox-index .col-lg-2,.ibox-index .col-lg-3,.ibox-index .col-lg-4,.ibox-index .col-lg-5,.ibox-index .col-lg-6,.ibox-index .col-lg-7,.ibox-index .col-lg-8,.ibox-index .col-lg-9,.ibox-index .col-md-1,.ibox-index .col-md-10,.ibox-index .col-md-11,.ibox-index .col-md-12,.ibox-index .col-md-2,.ibox-index .col-md-3,.ibox-index .col-md-4,.ibox-index .col-md-5,.ibox-index .col-md-6,.ibox-index .col-md-7,.ibox-index .col-md-8,.ibox-index .col-md-9,.ibox-index .col-sm-1,.ibox-index .col-sm-10,.ibox-index .col-sm-11,.ibox-index .col-sm-12,.ibox-index .col-sm-2,.ibox-index .col-sm-3,.ibox-index .col-sm-4,.ibox-index .col-sm-5,.ibox-index .col-sm-6,.ibox-index .col-sm-7,.ibox-index .col-sm-8,.ibox-index .col-sm-9,.ibox-index .col-xs-1,.ibox-index .col-xs-10,.ibox-index .col-xs-11,.ibox-index .col-xs-12,.ibox-index .col-xs-2,.ibox-index .col-xs-3,.ibox-index .col-xs-4,.ibox-index .col-xs-5,.ibox-index .col-xs-6,.ibox-index .col-xs-7,.ibox-index .col-xs-8,.ibox-index .col-xs-9 {
    position: relative;
    min-height: 1px;
    padding-right: 7px;
    padding-left: 7px;
}