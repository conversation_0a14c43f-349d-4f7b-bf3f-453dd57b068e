package com.yunqu.cc.statgw.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.statgw.base.Constants;
import com.yunqu.cc.statgw.dao.QcAgentDao;
import com.yunqu.cc.statgw.model.Agent;


public class QcAgentMediaService extends BaseService {
	private QcAgentDao qcAgentDao = new QcAgentDao();
	private Map<String, Agent> agents = new HashMap<String, Agent>();
	private Map<String, String> stIds = new HashMap<String, String>();
	
	public QcAgentMediaService(String stDate,String schema) {
		List<EasyRow> list = qcAgentDao.findAllQcAgent(stDate,schema);
		for(EasyRow row : list) {
			String userAcc = row.getColumnValue("QC_ACC");			
			Agent agent = new Agent();
			agent.setUserAcc(userAcc);
			agent.setUserName(row.getColumnValue("QC_NAME"));
			agent.setDeptCode(row.getColumnValue("DEPT_CODE"));
			agent.setDeptName(row.getColumnValue("DEPT_NAME"));
			agent.setEpCode(row.getColumnValue("EP_CODE"));
			agent.setAreaCode(row.getColumnValue("AREACODE"));
			agent.setAreaName(row.getColumnValue("AREA_NAME"));
			agent.setQcGroup(row.getColumnValue("QC_GROUP"));
			agent.setWorkMonths(row.getColumnValue("ENTRY_MONTHS"));	
			agent.setSchedulingId(row.getColumnValue("SCHEDULING_ID"));
			agent.setSchedulingName(row.getColumnValue("SCHEDULING_NAME"));
			
			agents.put(userAcc, agent);
		}	
	}
	
	
	/**
	 * 按渠道、方向统计质检员的明细记录，生成具体的sql入库语句
	 * 
	 * @param sqlList
	 * @param stAgentId
	 * @param list
	 */
	public void stQcAgentQd(String stDate,String schema) {
		// 删除质检员的明细记录
		qcAgentDao.delQcRecord("C_ST_QCAGENT_QD", stDate, Constants.ST_TYPE_DAY,schema,"02");
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 提取量、监听未完成量
		List<EasyRow> list = qcAgentDao.stQcAgentExtract(stDate,schema);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到提取量、监听未完成量的记录数:" + (list == null ? 0 :list.size()));		
		if(list!=null&&list.size()>0){
			for (EasyRow row : list) {
				String qcAcc = row.getColumnValue("QC_ACC");
				String channelId = row.getColumnValue("CHANNEL_ID");
				String direction = row.getColumnValue("DIRECTION");
				String key = qcAcc + channelId + direction;
				
				Map<String, Object> param = new HashMap<String, Object>();	
				if(map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stAgentId = stIds.get(qcAcc);
					if (StringUtils.isBlank(stAgentId)) {
						stAgentId = qcAgentDao.getQcAgentRecord(qcAcc, stDate, Constants.ST_TYPE_DAY,schema);
						stIds.put(qcAcc, stAgentId);
					}					
					if (StringUtils.isBlank(stAgentId)) {
						stAgentId = IDGenerator.getDefaultNUMID();
						Agent agent = agents.get(qcAcc);
						if(agent == null)
							continue;
						stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY,schema);
						stIds.put(qcAcc, stAgentId);
					}	
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_QCAGENT_ID", stAgentId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					param.put("CHANNEL_NO", channelId);
					param.put("DIRECTION", direction);
				}			
				param.put("EXTRACT_NUM", CommonUtil.parseInt(row.getColumnValue("EXTRACT_NUM")));
				param.put("WAIT_LISTEN_NUM", CommonUtil.parseInt(row.getColumnValue("WAIT_LISTEN_NUM")));
				map.put(key, param);
			}
		}
		
		
		// 监听完成量、监听时长、质检总分、平均分
		list = qcAgentDao.stQcAgentListenM(stDate,schema);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到监听完成量、监听时长、质检总分、平均分的记录数:" + (list == null ? 0 :list.size()));		
		for (EasyRow row : list) {
			String qcAcc = row.getColumnValue("QC_ACC");
			String channelId = row.getColumnValue("CHANNEL_ID");
			String direction = row.getColumnValue("DIRECTION");
			String key = qcAcc + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();	
			if(map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(qcAcc);
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = qcAgentDao.getQcAgentRecord(qcAcc, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agents.get(qcAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_QCAGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
			}		
			int listenNum = CommonUtil.parseInt(row.getColumnValue("LISTEN_NUM"));
			int manualZeroNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_ZERO_NUM"));
			double manualQcScore = CommonUtil.parseDouble(row.getColumnValue("MANUAL_QC_SCORE"));
			double manualQcAvgScore = calcAvg(listenNum - manualZeroNum, manualQcScore);
						
			param.put("LISTEN_NUM", listenNum);
			param.put("LISTEN_SECONDS", CommonUtil.parseInt(row.getColumnValue("LISTEN_SECONDS")));
			param.put("MANUAL_ZERO_NUM", manualZeroNum);
			param.put("MANUAL_QC_SCORE", manualQcScore);
			param.put("MANUAL_QC_AVG_SCORE", manualQcAvgScore);
			map.put(key, param);
		}
		
		// 申诉量
		list = qcAgentDao.stQcAgentComplaintM(stDate,schema);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到申诉量的记录数:" + (list == null ? 0 :list.size()));		
		for (EasyRow row : list) {
			String qcAcc = row.getColumnValue("QC_ACC");
			String channelId = row.getColumnValue("CHANNEL_ID");
			String direction = row.getColumnValue("DIRECTION");
			String key = qcAcc + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();	
			if(map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(qcAcc);
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = qcAgentDao.getQcAgentRecord(qcAcc, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agents.get(qcAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_QCAGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
			}			
			param.put("COMPLAINT_NUM", CommonUtil.parseInt(row.getColumnValue("COMPLAINT_NUM")));
			map.put(key, param);
		}
		
		// 审核通过量
		list = qcAgentDao.stQcAgentComPassM(stDate,schema);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ stDate + "，查询到审核通过量的记录数:" + (list == null ? 0 :list.size()));		
		for (EasyRow row : list) {
			String qcAcc = row.getColumnValue("QC_ACC");
			String channelId = row.getColumnValue("CHANNEL_ID");
			String direction = row.getColumnValue("DIRECTION");
			String key = qcAcc + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();	
			if(map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(qcAcc);
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = qcAgentDao.getQcAgentRecord(qcAcc, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				if (StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agents.get(qcAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY,schema);
					stIds.put(qcAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_QCAGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
			}			
			param.put("COMPLAINT_PASS_NUM", CommonUtil.parseInt(row.getColumnValue("COMPLAINT_PASS_NUM")));
			map.put(key, param);
		}
		try {
			qcAgentDao.map2Save(sqlList, map, ""+schema+".C_ST_QCAGENT_QD");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "全媒体统计质检员的工作量 ,入库记录数:" + sqlList.size());
			qcAgentDao.batchExecSql(qcAgentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 全媒体统计质检员的工作量 ,入库时失败", e);
		}
	}

	
	/**
	 * 添加质检员的汇总记录
	 * @param sqlList
	 * @param agent
	 * @param stAgentId
	 * @param stDate
	 * @param stType
	 */
	public void stRecord(ArrayList<String> sqlList, Agent agent, String stAgentId, String stDate, String stType,String schema) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("ID", stAgentId);
		param.put("QCAGENT_ACC", agent.getUserAcc());
		param.put("QCAGENT_NAME", agent.getUserName());
		param.put("DEPT_CODE", agent.getDeptCode());
		param.put("DEPT_NAME", agent.getDeptName());
		param.put("AREA_CODE", agent.getAreaCode());
		param.put("AREA_NAME", agent.getAreaName());
		param.put("EP_CODE", agent.getEpCode());
		param.put("ST_TYPE", stType);
		param.put("ST_DATE", stDate);
		param.put("SCHEDULING_ID", agent.getSchedulingId());
		param.put("SCHEDULING_NAME", agent.getSchedulingName());
		param.put("QC_GROUP_ID", agent.getQcGroup());
		param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		param.put("WORK_MONTHS", agent.getWorkMonths());
		
		qcAgentDao.mapToSave(sqlList, param, ""+schema+".C_ST_QCAGENT");
	}
	
}
