package com.yunqu.cc.statgw.dao;

import java.util.List;

import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyRow;

import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.statgw.base.QueryFactory;


public class ChannelDao extends BaseDao {
	/**
	 * 找出所有的渠道
	 * @return
	 */
	public List<EasyRow> findAllChannel(String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			DBTypes type=this.getQueryHelper().getTypes();
			if(DBTypes.MYSQL==type){
				sql.append("select ID,CODE,NAME,EP_CODE from "+schema+".V_CF_CHANNEL ");
				sql.append("where SESSION_TYPE=CONVERT('02' USING utf8) COLLATE utf8_unicode_ci and CODE!='99'");
			}else{
				sql.append("select ID,CODE,NAME,EP_CODE from "+schema+".V_CF_CHANNEL ");
				sql.append("where SESSION_TYPE='02' and CODE!='99'");
			}
			
			logger.debug(CommonUtil.getClassNameAndMethod(this) + "找出所有的渠道:" + sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(), null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "找出所有的渠道失败.", e);
		}
		return null;
	}
	
	
	/**
	 * 删除渠道的汇总记录
	 * @param tableName
	 * @param stDate
	 * @param stType
	 */
	public void delChannelRecord(String tableName, String stDate, String stType,String schema) {
		try {
			String sql = "delete from " + schema+"."+tableName + " where ST_CHANNEL_ID in (select ID from "+schema+".C_ST_CHANNEL where ST_DATE='"
					+ stDate + "' and ST_TYPE='" + stType + "')";
			logger.info(CommonUtil.getClassNameAndMethod(this) + "删除渠道的汇总记录:" + sql);
			queryHelper.execute(sql, null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除渠道的汇总记录失败.", e);
		}
	}
	
	
	/**
	 * 获取渠道汇总记录
	 * 
	 * @param channelCode
	 * @param stDate
	 * @param stType
	 * @return
	 */
	public String getChannelRecord(String channelCode, String stDate, String stType,String schema) {
		String id = "";
		
		try {
			String sql = "select ID from "+schema+".C_ST_CHANNEL where ST_TYPE='" + stType + "' and ST_DATE='" + stDate
					+ "' and CHANNEL_CODE='" + channelCode + "'";			
			id = queryHelper.queryForString(sql, null);					
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "获取渠道汇总记录失败", e);
		}
		return id;
	}
	
	
	/**
	 * 根据技能组统计渠道的会话量、客户量
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stChannelSkillCallTotal(String beginTime, String endTime,String schema) {
		try {		
			StringBuffer sql = new StringBuffer();
			sql.append("select CHANNEL_ID,GROUP_ID,ENT_ID,LEVEL_CODE,");
			sql.append("count(1) RECORD_NUM, count(distinct CUSTOMER_ACC) CUST_NUM,");
			sql.append("SUM(CASE WHEN IN_TYPE = '01' THEN 1 ELSE 0 END) IN_NUM,");
			sql.append("SUM(CASE WHEN IN_TYPE = '01' OR IN_TYPE = '02' THEN 1 ELSE 0 END) TOTAL ");
			sql.append("from "+schema+".V_MEDIA_RECORD ");
			sql.append("where ANSWER_TIME>='").append(beginTime).append("' ");
			sql.append("and ANSWER_TIME<'").append(endTime).append("' ");
			sql.append("group by CHANNEL_ID,GROUP_ID,ENT_ID,LEVEL_CODE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的会话量、客户量:"+ sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的会话量、客户量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据技能组统计渠道的人工考评量、人工考评分
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stChannelSkillManualQc(String beginTime, String endTime,String schema) {
		try {		
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.CHANNEL_ID,t2.GROUP_ID,LEVEL_CODE,");
			sql.append("count(1) MANUAL_QC_NUM,");
			sql.append("sum(case when t1.SCORE=0 then 1 else 0 end) MANUAL_ZERO_NUM,");
			sql.append("sum(t1.SCORE) MANUAL_QC_SCORE ");
			sql.append("from "+schema+".CC_QC_RESULT t1 ");
			sql.append("left join "+schema+".V_MEDIA_RECORD t2 on t1.SERIAL_ID=t2.ID ");
			sql.append("where t1.CALL_TYPE='02' and t1.QC_TIME>='").append(beginTime).append("' ");
			sql.append("and t1.QC_TIME<'").append(endTime).append("' ");
			sql.append("group by t2.CHANNEL_ID,t2.GROUP_ID,LEVEL_CODE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的人工考评量、人工考评分:"+ sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的人工考评量、人工考评分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据技能组统计渠道的自动考评量、自动考评分
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stChannelSkillAutoQc(String beginTime, String endTime) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.CHANNEL_ID,t2.GROUP_ID,");
			sql.append("count(1) AUTO_QC_NUM,");
			sql.append("sum(t1.AUTO_QC_SCORE) AUTO_QC_SCORE,");
			sql.append("round(avg(t1.AUTO_QC_SCORE),2) AUTO_QC_AVG_SCORE ");
			sql.append("from C_PF_QC_RECORD t1 left join V_PF_SESSION_RECORD t2 ");
			sql.append("on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("where t1.SESSION_TYPE='02' and t1.AUTO_QC_TIME>='").append(beginTime).append("' ");
			sql.append("and t1.AUTO_QC_TIME<'").append(endTime).append("' ");
			sql.append("group by t2.CHANNEL_ID,t2.GROUP_ID");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的自动考评量、自动考评分:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的自动考评量、自动考评分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据技能组统计渠道的扩展属性
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stChannelSkillExtend(String beginTime, String endTime,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t1.CHANNEL_ID,t1.GROUP_ID,t1.LEVEL_CODE,");
			sql.append("sum(case when t1.HANGUP_TYPE='04' then 1 else 0 end) TRANS_OUT_NUM,");
			sql.append("sum(case when t1.IN_TYPE='03' then 1 else 0 end) TRANS_IN_NUM,");
			sql.append("sum(t1.LENS) SERV_LEN,");
			sql.append("round(avg(t1.LENS),2) AVG_SERV_LEN,");
			sql.append("max(t1.LENS) MAX_SERV_LEN,");
			sql.append("sum(t2.FIRST_REPLY) FIRST_REPLY_LEN,");
			sql.append("round(avg(t2.FIRST_REPLY),2) AVG_FIRST_REPLY_LEN,");
			sql.append("sum(t2.TOTAL_REPLY_SECONDS) REPLY_NUM,");
			sql.append("round(avg(t2.AVG_REPLY),2) AVG_REPLY_NUM,");
			sql.append("sum(0) SH_ORDER_NUM,");
			sql.append("sum(t2.CONSULT_ORDER_NUM) ZX_ORDER_NUM,");
			/*sql.append("sum(case when t1.SATISF_NAME='非常满意' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='满意' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='一般' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='对处理结果不满意' then 1 else 0 end) UNSATIS_RESULT_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='对坐席服务不满意' then 1 else 0 end) UNSATIS_SERVICE_NUM,");
			sql.append("sum(case when t1.SATISF_NAME is not null then 1 else 0 end) EVAL_NUM,");*/
			
			sql.append("sum(case when t1.SATISF_CODE='1' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_CODE='2' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_CODE='3' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when t1.SATISF_CODE='5' then 1 else 0 end) UNSATIS_RESULT_NUM,");
			sql.append("sum(case when t1.SATISF_CODE='4' then 1 else 0 end) UNSATIS_SERVICE_NUM,");
			sql.append("sum(case when t1.SATISF_CODE='1' OR SATISF_CODE='2' OR SATISF_CODE='3' OR SATISF_CODE='4' OR SATISF_CODE='5'  then 1 else 0 end) EVAL_NUM,");		
			
			sql.append("sum(t2.CUST_MSG_NUM) CUST_MSG_NUM,");
			sql.append("sum(t2.AGENT_MSG_NUM) AGENT_MSG_NUM,");
			sql.append("sum(t2.THIRTY_SECONDS_REPLY_NUM) THIRTY_REPLY_NUM,");
			sql.append("sum(t2.REPLY_NUM) TOTAL_REPLY, ");
			sql.append("sum(case when SLOW_REPLY_NUM>=3 then 1 else 0 end) SLOW_REPLY_NUM ");			
			sql.append("from "+schema+".V_MEDIA_RECORD t1 left join "+schema+".C_PF_MEDIA_RECORD_EX t2 ");
			sql.append("on t1.ID=t2.SESSION_RECORD_ID ");
			sql.append("where t1.ANSWER_TIME>='").append(beginTime).append("' ");
			sql.append("and t1.ANSWER_TIME<'").append(endTime).append("' ");
			sql.append("group by t1.CHANNEL_ID,t1.GROUP_ID,t1.LEVEL_CODE ");
			
			// logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的扩展属性:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的扩展属性失败.",e);
		}
		return null;
	}
	
	
	
	/**
	 * 根据技能 全媒体接入记 渠道的扩展属性
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stChannelSkill(String beginTime, String endTime,String schema) {	
		try {
			StringBuffer sql = new StringBuffer();
			DBTypes type = QueryFactory.getReadQuery().getTypes();
			//统计类型
			String ifNull = "IFNULL";
			if(DBTypes.ORACLE == type){
				ifNull = "nvl";
			}
			sql.append("select T1.ENT_ID,T3.CODE CHANNEL_ID,T2.LEVEL_CODE,T1.SKILL_GROUP_ID GROUP_ID,COUNT(1) IN_NUM,SUM(T1.IS_IN_ROBOT) IN_ROBOT_NUM,SUM(case when (T1.CLEAR_CAUSE=1 or T1.CLEAR_CAUSE=2) then 1 else 0 end) ROBOT_DONE_NUM,");
			//sql.append("select T3.CODE CHANNEL_ID,T1.SKILL_GROUP_ID GROUP_ID,COUNT(1) IN_NUM,SUM(T1.IS_IN_ROBOT) IN_ROBOT_NUM,SUM(case when (T1.CLEAR_CAUSE=1 or T1.CLEAR_CAUSE=2) then 1 else 0 end) ROBOT_DONE_NUM,");
			sql.append("SUM(case when (T1.CLEAR_CAUSE=3 or T1.CLEAR_CAUSE=4 or T1.CLEAR_CAUSE=5 or T1.CLEAR_CAUSE=6 or T1.CLEAR_CAUSE=7) then 1 else 0 end) REQ_AGENT_NUM,");
			sql.append("SUM(T1.IS_IN_QUEUE) IN_QUEUE_NUM,SUM(case when T1.CLEAR_CAUSE=3 then 1 else 0 end) QUETE_TIMEOUT_NUM,");
			sql.append("SUM(case when T1.CLEAR_CAUSE=4 then 1 else 0 end) QUETE_GIVEUP_NUM,");
			sql.append("SUM(case when (T1.CLEAR_CAUSE=6 or T1.CLEAR_CAUSE=7) then 1 else 0 end) IN_WORD_NUM,");
			if(DBTypes.ORACLE == type){
				sql.append(ifNull+"(SUM(ceil((To_date(T1.ROBOT_END_TIME, 'yyyy-mm-dd hh24-mi-ss') - To_date(T1.ROBOT_START_TIME , 'yyyy-mm-dd hh24-mi-ss')) * 24 * 60 * 60)),0) ROBOT_SERVICE_LEN,");
				sql.append(ifNull+"(SUM(ceil((To_date(T1.QUEUE_END_TIME, 'yyyy-mm-dd hh24-mi-ss') - To_date(T1.QUEUE_START_TIME , 'yyyy-mm-dd hh24-mi-ss')) * 24 * 60 * 60)),0) QUEUE_LEN ");
			}else{
				sql.append(ifNull+"(SUM(TIMESTAMPDIFF(SECOND,T1.ROBOT_START_TIME,T1.ROBOT_END_TIME)),0) ROBOT_SERVICE_LEN,");
				sql.append(ifNull+"(SUM(TIMESTAMPDIFF(SECOND,T1.QUEUE_START_TIME,T1.QUEUE_END_TIME)),0) QUEUE_LEN ");
			}
			
			sql.append("from "+schema+".CC_MEDIA_ACCESS_RECORD T1 LEFT JOIN "+schema+".CC_MEDIA_RECORD T2 ON T1.CHAT_SESSION_ID=T2.SERIAL_ID ");
			
			sql.append(" left join "+schema+".v_cf_channel T3 ON T3.ID = T1.CHANNEL_ID ");
			
			sql.append("where T1.START_TIME>='").append(beginTime).append("' ");
			sql.append("and T1.END_TIME<'").append(endTime).append("' ");
			sql.append("and T1.ENT_ID IS NOT NULL ");
			sql.append("group by T1.ENT_ID,T3.CODE,T1.SKILL_GROUP_ID,T2.LEVEL_CODE");
			//sql.append("group by T3.CODE,T1.SKILL_GROUP_ID");
			
			logger.info(CommonUtil.getClassNameAndMethod(this) + "stChannelSkill根据技能 全媒体接入记 渠道的扩展属性：" + sql.toString());
			return queryHelperRead.queryForList(sql.toString(), new Object[]{});

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的 全媒体接入记 录属性失败.",e);
		}
		return null;
	}
	
	
	
	/**
	 * 根据技能组统计渠道的处理留言数
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public  List<EasyRow> stChannelSkillMess(String beginTime, String endTime,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select CHANNEL_KEY,GROUP_ID,count(1) HAND_LMSG_NUM,LEVEL_CODE ");
			sql.append("from "+schema+".CC_MEDIA_WORD ");
			sql.append("where HANDLE_TIME>='").append(beginTime).append("' ");
			sql.append("and HANDLE_TIME<'").append(endTime).append("' and STATE=1 ");
			sql.append("group by CHANNEL_KEY,GROUP_ID,LEVEL_CODE");
			
			// logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的处理留言数:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的处理留言数失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据技能组统计渠道的接入量、接通量、等待时长、放弃时长、放弃量
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public  List<EasyRow> stChannelSkillWait(String beginTime, String endTime,String schema) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select T1.CHANNEL_ID,T1.GROUP_ID,count(1) IN_NUM,T2.LEVEL_CODE,");
			sql.append("sum(case when T1.END_CAUSE=0 then 1 else 0 end) ANSWER_NUM,");
			sql.append("sum(case when T1.END_CAUSE=0 then T1.QUEUE_STAY_TIME else 0 end) TOTAL_WAIT_LEN,");
			sql.append("max(case when T1.END_CAUSE=0 then T1.QUEUE_STAY_TIME else 0 end) MAX_WAIT_LEN,"); 
			sql.append("sum(case when T1.END_CAUSE=2 then 1 else 0 end) GIVEUP_NUM,");
			sql.append("sum(case when T1.END_CAUSE=2 then T1.QUEUE_STAY_TIME else 0 end) TOTAL_GIVEUP_LEN ");
			sql.append("from "+schema+".CC_MEDIA_QUEUE T1 LEFT JOIN  "+schema+".CC_MEDIA_RECORD T2 ON T1.SERIAL_ID=T2.SERIAL_ID ");
			sql.append("where T1.QUEUE_TIME>='").append(beginTime).append("' ");
			sql.append("and T1.QUEUE_TIME<'").append(endTime).append("' ");
			sql.append("group by T1.CHANNEL_ID,T1.GROUP_ID,T2.LEVEL_CODE ");

			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的接入量、接通量、等待时长、放弃时长、放弃量:" + sql.toString());
			logger.info(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的接入量、接通量、等待时长、放弃时长、放弃量:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据技能组统计渠道的接入量、接通量、等待时长、放弃时长、放弃量失败.",e);
		}
		return null;
	}
	
}
