package com.yunqu.cx.monitor.base;

import java.io.BufferedReader;
import java.io.IOException;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.core.Globals;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.sso.impl.YCUserPrincipal;
public abstract class AppBaseServlet extends EasyBaseServlet { 

	private static final long serialVersionUID = 1L;
    
	protected EasyCache cache = CacheManager.getMemcache();
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_WIRTE_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}
	protected YCUserPrincipal getUserPrincipal(){ 
		return (YCUserPrincipal)this.getRequest().getUserPrincipal();
	}

	protected String getBusiOrderId() {
		YCUserPrincipal principal = (YCUserPrincipal) this.getRequest().getUserPrincipal();
		return principal.getBusiOrderId();
	}
	
	public String getResEntId() {
		YCUserPrincipal principal = (YCUserPrincipal) this.getRequest().getUserPrincipal();
		return principal.getResEntId();
	}

	protected String getBusiId() {
		YCUserPrincipal principal = (YCUserPrincipal) this.getRequest().getUserPrincipal();
		return principal.getBusiId();
	}
	protected String getUserId(){
		YCUserPrincipal  principal  = getUserPrincipal();
		return principal.getUserId();
	}

	@Override
	protected String getResId() {
		return null;
	}
	
	public String getDbName(){
		YCUserPrincipal  principal  = getUserPrincipal();
		return principal.getSchemaName();
	}
	
	protected String getUserName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getUserName();
	}
	
	protected String getTableName(String tableName){
		String dbName=getDbName();
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	
	public String getEntId(){
		YCUserPrincipal  principal  = getUserPrincipal();
		return principal.getEntId();
	}
	
	public static String getFileServerRootDir(String basePathKey) {
	    String defaultPath = ServerContext.getProperties("G_EFS_DIR", Globals.FILE_SERVER_DIR);
	    if (StringUtils.notBlank(basePathKey))
	      return ServerContext.getProperties(basePathKey, defaultPath); 
	    return defaultPath;
	  }
	  
	  public static String getFileServerRootDir() {
	    return getFileServerRootDir(null);
	  }
	  
	  public String getFileURL(String filePath) {
	    if (filePath.toLowerCase().startsWith("http"))
	      return filePath; 
	    String urlPrefix = ServerContext.getProperties("file-server-url", "/easitline-fileserver/fileview/");
	    String path = String.valueOf(urlPrefix) + filePath;
	    return path.replaceAll("//", "/");
	  }
	  
	  /**
		 * 从请求体里获取数据
		 * @param request
		 * @return 请求json
		 */
		public String getRequestStr(HttpServletRequest request){
			StringBuilder dataBui = new StringBuilder(); //
			String line = null;
			BufferedReader reader = null;
			
			try{
			    reader = request.getReader();
			    while ((line = reader.readLine()) != null){
			    	dataBui.append(line);
			    }
			    //获取数据
			    StringBuilder logBui = new StringBuilder();
			    logBui.append("[MainServlet.getRequestStr] 获取请求数据").append(dataBui);
//			    CommonLogger.logger.info(logBui.toString());
			}catch(Exception e ){
//				CommonLogger.logger.error("[MainServlet.getRequestStr] error:"+e.getMessage(),e);
				return null;
			}finally{
			    try {
					reader.close();
				} catch (IOException e) {
				}
			    reader = null;
			}
			return dataBui.toString();
		}
		
		/**
		 * 操作权限
		 * @param resId
		 * @return
		 */
		protected boolean isRes(String ...resIds){
			for (String resId : resIds) {
				if(getUserPrincipal().isResource(resId)){
					return false;
				}
			}
			return true;
		}

}
