package com.yunqu.yc.center.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;

@WebObject(name="label")
public class LabelDao extends AppDaoContext {
	
	@WebControl(name="labelOrderList",type=Types.LIST)
	public JSONObject labelOrderList(){
		EasySQL sql = this.getEasySQL("select DISTINCT t2.* from CC_LABEL_ORDER t1,CC_BD_LABEL_ITEM t2 where t1.ITEM_ID = t2.ITEM_ID and t1.LABEL_ID = t2.LABEL_ID");
		sql.append(param.getString("entId"), " and t1.ENT_ID = ?",false);
		sql.append(param.getString("labelId"), " and t2.LABEL_ID = ?");
		sql.appendLike(param.getString("itemName"), " and ITEM_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 定制标签
	 * @return
	 */
	@WebControl(name="labelList1", type=Types.TEMPLATE)
	public JSONObject labelList1(){
		JSONArray array=new JSONArray();
		EasyQuery query = this.getQuery();
		JSONObject cache = new JSONObject();//数据缓存
		try {
			EasySQL sql = this.getEasySQL("select t1.*,t3.LABEL_ID,t3.LABEL_NAME,t3.LABEL_DESC from CC_BD_LABEL_ITEM t1,CC_BD_LABEL t3 where t1.LABEL_ID = t3.LABEL_ID");
			sql.append(1, "and t3.LABEL_SRC = ?");
			sql.append(" order by t1.LABEL_ID,t1.P_ITEM_ID,t1.ITEM_ID");
			List<Map<String, String>> labelList = query.queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
			List<Map<String, String>> orderList = query.queryForList("select ITEM_ID,LABEL_ID,P_ITEM_ID from " +
					"CC_LABEL_ORDER " +
					"where ENT_ID = ?", new Object[]{param.getString("pk")},new MapRowMapperImpl());
			JSONObject label = null;   //大标签
			JSONArray itemList = null; //大标签中的标签子集
			for (Map<String, String> map : labelList) {
				if(cache.containsKey(map.get("LABEL_ID"))){//从缓存中获取标签
					label = cache.getJSONObject(map.get("LABEL_ID"));
					itemList = label.getJSONArray("items");
				}else{
					label = new JSONObject();
					itemList = new JSONArray();
					label.put("LABEL_ID", map.get("LABEL_ID"));
					label.put("LABEL_NAME", map.get("LABEL_NAME"));
					label.put("LABEL_DESC", map.get("LABEL_DESC"));
					label.put("LABEL_TYPE", map.get("LABEL_TYPE"));
				}
				map.put("ORDER_FLAG", "0");
				for(int j = 0; j < orderList.size(); j++){
					if(map.get("ITEM_ID").equals(orderList.get(j).get("ITEM_ID"))
							&&map.get("LABEL_ID").equals(orderList.get(j).get("LABEL_ID"))
							&&map.get("P_ITEM_ID").equals(orderList.get(j).get("P_ITEM_ID"))){
						map.put("ORDER_FLAG", "1");
					}
				}
				itemList.add(map);
				label.put("items", itemList);
				cache.put(map.get("LABEL_ID"), label);
			}
			orderList = null;
			labelList = null;
		    //将缓存的JSONObject对象转化成JSONArray对象，输出需要
			if(cache.size() > 0){
				for(String key : cache.keySet()){
					array.add(cache.getJSONObject(key));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(array);
	}
	
	/**
	 * 通用标签
	 * @return
	 */
	@WebControl(name="labelList", type=Types.TEMPLATE)
	public JSONObject labelList(){
		JSONArray array=new JSONArray();
		EasyQuery query = this.getQuery();
		JSONObject cache = new JSONObject();//数据缓存
		try {
			EasySQL sql = this.getEasySQL("select t1.*,t3.LABEL_ID,t3.LABEL_NAME,t3.LABEL_DESC from CC_BD_LABEL_ITEM t1,CC_BD_LABEL t3 where t1.LABEL_ID = t3.LABEL_ID");
			sql.append(0, "and t3.LABEL_SRC = ?");
			sql.append(" order by t1.LABEL_ID,t1.P_ITEM_ID,t1.ITEM_ID");
			List<Map<String, String>> labelList = query.queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
			List<Map<String, String>> orderList = query.queryForList("select ITEM_ID,LABEL_ID,P_ITEM_ID from " +
					"CC_LABEL_ORDER " +
					"where ENT_ID = ?", new Object[]{param.getString("pk")},new MapRowMapperImpl());
			JSONObject label = null;   //大标签
			JSONArray itemList = null; //大标签中的标签子集
			for (Map<String, String> map : labelList) {
				if(cache.containsKey(map.get("LABEL_ID"))){//从缓存中获取标签
					label = cache.getJSONObject(map.get("LABEL_ID"));
					itemList = label.getJSONArray("items");
				}else{
					label = new JSONObject();
					itemList = new JSONArray();
					label.put("LABEL_ID", map.get("LABEL_ID"));
					label.put("LABEL_NAME", map.get("LABEL_NAME"));
					label.put("LABEL_DESC", map.get("LABEL_DESC"));
					label.put("LABEL_TYPE", map.get("LABEL_TYPE"));
				}
				map.put("ORDER_FLAG", "0");
				for(int j = 0; j < orderList.size(); j++){
					if(map.get("ITEM_ID").equals(orderList.get(j).get("ITEM_ID"))
							&&map.get("LABEL_ID").equals(orderList.get(j).get("LABEL_ID"))
							&&map.get("P_ITEM_ID").equals(orderList.get(j).get("P_ITEM_ID"))){
						map.put("ORDER_FLAG", "1");
					}
				}
				itemList.add(map);
				label.put("items", itemList);
				cache.put(map.get("LABEL_ID"), label);
			}
			orderList = null;
			labelList = null;
		    //将缓存的JSONObject对象转化成JSONArray对象，输出需要
			if(cache.size() > 0){
				for(String key : cache.keySet()){
					array.add(cache.getJSONObject(key));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(array);
	}
	
	@WebControl(name="labelDict", type=Types.DICT)
	public JSONObject labelDic(){
		return this.getDictByQuery("select LABEL_ID,LABEL_NAME from CC_BD_LABEL", new Object[]{});
	}
	
}
