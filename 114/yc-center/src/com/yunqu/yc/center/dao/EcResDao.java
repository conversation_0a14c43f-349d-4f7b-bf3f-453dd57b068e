package com.yunqu.yc.center.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;

/**
 * 客服资源管理
 * 
 * <AUTHOR>
 */
@WebObject(name = "EcResDao")
public class EcResDao extends AppDaoContext {

	@WebControl(name = "list", type = Types.LIST)
	public JSONObject list() {
		EasySQL sql = this.getEasySQL("select * from CC_EC_RES where 1=1");
		sql.append(param.getString("entId"), " and ENT_ID = ?");
		sql.appendLike(param.getString("resName"), " and RES_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name = "record", type = Types.RECORD)
	public JSONObject record() {
		return queryForRecord(new EasyRecord("CC_EC_RES", "RES_ID").setPrimaryValues(param.getString("ecRes.RES_ID")));
	}

	/**
	 * 获取父资源字典
	 */
	@WebControl(name = "pResDict", type = Types.DICT)
	public JSONObject pResDict() {
		return this.getDictByQuery("select RES_ID,RES_ID from CC_EC_RES where P_RES_ID = ?", new Object[] { "2000" });
	}

	@WebControl(name = "getObject", type = Types.RECORD)
	public JSONObject getObject() {
		String pk = param.getString("pk");
		EasyRecord record = new EasyRecord("CC_EC_RES", "RES_ID").setPrimaryValues(pk);
		JSONObject jsonObject = queryForRecord(record);
		return jsonObject;
	}

	@WebControl(name = "resList", type = Types.LIST)
	public JSONObject resList() {
		EasySQL sql = this.getEasySQL("SELECT *  FROM CC_EC_RES   WHERE  1=1  ");
		sql.append(this.param.getString("resId"), " and P_RES_ID = ? ");
		sql.appendLike(this.param.getString("condition"), " and RES_NAME like ? ");
		sql.append(this.param.getString("entId"), "and OWNER_ENT_ID = ?");
		sql.append(" ORDER BY IDX_ORDER ASC");
		return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
	}

	/**
	 * 资源树
	 */
	@WebControl(name = "getResTree", type = Types.TREE)
	public JSONObject getResTree() {
		String entId = param.getString("entId");
		String sql = "SELECT *  FROM CC_EC_RES order by idx_order";
		if(StringUtils.isNotBlank(entId)){
			sql = "select t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t2.ENT_ID,t1.OWNER_ENT_ID from CC_EC_RES t1 left join CC_EC_ENT_RES t2 on t1.RES_ID = t2.RES_ID and t2.ENT_ID = '"+entId+"' where t1.RES_STATE = 0 and t1.OWNER_ENT_ID in ('0','"+entId+"')  order by t1.IDX_ORDER";
		}
		JSONArray arr = new JSONArray();
		JSONObject root = new JSONObject();
		root.put("resId", "2000");
		root.put("resName", "eportal资源信息");
		root.put("name", "eportal资源信息");
		root.put("open", "true");

		try {
			List<JSONObject> list = this.getQuery().queryForList(sql, null, new JSONMapperImpl());

			for (JSONObject res : list) {
				if (!"2000".equals(res.getString("P_RES_ID"))) {
					continue;
				}				
				String resName = res.getString("RES_NAME") + ("0".equals(res.getString("OWNER_ENT_ID")) ? "":"(私有)");
				JSONObject _json = new JSONObject();
				_json.put("resId", res.getString("RES_ID"));
				_json.put("resName", resName);
				_json.put("name", resName);
				_json.put("open", "true");
				_json.put("checked", StringUtils.isBlank(res.getString("ENT_ID")) ? false : true);
				this.addChildNode(_json, res.getString("RES_ID"), list, 1);
				arr.add(_json);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		if (arr.size() > 0) {
			root.put("children", arr);
		}
		return getTree(root);
	}

	private void addChildNode(JSONObject jsonObject, String presId, List<JSONObject> list, int level) {
		JSONArray arr = new JSONArray();
		for (JSONObject res : list) {
			if (presId.equals(res.getString("P_RES_ID"))) {
				String resName = res.getString("RES_NAME") + ("0".equals(res.getString("OWNER_ENT_ID")) ? "":"(私有)");
				JSONObject _json = new JSONObject();
				_json.put("resId", res.getString("RES_ID"));
				_json.put("resName", resName);
				_json.put("name", resName);
				_json.put("checked", StringUtils.isBlank(res.getString("ENT_ID")) ? false : true);
				if (level < 3) {
					_json.put("open", "true");
				}

				this.addChildNode(_json, res.getString("RES_ID"), list, level++);
				arr.add(_json);
			}
		}
		if (arr.size() > 0) {
			jsonObject.put("children", arr);
		}
	}
	
	 /**
	 * 企业资源
	 */
	@WebControl(name="entResList",type=Types.TEMPLATE)
	public JSONObject entResList(){
		String entId = param.getString("entId");
		JSONArray array=new JSONArray();
		try {
			String sql = "select t1.RES_ID,t1.RES_NAME,t2.ENT_ID from CC_EC_RES t1 left join cc_ec_ent_res t2 on t1.RES_ID = t2.RES_ID and t2.ENT_ID = ? where 1=1 and t1.P_RES_ID = ? order by t1.IDX_ORDER asc,t1.RES_ID desc";
			List<Map<String, String>> list = this.getQuery().queryForList(sql, new Object[]{entId, "2000"}, new MapRowMapperImpl());
			for(Map<String,String> map:list){
				JSONObject jsonObject=new JSONObject();
				String resId=map.get("RES_ID");
				jsonObject.put("resId", resId); 
				jsonObject.put("resName", map.get("RES_NAME"));
				jsonObject.put("checked", map.get("ENT_ID") == null ? "":map.get("ENT_ID"));
				jsonObject.put("entId", entId);
				List<Map<String, String>> list2=this.getQuery().queryForList(sql, new Object[]{entId, resId},new MapRowMapperImpl());
				jsonObject.put("list", list2);
				array.add(jsonObject);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(array);
	}
	
	/**
	 * 获取管理员
	 */
	@WebControl(name="getEcAdmin",type=Types.RECORD)
	public JSONObject getEcAdmin(){
		EasySQL sql = this.getEasySQL("select * from CC_EC_USER where 1=1");
		sql.append(param.getString("entId"), "and ENT_ID = ?");
		sql.append("order by CREATE_TIME");
		return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
}
