package com.yunqu.yc.center.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;

@WebObject(name="entSbc")
public class EntSbcDao extends AppDaoContext {
	
	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		EasySQL sql = this.getEasySQL("select t2.*,t1.IS_MAIN from CC_ENT_SBC t1 left join CC_PETRA_SBC_RES t2 on t1.SBC_ID = t2.SBC_ID where 1=1 ");
		sql.appendLike(param.getString("sbcName"), " and t2.SBC_NAME like ?");
		sql.append(param.getInteger("joinType"), " and t2.JOIN_TYPE = ?");
		sql.append(param.getString("entId")," and t1.ENT_ID = ?",false);
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="selectSbc",type=Types.LIST)
	public  JSONObject record(){
		EasySQL sql = this.getEasySQL("select * from CC_PETRA_SBC_RES t1 where 1=1");
		sql.append(param.getString("entId"), " and not exists (select 1 from CC_ENT_SBC where SBC_ID = t1.SBC_ID and ENT_ID = ?)");
		sql.appendSort(param.getString("sortName"), param.getString("sortType"));
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
}
