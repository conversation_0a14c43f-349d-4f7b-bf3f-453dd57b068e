package com.yunqu.yc.center.http;

import java.io.BufferedReader;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.log.YcCenterLogger;

/**
 * Http request proxy.
 * <AUTHOR>
 *
 */
public class Proxy {
    
    /**
     * Post 
     * @param reqUrl
     * @param params
     * @return
     * @throws Exception
     */
    public  static String doPost(String reqUrl, String params) throws Exception {
    	YcCenterLogger.getLogger().debug("doPost >> url:"+reqUrl + ",params:"+params);
    	if(reqUrl.toLowerCase().startsWith("https")){
    		return doPostNotSSL(reqUrl,params);
    	}else{
    		return doPostJson(reqUrl,params);
    	}
    }
    
	/**
	 * POST方式发起http请求，使用RequestEntity来保存数据
	 * 
	 * @param reqUrl
	 * @param params
	 * @param encode
	 * @return
	 * @throws Exception
	 */
	private static String doPostJson(String reqUrl, String params) throws Exception {
		
		RequestEntity requestEntity = new StringRequestEntity(params,"application/json","UTF-8");
		
		int statusCode = 0;
		HttpClient httpClient = new HttpClient();
		httpClient.getParams().setIntParameter("http.socket.timeout", 5000);
		httpClient.getParams().setContentCharset("UTF-8");

		HttpConnectionManagerParams hcmp = httpClient
				.getHttpConnectionManager().getParams();
		hcmp.setConnectionTimeout(5000); // 连接超时设置
		hcmp.setSoTimeout(5000); // 读取超时设置
		PostMethod postMethod = null;
		String resTemp = "";
		StringBuffer resBuffer = new StringBuffer();
		try {
			postMethod = new PostMethod(reqUrl);
			postMethod.setRequestHeader("Content-Type","application/json;charset=UTF-8");
			postMethod.setRequestEntity(requestEntity);
			
			statusCode = httpClient.executeMethod(postMethod);
			if (statusCode != 200) {
				throw new Exception("响应状态为：" + statusCode + ",返回空串！");
			}
			InputStream resStream = postMethod.getResponseBodyAsStream();
			BufferedReader br = new BufferedReader(new InputStreamReader(
					resStream, "UTF-8"));
			while ((resTemp = br.readLine()) != null) {
				resBuffer.append(resTemp);
			}
			return resBuffer.toString();
		} catch (Exception ex) {
			throw ex;
		} finally {
			postMethod.releaseConnection();
		}
	}
	
    /**
     * 
     * @param reqUrl
     * @param params
     * @return
     */
	private static String doPostNotSSL(String reqUrl, String params){

        try {

            //采用绕过验证的方式处理https请求
            SSLContext sslcontext = createIgnoreVerifySSL();
            // 设置协议http和https对应的处理socket链接工厂的对象
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.INSTANCE)
                    .register("https", new SSLConnectionSocketFactory(sslcontext))
                    .build();
            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            HttpClients.custom().setConnectionManager(connManager);

            //创建自定义的httpclient对象
            CloseableHttpClient client = HttpClients.custom().setConnectionManager(connManager).build();
            //创建post方式请求对象
            HttpPost httpPost = new HttpPost(reqUrl);

            StringEntity requestEntity = new StringEntity(params,"utf-8");
	        requestEntity.setContentEncoding("UTF-8");    	        
	        httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
	        httpPost.setEntity(requestEntity);
	        
	        ResponseHandler<String> responseHandler = new BasicResponseHandler();

	        String returnValue = client.execute(httpPost,responseHandler); 

	        client.close();
            /*
            //装填参数
            List<BasicNameValuePair> nvps = new ArrayList<BasicNameValuePair>();
            if(params!=null){
                for (String key : params.keySet()) {
                    nvps.add(new BasicNameValuePair(key, params.getString(key)));
                }
            }
            //设置参数到请求对象中
            httpPost.setEntity(new UrlEncodedFormEntity(nvps,"utf-8"));

            //设置header信息
            //指定报文头【Content-type】、【User-Agent】
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

            //执行请求操作，并拿到结果（同步阻塞）
            CloseableHttpResponse response = client.execute(httpPost);
            //获取结果实体
            HttpEntity entity = response.getEntity();
            String body=null;

            if (entity != null) {
                //按指定编码转换结果实体为String类型
                body = EntityUtils.toString(entity, "UTF-8");
            }
            EntityUtils.consume(entity);
            //释放链接
            response.close();*/
            return returnValue;
        } catch(Exception e) {
            e.printStackTrace();
            YcCenterLogger.getLogger().error("http doGet is error case: " + e.getMessage(),e);
            return "";
        }finally{
        }
    }
	
	 /**
     * 绕过SSL验证
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException,KeyManagementException{
	    SSLContext sslContext = SSLContext.getInstance("SSLv3");
        X509TrustManager x509TrustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };
        sslContext.init(null,new TrustManager[]{x509TrustManager},null);
        return sslContext;
    }
}

