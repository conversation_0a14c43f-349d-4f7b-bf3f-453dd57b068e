package com.yunqu.yc.center.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppBaseServlet;

@WebServlet("/servlet/smsChn")
public class SmsChnServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord("CC_SMS_CHN", "SMS_CHN_ID");
			record.setColumns(getJSONObject("smsChn"));
			record.setPrimaryValues(RandomKit.randomStr());
			setIntType(record, "SMS_CHN_TYPE", "IS_BIND_SKILL", "SMS_PLATFORM_TYPE");
			this.getQuery().save(record);
			result.setMsg("添加成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("添加失败");
		}
		return result;
	}
	
	public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord("CC_SMS_CHN", "SMS_CHN_ID");
			record.setColumns(getJSONObject("smsChn"));
			setIntType(record, "SMS_CHN_TYPE", "IS_BIND_SKILL", "SMS_PLATFORM_TYPE");
			this.getQuery().update(record);
			result.setMsg("修改成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("修改失败");
		}
		return result;
	}
	
	public EasyResult actionForDelete(){
		EasyResult result = new EasyResult();
		try {
			this.getQuery().deleteById(new EasyRecord("CC_SMS_CHN", "SMS_CHN_ID").setPrimaryValues(getJsonPara("smsChnId")));
			result.setMsg("删除成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("删除失败");
		}
		return result;
	}
	

	/*
	 * 添加参数
	 */
	public EasyResult actionForAddParam(){
		EasyResult result = new EasyResult();
		try {
			JSONObject smsParam = getJSONObject("smsParam");
			if(this.getQuery().queryForExist("select count(1) from CC_SMS_TEMP_PARAM where SMS_CHN_ID = ? and SMS_PARAM_NAME = ?", new Object[]{smsParam.getString("SMS_CHN_ID"), smsParam.getString("SMS_PARAM_NAME")})){
				result.addFail("添加失败，参数名称已经存在");
				return result;
			}

			if(this.getQuery().queryForExist("select count(1) from CC_SMS_TEMP_PARAM where SMS_CHN_ID = ? and SMS_PARAM_CODE = ?", new Object[]{smsParam.getString("SMS_CHN_ID"), smsParam.getString("SMS_PARAM_CODE")})){
				result.addFail("添加失败，参数编码已经存在");
				return result;
			}
			EasyRecord record = new EasyRecord("CC_SMS_TEMP_PARAM", "SMS_PARAM_ID");
			record.setColumns(smsParam);
			record.setPrimaryValues(RandomKit.randomStr());
			setIntType(record, "SMS_PARAM_TYPE", "MAX_LENGTH", "IDX_ORDER");
			this.getQuery().save(record);
			result.setMsg("添加成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("添加失败");
		}
		return result;
	}
	
	/**
	 * 修改参数
	 * @return
	 */
	public EasyResult actionForUpdateParam(){
		EasyResult result = new EasyResult();
		try {
			JSONObject smsParam = getJSONObject("smsParam");
			if(this.getQuery().queryForExist("select count(1) from CC_SMS_TEMP_PARAM where SMS_CHN_ID = ? and SMS_PARAM_ID <> ? and SMS_PARAM_NAME = ?", new Object[]{smsParam.getString("SMS_CHN_ID"), smsParam.getString("SMS_PARAM_ID"), smsParam.getString("SMS_PARAM_NAME")})){
				result.addFail("添加失败，参数名称已经存在");
				return result;
			}

			if(this.getQuery().queryForExist("select count(1) from CC_SMS_TEMP_PARAM where SMS_CHN_ID = ? and SMS_PARAM_ID <> ? and SMS_PARAM_CODE = ?", new Object[]{smsParam.getString("SMS_CHN_ID"), smsParam.getString("SMS_PARAM_ID"), smsParam.getString("SMS_PARAM_CODE")})){
				result.addFail("添加失败，参数编码已经存在");
				return result;
			}
			EasyRecord record = new EasyRecord("CC_SMS_TEMP_PARAM", "SMS_PARAM_ID");
			record.setColumns(smsParam);
			setIntType(record, "SMS_PARAM_TYPE", "MAX_LENGTH", "IDX_ORDER");
			this.getQuery().update(record);
			result.setMsg("修改成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("修改失败");
		}
		return result;
	}
	
	/**
	 * 删除参数
	 * @return
	 */
	public EasyResult actionForDeleteParam(){
		EasyResult result = new EasyResult();
		try {
			this.getQuery().deleteById(new EasyRecord("CC_SMS_TEMP_PARAM", "SMS_PARAM_ID").setPrimaryValues(getJsonPara("smsParamId")));
			result.setMsg("删除成功");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("删除失败");
		}
		return result;
	}
	
	@Override
	protected String getResId() {
		return "yc_center_ent_list";
	}

}
