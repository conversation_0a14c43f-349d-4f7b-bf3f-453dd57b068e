<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业号码</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editform" data-mars="channel.getSW"  method="post"  autocomplete="off" data-mars-prefix="sw.">
			      <input type="hidden" name="sw.ENT_ID" value="${param.entId}" >
			      <input type="hidden" name="entId" value="${param.entId}" >
			      <input type="hidden" name="sw.SW_ID" value="${param.swId}" >
				  <table class="table table-edit table-vzebra">
	                    <tbody>
		                      <tr>
			                        <td class="required" style="width: 40px">敏感词</td>
			                        <td><input type="text" name="sw.SW_TEXT" class="form-control input-sm" maxlength="50"></td>
			                  </tr>
		                      <tr>
			                        <td class="required">服务渠道</td>
			                        <td>
				                        <select name="sw.CHANNEL_ID" data-mars="channel.channelDict" class="form-control input-sm">
											<option value="0">全局敏感词</option>
										</select>
									</td>
			                 </tr>              
	                    </tbody>
	              </table>
				  <div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="ChannelSwEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("ChannelSwEdit");
	
	ChannelSwEdit.entId = '${param.entId}';
	ChannelSwEdit.swId = '${param.swId}';
	
	$(function(){
		$("#editform").render();
	});

	 ChannelSwEdit.ajaxSubmitForm = function(){
		 if(form.validate("#editform")){
			 if(ChannelSwEdit.swId==''){
				 ChannelSwEdit.insertData(); 
			 }else{
				 ChannelSwEdit.updateData(); 
			 }
		 };
	}
	 ChannelSwEdit.insertData = function() {
			var data = form.getJSONObject("#editform");
			ajax.remoteCall("${ctxPath}/servlet/channel?action=addChannelSw",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					ChannelSw.searchData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	}
	 ChannelSwEdit.updateData = function(){
		var data = form.getJSONObject("#editform");
		ajax.remoteCall("${ctxPath}/servlet/channel?action=updateChannelSw",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1},function(){
					layer.closeAll();
					ChannelSw.searchData();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>