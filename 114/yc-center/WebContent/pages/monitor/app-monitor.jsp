<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>业务运营监控</title>
	<style>
	.width-95{width: 95px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5> 业务运营监控<span id="titleAndTime"> </span></h5>
	             		 </div>
	             		 <div class="form-group">
	             		 	 	<hr style="margin: 3px -15px">
				       		   <div class="input-group input-group-sm ml-20">
									  <span class="input-group-addon">业务KEY</span>	
									  <input type="text" name="busiKey" class="form-control input-sm" style="width: 95px;">
							   </div>
				       		   <div class="input-group input-group-sm">
									  <span class="input-group-addon">企业ID</span>	
									  <input type="text" name="entId" class="form-control input-sm" style="width: 95px;">
							   </div>
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon">应用名称</span>	
									  <select class="form-control input-sm" name="appId" data-mars="statistic.appDict" onchange="AppMonitor.searchData()">
									      <option value="">请选择</option>
									  </select>
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">类型</span>	
									  <select class="form-control input-sm" name="logLevel" onchange="AppMonitor.searchData()">
                                          <option value="">请选择</option>
                                          <option value="info">info</option>
                                          <option value="warn" data-class="label label-warning">warn</option>
                                          <option value="error" data-class="label label-danger">error</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="AppMonitor.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="statistic.applist">
                             <thead>
	                         	 <tr>
	                         	      <th class="text-c">应用名称</th>
	                         	      <th class="text-c">企业ID</th>
								      <th class="text-c">业务KEY</th>
								      <th class="text-c" width="22%">业务名称</th>
								      <th class="text-c" width="24%">日志内容</th>
								      <th class="text-c">类型</th>
								      <th class="text-c">时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{getText:APP_ID 'appId'}}</td>
                                            <td>{{:ENT_ID}}</td>
											<td>{{:BUSI_KEY}}</td>
											<td>{{:BUSI_NAME}}</td>
                                            <td>{{:CONTENT}}</td>
											<td>{{getText:LOG_LEVEL 'logLevel'}}</td>
											<td>{{:LOG_DATE}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		jQuery.namespace("AppMonitor");
		
		$(function(){
			var today = AppMonitor.dateToString(new Date());
			$("#dateId").val(today);
			$("#searchForm").render({success:function(data){
				if(data['statistic.applist']){
					var updateTime = data['statistic.applist'].updateTime;
					if(updateTime&&updateTime!=""){
						$("#titleAndTime").html("<font color='#5cb85c'>(数据更新时间:"+updateTime+")</font>");
					}
				}
				
			}});
		})
		
		AppMonitor.searchData=function(){
			$("#searchForm").searchData();
		}
		
		AppMonitor.dateToString = function(now){  
		    var year = now.getFullYear();  
		    var month =(now.getMonth() + 1).toString();  
		    var day = (now.getDate()).toString();  
		    var hour = (now.getHours()).toString();  
		    var minute = (now.getMinutes()).toString();  
		    var second = (now.getSeconds()).toString();  
		    if (month.length == 1) {  
		        month = "0" + month;  
		    }  
		    if (day.length == 1) {  
		        day = "0" + day;  
		    }  
		     var dateTime = year + "" + month + "" + day;  
		     return dateTime;  
		 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>