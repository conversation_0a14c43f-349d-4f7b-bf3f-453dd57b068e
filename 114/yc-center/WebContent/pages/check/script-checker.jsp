<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>数据库资源列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 业务库脚本匹配</h5>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-info" onclick="matchFn('A')">
											<span class="glyphicon glyphicon-search"></span> 检查平台标准脚本
										</button>
										<button type="button" class="btn btn-sm btn-primary ml-15" onclick="matchFn('B')">
											<span class="glyphicon glyphicon-search"></span> 检查上传的脚本
										</button>
								</div>
							   <div class="input-group pull-right">
										<button id="upload-btn" type="button" class="btn btn-sm btn-success">
											<span class="glyphicon glyphicon-upload"></span> 上传脚本
										</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c"  id="tableHead" data-mars="schemaRes.ycSchemaList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">云呼数据库类型</th>
								      <th class="text-c">数据库名</th>
								      <th class="text-c">数据库说明</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
		                 <div id="msg"></div>
	              	</div> 
                </div>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td><input name="names" type="hidden" value="{{:schemaType}}">{{:schemaType}}</td>
											<td>{{:schemaName}}</td>
											<td>{{:schemaDesc}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("SchemaRes");
		
		 var _index = 0 ;
		 function matchFn(type){
		    	var checkList = $("[name='names']");
				if(checkList.length==0){
					layer.msg("没有数据库资源可匹配。");
					return;
				}
				var array=[];
				checkList.each(function(){
					var t=$(this);
					var dbName=t.val();
					array.push(dbName);
				});
				_index = 0 ;
				doMatch(type,array);
				

		  }
		 function doMatch(type,array){
			 $("#msg").html("");
			   var dbName = array[_index];
	    		var index= layer.msg('后台匹配处理中,耐心等候...', {icon: 16 ,shade: 0.01,time:0,area:'300px'});
		    	ajax.remoteCall("${ctxPath}/servlet/schemaRes?action=matchTable",{dbName:dbName,type:type},function(result){  
					layer.close(index);
		    		console.log(result);
		    		if(result.state==404){
		    			layer.msg(result.msg);
		    			return false;
		    		}
		    		var data=result.data;
		    		for(var index in data){
		    			var str = JSON.stringify(data[index]);
		    			str =  JSON.stringify(JSON.parse(str), null, 10);
			    		//var html="<div style='margin:15px;text-align: center;'><font size=3>"+index+"</font><br><textarea id='"+index+"' class='layui-textarea' style='height:200px'>"+str+"</textarea></div>";
			    		var html='<fieldset class="layui-elem-field" class="layui-elem-field"><legend>'+index+' <a title="执行自动添加缺失的字段" href="javascript:void(0);" data-value="'+index+'" class="alterTable hidden">执行</a>'+' <a title="下载脚本" href="javascript:void(0);" data-value="'+index+'" class="downloadScript">下载脚本</a>'+'</legend><div class="layui-field-box"><pre class="layui-code">'+str+'</pre></div></fieldset>';
			    		$("#msg").append(html);
		    		}
		    	},{loading:false,async:true,error:function(){
		    		layer.close(index);
		    	},then:function(){
		    		layui.use('code', function(){ 
		    			  layui.code({height:'250px',about:true,encode:true});
		    		});
		    		layer.close(index);
		    		/* _index++;
		    		if(array.length==_index){
		    			return;
		    		}
		    		doMatch(array) */
		    	}});
		 }
		 
		 $('body').on('click','.alterTable',function(e){
			 var dbName = $(this).data("value");
			 var index= layer.msg('后台正在自动添加缺失的字段,耐心等候...', {icon: 16 ,shade: 0.01,time:0,area:'300px'});
			 ajax.remoteCall("${ctxPath}/servlet/schemaRes?action=alterTable",{dbName:dbName},function(result){  
				layer.close(index);
	    		console.log(result);
	    		if(result.state==404){
	    			layer.msg(result.msg);
	    			return false;
	    		}
	    		layer.msg(result.msg,{icon:1});
		 	},{loading:false,async:true,error:function(){
	    		layer.close(index);
	    	},then:function(){
	    	}});
		 });
		 
		 layui.use('upload', function(){
		 	  var upload = layui.upload;
			  var uploadInst = upload.render({
			    elem: '#upload-btn' //绑定元素
			    ,url: '${ctxPath}/servlet/schemaRes?action=uploadSqlFile' //上传接口
			    ,number: 1
			    ,accept: 'file'
			    ,exts:'json|txt|sql'
			    ,size:1024*50
			    ,field: 'sqlFile'//设定文件域的字段名
			    ,data: {}
			    ,done: function(res, index, upload){
				    if(res&&res.state==1){
				    	layer.msg(res.msg,{icon: 1,time:800},function(){
							layer.closeAll();
						}); 
					}else{
						layer.alert(res.msg,{icon: 5});
					}
			    },before:function(){
			    	
			    }
			    ,error: function(res, index, upload){
			    	layer.alert("上传文件请求异常！",{icon: 5});
			    }
	      });
  	  });

		$('body').on('click','.downloadScript',function(e){
			var dbName = $(this).data("value");
			window.open("${ctxPath}/servlet/schemaRes?action=downloadScript&dbName="+dbName);
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp" %>