<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>通话记录查询</title>
</EasyTag:override>

<EasyTag:override name="content">
	<form class="form-inline" method="post" name="searchForm" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
        		   <h5> 企业操作流水</h5>
        		   <div class="input-group input-group-sm">
				      <span class="input-group-addon">企业名称</span>	
		              <select name="entId" class="form-control input-sm" id="entId" data-mars-top="true" data-mars="ent.entDict" onchange="Report.loadData()">
		              	<option value="">请选择</option>
		              </select>
			  		</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">日期时间</span> 
						<input  name="beginTime" onClick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}',dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="startDate" data-mars-top="true"  data-mars="common.todayBegin" class="form-control input-sm">
						<span class="input-group-addon">-</span> 
						<input  name="endTime" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="endDate" data-mars-top="true"  data-mars="common.todayEnd" class="form-control input-sm">
					</div>
        		   <div class="input-group input-group-sm">
				      <span class="input-group-addon">信息类型</span>	
		              <select name="eventType" class="form-control input-sm" data-mars-top="true" data-mars="EntLogDao.eventTypeDict" onchange="Report.loadData()">
		              	<option value="">请选择</option>
		              </select>
			  		</div>
        		   <div class="input-group input-group-sm">
				      <span class="input-group-addon">操作类型</span>	
		              <select name="operType" class="form-control input-sm" data-mars-top="true" data-mars="EntLogDao.operTypeDict" onchange="Report.loadData()">
		              	<option value="">请选择</option>
		              </select>
			  		</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">操作人</span> 
						<input  name="username"  size = "11"  class="form-control">
					</div>
					
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="Report.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				    </div>
				    
			    </div>
			</div>
			<div class="ibox-content">
				<table  data-mars="EntLogDao.list" data-auto-fill="10" class="table table-auto table-striped table-bordered table-hover table-condensed text-c" id="tableHead">
					<thead>
						<tr>
							<th class="text-c" width="152">日期时间</th>
							<th class="text-l">企业名称</th>
							<th class="text-c" width="70">信息类型</th>
							<th class="text-l" width="70">操作类型</th>
							<th class="text-l">操作描述</th>
							<th class="text-c">操作人</th>
						</tr> 
					</thead>
					<tbody id="dataList"></tbody>
				</table>
				<div class="row paginate">
               	        <jsp:include page="/pages/common/pagination.jsp"/>
                   </div> 
				<script id="list-template" type="text/x-jsrender">
					 {{for list}}
							<tr>
							<td>{{:CREATE_TIME}}</td>
							<td class="text-l">{{:ENT_NAME}}</td>
							<td>{{getText:EVENT_TYPE 'eventType'}}</td>
							<td class="text-l">{{getText:OPER_TYPE 'operType'}}</td>
							<td class="text-l">{{:LOG_DESC}}</td>
							<td>{{:USERNAME}}</td>
							</tr>
						{{/for}}
					 </script>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">

<script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript">
		jQuery.namespace("Report");
		
		$(function(){
			$("#searchForm").render();
		});
	
		Report.loadData=function(){
			$("#searchForm").searchData();
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>