package com.yunqu.cx.monitor.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cx.monitor.util.ParamUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataInputStream;
import java.io.IOException;
import java.util.Enumeration;

/**
 * Servlet implementation class AgentBaseServlet
 */
public abstract class HttpServletBase extends HttpServlet {
	
	private static final long serialVersionUID = 1L;
	protected static EasyCache cache = CacheManager.getMemcache();

	private final Logger logger = CommonLogger.getLogger("AgentMonitor");
    /**
     * @see HttpServlet#HttpServlet()
     */
    public HttpServletBase() {
        super();
    }

	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setHeader("Access-Control-Allow-Origin", "*");
		doPost(request,response);
		 

	}
	
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

		
		int contLength = request.getContentLength();
		JSONObject jsonObject = null;
		if(contLength>0){
			
			byte[] content = new byte[request.getContentLength()];
			
			try {
				DataInputStream dis = new DataInputStream(request.getInputStream());
				dis.readFully(content);
				logger.info("<< Request["+request.getRemoteAddr()+","+request.getRemotePort()+"],body->"+new String(content));
				//String str=URLDecoder.decode(new String(content), "UTF-8");
				String str = new String(content);
				try {
					jsonObject = JSON.parseObject(str);
				} catch (Exception e) {
					jsonObject = ParamUtil.parseParamToJson(str);
				}
				if(jsonObject == null){
					response.getWriter().write(this.getErrorResult("Fail,JSON format error!").toJSONString());
					return;
				}
			} catch (Exception ex) {
				logger.error(ex,ex);
				response.setCharacterEncoding("UTF-8");
				response.getWriter().write(this.getErrorResult(ex.getMessage()).toJSONString());
				return;
			}
		}else {
			//contLength小于等于0时看是否request是否有值
			jsonObject = new JSONObject();
			Enumeration enu=request.getParameterNames();  
			while(enu.hasMoreElements()){  
				String paraName=(String)enu.nextElement();  
				jsonObject.put(paraName, request.getParameter(paraName));
			} 
			if(StringUtils.isBlank(jsonObject.getString("command"))) {
				response.getWriter().write(this.getErrorResult("Fail,content is null").toJSONString());
				return ;
			}
		}
		JSONObject result = null;
		try {
			result = this.proxy(jsonObject,request);
			logger.info(">> Response["+request.getRemoteAddr()+","+request.getRemotePort()+"]->" + result.toJSONString());
			response.setCharacterEncoding("UTF-8");
			response.setContentType("text/html;charset=UTF-8");
			response.getWriter().write(result.toJSONString());
		} catch (Exception ex) {
			logger.error(ex,ex);
			response.setCharacterEncoding("UTF-8");
			response.getWriter().write(getErrorResult(ex.getMessage()).toJSONString());
		}
	}
	
	/**
	 * 返回异常结果
	 * @param msg
	 * @return
	 */
	protected JSONObject getErrorResult(String msg){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("result", "999");
		jsonObject.put("desc", msg);
		return jsonObject;
	}
	
	/**
	 * 返回异常结果
	 * @param msg
	 * @param code
	 * @return
	 */
	protected JSONObject getErrorResult(String msg, String code){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("result", code);
		jsonObject.put("desc", msg);
		return jsonObject;
	}
	
	
	protected abstract JSONObject proxy(JSONObject jsonObject,HttpServletRequest request) throws Exception;

	
}
