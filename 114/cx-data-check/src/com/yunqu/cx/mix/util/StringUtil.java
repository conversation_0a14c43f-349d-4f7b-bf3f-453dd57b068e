package com.yunqu.cx.mix.util;

import com.yq.busi.common.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.NumberFormat;
import java.util.*;

public class StringUtil {
	/**
	 * 空值转成空字符串
	 * 
	 * @param object
	 * @return
	 */
	public static String nullToSpace(Object object) {
		if (isNull(object))
			return "";
		return object.toString().trim();
	}
	/**
	 * 判断是否空值
	 * 
	 * @param object
	 * @return
	 */
	public static boolean isNull(Object object) {
		return null == object || "".equals(object.toString().trim())
				|| "null".equals(object.toString());
	}
	
	public static boolean isNotNull(Object object){
		return !isNull(object);
	}
	
	/**
	 * 对象转字符串,带缺省
	 * 
	 * @param object
	 * @param defaultValue
	 * @return
	 */
	public static String nvl(Object object, String defaultValue) {
		if (isNull(object))
			return defaultValue;
		return nullToSpace(object);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static String getKey(Map map, Object value){
	    Set<Map.Entry<Object,Object>> set = map.entrySet(); //通过entrySet()方法把map中的每个键值对变成对应成Set集合中的一个对象
	    Iterator<Map.Entry<Object, Object>> iterator = set.iterator();
	    ArrayList<Object> arrayList = new ArrayList<Object>();
	    while(iterator.hasNext()){
	        //Map.Entry是一种类型，指向map中的一个键值对组成的对象
	        Map.Entry<Object, Object> entry = iterator.next();
	        if(entry.getValue().equals(value)){
	            arrayList.add(entry.getKey());
	        }
	    }
	    String ret = value.toString();
	    if(arrayList!=null&&!arrayList.isEmpty()){
	    	ret = arrayList.get(0).toString();
	    }
	    return ret;
	}
	
	public static String join(String seperator, String[] strings) {
		StringBuffer buf = new StringBuffer();
		int length = strings.length;
		if (length > 0) {
			buf.append(strings[0]);
		}

		for (int i = 1; i < length; ++i) {
			buf.append(seperator).append(strings[i]);
		}

		return buf.toString();
	}
	
	/**
	 * 将小数转换为百分比
	 * @param decimal 0.1234
	 * @param digit  保留小数位数
	 * @return 12.34%
	 */
	public static String convertDecimal(String decimal,int digit){
		NumberFormat nf = NumberFormat.getPercentInstance();
		nf.setMaximumFractionDigits(2);//保留2位小数
		String result = nf.format(Double.valueOf(decimal));//
		return result;
	}
	
	@SuppressWarnings("rawtypes")
	public static String join(Collection collection, String separator) {
		return collection == null ? null : join(collection.iterator(),
				separator);
	}
	
	@SuppressWarnings("rawtypes")
	public static String join(Iterator iterator, String separator) {
		if (iterator == null) {
			return null;
		} else if (!iterator.hasNext()) {
			return "";
		} else {
			Object first = iterator.next();
			if (!iterator.hasNext()) {
				return nullToSpace(first);
			} else {
				StringBuffer buf = new StringBuffer(256);
				if (first != null) {
					buf.append(first);
				}
				while (iterator.hasNext()) {
					if (separator != null) {
						buf.append(separator);
					}
					Object obj = iterator.next();
					if (obj != null) {
						buf.append(obj);
					}
				}
				return buf.toString();
			}
		}
	}
	
	public static boolean custExIsValidity(String exName) {
		if(StringUtils.startsWith(exName, "EX")) {
			int num = CommonUtil.parseInt(exName.replace("EX", ""));
			if(1 <= num && num <= 34) {
				return true;
			}
		}
		return false;
	}

}
