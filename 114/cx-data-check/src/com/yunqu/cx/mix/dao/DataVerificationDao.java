package com.yunqu.cx.mix.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cx.mix.base.AppDaoContext;
import com.yunqu.cx.mix.base.CommonLogger;
import com.yunqu.cx.mix.base.Constants;
import com.yunqu.cx.mix.service.SqlService;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.util.Calendar;
import java.util.List;

/**
 * 数据核验
 */
@WebObject(name = "dataVerificationDao")
public class DataVerificationDao extends AppDaoContext {
    private static final Logger logger = CommonLogger.getLogger();

    /**
     * 号线数据核验查询
     */
    @WebControl(name = "list", type = Types.LIST)
    public JSONObject list() {
        try {
            SqlService sqlService = new SqlService(getUserPrincipal());
            EasySQL sql = sqlService.getTaskObJListSql(param, getDbName());
            logger.info("sql:" + sql.getSQL() + "param:" + JSONObject.toJSONString(sql.getParams()));
            return this.queryForPageList(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            this.error("号线数据核验查询异常", e);
        }
        return EasyResult.fail("号线数据核验查询异常");
    }

    @WebControl(name = "getVerificationTaskDict", type = Types.DICT)
    public JSONObject getVerificationTaskDict() {
        EasySQL sql = new EasySQL("select TASK_ID,TASK_NAME FROM " + getTableName("cx_data_verification_task"));
        // 任务类型 1 数据核验  2 余额提醒
        sql.append(" where TASK_TYPE = 1 ");
        sql.append(getEntId(), "and ENT_ID = ?");
        sql.append(getBusiOrderId(), "and BUSI_ORDER_ID = ?");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }

    /**
     * 获取核验标准配置
     */
    @WebControl(name = "config", type = Types.RECORD)
    public JSONObject config() {
        String sql = "SELECT PARAM_CONFIG FROM " + this.getTableName("C_CF_ENT_PARAM") + " WHERE 1 = 1 AND LABEL_CODE = ? AND BUSI_ORDER_ID = ? AND ENT_ID = ? AND MODULE_CODE = ?";
        try {
            String paramConfig = getQuery().queryForString(sql, Constants.CBSS_DATAVERIFICATION_CONFIG_KEY, this.getBusiOrderId(), this.getEntId(), Constants.APP_NAME);
            if (StringUtils.isNotBlank(paramConfig)) {
                return EasyResult.ok(JSONObject.parseObject(paramConfig));
            } else {
                return EasyResult.ok(new JSONObject());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return EasyResult.ok(new JSONObject());
    }


    /**
     * 客户数据外呼管理
     */
    @WebControl(name = "callOutList", type = Types.LIST)
    public JSONObject callOutList() {
        //获取数据外呼管理sql
        EasySQL sql = DataVerificationSql.getCallOutList(param);
        int rebotMaxCount = Constants.getRebotMaxCount();
        JSONObject json = this.queryForPageList(sql.getSQL(), sql.getParams());
        int totalRow = json.getIntValue("totalRow");

        int day = 1;
        if (totalRow > rebotMaxCount) {
            day = totalRow / rebotMaxCount + 1;
        }
        String startDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        String endDate = EasyDate.addTime("yyyy-MM-dd", startDate, Calendar.DAY_OF_YEAR, day);
        json.put("startDate", startDate);
        json.put("endDate", endDate);

        return json;
    }


    /**
     * 客户数据外呼详情
     */
    @WebControl(name = "callOutListRecord", type = Types.LIST)
    public JSONObject callOutListRecord() {
        //获取数据外呼详情sql
        EasySQL sql = DataVerificationSql.getCallOutListRecordSql(getDbName(), param);
        return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "getTaskDict", type = Types.DICT)
    public JSONObject getTaskDict() {
        EasySQL sql = new EasySQL("select TASK_ID,TASK_NAME FROM " + getTableName("cc_task"));
        sql.append(" where TASK_SOURCE='2'");//任务来源是接口创建
        sql.append(getEntId(), "and ENT_ID = ?");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }


    /**
     * 获取行业数据级联树
     */
    @WebControl(name = "getIndustryTree", type = Types.RECORD)
    public JSONObject getIndustryTree() {
        String sql = "SELECT INDUSTRY_ID,PARENT_ID,INDUSTRY_NAME FROM " + Constants.getQueryDbName() + ".t_industry";
        try {
            String industryId = param.getString("industryId");
            if (StringUtils.isNotBlank(industryId)) {
                sql += " where INDUSTRY_ID = ?";
            }
            List<JSONObject> list = getQuery().queryForList(sql, null, new JSONMapperImpl());
            JSONObject data = new JSONObject();
            data.put("children", new JSONArray());
            getTreeData(list, "0", data);
            return EasyResult.ok(data.getJSONArray("children"));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return EasyResult.ok(new JSONObject());
    }

    /**
     * 通过list获取treeData
     * @param list
     * @param parentId
     * @param data
     */
    public void getTreeData(List<JSONObject> list, String parentId, JSONObject data) {
        for (JSONObject json : list) {
            if (json.getString("PARENT_ID").equals(parentId)) {
                JSONArray array = data.getJSONArray("children");
                if (array == null) {
                    array = new JSONArray();
                }
                JSONObject obj = new JSONObject();
                obj.put("value", json.getString("INDUSTRY_ID"));
                obj.put("label", json.getString("INDUSTRY_NAME"));
                array.add(obj);
                data.put("children", array);
                getTreeData(list, json.getString("INDUSTRY_ID"), obj);
            }
        }
    }

}
