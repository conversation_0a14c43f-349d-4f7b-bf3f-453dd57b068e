package com.yunqu.cc.mixgw.listener;


import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.apache.log4j.Logger;

import com.yunqu.cc.mixgw.base.CommonLogger;




/**
 * 应用初始化 InitListener.java
 */
@WebListener
public class InitListener implements ServletContextListener
{
	protected Logger logger = CommonLogger.logger;

	/**
	 * 模块启动
	 */
	@Override
	public void contextInitialized(ServletContextEvent arg0) {	
	}


	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
	}
	
}