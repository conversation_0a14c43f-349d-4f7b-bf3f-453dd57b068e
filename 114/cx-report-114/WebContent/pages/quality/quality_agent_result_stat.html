<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <link rel="stylesheet" href="/cx-report-114/static/css/common.css" />
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
</head>
<style>

</style>
<body>
<div id="checkList">
    <el-card shadow="never">
        <div slot="header" class="headBox">
            <div class="title-box">质检员工作量统计表
            <el-popover placement="bottom" width="500" trigger="hover">
                    <div style="padding: 16px; height: 400px; overflow:auto">
                        报表更新规则：查询时实时更新<br />
                        <br />
                        质检单（张）：质检员合成的质检单数量<br />
                        优：单个质检单内所有通话录音扣分计算后评定分数在100的数量<br />
                        良：单个质检单内所有通话录音扣分计算后评定分数在90-99的数量<br />
                        中：单个质检单内所有通话录音扣分计算后评定分数在71-89的数量<br />
                        差：单个质检单内所有通话录音扣分计算后评定分数在1-70的数量<br />
                        劣：单个质检单内所有通话录音扣分计算后评定分数在0的数量<br />
                        业务问题：质检单中所有通话录音包含普通差错和增值差错的问题数量<br />
                        服务问题-用语：质检单中所有通话录音包含用语问题的数量<br />
                        服务问题-态度：质检单中所有通话录音包含态度问题的数量<br />
                        质检处理时长：开始时间：质检单点击开始质检时间；结束时间：本张质检单提交时间；<br />
                    </div>
                    <div slot="reference" style="display: inline-block">
                        <i class="el-icon-warning-outline" style="color: #67c23a"></i>
                    </div>
                </el-popover>
            </div>
            <div style="float: right;">
                <el-button type="primary" size="small" icon="el-icon-download" plain @click="exportData">{{'导出'}}</el-button>
            </div>
        </div>
        <el-form ref="form" :model="sizeForm" label-width="80px" size="small" >
            <el-row>
                <el-col :span="8">
                    <el-form-item label="时间">
                        <el-date-picker
                                v-model="sizeForm.date"
                                type="datetimerange"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                align="right"
                                unlink-panels
                                range-separator="至"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                :default-time="['00:00:00','23:59:59']"
                                :clearable="false"
                                >
                        </el-date-picker>

                    </el-form-item>
                </el-col>
                <el-col :span="3">
                    <el-form-item label="统计步长">
                        <el-select v-model="sizeForm.step" >
                            <el-option
                                    v-for="item in stepOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="5">
                    <el-form-item label="工号">
                        <el-select v-model="sizeForm.agent" placeholder="全部" filterable clearable>
                            <el-option v-for="(key,value) in agentOptions" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="4">
                    <el-form-item label="员工类型">
                        <el-select v-model="sizeForm.role" clearable placeholder="全部">
                            <el-option
                                    v-for="item in roleOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="4" style="display: flex; justify-content: flex-end;float: right">
                    <el-button type="primary" size="small" @click="getSearchData" icon="el-icon-search">查询</el-button>
                    <el-button size="small" type="primary" @click="reset" plain icon="el-icon-refresh">重置</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="tableData" border stripe style="width: 100%;" height="75vh" v-loading="loading">
            <el-table-column type="index" label="序号" width="60"></el-table-column>
            <el-table-column label="日期" prop="SELECT_TIME" :min-width="100"></el-table-column>
            <el-table-column label="工号"  prop="USER_ACCT" :min-width="100"></el-table-column>
            <el-table-column label="质检单（张）"  width="120" prop="TASK_COUNT"></el-table-column>
            <el-table-column label="优" prop="GOOD_COUNT"></el-table-column>
            <el-table-column label="良" prop="NOTGOOD_COUNT"></el-table-column>
            <el-table-column label="中" prop="CENTER_COUNT"></el-table-column>
            <el-table-column label="差" prop="BAD_COUNT"></el-table-column>
            <el-table-column label="劣" prop="VERYBAD_COUNT"></el-table-column>
            <el-table-column label="业务问题" prop="BUSI_COUNT"></el-table-column>
            <el-table-column label="服务问题" >
                <el-table-column label="用语" width="120" prop="PREBLEM_LANGUE_COUNT"></el-table-column>
                <el-table-column label="态度" width="120" prop="PREBLEM_ATTITUDE_COUNT"></el-table-column>
            </el-table-column>
            <el-table-column label="质检处理时长" prop="TASK_TIME"></el-table-column>
        </el-table>


        <div class="paginBox">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
                           :current-page="pageInfo.pageNumber" :page-sizes="[10,15,20,30,50,100]" :page-size="pageInfo.pageSize"
                           layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
            </el-pagination>
        </div>
    </el-card>
</div>
</body>
<script type="text/javascript" src="/cx-report-114/static/js/time.js"></script>
<script type="text/javascript" src="/cx-report-114/static/js/common.js"></script>
<script>
    var app = new Vue({
        el: '#checkList',
        data: {
            loading:false,
            tableData: [],
            // TODO: 筛选
            sizeForm: {
                date: [getTodayStartTime(),getTodayEndTime()],
                step:'2',
                role:'',
                USER_ID:''
            },
            pageInfo: {
                pageSize: 100,
                pageNumber: 1,
                pageType:3
            },
            pageTotal: 10,
            stepOptions: [ {
                value: '2',
                label: '天'
            }, {
                value: '3',
                label: '月'
            }],
            
            roleOptions: [  {
                value: 'N',
                label: '正式'
            }, {
                value: 'Y',
                label: '实习'
            }],
            agentOptions:[]

        },
        created() {
        },
        mounted(){
             this.getData()
             this.getDict("common.userDict");
        },
        methods: {
            // -----------------获取列表接口----------------
            getSearchData() {
                this.pageInfo.pageNumber=1;
                this.getData();
            },
            getData() {
                var _this = this;
                _this.loading=true;
                let data = {
                    BEGIN_TIME: _this.sizeForm.date[0],
                    END_TIME: _this.sizeForm.date[1],
                    step:_this.sizeForm.step,
                    ROLE:_this.sizeForm.role,
                    USER_ID:_this.sizeForm.agent
                }
                yq.tableCall(`/cx-report-114/webcall?action=qualityStatDao.qualityAgentResultStat`,{
                    pageSize:_this.pageInfo.pageSize,pageIndex:_this.pageInfo.pageNumber,pageType:_this.pageInfo.pageType,data:data
                }).then(function(res){
                    _this.loading=false;
                    if (res.state==1){
                        // 表格数据
                        _this.tableData = res.data
                        // 总数
                        _this.pageTotal = res.totalRow
                    }else{
                        _this.$message({
                            showClose: true,
                            message:res.msg,
                            type: 'error',
                            duration:1000
                        });
                    }
                }).catch(err=>{
                    _this.loading=false;
                    _this.$message({
                        showClose: true,
                        message:err,
                        type: 'error',
                        duration:1000
                    });
                })
            },
            getDict(str) {
                var _this = this;
                let data = {
                    "params": {},
                    controls: [str],
                }
                yq.remoteCall('/cx-report-114/webcall',data).then(function(res){
                    console.log(res)
                    if(str == 'common.userDict'){
                        _this.agentOptions = res[str].data
                    }

                })
            },
            // TODO: 重置
            reset() {
                this.sizeForm = {
                    date: [getTodayStartTime(),getTodayEndTime()],
                    step:'3',
                    role:'',
                    USER_ID:''
                }
                this.getData()
            },
            // TODO: 导出
            exportData: function () {
                var _this = this;
                let data = {
                    BEGIN_TIME: _this.sizeForm.date[0],
                    END_TIME: _this.sizeForm.date[1],
                    step:_this.sizeForm.step,
                    ROLE:_this.sizeForm.role,
                    USER_ID:_this.sizeForm.agent

                }
                var params = jsonToQueryParams(data);
                window.location.href = '/cx-report-114/servlet/export?action=ExportQualityAgentResultStat&' +params;
            },

            // -------------------分页开始-------------------
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
               this.pageInfo.pageSize=val;
               this.getData();
            },
            handleCurrentChange(val) {
                this.pageInfo.pageNumber=val;
                this.getData();
            },
            // -------------------分页结束-------------------
        }
    })
</script>

</html>