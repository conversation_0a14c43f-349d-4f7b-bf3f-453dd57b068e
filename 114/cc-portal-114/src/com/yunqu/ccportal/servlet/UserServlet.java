/**
 * 
 */
package com.yunqu.ccportal.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.UserPosition;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppBaseServlet;

/**
 * <AUTHOR>
 * @date 2017年7月10日
 */
@MultipartConfig
@WebServlet("/servlet/user/*")
public class UserServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	
	public EasyResult actionForUpdatePw() throws IOException{
		UserModel user = UserUtil.getUser(this.getRequest());
		
		//当前用户id，要修改密码的用户id
		String userId = user.getUserId();
		//加密前的新密码
		String pw = getJsonPara("pw");
		//加密前的原密码
		String currentPw=getJsonPara("currentPw");
		if(StringUtils.isBlank(pw) || StringUtils.isBlank(currentPw)){
			return EasyResult.fail("修改密码失败:参数为空!");
		}
		
		//加密后的新密码
		String newPW=MD5Util.getHexMD5(getJsonPara("pw"));
		
		try {
			//查询出当前用户信息
			EasySQL sql = new EasySQL();
			sql.append("SELECT t1.EXT_CONF,t2.USER_PWD FROM ").append(getTableName("CC_BUSI_USER")).append(" t1 ");
			sql.append(" LEFT JOIN CC_USER t2 on t1.USER_ID = t1.USER_ID ");
			sql.append(userId," WHERE t1.USER_ID=?");
			sql.append(user.getEpCode()," AND t1.ENT_ID=?");
			sql.append(user.getBusiOrderId()," AND t1.BUSI_ORDER_ID =?");
			JSONObject currUser = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(currUser==null){
				return EasyResult.fail("修改密码失败:查询用户信息失败!");
			}
			
			//验证原密码是否正确
			if(!MD5Util.getHexMD5(currentPw).equalsIgnoreCase(currUser.getString("USER_PWD"))){
				return EasyResult.fail("当前密码错误！");
			}
			
			//update 20210408 修改密码时，兼容之前的老组织架构 CC_EC_USER；mars登录时会优化取CC_EC_USER里账号的密码
			updateCcEcUserPwd(userId,newPW);
			
			
			//修改用户密码
			EasyRecord record=new EasyRecord("CC_USER","USER_ID").setPrimaryValues(userId);
			record.set("USER_PWD", newPW);
			this.getQuery().update(record);
			
			//更新密码修改时间
			JSONObject extConf = currUser.getJSONObject("EXT_CONF");
			if(extConf==null){
				extConf = new JSONObject();
			}
			extConf.put("UPDATE_PWD_TIME", DateUtil.getCurrentDateStr());
			EasyRecord record2=new EasyRecord("CC_BUSI_USER","USER_ID","ENT_ID","BUSI_ORDER_ID").setPrimaryValues(userId,user.getEpCode(),user.getBusiOrderId());
			record2.put("EXT_CONF", extConf.toJSONString());
			this.getQuery().update(record2);
			
			this.info("用户["+user.getUserAcc()+"]修改密码->"+newPW+".", null);
			return EasyResult.ok(null,"密码修改成功！");
		} catch (Exception e) {
			this.error(e.getMessage(),e);
			return EasyResult.fail("修改密码失败:"+e.getMessage());
		}
	}
	
	public EasyResult actionForResetPw(){
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject jsonObject=getJSONObject();
		String userId = jsonObject.getString("userId");
		String newPw=jsonObject.getString("pwd");
		String pwd = MD5Util.getHexMD5(newPw);
		
		EasyRecord record=new EasyRecord("CC_USER","USER_ID").setPrimaryValues(userId);
		record.set("USER_PWD",pwd );
		try {
			//update 20210408 修改密码时，兼容之前的老组织架构 CC_EC_USER；mars登录时会优化取CC_EC_USER里账号的密码
			updateCcEcUserPwd(userId,pwd);
			
			this.info("用户["+user.getUserAcc()+"]重置用户的密码->"+userId+","+pwd+".", null);
			this.getQuery().update(record);
		} catch (Exception e) {
			this.error(e.getMessage(),e);
			return EasyResult.fail("重置密码失败:"+e.getMessage());
		}
		return EasyResult.ok(null,"重置成功!");
	}
	
	/**
	 * 更新CC_EC_USER表的密码
	 * 有些现场同时有CC_EC_USER和CC_USER表，两张表都需要更新；否则无法登录；因为登录时，先从CC_EC_USER表里查到用户账号和密码校验
	 * @param userId
	 * @param newPW
	 */
	private void updateCcEcUserPwd(String userId, String newPW) {
		try {
			EasyRecord record2=new EasyRecord("CC_EC_USER","USER_ID").setPrimaryValues(userId);
			record2.set("USER_PWD", newPW);
			getQuery().update(record2);
		} catch (Exception e) {
			this.error("修改密码异常,修改CC_EC_USER表,不影响使用CC_USER表的项目,"+ e.getMessage(),e);
		}
	}
	
	public EasyResult actionForUpdate(){
		JSONObject params = this.getJSONObject();
		EasyQuery query = this.getQuery();
		EasyResult result = new EasyResult();
		JSONObject jsonObject=JsonKit.getJSONObject(params, "user");
		String userId = jsonObject.getString("USER_ID");
		try {
			query.begin();
			EasyRecord easyRecord=new EasyRecord(getTableName("CC_BUSI_USER"),"USER_ID","BUSI_ORDER_ID");
			easyRecord.setColumns(jsonObject);
			easyRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			easyRecord.set("CREATOR", this.getUserPrincipal().getNickName());
			easyRecord.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			easyRecord.remove("MOBILE");
			easyRecord.remove("SALES_CODE");
			easyRecord.remove("PREFIX_NUM_TEXT");
			easyRecord.remove("F_PHONE_NUM");
			query.update(easyRecord);
			
			query.execute("update CC_USER set MOBILE = ?,SALES_CODE = ? where USER_ID = ? and ENT_ID = ?", new Object[]{jsonObject.getString("MOBILE"),jsonObject.getString("SALES_CODE"),userId,getEntId()});
			query.commit();
			result.setMsg("修改成功！");
			updatAagentConfig(userId);
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("修改失败！失败原因:"+e1.getMessage(), e1);
				result.addFail("修改失败！失败原因:"+e1.getMessage());
			}
			e.printStackTrace();
			this.error("修改失败！失败原因:"+e.getMessage(), e);
			result.addFail("修改失败！失败原因:"+e.getMessage());
		}
		return result;
	}
	/**
	 * 更新坐席配置
	 */
	private void updatAagentConfig(String userId){
		JSONObject jsonObject=getJSONObject("agentConfig");
		Set<String> keys=jsonObject.keySet();
		if(keys!=null&&keys.size()>0){
			for(String key:keys){
				String sql="update "+getTableName("cc_ent_config")+" set config_value = ? where CONFIG_TYPE = ? and USER_ID = ? and CONFIG_KEY = ?";
				try {
					int result=this.getQuery().executeUpdate(sql,jsonObject.getString(key),"AgentConfig",userId,key);
					if(result==0){
						EasyRecord record=new EasyRecord(getTableName("cc_ent_config"),"CONFIG_ID");
						record.setPrimaryValues(RandomKit.uuid());
						record.set("CONFIG_VALUE", jsonObject.getString(key));
						record.set("CONFIG_KEY", key);
						record.set("CONFIG_TYPE", "AgentConfig");
						record.set("USER_ID", userId);
						record.set("ENT_ID", getEntId());
						record.set("CREATOR", getUserPrincipal().getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
					}
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
	}
	
	
	/**
	 * 修改外呼状态
	 */
	public EasyResult actionForUpdateBound(){
		JSONObject json = this.getJSONObject();
		try {
			if("OUTBOUND".equals(json.getString("type"))){
				this.getQuery().execute("update "+getTableName("CC_BUSI_USER")+" set OUTBOUND = abs(OUTBOUND - 1) where USER_ID = ? and BUSI_ORDER_ID = ?", new Object[]{json.getString("userId"),this.getBusiOrderId()});
			}else if("INBOUND".equals(json.getString("type"))){
				this.getQuery().execute("update "+getTableName("CC_BUSI_USER")+" set INBOUND = abs(INBOUND - 1) where USER_ID = ? and BUSI_ORDER_ID = ?", new Object[]{json.getString("userId"),this.getBusiOrderId()});
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("修改失败，失败原因："+e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	/**
	 * 保存批量数据
	 * @throws SQLException
	 */
	public EasyResult actionForSaveBatch() throws SQLException{
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		try {
			JSONArray list = cache.get("USER_LIST_UPDATE_"+this.getEntId());
			if(list==null||list.size()<=0){
				renderJson(EasyResult.fail("读取数据为空!"));
				result.addFail("读取数据为空!");
				return result;
			}
			EasyRecord record=new EasyRecord("CC_BUSI_USER","USER_ID");
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			query.begin();
			if(list.size()>0){
				for (int i=0;i<list.size();i++) {
					JSONObject row=list.getJSONObject(i);
					query.execute("update " + getTableName("CC_BUSI_USER") + " set AGENT_NAME = ?,ROLE_ID = ?,PREFIX_NUM=? where USER_ID = ? and BUSI_ORDER_ID = ? and ENT_ID = ?",
							new Object[]{row.getString("AGENT_NAME"), row.getString("ROLE_ID"),row.getString("PREFIX"), row.getString("USER_ID"), busiOrderId, entId});
					query.execute("update CC_USER set MOBILE = ?,SALES_CODE = ? where USER_ID = ? and ENT_ID = ?", new Object[]{row.getString("MOBILE"),StringUtils.isNotBlank(row.getString("CODE"))?row.getString("CODE"):0,row.getString("USER_ID"),entId});
					//每五百条提交一次事务
					if(i%500 == 0){
						query.commit();
						query.begin();
					}
				}
			}
			query.commit();
			this.updateSkillUserCount();//更新技能组坐席数
			result.setMsg("批量修改成功");
		} catch (Exception e) {
			this.error("批量修改失败！失败原因：" + e.getMessage(), e);
			query.roolback();
			result.addFail("批量修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	/**
	 * 移除坐席的所有技能组关系
	 * @return
	 * @throws SQLException
	 */
	public EasyResult actionForDelAllGroup() throws SQLException{
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		JSONObject jsonObject = this.getJSONObject();
		String oldGroupIds = jsonObject.getString("oldGroupIds");
		String syncGroupInfo = null;
		try {
			if(StringUtils.isNotBlank(oldGroupIds)){
				String userId = jsonObject.getString("userId");
				String entId = getEntId();
				String busiOrderId = getBusiOrderId();
				//更新用户技能组集合
				String updateUserGroupSQL = "update " + getTableName("CC_BUSI_USER") + " set GROUP_LIST=? where ENT_ID=? AND BUSI_ORDER_ID=? AND USER_ID=?";
				query.executeUpdate(updateUserGroupSQL, new Object[]{"",entId,busiOrderId,userId});
				//移除技能组用户
				String delGroupUserSQL = "delete from " + getTableName("CC_SKILL_GROUP_USER") + " where ENT_ID=? AND BUSI_ORDER_ID=? AND USER_ID=? AND SKILL_GROUP_ID=?";
				for (String groupId : oldGroupIds.split(",")) {
					
					query.executeUpdate(delGroupUserSQL, new Object[]{entId,busiOrderId,userId,groupId});
					syncGroupInfo = this.syncGroupInfo(entId,groupId,"update");
					this.syncSkillCache(groupId);
				}
				this.updateSkillUserCount();
				if(syncGroupInfo != null){
					result.addFail(syncGroupInfo);
				}
				result.setMsg("移除技能组关系成功");
			}
		} catch (Exception e) {
			this.error("移除技能组关系失败！失败原因：" + e.getMessage(), e);
			result.addFail("移除技能组关系失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 团队长管理技能组
	 * @return
	 */
	public EasyResult actionForUpdateTeam(){
		JSONObject params = this.getJSONObject();
		String userId = params.getString("userId");
		JSONArray skillArray = params.getJSONArray("skillIds");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		EasyQuery query = this.getQuery();
		String skillName = "";
		try {
			query.executeUpdate("delete from "+getTableName("CC_BUSI_USER_GROUP") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and USER_ID = ?", new Object[]{entId,busiOrderId,userId});
			if(skillArray != null && skillArray.size() > 0){
				for(int i = 0; i < skillArray.size(); i++){
					String skillId = skillArray.getString(i);
					String[] split = skillId.split("_");
					query.executeUpdate("insert into "+getTableName("CC_BUSI_USER_GROUP")+" values(?,?,?,?,?)", new Object[]{RandomKit.orderId(),entId,busiOrderId,userId,split[0]});
					skillName += "，" + split[1];
				}
				skillName = skillName.substring(1);
			}
			query.execute("update "+getTableName("CC_BUSI_USER")+" set OWNER_GROUP_LIST = ? where USER_ID = ? and ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{skillName,userId,entId,busiOrderId});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("修改失败，失败原因："+e.getMessage());
		}
		return EasyResult.ok();
	}
	
	@Override
	protected String getResId() {
		return "rs_org_user";
	}
	
	/**
	 * 个人资料
	 * @return
	 */
	public JSONObject actionForGetUserInfo(){
		JSONObject json = new JSONObject();
		UserModel user = UserUtil.getUser(this.getRequest());
		String userAcc = user.getUserAcc();
		//姓名
		String userName = user.getUserName();
		//部门
		String deptName = user.getDeptName();
		//工号
		String userNo = user.getUserNo();
		//职位
		List<UserPosition> positions = user.getPositions();
		String userPosition = "";
		for(int i=0;positions!=null&&positions.size()>0&&i<positions.size()-1;i++){
			userPosition += positions.get(i).getName()+'、';
		}
		if(positions!=null&&positions.size()>0){
			userPosition += positions.get(positions.size()-1).getName();
		}
		json.put("userAcc", userAcc);
		json.put("userName", userName);
		json.put("deptName", deptName);
		json.put("userNo", userNo);
		json.put("entId", user.getEpCode());
		json.put("busiOrderId", user.getBusiOrderId());
		json.put("userPosition", userPosition);
		
		return EasyResult.ok(json.toString(), "");
	}
}
