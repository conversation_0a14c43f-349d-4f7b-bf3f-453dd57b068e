<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>外呼任务表</title>
	<style type="text/css">
		#dataList td{display:none;}
		#dataList td:nth-child(1){display: table-cell;}
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 客户列表</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">选择模板</span>	
									  <select data-value="${param.tempId}"  name="tempId"  data-mars-top="true" class="form-control input-sm" id="tempId" data-mars="custTemp.dict" onchange="TaskCustList.tashChange()"></select>
							   </div>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">客户名称</span>	
									  <input type="text" name="custName" class="form-control input-sm" style="width:100px">
							   </div>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">客户号码</span>	
									  <input type="text" name="custPhone" class="form-control input-sm" style="width:122px">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="TaskCustList.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content table-responsive">
		           	     <table style="min-width: 600px" class="table table-auto table-bordered table-hover table-condensed text-nowrap" id="tableHead" data-mars="taskObj.list">
                             <thead>
	                         	 <tr data-mars="taskObj.header" data-template="list-template2" id="data-c">
								      
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:TASK_NAME}}</td>                                         
											<td class="CUST_ID">{{:CUST_ID}}</td>                                         
											<td class="CUST_NAME">{{:CUST_NAME}}</td>                                         
											<td class="CUST_GROUP_NAME">{{:CUST_GROUP_NAME}}</td>                                         
											<td class="TEL_NUM1">{{call:TEL_NUM1 _TEL_NUM1 fn='getPhone'}}</td>                                         
											<td class="TEL_NUM2">{{call:TEL_NUM2 _TEL_NUM2 fn='getPhone'}}</td>                                         
											<td class="TEL_NUM3">{{call:TEL_NUM3 _TEL_NUM3 fn='getPhone'}}</td>                                         
											<td class="TEL_NUM4">{{call:TEL_NUM4 _TEL_NUM4 fn='getPhone'}}</td>                                           
											<td class="F1" title="{{:F1}}">{{:F1}}</td>                                         
											<td class="F2" title="{{:F2}}">{{:F2}}</td>                                         
											<td class="F3" title="{{:F3}}">{{:F3}}</td>                                         
											<td class="F4" title="{{:F4}}">{{:F4}}</td>                                         
											<td class="F5" title="{{:F5}}">{{:F5}}</td>                                         
											<td class="F6" title="{{:F6}}">{{:F6}}</td>                                         
											<td class="F7" title="{{:F7}}">{{:F7}}</td>                                         
											<td class="F8" title="{{:F8}}">{{:F8}}</td>                                         
											<td class="F9" title="{{:F9}}">{{:F9}}</td>                                         
											<td class="F10" title="{{:F10}}">{{:F10}}</td>                                         
											<td class="F11" title="{{:F11}}">{{:F11}}</td>                                         
											<td class="F12" title="{{:F12}}">{{:F12}}</td>                                         
											<td class="F13" title="{{:F13}}">{{:F13}}</td>                                         
											<td class="F14" title="{{:F14}}">{{:F14}}</td>                                         
											<td class="F15" title="{{:F15}}">{{:F15}}</td>                                         
											<td class="F16" title="{{:F16}}">{{:F16}}</td>                                         
											<td class="F17" title="{{:F17}}">{{:F17}}</td>                                         
											<td class="F18" title="{{:F18}}">{{:F18}}</td>                                         
											<td class="F19" title="{{:F19}}">{{:F19}}</td>                                         
											<td class="F20" title="{{:F20}}">{{:F20}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
                        	 <script id="list-template2" type="text/x-jsrender">
										<td>任务名称</td>
										{{for data}}
									       <th nowrap data-inuse="{{:inuse}}" data-data-type="{{:dataType}}" data-field="{{:field}}">{{:title}}</th>
										{{/for}}
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("TaskCustList");
		$(function(){
			$("#searchForm").render({success:function(){
				var length=$("#tempId option").length;
				if(length==0){
					layer.msg("无可选择的任务！");
					return;					
				}
				hand();
			}}) ;
		});
		function hand(){
			$('[data-mars] th').each(function(){
				var inuse=$(this).data("inuse");
				var field=$(this).data('field');
				var dataType=$(this).data('dataType');
				if(inuse==0||(dataType&&dataType=='hide')){
					$(this).hide();
					$("."+field).hide();
				}else{
					$("."+field).show();
					if(dataType&&dataType!=''){
						$("."+field).each(function(){
							var content=$(this).text();
							if(dataType=='phone'){
								$(this).text(advReplace(content,-4,3,'*'));
						    }if(dataType=='idcard'){
								$(this).text(advReplace(content,10,4,'*'));
						    }if(dataType=='userName'){
								$(this).text(advReplace(content,1,3,'*'));
						    }
						});
					}
				}
			});
		}
		TaskCustList.searchData=function(){
			$("#searchForm").find(".pageIndexV").val(-1)
		 	$(".ibox-content").render({data:form.getJSONObject("#searchForm"),success:function(){
		 		hand();
		 	}}); 
		}
		TaskCustList.tashChange=function(){
			var tempId=$("#tempId").val();
			location.href="${ctxPath}/pages/cust/cust-mgr.jsp?tempId="+tempId;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
