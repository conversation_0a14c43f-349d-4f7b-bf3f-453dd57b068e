<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业营销短信模板</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> <span class="portal-name">营销</span>短信模板管理</h5>
	             		      <div class="input-group input-group-sm pull-right">
							       <button type="button" class="btn btn-sm btn-success" onclick="SmsTemp.addData()">+申请模板</button>
							   </div>
							   <div class="input-group input-group-sm" style="width: 170px">
								      <span class="input-group-addon">模板名称</span>	
									  <input type="text" name="smsTempName" class="form-control input-sm">
							   </div>
			        		   <div class="input-group input-group-sm">
									<span class="input-group-addon">模板状态</span>	
									<select class="form-control input-sm" name="tempState" onchange="SmsTemp.searchData()" >
										<option value="">请选择</option>
									    <option value="0"> 正常</option>
									    <option value="1" data-class="label label-warning"> 停用</option>
									    <option value="9"> 待审批</option>
									</select>
							   </div>
							   <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="SmsTemp.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
							   <div class="input-group input-group-sm pull-right hidden">
							       <button type="button" class="btn btn-sm btn-success" onclick="SmsTemp.addData()">+新增</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="smsTemp.list">
                             <thead>
	                         	 <tr>
								      <th>模板名称</th>
								      <th>模板内容</th>
								      <th class="text-c">模板状态</th>
								      <th class="text-c">创建时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:SMS_TEMP_NAME}}</td>
											<td>{{cutText:CONTENT 100}}</td>
                                            <td class="text-c">{{getText:TEMP_STATE 'tempState'}}</td>
											<th class="text-c">{{:CREATE_TIME}}</th>
											<td class="text-c">
												<a href="javascript:void(0)" onclick="SmsTemp.editData('{{:SMS_TEMP_ID}}')" > 查看</a> 
												<a href="javascript:void(0)" class="hidden" onclick="SmsTemp.delData('{{:SMS_TEMP_ID}}')" > 移除</a>
                                            </td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("SmsTemp");
		
		SmsTemp.searchData=function(){
			$("#searchForm").searchData();
		}
		
		SmsTemp.addData=function(){
		    popup.layerShow({type:1,title:'企业'+portalName+'短信模板申请',offset:'20px',area:['550px','380px']},"${ctxPath}/pages/bigdata/data-mess-temp-edit.jsp",{});
		}
		SmsTemp.editData=function(smsTempId){
		    popup.layerShow({type:1,title:'企业'+portalName+'短信模板详情',offset:'20px',area:['550px','600px']},"${ctxPath}/pages/bigdata/data-mess-temp-edit.jsp",{smsTempId:smsTempId});
		}
		SmsTemp.delData=function(smsTempId){
			layer.confirm('当前短信模板将要被注销，是否继续？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		    	var data = {smsTempId:smsTempId};
		  		ajax.remoteCall("${ctxPath}/servlet/smsTemp?action=delete", data, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	SmsTemp.searchData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>