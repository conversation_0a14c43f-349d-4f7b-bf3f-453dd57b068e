<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>分配到技能组</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" method="post">
			      <input type="hidden" name="taskId" value="${param.taskId}">
			      <input type="hidden" name="taskState" value="${param.taskState}">
				  <table class="table table-auto table-bordered table-condensed" data-container="dataTbody" data-mars="task.taskGroupList" data-template="template-assign" >
	                   	<thead>
	                   		<tr>
	                   			<td style="width: 30%" class="text-c">技能组</td>
	                   			<td>已分配总数</td>
	                   			<td>已分配坐席数</td>
	                   			<td>已使用名单数</td>
	                   			<td>新增分配数</td>
	                   		</tr>
	                   	</thead>
	                    <tbody id="dataTbody">
	                    
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="TaskEdit.saveData()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
				</form>	
	                  <script id="template-assign" type="text/x-jsrender">
								{{for data}}								
									   <tr>
											<td class="text-c">{{:SKILL_GROUP_NAME}}</td>
											<td>{{:OBJ_COUNT}}</td>
											<td>{{:OBJ_COUNT-ASIGN_COUNT}}</td>
											<td>{{:OBJ_USE_COUNT}}</td>
											<td><input type="hidden" name="{{:TASK_GROUP_ID}}" value="{{:SKILL_GROUP_ID}}"/><input type="number" value="" style="width:80px;" name="group.{{:TASK_GROUP_ID}}" onkeyup="this.value=this.value.replace(/\D/g,'')" class="form-control input-sm"/></td>
									</tr>
								{{/for}}
 					 </script>
	
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("TaskEdit");
	
	TaskEdit.taskId='${param.taskId}';
	TaskEdit.taskType='${param.taskType}';
	TaskEdit.bdActId='${param.bdActId}';
	$(function(){
		$("#easyform").render();
	});
	TaskEdit.saveData = function(){
		var count=${param.count};//总待分配数
		var z=0;
		$("#easyform #dataTbody input[type='number']").each(function(){
			if($(this).val()!=''){
				z=z+parseInt($(this).val());
			}
		});
		if(z<=0){
			layer.msg("分配数必须大于0！");	
			return;
		}
		/* if(z>count){
			layer.msg("分配数"+z+"不能大于："+count+"");	
			return;
		} */
 		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/task/customer?action=taskAssign",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					popup.layerClose("#easyform");
					if(TaskEdit.bdActId.trim()!=""){
						if(TaskEdit.taskType=="1"){
							popup.openTab("${ctxPath}/pages/task/manual-list.jsp","人工外呼任务",{});
						}else if(TaskEdit.taskType=="2"){
							popup.openTab("${ctxPath}/pages/task/auto-list.jsp","自动外呼任务",{});
						}else if(TaskEdit.taskType=="3"){
							popup.openTab("${ctxPath}/pages/task/ivr-task-list.jsp","IVR语音任务",{});
						}else if(TaskEdit.taskType=="4"){
							popup.openTab("${ctxPath}/pages/task/msg-task-list.jsp","短信"+portalName+"任务",{});
						}
					}else{
						Task.loadData();
					}
				});
			}else{
			      layer.alert(result.msg);
			}
		  }
		);
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>