<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务工作时间</title>
	<style>
		#editForm input{max-width: 400px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <div class="ibox-content">
			<form id="editForm" data-mars="task.getSaasTaskExt" class="form-horizontal" method="post"  autocomplete="off" data-mars="">
			      <input type="hidden" name="taskId" value="${param.taskId }">
			       <input type="hidden" name="dataSources" value="${param.dataSource }">
				  <table class="table table-vzebra table-edit">
	                    <tbody>
	                        <tr>
	                            <td>启用状态</td>
	                            <td colspan="3">
									<label class="radio radio-inline radio-info">
									    <input type="radio" name="USE_FLAG" id="inlineRadio3" value="1"> <span>启用</span>
									</label>
									<label class="radio radio-inline radio-info">
										<input type="radio" name="USE_FLAG" id="inlineRadio2" checked="checked" value="0"> <span>不启用</span>
									</label>
	                            </td>
	                        </tr>
	                        <tr>
	                            <td>工作日</td>
	                            <td colspan="3">
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.MONDAY" value="1"> <span>周一</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.TUESDAY" value="1"> <span>周二</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.WEDNESDAY" value="1"> <span>周三</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.THURSDAY" value="1"> <span>周四</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.FRIDAY" value="1"> <span>周五</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.SATURDAY" value="1"> <span>周六</span>
									</label>
									<label class="checkbox checkbox-inline checkbox-info">
									    <input type="checkbox" name="WORKDAY_CONF.SUNDAY" value="1"> <span>周日</span>
									</label>
	                            </td>
	                        </tr>
	                         <tr>
	                         	<td colspan="2" style="color:red">可突破外呼工作日期：</td>
				                <td colspan="3" id="workExplainDate" style="color:red">{{:WORK_EXPLAIN_DATE}}</td>
	                        </tr>
	                         <tr>
	                         	<td colspan="2" style="color:red">OCS外呼工作时间：</td>
				                <td colspan="3" id="ocsWorkExplain" style="color:red">{{:OCS_WORK_EXPLAIN}}</td>
	                        </tr>
	                        <tr>
	                         	<td colspan="2" style="color:red">可外呼工作时间：</td>
				                <td colspan="3" id="workExplain" style="color:red">{{:WORK_EXPLAIN}}</td>
	                        </tr>
		                    <tr>
		                        <td width="120px">开始时间段1</td>
		                        <td><input type="text" name="BEGIN_TIME_1" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'end1\')}'})" id="start1" class="form-control input-sm Wdate"></td>
		                        <td width="120px">结束时间段1</td>
		                        <td><input type="text" name="END_TIME_1" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'start2\')}',minDate:'#F{$dp.$D(\'start1\')}'})" id="end1" class="form-control input-sm Wdate"></td>
			                </tr>
		                    <tr>
		                        <td>开始时间段2</td>
		                        <td><input type="text" name="BEGIN_TIME_2" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'end2\')}',minDate:'#F{$dp.$D(\'end1\')}'})" id="start2" class="form-control input-sm Wdate"></td>
		                        <td>结束时间段2</td>
		                        <td><input type="text" name="END_TIME_2" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'start3\')}',minDate:'#F{$dp.$D(\'start2\')}'})" id="end2" class="form-control input-sm Wdate"></td>
			                </tr>
		                    <tr>
		                        <td>开始时间段3</td>
		                        <td><input type="text" name="BEGIN_TIME_3" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'end3\')}',minDate:'#F{$dp.$D(\'end2\')}'})" id="start3" class="form-control input-sm Wdate"></td>
		                        <td>结束时间段3</td>
		                        <td><input type="text" name="END_TIME_3" onClick="WdatePicker({dateFmt:'HH:mm',minDate:'#F{$dp.$D(\'start3\')}'})" id="end3" class="form-control input-sm Wdate"></td>
			                </tr>
	                    </tbody>
                </table>
                
	            <p class="text-c layer-foot">
				    <button class="btn btn-sm btn-primary" style="width: 80px"  type="button" onclick="WorktimeEdit.ajaxSubmitForm()">  保  存  </button>
		        </p> 
		</form>	
    </div>	
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="${staticPath}/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
		var WorktimeEdit={};
		
		WorktimeEdit.initData = function(){
			$("#editForm").render({success:function(result){
				WorktimeEdit.data = result['task.getSaasTaskExt'].data;
				if(WorktimeEdit.data['WORKDAY_CONF']){
					fillRecord(WorktimeEdit.data['WORKDAY_CONF'],'WORKDAY_CONF.','','#editForm')
				}
				if($("#ocsWorkExplain").text()==""){
			 		$("#ocsWorkExplain").closest('tr').hide()
			 	}
				if($("#workExplain").text()==""){
			 		$("#workExplain").closest('tr').hide()
			 	}
				if($("#workExplainDate").text()==""){
			 		$("#workExplainDate").closest('tr').hide()
			 	}
				
			}});  
		}
		
		WorktimeEdit.ajaxSubmitForm = function(){
			 if(form.validate("#editForm")&&validateForm()){
				 WorktimeEdit.updateData();
			 }
		}
		WorktimeEdit.updateData = function(){
			var data = form.getJSONObject("#editForm");
			var workday = {}
			var params = {};
			data = $.extend({}, WorktimeEdit.data, data);
			for(var key in data){
				if(key.indexOf('WORKDAY_CONF.')>=0){
					workday[key.substring(13)]=1;
				}else{
					params[key]=data[key];
				}
			}
			params['WORKDAY_CONF']=workday;
			ajax.remoteCall("${ctxPath}/servlet/task?action=updateAuditExt",params,function(result) { 
				if(result.state == 1){
					popup.layerClose("#editForm");
					layer.alert(result.msg,{icon: 1});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function validateForm(){
			 var data = form.getJSONObject("#editForm");
			 if(data['BEGIN_TIME_1'] != '' && data['END_TIME_1'] == ''){
				 layer.alert('结束时间段1不能为空',{icon: 5});
				 return false;
			 }
			 if(data['BEGIN_TIME_1'] == '' && data['END_TIME_1'] != ''){
				 layer.alert('开始时间段1不能为空',{icon: 5});
				 return false;
			 }
			 
			 if(data['BEGIN_TIME_2'] != '' && data['END_TIME_2'] == ''){
				 layer.alert('结束时间段2不能为空',{icon: 5});
				 return false;
			 }
			 if(data['BEGIN_TIME_2'] == '' && data['END_TIME_2'] != ''){
				 layer.alert('开始时间段2不能为空',{icon: 5});
				 return false;
			 }
			 
			 if(data['BEGIN_TIME_3'] != '' && data['END_TIME_3'] == ''){
				 layer.alert('结束时间段3不能为空',{icon: 5});
				 return false;
			 }
			 if(data['BEGIN_TIME_3'] == '' && data['END_TIME_3'] != ''){
				 layer.alert('开始时间段3不能为空',{icon: 5});
				 return false;
			 }
			return true;
		}
		$(function(){
			WorktimeEdit.initData();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>