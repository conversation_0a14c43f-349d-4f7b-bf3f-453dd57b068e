<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发送短信</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="taskSmsForm" data-mars="common.smsTempRecord" data-mars-prefix="smsTemp.">
				<input type="hidden" name="objId" value="${param.objId}"/>
				<input type="hidden" name="smsTempId" value="${param.smsTempId}"/>
				<table class="table table-vzebra">
					<tr>
						<td style="width: 50px">
							客户号码
						</td>
						<td>
							<input type="hidden" value="${param.telNum }" id="telNum" name="telNum" class="form-control input-sm"/>
							<input type="text" id="showTelNum" class="form-control input-sm"/>
						</td>
					</tr>
					<tr id="url_tr" style="display: none;">
						<td style="width: 50px">
							链接
						</td>
						<td>
							<input  id="url" class="form-control input-sm" onblur="getUrl(this)"/>
						</td>
					</tr>
					<tr>
						<td>
							短信内容
						</td>
						<td>
							<textarea name="smsContent" id="smsContent" readonly="readonly" style="height: 130px" class="form-control input-sm"></textarea>
						</td>
					</tr>
				</table>
				
				<div class="layer-foot text-c">
				   		<button class="btn btn-sm btn-primary"  type="button" onclick="sendSms()">确认发送</button>
				   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			   </div>
		</form>	
	
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	var smsContent='';
	$(function(){
		$("#taskSmsForm").render({success:function(result){
			var data=result['common.smsTempRecord'].data;
			if(data){
				var singName="【"+data.SING_NAME+"】";
				smsContent=data.CONTENT;
				if(smsContent&&smsContent.indexOf("<url>")>-1){
					$("#url_tr").show();
				}else{
					
				}
				$("#smsContent").val(singName+smsContent);
				smsContent=$("#smsContent").val();
				var telNum = "${param.telNum}";
				$("#showTelNum").val(telNum);
			}
		}});
	});
	function getUrl(obj){
		var c=smsContent.replace('<url>',obj.value);
		$("#smsContent").val(c);
	}
	function sendSms(){
		if($("#url_tr").css("display")!="none"&&$("#url").val()==''){
			alert("请填写链接！");
			return;
		}
		if($("#telNum").val()==''){
			alert("号码不能为空！");
			return;
		}
		if($("#telNum").val().length<11){
			alert("号码位数不对！");
			return;
		}
		var data = form.getJSONObject("#taskSmsForm");
		ajax.remoteCall("${ctxPath}/servlet/taskObj?action=sendSms",data,function(result) { 
			if(result.state == 1){
				popup.layerClose("taskSmsForm");
				layer.msg(result.msg);
			}else{
			    layer.alert(result.msg);
			}
		  }
		);
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>