package com.yunqu.yc.agent.msg.event;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AiccAgentState;
import com.yunqu.yc.agent.msg.RequestData;

/**
 * 保持
 *
 * <AUTHOR>
 * 请求信息包括：
 */
public class HoldEvent extends AbstractEvent {


    public HoldEvent(String agentId) {
        super(agentId);
    }


    @Override
    public String checkRequestParam(RequestData requestData) {
        return null;
    }


    @Override
    public void initCmdEventParam(RequestData requestData) {
    }


    @Override
    public JSONObject getRequestBody() {
        return new JSONObject();
    }


    @Override
    public String getCmdEventName() {
        return "cmdHoldCall";
    }


    @Override
    public String getRespEventName() {
        return "respHoldCall";
    }

    @Override
    public String getRequestEventURI() {
        // 9.8 呼叫保持
        return "agentgateway/resource/voicecall/" + agentId + "/hold";
    }

    @Override
    public String getHttpMothed() {
        return "POST";
    }

    @Override
    public String getEventName() {
        return "hold";
    }


    @Override
    public boolean isFlashAgentState() {
        return true;
    }

    @Override
    public void doRespResult(JSONObject responseObj) {
        String agentStatus = agent.getAgentStatus();
        // 座席最后的状态
        agent.setLastStatus(agentStatus);
        // 当前状态
        agent.setAgentStatus(AiccAgentState.STATE_11);
    }
}
