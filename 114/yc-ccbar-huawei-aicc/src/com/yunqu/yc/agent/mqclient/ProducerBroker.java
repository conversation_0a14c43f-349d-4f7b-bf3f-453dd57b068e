package com.yunqu.yc.agent.mqclient;

import org.easitline.common.core.activemq.Broker;

import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.context.AppContext;

import com.yunqu.yc.agent.log.CcbarLogger;


public class ProducerBroker {
	
	
	public final static String YC_SMS_BROKER = "smsproxy-broker";
	private static  Broker broker = null;
	
	/**
	 * 获得用户所对应的MARS。
	 * @param agentId
	 * @return
	 */
	public synchronized static void sendSmsMessage(String msg) throws Exception{
		
		if(broker==null){
			String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
			broker =  BrokerFactory.getProducerQueueBroker(addr,YC_SMS_BROKER,"","");
		}
		CcbarLogger.getLogger().info("ProducerBroker.sendSmsMessage("+YC_SMS_BROKER+")->"+msg);
		broker.sendMessage(msg);
	}
	
	public static void close(){
		try {
			if(broker!=null) broker.close();
		} catch (Exception e) {
			// TODO: handle exception
		}
	}
	
}
