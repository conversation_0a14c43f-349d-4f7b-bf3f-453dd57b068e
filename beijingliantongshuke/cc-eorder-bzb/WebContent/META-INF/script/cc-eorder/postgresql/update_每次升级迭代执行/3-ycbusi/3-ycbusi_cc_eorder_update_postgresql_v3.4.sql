-- 20230301
alter table c_wf_process_node add NODE_TYPE varchar(20) ;
comment on column c_wf_process_node.NODE_TYPE is '节点类型';


-- 20230306
ALTER TABLE c_bo_order_ex ADD IS_IMPORTANT varchar(10);
alter table c_bo_order_ex alter column IS_IMPORTANT set default 'N';
COMMENT on column c_bo_order_ex.IS_IMPORTANT is '是否重点标记 Y-是 N-否';

/*==============================================================*/
/* Table: C_BO_ORDER_DEFINE                                     */
/*==============================================================*/
create table C_BO_ORDER_DEFINE
(
   ID                   varchar(64) not null ,
   ORDER_ID             varchar(64) null,
   CREATE_ACC           varchar(30) null,
   CREATE_NAME          varchar(50) null,
   CREATE_TIME          varchar(19) null,
   CREATE_DEPT_CODE     varchar(30) null,
   ENT_ID               varchar(30) null,
   BUSI_ORDER_ID        varchar(64) null,
   ALL_TYPE_NAME        varchar(500) null,
   REMARK               varchar(1000) null,
   constraint PK_C_BO_ORDER_DEFINE primary key (ID)
);

comment on column C_BO_ORDER_DEFINE.ID is 'ID';
comment on column C_BO_ORDER_DEFINE.ORDER_ID is '工单id';
comment on column C_BO_ORDER_DEFINE.CREATE_ACC is '工单创建人账号';
comment on column C_BO_ORDER_DEFINE.CREATE_NAME is '工单创建人姓名';
comment on column C_BO_ORDER_DEFINE.CREATE_TIME is '工单创建时间';
comment on column C_BO_ORDER_DEFINE.CREATE_DEPT_CODE is '工单创建人部门编号';
comment on column C_BO_ORDER_DEFINE.ENT_ID is '所属企业';
comment on column C_BO_ORDER_DEFINE.BUSI_ORDER_ID is '企业业务订购ID';
comment on column C_BO_ORDER_DEFINE.ALL_TYPE_NAME is '所有分类名称,将一级、二级、三级分类全部通过斜杠拼起来,用于展示';
comment on column C_BO_ORDER_DEFINE.REMARK is '备注';

comment on table C_BO_ORDER_DEFINE is '工单定性表，一条工单对应一条数据';

/*==============================================================*/
/* Index: IDX_ORDERDEFINE_1                                     */
/*==============================================================*/
create index IDX_ORDERDEFINE_1 on C_BO_ORDER_DEFINE
(
   ORDER_ID ASC
);

/*==============================================================*/
/* Index: IDX_ORDERDEFINE_2                                     */
/*==============================================================*/
create index IDX_ORDERDEFINE_2 on C_BO_ORDER_DEFINE
(
   CREATE_TIME ASC
);


/*==============================================================*/
/* Table: C_BO_ORDER_DEFINE_DETAIL                              */
/*==============================================================*/
create table C_BO_ORDER_DEFINE_DETAIL
(
   ID                   varchar(64) not null ,
   ORDER_ID             varchar(64) null,
   DEFINE_ID            varchar(64) null,
   ENT_ID               varchar(30) null,
   BUSI_ORDER_ID        varchar(64) null,
   ONE_TYPE             varchar(64) null,
   ONE_TYPE_NAME        varchar(100) null,
   TWO_TYPE             varchar(64) null,
   TWO_TYPE_NAME        varchar(100) null,
   THREE_TYPE           varchar(64) null,
   THREE_TYPE_NAME      varchar(100) null,
   FOUR_TYPE            varchar(64) null,
   FOUR_TYPE_NAME       varchar(100) null,
   FIVE_TYPE            varchar(64) null,
   FIVE_TYPE_NAME       varchar(100) null,
   constraint PK_C_BO_ORDER_DEFINE_DETAIL primary key (ID)
);

comment on column C_BO_ORDER_DEFINE_DETAIL.ID is 'ID';
comment on column C_BO_ORDER_DEFINE_DETAIL.ORDER_ID is '工单id';
comment on column C_BO_ORDER_DEFINE_DETAIL.DEFINE_ID is '定性id';
comment on column C_BO_ORDER_DEFINE_DETAIL.ENT_ID is '所属企业';
comment on column C_BO_ORDER_DEFINE_DETAIL.BUSI_ORDER_ID is '企业业务订购ID';
comment on column C_BO_ORDER_DEFINE_DETAIL.ONE_TYPE is '一级分类';
comment on column C_BO_ORDER_DEFINE_DETAIL.ONE_TYPE_NAME is '一级分类名称';
comment on column C_BO_ORDER_DEFINE_DETAIL.TWO_TYPE is '二级分类';
comment on column C_BO_ORDER_DEFINE_DETAIL.TWO_TYPE_NAME is '二级分类名称';
comment on column C_BO_ORDER_DEFINE_DETAIL.THREE_TYPE is '三级分类';
comment on column C_BO_ORDER_DEFINE_DETAIL.THREE_TYPE_NAME is '三级分类名称';
comment on column C_BO_ORDER_DEFINE_DETAIL.FOUR_TYPE is '四级分类';
comment on column C_BO_ORDER_DEFINE_DETAIL.FOUR_TYPE_NAME is '四级分类名称';
comment on column C_BO_ORDER_DEFINE_DETAIL.FIVE_TYPE is '五级分类';
comment on column C_BO_ORDER_DEFINE_DETAIL.FIVE_TYPE_NAME is '五级分类名称';

comment on table C_BO_ORDER_DEFINE_DETAIL is '工单定性明细表，一条工单对应多条明细数据，保存多选的每个细项';

/*==============================================================*/
/* Index: IDX_ORDERDEFINEDETAIL_1                               */
/*==============================================================*/
create index IDX_ORDERDEFINEDETAIL_1 on C_BO_ORDER_DEFINE_DETAIL
(
   ORDER_ID ASC
);

/*==============================================================*/
/* Index: IDX_ORDERDEFINEDETAIL_2                               */
/*==============================================================*/
create index IDX_ORDERDEFINEDETAIL_2 on C_BO_ORDER_DEFINE_DETAIL
(
   DEFINE_ID ASC
);


-- 20230323
alter table C_WF_PROCESS_NODE add ENABLE_CUST_TRANSER varchar(10);
alter table C_WF_PROCESS_NODE alter column ENABLE_CUST_TRANSER set default '2';
comment on column C_WF_PROCESS_NODE.ENABLE_CUST_TRANSER is '是否开启自定义转派人员 1-开启 2-禁用';



-- 20230330
/*==============================================================*/
/* Table: C_BO_ORDER_TOMAKEACOPY                                */
/*==============================================================*/
create table C_BO_ORDER_TOMAKEACOPY
(
   ID                   VARCHAR(64)         not null,
   ORDER_ID             VARCHAR(64),
   ORDER_NO             VARCHAR(64),
   CREATE_ACC           VARCHAR(30),
   CREATE_NAME          VARCHAR(50),
   CREATE_TIME          VARCHAR(19),
   COPY_ACC             VARCHAR(64),
   COPY_CONTENT         VARCHAR(500),
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_BO_ORDER_TOMAKEACOPY primary key (ID)
);

comment on table C_BO_ORDER_TOMAKEACOPY is
'工单抄送信息表';

comment on column C_BO_ORDER_TOMAKEACOPY.ORDER_ID is
'工单id';

comment on column C_BO_ORDER_TOMAKEACOPY.ORDER_NO is
'工单编号';

comment on column C_BO_ORDER_TOMAKEACOPY.CREATE_ACC is
'创建人账号';

comment on column C_BO_ORDER_TOMAKEACOPY.CREATE_NAME is
'创建人姓名';

comment on column C_BO_ORDER_TOMAKEACOPY.CREATE_TIME is
'创建时间';

comment on column C_BO_ORDER_TOMAKEACOPY.COPY_ACC is
'抄送人账号';

comment on column C_BO_ORDER_TOMAKEACOPY.COPY_CONTENT is
'抄送内容';

comment on column C_BO_ORDER_TOMAKEACOPY.ENT_ID is
'所属企业';

comment on column C_BO_ORDER_TOMAKEACOPY.BUSI_ORDER_ID is
'企业业务订购ID';

/*==============================================================*/
/* Index: IDX_ORDERTOMAKEACOPY_1                                */
/*==============================================================*/
create index IDX_ORDERTOMAKEACOPY_1 on C_BO_ORDER_TOMAKEACOPY (
   ORDER_ID ASC
);

-- 20230508
alter table c_bo_auto_form add  FORM_VERSION int DEFAULT 0 NOT NULL ;
comment on column c_bo_auto_form.FORM_VERSION is '表单版本';
alter table c_bo_auto_form add  FORM_STATUS varchar(2)   DEFAULT '1' NOT NULL;
comment on column c_bo_auto_form.FORM_STATUS is '表单状态（0-历史版本、1-当前版本、2-修改版本）';
alter table C_BO_AUTO_FORM drop constraint PK_C_BO_AUTO_FORM;
alter table C_BO_AUTO_FORM add constraint PK_C_BO_AUTO_FORM PRIMARY KEY (ID, FORM_VERSION,ENT_ID);

alter table C_WF_PROCESS_AUTH_DETAIL add TARGET_NAME varchar(100);
comment on column C_WF_PROCESS_AUTH_DETAIL.TARGET_NAME is '权限控制资源名称';
alter table C_WF_PROCESS_AUTH add STATUS varchar(10);
alter table C_WF_PROCESS_AUTH alter column STATUS set default '01';
comment on column C_WF_PROCESS_AUTH.STATUS is '启用状态 字典：ENABLE_STATUS 01-启用 02-禁用';


-- 20230516
alter table C_WF_PROCESS_NODE add ENABLE_AUTO_ASSIGN   varchar(10);
alter table C_WF_PROCESS_NODE alter column ENABLE_AUTO_ASSIGN set default 'N';
comment on column C_WF_PROCESS_NODE.ENABLE_AUTO_ASSIGN is '是否开启智能分配';
alter table C_WF_PROCESS_NODE add ASSIGN_OBJ           varchar(64);
comment on column C_WF_PROCESS_NODE.ASSIGN_OBJ is '分配对象';
alter table C_WF_PROCESS_NODE add OBJ_TYPE             varchar(20);
comment on column C_WF_PROCESS_NODE.OBJ_TYPE is '对象类型 assignee-处理人 candidateUsers-备选人 candidateDepts-备选部门 candidateGroups-备选工作组';
alter table C_WF_PROCESS_NODE add ASSIGN_TYPE          varchar(10);
alter table C_WF_PROCESS_NODE alter column ASSIGN_TYPE set default '02';
comment on column C_WF_PROCESS_NODE.ASSIGN_TYPE is '分配类型 01-在线坐席 02-所有坐席';
alter table C_WF_PROCESS_NODE add ASSIGN_MODE          varchar(10);
comment on column C_WF_PROCESS_NODE.ASSIGN_MODE is '分配模式 01-轮询分配 02-平均分配';



-- 20230523

/*==============================================================*/
/* Table: C_BO_MIND_DISTRIBUTE_LOG                              */
/*==============================================================*/
create table C_BO_MIND_DISTRIBUTE_LOG
(
   ID                   VARCHAR(64)         not null,
   ORDER_ID             VARCHAR(64)         not null,
   PROC_INST_ID         VARCHAR(64),
   TASK_ID              VARCHAR(64),
   ASSIGN_OBJ           VARCHAR(64),
   OBJ_TYPE             VARCHAR(20),
   ASSIGN_TYPE          VARCHAR(10)         default '02',
   ASSIGN_MODE          VARCHAR(10),
   NODE_ID              VARCHAR(64),
   NODE_KEY             VARCHAR(64),
   STATUS               VARCHAR(10),
   CREATE_TIME          VARCHAR(19),
   FIRST_ASSIGN_TIME    VARCHAR(19),
   ASSIGN_TIME          VARCHAR(19),
   ASSIGN_NUM           numeric               default 0,
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_BO_MIND_DISTRIBUTE_LOG primary key (ID)
);

comment on column C_BO_MIND_DISTRIBUTE_LOG.ID is
'ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ORDER_ID is
'工单ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG.PROC_INST_ID is
'流程实例ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG.TASK_ID is
'任务ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGN_OBJ is
'分配对象';

comment on column C_BO_MIND_DISTRIBUTE_LOG.OBJ_TYPE is
'对象类型
assignee-处理人
candidateUsers-备选人
candidateDepts-备选部门
candidateGroups-备选工作组';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGN_TYPE is
'分配类型 01-在线坐席 02-所有坐席';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGN_MODE is
'分配模式 01-轮询分配 02-平均分配';

comment on column C_BO_MIND_DISTRIBUTE_LOG.NODE_ID is
'节点id';

comment on column C_BO_MIND_DISTRIBUTE_LOG.NODE_KEY is
'节点key';

comment on column C_BO_MIND_DISTRIBUTE_LOG.STATUS is
'状态 01-待分配 02-已分配 03-已回收 04-分配异常';

comment on column C_BO_MIND_DISTRIBUTE_LOG.CREATE_TIME is
'创建时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG.FIRST_ASSIGN_TIME is
'首次分配时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGN_TIME is
'分配时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGN_NUM is
'分配次数';

comment on column C_BO_MIND_DISTRIBUTE_LOG.ENT_ID is
'所属企业';

comment on column C_BO_MIND_DISTRIBUTE_LOG.BUSI_ORDER_ID is
'业务订购ID';

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOG_1                            */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOG_1 on C_BO_MIND_DISTRIBUTE_LOG (
   CREATE_TIME ASC
);

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOG_2                            */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOG_2 on C_BO_MIND_DISTRIBUTE_LOG (
   NODE_ID ASC
);

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOG_3                            */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOG_3 on C_BO_MIND_DISTRIBUTE_LOG (
   ORDER_ID ASC
);

/*==============================================================*/
/* Table: C_BO_MIND_DISTRIBUTE_LOG_HIS                          */
/*==============================================================*/
create table C_BO_MIND_DISTRIBUTE_LOG_HIS
(
   ID                   VARCHAR(64)         not null,
   ORDER_ID             VARCHAR(64)         not null,
   PROC_INST_ID         VARCHAR(64),
   TASK_ID              VARCHAR(64),
   ASSIGN_OBJ           VARCHAR(64),
   OBJ_TYPE             VARCHAR(20),
   ASSIGN_TYPE          VARCHAR(10)         default '02',
   ASSIGN_MODE          VARCHAR(10),
   NODE_ID              VARCHAR(64),
   NODE_KEY             VARCHAR(64),
   STATUS               VARCHAR(10),
   CREATE_TIME          VARCHAR(19),
   FIRST_ASSIGN_TIME    VARCHAR(19),
   ASSIGN_TIME          VARCHAR(19),
   ASSIGN_NUM           numeric               default 0,
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_BO_MIND_DISTRIBUTE_LOG_HI primary key (ID)
);

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ID is
'ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ORDER_ID is
'工单ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.PROC_INST_ID is
'流程实例ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.TASK_ID is
'任务ID';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ASSIGN_OBJ is
'分配对象';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.OBJ_TYPE is
'对象类型
assignee-处理人
candidateUsers-备选人
candidateDepts-备选部门
candidateGroups-备选工作组';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ASSIGN_TYPE is
'分配类型 01-在线坐席 02-所有坐席';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ASSIGN_MODE is
'分配模式 01-轮询分配 02-平均分配';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.NODE_ID is
'节点id';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.NODE_KEY is
'节点key';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.STATUS is
'状态 01-待分配 02-已分配 03-已回收 04-分配异常';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.CREATE_TIME is
'创建时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.FIRST_ASSIGN_TIME is
'首次分配时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ASSIGN_TIME is
'分配时间';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ASSIGN_NUM is
'分配次数';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.ENT_ID is
'所属企业';

comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.BUSI_ORDER_ID is
'业务订购ID';

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOGHIS_1                         */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOGHIS_1 on C_BO_MIND_DISTRIBUTE_LOG_HIS (
   CREATE_TIME ASC
);

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOGHIS_2                         */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOGHIS_2 on C_BO_MIND_DISTRIBUTE_LOG_HIS (
   NODE_ID ASC
);

/*==============================================================*/
/* Index: IDX_CWFMINDDISTRIBUTELOGHIS_3                         */
/*==============================================================*/
create index IDX_CWFMINDDISTRIBUTELOGHIS_3 on C_BO_MIND_DISTRIBUTE_LOG_HIS (
   ORDER_ID ASC
);



-- 20230525
alter table C_BO_ORDER_PUSH_RECORD alter column EX_JSON type VARCHAR(2000);



-- 20230530
alter table C_BO_BASE_ORDER add CONTENT varchar(2000);
comment on column C_BO_BASE_ORDER.CONTENT is '工单内容';
alter table C_BO_BASE_ORDER_HIS add CONTENT varchar(2000);
comment on column C_BO_BASE_ORDER_HIS.CONTENT is '工单内容';


-- 20230612
alter table C_BO_BASE_ORDER drop index IDX_C_BO_BASE_ORDER_1;
create index IDX_C_BO_BASE_ORDER_1 on C_BO_BASE_ORDER (
   END_TIME ASC
);

-- 20230629
ALTER TABLE c_wf_node_exconfig ADD CONSTRAINT c_wf_node_exconfig_pk PRIMARY KEY (ID);


-- 20230713
comment on table c_bo_auto_form_db_field is '自定义表单数据源字段信息';
comment on column c_bo_auto_form_db_field.ID is '唯一标识';
comment on column c_bo_auto_form_db_field.SORT is '序号';
comment on column c_bo_auto_form_db_field.EXT_CONFIG is '展示类型,BO_FORM_FIELD_SHOWTYPE 1-单行文本框 2-多行文本框 3-计数器 4-单选框 5-多选框 6-时间选择器  7-日期选择器 8-下拉选择框 9-开关 10-附件 11-文字 12-工作组 13-工单编号';
comment on column c_bo_auto_form_db_field.BIND_MODULE_TYPE is '绑定模型类型';
comment on column c_bo_auto_form_db_field.SEARCH_EXTCONFIG is '查询扩展配置';


comment on table c_bo_grant_auth_record is '工单授权信息记录表';
comment on column c_bo_grant_auth_record.ID is '唯一标识';

comment on table c_bo_mind_distribute_log is '智能分配日志表';
comment on table c_bo_mind_distribute_log_his is '智能分配日志历史表';

comment on table c_bo_order_ext is '工单基础信息扩张表';


CREATE TABLE c_bo_order_field_setting (
  ID VARCHAR(64) NOT NULL,
  FIELD_CODE VARCHAR(30),
  FIELD_NAME VARCHAR(30),
  FIELD_TEXT VARCHAR(50),
  FLOW_KEY VARCHAR(50),
  ENT_ID VARCHAR(10),
  BUSI_ORDER_ID VARCHAR(64),
  CONSTRAINT PK_c_bo_order_field_setting PRIMARY KEY (ID)
);
comment on table c_bo_order_field_setting is '工单字段配置表';
comment on column c_bo_order_field_setting.ID is '唯一标识';
comment on column c_bo_order_field_setting.FIELD_CODE is '字段编码';
comment on column c_bo_order_field_setting.FIELD_NAME is '字段名称';
comment on column c_bo_order_field_setting.FIELD_TEXT is '字段说明';
comment on column c_bo_order_field_setting.FLOW_KEY is '流程编码';
comment on column c_bo_order_field_setting.ENT_ID is '企业编号';
comment on column c_bo_order_field_setting.BUSI_ORDER_ID is '业务订购id';

comment on table c_bo_order_key is '工单字段定义表';
comment on column c_bo_order_key.ID is '唯一标识';

comment on table c_bo_order_private_ext is '工单个人扩展信息';
comment on column c_bo_order_private_ext.ID is '唯一标识';

comment on table C_BO_ORDER_TAG is '工单自定义标签';
comment on column C_BO_ORDER_TAG.ID is '唯一标识';

comment on table c_bo_order_tag_ref is '工单标签关联表';
comment on column c_bo_order_tag_ref.ID is '唯一标识';

comment on table c_bo_question_type is '工单问题类型维护';

comment on table c_wf_list_config is '工单列表字段配置表';
comment on column c_wf_list_config.id is '唯一标识';
comment on column c_wf_list_config.ent_id is '企业编号';
comment on column c_wf_list_config.busi_order_id is '业务订购id';

comment on table c_wf_process_auth_detail is '流程权限明细';
comment on column c_wf_process_auth_detail.ID is '唯一标识';
comment on column c_wf_process_auth_detail.PROCESS_AUTH_ID is '流程权限配置Id';


comment on table c_wf_process_flowvar is '流程变量表';
comment on column c_wf_process_flowvar.ID is '唯一标识';

comment on table c_wf_process_node_auth is '流程节点权限配置';
comment on column c_wf_process_node_auth.ID is '唯一标识';

comment on column c_bo_base_order.DURATION is '工单处理历时';
comment on column c_bo_base_order.TOTAL_DURATION is '工单总历时';
comment on column c_bo_base_order.DURATION_EX is '扣除休息期间的处理历时';
comment on column c_bo_base_order.TOTAL_DURATION_EX is '扣除休息期间的工单总历时';
comment on column c_bo_base_order.DURATION_BUSI is '当时业务节点处理历时';


comment on column c_bo_base_order_his.PROC_NAME is '流程名称';

comment on column c_bo_order_follow.CREATE_ACC is '创建人账号';
comment on column c_bo_order_follow.ARRIVAL_TIME is '到达时间';
comment on column c_bo_order_follow.PLAN_DONE_TIME is '计划完成时间';
comment on column c_bo_order_follow.TIMEOUT is '超时时长';

comment on column c_bo_order_push_record.EP_CODE is '企业编号';
comment on column c_bo_order_push_record.EX_JSON is '扩展参数字段, json格式';

comment on column c_bo_order_visit.ID is '唯一标识';
comment on column c_bo_order_visit.MU_VISIT_USER is '人工回访人账号';
comment on column c_bo_order_visit.MU_VISIT_USER_NAME is '人工回访人姓名';
comment on column c_bo_order_visit.MU_VISIT_DEPT_ID is '人工回访部门编号';
comment on column c_bo_order_visit.MU_VISIT_DEPT_NAME is '人工回访部门名称';
comment on column c_bo_order_visit.MU_VISIT_TIME is '人工回访时间';
comment on column c_bo_order_visit.MU_VISIT_OPINION is '人工回访选项';
comment on column c_bo_order_visit.MU_VISIT_FAIL_NUMS is '人工回访不满意次数';
comment on column c_bo_order_visit.MU_VISIT_NUMS is '人工回访次数';
comment on column c_bo_order_visit.MU_VISIT_QUESTION_DETAIL is '人工回访问题描述';
comment on column c_bo_order_visit.MU_VISIT_QUESTION_RESOURCE is '人工回访问题原因';
comment on column c_bo_order_visit.MU_VISIT_QUESTION_TYPE is '人工回访问题类型';
comment on column c_bo_order_visit.AUTO_VISIT_TIME is '自动回访时间';
comment on column c_bo_order_visit.AUTO_VISIT_FAIL_NUMS is '自动回访不满意次数';
comment on column c_bo_order_visit.AUTO_VISIT_NUMS is '自动回访次数';
comment on column c_bo_order_visit.BASE_ORDER_ID is '工单基础表id';


comment on column c_bo_order_visit_his.ID is '唯一标识';
comment on column c_bo_order_visit_his.MU_VISIT_USER is '人工回访人账号';
comment on column c_bo_order_visit_his.MU_VISIT_USER_NAME is '人工回访人姓名';
comment on column c_bo_order_visit_his.MU_VISIT_DEPT_ID is '人工回访部门编号';
comment on column c_bo_order_visit_his.MU_VISIT_DEPT_NAME is '人工回访部门名称';
comment on column c_bo_order_visit_his.MU_VISIT_TIME is '人工回访时间';
comment on column c_bo_order_visit_his.MU_VISIT_OPINION is '人工回访选项';
comment on column c_bo_order_visit_his.MU_VISIT_FAIL_NUMS is '人工回访不满意次数';
comment on column c_bo_order_visit_his.MU_VISIT_NUMS is '人工回访次数';
comment on column c_bo_order_visit_his.MU_VISIT_QUESTION_DETAIL is '人工回访问题描述';
comment on column c_bo_order_visit_his.MU_VISIT_QUESTION_RESOURCE is '人工回访问题原因';
comment on column c_bo_order_visit_his.MU_VISIT_QUESTION_TYPE is '人工回访问题类型';
comment on column c_bo_order_visit_his.AUTO_VISIT_TIME is '自动回访时间';
comment on column c_bo_order_visit_his.AUTO_VISIT_FAIL_NUMS is '自动回访不满意次数';
comment on column c_bo_order_visit_his.AUTO_VISIT_NUMS is '自动回访次数';
comment on column c_bo_order_visit_his.BASE_ORDER_ID is '工单基础表id';

comment on column c_wf_process.TYPE is '流程分类';

comment on column c_wf_process_auth.ID is '唯一标识';
comment on column c_wf_process_auth.PROCESS_ID is '流程定义表id';
comment on column c_wf_process_auth.FLOW_KEY is '流程key';
comment on column c_wf_process_auth.PROCESS_NODE_ID is '流程节点id';
comment on column c_wf_process_auth.NODE_KEY is '流程节点key';

comment on column c_wf_process_listener.VALUE is '流程监听器的值';
comment on column c_wf_process_listener.CREATE_USER is '创建人账号';
comment on column c_wf_process_listener.CREATE_USER_NAME is '创建人名称';
comment on column c_wf_process_listener.CREATE_TIME is '创建时间';
comment on column c_wf_process_listener.UPDATE_USER is '修改人账号';
comment on column c_wf_process_listener.UPDATE_USER_NAME is '修改人姓名';
comment on column c_wf_process_listener.UPDAETIME is '修改时间';
comment on column c_wf_process_listener.BAKUP is '备注';

-- 2023-08-09
alter table C_BO_AUTO_FORM drop constraint PK_C_BO_AUTO_FORM;
alter table C_BO_AUTO_FORM add constraint PK_C_BO_AUTO_FORM PRIMARY KEY (ID, FORM_VERSION,ENT_ID,PROCESS_ID);


-- 2023-08-21
alter table C_BO_MIND_DISTRIBUTE_LOG add NODE_NAME varchar(100);
comment on column C_BO_MIND_DISTRIBUTE_LOG.NODE_NAME is '节点名称';
alter table C_BO_MIND_DISTRIBUTE_LOG_HIS add NODE_NAME varchar(100);
comment on column C_BO_MIND_DISTRIBUTE_LOG_HIS.NODE_NAME is '节点名称';


-- 2023-08-24
alter table C_BO_ORDER_EX add READ_TIME varchar(19);
comment on column C_BO_ORDER_EX.READ_TIME is '读取时间';

-- 20230918
alter table C_BO_ORDER_CUIBAN modify HANDLE_ACC varchar(64);

-- 20231008
alter table C_BO_AUTO_FORM_DB_FIELD add ENABLE_CRYPT varchar(10);
comment on column C_BO_AUTO_FORM_DB_FIELD.ENABLE_CRYPT is '是否开启加密, Y-是 N-否';

-- 20231017
alter table C_BO_MIND_DISTRIBUTE_LOG add ASSIGNED_ACC varchar(64);
comment on column C_BO_MIND_DISTRIBUTE_LOG.ASSIGNED_ACC is '被分配账号';

-- 20231108
alter table C_WF_PROCESS_NODE add SORT_INDEX numeric;
alter table C_WF_PROCESS_NODE alter column SORT_INDEX set default 0;
comment on column C_WF_PROCESS_NODE.SORT_INDEX is '排序序号';

-- 20231225
create index IDX_C_BO_BASE_ORDER_2 on C_BO_BASE_ORDER (
   CREATE_TIME ASC
);
create index IDX_C_BO_BASE_ORDER_HIS_2 on C_BO_BASE_ORDER_HIS (
   CREATE_TIME ASC
);

-- 20240118
alter table C_BO_ORDER_EX add IS_TIMEOUT varchar(10);
alter table C_BO_ORDER_EX alter column IS_TIMEOUT set default 'N';
comment on column C_BO_ORDER_EX.IS_TIMEOUT is '是否超时, Y-是 N-否';

-- 20240228
alter table C_BO_BASE_ORDER add MAX_CUIBAN_TIME varchar(19);
comment on column C_BO_BASE_ORDER.MAX_CUIBAN_TIME is '最近催办时间';
alter table C_BO_BASE_ORDER_HIS add MAX_CUIBAN_TIME varchar(19);
comment on column C_BO_BASE_ORDER_HIS.MAX_CUIBAN_TIME is '最近催办时间';

-- 20240308

create table c_bo_order_template
(
  id                  VARCHAR(64) not null,
  order_id            VARCHAR(64) not null,
  order_no            VARCHAR(50) not null,
  process_instance_id VARCHAR(64),
  create_user         VARCHAR(64) not null,
  create_time         VARCHAR(19),
  create_user_name    VARCHAR(100),
  create_dept         VARCHAR(64),
  create_dept_name    VARCHAR(100),
  ep_code             VARCHAR(30),
  busi_order_id       VARCHAR(64),
  tag                 VARCHAR(500),
  type                VARCHAR(10),
  remarks             VARCHAR(500),
constraint PK_c_bo_order_template primary key (ID)
)

;

comment on table c_bo_order_template
  is '模版工单'

;

comment on column c_bo_order_template.id
  is 'ID'

;

comment on column c_bo_order_template.order_id
  is '工单ID'

;

comment on column c_bo_order_template.order_no
  is '工单编号'

;

comment on column c_bo_order_template.process_instance_id
  is '流程实例ID'

;

comment on column c_bo_order_template.create_user
  is '创建人'

;

comment on column c_bo_order_template.create_time
  is '创建时间'

;

comment on column c_bo_order_template.create_user_name
  is '创建人名称'

;

comment on column c_bo_order_template.create_dept
  is '创建人部门'

;

comment on column c_bo_order_template.create_dept_name
  is '创建人部门名称'

;

comment on column c_bo_order_template.ep_code
  is '所属企业'

;

comment on column c_bo_order_template.busi_order_id
  is '企业业务订购ID'

;
comment on column c_bo_order_template.tag
  is '标签'

;
comment on column c_bo_order_template.type
  is '类型01个人 02共用'

;
comment on column c_bo_order_template.remarks
  is '备注'

;

create index IDX_TEMPLATE_ORDER on c_bo_order_template (ORDER_ID, ORDER_NO)

;

create index IDX_TEMPLATE_USER on c_bo_order_template (CREATE_TIME)

;

create index IDX_TEMPLATE_TIME on c_bo_order_template (CREATE_USER, CREATE_DEPT)

;


alter table C_BO_ORDER_EX add LOCK_STATUS varchar(10);
alter table C_BO_ORDER_EX alter column LOCK_STATUS set default '00';
comment on column C_BO_ORDER_EX.LOCK_STATUS is '锁定状态 00-无需锁定 01-待锁定 02-已锁定';


-- 20240311

create table c_bo_order_content_template
(
  id                  VARCHAR(64) not null,
  flow_key            VARCHAR(64) not null,
  create_user         VARCHAR(64) ,
  create_time         VARCHAR(19),
  create_user_name    VARCHAR(100),
  create_dept         VARCHAR(64),
  create_dept_name    VARCHAR(100),
  update_user         VARCHAR(64) ,
  update_time         VARCHAR(19),
  ep_code             VARCHAR(30),
  busi_order_id       VARCHAR(64),
  content             VARCHAR(500),
  title               VARCHAR(500),
  is_public           VARCHAR(10),
 busi_type            VARCHAR(10),
 sort                 VARCHAR(10),
 constraint PK_c_bo_order_content_template primary key (ID)
)

;

comment on table c_bo_order_content_template
  is '工单内容模版'

;

comment on column c_bo_order_content_template.id
  is 'ID'

;

comment on column c_bo_order_content_template.flow_key
  is '流程key'

;

comment on column c_bo_order_content_template.update_user
  is '修改人'

;

comment on column c_bo_order_content_template.update_time
  is '修改时间'

;
comment on column c_bo_order_content_template.create_user
  is '创建人'

;

comment on column c_bo_order_content_template.create_time
  is '创建时间'

;

comment on column c_bo_order_content_template.create_user_name
  is '创建人名称'

;

comment on column c_bo_order_content_template.create_dept
  is '创建人部门'

;

comment on column c_bo_order_content_template.create_dept_name
  is '创建人部门名称'

;

comment on column c_bo_order_content_template.ep_code
  is '所属企业'

;

comment on column c_bo_order_content_template.busi_order_id
  is '企业业务订购ID'

;
comment on column c_bo_order_content_template.content
  is '内容'

;
comment on column c_bo_order_content_template.title
  is '标题'

;
comment on column c_bo_order_content_template.is_public
  is '是否公开  数据字典 SF_YN  Y-是 N-否'

;
comment on column c_bo_order_content_template.busi_type
  is '业务类型C_ORDER_TEMPLATE_TYPE'

;
comment on column c_bo_order_content_template.sort
  is '序号'

;

create index IDX_TEMPLATE_FLOW_KEY on c_bo_order_content_template (FLOW_KEY)

;

create index IDX_TEMPLATE_TIME on c_bo_order_content_template (CREATE_TIME)

;


-- 20240313
alter table C_BO_ORDER_EX add CLAIM_TIME varchar(19) comment '分配时间';
comment on column C_BO_ORDER_EX.CLAIM_TIME is '分配时间';


-- 20240314
/*==============================================================*/
/* Table: C_BO_AUTO_FORM_SHOW_FIELD                             */
/*==============================================================*/
create table C_BO_AUTO_FORM_SHOW_FIELD 
(
   ID                   VARCHAR(64)         not null,
   CREATE_NAME          VARCHAR(50),
   CREATE_ACC           VARCHAR(30),
   CREATE_DATE          VARCHAR(19),
   AUTO_FORM_DB_ID      VARCHAR(64),
   FIELD_NAME           VARCHAR(30),
   FIELD_TEXT           VARCHAR(100),
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   GENERATE_TYPE        VARCHAR(10),
   STATUS               VARCHAR(10)         default '01',
   SORT                 int              default 1,
   IS_SHOW_FIELD        VARCHAR(10)         default 'N',
   IS_SEARCH_FIELD      VARCHAR(10)         default 'N',
   SEARCH_EXTCONFIG     VARCHAR(4000),
   PROCESS_ID           VARCHAR(64),
   constraint PK_C_BO_AUTO_FORM_SHOW_FIELD primary key (ID)
);

comment on table C_BO_AUTO_FORM_SHOW_FIELD is
'自定义表单数据源字段信息';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.ID is
'ID';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.CREATE_NAME is
'创建人名称';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.CREATE_ACC is
'创建人账号';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.CREATE_DATE is
'创建时间';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.AUTO_FORM_DB_ID is
'数据源ID';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.FIELD_NAME is
'字段名';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.FIELD_TEXT is
'字段文本';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.ENT_ID is
'企业ID';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.BUSI_ORDER_ID is
'订购ID';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.GENERATE_TYPE is
'字段生成数据方式';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.STATUS is
'启用状态，01-启用 02-禁用';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.SORT is
'序号';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.IS_SHOW_FIELD is
'是否显示字段 Y-是 N-否';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.IS_SEARCH_FIELD is
'是否查询字段 Y-是 N-否';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.SEARCH_EXTCONFIG is
'查询扩展配置';

comment on column C_BO_AUTO_FORM_SHOW_FIELD.PROCESS_ID is
'流程记录ID';

/*==============================================================*/
/* Index: IDX_AUTOFORMSHOWFIELD_1                               */
/*==============================================================*/
create index IDX_AUTOFORMSHOWFIELD_1 on C_BO_AUTO_FORM_SHOW_FIELD (
   PROCESS_ID ASC
);

/*==============================================================*/
/* Index: IDX_AUTOFORMSHOWFIELD_2                               */
/*==============================================================*/
create index IDX_AUTOFORMSHOWFIELD_2 on C_BO_AUTO_FORM_SHOW_FIELD (
   AUTO_FORM_DB_ID ASC,
   FIELD_NAME ASC
);


-- 20240326
alter table C_WF_PROCESS_NODE add DISTRIBUTION_TYPE    varchar(10);
comment on column C_WF_PROCESS_NODE.DISTRIBUTION_TYPE is '分发策略';
alter table C_WF_PROCESS_NODE add DISTRIBUTION_SERVICE varchar(50);
comment on column C_WF_PROCESS_NODE.DISTRIBUTION_SERVICE is '分发服务';
alter table C_WF_PROCESS_NODE add ASSIGN_SERVICE       varchar(50);
comment on column C_WF_PROCESS_NODE.ASSIGN_SERVICE is '分配服务';

-- 20240419
alter table C_BO_ORDER_CUIBAN add CHANNELS_KEY varchar(255);
comment on column C_BO_ORDER_CUIBAN.CHANNELS_KEY is '通知渠道key';
alter table C_BO_ORDER_CUIBAN add CHANNELS_NAME varchar(255);
comment on column C_BO_ORDER_CUIBAN.CHANNELS_NAME is '通知渠道名称';
alter table C_BO_ORDER_PUSH_RECORD add NOTICE_NODE_TYPES varchar(255);
comment on column C_BO_ORDER_PUSH_RECORD.NOTICE_NODE_TYPES is '推送通知渠道';


-- 20240429
alter table C_BO_BASE_ORDER add DATE_ID varchar(8);
comment on column C_BO_BASE_ORDER.DATE_ID is '日期';
alter table C_BO_BASE_ORDER_HIS add DATE_ID varchar(8);
comment on column C_BO_BASE_ORDER_HIS.DATE_ID is '日期';


-- 20240506
alter table C_BO_AUTO_FORM_DB_FIELD add IS_IMPORT_FIELD varchar(10);
alter table C_BO_AUTO_FORM_DB_FIELD alter column IS_IMPORT_FIELD set default 'N';
comment on column C_BO_AUTO_FORM_DB_FIELD.IS_IMPORT_FIELD is '是否导入字段 Y-是 N-否';
alter table C_BO_AUTO_FORM_SHOW_FIELD add IS_IMPORT_FIELD varchar(10);
alter table C_BO_AUTO_FORM_SHOW_FIELD alter column IS_IMPORT_FIELD set default 'N';
comment on column C_BO_AUTO_FORM_SHOW_FIELD.IS_IMPORT_FIELD is '是否导入字段 Y-是 N-否';



-- 20240520
create table C_BO_TEMPLATE 
(
   TEMPLATE_ID          VARCHAR(64)         not null,
   TEMPLATE_NAME        VARCHAR(128),
   TEMPLATE_DESC        VARCHAR(256),
   SORT_NUM             INTEGER,
   DATE_SOURCE          VARCHAR(10)         default '01',
   DATA_VALUE           VARCHAR(2000),
   FORM_COUNT           INTEGER              default 0,
   REF_FLOW_KEY         VARCHAR(2000),
   REF_FLOW_NAME        VARCHAR(2000),
   ENABLE_STATUS        VARCHAR(10),
   CREATE_TIME          VARCHAR(19),
   CREATE_ACC           VARCHAR(32),
   CREATE_NAME          VARCHAR(128),
   UPDATE_TIME          VARCHAR(19),
   UPDATE_ACC           VARCHAR(32),
   UPDATE_NAME          VARCHAR(128),
   ENT_ID               VARCHAR(32),
   BUSI_ORDER_ID        VARCHAR(64),
   DB_ID                VARCHAR(64),
   constraint PK_C_BO_TEMPLATE primary key (TEMPLATE_ID)
);
comment on table C_BO_TEMPLATE is
'表单模板信息';
comment on column C_BO_TEMPLATE.TEMPLATE_ID is
'模板ID';
comment on column C_BO_TEMPLATE.TEMPLATE_NAME is
'模板名称';
comment on column C_BO_TEMPLATE.TEMPLATE_DESC is
'描述';
comment on column C_BO_TEMPLATE.SORT_NUM is
'序号';
comment on column C_BO_TEMPLATE.DATE_SOURCE is
'数据来源 EORDER_TEMPLATE_DATA_SOURCE';
comment on column C_BO_TEMPLATE.DATA_VALUE is
'数据值，根据来源配置不同内容';
comment on column C_BO_TEMPLATE.FORM_COUNT is
'表单数量';
comment on column C_BO_TEMPLATE.REF_FLOW_KEY is
'关联流程ID（冗余）';
comment on column C_BO_TEMPLATE.REF_FLOW_NAME is
'关联流程名称(冗余)';
comment on column C_BO_TEMPLATE.ENABLE_STATUS is
'状态 01-启用 02-禁用';
comment on column C_BO_TEMPLATE.CREATE_TIME is
'创建时间';
comment on column C_BO_TEMPLATE.CREATE_ACC is
'创建人';
comment on column C_BO_TEMPLATE.CREATE_NAME is
'创建人名称';
comment on column C_BO_TEMPLATE.UPDATE_TIME is
'更新时间';
comment on column C_BO_TEMPLATE.UPDATE_ACC is
'更新人';
comment on column C_BO_TEMPLATE.UPDATE_NAME is
'更新人姓名';
comment on column C_BO_TEMPLATE.ENT_ID is
'企业ID';
comment on column C_BO_TEMPLATE.BUSI_ORDER_ID is
'业务订购ID';
comment on column C_BO_TEMPLATE.DB_ID is
'关联数据源ID';
create index IDX_BOTEMPLATE_1 on C_BO_TEMPLATE (
   DB_ID ASC
);

create table C_BO_TEMPLATE_FORM 
(
   FORM_ID              VARCHAR(32)         not null,
   FORM_NAME            VARCHAR(128),
   TEMPLATE_ID          VARCHAR(32),
   TEMPLATE_NAME        VARCHAR(128),
   SELECT_VAL           VARCHAR(64),
   FORM_CONFIG          CLOB,
   CREATE_TIME          VARCHAR(19),
   CREATE_ACC           VARCHAR(32),
   CREATE_NAME          VARCHAR(128),
   UPDATE_TIME          VARCHAR(19),
   UPDATE_ACC           VARCHAR(32),
   UPDATE_NAME          VARCHAR(128),
   ENT_ID               VARCHAR(32),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_BO_TEMPLATE_FORM primary key (FORM_ID)
);
comment on table C_BO_TEMPLATE_FORM is
'模板表单配置信息';
comment on column C_BO_TEMPLATE_FORM.FORM_ID is
'表单ID';
comment on column C_BO_TEMPLATE_FORM.FORM_NAME is
'表单名称';
comment on column C_BO_TEMPLATE_FORM.TEMPLATE_ID is
'模板ID';
comment on column C_BO_TEMPLATE_FORM.TEMPLATE_NAME is
'模板名称（冗余）';
comment on column C_BO_TEMPLATE_FORM.SELECT_VAL is
'关联选项值';
comment on column C_BO_TEMPLATE_FORM.FORM_CONFIG is
'表单配置';
comment on column C_BO_TEMPLATE_FORM.CREATE_TIME is
'创建时间';
comment on column C_BO_TEMPLATE_FORM.CREATE_ACC is
'创建人';
comment on column C_BO_TEMPLATE_FORM.CREATE_NAME is
'创建人名称';
comment on column C_BO_TEMPLATE_FORM.UPDATE_TIME is
'更新时间';
comment on column C_BO_TEMPLATE_FORM.UPDATE_ACC is
'更新人';
comment on column C_BO_TEMPLATE_FORM.UPDATE_NAME is
'更新人姓名';
comment on column C_BO_TEMPLATE_FORM.ENT_ID is
'企业ID';
comment on column C_BO_TEMPLATE_FORM.BUSI_ORDER_ID is
'业务订购ID';
create index IDX_CBTF_INDEX01 on C_BO_TEMPLATE_FORM (
   TEMPLATE_ID ASC,
   SELECT_VAL ASC
);

create table C_BOX_TEMPLATE 
(
   ID                   VARCHAR(64)         not null,
   M_ID                 VARCHAR(64),
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   CREATE_TIME          VARCHAR(19),
   TEMPLATE_ID          VARCHAR(64),
   FORM_ID              VARCHAR(64),
   EX1                  VARCHAR(50),
   EX2                  VARCHAR(50),
   EX3                  VARCHAR(50),
   EX4                  VARCHAR(50),
   EX5                  VARCHAR(50),
   EX6                  VARCHAR(100),
   EX7                  VARCHAR(100),
   EX8                  VARCHAR(100),
   EX9                  VARCHAR(100),
   EX10                 VARCHAR(100),
   EX11                 VARCHAR(200),
   EX12                 VARCHAR(200),
   EX13                 VARCHAR(200),
   EX14                 VARCHAR(200),
   EX15                 VARCHAR(200),
   EX16                 VARCHAR(200),
   EX17                 VARCHAR(200),
   EX18                 VARCHAR(200),
   EX19                 VARCHAR(200),
   EX20                 VARCHAR(200),
   EX21                 VARCHAR(500),
   EX22                 VARCHAR(500),
   EX23                 VARCHAR(500),
   EX24                 VARCHAR(500),
   EX25                 VARCHAR(500),
   EX26                 VARCHAR(500),
   EX27                 VARCHAR(500),
   EX28                 VARCHAR(500),
   EX29                 VARCHAR(500),
   EX30                 VARCHAR(500),
   EX31                 VARCHAR(500),
   EX32                 VARCHAR(500),
   EX33                 VARCHAR(500),
   EX34                 VARCHAR(1000),
   EX35                 VARCHAR(1000),
   EX36                 VARCHAR(1000),
   EX37                 VARCHAR(1000),
   EX38                 VARCHAR(1000),
   EX39                 CLOB,
   EX40                 CLOB,
   constraint PK_C_BOX_TEMPLATE primary key (ID)
);
comment on column C_BOX_TEMPLATE.ID is
'ID';
comment on column C_BOX_TEMPLATE.M_ID is
'工单ID，对于C_BO_BASE_ORDER表里的ID';
comment on column C_BOX_TEMPLATE.ENT_ID is
'所属企业';
comment on column C_BOX_TEMPLATE.BUSI_ORDER_ID is
'企业业务订购ID';
comment on column C_BOX_TEMPLATE.CREATE_TIME is
'创建时间';
comment on column C_BOX_TEMPLATE.TEMPLATE_ID is
'模板id';
comment on column C_BOX_TEMPLATE.FORM_ID is
'表单id';
comment on column C_BOX_TEMPLATE.EX1 is
'字段1';
comment on column C_BOX_TEMPLATE.EX2 is
'字段2';
comment on column C_BOX_TEMPLATE.EX3 is
'字段3';
comment on column C_BOX_TEMPLATE.EX4 is
'字段4';
comment on column C_BOX_TEMPLATE.EX5 is
'字段5';
comment on column C_BOX_TEMPLATE.EX6 is
'字段6';
comment on column C_BOX_TEMPLATE.EX7 is
'字段7';
comment on column C_BOX_TEMPLATE.EX8 is
'字段8';
comment on column C_BOX_TEMPLATE.EX9 is
'字段9';
comment on column C_BOX_TEMPLATE.EX10 is
'字段10';
comment on column C_BOX_TEMPLATE.EX11 is
'字段11';
comment on column C_BOX_TEMPLATE.EX12 is
'字段12';
comment on column C_BOX_TEMPLATE.EX13 is
'字段13';
comment on column C_BOX_TEMPLATE.EX14 is
'字段14';
comment on column C_BOX_TEMPLATE.EX15 is
'字段15';
comment on column C_BOX_TEMPLATE.EX16 is
'字段16';
comment on column C_BOX_TEMPLATE.EX17 is
'字段17';
comment on column C_BOX_TEMPLATE.EX18 is
'字段18';
comment on column C_BOX_TEMPLATE.EX19 is
'字段19';
comment on column C_BOX_TEMPLATE.EX20 is
'字段20';
comment on column C_BOX_TEMPLATE.EX21 is
'字段21';
comment on column C_BOX_TEMPLATE.EX22 is
'字段22';
comment on column C_BOX_TEMPLATE.EX23 is
'字段23';
comment on column C_BOX_TEMPLATE.EX24 is
'字段24';
comment on column C_BOX_TEMPLATE.EX25 is
'字段25';
comment on column C_BOX_TEMPLATE.EX26 is
'字段26';
comment on column C_BOX_TEMPLATE.EX27 is
'字段27';
comment on column C_BOX_TEMPLATE.EX28 is
'字段28';
comment on column C_BOX_TEMPLATE.EX29 is
'字段29';
comment on column C_BOX_TEMPLATE.EX30 is
'字段30';
comment on column C_BOX_TEMPLATE.EX31 is
'字段30';
comment on column C_BOX_TEMPLATE.EX32 is
'字段30';
comment on column C_BOX_TEMPLATE.EX33 is
'字段30';
comment on column C_BOX_TEMPLATE.EX34 is
'字段30';
comment on column C_BOX_TEMPLATE.EX35 is
'字段30';
comment on column C_BOX_TEMPLATE.EX36 is
'字段30';
comment on column C_BOX_TEMPLATE.EX37 is
'字段30';
comment on column C_BOX_TEMPLATE.EX38 is
'字段30';
comment on column C_BOX_TEMPLATE.EX39 is
'字段30';
comment on column C_BOX_TEMPLATE.EX40 is
'字段30';
create index IDX_BOXTEMPLATE_1 on C_BOX_TEMPLATE (
   M_ID ASC
);
create index IDX_BOXTEMPLATE_2 on C_BOX_TEMPLATE (
   TEMPLATE_ID ASC
);
create index IDX_BOXTEMPLATE_3 on C_BOX_TEMPLATE (
   FORM_ID ASC
);


-- 20240523
alter table C_BO_ORDER_EX add VISIT_PLAN_ID varchar(64);
comment on column C_BO_ORDER_EX.VISIT_PLAN_ID is '回访计划id';
/*==============================================================*/
/* Table: C_BO_ORDER_VISIT_PLAN                                 */
/*==============================================================*/
create table C_BO_ORDER_VISIT_PLAN 
(
   ID                   VARCHAR(64)         not null,
   PLAN_NO              VARCHAR(50),
   MERGE_STRATEGY       VARCHAR(20)         default '1',
   VISIT_STRATEGY       VARCHAR(20)         default '1',
   M_ID                 VARCHAR(64)         default '0',
   ORDER_NO             VARCHAR(50)         default '0',
   SESSION_ID           VARCHAR(64),
   CUST_PHONE           VARCHAR(20),
   CUST_ID              VARCHAR(64),
   CUST_NAME            VARCHAR(50),
   ORDER_NUM            INTEGER              default 1,
   VISIT_TYPE           VARCHAR(20)         default '1',
   STATUS               VARCHAR(20),
   FLOW_KEY             VARCHAR(50),
   CREATE_TIME          VARCHAR(19),
   DATE_ID              INTEGER,
   ORDER_CREATE_TIME    VARCHAR(19),
   ASSIGN_TIME          VARCHAR(19),
   ASSIGN_USER_ACC      VARCHAR(30),
   ASSIGN_DEPT_CODE     VARCHAR(30),
   ASSIGN_BAKUP         VARCHAR(500),
   VISIT_BEGIN_TIME     VARCHAR(19),
   VISIT_TIMES          INTEGER              default 0,
   VISIT_ETIME          VARCHAR(19),
   VISIT_USER_ACC       VARCHAR(30),
   VISIT_DEPT_CODE      VARCHAR(30),
   VISIT_BAKUP          VARCHAR(500),
   VISIT_STATSIFY       VARCHAR(20),
   VISIT_RESULT         VARCHAR(20),
   CONFIG_ID            VARCHAR(64),
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_BO_ORDER_VISIT_PLAN primary key (ID)
);

comment on table C_BO_ORDER_VISIT_PLAN is
'工单回访计划，对于开启回访计划的工单，会生成对应的回访计划；回访计划执行过程中，会生成回访记录。';

comment on column C_BO_ORDER_VISIT_PLAN.ID is
'工单回访计划ID';

comment on column C_BO_ORDER_VISIT_PLAN.PLAN_NO is
'回访计划编号';

comment on column C_BO_ORDER_VISIT_PLAN.MERGE_STRATEGY is
'合并类型，1-按工单 2-按客户';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_STRATEGY is
'1-不限制 2-仅自动回访 3-仅人工回访';

comment on column C_BO_ORDER_VISIT_PLAN.M_ID is
'工单ID，合并类型为1时，存工单ID';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_NO is
'工单编号(冗余),合并类型为1时，存工单ID';

comment on column C_BO_ORDER_VISIT_PLAN.SESSION_ID is
'会话ID';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_PHONE is
'客户号码';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_ID is
'客户资料ID';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_NAME is
'客户姓名(冗余)';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_NUM is
'工单条数';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_TYPE is
'回访方式 1-待定 2-自动回访 3-人工回访';

comment on column C_BO_ORDER_VISIT_PLAN.STATUS is
'回访计划状态,1-待回访 2-回访中 3-回访成功 4-回访失败 5-无需回访 6-不允许回访(预留)  99-未回访(预留)';

comment on column C_BO_ORDER_VISIT_PLAN.FLOW_KEY is
'所属流程key(冗余)';

comment on column C_BO_ORDER_VISIT_PLAN.CREATE_TIME is
'创建时间';

comment on column C_BO_ORDER_VISIT_PLAN.DATE_ID is
'创建日期';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_CREATE_TIME is
'工单创建时间(冗余)，如有多条工单，则显示第一条工单的创建时间';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_TIME is
'回访分配时间';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_USER_ACC is
'回访分配人员账号';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_DEPT_CODE is
'回访分配人员部门';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_BAKUP is
'回访分配说明';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_BEGIN_TIME is
'回访开始时间';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_TIMES is
'回访次数';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_ETIME is
'回访处理时间';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_USER_ACC is
'回访处理人账号';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_DEPT_CODE is
'回访处理人部门';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_BAKUP is
'回访处理说明';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_STATSIFY is
'回访满意度 1-非常满意 2-满意 3-一般 4-不满意 5-非常不满意';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_RESULT is
'问题解决情况  1-已解决 2-未解决';

comment on column C_BO_ORDER_VISIT_PLAN.CONFIG_ID is
'回访计划配置ID';

comment on column C_BO_ORDER_VISIT_PLAN.ENT_ID is
'所属企业';

comment on column C_BO_ORDER_VISIT_PLAN.BUSI_ORDER_ID is
'业务订购ID';

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN1                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN1 on C_BO_ORDER_VISIT_PLAN (
   PLAN_NO ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN2                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN2 on C_BO_ORDER_VISIT_PLAN (
   CREATE_TIME ASC,
   STATUS ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN3                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN3 on C_BO_ORDER_VISIT_PLAN (
   CUST_PHONE ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN4                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN4 on C_BO_ORDER_VISIT_PLAN (
   CUST_ID ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN5                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN5 on C_BO_ORDER_VISIT_PLAN (
   ASSIGN_USER_ACC ASC,
   ASSIGN_TIME ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN6                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN6 on C_BO_ORDER_VISIT_PLAN (
   CONFIG_ID ASC
);
/*==============================================================*/
/* Table: C_WF_ORDER_VISITPLAN_CONFIG                           */
/*==============================================================*/
create table C_WF_ORDER_VISITPLAN_CONFIG 
(
   ID                   VARCHAR(64)         not null,
   MERGE_STRATEGY       VARCHAR(20)         default '1',
   VISIT_STRATEGY       VARCHAR(20)         default '1',
   VISIT_TYPE           VARCHAR(20)         default '1',
   VISIT_DATA_EXTRACT   VARCHAR(10),
   VISIT_DATE_CONFIG    VARCHAR(1000),
   VISIT_REQ_SERVICE    VARCHAR(64),
   VISIT_RESP_SERVICE   VARCHAR(64),
   DATA_DATE_FIELD      VARCHAR(64),
   CUST_ID_FIELD        VARCHAR(64),
   CUST_PHONE_FIELD     VARCHAR(64),
   START_TYPE           VARCHAR(10),
   ENABLE_STATUS        VARCHAR(10),
   FLOW_KEY             VARCHAR(50),
   VISIT_FORM_KEY       VARCHAR(64),
   CREATE_TIME          VARCHAR(19),
   DATE_ID              INTEGER,
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   constraint PK_C_WF_ORDER_VISITPLAN_CONFIG primary key (ID)
);

comment on table C_WF_ORDER_VISITPLAN_CONFIG is
'工单回访计划配置';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.ID is
'工单回访计划ID';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.MERGE_STRATEGY is
'合并类型，1-按工单 2-按客户';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_STRATEGY is
'1-不限制 2-仅自动回访 3-仅人工回访';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_TYPE is
'回访方式 1-待定 2-自动回访 3-人工回访';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_DATA_EXTRACT is
'回访数据提取 字典: ORDER_VISIT_DATA_EXTRACT
1-近一天 2-近两天 3-近三天';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_DATE_CONFIG is
'回访时间段,JSON格式';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_REQ_SERVICE is
'回访请求服务接口';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_RESP_SERVICE is
'回访结果服务接口';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.DATA_DATE_FIELD is
'数据提取时间字段 格式:数据源id.字段名';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.CUST_ID_FIELD is
'客户资料id字段 格式:数据源id.字段名';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.CUST_PHONE_FIELD is
'客户号码字段 格式:数据源id.字段名';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.START_TYPE is
'启动策略 字典: ORDER_VISIT_START_TYPE
01-自动启动 02-手动启动';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.ENABLE_STATUS is
'启用状态 01-启用 02-禁用';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.FLOW_KEY is
'所属流程key';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_FORM_KEY is
'人工回访表单KEY';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.CREATE_TIME is
'创建时间';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.DATE_ID is
'创建日期';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.ENT_ID is
'所属企业';

comment on column C_WF_ORDER_VISITPLAN_CONFIG.BUSI_ORDER_ID is
'业务订购ID';

/*==============================================================*/
/* Index: IDX_ORDER_VISITPLAN_CONFIG_1                          */
/*==============================================================*/
create index IDX_ORDER_VISITPLAN_CONFIG_1 on C_WF_ORDER_VISITPLAN_CONFIG (
   FLOW_KEY ASC
);

alter table C_WF_ORDER_VISITPLAN_CONFIG add VISIT_TASK_ID varchar(64);
comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_TASK_ID is '回访任务ID';


-- 20240527
alter table C_WF_ORDER_VISITPLAN_CONFIG add VISIT_GROUP varchar(64);
comment on column C_WF_ORDER_VISITPLAN_CONFIG.VISIT_GROUP is '回访工作组';


-- 20240528
/*==============================================================*/
/* Table: C_BO_ORDER_VISIT_PLAN                                 */
/*==============================================================*/
create table C_BO_ORDER_VISIT_PLAN 
(
   ID                   VARCHAR(64)         not null,
   PLAN_NO              VARCHAR(50),
   MERGE_STRATEGY       VARCHAR(20)         default '1',
   VISIT_STRATEGY       VARCHAR(20)         default '1',
   M_ID                 VARCHAR(64)         default '0',
   ORDER_NO             VARCHAR(50)         default '0',
   SESSION_ID           VARCHAR(64),
   CUST_PHONE           VARCHAR(20),
   CUST_ID              VARCHAR(64),
   CUST_NAME            VARCHAR(50),
   ORDER_NUM            INTEGER              default 1,
   VISIT_TYPE           VARCHAR(20)         default '1',
   STATUS               VARCHAR(20),
   FLOW_KEY             VARCHAR(50),
   CREATE_TIME          VARCHAR(19),
   DATE_ID              INTEGER,
   ORDER_TITLE          VARCHAR(200),
   ORDER_CREATE_TIME    VARCHAR(19),
   ASSIGN_TIME          VARCHAR(19),
   ASSIGN_USER_ACC      VARCHAR(30),
   ASSIGN_DEPT_CODE     VARCHAR(30),
   ASSIGN_BAKUP         VARCHAR(500),
   VISIT_BEGIN_TIME     VARCHAR(19),
   VISIT_TIMES          INTEGER              default 0,
   VISIT_ETIME          VARCHAR(19),
   VISIT_USER_ACC       VARCHAR(30),
   VISIT_DEPT_CODE      VARCHAR(30),
   VISIT_BAKUP          VARCHAR(500),
   VISIT_STATSIFY       VARCHAR(20),
   VISIT_RESULT         VARCHAR(20),
   CONFIG_ID            VARCHAR(64),
   ENT_ID               VARCHAR(30),
   BUSI_ORDER_ID        VARCHAR(64),
   VISIT_SERIALID       VARCHAR(64),
   constraint PK_C_BO_ORDER_VISIT_PLAN primary key (ID)
);

comment on table C_BO_ORDER_VISIT_PLAN is
'工单回访计划，对于开启回访计划的工单，会生成对应的回访计划；回访计划执行过程中，会生成回访记录。';

comment on column C_BO_ORDER_VISIT_PLAN.ID is
'工单回访计划ID';

comment on column C_BO_ORDER_VISIT_PLAN.PLAN_NO is
'回访计划编号';

comment on column C_BO_ORDER_VISIT_PLAN.MERGE_STRATEGY is
'合并类型，1-按工单 2-按客户';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_STRATEGY is
'1-不限制 2-仅自动回访 3-仅人工回访';

comment on column C_BO_ORDER_VISIT_PLAN.M_ID is
'工单ID，合并类型为1时，存工单ID';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_NO is
'工单编号(冗余),合并类型为1时，存工单ID';

comment on column C_BO_ORDER_VISIT_PLAN.SESSION_ID is
'会话ID';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_PHONE is
'客户号码';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_ID is
'客户资料ID';

comment on column C_BO_ORDER_VISIT_PLAN.CUST_NAME is
'客户姓名(冗余)';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_NUM is
'工单条数';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_TYPE is
'回访方式 1-待定 2-自动回访 3-人工回访';

comment on column C_BO_ORDER_VISIT_PLAN.STATUS is
'回访计划状态,1-待回访 2-回访中 3-回访成功 4-回访失败 5-无需回访 6-不允许回访(预留)  21-回访执行中 99-未回访(预留)';

comment on column C_BO_ORDER_VISIT_PLAN.FLOW_KEY is
'所属流程key(冗余)';

comment on column C_BO_ORDER_VISIT_PLAN.CREATE_TIME is
'创建时间';

comment on column C_BO_ORDER_VISIT_PLAN.DATE_ID is
'创建日期';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_TITLE is
'工单标题';

comment on column C_BO_ORDER_VISIT_PLAN.ORDER_CREATE_TIME is
'工单创建时间(冗余)，如有多条工单，则显示第一条工单的创建时间';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_TIME is
'回访分配时间';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_USER_ACC is
'回访分配人员账号';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_DEPT_CODE is
'回访分配人员部门';

comment on column C_BO_ORDER_VISIT_PLAN.ASSIGN_BAKUP is
'回访分配说明';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_BEGIN_TIME is
'回访开始时间';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_TIMES is
'回访次数';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_ETIME is
'回访处理时间';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_USER_ACC is
'回访处理人账号';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_DEPT_CODE is
'回访处理人部门';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_BAKUP is
'回访处理说明';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_STATSIFY is
'回访满意度 1-非常满意 2-满意 3-一般 4-不满意 5-非常不满意';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_RESULT is
'问题解决情况  1-已解决 2-未解决';

comment on column C_BO_ORDER_VISIT_PLAN.CONFIG_ID is
'回访计划配置ID';

comment on column C_BO_ORDER_VISIT_PLAN.ENT_ID is
'所属企业';

comment on column C_BO_ORDER_VISIT_PLAN.BUSI_ORDER_ID is
'业务订购ID';

comment on column C_BO_ORDER_VISIT_PLAN.VISIT_SERIALID is
'回访流水号';

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN1                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN1 on C_BO_ORDER_VISIT_PLAN (
   PLAN_NO ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN2                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN2 on C_BO_ORDER_VISIT_PLAN (
   CREATE_TIME ASC,
   STATUS ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN3                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN3 on C_BO_ORDER_VISIT_PLAN (
   CUST_PHONE ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN4                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN4 on C_BO_ORDER_VISIT_PLAN (
   CUST_ID ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN5                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN5 on C_BO_ORDER_VISIT_PLAN (
   ASSIGN_USER_ACC ASC,
   ASSIGN_TIME ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN6                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN6 on C_BO_ORDER_VISIT_PLAN (
   CONFIG_ID ASC
);

/*==============================================================*/
/* Index: IDX_C_BO_ORDER_VISIT_PLAN7                            */
/*==============================================================*/
create index IDX_C_BO_ORDER_VISIT_PLAN7 on C_BO_ORDER_VISIT_PLAN (
   VISIT_SERIALID ASC
);

-- 20240607
alter table C_BO_ORDER_VISIT_RECORD add CONTACT_NUM varchar(50);
comment on column C_BO_ORDER_VISIT_RECORD.CONTACT_NUM is
'回访号码';
