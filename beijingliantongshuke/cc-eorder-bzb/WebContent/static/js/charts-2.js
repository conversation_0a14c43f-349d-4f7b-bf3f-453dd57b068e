$(function(){
	clockTime();
	$('body').on('click','.system-title2',function(e){
		var target = $(this).data('target');
		$(this).addClass('active').siblings().removeClass('active');
		$('.detail-box').hide();
		$(target).show();
	})
});
function initLine(series,hour, id) {
    var option = {
    	color:['#65fff6', '#e43c59'],
    	tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        legend: {
            selectedMode: true,
            right:20,
            textStyle:{color: '#FFF'},
            data: ['咨询工单', '投诉工单']
        },
        grid: {
            left: '3%',
            right: '3%',
            bottom: '2%',
            top: '6%',
            containLabel: true
        },
        xAxis: [{
            data: hour,
            axisLabel: {
                textStyle: {
                    color: '#9fc9ff',
                    fontSize: '16px'
                },

            },
            axisLine:{
            	lineStyle:{color:'#65a0ff',width:2}
            },
        }],
        yAxis: [{
            type: 'value',
            axisLabel: {
                textStyle: {
                    color: '#9fc9ff',
                    fontSize: '16px'
                },

            },
            splitLine:{lineStyle:{color:'rgba(10,233,255,0.16)'}},
            axisLine:{
            	lineStyle:{color:'#65a0ff',width:2}
            }
        }],
        label: {
            normal: {
                show: true,
                position: 'top',
                formatter: '{c}',
                fontSize: '16px'
            }
        },
        series: series
    }
    var chart = echarts.init(document.getElementById(id));
    chart.setOption(option);
    $(window).on("resize", chart.resize);
}

function initPie2(data, id){

}

function initPIE(data,id){
	//formatter: "{a} <br/>{b} : {c} ({d}%)"
	var option = {
	    tooltip : {
	        trigger: 'item',
	        formatter: "{b} : {c} ({d}%)"
	    },
	    color:['#f64d76','#7b6fe9','#32c5e9','#65f096','#329cd3','#f97b51','#f4cf4c'],
	    // visualMap: {
	    //     show: false,
	    //     min: 1,
	    //     max: 100,
	    //     inRange: {
	    //         colorLightness: [0, 1]
	    //     }
	    // },
	    calculable:true,
	    series : [
	        {
	            name:'',
	            type:'pie',
	    		calculable:true,

	            radius : ['8px','55%'],
	            center: ['50%', '50%'],
	            data:data.sort(function (a, b) { return a.value - b.value; }),
	            roseType: 'radius',
	            label: {
	                normal: {
	                    textStyle: {
	                        color: '#aaebfd'
	                    },
	                    formatter: "{b}\n{c}"
	                }
	            },
	            labelLine: {
	                normal: {
	                    lineStyle: {
	                        color: '#aaebfd'
	                    },
	                    smooth: 0.2,
	                    length: 10,
	                    length2: 20
	                }
	            },
	            itemStyle: {
	                normal: {
	                    //color: '#c23531',
	                    shadowBlur: 200,
	                    shadowColor: 'rgba(0, 0, 0, 0.5)'
	                }
	            },

	            animationType: 'scale',
	            animationEasing: 'elasticOut',
	            animationDelay: function (idx) {
	                return Math.random() * 200;
	            }
	        }
	    ]
	};
	var chart = echarts.init(document.getElementById(id));
    chart.setOption(option);
    $(window).on("resize",chart.resize);
}

function createTable(data,id){
	// var html = $.templates("#table-tmpl").render(data);
	// $(id).html(html);
	var th = '<th class="color-th">投诉分类名称</th>';
	var td = '<td class="color-th">投诉工单量</td>';
	for (var i = 0; i < data.length; i++) {
		th+= '<th>'+data[i].name+'</th>';
		td+= '<td>'+data[i].value+'</td>';
	}
	var dhtml = '<table class="monitor-table"><thead><tr>'+th+'</tr></thead><tbody><tr>'+td+'</tr></tbody></table>'
	$(id).html(dhtml);

}


function clockTime(){  
    var enterChat=setInterval(function () {  
        var time = UTCToLocalTimeString(new Date(),'yyyy-MM-dd hh:mm:ss');  
        $("#clockTime").text(time);

        // clearInterval(enterChat);  
    }, 1000);  
}

Date.prototype.strftime = function(format){  
    var o = {  
      "M+" :  this.getMonth()+1,  //month  
      "d+" :  this.getDate(),     //day  
      "h+" :  this.getHours(),    //hour  
      "m+" :  this.getMinutes(),  //minute  
      "s+" :  this.getSeconds(), //second  
      "q+" :  Math.floor((this.getMonth()+3)/3),  //quarter  
      "S"  :  this.getMilliseconds() //millisecond  
    }  
    if(/(y+)/.test(format)) {  
        format = format.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));  
    }  
   for(var k in o) {  
    if(new RegExp("("+ k +")").test(format)) {  
      format = format.replace(RegExp.$1, RegExp.$1.length==1 ? o[k] : ("00"+ o[k]).substr((""+ o[k]).length));  
    }  
   }  
    return format;  
}  
function UTCToLocalTimeString(d, format) {  
     var   timeOffsetInHours = (new Date().getTimezoneOffset()/60)  + 8;//这里的 8是时区，东八区为北京时间，PS：东区为正数 西区为负数  
    d.setHours(d.getHours() + timeOffsetInHours);  
    return d.strftime(format);  
} 