<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>办理工单</title>
	<style type="text/css">
		.container-fluid {
			height: 100%;
		}

		.labelListDiv {
			padding: 10px 10px;
			color: #76838F;
    		font-size: 15px;
			/* border: 1px solid; */
		    /* border-color: #8ba99f33; */
		    border-radius: 3px;
		    margin: 2px;
		}

		a:link {
			color: #00adff;
		}

		#leftBox a:link {
			color: inherit !important;
			text-decoration: none !important;
		}

		.labelListDiv:hover {
			color: #fff !important;
			background-color: rgba(27, 168, 237, 0.6);
			cursor: pointer;
		}

		.labelListDivClick {
			color: #fff !important;
			background-color: #1BA8ED;
		}

		#tagManage {
			position: absolute;
			bottom: 30px;
			left: 50%;
			transform: translateX(-50%);
		}

		td[data-field="ID"] .layui-table-cell {
			overflow: inherit;
		}

		.layui-table-view .layui-table {
			position: relative;
			width: 100%;
			margin: 0px;
		}

		.layui-table-cell,
		.layui-table-tool-panel li {
			overflow: hidden;
		}

		.layui-table-header .layui-table-cell,
		.layui-table-tool-panel li {
			overflow: inherit !important;
		}
	</style>
	<style>
		<%-- 这个会导致单元格内容过长时无法点击按钮显示内容 添加id域，限制生效范围--%>
	    #executionListener .layui-layer-content{
	        overflow-x: hidden !important;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<div class="box" style="height: 100%;">
					<div class="">
					
						<form action="" method="post" name="tsTableForm" class="form-inline" id="tsTableForm"
							onsubmit="return false" data-toggle="">
							<div class="ibox">
								<div class="ibox-title clearfix" id="divId">
									<div class="form-group">
										<div class="input-group input-group-sm">
											<span class="input-group-addon" style="width:82px;" i18n-content="流程分类"></span>
											<select name="processId" id="tsProcessId" noclear
												data-mars="DoOrder.getAllProName" class="form-control input-sm"
												style="width:150px;" onchange="Process.watchSelect('4')">
												<option value="" i18n-content="请选择"></option>
											</select>
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" i18n-content="工单编号"></span>
											<input type="text" name="orderNo" class="form-control input-sm"
												style="width:150px;">
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" i18n-content="客户号码"></span>
											<input type="text" name="caller" class="form-control input-sm"
												style="width:150px;">
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" i18n-content="创建时间"></span>
											<input type="text" name="tsCreateStartTime" id="tsCreateStartTime"
												class="form-control input-sm" style="width:152px;" autocomplete="off"
												data-type-date="StartDate">
											<span class="input-group-addon">~</span>
											<input type="text" id="tsCreateEndTime" name="tsCreateEndTime"
												class="form-control input-sm" style="width: 150px;" autocomplete="off"
												data-type-date="EndDate">
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" id="orderTitleHis" i18n-content="标题"></span>
											<input type="text" name="orderTitle" class="form-control input-sm"
												style="width:150px;">
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" id="REMARK1TS"></span>
											<input type="text" name="REMARK1" id="parameter7"
												class="form-control input-sm" style="width:150px;">
										</div>
										<div class="input-group input-group-sm">
											<span class="input-group-addon" id="REMARK2TS"></span>
											<input type="text" name="REMARK2" id="parameter8"
												class="form-control input-sm" style="width:150px;">
										</div>
										<div class="input-group input-group-sm">
											<button type="button" data-event="enter" class="btn btn-sm btn-default"
												onclick="Process.searchTsList('1')" i18n-content="搜索"><span
													class="glyphicon glyphicon-search"></span>搜索</button>
										</div>
										<div class="input-group ">
											<button type="button" class="btn btn-sm btn-default"
												onclick="Process.reset()" i18n-content="重置"><span
													class="glyphicon glyphicon-repeat"></span>
												</button>
										</div>
										<div class="input-group input-group-sm">
											<button type="button" class="btn btn-sm btn-default more-query"
												onclick="Process.show('tsSenior')" i18n-content="高级查询"></button>
										</div>
									</div>
									<div id="tsSenior" style="display:none;" class="form-group">
									</div>
								</div>
								<div class="ibox-content">
									<table id="tsMain"></table>
								</div>
							</div>
						</form>
					</div>
				</div>
				
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/html" id="contentDivTemplate">
		<form class="layui-form" action="">
			<div class="layui-form-item layui-form-text" style="padding: 5px;">
      			<textarea name="desc" id="area" i18n-placeholder="请输入内容" class="layui-textarea" rows="8"></textarea>
			</div>
		</form>
	</script>
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/time.js?v=20210803-1"></script>
	<!-- 解决ie不支持promise -->
	<script type="text/javascript" src="${ctxPath}/static/js/promise.js"></script>
	<script type="text/javascript">
	jQuery.namespace("Process");
	
	$(function(){
		//初始化tab页面的下拉框
		$("#tsTableForm").render({
			success: function () {
				layui.use('laydate', function () {
					var laydate = layui.laydate;
	
					laydate.render({
						elem: '#tsCreateStartTime',
						type: 'datetime',
						lang:getDateLang()
					});
					laydate.render({
						elem: '#tsCreateEndTime',
						type: 'datetime',
						lang:getDateLang()
					});
					
					
				});
				//初始化tab
				Process.initTsMain();
			}
		});
	})
	
	Process.query = function () {
		Process.searchTsList();
	}
	Process.searchTsList = function (flag) {
		if (flag == '1') {
			$("#tsTableForm").queryData({
				id: 'tsMain',
				page: {
					curr: 1
				}
			});
		} else {
			$("#tsTableForm").queryData({
				id: 'tsMain'
			});
		}
	}
	
	
		
		//重置
		Process.reset = function () {
			$("#divId select:not([noclear])").val("");
			$("#divId input").val("");
			//标签清除
			$("#tagSelect").val("");
			$('.labelListDiv').removeClass('labelListDivClick');
			LimitUtil.reset();
		};
		
	
		// 暂存
		Process.initTsMain = function () {
			$("#tsCreateStartTime").val(getPreSevenStartTime());
			$("#tsCreateEndTime").val(getTodayEndTime());
			//var cols = OrderCols.cols('qwe');
			var REMARK1Hid = $('#REMARK1Hid').val();
			if (!REMARK1Hid) {
				$("#REMARK1TS").attr("class", "hidden");
				$("#parameter7").attr("class", "hidden");
			}
			var REMARK2Hid = $('#REMARK2Hid').val();
			if (!REMARK2Hid) {
				$("#REMARK2TS").attr("class", "hidden");
				$("#parameter8").attr("class", "hidden");
			}
			var processId = $('#tsProcessId').val();
			var tsCols = OrderCols.cols({
				bakup1: REMARK1Hid,
				bakup2: REMARK2Hid,
				processId: processId
			});
			tsCols[0].pop();
			tsCols[0].push({
				width: 130,
				field: 'ID',
				title: getI18nValue('操作'),
				fixed: 'right',
				align: 'center',
				templet: function (row) {
					var html = '';
					if (row.PROC_INST_ID) {
						html += '<a class="layui-btn layui-btn layui-btn-xs"  lay-event="Process.completeOrder(\'' + row.ID +
							'\', \'' + row.PROC_INST_ID + '\', \'' + row.TASK_ID + '\', \'' + row.PROC_KEY + '\')">'+getI18nValue('编辑')+'</a>';
					} else {
						html += '<a class="layui-btn layui-btn layui-btn-xs"  lay-event="Process.tsOrderEdit(\'' + row.ID + '\', \'' +
							row.PROC_KEY + '\', \''+ row.P_ORDER_ID +'\')">'+getI18nValue('编辑')+'</a>';
					}
					if (!row.PROC_INST_ID && row.PROC_KEY) {
						html += '<a class="layui-btn layui-btn layui-btn-xs" lay-event="Process.tsOrderDel(\'' + row.ID + '\', \'' +
							row.PROC_KEY + '\')">'+getI18nValue('删除')+'</a>';
					}
					return html;

				}
			});
			//var tsCols = cols;
			/* var colOne = tsCols[0][0]
			tsCols[0].splice(0, 1, {
				minWidth: 200,
				field: 'ORDER_NO',
				title: '工单编号',
				align: 'left'
			}); */

			//渲染搜索框
			SearchBarUtil.initSearchBar({
				el: 'tsSenior',
				formEl: 'tsTableForm',
				processId: processId
			})

			$("#tsTableForm").initTableEx({
				mars: 'DoOrder.getAllTsOrderList',
				id: 'tsMain',
				limit: '15',
				height: 'full-160',
				limits: [15, 25, 50, 100, 200],
				cols: tsCols,
				done: function (res) {
					OrderCols.overTime(res, 'ts');
					//tsCols[0].splice(0, 1, );
				}
			});
			document.getElementById("REMARK1TS").innerText = REMARK1Hid;
			document.getElementById("REMARK2TS").innerText = REMARK2Hid;
		}

		Process.tsOrderEdit = function (orderId, flowKey, pOrderId) {
			var tabId = $('div.layui-show>iframe', window.top.document).attr('id');
			var title = getI18nValue('暂存工单编辑');
			var data = {
				parentTabId: orderId || 'completeOrder',
				tabId: tabId || 'cc-erder-handle-doOrderList',
				successCallBack: 'Process.startFormCallBack',
				orderId: orderId,
				pOrderId: pOrderId,
				flowKey: flowKey
			}
			popup.openTab({
				url: "${ctxPath}/pages/eorder/search/order-index.jsp?type=ts",
				title: title,
				id: orderId,
				data: data
			});
		}

		Process.tsOrderDel = function (orderId, flowKey) {
			layer.confirm(getI18nValue("是否确定删除暂存工单?"), {icon: 3, title:getI18nValue('信息'),offset:'20px',btn:[getI18nValue('确定'),getI18nValue('取消')]},function () {
				var data = {
					orderId: orderId,
					flowKey: flowKey
				};
				ajax.remoteCall("${ctxPath}/servlet/order?action=TsOrderDel", data, function (result) {
					if (result.state == 1) {
						layer.msg(result.msg, {
							icon: 1,
							time: 1200,
							offset: '40px'
						}, function () {
							Process.searchTsList('1');
							popup.layerClose();
						});
					} else {
						layer.alert(getI18nValue(result.msg), {
							icon: 5
						});
					}
				});
			});
		}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>