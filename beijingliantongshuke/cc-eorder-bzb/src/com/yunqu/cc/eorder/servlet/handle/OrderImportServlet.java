package com.yunqu.cc.eorder.servlet.handle;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.cache.MapCache;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.Dict;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.eorder.base.AppBaseServlet;
import com.yunqu.cc.eorder.base.Constants;
import com.yunqu.cc.eorder.base.QueryFactory;
import com.yunqu.cc.eorder.listener.imports.ImportOrderListener;
import com.yunqu.cc.eorder.log.CommonLogger;
import com.yunqu.cc.eorder.utils.excel.ExcelExport;
import com.yunqu.cc.eorder.utils.excel.ExcelExportDataHandle;
import com.yunqu.cc.eorder.utils.excel.impl.ExcelExportDataHandleImpl;

/**
 * 工单导入
 * <AUTHOR>
 */
@MultipartConfig
@WebServlet("/servlet/orderImport")
public class OrderImportServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	private Logger logger = CommonLogger.logger;

	/**
	 * 获取导入模板
	 * @return
	 */
	public void actionForImportTemplate() {
		try {
			JSONObject param = this.getJSONObject();
			
			List<JSONObject> list = getFormShowFieldConfig(param.getString("flowKey"));
			// 组装表头
			List<String> headers = new ArrayList<String>();
			// 组装所有需要展示的列
			List<String> columnNames = new ArrayList<String>();

			for (JSONObject row:list) {
				String fieldName = row.getString("FIELD_NAME");
				String fieldText = row.getString("FIELD_TEXT");
				// 增加字段标题注释
				String searchExtconfig = row.getString("SEARCH_EXTCONFIG");
				if (StringUtils.isNotBlank(searchExtconfig)) {
					JSONObject searchExtconfigJson = JSON.parseObject(searchExtconfig);
					String type = searchExtconfigJson.getString("type");
					String tips = "";
					if (Constants.ORDER_SEARCH_TYPE_02.equals(type)) {
						String dictCode = searchExtconfigJson.getString("dictCode");
						List<Dict> dicts = DictCache.getEnableDictListByGroupCode(getEntId(), dictCode);
						for (Dict dict:dicts) {
							// String code = dict.getCode();
							String name = dict.getName();
							if (StringUtils.isNotBlank(tips)) {
								tips += ",";
							}
							tips += name;
						}
					}
					if (StringUtils.isNotBlank(tips)) {
						fieldText = fieldText + "("+ tips +")";
					}
				}
				headers.add(fieldText);
				columnNames.add(fieldName);
			}

			Map<String,String> dictMap = new HashMap<>();
			Map<String,JSONObject> treeMap = new HashMap<>();
			Map<String,String> formatMap = new HashMap<>();
			String fileName = getI18nValue("工单导入模板") + DateUtil.getCurrentDateStr("yyyyMMddHHmmss") + ".xlsx";
			exportExcel(fileName, param, null, headers, columnNames, dictMap, treeMap, formatMap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			Render.renderJson(getRequest(), getResponse(), EasyResult.fail("获取导入模板异常"));
		}
	}

	/**
	 * 导入工单
	 * @return
	 */
	public void actionForImportOrder() {
		InputStream is = null;
		try {
			UserModel user = UserUtil.getUser(this.getRequest());
			String flowKey = getPara("flowKey");
			String operType = getPara("operType");
	        Part part = getFile("file");
	        is = part.getInputStream();
	        String name = part.getSubmittedFileName();
            logger.info("接收导入文件名:" + part.getSubmittedFileName());
            if (!name.endsWith(".xlsx") && !name.endsWith(".xls")) {
            	Render.renderJson(getRequest(), getResponse(), EasyResult.fail("上传失败:文件格式错误!"));
            	return;
            }
            // 表单
			List<JSONObject> fieldConfigs = getFormShowFieldConfig(flowKey);
            // 导入结果内容
    		List<List<Object>> resultData = new ArrayList<List<Object>>();
            // 读取内容
        	EasyExcel.read(is).registerReadListener(new ImportOrderListener(flowKey, fieldConfigs, user, resultData,operType)).readCache(new MapCache()).sheet().doRead();

    		List<String> headers = new ArrayList<String>();
    		headers.add("工单编号");
    		headers.add("处理结果");
    		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
    		for (String header : headers) {
    			ExcelHeaderStyle style = new ExcelHeaderStyle();
    			style.setData(header);
    			style.setWidth(3600);
    			style.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
    			styles.add(style);
    		}
    		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
    		ExcelUtils.getInstance().exportObjects2Excel(resultData, styles, file.getAbsolutePath());
    		renderFile(file, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "导入结果") + ".xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			Render.renderJson(getRequest(), getResponse(), EasyResult.fail("导入工单异常"));
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 获取表单字段查询配置信息
	 * @param flowKey		流程key
	 * @return
	 * @throws SQLException
	 */
	private List<JSONObject> getFormShowFieldConfig(String flowKey) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append("select t1.*");
		sql.append(", (case when t2.DB_TABLE_NAME is null then t1.AUTO_FORM_DB_ID else t2.DB_TABLE_NAME end) DB_TABLE_NAME");
		sql.append(", (case when t2.DB_NAME is null then 'db_order_main' else t2.DB_NAME end) DB_NAME");
		sql.append("from "+ getTableName("C_BO_AUTO_FORM_SHOW_FIELD") +" t1");
		sql.append("left join "+ getTableName("C_BO_AUTO_FORM_DB") +" t2 on t1.AUTO_FORM_DB_ID=t2.DB_NAME");
		sql.append(getEntId(), "and t2.ENT_ID=?", false);
		sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID=?", false);
		sql.append("where 1=1");
		sql.append(flowKey, "and t1.PROCESS_ID=?", false);
		// sql.append(DictConstants.ENABEL_STATUS_ENABLE, "and t1.STATUS=?", false);
		sql.append(DictConstants.DICT_SY_YN_Y, "and t1.IS_IMPORT_FIELD=?", false);
		sql.append(getEntId(), "and t1.ENT_ID=?", false);
		sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?", false);
		sql.append("order by t1.SORT asc");
		
		EasyQuery query = QueryFactory.getWriteQuery();
		List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		return list;
	}

	private void exportExcel(String title, JSONObject param, EasySQL sql, List<String> headers, List<String> fields, 
			Map<String, String> dictMap, Map<String,JSONObject> treeMap, Map<String, String> formatMap) throws Exception {
		ServletOutputStream os = null;
		UserModel user = UserUtil.getUser(getRequest());
		this.getResponse().reset();
		this.getResponse().setContentType("application/octet-stream; charset=utf-8");
		this.getResponse().setHeader("Content-Disposition", "attachment; filename="+URLEncoder.encode(title,"utf-8"));
		os = this.getResponse().getOutputStream();
		ExcelExportDataHandle handle = new ExcelExportDataHandleImpl();
		handle.setEntId(user.getEpCode());
		handle.setBusiOrderId(user.getBusiOrderId());
		handle.setRequest(getRequest());
		handle.setDictMap(dictMap);
		handle.setTreeMap(treeMap);
		handle.setFormatMap(formatMap);
		ExcelExport exportExcel = new ExcelExport().setUser(user).setEasySql(sql)
				.setFields(fields).setHeaderList(headers).setExcelExportDataHandle(handle);
		exportExcel.export(null,os,null);
	}
}
