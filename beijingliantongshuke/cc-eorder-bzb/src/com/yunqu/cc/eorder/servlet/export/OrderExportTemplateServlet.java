package com.yunqu.cc.eorder.servlet.export;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.DuplicateSubmit;
import com.yq.busi.common.annontation.DuplicateSubmit.LoseType;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.AttachmentUtil;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.FileUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.PdfUtil;
import com.yq.busi.common.util.UserUtil;
import com.yq.orderext.util.ProcessUtil;
import com.yunqu.cc.eorder.base.AppBaseServlet;
import com.yunqu.cc.eorder.base.Constants;
import com.yunqu.cc.eorder.base.QueryFactory;
import com.yunqu.cc.eorder.log.CommonLogger;
import com.yunqu.cc.eorder.utils.FileUtils;
import com.yunqu.cc.eorder.utils.FormUtils;
import com.yunqu.cc.eorder.utils.QueryProcessUtil;
import com.yunqu.cc.eorder.utils.WordUtils;
import com.yunqu.cc.eorder.utils.mapper.CryptorMapperImpl;
import com.yunqu.cc.oss.model.OSSAttachmentUtil;

@MultipartConfig
@WebServlet("/servlet/exportTemplateServlet")
public class OrderExportTemplateServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	WordUtils wordUtil = WordUtils.getInstance();

	private Map<String, Map<String, String>> dictMap = new HashMap<>();

	/**
	 * 上传附件
	 *
	 * @return
	 */
	@DuplicateSubmit(loseType = LoseType.CUSTOMIZE)
	public EasyResult actionForUpload() {
		EasyResult result = new EasyResult();
		try {

			String procKey = getPara("procKey");
			String processId = getPara("processId");

			if (StringUtils.isBlank(procKey)) {
				logger.error(getI18nValue("procKey为空"));
				result.addFail(procKey);
				return result;
			}

			logger.info("流程key:" + procKey);

			// 校验附件：检查业务对象的附件量是否超标
			String checkResult = AttachmentUtil.checkBusiAttachmentSize(this.getDbName(), getBusiId());

			if (StringUtils.isNotBlank(checkResult)) {
				logger.error(getI18nValue("上传word文档失败:") + checkResult);
				result.addFail(checkResult);
				return result;
			}

			List<Part> files = getFiles();
			for (Part part:files) {
				logger.info("看看part的情况" + part.toString());
				try {
					String submittedFileName = part.getSubmittedFileName();
					if (StringUtils.isBlank(submittedFileName)) {
						continue;
					}
					EasyQuery writeQuery = QueryFactory.getWriteQuery();
					long sizeInBytes = part.getSize();
					String fileName = submittedFileName;
					// long fileSize = sizeInBytes;

					// 校验附件：检查后缀、文件大小
					String checkResult2 = OSSAttachmentUtil.checkSizeAndSuffix(fileName, sizeInBytes);
					if (StringUtils.isNotBlank(checkResult2)) {
						logger.error("上传文档[" + fileName + "," + sizeInBytes + "]失败:" + checkResult2);
						String[] check = checkResult2.split(" ");
						result.addFail(getI18nValue(check[0]) + check[1] + getI18nValue(check[2]));
						return result;
					}

					// String suffix =
					// fileName.substring(fileName.lastIndexOf(".") + 1);
					// fileName = fileId+"."+suffix;

					// 获取存储的相对目录 如 /onlie/2020/10/10/11/
					String fileDir = "/online/template/eorder" + getEntId() + "/export/";
					logger.info("保存的目录：" + fileDir);

					// 获取文件相对路径 如 /onlie/2020/10/10/11/12.txt
					String filePath = OSSAttachmentUtil.getRelativeFilePath(fileDir, procKey +"_"+ fileName);
					logger.info("保存的文件的相对路径：" + filePath);

					// 获取文件绝对路径 如 /home/<USER>/onlie/2020/10/10/11/12.txt
					String fullPath = OSSAttachmentUtil.saveFileByRelativePath(part.getInputStream(), filePath);

					logger.info("全路径：" + fullPath);

					EasyRecord record = new EasyRecord(getTableName("C_WF_BPM_FILE"), "ID");
					record.put("ID", IDGenerator.getDefaultNUMID());
					record.put("PROCESS_ID", processId);
					record.put("FILE_TYPE", "4");
					JSONObject content = new JSONObject();
					content.put("fullPath", fullPath);
					record.put("CONTENT", content.toJSONString());
					record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					record.put("ENT_ID", getEntId());
					record.put("BUSI_ORDER_ID", getBusiOrderId());
					writeQuery.save(record);
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + "上传附件失败，原因：" + e.getMessage(), e);
					result.addFail(getI18nValue("操作失败"));
				} finally {
					if (part != null) {
						try {
							part.delete();
						} catch (Exception e2) {
						}
					}
				}
			}
			return EasyResult.ok("", getI18nValue("上传成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "上传文件失败，原因：" + e.getMessage(), e);
		}
		return result;
	}

	/**
	 * 检查上传的文件后缀、大小是否符合规范
	 *
	 * @param fileName
	 * @param fileSize
	 *            字节
	 * @return
	 */
	public String checkSizeAndSuffix(String fileName, long fileSize) {
		// 单个附件最大值(单位M)
		long maxFileLen = 10;
		// 附件格式
		String[] fileSuffix = "docx;".split(";");

		String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);

		if (!OSSAttachmentUtil.fileSuffixSet(fileSuffix, suffix)) {
			return "上传附件失败，附件格式应为: " + JSONObject.toJSONString(fileSuffix) + " 中的一种,可在参数配置中设置更多格式";
		}
		if (fileSize > 1024 * 1024 * maxFileLen) {
			return "上传附件失败，附件最大值只能为: " + maxFileLen + " M";
		}
		return null;
	}

	/**
	 * 按业务类型+业务id，删除附件
	 */
	public void deleteOrderTemplate(String filePath) {

		if (StringUtils.isNotBlank(filePath)) {

			FileUtil.deleFile(filePath);

		}

	}
	
	public EasyResult actionForDeleteFile() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String fileId = jsonObject.getString("fileId");
			
			EasyQuery query = QueryFactory.getWriteQuery();
			EasyRecord record = new EasyRecord(getTableName("C_WF_BPM_FILE"), "ID");
			record.put("ID", fileId);
			query.deleteById(record);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return EasyResult.error(500, BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败，原因：") + e.getMessage());
		}
	}

	/**
	 * 下载当前模板
	 */
	public EasyResult actionForDownLoad() {
		OutputStream toClient = null;
		try {
			JSONObject jsonObject = this.getJSONObject();
			String procKey = jsonObject.getString("procKey");
			String fileId = jsonObject.getString("fileId");
			
			String srcPath = getExportFilePathById(fileId);
			
			this.getResponse().reset();
			toClient = new BufferedOutputStream(this.getResponse().getOutputStream());
			this.getResponse().setContentType("application/octet-stream");
			// 如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
			this.getResponse().setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode(procKey + ".docx", "UTF-8"));
			FileUtil.readFile(srcPath, toClient);
			toClient.flush();
		} catch (Exception ex) {
			return EasyResult.error(500,
					BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败，原因：") + ex.getMessage());
		} finally {
			try {
				if (toClient != null) {
					toClient.close();
				}
			} catch (Exception ex) {
				logger.error(ex.getMessage(), ex);
			}
		}
		return null;
	}

	private String getExportFilePathById(String fileId) throws SQLException {
		EasyQuery query = QueryFactory.getReadQuery();
		EasySQL sql = new EasySQL();
		sql.append("select t1.*");
		sql.append("from "+ getTableName("C_WF_BPM_FILE") +" t1");
		sql.append("where 1=1");
		sql.append(fileId, "and t1.ID=?", false);
		JSONObject row = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		String content = row.getString("CONTENT");
		JSONObject contentJson = JSONObject.parseObject(content);
		String srcPath = contentJson.getString("fullPath");
		return srcPath;
	}

	public JSONObject actionForExistsFile() {
		try {
			JSONObject jsonObject = this.getJSONObject();
	
			String procKey = jsonObject.getString("procKey");
			String processId = jsonObject.getString("processId");
			
			EasyQuery query = QueryFactory.getReadQuery();
			
			EasySQL sql = new EasySQL();
			sql.append("select t1.*");
			sql.append("from "+ getTableName("C_WF_BPM_FILE") +" t1");
			sql.append("where 1=1");
			if (StringUtils.isBlank(processId) && StringUtils.isNotBlank(procKey)) {
				sql.append(procKey, "and t1.PROCESS_ID in (select ID from "+ getTableName("C_WF_PROCESS") +" where FLOW_KEY=?");
				sql.append(getEntId(), "and ENT_ID=?");
				sql.append(getBusiOrderId(), "and BUSI_ORDER_ID=?");
				sql.append(")");
			} else {
				sql.append(processId, "and t1.PROCESS_ID=?", false);
			}
			sql.append("4", "and t1.FILE_TYPE=?");
			sql.append(getEntId(), "and t1.ENT_ID=?");
			sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
			List<JSONObject> rows = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			JSONArray list = new JSONArray();
			for (JSONObject row:rows) {
				String id = row.getString("ID");
				String content = row.getString("CONTENT");
				JSONObject contentJson = JSONObject.parseObject(content);
				String fullPath = contentJson.getString("fullPath");
				File file = new File(fullPath);
				String fileName = file.getName();
				
				JSONObject data = new JSONObject();
				data.put("fileName", fileName);
				data.put("fileId", id);
				list.add(data);
			}
	
			return EasyResult.ok(list);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return EasyResult.error();
		}
	}

	/**
	 * 判断文件是否存在
	 *
	 * @param path
	 */
	public Boolean existFile(String path) {
		try {
			File file = new File(path);
			return file.exists();

		} catch (Exception e) {
			logger.error("[FileUtil.createFile] throw exception:" + e.getMessage(), e);
			return false;
		}
	}

	public void actionForExportPdfFile() {
		ZipOutputStream zos = null;
		try {
			String procIdStr =  this.getRequest().getParameter("data");// 判断页面是否有勾选ID
			String fileId =  this.getRequest().getParameter("fileId");// 判断页面是否有勾选ID
			Assert.hasText(procIdStr, "流程实例id不能为空");

			logger.info(procIdStr+"的word导出");
			EasyQuery query = this.getQuery();

			String click = "☑";

			String procInstId = procIdStr;
			String useTableName = "C_BO_BASE_ORDER";
			if("2".equals(this.getRequest().getParameter("type"))){
				useTableName = "c_bo_base_order_his";
			}
			EasySQL sql = new EasySQL(" select  ");
			sql.append(" ID,PROC_KEY,ORDER_NO from  "+this.getTableName(useTableName));
			sql.append(" where 1=1 ");
			sql.append(procInstId," and PROC_INST_ID = ?");

			logger.info("sql:"+sql.getSQL());
			logger.info(sql.getParamsList().toString());

			EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams());

			String procKey = row.getColumnValue("PROC_KEY");
			String formId = row.getColumnValue("ID");

			String srcPath = getExportFilePathById(fileId);
			// String srcPath = OSSAttachmentUtil.getRootPath() + "/online/template/eorder" + getEntId() + "/export/" + procKey + ".docx";
			logger.info("读取文件的路径：" + srcPath);
			if (!existFile(srcPath)) {
				renderText("未上传工单模板文件, 无法导出");
				return;
			}

			String orderNo = row.getColumnValue("ORDER_NO");
			if (StringUtils.isNotBlank(formId) && StringUtils.isNotBlank(procInstId)) {
				File file2 = order2Word(click, procInstId, formId, srcPath);
				OutputStream os = null;
				try (FileInputStream fs = new FileInputStream(file2)) {
					String fileName = orderNo +".pdf";
					HttpServletResponse response = getResponse();
					HttpServletRequest request = getRequest();

					String contentType = "application/pdf";
		            response.setCharacterEncoding("UTF-8");
		            response.setContentType(contentType == null ? "application/octet-stream" : contentType);
		            String clientFileName = StringUtils.isBlank(fileName) ? file2.getName() : fileName;
		            response.setHeader("Content-disposition", (new StringBuilder("attachment;")).append(Render.encodeFileName(request, clientFileName)).toString());
					os = response.getOutputStream();
					PdfUtil.doc2pdf(fs, os);
					os.flush();
				} catch (Exception e) {
					throw e;
				} finally {
					if (file2 != null) {
						file2.deleteOnExit();
					}
					if (os != null) {
			            os.close();
					}
				}
			} else {
				renderText("表单id或流程实例id为空, 无法导出");
				return;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			renderText("导出异常: "+ e.getMessage());
		} finally {
			if (zos != null) {
				try {
					zos.close();
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}

	public void actionForExportWordFile() throws FileNotFoundException {
		ZipOutputStream zos = null;
		FileInputStream fis = null;
		try {
			String procIdStr =  this.getRequest().getParameter("data");// 判断页面是否有勾选ID
			logger.info(procIdStr+"的word导出");
			String[] procIds = procIdStr.split(",");
			EasyQuery query = this.getQuery();

			String click = "☑";
			if (procIds != null) {
				File zipFile = new File("工单导出文件" + ".zip");
				zos = new ZipOutputStream(new FileOutputStream(zipFile));
				byte[] buffer = new byte[1024];
				for (String procId : procIds) {
					String procInstId =procId;
					String useTableName = "C_BO_BASE_ORDER";
					if("2".equals(this.getRequest().getParameter("type"))){
						useTableName = "c_bo_base_order_his";
					}
					EasySQL sql = new EasySQL(" select  ");
					sql.append(" ID,PROC_KEY,ORDER_NO from  "+this.getTableName(useTableName));
					sql.append(" where 1=1 ");
					sql.append(procInstId," and PROC_INST_ID = ?");

					logger.info("sql:"+sql.getSQL());
					logger.info(sql.getParamsList().toString());

					EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams());
					if(Objects.isNull(row) && "0".equals(this.getRequest().getParameter("type"))){
						sql = new EasySQL(" select  ");
						sql.append(" ID,PROC_KEY,ORDER_NO from  "+this.getTableName("c_bo_base_order_his"));
						sql.append(" where 1=1 ");
						sql.append(procInstId," and PROC_INST_ID = ?");
						row = query.queryForRow(sql.getSQL(), sql.getParams());
					}
					if (Objects.isNull(row)) {
						continue;
					}
					String procKey = row.getColumnValue("PROC_KEY");
					String formId = row.getColumnValue("ID");
					
					String srcPath = cache.get("eorder_first_template_path_"+ procKey);
					if (StringUtils.isBlank(srcPath)) {
						srcPath = getFirstTemplatePath(procKey);
						cache.put("eorder_first_template_path_"+ procKey, srcPath, 60);
					}
					logger.info("读取文件的路径：" + srcPath);
					if (existFile(srcPath)) {
						String orderNo = row.getColumnValue("ORDER_NO");
						if (StringUtils.isNotBlank(formId) && StringUtils.isNotBlank(procInstId)) {
							File file2 = order2Word(click, procInstId, formId, srcPath);
							String suffix = ".docx";
							String serviceId = "STAMP-SERVICE-cx-order-hlj";
							IService service = ServiceContext.getService(serviceId);
							if(file2 != null && service!=null){
								//调用ISERVICE接口处理需要盖章的工单
								logger.info("[OrderExportTemplateServlet.actionForExportWordFile]存在接口,进行签章处理......");
								String path = file2.getPath();
								String command = "wordStampByKey";
								JSONObject j= new JSONObject();
								//文件绝对路径地址
								j.put("fullPath", path);
								//文件签章方式
								j.put("command", command);
								//工单流程key，特定的工单才能进行签章
								j.put("orderProcess", procKey);
								JSONObject serviceResult = service.invoke(j);
								//接口返回成功进行替换文件处理
								if(serviceResult!=null && StringUtils.equals(serviceResult.getString("respCode"),"000")){
									File pdfFile = new File(serviceResult.getString("filePath"));
									if(pdfFile.exists()){
										//判断接口返回的工单是否存在，存在进行替换文件和后缀
										file2 = pdfFile;
										suffix = ".pdf";
									}
								}
							}
							fis = new FileInputStream(file2);
							zos.putNextEntry(new ZipEntry(orderNo + suffix));
							int len;
							// 读入需要下载的文件的内容，打包到zip文件
							while ((len = fis.read(buffer)) != -1) {
								zos.write(buffer, 0, len);
							}
						}
					}
				}
				zos.close();
				FileUtils.downloadZip(zipFile, this.getResponse());
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			try {
				zos.flush();
				zos.closeEntry();
				fis.close();
			} catch (IOException e) {
				logger.error(e.getMessage(), e);
			}
		}
	}
	
	/**
	 * 根据流程key获取第一份模板文件
	 * @param flowKey
	 * @return
	 */
	private String getFirstTemplatePath(String flowKey) {
		try {
			EasyQuery query = QueryFactory.getReadQuery();
			EasySQL sql = new EasySQL();
			sql.append("select t1.*");
			sql.append("from "+ getTableName("C_WF_BPM_FILE") +" t1");
			sql.append("left join "+ getTableName("C_WF_PROCESS") +" t2 on t1.PROCESS_ID=t2.ID");
			sql.append("where 1=1");
			sql.append("4", "and t1.FILE_TYPE=?", false);
			sql.append(flowKey, "and t2.FLOW_KEY=?", false);
			sql.append(getEntId(), "and t2.ENT_ID=?", false);
			sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID=?", false);
			JSONObject row = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (row != null) {
				String content = row.getString("CONTENT");
				JSONObject contentJson = JSON.parseObject(content);
				return contentJson.getString("fullPath");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}

	private File order2Word(String click, String procInstId, String formId, String srcPath)
			throws ParseException, SQLException {
		JSONObject result = getDivFieldValues(formId, procInstId).getJSONObject("data");
		Map<String, String> replaceMap = new HashMap<>();
		Set<String> dbSet = result.keySet();
		String dateFieldStr = result.getString("date");
		String treeSelectFieldStr = result.getString("treeSelect");
		Set<String> treeSelectFields = new HashSet<String>();
		Set<String> dateFields = new HashSet<String>();
		if (StringUtils.isNotBlank(dateFieldStr)) {
			dateFieldStr = dateFieldStr.substring(1, dateFieldStr.length() - 1);
			String[] dateFieldArr = dateFieldStr.split(",");
			for (String dateField : dateFieldArr) {
				dateFields.add(StringUtils.trim(dateField));
			}
		}
		dateFields.add("CREATE_TIME");
		logger.info("dateFields为：" + dateFields);
		if (StringUtils.isNotBlank(treeSelectFieldStr)) {
			treeSelectFieldStr = treeSelectFieldStr.substring(1, treeSelectFieldStr.length() - 1);
			logger.info("treeSelectFieldStr：" + treeSelectFieldStr);
			String[] treeSelectFieldArr = treeSelectFieldStr.split(",");
			for (String treeSelectField : treeSelectFieldArr) {
				logger.info("添加进set:" + treeSelectField);
				treeSelectFields.add(StringUtils.trim(treeSelectField));
			}
			logger.info("treeSelectFields为：" + treeSelectFields);
		}
		for (String db : dbSet) {
			// 获得key
			if (!"date".equals(db) && !"treeSelect".equals(db)) {
				JSONObject value = result.getJSONObject(db);
				Set<String> mainSet = value.keySet();
				logger.info("dictMap：" + dictMap.toString());
				Map<String, String> dict = dictMap.get(db);
				String dbChName = value.getString("DB_CH_NAME");
				for (String mainObject : mainSet) {
					logger.info("循环的过程中key：" + mainObject);
					String replaceValue = value.getString(mainObject);
					logger.info("循环的过程中value：" + replaceValue);
					if (dict != null && dict.containsKey(db + "$" + mainObject)) {
						String selectContent = dict.get(db + "$" + mainObject);
						JSONObject selectOptions = isJsonObject(selectContent);
						if (selectOptions == null) {
							// 说明select使用的字典
							logger.info("该字段为" + mainObject + "--使用字段：" + selectContent);
							String replaceValue1 = "";
							Map<String, Object> selectMaps = DictCache.getMapEnableDictListByGroupCode(getEntId(),
									selectContent);
							StringBuilder newStr = new StringBuilder();
							for (Entry<String, Object> entry : selectMaps.entrySet()) {
								if (StringUtils.trim(replaceValue).equals(StringUtils.trim(entry.getValue().toString()))) {
									newStr.append(click + entry.getKey().toString() + "   ");
									replaceValue1 = entry.getKey();
								} else {
									newStr.append("口" + entry.getKey().toString() + "   ");
								}
							}
							if (StringUtils.isBlank(replaceValue1)) {
								replaceMap.put("${" + dbChName + "." + mainObject + "#1}", replaceValue);
							} else {
								replaceMap.put("${" + dbChName + "." + mainObject + "#1}", replaceValue1);
							}
							replaceValue = newStr.toString();
							replaceMap.put("${" + dbChName + "." + mainObject + "#2}", replaceValue);
						} else {
							logger.info("需要转化的options JSONarray 的str:" + selectContent);
							String replaceValue1 = "";
							logger.info(replaceValue1);
							StringBuilder newStr = new StringBuilder();
							Iterator iter = selectOptions.entrySet().iterator();
							while (iter.hasNext()) {
								Map.Entry entry = (Map.Entry) iter.next();
								if (StringUtils.trim(replaceValue).equals(StringUtils.trim(entry.getValue().toString()))
										|| StringUtils.trim(replaceValue)
												.equals(StringUtils.trim(entry.getKey().toString()))) {
									newStr.append(click + entry.getValue().toString() + "   ");
									replaceValue1 = entry.getValue().toString();
								} else {
									newStr.append("口" + entry.getValue().toString() + "   ");
								}
							}
							if (StringUtils.isBlank(replaceValue1)) {
								replaceMap.put("${" + dbChName + "." + mainObject + "#1}", replaceValue);
							} else {
								replaceMap.put("${" + dbChName + "." + mainObject + "#1}", replaceValue1);
							}
							replaceValue = newStr.toString();
							replaceMap.put("${" + dbChName + "." + mainObject + "#2}", replaceValue);
						}
					}
					// 判断字段是不是时间
					logger.info(dateFields + "---" + mainObject);
					// 先隐藏起来 时间字段通过这种形式规则转化 ${CREATE_TIME#yyyy-MM-dd}
					if (dateFields.contains(mainObject) && StringUtils.isNotBlank(replaceValue)) {
						logger.info(mainObject + "为时间字段");
						logger.info("前：" + replaceValue);
						// 获取该形式的日期转化格式
						List<String> timeFieldValues = changeDateFormat(replaceValue);
						if (timeFieldValues.size() > 0) {
							replaceMap.put("${" + dbChName + "." + mainObject + "#yyyy年MM月dd日 hh时mm分ss秒}",
									timeFieldValues.get(0));
							replaceMap.put("${" + dbChName + "." + mainObject + "#yyyy年MM月dd日}",
									timeFieldValues.get(1));
							replaceMap.put("${" + dbChName + "." + mainObject + "#yyyy-MM-dd}", timeFieldValues.get(2));
							replaceMap.put("${" + dbChName + "." + mainObject + "#yyyy-MM-dd hh:mm:ss}",
									timeFieldValues.get(3));
						}
						logger.info("后：" + replaceValue);
					}
					if (treeSelectFields.contains(mainObject) && StringUtils.isNotBlank(replaceValue)) {
						logger.info(mainObject + "为通用树字段");
						logger.info("前：" + replaceValue);
						replaceValue = changeTreeSelect(replaceValue);
						logger.info("后：" + replaceValue);
					}
					// logger.info("循环的过程中value："+replaceValue);
					replaceMap.put("${" + dbChName + "." + mainObject + "}", replaceValue);
				}
			}
		}
		logger.info("replaceMap:" + replaceMap.toString());
		logger.info("读取文件的路径：" + srcPath);
		File file2 = wordUtil.searchAndReplace(srcPath, replaceMap, this.getResponse());
		return file2;
	}

	public void actionForTest() {
		Map<String, String> map = new HashMap<>();
		map.put("${工单主数据源.ORDER_NO}", "融化黑暗之温暖");
		map.put("${工单主数据源.ID}", "1111111111");
		map.put("${特殊业务受理.NEXT_HANDLE_NAME}", "小花");
		map.put("${特殊业务受理.CUST_NAME}", "小明");
	}



	public static void main(String[] args) throws FileNotFoundException {

		String string = "11111";
		System.out.println(string.split(",")[0]);
	}

	private JSONObject getDivFieldValues(String dataId, String formId) {

		EasyQuery query = this.getQuery();
		try {
			JSONObject result = new JSONObject();
			EasySQL sql = new EasySQL();

			// logger.info("重头戏");
			logger.info(ProcessUtil.findLastTask(formId));

			// logger.info("进来这里了");
			sql.append("SELECT ID,FORM_NAME,FORM_CONTENT,LOWER(MAIN_TABLE_SOURCE) MAIN_TABLE_SOURCE");
			sql.append("FROM " + this.getTableName("C_BO_AUTO_FORM"));
			sql.append(ProcessUtil.findLastTask(formId).getTaskFormKey(), "WHERE ID = ?", false);
			sql.append("1"," AND FORM_STATUS = ? ");
			logger.info("sql是：" + sql.getSQL() + "--------" + sql.getParamsList().toString());

			JSONObject autoFormJson = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String, List<String>> tableMap = getTableFieldList(autoFormJson.getJSONObject("FORM_CONTENT"));

			logger.info(CommonUtil.getClassNameAndMethod(this) + "tableMap:" + JSONObject.toJSONString(tableMap));
			logger.info(CommonUtil.getClassNameAndMethod(this) + "dataId:" + dataId);
			JSONObject dataJson = new JSONObject();

			for (String tableName : tableMap.keySet()) {

				if(!"date".equals(tableName)&&!"treeSelect".equals(tableName)){
					JSONObject orderMainData = queryTableInfo(dataId, tableMap.get(tableName), tableName);
					if (orderMainData != null) {
						dataJson.put(tableName.toLowerCase(), orderMainData);
					}
					// 兼容历史工单
					if ("db_order_main".equalsIgnoreCase(tableName) && !dataJson.containsKey(tableName.toLowerCase())) {
						JSONObject orderMainHisData = queryHisTableInfo(dataId, tableMap.get(tableName), tableName);
						dataJson.put(tableName.toLowerCase(), orderMainHisData);
					}
				}else{
					dataJson.put(tableName, tableMap.get(tableName));
				}

			}
			result.put("data", dataJson);
			logger.info("看看是什么");
			logger.info(result.toJSONString());
			return result;
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.error(999, "查询失败");
		}
	}

	private Map<String, List<String>> getTableFieldList(JSONObject formContent) {
		Map<String, List<String>> map = new HashMap<String, List<String>>();

		List<JSONObject> optionList = getOptions(formContent.getJSONArray("list").toJavaList(JSONObject.class));
		String tableName = null;
		Map<String,String> dict = new HashMap<>();

		for (JSONObject option : optionList) {

			//logger.info("optionList的长度："+optionList.size());;

			JSONObject options = option.getJSONObject("options");

			logger.info("options如下：");
			logger.info(option.toJSONString());

			if("address".equals(option.getString("type"))) {
				getDbFieldToMap(options.getString("provinceName"), map,"1");
				getDbFieldToMap(options.getString("areaName"), map,"1");
				getDbFieldToMap(options.getString("countyName"), map,"1");
			} else if("user-select".equals(option.getString("type"))) {
				getDbFieldToMap(options.getString("accField"), map,"1");
				getDbFieldToMap(options.getString("nameField"), map,"1");
			} else if("checkbox".equals(option.getString("type"))||"select".equals(option.getString("type"))||"radio".equals(option.getString("type"))) {
				logger.info("select类型的字段");
				String fieldName = null;
				String dbField = options.getString("dbField");
				logger.info(dbField);
					if(StringUtils.isNotBlank(dbField) && dbField.indexOf("[") > -1) {
						String dictName = options.getString("dictName");
						JSONArray dbFieldArr = JSONArray.parseArray(dbField);
						tableName = dbFieldArr.getString(0);
						fieldName = dbFieldArr.getString(1);
						logger.info("开始puttoDict"+fieldName+"----"+dictName);
						if(options.containsKey("options")) {
							if(options.containsKey("treeType")){
								//通用树取值
								getDbFieldToMap(options.getString("dbField"), map,"4");
							}else{
								//否则select类型的字段是从字典取到的
								String selectOption = options.getString("options");
								JSONArray jsonStr = JSONArray.parseArray(selectOption);
								JSONObject optionObject = new JSONObject();
						        for(int i=0;i<jsonStr.size();i++){
						        	JSONObject json = jsonStr.getJSONObject(i); // 遍历 jsonarray 数组，把每一个对象转成 json 对象
						        	if(json !=null) {
							        	optionObject.put(json.getString("value"), json.getString("label"));
						        	}
						        }
								dict.put(tableName+"$"+fieldName,optionObject.toJSONString());
							}
						}else {
							dict.put(tableName+"$"+fieldName,dictName);


						}

					}

				getDbFieldToMap(options.getString("dbField"), map,"1");


			} else if("dept-select".equals(option.getString("type"))){
				getDbFieldToMap(options.getString("deptCodeField"), map,"1");
				getDbFieldToMap(options.getString("deptNameField"), map,"1");
			}

			else if("date".equals(option.getString("type"))||"datetime".equals(option.getString("type"))) {

				logger.info("这个是date类型的字段");
				getDbFieldToMap(options.getString("dbField"), map,"3");
			}else if("cascader".equals(option.getString("type"))){

				getDbFieldToMap(options.getString("dbField"), map,"4");


			}

			else {

				//logger.info("key:"+options.getString("dbField"));
				//logger.info("value:"+map.toString());

				getDbFieldToMap(options.getString("dbField"), map,"1");
			}
			if (options.containsKey("labelModel")) {
				getDbFieldToMap(options.getString("labelModel"), map,"1");
			}

			if(!dictMap.containsKey(tableName)){
				//System.out.println(dict.toString());
				dictMap.put(tableName, dict);
			}

		}


		//logger.info("进来这里存缓存了");
		//logger.info(dictMap.toString());
		//if(flag){
			//cache.put("CC_EORDER_AUTO_FIELD_DICT", dictMap,3600*2);
			//logger.info("看看到底是什么？"+cache.get("CC_EORDER_AUTO_FIELD_DICT").toString());

		//}


		return map;
	}

	/**
	 *
	 * @param dataId
	 *            主表ID
	 * @param fieldList
	 *            字段列表
	 * @param dbName
	 *            数据源key
	 * @throws SQLException
	 */
	private JSONObject queryTableInfo(String dataId, List<String> fieldList, String dbName) throws SQLException {
		List<JSONObject> mainFieldList = FormUtils.getFieldListByDb(dbName, getEntId());
		
		// String mainKey = getMainKeyField(mainFieldList); // 查询该表主键
		String dataKey = "Y".equals(getIsMainByDb(dbName)) ? "ID" : "M_ID"; // 主表使用ID
		Map<String, JSONObject> fieldOptions = mainFieldList.stream().distinct().collect(Collectors.toMap(item -> item.getString("FIELD_NAME"), item -> item));

		String dbInfo = getTableNameByDb(dbName);

		logger.info("dbInfo:" + dbInfo);

		String[] dbInfoArr = dbInfo.split("~");
		logger.info("tableName:" + dbInfoArr[1]);
		JSONObject jsonObject = this.getQuery()
				.queryForRow("SELECT * FROM "
						+ this.getTableName(dbInfoArr[1]) + " WHERE " + dataKey + " = ? ORDER BY CREATE_TIME DESC",
						new Object[] { dataId }, new CryptorMapperImpl(getEntId(), fieldOptions));

		if (jsonObject != null) {
			jsonObject.put("DB_CH_NAME", dbInfoArr[0]);
		}

		return jsonObject;
	}

	/**
	 *
	 * @param dataId
	 *            主表ID
	 * @param fieldList
	 *            字段列表
	 * @param dbName
	 *            数据源key
	 * @throws SQLException
	 */
	private JSONObject queryHisTableInfo(String dataId, List<String> fieldList, String dbName) throws SQLException {

		String mainKey = "ID"; // 查询该表主键
		String dataKey = "ID"; // 主表使用ID

		String dbInfo = getTableNameByDb(dbName);
		String[] dbInfoArr = dbInfo.split("~");
		JSONObject jsonObject = this.getQuery()
				.queryForRow("SELECT " + mainKey + "," + String.join(",", fieldList) + " FROM "
						+ this.getTableName("C_BO_BASE_ORDER_HIS") + " WHERE " + dataKey
						+ " = ? ORDER BY CREATE_TIME DESC", new Object[] { dataId }, new JSONMapperImpl());
		jsonObject.put("DB_CH_NAME", dbInfoArr[0]);

		return jsonObject;
	}

	/**
	 * 获取所有控件属性集合
	 *
	 * @param formContent
	 * @return
	 */
	private List<JSONObject> getOptions(List<JSONObject> list) {
		List<JSONObject> options = new ArrayList<JSONObject>();
		for (JSONObject option : list) {
			if ("grid".equals(option.getString("type"))) {
				setGridOptions(options, option);
			} else if ("tabs".equals(option.getString("type"))) {
				setTabsOptions(options, option);
			} else {
				options.add(option);
			}
		}
		return options;
	}

	private void setTabsOptions(List<JSONObject> options, JSONObject option) {
		List<JSONObject> items = option.getJSONArray("items").toJavaList(JSONObject.class);
		for (JSONObject item : items) {
			JSONArray ls = item.getJSONArray("list");
			for (int i = 0; ls != null && i < ls.size(); i++) {
				JSONObject it = ls.getJSONObject(i);
				if ("grid".equals(it.getString("type"))) {
					setGridOptions(options, it);
				} else {
					options.add(it);
				}
			}
		}
	}

	private void setGridOptions(List<JSONObject> options, JSONObject option) {
		for (JSONObject columns : option.getJSONArray("columns").toJavaList(JSONObject.class)) {
			options.addAll(columns.getJSONArray("list").toJavaList(JSONObject.class));
		}
	}

	/**
	 *
	 * @param dbField
	 * @param map
	 * @param type  3 为 date 1为无多余处理    2为select  4为通用树 5为checkbox
	 * @param extend 扩展字符串
	 */
	private void getDbFieldToMap(String dbField, Map<String, List<String>> map,String type) {
		String tableName = null;
		String fieldName = null;
		if(StringUtils.isNotBlank(dbField) && dbField.indexOf("[") > -1) {
			JSONArray dbFieldArr = JSONArray.parseArray(dbField);
			if(!CommonUtil.listIsNotNull(dbFieldArr)) {
				return;
			}
			tableName = dbFieldArr.getString(0);
			fieldName = dbFieldArr.getString(1);
		} else if (StringUtils.isNotBlank(dbField) && dbField.indexOf("|") > -1) {
			String[] dbFieldArr = dbField.split("\\|");
			tableName = dbFieldArr[0];
			fieldName = dbFieldArr[1];
		}
		if (StringUtils.isNotBlank(tableName) && StringUtils.isNotBlank(fieldName)) {
			List<String> fieldList = null;
			if(map.containsKey(tableName)) {
				fieldList = map.get(tableName);
			} else {
				fieldList = new ArrayList<String>();
			}
			fieldList.add(fieldName);
			map.put(tableName, fieldList);
		}


		if("3".equals(type)){
			//说明是时间类型
			List<String> dateFieldList = null;
			if(map.containsKey("date")) {
				dateFieldList = map.get("date");
			} else {
				dateFieldList = new ArrayList<String>();
			}
			dateFieldList.add(fieldName);
			map.put("date", dateFieldList);

		}

		if("4".equals(type)){
			//说明是通用树字段
			List<String> dateFieldList = null;
			if(map.containsKey("treeSelect")) {
				dateFieldList = map.get("treeSelect");
			} else {
				dateFieldList = new ArrayList<String>();
			}



			dateFieldList.add(fieldName);
			map.put("treeSelect", dateFieldList);
		}




	}

	/**
	 * 根据数据源Key查询字段
	 *
	 * @param dbName
	 *            数据源key
	 * @return
	 * @throws SQLException
	 */
	private List<JSONObject> getFieldListByDb(String dbName) throws SQLException {
		return this.getQuery().queryForList(
				"SELECT FIELD_NAME,FIELD_TEXT,FIELD_TYPE,REF_TABLE_NAME,REF_TABLE_FIELD,GENERATE_TYPE FROM "
						+ this.getTableName("C_BO_AUTO_FORM_DB_FIELD") + " WHERE LOWER(AUTO_FORM_DB_ID) = ?",
				new Object[] { dbName }, new JSONMapperImpl());
	}

	/**
	 * 获取主键字段
	 *
	 * @param fieldList
	 *            字段列表
	 * @return
	 */
	private String getMainKeyField(List<JSONObject> fieldList) {
		for (JSONObject fieldJson : fieldList) {
			if ("1".equals(fieldJson.getString("FIELD_TYPE"))) {
				return fieldJson.getString("FIELD_NAME");
			}
		}
		return null;
	}

	private String getTableNameByDb(String dbName) throws SQLException {


		logger.info("getTableNameByDb:");
		logger.info("SELECT CONCAT(CONCAT(DB_CH_NAME, '~'),DB_TABLE_NAME) FROM "+this.getTableName("C_BO_AUTO_FORM_DB") + " WHERE DB_NAME = "+dbName+" AND STATUS = 01");

		return this.getQuery().queryForString("SELECT CONCAT(CONCAT(DB_CH_NAME, '~'),DB_TABLE_NAME) FROM "
				+ this.getTableName("C_BO_AUTO_FORM_DB") + " WHERE DB_NAME = ? AND STATUS = ?", dbName, "01");
	}

	private String getIsMainByDb(String dbName) throws SQLException {
		return this.getQuery().queryForString(
				"SELECT IS_MAIN FROM " + this.getTableName("C_BO_AUTO_FORM_DB") + " WHERE DB_NAME = ? AND STATUS = ?",
				dbName, "01");
	}

	public EasySQL getSimpleOrderListSql(String dept, String orderNo, String status, String sendStartTime,
			String sendEndTime, String createStartTime, String createEndTime, String orderTitle, String caller,
			String REMARK1, String REMARK2, String processId, JSONObject baseJson, String userDataBase) {
		UserModel user = UserUtil.getUser(this.getRequest());
		String dbTableName = null;
		List<JSONObject> queryForList = null;
		List<EasyRow> records = new ArrayList<EasyRow>();
		EasySQL sql = new EasySQL();
		try {
			if (StringUtils.isNotBlank(processId)) {

				// 有流程分类
				records = QueryProcessUtil.getDbField(user, processId);

			}
			sql = new EasySQL(" select  ");
			sql.append(" C1.PROC_NAME AS NAME,C1.ID as M_ID,C1.PROC_KEY,C1.ORDER_NO,C1.PROC_INST_ID ");

			Map<String, String> relationMap = new HashMap<String, String>();
			for (int i = 0; i < records.size(); i++) {
				EasyRow record = records.get(i);
				if (record != null) {
					String dbName = record.getColumnValue("DB_NAME");
					dbTableName = record.getColumnValue("DB_TABLE_NAME");
					// 查询要显示的字段
					queryForList = QueryProcessUtil.getFieldList(user, dbName, processId);
					if (StringUtils.isNotBlank(dbTableName)) {
						// 遍历list
						for (JSONObject json : queryForList) {
							String fieldName = json.getString("FIELD_NAME");
							// 拼接sql
							sql.append(",C" + (5 + i) + "." + fieldName);
						}
						relationMap.put(dbName, "C" + (5 + i));
					}
				}
			}

			sql.append(" from " + getTableName(userDataBase) + " C1 ");

			if (Integer.parseInt(dept) == 0 || Integer.parseInt(dept) == 1) {
				sql.append(" LEFT JOIN " + getTableName(" C_BO_ORDER_FOLLOW ") + " C2 on C1.ID=C2.ORDER_ID ");
			}

			for (int i = 0; i < records.size(); i++) {
				EasyRow record = records.get(i);
				String dbName = record.getColumnValue("DB_NAME");
				dbTableName = record.getColumnValue("DB_TABLE_NAME");
				if (StringUtils.isNotBlank(dbTableName)) {
					sql.append(" LEFT JOIN " + getTableName(dbTableName) + " " + relationMap.get(dbName) + " on "
							+ relationMap.get(dbName) + ".M_ID=C1.ID");
				}
			}

			sql.append(" where 1=1 ");
			if (Integer.parseInt(dept) == 0) {
				sql.appendLike(UserUtil.getUser(getRequest()).getDeptCode(), " and C2.CREATE_DEPT_CODE like ? ");
			} else if (Integer.parseInt(dept) == 1) {
				sql.append(UserUtil.getUser(getRequest()).getUserAcc(), " and C2.CREATE_ACC = ? ");
			}

			sql.append(processId, " and C1.PROC_KEY = ? ");
			sql.append(getEntId(), " and C1.EP_CODE = ? ");
			sql.append(getBusiOrderId(), " and C1.BUSI_ORDER_ID = ? ");
			// sql.append(orderNo," and C1.ORDER_NO = ? ");
			sql.appendLike(orderNo, " and C1.ORDER_NO like ? ");
			sql.append(status, " and C1.STATUS = ? ");
			sql.append(sendStartTime, " and C1.SEND_TIME >= ?  ");
			sql.append(sendEndTime, " and C1.SEND_TIME <= ? ");
			sql.append(createStartTime, " and C1.CREATE_TIME >= ? ");
			sql.append(createEndTime, " and C1.CREATE_TIME <= ? ");
			sql.append(orderTitle, " and C1.ORDER_TITLE = ?");
			sql.appendRLike(caller, " and C1.CALLER like ?");
			sql.appendLike(REMARK1, " and C1.REMARK1 like ? ");
			sql.appendLike(REMARK2, " and C1.REMARK2 like ? ");
			QueryProcessUtil.extendQuery(getJSONObject(), sql, relationMap,user, processId);
			sql.append(" order by C1.CREATE_TIME desc ");

		} catch (SQLException e) {
			CommonLogger.logger.error(e.getMessage(), e);

		}
		return sql;
	}


	/**
	 * 转化时间字段字符串为 yyyy年MM月dd日 hh时mm分ss秒这种模式，人性化操作
	 * @param dateStr
	 * @return
	 * @throws ParseException
	 */
	  public List<String> changeDateFormat(String dateStr) throws ParseException
	  {
		//  时间字段可以添加了格式转化后word导出,例如${投诉流程.CREATE_TIME#yyyy年MM月dd日}
		  List<String> timeFormatValue = new ArrayList<>();

		  try {
			     SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
				 SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy年MM月dd日 hh时mm分ss秒");
			     SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy年MM月dd日");
			     SimpleDateFormat sdf4 = new SimpleDateFormat("yyyy-MM-dd");

			     //String dateStr = "2009-02-15 09:21:35.345";
			     //默认转化为这个
				 Date date = sdf1.parse(dateStr);//提取格式中的日期



				 String dateStr2 = sdf2.format(date); //改变格式

				 timeFormatValue.add(dateStr2);

				 String dateStr3 = sdf3.format(date); //改变格式

				 timeFormatValue.add(dateStr3);

				 String dateStr4 = sdf4.format(date); //改变格式

				 timeFormatValue.add(dateStr4);


				 String dateStr1 = sdf1.format(date); //改变格式

				 timeFormatValue.add(dateStr1);
				 return timeFormatValue;

		  }catch (Exception e) {

			  logger.error("word导出时时间字段的值转化失败："+dateStr);
				return timeFormatValue;
		}





	  }

	  /**
	  * 判断字符串是否可以转化为JSON数组
	  * @param content
	  * @return
	  */
	  public  JSONArray isJsonArray(String content) {
	      if(StringUtils.isBlank(content))
	          return null;
	      StringUtils.isEmpty(content);
	      try {
	          JSONArray jsonStr = JSONArray.parseArray(content);
	          return jsonStr;
	      } catch (Exception e) {
	          return null;
	      }
	  }


	  /**
	  * 判断字符串是否可以转化为json对象
	  * @param content
	  * @return
	  */
	  public  JSONObject isJsonObject(String content) {

	      if(StringUtils.isBlank(content))
	          return null;
	      try {
	          JSONObject jsonStr = JSONObject.parseObject(content);
	          return jsonStr;
	      } catch (Exception e) {
	          return null;
	      }
	  }


	  /**
		 * @throws SQLException
		 *
		 * @param Str
		 * @return
		 * @throws
		 */
	  public String changeTreeSelect(String treeSelectStr) throws SQLException {
		  String[] strings = treeSelectStr.split(",");
		  if(strings == null){
			  return null;
		  }else{
			  EasySQL sql = new EasySQL("select FULL_PATH from "+this.getTableName("c_cf_common_tree"));
			  sql.append(strings[strings.length-1]," where  ID = ?");
			  return this.getQuery().queryForString(sql.getSQL(), sql.getParams());
		  }
    }


	/**
	 * @return
	 * @throws SQLException
	 */
	public EasyResult actionForCheck() throws SQLException {
		String procIdStr =  this.getJSONObject().getString("data");// 判断页面是否有勾选ID
		logger.info("procIdStr:"+procIdStr);
		String[] procIds = procIdStr.split(",");
		if (procIds != null) {
			for (String procId : procIds) {
				String procInstId =procId;
				String useTableName = "C_BO_BASE_ORDER";
				if("2".equals(this.getJSONObject().getString("type"))){
					useTableName = "c_bo_base_order_his";
				}
				EasySQL sql = new EasySQL(" select  ");
				sql.append(" ID,PROC_KEY,ORDER_NO from  "+this.getTableName(useTableName));
				sql.append(" where 1=1 ");
				sql.append(procInstId," and PROC_INST_ID = ?");

				logger.info("sql:"+sql.getSQL());
				logger.info(sql.getParamsList().toString());

				EasyRow row = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams());
				if(Objects.isNull(row) && "0".equals(this.getJSONObject().getString("type"))){
					sql = new EasySQL(" select  ");
					sql.append(" ID,PROC_KEY,ORDER_NO from  "+this.getTableName("c_bo_base_order_his"));
					sql.append(" where 1=1 ");
					sql.append(procInstId," and PROC_INST_ID = ?");
					row = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams());
				}
				if (Objects.isNull(row)) {
					continue;
				}
				String procKey = row.getColumnValue("PROC_KEY");
				String srcPath = getFirstTemplatePath(procKey);
				if (!existFile(srcPath)) {
					return EasyResult.ok().put("msg", getI18nValue("经检测")+procKey+getI18nValue("流程尚未配置导出文件"));
				}
			}
		}
		return EasyResult.ok().put("msg", getI18nValue("经检测无异常"));
	}
}
