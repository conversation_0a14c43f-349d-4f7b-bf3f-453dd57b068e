package com.yunqu.cc.eorder.utils.excel;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.*;

public class CellStyleUtil {

	/**
	 * excel首列序号列样式
	 *
	 * @param workbook
	 * @return
	 */
	public static CellStyle firstCellStyle(Workbook workbook) {
		CellStyle cellStyle = workbook.createCellStyle();
		// 居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		cellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
		// 设置边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		// 文字
		Font font = workbook.createFont();
		font.setBold(Boolean.TRUE);
		cellStyle.setFont(font);
		return cellStyle;
	}

	/**
	 * 用于设置excel导出时的样式 easyexcel 导出样式
	 *
	 * @return
	 */
	public static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
		// 头的策略
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		// 背景设置为红色
		headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

		// 字体
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setFontName("微软雅黑");// 设置字体名字
		headWriteFont.setFontHeightInPoints((short) 10);// 设置字体大小
		headWriteFont.setBold(true);// 字体加粗
		headWriteCellStyle.setWriteFont(headWriteFont); // 在样式用应用设置的字体;
		headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
		headWriteCellStyle.setBorderTop(BorderStyle.NONE);
		headWriteCellStyle.setBorderLeft(BorderStyle.NONE);
		headWriteCellStyle.setBorderRight(BorderStyle.NONE);
		headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headWriteCellStyle.setShrinkToFit(true);
		// 内容的策略
		WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
		// 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
		// 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
		/*
		 * contentWriteCellStyle.setFillPatternType(FillPatternType.
		 * SOLID_FOREGROUND); // 背景
		 * contentWriteCellStyle.setFillForegroundColor(IndexedColors.
		 * GREY_40_PERCENT.getIndex());
		 * contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.
		 * CENTER);
		 * contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		 * //边框 contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
		 * contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
		 */
		// 文字
		WriteFont contentWriteFont = new WriteFont();
		// 字体大小
		contentWriteFont.setFontHeightInPoints((short) 10);
		contentWriteFont.setFontName("微软雅黑");// 设置字体名字
		contentWriteCellStyle.setWriteFont(contentWriteFont);
		// 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现

		return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
	}

}
