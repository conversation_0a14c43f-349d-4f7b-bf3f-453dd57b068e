package com.yunqu.cc.eorder.utils;

import org.easitline.common.utils.string.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * 定是作业任务类
 * <AUTHOR>
 */
public class SchedulerTaskUtils {

	private static Scheduler scheduler = null;

	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @throws ClassNotFoundException
	 * @throws SchedulerException
	 */
	public static void addTask(final String clazz, final String cron, String groupName) throws ClassNotFoundException, SchedulerException{
		addTask(clazz, cron, clazz, groupName);
	}
	
	

	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @param triggerName
	 * @throws SchedulerException
	 * @throws ClassNotFoundException
	 */
	public static void addTask(final String clazz, final String cron, final String triggerName, String groupName) throws SchedulerException,ClassNotFoundException{
		try {
			addTask(Class.forName(clazz),cron,triggerName,groupName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new ClassNotFoundException(clazz +" not found.",e);
		}

	}
	
	public static void addTask(final String clazz, final int jobTime, final int jobTimes,final String groupName) throws SchedulerException,ClassNotFoundException {
		addTask(clazz, jobTime, jobTimes, clazz,groupName);
	}
	
	public static void addTask(final String clazz, final int jobTime, final int jobTimes, final String triggerName,final String groupName) throws SchedulerException,ClassNotFoundException {
		try {
			addTask(Class.forName(clazz), jobTime, jobTimes, clazz, groupName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new ClassNotFoundException(clazz +" not found.",e);
		}
	}
	
	public static void addTask(final Class clazz, final int jobTime, final int jobTimes, final String triggerName,final String groupName) throws SchedulerException {
		if(clazz==null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if(scheduler==null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！",e);
			}
		}
		Trigger trigger = TriggerBuilder
				.newTrigger()
				.withIdentity(triggerName, groupName)
				.withSchedule(
						SimpleScheduleBuilder.repeatSecondlyForever(1)
								.withIntervalInSeconds(jobTime)
								.withRepeatCount(jobTimes)).startNow()
				.build();
		scheduler.scheduleJob(getJob(clazz,triggerName,groupName),trigger);
	}

	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @param triggerName
	 * @throws SchedulerException
	 */
	@SuppressWarnings("rawtypes")
	public static void addTask(final Class clazz, final String cron, final String triggerName, final String groupName) throws SchedulerException{
		if(clazz==null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if(StringUtils.isBlank(cron) || !CronExpression.isValidExpression(cron)) {
			throw new RuntimeException("cron表达式不符合要求！");
		}
		if(StringUtils.isBlank(triggerName)) {
			throw  new NullPointerException("触发器名称triggerName不能为空！");
		}
		if(scheduler==null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！",e);
			}
		}
		Trigger trigger = newTrigger().withIdentity(triggerName,groupName).startNow().withSchedule(cronSchedule(cron).withMisfireHandlingInstructionFireAndProceed()).build();
		scheduler.scheduleJob(getJob(clazz,triggerName,groupName),trigger);
	}

	/**
	 * 实例调度任务
	 * @param clazz
	 * @param triggerName
	 * @param group
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static JobDetail getJob(Class clazz, String triggerName, String group) {
		JobDetail job = newJob(clazz).withIdentity(triggerName,group)
				.usingJobData("name","quartz").build();
		return job;
	}
	
	/**
	 * 开始作业
	 * @throws SchedulerException
	 */
	public static void start() throws SchedulerException{
		try {
			scheduler.start();
		} catch (SchedulerException e) {
			e.printStackTrace();
			throw new SchedulerException("启动调度器scheduler异常！",e);
		}
	}

	/**
	 * 注销调度器
	 * @throws SchedulerException
	 */
	public static void shutDown() throws SchedulerException {
		try {
			scheduler.shutdown(true);
		} catch (SchedulerException e) {
			e.printStackTrace();
			throw new SchedulerException("关闭调度器scheduler异常！",e);
		}
	}
	
	public static void deleteSchedul(String name,String groupName) throws SchedulerException {
		TriggerKey triggerKey = TriggerKey.triggerKey(name,groupName);
        // 停止触发器
        scheduler.pauseTrigger(triggerKey);
        // 移除触发器
        scheduler.unscheduleJob(triggerKey);
        // 删除任务
        scheduler.deleteJob(JobKey.jobKey(name,groupName));
	}
}
