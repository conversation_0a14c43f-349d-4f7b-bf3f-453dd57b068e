package com.yunqu.cc.eorder.utils.solr;

import org.easitline.common.utils.kit.RandomKit;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;


public class SolrQueryBuilder {

	private SolrQueryParams queryParams = new SolrQueryParams(); 
	
	private JSONArray sortFields = new JSONArray();
	
//	private JSONArray filterParms = null;
	
	private String queryFields;
	
	private int pageNo = 1;
	
	private int pageSize = 10;
	
	private String command;
	
	private String core;
	
	private String type;
	
	private String busiType;
	
	private JSONArray hlFields;
	
	private JSONArray filterFields;

	
	public JSONArray getFilterFields() {
		return filterFields;
	}

	public void setFilterFields(JSONArray filterFields) {
		this.filterFields = filterFields;
	}

	public JSONArray getHlFields() {
		return hlFields;
	}

	public void setHlFields(JSONArray hlFields) {
		this.hlFields = hlFields;
	}

	public static SolrQueryBuilder create() {
		return new SolrQueryBuilder();
	}
	
	/**
	 * 创建查询请求参数
	 * 
	 */
	public JSONObject creatQueryParams() {
		JSONObject json = new JSONObject();
		String serialId = RandomKit.uuid();
		json.put("serialId", serialId);
		json.put("timestamp", DateUtil.getTimestamp(DateUtil.getCurrentDateStr()));
		json.put("type", type);
		json.put("busiType", busiType);
		json.put("core", core);
		json.put("command", command);
		
		JSONObject data = new JSONObject();
		data.put("queryParms", queryParams.build());
		data.put("queryParams", queryParams.build());
//		data.put("filterParms", filterParms);
		data.put("queryFields",queryFields);
		data.put("indexName","coreLog");
		if (pageNo > 0) {
			data.put("pageNo", pageNo);
			data.put("pageSize", pageSize);
		}
		
		if(sortFields != null && sortFields.size() > 0) {
			data.put("sortFields", sortFields);
		}
		data.put("hlFields", hlFields);
		data.put("filterParms", filterFields);

		json.put("data", data);
		return json;
	}

	public SolrQueryParams getQueryParams() {
		return queryParams;
	}

	public void setQueryParams(SolrQueryParams queryParams) {
		this.queryParams = queryParams;
	}


	public String getQueryFields() {
		return queryFields;
	}


	public void setQueryFields(String queryFields) {
		this.queryFields = queryFields;
	}

	public int getPageSize() {
		return pageSize;
	}

	public SolrQueryBuilder  setPageSize(int pageSize) {
		this.pageSize = pageSize;
		return this;
	}

	public int getPageNo() {
		return pageNo;
	}

	public SolrQueryBuilder setPageNo(int pageNo) {
		this.pageNo = pageNo;
		return this;
	}

	public String getCommand() {
		return command;
	}

	public SolrQueryBuilder setCommand(String command) {
		this.command = command;
		return this;
	}

	public String getCore() {
		return core;
	}

	public SolrQueryBuilder setCore(String core) {
		this.core = core;
		return this;
	}

	public String getType() {
		return type;
	}

	public SolrQueryBuilder setType(String type) {
		this.type = type;
		return this;
	}

	public JSONArray getSortFields() {
		return sortFields;
	}

	public void setSortFields(JSONArray sortFields) {
		this.sortFields = sortFields;
	}
	
	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public void setSortField(String name,String type){
		JSONObject sortField = new JSONObject();
		sortField.put("name", name);
		sortField.put("type", type);
		this.sortFields.add(sortField);
	}
	
}
