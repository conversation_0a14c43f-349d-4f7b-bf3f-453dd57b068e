package com.yunqu.cc.eorder.dao.search;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.eorder.base.AppDaoContext;
import com.yunqu.cc.eorder.dao.search.sql.CallRecordHisSql;
import com.yunqu.cc.eorder.dao.search.sql.CallRecordSql;
import com.yunqu.cc.eorder.log.CommonLogger;
import com.yunqu.cc.eorder.utils.PhoneCryptor;

@WebObject(name = "callOrder")
public class CallOrderDao extends AppDaoContext {
	
	private static Logger logger = CommonLogger.logger;
	
	@WebControl(name = "getCallOrderList", type = Types.LIST)
	@InfAuthCheck(resId={"cc-erder-manage-upgradeOrderList","cc-erder-search-allOrderList","cc-erder-search-deptOrderList","cc-erder-search-myOrderList"})
	public JSONObject getCallOrderList() {
		/*EasySQL sql = this.getCallOrderListSql(param.getString("dept"), param.getString("orderNo"), param.getString("status"),param.getString("sendStartTime") ,param.getString("sendEndTime") ,param.getString("endStartTime") ,param.getString("endEndTime"),param.getString("FLOW_KEY"));
		return queryForPageList(sql.getSQL(), sql.getParams());*/
		
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		//判断是否选择了历史话单查询条件，是的话从CC_CALL_RECORD_HIS查询数据
		param.put("dbName",getTableName("CC_CALL_RECORD"));
		param.put("summaryTableName",getTableName("C_CALL_SUMMARY_RECORD"));
		param.put("summaryDetailTableName", getTableName("C_CALL_SUMMARY_DETAIL"));
		param.put("deptTable", getTableName("CC_SKILL_GROUP"));
		param.put("areaTable", "CC_AREA");
		param.put("orderTable",getTableName("C_BO_BASE_ORDER"));
		
		param.put("entId", getEntId());
		param.put("busiOrderId",getBusiOrderId());
		UserModel user = UserUtil.getUser(this.request);
		param.put("userAcc", user.getUserAcc());
		String agentId = param.getString("agentId");
		EasySQL sql = CallRecordSql.callRecordSql(param,cryptor,agentId, user);
		JSONObject list = this.queryForPageList(sql.getSQL(), sql.getParams());
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(CallRecordSql.class) + "工单通话记录查询条数 " + list.toJSONString());
		}
		try {
			list.put("data", cryptor.decrypt(list.getJSONArray("data"), new String[]{"CALLER","CALLED","CUST_PHONE"}, false));
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return list;
		
	}

	@WebControl(name = "getCallOrderHisList", type = Types.LIST)
	@InfAuthCheck(resId={"cc-erder-manage-upgradeOrderList","cc-erder-search-allOrderList","cc-erder-search-deptOrderList","cc-erder-search-myOrderList"})
	public JSONObject getCallOrderHisList() {
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		//判断是否选择了历史话单查询条件，是的话从CC_CALL_RECORD_HIS查询数据
		param.put("dbName", StringUtils.isNotBlank(param.getString("billRange")) && param.getString("billRange").equals("2")?this.getTableName("CC_CALL_RECORD_HIS"):this.getTableName("CC_CALL_RECORD"));
		param.put("summaryTableName", this.getTableName("C_CALL_SUMMARY_RECORD"));
		param.put("summaryDetailTableName", this.getTableName("C_CALL_SUMMARY_DETAIL"));
		param.put("deptTable", this.getTableName("CC_SKILL_GROUP"));
		param.put("areaTable", ("CC_AREA"));
		param.put("orderTable", this.getTableName("C_BO_BASE_ORDER_HIS"));
		
		param.put("entId", this.getEntId());
		param.put("busiOrderId", this.getBusiOrderId());
		UserModel user = UserUtil.getUser(this.request);
		param.put("userAcc", user.getUserAcc());
		String agentId = param.getString("agentId");
		EasySQL sql = CallRecordHisSql.callRecordHisSql(param,cryptor,agentId, user);
		JSONObject list = this.queryForPageList(sql.getSQL(), sql.getParams());
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(CallRecordSql.class) + "工单通话历史记录查询条数 " + list.toJSONString());
		}
		try {
			list.put("data", cryptor.decrypt(list.getJSONArray("data"), new String[]{"CALLER","CALLED","CUST_PHONE"}, false));
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return list;
	}
	

}
