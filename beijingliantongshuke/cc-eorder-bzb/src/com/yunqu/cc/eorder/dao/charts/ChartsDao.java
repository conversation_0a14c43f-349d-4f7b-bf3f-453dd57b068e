package com.yunqu.cc.eorder.dao.charts;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.Dict;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.eorder.base.AppDaoContext;
import com.yunqu.cc.eorder.log.CommonLogger;

/**
 * charts图表工单统计查询
 * @Description:提供工单统计查询相关方法
 * @Company:云趣科技
 * @Author: panyibao
 * @Date: 2019/3/30 16:42
 */
@WebObject(name = "charts")
public class ChartsDao extends AppDaoContext{

	private Logger logger = CommonLogger.logger;
	
	/**
	 * 统计工单数量
	 * 工单总量 totel
	 * 已完成量(包含已关闭05，已删除07) finished
	 * 未完成量(已关闭，已删除之外) notFinished
	 * 完成率 = 已完成量/工单总量   finishRate
	 * @return
	 */
	@WebControl(name="statOrderNum",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderNum() {
		//工单总量
		EasySQL sql = new EasySQL();
		sql.append("SELECT");
		sql.append("SUM(T1.TOTAL_NUM) TOTEL");//工单数量
		sql.append(",SUM(T1.DONE_NUM) FINISH");
		sql.append(",SUM(T1.CLOSE_NUM) CLOSE_NUM");
		sql.append(",SUM(T1.DEL_NUM) DEL_NUM");
		sql.append("FROM " + getTableName("C_BO_ORDER_STAT") + " T1");
		sql.append("WHERE 1=1 ");
		addCommonFilter(sql, "T1.");
		JSONObject json = new JSONObject();
		try {
			json = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			int totel = json.getIntValue("TOTEL");
			int finish = json.getIntValue("FINISH");
			int closeNum = json.getIntValue("CLOSE_NUM");
			int delNum = json.getIntValue("DEL_NUM");
			json.put("NOT_FINISHED", (totel - finish - closeNum - delNum));
			if(totel != 0) {
				double rate = (1.00 * finish) / totel * 100;
				json.put("FINISH_RATE", String.format("%.2f", rate) + "%");
			}else {
				json.put("FINISH_RATE", "0.00%");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 统计工单数量错误" + e.getMessage(), e);
		}
		return json;
	}
	
	/**
	 * 
	 * @return
	 */
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	@WebControl(name="statProvinceOrder",type=Types.DICT)
	public JSONObject statProvinceOrder() {
		EasySQL sql = new EasySQL("select T1.ORDER_CREATE_DATE");
		sql.append(" ,SUM(T1.DONE_NUM-T1.DONE_OT_NUM) AS DONE_NUM");//完成(未超时)
		sql.append(" ,SUM(T1.DONE_OT_NUM) AS DONE_OT_NUM");//完成(超时)
		sql.append(" ,SUM(T1.DOING_NUM-T1.DOING_OT_NUM) AS DOING_NUM");//处理中(未超时)
		sql.append(" ,SUM(T1.DOING_OT_NUM) AS DOING_OT_NUM");//处理中(超时)
		sql.append(" ,SUM(T1.CLOSE_NUM) AS CLOSE_NUM");//关闭
		sql.append(" ,SUM(T1.DEL_NUM) AS DEL_NUM");//删除
		
		sql.append(" ,SUM(T1.CUIBAN_NUM) AS CUIBAN_NUM");//催办量
		sql.append(" ,SUM(T1.OVERSEE_NUM) AS OVERSEE_NUM");//督办量
		sql.append(" ,SUM(T1.NOTE_NUM) AS NOTE_NUM");//备注量
		sql.append(" ,SUM(T1.BAKUP_NUM) AS BAKUP_NUM");//批注量
		
		sql.append(" from " + getTableName("C_BO_ORDER_STAT") + " T1 Where 1=1 ");
		addCommonFilter(sql, "T1.");
		sql.append("GROUP BY T1.ORDER_CREATE_DATE");
		List<String> days = getDays();
		JSONObject result = new JSONObject();
		result.put("days", days);
		try {
			EasyQuery query = getQuery();
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String ,JSONObject> dayJsonMap = new HashMap<>();
			if(CommonUtil.listIsNotNull(list)) {
				list.forEach((json)->{
					if(json != null) {
						dayJsonMap.put(json.getString("ORDER_CREATE_DATE"), json);
					}
				});
			}
			List<Integer> doneNum = getAttrList(days, dayJsonMap, "DONE_NUM");
			List<Integer> doneOtNum = getAttrList(days, dayJsonMap, "DONE_OT_NUM");
			List<Integer> doingNum = getAttrList(days, dayJsonMap, "DOING_NUM");
			List<Integer> doingOtNum = getAttrList(days, dayJsonMap, "DOING_OT_NUM");
			List<Integer> closeNum = getAttrList(days, dayJsonMap, "CLOSE_NUM");
			List<Integer> delNum = getAttrList(days, dayJsonMap, "DEL_NUM");
			
			List<Integer> cuibanNum = getAttrList(days, dayJsonMap, "CUIBAN_NUM");
			List<Integer> overseeNum = getAttrList(days, dayJsonMap, "OVERSEE_NUM");
			List<Integer> noteNum = getAttrList(days, dayJsonMap, "NOTE_NUM");
			List<Integer> bakupNum = getAttrList(days, dayJsonMap, "BAKUP_NUM");
			
			
			result.put("doneNum", doneNum);
			result.put("doneOtNum", doneOtNum);
			result.put("doingNum", doingNum);
			result.put("doingOtNum", doingOtNum);
			result.put("closeNum", closeNum);
			result.put("delNum", delNum);
			
			result.put("cuibanNum", cuibanNum);
			result.put("overseeNum", overseeNum);
			result.put("noteNum", noteNum);
			result.put("bakupNum", bakupNum);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage(), e);
		}
		
		return result;
	}
	
	private List<Integer> getAttrList(List<String> keys, Map<String ,JSONObject> jsonMap, String attr){
		List<Integer> list = new ArrayList<>();
		if(CommonUtil.listIsNotNull(keys)) {
			keys.forEach((key)->{
				JSONObject json = jsonMap.get(key);
				int num = 0;
				if(json != null) {
					num = NumberUtils.toInt(json.getString(attr), 0);
				}
				list.add(num);
			});
		}
		return list;
	}
	
	/**
	 * 派发趋势，关闭趋势
	 * @return
	 */
	@WebControl(name="statOrderTrend",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderTrend() {
		JSONObject result = new JSONObject();
		EasyQuery query = this.getQuery();
		String timeRange = param.getString("timeRange");
		String startDate = "";
		String endDate = "";
		if(timeRange != null) {
			String[] dateArr = timeRange.split(" - ");
			if(dateArr.length>1) {
				startDate = dateArr[0];
				endDate = dateArr[1];
			}
		}
		
		//查找新增派发, 新增关闭
		EasySQL sql = new EasySQL();
		sql.append("select T1.DATE_ID, SUM(T1.ADD_NUM) ADD_NUM, SUM(T1.CLOSE_NUM) CLOSE_NUM FROM "
		+ getTableName("c_bo_order_day_stat") + " T1");
		sql.append("WHERE 1=1");
		sql.append(Integer.parseInt(startDate.replaceAll("-", "")), "AND T1.DATE_ID>=?");
		sql.append(Integer.parseInt(endDate.replaceAll("-", "")), "AND T1.DATE_ID<=?");
		sql.append(getEntId(), "AND T1.ENT_ID=?");
		sql.append(getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		sql.append("GROUP BY T1.DATE_ID");
		try {
			List<String> days = getDays();
			List<String> creates = new ArrayList<>();
			List<String> closes = new ArrayList<>();
			List<JSONObject> createList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String, String> createMap = new HashMap<>();
			Map<String, String> closeMap = new HashMap<>();
			if(CommonUtil.listIsNotNull(createList)) {
				createList.forEach((json)->{
					if(json != null) {
						createMap.put(json.getString("DATE_ID"), json.getString("ADD_NUM"));
						closeMap.put(json.getString("DATE_ID"), json.getString("CLOSE_NUM"));
					}
				});
			}
			if(CommonUtil.listIsNotNull(days)) {
				days.forEach((day)->{
					day = day.replaceAll("-", "");
					String count1 = createMap.get(day);
					if(CommonUtil.isNotBlank(count1)) {
						creates.add(count1);
					}else {
						creates.add("0");
					}
					String count2 = closeMap.get(day);
					if(CommonUtil.isNotBlank(count2)) {
						closes.add(count2);
					}else {
						closes.add("0");
					}
				});
			}
			result.put("days", days);
			result.put("creates", creates);
			result.put("closes", closes);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"获取天数统计错误" + e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 统计处理时限
	 * 不同时限的总量
	 * 超时工单总量
	 * 超时未完成总量
	 * @return
	 */
	@WebControl(name="statTimeLimit",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statTimeLimit() {
		
		JSONObject result = new JSONObject();
		EasyQuery query = this.getQuery();
		String timeRange = param.getString("timeRange");
		String startDate = "";
		String endDate = "";
		if(timeRange != null) {
			String[] dateArr = timeRange.split(" - ");
			if(dateArr.length>1) {
				startDate = dateArr[0];
				endDate = dateArr[1];
			}
		}
		//查找TOTAL_NUM总量, DONE_OT_NUM超时完成，DOING_OT_NUM超时未完成完成
		EasySQL sql = new EasySQL();
		sql.append("select T1.ORDER_CREATE_DATE, SUM(T1.TOTAL_NUM) TOTAL_NUM, SUM(T1.DONE_OT_NUM) DONE_OT_NUM, SUM(T1.DOING_OT_NUM) DOING_OT_NUM FROM "
		+ getTableName("C_BO_ORDER_STAT") + " T1");
		sql.append("WHERE 1=1");
		sql.append(startDate, "AND T1.ORDER_CREATE_DATE>=?");
		sql.append(endDate, "AND T1.ORDER_CREATE_DATE<=?");
		sql.append(getEntId(), "AND T1.ENT_ID=?");
		sql.append(getBusiOrderId(), "AND T1.BUSI_ORDER_ID=?");
		sql.append("GROUP BY T1.ORDER_CREATE_DATE");
		
		try {
			List<String> days = getDays();
			List<String> totelList = new ArrayList<>();
			List<String> overTimeList = new ArrayList<>();
			List<String> notfinishList = new ArrayList<>();
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			Map<String,String> totelNumMap = new HashMap<>();
			Map<String,String> overTimeMap = new HashMap<>();
			Map<String,String> notFinishMap = new HashMap<>();
			if(CommonUtil.listIsNotNull(list)) {
				list.forEach((json)->{
					//超时总量
					int overNum = NumberUtils.toInt(json.getString("DONE_OT_NUM"), 0)
							+ NumberUtils.toInt(json.getString("DOING_OT_NUM"));
					totelNumMap.put(json.getString("ORDER_CREATE_DATE"), json.getString("TOTAL_NUM"));
					overTimeMap.put(json.getString("ORDER_CREATE_DATE"), "" + overNum);
					notFinishMap.put(json.getString("ORDER_CREATE_DATE"), json.getString("DOING_OT_NUM"));
				});
			}
			if(CommonUtil.listIsNotNull(days)) {
				days.forEach((key)->{
					String num1 = totelNumMap.get(key);
					totelList.add(num1==null ? "0" : num1);
							
					String num2 = overTimeMap.get(key);
					overTimeList.add(num2 == null ? "0" : num2);
							
					String num3 = notFinishMap.get(key);
					notfinishList.add(num3 == null ? "0" : num3);
				});
			}
		
			result.put("timeLimits", days);
			result.put("totelList", totelList);
			result.put("overTimeList", overTimeList);
			result.put("notfinishList", notfinishList);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"统计处理时限错误" + e.getMessage(), e);
		}
		return result;
	}
	
	/**
	 * 按状态统计
	 * @return
	 */
	@WebControl(name="statOrderStatus",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderStatus() {
		EasySQL sql = this.getEasySQL("select ");
		sql.append(" SUM(T1.DOING_NUM) AS 处理中");
		sql.append(" ,SUM(T1.SAVE_NUM) AS 暂存");
		sql.append(" ,SUM(T1.DONE_NUM) AS 处理完成");
		sql.append(" ,SUM(T1.CLOSE_NUM) AS 强制关闭");
		sql.append(" ,SUM(T1.DEL_NUM) AS 强制删除");
		sql.append(" from " +getTableName("C_BO_ORDER_STAT")+" T1 where 1=1 ");
		addCommonFilter(sql, "T1.");
		JSONObject json = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		return json;
	}
	
	/**
	 * 按类型统计
	 * @return
	 */
	@WebControl(name="statOrderType",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderType() {
		EasySQL sql = this.getEasySQL("select ");
		sql.append(" T1.PROC_NAME, SUM(T1.TOTAL_NUM) AS TOTAL");
		sql.append(" from " +getTableName("C_BO_ORDER_STAT")+" T1 where 1=1 ");
		addCommonFilter(sql, "T1.");
		sql.append("GROUP BY PROC_NAME");
		JSONObject result = new JSONObject();
		try {
			List<JSONObject> list = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			JSONObject data = new JSONObject();
			if(CommonUtil.listIsNotNull(list)) {
				list.forEach((json)->{
					data.put(json.getString("PROC_NAME"), json.getString("TOTAL"));
				});
			}
			result.put("data", data);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)
					+ "按类型统计工单出错", e);
		}
		return result;
	}
	
	/**
	 * 按优先级统计
	 * @return
	 */
	@WebControl(name="statOrderPriority",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderPriority() {
		EasySQL sql = this.getEasySQL("select ");
		Map<String, Object> map = DictCache.getMapAllDictListByGroupCode(getEntId(), "COMMON_ORDER_LEVEL");
		Iterator<String> iter = map.keySet().iterator();
		while(iter.hasNext()){
			String key=iter.next();
			String value = (String) map.get(key);
			sql.append(" SUM(case when T1.ORDER_LEVEL='"+key+"' then T1.TOTAL_NUM else 0 end ) AS " + value);
			if(iter.hasNext()) {
				sql.append(",");
			}
		}
		sql.append(" from " +getTableName("C_BO_ORDER_STAT")+" T1 where 1=1 ");
		addCommonFilter(sql, "T1.");
		JSONObject json = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		return json;
	}
	
	/**
	 * 按来源类型统计
	 * @return
	 */
	@WebControl(name="statOrderChannel",type=Types.DICT)
	@InfAuthCheck(resId="cc-erder-search-statCharts")
	public JSONObject statOrderChannel() {
		List<Dict> dicts = DictCache.getEnableDictListByGroupCode(getEntId(), "BO_ORDER_SOURCE");
		EasySQL sql = this.getEasySQL("select ");
		sql.append("SUM(case when T1.SOURCE='manual' then T1.TOTAL_NUM else 0 end ) AS 坐席添加");
		for (Dict dict:dicts) {
			String code = dict.getCode();
			String name = dict.getName();
			sql.append(", SUM(case when T1.SOURCE='"+code+"' then T1.TOTAL_NUM else 0 end ) AS "+name+"");
		}
		sql.append(" from " +getTableName("C_BO_ORDER_STAT")+" T1 where 1=1 ");
		addCommonFilter(sql, "T1.");
		JSONObject json = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		return json;
	}
	
	/**
	 * 获取天数集合
	 * @return
	 */
	private List<String> getDays() {
		String timeRange = param.getString("timeRange");
		String startDate = "";
		String endDate = "";
		if(timeRange != null) {
			String[] dateArr = timeRange.split(" - ");
			if(dateArr.length>1) {
				startDate = dateArr[0];
				endDate = dateArr[1];
			}
		}
		List<String> days = new ArrayList<String>();
		try {
			Date[] dates = DateUtil.getDateArrays(startDate, endDate, Calendar.DATE);
			if(dates != null) {
				SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.TIME_FORMAT_YMD);  
				for (Date date : dates) {
					days.add(sdf.format(date));
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"获取天数错误" + e.getMessage(), e);
		}
		return days;
	}

	/**
	 * 添加公共查找条件
	 * @param sql
	 * @param prefix
	 * @return
	 */
	private int addCommonFilter(EasySQL sql, String prefix) {
		String timeRange = param.getString("timeRange");
		String startDate = "";
		String endDate = "";
		if(timeRange != null) {
			String[] dateArr = timeRange.split(" - ");
			if(dateArr.length>1) {
				startDate = dateArr[0];
				endDate = dateArr[1];
			}
		}
		if(StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
			return 1;
		}
		sql.append(startDate, "AND " + prefix + "ORDER_CREATE_DATE>=?");
		sql.append(endDate, "AND " + prefix + "ORDER_CREATE_DATE<=?");
		sql.append(getEntId(), "AND " + prefix + "ENT_ID=?");
		sql.append(getBusiOrderId(), "AND " + prefix + "BUSI_ORDER_ID=?");
		return 0;
	}
}
