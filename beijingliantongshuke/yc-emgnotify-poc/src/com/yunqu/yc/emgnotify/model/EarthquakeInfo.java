package com.yunqu.yc.emgnotify.model;

import java.io.Serializable;

/**
*将接收到的灾害信息记录到此表
*/
public class EarthquakeInfo implements Serializable {
    private static final long serialVersionUID = -67814051432205387L;

    /**
     *事件id
     */
    private String infoId;

    /**
     *应急响应级别  1-一般 2-较大 3-重大 4-特别重大
     */
    private String respLevel;

    /**
     *灾害类型编码，取字典编码
     */
    private String disasterTypeCode;

    /**
     *灾害名称
     */
    private String disasterName;

    /**
     *参考位置
     */
    private String location;

    private String locationName;

    /**
     *灾害经度
     */
    private String lon;

    /**
     *灾害纬度
     */
    private String lat;

    /**
     *灾害发生时间
     */
    private String oTime;

    /**
     *灾害事件说明
     */
    private String infoMsg;

    /**
     *启用状态
            01-启用
            02-禁用
     */
    private String enableStatus;

    /**
     *审批类型，1-无需审批 2-一级审批 3-二级审批
     */
    private String approvalType;

    /**
     *处理状态,1-待初审 2-初审不通过 3-待复审 4-复审不通过 5-待派任务 6-已派任务
     */
    private String handleStatus;

    /**
     *应急叫应任务id，派发了叫应任务后存在
     */
    private String taskId;

    /**
     *企业id
     */
    private String entId;

    /**
     *订购id
     */
    private String busiOrderId;

    /**
     *关联灾害ID，如需关联其他灾害信息，选择对应的灾害事件id
     */
    private String refDisasterId;

    private String refDisasterName;

    /**
     *被其他灾害关联次数(冗余)
     */
    private String refCount;

    /**
     *指挥部ID
     */
    private String comandId;

    /**
     *是否测试事件
     */
    private String isTest;

    /**
     *创建人账号
     */
    private String createAcc;

    /**
     *创建时间
     */
    private String createTime;

    /**
     *
     */
    private String data1;

    /**
     *
     */
    private String data2;

    /**
     *
     */
    private String data3;

    /**
     *
     */
    private String data4;

    /**
     *
     */
    private String data5;

    /**
     *
     */
    private String data6;

    /**
     *
     */
    private String data7;

    /**
     *
     */
    private String data8;

    /**
     *
     */
    private String data9;

    /**
     *
     */
    private String data10;

    /**
     *字段定义信息json
     */
    private String fieldJson;

    private CommandInfo commandInfo;

    /**
     *事件来源，1 对接系统自动创建； 2 对接系统点选创建； 3 人工创建； 4 接口创建
     */
    private String infoSource;

    private String checkResult;

    public void setInfoId(String infoId) {
            this.infoId = infoId;
        }

    public String getInfoId() {
            return this.infoId;
        }

    public void setRespLevel(String respLevel) {
            this.respLevel = respLevel;
        }

    public String getRespLevel() {
            return this.respLevel;
        }

    public void setDisasterTypeCode(String disasterTypeCode) {
            this.disasterTypeCode = disasterTypeCode;
        }

    public String getDisasterTypeCode() {
            return this.disasterTypeCode;
        }

    public void setDisasterName(String disasterName) {
            this.disasterName = disasterName;
        }

    public String getDisasterName() {
            return this.disasterName;
        }

    public void setLocation(String location) {
            this.location = location;
        }

    public String getLocation() {
            return this.location;
        }

    public void setLon(String lon) {
            this.lon = lon;
        }

    public String getLon() {
            return this.lon;
        }

    public void setLat(String lat) {
            this.lat = lat;
        }

    public String getLat() {
            return this.lat;
        }

    public void setOTime(String oTime) {
            this.oTime = oTime;
        }

    public String getOTime() {
            return this.oTime;
        }

    public void setInfoMsg(String infoMsg) {
            this.infoMsg = infoMsg;
        }

    public String getInfoMsg() {
            return this.infoMsg;
        }

    public void setEnableStatus(String enableStatus) {
            this.enableStatus = enableStatus;
        }

    public String getEnableStatus() {
            return this.enableStatus;
        }

    public void setApprovalType(String approvalType) {
            this.approvalType = approvalType;
        }

    public String getApprovalType() {
            return this.approvalType;
        }

    public void setHandleStatus(String handleStatus) {
            this.handleStatus = handleStatus;
        }

    public String getHandleStatus() {
            return this.handleStatus;
        }

    public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

    public String getTaskId() {
            return this.taskId;
        }

    public void setEntId(String entId) {
            this.entId = entId;
        }

    public String getEntId() {
            return this.entId;
        }

    public void setBusiOrderId(String busiOrderId) {
            this.busiOrderId = busiOrderId;
        }

    public String getBusiOrderId() {
            return this.busiOrderId;
        }

    public void setRefDisasterId(String refDisasterId) {
            this.refDisasterId = refDisasterId;
        }

    public String getRefDisasterId() {
            return this.refDisasterId;
        }

    public void setRefCount(String refCount) {
            this.refCount = refCount;
        }

    public String getRefCount() {
            return this.refCount;
        }

    public void setComandId(String comandId) {
            this.comandId = comandId;
        }

    public String getComandId() {
            return this.comandId;
        }

    public void setIsTest(String isTest) {
            this.isTest = isTest;
        }

    public String getIsTest() {
            return this.isTest;
        }

    public void setCreateAcc(String createAcc) {
            this.createAcc = createAcc;
        }

    public String getCreateAcc() {
            return this.createAcc;
        }

    public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

    public String getCreateTime() {
            return this.createTime;
        }

    public void setData1(String data1) {
            this.data1 = data1;
        }

    public String getData1() {
            return this.data1;
        }

    public void setData2(String data2) {
            this.data2 = data2;
        }

    public String getData2() {
            return this.data2;
        }

    public void setData3(String data3) {
            this.data3 = data3;
        }

    public String getData3() {
            return this.data3;
        }

    public void setData4(String data4) {
            this.data4 = data4;
        }

    public String getData4() {
            return this.data4;
        }

    public void setData5(String data5) {
            this.data5 = data5;
        }

    public String getData5() {
            return this.data5;
        }

    public void setData6(String data6) {
            this.data6 = data6;
        }

    public String getData6() {
            return this.data6;
        }

    public void setData7(String data7) {
            this.data7 = data7;
        }

    public String getData7() {
            return this.data7;
        }

    public void setData8(String data8) {
            this.data8 = data8;
        }

    public String getData8() {
            return this.data8;
        }

    public void setData9(String data9) {
            this.data9 = data9;
        }

    public String getData9() {
            return this.data9;
        }

    public void setData10(String data10) {
            this.data10 = data10;
        }

    public String getData10() {
            return this.data10;
        }

    public void setFieldJson(String fieldJson) {
            this.fieldJson = fieldJson;
        }

    public String getFieldJson() {
            return this.fieldJson;
        }

    public void setInfoSource(String infoSource) {
            this.infoSource = infoSource;
        }

    public String getInfoSource() {
            return this.infoSource;
        }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public CommandInfo getCommandInfo() {
        return commandInfo;
    }

    public void setCommandInfo(CommandInfo commandInfo) {
        this.commandInfo = commandInfo;
    }

    public String getRefDisasterName() {
        return refDisasterName;
    }

    public void setRefDisasterName(String refDisasterName) {
        this.refDisasterName = refDisasterName;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }
}
