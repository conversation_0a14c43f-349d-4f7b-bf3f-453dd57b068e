package com.yunqu.yc.emgnotify.model;

import java.io.Serializable;

/**
*按照五级层级划分的行政区域（无界面，系统初始化时设置数据进去）
*/
public class Area implements Serializable {
    private static final long serialVersionUID = 114076187211779061L;

    /**
     *id
     */
    private String id;

    /**
     *地区名称
     */
    private String areaName;

    /**
     *地区层级
     */
    private Integer areaClass;

    /**
     *父id
     */
    private String pId;

    /**
     *地区中心经度
     */
    private String lon;

    /**
     *地区中心纬度
     */
    private String lat;

    /**
     *下级区域数量
     */
    private String sAreaNum;

    /**
     *区域信息员数量
     */
    private String informantNum;

    public void setId(String id) {
            this.id = id;
        }

    public String getId() {
            return this.id;
        }

    public void setAreaName(String areaName) {
            this.areaName = areaName;
        }

    public String getAreaName() {
            return this.areaName;
        }

    public void setAreaClass(Integer areaClass) {
            this.areaClass = areaClass;
        }

    public Integer getAreaClass() {
            return this.areaClass;
        }

    public void setPId(String pId) {
            this.pId = pId;
        }

    public String getPId() {
            return this.pId;
        }

    public void setLon(String lon) {
            this.lon = lon;
        }

    public String getLon() {
            return this.lon;
        }

    public void setLat(String lat) {
            this.lat = lat;
        }

    public String getLat() {
            return this.lat;
        }

    public void setSAreaNum(String sAreaNum) {
            this.sAreaNum = sAreaNum;
        }

    public String getSAreaNum() {
            return this.sAreaNum;
        }

    public void setInformantNum(String informantNum) {
            this.informantNum = informantNum;
        }

    public String getInformantNum() {
            return this.informantNum;
        }

}
