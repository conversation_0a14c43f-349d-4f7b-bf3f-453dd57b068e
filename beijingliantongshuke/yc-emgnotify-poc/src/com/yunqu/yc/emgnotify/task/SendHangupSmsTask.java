package com.yunqu.yc.emgnotify.task;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.emgnotify.base.Constants;
import com.yunqu.yc.emgnotify.base.QueryFactory;
import com.yunqu.yc.emgnotify.log.CommonLogger;
import com.yunqu.yc.emgnotify.thread.SmsTaskCommon;
import com.yunqu.yc.emgnotify.thread.SmsTaskThread;
import com.yunqu.yc.emgnotify.utils.CacheUtil;
import com.yunqu.yc.emgnotify.utils.DateUtil;
import com.yunqu.yc.emgnotify.utils.DateUtils;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 扫描话单表，发送挂机短信
 * <AUTHOR>
 * @date 2024年1月9日i下午6:49:42
 *
 */
public class SendHangupSmsTask implements Job {
	
	private Logger logger = CommonLogger.getLogger("sendHangupSms");

	@Override
	public void execute(JobExecutionContext jobExecutionContext) {
		//获取三天前的日期
		String dateId = DateUtil.addDay(DateUtil.getCurrentDateStr("yyyyMMdd"), "yyyyMMdd", -300);
		// 查询所有启动中、外呼的任务
		String sql = "SELECT * FROM "+Constants.getBusiName()+".cc_call_record where CREATE_CAUSE in (1,2) and DATE_ID>='"
				+dateId+"' and (DATA3 ='' or DATA3 is null)";

		EasyQuery query = QueryFactory.getQuery();
		try {
			List<Map<String, String>> recordList = query.queryForList(sql, null, new MapRowMapperImpl());
			for (Map<String, String> task : recordList) {
				String caller = task.get("CALLED");
				JSONObject provinceJson = getProvinceJson(caller,query);
				String taskName = "";
				if(provinceJson!=null) {
					taskName = provinceJson.getString("PROVINCE_NAME")+"挂机短信任务";
				}else {
					logger.info("[SendHangupSmsTask.execute] >>> 未获取到当前人的信息！无法发送短信");
					continue;
				}
				logger.info("[SendHangupSmsTask.execute] >>> taskName："+taskName);
				JSONObject taskJson = getTaskJson(taskName,query);
				logger.info("[SendHangupSmsTask.execute] >>> taskJson："+taskJson);
				if(taskJson!=null){
					addTaskObj(task.get("SERIAL_ID"),taskJson,provinceJson,query);
				} else {//如果为空先去库中新增任务
					String f1 = query.queryForString("select NOTICE_CONTENT from "+Constants.getBusiName()+".jy_task_noticetemplate where SMS_TEMPLATE_TYPE=2", new Object[] {});
					logger.info("[SendHangupSmsTask.execute] >>> f1："+f1);
					if(StringUtils.isNotBlank(f1)) {
						addTask(provinceJson.getString("NAME"),taskName,provinceJson.getString("ENT_ID"),provinceJson.getString("BUSI_ORDER_ID")
								,Constants.TASK_TYPE_MESSAGE, "",provinceJson.getString("NAME"),f1);
					}else {
						logger.info("[SendHangupSmsTask.execute] >>> 当前企业下没有挂机短信模版！跳出");
						continue;
					}
					
				}
				
				
			}
			logger.info("[SendHangupSmsTask.execute] >>> excuete stat report data over");
		} catch (Exception e) {
			logger.info("[SendHangupSmsTask.execute] >>> excuete stat report data over:"+e.getMessage(), e);
		}
	}

	/**
	 * 
	 * @param taskJson
	 * @param provinceJson
	 * @param query
	 * @throws SQLException
	 */
	public void addTaskObj(String serialId,JSONObject taskJson,JSONObject provinceJson,EasyQuery query) throws SQLException {
		String insertSql = "insert  into " + Constants.getBusiName() + ".cc_task_obj (OBJ_ID,ENT_ID,BUSI_ORDER_ID,TASK_ID,MONTH_ID,TASK_STATE,CUST_NAME,TEL_NUM1,F1,F22,F23,F24,F25,F26,F27,F28,F29,F30) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		List<Object[]> values = new ArrayList<Object[]>();
		values.add(new Object[]{
                //OBJ_ID
                RandomKit.uuid(),
                Constants.getEntId(),
                Constants.getBusiOrderId(),
                taskJson.getString("TASK_ID"),
                //MONTH_ID
                EasyCalendar.newInstance().getFullMonth(),
                // 名单状态
                0,
                // 志愿者姓名
                provinceJson.getString("NAME"),
                // 志愿者电话
                provinceJson.getString("PHONE"),
                // 通知内容 F1
                taskJson.getString("DATA3"),
                // 任职日期  F22
                provinceJson.getString("ENTRY_DATE"),
                // 座机号  F23
                provinceJson.getString("TEL"),
                // 人员类型名称,多个逗号隔开  F24
                provinceJson.getString("PERSONTYPE_NAMES"),
                // 叫应通知人员id  F25
                provinceJson.getString("ID"),
                // 省    F26
                provinceJson.getString("PROVINCE"),
                // 市    F27
                provinceJson.getString("CITY"),
                // 县    F28
                provinceJson.getString("DISTRICT"),
                // 街道   F29
                provinceJson.getString("STREET"),
                // 单位   F30
                provinceJson.getString("DEPARTMENT")
        });
		query.executeBatch(insertSql, values);//新增cc_task_obj
		int orderCount = query.queryForInt("select count(1) from "+Constants.getBusiName()+".cc_task_obj where TASK_ID=?", new Object[] {taskJson.getString("TASK_ID")});
		EasyRecord record = new EasyRecord(Constants.getBusiName()+".cc_task","task_id");
		record.set("task_id",  taskJson.getString("TASK_ID"));
		record.set("OBJ_COUNT",  orderCount);
		query.update(record);//更新cc_task总数
		record = new EasyRecord(Constants.getBusiName()+".cc_call_record","SERIAL_ID");
		record.set("SERIAL_ID", serialId);
		record.set("DATA3", "1");
		query.update(record);//更新cc_call_record发送短信字段
	}
	
	
	/**
	 * 获取当前号码的区域
	 * @param caller
	 * @param query
	 * @return
	 * @throws SQLException
	 */
	public JSONObject getProvinceJson(String caller,EasyQuery query) throws SQLException {
		JSONObject provinceJson = CacheUtil.get("PROVINCE_NAME_"+caller);
		if(provinceJson==null) {
			provinceJson = query.queryForRow("select * from "+Constants.getBusiName()+".jy_informant_info where PHONE=?"
					, new Object[] {caller},new JSONMapperImpl());
			if(provinceJson!=null) {
				CacheUtil.put("PROVINCE_NAME_"+caller, provinceJson,24*60*60);
			}
		}
		return provinceJson;
	}
	
	
	/**
	 * 根据任务名称获取任务详情
	 * @param taskName
	 * @param query
	 * @return
	 * @throws SQLException
	 */
	public JSONObject getTaskJson(String taskName,EasyQuery query) throws SQLException {
		JSONObject json = CacheUtil.get("TASK_JSON_"+taskName);
		if(json==null) {
			json = query.queryForRow("select * from "+Constants.getBusiName()+".cc_task where task_Name=?", new Object[] {taskName},new JSONMapperImpl());
			if(json!=null) {
				CacheUtil.put("TASK_JSON_"+taskName, json);
			}
		}
		return json;
	}

	/*
    *
    * <AUTHOR>
    * @date  2024/1/10 09:14
    * @param taskName = rescueEventName + "接话人外呼提醒任务";
    * @param entId
    * @param busiOrderId
    * @param taskType    任务类型 3-ivr任务 4-短信任务 6-广播
    * @param robotId    机器人id
    * @param f1    String f1 = "{姓名}您好，您已成为 {灾情事件名称} 的接话响应人员，请注意及时接听响应95707来电。";
    * @return void
    */
   public void addTask(String rescueName,String taskName,String entId,String busiOrderId,Integer taskType,String robotId,String caller,String f1){
       try {
           EasyQuery readQuery = QueryFactory.getReadQuery();
           EasyQuery writequery = QueryFactory.getWriteQuery();


           String taskId = readQuery.queryForString("select TASK_ID from " + Constants.getBusiName() + ".cc_task where task_name = ?", taskName);
           if(StringUtils.isBlank(taskId)){
               // 任务号
               int taskNum =  readQuery.queryForInt("SELECT max(TASK_NUM) from "+Constants.getBusiName()+".cc_task WHERE ENT_ID = ?", entId)+1;
               taskId = RandomKit.randomStr();
               EasyRecord record = new EasyRecord(Constants.getBusiName()+".CC_TASK", "TASK_ID");
               record.put("TASK_ID", taskId);//任务id
               record.put("TASK_NAME", taskName);//任务名称
               record.put("ENT_ID", entId);//企业id
               record.put("BUSI_ORDER_ID", busiOrderId);//订购id
               record.put("TASK_NUM",taskNum);//任务号
               //前端或对外接口未传客户资料模板ID，获取配置项的模板id
               record.put("TEMP_ID", Constants.getJyInformantTempId());
               //前端或对外接口未传 开始和结束时间
               record.put("START_DATE", DateUtils.getTody());
               record.put("END_DATE", DateUtils.getLastMonthDate(DateUtils.getTody()));

               record.put("TASK_STATE",  Constants.TASK_STATE_005);//5启动 4 待启动

               record.put("TASK_TYPE", taskType);//任务类型 3-ivr任务 4-短信任务 6-广播
               record.put("PREFIX_NUM", Constants.getJyPrefixNum());//外显号码
               record.put("FAIL_CALL_COUNT", 0);//失败呼叫次数，针对单个号码，在同一个任务中
               record.put("CALL_INTERVAL", 0);//失败后，下次的呼叫时间间隔，单位：秒
               record.put("OBJ_COUNT", 1);//名单总量
               record.put("CALL_RATE", 1);//呼叫比，任务的外呼和坐席呼叫比，[无用但必填]
               record.put("CREATOR", "");//创建人账号
               record.put("CREATE_TIME", DateUtil.getCurrentDateStr());//创建时间

               record.put("IVR_FLOW_NAME", robotId);//机器人id

               record.put("TASK_DEMO", "");//备注
               record.put("DATA1", "1");//拓展1 机器人类型 1-通知 2-交互
               record.put("DATA2", Constants.getSmsChannelId());//拓展2 短信渠道id
               writequery.save(record);
               // 新建好任务后 将任务添加到发短信线程中
               SmsTaskThread taskThread = new SmsTaskThread(taskId);
               // 将启动中的任务添加到内存中
               SmsTaskCommon.addTask(taskThread);
           }
           
           String insertSql = "insert  into " + Constants.getBusiName() + ".cc_task_obj (OBJ_ID,ENT_ID,BUSI_ORDER_ID,TASK_ID,MONTH_ID,TASK_STATE,TEL_NUM1,CUST_NAME,F1) values (?,?,?,?,?,?,?,?,?)";
           writequery.execute(insertSql,new Object[]{RandomKit.uuid(), entId, busiOrderId, taskId, EasyCalendar.newInstance().getFullMonth(), 0, caller, rescueName,f1});
           writequery.executeUpdate("update " + Constants.getBusiName() + ".cc_task set OBJ_COUNT = OBJ_COUNT + 1 where TASK_ID = ?",taskId);
           // 如果是外呼任务，通知ocs
           if(Constants.TASK_TYPE_IVR.equals(taskType)){
               syncTaskInfo(taskId,"3",entId,3);
           }
       } catch (SQLException e) {
           logger.error(e.getMessage(),e);
       }
   }
	
	/**
     * 同步任务状态到ocs
     * @param taskId 任务ID
     * @param status 任务状态，取值：1.停止 2.暂停 3.启动
     * @param entId 企业id
     * @param taskType 任务类型，1：人工外呼　　2：自动外呼  3.IVR语音外呼 4：短信营销任务
     */
    protected String syncTaskInfo(String taskId, String status, String entId, int taskType) {
        IService service;
        try {
            service = ServiceContext.getService("YC-TASK-NOTIFY-SERVICE");//yc-api
            JSONObject jsonIn = new JSONObject();
            jsonIn.put("entId", entId);
            jsonIn.put("taskId", taskId);
            jsonIn.put("status", status);
            jsonIn.put("taskType", taskType);
            JSONObject result = service.invoke(jsonIn);
            String rs = result.getString("result");
            logger.info("syncTaskInfo success >> taskId:" + taskId + " status:" + status + ",result:" + result.toJSONString(), null);
            if ("000".equals(rs)) {
                return null;
            } else {
                return result.getString("message");
            }
        } catch (Exception ex) {
            logger.error("syncTaskInfo error >> taskId:" + taskId + " status:" + status + ",cause:" + ex.getMessage(), ex);
            return ex.getMessage();
        }
    }
}
