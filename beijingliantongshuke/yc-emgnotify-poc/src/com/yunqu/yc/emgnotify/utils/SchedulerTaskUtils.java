package com.yunqu.yc.emgnotify.utils;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.TriggerBuilder.newTrigger;

import org.easitline.common.utils.string.StringUtils;
import org.quartz.CronExpression;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;

import com.yunqu.yc.emgnotify.base.Constants;

/**
 * 定是作业任务类
 *
 * <AUTHOR>
 */
public class SchedulerTaskUtils {

	private static Scheduler scheduler = null;

	private static final String GROUP_NAME = Constants.APP_NAME + "_sch";

	/**
	 * 创建调度任务
	 *
	 * @param clazz 类全路径
	 * @param cron  定时表达式
	 */
	public static void addTask(final String clazz, final String cron) throws ClassNotFoundException, SchedulerException {
		addTask(clazz, cron, clazz);
	}

	/**
	 * 创建调度任务
	 *
	 * @param clazz       类全路径
	 * @param cron        定时表达式
	 * @param triggerName 触发器
	 * @throws SchedulerException
	 * @throws ClassNotFoundException
	 */
	public static void addTask(final String clazz, final String cron, final String triggerName) throws SchedulerException, ClassNotFoundException {
		try {
			addTask(Class.forName(clazz), cron, triggerName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new ClassNotFoundException(clazz + " not found.", e);
		}

	}

	public static void addTask(final String clazz, final int jobTime, final int jobTimes) throws SchedulerException, ClassNotFoundException {
		try {
			addTask(Class.forName(clazz), jobTime, jobTimes, clazz);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new ClassNotFoundException(clazz + " not found.", e);
		}
	}

	public static void addTask(final Class clazz, final int jobTime, final int jobTimes, final String triggerName) throws SchedulerException {
		if (clazz == null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if (scheduler == null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！", e);
			}
		}
		Trigger trigger = TriggerBuilder
				.newTrigger()
				.withIdentity(triggerName, GROUP_NAME)
				.withSchedule(
						SimpleScheduleBuilder.repeatSecondlyForever(1)
								.withIntervalInSeconds(jobTime)
								.withRepeatCount(jobTimes)).startNow()
				.build();
		scheduler.scheduleJob(getJob(clazz, triggerName), trigger);
	}

	/**
	 * 创建调度任务
	 *
	 * @param clazz       类全路径
	 * @param cron        定时表达式
	 * @param triggerName 触发器名称
	 * @throws SchedulerException
	 */
	@SuppressWarnings("rawtypes")
	public static void addTask(final Class clazz, final String cron, final String triggerName) throws SchedulerException {
		if (clazz == null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if (StringUtils.isBlank(cron) || !CronExpression.isValidExpression(cron)) {
			throw new RuntimeException("cron表达式不符合要求！");
		}
		if (StringUtils.isBlank(triggerName)) {
			throw new NullPointerException("触发器名称triggerName不能为空！");
		}
		if (scheduler == null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！", e);
			}
		}
		Trigger trigger = newTrigger()
				.withIdentity(triggerName, GROUP_NAME)
				.startNow()
				.withSchedule(cronSchedule(cron).withMisfireHandlingInstructionFireAndProceed())
				.build();
		scheduler.scheduleJob(getJob(clazz, triggerName), trigger);
	}

	/**
	 * 创建调度任务
	 *
	 * @param clazz       类全路径
	 * @param cron        定时表达式
	 * @param triggerName 触发器名称
	 * @throws SchedulerException
	 */
	@SuppressWarnings("rawtypes")
	public static void addTaskRunNow(final Class clazz, final String cron, final String triggerName) throws SchedulerException {
		if (clazz == null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if (StringUtils.isBlank(cron) || !CronExpression.isValidExpression(cron)) {
			throw new RuntimeException("cron表达式不符合要求！");
		}
		if (StringUtils.isBlank(triggerName)) {
			throw new NullPointerException("触发器名称triggerName不能为空！");
		}
		if (scheduler == null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！", e);
			}
		}
		Trigger trigger = newTrigger()
				.withIdentity(triggerName, GROUP_NAME)
				.withSchedule(cronSchedule(cron).withMisfireHandlingInstructionIgnoreMisfires())
				.withPriority(1)
				.startNow()
				.build();
		scheduler.scheduleJob(getJob(clazz, triggerName), trigger);
	}

	/**
	 * 实例调度任务
	 *
	 * @param clazz       类对象
	 * @param triggerName 触发器名称
	 * @return
	 */
	@SuppressWarnings({"rawtypes", "unchecked"})
	private static JobDetail getJob(Class clazz, String triggerName) {
		JobDetail job = newJob(clazz).withIdentity(triggerName, GROUP_NAME)
				.usingJobData("name", "quartz").build();
		return job;
	}

	/**
	 * 开始作业
	 *
	 * @throws SchedulerException
	 */
	public static void start() throws SchedulerException {
		try {
			scheduler.start();
		} catch (SchedulerException e) {
			e.printStackTrace();
			throw new SchedulerException("启动调度器scheduler异常！", e);
		}
	}

	/**
	 * 注销调度器
	 *
	 * @throws SchedulerException
	 */
	public static void shutDown() throws SchedulerException {
		try {
			scheduler.shutdown(true);
		} catch (SchedulerException e) {
			e.printStackTrace();
			throw new SchedulerException("关闭调度器scheduler异常！", e);
		}
	}

	/**
	 * 移除任务
	 *
	 * @param clazz 类路径
	 * @throws SchedulerException
	 */
	public static void deleteSchedul(String clazz) throws SchedulerException {
		TriggerKey triggerKey = TriggerKey.triggerKey(clazz, GROUP_NAME);
		// 停止触发器
		scheduler.pauseTrigger(triggerKey);
		// 移除触发器
		scheduler.unscheduleJob(triggerKey);
		// 删除任务
		scheduler.deleteJob(JobKey.jobKey(clazz, GROUP_NAME));
	}
}
