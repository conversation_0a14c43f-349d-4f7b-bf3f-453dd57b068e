package com.yunqu.yc.emgnotify.utils;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.yunqu.yc.emgnotify.log.CommonLogger;
import com.yunqu.yc.emgnotify.model.UserMgr;
import com.yunqu.yc.emgnotify.model.UserModel;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

public class UserUtil {
	private static Logger logger = CommonLogger.getLogger("user");
		
	private static String USER_SESSION_KEY = "B_USER_SESSION_KEY";
	/**
	 * 获取登录用户
	 * @return
	 */
	public static UserModel getUser(HttpServletRequest request){
		try {
			UserModel user = null;
			YCUserPrincipal ycuser = getUserPrincipal(request);
			if(ycuser==null){
				logger.error(" [UserUtil.getUser] 获取用户信息失败,无法获取到用户登录session!  尝试获取用户账号:"+request.getRemoteUser()+",用户ip:"+request.getRemoteAddr());
				return null;
			}
			
			//从登录对象里获取企业呼叫中心对象，如果为空，则需要
			user = (UserModel)ycuser.getAttribute(USER_SESSION_KEY);
			
			if(user==null){
				String userAcc = ycuser.getLoginAcct();//ycuser.getEcUserPrincipal();
				String schema = ycuser.getSchemaName();
				String entId = ycuser.getEntId();
				String busiOrderId = ycuser.getBusiOrderId();
				
				if(StringUtils.isNotBlank(userAcc)){
					
					/*if(CEConstants.openNoAuthMarsTokenCheck()){
						//登录时，绑定sessionid和用户的关系
						String sessionId = CookieUtil.getMarsToken(request);
						if(StringUtils.isNotBlank(sessionId)){
							String key = "cc-base-"+sessionId;
							CacheUtil.put(key, userAcc,50000);
							logger.info("【垂直越权】用户新登录,插入用户与session的对应关系,userAcc:"+userAcc+",sessionKey:"+key);
						}
					}*/
					//查询用户信息并加入到session中
					user = UserUtil.setUser(request, userAcc,schema,entId,busiOrderId);
					ycuser.setAttribute(USER_SESSION_KEY, user);
				}else{
					logger.error("用户新登录,无法从mars单点登录里获取账号信息!");
				}
			}
//			user = (UserModel)request.getSession().getAttribute(USER_SESSION_KEY);
			
			return user;
		} catch (Exception e) {
			logger.error( "[UserUtil.getUser]  error:"+e.getMessage(), e);	
		}
		return null;
	}
	/**
	 * 从请求里获取登录用户账号，如admin@1000
	 * @param request
	 * @return
	 */
	public static String getRequestUserAcc(HttpServletRequest request){
		YCUserPrincipal  userPrincipal = (YCUserPrincipal)request.getUserPrincipal();
		if(userPrincipal.getEcUserPrincipal() != null){
			 return userPrincipal.getEcUserPrincipal().getLoginAcct();
		}

		return userPrincipal.getLoginAcct();
	}
	
	/**
	 * 从请求里单点登录后的用户信息
	 * @param request
	 * @return
	 */
	public static YCUserPrincipal getUserPrincipal(HttpServletRequest request){
		YCUserPrincipal  userPrincipal = (YCUserPrincipal)request.getUserPrincipal();
		return userPrincipal;
	}
	
	/**
	 * 从请求里获取登录用户账号，如admin@1000
	 * @param request
	 * @return
	 */
	public static String getRequestUserSchema(HttpServletRequest request){
		YCUserPrincipal  userPrincipal = (YCUserPrincipal)request.getUserPrincipal();
		return userPrincipal.getSchemaName();
	}
	
	/**
	 * 用户登录时设置
	 * @param schema 
	 * @return
	 * @throws Exception 
	 */
	public static UserModel setUser(HttpServletRequest request,String acc, String schema,String entId,String busiOrderId) throws Exception{
		UserModel user = UserMgr.getUserByUserAcc(acc,schema,true,true,true,entId,busiOrderId);
		if(user==null){
			logger.error("用户不存在:"+acc);
		}
		//设置用户信息所属数据库
		user.setSchemaName(schema);
		
		//设置用户单点登录信息
		//user.setYcUserPrincipal(getUserPrincipal(request));
		
		
//		request.getSession().setAttribute(USER_SESSION_KEY, user);
		return user;
	}

	


	
}
