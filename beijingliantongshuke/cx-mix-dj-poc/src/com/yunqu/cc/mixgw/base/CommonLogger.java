package com.yunqu.cc.mixgw.base;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;


public class CommonLogger {
	public static Logger logger = new CommonLogger().getLogger() ;
	
	public CommonLogger(){}
	
	public Logger getLogger(){
		return LogEngine.getLogger(Constants.APP_NAME);
	}
	public static Logger getLogger(String suffix){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME + "-" + suffix);
	}
}
