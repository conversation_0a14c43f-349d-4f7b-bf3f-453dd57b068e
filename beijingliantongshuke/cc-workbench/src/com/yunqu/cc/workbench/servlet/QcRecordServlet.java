package com.yunqu.cc.workbench.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.druid.Constants;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.workbench.base.AppBaseServlet;
import com.yunqu.cc.workbench.base.CommonLogger;
import com.yunqu.cc.workbench.utils.PhoneCryptor;

/**
 * 质检结果Servlet
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/qcRecord")
public class QcRecordServlet extends AppBaseServlet {

	private Logger logger = CommonLogger.logger;
	
	private static final long serialVersionUID = 1L;
	
	
	/**
	 * 根据objId获取对应的录音文件
	 * @return
	 */
	public EasyResult actionForGetRecordFile(){
		EasyResult result = new EasyResult();
		JSONObject json = this.getJSONObject();
		try {
			List<JSONObject> queryForList = null;
			String id = json.getString("serialId");
			if(StringUtils.isNotBlank(id)){
				if("ecuser".equals(json.getString("userType"))){
					queryForList = this.getQuery().queryForList("select t1.*,t2.USER_ACCT,t2.USERNAME from " + getTableName("CC_CALL_RECORD t1") + " left join CC_EC_USER t2 on t1.AGENT_ID = t2.AGENT_ID where t1.SERIAL_ID = ?", new Object[]{id},new JSONMapperImpl());
				}else{
					queryForList = this.getQuery().queryForList("select * from " + getTableName("CC_CALL_RECORD") + " where SERIAL_ID = ?", new Object[]{id},new JSONMapperImpl());
				}
			}
			//判断该对象是否存在录音文件
			if(queryForList != null && queryForList.size() > 0){
				String recordFile=queryForList.get(0).getString("RECORD_FILE");
				if (StringUtils.isNotBlank(recordFile)) {
					if(!recordFile.startsWith("/")&&!recordFile.startsWith("http")){
						recordFile=getRecordUrlPrefix()+recordFile;
					}
				}

				if(StringUtils.isNotBlank(recordFile)){
					result.setUrl(recordFile);
					result.setSuccess(queryForList.get(0), "获取录音文件成功");
				}else{
					result.addFail("没有通话录音文件！");
				}
			}else {
				result.addFail("没有通话录音文件！");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("获取录音文件失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 录音文件前缀
	 * @return
	 */
	private String getRecordUrlPrefix(){
		String entId=getEntId();
		String url=CacheManager.getMemcache().get("RecordUrlPrefix_"+entId);
		   try {
			   if(StringUtils.isBlank(url)){
				   String sql = "select t2.RECORD_FILE_URL from CC_ENT_RES  t1  , CC_PETRA_RES t2 where t1.PETRA_ID = t2.PETRA_ID    and t1.ENT_ID =  ?";
				    url=this.getQuery().queryForString(sql, new Object[]{this.getResEntId()});
				    if(StringUtils.isNotBlank(url)){
				    	if(url.lastIndexOf("/")==-1){
				    		url=url+"/";
				    	}
				    }
				    CacheManager.getMemcache().put("RecordUrlPrefix_"+entId, url);
			   }
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return url;
	}
	
	public EasyResult actionForGetRecordFile2(){
		EasyResult result = new EasyResult();
		try {
			List<JSONObject> queryForList = null;
			String id=getJsonPara("serialId");
			if(StringUtils.isNotBlank(id)){
				queryForList = this.getQuery().queryForList("select * from " + getTableName("CC_CALL_RECORD") + " where SERIAL_ID = ?", new Object[]{id},new JSONMapperImpl());
			}else{
				id=getJsonPara("objId");
				queryForList = this.getQuery().queryForList("select * from " + getTableName("CC_CALL_RECORD") + " where OBJ_ID = ? order by SERIAL_ID", new Object[]{id},new JSONMapperImpl());
			}
			queryForList = PhoneCryptor.getInstance().decrypt(queryForList, new String[]{"CALLER","CALLED","CUST_PHONE"}, false);
			//判断该对象是否存在录音文件
			if(queryForList != null && queryForList.size() > 0){
				String recordFile=queryForList.get(0).getString("RECORD_FILE");
				if (StringUtils.isNotBlank(recordFile)) {
					
					if(!recordFile.startsWith("/")&&!recordFile.startsWith("http")){
						
						String prefix = getRecordUrlPrefix(); //老版本是用 /recordfile，mount在ROOT里；新版本是配置的文件相对路径，如/home/<USER>
						if(prefix.startsWith("/recordfile")){
							recordFile=prefix+recordFile;
						}else{
							recordFile = CEConstants.getContextPath("cc-base")+"/api/voice?action=play&filePath="+prefix+recordFile;	
						}
					}
				}
				/*recordFile =getRecordUrlPrefix() + recordFile;*/
				result.setUrl(recordFile);
				result.put("schema", getDbName());
				result.setSuccess(queryForList.get(0), "获取录音文件成功");
			}else {
				result.addFail("没有通话录音文件！");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("获取录音文件失败！失败原因：" + e.getMessage());
		}
		result.put("fileType", AppContext.getContext("cc-base").getProperty("FILE_TYPE", ""));
		
		
		if(ServerContext.isDebug()){
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询录音路径返回:"+result);
		}
		
		return result;
	}
	
	
	

}
