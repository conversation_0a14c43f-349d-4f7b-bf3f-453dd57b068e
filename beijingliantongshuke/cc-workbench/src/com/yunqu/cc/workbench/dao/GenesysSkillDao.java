package com.yunqu.cc.workbench.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.workbench.base.AppDaoContext;
import com.yunqu.cc.workbench.base.CommonLogger;

@WebObject(name="genesysSkill")
public class GenesysSkillDao extends AppDaoContext {
	private static Logger logger = CommonLogger.logger;
	
	@WebControl(name="genesysSkillFindSkillName", type=Types.RECORD)
	public JSONObject genesysSkillFindSkillName() {
		String skillId = param.getString("skillId");
		EasySQL sql = getEasySQL("select NAME from C_GENESYS_SKILL ");
		sql.append(skillId, "where CODE = ? ");
		logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过genesys技能组id获取genesys技能组名称SQL:"+ sql.getSQL()+ ","+ JSONObject.toJSONString(sql.getParams()));
//			String skillName = getQuery().queryForString(sql.getSQL(), sql.getParams());
		return queryForList(sql.getSQL(), sql.getParams());
	}
}
