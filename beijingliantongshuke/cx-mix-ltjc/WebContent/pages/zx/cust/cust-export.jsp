<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传数据</title>
	<style>
		 #importDataList-body td:nth-child(even) {  
			 display: none;
   		 }  
	    #importDataList-body td:nth-child(odd) {  
	        background: White;  
	    } 
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="custDownload" method="post">
				  <table class="table table-vzebra mt-10">
	                    <tbody>
		                     <tr>
			                        <td class="required" class="text-c" width="130px">
			                        	选择模板
			                        </td>
			                        <td>
			                         	  <select name="tempId" data-rules="required" class="form-control input-sm" data-title="请选择客户模板" onchange="changeTempId($(this));" id="tempId" data-mars="CustDataObj.templateDict">
			                         	  		<option value="">请选择</option>
			                         	  </select>			                         	
			                        </td>
		                     </tr>
		                     <tr id="batchTr" style="display: none;">
			                        <td class="required">批次</td>
			                        <td>
			                        	  <select name="batchId" class="form-control input-sm" id="batchId" data-mars="CustDataObj.custBatchDict"></select>			                         	
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c">
			                        	提示信息
			                        </td>
			                        <td id="import_result">
			                        	请选择客户模板
			                        </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="custDownload.ajaxSubmitForm()"> 导出 </button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("custDownload");
	
	custDownload.tempId='${param.tempId}';
	
	$(function(){
		var data = new Array();
		data["contextPath"] = "/yc-agent";
		$("#custDownload").render({data:data});
	});
	
	custDownload.ajaxSubmitForm = function(){
		if(form.validate("#custDownload")){
			layer.closeAll();
			location.href = "/yc-agent/servlet/export?action=exportCust&"+$("#custDownload").serialize();
		};
	}
	
	var changeTempId = function(el){
		$("#batchId").render({data:{custTempId:el.val(),contextPath:"/yc-agent"},success:function(result){
			var data = result['CustDataObj.custBatchDict'];
			if(data){
				if(data.total > 1){
					$("#batchTr").show();
				}else{
					$("#batchTr").hide();
				}
				$("#import_result").html(data.msg);
			}
		}});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>