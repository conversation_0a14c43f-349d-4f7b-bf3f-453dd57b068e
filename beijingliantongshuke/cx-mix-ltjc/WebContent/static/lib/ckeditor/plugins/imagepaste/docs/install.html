<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
	"http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Image paste plugin</title>
<link href="styles.css" rel="stylesheet" type="text/css">
</head>

<body>
<h1>Image paste Plugin for CKEditor</h1>

<h2>Introduction</h2>
<p>This is a plugin that automatically uploads to the server the images pasted from the clipboard with Firefox in <a href="http://www.ckeditor.com">CKEditor</a>.</p>

<h3 id="contact">Author:</h3>
<p><a href="mailto:<EMAIL>">Alfonso Mart&iacute;nez de Lizarrondo</a></p>

<h3>Version history: </h3>
<ol>
  <li>1.0: 28-September-2011. First public version.</li>
  <li>1.1: 7-October-2013. Compatibility with CKEditor 4. Sorry to everyone that has tried to use it before, but I didn't realize that it was broken all along as it's a simple fix that I did long ago in the commercial version.<br>
  At the same time, I'm removing the useless "webkit-fake-url" crap that Safari generates but that it can't be used for anything.</li>
</ol>

<h2>Installation</h2>
<h3>1. Copying the files</h3>
<p>Extract the contents of the zip in you plugins directory, so it ends up like
    this<br>
    <!--<img src="installation.png" alt="Screenshot of installation" width="311" height="346" longdesc="#install">-->
    </p>
<pre id="--install">
ckeditor\
	...
	images\
	lang\
	plugins\
		...
		imagepaste\
			plugin.js
			docs\
				install.html
		...
	skins\
	themes\
</pre>
<h3>2. Adding it to CKEditor</h3>
<p>Now add the plugin in your <em>config.js</em> or custom js configuration
file:
<code>config.extraPlugins='imagepaste'; </code>
</p>

<h3>3. Configuration</h3>
<p>You have to configure the filebrowserImageUploadUrl entry as you might have already done to allow the user to upload images. You can get a simple script to use as a guide for your code <a href="http://alfonsoml.blogspot.com.es/2013/08/a-basic-upload-script-for-ckeditor.html">in this post in my blog</a>.</p>

<h3>4. Use it</h3>
<p>Using Firefox, paste an image into the body of CKEditor. That image will be uploaded to the server and it will use the correct URL instead of "data:".</p>

<!--
<h2>Final notes</h2>
-->

<h2>Disclaimers</h2>
<p>CKEditor is  &copy; CKSource.com</p>
</body>
</html>
