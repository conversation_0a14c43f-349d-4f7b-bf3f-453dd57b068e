package com.yunqu.cc.mixgw.servlet;


import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.cc.mixgw.base.CommonLogger;




/**
 * 文件预览
 */
@WebServlet(urlPatterns = { "/fileview/*" })
public class FileViewServlet extends HttpServlet {
	
	private static final long serialVersionUID = 1L;
	private static Logger logger = CommonLogger.logger;
	
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		this.doPost(req,resp);  
		
	}
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)throws ServletException, IOException {
	 
		try{
		
			String fileName = StringUtils.trimToEmpty(request.getParameter("filename"));
			String url = request.getRequestURI();  
			url = url.substring(url.indexOf("/fileview")+9);
			
			File file =  getFile(url);
			if(file==null||!file.exists()){
					response.sendError(404);
					return;
			}
				if(StringUtils.notBlank(fileName)){
					response.setHeader("Content-disposition","attachment;filename="+toUtf8String(fileName));
					if(file!=null){
						response.setHeader("Content-Length",file.length()+"");
					}
				}
				response.setContentType(getContentType(FileKit.getHouzui(file.getName()))+";charset=UTF-8");
				FileUtils.copyFile(file, response.getOutputStream());
			
			
		}catch(Exception ex){
			
		}
	}
	public static String getContentType(String filePath){
	    Path path = Paths.get(filePath);  
	    String contentType = null;  
	    try {  
	        contentType = Files.probeContentType(path);  
	    } catch (IOException e) {  
	        e.printStackTrace();  
	        logger.error("操作失败，原因：" + e.getMessage(), e);
	    }
	    return contentType; 
	}
	/**
	 * 处理附件为中文名的问题
	 * @param fn
	 * @return
	 */
	private String toUtf8String(String fn) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < fn.length(); i++) {
			char c = fn.charAt(i);
			if (c >= 0 && c <= 255) {
				sb.append(c);
			} else {
				byte[] b;
				try {
					b = Character.toString(c).getBytes("utf-8");
				} catch (Exception ex) {
					// System.out.println(ex);
					b = new byte[0];
				}
				for (int j = 0; j < b.length; j++) {
					int k = b[j];
					if (k < 0)
						k += 256;
					sb.append("%" + Integer.toHexString(k).toUpperCase());
				}
			}
		}
		return sb.toString();
	}
	
	
	//如果文件存在直接返回
	private File getFile(String uri){
	  File distFile =  new File(getBaseDir()+uri);
	  if(distFile.isFile()) return distFile;
	  return null;
	}
	
	private static String getBaseDir() {
		//String bastPath = AppContext.getContext(Constants.APP_NAME).getProperty("kmAttachPath", "");
		String bastPath = AppContext.getContext("cc-base").getProperty("ATTACHMENT_ROOT_PAHT", "");
		if(StringUtils.isBlank(bastPath)){
			bastPath=ServerContext.getProperties("G_EFS_DIR", Globals.FILE_SERVER_DIR);
		}
		if (!bastPath.endsWith("/") && !bastPath.endsWith("\\")) {
			bastPath = bastPath + File.separator;
		}
		return bastPath;
	}
	
	
}