
.portal-header,
.portal-sidebar,
.portal-main {
  position: absolute;
  z-index: 5;
}
.portal-header {
  z-index: 5;
  top: 0;
  left: 0;
  height: 50px;
  right: 0;
  background-color: #fff;
}
.portal-header:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #e1e4e5;
}
.portal-header .logo {
  float: left;
  height: 100%;
  cursor: pointer;
}
.portal-header .logo img {
  height: 100%;
}


.navbar-left-nav {
  float: left;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  letter-spacing: 1px;
}
.navbar-right-nav {
  float: right;
  padding: 0 20px;
}
.navbar-right-nav > ul {
  list-style: none;
  font-size: 0;
  margin: 0;
  padding: 0;
}
.navbar-right-nav > ul > li {
  padding: 0 10px;
  display: inline-block;
  font-size: 14px;
  color: #fff;
  line-height: 20px;
}
.navbar-right-nav > ul > li > a {
  color: #fff;
  text-decoration: none;
}
.navbar-userinfo {
	display:inline-block;
	margin-top:6px;
    padding-left: 10px;
}
.user-avatar {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

/* ccbar样式 */
.ccbar-box {
  height: 30px;
  margin-top: 10px;
  display: inline-block;
  vertical-align: top;
}
.ccbar-box .needLogonFunc {
  display: none;
}
.ccbar-box .needLogonFunc.logoned {
  display: inline-block;
}
.ccbar-box .needLogonFunc.multiMediaAgent {
  display: none!important;
}
.ccbar-box > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.ccbar-box > ul > li {
  display: inline-block;
  position: relative;
  vertical-align: top;
}
.ccbar-box .ccbar-select {
  border: 0;
  outline: none;
  background: none;
}
.ccbar-box .ccbar-btn {
  display: inline-block;
  line-height: 20px;
  height: 24px;
  text-decoration: none;
  padding: 2px 12px;
  margin-left: 5px;
  margin-top: 2px;
  color: #919799;
  -webkit-border-radius: 14px;
  -moz-border-radius: 14px;
  -ms-border-radius: 14px;
  -o-border-radius: 14px;
  border-radius: 14px;
}
.ccbar-box .ccbar-btn span,
.ccbar-box .ccbar-btn i {
  vertical-align: top;
  font-size: 14px;
}
.ccbar-box .ccbar-btn:hover {
  opacity: 0.9;
}
.ccbar-box .ccbar-btn.btn-agent.logoff {
  background-color: #aaa!important;
}
.ccbar-box .ccbar-btn.btn-answercall,
.ccbar-box .ccbar-btn.btn-makecall {
  color: #20d99b;
  background-color: #fff;
  border: 1px solid #20d99b;
}
.ccbar-box .ccbar-btn.btn-answercall:hover,
.ccbar-box .ccbar-btn.btn-makecall:hover {
  background-color: #20d99b;
  color: #fff;
}
.ccbar-box .ccbar-btn.btn-clearcall.disabled,
.ccbar-box .ccbar-btn.btn-answercall.disabled {
  display: none!important;
}
.ccbar-box .ccbar-btn.btn-clearcall {
  color: #ff6e61;
  background-color: #fff;
  border: 1px solid #ff6e61;
}
.ccbar-box .ccbar-btn.btn-agentready,
.ccbar-box .ccbar-btn.btn-workready,
.ccbar-box .ccbar-btn.btn-agent {
  background-color: #20d99b;
  color: #fff;
}
.ccbar-box .ccbar-btn.btn-agentnotready {
  background-color: #ff6e61;
  color: #fff;
}
.ccbar-box .ccbar-btn.disabled {
  cursor: not-allowed;
  background-color: #aaa!important;
  color: #fff;
  border-color: transparent;
}
.ccbar-box .ccbar-label {
  line-height: 30px;
  margin-bottom: 0;
  color: #919799;
}
.ccbar-box .ccbar-label span {
  color: #0092d8;
}
.ccbar-box .ccbar-label span.cur-agentstate {
  display: inline-block;
  min-width: 4em;
}
.ccbar-box .ccbar-call {
  line-height: 28px;
  border: 1px solid #e1e4e5;
  -webkit-border-radius: 14px;
  -moz-border-radius: 14px;
  -ms-border-radius: 14px;
  -o-border-radius: 14px;
  border-radius: 14px;
}
.ccbar-box .ccbar-call .iconfont {
  color: #33beff;
}
.ccbar-box .ccbar-clock {
  float: right;
  color: #919799;
  margin-right: 5px;
  font-size: 12px;
}
.ccbar-box .ccbar-info {
  padding: 0 5px;
}
.ccbar-box .ccbar-info .ccbar-label {
  display: block;
  line-height: 15px;
  text-align: right;
  font-size: 14px;
}
.ccbar-box .ccbar-info span {
  display: inline-block;
  width: 4em;
  text-align: center;
}
.ccbar-box .ccbar-split {
  padding-right: 10px;
}
.ccbar-box .ccbar-split:after {
  position: absolute;
  background-color: #e1e4e5;
  content: '';
  right: 0;
  top: 50%;
  width: 1px;
  opacity: 0.9;
  height: 20px;
  margin-top: -10px;
}
.ccbar-box .ccbar-form {
  width: 140px;
  padding-left: 5px;
}
#ccbar_callinfo{
  width: auto;
}
#ccbar_callinfo .ccbar-input{width: 100px;}
/*#ccbar_callinfo .ccbar-input:focus{width:120px;}*/

.ccbar-box .ccbar-form input {
  border: 0;
  outline: none;
  background: none;
  width: 100px;
  line-height: 28px;
  height: 28px;
  color: #5c6366;
  font-size: 14px;
}
.ccbar-box .ccbar-department {
  padding-left: 10px!important;
  position: relative;
}
.ccbar-box .ccbar-department > a {
  text-decoration: none;
}
.ccbar-box .ccbar-department .caret {
  position: absolute;
  right: 6px;
  top: 50%;
  margin-top: -2px;
}
.ccbar-box .ccbar-department select {
  height: 20px;
  width: 100px;
  margin-left: 5px;
}
.ccbar-box .ccbar-department.onlyone select {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.ccbar-box .ccbar-department.onlyone select.ccbar-box .ccbar-department.onlyone select::-ms-expand {
  display: none;
}
.ccbar-box .ccbar-department.onlyone:after {
  content: '';
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.ccbar-box .ccbar-form,
.ccbar-box .ccbar-department {
  padding: 0 5px;
  height: 30px;
  box-sizing: border-box;
}
/* 签入框 */
.ccbr-login-input {
  position: absolute;
  display: none;
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #fff;
  padding: 10px 0 0;
  top: 35px;
  z-index: 6;
  left: -130px;
  box-shadow: 0 0 7px rgba(0, 0, 0, 0.2);
}
.ccbr-login-input:after {
  content: '';
  display: block;
  clear: both;
}
.ccbr-login-input label {
  margin-bottom: 0;
}
.ccbr-login-input input[type=radio] {
  margin-top: 0;
  vertical-align: middle;
}
.ccbr-login-input:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  top: -14px;
  right: 20px;
  margin-left: -10px;
  border-style: dashed dashed solid;
  border-color: transparent transparent #fff;
  overflow: hidden;
  border-width: 7px;
  z-index: 2;
}
.ccbr-login-input:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  top: -19px;
  right: 18px;
  /* margin-left: -11px; */
  border-style: dashed dashed solid;
  border-color: transparent transparent #eee;
  overflow: hidden;
  border-width: 9px;
}
.ccbr-login-input .login-input {
  line-height: 28px;
  height: 28px;
  font-size: 14px;
  padding: 0 10px;
  width: 140px;
  margin: 0 6px;
  border: 0;
  border: 1px solid #ccc;
  outline: none;
  border-radius: 3px;
}
.ccbr-login-input .ccbr-login-agenttype {
  padding: 5px 10px;
}
.ccbr-login-input .mediaSwitch {
  cursor: pointer;
  display: block;
  padding-left: 2px;
}
.ccbr-login-input .mediaSwitch input {
  display: none;
}
.ccbr-login-input .mediaSwitch input[type=radio] ~ .radio-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../static/images/radio.png) no-repeat;
}
.ccbr-login-input .mediaSwitch input[type=radio]:checked ~ .radio-icon {
  background-position-x: -20px;
}
.ccbr-login-input .mediaSwitch input[type=checkbox] ~ .radio-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../static/images/radio.png) no-repeat;
}
.ccbr-login-input .mediaSwitch input[type=checkbox]:checked ~ .radio-icon {
  background-position-x: -20px;
}
.ccbr-login-input .mediaSwitch span {
  vertical-align: top;
}
.ccbr-login-input .login-btn {
  display: block;
  text-decoration: none;
  cursor: pointer;
  color: #fff;
  padding: 5px 7px;
  line-height: 24px;
  margin-top: 5px;
  text-align: center;
  background: #0092d8;
}
.agent-btn-group {
  display: inline-block;
  font-size: 0;
  margin-left: 5px;
  vertical-align: top;
}
.agent-btn-group .ccbar-btn {
  border-radius: 0;
  background-color: #20d99b;
  color: #fff;
  font-size: 14px;
  margin-left: 0!important;
  position: relative;
}
.agent-btn-group .ccbar-btn:nth-child(1) {
  border-radius: 10px 0 0 10px;
}
.agent-btn-group .ccbar-btn:nth-last-child(1) {
  border-radius: 0 10px 10px 0 ;
}
.agent-btn-group .ccbar-btn:nth-last-child(1):after {
  display: none;
}
.agent-btn-group .ccbar-btn.active {
  background-color: #919799;
  cursor: not-allowed;
}

.cur-workMode[data-curworkmode='inbound']{
  color:#20d99b;
  
}
.cur-workMode[data-curworkmode='inbound']:before{
  
  font-family: "iconfont" !important;
   font-size: 18px;   
      content: "\e643";
}
.cur-workMode[data-curworkmode='outbound']{

  color:#40AFFF;
}
.cur-workMode[data-curworkmode='outbound']:before{
  font-family: "iconfont" !important;
    font-size: 18px;
      content: "\e630";
  
}

body{min-width: 1370px!important;}
.ccbar-btn[data-ccbartype="workReady"].disabled,.ccbar-btn[data-ccbartype="stopInvent"].disabled,.ccbar-btn[data-ccbartype="stopMonitor"].disabled{
  display: none;
}
.ccbar-btn.btn-hide-disabled.disabled{
  display: none;
}
@media screen and (max-width: 1370px) {
  #ccbar .ccbar-btn .iconfont{display: none;}
}
.ccbar_btn_login.answercall{border-color: #20d99b;background-color: #20d99b;color: #fff;}
.ccbar_btn_login.answercall:hover{background-color: #fff;color: #333;}

.ccbar_btn_login.clearcall{border-color: #ff6e61;background-color: #ff6e61;color: #fff;}
.ccbar_btn_login.clearcall:hover{background-color: #fff;color: #333;}

.ccbar_btn_login.disabled{
  cursor: not-allowed;border: 1px solid #C9C9C9!important;
  background-color: #eee!important;
  color: #666!important;
}
.ccbar-form-input-group{border-radius: 4px}
.ccbar-text-caller{font-size: 16px; color: red}
.ccbar-text-called{font-size: 12px;line-height: 1.5; color:#333;}
#answercallpanel{
       animation:shadowAnimate 1.5s infinite alternate;/*动画捆绑，两个值，动画名称、时长，加上一个无限执行，交替执行*/
}
   @keyframes shadowAnimate{/*改变位置和背景颜色*/
       0% {box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);}
       50% {box-shadow: 1px 1px 70px red;}
       100% {box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);}
   }
/*修改样式加强对比 1113*/
#ccbar.ccbar-box .ccbar-btn.btn-answercall, #ccbar.ccbar-box .ccbar-btn.btn-makecall {
    color: #4695e4;
    background-color: #fff;
    border: 1px solid #4695e4;
}

#ccbar.ccbar-box .ccbar-btn.btn-agentready, #ccbar.ccbar-box .ccbar-btn.btn-workready, #ccbar.ccbar-box .ccbar-btn.btn-agent {
    background-color: #4695e4!important;
    color: #fff!important
}

#ccbar.ccbar-box .ccbar-btn.btn-agentready, #ccbar.ccbar-box .ccbar-btn.btn-workready {
    background-color: #f2f2f2!important;
    color: #666!important;
    border: 1px solid #ddd;
}

#ccbar.ccbar-box[state='IDLE'] .ccbar-btn.btn-agentready.disabled{
    background-color: #4dd774!important;
    color:#fff!important;
}
#ccbar.ccbar-box[state='BUSY'] .ccbar-btn.btn-agentnotready.disabled, #ccbar.ccbar-box[state='TALK'] .ccbar-btn.btn-agentnotready.disabled{
    background-color: #ff6e61!important;
    color:#fff!important;
}
#ccbar.ccbar-box[state='IDLE'] .cur-agentstate{
  color: #4dd774!important
}
#ccbar.ccbar-box[state='BUSY'] .cur-agentstate{
  color: #ff6e61!important
}

#ccbar.ccbar-box[state='BUSY'] .btn-agentnotready .layui-icon-triangle-d,#ccbar.ccbar-box .btn-agentnotready.disabled .layui-icon-triangle-d{
  opacity: 0;
}

#ccbar.ccbar-box .ccbar-btn.btn-agentnotready {
    background-color: #fff;
    color: #666;
    border: 1px solid #ddd;
}

#ccbar.ccbar-box .ccbar-btn.btn-hide-disabled.disabled, #ccbar.ccbar-box .needLogonFunc.disabled {
    display: none!important;
}

#ccbar.ccbar-box[state='LOGOFF'] #workModeSpan{
    display: none!important;

}
#ccbar.ccbar-box[state='LOGOFF'] #autoAnswerSpan{
    display: none!important;

}