/** 透明侧边栏导航 */
.layui-layout-admin .layui-side .layui-nav {
    background-color: transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(255, 255, 255, .03);
}

/** logo部分样式 */
.layui-layout-admin .layui-header .layui-logo {
    background-color: #002140;
    color: #fff;
}

/** header样式 */
.layui-layout-admin .layui-header {
    background-color: #fff;
}

.layui-layout-admin .layui-header a {
    color: #333;
}

.layui-layout-admin .layui-header a:hover {
    color: #333;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #666 transparent transparent;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #666;
}

/** header线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #002140;
}

/** 侧边栏样式 */
.layui-layout-admin .layui-side {
    background-color: #002140;
}

/** 侧边栏文字颜色 */
.layui-side .layui-nav .layui-nav-item a {
    color: rgba(255, 255, 255, 0.65);
}

.layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a, .layui-nav-tree .layui-this > a:hover {
    background-color: #1890FF;
}

.layui-nav-tree .layui-nav-bar {
    background-color: #1890FF;
}

.layui-side .layui-nav-itemed > a, .layui-nav-tree .layui-nav-title a, .layui-nav-tree .layui-nav-title a:hover, .layui-side .layui-nav-item.layui-this > a {
    color: #fff !important;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-itemed > .layui-nav-child {
    background-color: rgba(0, 0, 0, .2) !important;
}

/** PC端折叠鼠标经过样式 */
.layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child:before {
    background: #002140 !important;
}

/** tab下划线 */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    background-color: #002140;
}

/** 主体标题 */
.layui-body-header-title {
    border-left-color: #1890FF;
}

/** 主题切换 */
.btnTheme:hover, .btnTheme.active {
    border-color: #1890FF;
}

/** admin风格弹窗样式 */
.layui-layer.layui-layer-admin .layui-layer-title {
    background-color: #fff;
    color: #333;
    border-bottom: 1px solid #eee;
}

/** 按钮颜色 */
.layui-layer.layui-layer-admin .layui-layer-setwin a {
    color: #000;
}

/* 最小化按钮 */
.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-min cite {
    background-color: #555;
}

/** 弹窗按钮 */
.layui-layer.layui-layer-admin .layui-layer-btn .layui-layer-btn0 {
    border-color: #1890FF;
    background-color: #1890FF;
}

/* 圆形按钮 */
.btn-circle {
    background: #1890FF;
}

/** 主题颜色 */

/** 按钮 */
.layui-btn {
    background-color: #1890FF;
}

.layui-btn.layui-btn-primary {
    background-color: #fff;
}

.layui-btn.layui-btn-normal {
    background-color: #1E9FFF;
}

.layui-btn.layui-btn-warm {
    background-color: #FFB800;
}

.layui-btn.layui-btn-danger {
    background-color: #FF5722;
}

.layui-btn-disabled, .layui-btn-disabled:hover, .layui-btn-disabled:active {
    background-color: #FBFBFB;
}

/** 开关 */
.layui-form-onswitch {
    border-color: #1890FF;
    background-color: #1890FF;
}

/** 分页插件 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #1890FF;
}

.layui-table-page .layui-laypage input:focus {
    border-color: #1890FF !important;
}

.layui-table-view select:focus {
    border-color: #1890FF !important;
}

.layui-table-page .layui-laypage a:hover {
    color: #1890FF;
}

/** 单选按钮 */
.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #1890FF;
}

/** 下拉条目选中 */
.layui-form-select dl dd.layui-this {
    background-color: #1890FF;
}

/** 选项卡 */
.layui-tab-brief > .layui-tab-title .layui-this {
    color: #1890FF;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-color: #1890FF !important;
}

/** 面包屑导航 */
.layui-breadcrumb a:hover {
    color: #1890FF !important;
}

/** 日期选择器按钮 */
.laydate-footer-btns span:hover {
    color: #1890FF !important;
}

/** 时间轴 */
.layui-timeline-axis {
    color: #1890FF;
}

/** 复选框 */
.layui-form-checked[lay-skin=primary] i {
    border-color: #1890FF;
    background-color: #1890FF;
}

.layui-form-checkbox[lay-skin=primary] i:hover {
    border-color: #1890FF;
}

/** 加载动画颜色 */
.ball-loader > span, .signal-loader > span {
    background-color: #1890FF;
}

