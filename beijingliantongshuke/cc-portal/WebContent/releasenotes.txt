---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.3#20231113-1
修改日期: 2023-11-13
修改人:  ocj
说明:
	1、等保二级，增加自动推出策略
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.3#20220914-1
修改日期: 2022-09-14
修改人:  刘杰
说明:
	1、支持远程协助开关，需要升级对应的cc-base。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220721-1
修改日期: 2022-07-21
修改人:  林佳兴
说明:
	1、登录界面新增扫码登录功能，用于H5移动座席扫码登录。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220620-1
修改日期: 2022-06-20
修改人:  陈建立
说明:
	1、增加坐席助手侧边栏配置，需配合cc-base 3.2#20220620-1版本升级
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220614-2
修改日期: 2022-06-14
修改人:  刘杰
说明:
	1、修复重复来电规则，客户本次来电距离上次来电少于X小时(配置项：SAVE_CUST_LATESTAGENT_TIMES，缓存客户最近接待坐席的时间)时，当作重复来电
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220614-1
修改日期: 2022-06-14
修改人:  郭远平
说明:
	1、加入jackson的包依赖，防止现场mars缺少jackson包导致token获取异常
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220525-1
修改日期: 2022-05-25
修改人:  刘杰
说明:
	1、增加sso单点登录接口，主要用于与微服务版本进行单点登录（http://172.16.68.179:8181/docs/cc-qhdfl/cc-qhdfl-1ds128mfv9i43）
	2、在门户首页增加通知栏，显示常用公告信息（支持通过配置项进行关闭）
	3、增加数据库监控调度，调整各接口，确保用户在登录后，数据库异常时，各接口直接返回缓存的数据，避免查询数据库，避免影响话务条操作
	4、对门户的里定时请求，如获取通知、获取公告，由于已有实时通知，加长定时获取间隔；减小请求后台接口的超时时间； 对于ws无法连接上的时候不再自动重连，需要手工刷新界面；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220517-1
修改日期: 2022-05-17
修改人:  郭远平
说明:
	1、/cc-portal/workbranch增加重定向的请求，用于重定向带token的地址，便于对接其它系统单点登录嵌入页问题
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220511-1
修改日期: 2022-05-11
修改人:  刘杰
说明:
	1、修复语音视频客服，在语音视频通话过程中，无法进行视频转移、视频咨询的问题；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220507-1
修改日期: 2022-05-07
修改人:  郭远平
说明:
	1、为了挂在第三方系统的菜单，点击第三方菜单时支持带token参数
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.2#20220422-1
修改日期: 2022-04-22
修改人:  刘杰
说明:
	1、为了避免用户在非内网情况下使用系统，通过录屏、截图等方式泄露信息，没有在企业参数配置界面关闭水印的情况下，默认都开启水印（不做任何设置时，也默认开启水印）；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号: 3.1#20220418-1
修改日期: 2022-04-21
修改人:  林佳兴
说明:
	1、新增sys-config3tab页风格；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220418-1
修改日期: 2022-04-18
修改人:  陈建立
说明:
	1、增加坐席助手提醒(情绪监控、话术引导)，需同步升级cc-base 3.1#20220418-1
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220402-1
修改日期: 2022-04-02
修改人:  刘杰
说明:
	1、增加配置项，用于配置预测式外呼的弹屏地址；对于挂载云电销菜单到企业客服下，进行预测式外呼时，可以弹屏到电销弹屏里
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220223-1
修改日期: 2022-02-23
修改人:  刘杰
说明:
	1、话务条增加结束密语操作功能
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220211-1
修改日期: 2022-02-11
修改人:  陈建立
说明:
	1、增加来电自动处理留言功能 需同步升级cc-base

---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220124-1
修改日期: 2022-01-24
修改人:  陈建立
说明:
	1、workbench?query=isCcbrWorkmode 接口增加showPhoneDirBtn 话务条开启通讯录按钮 1-是 0-否 需同步升级cc-base

---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20220121-1
修改日期: 2022-01-21
修改人:  陈建立
说明:
	1、ccbar_agent.js 弹屏时增加参数tabId,用于弹屏界面关闭指定tab

---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211230-1
修改日期: 2021-12-30
修改人:  刘杰
说明:
	1、解决通话过程中，不能点击置忙按钮的问题
	2、在客户来电接通、呼出客户接通时，将最近服务客户、客户当天呼入、呼出次数写入缓存；在cc-base里通过mars接口给ivr使用
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211217-1
修改日期: 2021-12-17
修改人:  王丹
说明:
	1、账号已在其它地方登录，点击我知道了，无响应问题修改
	2、点击查看弹屏紧急公告，会出现两个重复的tab页问题修改
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211202-1
修改日期: 2021-12-02
修改人:  许诚誉、刘杰
说明:
	1、话务条增加语音视频客服入口，在安装了云趣视频客服产品时，坐席在置忙、置闲状态下可以发送视频呼叫；该按钮入口默认为关闭状态，可以在安装产品后，通过系统-企业参数配置里开启，需要同步升级cc-base-3.1#20211202-1及以上版本。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211124-1
修改日期: 2021-11-24
修改人:  刘杰
说明:
	1、处理固话接入时，来电弹屏获取不到区号的问题；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211116-1
修改日期: 2021-11-16
修改人:  陈志宾
说明:
	1、通知，超时待跟进会话通知弹框显示404问题处理；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211111-1
修改日期: 2021-11-11
修改人:  刘杰
说明:
	1、处理切换新首页后，系统右下角通知会失效的问题；(将通知服务从cc-workbench的首页里移到cc-portal里加载)
	2、增加限制同一账号在不同地方登录的功能，(配置项放在cc-base)
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211027-1 
修改日期: 2021-10-27
修改人:  刘杰
说明:
	1、本模块增加配置项(配置项名称：支持弹屏的服务ID)，可以配置支持弹屏的服务ID(如007）；对于非本门户配置内的弹屏请求，直接返回空(如使用云电销模块接听电话的时候，ccbar请求cc-portal的弹屏服务接口，此时不会再返回弹屏地址)；ccbar会调用所有门户的弹屏服务，需要门户里控制是否返回；
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211019-1
修改日期: 2021-10-19
修改人:  刘杰
说明:
	1、坐席振铃弹屏上，优先显示来电的队列名称；队列名称为空时才显示技能组名称（原来显示技能组名称）
	2、缓存客户最近接待的坐席的有效时长，由默认50小时改为配置项（缓存格式：CC_BASE_CUST_LATESTAGENT_企业id_客户号码=坐席账号）
	3、修复客户来电时，自动设置客户未接、漏话为已处理失败的问题
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20211012-1
修改日期: 2021-10-12
修改人:  刘杰
说明:
	1、支持坐席在签入后直接隐藏自动应答入口，需要同时升级3.1#20211012-1以上版本的cc-base，在企业参数配置里设置是否隐藏自动应答
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210922-1
修改日期: 2021-09-22
修改人:  刘杰
说明:
	1、来电弹屏相关代码进行优化
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210825-1
修改日期: 2021-08-25
修改人:  许正佳
说明:
	1、国际化优化
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210816-1
修改日期: 2021-08-16
修改人:  欧诚剑
说明:
	1、新增双因子绑定。提交时如果9059开启双因子，点登录会弹出短信验证（需升级yc-login）
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210811-1
修改日期: 2021-08-11
修改人:  冼志泳
说明:
	1、当页面打开了20个，弹出的提示国际化和菜单国际化。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210726-1
修改日期: 2021-07-26
修改人:  陈志宾
说明:
	1、菜单国际化处理。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210617-1
修改日期: 2021-06-17
修改人:  刘杰
说明:
	1、门户首页查询菜单接口，限制只返回菜单类型的记录。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.1#20210611-1
修改日期: 2021-06-11
修改人:  刘杰
说明:
	1、坐席在登录时，根据坐席的所拥有的技能组类型，设置坐席的类型，voice-语音坐席，media-在线坐席，all-混合坐席
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210607-1
修改日期: 2021-06-07
修改人:  许正佳
说明:
	1、话务条国际化
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210604-1
修改日期: 2021-06-04
修改人:  叶权辉
说明:
	1、上角头像处的修改密码，增加权限控制
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210524-1
修改日期: 2021-05-24
修改人:  许正佳
说明:
	1、归属地获取不到时设置为未知
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210520-1
修改日期: 2021-05-20
修改人:  叶权辉
说明:
	1、右上角头像处新增备忘录入口
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210511-1
修改日期: 2021-05-11
修改人:  刘杰
说明:
	1、在坐席点击退出系统时，系统自动调用CC_LOGOUT_NOTICE_SERVICE服务，该服务由各项目定制模块实现，一般在单点登录到其他系统后，坐席退出时同步通知单点登录系统即将退出。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:  3.0#20210508-1
修改日期: 2021-05-08
修改人:  刘杰
说明:
	1、增加配置项：PWD_UPDATE_FLAG、PWD_UPDATE_DAYS，用于控制是否开启密码超期提醒；配置开启后，需要同时将cc-base里开启允许用户修改密码才能生效。
---------------------------------------------------------------------------------------
模块名：   cc-portal.war
版本号:   3.0#20210426-1
修改日期: 2021-04-26
修改人:  欧诚剑
说明:
	1、优化修改密码正则校验，密码最低8位
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210408-1
修改日期: 2021-04-08
修改人:   刘杰
说明:
	    1、在用户登录时会检测用户距离上次修改密码的时间是否超过90天，如果超过，进入系统后会提示用户需要修改密码；
	             密码修改时间更新由cc-workbench处理，需要依赖cc-workbench 3.0#20210408-1或以上版本	
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210323-1
修改日期: 2021-03-23
修改人:   刘杰
说明:
	    1、登录时将企业参数配置里的待跟进会话刷新频率写入到浏览器缓存，由cc-workbench控制刷新待跟进会话操作
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210204-1
修改日期: 2021-02-04
修改人:   丁泉铭、刘杰
说明:
	    1、解决坐席签入默认置忙配置项无效问题
	    2、增加支持按企业设置首页，在系统参数配置里，可以设置当前企业的门户界面
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#202101291-1
修改日期: 2021-01-29
修改人:   欧诚剑
说明:
	    1、增加登录界面，点击备案版权信息可跳到指定备案版权网址
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210121-1
修改日期: 2021-01-21
修改人:   刘杰
说明:
	    1、增加配置项AUTO_LOGOUT_TIME_OUT，用于控制前端在X分钟内没有进行任何操作时自动退出,默认为0，不开启自动退出；如现场有需要可以自行设置，设置后需要重新加载模块
---------------------------------------------------------------------------------------
	    
模块名: cc-portal
版本号:   3.0#20210120-1
修改日期: 2021-01-20
修改人:   欧诚剑
说明:
	    1、来电弹屏是否接听弹框，地区显示增加省份
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210119-1
修改日期: 2021-01-19
修改人:   刘杰
说明:
	    1、修改网页标签上的图像，之前默认取云呼的logo，改成取企业参数配置里的企业图片；与门户菜单顶部左上角的图片保持一致
	    2、门户右上角从显示系统默认头像，改成显示用户自行上传的头像
	    3、该系统在浏览器页签上的logo取企业参数配置里的图片，如果未配置则取企业logo，需要同步升级cc-base
---------------------------------------------------------------------------------------	   
模块名: cc-portal 
版本号:   3.0#20210119-1
修改日期: 2021-01-19
修改人:  陈志彬
说明:
	1、新增全局页面引入JS脚本（ccbase  引入js脚本/JS_SCRIPT配置项）
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20210105-1
修改日期: 2021-01-05
修改人:   欧诚剑
说明:
	    1.限制席间交流按企业来设置权限;
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201231-1
修改日期: 2020-12-31
修改人:   陈志宾
说明:
	    1.【英文】首页，收藏夹，提示语等存在中文问题处理;
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201228-2
修改日期: 2020-12-28
修改人:   欧诚剑
说明:
	    1.解决当席间交流和坐席助手并存时，会叠加在一起
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201228-1
修改日期: 2020-12-28
修改人:   秦朕
说明:
	    1.菜单国际化
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201225-1
修改日期: 2020-12-25
修改人:   秦朕
说明:
	    1.菜单页面国际化
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201216-1
修改日期: 2020-12-16
修改人:   丁泉铭
说明:
	    1.收藏夹弹出模式修改
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201216-1
修改日期: 2020-12-16
修改人:   许正佳
说明:
	    1.话务条增加归属地信息
---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201214-1
修改日期: 2020-12-14
修改人:   欧诚剑
说明:
	    1.呼出弹屏增加配置，是否开启呼出弹屏
---------------------------------------------------------------------------------------
模块名: cc-portal

---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201116-1
修改日期: 2020-11-16
修改人:   林岳渊
说明:
	1、修改话务条样式，置忙的时候高亮置忙按钮，置闲的时候高亮置闲按钮，与原来相反
	2、增加界面/cc-portal/portal/jump.jsp?jumpType=1，用于跳转到全媒体工作台，跳转前查询用户信息给工作台；
	      适用于一些通过单点登录到系统，直接挂载菜单到其他系统里出现工作台里拿不到用户信息的问题。



---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201105-2
修改日期: 2020-10-19
修改人:   许正佳
说明:
	1、便签兼容oracle查询语句


---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20201019-1
修改日期: 2020-10-19
修改人:   刘杰
说明:
	1、解决坐席签入后签出按钮不消失的问题,签出时平台层控制为签出按钮可用,通过portal控制,ccbar_agent.js


---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20200720-1
修改日期: 2020-07-20
修改人:   刘杰
说明:
	1、增加my_i18n.js文件, 用于存放系统菜单的国际化内容
	2、修改菜单展示的地址，先根据菜单用国际化库里获取显示内容


---------------------------------------------------------------------------------------
模块名: cc-portal
版本号:   3.0#20200113-1
修改日期: 2020-01-13
修改人:   刘杰
说明:
	1、增加配置项PORTAL_TITILE、PORTAL_LOGO，可以配置首页的门户标题、门户logo图片路径
