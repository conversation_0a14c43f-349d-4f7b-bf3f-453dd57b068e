<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>个人收藏</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" class="form-horizontal form-inline" data-mars="bookMark.record" autocomplete="off">
		<input type="hidden" name="ID" id="ID" value="${param.ID}">
		<table class="table table-edit table-vzebra mt-20 mb-50">
			<tbody>
			<tr>
				<td class="layui-col-md2"><span class="required" i18n-content="网站名称"></span></td>
				<td>
					<div class="input-group input-group-sm" style="width: 100%;">
						<input type="text" name="NAME" id="NAME" data-rules="required" class="form-control input-sm" style="width: 100%;">
					</div>
				</td>
			</tr>
			<tr>
				<td class="layui-col-md2"><span class="required" i18n-content="网站链接"></span></td>
				<td>
					<input type="text" name="URL" id="URL" value="" class="form-control input-sm" data-rules="required" style="width: 100%;" >
				</td>
			</tr>
			<tr>
				<td class="layui-col-md2"><span class="required" i18n-content="打开方式"></span></td>
				<td>
					<select name="OPEN_TYPE" id="OPEN_TYPE" class="form-control input-sm" style="width:100%" data-rules="required">
						<option value="01" i18n-content="弹出层"></option>
						<option value="02" i18n-content="独立页签"></option>
						<option value="03" i18n-content="新窗口"></option>
					</select>
				</td>
			</tr>
			<tr>
				<td class="layui-col-md2" i18n-content="是否公开"></td>
				<td>
					<select name="IS_PUBLIC" id="IS_PUBLIC" class="form-control input-sm" style="width:100%">
						<option value="N" i18n-content="否"></option>
						<option value="Y" i18n-content="是"></option>
					</select>
				</td>
			</tr>
			<tr>
				<td class="layui-col-md2" i18n-content="备注"></td>
				<td>
					<textarea name="BAKUP" id="BAKUP" class="form-control input-sm" style="width: 100%;height:120px" i18n-placeholder="可记录账号名称密码..."></textarea>
				</td>
			</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button type="button" id="saveBtn" class="btn btn-primary btn-sm" onclick="bookMarkAdd.save()" i18n-content="保存">  </button>
			<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)" i18n-content="关闭">  </button>
		</div>
	</form>
</EasyTag:override>
<script type="text/javascript" src="/cc-portal/static/js/my_i18n.js?2020071711"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711"></script>
<EasyTag:override name="script">
	<script type="text/javascript">

		var bookMarkAdd = {
			save : function () {
				if(form.validate("easyform")){
					var id =$("#ID").val();
					var url = "${ctxPath}/servlet/bookMark?action=addRecord";
					if(id && id!='undefined') {
						url = "${ctxPath}/servlet/bookMark?action=updateRecord";
					}
					var data = form.getJSONObject("easyform");
					ajax.remoteCall(url, data, function (result) {
						if(result.state == 1){
							layer.msg(result.msg,{icon:1},function(){
								$('layui-layer-setwin').click();
								location.reload();
								popup.layerClose("#easyform");
							});
						}else{
							layer.alert(result.msg,{icon: 5, title: getI18nValue('确定提示'),btn: [getI18nValue('确定'),getI18nValue('取消')]});
						}
					});
				}
			},
		}

		$(function(){
			$("#easyform").render({success:function(result){
				if(result['bookMark.record'].isAdmin == false){
					$("#IS_PUBLIC option[value='Y']").remove();
				}
				if('${param.isMine}'=='0'){
					$("input").attr("disabled","disabled");
					$("select").attr("disabled","disabled");
					$("textarea").attr("disabled","disabled");
					$("#saveBtn").hide();
				}
			}});
		})
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>