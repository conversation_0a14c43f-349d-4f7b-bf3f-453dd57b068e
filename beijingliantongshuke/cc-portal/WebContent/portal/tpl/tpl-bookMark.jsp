<%@page import="com.yunqu.ccportal.utils.RoleUtil" %>
    <%@ page language="java" contentType="text/html;charset=UTF-8"%>
        <%@ include file="/pages/common/global.jsp" %>
                <EasyTag:override name="head">
                    <title>收藏夹</title>
                    <meta charset="utf-8" />
                    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
                    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
                    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css" />
                    <link rel="stylesheet" href="../../portal/module/admin.css" />
                    <style>
                        html,
                        body {
                            background-color: #F8F8F8;
                        }
                        
                        .note-wrapper {
                            padding-left: 15px;
                            padding-top: 20px;
                            margin-bottom: 10px;
                        }
                        
                        .note-item {
                            width: auto;
                            height: 100px;
                            display: block;
                            margin: 0 6px 15px 0;
                            padding: 13px;
                            border: 1px solid #dddddd;
                            border-radius: 8px;
                            background-color: #ffffff;
                            position: relative;
                            cursor: pointer;
                        }
                        
                        .note-item:hover {
                            background-color: #f1f1f1;
                        }
                        
                        .note-item .note-item-content {
                            font-size: 14px;
                            color: #666666;
                            overflow: hidden;
                            word-wrap: break-word;
                        }
                        
                        .note-item .note-item-time {
                            font-size: 12px;
                            color: #999999;
                            margin-top: 10px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            width: 100%;
                        }
                        
                        .note-empty {
                            text-align: center;
                            color: rgba(0, 0, 0, .45);
                            padding: 73px 0 88px;
                            display: none;
                        }
                        
                        .note-empty .layui-icon {
                            margin-bottom: 10px;
                            display: inline-block;
                            font-size: 60px;
                        }
                        
                        .note-item-del {
                            position: absolute;
                            right: 3px;
                            top: 3px;
                            display: none;
                            color: #FF5722;
                        }
                        
                        .note-item-btn {
                            position: absolute;
                            right: 3px;
                            display: none;
                            bottom: 3px;
                        }
                        
                        .note-item-del.show {
                            display: inline-block;
                        }
                        
                        .note-item-btn.show {
                            display: inline-block;
                        }
                        
                        .note-item-del .layui-icon {
                            font-size: 22px;
                        }
                    </style>
                </EasyTag:override>
                <EasyTag:override name="content">
                    <div class="note-wrapper"></div>
                    <div class="note-empty">
                        <i class="layui-icon layui-icon-face-surprised"></i>
                        <div i18n-content="没有网址"></div>
                    </div>
                    <EasyTag:res resId="cc-portal-btnauth-bookMark-add">
                    	<div class="btn-circle" id="btnAdd" i18n-title="添加网址"><i class="layui-icon layui-icon-add-1"></i></div>
                    </EasyTag:res>
                </EasyTag:override>

                <script type="text/javascript" src="/cc-portal/static/js/my_i18n.js?2020071711"></script>
                <script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711"></script>
                <script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
                <script type="text/javascript" src="../../portal/js/common.js"></script>
                <EasyTag:override name="script">
                    <script>
                        layui.use(['layer', 'form', 'util'], function() {
                            var $ = layui.jquery;
                            var layer = layui.layer;
                            var util = layui.util;
                            var admin = layui.admin;

                            renderList(); // 渲染列表

                            // 添加
                            $('#btnAdd').click(function() {
                                showNote('', "1");
                            });
                            // 显示编辑弹窗
                            function showNote(id, isMine) {
                            	top.popup.layerShow({
                                    id: 'layer-bookMark-item-edt',
                                    title: getI18nValue('个人收藏'),
                                    type: 2,
                                    area: ['600px','60%'],
                                    offset: '100px',
                                    shadeClose: true,
                                    url: '/cc-portal/portal/tpl/bookMarkAdd.jsp?ID=' + id + '&isMine=' + isMine,
                                    end: function() {
                                        renderList();
                                    }
                                });
                            }

                            // 渲染列表
                            function renderList() {
                                $('.note-wrapper').empty();
                                //请求数据
                                ajax.daoCall({
                                    controls: ['bookMark.list'],
                                    params: {}
                                }, function(result) {
                                    var dataList = result['bookMark.list'].data;

                                    if (dataList == undefined) {
                                        dataList = [];
                                    }
                                    for (var i = 0; i < dataList.length; i++) {
                                        var item = dataList[i];
                                        var str = '<div class="note-item" data-id="' + item.ID + '" data-type="' + item.OPEN_TYPE + '" data-ismine="' + item.IS_MINE + '">';
                                        str += '<div class="note-item-content">' + item.NAME + '</div>';
                                        str += '<div class="note-item-time">' + item.URL + '</div>';
                                        str += '<span class="note-item-del" onClick="event.cancelBubble=true"><i class="layui-icon layui-icon-close-fill"></i></span>';
                                        str += '<button type="button" class="btn btn-sm btn-default note-item-btn" onClick="event.cancelBubble=true"><span i18n-content="快速访问"></span></button>';
                                        str += '</div>';
                                        $('.note-wrapper').prepend(str);
                                    }
                                    execI18n();

                                    if (dataList.length == 0) {
                                        $('.note-empty').css('display', 'block');
                                    } else {
                                        $('.note-empty').css('display', 'none');
                                    }

                                    // 点击修改
                                    $('.note-item').click(function() {
                                        var id = $(this).data('id');
                                        var isMine = $(this).data('ismine');
                                        showNote(id, isMine);
                                    });

                                    // 鼠标经过显示删除按钮 和 访问按钮
                                    $('.note-item').mouseenter(function() {
                                        $(this).find('.note-item-del').addClass('show');
                                        $(this).find('.note-item-btn').addClass('show');
                                    });
                                    $('.note-item').mouseleave(function() {
                                        $(this).find('.note-item-del').removeClass('show');
                                        $(this).find('.note-item-btn').removeClass('show');
                                    });

                                    // 点击删除
                                    $('.note-item-del').click(function() {
                                        if ($("#isMine").val() == '0' && $(this).parents('.note-item').data('ismine') == '0') {
                                            layer.msg(getI18nValue('只有管理员才能删除公共便签！'), {
                                                icon: 5
                                            });
                                            return;
                                        }
                                        var id = $(this).parent().data('id');
                                        layer.confirm(getI18nValue('是否移除该收藏?'), {
                                            icon: 3,
                                            title: getI18nValue('确定提示'),
                                            btn: [getI18nValue('确定'),getI18nValue('取消')],
                                            offset: '20px'
                                        }, function(index) {
                                            var url = "${ctxPath}/servlet/bookMark?action=delRecord";
                                            ajax.remoteCall(url, {
                                                ID: id
                                            }, function(result) {
                                                var state = result.state;
                                                if (state == '1') {
                                                    layer.msg(result.msg, {
                                                        icon: 1,
                                                        time: 1200
                                                    });
                                                    renderList();
                                                } else {
                                                    layer.alert(result.msg, {
                                                        icon: 5,
                                                        title: getI18nValue('确定提示'),
                                                        btn: [getI18nValue('确定'),getI18nValue('取消')],
                                                    });
                                                    return;
                                                }
                                            });
                                        })
                                    });

                                    // 点击访问
                                    $('.note-item-btn').click(function() {
                                        var type = $(this).parent().data("type");
                                        var url = $(this).parent().find(".note-item-time").text();
                                        var title = $(this).parent().find(".note-item-content").text();
                                        if (type == "01") { // 01-弹出层 02-独立页签 03-新窗口
                                            top.popup.layerShow({
                                                type: 2,
                                                title: title,
                                                area: ['80%', '80%'],
                                                maxmin: true,
                                                full: true,
                                                shadeClose: false,
                                                moveOut: true
                                            }, url);
                                        } else if (type == "02") {
                                            popup.openTab({
                                                url: url,
                                                title: title
                                            });
                                        } else if (type == "03") {
                                            window.open(url, 'newwindow', 'toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no');
                                        }
                                    });
                                })
                            }
                        });
                    </script>
                </EasyTag:override>
                <%@ include file="/pages/common/layout_list.jsp" %>