<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>消息</title>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../portal/module/admin.css"/>
    <style>
        /** 消息列表样式 */
        .message-list {
            position: absolute;
            top: 51px;
            bottom: 44px;
            left: 0;
            right: 0;
            overflow-y: auto;
        }

        .message-list-item {
            padding: 10px 24px;
            border-bottom: 1px solid #e8e8e8;
            -ms-flex-align: start;
            align-items: flex-start;
            display: flex;
            -ms-flex: 1 1;
            flex: 1 1;
        }

        .message-list-item:hover, .message-btn-clear:hover, .message-btn-more:hover {
            background: #F2F2F2;
        }

        .message-item-icon {
            width: 40px;
            height: 40px;
            margin-right: 16px;
            display: block;
            margin-top: 4px;
        }

        .message-item-right {
            display: block;
            flex: 1 0;
            line-height: 24px;
        }

        .message-item-title {
            font-size: 14px;
            color: rgba(0, 0, 0, .65);
        }

        .message-item-text {
            color: rgba(0, 0, 0, .45);
            font-size: 12px;
        }

        .pull-right {
            float: right;
        }

        .message-btn-clear, .message-btn-more {
            display: block;
            padding: 10px 5px;
            text-align: center;
            line-height: 24px;
            color: #333;
        }

        .message-btn-clear {
            position: absolute;
            bottom: 0;
            left: 0;
            background: white;
            right: 0;
            border-top: 1px solid #e8e8e8;
        }

        .message-btn-more {
            color: #666;
            font-size: 13px;
        }

        .message-list-empty {
            text-align: center;
            color: rgba(0, 0, 0, .45);
            padding: 73px 0 88px;
        }

        .message-list-empty img {
            height: 76px;
            margin-bottom: 16px;
        }

        /** //消息列表样式结束 */
    </style>
</EasyTag:override>
<EasyTag:override name="content">

<div class="layui-tab layui-tab-brief" style="padding: 5px 0;margin: 0;">

    <ul class="layui-tab-title" style="text-align: center;">
        <li class="layui-this" style="width:50%"><span  i18n-content="紧急公告(0)"></span></li>
        <li style="width:50%"  i18n-content="备忘录(0)"></li>
    </ul>

    <div class="layui-tab-content" style="padding: 5px 0;">
        <!-- 紧急公告 -->
        <div class="layui-tab-item layui-show">
            <div class="message-list">
            	<span id="afficheList"></span>
				<script type="text/x-jsrender" id="affiche-tpl">
					<a class="message-list-item" href="javascript:;" onclick="notification.openAffiche('{{:NOTICE_ID}}','{{:NOTICE_TITLE}}')">
                    	<div class="message-item-right">
                        	<h2 class="message-item-title">{{:NOTICE_TITLE}}</h2>
                        	<p class="message-item-text">{{:PUBLISH_TIME}}</p>
                    	</div>
						<span class="layui-badge pull-right" name="{{:NOTICE_ID}}"   i18n-content='未读'></span>
                	</a>
				</script>
				<a id="btn-more1" class="message-btn-more" href="javascript:;"  i18n-content="加载更多"></a>
                <!-- 列表为空 -->
                <div class="message-list-empty" style="display: none;">
                    <img src="../../portal/images/img_msg_notice.svg">
                    <div   i18n-content="没有通知"></div>
                </div>
            </div>
            <a class="message-btn-clear" href="javascript:;"   i18n-content="全部标记已读"></a>
        </div>
        <!-- 备忘录 -->
        <div class="layui-tab-item">
            <div class="message-list">
            	<span id="memoList"></span>
            	<script type="text/x-jsrender" id="memo-tpl">
					<a class="message-list-item" href="javascript:;" onclick="notification.openMemo('{{:ID}}')">
                    	<img class="message-item-icon" src="../../portal/images/message.png">
                    	<div class="message-item-right">
                        	<h2 class="message-item-title"  >{{:CONTENT}}</h2>
                        	<p class="message-item-text">{{:REMIND_TIME}}</p>
                    	</div>
                	</a>
				</script>
                <a id="btn-more1" class="message-btn-more" href="javascript:;"  i18n-content="加载更多"></a>
                <!-- 列表为空 -->
                <div class="message-list-empty" style="display: none;">
                    <img src="../../portal/images/img_msg_pri.svg">
                    <div  i18n-content="没有消息"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</EasyTag:override>

<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
<script type="text/javascript" src="../../portal/js/common.js"></script>
<EasyTag:override name="script">
<script>
    layui.use(['element'], function () {
    	jQuery.namespace("notification");
        var $ = layui.jquery;
        var element = layui.element;
        var dataList = {"afficheList":[],"memoList":[],"afficheLen":0,"afficheAll":[]};

        // 加载更多按钮点击事件
        $('#btn-more1').click(function () {
            var $that = $(this);
            var id = $that.prev().attr("id").slice(0,-4)+"List";
            var tpl = $.templates("#"+$that.prev().attr("id"));
            showList(id,tpl,4);
        });

        // 全部标记已读事件
        $('.message-btn-clear').click(function () {
            $(this).css('display', 'none');
            $(this).prev().find('.message-list-item').remove();
            $(this).prev().find('.message-btn-more').remove();
            $(this).prev().find('.message-list-empty').css('display', 'block');
        	//控制提醒图标的小红点
        	parent.noticeTip("none");
          	//把已读状态更新到数据库
          	for(var i = 0; i<dataList["afficheAll"].length; i++){
          		var id = dataList["afficheAll"][i].NOTICE_ID;
	        	ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=read&noticeId="+ id, {}, function(result) {});
          	}
        });
        
        //渲染紧急公告
        var afficheRender = function(){
        	ajax.daoCall({controls: ['messageView.getAffiche'], params: {}}, function (result) {
        		dataList["afficheAll"] = result['messageView.getAffiche'].data;
        		dataList["afficheList"] = result['messageView.getAffiche'].data;
        		dataList["afficheLen"] = dataList["afficheList"].length;
        		$(".layui-tab-title li:eq(0)").text(getI18nValue("紧急公告(")+dataList["afficheList"].length+")");
        		if(dataList["afficheList"].length<1){
        			$("#affiche-tpl").next().remove();
        			$("#affiche-tpl").next().css('display', 'block');
        			$(".message-btn-clear").css('display','none');
        		}else{
	        		//显示8条
	        		showList("afficheList",$.templates("#affiche-tpl"),8);
        		}
        		execI18n();
        	})
        	
        }
        //渲染备忘录
        var memoRender = function(){
        	ajax.daoCall({controls: ['messageView.getMemo'], params: {}}, function (result) {
        		dataList["memoList"] = result['messageView.getMemo'].data;
        		$(".layui-tab-title li:eq(1)").text(getI18nValue("备忘录(")+dataList["memoList"].length+")");
        		if(dataList["memoList"].length<1){
        			$("#memo-tpl").next().remove();
        			$("#memo-tpl").next().css('display', 'block');
        		}else{
	        		//显示8条
	        		showList("memoList",$.templates("#memo-tpl"),8);
        		}
        		execI18n();
        	})
        }
        /**展示提醒条数
         * id : 数据展示位置<span>的id
         * template : 渲染的模板
         * num : 设定展示的条数
        */
        var showList = function(id,template,num){
			if(dataList[id].length>=num){
				var html = template.render(dataList[id].slice(0,num));
				dataList[id] = dataList[id].slice(num,dataList[id].length);
				$("#"+id).html(html);
    		}else{
    			var html = template.render(dataList[id]);
    			dataList[id] = "";
    			//移除 查看更多
    			$("#"+id).next().next().remove();
    			$("#"+id).append(html);
    		}
        }
        //打开公告界面
        notification.openAffiche = function(id,title){
        	var url = "/cc-workbench/servlet/noticeInfo?action=getNoticeInfo";
        	//将公告标记为已读
        	if($("span[name='"+id+"']").text()==getI18nValue("未读")){
	        	$("span[name='"+id+"']").addClass("layui-bg-gray");
	        	$("span[name='"+id+"']").text(getI18nValue("已读"));
	        	dataList["afficheLen"]--;
	        	//把已读状态更新到数据库
	        	ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=read&noticeId="+ id, {}, function(result) {});
        	}
        	if(dataList["afficheLen"] < 1 ){
	        	//控制提醒图标的小红点
	        	parent.noticeTip("none");
        	}
        	top.popup.layerShow({type:1,title:getI18nValue(title),time:60000,offset:'r',area:['800px','100%']},url,{noticeId:id});
        }
        //打开备忘录
        notification.openMemo = function(id){
        	var url = "/cc-workbench/servlet/noticeInfo?action=getMemoInfo";
        	if(dataList["afficheLen"] < 1 ){
	        	//控制提醒图标的小红点
	        	parent.noticeTip("none");
        	}
	        top.popup.layerShow({type:2,title:getI18nValue('修改备忘录'),area:['500px','460px'],offset:'100px'},url,{Id:id,opener:"notice"});
        }
        $(function(){
        	var memo = $.templates("#memo-tpl");
        	afficheRender();
        	memoRender();
        	//消除首页的小红点
        	parent.noticeTip("none");
        })
    });
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>