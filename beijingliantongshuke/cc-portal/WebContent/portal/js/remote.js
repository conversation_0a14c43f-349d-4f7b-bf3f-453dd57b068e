// 全局变量
// 当前连接状态 0-连接正常 1-连接异常
var current_connection_status
// 上一次连接状态 0-连接正常 1-连接异常
var last_connection_status
// 控制出现异常后恢复时提示
var exception
// 上一次轮询发起的时间戳
var last_polling_timestamp
// 结束远程停止轮询
var stop_polling
// 坐席账号
var sessionId = localStorage.getItem('userAccount')
// 读取远程配置 1:开启 0：关闭
var remoteSwitch = localStorage.getItem('remoteSwitch')
// 远程recordId
var guId
// 控制超时弹窗
var overtimeIdx
// 控制等待客户响应远程弹窗
var waitAcceptIdx

// 生成唯一id
function guid() {
    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 时间格式化
Date.prototype.format = function(format) {
    var o = {
        "M+": this.getMonth() + 1, //month  
        "d+": this.getDate(), //day  
        "h+": this.getHours(), //hour  
        "m+": this.getMinutes(), //minute  
        "s+": this.getSeconds(), //second  
        "q+": Math.floor((this.getMonth() + 3) / 3), //quarter  
        "S": this.getMilliseconds() //millisecond  
    }
    if (/(y+)/.test(format)) format = format.replace(RegExp.$1,
        (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(format))
            format = format.replace(RegExp.$1,
                RegExp.$1.length == 1 ? o[k] :
                ("00" + o[k]).substr(("" + o[k]).length));
    return format;
}

// 拉起主控端
function wakeupSip(address) {
    if (address) {
        var iframe = `<iframe src="${address}">`
        $('body').append(iframe)
        var data = {
            event:'loginRemote',
            sessionId: localStorage.getItem('userAccount'),
            state: 0,
            time:new Date().format("yyyy-MM-dd hh:mm:ss")
        }
        // 上传登录事件到后台
        event2back(data)
        // 发送心跳到主控端
        deliverHeartbeat()
    }
}

if (remoteSwitch == '1') {
    wakeupSip('yunquremote://' + sessionId)
}
// jsonp请求
function doJsonp(url,data,callBack,errBack){
	$.ajax({
		url:url,
 	    dataType: 'jsonp',
        jsonp: "callbackFunc",
        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
        data: {
			'cmdJson': JSON.stringify(data)
		},
 	    success:function(res){
            callBack && callBack(res)
 	    },
 	    error:function(res){
            errBack && errBack(res)
 	    }
	});
}
// ajax请求
function httpAjax(url, type, dataJson, callBack, errBack, canBack) {
    return $.ajax({
        type: type,
        url: url,
        data: dataJson,
        cache: false,
        dataType: 'json',
        success: function (res) {
            if (res.state == 1) {
                callBack && callBack(res);
            } else if (typeof res.state == 'undefined') {
                callBack && callBack(res)
            } 
        },
        error: function(res) {
            errBack && errBack(res)
        },
        complete: function() {
            canBack && canBack()
        }
    })
}

function popUp(custSessionId) {
    if (current_connection_status == 1 && last_connection_status == 1) {
        layer.msg('远程服务未连接，请检查主控端是否正常安装、启动，网络是否正常!', {icon: 2,offset:'30px'});
        return
    } else {
        layer.prompt({
            formType: 2, // 0是普通输入框，1是密码输入框，2是文本域输入框，默认值是0
            value:custSessionId, // 输入框初始值
            title:"请输入客户识别码",
            btn: ['请求远程','取消'],
            area: ['300px', '30px']
        },function(value,index) {
            layer.close(index);
            overtimeIdx = setTimeout(function() {
                layer.msg('等待超时,可能客户未及时接受远程邀请或网络异常', {icon: 2,offset:'30px'}); 
            },60000)
            waitAcceptIdx = layer.msg('等待客户接受中，请稍后...', { icon: 16, shade: 0.01,shadeClose:false,time:60000,scrollbar:false,area: ['360px', '170px'],offset:'rb',btn:['关闭']});
            
            remote2client(value,function(respCode) {
                // 收到远程请求结果延迟1s再关闭等待客户接受弹窗，避免一弹窗又马上消失
                setTimeout(function(){
                    layer.close(waitAcceptIdx)
                },1000)
                var reomoteResultFun = function() {
                    switch (respCode) {
                        case '000':
                            layer.msg('客户接受了远程邀请', {icon: 1,offset:'30px'}); 
                            break;
                        case '101':
                            layer.msg('客户拒绝了远程邀请', {icon: 2,offset:'30px'}); 
                            break;
                        case '104':
                            layer.msg('客户正在远程，无法发起远程', {icon: 2,offset:'30px'}); 
                            break;
                        case '105':
                            layer.msg('坐席正在远程，无法发起远程', {icon: 2,offset:'30px'}); 
                            break;
                        case '106':
                            layer.msg('客户识别码不存在，无法发起远程', {icon: 2,offset:'30px'}); 
                            break;
                        case '107':
                            layer.msg('客户未注册，无法发起远程', {icon: 2,offset:'30px'}); 
                            break;
                        case '108':
                            layer.msg('客户不在线，无法发起远程', {icon: 2,offset:'30px'}); 
                            break;
                        case '401':
                            layer.msg('磐石平台断开', {icon: 2,offset:'30px'}); 
                            break; 
                        case '402':
                            layer.msg('向日葵连接断开', {icon: 2,offset:'30px'}); 
                            break;        
                        default:
                            layer.msg('无法发起远程'+respCode, {icon: 2,offset:'30px'});
                            break;
                    }
                    // 如果收到了远程申请结果,则清除超时的弹窗
                    overtimeIdx && clearTimeout(overtimeIdx)
                }
                // 延迟2s再提示远程结果
                setTimeout(reomoteResultFun,2000)
                
            })
        });
 }
    
}
// 申请远程客户端
function remote2client(custSessionId,callBack) {
    guId = guid()
    var data = {
            cmd: 'applyRemote',
            sessionId: localStorage.getItem('userAccount'),
            custSessionId: custSessionId,
            recordId: guId,
    }
    doJsonp('http://127.0.0.1:8911/remoteMaster/openApi?ReceiverGOR=26',data, function(res) {
        var data2 = {
            event:'applyRemote',
            sessionId: localStorage.getItem('userAccount'),
            custSessionId: custSessionId,
            recordId: guId,
            time:new Date().format("yyyy-MM-dd hh:mm:ss")
        }
        if (res.respCode == '000') {
            event2back(data2)
            getProcessEvent(callBack)
        }
    },function() {
        if (current_connection_status != 1 && last_connection_status != 1) {
            layer.msg('请检查远程主控端是否正常安装、是否已启动、网络连接是否正常...',{icon: 2,offset: '30px'}); 
        }
        current_connection_status = 1
        last_connection_status = 1
    })
}

// 每隔30s推送一次心跳
function deliverHeartbeat() {
    var data = {
        cmd: 'heartbeat',
        sessionId: localStorage.getItem('userAccount'),
    }
    var heartbeatFun = function() {
        doJsonp('http://127.0.0.1:8911/remoteMaster/openApi/messageDeliver?ReceiverGOR=26',  data, function(res) {
            if (res.respCode != '000') {
                if (current_connection_status != 1 && last_connection_status != 1) {
                    layer.msg('您的远程服务连接断开，请检查网络连接是否正常，错误码为['+res.respCode+']',{icon:2,offset: '30px'})
                }
                current_connection_status = 1
                last_connection_status = 1
                exception = true
                setTimeout(heartbeatFun,10000)
            } else {
                current_connection_status = 0
                last_connection_status = 0
                if (exception) {
                    layer.msg('远程服务连接已恢复!',{icon:1,offset: '30px'})
                    exception = false
                }
                setTimeout(heartbeatFun,30000)
            }
        },function() {
            if (current_connection_status != 1 && last_connection_status != 1) {
                layer.msg('请检查远程主控端是否正常安装、是否已启动、网络连接是否正常...',{icon: 2,offset: '30px'}); 
            }
            current_connection_status = 1
            last_connection_status = 1
            exception = true
            setTimeout(heartbeatFun,10000)
        })
    }
    
    setTimeout(heartbeatFun,30000)

}

// 轮询事件
function getProcessEvent(callBack) {
    stop_polling = false
    var data = {
        cmd: 'longpolling',
        sessionId: localStorage.getItem('userAccount'),
    }
    var eventFun = function() {
        doJsonp('http://127.0.0.1:8911/remoteMaster/openApi?ReceiverGOR=26',data,function(res) {
                if (res.events.length == 0 && !stop_polling) {
                    setTimeout(eventFun,2000)
                } else {
                    res.events.forEach(item=>{
                        event2back(item)
                        if (item.event == 'endRemote' && item.recordId == guId) {
                            stop_polling = true
                        }
                        if (item.event == 'respRemote') {
                            callBack && callBack(item.respCode)
                        }
                        if (item.event == 'respRemote' && item.respCode != '000') {
                            stop_polling = true
                        }
                    })
                    if (!stop_polling) {
                        setTimeout(eventFun,500)
                    }
                }
        },function() {
            var data = {
                event:'endRemote',
                sessionId: localStorage.getItem('userAccount'),
                custSessionId: custSessionId,
                recordId: guId,
                clearCause:99,
                time:new Date().format("yyyy-MM-dd hh:mm:ss")
            }
            event2back(data)
        })
    }
    eventFun()
}

// 呼叫中心前端将事件转存后端
function event2back(res) {
    var data = {
        cmdJson:JSON.stringify(res)
    }
    httpAjax('/cc-media/servlet/remoteEvent?action=event','POST', data, res => {
        console.log('event2backRes',res);
    })
}



