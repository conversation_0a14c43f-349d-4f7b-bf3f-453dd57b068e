/** 透明侧边栏导航 */
.layui-layout-admin .layui-side .layui-nav {
    background-color: transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(59, 116, 255, .02);
}

/** logo部分样式 */
.layui-layout-admin .layui-header .layui-logo {
    background-color: #fff;
    color: #666;
}

/** header样式 */
.layui-layout-admin .layui-header {
    background-color: #fff;
}

.layui-layout-admin .layui-header a {
    color: #555;
}

.layui-layout-admin .layui-header a:hover {
    color: #555;
}

/** header里面三角箭头 */
.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #666 transparent transparent;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #666;
}

/** header线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #3B74FF;
}

/** 侧边栏样式 */
.layui-layout-admin .layui-side {
    background-color: #fff;
}

/** 侧边栏文字颜色 */
.layui-side .layui-nav .layui-nav-item a {
    color: #555;
}

.layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a, .layui-nav-tree .layui-this > a:hover {
    background: rgba(59, 116, 255, .03);
    color: #3B74FF !important;
}

.layui-nav-tree .layui-nav-bar {
    background-color: #3B74FF;
    width: 4px;
}

.layui-side .layui-nav-itemed > a, .layui-nav-tree .layui-nav-title a, .layui-nav-tree .layui-nav-title a:hover {
    color: #555 !important;
}

.layui-layout-admin.admin-nav-mini .layui-side .layui-nav li.layui-nav-itemed > a {
    background: rgba(59, 116, 255, .03);
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-itemed > .layui-nav-child {
    background-color: #fff !important;
}

/** PC端折叠鼠标经过样式 */
.layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child:before {
    background: #fff !important;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child > dd > a .layui-nav-more {
    border-color: transparent transparent transparent #999 !important;
}

/** 移动设备样式 */
@media screen and (max-width: 750px) {
    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav li.layui-nav-itemed > a {
        background: transparent;
    }
}

/** 侧边栏小三角样式 */
.layui-layout-admin .layui-side .layui-nav .layui-nav-more {
    border-color: #999 transparent transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-itemed > a .layui-nav-more {
    border-color: transparent transparent #999;
}

.layui-nav.arrow3 .layui-nav-itemed > a > .layui-nav-more:before {
    background-color: #888;
}

/** tab下划线 */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    background-color: #2486FF;
    /*top: 38px;*/
}

/** 主体标题 */
.layui-body-header-title {
    border-left-color: #2486FF;
}

/** 主题切换 */
.btnTheme:hover, .btnTheme.active {
    border-color: #2486FF;
}

/** admin风格弹窗样式 */
.layui-layer.layui-layer-admin .layui-layer-title {
    background-color: #fff;
    color: #333;
    border-bottom: 1px solid #eee;
}

/** 按钮颜色 */
.layui-layer.layui-layer-admin .layui-layer-setwin a {
    color: #000;
}

/* 最小化按钮 */
.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-min cite {
    background-color: #555;
}

/** 弹窗按钮 */
.layui-layer.layui-layer-admin .layui-layer-btn .layui-layer-btn0 {
    border-color: #2486FF;
    background-color: #2486FF;
}

/* 圆形按钮 */
.btn-circle {
    background: #2486FF;
}

/** 主题颜色 */

/** 按钮 */
.layui-btn:not(.layui-btn-primary):not(.layui-btn-normal):not(.layui-btn-warm):not(.layui-btn-danger):not(.layui-btn-disabled) {
    background-color: #2486FF;
}

.layui-btn.layui-btn-primary:hover {
    border-color: #2486FF;
}

/** 开关 */
.layui-form-onswitch {
    border-color: #2486FF;
    background-color: #2486FF;
}

/** 分页插件 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #2486FF;
}

.layui-table-page .layui-laypage input:focus {
    border-color: #2486FF !important;
}

.layui-table-view select:focus {
    border-color: #2486FF !important;
}

.layui-table-page .layui-laypage a:hover {
    color: #2486FF;
}

/** 单选按钮 */
.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #2486FF;
}

/** 下拉条目选中 */
.layui-form-select dl dd.layui-this {
    background-color: #2486FF;
}

/** 选项卡 */
.layui-tab-brief > .layui-tab-title .layui-this {
    color: #2486FF;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-color: #2486FF !important;
}

/** 面包屑导航 */
.layui-breadcrumb a:hover {
    color: #2486FF !important;
}

/** 日期选择器按钮 */
.laydate-footer-btns span:hover {
    color: #2486FF !important;
}

/** 时间轴 */
.layui-timeline-axis {
    color: #2486FF;
}

/** 复选框 */
.layui-form-checked[lay-skin=primary] i {
    border-color: #2486FF;
    background-color: #2486FF;
}

.layui-form-checkbox[lay-skin=primary] i:hover {
    border-color: #2486FF;
}

/** 加载动画颜色 */
.ball-loader > span, .signal-loader > span {
    background-color: #2486FF;
}
