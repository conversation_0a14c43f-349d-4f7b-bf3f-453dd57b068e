/** EasyWeb iframe v3.1.3 date:2019-07-15 License By http://easyweb.vip */

html {
    background-color: #f2f2f2
}

.layui-layout-body {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow-x: hidden;
    overflow-y: auto
}

.layui-layout-admin {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    transition: all .3s
}

.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    height: 2px;
    background-color: #03152a;
    top: 0 !important
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-item {
    line-height: 50px
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-item .layui-nav-child {
    top: 55px
}

.layui-layout-admin .layui-header .layui-nav-item .layui-icon {
    font-size: 16px
}

.layui-layout-admin .layui-header .layui-layout-left {
    left: 220px;
    padding: 0 5px;
    transition: all .3s
}

.layui-layout-admin .layui-header .layui-layout-right {
    padding: 0
}

.layui-layout-admin .layui-header {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    background-color: #fff;
    height: 50px;
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, .05);
    z-index: 1000
}

.layui-layout-admin .layui-header a {
    color: #333;
    padding: 0 15px
}

.layui-layout-admin .layui-header a:hover {
    color: #333
}

.layui-layout-admin .layui-header .layui-nav-child a {
    color: #333 !important
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #666 transparent transparent
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #666
}

.layui-layout-admin .layui-header .layui-nav-child dd {
    text-align: center
}

.layui-layout-admin .layui-header a {
    cursor: pointer
}

.layui-layout-admin .layui-header .layui-logo {
    width: 220px;
    background-color: #2b2d36;
    color: #f2f2f2;
    font-size: 18px;
    font-family: Myriad Pro, Helvetica Neue, Arial, Helvetica, sans-serif;
    font-weight: bold;
    overflow: hidden;
    line-height: 50px;
    transition: all .3s;
    white-space: nowrap;
    box-shadow: 1px 0 2px 0 rgba(0, 0, 0, .05)
}

.layui-layout-admin .layui-header .layui-logo img {
    height: 35px
}

.layui-layout-admin .layui-header .layui-logo cite {
    font-style: normal
}

.layui-layout-admin .layui-header .layui-nav-img {
    margin-right: 5px
}

.layui-layout-admin .layui-header .layui-nav-img + cite {
    margin-right: 5px;
    display: inline-block
}

.layui-header .layui-badge-dot {
    margin: -9px 0 0 11px
}

.layui-layout-admin .layui-body {
    position: absolute;
    left: 220px;
    top: 50px;
    transition: left .3s;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.layui-fluid {
    padding: 15px
}

.admin-iframe {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.layui-layout-admin .layui-footer {
    position: absolute;
    left: 220px;
    background: #fff;
    transition: all .3s;
    color: #666;
    border-top: 1px solid #eee;
    height: 43px;
    line-height: 43px;
    overflow: hidden
}

.layui-layout-admin .layui-footer a {
    color: #666
}

.layui-layout-admin .layui-footer a:hover {
    color: #333
}

.close-footer .layui-layout-admin .layui-footer {
    display: none
}

.close-footer .layui-layout-admin .layui-body {
    bottom: 0
}

.layui-layout-admin .layui-side {
    position: absolute;
    top: 50px;
    width: 220px;
    background-color: #24262f;
    transition: all .3s;
    -webkit-transition: all .3s;
    box-shadow: 1px 0 2px 0 rgba(0, 0, 0, .05);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    z-index: 999
}

.layui-layout-admin .layui-side .layui-side-scroll {
    width: 240px;
    transition: all .3s;
    -webkit-transition: all .3s
}

.layui-layout-admin .layui-side .layui-nav {
    width: 220px;
    background-color: transparent
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a {
    padding-top: 8px;
    padding-bottom: 8px;
    cursor: pointer
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(255, 255, 255, .03)
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item a {
    transition: none;
    -webkit-transition: none
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-itemed > .layui-nav-child {
    padding: 5px 0;
    background-color: rgba(0, 0, 0, .2) !important;
    position: static
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-more {
    right: 15px
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item .layui-nav-child a {
    padding-left: 50px;
    cursor: pointer
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item .layui-nav-child .layui-nav-child a {
    padding-left: 70px
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item .layui-nav-child .layui-nav-child .layui-nav-child a {
    padding-left: 90px
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item .layui-nav-child .layui-nav-child .layui-nav-child .layui-nav-child a {
    padding-left: 110px
}

@media screen and (min-width: 750px) {
    .layui-layout-admin.admin-nav-mini .layui-header .layui-logo {
        width: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-header .layui-logo cite {
        display: none
    }

    .layui-layout-admin.admin-nav-mini .layui-header .layui-layout-left {
        left: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-side {
        width: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-side-scroll {
        width: 80px
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav {
        width: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item > a {
        overflow: visible
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item > a > .layui-icon {
        font-size: 18px;
        transition: font-size .3s;
        -webkit-transition: font-size .3s
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item > a > cite {
        display: none
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-item > a .layui-nav-more {
        display: none
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav li.layui-nav-itemed > a {
        background: rgba(0, 0, 0, .3)
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .layui-nav-itemed .layui-nav-child {
        display: none
    }

    .layui-layout-admin.admin-nav-mini .layui-body {
        left: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-footer {
        left: 60px
    }

    .layui-layout-admin.admin-nav-mini .layui-header .layui-icon-shrink-right:before {
        content: "\e66b"
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child {
        position: fixed;
        top: 0;
        left: 60px;
        background: transparent !important;
        min-width: 150px;
        padding: 5px;
        display: block
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child:before {
        content: '';
        position: absolute;
        left: 5px;
        right: 5px;
        top: 0;
        bottom: 0;
        background: #24262f;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1)
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child dd:first-child > .layui-nav-child {
        margin-top: -5px
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child dd:last-child > .layui-nav-child.show-top {
        margin-top: 5px
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child > dd > a {
        padding: 0 20px !important
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child > dd > a .layui-nav-more {
        border-color: transparent transparent transparent rgba(255, 255, 255, .7) !important;
        right: 7px !important;
        margin-top: -6px !important
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav.arrow2 .admin-nav-hover > .layui-nav-child > dd > a .layui-nav-more {
        top: 18px !important;
        transform: rotate(-90deg) !important;
        -ms-transform: rotate(-90deg) !important;
        -moz-transform: rotate(-90deg) !important;
        -webkit-transform: rotate(-90deg) !important;
        -o-transform: rotate(-90deg) !important
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav.arrow3 .admin-nav-hover > .layui-nav-child > dd > a .layui-nav-more {
        top: 18px !important;
        transform: rotate(0deg) !important;
        -ms-transform: rotate(0deg) !important;
        -moz-transform: rotate(0deg) !important;
        -webkit-transform: rotate(0deg) !important;
        -o-transform: rotate(0deg) !important
    }

    .layui-layout-admin.admin-nav-mini .layui-side .layui-nav.arrow3 .admin-nav-hover > .layui-nav-child > dd > a .layui-nav-more:before {
        content: "\e602" !important;
        background: transparent !important;
        height: unset !important;
        width: unset !important;
        vertical-align: baseline !important
    }
}

@media screen and (max-width: 750px) {
    .layui-layout-admin {
        left: -220px
    }

    .layui-layout-admin .layui-header .layui-icon-shrink-right:before {
        content: "\e66b"
    }

    .layui-layout-admin .layui-side, .layui-layout-admin .layui-header .layui-logo {
        box-shadow: none
    }

    .layui-layout-admin.admin-nav-mini {
        left: 0;
        right: -220px
    }

    .layui-layout-admin.admin-nav-mini .layui-side, .layui-layout-admin.admin-nav-mini .layui-header .layui-logo {
        box-shadow: 1px 0 2px 0 rgba(0, 0, 0, .05)
    }

    .layui-layout-admin.admin-nav-mini .site-mobile-shade {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 220px;
        right: 0;
        background-color: rgba(0, 0, 0, .25);
        transition: background-color .3s;
        z-index: 10001;
        cursor: pointer
    }

    .layui-layout-admin.admin-nav-mini .layui-header .layui-icon-shrink-right:before {
        content: "\e668"
    }
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-content {
    padding: 0;
    position: absolute;
    left: 0;
    top: 40px;
    right: 0;
    bottom: 0
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-content > .layui-tab-item {
    position: absolute;
    bottom: 0;
    right: 0;
    top: 0;
    left: 0;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    display: block;
    visibility: hidden
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-content > .layui-tab-item.layui-show {
    visibility: visible
}

.layui-layout-admin .layui-body > .layui-tab {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: 0
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title {
    height: 40px;
    line-height: 40px;
    padding: 0 80px 0 40px;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
    position: absolute;
    right: 0;
    z-index: 999;
    border: 0;
    overflow: hidden
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li {
    min-width: 0;
    line-height: 40px;
    max-width: 160px;
    text-overflow: ellipsis;
    overflow: hidden;
    border-right: 1px solid #eee;
    vertical-align: top;
    padding: 0 30px 0 15px
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li:first-child {
    padding: 0 15px
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li cite {
    font-style: normal
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title .layui-tab-bar {
    display: none
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this, .layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li:hover {
    background-color: #eee
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    width: 100%;
    border: 0;
    height: 2px;
    background-color: #292b34;
    border-radius: 0
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li .layui-tab-close {
    width: 16px;
    height: 16px;
    line-height: 16px;
    border-radius: 50%;
    font-size: 12px;
    position: absolute;
    top: 12px;
    right: 8px
}

.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li:first-child .layui-tab-close {
    display: none
}

.layui-layout-admin .layui-body .admin-tabs-control {
    position: absolute;
    top: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    transition: all .3s;
    border-left: 1px solid #eee;
    z-index: 1000;
    background-color: white
}

.layui-layout-admin .layui-body .admin-tabs-control:hover {
    background-color: #eee
}

.layui-layout-admin .layui-body .layui-icon-prev {
    left: 0;
    border-left: none;
    border-right: 1px solid #eee
}

.layui-layout-admin .layui-body .layui-icon-next {
    right: 40px
}

.layui-layout-admin .layui-body .layui-icon-down {
    right: 0
}

.admin-tabs-select.layui-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    background: 0
}

.admin-tabs-select.layui-nav .layui-nav-item {
    line-height: 40px
}

.admin-tabs-select.layui-nav .layui-nav-item > a {
    height: 40px
}

.admin-tabs-select.layui-nav .layui-nav-item a {
    color: #666
}

.admin-tabs-select.layui-nav .layui-nav-child {
    top: 40px;
    left: auto;
    right: 0
}

.admin-tabs-select.layui-nav .layui-nav-child dd.layui-this, .admin-tabs-select.layui-nav .layui-nav-child dd.layui-this a {
    background-color: #f2f2f2 !important;
    color: #333
}

.admin-tabs-select.layui-nav .layui-nav-bar, .admin-tabs-select.layui-nav .layui-nav-more {
    display: none
}

.layui-nav.arrow2 .layui-nav-more {
    font-family: layui-icon !important;
    font-size: 12px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    width: auto;
    height: auto;
    line-height: normal;
    border: 0;
    top: 23px;
    margin: 0;
    padding: 0;
    display: inline-block;
    transition: all .1s;
    -webkit-transition: all .1s
}

.layui-nav.arrow2 .layui-nav-child .layui-nav-more {
    top: 13px
}

.layui-nav.arrow2 .layui-nav-more:before {
    content: "\e61a"
}

.layui-nav.arrow2 .layui-nav-itemed > a > .layui-nav-more {
    transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -moz-transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg);
    -o-transform: rotate(-180deg)
}

.layui-nav.arrow3 .layui-nav-more {
    font-family: layui-icon !important;
    font-size: 12px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    width: auto;
    height: auto;
    line-height: normal;
    border: 0;
    top: 23px;
    margin: 0;
    padding: 0;
    display: inline-block;
    transition: all .1s;
    -webkit-transition: all .1s
}

.layui-nav.arrow3 .layui-nav-child .layui-nav-more {
    top: 13px
}

.layui-nav.arrow3 .layui-nav-more:before {
    content: "\e654"
}

.layui-nav.arrow3 .layui-nav-itemed > a > .layui-nav-more {
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    width: 12px;
    text-align: center
}

.layui-nav.arrow3 .layui-nav-itemed > a > .layui-nav-more:before {
    content: '';
    width: 8px;
    height: 2px;
    background-color: rgba(255, 255, 255, .7);
    display: inline-block;
    vertical-align: middle
}

.page-loading {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 19891017;
    background-color: #fff
}

.page-no-scroll {
    overflow: hidden;
    min-height: 80px
}

.rubik-loader, .ball-loader, .signal-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%)
}

.ball-loader > span, .signal-loader > span {
    background-color: #4aca85;
    display: inline-block
}

.ball-loader > span:nth-child(1), .ball-loader.sm > span:nth-child(1), .signal-loader > span:nth-child(1), .signal-loader.sm > span:nth-child(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s
}

.ball-loader > span:nth-child(2), .ball-loader.sm > span:nth-child(2), .signal-loader > span:nth-child(2), .signal-loader.sm > span:nth-child(2) {
    -webkit-animation-delay: .1s;
    animation-delay: .1s
}

.ball-loader > span:nth-child(3), .ball-loader.sm > span:nth-child(3), .signal-loader > span:nth-child(3), .signal-loader.sm > span:nth-child(3) {
    -webkit-animation-delay: .15s;
    animation-delay: .15s
}

.ball-loader > span:nth-child(4), .ball-loader.sm > span:nth-child(4), .signal-loader > span:nth-child(4), .signal-loader.sm > span:nth-child(4) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.rubik-loader {
    width: 64px;
    height: 64px;
    background-image: url(img/ic_loading.gif);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain
}

.rubik-loader.sm {
    width: 50px;
    height: 50px
}

.ball-loader > span {
    width: 20px;
    height: 20px;
    margin: 0 3px;
    border-radius: 50%;
    transform: scale(0);
    -ms-transform: scale(0);
    -webkit-transform: scale(0);
    animation: ball-load 1s ease-in-out infinite;
    -webkit-animation: 1s ball-load ease-in-out infinite
}

@-webkit-keyframes ball-load {
    0% {
        transform: scale(0);
        -webkit-transform: scale(0)
    }
    50% {
        transform: scale(1);
        -webkit-transform: scale(1)
    }
    100% {
        transform: scale(0);
        -webkit-transform: scale(0)
    }
}

@keyframes ball-load {
    0% {
        transform: scale(0);
        -webkit-transform: scale(0)
    }
    50% {
        transform: scale(1);
        -webkit-transform: scale(1)
    }
    100% {
        transform: scale(0);
        -webkit-transform: scale(0)
    }
}

.ball-loader.sm > span {
    width: 15px;
    height: 15px;
    margin: 0 2px
}

.signal-loader {
    width: 50px;
    height: 22px
}

.signal-loader > span {
    width: 8px;
    height: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    margin: 0;
    animation: signal-load 1s infinite;
    -webkit-animation: signal-load 1s infinite
}

.signal-loader > span:nth-child(2) {
    left: 14px
}

.signal-loader > span:nth-child(3) {
    left: 28px
}

.signal-loader > span:nth-child(4) {
    left: 42px
}

@keyframes signal-load {
    0% {
        height: 0
    }
    50% {
        height: 22px
    }
    100% {
        height: 0
    }
}

@-webkit-keyframes signal-load {
    0% {
        height: 0
    }
    50% {
        height: 22px
    }
    100% {
        height: 0
    }
}

.signal-loader.sm {
    width: 32px;
    height: 15px
}

.signal-loader.sm > span {
    width: 5px;
    animation: signal-load-sm 1s infinite;
    -webkit-animation: signal-load-sm 1s infinite
}

.signal-loader.sm > span:nth-child(2) {
    left: 9px
}

.signal-loader.sm > span:nth-child(3) {
    left: 18px
}

.signal-loader.sm > span:nth-child(4) {
    left: 27px
}

@keyframes signal-load-sm {
    0% {
        height: 0
    }
    50% {
        height: 15px
    }
    100% {
        height: 0
    }
}

@-webkit-keyframes signal-load-sm {
    0% {
        height: 0
    }
    50% {
        height: 15px
    }
    100% {
        height: 0
    }
}

.layui-body-header {
    padding: 13px 20px;
    color: #333;
    font-size: 16px;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    position: absolute;
    left: 0;
    right: 0;
    z-index: 998;
    display: none
}

.layui-body-header-title {
    border-left: 4px solid #009688;
    padding-left: 6px
}

.layui-body-header.show {
    display: block
}

.layui-body-header.show + div {
    position: absolute;
    left: 0;
    right: 0;
    top: 45px;
    bottom: 0;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.hide-body-title .layui-body-header.show {
    display: none
}

.hide-body-title .layui-body-header.show + div {
    top: 0
}

.layui-layer.layui-layer-adminRight {
    top: 50px !important;
    bottom: 0;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, .3) !important;
    border: none !important;
    overflow: auto
}

.layui-layer.layui-layer-admin {
    box-shadow: 1px 1px 50px rgba(0, 0, 0, .3) !important;
    border: none !important
}

.layui-layer.layui-layer-admin .layui-layer-title {
    background-color: #24262f;
    color: #fff;
    height: 50px;
    line-height: 50px;
    border: 0
}

.layui-layer.layui-layer-admin .layui-layer-setwin {
    top: 17px
}

.layui-layer.layui-layer-admin .layui-layer-setwin a {
    font-family: layui-icon !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #fff
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-close1 {
    background: 0
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-close1:before {
    content: "\1006"
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-min cite {
    background-color: #ddd
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-max {
    background: 0;
    font-size: 14px;
    padding-top: 1px
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-max:before {
    content: "\e622"
}

.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-maxmin:before {
    content: "\e758"
}

.layui-layer.layui-layer-admin .layui-layer-btn .layui-layer-btn0 {
    border-color: #009688;
    background-color: #009688
}

.layui-layer.layui-layer-admin .layui-layer-btn a {
    height: 34px;
    line-height: 34px
}

.layui-layer-admin {
    max-width: 100%
}

.layui-layer-iframe .layui-layer-content {
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.layui-layer-msg {
    border-radius: 4px !important;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
    border: none !important
}

.layui-form.toolbar .layui-form-item {
    margin-bottom: 0
}

.toolbar + table, .layui-card-body > .layui-table {
    margin: 2px 0
}

.toolbar + table + .layui-table-view, .layui-card-body > .layui-table-view {
    margin: 0
}

.mr0 {
    margin-right: 0 !important
}

.w-auto {
    width: auto
}

@media screen and (max-width: 450px) {
    .layui-form-item .w-auto + .layui-input-inline {
        margin-left: 100px
    }

    .layui-form-item .layui-inline {
        margin-bottom: 8px
    }
}

.layui-form-select .layui-input {
    padding-right: 0
}

.layui-input[type=number] {
    line-height: 38px
}

.layui-form.model-form {
    padding: 25px 30px 0 0
}

.model-form.no-padding {
    padding: 0
}

.model-form .model-form-body {
    padding: 25px 30px 0 0;
    overflow-y: auto;
    max-height: calc(100vh - 180px)
}

.model-form.no-padding .model-form-footer {
    padding-right: 30px;
    padding-top: 10px
}

.icon-btn {
    padding: 0 10px
}

.layui-btn-primary {
    border-color: #e6e6e6;
    color: #666
}

.layui-btn-group .layui-btn-primary:first-child {
    border-left: 1px solid #e6e6e6
}

.layui-btn-primary:hover {
    border-color: #c9c9c9;
    color: #333
}

.layui-btn-group .layui-btn-primary:hover {
    border-color: #c9c9c9;
    color: #333
}

.layui-btn {
    transition: none
}

.layui-btn[disabled] {
    cursor: not-allowed;
    opacity: .6
}

.layui-card .layui-card-header .layui-badge.pull-right {
    top: 50%;
    margin-top: -9px
}

.lay-big-font {
    font-size: 36px;
    color: #666;
    line-height: 36px;
    padding: 5px 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap
}

.date-icon {
    background-image: url(img/icon_date.png);
    background-repeat: no-repeat;
    background-position: right center;
    padding-right: 28px
}

.icon-search {
    background-image: url(img/icon_search.png);
    background-repeat: no-repeat;
    background-position: right center;
    padding-right: 26px
}

*[ew-href], [lay-tips] {
    cursor: pointer
}

.pull-right {
    float: right
}

.pull-left {
    float: left
}

.inline-block {
    display: inline-block
}

.text-center {
    text-align: center
}

.text-left {
    text-align: left
}

.text-right {
    text-align: right
}

.bg-white {
    background-color: white
}

.layui-link {
    color: #029789 !important;
    cursor: pointer
}

.layui-link:hover {
    opacity: .8
}

.text-muted {
    color: #c2c2c2 !important
}

.text-primary {
    color: #009688 !important
}

.text-success {
    color: #5fb878 !important
}

.text-info {
    color: #01aaed !important
}

.text-warning {
    color: #ffb800 !important
}

.text-danger {
    color: #ff5722 !important
}

.layui-text a:not(.layui-btn) {
    cursor: pointer;
    color: #01aaed
}

.layui-text a:not(.layui-btn):hover {
    opacity: .8;
    text-decoration: none
}

span .layui-icon {
    font-size: 14px;
    margin-left: 2px
}

.btn-circle {
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50%;
    background: #009688;
    color: #fff;
    position: fixed;
    right: 15px;
    bottom: 15px;
    text-align: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, .28);
    cursor: pointer
}

.btn-circle:hover {
    opacity: .8;
    color: #fff
}

.btn-circle .layui-icon {
    font-size: 24px
}

.no-scrollbar {
    overflow: hidden !important
}

.layui-disabled, .layui-disabled:hover {
    color: #545454 !important;
    background-color: #eee
}

.dropdown-menu {
    display: inline-block
}

.dropdown-menu .dropdown-menu-nav {
    display: none
}

.dropdown-menu + .dropdown-menu, .layui-btn + .dropdown-menu, .dropdown-menu + .layui-btn {
    margin-left: 10px
}

.layui-badge-rim + .layui-badge-rim {
    margin-left: 8px
}

.layui-form-select-top .layui-form-select > dl {
    top: unset;
    bottom: 42px
}

.xm-select-parent .xm-select-title div.xm-select-label > span i {
    top: 2px;
    right: 5px
}

.xm-select-parent .xm-select-title div.xm-select-label > span font {
    overflow: hidden;
    max-height: 18px;
    display: inline-block
}

.xm-select-parent dd > .xm-cz-group {
    margin-right: 22px !important
}

.xm-select-nri .xm-select-parent dd > .xm-cz-group {
    border-right: 0;
    margin-right: 0 !important;
    max-width: unset !important
}

.xm-select-nri .xm-select-parent dd > .xm-cz {
    display: none !important
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: transparent
}

::-webkit-scrollbar-track {
    background: transparent
}

::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: #c1c1c1
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8
}

@media screen and (max-width: 750px) {
    ::-webkit-scrollbar {
        width: 5px;
        height: 5px
    }
}

.mini-bar::-webkit-scrollbar {
    width: 5px;
    height: 5px
}

.layui-nav-item.admin-nav-hover > .layui-nav-child::-webkit-scrollbar {
    width: 3px;
    height: 3px
}

#ew-map-select-map {
    height: 450px
}

#ew-map-select-pois {
    height: 450px;
    overflow-x: hidden;
    overflow-y: auto
}

.ew-map-select-search-list-item {
    padding: 10px 30px 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    position: relative
}

.ew-map-select-search-list-item:hover {
    background: #f2f2f2
}

.ew-map-select-search-list-item:last-child {
    border-bottom: 0
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-title {
    font-size: 14px;
    color: #333
}

.ew-map-select-search-list-item .ew-map-select-search-list-item-address {
    font-size: 12px;
    color: gray;
    padding-top: 5px
}

.ew-map-select-search-list-item-icon-ok {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%)
}

.ew-map-select-search-list-item-icon-ok .layui-icon {
    color: #3b74ff
}

.ew-map-select-tool {
    padding: 5px 15px;
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, .05)
}

#ew-map-select-center-img {
    position: absolute;
    bottom: 50%;
    left: 50%;
    width: 26px;
    margin-left: -13px
}

#ew-map-select-center-img2 {
    position: absolute;
    left: 50%;
    top: 50%;
    font-size: 12px;
    display: inline-block;
    margin-left: -6px;
    margin-top: -7px;
    color: #3b74ff
}

.bounceInDown {
    animation: bounceInDown 500ms;
    animation-direction: alternate;
    -webkit-animation: bounceInDown 500ms;
    -webkit-animation-direction: alternate
}

@-webkit-keyframes bounceInDown {
    0%, 60%, 75%, 90%, to {
        -webkit-transition-timing-function: cubic-bezier(.215, .61, .355, 1);
        transition-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    0%, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    25% {
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -10px, 0)
    }
    50% {
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -20px, 0)
    }
    75% {
        -webkit-transform: translate3d(0, -4px, 0);
        transform: translate3d(0, -10px, 0)
    }
}

@keyframes bounceInDown {
    0%, 60%, 75%, 90%, to {
        -webkit-transition-timing-function: cubic-bezier(.215, .61, .355, 1);
        transition-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    0%, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    25% {
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -10px, 0)
    }
    50% {
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -20px, 0)
    }
    75% {
        -webkit-transform: translate3d(0, -4px, 0);
        transform: translate3d(0, -10px, 0)
    }
}

#ew-map-select-tips {
    position: absolute;
    z-index: 999;
    background: #fff;
    max-height: 430px;
    overflow: auto;
    top: 48px;
    left: 56px;
    width: 280px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
    border: 1px solid #d2d2d2
}

#ew-map-select-tips .ew-map-select-search-list-item {
    padding: 10px 15px 10px 35px
}

.ew-map-select-search-list-item-icon-search {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%)
}

.ew-map-select-search-list-item-icon-search .layui-icon {
    color: #ccc
}

.table-tool-mini .layui-table-view {
    position: relative
}

.table-tool-mini .layui-table-tool {
    position: absolute;
    min-height: unset;
    height: auto;
    padding: 3px 10px;
    width: auto;
    top: -38px;
    right: -1px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    border: 1px solid #e6e6e6;
    border-bottom: 0
}

.table-tool-mini .layui-table-tool .layui-table-tool-temp {
    padding-right: 0;
    display: inline-block
}

.table-tool-mini .layui-table-tool .layui-table-tool-self {
    position: static;
    display: inline-block
}

.table-tool-mini .layui-table-tool .layui-table-tool-self .layui-inline[lay-event]:first-child {
    margin: 0
}

.layui-badge-list .layui-badge {
    margin-right: 6px
}

.layui-badge-list .layui-badge {
    padding: 2px 7px;
    border: 1px solid #ccc;
    margin-bottom: 8px;
    background-color: #fafafa !important
}

body .layui-layer-tips .layui-layer-content {
    line-height: 1.5;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #303133;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2)
}

body .layui-layer-tips i.layui-layer-TipsG {
    border-width: 5px
}

body .layui-layer-tips i.layui-layer-TipsR, body .layui-layer-tips i.layui-layer-TipsL {
    top: 11px
}

body .layui-layer-tips i.layui-layer-TipsT, body .layui-layer-tips i.layui-layer-TipsB {
    left: 12px
}

body .layui-layer-tips i.layui-layer-TipsT {
    bottom: -10px
}

body .layui-layer-tips i.layui-layer-TipsT {
    border-right-color: transparent;
    border-top-style: solid;
    border-top-color: #303133
}

body .layui-layer-tips i.layui-layer-TipsB {
    top: -10px
}

body .layui-layer-tips i.layui-layer-TipsB {
    border-right-color: transparent;
    border-bottom-style: solid;
    border-bottom-color: #303133
}

body .layui-layer-tips i.layui-layer-TipsL {
    right: -10px
}

body .layui-layer-tips i.layui-layer-TipsL {
    border-bottom-color: transparent;
    border-left-style: solid;
    border-left-color: #303133
}

body .layui-layer-tips i.layui-layer-TipsR {
    left: -10px
}

body .layui-layer-tips i.layui-layer-TipsR {
    border-bottom-color: transparent;
    border-right-style: solid;
    border-right-color: #303133
}

body .layui-layer-tips.layui-table-tips .layui-layer-content {
    background: 0 0;
    padding: 0;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .12);
    line-height: 22px;
}
