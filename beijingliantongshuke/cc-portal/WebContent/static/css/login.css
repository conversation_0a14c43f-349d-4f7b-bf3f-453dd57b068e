input:-webkit-autofill{-webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;}
    input:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white inset;}
      article,aside,details,figcaption,figure,footer,header,hgroup,nav,section{display:block}audio,canvas,video{display:inline-block;*display:inline;*zoom:1}audio:not([controls]){display:none}html{font-size:100%;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}a:hover,a:active{outline:0}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{max-width:100%;width:auto\9;height:auto;vertical-align:middle;border:0;-ms-interpolation-mode:bicubic}#map_canvas img,.google-maps img{max-width:none}button,input,select,textarea{margin:0;font-size:100%;vertical-align:middle}button,input{*overflow:visible;line-height:normal}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}label,select,button,input[type="button"],input[type="reset"],input[type="submit"],input[type="radio"],input[type="checkbox"]{cursor:pointer}input[type="search"]{-webkit-appearance:textfield}input[type="search"]::-webkit-search-decoration,input[type="search"]::-webkit-search-cancel-button{-webkit-appearance:none}textarea{overflow:auto;vertical-align:top}@media print{*{text-shadow:none!important;color:#000!important;background:transparent!important;box-shadow:none!important}a,a:visited{text-decoration:underline}a[href]:after{content:" ("attr(href) ")"}abbr[title]:after{content:" ("attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100%!important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h2,h3{page-break-after:avoid}}.clear{clear:both}.clear:after{content:"\0020";visibility:hidden;display:block;height:0;clear:both}body,html{margin:0;padding:0;width:100%;height:100%;position:relative;overflow:hidden}
.page-login{
  background: #fff url(../images/login-bg.jpg) no-repeat center;
  background-size:  cover;
}
.login-panel{
  position: absolute;
  display: inline-block;
  top: 50%;
  margin-top: -220px;
  right: 20%;
}
.login-panel .login-right {
    width: 400px;
    /*background-color: #fff*/
}

.login-panel .login-right .login-box {
    padding: 40px 50px
}

.login-panel .login-right .login-box .login-title {
    position: relative;
    line-height: 1;
    font-size: 20px;
    /*font-weight: bold;*/
    text-align: center;
    letter-spacing: 1px;
    margin-bottom: 30px;
    color: #17a6f0
}

.login-panel .login-right .login-box .modeText {
    /* position: absolute;
    right: 0; */
    font-size: 12px;
    line-height: 1;
    color: #409eff;
    /* top: 100%; */
    cursor: pointer;
    margin-top: 10px;
    text-align: right;
}

.login-panel .login-right .login-box .scan {
    width: 300px;
    height: 300px;
    position: relative;
    text-align: center;
    background: #F6F7F9;
}

.login-panel .login-right .login-box .scan .qrcode {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 180px;
}

.login-panel .login-right .login-box .scan .qrcode-status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 180px;
    height: 180px;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    background: rgba(0, 0, 0, 0.6);
}

.login-panel .login-right .login-box .scan .qrcode-status .text {
    margin: 10px 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
}

.login-panel .login-right .login-box .scan .qrcode-status .refreshBtn {
    width: 110px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    margin: 0 auto;
    color: #fff;
    background: #f40;
    border-color: #f40;
    font-size: 12px;
    border-radius: 2px;
    cursor: pointer;
}

.login-panel .login-right .login-box .scan .qrcode-status img {
    width: 32px;
    height: 32px;
}

.login-panel .login-right .login-box .scan .tips {
    position: absolute;
    bottom: 12px;
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c6c6c;
}

.login-panel .login-right .login-box .scan .tips img {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.login-panel .login-right .login-box .scan .tips .scanName {
    color: #e4393c;
}

.login-form .form-input-box {
    border-radius: 6px;
    border: 1px solid #e5e5e5;
    position: relative;
    margin-bottom: 20px;
    box-sizing: border-box;
    background: #fff;
}

.login-form .form-input-box.hasIcon {
    padding-left: 40px
}

.login-form .form-input-box.code-input {
    float: left;
    width: 170px
}

.login-form .form-input-box input {
    font-size: 14px;
    line-height: 24px;
    height: 44px;
    border: 0;
    outline: none;
    width: 100%;
    box-sizing: border-box;
    background: none;
    color: #999;
    display: block;
    padding: 10px
}

.login-form .form-input-box .form-input-box-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 40px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 16px center
}

.login-form .form-input-box .form-input-box-icon.username {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAQCAYAAAAmlE46AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjc2OEM3RkFFQTVCOTExRTc5RTY5QzJFMjA3MzAzM0E1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjc2OEM3RkFGQTVCOTExRTc5RTY5QzJFMjA3MzAzM0E1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NzY4QzdGQUNBNUI5MTFFNzlFNjlDMkUyMDczMDMzQTUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NzY4QzdGQURBNUI5MTFFNzlFNjlDMkUyMDczMDMzQTUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6V8ow0AAAA/ElEQVR42pTSvQ4BQRQFYCtayTai8AAUEg0SvVUtBV5DYpXqVbIKvW5pVkOFaBHip+E1PIIzyZGMMf5u8iWYe3ZmrzHi/i2kVBkcyPH7Dnowk5vCSsiFLgwgQQMGXbkxIn22oQ55kI8xgRVsYAtTdccWtJXQo25cc3RHzcIy9L7m7NG+47cydME9FD+ESpzwS9CDDpiakMk1TxcU0wo4vSpEqcrfgsdE1b9DVB8sNsm15pr2AtTgwqcXpB0LfLcre552rPGJYgAnZbcNDaWjBgbuaow7WZqQWhlYQFoctQGjH0KizjCGpghWwP/jEoheWwRTcPgjeITkXYABACapNgvajseEAAAAAElFTkSuQmCC)
}

.login-form .form-input-box .form-input-box-icon.code {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAQCAYAAAAmlE46AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjZDOTY4NTI1QTVCOTExRTdBNDA1RkQ1QUQ4QUM0QjU1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjZDOTY4NTI2QTVCOTExRTdBNDA1RkQ1QUQ4QUM0QjU1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NkM5Njg1MjNBNUI5MTFFN0E0MDVGRDVBRDhBQzRCNTUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NkM5Njg1MjRBNUI5MTFFN0E0MDVGRDVBRDhBQzRCNTUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6jqDHgAAABMElEQVR42ozSvUsDMRjH8buiIl2cfBnEgoiDbzh1KYKDkwp2UEFEF0WHToKrq3TsVujQuqiLSFGHooMV66bdRP0D3EQEwU3Ob+B3cIT0auADySXPJXme+P0nX56j9eEI3djAu70gYY07sYMmGrjGI3bR5QocxB7esIxFHCKPOczjBfsYMgEd+uMALrGCJ+sUz1jCNLbxgA+fOwbaOfD+34LwqO2CRtAblxxXS+MeqbjABZzC1ziDKjaVi5aBNSSVvRmcYR03reoY7vCLNWQVtIpb1/kTKnY28u1H41ndzW6mxk1TDrPgGJP4bJOoHm20ZXaso6KjJWOCzNy5klcP73igJ2VexagjaBh3eNVaL/oAcijqXgVMYEz9huZy4WOxy1HCOL5xgSv1p1COLvwTYABssUGAbRBuMAAAAABJRU5ErkJggg==)
}

.login-form .form-input-box .form-input-box-icon.psw {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAQCAYAAAAmlE46AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjYxQjgyRDdGQTVCOTExRTdBNjkzQzQ3OTMwRDY5N0Q0IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjYxQjgyRDgwQTVCOTExRTdBNjkzQzQ3OTMwRDY5N0Q0Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NjFCODJEN0RBNUI5MTFFN0E2OTNDNDc5MzBENjk3RDQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NjFCODJEN0VBNUI5MTFFN0E2OTNDNDc5MzBENjk3RDQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4o3MrCAAAA0klEQVR42mJUWfeJAQ34AHEREJtC+aeBuA+ItyArYkLT1A7EvUA8CYilgVgJiKcAcQ8QdyArZEFi+wJxIBBbAPF7JPF1QLwfiI8D8VEg3oxuYyEQV6JpgoH3ULlCbE5VBuL1DLjBbiD+gE2jHAN+8AWIg3AFDtEApNETiJ9B+f+JwCC1niCNc4E4DIgZicQgtXMZgQngP1SAFPAfnx9BiaECnx9xAXdogsAKWPBo/IbPrfg02hCKjueEFKEBayB+AbIxBYhXA7EEkRqfAnEyQIABAAQDLgDPS17QAAAAAElFTkSuQmCC)
}

.login-form .form-submit {
    border-radius: 6px;
    border: 0;
    outline: none;
    background: #2f7ef5;
    font-size: 16px;
    line-height: 24px;
    padding: 10px;
    color: #fff;
    display: block;
    width: 100%;
    letter-spacing: 8px
}

.login-form .get-code-box {
    float: right;
    width: 120px;
    height: 46px
}

.login-form .get-code-box img {
    border-radius: 6px;
    display: block;
    width: 100%;
    height: 100%
}

.row-code {
    margin-bottom: 30px
}

.copy-info {
    font-size: 14px;
    color: #f5f5f5;
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 40px;
    left: 0;
    line-height: 24px;
}

@media (max-width:1000px) {
    .login-panel {
        width: 100%;
        right: inherit;
        margin-right: 0;
    }

    .login-panel .login-right {
        width: 80%;
        max-width: 400px;
        margin:0 auto;
    }
}

@media (max-width:765px) {
    .login-panel {
        /*width: 400px*/
    }

    .login-panel .login-left {
        display: none
    }
}

/*@media (max-width:420px) {
    .login-panel {
        width: 90%
    }

    .login-panel .login-right {
        width: 100%
    }

    .login-form .form-input-box.code-input {
        width: 65%
    }

    .login-form .get-code-box {
        width: 30%;
        height: 36px
    }

    .login-panel .login-right .login-box {
        padding: 30px
    }

    .login-panel .login-right .login-box .login-title {
        margin-bottom: 20px
    }

    .login-form .form-input-box input {
        padding: 5px 10px;
        height: 34px
    }
}*/

.form-submit:hover {
    background-color: #3276b1
}

body,
html {
    padding: 0;
    min-height: 600px;
    overflow-y: auto;
}

.login-panel .login-left .logo {
    max-width: 90%;
    max-height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.login-select-panel {
    display: inline-block;
    background-color: #fff;
}

.login-select-panel .login-select-header {
    padding: 16px 20px;
    font-size: 16px;
    line-height: 1;
    color: #333;
    border-bottom: 1px solid #e5e5e5
}

.login-select-panel .login-select-content {
    padding: 20px 50px;
}

.login-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.login-list li {
    display: inline-block;
    width: 134px;
    height: 150px;
    margin-right: 40px;
    background: url('/yc-login/static/images/slt-default.png') no-repeat 0 0
}

.login-list li:nth-last-child(1) {
    margin-right: 0;
}

.login-list li a {
    display: block;
    text-decoration: none;
    text-align: center;
    width: 100%;
    height: 100%;
}

.login-list li a span {
    padding-top: 104px;
    line-height: 30px;
    font-size: 14px;
    font-weight: bold;
    color: #666;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.login-list li {}

.login-list li:hover {
    background-position-y: -149.5px
}

.login-list li a:hover {
    color: #17a6f0;
    background: url('/yc-login/static/images/slt-link.png') no-repeat bottom right
}

.login-list li a:hover span {
    color: #17a6f0
}
.userLang{
	padding: 10px;
}
.userLang:hover {
    color: #77879c;
}