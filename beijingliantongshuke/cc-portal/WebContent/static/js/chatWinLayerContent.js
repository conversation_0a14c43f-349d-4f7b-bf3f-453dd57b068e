//席间聊天
var isOpenChat = false,isShowChat = false;
var isFixedChatPanel = false;
function openChatWin(flag){
	var callback_success = function(){
		isOpenChat = true;
	}
	var callback_cancel = function(){
		isOpenChat = false;
	}
	if(isShowChat){
		hideChatWin();
	}else{
		showChatWin();
	}
	
	if(isOpenChat){
		return;
	}
	//console.log('${user.loginAcct }');
	ajax.remoteCall("/cc-portal/servlet/userRes?action=getUserInfo",'',function(result) { 
		if(result.state==1){
			var menuObj = JSON.parse(result.data); 
				popup.layerShow(
						{
						id:"chatWinLayerContent",
						shade:false,
						shadeClose:false,
						type:2,
						title:"席间交流",
						area:['80%','90%'],
						offset:'auto',
						zIndex:8888,
						maxmin:false,
						closeBtn:0,
						success:callback_success,
						end:callback_cancel,
						resizing:chatWinResizingCallback
					},//agentId=8001@cc&entId=17001&busiOrderId=84433783465367299769476
				"/webim/pages/index.jsp?agentId="+menuObj.userAcc+"&entId="+menuObj.entId+"&busiOrderId="+menuObj.busiOrderId);
			isShowChat = true;
			if(flag){
				_askPanel=true;
				_askPanel_show = false;
				$(top.document.body).find('#chatWinLayerContent').parents('.layui-layer').toggle();
				$("#_askPanel_").addClass('hideExpertsAsk')
				$("#_askPanel_").parent().css({'zIndex':-1,'opacity':0});
			}
		}
	});
//popup.layerShow({type:2,title:"",area:['955px','90%'],offset:'auto',maxmin:false,closeBtn:0,move:'.chat-agent-header'},"/webim/pages/index.jsp?agentId=${userInfo.userAcc}");
}

//提供给其他页面打开席间交流并发起会话
var openLaunchChatFunc = null;
var openLaunchChat = function(userAcct){
	try {
		showChatWin();
		openLaunchChatFunc&&openLaunchChatFunc(userAcct);
	} catch (e) {
	}
}
//席间交流窗口缩放
var chatWinResizing = null;
var chatWinResizingCallback = function(layero){
	try {
		chatWinResizing&&chatWinResizing(layero);
	} catch (e) {
	}
}

//隐藏席间交流
function hideChatWin(){
	if(isFixedChatPanel){
		$(".hiddenChatPanel").hide();
		return;
	}
	isShowChat = false;
	$("#chatWinLayerContent").parent().hide();
	$(".hiddenChatPanel").hide();
}
//显示席间交流
function showChatWin(){
	isShowChat = true;
	$("#chatWinLayerContent").parent().show();
	//showUnReadNum(0);
	$(".hiddenChatPanel").show();
}

