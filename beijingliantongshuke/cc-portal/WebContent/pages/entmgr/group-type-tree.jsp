<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>类型架构</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form  id="searchForm" name="searchForm" class="form-inline">
			<input type="hidden" value="${param.childDeptKey }" name="childDeptKey"><!-- 打开子菜单，设置参数为：open -->
			<input type="hidden" name="groupType" id="groupType">
			<div class="row">
					<div  style="background-color: #fff;margin-left: 15px;width: 18%;float: left;height: 100%">
						<div style="border-bottom: 1px solid #eee;height: 52px;line-height: 52px;padding: 0px 15px"><span class="glyphicon glyphicon-home"></span> 类型架构</div>
						<div class="ztree" data-mars="GroupTypeDao.groupTypeTree" id="ztree" data-setting="{callback: {onClick: zTreeOnClick}}" style="height: 100%;overflow:auto; padding: 15px;"></div>
					</div>
					<div style="height: 450px;width: 78%;float: left;margin-left: 10px;">
						<div class="ibox ">
							<div class="ibox-title clearfix" >
								<div class="form-group">
			             		      <h5> <span style="margin-right: 5px">部门类型列表</span></h5>
					         		  <div class="input-group input-group-sm">
										 	<span class="input-group-addon">类型名称</span>	
										 	<input type="text" name="nodeTypeName" class="form-control input-sm" style="width: 128px;">
								      </div>
									  <div class="input-group input-group-sm">
										 	<button type="button" class="btn btn-sm btn-default" onclick="GroupType.searchResData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
									  </div>
									  <div class="input-group input-group-sm pull-right btn-group">
								    	 	<button type="button" class="btn btn-sm btn-success " onclick="GroupType.addData()">+添加类型</button>
							    	  </div>
								</div>
							</div>
							 <div class="ibox-content">
							 	<table class="layui-hide" id="gm"></table>
							 </div>
						</div>
					</div>
		</div>
	
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">

		jQuery.namespace("GroupType");
		var zTree, rMenu;
		$(function(){
			requreLib.setplugs('slimscroll,ztree,layui',function(){
				var height=$(window).height();
				 $('#ztree').slimScroll({  
		                height: height-100,
		                color: '#ddd'
		         });
				 initGM();
			});
			$("#searchForm").render();
		});
		
		function initGM(){
			$("#searchForm").initTable({
				url:'${ctxPath}/webcall?action=GroupTypeDao.groupTypeList',
				cols: [[
	            {
	        	 	field: 'GROUP_TYPE_NAME',
					title: '类型名称',
					align: 'left'
				},{
	        	 	field: 'GROUP_TYPE',
					title: '类型名称',
					align: 'left'
				},{
					field: 'CALL_FLAG',
					title: '话务标志',
					align: 'left',
					templet:function(row){
						if(row.CALL_FLAG == 1){
							return '话务权限 ';
						}else{
							return '无话务权限';
						}
						return '<a href="javascript:void(0)" onclick="GroupType.delData(\''+row.GROUP_TYPE+'\',\''+row.GROUP_TYPE_NAME+'\')" > 删除</a>';
					}
				},{
					field: 'MAX_NODE_COUNT',
					title: '最大节点数',
					align:'left'
				},{
					field: 'GROUP_TYPE',
					title: '操作',
					align: 'center',
					templet:function(row){
						return '<a href="javascript:void(0)" onclick="GroupType.delData(\''+row.GROUP_TYPE+'\',\''+row.GROUP_TYPE_NAME+'\')" > 删除</a>';
					}
				}
				]]}
			);
		}
		
	  	function zTreeOnClick(event, treeId, treeNode){
		  	$("#groupType").val(treeNode.id);
		  	GroupType.searchResData();
	  	}
	  
	  	GroupType.searchResData = function(){
		  	$("#searchForm").queryData();
	  	}
	  
	  	GroupType.searchData = function(){
	  		$("#searchForm").render();
	  		GroupType.searchResData();
	  	}
	  	
	    GroupType.addData = function(){
	    	var groupType = $("#groupType").val();
	    	if(groupType==undefined || groupType==''){
	    		groupType = '';
	    	}
			popup.layerShow({type:1,title:'添加类型',offset:'20px',area:['475px','340px']},"${ctxPath}/pages/entmgr/group-type-edit.jsp",{pNodeType:groupType});
		}
	    
	    GroupType.delData = function(groupType){
	    	var data = {groupType:groupType}
	    	ajax.remoteCall("${ctxPath}/servlet/groupType?action=childNode", data, function(result) {
	  			if(result.state != 1){
	  				layer.alert("当前类型存在子节点，不能删除",{icon: 5});
	  				return;
				}
				layer.confirm('当前类型将要被删除，是否继续？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
					layer.close(index);
			  		ajax.remoteCall("${ctxPath}/servlet/groupType?action=delete", data, function(result) {
			  			if(result.state == 1){
						    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
						    	GroupType.searchData();
						    });
						}else{
							layer.alert(result.msg,{icon: 5});
						}
		  			});
				});
  			});
		}
	</script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>