<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>角色管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 角色权限管理</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">角色名称</span>	
									  <input type="text" name="condition" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width:100px">
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="CCRole.loadData()">
										<span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
							   <div class="input-group input-group-sm pull-right btn-group">
							       <button type="button" class="btn btn-sm btn-success btn-outline" onclick="CCRole.addData()">+新增角色</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
	              		<table id="main">
	              	    </table>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("CCRole");
		
		requreLib.setplugs("layui",function(){
			CCRole.loadData();
        });
		
		CCRole.loadData = function(){
			$("#searchForm").initTable({
				 mars:'role.list'
				,totalRow:false//显示汇总行
				,limit:15
				,page:true
				,toolbar:false
				,cols: [
					[
			         {width:90, title: '序号',type:'numbers'}
			         ,{width:200,field:'ROLE_NAME', title: '名称', templet:function(row){
			        	 return '<a  href="javascript:void(0)" onclick="CCRole.editData(\''+row.ROLE_ID+'\')">'+row.ROLE_NAME+'</a>';
			         }}
			         ,{width:94,field:'ROLE_TYPE', title: '类型', templet:function(row){
			        	 return roleType(row.ROLE_TYPE, row.SYS_FLAG);
			         }}
			         ,{field:'ROLE_DESC', title: '描述'}
			         ,{width:200,field:'ROLE_ID', title: '操作', templet:function(row){
			        	 var html = '';
			        	 if(row.SYS_FLAG != 1){
			        		 html += '<a  href="javascript:void(0)" onclick="CCRole.delData(\''+row.ROLE_ID+'\')">删除</a>&nbsp;&nbsp';
			        	 }
			        	 html += '<a  href="javascript:void(0)" onclick="CCRole.assignAgent(\''+row.ROLE_ID+'\',\''+row.ROLE_NAME+'\')">权限分配</a>&nbsp;&nbsp';
			        	 html += '<a  href="javascript:void(0)" onclick="CCRole.assignUser(\''+row.ROLE_ID+'\',\''+row.ROLE_NAME+'\')">成员管理</a>';
			        	 return html;
			         }}
			         ]
				]
			});
		}
		
		//新增角色
		CCRole.addData=function(){
			popup.layerShow({type:1,title:'角色新增',offset:'20px',area:['480px','320px']},"${ctxPath}/pages/entmgr/role-edit.jsp",null);
		}
		
		CCRole.editData=function(roleId){
		    popup.layerShow({type:1,title:'角色编辑',offset:'20px',area:['480px','280px']},"${ctxPath}/pages/entmgr/role-edit.jsp",{roleId:roleId});
		}
		
		CCRole.delData = function(roleId){
			layer.confirm('是否删除当前角色？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/servlet/role?action=delete", {roleId:roleId}, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	CCRole.loadData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		
		CCRole.assignAgent=function(roleId,roleName){
			popup.layerShow({btn: ['保存','关闭'],type:2,title:'角色资源设置('+roleName+")",offset:'20px',area:['380px','420px'],yes:function(index, layero){
				var data =treeObj.getCheckedNodes(true);
				ajax.remoteCall("${ctxPath}/servlet/role?action=updateRes&roleId="+roleId, data, function(result) {
					if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'160px'},function(){
					    	layer.closeAll();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
						layer.closeAll();
					}
				});
			}},"${ctxPath}/pages/entmgr/role-res-tree.jsp",{roleId:roleId});
		}
		
		CCRole.assignUser = function(roleId,roleName){
			popup.openTab('${ctxPath}/pages/entmgr/role-user-list.jsp','角色成员('+roleName+')',{roleId:roleId});
		}
		
		
		function roleType(roleType,sysFlag){
			if(sysFlag == 1){
				if(roleType==1){
					return "<span class='label label-info label-outline'>管理员</span>";
				}else if(roleType==2){
					return "<span class='label label-info label-outline'>班长</span>";
				}else if(roleType==3){
					return "<span class='label label-info label-outline'>坐席</span>";
				}else{
					return "自定义";
				}
			}else{
				if(roleType==1){
					return "管理员";
				}else if(roleType==2){
					return "班长";
				}else if(roleType==3){
					return "坐席";
				}else{
					return "自定义";
				}
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>