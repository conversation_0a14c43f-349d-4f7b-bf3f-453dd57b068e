<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>角色用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchUserForm" data-toggle="render">
       			<input type="hidden" name="roleId" value="${param.roleId }">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
             		          	<div class="input-group input-group-sm">
								      	<span class="input-group-addon">姓名</span>	
									  	<input type="text" name="agentName" autocomplete="off" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width:100px">
							   	</div>
							   	<div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="RoleUserSelector.loadData()">
										<span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
	              		<table id="main" lay-filter="test">
	              	    </table>
	              	</div> 
	              	
	              	<div class="layer-foot text-c" style="position: fixed;z-index: 5">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="RoleUserSelector.saveData()">确定</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   	</div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("RoleUserSelector");
		
		requreLib.setplugs("layui",function(){
			RoleUserSelector.loadData();
        });
		
		RoleUserSelector.loadData = function(){
			$("#searchUserForm").initTable({
				 mars:'role.selectRoleUser'
				,totalRow:false//显示汇总行
				,limit:10
				,page:true
				,toolbar:false
				,cols: [
					[
			         {width:90,field:'USER_ID', title: '选择',type:'checkbox'}
			         ,{field:'AGENT_NAME', title: '姓名'}
			         ,{field:'AGENT_PHONE', title: '工号'}
			         ,{field:'GROUP_LIST', title: '部门'}
			         ]
				]
			});

		}
		
		var userIds = [];
		
		RoleUserSelector.saveData = function(){

			layui.use('table', function(){
      		  	var table = layui.table;
      		    var data = table.cache.main;
      		    $.each(data,function(index,item){
      		    	if(item['LAY_CHECKED'] && item['LAY_CHECKED'] == true){
      		    		userIds.push(item.USER_ID);
      		    	}
      		    });
         	});

			var data = {userIds:userIds,roleId:'${param.roleId }'};
		  	ajax.remoteCall("${ctxPath}/servlet/role?action=addUser",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
						layer.closeAll();
						parent.RoleUser.loadData();
						popup.layerClose();
					});
				}else{
					layer.alert(result.msg);
				}
			});
			userIds = [];
			
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>