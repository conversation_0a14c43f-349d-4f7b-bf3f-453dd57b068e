<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>资源树</title>
	<link rel="stylesheet" type="text/css" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css">
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="zTreeDemoBackground left" id="entBusiTree">
		<ul id="tree" class="ztree"></ul>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		 	var setting = {
					view: {
						selectedMulti: false
					},
					check: {
						enable: true,
					}
			};
			var roleId = '${param.roleId}'
			$(document).ready(function(){
				ajax.remoteCall("${ctxPath}/webcall?action=role.getResTree",{roleId:roleId},function(result) { 
					var data = result.data;
					$.fn.zTree.init($("#tree"), setting, data);
					parent.treeObj = $.fn.zTree.getZTreeObj("tree");
				});
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>