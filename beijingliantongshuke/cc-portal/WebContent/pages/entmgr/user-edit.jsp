
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>座席账号</title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
	</style>
	</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="user.record" autocomplete="off" data-pk="${param.userId}"  method="post"  autocomplete="off" data-mars-prefix="user.">
				  <input type="hidden" name="user.USER_ID" class="form-control input-sm" value="${param.userId}">
				  <table class="table table-edit table-vzebra" >
	                    <tbody >
		                     <tr>
			                        <td width="30px">账号</td>
			                        <td><input type="text" value="${param.userAcct} "  readonly data-rules="required" class="form-control input-sm" /></td>
		                     </tr>
		                      <tr>
			                        <td class="required">姓名</td>
			                        <td><input type="text"  name="user.AGENT_NAME" data-rules="required" class="form-control input-sm readonly-class"></td>
		                     </tr>
		                     <tr>
			                        <td class="required">角色</td>
			                        <td>
			                             <select class="form-control input-sm" name="user.ROLE_ID" data-rules="required" data-mars="role.roleDict">  
			                                    <option value="">--请选择--</option>	
			                      		 </select>
			                        </td>
		                     </tr>
		                      <tr class="hidden">
			                        <td class="required">员工编码</td>
			                        <td><input type="text"  name="user.SALES_CODE" data-rules="required" class="form-control input-sm readonly-class" readonly="readonly"></td>
		                     </tr>
		                      <tr>
			                        <td>手机号码</td>
			                        <td><input type="text"  name="user.MOBILE" data-rules="mobile" class="form-control input-sm readonly-class"></td>
		                     </tr>
		                     <tr>
			                        <td>外显号码</td>
			                        <td>
			                        	<select class="form-control input-sm disable-class" id="prefixSelect2" name="user.PREFIX_NUM" data-mars="prefix.dict">
			                        		<option value="">请选择</option>	
			                        	</select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td>账号状态</td>
			                       <td >
				                        <label class="radio radio-inline radio-success">
				                        	<input type="radio" value="0" checked="checked" name="user.USER_STATE"> <span>启用</span>
				                        </label>
				                        <label class="radio radio-inline radio-success">
				                        	<input type="radio" value="1" name="user.USER_STATE"> <span>停用</span>
				                        </label>
			                        </td>
		                     </tr>
	                    </tbody>
	                  </table>
	                  <hr>
	                  
	                   <table class="table table-edit table-vzebra config hidden" >
	                      <tbody>
			                     <tr>
				                        <td width="30px">分配客户</td>
				                        <td>
				                        	<label class="radio radio-inline radio-success">
				                        		<input type="radio" value="1" checked="checked" name="agentConfig.allocCust"><span>允许</span>
					                        </label>
					                        <label class="radio radio-inline radio-success">
					                        	<input type="radio" value="0" name="agentConfig.allocCust"><span>禁止</span>
					                        </label>
				                        </td>
			                     </tr>
			                     <tr>
				                        <td>核保</td>
				                        <td>
				                        	<label class="radio radio-inline radio-success">
				                        		<input type="radio" value="1" checked="checked" name="agentConfig.checkBao"> <span>允许</span>
					                        </label>
					                        <label class="radio radio-inline radio-success">
					                        	<input type="radio" value="0" name="agentConfig.checkBao"> <span>禁止</span>
					                        </label>
				                        </td>
			                     </tr>
			                     <tr>
				                        <td>公海提取数据</td>
				                        <td>
				                        	<label class="radio radio-inline radio-success">
				                        		<input type="radio" value="1" name="agentConfig.poolAlloc"> <span>允许</span>
					                        </label>
					                        <label class="radio radio-inline radio-success">
					                        	<input type="radio" value="0"  checked="checked" name="agentConfig.poolAlloc"> <span>禁止</span>
					                        </label>
				                        </td>
			                     </tr>
			                     <tr>
				                        <td>号码加密显示</td>
				                        <td>
				                        	<label class="radio radio-inline radio-success">
				                        		<input type="radio" value="1" name="agentConfig.phoneShowEncrypt"> <span>是</span>
					                        </label>
					                        <label class="radio radio-inline radio-success" style="margin-left: 25px;">
					                        	<input type="radio" value="0" checked="checked" name="agentConfig.phoneShowEncrypt"> <span>否</span>
					                        </label>
				                        </td>
			                     </tr>
		                     </tbody>
	                  </table>
					<div class="layer-foot text-c">
				   		    <button class="btn btn-sm btn-default hidden-class"  type="button" onclick="Agent.resetPwd()">重置密码</button>
					   		<button class="btn btn-sm btn-info ml-20"  type="button" onclick="Agent.ajaxSubmitForm()">保存</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("Agent");
	
	Agent.userId='${param.userId}';
	
	$(function(){
		$("#easyform").render({success:function(result){
			fillRecord(result['user.record']['agentConfig'],"agentConfig.",",","#easyform");
			requreLib.setplugs('select2',function(){
				 $("#prefixSelect2").select2({theme: "bootstrap",openOnEnter:false});
			});
		}});
	});
	Agent.resetPwd=function(){
		layer.prompt({title:'输入密码',offset:'60px'},function(val, index){
			  if(val==''||val.length<6){
					layer.msg('密码长度需大于6位数!');
					return;
			  }
			  var data={userId:Agent.userId,pwd:val};
			  ajax.remoteCall("${ctxPath}/servlet/user?action=resetPw",data,function(result) { 
				  layer.close(index);
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,offset:'80px'});
					}else{
						layer.alert(result.msg);
					}
				  }
				);
		});
	}
	 Agent.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 Agent.updateData(); 
		 };
	}
	
	 Agent.updateData = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/user?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,offset:'80px',time:2000},function(){
					popup.layerClose("#easyform");
					CCUser.loadData({jumpPage:false});
				});
			}else{
				layer.alert(result.msg);
			}
		  }
		);
	}
	 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>