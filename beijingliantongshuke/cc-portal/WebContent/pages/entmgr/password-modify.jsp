<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@page import="com.yunqu.ccportal.base.Constants"%>
<%-- <%@ include file="/pages/common/layout_list.jsp" %> --%>
<EasyTag:override name="head">
	<title>修改密码</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="editForm" data-mars="" class="form-horizontal" style="margin-top:26px" method="post"  autocomplete="off" data-mars-prefix="" >
			   <div class="form-group">
			       <span class="col-sm-3 control-label" style="padding-right:0" i18n-content="当前密码"></span>
				   <div class="col-sm-8">
				      <input type="password" data-rules="required" name="" i18n-title="当前密码不能为空" data-original-title="当前密码" data-title="当前密码" data-toggle="rigthtip" class="form-control" id="oldPwd">
				   </div>
				</div>
				<div class="form-group">
				   <span class="col-sm-3 control-label" style="padding-right:0" i18n-content="新密码"></span>
				   <div class="col-sm-8">
				      <input type="password" data-rules="required" data-original-title="新密码不能与旧密码相同" name="" class="form-control" data-toggle="rigthtip" id="newPwd">
				   </div>
				</div>
				<div class="form-group">
				   <span class="col-sm-3 control-label" style="padding-right:0" i18n-content="确认密码"></span>
				   <div class="col-sm-8">
				      <input type="password" data-rules="required" data-original-title="确认密码必须与新密码相同" name="" class="form-control" data-toggle="rigthtip" id="confirmPwd">
				   </div>
				</div>
			    <div class="layer-foot text-c" align="center">
				   		<button class="btn btn-sm btn-primary"  type="button" onclick="savePwd()" i18n-content="保存"></button>
				   		<button class="btn btn-sm btn-default"  style="margin-left:20px" type="button"  onclick="cancel()" i18n-content="取消"></button>
			    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/cc-portal/static/js/my_i18n.js?v=5"></script>
	<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.md5.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("passwordTemp");
	    function savePwd(){
	    	if(form.validate("editForm")){
	    		var data = new Object();
	    		data.oldPwd = $("#oldPwd").val();
	    		data.newPwd = $("#newPwd").val();
	    		data.confirmPwd = $("#confirmPwd").val();
	    		if(<%=Constants.setPwdRule%>==1){
		    		var pPattern = /^.*(?=.{8,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/;
		    		if(!pPattern.test(data.confirmPwd)){
		    			$("#confirmPwd").attr("data-original-title",getI18nValue("密码至少8位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符，请重新输入密码！"));
						$("#confirmPwd").val("");
						if(form.validate("editForm")){}
						return ;
		    		}
	    		}
	    		if(data.newPwd!=data.confirmPwd){
	    			$("#confirmPwd").attr("data-original-title","确认密码和新密码不一致，请重新输入确认密码！");
	    			$("#confirmPwd").val("");
	    			if(form.validate("editForm")){}
	    			//layer.alert("确认秘密和新密码不一致，请重新输入确认密码！",{icon: 5});
	    			return ;
	    			
	    		}
	    		
				// data.oldPwd = $.md5(data.oldPwd).toLocaleUpperCase();
				//
				// data.newPwd = $.md5(data.newPwd).toLocaleUpperCase();
				
				data.confirmPwd= $.md5(data.confirmPwd).toLocaleUpperCase();
	    		
	    		//校验当前秘密是否正确
	    		ajax.remoteCall("/cc-workbench/Servlet/passwordmng?action=updatePWD",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg, {offset: 't',anim: 6},function(){
							layer.closeAll();
						});
					}else{
						layer.msg(result.msg, {offset: 't',anim: 6});
					}
				}); 
			};
	    }
	    
	    function cancel(){
			layer.closeAll();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>