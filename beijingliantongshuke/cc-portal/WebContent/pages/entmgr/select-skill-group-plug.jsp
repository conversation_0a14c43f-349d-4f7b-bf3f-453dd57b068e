<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择班组</title>
	<style>
		#dataList2 tr{cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="skillGroupSelectForm" data-user-id="${param.userId }" data-task-id="${param.taskId }" style="margin-bottom: 65px">
             	<div class="ibox">
	              	<div class="ibox-content" style="padding: 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="6" data-container="dataList2" data-template="list-template2" data-auto-fill="5" data-mars="skillGroup.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">选择</th>
								      <th>班组名称</th>
								      <th>坐席数</th>
								      <th>创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList2">
                             </tbody>
		                 </table>
                        	 <script id="list-template2" type="text/x-jsrender">
								   {{for  list}}
										<tr class="{{:SKILL_GROUP_ID}}">
											<td class="text-c"><label class="{{getType:SKILL_GROUP_ID}} {{getType:SKILL_GROUP_ID}}-info"><input type="{{getType:SKILL_GROUP_ID}}" {{if CHECKED}}checked{{/if}} {{hasCheck:SKILL_GROUP_ID}} data-name="{{:SKILL_GROUP_NAME}}" name="skillGroupIds" value="{{:SKILL_GROUP_ID}}"/><span></span></label></td>
											<td>{{:SKILL_GROUP_NAME}}</td>                                         
											<td>{{:AGENT_COUNT}}人</td>                                         
											<td>{{cutText:CREATE_TIME 12 ''}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
	                     <%-- <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp">
	                     		<jsp:param value="6" name="pageSizes"/>
	                     	</jsp:include>
	                     </div> --%>
	              	</div> 
	              	<div class="layer-foot text-c" style="position: fixed;">
	              		<c:if test="${!empty param.userId }">
	              			<button class="btn btn-sm btn-warning for-agentSys" type="button" onclick="SkillGroupSelect.delAllGroup()">移除班组</button>
	              		</c:if>
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="SkillGroupSelect.saveData()">确定</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("SkillGroupSelect");
		
		SkillGroupSelect.type=${param.type};
		SkillGroupSelect.userId = "${param.userId}";
		SkillGroupSelect.oldGroupIds = "";
		$(function(){
			$("#skillGroupSelectForm").render({success:function(result){
				$("#dataList2 tr").on('click',function(event){
					if(event.target.type == 'radio'|| event.target.type == 'checkbox') { event.stopPropagation(); return;}
					var val=$(this).find("input").prop("checked");
					$(this).find("input").prop("checked",!val);
				});
				if(SkillGroupSelect.type==2){
					$("tr[class^='ivr']").show();
				}else{
					$("tr[class^='ivr']").hide();
				}
				if(result["skillGroup.list"]){
					var list = result["skillGroup.list"].data;
					for (var i = 0; i < list.length; i++) {
						if(list[i].CHECKED){
							SkillGroupSelect.oldGroupIds += list[i].SKILL_GROUP_ID + ",";
						}
					}
					SkillGroupSelect.oldGroupIds.substring(0,ids.length-1);
				}
			}});
		});
		$.views.converters("getType", function() {
			if(SkillGroupSelect.type==1){
				return "checkbox";
			}else{
				return "radio";
			}
		});
		var ids = '${param.ids }';
		console.info(ids);
		$.views.converters("hasCheck", function(val) {
			if(ids != ''){
				var arr = ids.split(',');
				for(var i in arr){
					if(val == arr[i]){
						return "checked";
					}
				}
			}
		});
		
		SkillGroupSelect.saveData=function(){
			if(SkillGroupSelect.type==1){
				var length=$("#skillGroupSelectForm input[type='checkbox']:checked").length;
				if(length==0){
					layer.msg("请至少选择一个班组!");
					return;
				}
			}else{
				var val=$('input:radio[name="skillGroupIds"]:checked').val();
				if(val==null){
					layer.msg("请选择班组!");
					return;
				}
			}
			var arr =  new Array();
			$("#skillGroupSelectForm input[name='skillGroupIds']:checked").each(function(index){
				var data ={id:$(this).val(),name:$(this).data("name")};
				arr.push(data);
			});
			parent.changeSkillGroup(arr);
			popup.layerClose();
		}
		
		SkillGroupSelect.delAllGroup=function(){
			layer.confirm("是否移除该坐席的所有班组关系？",{icon: 3, title:'删除提示',offset:'20px'},function(index){
				var length=$("#skillGroupSelectForm input[type='checkbox']:checked").length;
				if(length==0){
					layer.msg("未选择班组!");
					return;
				}
				ajax.remoteCall("${ctxPath}/servlet/user?action=delAllGroup",{userId:SkillGroupSelect.userId,oldGroupIds:SkillGroupSelect.oldGroupIds},function(result) { 
					if(result.state == 1){
						layer.msg("操作成功",{icon:1,time:1500},function(index2){
							popup.layerClose();
							parent.delAllGroup();
						});
					}else{
						layer.msg(result.msg);
					}
				});
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>