<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head"><title>部门类型编辑</title></EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="GroupTypeDao.record"  method="post"  autocomplete="off" data-mars-prefix="groupType.">
				  <table class="table table-edit table-vzebra">
	                    <tbody>
		                     <tr>
		                            <td width="120px">父类型编码</td>
			                        <td><input type="text" maxlength="20" name="pGroupType" class="form-control input-sm" value="${param.pNodeType }" readonly="readonly" ></td>
		                     </tr>
		                     <tr>
		                            <td class="required" width="120">类型编码</td>
			                        <td><input type="text" maxlength="20" name="groupType.GROUP_TYPE" class="form-control input-sm" value="${param.nodeTypeId }" <c:if test="${!empty param.nodeTypeId }">readonly="readonly"</c:if>></td>
		                     </tr>
		                     <tr>
		                            <td class="required" width="120">类型名称</td>
			                        <td><input type="text" maxlength="20" name="groupType.GROUP_TYPE_NAME" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                            <td class="required">话务标志</td>
			                        <td>
			                        	<label class="radio radio-inline radio-success">
				                        	<input type="radio" value="0" checked="checked" name="groupType.CALL_FLAG"> <span>无话务权限</span>
				                        </label>
				                        <label class="radio radio-inline radio-success">
				                        	<input type="radio" value="1" name="groupType.CALL_FLAG"> <span>话务权限</span>
				                        </label>
			                        </td>
		                     </tr>
		                     <tr>
		                            <td class="required">最大节点数</td>
			                        <td><input type="number" name="groupType.MAX_NODE_COUNT" class="form-control input-sm" placeholder="0不限制，1代表只能创建一个节点,缺省0"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="NodeTypeEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("NodeTypeEdit");
	
	NodeTypeEdit.nodeTypeId='${param.nodeTypeId}';
	
	$(function(){
		$("#easyform").render({data:{pk:'${param.nodeTypeId}'}});
	});
	 NodeTypeEdit.ajaxSubmitForm = function(){
		 console.info(12);
		 if(form.validate("easyform")){
			 if(NodeTypeEdit.nodeTypeId==''){
				 NodeTypeEdit.insertData(); 
			 }else{
				 NodeTypeEdit.updateData(); 
			 }
		 };
	 }
	 NodeTypeEdit.insertData = function() {
		 console.info(123);
			var data = form.getJSONObject("easyform");
			ajax.remoteCall("${ctxPath}/servlet/groupType?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						GroupType.searchData();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	 }
	 NodeTypeEdit.updateData = function(){
		 console.info(123);
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("${ctxPath}/servlet/groupType?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,offset:'100px',time:1200},function(){
					GroupType.searchData();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>