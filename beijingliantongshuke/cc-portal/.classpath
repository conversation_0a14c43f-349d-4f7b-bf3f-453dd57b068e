<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/mars"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v8.0"/>
	<classpathentry combineaccessrules="false" kind="src" path="/cc-commonext"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-annotations-2.9.7.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-core-2.9.7.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jackson-databind-2.9.7.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/java-jwt-3.12.1.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
