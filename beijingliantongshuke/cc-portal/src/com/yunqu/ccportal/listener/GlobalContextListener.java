package com.yunqu.ccportal.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletContextEvent;
import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.ccportal.base.Constants;




@WebListener
public class GlobalContextListener extends  ServiceContextListener {

	@Override
	public void contextInitialized(ServletContextEvent event) {
		super.contextInitialized(event);
		event.getServletContext().setAttribute("xxx", "ssss");
	}

	@Override
	public void contextDestroyed(ServletContextEvent event) {
		super.contextDestroyed(event);
	}
	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource  resource = new ServiceResource();
		resource.appName     =  Constants.APP_NAME; //服务所在的WAR应用名   
		resource.className   =  "com.yunqu.ccportal.service.CallUrlService";//服务实现类，类必须实现IService接口
		resource.description =  "企业呼叫中心来电弹屏服务";//服务描述
		resource.serviceId   =  "CALLIN_URL_CC_PORTAL";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource.serviceName =  "企业呼叫中心来电弹屏服务";//服务名称
		list.add(resource);
		
		ServiceResource  resource2 = new ServiceResource();
		resource2.appName     =  Constants.APP_NAME; //服务所在的WAR应用名   
		resource2.className   =  "com.yunqu.ccportal.service.CallOutUrlService";//服务实现类，类必须实现IService接口
		resource2.description =  "企业呼叫中心来电弹屏服务";//服务描述
		resource2.serviceId   =  "CALLOUT_URL_CC_PORTAL";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource2.serviceName =  "企业呼叫中心来电弹屏服务";//服务名称
		list.add(resource2);
		
		//注册模块初始化数据接口
		ServiceResource resource3 = new ServiceResource();
		resource3.appName = Constants.APP_NAME;
		resource3.className = "com.yunqu.ccportal.inf.RegInitDataService";
		resource3.description = "cc-portal注册模块初始化数据接口";
		resource3.serviceId = "CC-REG-INIT-DATA-"+Constants.APP_NAME;
		resource3.serviceName = "cc-portal注册模块初始化数据接口";
		list.add(resource3);		
		
		return list;
	}
	

}
