package com.yunqu.ccportal.listener;

import java.io.File;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.apache.log4j.Logger;

import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.ccportal.base.CommonLogger;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.utils.DBMonitor;

/**
 * 应用初始化 InitListener.java
 */
@WebListener
public class InitListener implements ServletContextListener
{

	protected Logger logger = CommonLogger.logger;
	
	public static boolean start = false;

	/**
	 * 模块启动
	 */
	public void contextInitialized(ServletContextEvent arg0) {
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始加载模块..");
		new Thread(){
			public void run() {
				try {
					Thread.sleep(100);
					
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始启动线程加载模块..");
					
					//加载出本模块特有的国际化信息
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始加载模块的国际化信息..");
					BaseI18nUtil.loadModuleLang(arg0.getServletContext().getRealPath("/")+"static"+File.separator+"js"+File.separator+"my_i18n.js",Constants.APP_NAME);
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 结束加载国际化信息..");
					
					new Thread(new DBMonitor()).start();
				} catch (Exception e) {
					logger.info(CommonUtil.getClassNameAndMethod(this)+"模块加载异常:"+e.getMessage(),e);
				}
				
			};
		}.start();
		
		start = true;
		
	}

	/**
	 * 模块停止
	 */
	public void contextDestroyed(ServletContextEvent arg0) {
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始停止模块..");
		
		start = false;
	}
}