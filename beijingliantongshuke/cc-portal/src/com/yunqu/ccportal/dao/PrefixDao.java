package com.yunqu.ccportal.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;
import com.yunqu.ccportal.model.Prefix;

@WebObject(name="prefix")
public class PrefixDao extends AppDaoContext {

	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		EasySQL sql = this.getEasySQL("select * from CC_PREFIX where 1=1 ");
		sql.append(getEntId(), " AND ENT_ID = ?");
		sql.append(param.getString("prefixType"), " and PREFIX_TYPE = ?");
		sql.append(param.getString("prefixState"), " and PREFIX_STATE = ?");
		sql.appendLike(param.getString("prefixNum"), " and PREFIX_NUM like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getPrefix",type=Types.RECORD)
	public  JSONObject getPrefix(){
		String pk=param.getString("pk");
		Prefix prefix=new Prefix();
		prefix.setPrimaryValues(pk);
		return queryForRecord(prefix);
	}
	
	/**
	 * 获取尚未分配的外显号码字典
	 */
	@WebControl(name="dict", type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql = this.getEasySQL("select t1.PREFIX_NUM,t1.PREFIX_NUM from CC_PREFIX t1 where PREFIX_STATE = 0");
		sql.append(getEntId(), " and ENT_ID = ?");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 坐席选择外显号码列表
	 */
	@WebControl(name="selectorList", type=Types.LIST)
	public JSONObject selectorList(){
		EasySQL sql = this.getEasySQL("select * from CC_PREFIX where PREFIX_STATE = 0");
		sql.append(getEntId(), " and ENT_ID = ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 外显号码选择组件
	 */
	@WebControl(name="prefixOther", type=Types.OTHER)
	public JSONObject prefixOther(){
		EasySQL sql = this.getEasySQL("select PREFIX_NUM prefixNum from CC_PREFIX where PREFIX_STATE = 0");
		sql.append(getEntId(), " and ENT_ID = ?");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

}
