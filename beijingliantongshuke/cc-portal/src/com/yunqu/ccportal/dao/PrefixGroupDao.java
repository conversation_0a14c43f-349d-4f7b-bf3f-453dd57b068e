package com.yunqu.ccportal.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.model.PrefixGroup;

@WebObject(name="prefixGroup")
public class PrefixGroupDao extends AppDaoContext {

	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		String groupName = param.getString("groupName");
		EasySQL sql = this.getEasySQL("select t1.* from cc_prefix_group t1  where 1=1");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.appendLike(groupName, " AND t1.PREFIX_GROUP_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getPrefixGroup",type=Types.RECORD)
	public  JSONObject record(){
		String pk=param.getString("pk");
		PrefixGroup prefixGroup=new PrefixGroup();
		prefixGroup.setPrimaryValues(pk);
		return queryForRecord(prefixGroup);
	}
	
	@WebControl(name="prefixList",type=Types.LIST)
	public  JSONObject prefixList(){
		EasySQL sql = this.getEasySQL("select t1.*,t2.PREFIX_STATE from CC_PREFIX_GROUP_PREFIX t1,CC_PREFIX t2 where t1.ENT_ID = t2.ENT_ID and t1.PREFIX_NUM = t2.PREFIX_NUM and t2.PREFIX_STATE = 0");
		sql.append(param.getString("groupId")," and t1.PREFIX_GROUP_ID = ? ");
		sql.append(param.getString("entId"), " and t1.ENT_ID = ? ");
		sql.appendLike(param.getString("pk"), " and t1.PREFIX_NUM like ? ");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="selectPrefixList",type=Types.TEMPLATE)
	public  JSONObject selectPrefixList(){
		String groupId = param.getString("pk");
		EasySQL sql = this.getEasySQL("select * from CC_PREFIX t1 where t1.PREFIX_STATE = 0 and not exists (select PREFIX_NUM from CC_PREFIX_GROUP_PREFIX t2 where t2.PREFIX_NUM = t1.PREFIX_NUM");
		sql.append(groupId, " and t2.PREFIX_GROUP_ID = ? ");
		sql.append(")");
		sql.append(getEntId(), " and t1.ENT_ID = ?");
		sql.append(Constants.PREFIX_STATE_USE, " and t1.PREFIX_STATE = ?");
		return this.queryForList(sql.getSQL(), sql.getParams(),null);
	}

	@WebControl(name="prefixGroupDict",type=Types.DICT)
	public JSONObject prefixGroupDict(){
		return getDictByQuery("select PREFIX_GROUP_ID,PREFIX_GROUP_NAME from CC_PREFIX_GROUP where ENT_ID = ?", new Object[]{getEntId()});
	}
	
	/**
	 * 号码组的子号码数字典
	 */
	@WebControl(name="prefixCountDict", type=Types.DICT)
	public JSONObject prifixCountDict(){
		EasySQL sql = this.getEasySQL("select t1.PREFIX_GROUP_ID,(select count(1) from CC_PREFIX_GROUP_PREFIX where PREFIX_GROUP_ID=t1.PREFIX_GROUP_ID) as PREFIX_COUNT from CC_PREFIX_GROUP t1 where 1=1");
		sql.append(getEntId()," and t1.ENT_ID = ?");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
}
