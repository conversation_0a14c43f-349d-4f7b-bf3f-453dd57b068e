package com.yunqu.ccportal.dao;


import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppDaoContext;
import com.yunqu.ccportal.utils.RoleUtil;
/**
 * 个人收藏夹DAO
 * <AUTHOR>
 *
 */
@WebObject(name="bookMark")
public class BookMarkDao extends AppDaoContext {

	/**
	 * 获取收藏对象
	 * @return
	 */
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		EasySQL sql = this.getEasySQL("SELECT * FROM "+this.getTableName("C_CF_URL_FAVORITES"));
		sql.append("WHERE 1=1");
		sql.append(param.getString("ID"), "AND ID = ?",false);
		JSONObject result = this.queryForRecord(sql.getSQL(), sql.getParams(), null);
		result.put("isAdmin", RoleUtil.isRoleType(request));
		return result;
	}
	
	/**
	 * 获取收藏列表
	 * @return
	 */
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		UserModel user = UserUtil.getUser(this.request);
		EasySQL sql = this.getEasySQL("SELECT T.*,(CASE WHEN (CREATE_USER_ACC = '"+user.getUserAcc()+"' ) THEN 1 ELSE 0 END) IS_MINE");
		sql.append("FROM "+this.getTableName("C_CF_URL_FAVORITES T"));
		sql.append("WHERE 1=1");
		sql.append(user.getUserAcc(),"AND (IS_PUBLIC = 'Y' OR CREATE_USER_ACC = ?)");
		sql.append(getEntId(),"AND ENT_ID = ?");
		sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ?");
		JSONObject list =  queryForList(sql.getSQL(), sql.getParams());
		if(list!=null && list.getJSONArray("data")!=null){
			JSONArray array = list.getJSONArray("data");
			for(int i = 0 ;i<array.size(); i++){
				JSONObject json = array.getJSONObject(i);
				String url = json.getString("URL");
				if(StringUtils.isNotBlank(url)){
					url = url.replace("[#USER_ACC#]", user.getUserAcc());
					url = url.replace("[#USER_NAME#]", user.getUserName());
					url = url.replace("[#USER_DEPT_CODE#]", user.getDeptCode());
				}
				json.put("URL", url);
			}
		}
		return list;
	}
	
}