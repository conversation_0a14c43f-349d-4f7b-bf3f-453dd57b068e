package com.yunqu.ccportal.dao;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;
/**
 * 部门DAO
 * <AUTHOR>
 *
 */
@WebObject(name="skillGroup2")
public class SkillGroupDao extends AppDaoContext {

	/**
	 * 获取部门对象
	 * @return
	 */
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		EasySQL sql = this.getEasySQL("select t1.*,t2.GROUP_TYPE_NAME,t2.CALL_FLAG from");
		sql.append(this.getTableName("CC_SKILL_GROUP t1"));
		sql.append("left join").append(this.getTableName("CC_GROUP_TYPE")).append("t2 on t1.GROUP_TYPE = t2.GROUP_TYPE");
		sql.append("where 1=1");
		sql.append(param.getString("pk"), "and t1.SKILL_GROUP_ID = ?",false);
		return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
	
	/**
	 * 用户选择列表
	 * @return
	 */
	@WebControl(name="agentListSelect",type=Types.LIST)
	public  JSONObject agentListSelect(){
		EasySQL sql=this.getEasySQL("select t1.USER_ID,t1.AGENT_PHONE,t2.AGENT_NAME,t2.GROUP_LIST,t2.ROLE_LIST from CC_USER t1,");
		sql.append(getTableName("cc_busi_user t2"));
		sql.append(" where t1.USER_STATE <> 9 and t1.USER_ID = t2.USER_ID and t2.USER_STATE = 0 and t1.ADMIN_FLAG = 0");
		sql.append(this.getEntId()," and t2.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? ");
		sql.appendLike(param.getString("agentPhone")," and t1.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t2.AGENT_NAME like ?");
		sql.append(" and t1.USER_ID not in (select USER_ID from ");
		sql.append(getTableName("CC_SKILL_GROUP_USER"));
		sql.append(" where 1=1");
		sql.append(this.getEntId(), " and ENT_ID = ?");
		sql.append(this.getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(param.getString("groupId"), "and SKILL_GROUP_ID = ?");
		sql.append(") order by t1.AGENT_PHONE");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 班组架构
	 * @return
	 * @throws SQLException
	 */
	@WebControl(name="groupTree", type=Types.TREE)
	public JSONObject groupTree(){
		JSONObject root = new JSONObject();
		root.put("name","组织架构");
		root.put("id", 0);
		root.put("open", true);
		String pSkillId = "0";
		String[] skillGroupIds = this.getSkillGroupIdChilds();
		if(skillGroupIds != null && skillGroupIds.length > 0){
			pSkillId = skillGroupIds[0];
		}
		List<JSONObject> jsonArray;
		try {
			EasySQL sql = this.getEasySQL("select SKILL_GROUP_ID,SKILL_GROUP_NAME,P_GROUP_ID,GROUP_TYPE,SKILL_GROUP_CODE from ");
			sql.append(getTableName("CC_SKILL_GROUP"));
			sql.append(" where 1=1");
			sql.append(this.getEntId(), "and ENT_ID = ?");
			sql.append(this.getBusiOrderId(), "and BUSI_ORDER_ID = ?");
			sql.append("order by IDX_ORDER");
			jsonArray = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			/*
			 * 管理员权限，从根节点开始
			 * 非管理员，从本节点开始
			 */
			if("0".equals(pSkillId)){
				root.put("children", skillNext("0", jsonArray));
			}else{
				JSONArray children = new JSONArray();
				for(int i = 0; i < jsonArray.size(); i++){
					if(pSkillId.equals(jsonArray.get(i).getString("SKILL_GROUP_ID"))){
						JSONObject next = new JSONObject();
						next.put("name",jsonArray.get(i).getString("SKILL_GROUP_NAME"));
						next.put("id", jsonArray.get(i).getString("SKILL_GROUP_ID"));
						next.put("open", true);
						next.put("groupType", jsonArray.get(i).getString("GROUP_TYPE"));
						next.put("skillGroupCode", jsonArray.get(i).getString("SKILL_GROUP_CODE"));
						next.put("children", skillNext(jsonArray.get(i).getString("SKILL_GROUP_ID"), jsonArray));
						children.add(next);
					}
				}
				root.put("children", children);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTree(root);
	}
	
	/**
	 * 获取下级部门
	 * @param pId
	 * @param skillList
	 * @return
	 */
	private JSONArray skillNext(String pId,  List<JSONObject> skillList){
		JSONArray teamChildren = new JSONArray();
		for (JSONObject skill : skillList) {
			if(skill.getString("P_GROUP_ID").equals(pId)){
				skill.put("name", skill.getString("SKILL_GROUP_NAME"));
				skill.put("id", skill.getString("SKILL_GROUP_ID"));
				skill.put("open", true);
				skill.put("skillGroupCode", skill.getString("SKILL_GROUP_CODE"));
				skill.put("children", skillNext(skill.getString("SKILL_GROUP_ID"), skillList));
				skill.put("groupType", skill.getString("GROUP_TYPE"));
				teamChildren.add(skill);
			}
		}
		return teamChildren;
	}
	
	/**
	 * 部门用户
	 * @return
	 */
	@WebControl(name="groupUserList",type=Types.LIST)
	public JSONObject groupUserList(){
		String id = param.getString("pk");
		if("0".equals(id)){
			id = null;
		}
		EasySQL sql = this.getEasySQL("select t1.USER_ID,t1.AGENT_NAME,t1.ROLE_ID,t1.USER_STATE,t2.MOBILE,t1.PREFIX_NUM, t2.USERNAME,t2.USER_ACCT,t2.AGENT_PHONE,t2.USER_STATE CENTER_USER_STATE,t4.SKILL_GROUP_NAME,t5.ROLE_NAME from ");
		sql.append(getTableName("CC_BUSI_USER t1, "));
		sql.append("CC_USER t2, ");
		sql.append(getTableName("CC_SKILL_GROUP_USER t3,"));
		sql.append(getTableName("CC_SKILL_GROUP t4,"));
		sql.append(getTableName("CC_ROLE t5"));
		sql.append(" where t1.USER_ID = t2.USER_ID and t2.ADMIN_FLAG = 0 and t1.BUSI_ORDER_ID = t3.BUSI_ORDER_ID and t1.USER_ID = t3.USER_ID and t3.SKILL_GROUP_ID = t4.SKILL_GROUP_ID and t1.ROLE_ID = t5.ROLE_ID");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? " );
		sql.append(getEntId(), " and t1.ENT_ID = ? ");
		sql.append(id," and t3.SKILL_GROUP_ID = ?");
		sql.appendLike(param.getString("condition"), " and t2.AGENT_PHONE like ? ");
		sql.appendLike(param.getString("agentName"), "and t2.AGENT_NAME like ?");
		sql.append(" order by t3.IDX_ORDER, t2.AGENT_PHONE");
		
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
}