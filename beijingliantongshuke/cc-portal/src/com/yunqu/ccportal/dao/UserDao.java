/**
 * 
 */
package com.yunqu.ccportal.dao;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;

@WebObject(name="user")
public class UserDao extends AppDaoContext {
	
	/**
	 * 坐席列表
	 * @return
	 */
	@WebControl(name="agentList",type=Types.LIST)
	public  JSONObject agentList(){
		
		EasySQL sql = this.getEasySQL("select t1.USER_ID ,t1.AGENT_NAME, t1.ENT_ID ,t1.BUSI_ORDER_ID,t1.USER_STATE,t1.GROUP_LIST,t1.ROLE_LIST,t2.USERNAME, t2.USER_ACCT,t2.AGENT_PHONE ,t2.LOGIN_TIME,t1.ROLE_ID,t1.PREFIX_NUM,t2.MOBILE,t2.SALES_CODE,t2.F_PHONE_NUM,t3.ROLE_NAME,t3.ROLE_TYPE");
		sql.append("from");
		sql.append(getTableName("CC_BUSI_USER t1 "));
		sql.append("left join CC_USER t2 on t1.USER_ID = t2.USER_ID ");
		sql.append("left join ").append(getTableName("CC_ROLE")).append(" t3 on t1.ROLE_id = t3.ROLE_ID");
		
		sql.append(" where 1 = 1 and t2.ADMIN_FLAG = 0  and t2.USER_STATE = 0 ");
		sql.append(this.getBusiOrderId()," and t1.BUSI_ORDER_ID = ? "); 
		sql.append(this.param.getString("userState")," and t1.USER_STATE = ? ");
		
		sql.append(this.getEntId()," and t1.ENT_ID = ? ");
		sql.appendLike(this.param.getString("agentName"), "and t1.AGENT_NAME like ? ");
		sql.appendLike(this.param.getString("agentPhone"), "and t2.AGENT_PHONE like ? ");
		sql.append(this.param.getString("roleId"), "and t1.ROLE_ID = ? ");
		sql.appendLike(this.param.getString("agentGroup"), "and t1.GROUP_LIST like ? ");
		
		sql.appendSort(param.getString("sortName"),param.getString("sortType"),"t2.AGENT_PHONE");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public  JSONObject record(){
		EasySQL sql = this.getEasySQL("select t1.OUTBOUND,t1.INBOUND,t1.USER_ID ,t1.AGENT_NAME, t1.GROUP_LIST, t2.AGENT_PHONE,t2.USER_ACCT,t1.USER_STATE ,t1.ROLE_ID,t1.PREFIX_NUM,t2.MOBILE,t2.SALES_CODE,t2.F_PHONE_NUM from ");
		sql.append(getTableName("CC_BUSI_USER t1,"));
		sql.append(" CC_USER t2 where t1.USER_ID = t2.USER_ID ");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");
		sql.append(this.param.getString("pk")," and t1.USER_ID = ? ");
		JSONObject jsonObject=this.queryForRecord(sql.getSQL(),sql.getParams(),null);
		jsonObject.put("agentConfig", getAgentConfig(this.param.getString("pk")));
		return jsonObject;
	}
	
	private JSONObject getAgentConfig(String userId){
		EasySQL sql=this.getEasySQL("select CONFIG_KEY,CONFIG_VALUE from "+getTableName("CC_ENT_CONFIG")+" where USER_ID = ? and CONFIG_TYPE = ?");
		JSONObject result=new JSONObject();
		try {
			List<JSONObject> list=this.getQuery().queryForList(sql.getSQL(), new Object[]{userId,"AgentConfig"},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject object:list){
					result.put(object.getString("CONFIG_KEY"),object.getString("CONFIG_VALUE"));
				}
			}
			return result;
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return result;
	}
	
	@WebControl(name="agentListSelect",type=Types.DICT)
	public  JSONObject agentListSelect(){
		return this.getDictByQuery("select USER_ID,AGENT_NAME from cc_user where 1=1  and USER_STATE=0  and ENT_ID = ?", new Object[]{getEntId()});
	}
	/**
	 * 登录日志列表
	 * @return
	 */
	@WebControl(name="loginLogList",type=Types.LIST)
	public  JSONObject userList(){
		EasySQL sql = this.getEasySQL("select * from cc_login_log where 1=1 ");
		sql.append(getEntId(), " and ENT_ID = ? ");
		sql.append(param.getString("startDate"), " and LOGIN_TIME >= ? ");
		sql.append(param.getString("endDate"),   " and LOGIN_TIME <= ? ");
		if(!getUserPrincipal().isAdmin()){
			sql.append(getUserPrincipal().getLoginAcct()," and USER_ACCT = ?");
		}
		sql.append(" order by LOGIN_TIME desc");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}

}
