package com.yunqu.ccportal.dao;

import java.util.Calendar;
import java.util.Date;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppDaoContext;

@WebObject(name="common")
public class CommonDao extends AppDaoContext {
	
	/**
	 * 企业订购清单
	 * @return
	 */
	@WebControl(name="bussOrderList",type=Types.DICT)
	public JSONObject bussOrderList(){
		return getDictByQuery("select t1.BUSI_ORDER_ID,t2.BUSI_NAME from cc_busi_order t1 INNER JOIN cc_busi t2 on t2.BUSI_ID=t1.BUSI_ID where ENT_ID = ? ", new Object[]{getEntId()});
	}

	//外呼字冠组
	@WebControl(name="prefixGroupDic",type=Types.DICT)
	public JSONObject prefixGroupDic(){
		return getDictByQuery("select prefix_group_id,prefix_group_name from cc_prefix_group where ent_id = ? ", getEntId());
	}
	//呼出
	@WebControl(name="prefixNumDic",type=Types.DICT)
	public JSONObject prefixNumDic(){
		return getDictByQuery("select prefix_num,prefix_num from cc_prefix where ent_id = ? and PREFIX_TYPE = 1 and PREFIX_STATE = 0", getEntId());
	}
	//欢迎语
	@WebControl(name="entVoxDic",type=Types.DICT)
	public JSONObject entVoxDic(){
		return getDictByQuery("select VOX_ID,VOX_NAME from cc_ent_vox where ENT_ID = ? and VOX_TYPE = 1 ", getEntId());
	}
	//短信模板
	@WebControl(name="smsTemp",type=Types.DICT)
	public JSONObject smsTemp(){
		return getDictByQuery("select SMS_TEMP_ID,SMS_TEMP_NAME from CC_SMS_TEMP where ENT_ID = ? and  TEMP_STATE=0 ", getEntId());
	}
	//问卷调查
	@WebControl(name="questionnaire",type=Types.DICT)
	public JSONObject questionnaire(){
		return getDictByQuery("select QT_ID,QT_NAME from "+getTableName("QT_QUESTIONNAIRE")+" where ENT_ID = ? and  QT_STATUS = 0 order by CREATE_TIME desc", getEntId());
	}
	@WebControl(name="smsTempRecord",type=Types.RECORD)
	public JSONObject smsTempRecord(){
		return queryForRecord("select * from CC_SMS_TEMP where SMS_TEMP_ID = ? ",new Object[]{param.getString("smsTempId")},new JSONMapperImpl());
	}
	
	@WebControl(name="userName",type=Types.TEXT)
	public JSONObject userName(){
		return getJsonResult(getUserPrincipal().getNickName());
	}
	@WebControl(name="roleType",type=Types.TEXT)
	public JSONObject roleType(){
		return getJsonResult(getUserPrincipal().getRoleType());
	}
	
	@WebControl(name="getEntRes",type=Types.OTHER)
	public JSONObject getEntRes(){
		return queryForRecord("SELECT * FROM CC_ENT_RES where ENT_ID = ?",new Object[]{getEntId()},new JSONMapperImpl());
	}
	
	@WebControl(name="currentDate",type=Types.TEXT)
	public JSONObject currentDate(){
		return getJsonResult(EasyDate.getCurrentDateString());
	}
	
	@WebControl(name="weekAgo",type=Types.TEXT)
	public JSONObject weekBegin(){
	  	Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, - 7);
        String day = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd HH:mm");
		return getJsonResult(day);
	}
	
	@WebControl(name="todayBegin",type=Types.TEXT)
	public JSONObject todayBegin(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00");
	}
	@WebControl(name="todayEnd",type=Types.TEXT)
	public JSONObject todayEnd(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd") + " 23:59");
	}
	/**
	 * 获取服务器的当前日期
	 * @return
	 */
	@WebControl(name="today",type=Types.TEXT)
	public JSONObject today(){
		return getText(EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	@WebControl(name="day30After",type=Types.TEXT)
	public JSONObject twoWeekAfter(){
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		c.add(Calendar.DATE, 30);
		String day = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd");
		return getJsonResult(day);
	}
	//技能组
	@WebControl(name="skillGroupDict",type=Types.DICT)
	public JSONObject skillGroupDict(){
		return getDictByQuery("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? ", getEntId());
	}
	
	//IVR语音字典
	@WebControl(name="entVoxIvrDic",type=Types.DICT)
	public JSONObject entVoxIvrDic(){
		return getDictByQuery("select VOX_ID,VOX_NAME from CC_ENT_VOX where ENT_ID = ? and VOX_TYPE = 3 ", getEntId());
	}
	
}
