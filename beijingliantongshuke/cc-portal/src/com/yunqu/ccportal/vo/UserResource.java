package com.yunqu.ccportal.vo;

/**
 * 用户资源模型
 * <AUTHOR>
 *
 */
public class UserResource {
	/**
	 * 资源ID
	 */
	private String  resId;
	/**
	 * 资源名称
	 */
	private String  resName;
	/**
	 * 父资源ID
	 */
	private String  presId;
	/**
	 * 资源访问地址
	 */
	private  String resUrl;
	
	private String resIcon;
	
	/**
	 * 资源目标
	 */
	private  String resTarget;
	/**
	 * 资源排序
	 */
	private  int    idxOrder;
	/**
	 * 资源类型
	 */
	private  int    resType;
	
	/**
	 * 菜单编码
	 */
	private int resCode;
	
	/**
	 * 子节点数量，仅查询某个子节点的场景下才会设置该值
	 */
	private int sons;
	
	public String getResId() {
		return resId;
	}
	public void setResId(String resId) {
		this.resId = resId;
	}
	public String getResName() {
		return resName;
	}
	public void setResName(String resName) {
		this.resName = resName;
	}
	public String getPresId() {
		return presId;
	}
	public void setPresId(String presId) {
		this.presId = presId;
	}
	public String getResUrl() {
		return resUrl;
	}
	public void setResUrl(String resUrl) {
		this.resUrl = resUrl;
	}
	public String getResIcon() {
		return resIcon;
	}
	public void setResIcon(String resIcon) {
		this.resIcon = resIcon;
	}
	public String getResTarget() {
		return resTarget;
	}
	public void setResTarget(String resTarget) {
		this.resTarget = resTarget;
	}
	public int getIdxOrder() {
		return idxOrder;
	}
	public void setIdxOrder(int idxOrder) {
		this.idxOrder = idxOrder;
	}
	public int getResType() {
		return resType;
	}
	public void setResType(int resType) {
		this.resType = resType;
	}
	public int getResCode() {
		return resCode;
	}
	public void setResCode(int resCode) {
		this.resCode = resCode;
	}
	public int getSons() {
		return sons;
	}
	public void setSons(int sons) {
		this.sons = sons;
	}
	
}
