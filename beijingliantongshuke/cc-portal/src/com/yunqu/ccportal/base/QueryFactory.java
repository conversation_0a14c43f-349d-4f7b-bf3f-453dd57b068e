package com.yunqu.ccportal.base;

import java.util.Random;

import org.apache.commons.lang3.StringUtils;

import org.easitline.common.db.EasyQuery;


public class QueryFactory {

	
	private static EasyQuery writeQuery1 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	private static EasyQuery writeQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
	
	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);

	public static EasyQuery getReadQuery(){
		return readQuery;
	}
	

	/**
	 * 随机获取一个写数据源(当没有request请求时，要使用query对象，可以用该方法)
	 * @return
	 */
	public static EasyQuery getWriteQuery(){
		Random random = new Random();
		int num = random.nextInt(3);
		if(num==2){
			return writeQuery2;
		}
		return writeQuery1;
	}
	
	
	public static EasyQuery getQuery(String entId){
		if(StringUtils.isBlank(entId)){
			return writeQuery1;
		}
		int _entId = Integer.parseInt(entId);
		if(_entId%2==0){  
			return writeQuery1;
		}
		return writeQuery2;
	}
}
