package com.yunqu.ccportal.base;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
/**
 * dao base class
 * <AUTHOR>
 */

public class StatDaoContext extends DaoContext {
	protected EasyCache cache = CacheManager.getMemcache();
	

	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_READ_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected boolean loginCheck() {
		return true;
	}
	
	protected String getTableName(String tableName){
		String dbName=getDbName();
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	
	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	protected String getEntId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getEntId();
	}
	
	protected String getDbName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getSchemaName();
	}
	
	protected String getBusiOrderId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getBusiOrderId();
	}
	protected YCUserPrincipal getUserPrincipal(){ 
		return (YCUserPrincipal)request.getUserPrincipal();
	}
	//用户数据库ID 
	protected String getUserId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getUserId();
	}
	
	/**
	 * 获取技能组
	 * @return
	 */
	protected String[] getGroupSkills(){
		String[] skills = null;
		YCUserPrincipal userPrincipal = this.getUserPrincipal();
		int roleType = userPrincipal.getRoleType();
		try {
			if(roleType == Constants.ROLE_TYPE_AGENT || roleType == Constants.ROLE_TYPE_MONITOR){
				List<JSONObject> list = this.getQuery().queryForList("select SKILL_GROUP_ID from "+getTableName("CC_SKILL_GROUP_USER")+" where USER_ID = ? and BUSI_ORDER_ID = ? and ENT_ID = ?", new Object[]{userPrincipal.getUserId(),userPrincipal.getBusiOrderId(),userPrincipal.getEntId()}, new JSONMapperImpl());
				if(list != null){
					skills = new String[list.size()];
					for(int i = 0 ; i < list.size(); i++){
						skills[i] = list.get(i).getString("SKILL_GROUP_ID");
					}
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return skills;
	}
	
	/**
	 * 获取角色类型
	 * @return
	 */
	protected int getRoleType(){
		YCUserPrincipal user = this.getUserPrincipal();
		return user.getRoleType();
	}
	
	/**
	 * 获取技能组权限
	 * @param sqlKey
	 * @param pSkillId
	 * @return
	 */
	protected String getSkillRes(String sqlKey, String pSkillId){
		String sql = "";
		int roleType = this.getRoleType();
		String[] skillGroupIds = null;
		if(StringUtils.isNotBlank(pSkillId) && !"0".equals(pSkillId)){
			skillGroupIds = this.getChildSkill(pSkillId);
		}else if(roleType != Constants.ROLE_TYPE_MANAGER){
			skillGroupIds = getSkillGroupIdChilds();
			if(skillGroupIds != null && skillGroupIds.length == 1){
				skillGroupIds = this.getChildSkill(getSkillGroupIdChilds()[0]);
			}
			sql = " and "+sqlKey+" = -1";
		}
		if(skillGroupIds != null){					//存在技能组，获取该团队
			sql = " and "+sqlKey+" "+StringUtils.joinSql(skillGroupIds)+"";
		}
		return sql;
	}
	
	/**
	 * 获取部门下所有技能组
	 * @param pId
	 * @param reflash	是否刷新缓存
	 * @return
	 */
	private String[] getChildSkill(String pId){
		JSONArray array = null;
		String cacheKey = "SKILL_ID_FAMILY_"+pId;
		Object obj = cache.get(cacheKey);
		if(obj == null){
			array = new JSONArray();
			array.add(pId);
			try {
				List<JSONObject> list = this.getQuery().queryForList("select SKILL_GROUP_ID,P_GROUP_ID from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId()}, new JSONMapperImpl());
				if(list != null && list.size() > 0){
					this.addSkill(pId, array, list);
					cache.put(cacheKey, array, 3600);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		} else{
			array = (JSONArray)obj;
		}
		return JSONArray.toJavaObject(array, String[].class);
	}
	
	/**
	 * 获取子部门
	 * @param pId
	 * @param array
	 * @param list
	 */
	private void addSkill(String pId, JSONArray array, List<JSONObject> list ){
		for(JSONObject row : list){
			if(pId.equals(row.getString("P_GROUP_ID"))){
				array.add(row.getString("SKILL_GROUP_ID"));
				addSkill(row.getString("SKILL_GROUP_ID"), array, list);
			}
		}
	}
	
	/**
	 * 获取权限sql,
	 * @param sqlKey	sql查询条件
	 * @return
	 */
	protected String getUserResSql(String sqlKey){
		String sql = "";
		YCUserPrincipal user = this.getUserPrincipal();
		if(user.getRoleType() == Constants.ROLE_TYPE_AGENT){				//座席
			sql = " and "+sqlKey+" = '"+user.getUserId()+"'";
		}else if(user.getRoleType() == Constants.ROLE_TYPE_MONITOR){			//班长
			String[] skillGroupId = getSkillGroupIdChilds();
			if(skillGroupId != null){					//存在技能组，获取该团队
				sql = " and "+sqlKey+" in ( select t5.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t5 where t5.ENT_ID = '"+user.getEntId()+"' and t5.BUSI_ORDER_ID = '"+user.getBusiOrderId()+"'";
				if(skillGroupId.length == 1){
					sql +=" and t5.SKILL_GROUP_ID = '"+skillGroupId[0]+"')";
				}else{
					sql +=" and t5.SKILL_GROUP_ID in("+StringUtils.join(skillGroupId, ",")+"))";
				}
			}else{														//不存在技能组，获取个人
				sql +=" and "+sqlKey+" = '"+user.getUserId()+"'";
			}
		}
		return sql;
	}
	
	/**
	 * 获取技能组Id
	 */
	protected String[] getSkillGroupIdChilds(){
		Object attribute = this.getUserPrincipal().getAttribute("skillGroupIdChilds");
		try{
			String [] s = (String[]) attribute;
			this.error("skillGroupIds:"+s[0], null);
		}catch(Exception e){
		}
		if(attribute==null)return null;
		return (String[]) attribute;
	}
}
