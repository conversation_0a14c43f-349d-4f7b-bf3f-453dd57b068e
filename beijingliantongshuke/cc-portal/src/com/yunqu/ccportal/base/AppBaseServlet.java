package com.yunqu.ccportal.base;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyModel;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	
	private static Map<String,String>  schemas = new HashMap<String,String>();

	private static final long serialVersionUID = 1L;
	
	protected EasyCache cache = CacheManager.getMemcache();

	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_WIRTE_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}
	protected String getTableName(String dbName,String tableName){
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	protected String getTableName(String tableName){
		String dbName=getDbName();
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	
	protected boolean isAdmin(){ 
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.isAdmin();
	}
	protected YCUserPrincipal getUserPrincipal(){ 
		return (YCUserPrincipal)this.getRequest().getUserPrincipal();
	}
	
	protected String getBusiId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getBusiId();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	protected <T> T getModel(Class<T> modelClass,String modelName){
		EasyModel bean = null;
		try {
			bean = (EasyModel)modelClass.newInstance();
			bean.setDbName(getDbName());
		} catch (InstantiationException | IllegalAccessException e) {
			e.printStackTrace();
		}
		bean.setColumns(this.getJSONObject(modelName));
		return (T)bean;
	}
	@Override
	public EasyQuery getQuery(){
		String entId=getEntId();
		int _entId=Integer.valueOf(entId);
		EasyQuery query=null;
		if(_entId%2==0){
			 query=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		}else{
			query=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		query.setLogger(getLogger());;
		return query;
	}
	
//	public EasyQuery getReaderQuery(){
//		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
//	}
	
	public String getEntId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getEntId();
	}
	public String getDbName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getSchemaName();
	}
	
	/**
	 * 获取企业名称
	 */
	protected String getEntName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getEntName();
	}
	
	protected String getBusiOrderId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getBusiOrderId();
	}
	//用户数据库ID 
	protected String getUserId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getUserId();
	}
	//登录账号
	protected String getUserAccount(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getLoginAcct();
	}
	
	//用户姓名
	protected String getNickName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)this.getRequest().getUserPrincipal();
		return principal.getNickName();
	}
	
	/**
	 * 获取角色类型
	 * @return
	 */
	protected int getRoleType(){
		YCUserPrincipal user = this.getUserPrincipal();
		return user.getRoleType();
	}
	
	/**
	 * 获得
	 * @param entId
	 * @return
	 */
	protected String getSchema(String entId) {
		if (!schemas.containsKey(entId)) {
			try {
				String sql = "  SELECT  SCHEMA_ID  from  CC_ENT_RES  where  ENT_ID = ?  ";
				String schemaId = this.getQuery().queryForString(sql, new String[] { entId });
				schemas.put(entId, schemaId);
			} catch (Exception ex) {
			}
		}
		return schemas.get(entId);
	}
	
	/**
	 * 同步企业信息到磐石平台
	 * @param entId
	 */
	protected void syncEntInfo(String entId){
		IService service;
		try {
			service = ServiceContext.getService("YC-ENT-NOTIFY-SERVICE");
			JSONObject  jsonIn = new JSONObject();
			jsonIn.put("entId", entId);
			JSONObject  result = service.invoke(jsonIn);
			this.info("syncEntInfo success >> entId:"+entId+",result:"+result.toJSONString(),null);
		} catch (ServiceException ex) {
			this.error("syncEntInfo error >> entId:"+entId+",cause:"+ex.getMessage(), ex);
		}
	}
	
	 /**
	   * 更新技能组坐席数
	   */
	  protected void updateSkillUserCount(){
			String sql = "update "+getTableName("cc_skill_group t1 ")+"  set agent_count = (select count(1) from "
					+ getTableName("cc_skill_group_user") +  " where skill_group_id = t1.skill_group_id  ) where t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ?";
			try {
				this.getQuery().execute(sql, new Object[]{getEntId(),getBusiOrderId()});
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
	 }

  /**
	 * 同步技能组信息到磐石平台
	 * @param entId
	 */
	protected String syncGroupInfo(String entId,String groupId,String oper){
		IService service;
		String message = null;
		try {
			List<String> list = ServiceContext.findByPrefix("YC-SKILLGROUP-NOTIFY-");
			for (String serviceName : list) {
				service = ServiceContext.getService(serviceName);
				JSONObject  jsonIn = new JSONObject();
				jsonIn.put("entId", entId);
				jsonIn.put("groupId", groupId);
				jsonIn.put("oper", oper);
				JSONObject  result = service.invoke(jsonIn);	
				String rs=result.getString("result");
				this.info("syncGroupInfo success >> entId:"+entId+",groupId:"+groupId+",oper:"+oper+",serviceName:"+serviceName+",result:"+result,null);
				if(!"000".equals(rs)){
					message = result.getString("message");
				}
			}
		} catch (ServiceException ex) {
			this.error("syncGroupInfo error >> entId:"+entId+",groupId:"+groupId+",oper:"+oper+entId+",cause:"+ex.getMessage(), ex);
			return ex.getMessage();
		}
		return message;
	}
	
	protected void syncSkillCache(String skillGroupId){
		String reloadKey = "RELOAD_SKILLGROUP_" + skillGroupId;
		cache.put(reloadKey, System.currentTimeMillis() + "",3600);
	}
	
	
	 

	
	protected String getSkillGroupId(){
		String[] skillGroupIds =(String[])this.getUserPrincipal().getAttribute("skillGroupIds");
		return skillGroupIds[0];
	}
	@Override
	protected String getResId() {
		return null;
	}
	
	/**
	 * 国际化
	 */
	public String getI18nValue(String str){
		return BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, str);
	}
}
