package com.yunqu.ccportal.utils;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;

public class RoleUtil {

	public static boolean isRoleType(HttpServletRequest request) {
		UserModel userModel = UserUtil.getUser(request);
		List<RoleModel> roles = userModel.getRoles();
		if(roles != null && roles.size() > 0) {
			for (RoleModel roleModel : roles) {
				if("1".equals(roleModel.getRoleType())) {
					return true;
				}
			}
		}
		return false;
	}
	
}
