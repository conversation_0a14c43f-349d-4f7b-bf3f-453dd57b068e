package com.yunqu.ccportal.utils;

import com.yunqu.ccportal.base.CommonLogger;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.base.QueryFactory;
import com.yunqu.ccportal.listener.InitListener;

public class DBMonitor implements Runnable{
	
	
	private static boolean dbState = true;
	
	private static long checkTime = 0;
	
	public static boolean isOk(){
		
		if("N".equalsIgnoreCase(Constants.getDbStateConfig())){
			CommonLogger.getLogger().warn("数据库检查状态异常，当前处于离线运行状态");
			return false;
		}
		if(checkTime == 0) return true;
		long timer = System.currentTimeMillis() - checkTime;
		//如果数据库的检查时间少于10秒，则代表不可用。
		if(timer > 10*1000){
			return false;
		}
		return dbState;
	}

	@Override
	public void run() {
		
		try {
			Thread.sleep(30*1000);
		} catch (Exception ex) {
			// TODO: handle exception
		}
		
		CommonLogger.logger.info("DBMonitor 线程 "+ Thread.currentThread().getName()+" 启动...");
		while(InitListener.start){
			try {
				if(dbState)  Thread.sleep(5*1000);
			} catch (Exception ex) {
				// TODO: handle exception
			}
			
			long timer = System.currentTimeMillis();
			
			dbState = dbcheck();
			
			timer = System.currentTimeMillis() - timer;
			if(timer<3*1000){  //执行时间少于3秒则才更新数据库的检查时间，否则认为目前数据库是不可用状态
				checkTime = System.currentTimeMillis();
			}
		}
		CommonLogger.logger.info("DBMonitor 线程 "+ Thread.currentThread().getName()+" 停止...");
	}
	/**
	 * 检查数据库的状态是否正常。
	 * @return
	 */
	public boolean dbcheck(){
		try {
			String sql = "select PETRA_ID from CC_PETRA_RES ";
			QueryFactory.getWriteQuery().queryForRow(sql, new Object[]{});
		} catch (Exception ex) {
			CommonLogger.getLogger().error(ex,ex);
			return false;
		}
		return true;
	}
	
	

}
