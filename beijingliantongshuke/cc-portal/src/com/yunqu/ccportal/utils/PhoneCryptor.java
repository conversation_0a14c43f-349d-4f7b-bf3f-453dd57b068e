package com.yunqu.ccportal.utils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.crypt.DESUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.QueryFactory;

/**
 * 号码加解密统一接口
 *
 */
public class PhoneCryptor {
	
	private  Map<String,Boolean[]> cryptConf = new HashMap<String,Boolean[]>();
	private  long timer = System.currentTimeMillis();
	private  String prefix = "#";
	private  DESUtil desUtil;
	
	 private static class Holder{
		  private static PhoneCryptor cryptor=new PhoneCryptor();
	}
	public PhoneCryptor(){
		try {
			desUtil=new DESUtil();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	public static PhoneCryptor getInstance(){
		return Holder.cryptor;
	}
	/**
	 * 数据库加密
	 * @param entId
	 * @return
	 */
	public boolean iscrypt(String entId){
		Boolean[] bl=getIscrypt(entId);
		return bl[0];
	}
	/**
	 * 页面显示的号码是否要加密
	 * @param entId
	 * @return
	 */
	public boolean showcrypt(String entId){
		Boolean[] bl=getIscrypt(entId);
		return bl[1];
	}
	/**
	 * 数据库加密
	 * @param entId
	 * @return
	 */
	public Boolean[] getIscrypt(String entId){
		Boolean[] bl = cryptConf.get(entId);
		boolean flag=true;
		if(bl!=null){
	    	//如果超过缓存时间(5分钟)，则重新获取
	    	if(System.currentTimeMillis()-timer>300*1000){
	    		cryptConf.clear();; 
	    		timer=System.currentTimeMillis();
	    		flag = true;
	    	}else{
	    		flag=false;
	    	}
    	}
    	if(flag){
    		bl=new Boolean[2];
    		String sql = "select ENT_EXT_CONF from CC_ENT where ENT_ID = ?";
    		try {
				String  extConf = QueryFactory.getQuery(entId).queryForString(sql,new Object[]{entId});
				if(StringUtils.isNotBlank(extConf)){
					JSONObject extConfJson =  JSONObject.parseObject(extConf);
					if(extConfJson!=null){
						bl[0] = extConfJson.getBoolean("CCBAR_PHONE_CRYPT");//数据库加密
						bl[1] = extConfJson.getBoolean("PHONE_CRYPT_SHOW");//页面显示加密
						if(bl[0] == null){
							bl[0] = Boolean.FALSE;
						}
						if(bl[1] == null){
							bl[1] = Boolean.FALSE;
						}
					}
				}else{
					bl[0] = Boolean.FALSE;
					bl[1] = Boolean.FALSE;
				}
			} catch (Exception ex) {
				bl[0] = Boolean.FALSE;
				bl[1] = Boolean.FALSE;
				ex.printStackTrace();
			}
    		cryptConf.put(entId, bl);
    	}
    	return bl;
	}
	
	
	/**
	 * 号码加密(先根据配置判断是否要加密)，格式：(3des)密文
	 * @param phoneNum
	 * @return
	 */
    public String encrypt(String entId,String phoneNum) {
    	if(phoneNum.startsWith(prefix)){
    		return phoneNum;
    	}
    	Boolean bl = iscrypt(entId);
    	if(bl){
    		phoneNum = prefix + desUtil.encrypt(phoneNum.getBytes());
    	}
    	return phoneNum;
    }
    /**
     * 号码加密(直接加密)，格式：(3des)密文
     * @param phoneNum
     * @return
     */
    public String encrypt(String phoneNum) {
    	if(phoneNum.startsWith(prefix)){
    		return phoneNum;
    	}
		return  prefix + desUtil.encrypt(phoneNum.getBytes());
    }

	/**
	 *号码解密
	 */
    public String decrypt(String phoneNum){
    	if(StringUtils.isBlank(phoneNum))return "";
    	if(phoneNum.startsWith(prefix)){
    		phoneNum =phoneNum.substring(prefix.length());
    	}else{
    		return phoneNum;
    	}
    	return new String(desUtil.decrypt(phoneNum));
    }
    public String decrypt(String phoneNum,boolean placeholder){
    	return phonePlaceholder(decrypt(phoneNum),placeholder);
    }
    /**
     * 
     * @param jsonObject
     * @param fields 要解密的字段
     * @param placeholder 是否要加占位符
     * @return 解密后的数据
     */
    public JSONObject decrypt(JSONObject jsonObject,String[] fields,boolean placeholder){
    	if(fields==null){
    		Set<String> set=jsonObject.keySet();
    		Iterator<String> it=set.iterator();
    		while(it.hasNext()){
    			String key=it.next();
    			String value=jsonObject.getString(key);
    			if(value.startsWith(prefix)){
    				jsonObject.put(key, value);
    				jsonObject.put("_"+key, phonePlaceholder(decrypt(value),false));//明文,下划线开头
    			}
    		}
    	}else{
    		for(String field:fields){
    			String value=jsonObject.getString(field);
    			jsonObject.put(field,value);//保留
    			jsonObject.put("_"+field, phonePlaceholder(decrypt(value),false));//明文,下划线开头
    		}
    	}
    	return jsonObject;
    }
    private String phonePlaceholder(String phone,boolean placeholder){
    	if(!placeholder)return phone;
    	if(phone.length()<4)return "****";
    	return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
    }
    public JSONObject decrypt(JSONObject jsonObject,String[] fields){
    	return decrypt(jsonObject,fields,false);
    }
    /**
     * 解密
     * @param list
     * @return
     */
    public List<JSONObject> decrypt(List<JSONObject> list){
    	return decrypt(list,null,false);
    }
    
    public List<JSONObject> decrypt(List<JSONObject> list,String[] fields,boolean placeholder){
    	for(JSONObject jsonObject:list){
    		this.decrypt(jsonObject,fields,placeholder);
    	}
    	return list;
    }
    /**
     * 解密
     * @param array
     * @return
     */
    public List<JSONObject> decrypt(JSONArray array){
    	return decrypt(array,null,false);
    }
    public List<JSONObject> decrypt(JSONArray array,boolean placeholder){
    	return decrypt(JSONObject.parseArray(array.toJSONString(),JSONObject.class),null,placeholder);
    }
    /**
     * 
     * @param array 待解密的数组
     * @param fields 指定要解密的字段
     * @param placeholder 是否解密后再次用***加密
     * @return
     */
    public List<JSONObject> decrypt(JSONArray array,String[] fields,boolean placeholder){
    	return decrypt(JSONObject.parseArray(array.toJSONString(),JSONObject.class),fields,placeholder);
    }
    
    public List<Map<String, String>> decryptMap(List<Map<String, String>> data, String[] fields, boolean placeholder) {
		for (Map<String, String> map : data) {
			this.decryptMap(map,fields,placeholder);
		}
		return data;
	}
	
	public Map<String, String> decryptMap(Map<String, String> map,String[] fields,boolean placeholder){
		if(fields==null){
    		Set<String> set=map.keySet();
    		Iterator<String> it=set.iterator();
    		while(it.hasNext()){
    			String key=it.next();
    			String value=map.get(key);
    			if(value.startsWith(prefix)){
    				map.put(key, phonePlaceholder(decrypt(value),placeholder));
    				map.put("_"+key, phonePlaceholder(decrypt(value),false));//明文,下划线开头
    			}
    		}
    	}else{
    		for(String field:fields){
    			String value=map.get(field);
    			map.put(field,phonePlaceholder(decrypt(value),placeholder));
    			map.put("_"+field, phonePlaceholder(decrypt(value),false));//明文,下划线开头
    		}
    	}
    	return map;
	}
	public  Map<String,Boolean[]> getAll(){
		return cryptConf;
	}
    public  void removeAll(){
    	if(cryptConf!=null)cryptConf.clear();
    }
    
}
