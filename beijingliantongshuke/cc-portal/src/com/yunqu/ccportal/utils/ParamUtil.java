package com.yunqu.ccportal.utils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.util.SystemParamUtil;

/**
 * 参数格式化工具
 * <AUTHOR>
 *
 */
public class ParamUtil {

	/**
	 * 获取呼叫状态
	 * @param causeId
	 * @return
	 */
	public static String formatCause(String causeId){
		String str = "";
		switch (causeId) {
		case "0":
			str = "成功"; 
			break;
		case "1":
			str = "无人应答"; 
			break;
		case "2":
			str = "用户忙"; 
			break;
		case "3":
			str = "用户挂机"; 
			break;
		case "4":
			str = "网络忙"; 
			break;
		case "5":
			str = "空号"; 
			break;
		case "6":
			str = "拒接"; 
			break;
		case "7":
			str = "关机"; 
			break;
		case "8":
			str = "停机"; 
			break;
		case "9":
			str = "不在服务区"; 
			break;
		case "10":
			str = "传真机"; 
			break;
		case "11":
			str = "欠费"; 
			break;
		case "12":
			str = "重复号码"; 
			break;
		case "13":
			str = "电话总机"; 
			break;
		case "14":
			str = "久叫不应"; 
			break;
		case "98":
			str = "坐席挂机"; 
			break;
		case "99":
			str = "系统错误"; 
			break;
		case "100":
			str = "未接通"; 
			break;
		default:
			str = "";
			break;
		}
		return str;
	}
	
	/**
	 * String转化Int
	 * @param str
	 * @return
	 */
	public static int parseStrToInt(String str){
		if(StringUtils.isBlank(str)){
			return 0;
		}
		try {
			return Integer.parseInt(str);
		} catch (Exception e) {
		}
		return 0;
		
	}
	

	/**
	 * String转化Long
	 * @param str
	 * @return
	 */
	public static long parseStrToLong(String str){
		if(StringUtils.isBlank(str)){
			return 0;
		}
		try {
			return Long.parseLong(str);
		} catch (Exception e) {
		}
		return 0;
	}
	
	/**
	 * 格式化秒,将秒数转成 "00小时00分00秒"格式
	 * @param value
	 * @return
	 */
	public static String parseTime(String value) {
	    if(!StringUtils.isNumeric(value)){
	    	return "00小时00分00秒";
	    }
	    int theTime = Integer.valueOf(value);// 秒
	    int theTime1 = 0;// 分
	    int theTime2 = 0;// 小时
	    if(theTime > 60) {
	        theTime1 = theTime/60;
	        theTime = theTime%60;
	            if(theTime1 > 60) {
	            theTime2 = theTime1/60;
	            theTime1 = theTime1%60;
	            }
	    }
	    String result = ""+theTime+"秒";
	        if(theTime1 > 0) {
	        result = ""+theTime1+"分"+result;
	        }
	        if(theTime2 > 0) {
	        result = ""+theTime2+"小时"+result;
	        }
	    return result;
	}
	
	/**
	 * 格式化秒,将秒数转成 "00小时00分00秒"格式
	 * @param value
	 * @return
	 */
	public static String parseTime2(String value) {
	    if(!StringUtils.isNumeric(value)){
	    	return "00:00:00";
	    }
	    int theTime = Integer.valueOf(value);// 秒
	    int theTime1 = 0;// 分
	    int theTime2 = 0;// 小时
	    if(theTime > 60) {
	        theTime1 = theTime/60;
	        theTime = theTime%60;
            if(theTime1 > 60) {
	            theTime2 = theTime1/60;
	            theTime1 = theTime1%60;
            }
	    }
	    String result = theTime+"";
	    
	    if(theTime<10) {
	    	result = "0"+theTime;
	    }
	    
	    
	    if(theTime1<10) {
	    	result = "0"+theTime1+":"+result;
	    }else {
	    	result = theTime1+":"+result;
	    }
	    
	    if(theTime2<10) {
	    	result = "0"+theTime2+":"+result;
	    }else {
	    	result = theTime2+":"+result;
	    }
	    
	    return result;
	}
	
	/**
	 * 把时间字符串：yyyy-MM-dd 转化为数字yyyyMMdd
	 * @param date
	 * @return
	 */
	public static int parseDateToInt(String date){
		try {
			SimpleDateFormat strParseDate = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat dateFormatStr = new SimpleDateFormat("yyyyMMdd");
			date = dateFormatStr.format(strParseDate.parse(date));
			return Integer.parseInt(date);
		} catch (ParseException e) {
			return 0;
		}
	}
	
	/**
	 * 截取日期时间
	 */
	public static String cutDateTime(String date){
		if(StringUtils.isNotBlank(date)){
			return date.substring(11);
		}
		return "";
	}
	
	/**
	 * 请两个数的商
	 * @param value1
	 * @param value2
	 * @return
	 */
	public static int divideValue(String value1, String value2) {
        if (!StringUtils.isNumeric(value1) || !StringUtils.isNumeric(value2)
                || "0".equals(value1) || "0".equals(value2)) {
            return 0;
        }
        BigDecimal b1 = new BigDecimal(value1);
        BigDecimal b2 = new BigDecimal(value2);
        BigDecimal b3 = b1.divide(b2,4,BigDecimal.ROUND_HALF_UP).setScale(0, BigDecimal.ROUND_HALF_UP);
        return b3.intValue();
    }
	
	/**
	 * 请两个数得百分比
	 * @param value1
	 * @param value2
	 * @return
	 */
	public static String divideDouble(String value1, String value2) {
        if (!StringUtils.isNumeric(value1) || !StringUtils.isNumeric(value2)
                || "0".equals(value1) || "0".equals(value2)) {
            return "0%";
        }
        BigDecimal f1 = new BigDecimal((float) Integer.valueOf(value1) / Integer.valueOf(value2)).setScale(4, BigDecimal.ROUND_HALF_UP);
        BigDecimal f2 = f1.multiply(new BigDecimal(100));
        double f3 = f2.doubleValue();
        return f3 + "%";
    }
	
	private static Map<String,String> entParamMap = new HashMap<String,String>();

	/**
	 * 获取企业参数配置内容
	 * 兼容是数据库异常的情况下，也需要正常返回值，避免redis也出现异常，该值在内存里缓存一份；数据库正常时，按正常情况获取
	 * @param dbName
	 * @param entId
	 * @param busiOrderId
	 * @param appName
	 * @param key
	 * @return
	 */
	public static String getSystemEntParam(String dbName,String entId, String busiOrderId, String appName,String key){
		String cacheKey = "cc-portal-"+dbName+entId+busiOrderId+appName+key;
		if(DBMonitor.isOk()){
			
			String val =  SystemParamUtil.getEntParam(dbName, entId, busiOrderId, appName, key);
			entParamMap.put(cacheKey, val);
			return val;
		}
		return entParamMap.get(cacheKey);
	}
	
}
