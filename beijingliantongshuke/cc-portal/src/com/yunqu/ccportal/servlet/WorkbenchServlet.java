package com.yunqu.ccportal.servlet;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IpUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.MacUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.base.CommonLogger;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.base.QueryFactory;
import com.yunqu.ccportal.utils.DBMonitor;
import com.yunqu.ccportal.utils.JWTUtils;
import com.yunqu.ccportal.utils.ParamUtil;
import com.yunqu.ccportal.utils.PhoneCryptor;
import com.yunqu.ccportal.vo.UserResource;
import com.yunqu.ccportal.vo.UserResourceRowMapper;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
@WebServlet("/workbench/*")
public class WorkbenchServlet extends AppBaseServlet {
	
	private Logger logger = CommonLogger.logger;
	
	private static final long serialVersionUID = 1L;
	
	public void index(){
		actionForIndex();
	}
	public void actionForIndex(){
		UserModel user = UserUtil.getUser(this.getRequest());
		YCUserPrincipal  principal = this.getUserPrincipal();
		principal.setBusiId(Constants.BUSI_ID);
		setAttr("user", principal);
		setAttr("isAgentUser","true");
		
		setAttr("isOpenWebIM", CEConstants.isOpenWebIM() ? "Y":"N");
		setAttr("isCommunication",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "COMMUNICATION"));
		setAttr("showcrypt", PhoneCryptor.getInstance().showcrypt(getEntId()));
		setAttr("satisKey",satisKey());//满意度，话务条上转满意度按钮
		
		//用户头像
		String userIcon = user.getIcon();
		if(StringUtils.isBlank(userIcon)){
			userIcon = "/cc-portal/portal/images/head.png";
		}
		setAttr("userIcon",userIcon);	
		
		//前端无操作自动退出的时间（单位：小时）
		setAttr("autoLogoutTimeOut",Constants.AUTO_LOGOUT_TIME_OUT);		
		setAttr("showNotes",Constants.showNotes());		
		
				
		//坐席助手
		setAttr("agentAssistant",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "AGENT_ASSISTANT"));
		setAttr("slotKeys",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "SLOT_KEYS"));
		setAttr("purposeKey",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "PURPOSE_KEY"));
		setAttr("addressStatus",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "ADDRESS_STATUS"));
		setAttr("assistantExpandUrl",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "ASSISTANT_EXPAND_URL"));
		
		
		//首页刷新待跟进记录弹屏的频率，在cc-workbench首页里使用
		setAttr("refreshFollowMiniutes",CommonUtil.parseInt(SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "REFRESH_FOLLOW_MINIUTES")));
		
		//3.3#********-1 远程协助功能：0-关闭 1-开启
		setAttr("remoteSwitch",SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "remoteSwitch"));
		
		
		setAttr("busiOrderId",getBusiOrderId());
		setAttr("userAccount",getUserAccount());
		if(StringUtils.isNotBlank(user.getNickName())){
			setAttr("userNickName",user.getNickName());
		}else{
			setAttr("userNickName",user.getUserName());
		}
		setAttr("userAgentPhone",principal.getAgentPhone());
		
		//用户登录时将选择的语言推送给后台 SpecServlet.actionForSetUserLang , 登录后再回取一个
		String lang = CacheUtil.get("USER-LANG-"+user.getUserAcc());
		if(StringUtils.isNotBlank(lang)){
			UserUtil.setUserLang(this.getRequest(), lang);
		}
		
		
		
		setAttr("portalTitle", Constants.PORTAL_TITLE); 
		try {
			//设置logo
			String portalLogo = Constants.PORTAL_LOGO;
			
			String logoUrl = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(),"cc-base", "LOGO_URL");
			if(StringUtils.isNotBlank(logoUrl)){
				portalLogo = "/cc-base/servlet/attachment?action=download&filePath="+URLEncoder.encode(logoUrl, "UTF-8");
			}
			
			setAttr("portalLogo", portalLogo);
			
			//设置门户名称
			String portalTitle = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(),"cc-base", "PORTAL_TITLE");
			
			//门户名称可以设置为空
			if(StringUtils.isNotBlank(logoUrl)){
				setAttr("portalTitle",portalTitle);
			}else{
				if(StringUtils.isNotBlank(portalTitle)){
					setAttr("portalTitle",portalTitle);
				}else{
					setAttr("portalTitle", Constants.PORTAL_TITLE); 
				}
			}
			
			//系统在浏览器页签上的标题
			String portalIconUrl = SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "PORTAL_ICON_URL");
			if(StringUtils.isNotBlank(portalIconUrl)){
				portalIconUrl = "/cc-base/servlet/attachment?action=download&filePath="+URLEncoder.encode(portalIconUrl, "UTF-8");
			}
			if(StringUtils.isBlank(portalIconUrl)){
				portalIconUrl = portalLogo;
			}
			setAttr("portalIconUrl",portalIconUrl);
			
			//首页
			String welcomeUrl = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(),"cc-base", "WELCOME_PAGE_URL");
			if(StringUtils.isBlank(welcomeUrl)){
				welcomeUrl = Constants.WELCOME_PAGE_URL;
			}
			setAttr("welcomeUrl", welcomeUrl);
			
			setAttr("portalLogo", portalLogo);
			
			
			//update20210408 获取密码修改时间，判断是否需要修改密码; 不动jar包（如果升级了jar包，可以直接从user对象里取extConf，先这样查，兼容老版本）
			setAttr("needUpdatePwd", "N");
			//是否开启密码修改通知
			String pwdUpdateFlag = Constants.getPwdUpdateFlag();
			
			if(pwdUpdateFlag.equals("Y")){
				int pwdUpdateDays = Constants.getPwdUpdateDays();
				setAttr("needUpdatePwd", "Y");
				
				EasySQL sql = new EasySQL();
				sql.append(" SELECT t1.EXT_CONF FROM " ).append( getTableName("CC_BUSI_USER") ) .append(" t1 ");
				sql.append(user.getUserId()," WHERE USER_ID = ? ");
				sql.append(user.getEpCode()," AND ENT_ID = ? ");
				sql.append(user.getBusiOrderId()," AND BUSI_ORDER_ID = ? ");
				JSONObject ccBusiUser = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				if(ccBusiUser!=null){
					JSONObject ext =  ccBusiUser.getJSONObject("EXT_CONF");
					if(ext!=null){
						String updateDateTime = ext.getString("UPDATE_PWD_TIME");
						if(StringUtils.isNotBlank(updateDateTime)){
							int bwDays = DateUtil.bwDays( updateDateTime,DateUtil.getCurrentDateStr(),DateUtil.TIME_FORMAT);
							//开启密码提醒后，仅仅当密码为过期时不提醒修改密码，其他的都提醒修改密码
							if(bwDays>0 && bwDays < pwdUpdateDays){
								setAttr("needUpdatePwd", "N");
							}else{
								logger.info("["+user.getUserAcc()+"] 用户本次登录需要修改密码，已超过"+pwdUpdateDays+"天, 上次修改时间为:"+updateDateTime);
							}
						}else{
							logger.info("["+user.getUserAcc()+"] 用户本次登录需要修改密码，未修改过密码， 上次修改时间为:"+updateDateTime);
						}
					}
				}
			}
			
			//3.1#20211119-1 设置振铃弹屏地址
			//振铃弹屏类型：1-默认，弹出接听小窗口，0-自定义
			String callRingType = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(),"cc-base", "CALL_RINGING_TYPE");
			if("0".equals(callRingType)){
				EasySQL sql = new EasySQL("SELECT NAME,CALLURL,OPEN_TYPE FROM "+getDbName()+".C_CF_CALLURL WHERE CALLURL_TYPE=? AND ENT_ID=? AND BUSI_ORDER_ID=?"); 
				EasyRow row = QueryFactory.getWriteQuery().queryForRow(sql.getSQL(), "2",this.getEntId(),this.getBusiOrderId());
				String url = "";
				if(row!=null && StringUtils.isNotBlank(row.getColumnValue("CALLURL")) ){
					url = row.getColumnValue("CALLURL");
				}
				if(StringUtils.isBlank(url)){
					logger.warn("企业["+getEntId()+"]设置了自定义振铃弹屏，但是系统管理-参数配置-企业参数里未配置振铃弹屏地址，默认使用弹屏振铃框.");
				}else{
					setAttr("evtAlteringUrl", url);
				}
			}
			
		} catch (Exception e) { 
				this.error(e.getMessage(), e);
				setAttr("portalLogo", Constants.PORTAL_LOGO);
				setAttr("portalTitle", Constants.PORTAL_TITLE); 
		}
		setAttr("isShowEditPassword", Constants.IS_SHOW_EDIT_PASSWORD);
		
		//坐席语音技能组数量
		int voiceSKillCount = 0;
		int mediaSKillCount = 0;
		List<JSONObject> list = this.getGroupSkills();
		String[] skills = null;
		if(list != null){
			skills = new String[list.size()];
			for(int i = 0 ; i < list.size(); i++){
				String skillGroupType = list.get(i).getString("SKILL_GROUP_TYPE");
				String skillGroupId = list.get(i).getString("SKILL_GROUP_ID");
				skills[i] = skillGroupId;
				if("voice".equalsIgnoreCase(skillGroupType)){
					voiceSKillCount ++;
				}else if("media".equalsIgnoreCase(skillGroupType)){
					mediaSKillCount ++;
				}
			}
			
			//设置坐席的类型  all为混合坐席 "3.1#20210611-1"
			String agentType = "voice";
			if(voiceSKillCount>0 && mediaSKillCount>0){
				agentType = "all";
			}else if(mediaSKillCount>0){
				agentType = "media";
			}
			user.getYcUserPrincipal().setAttribute("agentType", agentType);
		}
		
		
		setAttr("voiceSKillCount", voiceSKillCount);
		principal.setAttribute("skillGroupIds", skills);
		
		//3.1#20211110-1 由于登录和刷新使用同一个界面，当用户登录时，需要设置httpsessonId，目前只有通过cc-portal登录，存在sessionId；其它方式登录的设置的0，避免该处重复调用
		if(StringUtils.isBlank(user.getUserHttpSessionId())){
			
			//3.1#20211110-1 开启登录限制后，对于不允许重复登录的方式，需要检测是否该账号已登录，已登录则不允许重复登录
			//登录限制方式：1:允许重复登录,2:账号已登录后不允许重复登录,3:账号登录后踢出之前登录的用户
			String repeatLoginType = ConfigUtil.getString("cc-base", "REPEAT_LOGIN_TYPE", "0");
			if("2".equals(repeatLoginType) && UserUtil.userIsLogin(user.getUserAcc())){
				CommonCoreLogger.getUserLogger().info("用户["+user.getUserAcc()+"]准备登录, 但是该用户在其它地方登录,本次不允许登录");
				actionForLoginPage("2");
				return;
			}
			
			//3.1#20211110-1 为本次登录生成唯一的id,避免坐席使用同一个浏览器开两个tab页登录，在sessionId上加上前缀
			String loginHttpSessionid = this.getRequest().getSession().getId()+"-"+System.currentTimeMillis();
			
			CommonCoreLogger.getUserLogger().info("用户["+user.getUserAcc()+"]准备登录, 本次登录的httpSessionid:"+loginHttpSessionid);
			
			String oldId = UserUtil.getUserLoginHttpSessionId(user.getUserAcc());
			if(StringUtils.isNotBlank(oldId)){
				CommonCoreLogger.getUserLogger().info("用户["+user.getUserAcc()+"]登录校验, 该账号已在其它地方登录,本次登录后其它地方会退出，原httpSessionId:"+oldId);
			}
			
			//设置坐席登录的httpsessonId
			UserUtil.setUserLoginHttpSessionId(user.getUserAcc(), loginHttpSessionid);
			user.setUserHttpSessionId(loginHttpSessionid);
			user.setLoginServerIp(IpUtil.getIpAddr());
			user.setLoginServerMac(MacUtil.getLocalMac());
			user.setLoginServerNodeName(MacUtil.getLocalMachineName());
			
			//3.1#20211203-1 设置坐席最后登录ip
			try {
				EasyRecord userRecord = new EasyRecord("CC_USER","USER_ID");
				userRecord.put("USER_ID", user.getUserId());
				userRecord.put("LAST_LOGIN_IP", IpUtil.getIpAddr(this.getRequest()));
				QueryFactory.getWriteQuery().update(userRecord);
			} catch (Exception e) {
				CommonCoreLogger.getUserLogger().info("用户["+user.getUserAcc()+"]记录登录ip异常:"+e.getMessage(),e);
			}
			
			//设置坐席登录的缓存
			JSONObject userJson = new JSONObject();
			userJson.put("time", DateUtil.getCurrentDateStr());
			userJson.put("loginIp", user.getLoginIp());
			userJson.put("loginServerIp", user.getLoginServerIp());
			userJson.put("loginServerMac", user.getLoginServerMac());
			userJson.put("logingServerNodeName", user.getLoginServerNodeName());
			userJson.put("loginIp-curr", IpUtil.getIpAddr(this.getRequest()));
			userJson.put("loginServerMac-curr", MacUtil.getLocalMac());
			userJson.put("httpSessionId", loginHttpSessionid);
			CacheUtil.put("CC-BASE-USER-ONLINE-STATE-"+user.getUserAcc(), userJson.toJSONString(),120);
		}
		
		renderJsp("/portal/index.jsp");
	}
	
	public void actionForRedirectWitchToken() {
		try {
			// 是否强制刷新token
			String force = getPara("force");
			// 重定向地址
			String url = getPara("url");
			url = URLDecoder.decode(url, "UTF-8");

			UserModel user = UserUtil.getUser(getRequest());
			if (user == null) {
				renderJson(EasyResult.error(400, "未登录用户"));
			}
			String key = "MARS_JWT_TOKEN_" + user.getUserAcc();
			// 获取token，通过用户密码加盐，缓存20分钟，过期重新生成
			String token = cache.get(key);
			if (StringUtils.isBlank(token) || JWTUtils.needRefresh(token) || "true".equals(force)) {
				token = JWTUtils.sign(user, Constants.getServiceInterfacePwd());
				cache.put(key, token, 2*60);
			}
			if (url.indexOf("?")!=-1) {
				url = url +"&token="+ token;
			} else {
				url = url +"?token="+ token;
			}
			
			redirect(url);
		} catch (Exception e) {
			logger.error(e.getMessage());
			renderText("重定向异常: "+ e.getMessage());
		}
	}
	
	/**
	 * 获取用户token
	 * @return
	 */
	public EasyResult queryForGetToken() {
		try {
			UserModel user = UserUtil.getUser(getRequest());
			if (user == null) {
				return EasyResult.error(400, "未登录用户");
			}
			String key = "MARS_JWT_TOKEN_" + user.getUserAcc();
			// 是否强制刷新
			String force = getPara("force");
			// 获取token，通过用户密码加盐，缓存20分钟，过期重新生成
			String token = cache.get(key);
			if (StringUtils.isBlank(token) || JWTUtils.needRefresh(token) || "true".equals(force)) {
				token = JWTUtils.sign(user, Constants.getServiceInterfacePwd());
				cache.put(key, token, 2*60);
			}
			return EasyResult.ok(token);
		} catch (Exception e) {
			this.error("获取token异常: " + e.getMessage(), e);
			return EasyResult.error(500, e.getMessage());
		}
	}
	
	/**
	 * 获取用户信息，用于单点登录界面跳转
	 * @return
	 */
	public EasyResult actionForGetUserInfo() {
		UserModel user = UserUtil.getUser(this.getRequest());
		Map<String,String> map = new HashMap<String,String>();
		try {
			String entId = getEntId();
			String busiOrderId = getBusiOrderId();
			map.put("entId", entId);
			map.put("busiOrderId", busiOrderId);
			map.put("agentId", user.getUserId());
			map.put("userAccount", user.getUserAcc());
			map.put("userId", user.getUserId());
			map.put("userName", user.getUserName());
			
			map.put("busiId", this.getBusiId());
			//设置门户名称
			String portalTitle = SystemParamUtil.getEntParam(getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "PORTAL_TITLE");
			//门户名称可以设置为空
			if(StringUtils.isBlank(portalTitle)){
				portalTitle = Constants.PORTAL_TITLE;
			}
			map.put("entName", portalTitle);
			if(StringUtils.isNotBlank(user.getNickName())){
				map.put("userNickName", user.getNickName());
			}else{
				map.put("userNickName", user.getUserName());
			}
			setAttr("userAgentPhone",user.getUserNo());
		} catch (Exception e) {
			getLogger().error(e.getMessage(),e);
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 获取用户信息:"+e.getMessage(),e);
		}
		return EasyResult.ok(map);
	}
	
	public EasyResult actionForSetDefaultDept() {
		String deptCode = this.getJsonPara("deptCode");
		UserModel user = UserUtil.getUser(getRequest());
		for(DeptModel dept : user.getDepts()) {
			if(dept.getDeptCode().equals(deptCode)) {
				user.setDept(dept);
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUserDepts() {
		UserModel user = UserUtil.getUser(getRequest());
		return EasyResult.ok(JsonUtil.toJSONArray(JsonUtil.toJSONString(user.getDepts()))).put("deptCode", user.getDeptCode());
	}
	
	public String satisKey(){
		String satisyNo="";
		try {
			satisyNo=SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "SATISY_FLOW_NO");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return satisyNo;
	}
	
	private JSONArray createChild(UserModel user,List<UserResource> list ,String presId){
		JSONArray arr = new JSONArray();
		for(UserResource res:list){
			if(!res.getPresId().equals(presId)){
				continue;
			}
			String url = StringUtils.trimToEmpty(res.getResUrl());
			String resIcon=StringUtils.trimToEmpty(res.getResIcon());
			String resName = BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, res.getResName());
			
			JSONObject item = new JSONObject();
			item.put("pid", res.getPresId());
			item.put("id", res.getResId());
			if("Y".equals(Constants.PORTAL_RES_CODE)) {
				item.put("title", resName + "(" + res.getResCode() + ")");
			} else {
				item.put("title", resName);
			}
			item.put("url", res.getResUrl());
			item.put("icon", resIcon);
			item.put("search", resName + "(" + res.getResCode() + ")");
			
			JSONArray childArr  = null;  
			//菜单url不为空时，需要继续查看是否有子节点
			if(StringUtils.isBlank(url)){
				//由于调整了菜单查询方式，对于查询某个子节点的菜单时，需要重新查询一次
				if(!"2000".equals(presId)){
					List<UserResource> ls  = null;
					
					//仅仅存在子节点的时候才查库
					if(res.getSons()>0){
						ls = getUserMenuList(user, res.getResId());
					}else{
						ls = list;
					}
					childArr  = this.createChild(user,ls, res.getResId());
				}else{
					childArr  = this.createChild(user,list, res.getResId());
				}
				item.put("nav", childArr);
			}
			
			
			arr.add(item);
		}
		return arr;
	}
	
	
	public String getEntResBusi(String entId,String busiId){
		String sql="select RES_ID from CC_ENT_BUSI_RES where ENT_ID = ? and BUSI_ID =?";
		try {
			 List<JSONObject> list= cache.get("ENT_RES_"+entId+"_"+busiId);
			 if(list==null){
				 EasyQuery query=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
				 list=query.queryForList(sql, new Object[]{entId,busiId},new JSONMapperImpl());
				 cache.put("ENT_RES_"+entId+"_"+busiId, list,180);
			 }
			 if(list==null||list.size()==0){
				 return null;
			 }
			 StringBuffer sb=new StringBuffer();
			 for(JSONObject jsonObject:list){
				 sb.append("'"+jsonObject.getString("RES_ID")+"',");
			 }
			 return sb.substring(0,sb.length()-1);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return null;
	}
	
	public JSONObject queryForMenu(){
		try {
			//3.2#20220517-1 兼容数据库异常时，能正常返回值
			if(!DBMonitor.isOk()){
				JSONObject json = (JSONObject) this.getRequest().getSession().getAttribute("CC_PORTAL_USER_MENU");
				CommonLogger.logger.warn("数据库存在异常,直接返回会话session缓存里的用户菜单");
				return json;
			}
			UserModel user = UserUtil.getUser(this.getRequest());
			String resId = getJsonPara("resId");
			
			List<UserResource> list = getUserMenuList(user,resId);
			
			JSONObject  jsonObject = new JSONObject();
			//必须放到查询之后，避免影响查询
		    if (StringUtils.isBlank(resId)){
		    	 resId = "2000";
		    }
		    
			jsonObject.put("data", this.createChild(user, list, resId));
			this.getRequest().getSession().setAttribute("CC_PORTAL_USER_MENU", jsonObject);
			return jsonObject;
		} catch (Exception e) {
			CommonLogger.logger.error("获取用户菜单异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	private List<UserResource> getUserMenuList(UserModel user,String pResId) {
		try {
			EasySQL sql = new EasySQL();
			List<UserResource> list = null;
			String busiId = this.getUserPrincipal().getBusiId();
			this.getLogger().info("json=" + this.getJSONObject().toJSONString());
			
			//3.2#20220524-1  对菜单查询进行优化，原来使用RES_ID in 有权限的菜单(从缓存里取），查询一次预计30s以后，改成质检从表里关联，结合CC_ENT_BUSI_RES的主键，预计查询在0.1s左右
			if(this.isAdmin()){
				sql.append("SELECT DISTINCT(t3.RES_ID) RES_ID,t3.RES_CODE,t3.P_RES_ID,t3.RES_NAME,t3.RES_URL,t3.RES_ICON,t3.RES_TYPE,t3.IDX_ORDER, ");
				
				//对于查询非根节点的子集时，需要查询出子节点数量，避免递归处理时，浪费sql查询次数
				if(!"2000".equals(pResId)){
					sql.append(" (SELECT COUNT(1) FROM CC_BUSI_RES T6 WHERE T6.P_RES_ID = T3.RES_ID) SONS ");
				}else{
					sql.append(" 0 SONS ");
				}
				sql.append(" FROM CC_BUSI_RES t3,CC_ENT_BUSI_RES t5 WHERE 1=1 ");
				sql.append(" AND t3.RES_ID = t5.RES_ID  "  );
			    sql.append(" AND t3.BUSI_ID = t5.BUSI_ID  "  );
			    
			    sql.append(" and t3.RES_STATE = 0 ");
			    sql.append(busiId," and t3.BUSI_ID = ? ");
			    sql.append(" and t3.RES_TYPE = 2 ");
			    sql.append(user.getEpCode()," and t5.ENT_ID = ? ");
			    sql.append(busiId," and t5.BUSI_ID = ? ");
			    sql.append(pResId," and t3.P_RES_ID = ? ");  //当查询某个节点下的所有子节点时
			    
			    sql.append("order by t3.IDX_ORDER asc ");
			    
				list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new UserResourceRowMapper());
			}else{
			    sql.append("SELECT DISTINCT(t3.RES_ID) RES_ID,t3.RES_CODE,t3.P_RES_ID,t3.RES_NAME,t3.RES_URL,t3.RES_ICON,t3.RES_TYPE,t3.IDX_ORDER , ");
			  //对于查询非根节点的子集时，需要查询出子节点数量，避免递归处理时，浪费sql查询次数
				if(!"2000".equals(pResId)){
					sql.append(" (SELECT COUNT(1) FROM CC_BUSI_RES T6 WHERE T6.P_RES_ID = T3.RES_ID) SONS ");
				}else{
					sql.append(" 0 SONS ");
				}
				
			    sql.append("FROM CC_USER t0 ,");
			    sql.append(getTableName("CC_BUSI_USER ")+" t1 ,");
			    sql.append(getTableName("CC_ROLE_USER ")+" t4 ,");
			    sql.append(getTableName("CC_ROLE_RES " )+" t2 ,");
			    sql.append("CC_BUSI_RES t3 , ");
			    sql.append("CC_ENT_BUSI_RES t5 ");
			    sql.append("where ");
			    
			    sql.append("  t0.USER_ID = t1.USER_ID  "  );
			    sql.append(" AND t1.USER_ID = t4.USER_ID  "  );
			    sql.append(" AND t4.ROLE_ID = t2.ROLE_ID   "  );
			    sql.append(" AND t3.RES_ID = t2.RES_ID  "  );
			    sql.append(" AND t3.RES_ID = t5.RES_ID  "  );
			    sql.append(" AND t3.BUSI_ID = t5.BUSI_ID  "  );
			    
			    sql.append(" and t0.USER_STATE <> 9 ");	//在cc_user中查询用户是否被删除,删除入口在总台用户录入界面
			    sql.append(user.getUserId()," and t1.USER_ID = ? ");
			    sql.append(user.getBusiOrderId()," and t1.BUSI_ORDER_ID = ? "); 
			    sql.append(" and t1.USER_STATE <> 1 ");  //查询在当前业务中是否被停用,启用入口在坐席编辑界面
			    
			    sql.append(user.getBusiOrderId()," and t2.BUSI_ORDER_ID = ? "); 
			    sql.append(user.getEpCode()," and t2.ENT_ID = ? "); 
			    
			    sql.append(" and t3.RES_STATE = 0 ");
			    sql.append(busiId," and t3.BUSI_ID = ? ");
			    sql.append(" and t3.RES_TYPE = 2 ");
			    
			    sql.append(user.getEpCode()," and t5.ENT_ID = ? ");
			    sql.append(busiId," and t5.BUSI_ID = ? ");
			    sql.append(pResId," and t3.P_RES_ID = ? ");  //当查询某个节点下的所有子节点时
			    
			    sql.append("order by t3.IDX_ORDER asc ");
			    CommonLogger.logger.info("查询菜单=============>sql:" + sql.getSQL()+",param:" + JsonUtil.toJSONString(sql.getParams()));
			    list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new UserResourceRowMapper());
			}
			
			return list;
		} catch (Exception e) {
			CommonLogger.logger.error("获取用户菜单异常:"+e.getMessage(),e);
		}
		return null;
	}
	private String getPresBusi(String pResId, String busiId)
	  {
	    String resId = (String)this.cache.get("P_RES_" + pResId);
	    this.cache.delete("P_RES_" + pResId);//每次进来删除缓存重新查
	    if (StringUtils.isNotBlank(resId)) {
	      return resId;
	    }
	    JSONArray array = new JSONArray();
	    try {
	      array = getPresBusi(pResId, busiId, array);
	      if ((array != null) && (array.size() > 0)) {
	        StringBuffer sb = new StringBuffer();
	        for (int i = 0; i < array.size(); i++) {
	          sb.append("'" + array.getString(i) + "',");
	        }
	        resId = sb.substring(0, sb.length() - 1);
	        this.cache.put("P_RES_" + pResId, resId);
	        return resId;
	      }
	    } catch (SQLException e) {
	      error(e.getMessage(), e);
	    }
	    return null;
	  }
	
	private JSONArray getPresBusi(String pResId, String busiId, JSONArray array)
	    throws SQLException
	  {
	    String sql = "select RES_ID from CC_BUSI_RES where BUSI_ID = ? and P_RES_ID = ?";
	    List<JSONObject> list = getQuery().queryForList(sql, new Object[] { busiId, pResId }, new JSONMapperImpl());
	    for (JSONObject row : list) {
	      String resId = row.getString("RES_ID");
	      array.add(resId);
	      getPresBusi(resId, busiId, array);
	    }
	    return array;
	  }

	
	/**
	 * 获取技能组
	 * @return
	 */
	private List<JSONObject> getGroupSkills(){
		YCUserPrincipal userPrincipal = this.getUserPrincipal();
		try {
			String userId = userPrincipal.getUserId();
			List<JSONObject> list = this.getQuery().queryForList("select T1.SKILL_GROUP_ID,T2.SKILL_GROUP_TYPE from "+getTableName("CC_SKILL_GROUP_USER")+" T1, "+getTableName("CC_SKILL_GROUP")+" T2 where T1.SKill_GROUP_ID = T2.SKILL_GROUP_ID and T1.USER_ID = ? and T1.BUSI_ORDER_ID = ? and T1.ENT_ID = ?", new Object[]{userId,userPrincipal.getBusiOrderId(),userPrincipal.getEntId()}, new JSONMapperImpl());
			return list;
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return null;
	}
	
	/**
	 * 兼容老版本，处理逻辑转移到actionForLoginPage("1");
	 */
	public void actionForLoginPage(){
		actionForLoginPage("1");
		return;
	}
	/**
	 * 3.1#20211111-1
	 * 登出操作方法
	 * @param logoutType 1-正常退出  2-账号已登录，不允许登录，直接退出
	 */
	public void actionForLoginPage(String logoutType){
		String url=ConfigUtil.getString(Constants.APP_NAME,"LOGOUT_URL", "");
		if(StringUtils.isBlank(url)){
			url = appContext.getProperty("LOGIN_URL", "/yc-login");
		}
		if(url.indexOf("?") != -1){
			url = url + "&logoutType="+logoutType;
		}else{
			url = url + "?logoutType="+logoutType;
		}
		try {
			UserModel user = UserUtil.getUser(this.getRequest());
			
			//仅正常登录处理
			if(!"2".equals(logoutType)){
				//用户退出登录
		    	try {
		    		//3.1#20211110-1 用户退出时，如果当前登录的用户是有效登录，则移除登录的httpSessionId
		    		if(StringUtils.isNotBlank(user.getUserHttpSessionId()) && user.getUserHttpSessionId().equals(UserUtil.getUserLoginHttpSessionId(user.getUserAcc()))){
		    			UserUtil.removeUserLoginHttpSessionId(user.getUserAcc());
		    			CacheUtil.delete("CC-BASE-USER-ONLINE-STATE-"+user.getUserAcc());
		    		}
		        	EasyQuery query = QueryFactory.getWriteQuery();
		        	EasySQL sql = new EasySQL();
		        	sql.append(" UPDATE CC_LOGIN_LOG SET LOGOUT_TIME =? WHERE USER_ACCT=?  AND ENT_ID=? AND LOGOUT_TIME IS NULL ");
		        	query.execute(sql.getSQL(), DateUtil.getCurrentDateStr(),user.getUserAcc(),user.getEpCode());
		        	
		        	if(ServerContext.isDebug()){
		        		CommonCoreLogger.getUserLogger().info(CommonUtil.getClassNameAndMethod(this)+" 用户退出登录,更新CC_LOGIN_LOG.LOGOUT_TIME,userAcc="+user.getUserAcc()+",entId="+user.getEpCode());
		        	}
				} catch (Exception e) {
					CommonCoreLogger.getUserLogger().info(CommonUtil.getClassNameAndMethod(this)+" 用户退出登录异常,userAcc="+user.getUserAcc(),e);
				}
		    	
		    	//3.0#20210511-1 退出时，通知相关服务进行退出处理（如单点登录模式下，通知对方登出，定制模块实现该服务 )
		    	try {
					IService service = ServiceContext.getService("CC_LOGOUT_NOTICE_SERVICE");
					JSONObject json = new JSONObject();
					json.put("userAcc", user.getUserAcc());
					json.put("entId", user.getEpCode());
					json.put("busiOrderid", user.getBusiOrderId());
					json.put("userId", user.getUserId());
					json.put("userNo", user.getUserNo()); //工号
					json.put("logoutType", logoutType); 
					
					CommonCoreLogger.getUserLogger().info(CommonUtil.getClassNameAndMethod(this)+"[CC_LOGOUT_NOTICE_SERVICE]退出通知请求: >> "+json);
					JSONObject result = service.invoke(json);
					CommonCoreLogger.getUserLogger().info(CommonUtil.getClassNameAndMethod(this)+"[CC_LOGOUT_NOTICE_SERVICE]退出通知响应: << "+result);
				} catch (Exception e) {
					CommonCoreLogger.getUserLogger().info(CommonUtil.getClassNameAndMethod(this)+"无[CC_LOGOUT_NOTICE_SERVICE]服务 ");
				}
			}
			
			CommonCoreLogger.getUserLogger().info("用户["+user.getUserAcc()+"]登出,url="+url);
			this.getRequest().logout();
			this.getRequest().getSession().invalidate();
		} catch (Exception e) {
			CommonCoreLogger.getUserLogger().error(CommonUtil.getClassNameAndMethod(this)+"退出处理异常:"+e.getMessage(),e);
		}
		
		redirect(url);
		return;
	}
	@Override
	protected String getResId() {
		return null;
	}
	
	
	public JSONObject queryForIsWatemaek() throws SQLException{
		JSONObject object=new JSONObject();
		try {
			//3.2#20220517-1 兼容数据库异常时，能正常返回值
			
			//是否开启水印：0-关闭  1-开启
			String labelCode = ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "ISWATERMAEK");
			
			//3.2#20220422-1 没开启的情况下，默认关闭
			if(!"1".equals(labelCode)){
				labelCode = "0";
			}
			
			object.put("labelCode",labelCode);
			
			//水印是否只显示用户名：0-关闭  1-开启
			object.put("bodyCode",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "WATERMAEKBODY"));
		} catch (Exception e) {
			 getLogger().error("水印sql报错"+e);
		}
		return object;
	}
	
	public JSONObject queryForIsCcbrWorkmode() throws SQLException{
		JSONObject object=new JSONObject();
		try {
			//3.2#20220517-1 兼容数据库异常时，能正常返回值
			object.put("dbStatus", DBMonitor.isOk());
			object.put("workModeCode",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "CCBAR_WORKMODE"));
			object.put("showAutoAnswer",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "CCBAR_SHOW_AUTO_ANSWER")); //是否显示自动应答勾选框 1-是 0-否
			object.put("showVideoBtn",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "CCBAR_SHOW_VIDEO_BTN")); //是否显示视频客服按钮 1-是 0-否
			object.put("showPhoneDirBtn",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "SHOW_PHONE_DIR_BTN")); //话务条开启通讯录按钮 1-是 0-否
			
		} catch (Exception e) {
			 getLogger().error("工作模式sql报错"+e);
		}
		return object;
	}
	public JSONObject queryForCcbarReadymode() throws SQLException{
		JSONObject object=new JSONObject();
		try {
			//3.2#20220517-1 兼容数据库异常时，能正常返回值
			object.put("ccbarReadymode",ParamUtil.getSystemEntParam(this.getDbName(), this.getEntId(), this.getBusiOrderId(), "cc-base", "CCBAR_READYMODE"));
		} catch (Exception e) {
			 getLogger().error("ccbar签入时是否默认置忙"+e);
		}
		return object;
	}
	
	/**
	 * 设置用户的国际化语言
	 * @return
	 */
	public EasyResult actionForSetLang(){
		String lang = this.getJsonPara("lang");
		if(StringUtils.isBlank(lang)){
			lang = "CN";
		}
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){	
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法设置用户语言信息,用户登录信息不存在!");
			return EasyResult.error(500, getI18nValue("用户登录信息失效"));
		}
		
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this)+" 设置用户语言信息,userAcc="+user.getUserAcc()+",lang="+lang);
		
		boolean flag = UserUtil.setUserLang(this.getRequest(),lang);
		if(!flag){
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this)+" 设置用户语言信息,设置失败!");
			return EasyResult.error(500, getI18nValue("操作失败"));
		}
		
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this)+" 设置后的用户语言信息,userAcc="+user.getUserAcc()+",lang="+UserUtil.getUserLang(getRequest()));
		
		return EasyResult.ok(null,getI18nValue("操作成功"));
		
	}
	
	/**
	 * 获取系统公告
	 * @return
	 */
	public JSONObject actionForGetNotes(){
		UserModel user = UserUtil.getUser(getRequest());
		if(user==null){
			return EasyResult.error();
		}
		String longUser=user.getUserAcc();
		IService service = null;
		JSONObject notesObj = new JSONObject();
		try {
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this)+"进去获取到的系统公告：notesObj="+notesObj);	
			}
			service = ServiceContext.getService(ServiceID.NOTES_INTEFACE);
			JSONObject json = new JSONObject();
			json.put("serviceId", ServiceID.NOTES_INTEFACE);
			json.put("command",ServiceCommand.NOTE_QUERY_NOTES);
			json.put("sender", Constants.APP_NAME);
			json.put("agentId", longUser);
			json.put("num", 10);
			json.put("schema",user.getSchemaName());
			json.put("epCode",user.getEpCode());
			json.put("busiOrderId",user.getBusiOrderId());
			
			notesObj = service.invoke(json);
			
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this)+"获取到的系统公告：notesObj="+notesObj);	
			}
		} catch (ServiceException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"查询首页公告出错:"+e.getMessage(), e);	
		}
		if(notesObj==null){
			return EasyResult.error();
		}else if("000".equals(notesObj.getString("respCode"))){
			return EasyResult.ok(notesObj.toString(), "");
		}else{
			logger.error(CommonUtil.getClassNameAndMethod(this)+"获取系统公告,公告模块返回respCode="+notesObj.getString("respCode")+",respDesc="+notesObj.getString("respDesc"));
			return EasyResult.error();
		}
		
		
	}
}
