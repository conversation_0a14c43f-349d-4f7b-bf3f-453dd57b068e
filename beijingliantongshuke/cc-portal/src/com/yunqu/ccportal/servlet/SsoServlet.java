package com.yunqu.ccportal.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.util.*;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.base.CommonLogger;
import com.yunqu.ccportal.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;

/**
 * 提供给企业客服v5微服务版本进行单点登录处理
 */
@WebServlet("/sso/*")
public class SsoServlet extends AppBaseServlet {
	private static final long serialVersionUID = 6688957706937889578L;

	private Logger logger = CommonLogger.getLogger("sso");

	/**
	 * 客户从v3(mars)里打开v5(如微服务模块)的链接时，v5通过该接口先获取code；
	 * 需要提前在
	 */
	public void actionForAuthCode() {
		String sender = this.getPara("sender");
		String signature = this.getPara("signature");
		String timestamp = this.getPara("timestamp");
		String state = this.getPara("state");
		String redirect_url = this.getPara("redirect_url");
		String logout_url = this.getPara("logout_url");
		try {
			logger.info(" << 收到获取authCode请求: sender = " + sender + ", signature = " + signature + ", timestamp = " + timestamp + ", state = " + state
					+ ", redirect_url = " + redirect_url + ", logout_url = " + logout_url);

			if (StringUtils.isBlank(redirect_url)) {
				logger.error("获取authCode异常,请求中的redirect_url用空，不处理.");
				return;
			}
			if (redirect_url.indexOf("?") == -1) {
				redirect_url = redirect_url + "?state=" + state;
			} else {
				redirect_url = redirect_url + "&state=" + state;
			}

			logger.error("获取authCode,重定向url:" + redirect_url);


			//未登录
			UserModel user = UserUtil.getUser(this.getRequest());
			if (user == null) {
				logger.error("获取authCode异常,当前用户在cc-portal登录超时，不处理.");
				this.redirect(redirect_url + "&respCode=001&respDesc=未登录");
				return;
			}

			//从缓存里获取接口注册信息
			JSONObject appJson = CacheUtil.getSystemRegCache().getCache(user.getEpCode(), null, sender);
			if (appJson == null) {
				logger.error("获取authCode异常,请求参数中的sender不合法，需要在系统管理-》接口调用方里配置，本次登录请求不处理.");
				this.redirect(redirect_url + "&respCode=002&respDesc=sender不存在");
				return;
			}
			String enableStatus = appJson.getString("ENABLE_STATUS");
			if (!"01".equalsIgnoreCase(enableStatus)) {
				logger.error("获取authCode异常,请求参数中的sender 已禁用，本次登录请求不处理.");
				this.redirect(redirect_url + "&respCode=003&respDesc=sender不可用");
				return;
			}
			String pwd = appJson.getString("PASSWORD");
			if (StringUtils.isNotBlank(pwd)) {
				String sign = MD5Util.getHexMD5(sender + timestamp + pwd);
				if (!sign.equalsIgnoreCase(signature)) {
					logger.error("获取authCode异常,请求signature 验证不通过,正确的是:" + sign + "，本次登录请求不处理.");
					this.redirect(redirect_url + "&respCode=004&respDesc=sender签名错误");
					return;
				}
			}
			String code = MD5Util.getHexMD5(IDGenerator.getDefaultNUMID());
			JSONObject codeJson = new JSONObject();
			codeJson.put("userAcc", user.getUserAcc());
			codeJson.put("code", code);
			codeJson.put("sender", sender);
			codeJson.put("time", DateUtil.getCurrentDateStr());
			codeJson.put("logout_url", logout_url);
			String cacheKey = Constants.APP_NAME + "-sso-authcode-" + code;
			CacheUtil.put(cacheKey, codeJson.toJSONString(), 3600 * 24);
			user.getYcUserPrincipal().setAttribute("v5-auth-code", code);

			logger.error("用户登录获取authcode成功并写入缓存,key= " + cacheKey + "  --> " + codeJson);

			long respTimestamp = System.currentTimeMillis();
			this.redirect(redirect_url + "&respCode=000&respDesc=成功&code=" + code + "&sender=" + sender + "&entId=" + user.getEpCode() + "&timestamp=" + respTimestamp + "&signature=" + (MD5Util.getHexMD5(sender + timestamp + pwd)));
			return;


		} catch (Exception e) {
			logger.error("处理单点登录的authcode异常：" + e.getMessage(), e);
			this.redirect(redirect_url + "&respCode=999&respDesc=异常");
		}
		return;
	}

	/**
	 * 根据authcode获取accesstoken
	 *
	 * @return
	 */
	public JSONObject actionForGetToken() {
		JSONObject result = new JSONObject();
		try {
			String sender = this.getPara("sender");
			String signature = this.getPara("signature");
			String timestamp = this.getPara("timestamp");
			String code = this.getPara("code");
			String entId = this.getPara("entId");
			logger.info(" << 收到获取token请求: sender = " + sender + ", signature = " + signature + ", timestamp = " + timestamp + ", code = " + code + ", entId = " + entId);

			if (StringUtils.isAnyBlank(sender, signature, timestamp, code, entId)) {
				result.put("respCode", "005");
				result.put("respDesc", "参数sender,signature,timestamp,code,entId不能为空!");
				logger.error("获取token异常,参数sender,signature,timestamp,code,entId不能为空!");
				return result;
			}

			JSONObject checkResult = check(entId, sender, timestamp, signature);
			if (checkResult != null) {
				logger.error("获取token异常,校验不通过：" + checkResult.getString("respCode"));
				return checkResult;
			}

			String codeStr = CacheUtil.get(Constants.APP_NAME + "-sso-authcode-" + code);
			if (StringUtils.isBlank(codeStr)) {
				result.put("respCode", "006");
				result.put("respDesc", "authcode不存在");
				logger.error("获取token异常,authcode不存在.");
				return result;
			}
			JSONObject codeJson = JsonUtil.toJSONObject(codeStr);

			String accessToken = MD5Util.getHexMD5(code + IDGenerator.getDefaultNUMID());
			JSONObject tokenJson = new JSONObject();
			tokenJson.put("userAcc", codeJson.getString("userAcc"));
			tokenJson.put("code", code);
			tokenJson.put("sender", sender);
			tokenJson.put("time", DateUtil.getCurrentDateStr());
			tokenJson.put("accessToken", accessToken);
			String cacheKey = Constants.APP_NAME + "-sso-accessToken-" + accessToken;
			CacheUtil.put(cacheKey, tokenJson.toJSONString(), 60 * 10);  //10分钟

			logger.error("获取token成功，并写入缓存,key: " + cacheKey + " --> " + tokenJson);

			result.put("respCode", "000");
			result.put("respDesc", "查询成功");
			result.put("data", tokenJson);


		} catch (Exception e) {
			logger.error("获取accessToken失败：" + e.getMessage(), e);
			result.put("respCode", "999");
			result.put("respDesc", "查询异常");
		}
		return result;
	}

	/**
	 * 根据accesstoken获取userinfo
	 *
	 * @return
	 */
	public JSONObject actionForGetUserInfo() {
		JSONObject result = new JSONObject();
		try {
			String sender = this.getPara("sender");
			String signature = this.getPara("signature");
			String timestamp = this.getPara("timestamp");
			String accessToken = this.getPara("accessToken");
			String entId = this.getPara("entId");
			logger.info(" << 收到获取userInfo请求: sender = " + sender + ", signature = " + signature + ", timestamp = " + timestamp + ", accessToken = " + accessToken + ", entId = " + entId);
			if (StringUtils.isAnyBlank(sender, signature, timestamp, accessToken, entId)) {
				result.put("respCode", "005");
				result.put("respDesc", "参数sender,signature,timestamp,code,entId不能为空!");
				logger.error("获取userInfo异常,参数sender,signature,timestamp,code,entId不能为空!");
				return result;
			}

			JSONObject checkResult = check(entId, sender, timestamp, signature);
			if (checkResult != null) {
				logger.error("获取userInfo异常,校验不通过：" + checkResult.getString("respCode"));
				return checkResult;
			}

			String codeStr = CacheUtil.get(Constants.APP_NAME + "-sso-accessToken-" + accessToken);
			if (StringUtils.isBlank(codeStr)) {
				logger.error("获取userInfo异常,accessToken不存在.");
				result.put("respCode", "007");
				result.put("respDesc", "accessToken不存在");
				return result;
			}
			JSONObject codeJson = JsonUtil.toJSONObject(codeStr);
			JSONObject user = CacheUtil.getCcUserCache().getCache(entId, null, codeJson.getString("userAcc"));
			if (user == null) {
				result.put("respCode", "008");
				result.put("respDesc", "无法查询到用户信息");
				logger.error("获取userInfo异常,无法查询到用户信息[" + codeJson.getString("userAcc") + "].");
				return result;
			}

			JSONObject userJson = new JSONObject();
			userJson.put("userAcc", user.getString("USER_ACCT"));
			userJson.put("userName", user.getString("USERNAME"));
			userJson.put("entId", user.getString("ENT_ID"));
			userJson.put("busiOrderId", user.getString("BUSI_ORDER_ID"));
			userJson.put("schema", SchemaService.findSchemaByEntId(user.getString("ENT_ID")));

			result.put("data", userJson);
			result.put("respCode", "000");
			result.put("respDesc", "查询成功");

			logger.error(" >> 获取userInfo成功，返回：" + result);

			return result;

		} catch (Exception e) {
			logger.error("根据accessToken查询用户信息失败：" + e.getMessage(), e);
			result.put("respCode", "999");
			result.put("respDesc", "查询异常");
		}
		return result;
	}

	/**
	 * 根据accesstoken获取userinfo
	 *
	 * @return
	 */
	public JSONObject actionForGetLogout() {
		JSONObject result = new JSONObject();
		try {
			String sender = this.getPara("sender");
			String signature = this.getPara("signature");
			String timestamp = this.getPara("timestamp");
			String userAcc = this.getPara("userAcc");
			String entId = this.getPara("entId");
			logger.info(" << 收到用户退出请求: sender = " + sender + ", signature = " + signature + ", timestamp = " + timestamp + ", userAcc = " + userAcc + ", entId = " + entId);
			if (StringUtils.isAnyBlank(sender, signature, timestamp, userAcc, entId)) {
				result.put("respCode", "005");
				result.put("respDesc", "参数sender,signature,timestamp,userAcc,entId不能为空!");
				logger.error("用户退出异常,参数sender,signature,timestamp,userAcc,entId不能为空!");
				return result;
			}

			JSONObject checkResult = check(entId, sender, timestamp, signature);
			if (checkResult != null) {
				logger.error("用户退出异常,校验不通过：" + checkResult.getString("respCode"));
				return checkResult;
			}

			// 退出
			result.put("respCode", "000");
			result.put("respDesc", "查询成功");

			logger.error(" >> 用户退出成功，返回：" + result);

			return result;

		} catch (Exception e) {
			logger.error("用户退出失败：" + e.getMessage(), e);
			result.put("respCode", "999");
			result.put("respDesc", "查询异常");
		}
		return result;
	}

	/**
	 * 检测调用方系统的身份，检查通过时返回空
	 *
	 * @param entId
	 * @param sender
	 * @param timestamp
	 * @param sign
	 * @return
	 */
	private JSONObject check(String entId, String sender, String timestamp, String signature) {
		JSONObject result = new JSONObject();
		JSONObject appJson = CacheUtil.getSystemRegCache().getCache(entId, null, sender);
		if (appJson == null) {
			result.put("respCode", "002");
			result.put("respDesc", "sender不存在");
			return result;
		}
		String enableStatus = appJson.getString("ENABLE_STATUS");
		if (!"01".equalsIgnoreCase(enableStatus)) {
			result.put("respCode", "003");
			result.put("respDesc", "sender不可用");
			return result;
		}
		String pwd = appJson.getString("PASSWORD");
		if (StringUtils.isNotBlank(pwd) && StringUtils.isNotBlank(signature)) {
			String sign = MD5Util.getHexMD5(sender + timestamp + pwd);
			if (!sign.equalsIgnoreCase(signature)) {
				result.put("respCode", "004");
				result.put("respDesc", "sender签名错误");
				return result;
			}
		}
		return null;
	}


}
