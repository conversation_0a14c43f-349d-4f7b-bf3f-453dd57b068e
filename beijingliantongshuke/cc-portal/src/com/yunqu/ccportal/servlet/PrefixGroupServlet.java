package com.yunqu.ccportal.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.model.Prefix;
import com.yunqu.ccportal.model.PrefixGroup;


@WebServlet("/servlet/prefixGroup")
public class PrefixGroupServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	public EasyResult actionForUpdatePrefixState(){
		EasyResult result = new EasyResult();
		try {
			JSONObject jsonObject=getJSONObject();
			Prefix prefix=new  Prefix();
			prefix.setPrimaryValues(jsonObject.getString("prefixNum"));
			prefix.set("PREFIX_STATE",jsonObject.getString("state"));
			result.setSuccess(null, "修改成功！");
			this.getQuery().update(prefix);
		} catch (SQLException e) {
			this.error("修改失败，原因："+e.getMessage(),e);
			result.addFail("修改失败，原因："+e.getMessage());
		}
		return result;
	}
	public EasyResult actionForAdd(){
		EasyResult result=new EasyResult();
		try {
			if(getQuery().queryForExist("select count(1) from CC_PREFIX_GROUP where ENT_ID = ? and PREFIX_GROUP_NAME = ?", new Object[]{getJsonPara("prefixGroup.ENT_ID"),getJsonPara("prefixGroup.PREFIX_GROUP_NAME")})){
				result.addFail("该号码组名称已经存在！");
				return result;
			}
			EasyRecord record=new EasyRecord("CC_PREFIX_GROUP","PREFIX_GROUP_ID");
			record.setColumns(getJSONObject("prefixGroup"));
			record.setPrimaryValues(RandomKit.randomStr());
			record.set("CREATE_TIME",EasyDate.getCurrentDateString(null) );
			record.set("CREATOR",this.getUserPrincipal().getUserName());
			record.set("ENT_ID", getEntId());
			getQuery().save(record);
			result.setSuccess(null, "添加成功！");
		} catch (SQLException e) {
			this.error("添加失败，原因："+e.getMessage(),e);
			result.addFail("添加失败，原因："+e.getMessage());
		}
		return result;
	}

	public EasyResult actionForUpdate(){
		EasyResult result = new EasyResult();
		try {
			if(getQuery().queryForExist("select count(1) from CC_PREFIX_GROUP where ENT_ID = ? and PREFIX_GROUP_NAME = ? and PREFIX_GROUP_ID <> ?", new Object[]{getJsonPara("prefixGroup.ENT_ID"),getJsonPara("prefixGroup.PREFIX_GROUP_NAME"),getJsonPara("prefixGroup.PREFIX_GROUP_ID")})){
				result.addFail("该号码组名称已经存在！");
				return result;
			}
			EasyRecord record=new EasyRecord("CC_PREFIX_GROUP","PREFIX_GROUP_ID");
			record.setColumns(getJSONObject("prefixGroup"));
			this.getQuery().update(record);
			result.setSuccess(null, "修改成功！");
		} catch (SQLException e) {
			this.error("修改失败，原因："+e.getMessage(),e);
			result.addFail("修改失败，原因："+e.getMessage());
		}
		return result;
	}
	
	public EasyResult actionForDelete(){
		EasyQuery query = this.getQuery();
		EasyResult result = new EasyResult();
		String prefixGroupId = getJsonPara("groupId");
		try {
			if(query.queryForExist("select count(1) from " + getTableName("CC_TASK") + " where PREFIX_GROUP_ID = ? and ENT_ID = ?", new Object[]{prefixGroupId,getEntId()})) {
				result.addFail("该号码组已经在任务中调用，不能删除！");
				return result;
			}
			if(query.queryForExist("select count(1) from " + getTableName("CC_SKILL_GROUP") + " where PREFIX_GROUP_ID = ? and ENT_ID = ?", new Object[]{prefixGroupId,getEntId()})) {
				result.addFail("该号码组已经被技能组调用，不能删除！");
				return result;
			}
			query.begin();
			PrefixGroup prefixGroup=new PrefixGroup();
			prefixGroup.setPrimaryValues(prefixGroupId);
			query.deleteById(prefixGroup);
			
			EasyRecord record = new EasyRecord("CC_PREFIX_GROUP_PREFIX", "PREFIX_GROUP_ID").setPrimaryValues(prefixGroupId);
			query.deleteById(record);
			query.commit();
			result.setSuccess(null, "删除成功！");
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("删除失败，原因："+e1.getMessage(),e1);
				result.addFail("删除失败，原因："+e1.getMessage());
			}
			this.error("删除失败，原因："+e.getMessage(),e);
			result.addFail("删除失败，原因："+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 号码组选择号码
	 * @return
	 */
	public EasyResult actionForSelect(){
		EasyQuery  easyQuery = this.getQuery();
		String entId = getEntId();
		try {
			String groupId=getJSONObject().getString("groupId");
			if(!easyQuery.queryForExist("select count(1) from CC_PREFIX_GROUP where PREFIX_GROUP_ID = ?", new Object[]{groupId})){
				return EasyResult.fail("该号码组已经删除，无效操作！");
			}
			easyQuery.begin();
			JSONArray array=getJSONObject().getJSONArray("prefixNums");
			if(array!=null && !array.isEmpty()){
				for(Object object:array){
					EasyRecord easyRecord=new EasyRecord("CC_PREFIX_GROUP_PREFIX",new String[]{"ENT_ID","PREFIX_GROUP_ID","PREFIX_NUM"});
					easyRecord.set("CREATOR", this.getUserPrincipal().getUserId());
					easyRecord.set("CREATE_TIME", EasyDate.getCurrentDateString(null));
					easyRecord.setPrimaryValues(entId,groupId,object);
					easyQuery.save(easyRecord);
				}
			}
			easyQuery.commit();
			return  EasyResult.ok(null,"选择外呼号码成功!");
		} catch (SQLException ex) {
			this.error("分配失败，原因："+ex.getMessage(),ex);
			try {
				easyQuery.roolback();
			} catch (SQLException sqle) {
				this.error(sqle.getMessage(),sqle);
				return EasyResult.error(501, "选择外呼号码失败，原因："+sqle.getMessage());
			}
			return EasyResult.error(501, "选择外呼号码失败，原因："+ex.getMessage());
		}
	}
	
	/**
	 * 移除号码组中的号码
	 * @return
	 */
	public EasyResult actionForDelPrefix(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord easyRecord = new EasyRecord("CC_PREFIX_GROUP_PREFIX", new String[]{"ENT_ID","PREFIX_GROUP_ID","PREFIX_NUM"});
			easyRecord.setPrimaryValues(new Object[]{getEntId(),getJsonPara("groupId"),getJsonPara("prefixNum")});
			getQuery().deleteById(easyRecord);
			result.setSuccess(null, "移除号码成功！");
		} catch (SQLException e) {
			this.error("移除号码失败，原因："+e.getMessage(),e);
			result.addFail("移除号码失败，原因："+e.getMessage());
		}
		return result;
	}
	public EasyResult actionForBatchDelPrefix(){
		EasyResult result = new EasyResult();
		try {
			JSONObject jsonObject=getJSONObject();
			JSONArray prefixNums=jsonObject.getJSONArray("prefixNums");
			EasyRecord easyRecord = new EasyRecord("CC_PREFIX_GROUP_PREFIX", new String[]{"ENT_ID","PREFIX_GROUP_ID","PREFIX_NUM"});
			if(prefixNums!=null&&prefixNums.size()>0){
				for(Object id:prefixNums){
					easyRecord.setPrimaryValues(new Object[]{getEntId(),jsonObject.getString("groupId"),id});
					getQuery().deleteById(easyRecord);
				}
				result.setSuccess(null, prefixNums.size()+"个号码移除成功！");
			}else{
				result.addFail("请选择号码移除！");
			}
		} catch (SQLException e) {
			this.error("移除号码失败，原因："+e.getMessage(),e);
			result.addFail("移除号码失败，原因："+e.getMessage());
		}
		return result;
	}

	@Override
	protected String getResId() {
		return null;
	}
}
