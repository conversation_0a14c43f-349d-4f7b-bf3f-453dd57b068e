package com.yunqu.ccportal.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.ccportal.base.AppBaseServlet;

/**
 * 个人收藏夹Servlet
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/bookMark/*")
public class BookMarkServlet extends AppBaseServlet {

	private static final long serialVersionUID = -1864094683991960092L;
	
	/**
	 * 更新收藏信息
	 * @return
	 */
	public EasyResult actionForUpdateRecord(){
		EasyRecord record = new EasyRecord(getTableName("C_CF_URL_FAVORITES"),"ID");
    	record.setColumns(getJSONObject());
    	record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
    	record.set("UPDATE_USER_ACC", UserUtil.getRequestUserAcc(getRequest()));
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(getI18nValue("操作失败"));
		}
		return EasyResult.ok(null, getI18nValue("操作成功"));
	}
	
    /**
     * 添加收藏网站
     * @return
     */
	public EasyResult actionForAddRecord() {
		UserModel model=UserUtil.getUser(getRequest());
		try {
			EasyRecord record = new EasyRecord(getTableName("C_CF_URL_FAVORITES"),"ID");
			record.setColumns(getJSONObject());
			record.setPrimaryValues(RandomKit.randomStr()); 
			record.set("CREATE_USER_NAME",model.getUserName());
			record.set("CREATE_USER_ACC", model.getUserAcc());
			record.set("CREATE_USER_DEPT", model.getDeptCode());
			record.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			record.set("UPDATE_USER_ACC", model.getUserAcc());
			record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("ENT_ID", this.getEntId());
			this.getQuery().save(record);
		} catch (Exception e) {
			this.error(CommonUtil.getClassNameAndMethod(this)+"添加失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 删除收藏
	 */
    public EasyResult actionForDelRecord() {
    	String id = getJSONObject().getString("ID");
		EasyRecord record = new EasyRecord(getTableName("C_CF_URL_FAVORITES"),"ID").setPrimaryValues(id);
		EasyQuery query = this.getQuery();
		try {
			query.deleteById(record);
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(CommonUtil.getClassNameAndMethod(this)+"删除回滚失败，原因："+e.getMessage(), e);
			}
			this.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
    }
}
