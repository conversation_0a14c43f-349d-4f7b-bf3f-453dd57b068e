package com.yunqu.ccportal.servlet;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppBaseServlet;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.base.QueryFactory;
import com.yunqu.ccportal.model.AgentWorkStat;
import com.yunqu.ccportal.utils.ParamUtil;
import com.yunqu.ccportal.utils.PhoneCryptor;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * 数据导出  
 */
@WebServlet("/servlet/export/*")
public class ExportServlet extends AppBaseServlet {

	
	private static final long serialVersionUID = 1L;
	
	/**
	 * 坐席工作情况统计模板文件
	 */
	public final static String AGENT_WORK_TEMP_PATH = "template/template_agent_work_stat.xlsx";
	/**
	 * 导出通话记录
	 * @throws Exception
	 */
	public void actionForExportEntRepotList() throws Exception{
		HttpServletRequest request = this.getRequest();
		EasyQuery query =this.getQuery();
		
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		
		EasySQL sql = new EasySQL("select DATE_ID,BILL_BEGIN_TIME,BILL_END_TIME,BILL_TIME,FEE_TIME_6,FEE_TIME_6,AGENT_NAME,AGENT_PHONE,PHONE_NUM,CALLER,CALLED,CLEAR_CAUSE,TASK_NAME from ");
		sql.append(this.getTableName("CC_CALL_RECORD t1"));
		sql.append(" where 1=1 and BILL_TIME > 0  ");
		sql.append(this.getEntId()," and t1.ENT_ID = ? ");
		sql.append(this.getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");

		sql.append(this.getSkillRes("t1.GROUP_ID", request.getParameter("groupId")));
		
		sql.appendLike(request.getParameter("agentName"), " and t1.AGENT_NAME like ? ");
		sql.append(request.getParameter("startDate").replaceAll("-", ""), " and t1.DATE_ID >= ? ");
		sql.append(request.getParameter("endDate").replaceAll("-", ""), " and t1.DATE_ID <= ? ");
		sql.append(request.getParameter("taskId"), " and t1.TASK_ID  = ? ");
		sql.append(request.getParameter("satisfId"), " and t1.SATISF_ID = ?");
		String clearCause=request.getParameter("clearCause");
		if(StringUtils.notBlank(clearCause)){
			sql.append(clearCause, " and t1.CLEAR_CAUSE  = ? ");
		}
		
		//加密号码查询，不支持模糊查询
		String custPhone = request.getParameter("custPhone");
		if(StringUtils.isNotBlank(custPhone)){
			sql.append("and t1.CUST_PHONE in('"+cryptor.encrypt(custPhone)+"','"+custPhone+"')");
		}
		
		int billTime = Integer.valueOf(request.getParameter("billTime"));
		switch(billTime){
		case 1://全部有效时间
			sql.append(0, " and t1.BILL_TIME > 0");
			break;
		case 2://0到30秒
			sql.append(0, " and t1.BILL_TIME > ?");
			sql.append(30, " and t1.BILL_TIME <= ?");
			break;
		case 3://30到60秒
			sql.append(30, " and t1.BILL_TIME > ?");
			sql.append(60, " and t1.BILL_TIME <= ?");
			break;
		case 4://60到120秒
			sql.append(60, " and t1.BILL_TIME > ?");
			sql.append(120, " and t1.BILL_TIME <= ?");
			break;
		case 5://120到300秒
			sql.append(120, " and t1.BILL_TIME > ?");
			sql.append(300, " and t1.BILL_TIME <= ?");
			break;
		case 6://300秒以上
			sql.append(300, " and t1.BILL_TIME > ?");
			break;
		}
		sql.appendSort(request.getParameter("sortName"),request.getParameter("sortType"),"t1.SERIAL_ID ");
		
		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		
		cryptor.decryptMap(data, new String[]{"CALLER","CALLED","CUST_PHONE"}, cryptor.showcrypt(getEntId()));
		
		String feeUnit = this.getQuery().queryForString("select FEE_UNIT from CC_FEE_CONF where ENT_ID = ?",  new Object[]{this.getEntId()});

		List<String> headers = new ArrayList<String>();
		headers.add("日期");
		headers.add("开始时间");
		headers.add("结束时间");
		headers.add("通话时长");
		if("1".equals(feeUnit)){
			headers.add("计费时长（6秒）");
		}else{
			headers.add("计费时长（分钟）");
		}
		headers.add("业务员");
		headers.add("话机号码");
		headers.add("主叫");
		headers.add("被叫");
		headers.add("呼叫结果");
		headers.add("所属任务");
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			style.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style);
		}
			
		List<List<String>> excelData=new ArrayList<List<String>>();
		if(data != null && data.size() > 0){
			boolean flag=PhoneCryptor.getInstance().showcrypt(getEntId());
			for(Map<String,String> map : data){
				List<String> list = new ArrayList<String>();
				list.add(StringUtils.trimToEmpty(map.get("DATE_ID")));
				list.add(map.get("BILL_BEGIN_TIME") != null ? map.get("BILL_BEGIN_TIME").substring(11) : "-");
				list.add(map.get("BILL_END_TIME")!= null ? map.get("BILL_END_TIME").substring(11) : "-");
				list.add(ParamUtil.parseTime(map.get("BILL_TIME")));
				if("1".equals(feeUnit)){
					list.add(StringUtils.trimToEmpty(map.get("FEE_TIME_6")));
				}else{
					list.add(StringUtils.trimToEmpty(map.get("FEE_TIME_60")));
				}
				list.add(map.get("AGENT_NAME")+"-"+map.get("AGENT_PHONE"));
				if(flag){
					list.add(PhoneCryptor.getInstance().decrypt(map.get("PHONE_NUM")));
					list.add(PhoneCryptor.getInstance().decrypt(map.get("CALLER")));
					list.add(PhoneCryptor.getInstance().decrypt(map.get("CALLED")));
				}else{
					list.add(StringUtils.trimToEmpty(map.get("PHONE_NUM")));
					list.add(StringUtils.trimToEmpty(map.get("CALLER")));
					list.add(StringUtils.trimToEmpty(map.get("CALLED")));
				}
				list.add(ParamUtil.formatCause(map.get("CLEAR_CAUSE")));
				list.add(StringUtils.trimToEmpty(map.get("TASK_NAME")));
				excelData.add(list);
			}
		}
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
		renderFile(file,"通话记录详单.xlsx");
	}
	
	/**
	 * 导出未接来电通话记录
	 * @throws Exception 
	 */
	public void actionForExportCallNoAnswer() throws Exception{
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		HttpServletRequest request = this.getRequest();
		EasySQL sql = new EasySQL("select * from ").append(getTableName("CC_CALL_NOANSWER")).append("where 1=1");
		sql.append(getEntId(),"and ENT_ID = ?");
		sql.append(getBusiOrderId(),"and BUSI_ORDER_ID =?");
		sql.append(request.getParameter("beginTime"), "and ALERING_TIME >= ?");
		sql.append(request.getParameter("endTime"), "and ALERING_TIME <= ?");

		sql.append(this.getSkillRes("GROUP_ID", request.getParameter("groupId")));
		
		//加密号码查询，不支持模糊查询
		String called = request.getParameter("called");
		String caller = request.getParameter("caller");
		if(StringUtils.isNotBlank(called)){
			sql.append("and CALLED in('"+cryptor.encrypt(called)+"','"+called+"')");
		}
		if(StringUtils.isNotBlank(caller)){
			sql.append("and CALLER in('"+cryptor.encrypt(caller)+"','"+caller+"')");
		}
		
		String agentId = request.getParameter("agentIds");
        if(StringUtils.isNotBlank(agentId)){
        	agentId = agentId.replaceAll("\"", "'").replace("[", "(").replace("]", ")");
        	sql.append("and AGENT_ID in " + agentId);
        }
		sql.appendSort(request.getParameter("sortName"), request.getParameter("sortType"), "ALERING_TIME");
		
		List<Map<String, String>> data = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		cryptor.decryptMap(data, new String[]{"CALLER","CALLED"}, cryptor.showcrypt(getEntId()));
		
		List<String> headers = new ArrayList<String>();
		headers.add("日期");
		headers.add("主叫");
		headers.add("被叫");
		headers.add("振铃开始时间");
		headers.add("振铃结束时间");
		headers.add("振铃时长");
		headers.add("工号");
		headers.add("班组");
		headers.add("处理状态");
		headers.add("处理人");
		headers.add("处理时间");
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			style.setBackgroundColor(IndexedColors.GREY_25_PERCENT.index);
			styles.add(style);
		}

		List<List<String>> excelData=new ArrayList<List<String>>();
		if(data != null && data.size() > 0){
			for(Map<String,String> map : data){
				List<String> list = new ArrayList<String>();
				list.add(map.get("DATE_ID"));
				list.add(map.get("CALLER"));
				list.add(map.get("CALLED"));
				list.add(ParamUtil.cutDateTime(map.get("ALERING_TIME")));
				list.add(ParamUtil.cutDateTime(map.get("END_TIME")));
				list.add(ParamUtil.parseTime(map.get("AGENT_STAY_TIME")));
				list.add(map.get("AGENT_NAME"));
				list.add(map.get("GROUP_NAME"));
				list.add("1".equals(map.get("STATE"))?"已处理":"待处理");
				list.add(map.get("USERNAME"));
				list.add(map.get("HANDLE_TIME"));
				excelData.add(list);
			}
		}
		
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
		renderFile(file, "未接来电列表.xlsx");
	}
	
	
	/**
	 * 导出工作情况统计表
	 * @throws Exception
	 */
	public void actionForExportAgentWorkStat() throws Exception{
		HttpServletRequest request = this.getRequest();
		EasySQL sql = new EasySQL("select ");
        sql.append("t6.SKILL_GROUP_NAME TEAM_NAME,t4.GROUP_LIST SKILL_GROUP_NAME, t1.AGENT_ID,");
        sql.append("SUM(t1.ONLINE_TIME) SERVICE_TIME,");//工作时长  = 签入签出的在线时长。
        sql.append("SUM(t1.NOTREADY_COUNT) NOTREADY_COUNT,");
        sql.append("SUM(t1.NOTREADY1_COUNT) NOTREADY1_COUNT,");
        sql.append("SUM(t1.NOTREADY2_COUNT) NOTREADY2_COUNT,");
        sql.append("SUM(t1.NOTREADY3_COUNT) NOTREADY3_COUNT,");
        sql.append("SUM(t1.NOTREADY_TIME) NOTREADY_TIME,");
        sql.append("SUM(t1.NOTREADY1_TIME) NOTREADY1_TIME,");
        sql.append("SUM(t1.NOTREADY2_TIME) NOTREADY2_TIME,");
        sql.append("SUM(t1.NOTREADY3_TIME) NOTREADY3_TIME,");
        sql.append("SUM(t1.INBOUND_COUNT) IN_CALL_COUNT,");
        sql.append("SUM(t1.OUTBOUND_COUNT) OUT_CALL_COUNT,");
        sql.append("SUM(t1.OUTBOUND_READY_TIME) OUTBOUND_STATE_TIME,");
        sql.append("SUM(t1.ONLINE_TIME-t1.NOTREADY1_TIME-t1.NOTREADY2_TIME-t1.NOTREADY3_TIME-t1.OUTBOUND_READY_TIME) VALID_SERVICE_TIME,");  //有效服务时长=通话时长+呼入状态下的空闲时长
        sql.append("SUM(t1.SERVICE_TIME) TOTAL_TIME "); //通话时长
        sql.append(" from "+getStatTableName("CC_RPT_AGENT_INDEX")+" t1");
        sql.append(" left join CC_USER t0 on t1.AGENT_ID = t0.USER_ACCT and t1.ENT_ID = t0.ENT_ID");
        sql.append(" left join "+getTableName("cc_busi_user")+" t4 on t0.USER_ID = t4.USER_ID and t1.ENT_ID = t4.ENT_ID");
        sql.append(" left join "+getTableName("cc_skill_group")+" t5 on t4.GROUP_LIST = t5.SKILL_GROUP_NAME");
        sql.append(" left join "+getTableName("cc_skill_group")+" t6 on t5.P_GROUP_ID = t6.SKILL_GROUP_ID");
        sql.append(" where 1=1");
        sql.append(getBusiOrderId()," and t4.BUSI_ORDER_ID = ?");
        sql.append(getEntId()," and t1.ENT_ID = ?");
        sql.appendLike(request.getParameter("agentPhone")," and t1.AGENT_ID like ?");
        sql.append(" and t4.GROUP_LIST is not null");
        sql.append(" and t4.GROUP_LIST<>''");
        
		sql.append(this.getUserResSql("t0.USER_ID"));
        
        if(StringUtils.isNotBlank(request.getParameter("startDate"))){
            sql.append(request.getParameter("startDate").replaceAll("-", ""),"and t1.DATE_ID >= ?");
        }
        if(StringUtils.isNotBlank(request.getParameter("endDate"))){
            sql.append(request.getParameter("endDate").replaceAll("-", ""),"and t1.DATE_ID <= ?");
        }
        sql.append(" group by t6.SKILL_GROUP_NAME, t4.GROUP_LIST, t1.AGENT_ID order by t4.GROUP_LIST,t1.AGENT_ID");
		List<Map<String, String>> data = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());

		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");

        List<AgentWorkStat> list = new ArrayList<AgentWorkStat>();
        if (data != null && data.size() > 0) {
        	JSONObject userDict = getUserDict(null);
        	long sum2=0,sum3=0,sum4=0,sum5=0,sum6=0,sum7=0,sum8=0,sum9=0,sum10=0,sum11=0,sum12=0,sum13=0;
            for (Map<String, String> map : data) {
            	AgentWorkStat agentStat = new AgentWorkStat();
            	agentStat.setTeamName(StringUtils.isNotBlank(map.get("TEAM_NAME"))?map.get("TEAM_NAME"):map.get("SKILL_GROUP_NAME"));
				agentStat.setGroupName(StringUtils.isAnyBlank(map.get("TEAM_NAME"),map.get("SKILL_GROUP_NAME"))?"":map.get("SKILL_GROUP_NAME"));
				agentStat.setAgent(userDict.getString(map.get("AGENT_ID")));
				agentStat.setWorkTime(ParamUtil.parseTime2(map.get("SERVICE_TIME")));
				agentStat.setNotReadyCount1(ParamUtil.parseStrToInt(map.get("NOTREADY1_COUNT"))+"");
				agentStat.setNotReadyCount2(ParamUtil.parseStrToInt(map.get("NOTREADY2_COUNT"))+"");
				agentStat.setNotReadyCount3(ParamUtil.parseStrToInt(map.get("NOTREADY3_COUNT"))+"");
				agentStat.setNotReadyTime1(ParamUtil.parseTime2(map.get("NOTREADY1_TIME"))+"");
				agentStat.setNotReadyTime2(ParamUtil.parseTime2(map.get("NOTREADY2_TIME"))+"");
				agentStat.setNotReadyTime3(ParamUtil.parseTime2(map.get("NOTREADY3_TIME"))+"");
				
				agentStat.setOutboundStateTime(ParamUtil.parseTime2(map.get("OUTBOUND_STATE_TIME")));
				agentStat.setServiceTime(ParamUtil.parseTime2(map.get("VALID_SERVICE_TIME")));
				agentStat.setInSuccCount(map.get("IN_CALL_COUNT"));
				agentStat.setOutSuccCount(map.get("OUT_CALL_COUNT"));
				agentStat.setTotalCallTime(ParamUtil.parseTime2(map.get("TOTAL_TIME")));
				agentStat.setServicePercent(ParamUtil.divideDouble(map.get("VALID_SERVICE_TIME"),map.get("SERVICE_TIME")));
                list.add(agentStat);
                
                //汇总每列数据
                sum2 += ParamUtil.parseStrToLong(map.get("SERVICE_TIME"));
                sum3 += ParamUtil.parseStrToLong(map.get("NOTREADY1_COUNT"));
                sum4 += ParamUtil.parseStrToLong(map.get("NOTREADY2_COUNT"));
                sum5 += ParamUtil.parseStrToLong(map.get("NOTREADY3_COUNT"));
                sum6 += ParamUtil.parseStrToLong(map.get("NOTREADY1_TIME"));
                sum7 += ParamUtil.parseStrToLong(map.get("NOTREADY2_TIME"));
                sum8 += ParamUtil.parseStrToLong(map.get("NOTREADY3_TIME"));
                sum9 += ParamUtil.parseStrToLong(map.get("IN_CALL_COUNT"));
                sum10 += ParamUtil.parseStrToLong(map.get("OUT_CALL_COUNT"));
                sum11 += ParamUtil.parseStrToLong(map.get("TOTAL_TIME"));
                sum12 += ParamUtil.parseStrToLong(map.get("VALID_SERVICE_TIME"));
                sum13 += ParamUtil.parseStrToLong(map.get("OUTBOUND_STATE_TIME"));
            }
            //汇总行
            AgentWorkStat agentStat = new AgentWorkStat();
            agentStat.setTeamName("汇总");
            agentStat.setGroupName("");
            agentStat.setAgent("");
			agentStat.setWorkTime(ParamUtil.parseTime2(sum2+""));
			agentStat.setNotReadyCount1(sum3+"");
			agentStat.setNotReadyCount2(sum4+"");
			agentStat.setNotReadyCount3(sum5+"");
			agentStat.setNotReadyTime1(ParamUtil.parseTime2(sum6+""));
			agentStat.setNotReadyTime2(ParamUtil.parseTime2(sum7+""));
			agentStat.setNotReadyTime3(ParamUtil.parseTime2(sum8+""));
			agentStat.setServiceTime(ParamUtil.parseTime2(sum12+""));
			agentStat.setInSuccCount(sum9+"");
			agentStat.setOutSuccCount(sum10+"");
			agentStat.setTotalCallTime(ParamUtil.parseTime2(sum11+""));
			agentStat.setServicePercent(ParamUtil.divideDouble(sum12+"",sum2+""));
			agentStat.setOutboundStateTime(ParamUtil.parseTime2(sum13+""));
            list.add(agentStat);
        }
        String templatePath = request.getServletContext().getRealPath("/") + AGENT_WORK_TEMP_PATH;
        Map<String, String> extendMap = new HashMap<>();
		extendMap.put("statTime",request.getParameter("startDate")+"至"+request.getParameter("endDate"));
        ExcelUtils.getInstance().exportObjects2Excel(templatePath, 0, list, extendMap, AgentWorkStat.class, false, file.getAbsolutePath());
        String fileName = "坐席工作情况统计"+EasyDate.getCurrentDateString()+".xlsx";
        renderFile(file, fileName);
	}
	
	
	/**
	 * 获取Stat数据库的表名
	 */
	private String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "select TARGET_TABLE_NAME,UPDATE_TIME from "+getStatTableName("CC_STAT_TABLE_INFO")+" where TABLE_ID = ?  ";
			tabInfo = this.getQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return tabInfo;
	}
	
	/**
	 * 获取角色列表
	 * @param groupId
	 * @return
	 * @throws SQLException
	 */
	private JSONObject getUserDict(String groupId) throws SQLException{
		JSONObject dict = new JSONObject();
		String connects = "CONCAT(CONCAT(t3.AGENT_NAME,'-'),t1.USER_ACCT)";
		if(DBTypes.ORACLE.equals(getQuery().getTypes())){
			connects = "t3.AGENT_NAME||'-'||t1.USER_ACCT ";
		}
		EasySQL sql = new EasySQL("select t1.USER_ACCT,connects USER_NAME from CC_USER t1,").append(getTableName("CC_BUSI_USER t3"));
		if(StringUtils.isNotBlank(groupId)){
			sql.append(",").append(getTableName("CC_SKILL_GROUP_USER t2"));
			sql.append("where t3.USER_ID = t2.USER_ID and t3.ENT_ID = t2.ENT_ID and t3.BUSI_ORDER_ID = t2.BUSI_ORDER_ID");
			sql.append(groupId, "and t2.SKILL_GROUP_ID = ?");
		}else{
			sql.append("where 1=1");
		}
		sql.append("and t1.ENT_ID = t3.ENT_ID and t1.USER_ID = t3.USER_ID and t1.USER_STATE = 0 and t1.LOCK_STATE = 0 and t1.AGENT_PHONE is not null");
		sql.append(this.getBusiOrderId(), "and t3.BUSI_ORDER_ID = ?");
		sql.append(this.getEntId(), " and t1.ENT_ID = ? ");
		sql.append("order by t1.AGENT_PHONE asc");
		List<Map<String, String>> list = this.getQuery().queryForList(sql.getSQL().replace("connects", connects), sql.getParams(),new MapRowMapperImpl());
		for (Map<String, String> map : list) {
			dict.put(map.get("USER_ACCT"), map.get("USER_NAME"));
		}
		return dict;
	}
	
	/**
	 * 获取技能组权限
	 * @param sqlKey
	 * @param pSkillId
	 * @return
	 */
	private String getSkillRes(String sqlKey, String pSkillId){
		String sql = "";
		int roleType = this.getRoleType();
		String[] skillGroupIds = null;
		if(StringUtils.isNotBlank(pSkillId) && !"0".equals(pSkillId)){
			skillGroupIds = this.getChildSkill(pSkillId);
		}else if(roleType != Constants.ROLE_TYPE_MANAGER){
			skillGroupIds = getSkillGroupIdChilds();
			if(skillGroupIds != null && skillGroupIds.length == 1){
				skillGroupIds = this.getChildSkill(getSkillGroupIdChilds()[0]);
			}
			sql = " and "+sqlKey+" = -1";
		}
		if(skillGroupIds != null){					//存在技能组，获取该团队
			sql = " and "+sqlKey+" "+StringUtils.joinSql(skillGroupIds)+"";
		}
		return sql;
	}
	
	/**
	 * 获取部门下所有技能组
	 * @param pId
	 * @param reflash	是否刷新缓存
	 * @return
	 */
	private String[] getChildSkill(String pId){
		JSONArray array = null;
		String cacheKey = "SKILL_ID_FAMILY_"+pId;
		Object obj = cache.get(cacheKey);
		if(obj == null){
			array = new JSONArray();
			array.add(pId);
			try {
				List<JSONObject> list = this.getQuery().queryForList("select SKILL_GROUP_ID,P_GROUP_ID from "+getTableName("CC_SKILL_GROUP")+" where ENT_ID = ? and BUSI_ORDER_ID = ?", new Object[]{this.getEntId(),this.getBusiOrderId()}, new JSONMapperImpl());
				if(list != null && list.size() > 0){
					this.addSkill(pId, array, list);
					cache.put(cacheKey, array, 3600);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		} else{
			array = (JSONArray)obj;
		}
		return JSONArray.toJavaObject(array, String[].class);
	}
	
	/**
	 * 获取子部门
	 * @param pId
	 * @param array
	 * @param list
	 */
	private void addSkill(String pId, JSONArray array, List<JSONObject> list ){
		for(JSONObject row : list){
			if(pId.equals(row.getString("P_GROUP_ID"))){
				array.add(row.getString("SKILL_GROUP_ID"));
				addSkill(row.getString("SKILL_GROUP_ID"), array, list);
			}
		}
	}
	
	/**
	 * 获取权限sql,
	 * @param sqlKey	sql查询条件
	 * @return
	 */
	protected String getUserResSql(String sqlKey){
		String sql = "";
		YCUserPrincipal user = this.getUserPrincipal();
		if(user.getRoleType() == Constants.ROLE_TYPE_AGENT){				//座席
			sql = " and "+sqlKey+" = '"+user.getUserId()+"'";
		}else if(user.getRoleType() == Constants.ROLE_TYPE_MONITOR){			//班长
			String[] skillGroupId = getSkillGroupIdChilds();
			if(skillGroupId != null){					//存在技能组，获取该团队
				sql = " and "+sqlKey+" in ( select t5.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t5 where t5.ENT_ID = '"+user.getEntId()+"' and t5.BUSI_ORDER_ID = '"+user.getBusiOrderId()+"'";
				if(skillGroupId.length == 1){
					sql +=" and t5.SKILL_GROUP_ID = '"+skillGroupId[0]+"')";
				}else{
					sql +=" and t5.SKILL_GROUP_ID in("+StringUtils.join(skillGroupId, ",")+"))";
				}
			}else{														//不存在技能组，获取个人
				sql +=" and "+sqlKey+" = '"+user.getUserId()+"'";
			}
		}
		return sql;
	}
	
	/**
	 * 获取技能组Id
	 */
	protected String[] getSkillGroupIdChilds(){
		Object attribute = this.getUserPrincipal().getAttribute("skillGroupIdChilds");
		try{
			String [] s = (String[]) attribute;
			this.error("skillGroupIds:"+s[0], null);
		}catch(Exception e){
		}
		if(attribute==null)return null;
		return (String[]) attribute;
	}
	
	@Override
	public EasyQuery getQuery() {
		return QueryFactory.getReadQuery();
	}

}
