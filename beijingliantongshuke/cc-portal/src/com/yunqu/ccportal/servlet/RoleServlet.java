package com.yunqu.ccportal.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.ccportal.base.AppBaseServlet;

/**
 * 角色管理类
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/role")
public class RoleServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 添加
	 * @return
	 */
	public EasyResult actionForAdd(){
		EasyResult result=new EasyResult();
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			String userId = this.getUserPrincipal().getUserId();
			String createTime = EasyDate.getCurrentDateString(null);
			String roleId = RandomKit.randomStr();
			String roleType = getJsonPara("role.ROLE_TYPE");
			EasyRecord easyRecord = new EasyRecord(getTableName("cc_role"), "ROLE_ID");
			easyRecord.set("ENT_ID", getEntId());
			easyRecord.set("BUSI_ORDER_ID", getBusiOrderId());
			easyRecord.setColumns(getJSONObject("role"));
			easyRecord.setPrimaryValues(roleId);
			easyRecord.set("CREATE_TIME", createTime);
			easyRecord.set("CREATOR", userId);
			//同步组织架构
			//easyRecord.set("SYS_FLAG", 1);
			easyRecord.set("IDX_ORDER", 99);
			query.save(easyRecord);
			
			//自动给角色分配权限
			if(!"9".equals(roleType) && StringUtils.isNotBlank(roleType)) {
				String sql = "insert into " + getTableName("CC_ROLE_RES")
				+ "(BUSI_ORDER_ID,ROLE_ID,RES_ID,ENT_ID,CREATOR,CREATE_TIME) select ? as BUSI_ORDER_ID, ? as ROLE_ID,RES_ID, ? as ENT_ID, ? as CREATOR, ? as CREATE_TIME from CC_BUSI_RES where BUSI_ID = ?";
				if("1".equals(roleType)) { //管理员
					sql = sql + " and ROLE1_FIAG = 1";
				} else if("2".equals(roleType)) { //班长
					sql = sql + " and ROLE2_FIAG = 1";
				} else if("3".equals(roleType)) { //坐席
					sql = sql + " and ROLE3_FIAG = 1";
				}
			    query.execute(sql + " ", new Object[]{getBusiOrderId(),roleId,getEntId(),userId,createTime,getBusiId()});
			}
			query.commit();
			
			result.setSuccess(null, "添加成功！");
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("添加失败，原因："+e1.getMessage(),e1);
				result.addFail("添加失败，原因："+e1.getMessage());
			}
			this.error("添加失败，原因："+e.getMessage(),e);
			result.addFail("添加失败，原因："+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 同时删除ycbusi1.cc_role_res的role
	 * @return
	 */
	public EasyResult actionForDelete(){
		String roleId = getJsonPara("roleId");
		EasyResult result = new EasyResult();
		EasyQuery query = getQuery();
		try {
			if(query.queryForExist("select count(1) from " + getTableName("CC_BUSI_USER") + " where ROLE_ID = ?", new Object[]{roleId})) {
				result.addFail("该角色存在关联用户，请先解除角色和用户的关联！");
				return result;
			}
			if(!query.queryForExist("select count(1) from " + getTableName("CC_ROLE") + " where SYS_FLAG = 2 and ROLE_ID = ?", new Object[]{roleId})){
				result.addFail("该角色不属于自定义角色，不能删除！");
				return result;
			}
			query.begin();
			EasyRecord easyRecord=new EasyRecord(getTableName("CC_ROLE"),"ROLE_ID");
			easyRecord.setPrimaryValues(roleId);
			query.deleteById(easyRecord);
			EasyRecord resrecord = new EasyRecord(getTableName("CC_ROLE_RES"),"ROLE_ID");
			resrecord.set("ROLE_ID", roleId);
			query.deleteById(resrecord);
			result.setMsg("删除成功！");
			query.commit();
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error("删除失败，失败原因:"+e1.getMessage(), e1);
				result.addFail("删除失败，失败原因:"+e1.getMessage());
			}
			this.error(e.getMessage(), e);
			result.addFail("删除失败，失败原因:"+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 修改
	 * @return
	 */
	public EasyResult actionForUpdate(){
		try {
			EasyRecord record=new EasyRecord(getTableName("CC_ROLE"),"ROLE_ID");
			record.setColumns(getJSONObject("role"));
			this.getQuery().update(record);
			return EasyResult.ok(null,"修改成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("修改失败:"+e.getMessage());
		}
	}
	
	/**
	 * 绑定资源
	 * @return
	 */
	public EasyResult actionForBindResource(){
		try {
			JSONObject params = getJSONObject();
			EasyQuery query = getQuery();
			
			String roleId = params.getString("roleId");
			JSONArray array = params.getJSONArray("resId");
			EasyRecord record = new EasyRecord(getTableName("CC_ROLE_RES"),"ROLE_ID");
			record.set("ROLE_ID", roleId);
			query.deleteById(record);
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("ENT_ID", this.getEntId());
			record.set("CREATOR", this.getUserPrincipal().getUserId());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString(null));
			for (Object resId : array) {
				record.set("RES_ID", resId);
				query.save(record);
			}
			return EasyResult.ok(null,"绑定成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("绑定失败:"+e.getMessage());
		}
	}


	/**
	 * 更新资源Tree
	 */
	public EasyResult actionForUpdateRes(){
		JSONArray jsonArray = this.getJSONArray();
    	String roleId = this.getRequest().getParameter("roleId");
		EasyQuery query = this.getQuery();
    	try {
    		query.begin();
    		query.execute("delete from "+getTableName("CC_ROLE_RES")+" where ROLE_ID = ?", new Object[]{roleId});

			EasyRecord record = new EasyRecord(getTableName("CC_ROLE_RES"),"ROLE_ID");
			record.set("ROLE_ID", roleId);
			record.set("BUSI_ORDER_ID", this.getBusiOrderId());
			record.set("ENT_ID", this.getEntId());
			record.set("CREATOR", this.getUserPrincipal().getUserId());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString(null));
    		for(int i = 0 ;i<jsonArray.size();i++){
    			record.set("RES_ID", jsonArray.getJSONObject(i).getString("resId"));
				query.save(record);
    		}
    		query.commit();
			return EasyResult.ok(null, "绑定成功！");
		} catch (Exception ex) {
			try {
				query.roolback();
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			this.error("绑定失败，原因："+ex.getMessage(),ex);
			return EasyResult.fail("绑定失败，原因："+ex.getMessage());
		}
	}
	
	
	/**
	 * 添加角色用户
	 * @return
	 */
	public EasyResult actionForAddUser(){
		JSONObject param = this.getJSONObject();
		JSONArray userIds = param.getJSONArray("userIds");
		String roleId = param.getString("roleId");
		String entId = this.getEntId();
		String busiOrderId = this.getBusiOrderId();
		String creator = this.getUserId();
		String createTime = EasyDate.getCurrentDateString();
		
		if(userIds!=null && userIds.size()>0){
			ArrayList<Object[]> list = new ArrayList<>();
			
			for(int i = 0; i < userIds.size(); i++){
				String userId = userIds.getString(i);
				if(StringUtils.isNotBlank(userId)){
					list.add(new Object[]{roleId,userId,entId,busiOrderId,creator,createTime});
				}
			}

			EasyQuery query = this.getQuery();
			try {
				query.begin();
				query.executeBatch("insert into "+getTableName("CC_ROLE_USER")+" (ROLE_ID,USER_ID,ENT_ID,BUSI_ORDER_ID,CREATOR,CREATE_TIME) values(?,?,?,?,?,?)", list);
				updateRoleUserList(query, roleId);
				query.commit();
			} catch (SQLException e) {
				try {
					query.roolback();
				} catch (SQLException e1) {
					this.error(e1.getMessage(), e1);
				}
				this.error(e.getMessage(), e);
				return EasyResult.fail("添加失败，失败原因："+e.getMessage());
			}
		}
		
		return EasyResult.ok();
	}
	
	/**
	 * 移除角色用户
	 * @return
	 */
	public EasyResult actionForDeleteUser(){
		JSONObject param = this.getJSONObject();
		String userId = param.getString("userId");
		String roleId = param.getString("roleId");
		JSONArray userIds = param.getJSONArray("userIds");
		
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			if(StringUtils.isNotBlank(userId)){
				query.execute("delete from "+getTableName("CC_ROLE_USER") +" where ROLE_ID = ? and USER_ID = ?", new Object[]{roleId,userId});
				updateUserList(query, userId);
			}else if(userIds != null && userIds.size() > 0){
				ArrayList<Object[]> list = new ArrayList<>();
				for(int i = 0; i < userIds.size(); i++){
					list.add(new Object[]{roleId,userIds.getString(i)});
				}
				query.executeBatch("delete from "+getTableName("CC_ROLE_USER") +" where ROLE_ID = ? and USER_ID = ?", list);
				updateRoleUserList(query, roleId);
			}
			query.commit();
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				this.error(e1.getMessage(), e1);
			}
			this.error(e.getMessage(), e);
			return EasyResult.fail("移除失败，失败原因："+e.getMessage());
		}
		
		return EasyResult.ok();
	}
	
	/**
	 * 批量修改用户角色
	 * @param query
	 * @param roleId
	 * @throws SQLException
	 */
	private void updateRoleUserList(EasyQuery query, String roleId) throws SQLException{
		List<JSONObject> list = query.queryForList("select USER_ID from "+getTableName("CC_ROLE_USER")+" where ROLE_ID = ?", new Object[]{roleId}, new JSONMapperImpl());
		if(list!=null){
			for (JSONObject row : list) {
				updateUserList(query, row.getString("USER_ID"));
			}
		}
	}
	
	/**
	 * 跟新坐席角色名称
	 * @param userId
	 * @throws SQLException
	 */
	private void updateUserList(EasyQuery query, String userId) throws SQLException{
		String roleList = "";
		List<EasyRow> list = query.queryForList("select t1.ROLE_NAME from "+getTableName("CC_ROLE")+" t1,"+getTableName("CC_ROLE_USER")+" t2 where t1.ROLE_ID = t2.ROLE_ID and t2.BUSI_ORDER_ID = ? and t2.USER_ID = ?", new Object[]{this.getBusiOrderId(), userId});
		if(list != null){
			for (EasyRow easyRow : list) {
				roleList += "," + easyRow.getColumnValue("ROLE_NAME");
			}
			if(roleList.length() > 1){
				roleList = roleList.substring(1);
			}
		}
		query.execute("update "+getTableName("CC_BUSI_USER")+ " set ROLE_LIST = ? where BUSI_ORDER_ID = ? and USER_ID = ?", new Object[]{roleList,this.getBusiOrderId(), userId});
	}
	
	@Override
	protected String getResId() {
		return null;
	}
}
