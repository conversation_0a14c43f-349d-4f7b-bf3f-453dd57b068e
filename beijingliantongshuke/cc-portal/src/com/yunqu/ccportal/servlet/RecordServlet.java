package com.yunqu.ccportal.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.ccportal.base.AppBaseServlet;

@WebServlet("/servlet/record/*")
public class RecordServlet extends AppBaseServlet {

	private static final long serialVersionUID = -1864094683991960092L;
	
	/**
	 * 跟新未接来电
	 * @return
	 */
	public EasyResult actionForUpdateNoAnswer(){
		try {
			this.getQuery().execute("update " + getTableName("CC_CALL_NOANSWER") + " set STATE = 1,HANDLE_TIME=?,USERNAME=?,USER_ID=? where SERIAL_ID = ?",
					new Object[]{EasyDate.getCurrentDateString(),this.getUserPrincipal().getUserName(),this.getUserPrincipal().getUserId(),getJsonPara("serialId")});
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("操作失败");
		}
		return EasyResult.ok(null, getI18nValue("操作成功"));
	}
	
	@Override
	protected String getResId() {
		return null;
	}

}
