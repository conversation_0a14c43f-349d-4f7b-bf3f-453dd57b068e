package com.yunqu.ccportal.service;

import java.util.Iterator;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.HCodeUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.ServiceUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.ccportal.base.Constants;
import com.yunqu.ccportal.base.QueryFactory;

/**
 * url处理基类
 */
public class BaseCallUrlService  extends IService{
	
	//默认使用云客服的弹屏地址，主要是兼容东风版本，其他所有项目都不需要
	private static String defaultUrl="/yc-agent/pages/portal/voice_portal.jsp";
	
	protected static Logger logger = LogEngine.getLogger(Constants.APP_NAME+"_callurl");

	@Override
	public JSONObject invoke(JSONObject arg0) throws ServiceException {
		// TODO Auto-generated method stub
		return null;
	}

	/**
	 * 在弹屏url里，根据请求参数、随路数据，拼接出最终的url；
	 * @param custPhone    客户号码
	 * @param servicePhone 平台号码
	 * @param callType     呼叫类型  01-呼入 02-话出
	 * @param url          弹屏url  
	 * @param jsonObject   弹屏事件json信息
	 * @return
	 */
	public String addUrlParamFromJson(String custPhone,String servicePhone,String callType, String url, JSONObject jsonObject) {
		//标记是电话来源
		if(!jsonObject.containsKey("source")){
			jsonObject.put("source", "call");
		}
		//标记呼叫时间
		if(!jsonObject.containsKey("callTime")){
			jsonObject.put("callTime", DateUtil.getCurrentDateStr());
		}
		//标记呼叫类型  01-呼入 02-呼出
		if(!jsonObject.containsKey("callType")){
			jsonObject.put("callType", callType);
		}
		//呼叫信息里的随路数据
		JSONObject userData = jsonObject.getJSONObject("userData");
		
		Iterator<String> iter = jsonObject.keySet().iterator();
		if(url.indexOf("?")==-1)url=url+"?";
		StringBuffer params=new StringBuffer();
		while(iter.hasNext()){
			String key = iter.next();
			if(jsonObject.get(key) instanceof JSONArray){
				
			}else if(jsonObject.get(key) instanceof JSONObject){
				JSONObject j = (JSONObject) jsonObject.get(key);
				if(j!=null){
					for(String k : j.keySet()){
						String v = j.getString(k);
						if(StringUtils.isBlank(v) || v.indexOf("{")!=-1 || v.indexOf("}")!=-1){
							continue;
						}
						params.append("&"+key+"-"+k+"="+v);
						if((url.indexOf("$"+k))>0){
							url = StringUtils.replace(url, "$"+k, v);
						}
					}
				}
			}else{
				String val = "";
				if(userData != null) {
					val = userData.getString(key);
				}
				if(StringUtils.isBlank(val) || "null".equals(val)) {
					val = jsonObject.getString(key);
				}
				if(StringUtils.isBlank(val) || val.indexOf("{")!=-1 || val.indexOf("}")!=-1){
					continue;
				}
				if((url.indexOf("$"+key))>0){
					url = StringUtils.replace(url, "$"+key, val);
				}else{
					params.append("&"+key+"="+val);
				}
			}
		}
		url=url+params.toString();
		
		//获取号码归属地
		url = getArea(url,custPhone);
		return url;
	}
	
	/**
	 * 获取自定义弹屏信息：根据接入号码、技能组获取对应的弹屏url配置
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param servicePhone
	 * @param skillId
	 * @return
	 */
	public JSONObject getCustCallUrl(String entId,String busiOrderId,String schema,String servicePhone,String skillId){
		try {
			EasyQuery query = QueryFactory.getQuery(entId);		
			//自定义弹屏
			EasySQL sql = new EasySQL("select * ");
			sql.append(" FROM " + schema + ".C_CF_CALLURL T1 ");
			sql.append(" WHERE 1=1 ");
			sql.append("01"," AND T1.ENABLE_STATUS=? ");
			sql.append("1"," AND T1.CALLURL_TYPE=? ");  //3.1#20211101-1 只提取来电接通弹屏的配置信息
			sql.append(entId, "AND T1.ENT_ID=?");
			sql.append(busiOrderId, "AND T1.BUSI_ORDER_ID=?");
			sql.append(servicePhone, "AND ((T1.BUSI_TYPE=2 AND BUSI_ID=?)", false);
			sql.append(skillId, "OR (T1.BUSI_TYPE=3 AND BUSI_ID=?) OR BUSI_TYPE='1')", false);
			sql.append(" ORDER BY IS_UNIQUE DESC, BUSI_TYPE DESC");
			if(ServerContext.isDebug()) {
				logger.info("查找自定义弹屏sql=" + sql.getSQL() + "param=" + JSON.toJSONString(sql.getParams()));
			}
			JSONObject row = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求来电自定义弹屏 busiInfo->"+row);
			}
			return row;
		} catch (Exception e) {
			logger.error("查询自定义弹屏异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 获取配置的唯一弹屏
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @return
	 */
	public JSONObject getSystemUniqueCallurl(String entId,String busiOrderId,String schema){
		try {
			EasyQuery query = QueryFactory.getQuery(entId);		
			//系统唯一弹屏
			String callUrlNotNull = "";
			if(query.getTypes() == DBTypes.MYSQL) {
				callUrlNotNull = " AND CALL_URL !=''";
			}
			
			StringBuffer sql = new StringBuffer();
			sql.append(" select CALL_URL from CC_BUSI_ORDER  where BUSI_ID=? AND BUSI_ORDER_ID = ? AND CALL_URL IS NOT NULL " + callUrlNotNull);
			sql.append(" UNION ");
			sql.append(" SELECT CALL_URL FROM CC_BUSI_ORDER T WHERE T.BUSI_ID=? AND T.ENT_ID = (SELECT P_ENT_ID FROM CC_ENT WHERE ENT_ID=?) AND CALL_URL IS NOT NULL " + callUrlNotNull);
			
			if(ServerContext.isDebug()){
				logger.info("查询唯一弹屏sql:"+sql.toString()+",BUSI_ID=007,BUSI_ORDER_ID"+busiOrderId+",entId="+entId);
			}
			JSONObject row = query.queryForRow(sql.toString(), new Object[] {"007", busiOrderId,"007",entId },new JSONMapperImpl());
				
			return row;
		} catch (Exception e) {
			logger.error("查询唯一弹屏弹屏异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 获取客户当天的来电次数，操作数据包含：当天来电次数、近X小时来电次数、近X小时最近接听坐席
	 * @param json
	 * @param caller
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param updateFlag true-直接更新缓存里当天来电次数、近X小时的呼入次数和最近服务的坐席；  false - 查询该号码的当天的呼入次数，不更新缓存
	 * @return 当天客户呼入的次数
	 */
	public int getCallTimes(JSONObject json,String caller, String entId,String busiOrderId,String schema,boolean updateFlag) {
		if(StringUtils.isBlank(caller) || StringUtils.isBlank(entId)){
			//默认为1（本次来电）
			return 1;
		}
		
		/** 3.1#20210809-1 改为写缓存
		String date = DateUtil.getCurrentDateStr("yyyyMMdd");
		EasySQL sql = new EasySQL("SELECT COUNT(1) FROM "+schema+".CC_CALL_RECORD T WHERE T.ENT_ID=? AND T.DATE_ID=? AND T.CALLER=?");
		EasyQuery query = QueryFactory.getWriteQuery();
		int callTimes = 1;
		try {
			callTimes = query.queryForInt(sql.getSQL(), entId,date,caller); //包含本次;
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询来电次数异常:"+e.getMessage(),e);
		}
		if(callTimes<=1){
			callTimes = 1;
		}
		*/
		//系统保存客户最近接待坐席的时长，单位小时
		int cacheCallTimesHour = getCacheCallTimesHour();
				
		//设置该号码当天来电次数
		String todayCallInTimesKey = "CC_BASE_CUST_CALLTIMES_"+entId+"_"+caller+"_"+DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
		int todayCallInTimes = CommonUtil.parseInt(CacheUtil.get(todayCallInTimesKey));
		
		//直接查询呼入次数后返回
		if(!updateFlag){
			return todayCallInTimes;
		}
		
		//本次加1
		if(todayCallInTimes<=0){
			todayCallInTimes = 1;
		}else{
			todayCallInTimes ++;
		}
		
		CacheUtil.put(todayCallInTimesKey, todayCallInTimes+"",26*3600);
		
		//记录该号码最近接听坐席
		String agentId = json.getString("agentId");
		if(StringUtils.isNotBlank(agentId)){
			String key2 = "CC_BASE_CUST_LATESTAGENT_"+entId+"_"+caller;
			CacheUtil.put(key2, agentId,cacheCallTimesHour*3600);
		}
		
		//设置该号码近X小时呼入信息汇总,上面的缓存先保留，有些模块里已经在使用，必须保留
		try {
			String latestCallInKey = "CC_BASE_CUST_CALLIN_"+entId+"_"+caller;
			String  latestCallInStr = CacheUtil.get(latestCallInKey);
			JSONObject cache = new JSONObject();
			if(StringUtils.isNotBlank(latestCallInStr)){
				cache = JsonUtil.toJSONObject(latestCallInStr);
				Integer latestCallInTimestamp = cache.getInteger("latestCallInTimestamp");
				
				//缓存中上次来电时间不为空，且  （当前时间-上次来电时间）< 配置的缓存最后接听坐席的时间时，将该通话标记为重复来电
				if(latestCallInTimestamp>0 && (System.currentTimeMillis() - latestCallInTimestamp) < cacheCallTimesHour*3600*1000){
					try {
						logger.info("号码"+caller+"的重复来电,距离上次来电未超过"+cacheCallTimesHour+" 小时...");
						EasyRecord record = new EasyRecord(schema+".CC_CALL_RECORD","SERIAL_ID");
						record.put("SERIAL_ID", json.getString("callSerialId"));
						record.put("REPEAT_CALL", "0");  //0-重复来电 1-非重复来电
						QueryFactory.getWriteQuery().update(record);
					} catch (Exception e) {
						logger.warn("号码"+caller+"的重复来电信息写入异常:"+e.getMessage(),e);
					}
				}
			}
			cache.put("latestCallInTimes", (cache.getInteger("latestCallInTimes")==null ? 0 : cache.getInteger("latestCallInTimes") )+1);
			cache.put("todayCallInTimes", todayCallInTimes);
			cache.put("latestCallInAgentId", agentId);
			cache.put("latestCallInTime", DateUtil.getCurrentDateStr());
			cache.put("latestCallInTimestamp", System.currentTimeMillis());
			cache.put("cacheCallInTimesHour", cacheCallTimesHour);
			
			CacheUtil.put(latestCallInKey,cache.toJSONString(),cacheCallTimesHour*3600);
			
		} catch (Exception e) {
			logger.error("号码"+caller+"的呼入汇总信息写入缓存异常:"+e.getMessage(),e);
		}
		
		return todayCallInTimes;
	}
	
	/**
	 * 获取当天呼出该号码的次数，操作数据包含：更新当天呼出次数、近X小时呼出次数，近X小时最近呼出该号码的坐席
	 * @param json
	 * @param caller
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param updateFlag true-直接更新缓存里近X小时的呼入次数和最近服务的坐席；false - 查询该号码的呼入次数
	 * @return  返回当天呼出该号码次数
	 */
	public int getCallOutTimes(JSONObject json,String caller, String entId,String busiOrderId,String schema,boolean updateFlag) {
		if(StringUtils.isBlank(caller) || StringUtils.isBlank(entId)){
			return 1;
		}
		
		//从ccbase配置项里获取缓存客户最近来电信息的保存时长，单位：小时
		int cacheCallTimesHour = getCacheCallTimesHour();
		
		
		//设置该号码最近呼出次数
		String todayCallOutTimeKey = "CC_BASE_CUST_CALLTIMES_OUT_"+entId+"_"+caller+"_"+DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
		int todayCallOutTime = CommonUtil.parseInt(CacheUtil.get(todayCallOutTimeKey));
		
		//直接查询当天呼出次数后返回
		if(!updateFlag){
			return todayCallOutTime;
		}
		//本次加1
		if(todayCallOutTime<=0){
			todayCallOutTime = 1;
		}else{
			todayCallOutTime ++;
		}
		
		CacheUtil.put(todayCallOutTimeKey, todayCallOutTime+"",26*3600);
		
		//记录该号码最近呼出坐席
		String agentId = json.getString("agentId");
		if(StringUtils.isNotBlank(agentId)){
			String key2 = "CC_BASE_CUST_LATESTAGENT_OUT_"+entId+"_"+caller;
			
			CacheUtil.put(key2, agentId,cacheCallTimesHour*3600);
		}
		
		//设置该号码近X小时呼出信息汇总,上面的缓存先保留，有些模块里已经在使用，必须保留
		try {
			String latestCallOutKey = "CC_BASE_CUST_CALLOUT_"+entId+"_"+caller;
			String  latestCallOutStr = CacheUtil.get(latestCallOutKey);
			JSONObject cache = new JSONObject();
			if(StringUtils.isNotBlank(latestCallOutStr)){
				cache = JsonUtil.toJSONObject(latestCallOutStr);
			}
			cache.put("latestCallOutTimes", (cache.getInteger("latestCallOutTimes")==null ? 0 : cache.getInteger("latestCallOutTimes")) +1);
			cache.put("todayCallOutTimes", todayCallOutTime);
			cache.put("latestCallOutAgentId", agentId);
			cache.put("latestCallOutTime", DateUtil.getCurrentDateStr());
			cache.put("cacheCallOutTimesHour", cacheCallTimesHour);
			
			CacheUtil.put(latestCallOutKey,cache.toJSONString(),cacheCallTimesHour*3600);
			
		} catch (Exception e) {
			logger.error("号码"+caller+"的呼出汇总信息写入缓存异常:"+e.getMessage(),e);
		}
		
		
		return todayCallOutTime;
	}
	
	/**
	 * 从ccbase配置项里获取缓存客户最近来电信息的保存时长，单位：小时
	 * @return
	 */
	private int getCacheCallTimesHour() {
		try {
			//系统保存客户最近接待坐席的时长，单位小时
			int saveCustLatestAgentTime = Constants.getSaveCustLatestAgentTime();
			if(saveCustLatestAgentTime<=0){
				saveCustLatestAgentTime = 50;
			}
			return saveCustLatestAgentTime;
		} catch (Exception e) {
		}
		return 50;
	}

	/**
	 * 对当天客户来电次数进线预警
	 * @param json
	 * @param caller
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param callTimes
	 */
	public void warningCallTimes(JSONObject json,String caller, String entId,String busiOrderId,String schema,int callTimes) {
		//是否需要监控客户当天的来电次数
		if(callTimes>1 &&  StringUtils.equals(Constants.MONITOR_CALL_TIME, "Y")){
			
			//来电次数大于1时，需要调用cc-base的服务通知来电次数
			try {
				
				/**
				JSONObject j = new JSONObject();
				j.put("sender", Constants.APP_NAME);
				j.put("serviceId", "CCBASE_CALL_INTERFACE");
				j.put("command", "customerCallTimes");
				j.put("caller", caller);
				j.put("called", json.getString("called"));
				j.put("callDirection", "1"); //呼叫方向，1-呼入  2-呼出 目前只有呼入弹屏，只能先写死 
				j.put("callTimes", callTimes);
				j.put("dateId", DateUtil.getCurrentDateStr("yyyyMMdd"));
				String agentId = json.getString("agentId");
				if(StringUtils.isNotBlank(agentId)){
					j.put("agentId", agentId);
					JSONObject userJson = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentId);
					if(userJson != null){
						j.put("userAcc", userJson.getString("USER_ACCT"));
						j.put("deptCode",userJson.getString("DEPT_CODE") );
					}
				}
				j.put("groupId", json.getString("groupId"));
				j.put("busiOrderId", busiOrderId);
				j.put("entId", entId);
				j.put("schema",schema );
				logger.info("通知客户来电次数,请求 >> "+j);
				//进行来电次数预警
				JSONObject result = ServiceUtil.invoke("CCBASE_CALL_INTERFACE", j);
				logger.info("通知客户来电次数,响应 << "+result);
				*/
				
				//3.1#20211224-1 通过cc-callmonitor模块的服务进行重复来电预警
				JSONObject j = new JSONObject();
				j.put("sender", Constants.APP_NAME);
				j.put("password", CEConstants.getServiceInterfacePwd());
				j.put("serialId", IDGenerator.getDefaultNUMID());
				j.put("command", "customerRepeatCall");
				j.put("entId", entId);
				j.put("busiOrderId", busiOrderId);
				j.put("caller", caller);
				j.put("count", callTimes);
				logger.info("客户来电次数预警,请求 >> "+j);
				JSONObject result = ServiceUtil.invoke("CALL_MONITOR_NOTICE_INTERFACE", j);
				logger.info("客户来电次数预警,响应 << "+result);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 调用通知客户来电次数服务异常:"+e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 获取号码归属地，拼接到url里
	 * @param url
	 * @param caller
	 * @return
	 */
	public String getArea(String url,String caller) {
		String areaCode = HCodeUtil.getAreacodeByCalled(caller);
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 根据号码查询hcode缓存,"+caller+","+areaCode);
		if(StringUtils.isBlank(areaCode)){
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 获取不到移动H码，尝试获取固话H码"+caller+","+areaCode);
			areaCode = getGhCode(caller);
		}
		if(StringUtils.isBlank(areaCode)){
			return url;
		}
		if(url.indexOf("?")!=-1){
			url += "&areaCode="+areaCode;
		}else{
			url += "?areaCode="+areaCode;
		}
		//查询省份编码
		JSONObject json = getAreaByAreaCode(areaCode); //t1.PROVINCE_CODE,t1.AREA_NAME
		if(json==null){
			return url;
		}else{
			url += "&provinceCode="+json.getString("PROVINCE_CODE")+"&areaName="+json.getString("AREA_NAME");
		}
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 根据号码查询hcode缓存后url,"+url);
		
		return url;
	}
	
	public String getGhCode(String _called){
		
		//3.1#20211124-1 修改获取固话区号逻辑，从云呼获取不到区号时，从企业客服获取
		String hcode = _called.substring(0,2);  //获取前面的2为H码
		String areacode  = getGhCodeByPrefix(hcode); //获得该H码对应的地区
		if(StringUtils.isBlank(areacode)){  //如果HCode没有配置，则不限
			hcode = _called.substring(0,3);  //获取前面的3为H码
			areacode  = getGhCodeByPrefix(hcode); //获得该H码对应的地区
			if(StringUtils.isBlank(areacode)){  //如果HCode没有配置，则不限
				hcode = _called.substring(0,4);  //获取前面的4为H码
				areacode  = getGhCodeByPrefix(hcode); //获得该H码对应的地区
			}
		}
		return areacode;
	}
	
	/**
	 * 根据号码前缀获取地区编号
	 * @param _called
	 * @return
	 */
	public String getGhCodeByPrefix(String _called_prefix){
		if(StringUtils.isBlank(_called_prefix)){
			return null;
		}
		
		String areacode  = CacheUtil.get("G_HCODE_" + _called_prefix); //从云呼获取
		if(StringUtils.isBlank(areacode)){  //如果HCode没有配置，则直接根据区号获取企业客服里的缓存
			
			//从企业客服获取，需要去掉前面的0
			if(_called_prefix.startsWith("0")){
				_called_prefix = _called_prefix.replaceFirst("0", "");
			}
			//如果只是一位，不用处理
			if(_called_prefix.length()<=1){
				return null;
			}
			JSONObject area = getAreaByAreaCode(_called_prefix);
			if(area!=null){
				areacode = area.getString("AREA_CODE");
			}
		}else{
			try{
				JSONObject areaObj = JSONObject.parseObject(areacode);
				areacode = areaObj.getString("area");
			}catch (Exception e) {
				areacode = "";
			}
		}
		return areacode;
	}
	
	/**
	 * 根据地区编号获取地区信息
	 * @param areaCode
	 * @return   AREA_CODE, PROVINCE_CODE, AREA_NAME, PROVINCE_NAME
	 */
	public JSONObject getAreaByAreaCode(String areaCode){
		//缓存在cc-base每晚3点更新
		String str = CacheUtil.get("CC_BASE_AREA_"+areaCode);
		if(StringUtils.isBlank(str)){
			return null;
		}
		try {
			return JsonUtil.toJSONObject(str);
		} catch (Exception e) {
			logger.error("查询地区异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 使用yc-agent的弹屏url； 在企业呼叫中心里基本用不上；除非开启了来电弹屏，但是又没配置url
	 * @param jsonObject
	 * @return
	 */
	public String getUrl(JSONObject jsonObject){
//		if(StringUtils.isBlank(jsonObject.getString("custObjId"))){
//			jsonObject.put("custObjId", RandomKit.randomStr());
//		}
		if(StringUtils.notBlank(jsonObject.getString("callSerialId"))){
			jsonObject.put("serialId", jsonObject.getString("callSerialId"));
		}
		jsonObject.put("source", "call");
		Iterator<String> iter = jsonObject.keySet().iterator();
		StringBuffer url=new StringBuffer(defaultUrl);
		url.append("?");
		while(iter.hasNext()){
			String key = iter.next();
			if(jsonObject.get(key) instanceof JSONArray){
				
			}else if(jsonObject.get(key) instanceof JSONObject){
				
			}else{
				String val = jsonObject.getString(key);
				url.append(key).append("=").append(val).append("&");
			}
		}
		return url.toString();
	}
	
	/**
	 * 当接听客户电话时，自动将客户的近三个月的待处理的未接、漏话、留言，设置为无需处理
	 * @param entId       企业ID
	 * @param busiOrderId 订购ID
	 * @param schema       
	 * @param servicePhone 平台号码
	 * @param custPhone    客户号码
	 * @param agentAcc   
	 */
	public void ignoreMissCall(String entId, String busiOrderId, String schema, String servicePhone, String custPhone,String agentAcc) {
		try {
			//是否自动处理客户漏话等记录，0-否 1-是
			//开启后，当该客户拨打电话客服接听时，如果该客户近三个月内有待处理的漏话、未接来电时，会自动将折线漏话、未接设置为已处理
			String autoHandleMiscall = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-base", "AUTO_HANDLE_MISCALL");
			//是否自动处理客户留言记录，0-否 1-是
			String autoHandleWord = SystemParamUtil.getEntParam(schema, entId, busiOrderId, "cc-base", "AUTO_HANDLE_WORD");
			if (StringUtils.equals("1", autoHandleMiscall)) {
				handleCallloss("1", entId, busiOrderId, schema, servicePhone, custPhone, agentAcc);
				handleCallloss("2", entId, busiOrderId, schema, servicePhone, custPhone, agentAcc);
			}
			if (StringUtils.equals("1", autoHandleWord)) {
				handleCallloss("3", entId, busiOrderId, schema, servicePhone, custPhone, agentAcc);
			}
		} catch (Exception e) {
			logger.info("自动设置客户漏话处理状态异常:"+e.getMessage(),e);
		}
		
	}
	/**
	 * 
	 * @param type 1:漏话 2:未接 3:留言
	 * @param entId
	 * @param busiOrderId
	 * @param schema
	 * @param servicePhone
	 * @param custPhone
	 * @param agentAcc
	 */
	private void handleCallloss(String type,String entId, String busiOrderId, String schema, String servicePhone
			, String custPhone,String agentAcc) throws Exception{
		if(StringUtils.isBlank(agentAcc)){
			logger.error("无法自动设置客户漏话处理状态失败，agentAcc为空.");
			return;
		}
		
		//USER_ID、USER_ACCT、USERNAME、DEPT_CODE、DEPT_NAME
		JSONObject user = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, agentAcc);
		if(user==null){
			logger.error("无法自动设置客户漏话处理状态失败,无法查询到坐席信息["+agentAcc+"].");
			return;
		}
		
		String userId = user.getString("USER_ID");
//		String userAcct = user.getString("USER_ACCT");
		String userName = user.getString("USERNAME");
		String deptCode = user.getString("DEPT_CODE");
//		String deptName = user.getString("DEPT_NAME");
		String handleTime = DateUtil.getCurrentDateStr();
		String handleDesc = "已接听客户来电,自动设置为已处理,无需再次回拨";
		
		EasyQuery query = QueryFactory.getWriteQuery();
		
		String dateId = DateUtil.addDay("yyyyMMdd", DateUtil.getCurrentDateStr("yyyyMMdd"), -90);
		
		if (StringUtils.equals(type, "1")) {
			//修改漏话
			String sql = "UPDATE "+schema+".CC_CALL_MISCALL SET STATE=1,USER_ID=?,USERNAME=?,HANDLE_TIME=?,HANDLE_DEPT_CODE=?,HANDLE_DESC=? "
					+ "WHERE ENT_ID=? AND DATE_ID>=? AND CALLER=?  AND STATE = 0";
			query.execute(sql, userId,userName,handleTime,deptCode,handleDesc,entId,dateId,custPhone);
			logger.info("自动设置客户漏话处理状态完成,entId="+entId+",custPhone="+custPhone);
		}else if (StringUtils.equals(type, "2")) {
			//修改未接
			String sql2 = "UPDATE "+schema+".CC_CALL_NOANSWER SET STATE=1,USER_ID=?,USERNAME=?,HANDLE_TIME=?,HANDLE_DEPT_CODE=?,HANDLE_DESC=? "
					+ "WHERE ENT_ID=? AND DATE_ID>=? AND CALLER=?  AND STATE = 0";
			query.execute(sql2, userId,userName,handleTime,deptCode,handleDesc,entId,dateId,custPhone);
			logger.info("自动设置客户未接处理状态完成,entId="+entId+",custPhone="+custPhone);
		}else if (StringUtils.equals(type, "3")) {
			//修改留言
			String sql3 = "UPDATE "+schema+".CC_CALL_WORD  SET STATE=1,USER_ID=?,USERNAME=?,HANDLE_TIME=?,HANDLE_DEPT_CODE=?,HANDLE_DESC=?"
					+ "WHERE ENT_ID=? AND DATE_ID>=? AND CALLER=? AND STATE = 0";
			query.execute(sql3, userId,userName,handleTime,deptCode,handleDesc,entId,dateId,custPhone);
			logger.info("自动设置客户留言处理状态完成,entId="+entId+",custPhone="+custPhone);
		}
	}
	
	/**
	 * 用户获取来电弹屏界面的id，该id作为界面tab页的唯一标识；
	 * 如果id相同，在界面上不能重复弹出
	 * @param unique
	 * @param entId
	 * @return
	 */
	public String getCallUrlPageId(int unique, String entId) {
		String id = "callin_007_"+entId;
		if(unique == 2) {
			//如果不是唯一弹屏
			id =  "callin_007_"+IDGenerator.getIDByCurrentTime(15);
		}
		return id;
	}
	
	/**
	 * 如果是预测式场景的外呼，此时要弹云电销的弹屏；
	 * 可能是在企业客服挂载了云电销的相关菜单
	 * @param jsonObject
	 * @return
	 */
	public JSONObject getYcportalUrl(JSONObject jsonObject) {
		String createCause = jsonObject.getString("createCause");
		String objId = jsonObject.getString("objId");
		String taskId = jsonObject.getString("taskId");
		String ycportalUrl = Constants.getYcportalUrl();
		JSONArray urls = new JSONArray();
		
		//仅仅预测式外呼，进入到云电销的弹屏地址；如果不配置则继续找企业客服的弹屏
		if("8".equals(createCause) && StringUtils.isNotBlank(objId) && StringUtils.isNotBlank(taskId) && StringUtils.isNotBlank(ycportalUrl)){
			JSONObject taskUrl = new JSONObject();
			taskUrl.put("id", "voiceCall");
			taskUrl.put("title", "外呼弹屏");
			taskUrl.put("type", "1");
			taskUrl.put("url", ycportalUrl.replace("#objId#", objId).replace("taskId", taskId));
			urls.add(taskUrl);
			
			JSONObject retObj = new JSONObject();
			retObj.put("urls", urls);
			
			if(ServerContext.isDebug()){
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回弹屏信息,urls->"+retObj);
			}
			
			return retObj;
		}
		return null;
		
	}
}
