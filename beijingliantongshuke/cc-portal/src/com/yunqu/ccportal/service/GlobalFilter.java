package com.yunqu.ccportal.service;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebFilter({"/*"})
public class GlobalFilter implements Filter{

	@Override
	public void destroy() {
		
	}

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)throws IOException, ServletException {
		 HttpServletRequest request = (HttpServletRequest)servletRequest;  
		 HttpServletResponse response = (HttpServletResponse)servletResponse;  
		 response.addHeader("X-Content-Type-Options", "nosniff");
		 response.addHeader("Content-Security-Policy", "none");
		 response.addHeader("X-XSS-Protection", "1; mode=block");
		 response.addHeader("X-FRAME-OPTIONS","SAMEORIGIN"); 
		 response.setHeader("Access-Control-Allow-Methods","GET,POST");
		 chain.doFilter(request, response);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		
	}
   
}
