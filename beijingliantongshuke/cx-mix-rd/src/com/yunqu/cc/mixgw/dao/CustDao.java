package com.yunqu.cc.mixgw.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppDaoContext;

@WebObject(name = "CustDao")
public class CustDao extends AppDaoContext {
	
	@WebControl(name = "record", type = Types.RECORD)
	public JSONObject record() {
		JSONObject result = new JSONObject();
		JSONObject data = new JSONObject();
		data.put("name", "");
		data.put("sex", "");
		data.put("card", "");
		data.put("phone", "");
		data.put("room", "");
		data.put("area", "");
		data.put("dept", "");
		String customerPhone = param.getString("customerPhone");
		if(StringUtils.isNotBlank(customerPhone)) {
			if("15963129651".equals(customerPhone)) {
				data.put("name", "王鹏远");
				data.put("sex", "1");
				data.put("card", "370124199809192010");
				data.put("phone", "17861514578");
				data.put("room", "2008");
				data.put("area", "天作国际");
				data.put("dept", "云趣");
			}
		}
		result.put("data", data);
		return result;
	}
	
	
	
}
