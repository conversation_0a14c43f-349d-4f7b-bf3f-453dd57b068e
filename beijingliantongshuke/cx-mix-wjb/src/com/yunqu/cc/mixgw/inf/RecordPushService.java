package com.yunqu.cc.mixgw.inf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.EntContext;
import com.yunqu.cc.mixgw.util.DateUtil;
import com.yunqu.cc.mixgw.util.HttpResp;
import com.yunqu.cc.mixgw.util.HttpUtil;

public class RecordPushService extends IBaseService{
	
	public Logger logger = CommonLogger.getLogger("_record");
		
	private String entId = Constants.getEntId();
	private static String  dbName= "ycbusi";
	
	protected static EasyQuery getQuery()
    {
 	  return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
    }
	

	public String updateRecord(String typeCall) {
		if(typeCall.equals(Constants.MISCALL)) {
			return "/workOrder/foreign-workorder/v1/traffic/receiveCallLoss";//漏话接口地址
		}else {
			return "/workOrder/foreign-workorder/v1/traffic/receiveCall";//话单接口地址
		}
	}
	
	
	@Override
	public JSONObject invokeMethod(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "调用成功，参数：" + command+json.toJSONString());
		
//		EntContext context = EntContext.getContext(Constants.getEntId());
//		if(context == null){
//			CommonLogger.getLogger().info(" >> context 为空，请检查");
//			return null;
//		}
		dbName=Constants.ORDER_DB_NAME;
		if ("pushRecord".equals(command)) {//话单
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始执行一分钟一次的定时任务-话单...");
			return pushRecord(Constants.CALL);
		}else if("pushMiscall".equals(command)){//漏话
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始执行一分钟一次的定时任务-漏话...");
			return pushRecord(Constants.MISCALL);
		}else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			return result;
		}
	}
	
	/**
	 * 话单、漏话数据推送
	 * @param typeCall 区分类型是话单还是漏话
	 * @return
	 */
	public JSONObject pushRecord(String typeCall) {
		JSONObject rtJson = new JSONObject();
		// 未同步的录音文件数据
		JSONObject json = getRecord(typeCall);
		if(!json.getString("size").equals("0")) {
			HttpResp resp = HttpUtil.sendPost(Constants.getOrderWjbIp()+updateRecord(typeCall), json.toString(), HttpUtil.TYPE_JSON, "UTF-8",Constants.HEAD_NAME, Constants.TOKEN,20000);
			JSONObject reJson = JSON.parseObject(resp.getResult()).getJSONObject("meta");
			logger.info(CommonUtil.getClassNameAndMethod(this)+"接口返回信息：" + reJson.toJSONString());
			String code = reJson.getString("code");
			if (!"0".equals(code)) {
				rtJson.put("respCode", "999");
				rtJson.put("respDesc", "操作失败");
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 话单数据数据传输失败...");
				return rtJson;
			}
			// 推送成功后修改 话单数据为已同步 1
			List<JSONObject> list=JSONArray.parseArray(JSONArray.toJSONString(json.getJSONArray("trafficList")), JSONObject.class);
			updateRecord(typeCall,list);
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 结束执行一分钟一次的定时任务...");
		rtJson.put("respCode", "000");
		rtJson.put("respDesc", "操作成功");
		return rtJson;
	}
	
	/**
	 * 更新推送完毕后的录音为已同步 1为已同步
	 * typeCall call 话单，miscall 漏话
	 */
	public void updateRecord(String typeCall,List<JSONObject> array) {
		String sql = "update " + dbName +".cc_call_record" + " set IS_SYNCH = ? where SERIAL_ID = ?";
		if(StringUtils.equals(typeCall,Constants.MISCALL)) {
			sql = "update " + dbName +".cc_call_miscall" + " set IS_SYNCH = ? where SERIAL_ID = ?";
		}
		for (JSONObject row : array) {
			try {
				getQuery().executeUpdate(sql, new Object[]{Constants.SYNC_1,row.getString("callSerialId")});
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(),e);
			}
		}
	}
	
	/**
	 * 话单、漏话 基本数据(参考集成接口说明文档)
	 * typeCall 类型
	 */
	private JSONObject getRecord(String typeCall) {
		JSONObject obj=new JSONObject();
		EasyQuery query = getQuery();
		List<JSONObject> rowList = new ArrayList<JSONObject>();
		try {
	        String day =DateUtil.getBeforeDateStr("yyyyMMdd");
			// 所有未同步的话单数据
			String sql = "select GROUP_ID,AGENT_ID,PHONE_NUM,SERIAL_ID,CALLER,CALLED,BILL_BEGIN_TIME,"
					+ "BILL_END_TIME,BILL_TIME,CREATE_CAUSE,CLEAR_CAUSE,RECORD_FILE ,ENT_ID from "
					+ "" + dbName +"."+ "cc_call_record" + " where IS_SYNCH = ? and ENT_ID = '" + entId + "' and DATE_ID=?";
			if(typeCall.equals(Constants.MISCALL)) {
				sql="select GROUP_ID,AGENT_ID,SERIAL_ID,CALLER,CALLED,QUEUE_TIME,END_TIME,TOTAL_TIME,"
					+ "CLEAR_CAUSE releaseCause from "
					+ "" + dbName +"."+ "cc_call_miscall" + " where IS_SYNCH = ? and ENT_ID = '" + entId + "' and DATE_ID>=?";
			}
			List<JSONObject> row = query.queryForList(sql, new Object[]{Constants.SYNC_0,day},new JSONMapperImpl());
			if(row!=null && row.size()>0){
				for (JSONObject jsonObject : row) {
					JSONObject json=new JSONObject();
					json.put("skillId", jsonObject.getString("GROUP_ID"));
					json.put("agentId", jsonObject.getString("AGENT_ID"));
					json.put("callSerialId", jsonObject.getString("SERIAL_ID"));
					json.put("caller", jsonObject.getString("CALLER"));
					json.put("called", jsonObject.getString("CALLED"));
					if(typeCall.equals(Constants.CALL)) {//话单的进入里面取值
						json.put("dn", jsonObject.getString("PHONE_NUM"));
						json.put("startTime", jsonObject.getString("BILL_BEGIN_TIME"));
						json.put("endTime", jsonObject.getString("BILL_END_TIME"));
						int callType = jsonObject.getIntValue("CREATE_CAUSE");
						 if(callType == 1||callType == 2||callType == 4){
							json.put("createCause","inbound");//呼入
						}else{
							json.put("createCause","outbound");//席间呼叫\呼出
						}
						json.put("clearCause", jsonObject.getString("CLEAR_CAUSE"));
						json.put("recordUrl", jsonObject.getString("RECORD_FILE"));
						json.put("busiCallId", jsonObject.getString("ENT_ID"));
						json.put("callTime", jsonObject.getString("BILL_TIME"));
					}else {
						json.put("startTime", jsonObject.getString("QUEUE_TIME"));
						json.put("endTime", jsonObject.getString("END_TIME"));
						json.put("totalTime", jsonObject.getString("TOTAL_TIME"));
						json.put("releaseCause", jsonObject.getString("CLEAR_CAUSE"));
						json.put("missCall", "missType");
					}
					rowList.add(json);
				}
			}
			obj.put("timestamp", System.currentTimeMillis() / 1000);
			obj.put("size", rowList.size());
			obj.put("trafficList",rowList);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(),e);
		}
		logger.info(typeCall+"话单信息：" + JSON.toJSONString(rowList));
		return obj;
	}
	
	@Override
	public String getServiceId() {
		return "CX_MIX_RECORD_PUSH";
	}

	@Override
	public String getName() {
		return "话单数据推送->中航信";
	}
}
