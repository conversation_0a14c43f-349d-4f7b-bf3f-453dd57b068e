// JavaScript Document
(function(){
	function addEvent(type,dom,fn){
		if(window.addEventListener){
			dom.addEventListener(type,fn,false);
		}else if(window.attachEvent){
			dom.attachEvent('on'+type,fn);
		}else dom['on'+type]=fn;
	}
	function removeEvent(type,dom,fn){
		if(window.removeEventListener){
			dom.removeEventListener(type,fn,false);
		}else if(window.detachEvent){
			dom.detachEvent('on'+type,fn);
		}else dom['on'+type]=null;
	}
	function Selection(id,data){
		var g=this,node,box,tm=null,z=function(name){return document.createElement(name)};
		node=document.getElementById(id);
		g.value=function(){
			return mySelection.text;
		};
		g.destory=function(){
			if(box){
				box.parentNode.removeChild(box);
				box=null;
				if(node) {
					removeEvent('mouseup',node,doShowSelectionMenu);
					removeEvent('mousedown',node,doHideSelectMenu);
				}
			}
		};
		if(node){
			intiStyle();
			box=z('div');
			box.className='selectMenuBar';
			box.id='SelectMenuBar';
			if(data&&data.length>0){
				var f=document.createDocumentFragment();
				for(var i=0; i<data.length; i++){
					var n=z('p');
					n.innerHTML=data[i];
					f.appendChild(n);
					initEvent(i,n);
				}
				box.appendChild(f);
			}
			addEvent('click',node,doShowSelectionMenu);
			addEvent('mousedown',node,doHideSelectMenu);
			addEvent('contextmenu',node,function(e){e.returnValue=false; return false});
			addEvent('click',window,doHideSelectMenu);
		}
		function initEvent(index,node){
			addEvent('click',node,function(e){
				e.stopPropagation();
				if(mySelection.onClick) mySelection.onClick(index,g.value(),node);
				if(g.onClick) g.onClick(index,g.value(),node);
				console.log('item click');
				doHideSelectMenu();
			});	
		}
		function doShowSelectionMenu(e){
			e.stopPropagation();
			if(e.button==2){
				e.returnValue=false; 
				return false	
			}
			var s=document.getSelection();
			mySelection.selection=s;
			mySelection.text=s.toString();
			if(tm) clearTimeout(tm);
			tm=setTimeout(function(){
				if(!s.isCollapsed){
					if(box) {
						document.body.appendChild(box);
					}
					box.style.left=e.clientX+'px';
					box.style.top=e.clientY+'px';
					box.style.display='block';
				}
			},0);
		}
		function doHideSelectMenu(e){
			box.style.display='none';
		}
	}
	function intiStyle(){
		var cs='.selectMenuBar{background-color:#fff; box-shadow:0 0 8px #ccc; position:absolute; left:50px; top:50px; z-index:9999; user-select:none; -webkit-user-select:none;}\
	.selectMenuBar .keyword{white-space:nowrap; overflow:hidden; text-overflow:ellipsis; color:#a9a9a9; padding:0 10px; line-height:30px;}\
	.selectMenuBar p{line-height:30px; border-top:1px solid #efefef; padding:0 10px; margin:0; cursor:default;}\
	.selectMenuBar p:hover{background-color:#f5f5f5;}';
		var s=document.getElementById('selectionstyle');
		if(!s){
			s=document.createElement('style');
			s.id='selectionstyle';
			s.type='text/css';
			s.innerHTML=cs;
			document.getElementsByTagName('head').item(0).appendChild(s);
		}
	}
	window.mySelection={
		getInstance:function(id,data){
			return new Selection(id,data);
		},
		text:'',
		selection:null
	};
})()