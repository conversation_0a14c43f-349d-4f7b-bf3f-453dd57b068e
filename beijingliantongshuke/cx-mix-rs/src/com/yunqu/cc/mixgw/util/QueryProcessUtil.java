package com.yunqu.cc.mixgw.util;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.JsonKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.QueryFactory;
/**
 * By RunX
 * <AUTHOR>
 *
 */

public class QueryProcessUtil {
	
	public static List<EasyRow> getDbField(UserModel user,String proId) throws SQLException{
		EasyQuery query = QueryFactory.getQuery(user.getEpCode());
		EasySQL sql = new EasySQL("select DB_NAME,DB_TABLE_NAME from "+user.getSchemaName()+".C_BO_AUTO_FORM_DB");
		sql.append(" where 1=1");
		sql.append(proId," and PROCESS_ID=?");
		sql.append(user.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(user.getEpCode()," and ENT_ID=?");
		sql.append("Y"," and IS_BUSI_DB=? ");
		sql.append("order by db_name desc");
		List<EasyRow> record = query.queryForList(sql.getSQL(), sql.getParams());
		return record;
	}
	
	public static List<JSONObject> getFieldList(UserModel user,String dbName) throws SQLException{
		EasyQuery query = QueryFactory.getQuery(user.getEpCode());
		EasySQL sql = new EasySQL("SELECT FIELD_NAME,FIELD_TEXT,SEARCH_EXTCONFIG FROM ");
		sql.append(user.getSchemaName()+".C_BO_AUTO_FORM_DB_FIELD"+" WHERE 1=1");
		sql.append(dbName," AND AUTO_FORM_DB_ID = ?");
		sql.append(user.getBusiOrderId()," and BUSI_ORDER_ID=?");
		sql.append(user.getEpCode()," and ENT_ID=?");
		sql.append("Y"," AND IS_SHOW_FIELD = ?");
		sql.append("ORDER BY SORT");
		return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
	}
	
	public static List<JSONObject> extendQuery(JSONObject param,EasySQL sql,Map<String,String> relation){
		if(relation == null){
			return null;
		}
		for (Entry<String, String> map : relation.entrySet()) {
			String alias = map.getValue();
			JSONObject json = JsonKit.getJSONObject(param, map.getKey());
			JSONObject normal = JsonKit.getJSONObject(json, "extSearch.normal");
			JSONObject like = JsonKit.getJSONObject(json, "extSearch.like");
			Iterator<String> normalKeys = normal.keySet().iterator();
			Iterator<String> likeKeys = like.keySet().iterator();
			//拼接
			while (normalKeys.hasNext()) {
			    String teams = normalKeys.next();
			    String teamsInfo = normal.getString(teams);
			    if(StringUtils.endsWith(teams, "StartTime")){
			    	sql.append(teamsInfo," and "+alias+"."+teams.substring(0,teams.length()-9)+" >= ? "); 
			    }else if( StringUtils.endsWith(teams, "EndTime")){
			    	sql.append(teamsInfo," and "+alias+"."+teams.substring(0,teams.length()-7)+" <= ? "); 
			    }else{
			    	sql.append(teamsInfo," and "+alias+"."+teams+" = ? "); 	
			    }
			}
			//拼接
			while (likeKeys.hasNext()) {
			    String teams = likeKeys.next();
			    String teamsInfo = like.getString(teams);
			    if(StringUtils.endsWith(teams, "RightLike")){
			    	sql.appendRLike(teamsInfo," and "+alias+"."+teams.substring(0,teams.length()-9)+" like ? "); 
			    }else if( StringUtils.endsWith(teams, "LeftLike")){
			    	sql.appendLLike(teamsInfo," and "+alias+"."+teams.substring(0,teams.length()-8)+" like ? "); 
			    }else{
			    	//AllLike
			    	sql.appendLike(teamsInfo," and "+alias+"."+teams.substring(0,teams.length()-7)+" like ? "); 
			    }
			}
		}
		
		return null;
	}
	
	/**
	 * 添加表头
	 * @param processId
	 * @param headers
	 * @param user
	 */
	public static void addExcelHeader(String processId,List<String> headers,UserModel user){
		if(StringUtils.isNotBlank(processId)){
			try{
			//有流程分类
			List<EasyRow> records = QueryProcessUtil.getDbField(user, processId);
			
			if(records != null){
				for (EasyRow record : records) {
					String dbName = record.getColumnValue("DB_NAME");
//					dbTableName = record.getColumnValue("DB_TABLE_NAME");
					//查询要显示的字段
					List<JSONObject> queryForList = QueryProcessUtil.getFieldList(user, dbName);
					//遍历列表
					for(JSONObject json : queryForList){
						headers.add(json.getString("FIELD_TEXT"));
					}
				}
				
			}
			}catch(SQLException e){
				CommonLogger.logger.error(e.getMessage(),e);
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 添加表头和列名
	 * @param processId
	 * @param headers
	 * @param user
	 * @param columnNames
	 */
	public static void addExcelHeader(String processId,List<String> headers,UserModel user,List<String> columnNames){
		if(StringUtils.isNotBlank(processId)){
			try{
			//有流程分类
			List<EasyRow> records = QueryProcessUtil.getDbField(user, processId);
			
			if(records != null){
				for (EasyRow record : records) {
					String dbName = record.getColumnValue("DB_NAME");
//					dbTableName = record.getColumnValue("DB_TABLE_NAME");
					//查询要显示的字段
					List<JSONObject> queryForList = QueryProcessUtil.getFieldList(user, dbName);
					//遍历列表
					for(JSONObject json : queryForList){
						headers.add(json.getString("FIELD_TEXT"));
						columnNames.add(json.getString("FIELD_NAME"));
					}
				}
				
			}
			}catch(SQLException e){
				CommonLogger.logger.error(e.getMessage(),e);
				e.printStackTrace();
			}
		}
	}
	
}
