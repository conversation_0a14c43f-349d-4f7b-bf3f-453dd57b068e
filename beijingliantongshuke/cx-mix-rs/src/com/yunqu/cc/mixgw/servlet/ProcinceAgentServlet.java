package com.yunqu.cc.mixgw.servlet;


import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;

/**
 * 关于坐席省份绑定关系
 */
@WebServlet("/servlet/procinceAgent")
public class ProcinceAgentServlet extends AppBaseServlet {
	protected Logger logger =CommonLogger.logger;
	private static final long serialVersionUID = 1L;

	/**
	 * 添加坐席省份绑定关系
	 * 
	 * 
	 */
	public EasyResult actionForAdd() {
		JSONObject data = this.getJSONObject();
		logger.info("添加坐席省份绑定关系数据："+data.toJSONString());
		try {

			EasyRecord record = new EasyRecord(getTableName("cx_user_province_bind"),"ID");
			record.setColumns(data);
			record.set("ID", RandomKit.randomStr());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			boolean save = this.getQuery().save(record);
				logger.info("添加坐席省份绑定关系结果："+save);
				if(save) {
					return EasyResult.ok();
				}else {
					return EasyResult.fail("添加坐席省份绑定关系失败");
				}
			
		} catch (Exception e) {
			logger.error("添加坐席省份绑定关系出错：",e);
			e.printStackTrace();
		}
		return EasyResult.fail("添加坐席省份绑定关系失败");
	}
	
	/**
	 * 修改坐席省份绑定关系
	 */
	public EasyResult actionForEdit() {
		JSONObject data = this.getJSONObject();
		logger.info("修改坐席省份绑定关系数据："+data.toJSONString());
		String id = data.getString("ID");
		try {
			if(StringUtils.notBlank(id)) {
				EasyRecord record = new EasyRecord(getTableName("cx_user_province_bind"),"ID");
				record.set("ID",id);
				record.set("BIND_STATE", data.getString("BIND_STATE"));
				record.set("IS_ADMIN", data.getString("IS_ADMIN"));
				boolean update = this.getQuery().update(record);
				logger.info("修改坐席省份绑定关系结果："+update);
				if(update) {
					return EasyResult.ok();
				}else {
					return EasyResult.fail("修改坐席省份绑定关系失败");
				}
			}else {
				return EasyResult.fail("参数错误");
				
			}
		} catch (Exception e) {
			logger.error("修改坐席省份绑定关系出错 ：",e);
			e.printStackTrace();
		}
		return EasyResult.fail("修改坐席省份绑定关系失败");
	}
	
	/**
	 * 删除坐席省份绑定关系
	 */
	public EasyResult actionForDel() {
		JSONObject data = this.getJSONObject();
		String id = data.getString("id");
		try {
			if(StringUtils.notBlank(id)) {
			EasyRecord record = new EasyRecord(getTableName("cx_user_province_bind"),"ID");
			record.set("ID",id);
				boolean del = this.getQuery().deleteById(record);
				logger.info("删除结果："+del);
				if(!del) {
					return EasyResult.ok();
				}else {
					return EasyResult.error();
				}
			}else {
				return EasyResult.fail("参数错误");
			}
		} catch (Exception e) {
			logger.error("删除出错 ：",e);
			e.printStackTrace();
		}
		return EasyResult.fail("删除失败");
	}

	/**
	 * 添加人寿云助理人员和省份绑定关系
	 */
	public EasyResult actionForAddAidProvince() {
		JSONObject data = this.getJSONObject();
		logger.info("添加坐席省份关系："+data.toJSONString()+"操作人："+getUserName());
		try {
			String aid = data.getString("aid");
			String aName = data.getString("aName");
			String province = data.getString("province");
			if(StringUtils.isAnyBlank(aid,province)){
				return EasyResult.fail("未获取到必填参数，请刷新后重试！");
			}
			String id = this.getQuery().queryForString("select ID from "
					+getTableName("cx_mix_rs_province_aid")+" where aid=? and province=?",new Object[]{aid,province});
			if(StringUtils.isNotBlank(id)){
				return EasyResult.fail("当前人员已经绑定了此省份，请换一个！");
			}
			EasyRecord record = new EasyRecord(getTableName("cx_mix_rs_province_aid"),"ID");
			record.set("ID", RandomKit.randomStr());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("CREATE_NAME", getUserPrincipal().getLoginAcct());
			record.set("AID", aid);
			record.set("A_NAME", aName);
			record.set("PROVINCE", province);
			boolean save = this.getQuery().save(record);
			logger.info("添加人寿云助理人员和省份绑定关系结果："+save);
			if(save) {
				return EasyResult.ok();
			}else {
				return EasyResult.fail("添加人寿云助理人员和省份绑定关系失败");
			}

		} catch (Exception e) {
			logger.error("添加人寿云助理人员和省份绑定关系出错：",e);
			e.printStackTrace();
		}
		return EasyResult.fail("添加人寿云助理人员和省份绑定关系失败");
	}

	/**
	 * 删除人寿云助理人员和省份绑定关系
	 */
	public EasyResult actionForDelAidProvince() {
		JSONObject data = this.getJSONObject();
		logger.info("删除坐席省份关系："+data.toJSONString()+"操作人："+getUserName());
		try {
			String id = data.getString("id");
			if(StringUtils.isAnyBlank(id)){
				return EasyResult.fail("未获取到必填参数，请刷新后重试！");
			}
			EasyRecord record = new EasyRecord(getTableName("cx_mix_rs_province_aid"),"ID");
			record.set("ID", id);
			boolean save = this.getQuery().deleteById(record);
			logger.info("删除人寿云助理人员和省份绑定关系结果："+save);
			if(save) {
				return EasyResult.ok();
			}else {
				return EasyResult.fail("删除人寿云助理人员和省份绑定关系失败");
			}
		} catch (Exception e) {
			logger.error("删除人寿云助理人员和省份绑定关系出错：",e);
			e.printStackTrace();
		}
		return EasyResult.fail("删除人寿云助理人员和省份绑定关系失败");
	}

}
