package com.yunqu.cc.mixgw.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.inf.CreateYunGroupService;
import org.apache.log4j.Logger;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 通过职场人员创建云助理群聊
 */
@WebServlet("/servlet/createYunGroup")
public class CreateYunGroupServlet extends AppBaseServlet {
	protected Logger logger = CommonLogger.getLogger("yun-group");
	private static final long serialVersionUID = 1L;

	// 创建线程池用于异步处理
	private static final ExecutorService executor = Executors.newFixedThreadPool(5);

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		req.setCharacterEncoding("UTf-8");//设置缓冲区的编码

		try {
			final JSONObject json = getRequestJson(req);
			logger.info("reqStr:" + json);

			// 立即返回成功响应
			JSONObject response = new JSONObject();
			response.put("code", "0");
			response.put("message", "请求已接收，正在异步处理");
			resp.setContentType("application/json;charset=UTF-8");
			resp.getWriter().write(response.toJSONString());

			// 异步处理逻辑
			CompletableFuture.runAsync(() -> {
				try {
					processYunGroupCreation(json);
				} catch (Exception e) {
					logger.error("异步处理创建云助理群聊失败: " + e.getMessage(), e);
				}
			}, executor);

		} catch (Exception e) {
			logger.error("处理请求失败: " + e.getMessage(), e);
			JSONObject errorResponse = new JSONObject();
			errorResponse.put("code", "1");
			errorResponse.put("message", "请求处理失败");
			resp.setContentType("application/json;charset=UTF-8");
			resp.getWriter().write(errorResponse.toJSONString());
		}
	}

	/**
	 * 异步处理创建云助理群聊的逻辑
	 * @param json 请求参数
	 */
	private void processYunGroupCreation(JSONObject json) {
		try {
			String orderId = json.getString("orderId");//工单id

			String type = json.getString("type");
			String key = "NEXT_NODE_ACC1";
			String cllx2 = json.getString("cllx2");//报障类型
			if("线上流转".equals(cllx2) || "流转".equals(cllx2)){
				// 原有逻辑
			}
			if("1".equals(type)){
				key = "NEXT_NODE_ACC1";
			}else {
				key = "NEXT_NODE_ACC2";
			}

			String userIds = QueryFactory.getReadQuery().queryForString("select "+key+" from "+getTableName("c_box_mix_order")+" t1 inner join "
					+getTableName("c_bo_base_order")+" t2 on t1.M_ID=t2.ID where t2.ORDER_NO=? ",new Object[]{orderId});//处理人id

			if(StringUtils.isBlank(userIds)){
				logger.info("未获取到人员id");
				return;
			}

			String province = json.getString("province");//报障人省份名称
			String zcname = json.getString("zcname");//职场名称
			String zcno = json.getString("zcno");//职场代码
			String ip = json.getString("ip");//报障人ip
			String uid = StringUtils.isBlank(json.getString("uid"))?json.getString("uId"):json.getString("uid");//报障人id
			String applyName = json.getString("custName");//报障人姓名
			String applyPhone = json.getString("caller");//报障人电话号码
			String applyContext = json.getString("applyContext");//故障现象
			String cljl = json.getString("cljl");//处理结论

			String[] ids = userIds.split(",");
			JSONObject param = null;

			for (String userId : ids){
				String outTid = QueryFactory.getReadQuery().queryForString("select ORDER_CREATE_GROUP_ID from "+getTableName("c_box_mix_order")+" t1 inner join "
						+getTableName("c_bo_base_order")+" t2 on t1.M_ID=t2.ID where t2.ORDER_NO=? ",new Object[]{orderId});

				logger.info("该工单群聊id："+outTid);
				if(StringUtils.isNotBlank(outTid)){
					return;
				}

				param = new JSONObject();
				param.put("orderId", orderId);
				userId = QueryFactory.getReadQuery().queryForString("select USER_ID from cc_user where USER_ACCT=? ",new Object[]{userId});
				param.put("userId", userId);
				param.put("province", province);
				param.put("zcname", zcname);
				param.put("zcno", zcno);
				param.put("ip", ip);
				param.put("uid", uid);
				param.put("applyName", applyName);
				param.put("applyPhone", applyPhone);
				param.put("applyContext", applyContext);
				param.put("cljl", cljl);

				CreateYunGroupService service = new CreateYunGroupService();
				service.invoke(param);
			}
		} catch (Exception e) {
			logger.error("异步处理创建云助理群聊异常: " + e.getMessage(), e);
		}
	}

	/**
	 * 获取业务库
	 * @param tableName
	 * @return
	 */
	public String getTableName(String tableName){
		return Constants.ORDER_DB_NAME+"."+tableName;
	}

	@Override
	public void destroy() {
		super.destroy();
		// 关闭线程池
		if (executor != null && !executor.isShutdown()) {
			executor.shutdown();
		}
	}
}