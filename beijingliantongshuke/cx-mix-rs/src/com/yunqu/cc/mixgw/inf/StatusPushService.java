package com.yunqu.cc.mixgw.inf;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.util.CreateToken;

public class StatusPushService extends IService {

	private Logger logger = CommonLogger.getLogger();

	protected String getTableName(String tableName){
		String dbName=Constants.ORDER_DB_NAME;
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		JSONObject sendPushJson = new JSONObject();
		JSONObject dictByQuery = new JSONObject();
		logger.error("进入回传状态接口。"+json.toJSONString());
		
		String DISPOSE_TYPE = json.getString("DISPOSE_TYPE");
		String status =json.getString("status");
		String orderNo =json.getString("ORDER_NO");
//		if(!orderNo.contains("D")){
//			dictByQuery.put("respCode", "999");
//			dictByQuery.put("respDesc", "此工单不是通过云助理建单的！");
//			logger.info("此工单不是通过云助理建单的！");
//			return dictByQuery;
//		}
		
		String type =json.getString("type");
		String access_token = CacheUtil.get("ACCESS_TOKEN");
		logger.info("传入参数orderNo:"+orderNo+"_DISPOSE_TYPE:"+DISPOSE_TYPE+"_status:"+status+"_type:"+type+"_access_token:"+access_token);
		if(orderNo==null || orderNo==null || status==null || status==null || DISPOSE_TYPE==null || DISPOSE_TYPE==null){
			dictByQuery.put("respCode", "999");
			dictByQuery.put("respDesc", "未传入工单编号或状态！");
			logger.error("未传入工单编号或状态");
			return dictByQuery;
		}
		if(DISPOSE_TYPE.equals("误报")||DISPOSE_TYPE.equals("结单")){
			status = "2";
		}
		

		sendPushJson.put("applyno", orderNo);
		sendPushJson.put("status", status);
		sendPushJson.put("type", type);
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
			try {
				long longValue = System.currentTimeMillis();
				int intValue = (int) (longValue / 1000);
				String id = query.queryForString("select a.ID from "+getTableName("`C_BOX_MIX_ORDER`")+" a left join "
				+getTableName("c_bo_base_order")+" b on a.M_ID=b.ID where b.ORDER_NO=?", new Object[] {orderNo});
				if(StringUtils.isNotBlank(id)) {
					EasyRecord record = new EasyRecord(getTableName("`C_BOX_MIX_ORDER`"),"ID");
					record.put("ID", id);
					record.put("APPLY_ID", intValue);
					query.update(record);
				}
			}catch (Exception e) {
				// TODO: handle exception
			}
			
			EasySQL sql = new EasySQL("SELECT b.ID id,a.APPLY_ID applyid,b.CREATE_TIME cdate,ifnull(a.NEXT_NODE_TIME2,a.NEXT_NODE_TIME1) handletime,"
					+ "DISPOSE_CONCLUSION context,APPLY_CAUSE reason"
					+ " FROM "+getTableName("`C_BOX_MIX_ORDER`")+" a left join "
					+getTableName("c_bo_base_order")+" b on a.M_ID=b.ID ");
			sql.append(orderNo,"where b.ORDER_NO=?");
		
			List<JSONObject> jsonRows = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			logger.info("查询出的值："+jsonRows!=null && jsonRows.size()>0?jsonRows.get(0).toString():"");
			List<JSONObject> jsons = new ArrayList<JSONObject>();
			for (JSONObject jsonObject : jsonRows) {
				JSONObject jsonRow = new JSONObject();
				long nowDate = new Date().getTime();
				jsonRow.put("id", Math.abs((int)nowDate));
				jsonRow.put("applyId", jsonObject.getString("APPLYID"));
				jsonRow.put("cdate", jsonObject.getString("CDATE"));
				jsonRow.put("handletime", jsonObject.getString("HANDLETIME")!=null
						&&jsonObject.getString("HANDLETIME").length()>0?jsonObject.getString("HANDLETIME").replace("min", ""):"0");
				jsonRow.put("context", jsonObject.getString("CONTEXT"));
				jsonRow.put("reason", jsonObject.getString("REASON"));
				jsons.add(jsonRow);
				
			}
			sendPushJson.put("rows", jsons);
			if(access_token==null || access_token.length()==0){
				access_token = CreateToken.getToken();
			}
			logger.info("access_token："+access_token);
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Authorization","Bearer "+access_token);
			headers.put("Content-Type","application/json");
			logger.info("sendPushJson："+sendPushJson.toJSONString());
			HttpResp resp =HttpUtil.sendPost(Constants.AID_CLOUD_DOMAIN_NAME+"/api/chinalife-shenzhen/wifiissue/progress",
					headers, sendPushJson.toJSONString(),HttpUtil.TYPE_JSON,"UTF-8");
			if(resp!=null && resp.getResult()!=null){
				JSONObject respJson = JSONObject.parseObject(resp.getResult());
				if(respJson!=null && respJson.getString("code")!=null){
					if(!"0".equals(respJson.getString("code"))){
						if(respJson.getString("msg")!=null && respJson.getString("msg").contains("请在Request Header中正确传递access_token")){
							access_token = CreateToken.getToken();
							headers = new HashMap<String, String>();
							headers.put("Authorization","Bearer "+access_token);
							headers.put("Content-Type","application/json");
							resp =HttpUtil.sendPost(Constants.AID_CLOUD_DOMAIN_NAME+"/api/chinalife-shenzhen/wifiissue/progress",
								headers, sendPushJson.toJSONString(),HttpUtil.TYPE_JSON,"UTF-8");
						}
					}
				}
			}
			logger.info("发送完成，返回信息："+resp.getResult());
			dictByQuery.put("respDesc", resp.getResult());
		} catch (Exception e) {
			logger.error("发送错误，错误信息:"+e.getMessage());
			dictByQuery.put("respDesc", "发送错误，错误信息:"+e.getMessage());
			dictByQuery.put("respCode", "999");
			e.printStackTrace();
			return dictByQuery;
		}
		//dictByQuery.put("respDesc", "0");
		dictByQuery.put("respCode", "0");
		return dictByQuery;
	}

}
