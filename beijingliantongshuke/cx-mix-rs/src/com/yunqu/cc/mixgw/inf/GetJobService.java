package com.yunqu.cc.mixgw.inf;


import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;

public class GetJobService extends IService {

	private Logger logger = CommonLogger.getLogger();
	
	protected String getTableName(String tableName){
		String dbName=Constants.ORDER_DB_NAME;
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		JSONObject dictByQuery = new JSONObject();
		if (json == null){
			dictByQuery.put("respCode", "500");
			dictByQuery.put("respDesc", "操作失败,请添加数据");
			return dictByQuery;
		}
		String orderNo = json.getString("orderNo");
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		EasySQL sql = new EasySQL("SELECT JOB_ID jobId,JOB_NAME jobName,JOB_IP ip FROM "+getTableName("C_BOX_MIX_ORDER")+" a left join "+getTableName("c_bo_base_order")+" b on a.M_ID=b.id where 1=1");
		sql.append(orderNo," and b.ORDER_NO = ?");
		try {
			List<Map<String, String>> row = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
			logger.info("orderId:"+json.getString("orderId")+";taskName:"+json.getString("taskName")+";sql:"+sql.getSQL()+";参数："+sql.getParams().toString());
			
			dictByQuery.put("data", row);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error("出现错误"+e.getMessage());
			e.printStackTrace();
		}
		dictByQuery.put("respCode", "000");
		dictByQuery.put("respDesc", "操作成功");
		return dictByQuery;
	}

}
