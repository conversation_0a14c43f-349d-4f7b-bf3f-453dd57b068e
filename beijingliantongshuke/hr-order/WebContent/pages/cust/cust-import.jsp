<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title >文件上传</title>
	<style>
		.ibox-title .input-group{ margin-bottom: 10px;	width: 49%;	display:table}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 <form action="" method="post" name="uploadForm" class="form-inline" id="uploadForm" enctype="multipart/form-data">
      <table class="table table-vzebra" >
                <tbody>
                <tr>
                     <td width="60px" >导入文件</td>
                     <td><input type="file" class="hidden" name="file" id="file" 
                                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/> 
                     <div class="input-group input-group-sm">
                     <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()" >选择文件</button> <a
						class="btn btn-sm btn-link" href="/hr-order/pages/cust/custTemplate.xlsx"  target="_blank">下载导入模板</a> 
                      </div>                 		                        	
                     </td>
                   </tr>
                   <tr>
					<td >文件名</td>
					<td><input id="filePath" class="form-control input-sm" style="width:200px" type="text" readonly="readonly"></td>
					</tr>
                 </tbody>
         </table>
	   <div class="layer-foot text-c">
			<input class="btn btn-sm btn-primary" type="button" onclick="Upload.ajaxSubmitForm()" value="上传"/>
			<input class="btn btn-sm btn-default" type="button" id="backbut" onclick="parent.layer.closeAll();" value="关闭"/>
		</div>
         	  
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Upload");		
		 $(document).ready(function() {
	        $(".hidden").change(function(){
	            var arrs=$(this).val().split('\\');
	            var filename=arrs[arrs.length-1];
	            $("#filePath").val(filename);
	        });
	    });
		 
		Upload.ajaxSubmitForm = function(){
			var filename = document.getElementById('file').value;
			var postfix = filename.substring(filename.lastIndexOf(".")+1,filename.length);
		    if(filename == "") {
		    	layer.msg("请选择导入文件！",{icon: 5,time:1000,offset:'30px'});
		    	return ;
		    }
		    if(filename!=""&&postfix!='xls'&&postfix!='xlsx'){
		    	layer.msg("导入文件格式不正确！",{icon: 7,time:2000,offset:'30px'});
		    	return;
		    }
			var formData = new FormData($("#uploadForm")[0]);
 			$.ajax({  
		          url: '${ctxPath}/servlet/cust?action=upload', 
		          type: 'POST',  
		          data: formData,async: false,cache: false,contentType: false,processData: false,  
		          success: function (result) {
		        	  if(result.state==1){
		        		  layer.msg(result.msg,{icon: 1,time:2000,offset:'30px'},function(){
								popup.layerClose("#uploadForm");
								parent.custListFun.query();
							 });
		        	  }else{
		        			parent.layer.alert(result.msg,{icon: 5,offset:"20px"});
		        	  }
		          }  
		     });		
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>