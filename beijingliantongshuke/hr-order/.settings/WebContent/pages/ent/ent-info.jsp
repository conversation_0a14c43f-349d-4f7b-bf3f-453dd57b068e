<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>${param.entName}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="renderer" content="webkit"> 	
    <link rel="stylesheet" type="text/css" href="/yc-center/css/panel.css">
    <style type="text/css">
		.tdtext2{
			color: #555;
		}
		.panel{min-height: 338px;}
		.spanleft{padding-left: 0.5em;}
		select{
		    border: none;
		    appearance:none;
            -moz-appearance:none;
            -webkit-appearance:none;
		}
		select.input-sm{line-height: 15px;padding: 2px 10px!important;}
		.edit-link{
			margin-left:20px;
			display:none;
		}
		.list-group .col-md-6:first-child:hover .edit-link{
		    display:inline;
		}
		.edit-float{
		    float: right;
		    display: none;
		}
		.list-group-item:hover .edit-float{
		    display:inline;
		}
		.td-overflow{
			white-space: nowrap !important;
		    text-overflow: ellipsis !important;
		    overflow-x: hidden;
		    max-width: 300px;
		}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
		<div class="row index-page">
			<div class="col-md-12">
		    <form action="" method="post" id="entInfo" autocomplete="off" data-mars="ent.getEntRes" data-mars-prefix="entRes." class="form-inline">
		        <input type="hidden" value="${param.entId}" name="entId">
		        <input type="hidden" value="${param.entId}" name="pk">
				<select id=userState class="hidden">
				    <option value="0">正常</option>
				    <option value="1" data-class="label label-warning">暂停</option>
				</select>	
				<select id=lockState class="hidden">
				    <option value="0"></option>
				    <option value="1" data-class="label label-danger">已锁定</option>
				</select>	
				<select id="prefixState" class="hidden">
				    <option value="0">正常</option>
				    <option value="1" data-class="label label-warning">暂停</option>
				</select>
				<select id="labelName" class="hidden" data-mars="label.labelDict">
				</select>
				<ul class="list-group">
				    <li class="list-group-item"  data-mars="ent.getEnt" data-mars-prefix="ent." data-text-model="true">
				    	
				    	<div class="row">
				    		<div class="col-md-6">
					    		<h4><span class="glyphicon glyphicon-home" aria-hidden="true" ></span> 
						    	    <span name="ent.ENT_NAME"></span> 
						    	    <small class="text-success ml-10">
						    	        <span class="glyphicon glyphicon-tag"></span>
						    	        <select name="ent.AUTHEN_FLAG" data-text="true">
							    	        <option value="0">未认证</option>
							    	        <option value="1">已认证</option>
						    	        </select>
						    	    </small>
						    	    <small class="edit-link"><a href="javascript:void(0)" onclick="Ent.editInfo()">修改</a></small>
						    	</h4>
						    	<div class="tag-group">
						    		<span class="btn btn-default  btn-link btn-xs" name="ent.AREA_CODE"></span>
						    		<span class="btn btn-default  btn-link btn-xs">
						    		    <select name="ent.INDUSTRY" data-mars="common.industryDict" data-text="true">
		                                    <option value="">请选择</option>
					                     </select>
					                 </span>
					                 <span class="btn btn-default  btn-link btn-xs">
					                     <select name="ent.ENT_SCALE" data-text="true" data-mars="common.entScaleDict">
				                    		<option value="">请选择</option>
		              				     </select>
		              				 </span >
					                 <span id="entState">
		              				 </span >
						    	</div>
				    			<table class="table table-no-border" style="margin-bottom:0">
					    			<tr>
					    				<td><span class="spanleft">企业ID：</span><span class="tdtext2" name="ent.ENT_ID"></span></td>
					    				<td>企业代码：<span class="tdtext2" name="ent.ENT_CODE"></span></td>
					    			</tr>
					    			<tr>
					    				<td><span class="spanleft" class="spanleft">开户时间：</span><span class="tdtext2" name="ent.OPEN_TIME"></span></td>
					    				<td>管理员账号：<span class="tdtext2" data-mars="ent.getuserAcct"></span></td>
					    			</tr>
					    			<tr>
					    				<td><span class="spanleft">联系人：</span><span class="tdtext2" name="ent.LINKMAN"></span></td>
					    				<td>联系电话：<span class="tdtext2" name="ent.LINKPHONE"></span></td>
					    			</tr>
					    			<tr>
					    				<td><span class="spanleft">联系邮箱：</span><span class="tdtext2" name="ent.EMAIL"></span></td>
					    				<td>计费编码：<span class="tdtext2" name="ent.FEE_CODE"></span></td>
					    			</tr>
					    			<tr>					    				
					    			    <td><span class="spanleft">大数据企业ID：</span><span class="tdtext2" name="ent.BD_ENT_ID"></span></td>
					    			    <td>大数据企业密匙：<span class="tdtext2" name="ent.BD_ENT_KEY"></span></td>
					    			</tr>
					    			<tr>
					    				<td colspan="3"><span class="spanleft">企业地址：</span><span class="tdtext2" name="ent.ADDR"></span></td>
					    			</tr>
					    		</table>
				    		</div>

				    		<div class="col-md-6 mt-10">
				    			<div class="pull-right mb-20"><button type="button" onclick="Ent.syncEntInfo()" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-refresh"></i> 同步企业数据</button></div>
								<table class="table table-no-border" data-container="#bussList-Data" data-template="bussList-template" data-mars="bussOrder.list" style="margin-bottom:0">
					    			<tr>
					    				<td><b>订购业务</b></td>
					    			</tr>
					    			<tr>
					    				<td>
											<div class="buss-tags pull-left" id="bussList-Data">
												
											</div>
											<script id="bussList-template" type="text/x-jsrender">
                                            {{for list}}
                                                <a href="javascript:void(0);" onclick="Ent.editBusiOrder('{{:BUSI_ORDER_ID}}')">
                                                    
												    <div class="buss-tag {{if ORDER_STATE == 2}}tag-gray{{else ORDER_STATE == 3}}tag-red{{/if}}">
													    <span class="b-name1">{{:BUSI_NAME}}</span>
													    <span class="b-name2">{{:ORDER_DATE}}</span>
												    </div>
                                                </a>
                                            {{/for}}
                                            </script>

					    					<div class="input-group input-group-sm pull-left" style="padding-top: 7px">
											<button type="button" class="btn btn-sm btn-success " onclick="Ent.addBusiOrder()">+ 新增订购</button>
									    </div>
					    				</td>
					    			</tr>
					    		</table>
				    		</div>
				    	</div>
				    </li>
				    <li class="list-group-item">
						<div>企业资源配置<a class="edit-float" href="javascript:void(0);" onclick="Ent.editRes()">修改</a></div>
						<br>
						<div class="row">
							<div class="col-md-8" >
				    			<table class="table table-no-border" style="margin-bottom:0">
					    			<tr>
					    				<td><span class="spanleft" >Petra节点：</span><span class="tdtext2" name="entRes.PETRA_NAME"></span></td>
					    				<td>数据库节点：<span class="tdtext2" name="entRes.SCHEMA_NAME"></span></td>
					    				<td>MARS节点：<span class="tdtext2" name="entRes.MARS_NAME"></span></td>
					    				<td>坐席工号数：<span class="tdtext2" name="entRes.USER_LICENSE"></span></td>
					    			</tr>
					    			<tr>
					    			    <td><span class="spanleft" >中继资源数：</span><span class="tdtext2" name="entRes.CALL_LICENSE"></span></td>
					    			    <td>智能外呼中继资源数：<span class="tdtext2" name="entRes.AUTO_CALL_LICENSE"></span></td>
					    				<td>话机数：<span class="tdtext2" name="entRes.AGENT_LICENSE"></span></td>
					    				<td>话机前缀：<span class="tdtext2" name="entRes.PHONE_PREFIX"></span></td>
					    			</tr>
					    		</table>
				    		</div>
						</div>
				    </li>
				    <li class="list-group-item">
						<div>企业外呼配置<a class="edit-float" href="javascript:void(0);" onclick="Ent.editCallRes()">修改</a></div>
						<br>
						<div class="row">
							<div class="col-md-10" >
				    			<table class="table table-no-border" style="margin-bottom:0">
					    			<tr>
					    				<td><span class="spanleft" >单客户号码最大外呼次数/天：</span><span class="tdtext2" name="entRes.CALL_COUNT"></span></td>
					    				<td>单客户号码外呼最少时间间隔/秒：<span class="tdtext2" name="entRes.CALL_INTERVAL"></span></td>
					    				<td>企业最大外呼次数/天：<span class="tdtext2" name="entRes.TOTAL_CALL_COUNT"></span></td>
					    			</tr>
				    				<tr>
					    				<td><span class="spanleft" >企业数据保存时间/月：</span><span class="tdtext2" name="entRes.DATA_VALID_TIME"></span></td>
				    					<td>BPO人工外呼号码前缀：<span class="tdtext2" name="entRes.BPO_CALL_PREFIX"></span></td>
				    					<td>BPO自动外呼号码前缀：<span class="tdtext2" name="entRes.BPO_AGENT_PHONE"></span></td>
				    				</tr>
					    		</table>
				    		</div>
						</div>
				    </li>
				    <!-- 企业计费配置 -->
				    <li class="list-group-item" data-mars="ent.getFeeConf" data-mars-prefix="feeConf." data-text-model="true">
						<div>企业计费配置<a class="edit-float" href="javascript:void(0);" onclick="Ent.editFeeConf()">修改</a></div>
						<br>
						<div class="row">
							<div class="col-md-11" >
				    			<table class="table table-no-border" style="margin-bottom:0">
					    			<tr>
					    				<td><span class="spanleft" >通道数：</span><span class="tdtext2" name="feeConf.CALL_LICENSE"></span></td>
					    				<td>标准计费：
					    				<span class="tdtext2">
					                     <select name="feeConf.FEE_UNIT" data-text="true" data-mars="common.entScaleDict">
					                        <option value=""></option>
				                    		<option value="1">按6秒计费</option>
				                    		<option value="2">按分钟计费</option>
		              				     </select>
		              				 	</span >
					    				</td>
					    				<td>每通道套餐内通话时长：<span class="tdtext2" name="feeConf.BASE_TIMES"></span>秒</td>
					    				<td>基础套餐费用：<span class="tdtext2" name="feeConf.BASE_FEE"></span>元</td>
					    				<td>超通话套餐<span class="tdtext2" name="feeConf.PER"></span>费用：<span class="tdtext2" name="feeConf.OVER_FEE"></span>元</td>
					    			</tr>
					    		</table>
				    		</div>
						</div>
				    </li>
				</ul>
            </form>
			</div>
		</div>
    
    <div class="row">
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entPhonePage()">详情</a></li>
    					</ul>
    					<div class="panel-title">话机列表</div>
    				</div>
    				<div class="panel-content">
                    <form action="" method="post" id="phoneForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="#phoneList-Data" data-template="phoneList-template" data-mars="phone.list">
					      <thead>
					        <tr>
					          <th>话机号码</th>
					          <th>话机类型</th>
					          <th>接入类型</th>
					        </tr>
					      </thead>
					      <tbody id="phoneList-Data" >
					      
					      </tbody>
					    </table>
                        <script id="phoneList-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:PHONE_NUM}}</td>
											<td>{{phoneState:PHONE_TYPE}}</td>  
											<td>{{joinType:JOIN_TYPE}}</td>                                   
									    </tr>
								    {{/for}}					         
				       </script>
                       <div class="row paginate" id="phone-page">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                       </div>
     				</form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entAgentPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">坐席工号列表</div>
    				</div>
    				<div class="panel-content">
    				
				    <form action="" method="post" id="agentForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline">
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="agenList-Data" data-template="agentList-template" data-mars="user.list">
					      <thead>
					        <tr>
					          <th>坐席工号</th>
					          <th>登录账号</th>
					          <th>用户状态</th>
					        </tr>
					      </thead>
					      <tbody id="agenList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="agentList-template">
                            {{for list}}
                                <tr>
                                    <td scope="row">{{:AGENT_PHONE}}</td>
                                    <td>{{:USER_ACCT}}</td>
                                    <td>{{getText:USER_STATE '#userState'}}&nbsp;{{getText:LOCK_STATE '#lockState'}}</td>
                                </tr>
                            {{/for}}
                        </script>
                        <div class="row paginate" id="agent-page">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
     				</form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    </div>
    
	<div class="row">
	
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entPrifixPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">外呼号码列表</div>
    				</div>
    				<div class="panel-content">
			        <form action="" method="post" id="prifixForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="#prefixList-Data" data-template="prefixList-template" data-mars="prefix.list">
					      <thead>
					        <tr>
					          <th>号码</th>
							  <th>状态</th>
					        </tr>
					      </thead>
					      <tbody id="prefixList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="prefixList-template">
                            {{for list}}
								<tr>
									<td>{{:PREFIX_NUM}}</td>
                                    <td>{{getText:PREFIX_STATE '#prefixState'}}</td>
								</tr>
							{{/for}}	
                        </script>
                        <div class="row paginate" id="prefix-page">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
    				</form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entPrefixRecordPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">外呼号码备案列表</div>
    				</div>
    				<div class="panel-content">
    				<form action="" method="post" id="prifixRecordForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="#prefixRecordList-Data" data-template="prefixRecordList-template" data-mars="prefixRecord.list">
					      <thead>
					        <tr>
					          <th>备案编号</th>
					          <th>备案号码</th>
					          <th>号码个数</th>
					        </tr>
					      </thead>
					      <tbody id="prefixRecordList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="prefixRecordList-template">
                            {{for list}}
								<tr>
                                    <td>{{:RECORD_NUM}}</td>
									<td class="td-overflow">{{:PREFIX_LIST}}</td>
                                    <td>{{prefixCout:PREFIX_LIST}}</td>
								</tr>
							{{/for}}	
                        </script>
                        <div class="row paginate">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
                    </form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		
    </div>

    <div class="row">
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entCallRangPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">呼叫范围列表</div>
    				</div>
    				<div class="panel-content">
    				<form action="" method="post" id="callRangForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="#callRangList-Data" data-template="callRangList-template" data-mars="callRang.callRangList">
					      <thead>
					        <tr>
					          <th>区号</th>
					          <th>地区名称</th>
					          <th>电信</th>
					          <th>移动</th>
					          <th>联通</th>
					          <th>固定电话</th>
					        </tr>
					      </thead>
					      <tbody id="callRangList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="callRangList-template">
                            {{for list}}
								<tr>
                                    <td>{{:AREA_CODE}}</td>
									<td>{{:AREA_NAME}}</td>
									<td>{{if DX_FLAG == 1}} 允许 {{/if}}</td>
									<td>{{if YD_FLAG == 1}} 允许 {{/if}}</td>
									<td>{{if LT_FLAG == 1}} 允许 {{/if}}</td>
									<td>{{if GH_FLAG == 1}} 允许{{/if}}</td>
								</tr>
							{{/for}}	
                        </script>
                        <div class="row paginate">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
                    </form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entVoxPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">语音文件列表</div>
    				</div>
    				<div class="panel-content">
    				<form action="" method="post" id="entVoxForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="#entVoxList-Data" data-template="entVoxList-template" data-mars="entVox.list" >
					      <thead>
					        <tr>
					          <th>文件名</th>
					          <th>文件路径</th>
					        </tr>
					      </thead>
					      <tbody id="entVoxList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="entVoxList-template">
                            {{for list}}
                                <tr>
                                    <td>{{:VOX_NAME}}</td>
                                    <td class="td-overflow">{{:VOX_PATH}}</td>
                                </tr>
                            {{/for}}
                        </script>
                        <div class="row paginate">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
    	            </form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		
    </div>
    
     <div class="row">
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.labelPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">大数据标签列表</div>
    				</div>
    				<div class="panel-content">
    				<form action="" method="post" id="labelForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="label-Data" data-template="labelList-template" data-mars="label.labelOrderList">
					      <thead>
    							<tr>
    								<th>标签名称</th>
    								<th>类别</th>
    								<th>描述</th>
    							</tr>
					      </thead>
					      <tbody id="label-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="labelList-template">
                            {{for list}}
                                <tr>
                                    <td>{{:ITEM_NAME}}</td>
                                    <td>{{getText:LABEL_ID '#labelName'}}</td>
                                    <td>{{:ITEM_DESC}}</td>
                                </tr>
                            {{/for}}
                        </script>
                        <div class="row paginate">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
                    </form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.entSbcPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">SBC资源列表</div>
    				</div>
    				<div class="panel-content">
    				
				    <form action="" method="post" id="sbcForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline">
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="sbcList-Data" data-template="sbcList-template" data-mars="entSbc.list">
					      <thead>
					        <tr>
					          <th>SBC名称</th>
					          <th>NGN或私网地址</th>
					          <th>SIP话机接入地址</th>
					        </tr>
					      </thead>
					      <tbody id="sbcList-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="sbcList-template">
                            {{for list}}
                                <tr>
                                    <td scope="row">{{:SBC_NAME}}</td>
									<td>{{if IP1}}{{:IP1}}:{{:PORT1}}{{/if}}</td>
									<td>{{if IP2}}{{:IP2}}:{{:PORT2}}{{/if}}</td>
                                </tr>
                            {{/for}}
                        </script>
                        <div class="row paginate" id="sbc-page">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
     				</form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    </div>
    
     <div class="row">
    		<!-- 块 -->
    		<div class="col-xs-12 col-md-6">
    			<div class="panel">
    				<div class="panel-header">
    					<ul class="panel-actions">
    						<li><a href="javascript:void(0);" onclick="Ent.smsPage()">详情</a></li>
    					</ul>
    					<div class="panel-title">营销短信模板列表</div>
    				</div>
    				<div class="panel-content">
    				<form action="" method="post" id="smsForm" data-ent-id="${param.entId}" autocomplete="off" class="form-inline" >
    					<table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" data-page-limit="5" data-container="sms-Data" data-template="smsList-template" data-mars="smsTemp.list">
					      <thead>
    							<tr>
    								<th>模板名称</th>
    								<th>短信签名</th>
    								<th>模板内容</th>
    							</tr>
					      </thead>
					      <tbody id="sms-Data">
					      </tbody>
					    </table>
					    <script type="text/x-jsrender" id="smsList-template">
                            {{for list}}
                                <tr>
                                    <td>{{:SMS_TEMP_NAME}}</td>
                                    <td>{{:SING_NAME}}</td>
                                    <td class="td-overflow">{{:CONTENT}}</td>
                                </tr>
                            {{/for}}
                        </script>
                        <div class="row paginate">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="5,10" name="pageSizes"/>
                            </jsp:include>
                        </div>
                    </form>
    				</div>
    			</div>
    		</div>
    		<!-- 块 -->
    		
    </div>
    
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/yc-center/js/matchHeight/jquery.matchHeight-min.js"></script>
	<script type="text/javascript">
	$.views.converters("joinType", function(val) {
		if(val==1){
			return "内网";
		}else if(val==2){
			return "公网";
		}
	});
	$.views.converters("phoneState", function(val) {
		if(val==1){
			return "<span>硬话机</span>";
		}else if(val==2){
			return "<span>SIP硬话机</span>";
		}else if(val==3){
			return "<span>SIP软话机</span>";
		}else if(val==4){
			return "<span>外线</span>";
		}
	});
	$.views.converters("prefixCout", function(val) {
		var arr = val.split('，');
		var arr2 = val.split(',');
		return arr.length + arr2.length - 1;
	});
	function entStateFn(entState){
		var html = "<span class='label label-success'>正常</span>";
		if(entState=="0"){
			html = "<span class='label label-success'>正常</span>";
		}else if(entState=="1"){
			html = "<span class='label label-warning'>暂停</span>";
		}else if(entState=="9"){
			html = "<span class='label label-danger'>销户</span>";
		}
		$("#entState").html(html);
	}
	$(function(){
		$("#entInfo").render({success:function(result){
			var entState = result['ent.getEnt'].data['ENT_STATE'];
			entStateFn(entState);
		}});  
		$("#phoneForm").render();  
		$("#agentForm").render();  
		$("#prifixGroupForm").render();  
		$("#prifixForm").render();
		$("#prifixRecordForm").render();
		$("#entVoxForm").render();
		$("#callRangForm").render();
		$("#labelForm").render();
		$("#sbcForm").render();
		$("#smsForm").render();
		$(".pageNumV").css("float","right");
		$(".pageNumV").css("margin-right","15px");
	});
	
	jQuery.namespace("Ent");
	
	Ent.entId = '${param.entId}';
	Ent.entCode = '${param.entCode}';
	
	Ent.searchEntInfo = function(){
		$("#entInfo").render({success:function(result){
			var entState = result['ent.getEnt'].data['ENT_STATE'];
			entStateFn(entState);
		}});
	}
	Ent.addBusiOrder = function() {
	    popup.layerShow({type:1,title:'新增订购',area:['400px','430px'],offset:'20px'},"${ctxPath}/pages/order/order-edit.jsp",{entId:Ent.entId});
	}
	Ent.editBusiOrder = function(orderId) {
	    popup.layerShow({type:1,title:'修改订购',area:['400px','430px'],offset:'20px'},"${ctxPath}/pages/order/order-edit.jsp",{entId:Ent.entId,orderId:orderId});
	}
	Ent.searchPhone = function(){
		$("#phoneForm").searchData();
	}
	Ent.entPhonePage = function() {
		popup.openTab("${ctxPath}/pages/phone/phone-list.jsp",'企业话机管理',{entId:Ent.entId});
	}
	
	Ent.searchAgent = function(){
		$("#agentForm").searchData();
	}
	Ent.entAgentPage = function() {
		popup.openTab("${ctxPath}/pages/user/user_list.jsp",'企业坐席管理',{entId:Ent.entId,entcode:Ent.entCode});
	}
	
	Ent.searchPrifix = function(){
		$("#prifixForm").searchData();
	}
	Ent.entPrifixPage = function() {
		popup.openTab("${ctxPath}/pages/prefix/prefix-list.jsp",'企业号码管理',{entId:Ent.entId});
	} 
	
	Ent.searchPrifixRecord = function(){
		$("#prifixRecordForm").searchData();
	}
	Ent.entPrefixRecordPage = function() {
		popup.openTab("${ctxPath}/pages/prefix/prefix-record.jsp",'企业号码备案',{entId:Ent.entId});
	}
	
	Ent.searchEntVox = function() {
		$("#entVoxForm").searchData();
	}
	Ent.entVoxPage = function() {
		popup.openTab("${ctxPath}/pages/vox/ent-vox-list.jsp",'企业语音文件管理',{entId:Ent.entId});
	}
	
	Ent.searchEntCallRang = function() {
		$("#callRangForm").searchData();
	}
	Ent.entCallRangPage = function() {
		popup.openTab("${ctxPath}/pages/callRang/call-rang.jsp",'企业呼叫范围管理',{entId:Ent.entId});
	}
	
	Ent.entSbcPage = function(){
		popup.openTab("${ctxPath}/pages/sbc/ent-sbc-list.jsp",'企业SBC资源管理',{entId:Ent.entId});
	}
	
	Ent.editRes = function() {
	    popup.layerShow({type:1,title:'修改资源配置',area:['400px','479px'],offset:'20px'},"${ctxPath}/pages/ent/ent-res-edit.jsp",{entId:Ent.entId});
	}
	Ent.editInfo = function() {
	    popup.layerShow({type:1,title:'修改企业信息',area:['670px','710px'],offset:'20px'},"${ctxPath}/pages/ent/ent-edit.jsp",{entId:Ent.entId});
	}
	Ent.editCallRes = function() {
	    popup.layerShow({type:1,title:'修改外呼配置',area:['505px','511px'],offset:'20px'},"${ctxPath}/pages/ent/ent-res-call-edit.jsp",{entId:Ent.entId});
	}
	
	Ent.searchLabel = function(){
		$("#labelForm").searchData();
	}
	Ent.labelPage = function() {
		popup.openTab("${ctxPath}/pages/label/label-list.jsp","企业大数据标签管理",{entId:Ent.entId});
	}
	Ent.syncEntInfo = function() {
		ajax.remoteCall("${ctxPath}/servlet/ent?action=syncInfo",{entId:Ent.entId},function(result) { 
			layer.msg(result.msg);
		});
	}
	Ent.smsPage = function() {
		popup.openTab("${ctxPath}/pages/sms/sms-temp-list.jsp",'企业营销短信模板管理',{entId:Ent.entId});
	}
	Ent.editFeeConf = function(){
	    popup.layerShow({type:1,title:'修改计费配置',area:['509px','442px'],offset:'20px'},"${ctxPath}/pages/ent/fee-conf.jsp",{entId:Ent.entId});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>