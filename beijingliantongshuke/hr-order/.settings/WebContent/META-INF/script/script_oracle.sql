/*==============================================================*/
/* Table: CC_ENT                                                */
/*==============================================================*/
create table CC_ENT
(
   ENT_ID               VARCHAR2(32) not null,
   ENT_CODE             VARCHAR2(20) not null,
   ENT_NAME             VARCHAR2(100) not null,
   OPEN_TIME            VARCHAR2(10) not null,
   ENT_STATE            INTEGER,
   LINKMAN              VARCHAR2(50),
   LINKPHONE            VARCHAR2(50),
   EMAIL                VARCHAR2(100),
   INDUSTRY             VARCHAR2(20),
   ENT_SCALE            INTEGER,
   AREA_CODE            VARCHAR2(4),
   ADDR                 VARCHAR2(255),
   CORPORATE            VARCHAR2(50),
   IDCARD_URL           VARCHAR2(500),
   B<PERSON>I_LICENSE_URL     VARCHAR2(500),
   MEMO                 VARCHAR2(500),
   CREATOR              VARCHAR2(50),
   CREATE_TIME          VARCHAR2(19),
   primary key (ENT_ID)
);



