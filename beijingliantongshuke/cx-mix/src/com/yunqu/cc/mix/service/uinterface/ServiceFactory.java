package com.yunqu.cc.mix.service.uinterface;

import com.yq.busi.common.service.BaseService;

public class ServiceFactory {
	
	private static ServiceFactory instance;
	
	private ServiceFactory(){
		
	}
	
	public static ServiceFactory getInstance(){
		instance = new ServiceFactory();
		return instance;
	}
	
	/**
	 * 根据操作命令返回对应的service处理对象
	 * @param command
	 */
	public  BaseService getService(String command){
		
		return null;
	}
}
