<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="在线留言"></title>
	 <style type="text/css">
		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		::-webkit-scrollbar-track {
			background: transparent;
		}
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		#searchForm{
			height:300px;
		}
		.shadow{
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
		.btn{
			margin-right: 10px;
		}
		#order_card{
			height: 100%;
			overflow-x: auto;
			/* overflow-y: hidden; */
			-webkit-box-flex: 1;
		    -ms-flex: 1;
		    flex: 1;
		    overflow: auto;
		    position: relative;
		}
		.custIcon {
			color: #31b0d5;
			font-size: 17px;
			cursor:pointer;
		}
		.out_a {
			position: absolute;
		    top: 50%;
		    right: 15px;
		    z-index: 333;
		}
		.out_a:hover .right_div_button {
			background-color: #179F69;
		}
		.flex-row {
		    width: 100%;
		    display: -webkit-box;
		    display: -ms-flexbox;
		    display: flex;
		    -webkit-box-orient: horizontal;
		    -webkit-box-direction: normal;
		    -ms-flex-direction: row;
		    flex-direction: row;
		    -webkit-box-pack: justify;
		    -ms-flex-pack: justify;
		    justify-content: space-between;
		    -webkit-box-sizing: border-box;
		    box-sizing: border-box;
		}
		.right_div_button {
			width: 30px;
			height: 30px;
			position: absolute;
			top: 46%;
			right: -8px;
			z-index: 998;
			cursor: pointer;
			transform: rotateZ(45deg);
			background-color: #337AB7;
			border-radius: 50%;
		}
	</style>
	
	
	<style>
	/* .layui-layer-content{
		overflow-x: hidden !important;
	} */

	.container-fluid {
		padding-right: 0px;
		padding-left: 0px;
		margin-right: 0px;
		margin-left: 0px;
		padding: 0px;
	}
	.layui-table-click{
				/* 1. table默认是有背景色的你要保证优先加载你的,所以使用important */
				background-color: #c9e2f9 !important;
				color: #FFF;
	}
.btn {
    margin-right: 0px;
}
.ibox-content{
		padding-top: -20px;
		margin-top: 0px;
	}
	.layui-table{
	margin-top: -10px;
	}
</style>
</EasyTag:override>

<EasyTag:override name="content">
<div class="flex-row" style="height: 100%">
	<div id="order_card" >
		<div class="layui-tab-card" id="order-bom" style="border: none;">
		<input type="hidden" id="dateTemp">
		<form action="" method="post" name="searchForm" class="form-inline" id="searchForm" autocomplete="off" onsubmit="return false" data-toggle="render">
			<input type="hidden" id="custCode" name="custCode" value="${param.custCode }">
			<div class="ibox shadow">
				<div class="ibox-title clearfix cust-hide" id="divId">
					<!-- <div class="form-group">
						<h5><span i18n-content="客户留言"></span><span id="sub" ><i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i></span></h5>
					</div>
					<hr style="margin: 3px -15px"> -->
					<div class="form-group" >
				  		<div class="input-group input-group-sm all" style="width: 210px;">
							<span class="input-group-addon" i18n-content="留言时间"></span>
							<input type="text" class="form-control input-sm" id="startDate" name="startDate" data-mars="common.todayFirstTime" style="width:135px" data-mars-reload="false" data-mars-top='true' autocomplete="off">
							<span class="input-group-addon">-</span>	
							<input type="text" class="form-control input-sm" id="endDate" name="endDate" data-mars="common.todayEndTime" style="width:135px" data-mars-reload="false" data-mars-top='true' autocomplete="off">									  
	                 		<!-- <span class="input-group-addon">-</span>
	                        <select class="form-control input-sm" name="dateRange" onchange="starEntTime(this.value)">
								<option value="" i18n-content="请选择"></option>
									<option value="today" i18n-content="今天"></option>
									<option value="yesterday" i18n-content="昨天"></option>
									<option value="thisWeek" i18n-content="本周"></option>
									<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
									<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
							</select> -->
	                    </div>
	                    
	                    <div class="input-group input-group-sm all" style="width: 180px;">
							<span class="input-group-addon" style="width:70px;"  i18n-content="关键字"></span>
							 <input type="text" name="keyWord" i18n-placeholder="客户姓名/客户电话" 
							 class="form-control input-sm" maxlength="200" autocomplete="off" style="">
						</div>
						
						<div class="input-group input-group-sm all" style="width: 180px;">
							<span class="input-group-addon"  i18n-content="客户邮箱"></span>
							 <input type="text"
								name="USER_EMAIL" class="form-control input-sm"
								style="width:128px;">
						</div>
						<div class="input-group input-group-sm all" style="width: 200px;">
							<span class="input-group-addon" style="width: 50px;" i18n-content="处理状态"></span> 
							<select name="HANDLE_STATUS" class="form-control input-sm">
								<option value=""  i18n-content="请选择"  style="width: 90px;"></option>
								<option value="0"  i18n-content="未处理"  style="width: 90px;"></option>
								<option value="1"  i18n-content="已经处理"  style="width: 90px;"></option>
							</select>
						</div>		
						
							<!-- <div class="input-group input-group-sm">
								<span class="input-group-addon"  style="width:70px;" i18n-content="渠道"></span> 
								<select id="CHANNEL_KEY" name="CHANNEL_KEY" data-mars="offLineDao.getChannel" 
									class="form-control input-sm" style="width: 150px" multiple="multiple" data-mars-top="true">
								</select>
							</div> -->
							
						 
						<div class="input-group input-group-sm all">
								<button type="button" class="btn btn-sm btn-default" title="快捷键：按回车查询" id="query"
									onclick="offlineMessNS.searchData('1')">
									<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span> 
								</button>
						</div>
						<!-- <div class="input-group input-group-sm all">
								<button type="button" class="btn btn-sm btn-default" onclick="moreQuery()"><span class="glyphicon  glyphicon-search"></span><span
														i18n-content="更多" title=""></span></button>
						</div> -->
							
						<div class="input-group input-group-sm pull-right"  id="menu">
							<button type="button" class="btn btn-sm btn-info btn-outline" onclick="" >
								<span i18n-content="操作" title=""></span>&nbsp;&nbsp;
								<i class="layui-icon layui-icon-triangle-d" style="font-size: 10px;"></i>
							</button>
							<div id="menudiv" style="display:none;color:#FFF;float:left;position: absolute;z-index:999;">
								<div class="input-group input-group-sm pull-right">
									<button type="button" class="btn btn-sm btn-info btn-outline" onclick="downloadExl()"  i18n-content="直接导出"> </button><br>
								</div>
							</div>
						</div>	
						
						 <div class="form-group" id="more-query-content" style="display: none;" id="divId">
							 <!-- <div class="input-group input-group-sm all" style="width: 200px;"> 
									<span class="input-group-addon" i18n-content="渠道名称"></span>
									<div class=""> 
										<div id="channelSelect" style="width: 110px;"></div>
										<input type="hidden" id="CHANNEL_KEY" name="CHANNEL_KEY">
									</div>
								</div> -->
						 	
							<div class="input-group input-group-sm all">
							<button type="button" title="快捷键：按Delete快速重置" id="reset" class="btn btn-sm btn-default" onclick="offlineMessNS.reset()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span> </button>
						</div>
							
	             	   </div> 
					</div>
				</div>
			   	<div class="ibox-content">
					<table class="layui-table layui-form" id="tree-table"></table>
				</div>
				
			</div>
		</form>
		</div>
		<!-- <a title="侧边伸缩" class="out_a">
			<div class="right_div_button" onclick="offlineMessNS.showAndHide()"></div>
			<i id="switch_show_card" class="layui-icon layui-icon-spread-left" style="font-size: 20px;position: absolute;top: 47%;right: -3px;z-index: 999;cursor: pointer;color: #fff;"
			 onclick="offlineMessNS.showAndHide()"></i>
		</a> -->
	</div>
	<%-- 	
	<div id="customer_card" style="width: 25%;display: none">
		<div class="layui-tab layui-tab-card" style="height: 100%;width: 100%;margin-top:0">
			<jsp:include page="/pages/together/sidebariframe.jsp"></jsp:include>
		</div>
	</div> --%>
</div>

</EasyTag:override>
<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<!-- <script type="text/javascript" src="/cx-mix/static/js/formSelects/xm-select.js"></script> -->
<EasyTag:override name="script">

	<script type="text/javascript">
		 jQuery.namespace("offlineMessNS");
		 
			function moreQuery(){
				$('#more-query-content').toggle();
			}
			
			 $(function() {
				 $("#searchForm").render({});
					//getChannelData();
					//execI18n();
					offlineMessNS.showAndHide();
					// offlineMessNS.renderRightPage();
					//自动填充
					$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
					
			})
			
			$(document).keyup(function(event){
			  if(event.keyCode ==13){
			    $("#query").trigger("click");
			  }
			  if(event.keyCode ==46){
				    $("#reset").trigger("click");
				  }
			});
			
			//鼠标悬停提示特效
				$("#menu").hover(function() {
					$("#menudiv").show({duration: 50});
				}, function() {
					$("#menudiv").hide({duration: 50});
				});
		 
		//重置
		offlineMessNS.reset=function(){
			$("#divId select").val("");
	    	$("#divId input").val("");
			$("#searchForm").render({success:function(){
	    		requreLib.setplugs('multiselect',function(){
				});
	    		
	    	}});
			// channelSelect.setValue([]);
		};
		 
		offlineMessNS.searchData=function(flag){
			if(flag=='1'){
				$("#searchForm").queryData({id:'tree-table',page:{curr:1}});
			}else{
				$("#searchForm").queryData({id:'tree-table'});
			}
		}
	       
       requreLib.setplugs("layui,multiselect",function(){
		   // 设置layui组件引入路径 ======> 兼容IE
		   layui.config({dir: '/easitline-static/lib/layui/'})
        	$("#searchForm").render({success:function(){
	        	//加载时间控件
				layui.use('laydate', function(){
			  		var laydate = layui.laydate;
			  		laydate.render({ elem: '#startDate' ,type: 'datetime',lang:getDateLang()});
			  		laydate.render({ elem: '#endDate' ,type: 'datetime',lang:getDateLang()});
				});
	        	//被客户资料页面引用时执行
				offlineMessNS.refCustOption();
	        	offlineMessNS.loadData();
        	}});
        });
	        
       offlineMessNS.loadData = function(){
			$("#searchForm").initTableEx({
				id:'tree-table',
				url:'${ctxPath}/webcall?action=offLineDao.messageManagement'
				,limit:10
				,page:true 	
				,title:'离线消息'
				,cellMinWidth:60
				,height: 'full-160'
				,loading:true
				,cols: [
						[
							 {width:40,field:'', title:getI18nValue('选择') ,fixed: 'left', type:'checkbox'},
						  {width:40,align:'center',title: getI18nValue('序号') ,type:'numbers'},
					      {minWidth:100,align:'center',field:'USER_NAME', title: getI18nValue('客户姓名')},
					      {minWidth:100,align:'center',field:'USER_PHONE', title:getI18nValue('客户电话')},
					      {minWidth:160,align:'center',field:'USER_EMAIL', title:getI18nValue('客户邮箱') },
					      {minWidth:100,align:'center',field:'USER_ADDRESS', title: getI18nValue('客户地址')},
					      {minWidth:100,align:'center',field:'HANDLE_STATUS', title: getI18nValue('处理状态'),
					        	 templet:function(row){
					        		 var json={0:'layui-btn',1:'layui-btn layui-btn-danger',3:'layui-btn layui-btn-warm'};
									 return "<span class='layui-btn layui-btn-xs "+json[row.HANDLE_STATUS]+"'>"+translation(row.HANDLE_STATUS)+"</span>"
					        }}, 
					     /*  {minWidth:150,align:'center',field:'EX1', title:getI18nValue('渠道'),sort:true ,
					    	  templet:function(row){
					    		  return getText(row.EX1 , $('#CHANNEL_KEY'))
					    	  }
					      }, */
					      {minWidth:150,align:'center',field:'CREATE_TIME', title:getI18nValue('留言时间'),sort:true},
					      
					        {minWidth:100,align:'center',field:'HANDLE_USER_ACC', title: getI18nValue('处理人账号')},
					      {minWidth:150,align:'center',field:'HANDLE_TIME', title: getI18nValue('处理时间'),sort:true},
					       {minWidth:120,align:'center',field:'', title:getI18nValue('操作') , fixed:'right' ,
					    	  templet:function(row){
					    		  return judgeState(row.ID,row.HANDLE_STATUS,row.EX1,row.EX2);
					       }}, 
				         ]
					],
				done:function(res,curr,count){
					//子页面向父页面传值
					//$(window.parent.$("#message").html(res.totalRow));
				},
				rowEvent: function(obj, event) {
					//$(event.tr[0]).addClass("layui-table-hover");
					offlineMessNS.showAndHide(obj.EX3, obj.EX2, obj.EX1, obj.HANDLE_USER_ACC, obj.USER_NAME, obj.EX4);
					//SERIAL_ID, SESSION_ID, CHANNEL_KEY, AGENT_NAME, CUST_NAME, GROUP_ID
				}
			});
       }
       // 把row.HANDLE_STATUS返回的 1、 0 数字 转换成汉字或者英文
       function translation(num){
       	if(num=="1"){return getI18nValue('已经处理');}else if(num=="0"){return getI18nValue('未处理');}
       	else if(num=="3"){return getI18nValue('无需处理');}
       }
			
	 //处理留言
	 offlineMessNS.dealdialog = function(id,state,ex1){
		 console.log(id, state, ex1);
		 if(ex1=="WeChat1"){
			 offlineMessNS.wechatDialog(id,state,ex1);
		 }else{
		    popup.layerShow({type:2,shadeClose:false,
			area:['700px','450px'],title:'',offset:'20px'},"${ctxPath}/pages/together/dealMessage.jsp",{id:id,state:state,ex1:ex1});
	 }}
	 
	 //微信留言
	 offlineMessNS.wechatDialog = function(id,state,ex1){
		 popup.layerShow({type:2,shadeClose:false,
			area:['1100px','700px'],title:'',offset:'20px',scrollbar: false},"${ctxPath}/pages/together/wechatMessage.jsp",{id:id,state:state,ex1:ex1});
	 }
	
    function judgeState(id,state,ex1,ex2) {
		// var temp = '<a class="layui-btn layui-btn-xs" href="javascript:offlineMessNS.dialog(\''+id+'\',\''+ex2+'\',\''+ex1+'\')" >'+getI18nValue('回复')+'</a> ';
		if(state=="0"){
			var temp = '<a  class="layui-btn layui-btn-xs" href="javascript:offlineMessNS.dealdialog(\''+id+'\',\''+state+'\',\''+ex1+'\')" >'+getI18nValue('处理留言')+'</a>'; 				
		}else{
			var temp = '<a class="layui-btn layui-btn-xs" href="javascript:offlineMessNS.dealdialog(\''+id+'\',\''+state+'\',\''+ex1+'\')" >'+getI18nValue('查看留言')+'</a>'; 	
		}
		return temp;
	}
    
  	//回复
	offlineMessNS.dialog = function(id,SESSION_ID,CHANNEL_KEY){ // id为word_id
		popup.layerShow({type:1,title: getI18nValue('发起会话'),shadeClose:false,
			area:['600px','550px'],offset:'20px'},"${ctxPath}/pages/together/online-dialog.jsp",{id:id,SESSION_ID:SESSION_ID,CHANNEL_KEY:CHANNEL_KEY});
	}
	
	//导出
	function downloadExl () {
		var arr=new Array();
		var checkStatus = table.checkStatus('tree-table');
		for(j = 0; j < checkStatus.data.length; j++) {
			arr.push(checkStatus.data[j].WORD_ID);
		}
	 location.href = "${ctxPath}/servlet/offLine?action=messageExport&"
			+ $("#searchForm").serialize()+"&ids="+arr.toString() + "&date=" + $("#startDate").val() + "~" + $("#endDate").val(); 
	}
	//设置时间
	function starEntTime(dateRange){
	    if(dateRange == "today") {
	   		$("#startDate").val(getTodayStartTime());
	   		$("#endDate").val(getTodayEndTime());
	    }else if(dateRange == "yesterday") {
	    	$("#startDate").val(getYesterDayStartTime());
	   		$("#endDate").val(getYesterDayEndTime());
	   	}else if(dateRange == "thisWeek") {
	   		$("#startDate").val(getThisWeekStartTime());
	   		$("#endDate").val(getThisWeekEndTime());
	    }else if(dateRange == "RecentlyOneMonth") {
	    	$("#startDate").val(getRecentlyOneMonthStartTime());
	   		$("#endDate").val(getTodayEndTime());
	    }else if(dateRange == "RecentlyThreeMonth") {
	    	$("#startDate").val(getRecentlyThreeMonthStartTime());
	   		$("#endDate").val(getTodayEndTime());
	   	}
	 }
	
	//侧边栏显示隐藏
	offlineMessNS.isShowSidebar = false;
	offlineMessNS.showAndHide = function(SERIAL_ID, SESSION_ID, CHANNEL_KEY, AGENT_NAME, CUST_NAME, GROUP_ID) {
		if (SERIAL_ID) {
			/* $("#switch_show_card").removeClass("layui-icon-shrink-right")
				.addClass("layui-icon-spread-left"); */
			offlineMessNS.renderRightPage(SERIAL_ID, SESSION_ID, CHANNEL_KEY, AGENT_NAME, CUST_NAME, GROUP_ID);
			return;
		} else {
			$("#switch_show_card").removeClass("layui-icon-spread-left")
				.addClass("layui-icon-shrink-right");
			$("#customer_card").hide();
		}
		if (offlineMessNS.isShowSidebar) {
			$("#switch_show_card").removeClass("layui-icon-spread-left")
				.addClass("layui-icon-shrink-right");
			$("#customer_card").hide();
			offlineMessNS.isShowSidebar = !offlineMessNS.isShowSidebar;
		} else {
			$("#switch_show_card").removeClass("layui-icon-shrink-right")
				.addClass("layui-icon-spread-left");
			//offlineMessNS.renderRightPage();
			$("#customer_card").show(SERIAL_ID, SESSION_ID, CHANNEL_KEY, AGENT_NAME, CUST_NAME, GROUP_ID);
			offlineMessNS.isShowSidebar = !offlineMessNS.isShowSidebar;
		}
		
	}
	offlineMessNS.renderRightPage = function(chatSessionId, sessionId, channelKey, agentName, custName, groupId) {
		var sourcePage = "mediaPage";
		var sidebarParam = {
			sourcePage: sourcePage,
			chatSessionId: chatSessionId,
			sessionId: sessionId,
			channelKey: channelKey,
			//p_username: encodeURIComponent(custName),
			//custName: encodeURIComponent(custName),
			//agentName: encodeURIComponent(agentName),
			groupId: groupId
		};
		if (sidebarApp) {
			sidebarApp.queryMenu(sidebarParam);
		}
	}
	
	//作为iframe被引用时
	offlineMessNS.refCustOption = function(){
		if('${param.opener}'=='cust'){
			$("body").removeClass("gray-bg");
			$(".cust-hide").hide();
			//starEntTime('RecentlyOneMonth');
			$("#startDate").val("");
		}
	}
	
	//请求后端拿渠道数据
	function getChannelData(){
		ajax.remoteCall("/cx-mix/webcall?action=offLineDao.queryChannel",{},function(result) {
			if(result.data){
				var channelList = [];
				var json = result.data;
				for(var key in json){
					var imp = {
						name:json[key],
						value:key
					}
					channelList.push(imp)
				}
				channelSelect.update({
					data: channelList
				})
			}
		});
	}
	
	
	//渠道选择下拉框
	/* var channelSelect = xmSelect.render({
	/* 	el: '#channelSelect',
		size: 'small',
		toolbar: {
			show: true,
			showIcon: false,
		},
		filterable: true,
		paging: true,
		pageSize: 10,
		data: [],
		model: {
			label: {
				type: 'block',
				block: {
					//最大显示数量, 0:不限制
					showCount: 1,
					//是否显示删除图标
					showIcon: true,
				}
			}
		},
		on: function(data){//监听选择框
			var arr = data.arr;//当前多选已选中的数据
			//以下对数据格式统一，满足现有后端查询方法
			var channelId = "";
			if(arr.length > 0){
				channelId = "[";
				for(var i in arr){
					channelId += '\'' + arr[i].value + '\',';
				}
				channelId = channelId.substring(0,channelId.length-1) + "]";
			}
			$("#CHANNEL_KEY").val(channelId)
		}
	}) */
	
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>