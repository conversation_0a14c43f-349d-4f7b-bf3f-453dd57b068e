package com.yq.busi.common.util.db;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;

import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.util.CommonUtil;

public class DBUtil {
	private static Logger logger = CommonCoreLogger.logger;
	
	/**
	 * 列类型：字符型
	 */
	public final static String COLUMN_TYPE_VARCHAR = "1";
	/**
	 * 列类型：数字类型
	 */
	public final static String COLUMN_TYPE_NUMBER = "2";
	/**
	 * 创建数据库表，创建表的同时，会创建 ID 字段
	 * @param tableName
	 * @return
	 */
	public static boolean createTable(EasyQuery query ,String tableName){
		try {
			String sql = " CREATE TABLE "+tableName+" ( ID VARCHAR(64) PRIMARY KEY) ";
			query.execute(sql);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(DBUtil.class)+" 创建数据表失败:"+e.getMessage(),e);
		}
		return false;
	}
	
	/**
	 * 删除数据库表
	 * @param tableName
	 * @return
	 */
	public static boolean dropTable(EasyQuery query ,String tableName){
		try {
			String sql = " DROP TABLE "+tableName;
			query.execute(sql);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(DBUtil.class)+" 删除数据表失败:"+e.getMessage(),e);
		}
		return false;
	}
	
	/**
	 * 添加数据库字段
	 * @param query
	 * @param tableName  数据库表名
	 * @param columnName 字段名称
	 * @param columType  列类型，读取DBUtil.COLUM_TYPE_ 常用类
	 * @param length   列长度
	 * @param precision   字段精度，当数字类型的字段有效
	 * @return
	 */
	public static boolean addColumn(EasyQuery query ,String tableName,String columnName,String columType,int length,int precision ){
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" ALTER TABLE ").append(tableName).append(" ADD ").append(columnName);
			if(COLUMN_TYPE_VARCHAR.equals(columType)){
				sql.append(" VARCHAR(").append(length).append(" ) ");
			}else if(COLUMN_TYPE_NUMBER.equals(columType)){
				sql.append(" NUMBER(").append(length).append(",").append(precision).append(" ) ");
			}
			query.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(DBUtil.class)+" 添加数据库字段失败:"+e.getMessage(),e);
		}
		return false;
	}
	
	/**
	 * 修改数据库字段
	 * @param query
	 * @param tableName  数据库表名
	 * @param columnName 字段名称
	 * @param columType  列类型，读取DBUtil.COLUM_TYPE_ 常用类
	 * @param length   列长度
	 * @param precision   字段精度，当数字类型的字段有效
	 * @return
	 */
	public static boolean modifyColumn(EasyQuery query ,String tableName,String columnName,String columType,int length,int precision ){
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" ALTER TABLE ").append(tableName).append(" MODIFY ").append(columnName);
			if(COLUMN_TYPE_VARCHAR.equals(columType)){
				sql.append(" VARCHAR(").append(length).append(" ) ");
			}else if(COLUMN_TYPE_NUMBER.equals(columType)){
				sql.append(" NUMBER(").append(length).append(",").append(precision).append(" ) ");
			}
			query.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(DBUtil.class)+" 添加数据库字段失败:"+e.getMessage(),e);
		}
		return false;
	}
	
	/**
	 * 删除表字段
	 * @param query
	 * @param tableName  表名
	 * @param columnName 要删除的字段
	 * @return
	 */
	public static boolean dropColumn(EasyQuery query ,String tableName,String columnName ){
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" ALTER TABLE ").append(tableName).append(" DROP COLUMN ").append(columnName);
			query.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(DBUtil.class)+" 删除数据库字段失败:"+e.getMessage(),e);
		}
		return false;
	}
}
