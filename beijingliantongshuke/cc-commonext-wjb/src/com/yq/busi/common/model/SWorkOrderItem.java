package com.yq.busi.common.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class SWorkOrderItem implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long workItemId;

    private Long workId;

    private Integer shopId;

    private Long orderItemId;

    private String itemId;

    private String itemName;

    private Short itemCnt;

    private BigDecimal itemPrice;

    private BigDecimal discountAmount;

    private BigDecimal paymentAmount;

    private String logisticsComCode;

    private String logisticsNo;

    private String backLogisticsNo;

    private String backLogisticsComCode;

    private String cateId;

	public Long getWorkItemId() {
		return workItemId;
	}

	public void setWorkItemId(Long workItemId) {
		this.workItemId = workItemId;
	}

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

	public Integer getShopId() {
		return shopId;
	}

	public void setShopId(Integer shopId) {
		this.shopId = shopId;
	}

	public Long getOrderItemId() {
		return orderItemId;
	}

	public void setOrderItemId(Long orderItemId) {
		this.orderItemId = orderItemId;
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public Short getItemCnt() {
		return itemCnt;
	}

	public void setItemCnt(Short itemCnt) {
		this.itemCnt = itemCnt;
	}

	public BigDecimal getItemPrice() {
		return itemPrice;
	}

	public void setItemPrice(BigDecimal itemPrice) {
		this.itemPrice = itemPrice;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}

	public String getLogisticsComCode() {
		return logisticsComCode;
	}

	public void setLogisticsComCode(String logisticsComCode) {
		this.logisticsComCode = logisticsComCode;
	}

	public String getLogisticsNo() {
		return logisticsNo;
	}

	public void setLogisticsNo(String logisticsNo) {
		this.logisticsNo = logisticsNo;
	}

	public String getBackLogisticsNo() {
		return backLogisticsNo;
	}

	public void setBackLogisticsNo(String backLogisticsNo) {
		this.backLogisticsNo = backLogisticsNo;
	}

	public String getBackLogisticsComCode() {
		return backLogisticsComCode;
	}

	public void setBackLogisticsComCode(String backLogisticsComCode) {
		this.backLogisticsComCode = backLogisticsComCode;
	}

	public String getCateId() {
		return cateId;
	}

	public void setCateId(String cateId) {
		this.cateId = cateId;
	}
    
	
}
