
<script type="text/javascript">
<!--
jq(function(){
	jq('#id').val(task.eventId);
	jq('#errorid').val(task.eventErrorId);
	jq('#attachedTo').val(task.eventTo);
});

function saveTaskProperties(){
	if(task){
		task.eventId=jq('#id').val();
		task.eventErrorId=jq('#errorid').val();
		task.eventTo=jq('#attachedTo').val();
		jq.messager.alert('信息','保存成功!','info');
	}else jq.messager.alert('信息','保存失败!','info');
}

//-->
</script>
<div id="task-properties-layout" class="easyui-layout" fit="true">
	<div id="task-properties-toolbar-panel" region="north" border="false" style="height:30px;background:#E1F0F2;">
		<a href="##" id="sb2" class="easyui-linkbutton" plain="true" iconCls="icon-save" onclick="saveTaskProperties()">保存</a>
	</div>
	<div id="task-properties-panel" region="center" border="true">
    	<div class="easyui-panel" title="常规" data-options="fit:true,border:false">
        <div>
            <table cellpadding="5">
                <tr>
                    <td nowrap="nowrap">ID:</td>
                    <td><input class="easyui-textbox" type="text" name="id" id="id"></input></td>
                </tr>
                <tr>
                    <td>错误ID:</td>
                    <td><input class="easyui-textbox" type="text" name="errorid" id="errorid"></input></td>
                </tr>
                <tr>
                    <td>依附到:</td>
                    <td><input class="easyui-textbox" type="text" name="attachedTo" id="attachedTo"></input></td>
                </tr>
            </table>
        </div>
		</div>
	</div>
</div>