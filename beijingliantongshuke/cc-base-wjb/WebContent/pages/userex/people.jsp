<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>

<EasyTag:override name="head">
	<title>人员选择</title>
	<style>
.ztree .line {
	border: none
}


</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" id="searchForm" name="searchForm" class="form-inline">
		<input class="hidden" id="dept_id" name="dept_id" value="">
		<div class="row">
			<div style="background-color: #fff; margin-left: 15px; width: 240px; float: left; height: 550px;">
				<div style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
					<span class="glyphicon glyphicon-home"  i18n-content="部门"></span> 
				</div>
				<div class="ztree" id="ztree" style="max-height: 480px; overflow: hidden; padding: 10px; width: auto; height: 480px;"></div>
			</div>
			<div style="height: auto; width: 500px; float: left; margin-left: 15px;">
				<div class="ibox ">
					<div class="ibox-title clearfix">
						<div class="form-group">
							<div class="input-group input-group-sm">
								<span class="input-group-addon" i18n-content="员工账号/名称"></span>
								<input type="text" name="userInfo" id="userInfo" class="form-control input-sm" style="width: 90px">
							</div>
							<div class="input-group input-group-sm">
								<button type="button" class="btn btn-sm btn-default" onclick="searchData()">
									<span class="glyphicon glyphicon-search" i18n-content="查询"></span> 
								</button>
							</div>
						</div>
					</div>
					<div class="ibox-content" >
						<table id="tableHead"  ></table>
					</div>
				</div>
			</div>
			<div
				style="background-color: #fff; margin-left: 15px; width: 100px; float: left; height: 550px">
				<div
					style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
					<span class="glyphicon glyphicon-home"  i18n-content="已选人员" ></span> 
				</div>
				<div data-template="labelList-template" id="labelTableHead"
					>
					<div class="clearfix labelListDiv">
						<div data-container="#labelTableHead" data-template="labelList-template" id="selectUser" ></div>
					</div>
				</div>
			</div>

			<script id="labelList-template" type="text/x-jsrender">

			</script>
		</div>
	</form>
	<div class="layer-foot text-c">
		<button class="btn btn-sm btn-primary" type="button" onclick="save()" i18n-content="保存"></button>
		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="popup.layerClose()" i18n-content="关闭"></button>
		<button class="btn btn-sm btn-default hidden"  id="clean" type="button" id="backbut"  i18n-content="刷新"></button>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.slimscroll.min.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/layui/layui.js"></script>
	<script type="text/javascript">
	jQuery.namespace("dept");
	$(function() {
		initData();
		$('#userInfo').bind('keypress',function(event){ 
	         if(event.keyCode == 13)      
	         {  
	        	 searchData();
	         }  

	     });
		
		setOldIds();
		$("#selectUser").slimScroll({color:'#aaa',height:450});
		$("#userList").slimScroll({color:'#aaa',height:340});
		var userIds="";
		var allowDept="";//不为空则拥有所有权限
		$('#searchForm').render({success : function() {
			requreLib.setplugs('slimscroll,ztree', function() {
				$('#ztree').slimScroll({
					 height: '430px',
		                color: '#ddd'
				});
				//data-setting="{check: {enable: ${enable }},callback: {onClick:dept.onClick,onCheck:dept.onClick},expandAll: true}"
				var setting = {
						callback: {onClick:dept.test,onCheck:dept.onClick},
						check: {
							enable: ${enable },
							autoCheckTrigger:true,
							chkStyle:"checkbox" 
							},
						expandAll: true,
				};
				commonTree.initTree(setting);
				
				var treeObj = $.fn.zTree.getZTreeObj("ztree");
				if(treeObj) {
					var nodes = treeObj.getNodes();
					if('${expandAll}'=="true"){//展示所以数据
						$.fn.zTree.getZTreeObj("ztree").expandAll(true);
					}else if (nodes.length > 0) {
						for (var i = 0; i < nodes.length; i++) {
							treeObj.expandNode(nodes[i], true, false, false);
						}
					}
				}
			});
			}
		});
		execI18n();
	});
	
	function searchData() {
		 if('${enable }'=='true'){//多选
			 var selectid="";
			 treeObj = $.fn.zTree.getZTreeObj("ztree");
				nodes = treeObj.getCheckedNodes(true);
				for (var i = 0; i < nodes.length; i++) {
					if(nodes[i].id!=-1)
						//拿到所有节点数据
						selectid=(selectid==""?nodes[i].id:selectid+","+nodes[i].id);
				}
				$("#dept_id").val(selectid);
		 } else{
			 var selectedNodes = $.fn.zTree.getZTreeObj("ztree").getSelectedNodes();
			 if(selectedNodes==""||selectedNodes[0].id=='-1') {
				 $("#dept_id").val('');
			 } else {
			 	$("#dept_id").val(selectedNodes[0].id);
			 }
		 }
			
		$("#searchForm").queryData({
			id : 'tableHead',
			page: {curr: 1}
		});	
	}

	$("input[name='checkAll']").click(function() {
			var ifChecked = $(this).prop("checked");
			$("#dataList input:checkbox").prop("checked", ifChecked);
			if(ifChecked){//选中
				var ids = $("#dataList").find("input[type='checkbox']");
				for(var i=0;i<ids.length;i++){
					selectUser[$(ids[i]).attr("data-id")]=$(ids[i]).attr("data-name"); 
				}
				selectUserlist();
			}else{
				var ids = $("#dataList").find("input[type='checkbox']");
				for(var i=0;i<ids.length;i++){
					delete selectUser[$(ids[i]).attr("data-id")];
				}
				selectUserlist();
			}  
		})
		
	dept.test = function(e,treeId, treeNode) {
		/* var zTree = $.fn.zTree.getZTreeObj("ztree");
		zTree.checkNode(treeNode, !treeNode.checked, true); */
		var zTree = $.fn.zTree.getZTreeObj("ztree");
		zTree.checkNode(treeNode, !treeNode.checked, true);
		searchData();
		}
	dept.onClick= function(e,treeId, treeNode) {
		/* var zTree = $.fn.zTree.getZTreeObj("ztree");
		zTree.checkNode(treeNode, !treeNode.checked, true); */
		searchData();
		}

	var selectUser={};//旧的值
	function update(data,type){
		if(type){//选中
			if ('${pageFlag}' == '1') {
				selectUser[data.USER_ACC]=data.USER_NAME + ":" + data.USER_ID;
			}else{
				selectUser[data.USER_ACC]=data.USER_NAME;
			}
		}else{
			delete selectUser[data.USER_ACC];
		}
		selectUserlist(); 
	}
	
	function selectUserlist(){
		var content="";
		for(var key in selectUser){// .split(":")[0]
			content=content+add(selectUser[key].split(":")[0]);
		}
		$("#selectUser").html(content);
	}
	function add(name){
		var content='<div class="clearfix labelListDiv">'+
		'<div class="pull-left">'+name+'</div>'  +                                
    '</div>';
    return content;
	}
	function save(){
		var selectid="";
		var selectName="";
		var selectUserId="";
		var str = "";
		var temp =new Array();
		for(var key in selectUser){
			selectid = (selectid==""?key:selectid+","+key);
			 if('${pageFlag}' == '1'){
				str = selectUser[key];
				temp =  str.split(":");
				selectName = (selectName ==""?temp[0]:selectName+","+temp[0]);
				selectUserId = (selectUserId ==""?temp[1]:selectUserId+","+temp[1]);
			} else {
				selectName = (selectName ==""?selectUser[key]:selectName+","+selectUser[key]);
			} 
		}
		if ('${pageFlag}' == '1') {
			window.parent.common.setUser(selectid,selectName,selectUserId);	
		}else {
			window.parent.common.setUser(selectid,selectName);	
		}
		 
		popup.layerClose();
	}
		
	function setOldIds() {//加载已选中人
		var ids='${oldIds}';
		var oldIds='${oldIds}'.split(',');
		if(window.parent.olduser!=null&&window.parent.olduser!="") {
			ids=window.parent.olduser;
			oldIds=window.parent.olduser.split(',');
		}
		if ('${pageFlag}' == '1') {
			var paramTo = {ids:ids,pageFlag:1};
		} else {
			var paramTo = {ids:ids};
		}
		ajax.remoteCall("${ctxPath}/servlet/userex?action=getUser",paramTo,function(result) {
			var data=result.data;
			for (var key in data ) {
				selectUser[key]=data[key];
			}
			selectUserlist();
		});	
		
	}
	
	 function initData () {
		$("#searchForm").initTableEx({
			mars : 'user.getUser',
			id : 'tableHead',
			height: 'full-140',
			limit:15,
			page:true,
        	limits: [15,30,50,100],
			cols : [ [
					{
						field : '',
						title : getI18nValue('选择'),
						type : 'checkbox'
					},
					{
						field : 'ID',
						align : 'center',
						title : getI18nValue('序号'),
						type:'numbers'
					},
					{
						field : 'AGENT_PHONE',
						align : 'center',
						title : getI18nValue('工号'),
						minWidth: '120',
						sort: true
					},
					{
						field : 'USER_NAME',
						align : 'center',
						title : getI18nValue('员工名称'),
						minWidth: '120',
					},
					{
						field : 'USER_ACC',
						align : 'center',
						title :getI18nValue('员工账号'),
						minWidth: '120',
					} 
				   ] ],
				done: function(res, page, count){
					$.each(res.data, function(k, row) {
						var id = row.USER_ACC;
						if(selectUser[id]!=null){
					        var index= res.data[k]['LAY_TABLE_INDEX'];
					        $('tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true);
					        $('tr[data-index=' + index + '] input[type="checkbox"]').next().addClass('layui-form-checked');								} 
					})
					table.on('checkbox(tableHead)', function(obj){
						var type = obj.type;
						if('all'== type&&obj.checked){
							var checkStatus = table.checkStatus('tableHead');
							var checkedRecord=checkStatus.data;
							for(var i = 0;i<checkedRecord.length;i++){
							    update(checkedRecord[i],true);
							}
						}else if('all'==type&&!obj.checked){
							selectUser={};
							selectUserlist();
						}else if (!type){
						    update(obj.data,obj.checked);
						}else{
							update(obj.data,obj.checked);
						}
				    });

			    }
			});
		}

	 	var commonTree = {
         	// 树div元素id
         	el: 'ztree',
         	// mars请求
         	mars: 'dept.getDeptZtree',
         	// 请求参数
        		params: {
        			
        		},
        		// id对应的数据库字段
        		idKey: 'ID',
        		// 父id对应的数据库字段
        		parentKey: 'PARENT_ID',
        		// 第一级目录的parentId
        		firstParentCode: '-1',
        		// 节点的数据格式转换
        		initNode: function(data) {
                 data.name = data.NAME;
                 data.id = data.ID;
                 return data;
        		},
        		// 第一个节点
        		firstNode: {
        			id: '-1',
        			name: getI18nValue('组织架构')
        		},
        		// 初始化树
        		initTree: function(setting) {
        			commonTree.getData(function(data) {
            			var $el = $('#'+commonTree.el);
            			var config = $.extend({}, $el.data('setting'), setting);
            			commonTree.firstNode.children = data || [];
            			$.fn.zTree.init($el, config, commonTree.firstNode);
            			if (setting && setting.expandAll) {
	                        var ztreeObject = $.fn.zTree.getZTreeObj(commonTree.el);
	                        if (ztreeObject != null) {
	                            ztreeObject.expandAll(true);
	                        }
            			}
             	});
        		},
         	handleData: function(datas, targetCode) {
                 var result = [];
                 for (var i in datas) {
                 	var data = datas[i];
                 	if (!data.ID) { continue; }
                     var resCode = data[commonTree.idKey];
                     var parentCode = data[commonTree.parentKey];
                     if ((targetCode==null && parentCode == commonTree.firstParentCode) || (targetCode && targetCode == parentCode)) {
                         datas.splice(i, 1, {});
                         data = commonTree.initNode(data);
                         data.children = commonTree.handleData(datas, resCode);
                         result.push(data);
                     }
                 }
                 return result;
             },
             getData: function(callback) {
                 var controls = commonTree.mars;
                 ajax.daoCall({
                     params: commonTree.params,
                     controls: [controls]
                 }, function(data) {
                     if (data[controls]) {
                         data = data[controls].data;
                         callback && callback(data);
                     }
                 })
             }
         }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>