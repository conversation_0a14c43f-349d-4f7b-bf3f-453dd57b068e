<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="添加节假日"></title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="" data-pk="" method="post"  autocomplete="off" data-mars-prefix="" >
				  <input type="hidden" name="noticeType.NOTICE_TYPE_ID" value="">
				  <table class="table  table-edit table-vzebra mt-20" >
	                    <tbody>
		                     <tr>
			                        <td class="required" width="60px" i18n-content="日期范围"></td>
			                        <td><input type="text" id="day" name="day" data-rules="required"  class="form-control input-sm" maxlength="50"></td>
		                     </tr>
		                     <tr>
			                        <td class="required" width="60px" i18n-content="节假日名称"></td>
			                        <td>
			                            <input type="text" name="holidayname" data-rules="required"  class="form-control input-sm" maxlength="50">
			                        </td>
		                     </tr>	
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="addholiday.ajaxSubmitForm();" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
    jQuery.namespace("addholiday");
    $(function(){
		$("#editForm").render(); 
	});

    
    addholiday.ajaxSubmitForm = function(){
		 if(form.validate("#editForm")){
			addholiday.insertData(); 
		 };
	}
    
    addholiday.insertData = function() {
		var data = form.getJSONObject("#editForm");
		ajax.remoteCall("${ctxPath}/servlet/calendar?action=addHoliday",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1},function(){
					holiday.query();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
    layui.use([ 'laydate', 'form' ], function() {
		var laydate = layui.laydate, form = layui.form;
		laydate.render({
			elem : '#day',
			range : true
		});
	});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>