<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title><span i18n-title="多语言管理" ></span></title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="multiLangForm" data-mars="multiLangDao.edit" data-pk="" method="post"
		autocomplete="off" data-mars-prefix="multiLang.">
		<input type="hidden" name="multiLang.ID" id="ID" value="${param.id }">
		<table class="table  table-edit table-vzebra mt-10">
			<tbody>
				<tr>
					<td class="required" width="60px" i18n-content="模块名"></td>
					<td><input  id="MODULE_NAME" data-rules="required"   maxlength="30" name="multiLang.MODULE_NAME" class="form-control input-sm" type="text" value="common"></td>
				</tr>
				<tr>
					<td class="required" width="60px" i18n-content="键值"></td>
					<td><input id="LANG_KEY" data-rules="required"   maxlength="80" name="multiLang.LANG_KEY" class="form-control input-sm" type="text"></td>
				</tr>
			<!-- 	<tr>
					<td class="required"  width="60px" i18n-content="中文"></td>
					<td><input id="CN" data-rules="required"    maxlength="40" name="multiLang.CN" class="form-control input-sm" type="text"></td>
				</tr>
				<tr>
					<td  width="60px" i18n-content="英文"></td>
					<td><input id="EN"  maxlength="40" name="multiLang.EN" class="form-control input-sm" type="text"></td>
				</tr> -->
				<tr>
					<td colspan="2" style="text-align: left">
						<span i18n-content="说明:"></span><br>
						<span i18n-content="1、多语言配置用于国际化显示各界面上的文字"></span><br>
						<span i18n-content="2、模块名为由各模块定制，如cc-base等,对于通用的键值可以放到通用模块common里"></span><br>
						<span i18n-content="3、每个模块下不能存在同名的键值"></span><br>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="multiLang.ajaxSubmitForm()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		requreLib.setplugs('wdate');
		jQuery.namespace("multiLang");
		
		$(function(){
			$("#multiLangForm").render({success : function(result){}}); 
		});
		multiLang.ajaxSubmitForm = function(){
				if(!form.validate("#multiLangForm")){
					return;
				};
				 var data = form.getJSONObject("multiLangForm");
					ajax.remoteCall("${ctxPath}/servlet/multiLang?action=save",data,function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon: 1});
								layer.closeAll();
								window.location.reload(); 

							}else{
								layer.alert(result.msg,{icon: 5});
							}
						}
					);
		 }
		 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>