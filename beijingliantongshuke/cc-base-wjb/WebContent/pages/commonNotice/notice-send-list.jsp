<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="通知发送记录"></title>
	<style type="text/css">
		a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" id="pushRecordForm">
		<input type="hidden" name="recordId" value="${param.id}"/>
		<div class="ibox-title clearfix">
			<div class="form-group" id="divId">
				<div class="input-group input-group-sm">
					<span class="input-group-addon" i18n-content="通知类型"></span>
					<select class="form-control input-sm" name="TYPE" style="width:152px;" data-mars="common.getDict(CC_BASE_NOTICE_TYPE)">
						<option value="" i18n-content="请选择"></option>
					</select>
				</div>
				<div class="input-group input-group-sm">
					<span class="input-group-addon" i18n-content="发送状态"></span>
					<select class="form-control input-sm" name="STATUS" style="width:152px;" data-mars="common.getDict(SEND_STATUS)">
						<option value="" i18n-content="请选择"></option>
					</select>
				</div>
				
				<div class="input-group input-group-sm">
					<button type="button" class="btn btn-sm btn-default" onclick="OrderPushRecord.searchData('1')"><span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span></button>
				</div>
				<div class="input-group input-group-sm">
			       <button type="button" class="btn btn-sm btn-default" onclick="OrderPushRecord.reset()">
			           <span class="glyphicon glyphicon-repeat"></span><span i18n-content="重置"></span> 
			       </button>
		        </div>
			</div>
		</div>
		<div class="ibox-content">
			<table id="pushRecordList" ></table>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("OrderPushRecord");
		$(function(){
			$("#pushRecordForm").render({success:function(){
				requreLib.setplugs('layui',function(){
					OrderPushRecord.loadData();
				})
			}});
		});
		//初始化加载数据
		OrderPushRecord.loadData = function() {
			$("#pushRecordForm").initTableEx({
				mars:"noticeRecord.getRecordPush",
				id:"pushRecordList",
				height: 'full-400',
				page: false,
				cols: [
					[
						{field:'NOTICE_TYPE',title:'通知类型',minWidth:120,align:'center',templet:function(row){
							return getDictTextByCode('CC_BASE_NOTICE_TYPE',row.NOTICE_TYPE);
						}},
						{field:'RECEIVER_NUM',title:'接收号码',minWidth:150,align:'center'},
						{field:'SEND_DESC',title:'内容', minWidth: 400},
						{field:'SEND_STATUS',title:'发送状态',align:'center',minWidth:100,templet:function(row){
							return getDictTextByCode('SEND_STATUS',row.SEND_STATUS);
						}},
						{field:'CREATE_TIME',title:'发送时间',align:'center',minWidth:160,sort:true}
					]
				]
			});
		}
		//查询
		OrderPushRecord.searchData = function(flag){
			if(flag=="1"){
				$("#pushRecordForm").queryData({id:'pushRecordList',page:{curr:1}});
			}else{
				$("#pushRecordForm").queryData({id:'pushRecordList'});
			}
		}
		//重置
		OrderPushRecord.reset=function(){
			$("#divId input").val("");
	    	$("#divId select").val("");
		};
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>