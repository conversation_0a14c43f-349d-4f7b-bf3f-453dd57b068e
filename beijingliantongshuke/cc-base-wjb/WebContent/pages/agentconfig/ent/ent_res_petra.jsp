<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>磐石资源管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5><span class="glyphicon glyphicon-user"></span> 磐石资源列表</h5>
             		          <div class="input-group input-group-sm">
								    <span class="input-group-addon">节点名称</span>	
									<input type="text" name="petraName" class="form-control input-sm" style="width:140px">
							  </div>
		        		      <div class="input-group input-group-sm hidden">
								 <span class="input-group-addon">节点状态</span>	
								 <select id="petraState">
								     <option value="0">正常</option>
			                         <option value="1">无法连接</option>
			                     </select>
						      </div>
							  <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="PetraRes.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="petraRes.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">节点名称</th>
								      <th class="text-c">网关</th>
								      <th class="text-c">CCBAR</th>
								      <th class="text-c">中继资源</th>
								      <th class="text-c">可分配中继资源</th>
								      <th class="text-c">已分配中继资源</th>
								      <th class="text-c">节点状态</th>
								      <th class="text-c">录音文件路径</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:PETRA_NAME}}</td>
											<td>{{if PETRA_IP}}{{:PETRA_IP}}:{{:PETRA_PORT}}{{/if}}</td>
											<td>{{if CCBAR_IP}}{{:CCBAR_IP}}:{{:CCBAR_PORT}}{{/if}}</td>
											<td>{{:RES_COUNT}}</td>
											<td>{{:VALID_COUNT}}</td>
                                            <td>{{:USED_RES_COUNT}}</td>
											<td>{{getText:PETRA_STATE '#petraState'}}</td>
                                            <td>{{:RECORD_FILE_URL}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("PetraRes");
		PetraRes.searchData=function(){
			$("#searchForm").searchData();
		}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>