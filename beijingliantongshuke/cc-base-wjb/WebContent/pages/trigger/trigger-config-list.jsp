<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title  i18n-content="通知配置列表"></title>
	<style type="text/css">
		.layui-form-label{float:left;display:block;padding:9px 15px;width:125px;font-weight:400;line-height:20px;text-align:right}
		.layui-card {
		    margin-bottom: 15px;
		    border-radius: 2px;
		    background-color: #fff;
		}
		.custIcon {
	    	color: #31b0d5;
	    	font-size: 17px;
	    	cursor:pointer;
	    }
		.disClick {pointer-events:none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false;" autocomplete="off" >
		<input type="hidden" name="ID" id="ID">
		<select id="condition" style="display:none" data-mars="triggerConfigDao.conditionAllDict"></select>
		<select id="noticeObj" style="display:none" data-mars="triggerConfigDao.objDict"></select>
		<select id="noticeUser" style="display:none" data-mars="triggerConfigDao.userDict"></select>
		<select id="noticeSkill" style="display:none" data-mars="triggerConfigDao.skillDict"></select>
		<select id="noticeDept" style="display:none" data-mars="triggerConfigDao.deptDict"></select>
		<select id="noticeWorkGroup" style="display:none" data-mars="triggerConfigDao.workGroupDict"></select>
    	<div class="ibox shadow">
        	<div class="ibox-title clearfix cust-hide" id="divId">
       			<div class="layui-col-md12">
   					<div class="layui-card" style="box-shadow: none;">
   						<div class="form-group">
		         			<h5>
		         				<span  i18n-content="场景配置"></span>
		         				<i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;" 
		         				tips-content="1.第一次使用或者有新的模块部署时，需要点击初始化按钮，系统会初始化几个通用场景  
		         				<br> 2.需要配置场景，并开启场景开关才能正常发送通知 
		         				<br> 3.如需公共场景，需要前往公共触发器中配置一条记录后点击初始化
		         				<br> 4.请确认配置对应的模块已安装，若未安装则配置不生效"></i>
								<button type="button" class="btn btn-sm btn-success btn-outline mr-5" onclick="toggleMore('moduleConfigList')" i18n-content="展开"></button>
		         			</h5>
		         			<div class="input-group input-group-sm pull-right">
		         				<button type="button" class="btn btn-sm btn-danger btn-outline mr-5" onclick="triggerList.initModuleConfig()" i18n-content="初始化"></button>
							</div>
		    			</div>
       					<div class="layui-row layui-col-space10 layui-form" id="moduleConfigList" data-mars="triggerConfigDao.noticeConfigList" style="margin-top: 10px; display: none;"></div>
  					</div>
        		</div>
        		<hr style="margin: 5px -15px">
	 			<div class="form-group">
         			<h5><span  i18n-content="通知配置列表"></span></h5>
         			<div class="input-group input-group-sm pull-right">
						<EasyTag:res resId="cc-base-system-tzpz_tz"><button type="button" class="btn btn-sm btn-info btn-outline mr-5" onclick="triggerList.noticeList()" i18n-content="通知记录"></button></EasyTag:res>
						<EasyTag:res resId="cc-base-system-tzpz_tz"><button type="button" class="btn btn-sm btn-info btn-outline mr-5" onclick="triggerList.objConfig()" i18n-content="通知方式配置"></button></EasyTag:res>
						<button type="button" class="btn btn-sm btn-info btn-outline mr-5" onclick="triggerList.commonConfig()" i18n-content="公共触发器配置"></button>
						<button type="button" class="btn btn-sm btn-success btn-outline mr-5" onclick="triggerList.edit()" i18n-content="新增"></button>
					</div>
    			</div>
    			
    			<div class="form-group" id="divId">
					<div class="input-group input-group-sm all" style="width: 210px;">
						<span class="input-group-addon" i18n-content="所属场景"></span>
						<select class="form-control input-sm" name="MODULE_CODE" data-mars="triggerConfigDao.moduleDict" onchange="triggerList.renderObj(this.value)">
							<option value="" i18n-content="请选择"></option>
						</select>								  
					</div>
					<div class="input-group input-group-sm all" style="width: 210px;">
						<span class="input-group-addon" i18n-content="提醒条件"></span>
						<select class="form-control input-sm" id="conditionCode" name="CONDITION_CODE" data-mars="triggerConfigDao.conditionDict">
							<option value="" i18n-content="请选择"></option>
						</select>								  
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="triggerList.searchData('1')">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span>
						</button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="triggerList.reset()">
							<span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span>
						</button>
					</div>
				</div>
	  		</div>
	  		<div class="ibox-content">
				<table id="triggerTable"></table>
			</div>
        </div> 
	</form>
	<script type="text/html" id="moduleConfigTpl">
	{{for list}}
		<div class="layui-col-md12 layui-form-item">
			<div class="layui-col-md12">
				<label class="layui-form-label">{{getModuleName:MODULE_CODE}}</label>
				<label class="layui-form-label" i18n-content="提醒开关"></label>
				<div class="layui-input-inline">
					<input type="checkbox" name="RECE_NOTICE" lay-filter="receNoticeFilter" data-module-code="{{:MODULE_CODE}}" {{if RECE_NOTICE == "Y" }}checked{{/if}} lay-skin="switch" i18n-lay-text="开启|关闭">
				</div>
				<label class="layui-form-label" i18n-content="邮箱发送账号"></label>
				<div class="layui-input-inline">
     				<select name="EMAIL_ACC" lay-filter="emailAccFilter" data-mars="triggerConfigDao.getEmailList" data-module-code="{{:MODULE_CODE}}" data-value="{{:EMAIL_ACC}}" lay-verType="tips">
  						<option value="" i18n-content="请选择"></option>
					</select>
				</div>
				<label class="layui-form-label required" i18n-content="短信发送渠道"></label>
				<div class="layui-input-inline">
					<select name="MSG_CHANNEL" lay-filter="msgChannelFilter" data-mars="triggerConfigDao.channelList" data-module-code="{{:MODULE_CODE}}" data-value="{{:MSG_CHANNEL}}" lay-verType="tips" lay-verify="required">
						<option value="" i18n-content="请选择"></option>
					</select>
				</div>
			</div>
		</div>
	{{/for}}
	</script>
	<script type="text/html" id="operationTpl">
  		<a class="layui-btn layui-btn-xs" lay-event="triggerList.triggerEdit" i18n-content="编辑"></a>
  		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="triggerList.triggerDel" i18n-content="删除"></a>
	</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("triggerList");
		//缓存通知配置组
		var noticeGroups = {};
		
		triggerList.reset = function() {
			$("#divId select").val("");
	    	$("#divId input").val("");
		}
		triggerList.searchData=function(flag){
			$("#searchForm").queryData({id:'triggerTable',page: false});
		}
		triggerList.loadData=function(){
        	$("#searchForm").initTableEx({
				mars:'triggerConfigDao.getConfigList',
				limit:200,
				page:false,
				id:'triggerTable',
				title:getI18nValue('通知配置列表'),
				cellMinWidth:60,
				loading:true,
				cols: [
					[
						{width:50,align:'center',title:'序号',type:'numbers'},
						{minWidth:100,align:'center',field:'MODULE_CODE', title:'所属场景', templet: function(row) {
							return getDictTextByCode('SYSTEM_MODULE', row.MODULE_CODE);
						}},
						{minWidth:100,align:'center',field:'status', title:'场景开关',templet:function(row){
							if(row.RECE_NOTICE == 'N' ){
								return '<i title="'+getI18nValue('所属场景未开启,请到上面的场景配置中开启')+'" class="layui-icon layui-icon-close custIcon" ></i>';
							} else {
								return '<i title="'+getI18nValue('已开启')+'" class="layui-icon layui-icon-ok-circle custIcon" ></i>';
							}
						}},
						{minWidth:100,align:'center',field:'criteria', title:'提醒条件',templet:function(row){
							return $("#condition").find("option[value="+ row.criteria + row.MODULE_CODE +"]").text();
						}},
					    {minWidth:150,align:'center',field:'noticeObjType', title:'提醒类型', templet: function(row) {
					    	return getDictTextByCode('NOTICE_OBJ_TYPE', row.noticeObjType);
					    }},
					    {minWidth:200,align:'center',field:'noticeObj', title:'提醒对象', templet:function(row){
					    	for(var key in row){ 
					    		//如果是通知对象需要加前缀
					    		var preFix = key=="noticeObj"?(row.MODULE_CODE + row['criteria']+","):"";
					    		if(key!='noticeObjType' && key.startsWith('notice')){
					    			if(row[key]){
					    				var html = '';
					    				var keys = row[key].split(",");
					    				for(var i=0;i<keys.length;i++){
					    					console.log(row.MODULE_CODE + preFix+keys[i]);
					    					html += '<span class="layui-badge layui-bg-green mr-5">' +
					    						$("#"+key).find("option[value='" + preFix+keys[i] +"']").text() + '</span>';
					    				}
					    				return html;
					    			}else{
					    				return "";
					    			}
					    		}
					    	}
					    }},
					    {minWidth:150,align:'center',field:'way', title:'提醒方式', templet:function(row){
					    	var result = "";
					    	var ways = row.way.split(",");
					    	$.each(ways,function(k,v){
					    		result += (getDictTextByCode('CC_BASE_NOTICE_TYPE', v)+",");
					    	})
					    	return result.slice(0,-1);
					    }},
					    {minWidth:150,align:'center',field:'HANDLE', title:'操作', templet: function(row){
					    	var temp  =
						    	'<span href="javascript:void(0)" class="layui-btn layui-btn-xs" onclick="triggerList.edit(this, \'' + row.noticeId + '\', \'' + row.MODULE_CODE + '\',\''+row.criteria+'\')">'+getI18nValue("编辑")+'</span> '
						    	+' <span href="javascript:void(0)" class="layui-btn layui-btn-xs layui-btn-danger" onclick="triggerList.del(\'' + row.noticeId + '\', \'' + row.MODULE_CODE + '\')">'+getI18nValue("删除")+'</span> ';
						    	return temp;
						    }}
			    	]
				],done: function(res, curr, count) {
					if(res.data) {
						for(var d of res.data) {
							noticeGroups[d.noticeId] = d;
						}
					}
					execI18n();
				}
			});
		}
		
		triggerList.initModuleConfig = function() {
			ajax.remoteCall("${ctxPath}/servlet/triggerConfig?action=initModuleConfig", {}, function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						window.location.reload();
					});
				}else{
					layer.msg(result.msg,{icon: 5,time:1200});
				}
			});
		}
		
		triggerList.commonConfig = function() {
			popup.layerShow({
				type : 2,
				title : getI18nValue('公共触发器配置'),
				shadeClose:false,
				offset : '0px',
				area : [ '100%', '100%' ],
				url:"${ctxPath}/pages/trigger/common/common-config.jsp"
			});
		}
		
		triggerList.noticeList = function() {
			popup.openTab({url:'${ctxPath}/pages/commonNotice/notice-list.jsp','title':getI18nValue('通知发送记录')});
		}
		
		triggerList.objConfig = function() {
			popup.openTab({url:'${ctxPath}/pages/commonNotice/noteConfig-list.jsp','title':getI18nValue('通知方式配置')});
		}
		
		triggerList.del = function(noticeId, moduleCode) {
  			layer.confirm(getI18nValue('确定要删除该配置吗？'), {
				  btn: [getI18nValue('确定'), getI18nValue('取消')] ,icon:5
				}, function(index, layero){
					ajax.remoteCall("${ctxPath}/servlet/triggerConfig?action=triggerDel", {noticeId: noticeId, moduleCode: moduleCode}, function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon: 1,time:1200},function(){
								triggerList.searchData(false);
							});
						}else{
							layer.msg(result.msg,{icon: 5,time:1200});
						}
					});
				}, function(index){
				  layer.close(index);
			    }
			);  
		}
		
		triggerList.edit = function(obj, noticeId,moduleCode,condition) {
			var param = {};
			if(obj){// 修改
				param.conditionCode = condition;
				param.moduleCode = moduleCode;
				param.noticeId = noticeId;
			}
			popup.layerShow({
				type : 1,
				title : getI18nValue('编辑通知配置'),
				shadeClose:false,
				offset : '20px',
				area : [ '705px', '666px' ],
				url:"${ctxPath}/pages/trigger/trigger-config-edit.jsp",
				data: param
			});
		}
		
		//级联
		triggerList.renderObj = function(value){
			$("#conditionCode").data("moduleCode", value);
			$("#conditionCode").render();
		}
		
		function receNoticeFilter(obj) {
			var data = {
				moduleCode: $(obj.elem).data('moduleCode'),
				receNotice: (obj.elem.checked)?'Y':'N'
			}
			recordEdit(data);
		}
		function emailAccFilter(obj) {
			console.log(obj)
			var data = {
				moduleCode: $(obj.elem).data('moduleCode'),
				emailAcc: obj.value
			}
			recordEdit(data);
		}
		function msgChannelFilter(obj) {
			console.log(obj)
			var data = {
				moduleCode: $(obj.elem).data('moduleCode'),
				msgChannel: obj.value
			}
			recordEdit(data);
		}
		
		function recordEdit(data) {
			ajax.remoteCall("${ctxPath}/servlet/triggerConfig?action=recordEdit", data, function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200});
				}else{
					layer.msg(result.msg,{icon: 5,time:1200});
				}
			});
		}
		
		//展开
		function toggleMore(id) {
			$("#" + id).slideToggle('fast');
		}
		
		var layform;
		$(function() {
			layui.use(['form', 'element'], function(){
				layform = layui.form;
				triggerList.loadData();
			});
			
			$("#searchForm").render({success: function(result) {
				if(result['triggerConfigDao.noticeConfigList']) {
					var noticeConfigList = result['triggerConfigDao.noticeConfigList'].data;
					var moduleConfigTpl = $.templates("#moduleConfigTpl");
					var moduleConfigHtml = moduleConfigTpl.render({
						list: noticeConfigList
					});
					$("#moduleConfigList").html(moduleConfigHtml);
					$("#moduleConfigList").render({success: function() {
						layform.render();
					}});
				}
				
				layui.use(['form', 'element'], function(){
					layform = layui.form;
					triggerList.loadData();
				});
			}});
		});
		
		$.views.converters("getModuleName", function(val) {
			return getDictTextByCode('SYSTEM_MODULE', val);
		});
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>