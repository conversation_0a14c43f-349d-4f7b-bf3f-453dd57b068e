<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="话术编辑"></title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="TaskWordDao.record"  autocomplete="off" data-mars-prefix="taskWord.">
				  <input type="hidden" name="taskWord.WORD_ID" class="form-control input-sm" value="${param.wordId}">
				  <table class="table table-edit table-vzebra mt-10">
	                    <tbody>
		                      <tr>
			                        <td class="required" width="30px" i18n-content="话术标题"></td>
			                        <td><input type="text" maxlength="100" name="taskWord.WORD_TITLE" data-rules="required" class="form-control input-sm" required></td>
		                     </tr>
		                      <tr>
			                        <td class="required" width="30px" i18n-content="发布人"></td>
			                        <td><input type="text" name="taskWord.CREATOR" data-rules="required" data-mars="common.userName" class="form-control input-sm" required readonly></td>
		                     </tr>
		                     <tr>
			                        <td class="required" i18n-content="话术内容"></td>
			                        <td>
			                       		 <div id="editor">
									        <p i18n-content="欢迎使用"> <b>wangEditor</b></p>
									    </div>
			                           <textarea id="wordText" style="height: 700px;display: none;" class="form-control input-sm" name="taskWord.WORD_TEXT"></textarea>
			                        </td>
		                      </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="TaskWordEdit.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/wangEditor/wangEditor.min.js"></script>
	
	<script type="text/javascript">
	
	 var E = window.wangEditor
     var editor = new E('#editor')
	 editor.customConfig.onchange = function (html) {
         $("#wordText").val(html)
     }
     editor.create()
	
	
	jQuery.namespace("TaskWordEdit");
	
	TaskWordEdit.wordId='${param.wordId}';
	
	$(function(){
		$("#easyform").render({success:function(res){
			 if (res["TaskWordDao.record"].data.WORD_TEXT != undefined) {
				$("#wordText").val(decodeURI(res["TaskWordDao.record"].data.WORD_TEXT));
			 } 
			 editor.txt.html($("#wordText").val());
		}});  
	});
	TaskWordEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 if(TaskWordEdit.wordId==''){
				 TaskWordEdit.insertData(); 
			 }else{
				 TaskWordEdit.updateData(); 
			 }
		 };
	}
	TaskWordEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			data["taskWord.WORD_TEXT"] = encodeURI(data["taskWord.WORD_TEXT"]);
			var wordText = $("#wordText").val();
			if(typeof wordText ===null || wordText==="" || wordText=== 'undefined' || wordText==="<p><br></p>"){
				layer.alert("话术内容不能为空！",{icon: 5});
			}else{
				ajax.remoteCall("${ctxPath}/servlet/taskWord?action=add",data,function(result) { 
					if(result.state == 1){
						layer.closeAll();
						TaskWord.loadData();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				  }
				);
			}
		}
	TaskWordEdit.updateData = function(){
		var data = form.getJSONObject("#easyform");
		data["taskWord.WORD_TEXT"] = encodeURI(data["taskWord.WORD_TEXT"]);
		var wordText = $("#wordText").val();
		if(typeof wordText ===null || wordText==="" || wordText=== 'undefined'|| wordText==="<p><br></p>"){
			layer.alert("话术内容不能为空！",{icon: 5});
		}else{
			ajax.remoteCall("${ctxPath}/servlet/taskWord?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						layer.closeAll();
						TaskWord.loadData();
					});
				}else{
					layer.alert(result.msg);
				}
			  }
			);
		}
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>