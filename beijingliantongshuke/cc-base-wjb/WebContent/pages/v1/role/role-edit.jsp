<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head"><title>角色编辑</title></EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="v1_roleEnt.getObject"  method="post"  autocomplete="off" data-mars-prefix="role.">
				  <table class="table table-edit table-vzebra" style="margin-top: 10px;">
	                    <tbody>
		                     <tr>
		                            <td class="required" width="90px">角色ID</td>
			                        <td>
			                        	  <c:choose>
			                        	  	<c:when test="${empty param.roleId }">
			                      		 		 <input type="text" name="role.ROLE_ID" data-rules="required" data-mars="commonEnt.getOrderId" class="form-control input-sm">
			                        	  	</c:when>
			                        	  	<c:otherwise>
					                      		  <input type="text" readonly="readonly" name="role.ROLE_ID" data-rules="required" value="${param.roleId }" class="form-control input-sm">
			                        	  	</c:otherwise>
			                        	  </c:choose>
			                        </td>
		                     </tr>
		                     <tr>
		                            <td class="required" width="120px">角色名称</td>
			                        <td><input type="text" name="role.ROLE_NAME" maxlength="20" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                     		<td>角色描述</td>
			                        <td><textarea name="role.ROLE_DESC" maxlength="50" rows="3" class="form-control input-sm"></textarea></td>
			                 </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="RoleEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("RoleEdit");
	
	RoleEdit.roleId='${param.roleId}';
	
	$(function(){
		$("#easyform").render({data:{pk:'${param.roleId}'}});
	});
	 RoleEdit.ajaxSubmitForm = function(){
		 if(form.validate("easyform")){
			 if(RoleEdit.roleId==''){
				 RoleEdit.insertData(); 
			 }else{
				 RoleEdit.updateData(); 
			 }
		 };
	 }
	 RoleEdit.insertData = function() {
			var data = form.getJSONObject("easyform");
			ajax.remoteCall("${ctxPath}/servlet/v1_roleEnt?action=addRole",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						Role.searchData();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	 }
	 RoleEdit.updateData = function(){
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("${ctxPath}/servlet/v1_roleEnt?action=modRole",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,offset:'100px',time:1200},function(){
					Role.searchData();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>