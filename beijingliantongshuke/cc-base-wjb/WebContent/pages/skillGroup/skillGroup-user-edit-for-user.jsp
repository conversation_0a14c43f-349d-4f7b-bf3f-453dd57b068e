<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>技能组用户编辑</title>
	</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" method="post"  autocomplete="off" data-mars-prefix="gkuser.">
				  <input type="hidden" name="mgrType" class="form-control input-sm" value="1">
				  <input type="hidden" name="gkuser.USER_ID" class="form-control input-sm" value="${param.userId}">
				  <input type="hidden" name="gkuser.SKILL_GROUP_ID" class="form-control input-sm" value="${param.skillGroupId}">
				  <input type="hidden" name="gkuser.BUSI_ORDER_Id" class="form-control input-sm" value="${param.busiOrderId}">
				  <table class="table table-edit table-vzebra mt-10">
	                    <tbody>
		                      <tr>
			                        <td class="required" style="width: 100px" i18n-content="排序"></td>
			                        <td><input type="number" name="gkuser.IDX_ORDER" data-rules="required" value="${param.idxOrder }" class="form-control input-sm"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="GroupUserEidt.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("GroupUserEidt");
	
	GroupUserEidt.ajaxSubmitForm = function(){
		if(form.validate("#editForm")){
			var data = form.getJSONObject("#editForm");
			var url = "${ctxPath}/servlet/skillGroup?action=updateGroupUserOrder&skillGroupName=${param.skillGroupName}&userAcc=${param.userAcc}";
			ajax.remoteCall(url,data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					userSkill.loadData();
				}else{
					layer.alert(result.msg);
				}
			});
		};
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>