
/*==============================================================*/
/* Table: C_BO_AUTO_FORM                                        */
/*==============================================================*/
create table C_BO_AUTO_FORM
(
   ID                   varchar(64) not null comment 'ID',
   FORM_NAME            varchar(100) comment '������',
   FORM_DESC            varchar(200) comment '������',
   FORM_STYLE_ID        varchar(64) comment 'ģ����ʽ',
   FORM_CONTENT         text comment '��ԭʼ�ı�',
   FORM_PARSE           text comment '����������',
   CREATE_NAME          varchar(50) comment '����������',
   CREATE_ACC           varchar(30) comment '�������˺�',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   CREATE_DEPT_CODE     varchar(30) comment '�����˲��ű��',
   UPDATE_ACC           varchar(30) comment '�޸����˺�',
   UPDATE_DATE          varchar(19) comment '�޸�ʱ��',
   MAIN_TABLE_SOURCE    varchar(50) comment '������Դ',
   PROCESS_ID           varchar(64) comment '���̼�¼ID',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   primary key (ID)
);

alter table C_BO_AUTO_FORM comment '�Զ����';

/*==============================================================*/
/* Index: IDX_CBAF_PROCESSID                                    */
/*==============================================================*/
create index IDX_CBAF_PROCESSID on C_BO_AUTO_FORM
(
   PROCESS_ID
);

/*==============================================================*/
/* Index: IDX_CBAF_EPCODE                                       */
/*==============================================================*/
create index IDX_CBAF_EPCODE on C_BO_AUTO_FORM
(
   ENT_ID,
   BUSI_ORDER_ID,
   FORM_NAME
);

/*==============================================================*/
/* Table: C_BO_AUTO_FORM_DB                                     */
/*==============================================================*/
create table C_BO_AUTO_FORM_DB
(
   ID                   varchar(64) not null comment 'ID',
   CREATE_NAME          varchar(50) comment '����������',
   CREATE_BY            varchar(30) comment '�������˺�',
   UPDATE_NAME          varchar(50) comment '�޸�������',
   UPDATE_BY            varchar(30) comment '�޸����˺�',
   CREATE_DEPT_CODE     varchar(30) comment '��������',
   CREATE_DATE          varchar(19) comment '����ʱ��',
   UPDATE_DATE          varchar(19) comment '�޸�ʱ��',
   DB_NAME              varchar(50) comment '����ԴӢ������,�ֹ���д',
   DB_CH_NAME           varchar(100) comment '����Դ��������,�ֹ���д',
   DB_TYPE              varchar(20) default '1' comment '����Դ���ͣ������ֵ� AUTO_FORM_DB_TYPE  table-���ݱ� sql-��̬SQL',
   DB_SCHEMA            varchar(50) comment '��ر��������ݿ�',
   DB_KEY               varchar(50) comment '����Դkey�����ݿ���ʶ',
   DB_TABLE_NAME        varchar(50) comment '���ݿ���������ݿ����',
   DB_DYN_SQL           text comment '��̬��ѯSQL',
   TB_DB_KEY            varchar(50) comment '�����Դ(��̬SQLʱʹ��)',
   TB_DB_TABLE_NAME     varchar(100) comment '����ݱ�(��̬SQLʱʹ��),��̬sql���������',
   AUTO_FORM_ID         varchar(64) comment '��ID',
   STATUS               varchar(20) default '01' comment '�����ֵ䣬ENABLE_STATUS  01-���� 02-����
            
            �����ֵ䣺STATUS_ENABLE_OR_NOT',
   SOURCE               varchar(20) default '1' comment '�����ֵ䣬AUTO_FORM_DB_SOURCE  1-ϵͳͨ������Դ 2-�û��Զ�������Դ',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   IS_MAIN              varchar(20) default 'N' comment '�Ƿ�Ϊ������Դ,SF_YN',
   primary key (ID)
);

alter table C_BO_AUTO_FORM_DB comment '�Զ��������Դ';

/*==============================================================*/
/* Index: IDX_CBAFD_SOURCE                                      */
/*==============================================================*/
create index IDX_CBAFD_SOURCE on C_BO_AUTO_FORM_DB
(
   ENT_ID,
   BUSI_ORDER_ID,
   SOURCE
);

/*==============================================================*/
/* Table: C_BO_AUTO_FORM_DB_FIELD                               */
/*==============================================================*/
create table C_BO_AUTO_FORM_DB_FIELD
(
   ID                   varchar(64) not null comment 'ID',
   CREATE_NAME          varchar(50) comment '����������',
   CREATE_ACC           varchar(30) comment '�������˺�',
   CREATE_DATE          varchar(19) comment '����ʱ��',
   UPDATE_NAME          varchar(50) comment '�޸�������',
   UPDATE_ACC           varchar(30) comment '�޸����˺�',
   UPDATE_DATE          varchar(19) comment '�޸�ʱ��',
   CREATE_DEPT_CODE     varchar(30) comment '��������',
   AUTO_FORM_DB_ID      varchar(64) comment '����ԴID',
   FIELD_NAME           varchar(30) comment '�ֶ���',
   FIELD_TEXT           varchar(100) comment '�ֶ��ı�',
   ENT_ID               varchar(30) comment '��ҵID',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   FIELD_TYPE           varchar(20) default '3' comment '�ֶ�����, 1-���� 2-��� 3-��ͨ',
   REF_TABLE_NAME       varchar(50) comment '����������������ʹ�ã������ı���������������Դ��Ӧ�ı�',
   REF_TABLE_FIELD      varchar(50) comment '�����ֶ�',
   primary key (ID)
);

alter table C_BO_AUTO_FORM_DB_FIELD comment '�Զ��������Դ�ֶ���Ϣ';

/*==============================================================*/
/* Index: IDX_CBAFDF_FORM                                       */
/*==============================================================*/
create index IDX_CBAFDF_FORM on C_BO_AUTO_FORM_DB_FIELD
(
   AUTO_FORM_DB_ID,
   ENT_ID,
   BUSI_ORDER_ID
);

/*==============================================================*/
/* Table: C_BO_AUTO_FORM_DB_REF                                 */
/*==============================================================*/
create table C_BO_AUTO_FORM_DB_REF
(
   ID                   varchar(64) not null comment 'ID',
   AUTO_FORM_ID         varchar(64) comment '�Զ����ID',
   AUTO_FORM_DB_ID      varchar(64) comment '�Զ�������ԴID',
   LINK_TYPE            varchar(2) comment '��������',
   primary key (ID)
);

alter table C_BO_AUTO_FORM_DB_REF comment '�Զ����������Դ������';

/*==============================================================*/
/* Index: IDX_CBAFDR_REF                                        */
/*==============================================================*/
create index IDX_CBAFDR_REF on C_BO_AUTO_FORM_DB_REF
(
   AUTO_FORM_ID,
   AUTO_FORM_DB_ID
);


ALTER TABLE C_BO_BASE_ORDER ADD CALLER varchar(20)  COMMENT '�������';
ALTER TABLE C_BO_BASE_ORDER ADD SOURCE varchar(10)  COMMENT '��Դ����,call-���� media-ȫý��';
ALTER TABLE C_BO_BASE_ORDER ADD CUIBAN_NUM int default 0 COMMENT '�߰���';
ALTER TABLE C_BO_BASE_ORDER ADD OVERSEE_NUM int default 0 COMMENT '������';
ALTER TABLE C_BO_BASE_ORDER ADD NOTE_NUM int default 0 COMMENT '��ע��';
ALTER TABLE C_BO_BASE_ORDER ADD BAKUP_NUM int default 0 COMMENT '��ע��';
ALTER TABLE C_BO_BASE_ORDER ADD DEAL_ACC varchar(30)  COMMENT '��һ���ڴ�����,��һ����Ϊ�û�����ʱ�����û�������˺ţ�����Ϊ�գ����ж����;����';
ALTER TABLE C_BO_BASE_ORDER ADD IS_READ varchar(20) default 'N'  COMMENT '�Ƿ��Ѷ���SF_YN,Y-�ǣ�N-��';

ALTER TABLE C_BO_BASE_ORDER_HIS ADD CALLER varchar(20)  COMMENT '�������';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD SOURCE varchar(10)  COMMENT '��Դ����,call-���� media-ȫý��';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD CUIBAN_NUM int default 0 COMMENT '�߰���';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD OVERSEE_NUM int default 0 COMMENT '������';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD NOTE_NUM int default 0 COMMENT '��ע��';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD BAKUP_NUM int default 0 COMMENT '��ע��';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD DEAL_ACC varchar(30)  COMMENT '��һ���ڴ�����,��һ����Ϊ�û�����ʱ�����û�������˺ţ�����Ϊ�գ����ж����;����';
ALTER TABLE C_BO_BASE_ORDER_HIS ADD IS_READ varchar(20) default 'N'  COMMENT '�Ƿ��Ѷ���SF_YN,Y-�ǣ�N-��';


ALTER TABLE C_BO_ORDER_FOLLOW ADD ARRIVAL_TIME varchar(19) default 'N'  COMMENT '����ʱ��,������Ծ����������ڵ�';
ALTER TABLE C_BO_ORDER_FOLLOW ADD PLAN_DONE_TIME varchar(19) default 'N'  COMMENT 'Ӧ�����ʱ��,������Ծ����������ڵ�';
ALTER TABLE C_BO_ORDER_FOLLOW ADD TIMEOUT int default 0  COMMENT '��ʱʱ��,������Ծ����������ڵ�';



/*==============================================================*/
/* Table: C_BO_ORDER_BAKUP                                      */
/*==============================================================*/
create table C_BO_ORDER_BAKUP
(
   ID                   varchar(64) not null comment 'ID',
   ORDER_ID             varchar(64) not null comment '����ID',
   ORDER_NO             varchar(50) not null comment '�������',
   PROCESS_INSTANCE_ID  varchar(64) comment '����ʵ��ID',
   ORDER_PRO            varchar(2) not null comment '�����ֵ䣺������ע���� BO_ORDER_BAKUP_PRO  1-�ϰ�',
   CONTENT              varchar(500) not null comment '��ע����',
   BAKUP                varchar(500) comment '��ע',
   CREATE_ACC           varchar(64) not null comment '������',
   CREATE_NAME          varchar(100) comment '����������',
   CREATE_DEPT_CODE     varchar(64) comment '�����˲���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   STATUS               varchar(2) not null default '1' comment '�����ֵ䣺������ע״̬ BO_ORDER_BAKUP_STATUS 1-����� 2-���ͨ�� 3-��˲�ͨ��',
   AUDIT_ACC            varchar(64) comment '�����',
   AUDIT_NAME           varchar(100) comment '���������',
   AUDIT_DEPT_CODE      varchar(64) comment '����˲���',
   AUDIT_TIME           varchar(19) comment '���ʱ��',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��ҵҵ�񶩹�ID',
   primary key (ID)
);

alter table C_BO_ORDER_BAKUP comment '�೤���ط���Ա���ԶԹ���������ע��
�೤��ע�Ĺ�������Ҫ���,�ط���Ա��ע�Ĺ�����Ҫ�೤���';

/*==============================================================*/
/* Index: IDX_CBOB_ORDER                                        */
/*==============================================================*/
create index IDX_CBOB_ORDER on C_BO_ORDER_BAKUP
(
   ORDER_ID,
   ORDER_NO
);

/*==============================================================*/
/* Index: IDX_CBOB_STATUS                                       */
/*==============================================================*/
create index IDX_CBOB_STATUS on C_BO_ORDER_BAKUP
(
   ENT_ID,
   BUSI_ORDER_ID,
   CREATE_TIME,
   STATUS
);

/*==============================================================*/
/* Table: C_BO_ORDER_CFG                                        */
/*==============================================================*/
create table C_BO_ORDER_CFG
(
   ID                   varchar(64) not null comment 'ID',
   RECE_NOTICE          varchar(10) default 'Y' comment '�Ƿ�������֪ͨ�������ֵ䣬SF_YN Y-�ǣ�N-��',
   NOTICE_CFG           varchar(2000) comment '����֪ͨ����(JSON)',
   ORDER_TIME_CALC_TYPE varchar(10) comment '������ʱ���㷽ʽ�������������ֵ䣬1- 7*24Сʱ 2-24Сʱ,�۳������ڼ��� 3-������ʱ��,���۳������ڼ��գ�4-������ʱ��,�۳������ڼ���',
   ORDER_TIME_CFG       varchar(500) comment '��������ʱ������(JSON)',
   CREATE_USER_ACC      varchar(30) comment '�������˺�',
   CRATE_TIME           varchar(19) comment '����ʱ��',
   UPDATE_TIME          varchar(19) comment '�޸�ʱ��',
   UPDATE_USER_ACC      varchar(30) comment '�޸����˺�',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   primary key (ID)
);

alter table C_BO_ORDER_CFG comment '����ȫ������';

/*==============================================================*/
/* Index: IDX_CSOC_ENT                                          */
/*==============================================================*/
create index IDX_CSOC_ENT on C_BO_ORDER_CFG
(
   ENT_ID,
   BUSI_ORDER_ID
);



/*==============================================================*/
/* Table: C_BO_ORDER_CUIBAN                                     */
/*==============================================================*/
create table C_BO_ORDER_CUIBAN
(
   ID                   varchar(64) not null comment 'ID',
   ORDER_ID             varchar(64) not null comment '����ID',
   ORDER_NO             varchar(50) not null comment '�������',
   PROCESS_INSTANCE_ID  varchar(64) comment '����ʵ��ID',
   CREATE_ACC           varchar(64) not null comment '������',
   CREATE_NAME          varchar(100) comment '����������',
   CREATE_DEPT_CODE     varchar(64) comment '�����˲���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   BAKUP                varchar(500) comment '��ע',
   BUSI_STATUS          varchar(200) comment '��ǰ����,ȡBASE_ORDER�����BUSI_STATUS',
   HANDLE_ACC           varchar(30) comment '��ǰ�����ˣ�����Ƿָ���ģ����޴�����',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��ҵҵ�񶩹�ID',
   primary key (ID)
);

alter table C_BO_ORDER_CUIBAN comment '�����߰��¼';

/*==============================================================*/
/* Index: IDX_CBOC_ORDERID                                      */
/*==============================================================*/
create index IDX_CBOC_ORDERID on C_BO_ORDER_CUIBAN
(
   ORDER_ID,
   ORDER_NO
);



/*==============================================================*/
/* Table: C_BO_OVERSEE_APPLY                                    */
/*==============================================================*/
create table C_BO_OVERSEE_APPLY
(
   ID                   varchar(64) not null comment 'ID',
   ORDER_ID             varchar(64) not null comment '����ID',
   ORDER_NO             varchar(64) not null comment '�������',
   PROCESS_INSTANCE_ID  varchar(64) comment '����ʵ��ID',
   RECEIVE_ACC          varchar(30) not null comment '������',
   RECEIVER_NAME        varchar(50) comment '����������',
   APPLY_RESAON         varchar(200) not null comment '����ԭ��',
   BAKUP                varchar(200) comment '��ע',
   CREATE_ACC           varchar(30) comment '������',
   CREATE_NAME          varchar(100) comment '����������',
   CREATE_DEPT_CODE     varchar(30) comment '�����˲���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   STATUS               varchar(10) not null default '1' comment '�����ֵ䣺������������״̬ BO_SUPERVISE_APPLY_STATUS 1-������ 2-�Ѵ���',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��ҵҵ�񶩹�ID',
   primary key (ID)
);

alter table C_BO_OVERSEE_APPLY comment '1�������µ������빤������';

/*==============================================================*/
/* Index: IDX_CBOA_ORDER                                        */
/*==============================================================*/
create index IDX_CBOA_ORDER on C_BO_OVERSEE_APPLY
(
   ORDER_ID
);

/*==============================================================*/
/* Index: IDX_CBOA_TIME                                         */
/*==============================================================*/
create index IDX_CBOA_TIME on C_BO_OVERSEE_APPLY
(
   ENT_ID,
   BUSI_ORDER_ID,
   CREATE_TIME,
   STATUS
);

/*==============================================================*/
/* Table: C_BO_OVERSEE_INFO                                     */
/*==============================================================*/
create table C_BO_OVERSEE_INFO
(
   ID                   varchar(64) not null comment 'ID',
   ORDER_ID             varchar(64) not null comment '����ID',
   ORDER_NO             varchar(64) not null comment '�������',
   NAME                 varchar(100) not null comment '����������,�ʹ����˿��ܲ���ͬһ�ˣ��ֹ���д',
   PHONENUM             varchar(20) not null comment '��������ϵ�绰,�ʹ����˿��ܲ���ͬһ�ˣ��ֹ���д',
   DEPT_NAME            varchar(100) comment '���������ڲ���,�ʹ����˿��ܲ���ͬһ�ˣ��ֹ���д',
   BAKUP                varchar(200) comment '��ע',
   CREATE_USER          varchar(64) comment '������',
   CREATE_USER_NAME     varchar(100) comment '����������',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   OVERSEE_APPLYID      varchar(64) comment '������������ID',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��ҵҵ�񶩹�ID',
   primary key (ID)
);

alter table C_BO_OVERSEE_INFO comment '����������Ϣ�������˸��ݶ��������˵�������ж��죬����д������Ϣ';

/*==============================================================*/
/* Index: IDX_CBOI_ORDERID                                      */
/*==============================================================*/
create index IDX_CBOI_ORDERID on C_BO_OVERSEE_INFO
(
   ORDER_ID
);

/*==============================================================*/
/* Index: IDX_CBOI_ORDERNO                                      */
/*==============================================================*/
create index IDX_CBOI_ORDERNO on C_BO_OVERSEE_INFO
(
   ORDER_NO
);

/*==============================================================*/
/* Index: IDX_CBOI_APPLYID                                      */
/*==============================================================*/
create index IDX_CBOI_APPLYID on C_BO_OVERSEE_INFO
(
   OVERSEE_APPLYID
);

/*==============================================================*/
/* Table: C_BO_QUESTION_TYPE                                    */
/*==============================================================*/
create table C_BO_QUESTION_TYPE
(
   ID                   varchar(64) not null comment 'ID',
   SERVICE_CATALOG_ID   varchar(64) not null comment '����Ŀ¼ID',
   ORDER_TYPE           varchar(20) not null comment '��������, BO_ORDER_TYPE 1-Ͷ�� 2-���� 3-����
            
            �����ֵ䣺WO_WQT_ORDER_TYPE',
   CODE                 varchar(20) not null comment '���ͱ���,ϵͳ�Զ�����',
   NAME                 varchar(100) not null comment '��������',
   BAKUP                varchar(200) comment '��ע',
   BUSI_TYPE            varchar(20) not null comment '����ҵ�� BO_BUSI_TYPE  1-96770 2-96100
            
            �����ֵ䣺WO_WQT_BUSI_TYPE',
   CREATE_USER          varchar(64) comment '������',
   CREATE_USER_NAME     varchar(100) comment '����������',
   CREATE_DEPT_CODE     varchar(64) comment '���������ڲ���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   SORT_NUM             int default 1 comment '���',
   primary key (ID)
);

alter table C_BO_QUESTION_TYPE comment '����������Ҫ�����Ŀ¼���������ͽ���(�����ֵ�)�󶨣�
��ҳ���ϲ�����ѡ�����Ŀ¼����ѡ�񹤵����ͣ���ʱ����';

/*==============================================================*/
/* Index: IDX_CBQT_SERVICE                                      */
/*==============================================================*/
create index IDX_CBQT_SERVICE on C_BO_QUESTION_TYPE
(
   SERVICE_CATALOG_ID,
   BUSI_TYPE
);

/*==============================================================*/
/* Index: IDX_CBQT_CODE                                         */
/*==============================================================*/
create index IDX_CBQT_CODE on C_BO_QUESTION_TYPE
(
   ORDER_TYPE,
   CODE
);

/*==============================================================*/
/* Table: C_BO_SERVICE_CATALOG                                  */
/*==============================================================*/
create table C_BO_SERVICE_CATALOG
(
   ID                   varchar(64) not null comment 'ID',
   CODE                 varchar(50) comment 'Ŀ¼���,��ͳһapi��ϵͳ�Զ�����,���㼶��4λһ��',
   NAME                 varchar(200) comment 'Ŀ¼����',
   BAKUP                varchar(500) comment '��ע',
   PARENT_ID            varchar(64) comment '�ϼ�Ŀ¼ID',
   CREATE_USER          varchar(30) comment '������',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   CREATE_DEPT_CODE     varchar(30) comment '��������������',
   UPDATE_USER          varchar(30) comment '�޸���',
   UPDATE_TIME          varchar(19) comment '�޸�ʱ��',
   STATUS               varchar(20) default '01' comment '����״̬  ENABLE_STATUS 01-���� 02-����',
   PROCESS_ID           varchar(64) comment '����Ŀ¼ͨ�����ֶ��ҵ���Ӧ�����̣��Ӷ��ҵ�����������Ϣ',
   SORT_NUM             int default 1,
   ORDER_NO_PREFIX      varchar(20) comment '���幤���������ݷ���Ŀ¼��Ӧ�����������ɹ������',
   SERVICE_CATAGORY     varchar(20) comment '�����ֵ䣺ORDER_SERVICE_CATAGORY ������������ࣺ01-��ѯ��02-Ͷ�ߡ�03-���顢04-�����',
   BUSI_CATAGORY        varchar(20) comment '�����ֵ䣺ORDER_SERVICE_BUSI_CATAGORY ������ҵ����ࣺ01-ͨ��ҵ��02-��ˮҵ��03-����ҵ���',
   TIME_LIMIT           varchar(20) default '8' comment 'ʹ�������ֵ䣺REQUIRED_TIME 4-4Сʱ  8-8Сʱ 24-24Сʱ 48-48Сʱ',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��������ID',
   primary key (ID)
);

alter table C_BO_SERVICE_CATALOG comment '����Ŀ¼';

/*==============================================================*/
/* Index: IDX_CBSC_PID                                          */
/*==============================================================*/
create index IDX_CBSC_PID on C_BO_SERVICE_CATALOG
(
   PARENT_ID
);

/*==============================================================*/
/* Index: IDX_CBSC_STSTUS                                       */
/*==============================================================*/
create index IDX_CBSC_STSTUS on C_BO_SERVICE_CATALOG
(
   ENT_ID,
   BUSI_ORDER_ID,
   STATUS
);

/*==============================================================*/
/* Table: C_BO_SERVICE_CATALOG_DEPT                             */
/*==============================================================*/
create table C_BO_SERVICE_CATALOG_DEPT
(
   ID                   varchar(64) not null comment 'ID',
   SERVICE_CATALOG_ID   varchar(64) comment 'SERVICE_CATALOG_ID',
   DEPT_CODE            varchar(64) comment 'DEPT_CODE',
   LINK_TYPE            varchar(10) default '1' comment '��������������֮�������Ϊ�˱�����ѡ����������չʾ����������ֶΣ�����ѡ�˵ڶ��������ĳ���ڵ�(�ýڵ�������Ԫ�صĹ�����ϵΪ ֱ�ӹ���)����ʱ��Ҫͬʱ�����ýڵ�ĸ��ڵ�������Ԫ�صĹ�ϵ(��ӹ���)�������Ϳ���ͨ�����νṹ�ķ�ʽչʾ���ѹ���������
            
            1-ֱ�ӹ���  2-��ӹ���  ��
            ����Ҫ�õ������ֵ�',
   primary key (ID)
);

alter table C_BO_SERVICE_CATALOG_DEPT comment '����Ŀ¼�벿�Ź�����ϵ';

/*==============================================================*/
/* Index: IDX_CBSCD_REF                                         */
/*==============================================================*/
create index IDX_CBSCD_REF on C_BO_SERVICE_CATALOG_DEPT
(
   SERVICE_CATALOG_ID,
   DEPT_CODE,
   LINK_TYPE
);


/*==============================================================*/
/* Table: C_BO_SERVICE_CATALOG_ROLE                             */
/*==============================================================*/
create table C_BO_SERVICE_CATALOG_ROLE
(
   ID                   varchar(64) not null comment 'ID',
   SERVICE_CATALOG_ID   varchar(64) not null comment '����Ŀ¼ID',
   ROLE_ID              varchar(64) not null comment '��ɫ����',
   ROLE_NAME            varchar(200) comment '��ɫ����',
   CREATE_USER          varchar(64) comment '������',
   CREATE_USER_NAME     varchar(100) comment '����������',
   CREATE_DEPT_CODE     varchar(64) comment '�����˲���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   primary key (ID)
);

alter table C_BO_SERVICE_CATALOG_ROLE comment '�洢����Ŀ¼���ɫ�Ĺ�����Ϣ';

/*==============================================================*/
/* Index: IDX_CBSCR_SERVICECATALOG                              */
/*==============================================================*/
create index IDX_CBSCR_SERVICECATALOG on C_BO_SERVICE_CATALOG_ROLE
(
   SERVICE_CATALOG_ID
);

/*==============================================================*/
/* Index: IDX_CBSCR_ROLE                                        */
/*==============================================================*/
create index IDX_CBSCR_ROLE on C_BO_SERVICE_CATALOG_ROLE
(
   ROLE_ID
);

--    20200512

/*==============================================================*/
/* Table: C_WF_BPM_FILE                                         */
/*==============================================================*/
create table C_WF_BPM_FILE
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_ID           varchar(64) comment '����ID',
   FILE_TYPE            varchar(2) comment '1-xml�ļ� 2-jpg�ļ�
            
            �����ֵ䣺WF_BPM_FILE_TYPE',
   CONTENT              text comment '�ļ�����',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_BPM_FILE comment '�����ļ���Ϣ��';

/*==============================================================*/
/* Index: IDX_CWBF_PROCESSID                                    */
/*==============================================================*/
create index IDX_CWBF_PROCESSID on C_WF_BPM_FILE
(
   PROCESS_ID
);

/*==============================================================*/
/* Table: C_WF_PROCESS                                          */
/*==============================================================*/
create table C_WF_PROCESS
(
   ID                   varchar(64) not null comment 'ID',
   NAME                 varchar(100) not null comment '��������',
   FLOW_KEY             varchar(100) comment '����KEY',
   TYPE                 varchar(2) comment '1-��������
            
            �����ֵ䣺WF_PROCESS_TYPE',
   STATUS               varchar(64) comment '1-δ���� 2-�ѷ���
            
            �����ֵ䣺WF_PROCESS_STATUS',
   CREATE_USER          varchar(64) comment '������',
   CREATE_TIME          varchar(64) comment '����ʱ��',
   CREATE_DEPT_ID       varchar(64) comment '�����˲���ID',
   UPDATE_USER          varchar(64) comment '�޸���',
   UPDATE_TIME          varchar(64) comment '�޸�ʱ��',
   UPDATE_DEPT_ID       varchar(64) comment '�޸��˲���ID',
   BAKUP                varchar(200) comment '��ע',
   VERSION              int default 1 comment '�汾��',
   WO_TABLE_NAME        varchar(50) comment '���ڹ����������빤����Ĺ�����ϵ�������̷���֮ǰ���������ù����Ĺ�����',
   PC_SRH_FORM_KEY      varchar(50) comment 'PC�˲鿴��key',
   APP_SRH_FORM_KEY     varchar(50) comment '�ƶ��˲鿴��key',
   ICONS                varchar(50) default 'default.png' comment '����ͼ��',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS comment '������Ϣ��';

/*==============================================================*/
/* Index: IDX_CWP_FLOWKEY                                       */
/*==============================================================*/
create index IDX_CWP_FLOWKEY on C_WF_PROCESS
(
   ENT_ID,
   BUSI_ORDER_ID,
   FLOW_KEY
);

/*==============================================================*/
/* Index: IDX_CWP_TIME                                          */
/*==============================================================*/
create index IDX_CWP_TIME on C_WF_PROCESS
(
   TYPE,
   STATUS,
   CREATE_TIME
);

/*==============================================================*/
/* Table: C_WF_PROCESS_AUTO                                     */
/*==============================================================*/
create table C_WF_PROCESS_AUTO
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_ID           varchar(64) comment '������ϢID',
   PROCESS_DEFINITION_ID varchar(64) comment '���̶���ID',
   FLOW_KEY             varchar(50) comment '����Key',
   START_FORM           varchar(64) comment '������',
   BUSI_TABLE_NAME      varchar(64) comment 'ҵ�����,�����Զ��ɷ�������Ϊ��ȡ�ɷ����ݵı��ɷ���������ʱ��Ϊ��ȡԭ������Ӧ�Ĺ�����һ����BASE_ORDER�����Ҫ��ȡ�Ĺ����ж��ű�����Ϊ���ű�����ͼ(����Ҫ��ID�ֶ�)��Ȼ�������õ�ʱ��ѡ����ͼ����������ʱ��ID�ֶ�ȥ��ȡԭ��������',
   PARAM_SOURCE         varchar(64) default '2' comment '������Դ,1-��ҵ��� 2-�ֹ���д
            ����û�ѡ����ҵ�����,�ò�������Ϊ1����������Ϊ2',
   AUTO_SEND            varchar(2) comment '�Ƿ��Զ��ɷ�,�����ֵ䣺sf_yn',
   OLD_PROCESS_ID       varchar(64) comment 'ԭ������ϢID',
   OLD_FLOW_KEY         varchar(50) comment 'ԭ����KEY',
   TYPE                 varchar(2) comment '�����Զ��ɷ��������ͣ�PROCESS_AUTO_SEND_TYPE
            1-�Զ��ɷ�����(�������õĲ����Զ��ɷ�ĳһ�ֹ���) 
            2-�ɷ���������(�������õĲ�������A�๤���������ɷ���B�๤��)',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_AUTO comment 'Ŀǰ֧���Զ��ɷ�ĳ�ֹ�����Ҳ֧������ĳ�ֹ���(��ĳ�������ɷ����������͵��¹���)';

/*==============================================================*/
/* Index: IDX_CWPA_OLD_FLOW                                     */
/*==============================================================*/
create index IDX_CWPA_OLD_FLOW on C_WF_PROCESS_AUTO
(
   OLD_PROCESS_ID,
   OLD_FLOW_KEY,
   TYPE
);

/*==============================================================*/
/* Index: IDX_CWPA_FLOW                                         */
/*==============================================================*/
create index IDX_CWPA_FLOW on C_WF_PROCESS_AUTO
(
   PROCESS_ID,
   FLOW_KEY,
   TYPE
);

/*==============================================================*/
/* Table: C_WF_PROCESS_AUTO_PARAM                               */
/*==============================================================*/
create table C_WF_PROCESS_AUTO_PARAM
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_AUTO_ID      varchar(64) not null comment '�Զ��ɷ�������ϢID',
   CN_NAME              varchar(100) comment '��������',
   EN_NAME              varchar(100) comment 'Ӣ������',
   BUSI_TABLE_FIELED    varchar(50) comment 'ҵ�����ݱ�����ֶ�',
   DEFAULT_VAL          varchar(100) comment 'Ĭ��ֵ',
   SORT_NUM             numeric(8,0) comment '���',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_AUTO_PARAM comment '�����Զ������ò�����';

/*==============================================================*/
/* Index: IDX_CWPAP_AUTOID                                      */
/*==============================================================*/
create index IDX_CWPAP_AUTOID on C_WF_PROCESS_AUTO_PARAM
(
   PROCESS_AUTO_ID
);

/*==============================================================*/
/* Table: C_WF_PROCESS_CATALOG                                  */
/*==============================================================*/
create table C_WF_PROCESS_CATALOG
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_ID           varchar(64) comment '���̼�¼id',
   SERVICE_CATALOG_ID   varchar(64) comment '����Ŀ¼ID',
   TYPE                 varchar(10) default '1' comment '��������������֮�������Ϊ�˱�����ѡ����������չʾ����������ֶΣ�����ѡ�˵ڶ��������ĳ���ڵ�(�ýڵ�������Ԫ�صĹ�����ϵΪ ֱ�ӹ���)����ʱ��Ҫͬʱ�����ýڵ�ĸ��ڵ�������Ԫ�صĹ�ϵ(��ӹ���)�������Ϳ���ͨ�����νṹ�ķ�ʽչʾ���ѹ���������
            
            1-ֱ�ӹ���  2-��ӹ���  ��
            ����Ҫ�õ������ֵ�',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_CATALOG comment '���������Ŀ¼�İ󶨵Ĺ�����ϵ;�ñ���Ҫ���ڸ�������Ȩ�ޣ���������ǰ�û��ܲ����ķ���Ŀ¼��';

/*==============================================================*/
/* Index: IDX_CWPC_PROCESSID                                    */
/*==============================================================*/
create index IDX_CWPC_PROCESSID on C_WF_PROCESS_CATALOG
(
   PROCESS_ID
);

/*==============================================================*/
/* Index: IDX_CWPC_CATALOG                                      */
/*==============================================================*/
create index IDX_CWPC_CATALOG on C_WF_PROCESS_CATALOG
(
   SERVICE_CATALOG_ID
);

create table C_WF_PROCESS_LISTENER
(
   ID                   varchar(64) not null comment 'ID',
   NAME                 varchar(100) comment '����',
   TYPE                 varchar(20) comment '���̼���������,�����ֵ�PROCESS_LISTENER_TYPE  EXEC-ִ�м�����  TASK-��������� SERVICETASK-��������
            
            �����ֵ䣺PORCESS_LISTENER_TYPE',
   ATTRIBUTE            varchar(10) comment '���̼��������ԣ������ֵ�PROCESS_LISTENER_ATTR��start-��ʼ  end-���� assignment-���߼���
            
            �����ֵ䣺PORCESS_LISTENER_ATTRIBUTE',
   VALUE_TYPE           varchar(10) comment '���̼�����ֵ����,�����ֵ�PROCESS_LISTENER_VTYPE JAVA-JAVA��  EXP-���ʽ
            
            �����ֵ䣺PORCESS_LISTENER_VALUE_TYPE',
   VALUE                varchar(200),
   CREATE_USER          varchar(64),
   CREATE_USER_NAME     varchar(100),
   CREATE_TIME          varchar(19),
   UPDATE_USER          varchar(64),
   UPDATE_USER_NAME     varchar(100),
   UPDAETIME            varchar(19),
   STATUS               varchar(2) default '01' comment '����״̬�������ֵ� ENABLE_STATUS   01-���� 02-����
            
            �����ֵ䣺STATUS_ENABLE_OR_NOT',
   BAKUP                varchar(500),
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_LISTENER comment '���̼�����';

/*==============================================================*/
/* Index: IDX_CWPL_TYPE                                         */
/*==============================================================*/
create index IDX_CWPL_TYPE on C_WF_PROCESS_LISTENER
(
   ENT_ID,
   BUSI_ORDER_ID,
   TYPE,
   ATTRIBUTE,
   VALUE_TYPE,
   STATUS
);

/*==============================================================*/
/* Table: C_WF_PROCESS_LISTENER_DEPT                            */
/*==============================================================*/
create table C_WF_PROCESS_LISTENER_DEPT
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_LISTENER_ID  varchar(100) not null comment '���̼�����ID',
   DEPT_CODE            varchar(30) not null comment '���ű��',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_LISTENER_DEPT comment '���̼���������ҵ(����)�Ĺ�����¼';

/*==============================================================*/
/* Index: IDX_CWPLD_LISTENER                                    */
/*==============================================================*/
create index IDX_CWPLD_LISTENER on C_WF_PROCESS_LISTENER_DEPT
(
   PROCESS_LISTENER_ID
);

/*==============================================================*/
/* Index: IDX_CWPLD_FRAM                                        */
/*==============================================================*/
create index IDX_CWPLD_FRAM on C_WF_PROCESS_LISTENER_DEPT
(
   DEPT_CODE
);

/*==============================================================*/
/* Table: C_WF_PROCESS_LISTENER_FIELD                           */
/*==============================================================*/
create table C_WF_PROCESS_LISTENER_FIELD
(
   ID                   varchar(64) not null comment 'ID',
   LISTENER_ID          varchar(64) comment '���̼�����ID',
   FIELD_NAME           varchar(100) comment '�ֶ�����',
   FIELD_TYPE           varchar(20) comment '�����ֵ䣬���̼������ֶ����ͣ�PROCESS_LISTENER_FIELD_TYPE string-String, expression-Expression
            �����ֵ䣺PROCESS_LISTENER_FIELD_TYPE',
   FIELD_VALUE          varchar(100) comment '�ֶ�ֵ',
   FIELD_DESC           varchar(500) comment '�ֶ�˵��',
   BAKUP                varchar(500) comment '��ע',
   STATUS               varchar(10) default '01' comment '�����ֵ䣬ENABLE_STATUS 01-���� 02-����
            
            �����ֵ䣺STATUS_ENABLE_OR_NOT',
   CREATE_USER          varchar(64) comment '������',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   UPDATE_USER          varchar(64) comment '�޸���',
   UPDATE_TIME          varchar(19) comment '�޸�ʱ��',
   SORT_NUM             int default 1 comment '���',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_LISTENER_FIELD comment '�������ֶ�����';

/*==============================================================*/
/* Index: IDX_CWPLF_LSID                                        */
/*==============================================================*/
create index IDX_CWPLF_LSID on C_WF_PROCESS_LISTENER_FIELD
(
   LISTENER_ID,
   STATUS
);

/*==============================================================*/
/* Table: C_WF_PROCESS_LOG                                      */
/*==============================================================*/
create table C_WF_PROCESS_LOG
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_ID           varchar(64) comment '����ID',
   FLOW_KEY             varchar(30) comment '����key',
   CREATE_USER          varchar(64) comment '������',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   OPER_TYPE            varchar(2) comment '��������   1-���� 2-�޸� 3-ɾ��
            
            �����ֵ䣺ͨ�����ݲ�������, DATA_OPER_TYPE',
   CONTENT              varchar(500) comment '����',
   BAKUP                varchar(200) comment '��ע',
   BAKUP2               varchar(200) comment '��ע2',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_LOG comment '���̲�����־';

/*==============================================================*/
/* Index: IDX_CWPL_FLOWKEY                                      */
/*==============================================================*/
create index IDX_CWPL_FLOWKEY on C_WF_PROCESS_LOG
(
   PROCESS_ID,
   FLOW_KEY,
   CREATE_TIME,
   OPER_TYPE
);

/*==============================================================*/
/* Table: C_WF_PROCESS_NODE                                     */
/*==============================================================*/
create table C_WF_PROCESS_NODE
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_ID           varchar(64) comment '����ID',
   FLOW_KEY             varchar(100) comment '����KEY(����)',
   NODE_ID              varchar(100) comment '�ڵ�ID',
   NODE_NAME            varchar(100) comment '�ڵ�����',
   DONE_MINUTES         numeric(8,0) comment 'Ҫ�����ʱ��(��Чʱ��,����)',
   CREATETIME           varchar(19) comment '����ʱ��',
   CREATE_USER          varchar(64) comment '������',
   UPDATETIME           varchar(19) comment '�޸�ʱ��',
   UPDATE_USER          varchar(64) comment '�޸���',
   WARNING_MINUTES      numeric(8,0) comment 'Ԥ��ʱ��(����)',
   WARNING_TIMES        numeric(8,0) default 1 comment 'Ԥ������',
   ALARM_MINUTES        numeric(8,0) comment '�澯ʱ��(����)',
   ALARM_TIMES          numeric(8,0) default 1 comment '�澯����',
   OVERTIME_NOTICE_TIMES numeric(8,0) default 1 comment '��ʱ֪ͨ����',
   ALLOW_REMIND         varchar(10) default 'Y' comment '�����ʱ���Ƿ��͹�����������;
            ʹ�������ֵ�:  sf_yn',
   ALLOW_CUIBAN         varchar(10) default 'Y' comment '�Ƿ�����Ըýڵ㷢�ʹ߰���Ϣ
            ʹ�������ֵ�:  sf_yn',
   ALLOW_APP            varchar(10) default 'N' comment '�Ƿ�֧���ƶ��칫  SF_YN Y-�� N-��
            ʹ�������ֵ�:  sf_yn',
   APP_FORM_KEY         varchar(30) comment '���ýڵ�֧���ƶ��칫ʱ��������д�ֻ���key',
   TRANS_DEPT_LEVEL     numeric(8,0) default 0 comment 'ת�ɹ����粿�Ų㼶 0-ֻ���ڵ�ǰ������ת�ɣ�1-���ϼ���������������Ӳ�����ת��....',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_NODE comment '���̽ڵ���Ϣ';

/*==============================================================*/
/* Index: IDX_CWPN_PROCESS                                      */
/*==============================================================*/
create index IDX_CWPN_PROCESS on C_WF_PROCESS_NODE
(
   PROCESS_ID,
   FLOW_KEY,
   NODE_ID
);

/*==============================================================*/
/* Index: IDX_CWPN_APP                                          */
/*==============================================================*/
create index IDX_CWPN_APP on C_WF_PROCESS_NODE
(
   FLOW_KEY,
   NODE_ID,
   ALLOW_APP
);

/*==============================================================*/
/* Index: IDX_CWPN_FLOW                                         */
/*==============================================================*/
create index IDX_CWPN_FLOW on C_WF_PROCESS_NODE
(
   FLOW_KEY,
   NODE_ID
);

/*==============================================================*/
/* Table: C_WF_PROCESS_NODE_DEPT                                */
/*==============================================================*/
create table C_WF_PROCESS_NODE_DEPT
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_NODE_ID      varchar(64) comment '���̽ڵ���Ϣ��¼ID',
   NODE_ID              varchar(64) comment '���̽ڵ�ID',
   JUMP_TYPE            varchar(10) comment '��ת����  1-���
            
            �����ֵ䣺������ת����-PROCESS_JUMP_TYPE',
   SENDER_TYPE          varchar(10) default '1' comment '�ύ������  1-���� 2-��ɫ 3-ָ����Ա
            
            �����ֵ䣺PROCESS_PEOPLE_TYPE',
   SENDER               varchar(64) comment '�ύ�˱��',
   SENDER_NAME          varchar(100) comment '�ύ������',
   RECEIVER_TYPE        varchar(10) default '1' comment '����������  1-���� 2-��ɫ 3-ָ����Ա
            
            �����ֵ䣺PROCESS_PEOPLE_TYPE',
   RECEIVER             varchar(64) comment '�����˱��',
   RECEIVER_NAME        varchar(100) comment '����������',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_NODE_DEPT comment '����������ĳ���ڵ��ϣ�A���ŵ���Ա�ύ������ֱ���ύ��B����(����Ա���ɫ)����';

/*==============================================================*/
/* Index: IDX_CWPND_NODE                                        */
/*==============================================================*/
create index IDX_CWPND_NODE on C_WF_PROCESS_NODE_DEPT
(
   PROCESS_NODE_ID,
   NODE_ID
);

/*==============================================================*/
/* Index: IDX_CWPND_SENDER                                      */
/*==============================================================*/
create index IDX_CWPND_SENDER on C_WF_PROCESS_NODE_DEPT
(
   SENDER,
   JUMP_TYPE,
   SENDER_TYPE
);

/*==============================================================*/
/* Table: C_WF_PROCESS_NODE_TIP                                 */
/*==============================================================*/
create table C_WF_PROCESS_NODE_TIP
(
   ID                   varchar(64) not null comment 'ID',
   PROCESS_NODE_ID      varchar(64) comment '���̽ڵ���Ϣ��¼ID',
   NODE_ID              varchar(64) comment '���̽ڵ�ID',
   TIP_TYPE             varchar(20) comment '����֪ͨ����  1-�Զ��ط� 2-�����ﵽ���� 3-�����߰� 4-����Ԥ�� 5-�����澯 6-������ʱ����
            �����ֵ䣺ORDER_NOTICE_TYPE',
   TIP_CONTENT          varchar(200) comment '��������Ա�˺š����ű�Ż��߽�ɫid����TYPE��Ӧ',
   CREATE_ACC           varchar(30) comment '������',
   CREATE_NAME          varchar(50) comment '����������',
   CREATE_DEPT_CODE     varchar(30) comment '�����˲���',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   UPDATE_ACC           varchar(30) comment '�޸���',
   UPDATE_TIME          varchar(19) comment '�޸�ʱ��',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_NODE_TIP comment '���̽ڵ�֪ͨ��������,�������ù����������ݡ�����Ԥ�����ݵȣ����û���ã���ȡĬ�ϵ�ȫ������';

/*==============================================================*/
/* Index: IDX_CWPNT_PROCESS_NODE                                */
/*==============================================================*/
create index IDX_CWPNT_PROCESS_NODE on C_WF_PROCESS_NODE_TIP
(
   PROCESS_NODE_ID
);

/*==============================================================*/
/* Index: IDX_CWPNT_NODE                                        */
/*==============================================================*/
create index IDX_CWPNT_NODE on C_WF_PROCESS_NODE_TIP
(
   NODE_ID
);

/*==============================================================*/
/* Table: C_WF_PROCESS_TASK                                     */
/*==============================================================*/
create table C_WF_PROCESS_TASK
(
   ID                   varchar(64) not null comment 'ID',
   ORDER_ID             varchar(64) comment '����ID',
   ORDER_NO             varchar(64) comment '�������',
   PROCESS_INSTANCE_ID  varchar(64) comment '����ʵ��ID',
   PROCESS_TASK_TYPE    varchar(10) default '1' comment '������������ PROCESS_TASK_TYPE 1-http���� 2-�Զ��ɷ�����
            ʹ�������ֵ䣺PROCESS_TASK_TYPE',
   REQ_MAIN             varchar(100) comment 'REQ_MAIN',
   REQ_PARAMS           varchar(500) comment 'REQ_PARAMS',
   STATUS               varchar(10) default '1' comment '1-������ 2-������ 3-�������
            
            ʹ�������ֵ䣺PROCESS_TASK_STATUS',
   RESULT               varchar(4000) comment '������',
   BAKUP                varchar(2000) comment '��ע',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   CREATE_USER          varchar(64) comment '������',
   HANDLE_TIME          varchar(19) comment '����ʱ��',
   HADNLE_TIMES         int default 0 comment '�������',
   BAK1                 varchar(200) comment '�����Զ��ɷ�����,��� ������Ӧ������key',
   BAK2                 varchar(200) comment '�����Զ��ɷ�����,��� ������Ӧ�����̶���id',
   BAK3                 varchar(200) comment '����3',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment 'ҵ�񶩹�ID',
   primary key (ID)
);

alter table C_WF_PROCESS_TASK comment '��������ת��������Ҫ����������緢��http�������������Ҫ�����µ�������Ҫ��չ��ṹ';

/*==============================================================*/
/* Index: IDX_CWPT_STATUS                                       */
/*==============================================================*/
create index IDX_CWPT_STATUS on C_WF_PROCESS_TASK
(
   ENT_ID,
   BUSI_ORDER_ID,
   STATUS
);

ALTER TABLE C_BO_QUESTION_TYPE ADD ENT_ID varchar(30) comment '������ҵ';
ALTER TABLE C_BO_QUESTION_TYPE ADD BUSI_ORDER_ID varchar(64) comment '��������ID';

ALTER TABLE C_BO_AUTO_FORM_DB_FIELD ADD GENERATE_TYPE varchar(10) comment 'Ĭ��ֵ,�ֶ��������ݷ�ʽ,1:����������� 2:���ݲ���ʱ�� 3:��ҵ��� 4:ҵ�񶩹���� 5:�������˺� 6:���������� 7:�����˲���';

ALTER TABLE C_BO_ORDER_PUSH_RECORD ADD ORDER_NOTICE_RECORD_ID varchar(64) comment '����֪ͨ��¼ID';
create index IDX_WOPR_ORDERNOTICE on C_BO_ORDER_PUSH_RECORD
(
   ORDER_NOTICE_RECORD_ID
);

ALTER TABLE C_BO_ORDER_NOTICE_RECORD ADD BASE_ORDER_ID varchar(64) comment '����ID';
create index IDX_ONR_ORDER on C_BO_ORDER_NOTICE_RECORD
(
   BASE_ORDER_ID
);

ALTER TABLE C_BO_ORDER_NOTICE_RECORD ADD ORDER_NO varchar(50) comment '�������';
create index IDX_ONR_ORDERNO on C_BO_ORDER_NOTICE_RECORD
(
   ORDER_NO
);


ALTER TABLE C_BO_ORDER_PUSH_RECORD ADD RECEIVER_NUM varchar(50) comment '��Ϣ���պ��룬���ݲ�ͬ�ķ�ʽ��ʾ��ͬ�ĺ��룬�������ֻ��š����䡢΢��openid��';

ALTER TABLE C_BO_ORDER_NOTICE_RECORD ADD CREATE_USER varchar(30) comment '�������˺�';
ALTER TABLE C_BO_ORDER_NOTICE_RECORD ADD CREATE_USER_NAME varchar(50) comment '����������';
ALTER TABLE C_BO_ORDER_NOTICE_RECORD ADD CREATE_USER_DEPT varchar(30) comment '���������ڲ��ű��';

ALTER TABLE C_WF_PROCESS ADD ENABLE_STATUS varchar(10) default '01' comment '����״̬,�����ֵ䣬ENABLE_STATUS 01-���� 02-����';

/*==============================================================*/
/* Table: C_BO_ORDER_STAT                                       */
/*==============================================================*/
create table C_BO_ORDER_STAT
(
   ID                   int not null auto_increment comment 'ID',
   ENT_ID               varchar(30) comment '��ҵID',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   UPDATE_TIME          varchar(19) comment '����ʱ��',
   PROC_KEY             varchar(50) comment '����key',
   PROC_NAME            varchar(100) comment '��������',
   ORDER_CREATE_DATE    varchar(10) comment '������������',
   TIME_LIMIT           numeric(10,2) comment '��������ʱ��',
   ORDER_LEVEL          varchar(10) comment '�����ȼ�',
   SOURCE               varchar(10) comment '������Դ',
   TOTAL_NUM            int default 0 comment '�ܹ�����',
   SAVE_NUM             int default 0 comment '�ݴ���',
   DONE_NUM             int default 0 comment '������ɹ�����',
   DONE_OT_NUM          int default 0 comment '��ʱ�����',
   CLOSE_NUM            int default 0 comment 'ǿ�ƹرչ�����',
   DEL_NUM              int default 0 comment 'ǿ��ɾ��������',
   CUIBAN_NUM           int default 0 comment '�д߰�Ĺ�����',
   OVERSEE_NUM          int default 0 comment '�ж���Ĺ�����',
   NOTE_NUM             int default 0 comment '�б�ע�Ĺ�����',
   BAKUP_NUM            int default 0 comment '����ע�Ĺ�����',
   DOING_NUM            int default 0 comment '�����еĹ�����',
   DOING_OT_NUM         int default 0 comment '�������ҳ�ʱ�Ĺ�����',
   AVG_TIME             numeric(10,2) default 0 comment 'ƽ������ʱ��(Сʱ)',
   TOTAL_TIME           numeric(10,2) default 0 comment '������ʱ��(Сʱ)',
   primary key (ID)
);

alter table C_BO_ORDER_STAT comment '���๤���������ܱ�,ÿ�๤��ÿ����ڶ������ܼ�¼��ÿ��10���Ӹ��£�ÿ��Ļ��ܼ�¼��ֱ�����й���������ɺ�Ų���ͳ��';

/*==============================================================*/
/* Index: IDX_CBOS_ENT                                          */
/*==============================================================*/
create index IDX_CBOS_ENT on C_BO_ORDER_STAT
(
   ENT_ID,
   BUSI_ORDER_ID,
   ORDER_CREATE_DATE,
   PROC_KEY
);

/*==============================================================*/
/* Index: IDX_CBOS_DATE                                         */
/*==============================================================*/
create index IDX_CBOS_DATE on C_BO_ORDER_STAT
(
   ORDER_CREATE_DATE
);


/*==============================================================*/
/* Table: C_BO_ORDER_DAY_STAT                                   */
/*==============================================================*/
create table C_BO_ORDER_DAY_STAT
(
   ID                   int not null auto_increment comment 'ID',
   ENT_ID               varchar(30) comment '��ҵID',
   BUSI_ORDER_ID        varchar(64) comment '����ID',
   UPDATE_TIME          varchar(19) comment '����ʱ��',
   PROC_KEY             varchar(50) comment '����key',
   PROC_NAME            varchar(100) comment '��������',
   DATE_ID              int comment 'ͳ������',
   TIME_LIMIT           numeric(10,2) comment '����������ʱ��(Сʱ)',
   ORDER_LEVEL          varchar(10) comment '�����ȼ�,1-��ͨ  2-����  �����ֵ䣺COMMON_ORDER_LEVEL',
   SOURCE               varchar(10) comment '������Դ,��Դ����,call-���� media-ȫý��',
   ADD_NUM              int default 0 comment '����������',
   DONE_NUM             int default 0 comment '���������',
   DONE_OT_NUM          int default 0 comment '��ʱ�����',
   CLOSE_NUM            int default 0 comment 'ǿ�ƹرչ�����',
   DEL_NUM              int default 0 comment 'ǿ��ɾ��������',
   SAVE_NUM             int default 0 comment '�ݴ���',
   CUIBAN_TIMES         int default 0 comment '�߰����',
   OVERSEE_TIMES        int default 0 comment '�������',
   NOTE_TIMES           int default 0 comment '��ע����',
   BAKUP_TIMES          int default 0 comment '��ע����',
   primary key (ID)
);

alter table C_BO_ORDER_DAY_STAT comment '�չ���ͳ�Ʊ�';

/*==============================================================*/
/* Index: IDX_CBODS_ENT                                         */
/*==============================================================*/
create index IDX_CBODS_ENT on C_BO_ORDER_DAY_STAT
(
   ENT_ID,
   BUSI_ORDER_ID,
   DATE_ID,
   PROC_KEY
);

/*==============================================================*/
/* Index: IDX_CBODS_DATE                                        */
/*==============================================================*/
create index IDX_CBODS_DATE on C_BO_ORDER_DAY_STAT
(
   DATE_ID
);


ALTER TABLE C_BO_AUTO_FORM_DB_FIELD ADD STATUS varchar(10) default '01' comment '����״̬��01-���� 02-����';
ALTER TABLE C_BO_AUTO_FORM_DB_FIELD ADD SORT int default 1 comment '���';

ALTER TABLE C_BO_ORDER_CUIBAN ADD TASK_ID varchar(10) default '64' comment '����ID������ǩʱ��ͬһ�ڵ���ж������ͬʱ���ɶ����߰�';

ALTER TABLE C_BO_ORDER_ARRIVE_SETTING ADD WORKGROUP_CODE varchar(50)   comment '��������';
ALTER TABLE C_BO_ORDER_ARRIVE_SETTING ADD WORKGROUP_NAME varchar(100)   comment '����������';


-- 20200506
alter table C_BO_AUTO_FORM_DB_REF add PROCESS_ID varchar(64) comment '���̼�¼ID';
create index IDX_CBAFDR_PROCESS on C_BO_AUTO_FORM_DB_REF
(
   PROCESS_ID,
   AUTO_FORM_ID
);


alter table C_BO_AUTO_FORM_DB_FIELD add SHOW_TYPE varchar(10) comment 'չʾ����,BO_FORM_FIELD_SHOWTYPE 1-�����ı��� 2-�����ı��� 3-������ 4-��ѡ�� 5-��ѡ�� 6-ʱ��ѡ����  7-����ѡ���� 8-����ѡ��� 9-���� 10-���� 11-���� 12-������ 13-�������';
alter table C_BO_AUTO_FORM_DB_FIELD add EXT_CONFIG text comment 'չʾ���͵���չ���ã�JSON��ʽ';

alter table C_BO_AUTO_FORM_DB add PROCESS_ID varchar(64) comment '���̼�¼ID';


/*==============================================================*/
/* Table: C_BOX_ORDER_BUSI                                      */
/*==============================================================*/
create table C_BOX_ORDER_BUSI
(
   ID                   varchar(64) not null comment 'ID',
   M_ID                 varchar(64) comment '����ID������C_BO_BASE_ORDER�����ID',
   ENT_ID               varchar(30) comment '������ҵ',
   BUSI_ORDER_ID        varchar(64) comment '��ҵҵ�񶩹�ID',
   CREATE_TIME          varchar(19) comment '����ʱ��',
   EX1                  varchar(50) comment '�ֶ�1',
   EX2                  varchar(50) comment '�ֶ�2',
   EX3                  varchar(50) comment '�ֶ�3',
   EX4                  varchar(50) comment '�ֶ�4',
   EX5                  varchar(50) comment '�ֶ�5',
   EX6                  varchar(100) comment '�ֶ�6',
   EX7                  varchar(100) comment '�ֶ�7',
   EX8                  varchar(100) comment '�ֶ�8',
   EX9                  varchar(100) comment '�ֶ�9',
   EX10                 varchar(100) comment '�ֶ�10',
   EX11                 varchar(200) comment '�ֶ�11',
   EX12                 varchar(200) comment '�ֶ�12',
   EX13                 varchar(200) comment '�ֶ�13',
   EX14                 varchar(200) comment '�ֶ�14',
   EX15                 varchar(200) comment '�ֶ�15',
   EX16                 varchar(200) comment '�ֶ�16',
   EX17                 varchar(200) comment '�ֶ�17',
   EX18                 varchar(200) comment '�ֶ�18',
   EX19                 varchar(200) comment '�ֶ�19',
   EX20                 varchar(200) comment '�ֶ�20',
   EX21                 varchar(500) comment '�ֶ�21',
   EX22                 varchar(500) comment '�ֶ�22',
   EX23                 varchar(500) comment '�ֶ�23',
   EX24                 varchar(500) comment '�ֶ�24',
   EX25                 varchar(500) comment '�ֶ�25',
   EX26                 varchar(500) comment '�ֶ�26',
   EX27                 varchar(500) comment '�ֶ�27',
   EX28                 varchar(500) comment '�ֶ�28',
   EX29                 varchar(500) comment '�ֶ�29',
   EX30                 varchar(500) comment '�ֶ�30',
   EX31                 varchar(500) comment '�ֶ�30',
   EX32                 varchar(500) comment '�ֶ�30',
   EX33                 varchar(500) comment '�ֶ�30',
   EX34                 varchar(1000) comment '�ֶ�30',
   EX35                 varchar(1000) comment '�ֶ�30',
   EX36                 varchar(1000) comment '�ֶ�30',
   EX37                 varchar(1000) comment '�ֶ�30',
   EX38                 varchar(1000) comment '�ֶ�30',
   EX39                 text comment '�ֶ�30',
   EX40                 text comment '�ֶ�30',
   primary key (ID)
);

alter table C_BOX_ORDER_BUSI comment '����ͨ��ҵ����Ϣ��,�������ڴ洢��ͬ�������̹�����ҵ����Ϣ��һ����ã�����������ᵽ����ѯЧ�ʵ�; ����ÿ�����̣��û���';

/*==============================================================*/
/* Index: IDX_CBOB_ORDER                                        */
/*==============================================================*/
create index IDX_CBOB_ORDER on C_BOX_ORDER_BUSI
(
   M_ID
);

/*==============================================================*/
/* Index: IDX_CBOB_ENT                                          */
/*==============================================================*/
create index IDX_CBOB_ENT on C_BOX_ORDER_BUSI
(
   ENT_ID,
   BUSI_ORDER_ID,
   CREATE_TIME
);

