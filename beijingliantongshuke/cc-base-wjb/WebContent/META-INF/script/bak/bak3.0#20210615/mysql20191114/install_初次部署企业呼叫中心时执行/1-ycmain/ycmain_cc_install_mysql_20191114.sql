
/*==============================================================*/
/* Table: C_CF_DATE_PRO                                         */
/*==============================================================*/
create table C_CF_DATE_PRO
(
   ID                   varchar(64) not null comment 'ID',
   S_DATE               varchar(10) comment '日期,格式如2019-05-24',
   S_DATE2              int comment '整形类型日期,格式如20190524',
   S_YEAR               varchar(4) comment '该日期所属年,如2019',
   S_MONTH              varchar(2) comment '该日期所属月,如05',
   S_DAY                varchar(2) comment '该日期所属天,如24',
   S_WEEK_YEAY          varchar(2) comment '该日期所属全年第几周,如51',
   S_WEEK_MONTH         varchar(2) comment '该日期所属当月第几周,如4',
   S_WEEKDAY            varchar(2) comment '该日期所属周里周几,如6,取值1-7',
   primary key (ID)
);

alter table C_CF_DATE_PRO comment '日期属性表,便于统计,表里存放每天的具体年、月、日、周等信息；表内数据由定时任务自动处理,为所有企业共用,数据不用区分企';

/*==============================================================*/
/* Index: IDX_CCDP_D1                                           */
/*==============================================================*/
create index IDX_CCDP_D1 on C_CF_DATE_PRO
(
   S_DATE
);

/*==============================================================*/
/* Index: IDX_CCDP_D2                                           */
/*==============================================================*/
create index IDX_CCDP_D2 on C_CF_DATE_PRO
(
   S_DATE2
);

/*==============================================================*/
/* Table: C_CF_INF_MONITOR                                      */
/*==============================================================*/
create table C_CF_INF_MONITOR
(
   ID                   varchar(64) not null comment 'ID',
   SENDER               varchar(50) comment '接口请求方',
   DATE_ID              int comment '调用日期',
   HOUR_ID              int comment '调用小时',
   TYPE                 varchar(20) comment '服务接口服务端:service-s,服务接口客户端 servie-c,http-s,http-c',
   URL                  varchar(200) comment '服务接口填SERVICE_ID，http接口填url地址',
   PARAM                varchar(100) comment '服务接口填command',
   REQ_METHOD           varchar(20) comment 'HTTP请求接口有POST、GET',
   STATUS_CODE          int comment '接口调用状态码,如http调用时有404、500、200',
   RESP_CODE            varchar(20) comment '接口调用结果码',
   RESP_DESC            varchar(200) comment '接口调用结果描述',
   DIRECTION            varchar(20) comment '方向  S-服务端 C-客户端',
   CREATE_TIME          varchar(19) comment '调用具体时间',
   CREATE_USER_ACC      varchar(30) comment '调用人账号',
   ENT_ID               varchar(64) default '0' comment '企业ID，无企业时默认为0',
   BUSI_ORDER_ID        varchar(64) default '0' comment '订购ID，无订购时默认为0',
   primary key (ID)
);

alter table C_CF_INF_MONITOR comment '接口调用监控明细记录,该表放到ycmain用户下';

/*==============================================================*/
/* Index: IDX_CCIM_URL                                          */
/*==============================================================*/
create index IDX_CCIM_URL on C_CF_INF_MONITOR
(
   ENT_ID,
   BUSI_ORDER_ID,
   CREATE_TIME,
   URL
);

/*==============================================================*/
/* Index: IDX_CCIM_DATE                                         */
/*==============================================================*/
create index IDX_CCIM_DATE on C_CF_INF_MONITOR
(
   DATE_ID,
   HOUR_ID
);

