package com.yunqu.cc.base.servlet.oufInf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.Constants;

/**
 * 外部接口控制类
 * <AUTHOR>
 */
@WebServlet("/servlet/outInf/*")
public class OutInfServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 1L;
	
	protected static EasyCache cache = CacheManager.getMemcache();
	
	/**
	 * 更新接口分类
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForUpdateOutInfType() {
		EasyResult result = EasyResult.ok("",BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作成功"));
		JSONObject json = this.getJSONObject();
		JSONArray outInfTypes = json.getJSONArray("outInfTypes");
		String outInfTypeId = json.getString("outInfTypeId");
		String typeName = json.getString("typeName");
		try {
			UserModel user = UserUtil.getUser(this.getRequest());
			EasyQuery query = this.getQuery();
			EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF_TYPE"),"ID");
			
			if(!CommonUtil.listIsNotNull(outInfTypes)){
				return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "参数为空！"));
			}
			if(StringUtils.isBlank(typeName)){
				return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "类型名称为空！"));
			}
			record.set("CONFIG_JSON", outInfTypes.toJSONString());
			record.set("TYPE_NAME", typeName);
			
			if(StringUtils.isBlank(outInfTypeId)){
				record.set("ID",RandomKit.randomStr());
				record.set("ENT_ID", getEntId());
				record.set("BUSI_ORDER_ID", getBusiOrderId());
				record.set("CREATE_ACC", user.getUserAcc());
				record.set("CREATE_TIME",EasyDate.getCurrentDateString());
				record.set("CREATE_DEPT", user.getDeptCode());
				query.save(record);
			}else{
				record.set("ID",outInfTypeId);
				query.update(record);
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败"));
		}
		return result;
	}
	
	/**
	 * 接口分类的删除方法
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForDelRecord()  {
		EasyQuery query=this.getQuery();
		try {
		String groupId = getJSONObject().getString("ID");
		EasySQL  sql= new EasySQL("select count(1) from "+getTableName("C_CF_OUT_INF")+" where TYPE_ID=? ");
		if(query.queryForExist(sql.getSQL(), new Object[]{groupId})){
			return EasyResult.fail(getI18nValue("接口分类中存在接口  不可删除"));
		}else{
			EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF_TYPE"),"ID").setPrimaryValues(groupId);
			query.deleteById(record);
		}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	
	/**
	 * 更新接口配置
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForUpdateOutInf() {
		EasyResult result = EasyResult.ok("",BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作成功"));
		JSONObject json = this.getJSONObject();
		JSONArray outInfTypes = json.getJSONArray("outInfTypes");
		String outInfTypeId = json.getString("outInfTypeId");
		String infId = json.getString("infId");
		String sortNum = json.getString("sortNum");
		String infName = json.getString("infName");
		String enableStatus = json.getString("enableStatus");
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		try {
			EasyQuery query = this.getQuery();
			
			if(StringUtils.isBlank(sortNum)){
				return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "接口编号为空！"));
			}
			if(!CommonUtil.listIsNotNull(outInfTypes)){
				return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "参数为空！"));
			}
			
			//判断接口编号是否重复
			EasySQL sql = new EasySQL("SELECT * FROM "+getTableName("C_CF_OUT_INF")+" WHERE 1=1 ");
			sql.append(sortNum,"AND SORT_NUM = ? ");
			sql.append(getEntId(),"AND ENT_ID = ? ");
			sql.append(getBusiOrderId(),"AND BUSI_ORDER_ID = ? ");
			if(StringUtils.isNotBlank(infId)){
				sql.append(infId,"AND ID != ? ");
			} 
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			if(list!=null&&list.size()>=1){
				return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "接口编号不能重复！"));
			}
			
			UserModel user = UserUtil.getUser(this.getRequest());
			
			EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF"),"ID");
			
			record.set("CONFIG_JSON", outInfTypes.toJSONString());
			record.set("SORT_NUM", sortNum);
			record.set("INF_NAME", infName);
			record.set("ENABLE_STATUS", enableStatus);
			record.set("TYPE_ID", outInfTypeId);
			
			if(StringUtils.isBlank(infId)){
				record.set("ID",RandomKit.randomStr());
				record.set("ENT_ID", entId);
				record.set("BUSI_ORDER_ID", busiOrderId);
				record.set("CREATE_ACC", user.getUserAcc());
				record.set("CREATE_TIME",EasyDate.getCurrentDateString());
				record.set("CREATE_DEPT", user.getDeptCode());
				query.save(record);
			}else{
				record.set("ID",infId);
				query.update(record);
			}
			//更新缓存
			List<JSONObject> listCache = new ArrayList<JSONObject>();
			JSONObject outInf = new JSONObject();
			outInf.put("CONFIG_JSON", outInfTypes.toJSONString());
			listCache.add(outInf);
			JSONObject jsonCache = new JSONObject();
			jsonCache.put("nodes", listCache);
			jsonCache.put("busiOrderId", busiOrderId);
			jsonCache.put("entId", entId);
			jsonCache.put("schema", getDbName());
			//重新查询到时，需要更新缓存时间
			json.put("CACHE_TIME", DateUtil.getCurrentDateStr());
			cache.put(CEConstants.CK_CC_OUT_INF + sortNum, jsonCache.toJSONString(),48*3600);
			
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME, "操作失败"));
		}
		return result;
	}
	
	/**
	 * 接口配置的删除方法(批量)
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForBatchDelInf()  {
		JSONArray jsonArray = getJSONObject().getJSONArray("ids");
		JSONArray sortNums = getJSONObject().getJSONArray("sortNums");
		EasyQuery query = getQuery();
		try {
			if(jsonArray!=null && jsonArray.size()>0) {
				for(int i=0;i<jsonArray.size();i++) {
					String id = jsonArray.getString(i);
					EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF"),"ID").setPrimaryValues(id);
					query.deleteById(record);
					String sortNum = sortNums.getString(i);
					cache.delete(CEConstants.CK_CC_OUT_INF + sortNum);
				}
			}
		} catch (Exception e) {
			 
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 接口配置的删除方法
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForDelInf()  {
		EasyQuery query=this.getQuery();
		try {
			String id = getJSONObject().getString("ID");
			String sortNum = getJSONObject().getString("sortNum");
			EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF"),"ID").setPrimaryValues(id);
			query.deleteById(record);
			cache.delete(CEConstants.CK_CC_OUT_INF + sortNum);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
	/**
	 * 修改状态
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForUpdateStatus()  {
		EasyQuery query=this.getQuery();
		try {
			String id = getJSONObject().getString("id");
			String status = getJSONObject().getString("status");
			
			EasyRecord record = new EasyRecord(getTableName("C_CF_OUT_INF"),"ID").setPrimaryValues(id);
			record.set("ENABLE_STATUS", status);
			query.update(record);
			
			if("02".equals(status)){
				EasySQL sql = new EasySQL("SELECT SORT_NUM FROM "+getTableName("C_CF_OUT_INF")+" WHERE 1=1 ");
				sql.append(id,"AND ID = ? ");
				String sortNum = query.queryForString(sql.getSQL(), sql.getParams());
				if(StringUtils.isNotBlank(sortNum)){
					//删除缓存
					cache.delete(CEConstants.CK_CC_OUT_INF + sortNum);
				}
			}
			
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"更新失败，原因："+e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	
}
