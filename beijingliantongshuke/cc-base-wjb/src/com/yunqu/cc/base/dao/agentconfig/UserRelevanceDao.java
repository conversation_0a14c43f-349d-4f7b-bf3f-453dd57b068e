package com.yunqu.cc.base.dao.agentconfig;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.CommonLogger;

@WebObject(name = "userRelevanceDao")
public class UserRelevanceDao extends AppDaoContext {

	private Logger logger = CommonLogger.getLogger();
	

	
	@WebControl(name = "userSelected", type = Types.LIST)
	public JSONObject userSelected() {
		EasySQL sql = new EasySQL("select t1.USER_ID,t1.USER_ACCT USER_ACC,t1.USERNAME USER_NAME,t2.IDX_ORDER,t2.LIMIT_TIME");
		sql.append("from CC_USER t1");
		sql.append("left join " + this.getTableName("CC_SKILL_GROUP_USER") + " t2 on t1.USER_ID = t2.USER_ID");
		sql.append(this.getEntId(), "where t1.ENT_ID = ?");
		sql.append(param.getString("skillId"), "and t2.SKILL_GROUP_ID = ?");
		sql.appendLike(param.getString("searchContent"), "and t1.USER_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "userNoSelected", type = Types.LIST)
	public JSONObject userNoSelected() {
		EasySQL sql = new EasySQL("select t1.USER_ID,t1.USER_ACCT USER_ACC,t1.USERNAME USER_NAME");
		sql.append("from CC_USER t1");
		sql.append(param.getString("skillId"), "left join " + this.getTableName("CC_SKILL_GROUP_USER") + " t2 on t1.USER_ID = t2.USER_ID and t2.SKILL_GROUP_ID = ?", false);
		sql.append(this.getEntId(), "where t1.ENT_ID = ?");
		sql.append(0, "and t1.USER_STATE = ?");
		sql.append("and t2.SKILL_GROUP_ID IS NULL");
		sql.appendLike(param.getString("searchContent"), "and t1.USER_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "skillNoSelected", type = Types.LIST)
	public JSONObject skillNoSelected() {
		EasySQL sql = new EasySQL("select SKILL_GROUP_ID,SKILL_GROUP_NAME");
		sql.append("from " + this.getTableName("CC_SKILL_GROUP") + " t1");
		sql.append(this.getEntId(), "where t1.ENT_ID = ?");
		sql.append(this.getBusiOrderId(), "and t1.BUSI_ORDER_ID = ?");
		if(StringUtils.isBlank(param.getString("skillType"))) {
			sql.append("voice", "and t1.SKILL_GROUP_TYPE in (?");
			sql.append("media", ", ?)");
		} else {
			sql.append(param.getString("skillType"), "and t1.SKILL_GROUP_TYPE = ?");
		}
		sql.append("and t1.SKILL_GROUP_ID not in (");
		sql.append("select SKILL_GROUP_ID");
		sql.append("from " + this.getTableName("CC_SKILL_GROUP_USER"));
		sql.append(param.getString("userId"), "where USER_ID = ?");
		sql.append(")");
		sql.appendLike(param.getString("searchContent"), "and t1.SKILL_GROUP_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "skillSelected", type = Types.LIST)
	public JSONObject skillSelected() {
		EasySQL sql = new EasySQL("select DISTINCT t1.SKILL_GROUP_ID,SKILL_GROUP_NAME,t2.IDX_ORDER,t2.LIMIT_TIME");
		sql.append("from " + this.getTableName("CC_SKILL_GROUP") + " t1");
		sql.append("left join " + this.getTableName("CC_SKILL_GROUP_USER") + " t2 on t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID");
		sql.append(this.getEntId(), "where t1.ENT_ID = ?");
		sql.append(this.getBusiOrderId(), "and t1.BUSI_ORDER_ID = ?");
		if(StringUtils.isBlank(param.getString("skillType"))) {
			sql.append("voice", "and t1.SKILL_GROUP_TYPE in (?");
			sql.append("media", ", ?)");
		} else {
			sql.append(param.getString("skillType"), "and t1.SKILL_GROUP_TYPE = ?");
		}
		sql.append(this.param.getString("userId"), "and t2.USER_ID = ?");
		sql.appendLike(param.getString("searchContent"), "and t1.SKILL_GROUP_NAME like ?");
		return this.queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "roleRelative", type = Types.LIST)
	public EasyResult roleRelative() {
		
		List<JSONObject> all = null;
		List<JSONObject> selected = null;
		
		try {
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			
			EasySQL sql = new EasySQL("select t1.ROLE_ID CODE,t1.ROLE_NAME as NAME from "+getTableName("V_CC_ROLE")+" t1 where 1=1");
			sql.append(entId, "and t1.ENT_ID = ?");
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID = ?");
			
			all = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			sql = new EasySQL("select t1.ROLE_ID CODE,t1.ROLE_NAME as NAME from "+getTableName("V_CC_ROLE")+" t1 where 1=1");
			sql.append(entId, "and t1.ENT_ID = ?");
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID = ?");
			sql.append(param.getString("userId"), "and t1.ROLE_ID in(select distinct ROLE_ID from "+getTableName("V_CC_USER_ROLE")+" where USER_ID = ? ");
			sql.append(entId, "and t1.ENT_ID = ?");
			sql.append(busiOrderId, "and t1.BUSI_ORDER_ID = ?");
			sql.append(" ) order by t1.ROLE_TYPE");
			selected = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return EasyResult.ok().put("all", all).put("selected", selected);
	}
	
}
