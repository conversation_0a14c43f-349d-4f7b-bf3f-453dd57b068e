package com.yunqu.cc.base.dao.black;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.CacheUtil;
import com.yunqu.cc.base.base.AppDaoContext;

/**
 * <AUTHOR>
 *
 */
@WebObject(name="white")
public class WhiteDao extends AppDaoContext {
	/**
	 *白名单管理查询
	 * @return
	 */
	@WebControl(name="list",type=Types.LIST)
	@InfAuthCheck(resId = "cc-base-system-hbmd-bmd",msg = "您无权访问!")
	public  JSONObject list(){
		EasySQL sql = this.getEasySQL("select  * ");
		sql.append(" from CC_WHITE_LIST");
		sql.append(" where 1=1  ");
		sql.append(param.getString("getStartDate")," and CREATE_TIME>= ?");
		sql.append(param.getString("getEndDate")," and CREATE_TIME<= ?");
		sql.appendLike(param.getString("PHONENUM"), " and PHONENUM like ?");
		sql.append(this.getEntId(), "and ENT_ID= ?");
		sql.append(" order by CREATE_TIME desc");
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		if(result!=null){
			JSONArray dataArray = result.getJSONArray("data");
			if(dataArray!=null){
				for(int i = 0 ;i<dataArray.size(); i++){
					JSONObject data = dataArray.getJSONObject(i);
					String timeoutState = CacheUtil.get("WHITE_LIST_"+getEntId()+"_"+data.getString("PHONENUM"));
					data.put("TIMEOUT_STATE", "Y".equals(timeoutState)?"Y":"N");
				}
				result.put("data", dataArray);
			}
		}
		return result;
	}
	/**
	 *白名单详情
	 * @return
	 */
	@WebControl(name="edit",type=Types.RECORD)
	@InfAuthCheck(resId = "cc-base-system-hbmd-bmd",msg = "您无权访问!")
	public JSONObject edit(){
		return this.queryForRecord(new EasyRecord("CC_WHITE_LIST","ENT_ID", "PHONENUM")
				.setPrimaryValues(this.getEntId(),param.getString("white.PHONENUM")));
	}
	
	
	
	
	
	
	
	
	
	
	
}
