package com.yunqu.cc.eorder.service.notice;

import java.sql.SQLException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.service.impl.BaseCacheServiceImpl;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.EasyQueryUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.eorder.base.Constants;

public class NoticeCacheService  extends BaseCacheServiceImpl {

	@Override
	@SuppressWarnings("unchecked")
	public <T> T getCache(String entId, String busiOrderId, String type) {
		// TODO Auto-generated method stub
		String[] split = type.split("_");
		String types=split[0];
		String code=split[1];
		
		String key = getCacheKey(entId, busiOrderId, type);//先从缓存里获取
		
		T val = cache.get(key);
		JSONObject json = null;
		//从缓存里获取不到，则从数据库加载
		if(val==null){
			String schema = SchemaService.findSchemaByEntId(entId);
			if(StringUtils.isBlank(schema)){
				logger.error("根据企业ID获取数据源名称为空,企业ID:"+entId);
				return null;
			}
			//数据不存在时,避免重复查库
			Long timestamp = cache.get(key+"-getTimestamp");
			if(timestamp!=null && timestamp>0 && System.currentTimeMillis()-timestamp<60000){
				logger.error("无法从数据库查询到orderFlow信息,60s内不能重复查询,entId="+entId+",busiOrderId="+busiOrderId+",types="+types);
				return null;
			}
			  try {
				    EasySQL sql = new EasySQL("SELECT * FROM "+ schema +".C_BO_ORDER_ARRIVE_SETTING ");
					sql.append("WHERE EP_CODE = ? ");
					sql.append("AND BUSI_ORDER_ID = ? ");
					sql.append("AND TYPE = ? ");
					switch(types) { // 1-用户 2-部门 3-角色 4-工作组
					case Constants.ORDER_NOTICE_TYPE_USER: 
						sql.append("AND USER_CODE = ? ");break;
					case Constants.ORDER_NOTICE_TYPE_DEPT:
						sql.append("AND DEPT_CODE = ? ");break;
					case Constants.ORDER_NOTICE_TYPE_ROLE:
						sql.append("AND ROLE_CODE = ? ");break;
					case Constants.ORDER_NOTICE_TYPE_WORKGROUP:
						sql.append("AND WORKGROUP_NAME = ? ");break;
					}
				  List<JSONObject> list = EasyQueryUtil.getBusiQuery().queryForList(sql.getSQL(), new Object[]{entId,busiOrderId,types,code}, new JSONMapperImpl());
					if(list!=null && list.size()>0){
						json = new JSONObject();
						json.put("nodes", list);
						json.put("busiOrderId", busiOrderId);
						json.put("entId", entId);
						json.put("schema", schema);
						//重新查询到时，需要更新缓存时间
						json.put("CACHE_TIME", DateUtil.getCurrentDateStr());
						cache.put(key, json.toJSONString(),getCacheSurvivalTime());
					}
			} catch (SQLException e) {
				logger.error("缓存接口调用方出错:"+e.getMessage(),e);
			}finally {
				if(json!=null){
					//重新查询到时，需要更新缓存时间
					json.put("CACHE_TIME", DateUtil.getCurrentDateStr());
					cache.put(key,json.toJSONString(),getCacheSurvivalTime());
				}
				cache.put(key+"-getTimestamp",System.currentTimeMillis());
			}
		}else{
			if(val instanceof String && ((String) val).trim().startsWith("{") && ((String) val).trim().endsWith("}")){
				json = JsonUtil.toJSONObject((String)val);
			}
		}
		if(json==null){
			logger.error("无法从数据库查询到orderFlow信息,entId="+entId+",busiOrderId="+busiOrderId+",types="+types);
			return null;
		}
		return (T) json;
	}

	/**
	 * @param busiId	type_code
	 */
	@Override
	public String getCacheKey(String entId, String busiOrderId, String busiId) {
		// TODO Auto-generated method stub
		return Constants.CK_CC_ORDER_NOTICE + busiId;
	}
}
