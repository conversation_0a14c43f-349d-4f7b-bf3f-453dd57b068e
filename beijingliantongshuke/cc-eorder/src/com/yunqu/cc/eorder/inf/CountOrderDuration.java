package com.yunqu.cc.eorder.inf;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.IBaseService;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.orderext.base.Constants;
import com.yq.orderext.model.WorkTimeModel;
import com.yq.orderext.util.OrderWorkUtil;
import com.yunqu.cc.eorder.base.QueryFactory;
import com.yunqu.cc.eorder.log.CommonLogger;
import com.yunqu.cc.eorder.log.NoticeLogger;
import com.yunqu.cc.eorder.service.config.OrderGlobalCfgService;
import com.yunqu.cc.eorder.thread.ThreadMgr;

/**
 * Title:计算工单历时
 * Description:
 * Company:云趣科技
 * <AUTHOR>
 * @Date:2020/04/15
 *
 */
public class CountOrderDuration extends IBaseService {
	private Logger logger = CommonLogger.getLogger("task");

	private static boolean isRun = false;
	
	@Override
	public String getServiceId() {
		return ServiceID.CC_EORDER_COUNT_DURATION;
	}

	@Override
	public String getName() {
		// TODO Auto-generated method stub
		return "计算工单历时接口";
	}

	@Override
	public JSONObject invokeMethod(JSONObject json) throws ServiceException {
		logger.info("启动[" + getName()+ "]务...");
		if (isRun) {
			String serialId = json.getString("serialId");
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("serialId", serialId);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "定时任务正在运行中:" + getName());
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务正在运行中:"+getName());
			return result;
		}
		try {
			isRun = true;
			logger.info("开始执行["+ getName() +"]定时任务...");
			JSONObject result = execute(json);
			logger.info("结束执行["+ getName() +"]定时任务...");
			return result;
//			if (!com.yunqu.cc.eorder.base.Constants.getEnableJob()) {
//				String serialId = json.getString("serialId");
//				JSONObject result = JsonUtil.createInfRespJson(json);
//				result.put("serialId", serialId);
//				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//				result.put("respDesc", "定时任务配置项不启用");
//				logger.error(CommonUtil.getClassNameAndMethod(this)+"定时任务配置项不启用");
//				return result;
//			}
//			if (isRun) {
//				String serialId = json.getString("serialId");
//				JSONObject result = JsonUtil.createInfRespJson(json);
//				result.put("serialId", serialId);
//				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//				result.put("respDesc", "定时任务正在运行中:" + getName());
//				logger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务正在运行中:"+getName());
//				return result;
//			} else {
//				isRun = true;
//				logger.info("开始执行["+ getName() +"]定时任务...");
//				JSONObject result = execute(json);
//				logger.info("结束执行["+ getName() +"]定时任务...");
//				return result;
//			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			String serialId = json.getString("serialId");
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("serialId", serialId);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", e.getMessage());
			logger.error(e.getMessage(), e);
			return result;
		} finally {
			isRun = false;
			logger.info("结束["+ getName()+"]任务...");
		}
	}
	
	private JSONObject execute(JSONObject json) {
		String command = json.getString("command");
		if(ServiceCommand.ORDER_COUNT_DURATION.equals(command)) {
			return countDuration(json);
		}else {
			String serialId = json.getString("serialId");
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("serialId", serialId);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 不存在的command,请检查:"+command);
			return result;
		}
	}

	/**
	 * 计算工单历时
	 * @param json
	 * @return
	 */
	private JSONObject countDuration(JSONObject json) {
		JSONObject resultJson = JsonUtil.createInfRespJson(json);
		try {
			String currentDateStr = DateUtil.getCurrentDateStr();
			JSONArray entModle = SchemaService.findEntBusiSchema(); 
			
			ExecutorService fixedThreadPool = ThreadMgr.getInstance().getScheOneService();
			
			Collection<Callable<Boolean>> callables = new ArrayList<>();
			for (int i=0;i<entModle.size();i++) {// 遍历所有企业
				String schema = entModle.getJSONObject(i).getString("SCHEMA_NAME");
				String busiOrderId = entModle.getJSONObject(i).getString("BUSI_ORDER_ID");
				String entId = entModle.getJSONObject(i).getString("ENT_ID");
				
				Callable<Boolean> countDurationCallable = new Callable<Boolean>() {
					@Override
					public Boolean call() throws Exception {
						// TODO Auto-generated method stub
						return countDuration(currentDateStr, schema, entId, busiOrderId);
					}
				};
				callables.add(countDurationCallable);
			}
			// 启用一个固定线程池分别对每个企业进行同时计时,计时线程都完成后再返回数据
			List<Future<Boolean>> futureList = fixedThreadPool.invokeAll(callables);
			resultJson.put("respCode", GWConstants.RET_CODE_SUCCESS);
			resultJson.put("respDesc", "计算完成");
		} catch (InterruptedException e) {
			resultJson.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			resultJson.put("respDesc", "计算工单历时出错！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 计算工单历时出错！", e);
	        Thread.currentThread().interrupt();
	    } catch (Exception e) {
			resultJson.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			resultJson.put("respDesc", "计算工单历时出错！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 计算工单历时出错！", e);
		}
		return resultJson;
	}
	
	private boolean countDuration(String currentDateStr, String schema, String entId, String busiOrderId) {
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			// 优先扫描服务接口，如果有则优先使用服务接口的计时规则
			List<String> serviceList = ServiceContext.findByPrefix("CC-COUNT-ORDER-DURATION-");
			if (serviceList!=null && serviceList.size()>0) {
				JSONObject params = new JSONObject();
				params.put("currentDateStr", currentDateStr);
				params.put("schema", schema);
				params.put("entId", entId);
				params.put("busiOrderId", busiOrderId);
				params.put("command", "countDuration");
				boolean result = true;
				for (String serviceId : serviceList) {
					IService service = ServiceContext.getService(serviceId);
					logger.info(CommonUtil.getClassNameAndMethod(this) + " serviceId[" + serviceId + "] param:" + params.toJSONString());
					try {
						JSONObject invokeResult = service.invoke(params);
						logger.info(CommonUtil.getClassNameAndMethod(this) + " serviceId[" + serviceId + "] result:" + JSONObject.toJSONString(invokeResult));
						if(invokeResult != null && invokeResult.getString("respCode").equals("500")) {
							result = false;
						}
					} catch (Exception e) {
						logger.error(e.getMessage(), e);
					}
				}
				return result;
			}
			// 按照工单默认三种方式计时
			WorkTimeModel workTime = OrderGlobalCfgService.getInstance().getWorkTimeModel(schema, entId, busiOrderId);
			if (workTime == null) {
				logger.warn(CommonUtil.getClassNameAndMethod(this)+" 计算 "+schema+" 工单历时没有找到时间模型配置信息！");
				return false;
			}
			String endTime = DateUtil.getCurrentDateStr();
			String beginTime = DateUtil.addDay(DateUtil.TIME_FORMAT, endTime, -31);//开始时间为31天前
			EasySQL countSql = new EasySQL();
			countSql.append("SELECT count(1) NUM");
			countSql.append("FROM "+schema+".C_BO_BASE_ORDER t");
			countSql.append("LEFT JOIN ACT_RU_TASK t2 ON t.PROC_INST_ID=t2.PROC_INST_ID_ ");
			countSql.append("LEFT JOIN "+ schema +".C_BO_ORDER_EX t3 ON t3.ORDER_ID=t.ID");
			countSql.append("WHERE 1=1");
			countSql.append(DictConstants.DICT_SY_YN_N, "AND t.CALC_DURATION_DONE=?");
			//过滤掉挂起的工单
//			sql.append("AND t.IS_HALT='N'");
			countSql.append(entId, "AND t.EP_CODE=?");
			countSql.append(busiOrderId, "AND t.BUSI_ORDER_ID=?");
			countSql.append(Constants.ORDER_STATUS_HANDLE, "AND t.STATUS=?");
			countSql.append(beginTime, "AND t3.DEAL_TIME>=?", false);
			countSql.append(endTime, "AND t3.DEAL_TIME<=?", false);
			String num = query.queryForString(countSql.getSQL(), countSql.getParams());
			if (StringUtils.isBlank(num)) {
				logger.warn(CommonUtil.getClassNameAndMethod(this)+" 计算 "+entId+" 工单历时获取不到工单数量！");
				return false;
			}
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 计算 "+entId+" 工单历时获取到工单数量: "+ num);
			long countNum = Long.parseLong(num);
			// 一页多少条数据
			int onePageSize = 1000;
			// 总共多少页数据
			long totalPage = countNum%onePageSize>0? countNum/onePageSize+1: countNum/onePageSize;
			for (int i=1; i<=totalPage; i++) {
				executeBatch(currentDateStr, schema, entId, busiOrderId, query, workTime, onePageSize, i);
			}
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 计算[" + entId + "] 工单历时成功");
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 计算 "+entId+" 工单历时出错！", e);
			return false;
		}
	}

	private void executeBatch(String currentDateStr, String schema, String entId, String busiOrderId, EasyQuery query,
			WorkTimeModel workTime, int onePageSize, int pageNum) throws SQLException {
		EasySQL sql = new EasySQL();
		sql.append("SELECT t.ID,t.ORDER_NO,t.PROC_INST_ID,t.TIME_LIMIT,t3.CURR_HANDLER_NAME,t3.CURR_HANDLER, t.CREATE_TIME, t.SEND_TIME, t.SEND_TIME_EX, t3.IS_HALT, t3.HALT_LONG, t3.HALT_UPDATE_TIME,");
		DBTypes dbTypes = query.getTypes();
		if (dbTypes == DBTypes.MYSQL) {
			sql.append("DATE_FORMAT(t2.CREATE_TIME_, '%Y-%m-%d %H:%i:%s') TASK_START_TIME");
		} else if (dbTypes == DBTypes.ORACLE) {
			sql.append("TO_CHAR(t2.CREATE_TIME_, 'yyyy-mm-dd hh24:mi:ss') TASK_START_TIME");
		} else if (dbTypes == DBTypes.PostgreSql) {
			sql.append("TO_CHAR(t2.CREATE_TIME_, 'yyyy-mm-dd hh24:mi:ss') TASK_START_TIME");
		} else {
			sql.append("t2.CREATE_TIME_ TASK_START_TIME");
		}
		sql.append("FROM "+schema+".C_BO_BASE_ORDER t");
		sql.append("LEFT JOIN ACT_RU_TASK t2 ON t.PROC_INST_ID=t2.PROC_INST_ID_ ");
		sql.append("LEFT JOIN "+ schema +".C_BO_ORDER_EX t3 ON t3.ORDER_ID=t.ID");
		sql.append("WHERE 1=1");
		sql.append(DictConstants.DICT_SY_YN_N, "AND t.CALC_DURATION_DONE=?");
		// 过滤掉挂起的工单
		// sql.append("AND t.IS_HALT='N'");
		sql.append(entId, "AND t.EP_CODE=?");
		sql.append(busiOrderId, "AND t.BUSI_ORDER_ID=?");
		sql.append(Constants.ORDER_STATUS_HANDLE, "AND t.STATUS=?");
		List<Object[]> params = new ArrayList<>();
		
		logger.info("处理["+ entId +"]企业历时统计["+ onePageSize +"]["+ pageNum +"]");
		Map<String, String> distinctMap = new HashMap<>();
		/*List<JSONObject> noticeArray = new ArrayList<>();
		OrderNoticeCfg noticeCfg = OrderGlobalCfgService.getCache(entId, busiOrderId);*/
		String updateSql = "UPDATE "+schema+".C_BO_ORDER_EX SET DURATION=?, DURATION_EX=?, TOTAL_DURATION=?, TOTAL_DURATION_EX=?, DURATION_BUSI=?,HALT_UPDATE_TIME=?,HALT_LONG=?,IS_TIMEOUT=? WHERE ORDER_ID=? ";
		query.queryForList(sql.getSQL(), sql.getParams(), pageNum, onePageSize, new EasyRowMapper<JSONObject>() {
			@Override
			@SuppressWarnings("unchecked")
			public JSONObject mapRow(ResultSet rs, int arg1) {
				// TODO Auto-generated method stub
				Object[] param = new Object[9];
				try {
					String orderId = rs.getString("ID");
					if (distinctMap.containsKey(orderId)) {
						return null;
					} else {
						int cal = 0;
						String taskStartTime = rs.getString("TASK_START_TIME");
						String createTime = rs.getString("CREATE_TIME");
						String sendTime = rs.getString("SEND_TIME");
						String sendTimeEx = rs.getString("SEND_TIME_EX");
						String orderNo = rs.getString("ORDER_NO");
						int tineLimit = rs.getInt("TIME_LIMIT");
						//获取工单挂起时间和上次更新时间
						String isHalt = rs.getString("IS_HALT");
						String haltUpdateTime = rs.getString("HALT_UPDATE_TIME");
						String haltLong = StringUtils.isBlank(rs.getString("HALT_LONG"))?"0":rs.getString("HALT_LONG");
						long maxTimeLong = tineLimit*60*60;
						int duration = DateUtil.bwSeconds(currentDateStr, sendTime);
						int durationEx = (int) OrderWorkUtil.getWorkLong(sendTime, currentDateStr, workTime);
						int totalDuration = DateUtil.bwSeconds(currentDateStr, createTime);
						int totalDurationEx = (int) OrderWorkUtil.getWorkLong(sendTimeEx, currentDateStr, workTime);
						int durationBusi = (int) OrderWorkUtil.getWorkLong(taskStartTime, currentDateStr, workTime);
						int haltDurationTime = "Y".equals(isHalt)?(int) OrderWorkUtil.getWorkLong(haltUpdateTime, currentDateStr, workTime) : 0;
						int totalHaltTime = (int) (haltDurationTime + Double.valueOf(haltLong).intValue());
						String isTimeout = totalDurationEx>maxTimeLong? "Y": "N";
						param[0] = duration;
						param[1] = durationEx>99999999? 99999999 : durationEx;				// 有些坏数据，防止超过数据库字段长度
						param[2] = totalDuration;
						param[3] = totalDurationEx>99999999? 99999999 : totalDurationEx;	// 有些坏数据，防止超过数据库字段长度
						param[4] = durationBusi;
						param[5] = "Y".equals(isHalt)?currentDateStr : haltUpdateTime;
						param[6] = totalHaltTime;
						param[7] = isTimeout;
						param[8] = orderId;
						params.add(param);
						
						distinctMap.put(orderId, "handled");
					}
					// 200条批处理一次
					if (params.size()%100 == 0) {
						query.executeBatch(updateSql, params);
						params.clear();
						Thread.sleep(2000);
					}
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this)+"参数计算失败"+e.getMessage(), e);
				}
				return null;
			}
		});
		if (params.size()>0) {
			query.executeBatch(updateSql, params);
		}
	}
	
}
