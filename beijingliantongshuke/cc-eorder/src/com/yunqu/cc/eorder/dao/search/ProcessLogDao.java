package com.yunqu.cc.eorder.dao.search;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.eorder.base.AppDaoContext;

@WebObject(name = "ProcessLog")
public class ProcessLogDao extends AppDaoContext {
	
	@WebControl(name = "getDatagridList", type = Types.LIST)
	public JSONObject getDatagridList() {
		EasySQL sql = new EasySQL(" select * from "+getTableName("C_WF_PROCESS_LOG"));
		sql.append("  where 1=1 ");
		sql.append(getEntId()," and ENT_ID = ? ");
		sql.append(getBusiOrderId()," and BUSI_ORDER_ID =? ");
		sql.append(param.getString("flowKey")," and FLOW_KEY = ? ");
		sql.append(param.getString("createUser")," and CREATE_USER = ? ");
		sql.append(param.getString("operType")," and OPER_TYPE = ? ");
		sql.append(param.getString("content")," and CONTENT =? ");
		String limitDate=param.getString("limitDate");
		if(StringUtils.notBlank(limitDate)) {
			String[] split = limitDate.split("~");
			sql.append(split[0].replace(" ","") + " 00:00:00"," AND CREATE_TIME >= ?");
			sql.append(split[1].replace(" ","") + " 23:59:59"," AND CREATE_TIME <= ?");
		}
		
		
		sql.append(" order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
}
