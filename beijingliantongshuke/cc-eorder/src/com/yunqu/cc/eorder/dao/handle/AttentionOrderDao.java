package com.yunqu.cc.eorder.dao.handle;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.yunqu.cc.eorder.dao.handle.sql.OrderHandleSql;
import com.yunqu.cc.eorder.utils.OrderBusiUtil;
import com.yunqu.cc.eorder.utils.mapper.OrderListMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.eorder.base.AppDaoContext;
import com.yunqu.cc.eorder.log.CommonLogger;
import com.yunqu.cc.eorder.utils.QueryProcessUtil;

/**
 * Title:已关注工单 Description: Company:云趣科技
 *
 * <AUTHOR>
 *
 */
@WebObject(name = "attentionOrder")
public class AttentionOrderDao extends AppDaoContext {

	private Logger logger = CommonLogger.logger;

	/**
	 * 获取已关注工单的列表
	 *
	 * @return
	 */
	@WebControl(name = "getList", type = Types.LIST)
	@InfAuthCheck(resId="cc-erder-manage-attentionOrderList")
	public JSONObject getList() {
		JSONObject checkResult = OrderBusiUtil.checkParam(param,request);
		if("0".equals(checkResult.getString("state"))){
			return checkResult;
		}
		UserModel user = UserUtil.getUser(request);
		EasySQL sql = OrderHandleSql.getAttentionSql(param,user);
		return queryForPageList(sql.getSQL(), sql.getParams(), new OrderListMapperImpl(getEntId()));
	}

	/**
	 * 获取工单信息
	 *
	 * @return
	 */
	@WebControl(name = "getRecord", type = Types.RECORD)
	public JSONObject getRecord() {
		EasySQL sql = this.getEasySQL("SELECT * FROM " + getTableName("C_BO_BASE_ORDER"));
		sql.append(param.getString("ID"), " WHERE ID = ?");
		sql.append(getEntId(), " AND EP_CODE = ? ");
		sql.append(getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
		logger.debug(CommonUtil.getClassNameAndMethod(this) + "获取已关注工单(单条),sql=" + sql.getSQL() + "{"
				+ JSON.toJSONString(sql.getParams()) + "}");
		JSONObject result = this.queryForRecord(sql.getSQL(), sql.getParams(), null);
		return result;
	}

	/**
	 * 获取工单催办列表
	 * @return
	 */
	@WebControl(name = "getCuiBanList", type = Types.LIST)
	public JSONObject getCuiBanList() {
		String isPage=param.getString("isPage");//是否分页

		EasySQL sql = this.getEasySQL("SELECT distinct t1.*,(case when t2.ID is not null and t2.ID != '' then 1 else 0 end) IS_HERE FROM " + getTableName("C_BO_ORDER_CUIBAN") + " t1");

		sql.append(" left join "+ getTableName("C_CF_ATTACHMENT") + " t2 ON t1.ID = t2.BUSI_ID ");

		sql.append(" WHERE 1=1");
		sql.append(param.getString("ORDER_ID"), "AND t1.ORDER_ID = ?", false);
		sql.append(getEntId(), " AND t1.ENT_ID = ? ");
		sql.append(getBusiOrderId(), " AND t1.BUSI_ORDER_ID = ? ");
		sql.append("ORDER BY t1.CREATE_TIME");
		logger.debug(CommonUtil.getClassNameAndMethod(this) + "获取工单催办列表,sql=" + sql.getSQL() + "{"
				+ JSON.toJSONString(sql.getParams()) + "}");
		if(StringUtils.isNotBlank(isPage)){
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		}else{
			return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
		}

	}
}
