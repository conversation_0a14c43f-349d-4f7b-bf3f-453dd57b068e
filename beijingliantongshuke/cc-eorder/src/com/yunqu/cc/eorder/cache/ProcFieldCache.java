package com.yunqu.cc.eorder.cache;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.springframework.util.Assert;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.SchemaService;
import com.yq.busi.common.service.impl.BaseCacheServiceImpl;
import com.yunqu.cc.eorder.base.QueryFactory;
import com.yunqu.cc.eorder.log.CommonLogger;

public class ProcFieldCache extends BaseCacheServiceImpl {

	private static Logger logger = CommonLogger.logger;

	public static final String CACHE_PREFIX = "PROC_FIELD_CONFIG_";

	@Override
	@SuppressWarnings("unchecked")
	public <T> T getCache(String entId, String busiOrderId, String busiId) {
		String key = getCacheKey(entId, busiOrderId, busiId);//先从缓存里获取

		T val = cache.get(key);
		JSONObject json = null;
		//从缓存里获取不到，则从数据库加载
		if(val==null){
			//数据不存在时,避免重复查库
			Long timestamp = cache.get(key+"-getTimestamp");
			if(timestamp!=null && timestamp>0 && System.currentTimeMillis()-timestamp<60000){
				logger.error("无法从数据库查询到ProcFieldCache信息,60s内不能重复查询,entId="+entId+",busiOrderId="+busiOrderId+",busiId="+busiId);
				return null;
			}
			try {
				String schema = SchemaService.findSchemaByEntId(entId);

				json = findProcField(schema, entId, busiOrderId, busiId);
				logger.info(String.format("获取流程字段信息params:[entId=%s,busiOrderId=%s,busiId=%s],resp:[%s]", entId, busiOrderId, busiId, json));
				cache.put(key, json, getCacheSurvivalTime());
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
			cache.put(key+"-getTimestamp", System.currentTimeMillis());
		}else{
			if(val instanceof JSONObject){
				json = (JSONObject) val;
			}
		}

		if(json==null){
			logger.error("无法从数据库查询到ProcFieldCache信息,entId="+entId+",busiOrderId="+busiOrderId+",busiId="+busiId);
			return null;
		}

		return (T) json;
	}

	private static JSONObject findProcField(String schema, String entId, String busiOrderId, String proId) {
		try {

			EasyQuery query = QueryFactory.getQuery(entId);

			EasySQL sql = new EasySQL();
			//查询要显示的字段
			sql = new EasySQL("SELECT t1.AUTO_FORM_DB_ID, t1.FIELD_NAME,t1.FIELD_TEXT,t1.SEARCH_EXTCONFIG,t1.IS_SHOW_FIELD,t1.IS_SEARCH_FIELD FROM ");
			sql.append(schema+".C_BO_AUTO_FORM_DB_FIELD t1  WHERE 1=1");
			sql.append(" AND  exists (select DB_NAME from "+schema+".C_BO_AUTO_FORM_DB t2");
			sql.append(" where 1=1");
			sql.append(" and t1.AUTO_FORM_DB_ID = t2.DB_NAME");
			sql.append(proId," and t2.PROCESS_ID=?");
			sql.append(entId," and t2.ENT_ID = ? ");
			sql.append(busiOrderId," and t2.BUSI_ORDER_ID = ? ");
			sql.append("Y"," and t2.IS_BUSI_DB=?)");
			sql.append(entId," and t1.ENT_ID = ? ");
			sql.append(busiOrderId," and  t1.BUSI_ORDER_ID = ? ");
			sql.append("Y"," AND ( t1.IS_SHOW_FIELD = ?");
			sql.append("Y"," OR  t1.IS_SEARCH_FIELD = ?)");
			sql.append("ORDER BY SORT ASC");
			ArrayList<JSONObject> searchList = new ArrayList<>();
			ArrayList<JSONObject> showList = new ArrayList<>();
			JSONObject result = new JSONObject();
			query.queryForList(sql.getSQL(), sql.getParams(), new EasyRowMapper<JSONObject>(){
				@SuppressWarnings("unchecked")
				@Override
				public JSONObject mapRow(ResultSet result, int arg1) {
					try {
						String isShow = result.getString("IS_SHOW_FIELD");
						String isSearch = result.getString("IS_SEARCH_FIELD");
						JSONObject jsonObject = new JSONObject();
						jsonObject.put("FIELD_NAME", result.getString("FIELD_NAME"));
						jsonObject.put("FIELD_TEXT", result.getString("FIELD_TEXT"));
						jsonObject.put("AUTO_FORM_DB_ID", result.getString("AUTO_FORM_DB_ID"));
						jsonObject.put("SEARCH_EXTCONFIG", result.getString("SEARCH_EXTCONFIG"));
						if ("Y".equals(isShow)) {
							showList.add(jsonObject);
						}
						if ("Y".equals(isSearch)) {
							searchList.add(jsonObject);
						}
					} catch (SQLException e) {
						e.printStackTrace();
						logger.error(e.getMessage(), e);
					}
					return null;
				}

			});
			result.put("showList", showList);
			result.put("searchList", searchList);
  			return result;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	}

  	/**
  	 * @param busiId	authType_type_code
  	 */
	@Override
	public String getCacheKey(String entId, String busiOrderId, String busiId) {
		// TODO Auto-generated method stub
		Assert.hasText(busiId, "未获取到业务id");
		return CACHE_PREFIX + busiId;
	}
}
