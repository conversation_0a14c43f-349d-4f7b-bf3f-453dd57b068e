<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>服务目录</title>
	<style type="text/css">
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
		a:link{ color:#00adff;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="ibox-title clearfix">
			<h5>服务目录</h5>
		<div class="input-group input-group-sm pull-right">
			<button type="button" class="btn btn-sm btn-success btn-outline" onclick="CATALOG.edit()"> 新增 </button>
		</div>
	</div>
	<div class="ibox-content">
		<table class="layui-table layui-form" id="treeTable" lay-size="sm"></table>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("CATALOG");
		$(function(){
			layui.config({base: '/cc-base/static/js/'});
			ajax.daoCall({controls: ['serviceCatalog.getList'], params: {}}, function (result) {
				var data = result['serviceCatalog.getList'] ? result['serviceCatalog.getList'].data : {};
				var datas = CATALOG.formatData(data);
				CATALOG.loadTable(datas);
			})
		});
		//
		CATALOG.formatData = function(data) {
			var array = [];
			if(data) {
				for (var i = 0; i < data.length; i++) {
					var d = data[i];
					d['id'] = d.ID;
					d['pid'] = d.PARENT_ID;
					array.push(d);
				}
			}
			return array;
		}
		//渲染treetable
		CATALOG.loadTable = function(datas) {
			layui.use(['treeTable','layer','code','form'],function(){
				var o = layui.$,
					form = layui.form,
					layer = layui.layer,
					treeTable = layui.treeTable;
				var	re = treeTable.render({
					elem: '#treeTable',
					data:datas,
					icon_key: 'NAME',
					end: function(e){
						form.render();
					},
					cols: [
						{key:'NAME', title: '名称',template: function(item){
							return '<span>'+item.NAME+'</span>';
						}},
						{key:'CODE',title:'编号',align:'center'},
						{key:'SORT_NUM', title: '序号',align:'center'},
						{key:'BAKUP',title:'备注',align:'center'},
						{key:'ORDER_NO_PREFIX',title:'工单编号前缀',align:'center'},
						//无匹配字段
//						{key:'NOTICE_MINUTES',title:'app启动工单标题',align:'center'},
						
						{key:'SERVICE_CATAGORY',title:'所属服务分类',align:'center',template:function(row){
							return getDictTextByCode('ORDER_SERVICE_CATAGORY',row.SERVICE_CATAGORY);
						}},
						{key:'BUSI_CATAGORY',title:'所属业务分类',align:'center',template:function(row){
							return getDictTextByCode('ORDER_SERVICE_BUSI_CATAGORY',row.BUSI_CATAGORY);
						}},
						{key:'STATUS',title:'状态',align:'center',template:function(row){
							return getDictTextByCode('ENABLE_STATUS',row.STATUS);
						}},
						{key:'TIME_LIMIT',title:'要求时限',align:'center',template:function(row){
							return getDictTextByCode('REQUIRED_TIME',row.TIME_LIMIT);
						}},
						{key:'CREATE_USER',title:'创建人',align:'center'},
						{key:'CREATE_TIME',title:'创建时间',align:'center'},
						{title:'操作',align:'center',template: function(row){
							var id = row.ID;
							var name = row.NAME;
							var pid = row.PARENT_ID;
							return '<a href="javascript:void(0)" onclick="CATALOG.edit(\''+id+'\',\''+name+'\',\'edit\')">编辑</a> '+
								'- <a href="javascript:void(0)" onclick="CATALOG.edit(\''+id+'\',\''+name+'\',\'addChild\')">添加</a> ' +
								'- <a href="javascript:void(0)" onclick="CATALOG.del(\''+id+'\')">删除</a>';
						}}
					]
				});
			})
		}
		
		CATALOG.edit = function(id,name,type) {
			var title = "新增根目录";
			var data = {};
			if(type=="edit"){
				title = "编辑目录( "+name+" )";
				data = {ID:id};
			}else if(type=="addChild"){
				title = "添加子目录( "+name+" )";
				data = {PARENT_ID:id};
			}
			popup.layerShow({type:1,title:title,offset:'20px',area:['560px;','500px']},"${ctxPath}/pages/eorder/config/service-catalog-edit.jsp",data);
		},
		CATALOG.del = function(id) {
			if(id) {
				layer.confirm('是否确定删除当前数据？',{icon: 3, title:'确定提示',offset:'20px'},  function(index){
					var url = "${ctxPath}/servlet/serviceCatalog?action=del";
					ajax.remoteCall(url,{ID:id},function(result) {
						if(result.state=='1') {
							layer.msg(result.msg,{icon: 1,time:1200});
							CATALOG.reflesh()
						} else {
							layer.alert(getI18nValue(result.msg),{icon: 5});
							return;
						}
					});
				});
			}
		}
		CATALOG.reflesh = function(time){
			time = time ? time : 1500;
			setTimeout(function(){
				window.location.reload();//刷新当前页面.
			},time);
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_treeTable.jsp" %>