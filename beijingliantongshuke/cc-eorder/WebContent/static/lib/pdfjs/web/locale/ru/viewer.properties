# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÑÐµÐ´ÑÐ´ÑÑÐ°Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°
previous_label=ÐÑÐµÐ´ÑÐ´ÑÑÐ°Ñ
next.title=Ð¡Ð»ÐµÐ´ÑÑÑÐ°Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°
next_label=Ð¡Ð»ÐµÐ´ÑÑÑÐ°Ñ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ°
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Ð¸Ð· {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ð¸Ð· {{pagesCount}})

zoom_out.title=Ð£Ð¼ÐµÐ½ÑÑÐ¸ÑÑ
zoom_out_label=Ð£Ð¼ÐµÐ½ÑÑÐ¸ÑÑ
zoom_in.title=Ð£Ð²ÐµÐ»Ð¸ÑÐ¸ÑÑ
zoom_in_label=Ð£Ð²ÐµÐ»Ð¸ÑÐ¸ÑÑ
zoom.title=ÐÐ°ÑÑÑÐ°Ð±
presentation_mode.title=ÐÐµÑÐµÐ¹ÑÐ¸ Ð² ÑÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸Ð¸
presentation_mode_label=Ð ÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸Ð¸
open_file.title=ÐÑÐºÑÑÑÑ ÑÐ°Ð¹Ð»
open_file_label=ÐÑÐºÑÑÑÑ
print.title=ÐÐµÑÐ°ÑÑ
print_label=ÐÐµÑÐ°ÑÑ
download.title=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ
download_label=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ
bookmark.title=Ð¡ÑÑÐ»ÐºÐ° Ð½Ð° ÑÐµÐºÑÑÐ¸Ð¹ Ð²Ð¸Ð´ (ÑÐºÐ¾Ð¿Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð»Ð¸ Ð¾ÑÐºÑÑÑÑ Ð² Ð½Ð¾Ð²Ð¾Ð¼ Ð¾ÐºÐ½Ðµ)
bookmark_label=Ð¢ÐµÐºÑÑÐ¸Ð¹ Ð²Ð¸Ð´

# Secondary toolbar and context menu
tools.title=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½ÑÑ
tools_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½ÑÑ
first_page.title=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿ÐµÑÐ²ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
first_page.label=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿ÐµÑÐ²ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
first_page_label=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿ÐµÑÐ²ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
last_page.title=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
last_page.label=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
last_page_label=ÐÐµÑÐµÐ¹ÑÐ¸ Ð½Ð° Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
page_rotate_cw.title=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿Ð¾ ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐµ
page_rotate_cw.label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿Ð¾ ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐµ
page_rotate_cw_label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿Ð¾ ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐµ
page_rotate_ccw.title=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿ÑÐ¾ÑÐ¸Ð² ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐ¸
page_rotate_ccw.label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿ÑÐ¾ÑÐ¸Ð² ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐ¸
page_rotate_ccw_label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÑ Ð¿ÑÐ¾ÑÐ¸Ð² ÑÐ°ÑÐ¾Ð²Ð¾Ð¹ ÑÑÑÐµÐ»ÐºÐ¸

cursor_text_select_tool.title=ÐÐºÐ»ÑÑÐ¸ÑÑ ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Â«ÐÑÐ´ÐµÐ»ÐµÐ½Ð¸Ðµ ÑÐµÐºÑÑÐ°Â»
cursor_text_select_tool_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Â«ÐÑÐ´ÐµÐ»ÐµÐ½Ð¸Ðµ ÑÐµÐºÑÑÐ°Â»
cursor_hand_tool.title=ÐÐºÐ»ÑÑÐ¸ÑÑ ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Â«Ð ÑÐºÐ°Â»
cursor_hand_tool_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Â«Ð ÑÐºÐ°Â»

scroll_vertical.title=ÐÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð²ÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ¾ÐºÑÑÑÐºÑ
scroll_vertical_label=ÐÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½Ð°Ñ Ð¿ÑÐ¾ÐºÑÑÑÐºÐ°
scroll_horizontal.title=ÐÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð³Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ¾ÐºÑÑÑÐºÑ
scroll_horizontal_label=ÐÐ¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½Ð°Ñ Ð¿ÑÐ¾ÐºÑÑÑÐºÐ°
scroll_wrapped.title=ÐÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð¼Ð°ÑÑÑÐ°Ð±Ð¸ÑÑÐµÐ¼ÑÑ Ð¿ÑÐ¾ÐºÑÑÑÐºÑ
scroll_wrapped_label=ÐÐ°ÑÑÑÐ°Ð±Ð¸ÑÑÐµÐ¼Ð°Ñ Ð¿ÑÐ¾ÐºÑÑÑÐºÐ°

spread_none.title=ÐÐµ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐµÐ¶Ð¸Ð¼ ÑÐ°Ð·Ð²Ð¾ÑÐ¾ÑÐ¾Ð² ÑÑÑÐ°Ð½Ð¸Ñ
spread_none_label=ÐÐµÐ· ÑÐ°Ð·Ð²Ð¾ÑÐ¾ÑÐ¾Ð² ÑÑÑÐ°Ð½Ð¸Ñ
spread_odd.title=Ð Ð°Ð·Ð²Ð¾ÑÐ¾ÑÑ Ð½Ð°ÑÐ¸Ð½Ð°ÑÑÑÑ Ñ Ð½ÐµÑÑÑÐ½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð² ÑÑÑÐ°Ð½Ð¸Ñ
spread_odd_label=ÐÐµÑÑÑÐ½ÑÐµ ÑÑÑÐ°Ð½Ð¸ÑÑ ÑÐ»ÐµÐ²Ð°
spread_even.title=Ð Ð°Ð·Ð²Ð¾ÑÐ¾ÑÑ Ð½Ð°ÑÐ¸Ð½Ð°ÑÑÑÑ Ñ ÑÑÑÐ½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð² ÑÑÑÐ°Ð½Ð¸Ñ
spread_even_label=Ð§ÑÑÐ½ÑÐµ ÑÑÑÐ°Ð½Ð¸ÑÑ ÑÐ»ÐµÐ²Ð°

# Document properties dialog box
document_properties.title=Ð¡Ð²Ð¾Ð¹ÑÑÐ²Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_label=Ð¡Ð²Ð¾Ð¹ÑÑÐ²Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_file_name=ÐÐ¼Ñ ÑÐ°Ð¹Ð»Ð°:
document_properties_file_size=Ð Ð°Ð·Ð¼ÐµÑ ÑÐ°Ð¹Ð»Ð°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
document_properties_title=ÐÐ°Ð³Ð¾Ð»Ð¾Ð²Ð¾Ðº:
document_properties_author=ÐÐ²ÑÐ¾Ñ:
document_properties_subject=Ð¢ÐµÐ¼Ð°:
document_properties_keywords=ÐÐ»ÑÑÐµÐ²ÑÐµ ÑÐ»Ð¾Ð²Ð°:
document_properties_creation_date=ÐÐ°ÑÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ:
document_properties_modification_date=ÐÐ°ÑÐ° Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ:
document_properties_producer=ÐÑÐ¾Ð¸Ð·Ð²Ð¾Ð´Ð¸ÑÐµÐ»Ñ PDF:
document_properties_version=ÐÐµÑÑÐ¸Ñ PDF:
document_properties_page_count=Ð§Ð¸ÑÐ»Ð¾ ÑÑÑÐ°Ð½Ð¸Ñ:
document_properties_page_size=Ð Ð°Ð·Ð¼ÐµÑ ÑÑÑÐ°Ð½Ð¸ÑÑ:
document_properties_page_size_unit_inches=Ð´ÑÐ¹Ð¼Ð¾Ð²
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=ÐºÐ½Ð¸Ð¶Ð½Ð°Ñ
document_properties_page_size_orientation_landscape=Ð°Ð»ÑÐ±Ð¾Ð¼Ð½Ð°Ñ
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ÐÑÑÑÑÑÐ¹ Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑ Ð² Web:
document_properties_linearized_yes=ÐÐ°
document_properties_linearized_no=ÐÐµÑ
document_properties_close=ÐÐ°ÐºÑÑÑÑ

print_progress_message=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ° Ðº Ð¿ÐµÑÐ°ÑÐ¸â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÐÑÐ¼ÐµÐ½Ð°

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ/ÑÐºÑÑÑÑ Ð±Ð¾ÐºÐ¾Ð²ÑÑ Ð¿Ð°Ð½ÐµÐ»Ñ
toggle_sidebar_notification.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ/ÑÐºÑÑÑÑ Ð±Ð¾ÐºÐ¾Ð²ÑÑ Ð¿Ð°Ð½ÐµÐ»Ñ (Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ñ Ð¸Ð¼ÐµÐµÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°Ð½Ð¸Ðµ/Ð²Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ)
toggle_sidebar_label=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ/ÑÐºÑÑÑÑ Ð±Ð¾ÐºÐ¾Ð²ÑÑ Ð¿Ð°Ð½ÐµÐ»Ñ
document_outline.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ ÑÐ¾Ð´ÐµÑÐ¶Ð°Ð½Ð¸Ðµ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ° (Ð´Ð²Ð¾Ð¹Ð½Ð¾Ð¹ ÑÐµÐ»ÑÐ¾Ðº, ÑÑÐ¾Ð±Ñ ÑÐ°Ð·Ð²ÐµÑÐ½ÑÑÑ/ÑÐ²ÐµÑÐ½ÑÑÑ Ð²ÑÐµ ÑÐ»ÐµÐ¼ÐµÐ½ÑÑ)
document_outline_label=Ð¡Ð¾Ð´ÐµÑÐ¶Ð°Ð½Ð¸Ðµ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°
attachments.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ Ð²Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
attachments_label=ÐÐ»Ð¾Ð¶ÐµÐ½Ð¸Ñ
thumbs.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ Ð¼Ð¸Ð½Ð¸Ð°ÑÑÑÑ
thumbs_label=ÐÐ¸Ð½Ð¸Ð°ÑÑÑÑ
findbar.title=ÐÐ°Ð¹ÑÐ¸ Ð² Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐµ
findbar_label=ÐÐ°Ð¹ÑÐ¸

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÐÐ¸Ð½Ð¸Ð°ÑÑÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÑ {{page}}

# Find panel button title and messages
find_input.title=ÐÐ°Ð¹ÑÐ¸
find_input.placeholder=ÐÐ°Ð¹ÑÐ¸ Ð² Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐµâ¦
find_previous.title=ÐÐ°Ð¹ÑÐ¸ Ð¿ÑÐµÐ´ÑÐ´ÑÑÐµÐµ Ð²ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ðµ ÑÑÐ°Ð·Ñ Ð² ÑÐµÐºÑÑ
find_previous_label=ÐÐ°Ð·Ð°Ð´
find_next.title=ÐÐ°Ð¹ÑÐ¸ ÑÐ»ÐµÐ´ÑÑÑÐµÐµ Ð²ÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ðµ ÑÑÐ°Ð·Ñ Ð² ÑÐµÐºÑÑ
find_next_label=ÐÐ°Ð»ÐµÐµ
find_highlight=ÐÐ¾Ð´ÑÐ²ÐµÑÐ¸ÑÑ Ð²ÑÐµ
find_match_case_label=Ð¡ ÑÑÑÑÐ¾Ð¼ ÑÐµÐ³Ð¸ÑÑÑÐ°
find_entire_word_label=Ð¡Ð»Ð¾Ð²Ð° ÑÐµÐ»Ð¸ÐºÐ¾Ð¼
find_reached_top=ÐÐ¾ÑÑÐ¸Ð³Ð½ÑÑ Ð²ÐµÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶ÐµÐ½Ð¾ ÑÐ½Ð¸Ð·Ñ
find_reached_bottom=ÐÐ¾ÑÑÐ¸Ð³Ð½ÑÑ ÐºÐ¾Ð½ÐµÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶ÐµÐ½Ð¾ ÑÐ²ÐµÑÑÑ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Ð¸Ð· {{total}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ñ
find_match_count[two]={{current}} Ð¸Ð· {{total}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count[few]={{current}} Ð¸Ð· {{total}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count[many]={{current}} Ð¸Ð· {{total}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count[other]={{current}} Ð¸Ð· {{total}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count_limit[one]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ñ
find_match_count_limit[two]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count_limit[few]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count_limit[many]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_match_count_limit[other]=ÐÐ¾Ð»ÐµÐµ {{limit}} ÑÐ¾Ð²Ð¿Ð°Ð´ÐµÐ½Ð¸Ð¹
find_not_found=Ð¤ÑÐ°Ð·Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð°

# Error panel labels
error_more_info=ÐÐµÑÐ°Ð»Ð¸
error_less_info=Ð¡ÐºÑÑÑÑ Ð´ÐµÑÐ°Ð»Ð¸
error_close=ÐÐ°ÐºÑÑÑÑ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ÑÐ±Ð¾ÑÐºÐ°: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ð¡Ð¾Ð¾Ð±ÑÐµÐ½Ð¸Ðµ: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ð¡ÑeÐº: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ð¡ÑÑÐ¾ÐºÐ°: {{line}}
rendering_error=ÐÑÐ¸ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ð¸ ÑÑÑÐ°Ð½Ð¸ÑÑ Ð¿ÑÐ¾Ð¸Ð·Ð¾ÑÐ»Ð° Ð¾ÑÐ¸Ð±ÐºÐ°.

# Predefined zoom values
page_scale_width=ÐÐ¾ ÑÐ¸ÑÐ¸Ð½Ðµ ÑÑÑÐ°Ð½Ð¸ÑÑ
page_scale_fit=ÐÐ¾ ÑÐ°Ð·Ð¼ÐµÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
page_scale_auto=ÐÐ²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸
page_scale_actual=Ð ÐµÐ°Ð»ÑÐ½ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ÐÑÐ¸Ð±ÐºÐ°
loading_error=ÐÑÐ¸ Ð·Ð°Ð³ÑÑÐ·ÐºÐµ PDF Ð¿ÑÐ¾Ð¸Ð·Ð¾ÑÐ»Ð° Ð¾ÑÐ¸Ð±ÐºÐ°.
invalid_file_error=ÐÐµÐºÐ¾ÑÑÐµÐºÑÐ½ÑÐ¹ Ð¸Ð»Ð¸ Ð¿Ð¾Ð²ÑÐµÐ¶Ð´ÑÐ½Ð½ÑÐ¹ PDF-ÑÐ°Ð¹Ð».
missing_file_error=PDF-ÑÐ°Ð¹Ð» Ð¾ÑÑÑÑÑÑÐ²ÑÐµÑ.
unexpected_response_error=ÐÐµÐ¾Ð¶Ð¸Ð´Ð°Ð½Ð½ÑÐ¹ Ð¾ÑÐ²ÐµÑ ÑÐµÑÐ²ÐµÑÐ°.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[ÐÐ½Ð½Ð¾ÑÐ°ÑÐ¸Ñ {{type}}]
password_label=ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð¿Ð°ÑÐ¾Ð»Ñ, ÑÑÐ¾Ð±Ñ Ð¾ÑÐºÑÑÑÑ ÑÑÐ¾Ñ PDF-ÑÐ°Ð¹Ð».
password_invalid=ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¿Ð°ÑÐ¾Ð»Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¾Ð²Ð°.
password_ok=OK
password_cancel=ÐÑÐ¼ÐµÐ½Ð°

printing_not_supported=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ¶Ð´ÐµÐ½Ð¸Ðµ: Ð ÑÑÐ¾Ð¼ Ð±ÑÐ°ÑÐ·ÐµÑÐµ Ð½Ðµ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ Ð¿ÐµÑÐ°ÑÑ.
printing_not_ready=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ¶Ð´ÐµÐ½Ð¸Ðµ: PDF Ð½Ðµ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½ Ð´Ð»Ñ Ð¿ÐµÑÐ°ÑÐ¸.
web_fonts_disabled=ÐÐµÐ±-ÑÑÐ¸ÑÑÑ Ð¾ÑÐºÐ»ÑÑÐµÐ½Ñ: Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ Ð²ÑÑÑÐ¾ÐµÐ½Ð½ÑÐµ PDF-ÑÑÐ¸ÑÑÑ.
document_colors_not_allowed=PDF-Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°Ð¼ Ð½Ðµ ÑÐ°Ð·ÑÐµÑÐµÐ½Ð¾ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐ²Ð¾Ð¸ ÑÐ²ÐµÑÐ°: Ð² Ð±ÑÐ°ÑÐ·ÐµÑÐµ Ð¾ÑÐºÐ»ÑÑÑÐ½ Ð¿Ð°ÑÐ°Ð¼ÐµÑÑ Â«Ð Ð°Ð·ÑÐµÑÐ¸ÑÑ Ð²ÐµÐ±-ÑÐ°Ð¹ÑÐ°Ð¼ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÐ²Ð¾Ð¸ ÑÐ²ÐµÑÐ°Â».
