package com.yunqu.cc.mixgw.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.AppBaseService;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.cache.impl.BigMonitorCache;
import com.yunqu.cc.mixgw.cache.impl.MapCache;
import com.yunqu.cc.mixgw.dao.BigMonitorDao;
import com.yunqu.cc.mixgw.model.Agents;
import com.yunqu.cc.mixgw.model.bigmonitor.CallHourModel;
import com.yunqu.cc.mixgw.model.bigmonitor.ServiceLevelModel;
import com.yunqu.cc.mixgw.model.bigmonitor.TotalCallModel;

/**
 * Title 语音监控service
 * Description: 生成语音监控的数据存入缓存
 * Copyright: Copyright (c) 2019
 * Company: 云趣科技
 * <AUTHOR>
 * @version 1.0
 *
 */
public class BigMonitorService extends AppBaseService {

	private Logger logger = CommonLogger.getLogger("big-monitor");
	
	private static class Holder {
		private static BigMonitorService instance = new BigMonitorService();
	}
	
	public static BigMonitorService getInstance() {
		return Holder.instance;
	}
	
	private BigMonitorDao dao = BigMonitorDao.getInstance();
	
	/**
	 * 总体话务
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void totalCall(String schema, String entId) {
		JSONObject totalCallJson = dao.totalCall(schema, entId);
		if(totalCallJson != null) {
			TotalCallModel totalCallModel = new TotalCallModel(totalCallJson);
			
			// 获取所有技能组成员数=在线坐席数
			Set<String> onlineAgentSet = MapCache.getByType(MapCache. GW_VOICE_AGENT_SKILL_ALL + "_" + entId);
			totalCallModel.setAgentCount(onlineAgentSet !=  null ? onlineAgentSet.size() : 0);
			
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] totalCallJson:" + JSONObject.toJSONString(totalCallModel));
			}
			BigMonitorCache.getInstance().put(BigMonitorCache. GW_TOTAL_CALL + "_" + entId, totalCallModel);
		} else {
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] 移除缓存信息");
			}
			BigMonitorCache.getInstance().remove(BigMonitorCache. GW_TOTAL_CALL + "_" + entId);
		}
		
		// 当天服务水平
		serviceLevel(entId, totalCallJson);
	}
	
	/**
	 * 当天服务水平
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void serviceLevel(String entId, JSONObject info) {
		if(info == null) {
			info = new JSONObject();
		}
		ServiceLevelModel serviceLevelModel = new ServiceLevelModel(info);

		// 获取企业当前排队数
		int currQueueCount = (MapCache.getByType(MapCache. GW_QUEUE_CALL_COUNT + entId) == null ? 0: (int)MapCache.getByType(MapCache. GW_QUEUE_CALL_COUNT + entId));
		serviceLevelModel.setCurrQueueCount(currQueueCount);
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] serviceLevelModel:" + JSONObject.toJSONString(serviceLevelModel));
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_SERVICE_LEAVE + "_" + entId, serviceLevelModel);
	}
	
	/**
	 * 24小时话务信息(呼入)
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void callInfoByHour(String schema, String entId) {
		List<CallHourModel> callHourList = dao.callInfoByHour(schema, entId);
		if(CommonUtil.listIsNotNull(callHourList)) {
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + JSONObject.toJSONString(callHourList));
			}
			BigMonitorCache.getInstance().put(BigMonitorCache. GW_CALL_INFO_BY_HOUR + "_" + entId, callHourList);
		} else {
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] 移除缓存信息");
			}
			BigMonitorCache.getInstance().remove(BigMonitorCache. GW_CALL_INFO_BY_HOUR + "_" + entId);
		}
	}

	
	/**
	 * 通话时长分析
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void callInfoByBill(String schema, String entId) {
		List<JSONObject> billList = dao.callInfoByBill(schema, entId);
		JSONArray callInfoList = new JSONArray();
		for(int i = 1; i < 8; i++ ) {
			JSONObject callInfo = new JSONObject();
			callInfo.put("inConnSuccCount", 0);
			callInfo.put("outConnSuccCount", 0);
			callInfoList.add(callInfo);
		}
		
		if(billList != null && billList.size() > 0) {
			for (JSONObject billJson : billList) {
				JSONObject callInfo = callInfoList.getJSONObject(billJson.getIntValue("BILL_TYPE") - 1);
				callInfo.put("inConnSuccCount", billJson.getIntValue("IN_CONN_SUCC_COUNT"));
				callInfo.put("outConnSuccCount", billJson.getIntValue("OUT_CONN_SUCC_COUNT"));
			}
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] callInfoList:" + callInfoList.toJSONString());
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_CALL_INFO_BY_BILL + "_" + entId, callInfoList);
	}
	

	
	/**
	 * 坐席状态分布信息
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void agentStateInfo(String schema, String entId) {
		Set<String> agentIds = MapCache.getByType(MapCache.GW_VOICE_AGENT_SKILL_ALL + "_" + entId);
		
		List<JSONObject> stateList = null;
		Map<String, JSONObject> stateMap = new HashMap<String, JSONObject>();
		if(agentIds != null && agentIds.size() > 0) {
			for (String agentId : agentIds) {
				Agents agent = MapCache.getByType(MapCache. GW_VOICE_AGENT_ACC + "_" + entId + "_" + agentId);
				String agentState = agent.getStatus();
				int agentCount = 0;
				if(stateMap.containsKey(agentState)) {
					agentCount = stateMap.get(agentState).getIntValue("agentCount");
				}
				agentCount++;
				
				JSONObject stateInfo = new JSONObject();
				stateInfo.put("agentState", agentState);
				stateInfo.put("agentCount", agentCount);
				stateMap.put(agentState, stateInfo);
			}
			
			stateList = new ArrayList<JSONObject>(stateMap.values());
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + JSONObject.toJSONString(stateList));
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_AGENT_STATE_INFO + "_" + entId, stateList);
	}
	
	/**
	 * 技能组话务信息
	 * @param schema 数据库名字
	 * @param entId 企业ID
	 */
	public void skillCallInfo(String schema, String entId) {
		JSONArray callInfoList = dao.skillCallInfo(schema, entId);
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + callInfoList.toJSONString());
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_SKILL_CALL_INFO + "_" + entId, callInfoList);
	}
	
	/**
	 * 省份话务信息
	 * @param schema 数据库名字
	 * @param entId 企业ID
	 */
	public void provinceCallInfo(String schema, String entId) {
		List<JSONObject> provinceCallList = dao.provinceCallInfo(schema, entId);
		JSONArray callInfoList = new JSONArray();
		if(CommonUtil.listIsNotNull(provinceCallList)) {
			for (JSONObject json : provinceCallList) {
				int connSuccCount = json.getIntValue("CONN_SUCC_COUNT");
				JSONObject info = new JSONObject();
				info.put("level", getLevel(connSuccCount));
				info.put("callCount", json.getIntValue("CALL_COUNT"));
				info.put("connSuccCount", connSuccCount);
				info.put("provinceName", json.getString("PROVINCE_NAME"));
				callInfoList.add(info);
			}
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + callInfoList.toJSONString());
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_PROVINCE_CALL_INFO + "_" + entId, callInfoList);
	}
	
	/**
	 * 获取当前话务量所属话务级别
	 * @param connSuccCount
	 * @return
	 */
	private int getLevel(int connSuccCount) {
		String levels = Constants.GW_PROVINCE_LEVEL_CONFIG;
		int level = 1;
		if(StringUtils.isNotBlank(levels)) {
			for(String levelStr : levels.split(",")) {
				if(connSuccCount < Integer.parseInt(levelStr)) {
					return level;
				}
				level++;
			}
		}
		return level;
	}
	
	
	
	
	/**
	 * 当月话务分析
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void monthCallList(String schema, String entId) {
		List<JSONObject> monthCallList = dao.monthCallList(schema, entId);
		
		JSONArray array = new JSONArray();
		if(CommonUtil.listIsNotNull(monthCallList)) {
			for (JSONObject json : monthCallList) {
				JSONObject info =new JSONObject();
				info.put("dateId", json.getString("DATE_ID"));
				info.put("outCallCount", json.getString("OUT_CALL_COUNT"));
				info.put("inCallCount", json.getString("IN_CALL_COUNT"));
				array.add(info);
			}
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + array.toJSONString());
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_CALL_INFO_BY_MONTH + "_" + entId, array);
	}
	/**
	 * 当年话务分析
	 * @param schema 数据库名称
	 * @param entId 企业ID
	 */
	public void yearCallList(String schema, String entId) {
		List<JSONObject> yearCallList = dao.yearCallList(schema, entId);
		
		JSONArray array = new JSONArray();
		if(CommonUtil.listIsNotNull(yearCallList)) {
			for (JSONObject json : yearCallList) {
				JSONObject info =new JSONObject();
				info.put("dateId", json.getString("DATE_ID"));
				info.put("outCallCount", json.getString("OUT_CALL_COUNT"));
				info.put("inCallCount", json.getString("IN_CALL_COUNT"));
				array.add(info);
			}
		}
		if(ServerContext.isDebug()) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " entId[" + entId + "] cache:" + array.toJSONString());
		}
		BigMonitorCache.getInstance().put(BigMonitorCache. GW_CALL_INFO_BY_YEAR + "_" + entId, array);
	}

}
