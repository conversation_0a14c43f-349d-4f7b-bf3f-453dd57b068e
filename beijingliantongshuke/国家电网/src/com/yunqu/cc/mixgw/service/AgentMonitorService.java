package com.yunqu.cc.mixgw.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.AppBaseService;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.cache.impl.MapCache;
import com.yunqu.cc.mixgw.gw.VoiceAgentGwFactory;
import com.yunqu.cc.mixgw.gw.VoiceAgentGwService;
import com.yunqu.cc.mixgw.model.Agents;


/**
 * Title 语音监控service
 * Description: 生成语音监控的数据存入缓存
 * Copyright: Copyright (c) 2019
 * Company: 云趣科技
 * <AUTHOR>
 * @version 1.0
 *
 */
public class AgentMonitorService extends AppBaseService {

	private Logger logger = CommonLogger.getLogger("agent");
	
	private static class Holder {
		private static AgentMonitorService instance = new AgentMonitorService();
	}
	
	public static AgentMonitorService getInstance() {
		return Holder.instance;
	}
	
	/**
	 * 加载语音坐席信息
	 * @throws SQLException 
	 */
	public void syncAgentMonitorInfo(String schema, String entId, Map<String,JSONObject> agentCallMap) {
		VoiceAgentGwService service = VoiceAgentGwFactory.getDeviceGwServiceByType();
		List<Agents> agentList = service.getAgentList(schema, entId);
		
		if(ServerContext.isDebug()){
			logger.info("获取坐席信息agentList:" + JSONObject.toJSONString(agentList));
		}
		
		Map<String, String> schedulingMap = getSchedulingMap(schema, entId);
		Map<String, JSONObject> skillMap = getSkillMap(schema, entId);
		
		Map<String, Set<String>> skillAgents = new HashMap<>(); //技能组状态信息
		Map<String, Set<String>> deptAgents = new HashMap<>(); //部门状态信息
		Map<String, Set<String>> schedulingAgents = new HashMap<>(); //班次状态信息
		Map<String, Agents> agents = new HashMap<>(); //语音坐席信息
		Map<String, String> roomAgents = new HashMap<>();
		
		Set<String> skillAllAgents = new HashSet<>();
		Set<String> schedulingAllAgents = new HashSet<>();
		Set<String> deptAllAgents = new HashSet<>();
		
		long updateTime = System.currentTimeMillis();
		if(agentList != null && agentList.size() > 0){
			for (Agents agent : agentList) {
				agent.setUpdateTime(updateTime);
				
				// 渲染坐席的技能组及部门信息
				renderSkillAndDept(skillMap, agent);
				
				String agentId = agent.getAgentId();
				
				JSONObject agentCallJson = agentCallMap.get(agent.getAgentId() + "_" + entId);
				if(agentCallJson == null) {
					agentCallJson = new JSONObject();
				}
				
				agent.setInCallCount(agentCallJson.getIntValue("IN_CALL_COUNT"));
				agent.setInConnSuccCount(agentCallJson.getIntValue("IN_CONN_SUCC_COUNT"));
				agent.setOutCallCount(agentCallJson.getIntValue("OUT_CALL_COUNT"));
				
				// 房间监控
				if("Y".equals(Constants.GW_IS_START_ROOM)){
					String ipAddr = agent.getIpAddr();
					String phoneNum = agent.getPhoneNum();
					if(StringUtils.isNotBlank(ipAddr)) {
						if(ipAddr.indexOf(",") != -1){
							ipAddr = ipAddr.substring(0, ipAddr.indexOf(","));
						}
						roomAgents.put(MapCache.GW_ROOM_AGENT_INFO + "_" + entId+ "_IP_" + ipAddr.trim(), JSONObject.toJSONString(agent));
					}
					
					if(StringUtils.isNotBlank(phoneNum)) {
						roomAgents.put(MapCache.GW_ROOM_AGENT_INFO + "_" + entId+ "_PHONE_" + phoneNum.trim(), JSONObject.toJSONString(agent));
					}
				}
				
				// 技能组监控队列
				for(String skillGroupId : agent.getSkillGroupId().split(",")) {
					Set<String> skillAgentAccList = null;
					if (skillAgents.containsKey(MapCache.GW_VOICE_AGENT_SKILL + "_" + entId+ "_" + skillGroupId)) {  // 判断缓存中是否存在该技能组
						skillAgentAccList = skillAgents.get(MapCache.GW_VOICE_AGENT_SKILL + "_" + entId+ "_" + skillGroupId);
					} else {
						skillAgentAccList = new HashSet<String>();
					}
					skillAgentAccList.add(agentId);
					skillAgents.put(MapCache.GW_VOICE_AGENT_SKILL + "_" + entId+ "_" + skillGroupId, skillAgentAccList);
					
					skillAllAgents.add(agentId);
				}
				
				// 部门监控队列
				for(String deptCode : agent.getDeptCode().split(",")) {
					Set<String> deptAgentAccList = null;
					if (deptAgents.containsKey(MapCache.GW_VOICE_AGENT_DEPT + "_" + entId+ "_" + deptCode)) {  // 判断缓存中是否存在该技能组
						deptAgentAccList = deptAgents.get(MapCache.GW_VOICE_AGENT_DEPT + "_" + entId+ "_" + deptCode);
					} else {
						deptAgentAccList = new HashSet<String>();
					}
					deptAgentAccList.add(agentId);
					deptAgents.put(MapCache.GW_VOICE_AGENT_DEPT + "_" + entId+ "_" + deptCode, deptAgentAccList);
					
					deptAllAgents.add(agentId);
				}
				
				// 班次监控队列
				String schedulingId = agent.getSchedulingId();
				if(StringUtils.isNotBlank(schedulingId)) {
					agent.setSchedulingName(schedulingMap.get(schedulingId));
					Set<String> schedulingAgentAccList = null;
					if (schedulingAgents.containsKey(MapCache.GW_VOICE_AGENT_SCHEDULING + "_" + entId+ "_" + schedulingId)) {  // 判断缓存中是否存在该班次
						schedulingAgentAccList = schedulingAgents.get(MapCache.GW_VOICE_AGENT_SCHEDULING + "_" + entId+ "_" + schedulingId);
					} else {
						schedulingAgentAccList = new HashSet<String>();
					}
					schedulingAgentAccList.add(agentId);
					schedulingAgents.put(MapCache.GW_VOICE_AGENT_SCHEDULING + "_" + entId+ "_" + schedulingId, schedulingAgentAccList);
					
					schedulingAllAgents.add(agentId);
				} else {
					agent.setSchedulingName("-");
				}
				
				//3.2#20210812-1 调整坐席时长监控
				String agentState = agent.getStatus();
				if(StringUtils.isNotBlank(agentState)){
//					agent.setNoticeEntId(agent.getEntId());
//					agent.setNoticeUserAcc(agent.getAgentId());
				}
				
				// 添加坐席到缓存中
				agents.put(MapCache.GW_VOICE_AGENT_ACC + "_" + entId+ "_" + agentId, agent);
			}
		}
		
		List<String> delKey = new ArrayList<String>();
		for (Entry<String, Object> entry : MapCache.getInstance().entrySet()) {
			String key = entry.getKey();
			if (key.startsWith(MapCache.GW_VOICE_AGENT_ACC + "_" + entId+ "_") || key.startsWith(MapCache.GW_VOICE_AGENT_SKILL + "_" + entId+ "_") || key.startsWith(MapCache.GW_ROOM_AGENT_INFO + "_" + entId + "_" ) || key.startsWith(MapCache.GW_VOICE_AGENT_DEPT + "_" + entId+ "_")|| key.startsWith(MapCache.GW_VOICE_AGENT_SCHEDULING + "_" + entId+ "_")) {
				if(!agents.containsKey(key) || !skillAgents.containsKey(key) || !roomAgents.containsKey(key)){
					delKey.add(key);
				}
			}
		}
		for (String key : delKey) {
			MapCache.getInstance().remove(key);
		}
		MapCache.getInstance().putAll(deptAgents);
		MapCache.getInstance().putAll(skillAgents);
		MapCache.getInstance().putAll(schedulingAgents);
		MapCache.getInstance().putAll(agents);
		MapCache.getInstance().put(MapCache.GW_VOICE_AGENT_SKILL_ALL + "_" + entId, skillAllAgents);
		if(ServerContext.isDebug()){
			logger.info("获取坐席信息agents:" + JSONObject.toJSONString(agents));
			logger.info("获取坐席信息skillAgents:" + JSONObject.toJSONString(skillAgents));
		}
	}
	
	
	/**
	 *  渲染坐席的部门、技能组信息
	 * @param skillMap 技能组列表
	 * @param agent 坐席信息
	 */
	private void renderSkillAndDept(Map<String, JSONObject> skillMap, Agents agent) {
		String skillGroupIds = agent.getSkillGroupId();
		Set<String> skillIds = new HashSet<String>();
		Set<String> skillNames = new HashSet<String>();
		Set<String> deptCodes = new HashSet<String>();
		Set<String> deptNames = new HashSet<String>();
		for (String skillGroupId : skillGroupIds.split(",")) {
			JSONObject skillJson = skillMap.get(skillGroupId);
			if(skillJson == null) {
				continue;
			}
			String skillGroupType = skillJson.getString("SKILL_GROUP_TYPE");
			String skillGroupName = skillJson.getString("SKILL_GROUP_NAME");
			if(Constants.GW_SKILL_GROUP_TYPE_VOICE.equals(skillGroupType)) {
				// 技能组
				skillIds.add(skillGroupId);
				skillNames.add(skillGroupName);
			} else if (Constants.GW_SKILL_GROUP_TYPE_DEPT.equals(skillGroupType)) {
				// 部门
				deptCodes.add(skillJson.getString("SKILL_GROUP_CODE"));
				deptNames.add(skillGroupName);
			}
		}
		
		agent.setSkillGroupId(String.join(",", skillIds));
		agent.setSkillGroupName(String.join(",", skillNames));
		agent.setDeptCode(String.join(",", deptCodes));
		agent.setDeptName(String.join(",", deptNames));
	}
	/**
	 * 获取排班班次Map
	 * @param schema
	 * @param entId
	 * @return
	 */
	private Map<String, String> getSchedulingMap(String schema, String entId) {
		EasySQL sql = new EasySQL("SELECT SCHEDULING_ID,SCHEDULING_NAME FROM "+ schema + ".CC_SD_SCHEDULING WHERE 1=1 ");
		sql.append(entId, "AND ENT_ID = ? ");
		sql.append("01","AND STATUS = ?");
		
		List<JSONObject> schedulingList = null;
		try {
			schedulingList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) { }
		Map<String,String> schedulingMap = new HashMap<String, String>();
		if(CommonUtil.listIsNotNull(schedulingList)) {
			for (JSONObject scheduling : schedulingList) {
				schedulingMap.put(scheduling.getString("SCHEDULING_ID"), scheduling.getString("SCHEDULING_NAME"));
			}
		}
		return schedulingMap;
	}
	
	/**
	 * 获取技能组部门Map
	 * @param schema
	 * @param entId
	 * @return
	 */
	private Map<String, JSONObject> getSkillMap(String schema, String entId) {
		EasySQL sql = new EasySQL("SELECT SKILL_GROUP_ID,SKILL_GROUP_NAME,SKILL_GROUP_TYPE,SKILL_GROUP_CODE FROM "+ schema + ".CC_SKILL_GROUP WHERE 1=1 ");
		sql.append(entId, "AND ENT_ID = ? ");
		
		List<JSONObject> skillList = null;
		try {
			skillList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) { }
		Map<String, JSONObject> skillMap = new HashMap<String, JSONObject>();
		if(CommonUtil.listIsNotNull(skillList)) {
			for (JSONObject skillJson : skillList) {
				skillMap.put(skillJson.getString("SKILL_GROUP_ID"), skillJson);
			}
		}
		return skillMap;
	}


	/**
	 * 删除超时的坐席
	 */
	public void delTimeoutAgent() {
		long thisTime = System.currentTimeMillis();
		List<String> needDelKeys = new ArrayList<String>();
		List<String> agentAccKeys = new ArrayList<String>(); //需要删除的坐席
		List<String> skillKeys = new ArrayList<String>(); //需要修改的技能组
		for (Entry<String, Object> entry : MapCache.getInstance().entrySet()) {
			String key = entry.getKey();
			if (key.startsWith(MapCache.GW_VOICE_AGENT_ACC + "_")) {
				JSONObject o = (JSONObject) entry.getValue();
				Agents agent = JSONObject.parseObject(o.toJSONString(),Agents.class);
				long updateTime = agent.getUpdateTime();
				long min = (thisTime - updateTime) / (60 * 1000);
				if (min > 15) {// 更新时间超过15分钟的删掉
					agentAccKeys.add(agent.getUserAcc());
					skillKeys.add(agent.getSkillGroupId());
				}
			}
		}
		for (String agentAcc : agentAccKeys) { //从缓存中删除坐席信息
			MapCache.getInstance().remove(MapCache.GW_VOICE_AGENT_ACC + "_" + agentAcc);
		}
		for (String key : skillKeys) {
			List<String> skillList = MapCache.getByType(MapCache.GW_VOICE_AGENT_SKILL + "_" + key);
			for (String agentAcc : agentAccKeys) {
				if(skillList.contains(agentAcc)){ //判断改技能组是否有需要删除的坐席
					skillList.remove(agentAcc); //删除技能组中的坐席
				}
			}
			if(skillList.size() == 0){ //判断该技能组下是否还有坐席
				needDelKeys.add(MapCache.GW_VOICE_AGENT_SKILL + "_" + key); //需要删除的技能组
			}
		}
		for (String key : needDelKeys) {
			MapCache.getInstance().remove(key); //删除空的技能组
		}
	}
	
}
