package com.yunqu.cc.mixgw.inf;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;

public class SmsCodeService extends IService {
    

	private static final Logger logger = CommonLogger.logger;

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
			return SendSmsCode(json);
	}
	/**
	 * 发送短信信息
	 * @param json
	 * @return
	 */
	private JSONObject SendSmsCode(JSONObject json){
		JSONObject rtJson = new JSONObject();
		try {
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 发送短信收到参数,json:" + json.toJSONString());
			String phoneNum =json.getString("receiver");	//客户的手机号码
			String count =json.getString("content");	//发送内容
			
			if(StringUtils.isBlank(phoneNum)){
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 手机号码不能为空");
				rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rtJson.put("respDesc", "手机号码不能为空");
				return rtJson;
			}
			if(StringUtils.isBlank(count)){
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 内容不能为空");
				rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rtJson.put("respDesc", "内容不能为空");
				return rtJson;
			}
			
			boolean resultFlag =true;
			resultFlag = this.sendSmsCodeList(json);
			if(!resultFlag){
				//手工发送失败的，发送通知
				String smsId = json.getString("smsId");
				updateSmsStatus(smsId);
				if(StringUtils.isNotBlank(smsId)) {
					sendIndoorMsg(smsId,json.getString("receiver"));
				}
				rtJson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rtJson.put("respDesc", "发送短信失败");
			}else{
				json.put("status", "2");
				rtJson.put("respCode", GWConstants.RET_CODE_SUCCESS);
				rtJson.put("respDesc", "发送短信成功");
			}
			//发送成功后，调用接口，通过状态status回传，通知服务接口，已推送到运营商
			IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
			logger.info(CommonUtil.getClassNameAndMethod(this) + ",rtJson:" + json.toJSONString());
			service.invoke(json);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "发送短信接口发送失败：:" + e+"  =="+e.getMessage());
		}
		return rtJson;
	}
	
	public boolean sendSmsCodeList(JSONObject json){
		JSONObject smsChannel=json.getJSONObject("smsChannel");
		String account = smsChannel.getString("ACCOUNT");
		String paswd = smsChannel.getString("PASSWD");
		if(StringUtils.isBlank(account)||StringUtils.isBlank(paswd)){
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 发送短信失败，账号或者密码为空!");
			return false;
		}
		int code=0;
		try {
			String content=java.net.URLEncoder.encode("【国网物资有限公司】"+json.getString("content"),"utf-8");
			String phoneNum=json.getString("receiver");
			String param="?sn="+account+"&pwd="+MD5Util.getHexMD5(account+paswd)+"&mobile="+phoneNum+"&content="+content;
			String url = Constants.SMS_URL+":8061/mdsmssend.ashx";	
//			if (ServerContext.isDebug()) {
//				logger.info(CommonUtil.getClassNameAndMethod(this) + " 发送短信接口,配置项url:" + url+" --入参："+param.toString());
//			}
			//String valueCont=phoneNum+json.getString("content");
			//String smsContent=CacheUtil.get("sms-send-repeat"+json.getString("smsId"));
			//if(valueCont.equals(smsContent)) {
			//	logger.info(CommonUtil.getClassNameAndMethod(this) + "发送短信接口重复发送,存在重复发送："+valueCont);
			//}
			HttpResp resp = HttpUtil.sendGet(url+param,"",GWConstants.ENCODE_UTF8);
			logger.info(CommonUtil.getClassNameAndMethod(this) + "发送短信接口是否成功code：:" + resp.getCode()+"  Result:"+resp.getResult()+"phoneNum"+phoneNum+"content"+content);
			code=resp.getCode();
			//因为数据存在重复发送情况-故而存入缓存，进行判断
			//smsContent=phoneNum+json.getString("content");
			//CacheUtil.put("sms-send-repeat"+json.getString("smsId"), smsContent,120);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return code==200 ? true : false;
		
	}
	
	public  void sendIndoorMsg(String smsId, String phone) {
		try {
			// 获取创建者
			EasySQL sql = new EasySQL("SELECT C.CREATE_ACC FROM " + Constants.dbName + ".C_SMS_SEND_RECORD" + " A ");
			sql.append("LEFT JOIN " + Constants.dbName + ".C_SMS_INFO C ON C.ID = A.SMS_INFO_ID ");
			sql.append(smsId, " where A.ID=?");
			String createAcc = QueryFactory.getReadQuery().queryForString(sql.getSQL(), sql.getParams());
			if(StringUtils.isBlank(createAcc)) {
				return;
			}
			JSONObject param = new JSONObject();
			param.put("serialId", ServiceID.NOTICE_INTERFACE);
			param.put("command", ServiceCommand.NOTICE_ADD_USER_NOTICE);
			param.put("sender", Constants.APP_NAME);
			param.put("receiverType", "01");// 01-个人 02-部门 03-所有人
			param.put("userAcc", createAcc);// 存储接收通知的账号,如有多个用;分割
			param.put("deptCode", "000");
			param.put("type", "短信异常提醒");
			param.put("module", Constants.APP_NAME);
			param.put("title", "发送给" + phone + "的短信失败！");
			param.put("content", "发送给" + phone + "的短信失败！");
			param.put("url", "/cc-sms/pages/send/smsAllSendRecord.jsp");// 点击通知展示的页面
			param.put("method", "03");// 通知推送方式,01-汇总显示 02-单条弹屏显示 03-单条显示
			param.put("expiredTime", "2999-12-30 23:59:59");// 失效时间
			param.put("publishTime", DateUtil.getCurrentDateStr());// 发布时间
			param.put("validTime", "2999-12-30 23:59:59");// 失效时间
			param.put("senderId", "system");// system
			param.put("senderName", "system");// system
			param.put("createUserAcc", "system");// system
			param.put("createUserDeptCode", "1");
			JSONArray noticeReceiver = new JSONArray();
			noticeReceiver.add(phone);
			param.put("noticeReceiver", noticeReceiver);
			param.put("epCode", Constants.entId);
			param.put("schema", Constants.dbName);
			param.put("busiOrderId", Constants.busiId);
			IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
			// 调用接口
			JSONObject result = service.invoke(param);
			if (result.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)) {
				logger.info("发送给 " + phone + " 的内部消息发送成功");
				return ;
			} else {
				logger.error("内部消息发送失败！接口返回码为：" + result.getString("respCode"));
				return ;
			}
		} catch (Exception e) {
			logger.error("短信失败调用内部信息接口异常！" + e.getMessage(), e);
		}
		return ;
	}
	
	
	
	public void updateSmsStatus(String smsId) throws Exception {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" UPDATE " + Constants.dbName+ ".C_SMS_SEND_RECORD SET GW_SEND_FAIL=1 where ID=?");
			QueryFactory.getWriteQuery().execute(sql.toString(), new Object[] {smsId});
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 操作出错", e);
		}
	}

}
