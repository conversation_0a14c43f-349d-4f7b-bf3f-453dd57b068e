package com.yunqu.cc.mixgw.base;

import java.util.HashMap;
import java.util.Map;

import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * dao base class
 * <AUTHOR>
 */

public class StatDaoContext extends DaoContext {
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_READ_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected boolean loginCheck() {
		return true;
	}
	
	protected String getTableName(String tableName){
		String dbName=getDbName();
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	public String getEntId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getEntId();
	}
	
	
	public String getDbName(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getSchemaName();
	}
	
	protected String getBusiOrderId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getBusiOrderId();
	}
	
	
	protected YCUserPrincipal getUserPrincipal(){ 
		return (YCUserPrincipal)request.getUserPrincipal();
	}
	//用户数据库ID 
	protected String getUserId(){
		YCUserPrincipal  principal  = (YCUserPrincipal)request.getUserPrincipal();
		return principal.getUserId();
	}
	
	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME from "+getStatTableName("cc_stat_table_info")+" where TABLE_ID = ?  ";
			tabInfo = this.getQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return tabInfo;
	}
	
	/**
	 * 把时间字符串：yyyy-MM-dd 转化为数字yyyyMMdd
	 * @param date
	 * @return
	 */
	public int parseDate(String date){
		if(StringUtils.isBlank(date)){
			return 0;
		}
		return Integer.parseInt(date.replaceAll("-", "").trim());
	}
	
	public String  parseDate1(String date){
		if(StringUtils.isBlank(date)){
			return null;
		}
		return date.replaceAll("-", "").trim();
	}
	
	/**
	 * 获取技能组Id
	 */
	public String[] getSkillGroupId(){
		return (String[]) this.getUserPrincipal().getAttribute("skillGroupIds");
	}
}
