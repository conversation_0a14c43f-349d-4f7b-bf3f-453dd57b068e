<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
<title>视频切割</title>
<style>
   .fragment{
      width:100%;
      text-align: center;
      padding:0px 1px;
   } 
	.slider{
		display:block;
		margin:10px 15px 10px;
	} 
	.layui-form-item{width:100%;} 
	.layui-input{color:#D2691E;}
	.fragment .layui-form-switch {
		min-width:65px!important;
		border-radius:10px;
	}
	.fragment .layui-input-block{margin-left:100px;}
	.fragment .layui-form-radio{margin-right:0px;}
	.fragment .label{text-align:left;}
	.fragment .label-time{text-align:left;}
	.fragment .label-time .layui-form-label,.fragment .label-time .layui-input-inline{
	     width:100px;
	}
	
</style>
<link href="${ctxPath}/static/css/layui.css" rel="stylesheet">
</EasyTag:override>
<body>
<form class="layui-form" lay-filter="videoForm">
  <div class="row fragment">
	  <div class="col-xs-8" style="padding-right:0px;">
	    <video id="fragment" src="${param.url}" preload="none" style="object-fit:contain" controls="controls" width="100%" height="380px">
	    </video>
	    <div class="slider">
	       <div id="fragment-slider" class="mt-15"></div>
	    </div> 
	  </div>
	  <div class="col-xs-4" style="padding:0 0 0 10;">	  	  
        <div class="layui-form-item label-time mt-5">
           <div class="layui-inline" id="videoTypeRadio">
            <label class="layui-form-label">视频源</label>
    		<div class="layui-input-block" style="border:solid 1px rgb(230,230,230);height:38px;padding:0 10px">
      			<input type="radio" name="videoSrc"  lay-filter="videoSrc" value="cust" title="客户" checked>
      			<input type="radio" name="videoSrc" lay-filter="videoSrc" value="agent" title="坐席">
    		</div>
           </div>
           <div class="layui-inline">
		      <label class="layui-form-label">原视频时长</label>
		      <div class="layui-input-inline">
		        <input type="text" id="fragment_duration" placeholder="00:00:00" readonly="readonly"  autocomplete="off" class="layui-input">		        	        
		      </div>
		      <span class="layui-form-mid layui-word-aux"></span>	      
	       </div>
	       <div class="layui-inline">
		      <label class="layui-form-label">截取开始点</label>
		      <div class="layui-input-inline">
		        <input type="text" id="fragment_min" placeholder="00:00:00" readonly="readonly"  autocomplete="off" class="layui-input">
		      </div>
		      <span class="layui-form-mid layui-word-aux"></span>
		      
	       </div>
	        <div class="layui-inline">
	           <label class="layui-form-label">截取结束点</label>
	           <div class="layui-input-inline">
	             <input type="text" id="fragment_max" placeholder="00:00:00" readonly="readonly" autocomplete="off" class="layui-input">
	           </div>
		       <span class="layui-form-mid layui-word-aux"></span>          
	        </div>
  	    </div>
  	    <div class="layui-form-item text-c">
		   <button type="button" class="layui-btn"  id="LAY-save">下载本地</button>
		   <button type="button" class="layui-btn"  id="LAY-upload">上传服务器</button>		   
        </div>
        <c:if test="${not empty param.startTag }">
          <div style="padding-top:15px;">
        	<div class="layui-form-item">
               <label class="layui-form-label" style="width:100px;">标记截取点</label>
               <div class="layui-input-inline" style="width:80px;">
      				<input type="text" id="tag_min" value="${param.startTagHms}" autocomplete="off" class="layui-input">
    			</div>
    			<div class="layui-form-mid">-</div>
    			<div class="layui-input-inline" style="width: 80px;">
    				 <input type="text" id="tag_max" value="${param.endTagHms}" autocomplete="off" class="layui-input">
    			</div>
          	</div>
          	<div class="layui-form-item text-c">
  				<button type="button" class="layui-btn layui-btn-sm" data-type="sync">使用截取方案</button>
			</div>
         </div>
        </c:if>
	  </div>
  </div>
  </form>
</body>
<EasyTag:override name="script">
<script type="text/javascript">  
    var video = document.getElementById("fragment");
    var min=0,max=0;
    var startTag = '${param.startTag}';
    var endTag = '${param.endTag}';
	var serialId = '${param.serialId}';
	var agentUrl = '${param.agentUrl}';
	if (agentUrl=='') {
		//兼容视频话单，如果未传入坐席视频地址，表示只展示单个视频，隐藏视频源选项
		$("#videoTypeRadio").attr("style", "display: none");
	}
	
    layui.use(['form','slider'],function(){
	   var $ = layui.$;
	   var form = layui.form;
	   var slider = layui.slider;
	   //开启输入框
	   var sliderElem = slider.render({
	      elem: '#fragment-slider',
	      input: true, //输入框
	      range: true, //范围选择
	      value:0,
	      setTips:function(value){ 
	    	  var tipMsg = "00:00";
	    	  if(video.duration){
	    		  tipMsg = getDurationByHms(value/100*video.duration);
		    	  if(tipMsg.startsWith("00:")) tipMsg = tipMsg.substr(3);
	    	  }
	    	  return tipMsg;
	      }, 
	      change:function(value){	    	  
	    	  if(min!=value[0]){
		    	  min = value[0];
		    	  video.currentTime = min/100*video.duration;
	    	  }
	    	  if(max!=value[1]){
		    	  max = value[1];
		    	  video.currentTime = max/100*video.duration;
	    	  }
	    	  $("#fragment_min").val(getDurationByHms(min/100*video.duration));
	  		  $("#fragment_min").parent().siblings("span").html((min/100*video.duration).toFixed(3));

	    	  $("#fragment_max").val(getDurationByHms(max/100*video.duration));
	  		  $("#fragment_max").parent().siblings("span").html((max/100*video.duration).toFixed(3));
	  		  
	    	  video.pause();
	      }
	   });
	   var path = '${param.path}';
	   form.on('radio(videoSrc)', function(data){
          if(data.value=="cust"){
        	  video.src = '${param.url}';
        	  path = '${param.path}';
          }else{
        	  video.src = '${param.agentUrl}';
        	  path = '${param.agentPath}';
          }
    	  video.load();

       });
	   video.addEventListener("loadeddata",function(){
	       var duration = video.duration;
  		   $("#fragment_duration").val(getDurationByHms(duration));
  		   $("#fragment_duration").parent().siblings("span").html(duration.toFixed(3));
  		  
  	   	   $("#fragment_min").val(getDurationByHms(min/100*duration));
  		   $("#fragment_min").parent().siblings("span").html((min/100*duration).toFixed(3));

  	   	   $("#fragment_max").val(getDurationByHms(max/100*duration));
  		   $("#fragment_max").parent().siblings("span").html((max/100*duration).toFixed(3));

    	   video.currentTime = max/100*duration;
  	   });
      
       $("#LAY-save").on("click",function(){
    	   
    	   var offset = min/100*video.duration;
    	   var duration = max/100*video.duration - min/100*video.duration;
		   //剪辑接口传入话单ID，适配剪辑视频话单的时候无视频文件相对路径，根据话单ID后续查询
    	   window.location.href = "/cc-video/servlet/videoFragment?action=fragment&startPoint="
    			   +$("#fragment_min").val()+"&endPoint="
    			   +$("#fragment_max").val()+"&path="+path+"&serialId="+serialId
    			   +"&offset="+offset+"&duration="+duration; 	   
       });
       
       $("#LAY-upload").on("click",function(){
    	   var offset = min/100*video.duration;
    	   var duration = max/100*video.duration - min/100*video.duration;
    	   ajax.remoteCall("/cc-video/servlet/videoFragment?action=fragment&startPoint="
    			   +$("#fragment_min").val()+"&endPoint="
    			   +$("#fragment_max").val()+"&path="+path+"&serialId="+serialId
    			   +"&offset="+offset+"&duration="+duration,{upload:true},function(result){
    		   if(result.state==1){
				   handleButton()
				   layer.closeAll();
    			   layer.msg(result.msg,{time:2000,icon:1});
    		   }else{
				   handleButton()
				   layer.closeAll();
    			   layer.alert(result.msg,{icon:6});
    		   }
    	  });
       });
       $("[data-type='sync']").on("click",function(){
    	   sliderElem.setValue(startTag/video.duration*100,0);
    	   sliderElem.setValue(endTag/video.duration*100,1);
       });
       
	   form.render();
	   $(".layui-form-radio:first").trigger("click");
	}); 
    
    function getDurationByHms(val){
    	var duration = fmtSeconds(val)
		  if(duration.length<=5)
			  duration = '00:'+duration;
    	return duration;
    }
	
</script>
</EasyTag:override>
<%@include file="/pages/volteVideo/layout_form.jsp" %>
