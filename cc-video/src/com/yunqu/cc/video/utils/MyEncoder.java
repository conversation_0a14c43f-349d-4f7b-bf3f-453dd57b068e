package com.yunqu.cc.video.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;

import com.yunqu.cc.video.base.CommonLogger;
import org.apache.log4j.Logger;
import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncoderException;
import it.sauronsoftware.jave.EncoderProgressListener;
import it.sauronsoftware.jave.EncodingAttributes;
import it.sauronsoftware.jave.FFMPEGLocator;

public class MyEncoder extends Encoder  {
	private static Logger logger =CommonLogger.getLogger();
	 public MyEncoder(FFMPEGLocator locator) {
	        super(locator);
	    }
	
	  protected void processErrorOutput(EncodingAttributes attributes, BufferedReader errorReader, File source, EncoderProgressListener listener) throws EncoderException, IOException {
	        //屏蔽错误处理
	        try {
	            String line;
	            while ((line = errorReader.readLine()) != null) {
	                System.out.println(line);
	            }
	        }
	        catch (Exception exp) {
	            System.out.println("file convert error message process failed. "+exp);
	        }
	    }
	  
}
