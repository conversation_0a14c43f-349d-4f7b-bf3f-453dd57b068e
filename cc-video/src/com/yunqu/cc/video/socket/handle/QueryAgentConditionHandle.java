package com.yunqu.cc.video.socket.handle;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.video.base.CommonLogger;
import com.yunqu.cc.video.base.Constants;
import com.yunqu.cc.video.base.QueryFactory;
import com.yunqu.cc.video.socket.Agent;
import com.yunqu.cc.video.socket.SocketCommand;
import com.yunqu.cc.video.socket.SocketResult;
import com.yunqu.cc.video.utils.DateUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.util.Date;

/**
 * QueryAgentConditionHandle
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/8 11:28:29
 */
public class QueryAgentConditionHandle {

    private Logger logger = CommonLogger.getLogger("websocket");

    public static QueryAgentConditionHandle getService() {
        return Holder.service;
    }

    public void handle(Agent agent) {
        try {
            String dateStr = DateUtil.getDateStr(new Date(), DateUtil.TIME_FORMAT_YMD) + " 00:00:00";
            EasySQL easySQL = new EasySQL("SELECT COUNT(DISTINCT T1.AGENT_ACC) AGENT_COUNT, SUM(IF(T1.BEGIN_TIME >= '" + dateStr + "', 1, 0)) TODAY_COUNT, ");
            easySQL.append("IF(COUNT(DISTINCT T1.AGENT_ACC) > 0, COUNT(T1.ID) / COUNT(DISTINCT T1.AGENT_ACC), 0) AGENT_AVG_VIDEO, ");
            easySQL.append("IF(COUNT(T1.ID) > 0, SUM(T1.TOTAL_TIME) / COUNT(T1.ID), 0) AVG_TIME, SUM(IF(T1.VIDEO_TYPE = '1', 1, 0)) VIDEO_CALL, ");
            easySQL.append("SUM(IF(T1.VIDEO_TYPE = '2', 1, 0)) VIDEO_MEDIA, SUM(IF(T1.VIDEO_TYPE = '3', 1, 0)) VIDEO_VOLTE, ");
            // TODO 参评率和满意率暂时无法统计,先写成0
            easySQL.append("0 EVAL_RATE, 0 SATISF_RATE ");
            easySQL.append("FROM " + getStatTableName("CC_RPT_VIDEO_AGENT_MONITOR" + " T1 "));
            if (ServerContext.isDebug()) {
                logger.info("QueryVideoListHandle SQL --> " + easySQL.toFullSql());
            }
            JSONObject jsonObject = QueryFactory.getWriteQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
            SocketResult result = new SocketResult(SocketCommand.QUERY_AGENT_CONDITION, jsonObject);
            agent.sendMsg(JSONObject.toJSONString(result));
        } catch (Exception e) {
            logger.error("QueryAgentConditionHandle error " + e.getMessage());
        }
    }

    private static class Holder {
        private static QueryAgentConditionHandle service = new QueryAgentConditionHandle();

    }

    /**
     * 获取统计库表名
     * @param tableName
     * @return
     */
    private String getStatTableName(String tableName){
        return Constants.getStatSchema() + "." + tableName;
    }
}
