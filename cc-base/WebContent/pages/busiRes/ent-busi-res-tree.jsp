<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title i18n-content="资源树"></title>
	<link rel="stylesheet" type="text/css" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css">
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<style>
	.container-fluid {
		padding : 0px 15px;
	}
	.row{
		position : fixed;
		width : 100%;
		background-color : #fff;
		padding : 10px 0px;
	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">

	<div class="form-inline" >
	  <div class="row">
		  <div class="col-xs-8">
		    <input type="text" name="title" id="searchInt" style="height: 30px;vertical-align: top;margin-left: 0%;width: 100%" autocomplete="off" i18n-placeholder="请输入关键字搜索">
		  </div>
		  <div class="col-xs-4">
		    <button id="openBtn" type="button" class="btn btn-default btn-sm" onclick="btnOpen()" style="width: 70%" i18n-content="收起"></button>
		  </div>
	  </div>
  	</div>
  	<br>
	<div class="zTreeDemoBackground left" id="entBusiTree" style="padding-top: 30px">
		<!-- <div id="busiSelector" style="border-bottom: 1px solid #eee;height: 52px;line-height: 52px;padding: 0px 15px">
   				<div class="input-group input-group-sm" style="width: 100%">
				    <span class="input-group-addon">请选择系统</span>	
			   		<select data-mars="entBusiRes.busiDict" id="treeBusiId" class="form-control input-sm" onchange="initBusiTree()" data-mars-reload="false">
			   		</select>
		       </div>
		</div> -->
		<ul id="tree" class="ztree"></ul>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	btnOpen=function(){
		var openText = $("#openBtn").text();
		var treeObj = $.fn.zTree.getZTreeObj("tree");
		if(openText == getI18nValue("展开")){
			treeObj.expandAll(true);
			$("#openBtn").text(getI18nValue("收起"));
		}else{
			//保留父节点收起
			treeObj.expandNode(treeObj.getNodes()[0], false, true, true);
			$("#openBtn").text(getI18nValue("展开"));
		}
	}
	
	var queryHideNodes = []; //查询隐藏的
	var queryShowNodes = []; //查询展示的
	$("#searchInt").bind('input propertychange',function(){
		var treeId = $("#tree").attr("id");
		var treeObj = $.fn.zTree.getZTreeObj(treeId);
		var queryWord = $("#searchInt").val().toUpperCase();
		if(!$("#searchInt").val()){
			resetColor();
			treeObj.expandAll(true);
			return false;
		}
		//颜色重置
		function resetColor() {
			for (var i = 0; i < queryShowNodes.length; i++) { //初始将上次查询的内容颜色改回黑色
				treeObj.setting.view.fontCss["color"] = "black";
				treeObj.updateNode(queryShowNodes[i]);
			}
		}

		resetColor();

		//展示查询的节点儿
		function showQueryNode(node) {
			if (node.getParentNode() != null) {
				treeObj.expandNode(node.getParentNode(), true);
			}
		}

		function hideQueryNode(node) {
			if (node.isParent) {
				treeObj.expandNode(node, false);
			}
		}

		function filter(queryShowNode, node) {
			if (queryShowNode.getParentNode() != null) {
				if (queryShowNode.name == node.name) {
					return true;
				} else {
					return filter(queryShowNode.getParentNode(), node);
				}
			} else if (queryShowNode.name == node.name) {
				return true;
			} else {
				return false;
			}
		}

		function filterHideFunc(node) {
			var boolen = true;
			for (var i = 0; i < queryShowNodes.length; i++) {
				if (filter(queryShowNodes[i], node)) {
					boolen = false;
				}
			}
			return boolen;
		}

		function filterShowFunc1(node) {
			if (node.name.toUpperCase().indexOf(queryWord) == -1) return false;
			return true;
		}

		function filterShowFunc2(node) {
			var boolen = false;
			for (var i = 0; i < queryShowNodes.length; i++) {
				if (filter(queryShowNodes[i], node)) {
					boolen = true;
				}
			}
			return boolen;
		}
		queryShowNodes = treeObj.getNodesByFilter(filterShowFunc1); //过滤得到匹配的名称的节点
		queryHideNodes = treeObj.getNodesByFilter(filterHideFunc); //过滤得到隐藏的
		var queryShowNodes2 = [];
		queryShowNodes2 = treeObj.getNodesByFilter(filterShowFunc2); //过滤得到展示的节点
		if (queryShowNodes.length) {
			//展开选中节点
			for (var i = 0; i < queryShowNodes.length; i++) {
				showQueryNode(queryShowNodes[i]);
				//改变展示节点的颜色
				treeObj.setting.view.fontCss["color"] = "red";
				treeObj.updateNode(queryShowNodes[i]);
			}
			//折叠隐藏节点
			for (var i = 0; i < queryHideNodes.length; i++) {
				hideQueryNode(queryHideNodes[i]);
			}
		}else{
			//展开隐藏节点
			for (var i = 0; i < queryHideNodes.length; i++) {
				showQueryNode(queryHideNodes[i]);
			}
			return true;
		}
		$(this).focus();
	});
	
		 	var setting = {
					view: {
						selectedMulti: false
					},
					check: {
						enable: true,
					}
			};
			
			var entId = '${param.entId }';
			$(document).ready(function(){
				$("#entBusiTree").render({data:{entId:entId},success:function(result){
					/* console.log(result['entBusiRes.busiDict'].data);
					var data = result['entBusiRes.busiDict'].data;
					var len = 0;
					for(var key in data){  
						len++;
				    };
					if(len <= 1){
						$("#busiSelector").hide();
					} */
					initBusiTree();
				}});
			});
			
			/*初始化菜单权限树*/
			function initBusiTree(){
				var busiId = "${param.busiId}";
				var busiName = "${param.busiName}";
				parent.treeBusiId = busiId;
				console.log(busiId);
				console.log(busiName);
				console.log(entId);
				ajax.remoteCall("${ctxPath}/webcall?action=entBusiRes.getResTree",{entId:entId,busiId:busiId,busiName:busiName},function(result) { 
					var data = result.data;
					$.fn.zTree.init($("#tree"), setting, data);
					parent.treeObj = $.fn.zTree.getZTreeObj("tree");
				});
			}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>