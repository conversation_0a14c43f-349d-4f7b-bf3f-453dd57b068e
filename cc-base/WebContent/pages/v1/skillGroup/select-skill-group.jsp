<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择技能组</title>
	<style>
		#dataList2 tr{cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="skillGroupSelectForm" style="margin-bottom: 65px">
       			<input name="taskType" value="${param.type}" type="hidden"/>
       			<input name="taskId" value="${param.taskId}" type="hidden"/>
       			<input name="bussOrderId" value="${param.bussOrderId}" type="hidden"/>
       			 <select class="form-control input-sm" name="queue" style="display: none;">
               			<option value="">请选择</option>
               			<option value="1">按最长等待时间</option>
               			<option value="2">按坐席优先级别</option>
                </select>
             	<div class="ibox">
	              	<div class="ibox-content" style="padding: 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-container="dataList2" data-template="list-template2" data-auto-fill="5" data-mars="v1_skillGroup.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">选择</th>
								      <th>技能队列名称</th>
								      <th>坐席数</th>
								      <!-- <th>服务渠道</th> -->
								      <th>技能组排队策略</th>
								      <th>创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList2">
                             </tbody>
		                 </table>
                        	 <script id="list-template2" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td class="text-c"><input type="{{getType:SKILL_GROUP_ID}}" name="skillGroupIds" value="{{:SKILL_GROUP_ID}}"/></td>
											<td>{{:SKILL_GROUP_NAME}}</td>                                         
											<td>{{:AGENT_COUNT}}人</td>                                         
											{{!--<td>{{getText:CHANNEL_ID 'channelId'}}</td>--}}
											<td>{{getText:QUEUE_STRATEGY 'queue'}}</td>
											<td>{{cutText:CREATE_TIME 12 ''}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
	                     <%-- <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div>  --%>
	              	</div> 
	              	<div class="layer-foot text-c" style="position: fixed;">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="SkillGroupSelect.saveData()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("SkillGroupSelect");
		
		SkillGroupSelect.type=${param.type};
		
		$(function(){
			$("#skillGroupSelectForm").render({success:function(){
				$("#dataList2 tr").click(function(){
					$(this).find("input").click();
				});
			}});
		});
		$.views.converters("getType", function() {
			if(SkillGroupSelect.type==1){
				return "checkbox";
			}else{
				return "radio";
			}
		});
		
		SkillGroupSelect.saveData=function(){
			if(SkillGroupSelect.type==1){
				var length=$("#skillGroupSelectForm input[type='checkbox']:checked").length;
				if(length==0){
					layer.msg("请至少选择一个技能组!");
					return;
				}
			}else{
				var val=$('input:radio[name="skillGroupIds"]:checked').val();
				if(val==null){
					layer.msg("请选择技能组!");
					return;
				}
			}
			ajax.remoteCall("/yc-portal/servlet/task?action=addSkillGroup",form.getJSONObject("#skillGroupSelectForm"),function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,function(){
						if(SkillGroupSelect.type==1){
							//人工外呼任务
							popup.layerClose();
							parent.$("#taskAssign_${param.taskId}").click();
						}else{
							parent.Task.loadData();
							popup.layerClose();
						}
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		  );
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>