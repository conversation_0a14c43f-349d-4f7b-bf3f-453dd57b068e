<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title><span i18n-content="字典新增" ></span></title>
</EasyTag:override>
<EasyTag:override name="content">

	<form id="dictForm" data-mars="dict.edit" data-pk="" method="post"
		autocomplete="off" data-mars-prefix="dict.">
		<input name="dict.DICT_GROUP_ID" class="form-control input-sm" type="hidden" value="${param.DICT_GROUP_ID }">
		<input name="GROUP_CODE" class="form-control input-sm" type="hidden" value="${param.GROUP_CODE }">
		<input name="dict.ID" class="form-control input-sm" type="hidden" value="${param.ID }">
		<table class="table  table-edit table-vzebra mt-12 " style="padding-top: 25px">
			<tbody>
				<tr>
					<td class="required" i18n-content="名称"></td>
					<td><input  data-rules="required"  maxlength="200" name="dict.NAME" class="form-control input-sm" type="text"></td>
					
				</tr>
				<tr>
					<td class="required" i18n-content="编号"></td>
					<td><input  data-rules="required" maxlength="100" name="dict.CODE" class="form-control input-sm" type="text" value="${param.CODE}"></td>

				</tr>
				<tr>
					<td class="required" i18n-content="序号"></td>
					<td><input  data-rules="required|digits"  maxlength="8" name="dict.SORT_NUM" class="form-control input-sm" type="text" value="1"></td>
				</tr>
				<tr>
					<td class="required" width="60px" i18n-content="是否启用"></td>
					<td><select class="form-control input-sm" name="dict.ENABLE_STATUS" data-rules="required" data-mars="dict.status">
					</select></td>
				</tr>
				
				<tr>
					<td i18n-content="备注"></td>
					<td><textarea rows="3" name="dict.BAKUP" maxlength="200" class="form-control input-sm"></textarea>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="dict.ajaxSubmitForm()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		requreLib.setplugs('wdate');
		jQuery.namespace("dict");

		$(function(){
			$("#dictForm").render();
		});
		dict.ajaxSubmitForm = function(){
				if(form.validate("#dictForm")){
					var data = form.getJSONObject("dictForm");
					ajax.remoteCall("${ctxPath}/servlet/dict?action=dictAdd",data,function(result) { 
							if(result.state == 1){
								layer.closeAll();
								layer.msg(result.msg,{icon: 1});
								if(CustTemp && CustTemp.renderData){
									CustTemp.renderData();
								}else{
									CustTemp.initData();
								}
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});
				}else{
					 i18nTooltip();
					 return;
				 };
				 
					
		 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>