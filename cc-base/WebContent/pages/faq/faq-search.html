<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ快捷查询</title>
    <link rel="stylesheet" href="/easitline-static/lib/layui/css/layui.css" />
    <style>
        ::-webkit-scrollbar {
            width: 3px;
            height: 3px;
            background: transparent;
        }
        ::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background-color: #C1C1C1;
        }
        ::-webkit-scrollbar-track {
            background: transparent;
        }
        html,body {
            margin: 0;
            padding: 0;
            height: 100%;
        }
        .search-wrapper {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
        }
        .search-form {
            display: flex;
            align-items: center;
        }
        .result-content {
            flex: 1;
            overflow-y: auto;
            margin: 15px 0;
        }
        .problem {
            font-size: 16px;
            line-height: 2;
            font-weight: bold;
            padding-left: 36px;
            background: url('/cc-base/static/images/problem.jpg') no-repeat left center;
        }
        .answer {
            padding-left: 36px;
            background: url('/cc-base/static/images/answer.jpg') no-repeat left top;
            margin-top: 5px;
            line-height: 24px;
            color: #666;
        }

        .result-item {
            border-width: 1px;
            border-style: solid;
            border-radius: 2px;
            background-color: #fff;
            border-color: #eee;
            padding: 10px;
            margin: 8px 0;
            box-shadow: 1px 1px 4px rgb(0 0 0 / 8%);
            cursor: pointer;
        }

        .empty-wrapper {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="search-wrapper">
        <div class="search-form layui-form">
            <select name="MODULE">
                <option value=""></option>
            </select>
            <input type="text" name="QUESTION" i18n-placeholder="FAQ" autocomplete="off" class="layui-input" style="margin-right: 10px;margin-left: 5px;">
            <button type="button" class="layui-btn layui-btn-normal searchBtn" i18n-content="搜索">
            </button>
            <button type="button" class="layui-btn openBtn" i18n-content="管理" style="display: none;">
            </button>
        </div>
        <div class="result-content">
        </div>
        <div class="empty-wrapper" style="display: none;">
            <svg width="184" height="152" viewBox="0 0 184 152" xmlns="http://www.w3.org/2000/svg"><g fill="none" fillRule="evenodd"><g transform="translate(24 31.67)"><ellipse fillOpacity=".8" fill="#F5F5F7" cx="67.797" cy="106.89" rx="67.797" ry="12.668"></ellipse><path d="M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z" fill="#AEB8C2"></path><path d="M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z" fill="url(#linearGradient-1)" transform="translate(13.56)"></path><path d="M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z" fill="#F5F5F7"></path><path d="M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z" fill="#DCE0E6"></path></g><path d="M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z" fill="#DCE0E6"></path><g transform="translate(149.65 15.383)" fill="#FFF"><ellipse cx="20.654" cy="3.167" rx="2.849" ry="2.815"></ellipse><path d="M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"></path></g></g></svg>
        </div>
        <div class="page-wrapper" id="page"></div>
    </div>
</body>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script type="text/javascript" src="/easitline-static/lib/layui/layui.js" charset="utf-8"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script src="/easitline-static/js/easitline.core-2.0.0.js?v=20211029"></script>
<script type="text/javascript" src="/cc-base/static/js/my_i18n.js?v=20211103"></script> 
<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=1231"></script> 
<script src="/cc-base/pages/faq/base.js"></script>
<script>
    var form = '', laypage = '', pageIndex = 1;
    layui.use(['form', 'laypage'], function(){
        form = layui.form;
        laypage = layui.laypage;

        laypage.render({
            elem: 'page',
            count: 0,
            limit: 10,
            curr: 1,
            layout: ['count', 'prev', 'page', 'next']
        });

        getMoudle();
        faqList(pageIndex);
    });

    $('.searchBtn').on('click', function() {
        pageIndex = 1;
        faqList(pageIndex)
    })

    $('.openBtn').on('click', function() {
        top.layer.closeAll();
        popup.openTab('/cc-base/pages/faq/faqList.jsp', getI18nValue('FAQ管理'))
    })

    $('.result-content').on('click', '.result-item', function(e) {
        var id = $(e.currentTarget).data('id')
        // $('.main iframe', window.parent.document).attr('src', '/cc-base/pages/faq/faq-edit.html')
        parent.getMsg(id)
    })

    // 获取faq接口
    function faqList(page) {
        var dataJson = {
            pageType: 3,
            QUESTION: $('[name=QUESTION]').val(),
            MODULE: $('[name=MODULE]').val(),
            TYPE: ''
        }
        $.ajax({
            url: '/cc-base/webcall?action=faqDao.faqList',
            type: 'POST',
            cache: false,
            dataType: 'json',
            data: {
                pageIndex: page,
                pageSize: 10,
                data: JSON.stringify(dataJson)
            },
            success: function(res) {
                var html = ''
                var list = res.data || []
                if (res.state == 1) {
                    for (var i = 0; i < list.length; i++) {
                        html += '<div class="result-item" data-id="' + list[i].ID + '">' + 
                                '<div class="problem">' + (i+1) + '、' + list[i].QUESTION + '</div>' +
                                '<div class="answer">' + list[i].FAQ_DESC + '</div></div>'
                    }
                }
                $('.result-content').html(html)
                if (list.length) {
                    $('.result-content .result-item').first().click()
                    $('.result-content').show()
                    $('.empty-wrapper').hide()
                } else {
                    $('.result-content').hide()
                    $('.empty-wrapper').show()
                }
                laypage.render({
                    elem: 'page',
                    count: res.totalRow,
                    limit: 10,
                    curr: res.pageNumber,
                    layout: ['count', 'prev', 'page', 'next'],
                    prev: getI18nValue('上一页'),
                    next: getI18nValue('下一页'),
                    jump: function(obj, first) {
                        console.log(obj, first);
                        $('.layui-laypage-count').html(getI18nValue('共')+' '+obj.count+' '+getI18nValue('条'))
                        if (!first) {
                            pageIndex = obj.curr
                            faqList(pageIndex)
                        }
                    }
                });
            }
        })
    }

    // 权限获取
    function getJurisdiction() {
        $.ajax({
            url: '/cc-base/servlet/menu?action=checkRes&ids=cc-base-sys-faq-mgr',
            success: function(res) {
                console.log(res);
                if (res.state == 1) {
                    if (res.data && res.data.length && res.data[0]['cc-base-sys-faq-mgr']) {
                        var isShow = res.data[0]['cc-base-sys-faq-mgr'] == 'Y'
                        if (isShow) $('.openBtn').show();
                    }
                }
            }
        })
    }

    getJurisdiction()

    // 获取系统模块
    function getMoudle() {
        var dataJson = {
            params: {},
            controls: ["common.getDict(SYSTEM_MODULE)"]
        }
        httpAjax('/cc-base/webcall', 'POST', dataJson, function(res) {
            var data = res['common.getDict(SYSTEM_MODULE)']
            if (data) {
                var html = ''
                dict = data.data
                for (var key in dict) {
                    html += '<option value="' + key + '">' + dict[key] + '</option>'
                }
                $('[name=MODULE]').append(html)
                form.render();
            }
        })
    }
 
</script>
</html>