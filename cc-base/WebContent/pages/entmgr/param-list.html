<!DOCTYPE html>
<html>

<head>
	<title>企业参数配置</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<!-- 基础的 css js 资源 -->
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
	<link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
	<link rel="stylesheet" type="text/css" href="/cc-base/static/layui/css/layui.css"/>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" />
	<link href="/easitline-static/lib/check/awesome-bootstrap-checkbox.css" rel="stylesheet">
	<link href="/easitline-static/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="/easitline-static/css/easitline.ui.css?v=20180129" rel="stylesheet">


	<style>
		.vue-box {
			background-color: rgba(242, 244, 247);
			overflow: hidden;
		}

		.vue-box-config {
			padding: 16px;
			background-color: #fff;
			border-radius: 4px;
			height: calc(100vh - 120px);
			overflow-y: scroll;
		}

		.el-collapse-item__header {
			font-size: 16px;
			font-weight: bold;
		}

		.footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80px;
			width: calc(100% - 28px);
			position: fixed;
			bottom: 0;
			background-color: #fff;
			border-radius: 4px;
			border-top: 1px solid #ebeef5;
		}

		.hidden {
			display: none;
		}
		.head-title{
			color: #222222;
		}
		.DelIocn{
			position: absolute;
			left: 60px;
			top: 0;
			width: 16px;
			height: 16px;
		}
		.yq-table-page .yq-card .card-content{
			padding: 0 8px;
			height: 100%;
		}
	</style>
</head>

<body class="yq-page-full vue-box">
	<div id="app" class="flex yq-table-page">
		<div class="flex yq-card">
			<div class="card-header">
				<div class="head-title">
					{{getI18nValue('企业参数配置')}}
					<el-tooltip class="item" effect="dark" placement="bottom-start">
						<template slot="content">
							{{getI18nValue('调用参数时请参照以下模块名和参数')}}<br/>
							{{getI18nValue('模块名：cc-base')}}<br/>
							<div style="margin-left:16px;">{{getI18nValue('企业门户logo - LOGO_URL')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('企业首页URL - WELCOME_PAGE_URL')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('满意度 - SATISY_STATUS')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('满意度流程号 - SATISY_FLOW_NO')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('客户资料是否显示会话小结 - SHOW_STATUS')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('来电弹屏类型 - CALL_OPEN')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否同步技能组 - JNZ_STATUS')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否开启每日问卷 - TEMPLETE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('问卷 - TEMPLETE_ID')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('弹屏时是否展示工单记录 - ORDERLISTISSHOW')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否开启水印 - ISWATERMAEK')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('签入时是否默认置忙 - CCBAR_READYMODE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('签入时是否隐藏工作模式 - CCBAR_WORKMODE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('客服输入引导查询常用语前缀 - ENABLE_PHARSE_PREFIX')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('常用语支持内容检索 - PHARSE_SEARCH_CONTENT')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('首页刷新待跟进会话的频率 - REFRESH_FOLLOW_MINIUTES')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('侧边栏是否开启按角色分配- DISTRIBUTION_BY_ROLE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否开启坐席助手 - AGENT_ASSISTANT')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('坐席助手是否开启查询知识库 - AGENT_KNOWLEDGE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('坐席助手卡槽key - SLOT_KEYS')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('坐席助手卡意图key - PURPOSE_KEY')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('坐席助手侧边栏 - ASSISTANT_EXPAND_URL')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否开启呼出弹屏 - INCOMING_CALL')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('最大导出数量 - ENT_MAX_EXPORT_SIZE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('离线导出数量 - ENT_OFFLINE_EXPORT_SIZE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('离线导出审核 - ENT_OFFLINE_EXPORT_AUDIT')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('单机并发导出数 - ENT_MAX_CONCURRENCE_EXPORT_SIZE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('大数据量界面是否显示总页数 - ENT_BIG_TABLE_SHOW_PAGE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('最大导入数量 - ENT_MAX_IMPORT_SIZE')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('是否开启席间交流 - COMMUNICATION')}}<br/></div>
							<div style="margin-left:16px;">{{getI18nValue('黑白红、免打扰名单是否需要审核- NEED_REVIEWED')}}<br/></div>
							{{getI18nValue('例如：SystemParamUtil.getEntParam(schemaName, entId, busiOrderId, "cc-base", "SATISY_STATUS");')}}<br/>
						</template>
						<span class="el-icon-info"></span>
					</el-tooltip>
				</div>
				<div class="yq-table-control">
					<el-dropdown @command="goPage">
						<el-button type="primary" @click="openAllcollapse">{{getI18nValue('全部展开/收起')}}</el-button>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item command="portal">{{getI18nValue('企业门户配置')}}</el-dropdown-item>
							<el-dropdown-item command="callBar">{{getI18nValue('话务条配置')}}</el-dropdown-item>
							<el-dropdown-item command="media">{{getI18nValue('全媒体配置')}}</el-dropdown-item>
							<el-dropdown-item command="attout">{{getI18nValue('导入导出配置')}}</el-dropdown-item>
							<el-dropdown-item command="other">{{getI18nValue('其他配置')}}</el-dropdown-item>
							<el-dropdown-item command="assistance">{{getI18nValue('远程协助')}}</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
			</div>
			<div class="card-content">
				<div class="vue-box-config">
					<el-form :model="form" :rules="rules" label-width="200px" ref="form">
						<el-collapse v-model="activeNames">
							<el-collapse-item :title="getI18nValue('企业门户配置')" name="portal" id="portal">
								<el-row>
									<el-col :span="12">
										<el-form-item :label="getI18nValue('企业门户logo')" prop="LOGO_URL" label-width="130px">
											<input class="hidden" type="file" id="file" name='lefile'
												accept="image/gif,image/jpeg,image/jpg,image/png,image/svg">
												<div style="position: relative;cursor: pointer;height: 65px;width: 65px;" @click="uploadFile('logo')">
													<el-avatar icon="el-icon-user-solid" :size="60" :src="logoUrl" >
													</el-avatar>
													<img v-if="logoUrl != ''" src="/cc-base/static/images/del-right.png" @click.stop="delImg('logo')" alt="" class="DelIocn">
												</div>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item :label="getI18nValue('浏览器页签logo')" prop="PORTAL_ICON_URL" label-width="120px">
											<input class="hidden" type="file" id="file1" name='lePortalUrl'
												accept="image/gif,image/jpeg,image/jpg,image/png,image/svg">
												<div style="position: relative;cursor: pointer;height: 65px;width: 65px;" @click="uploadFile('portal')">
													<el-avatar icon="el-icon-user-solid" :size="60" :src="portalIconUrl" >
													</el-avatar>
													<img v-if="portalIconUrl != ''" src="/cc-base/static/images/del-right.png" @click.stop="delImg('portal')" alt="" class="DelIocn">
												</div>
										</el-form-item>
									</el-col>
								</el-row>
								<el-form-item prop="PORTAL_TITLE" label-width="130px">
									<template slot="label">
										<span>{{getI18nValue('企业门户名称')}}
											<el-tooltip class="item" effect="dark" :content="getI18nValue('用于配置工作首页左上角、浏览器页签等地方的名称')"
												placement="bottom-start">
												<span class="el-icon-info"></span>
											</el-tooltip>
										</span>
									</template>
									<el-input v-model="form.PORTAL_TITLE" :placeholder="getI18nValue('企业门户名称')"></el-input>
								</el-form-item>
								<el-form-item prop="WELCOME_PAGE_URL" label-width="130px">
									<template slot="label">
										<span>{{getI18nValue('企业门户首页')}}
											<el-tooltip class="item" effect="dark" :content="getI18nValue('用于配置登录成功后的工作台首页地址')"
												placement="bottom-start">
												<span class="el-icon-info"></span>
											</el-tooltip>
										</span>
									</template>
									<div style="display: flex;">
										<el-input v-model="form.WELCOME_PAGE_URL" style="width: calc(100% - 320px);"></el-input>
										<el-select v-model="selectPortal" style="width:300px; margin-left:20px;" @change="setPageUrl">
											<el-option :label="getI18nValue('请选择')" value="" key=""></el-option>
											<el-option v-for="(key,value) of portalOpt" :label="key" :value="value" :key="value"></el-option>
										</el-select>
									</div>
								</el-form-item>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="WELCOME_PAGE_URL" label-width="130px">
											<template slot="label">
												{{getI18nValue('是否开启水印')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启水印后,界面上会用企业名称、坐席账号、当前时间做为水印，充满整个界面；需要坐席重新登录')" placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.ISWATERMAEK" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.ISWATERMAEK" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="WATERMAEKBODY" label-width="185px">
											<template slot="label">
												{{getI18nValue('水印是否只显示用户名')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开启后，界面上的水印只显示用户名')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.WATERMAEKBODY" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.WATERMAEKBODY" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
							</el-collapse-item>
							<el-collapse-item :title="getI18nValue('话务条配置')" name="callBar" id="callBar">
								<el-row>
									<el-col :span="12">
										<el-form-item prop="ADDRESS_STATUS">
											<template slot="label">
												{{getI18nValue('话务条显示归属地按钮')}}
											</template>
											<el-radio v-model="form.ADDRESS_STATUS" label="Y">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.ADDRESS_STATUS" label="N">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SATISY_STATUS">
											<template slot="label">
												{{getI18nValue('话务条显示满意度按钮')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，坐席在接听客户电话过程中，话务条上显示满意度按钮，坐席可以点击满意度按钮，将话务转接到满意度调查流程。（注意：点挂机按钮是直接挂断客户电话）')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.SATISY_STATUS" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SATISY_STATUS" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="24">
										<template v-if="form.SATISY_STATUS==1">
											<el-form-item prop="SATISY_FLOW_NO">
												<template slot="label">
													{{getI18nValue('满意度流程号')}}
													<el-tooltip class="item" effect="dark"
														:content="getI18nValue('开启话务条显示满意度按钮后，此处需要配置转接的满意度流程号码，话务满意度调查由该流程实现')"
														placement="bottom-start">
														<span class="el-icon-info"></span>
													</el-tooltip>
												</template>
												<el-input v-model="form.SATISY_FLOW_NO" :placeholder="getI18nValue('满意度流程号')"></el-input>
											</el-form-item>
										</template>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="CCBAR_READYMODE">
											<template slot="label">
												{{getI18nValue('签入时是否默认置忙')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开启后，坐席签入话务后，默认自动将坐席置忙')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CCBAR_READYMODE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.CCBAR_READYMODE" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="CCBAR_WORKMODE">
											<template slot="label">
												{{getI18nValue('签入时是否隐藏工作模式')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开启后，坐席签入话务后，话务条上不显示工作模式选择入口')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CCBAR_WORKMODE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.CCBAR_WORKMODE" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="CCBAR_SHOW_AUTO_ANSWER">
											<template slot="label">
												{{getI18nValue('签入时是否隐藏自动应答')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开启后，坐席签入话务后，话务条上不显示自动应答入口')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CCBAR_SHOW_AUTO_ANSWER" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.CCBAR_SHOW_AUTO_ANSWER" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="CCBAR_SHOW_VIDEO_BTN">
											<template slot="label">
												{{getI18nValue('显示视频客服按钮')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，坐席签入话务后，在坐席置忙和置闲的情况下，可以发起视频呼叫；如需开启，请先确认是否安装了云趣的语音视频呼叫平台产品')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CCBAR_SHOW_VIDEO_BTN" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.CCBAR_SHOW_VIDEO_BTN" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="BEGIN_CALL_OPEN">
											<template slot="label">
												{{getI18nValue('是否开启来电弹屏')}}
												<el-tooltip class="item" effect="dark" placement="bottom-start">
													<template slot="content">
														{{getI18nValue('开启后，坐席接听客户来电时，会弹屏(弹屏地址在来电弹屏类型处配置)')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.BEGIN_CALL_OPEN" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.BEGIN_CALL_OPEN" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="INCOMING_CALL">
											<template slot="label">
												{{getI18nValue('是否开启呼出弹屏')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开启后，坐席外呼客户且接通时，会弹屏(弹屏地址在来电弹屏类型处配置)')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.INCOMING_CALL" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.INCOMING_CALL" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="ORDERLISTISSHOW">
											<template slot="label">
												{{getI18nValue('弹屏时是否展示工单记录')}}
											</template>
											<el-radio v-model="form.ORDERLISTISSHOW" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.ORDERLISTISSHOW" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SHOWHANGUP">
											<template slot="label">
												{{getI18nValue('弹屏时是否屏蔽挂机按钮')}}
											</template>
											<el-radio v-model="form.SHOWHANGUP" label="0">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SHOWHANGUP" label="1">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SHOW_PHONE_DIR_BTN">
											<template slot="label">
												{{getI18nValue('话务条开启通讯录按钮')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，话务条上外呼右侧会增加通讯录按钮，点击后会打开通讯录列表，对通讯录记录进行外呼')" placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.SHOW_PHONE_DIR_BTN" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SHOW_PHONE_DIR_BTN" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="IS_SHOW_MONITOR">
											<template slot="label">
												{{getI18nValue('无感监听')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，当班长进行监听的时候，被监听坐席不会显示任何改变')" placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.IS_SHOW_MONITOR" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.IS_SHOW_MONITOR" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="24">
										<el-form-item prop="CALL_OPEN">
											<template slot="label">
												{{getI18nValue('来电弹屏类型')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('用于配置在电话接通时的弹屏信息，包含呼入、呼出接通')}}
														<div style="margin-left:16px;">{{getI18nValue('1、唯一弹屏：企业任何场景的客户来电都弹统一的弹屏界面；')}}</div>
														<div style="margin-left:16px;">{{getI18nValue('2、自定义弹屏: 用于配置在一些特殊场景下的弹屏，客户来电时，如不同接入号码或不同技能组弹不同的屏;')}}</div>
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CALL_OPEN" label="1">{{getI18nValue('唯一弹屏')}}</el-radio>
											<el-radio v-model="form.CALL_OPEN" label="0">{{getI18nValue('自定义弹屏')}}</el-radio>
											<el-button type="primary" size="small" @click="openCallinCfg">{{getI18nValue('配置弹屏详情')}}</el-button>
										</el-form-item>
									</el-col>
									<el-col :span="24">
										<el-form-item prop="CALL_RINGING_TYPE">
											<template slot="label">
												{{getI18nValue('振铃弹屏配置')}}
												<el-tooltip class="item" effect="dark" placement="bottom-start">
													<template slot="content">
														{{getI18nValue('用于配置在电话振铃时的弹屏配置，对于使用自动应答的场景，该弹屏不生效')}}
														<div style="margin-left: 16px;">{{getI18nValue('1、默认弹屏：显示默认的振铃框，包含号码信息、接听按钮；')}}</div>
														<div style="margin-left: 16px;">{{getI18nValue('2、自定义弹屏: 显示号码信息、接听按钮，还可以自定义显示一些数据列表，如联络历史、历史工单等;')}}</div>
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.CALL_RINGING_TYPE" label="1">{{getI18nValue('默认')}}</el-radio>
											<el-radio v-model="form.CALL_RINGING_TYPE" label="0">{{getI18nValue('自定义')}}</el-radio>
											<template v-if="form.CALL_RINGING_TYPE==0">
												<el-button type="primary" size="small"
													@click="openCallRingingCfg">{{getI18nValue('配置弹屏详情')}}</el-button>
											</template>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="CALL_BACK_SERVICE">
											<template slot="label">
												{{getI18nValue('通话回调服务')}}
												<el-tooltip class="item" effect="dark" placement="bottom-start">
													<template slot="content">
														{{getI18nValue('用于配置在电话通话结束后，触发的服务接口ID，默认为空则不触发')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-input v-model="form.CALL_BACK_SERVICE" :placeholder="getI18nValue('通话回调服务ID')"></el-input>
										</el-form-item>
									</el-col>
								</el-row>
							</el-collapse-item>
							<el-collapse-item :title="getI18nValue('全媒体配置')" name="media" id="media">
								<el-form-item prop="ENABLE_PHARSE_PREFIX" label-width="230px">
									<template slot="label">
										{{getI18nValue('客服输入引导查询常用语前缀')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('全媒体坐席工作台，在输入框回复客户时，支持通过查询机器人和常用语提示坐席输入；不设置内容时，默认查常用语；设置内容时，如设置为=，则默认从机器人查询，坐席输入=开头，才查常用语。')" placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.ENABLE_PHARSE_PREFIX" :placeholder="getI18nValue('客服输入引导查询常用语前缀')"></el-input>
								</el-form-item>
								<el-form-item prop="PHARSE_SEARCH_CONTENT" label-width="220px">
									<template slot="label">
										{{getI18nValue('常用语支持内容检索')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('开启后，坐席在全媒体工作台输入框回复客户时，如果从常用语查询提示信息，会同时查询常用语标题、内容；否则只查询常用语标题')"
											placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-radio v-model="form.PHARSE_SEARCH_CONTENT" label="1">{{getI18nValue('是')}}</el-radio>
									<el-radio v-model="form.PHARSE_SEARCH_CONTENT" label="0">{{getI18nValue('否')}}</el-radio>
								</el-form-item>
								<el-form-item prop="REFRESH_FOLLOW_MINIUTES" label-width="220px">
									<template slot="label">
										{{getI18nValue('首页刷新待跟进会话的频率')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('全媒体坐席在接待客户时，在全媒体工作台上可以将一些会话标记为待跟进，系统会间隔一段时间查询坐席是否有待跟进会话，进行弹屏提示；设置为0时，不启动提示')"
											placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<div style="display: flex;">
										<el-input type="number" v-model="form.REFRESH_FOLLOW_MINIUTES" :placeholder="getI18nValue('首页刷新待跟进会话的频率')"
										style="margin-right: 16px;"></el-input><span>Min</span>
									</div>
								</el-form-item>
							</el-collapse-item>
							<el-collapse-item :title="getI18nValue('导入导出配置')" name="attout" id="attout">
								<el-form-item prop="ENT_MAX_IMPORT_SIZE" label-width="150px">
									<template slot="label">
										{{getI18nValue('企业导入数量')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('用户通过界面上的执行导入操作时，excel里的数量不能超过该值；企业的该配置值不能超过cc-base模块里配置的最大导入数量.')"
											placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.ENT_MAX_IMPORT_SIZE" :placeholder="getI18nValue('企业导入数量')">
									</el-input>
								</el-form-item>
								<el-form-item prop="ENT_MAX_EXPORT_SIZE" label-width="150px">
									<template slot="label">
										{{getI18nValue('企业导出数量')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('用户通过界面上的执行导出操作时，excel里的数量不会超过该值；企业的该配置值不能超过cc-base模块里配置的最大导出数量.')"
											placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.ENT_MAX_EXPORT_SIZE" :placeholder="getI18nValue('企业导出数量')">
									</el-input>
								</el-form-item>
								<el-form-item prop="ENT_OFFLINE_EXPORT_SIZE" label-width="150px">
									<template slot="label">
										{{getI18nValue('离线导出数量')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('由于普通导出限制了数量,需要导出大量数据的界面上会有离线导出按钮,先由坐席发起申请，系统导出完成后再下载导出结果；')}}
												<div style="margin-left:16px;">{{getI18nValue('该配置用于控制离线导出的数量,可以大于企业导出数量;')}}</div>
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.ENT_OFFLINE_EXPORT_SIZE" :placeholder="getI18nValue('离线导出数量')">
									</el-input>
								</el-form-item>
								<el-form-item prop="ENT_OFFLINE_EXPORT_AUDIT" label-width="150px">
									<template slot="label">
										{{getI18nValue('离线导出审核')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('开启后，坐席提交离线申请后，需要管理员进行审核，审核通过后才开始执行导出')" placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-radio v-model="form.ENT_OFFLINE_EXPORT_AUDIT" label="1">{{getI18nValue('是')}}</el-radio>
									<el-radio v-model="form.ENT_OFFLINE_EXPORT_AUDIT" label="0">{{getI18nValue('否')}}</el-radio>
								</el-form-item>
								<el-form-item prop="ENT_MAX_CONCURRENCE_EXPORT_SIZE" label-width="150px">
									<template slot="label">
										{{getI18nValue('单机并发导出数')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('系统收到多个离线导出任务时，该参数用于配置并发任务执行数量，无特殊要求，保持默认值即可')" placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.ENT_MAX_CONCURRENCE_EXPORT_SIZE" :placeholder="getI18nValue('单机并发导出数')">
									</el-input>
								</el-form-item>
								<el-form-item prop="ENT_BIG_TABLE_SHOW_PAGE" label-width="150px">
									<template slot="label">
										{{getI18nValue('大数据量界面是否显示总页数')}}
										<el-tooltip class="item" effect="dark"
											:content="getI18nValue('开启后，对于大数据量界面(如通话记录、客户资料列表、全媒体记录等，由系统内置，不支持定义)，不显示总页数，只能手工点击查询总条数，提高界面查询性能')" placement="bottom-start">
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-radio v-model="form.ENT_BIG_TABLE_SHOW_PAGE" label="1">{{getI18nValue('是')}}</el-radio>
									<el-radio v-model="form.ENT_BIG_TABLE_SHOW_PAGE" label="0">{{getI18nValue('否')}}</el-radio>
								</el-form-item>
							</el-collapse-item>

							<el-collapse-item :title="getI18nValue('数据加密脱敏配置')" name="attout" id="attout">
								<el-row>
									<el-col :span="12">
										<el-form-item prop="DATA_ENCRPT_DATA" label-width="220px">
											<template slot="label">
												{{getI18nValue('加密存储客户信息')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														用于控制当前企业针对客户的敏感信息是否需要加密后入库。加密时，默认使用3des加解密，如需按定制方式加解密，需要自行定制服务PHONE_CRYPTOR_SERVICE。 另外，该修改会同步修改后端管理系统里企业信息的扩展配置字段(9059企业管理的加密数据库客户号码)，请慎重修改。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DATA_ENCRPT_DATA" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.DATA_ENCRPT_DATA" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SUPPORT_MODULE_CONFIG" label-width="220px">
											<template slot="label">
												{{getI18nValue('支持按模块配置')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														该模块慎重选择，建议设置为否; 选择是，系统根据加密、脱敏配置，如需进行加密、脱敏，则会继续读取模块上的配置，如果模块也开启相关控制，才会进行加密、脱敏。	如：企业参数里开启数据加密保存、支持按模块配置，但是模块配置里未开启加密保存，则保存数据为明文。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.SUPPORT_MODULE_CONFIG" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SUPPORT_MODULE_CONFIG" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="DATA_DESENS_PAGE" label-width="220px">
											<template slot="label">
												{{getI18nValue('客户信息脱敏展示')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														选择是，界面上进行脱敏；选择否，界面上显示明文。对于可查看脱敏数据角色里的人员、或未勾选脱敏数据类型，默认显示明文。另外，该修改会同步修改后端管理系统里企业信息的扩展配置字段(9059企业管理的加密页面显示号码)，请慎重修改。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DATA_DESENS_PAGE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.DATA_DESENS_PAGE" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="DATA_DESENS_EXPORT" label-width="220px">
											<template slot="label">
												{{getI18nValue('客户信息脱敏导出')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														选择是，导出的数据进行脱敏；选择否，导出的数据为明文。对于可查看脱敏数据角色里的人员、或未勾选脱敏数据类型，默认显示明文。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DATA_DESENS_EXPORT" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.DATA_DESENS_EXPORT" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="DESENS_TYPE" label-width="220px">
											<template slot="label">
												{{getI18nValue('数据脱敏方式')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														对于前端脱敏，返回明文给前端，由前端按配置进行脱敏。对于后端脱敏，返回密文给前端，前端直接显示密文，提供查看明文入口。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DESENS_TYPE" label="1">{{getI18nValue('前端脱敏')}}</el-radio>
											<!--
											<el-radio v-model="form.DESENS_TYPE" label="2">{{getI18nValue('后端脱敏')}}</el-radio>
											-->
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SUPPORT_APPLY_DESENS" label-width="220px">
											<template slot="label">
												{{getI18nValue('支撑申请查看脱敏数据')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														对于界面上的数据进行脱敏显示时，如果开启该配置，则在脱敏数据旁显示查看原始数据入口，否则不显示。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.SUPPORT_APPLY_DESENS" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SUPPORT_APPLY_DESENS" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item prop="SUPPORT_BUSI_TYPE" label-width="220px">
											<template slot="label">
												{{getI18nValue('需脱敏数据类型')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														系统对勾选的数据类型，会进行脱敏处理；未勾选的数据类型，直接返回明文。界面上每个字段的所属类型由各业务模块决定；
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-checkbox-group v-model="form.SUPPORT_BUSI_TYPE">
												<el-checkbox label="02">{{getI18nValue('客户名称')}}</el-checkbox>
												<el-checkbox label="03">{{getI18nValue('客户手机号')}}</el-checkbox>
												<el-checkbox label="04">{{getI18nValue('客户身份证号')}}</el-checkbox>
												<el-checkbox label="05">{{getI18nValue('客户地址')}}</el-checkbox>
												<el-checkbox label="98">{{getI18nValue('客户邮箱')}}</el-checkbox>
												<el-checkbox label="99">{{getI18nValue('其他')}}</el-checkbox>
											</el-checkbox-group>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item prop="DESENS_ROLES" label-width="220px">
											<template slot="label">
												{{getI18nValue('可查看脱敏数据角色')}}
												<el-tooltip class="item" effect="dark"
															placement="bottom-start">
													<template slot="content">
														对于界面上的数据进行脱敏显示时，如果用户在所选的角色里，则显示为明文。该配置仅仅针对后端脱敏生效。
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<!-- <el-radio v-model="form.DESENS_ROLES" label="1">{{getI18nValue('角色1')}}</el-radio>
											<el-radio v-model="form.DESENS_ROLES" label="2">{{getI18nValue('角色2')}}</el-radio> -->
											<el-select v-model="form.DESENS_ROLES" :placeholder="getI18nValue('请选择角色')" style="margin-right: 16px;width: 100%;" multiple>
												<el-option v-for="(item,index) of roleRelative" :label="item.NAME" :value="item.CODE" :key="item.CODE"></el-option>
											</el-select>
										</el-form-item>
									</el-col>
								</el-row>
							</el-collapse-item>

							<el-collapse-item :title="getI18nValue('其他配置')" name="other" id="other">
								<el-row>
									<el-col :span="12">
										<el-form-item prop="SHOW_STATUS" label-width="220px">
											<template slot="label">
												{{getI18nValue('客户资料是否显示会话小结')}}
											</template>
											<el-radio v-model="form.SHOW_STATUS" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.SHOW_STATUS" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="JNZ_STATUS" label-width="220px">
											<template slot="label">
												{{getI18nValue('是否同步技能组')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('在企业客服3.0版本之前，技能组修改后，需要通过服务(YC-SKILLGROUP-NOTIFY-)通知yc-api，最终通知磐石重新同步对应的技能组；3.0后的版本不需要使用该配置')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.JNZ_STATUS" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.JNZ_STATUS" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="TEMPLETE" label-width="220px">
											<template slot="label">
												{{getI18nValue('是否开启每日问卷')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('在一些特殊场景，如疫情期间每天坐席首次登录需要填写个人健康信息，可以使用该功能，弹出配置好的调查问卷界面给坐席填写')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.TEMPLETE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.TEMPLETE" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="TEMPLETE_ID" label-width="220px">
											<template slot="label">
												{{getI18nValue('问卷')}}
												<el-tooltip class="item" effect="dark" :content="getI18nValue('开始每日问卷后，需要选择要使用的问卷')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-select v-model="form.TEMPLETE_ID" :placeholder="getI18nValue('问卷')" style="margin-right: 16px;">
												<el-option v-for="(key,value) of templeteList" :label="key" :value="value" :key="value"></el-option>
											</el-select>
											<el-button type="primary" @click="QuestionManager">{{getI18nValue('问卷管理')}}</el-button>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="DISABLE_NO_LOGIN_USER" label-width="220px">
											<template slot="label">
												{{getI18nValue('自动禁用用户')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('每晚自动将3个月未登录、以及已过账号有效期的用户账号 禁用')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DISABLE_NO_LOGIN_USER" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.DISABLE_NO_LOGIN_USER" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="DISTRIBUTION_BY_ROLE" label-width="220px">
											<template slot="label">
												{{getI18nValue('侧边栏是否开启按角色分配')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后,在全媒体工作台、会话记录、通话记录等存在侧边栏相关界面，查询当前用户的侧边栏会按角色进行权限控制，此时需要在侧边栏配置界面里、将侧边栏与角色关联')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.DISTRIBUTION_BY_ROLE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.DISTRIBUTION_BY_ROLE" label="0">{{getI18nValue('否')}}</el-radio>
											<el-button type="primary" @click="openCbl">{{getI18nValue('侧边栏')}}</el-button>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="AGENT_ASSISTANT" label-width="220px">
											<template slot="label">
												{{getI18nValue('是否开启坐席助手')}}
											</template>
											<el-radio v-model="form.AGENT_ASSISTANT" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.AGENT_ASSISTANT" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="AGENT_KNOWLEDGE" label-width="220px">
											<template slot="label">
												{{getI18nValue('坐席助手是否开启查询知识库')}}
											</template>
											<el-radio v-model="form.AGENT_KNOWLEDGE" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.AGENT_KNOWLEDGE" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-form-item prop="SLOT_KEYS" label-width="220px">
									<template slot="label">
										{{getI18nValue('坐席助手卡槽key')}}
									</template>
									<el-input v-model="form.SLOT_KEYS" type="textarea" :rows="3"
										:placeholder="getI18nValue('多个用英文逗号隔开,如:cust_name,cust_sex')"></el-input>
								</el-form-item>
								<el-form-item prop="PURPOSE_KEY" label-width="220px">
									<template slot="label">
										{{getI18nValue('坐席助手卡意图key')}}
									</template>
									<el-input v-model="form.PURPOSE_KEY" type="texteara" :rows="1" maxlength="100"
										:placeholder="getI18nValue('坐席助手卡意图key')"></el-input>
								</el-form-item>
								<el-form-item prop="ASSISTANT_EXPAND_URL" label-width="220px">
									<template slot="label">
										{{getI18nValue('坐席助手侧边栏')}}
									</template>
									<el-input v-model="form.ASSISTANT_EXPAND_URL" type="texteara" :rows="1"
										:placeholder="getI18nValue('坐席助手侧边栏')"></el-input>
								</el-form-item>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="COMMUNICATION" label-width="220px">
											<template slot="label">
												{{getI18nValue('是否开启席间交流')}}
											</template>
											<el-radio v-model="form.COMMUNICATION" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.COMMUNICATION" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="OPEN_VOICE_SMS_SATISFY" label-width="220px">
											<template slot="label">
												{{getI18nValue('开启话务短信满意度邀评')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('开启后,通话结束1-2分钟,cc-statgw会对ivr未发起满意度邀评的客户,自动发送短信满意度邀评;')}}
														<div style="margin-left:16px;">{{getI18nValue('注意：仅仅针对近5小时的通话记录(包含 1-外线呼入、2-IVR呼入、4-IVR咨询呼入)、且通话时长在5秒以上(可以通过下方配置项配置)、且属于客户主动挂机，才发送短信满意度。')}}</div>
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.OPEN_VOICE_SMS_SATISFY" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.OPEN_VOICE_SMS_SATISFY" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<template v-if="form.OPEN_VOICE_SMS_SATISFY==1">
									<el-form-item prop="VOICE_SMS_SATISFY_CONTENT" label-width="220px">
										<template slot="label">
											{{getI18nValue('话务短信满意度邀评内容')}}
											<el-tooltip class="item" effect="dark"
												placement="bottom-start">
												<template slot="content">
													{{getI18nValue('支持两种方式:')}}
													<div style="margin-left: 16px;">{{getI18nValue('1、短信无需按固定模板发送，可以直接配置具体的内容，不支持参数；')}}</div>
													<div style="margin-left: 16px;">{{getI18nValue('2、短信需要按模板发送,配置成@VOICE_SMS_STATSY，然后在短信模块的短信模板配置界面，名称为  系统类(内置) 目录下找到该编号模板(如没有，可以一键部署，也可以手工创建,模板编号保持一致即可),该方式支持通过点击url评价满意度，模板内容里用参数url，fullUrl接收评价链接')}}</div>
												</template>
												<span class="el-icon-info"></span>
											</el-tooltip>
										</template>
										<el-input v-model="form.VOICE_SMS_SATISFY_CONTENT"
											:placeholder="getI18nValue('话务短信满意度邀评内容')"></el-input>
									</el-form-item>
									<el-form-item prop="VOICE_SMS_SATISFY_TIMEOUT" label-width="220px">
										<template slot="label">
											{{getI18nValue('话务短信满意度评价有效期(分钟)')}}
										</template>
										<el-input type="number" v-model="form.VOICE_SMS_SATISFY_TIMEOUT"></el-input>
									</el-form-item>
									<el-form-item prop="VOICE_SMS_SATISFY_BILLTIME" label-width="220px">
										<template slot="label">
											{{getI18nValue('话务短信满意度话单时长(秒)')}}
											<el-tooltip class="item" effect="dark" :content="getI18nValue('只有通话时长超过该配置的话单，才会发送短信满意度')"
												placement="bottom-start">
												<span class="el-icon-info"></span>
											</el-tooltip>
										</template>
										<el-input type="number" v-model="form.VOICE_SMS_SATISFY_BILLTIME"></el-input>
									</el-form-item>
									
									<el-form-item prop="VOICE_SMS_SATISFY_LIMIT_HOUR" label-width="220px">
										<template slot="label">
											{{getI18nValue('限制同个号码X小时内只能发送多少条短信邀评(小时)')}}
											<el-tooltip class="item" effect="dark" :content="getI18nValue('必须和下面参数同时大于0才有效,如果都等于0则不限制')"
												placement="bottom-start">
												<span class="el-icon-info"></span>
											</el-tooltip>
										</template>
										<el-input type="number" v-model="form.VOICE_SMS_SATISFY_LIMIT_HOUR"></el-input>
									</el-form-item>
									
									<el-form-item prop="VOICE_SMS_SATISFY_LIMIT_COUNT" label-width="220px">
										<template slot="label">
											{{getI18nValue('限制同个号码多少小时内只能发送X条短信邀评(次数) ')}}
											<el-tooltip class="item" effect="dark" :content="getI18nValue('必须和上面参数同时大于0才有效,如果都等于0则不限制')"
												placement="bottom-start">
												<span class="el-icon-info"></span>
											</el-tooltip>
										</template>
										<el-input type="number" v-model="form.VOICE_SMS_SATISFY_LIMIT_COUNT"></el-input>
									</el-form-item>
									
								</template>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="AUTO_HANDLE_MISCALL" label-width="220px">
											<template slot="label">
												{{getI18nValue('通话接通时自动处理漏话')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，当该客户拨打电话客服接听时，如果该客户近三个月内有待处理的漏话、未接来电时，会自动将漏话、未接设置为已处理(cc-portal弹屏事件里自动更新数据)')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.AUTO_HANDLE_MISCALL" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.AUTO_HANDLE_MISCALL" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="AUTO_HANDLE_WORD" label-width="220px">
											<template slot="label">
												{{getI18nValue('通话接通时自动处理留言')}}
												<el-tooltip class="item" effect="dark"
													:content="getI18nValue('开启后，当该客户拨打电话客服接听时，如果该客户近三个月内有待处理的留言时，会自动将留言设置为已处理(cc-portal弹屏事件里自动更新数据)')"
													placement="bottom-start">
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.AUTO_HANDLE_WORD" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.AUTO_HANDLE_WORD" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-form-item prop="SUBMIT_ORDER_URL" label-width="220px">
									<template slot="label">
										{{getI18nValue('派发工单地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('配置用于在通话记录、会话记录、邮件会话记录补录工单的地址，系统默认为使用工单系统cc-eorder的链接，也可以改为其它链接，链接需要支持如下参数：')}}
												<div style="margin-left: 16px;">{{getI18nValue('1、sourceType，数据来源，值包含:voice、media、email')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('2、sessionId，客户标识，如客户来电号码、客户会话接入标识、客户邮件地址')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('3、channelKey，平台标识，如话务平台号码、客户接入渠道key、接收客户邮件的平台邮件地址')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('4、sourceRecordId，业务记录id，如通话记录id、会话记录id、邮件会话id')}}</div>
												<div>{{getI18nValue('注意：')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('1、在通话记录、会话记录、邮件记录界面，补录工单链接上，需要拼接上以上参数')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('2、配置的链接必须带上一个参数，必须包含一个问号(?)')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('3、在通话记录、会话记录、邮件记录通过该链接补录工单后，提交工单时，工单系统需要将工单id写入通话记录或会话记录或邮件会话记录的ORDER_ID字段中；对于邮件会话还需写入ORDER_NO')}}</div>
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<div style="display:flex;">
										<el-input v-model="form.SUBMIT_ORDER_URL" maxLength="100"
											style="width: calc(100% - 80px);margin-right: 16px;"
											:placeholder="getI18nValue('派发工单地址')"></el-input>
										<el-button type="primary" @click="fullUrl('sumbitUrl')">URL</el-button>
									</div>
								</el-form-item>
								<el-form-item prop="VIEW_ORDER_URL" label-width="220px">
									<template slot="label">
										{{getI18nValue('查看工单地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('配置用于在通话记录、会话记录、邮件会话记录等界面查看对应工单的链接，系统默认为使用工单系统cc-eorder的链接，也可以改为其它链接，链接需要支持如下参数：')}}
												<div style="margin-left: 16px;">{{getI18nValue('1、sourceType，数据来源，值包含:voice、media、email')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('2、sessionId，客户标识，如客户来电号码、客户会话接入标识、客户邮件地址')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('3、channelKey，平台标识，如话务平台号码、客户接入渠道key、接收客户邮件的平台邮件地址')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('4、sourceRecordId，业务记录id，如通话记录id、会话记录id、邮件会话id')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('5、orderNo,由工单系统反向写入的ORDER_NO字段')}}</div>
												<div>{{getI18nValue('注意：')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('1、在通话记录、会话记录、邮件记录界面，如果有工单，查看工单界面时，需要拼接上以上参数')}}</div>
												<div style="margin-left: 16px;">{{getI18nValue('2、配置的链接必须带上一个参数，必须包含一个问号(?)')}}</div>
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<div style="display: flex;">
										<el-input v-model="form.VIEW_ORDER_URL" maxLength="100"
											style="width: calc(100% - 80px);margin-right: 16px;"
											:placeholder="getI18nValue('查看工单地址')"></el-input>
										<el-button type="primary" @click="fullUrl('viewOrderUrl')">URL</el-button>
									</div>
								</el-form-item>
								<el-form-item prop="H5_SUBMIT_ORDER_URL" label-width="220px">
									<template slot="label">
										{{getI18nValue('H5派发工单地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('配置用于在手机移动端补录工单的地址，系统默认为使用工单系统cc-eorder的链接，也可以改为其它链接，链接需要支持如下参数：')}}
												<div>{{getI18nValue('与派发工单地址链接的参数保持一致')}}</div>
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<div style="display: flex;">
										<el-input v-model="form.H5_SUBMIT_ORDER_URL" maxLength="100"
											style="width: calc(100% - 80px);margin-right: 16px;"
											:placeholder="getI18nValue('H5派发工单地址')"></el-input>
										<el-button type="primary" @click="fullUrl('H5OrderUrl')">URL</el-button>
									</div>
								</el-form-item>
								<el-form-item prop="H5_VIEW_ORDER_URL" label-width="220px">
									<template slot="label">
										{{getI18nValue('H5查看工单地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">'
												{{getI18nValue('配置用于在手机移动端界面查看对应工单的链接，系统默认为使用工单系统cc-eorder的链接，也可以改为其它链接，链接需要支持如下参数：')}}
												<div>{{getI18nValue('与查看工单地址链接的参数保持一致')}}</div>
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<div style="display: flex;">
										<el-input v-model="form.H5_VIEW_ORDER_URL" maxLength="100"
											style="width: calc(100% - 80px);margin-right: 16px;"
											:placeholder="getI18nValue('H5查看工单地址')"></el-input>
										<el-button type="primary" @click="fullUrl('H5ViewOrderUrl')">URL</el-button>
									</div>
								</el-form-item>
								<el-row>
									<el-col :span="12">
										<el-form-item prop="AGENT_OPERATE_ROSTER" label-width="220px">
											<template slot="label">
												{{getI18nValue('允许坐席操作黑白名单')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('开启后，坐席在来电弹屏或在线坐席工作台界面侧边栏的客户资料界面里，允许坐席将客户加入到黑白红、免打扰名单、以及从黑白红、免打扰名单里移除')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.AGENT_OPERATE_ROSTER" label="1">{{getI18nValue('开启')}}</el-radio>
											<el-radio v-model="form.AGENT_OPERATE_ROSTER" label="0">{{getI18nValue('关闭')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="OPERATE_NEED_CHECK" label-width="220px">
											<template slot="label">
												{{getI18nValue('操作黑白名单审核')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('开启后，坐席将客户加入到黑白红、免打扰名单，或从黑白红、免打扰名单移除时，需要审核通过后才生效')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.OPERATE_NEED_CHECK" label="1">{{getI18nValue('开启')}}</el-radio>
											<el-radio v-model="form.OPERATE_NEED_CHECK" label="0">{{getI18nValue('关闭')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="SHOW_PREVIEW" label-width="220px">
											<template slot="label">
												{{getI18nValue('附件上传界面是否开启预览功能')}}
											</template>
											<el-radio v-model="form.SHOW_PREVIEW" label="1">{{getI18nValue('开启')}}</el-radio>
											<el-radio v-model="form.SHOW_PREVIEW" label="0">{{getI18nValue('关闭')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
							</el-collapse-item>
							<el-collapse-item :title="getI18nValue('远程协助')" name="assistance" id="assistance">
								<el-row>
									<el-col :span="12">
										<el-form-item prop="remoteSwitch" label-width="200px">
											<template slot="label">
												{{getI18nValue('开启远程协助')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('开启远程协助功能，需要配套安装主控端、被控端、远程协助引擎等功能；坐席登录系统后，自动自动拉起远程协助主控端。 远程协助的配置会写入redis，格式如：CC-REMOTE-CFG-企业id')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.remoteSwitch" label="1">{{getI18nValue('是')}}</el-radio>
											<el-radio v-model="form.remoteSwitch" label="0">{{getI18nValue('否')}}</el-radio>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item prop="remoteServerType" label-width="200px">
											<template slot="label">
												{{getI18nValue('远程协助服务商')}}
												<el-tooltip class="item" effect="dark"
													placement="bottom-start">
													<template slot="content">
														{{getI18nValue('远程协助服务商')}}
													</template>
													<span class="el-icon-info"></span>
												</el-tooltip>
											</template>
											<el-radio v-model="form.remoteServerType" label="1">{{getI18nValue('向日葵')}}</el-radio>
										</el-form-item>
									</el-col>
								</el-row>
								<el-form-item prop="remotePetraUrl" label-width="200px">
									<template slot="label">
										{{getI18nValue('磐石接口地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('处理远程协助过程中，企业客服需要与磐石网关进行对接，包含通知磐石更新缓存、发送通知给客户端等; 此处保存参数时，会立即通知磐石更新缓存；格式如：http://************:8819')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remotePetraUrl" :placeholder="getI18nValue('磐石接口地址')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteOpenId" label-width="200px">
									<template slot="label">
										{{getI18nValue('openId')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('对接远程协助所需的参数，从远程服务提供商处获取')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteOpenId" :placeholder="getI18nValue('openId')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteOpenKey" label-width="200px">
									<template slot="label">
										{{getI18nValue('openKey')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('对接远程协助所需的参数，从远程服务提供商处获取')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteOpenKey" :placeholder="getI18nValue('openKey')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteDomain" label-width="200px">
									<template slot="label">
										{{getI18nValue('domain')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('对接远程协助所需的参数，从远程服务提供商处获取')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteDomain" :placeholder="getI18nValue('domain')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteUseSSL" label-width="200px">
									<template slot="label">
										{{getI18nValue('useSSL')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('对接远程协助所需的参数，从远程服务提供商处获取')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-radio v-model="form.remoteUseSSL" label="1">{{getI18nValue('是')}}</el-radio>
									<el-radio v-model="form.remoteUseSSL" label="0">{{getI18nValue('否')}}</el-radio>
								</el-form-item>
								<el-form-item prop="remoteUploadUrl" label-width="200px">
									<template slot="label">
										{{getI18nValue('远程录屏上传接口地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('对接远程协助时，录屏、截图文件上传的接口地址')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteUploadUrl" :placeholder="getI18nValue('远程录屏上传接口地址')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteClientH5Url" label-width="200px">
									<template slot="label">
										{{getI18nValue('远程客户端地址')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('客户安装远程客户端后，打开界面显示的地址')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteClientH5Url" :placeholder="getI18nValue('远程客户端地址')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteUninstallPwd" label-width="200px">
									<template slot="label">
										{{getI18nValue('卸载密码')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('客户安装远程客户端后，需要卸载时输入的密码，为空时，客户端卸载密码默认为 yunqu168')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input v-model="form.remoteUninstallPwd" :placeholder="getI18nValue('卸载密码')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteClientH5Hint" label-width="200px">
									<template slot="label">
										{{getI18nValue('客户远程免责提示')}}
										<el-tooltip class="item" effect="dark"
											placement="bottom-start">
											<template slot="content">
												{{getI18nValue('客户是否接受远程所展示的提示')}}
											</template>
											<span class="el-icon-info"></span>
										</el-tooltip>
									</template>
									<el-input type="textarea" :rows="5" v-model="form.remoteClientH5Hint" :placeholder="getI18nValue('客户远程免责提示')">
										免责声明：
本次远程协助是为了帮助您解决工作中碰到的问题，服务人员将在远端操作您的电脑桌面。服务人员承诺远程操作过程中遵守《中华人民共和国计算机信息系统安全保护条例》和《计算机信息网络国际联网安全保护管理办法》及其他相关法律、法规和行政规章制度。您电脑的网络安全及数据安全需自行维护，
因个人维护不当造成的黑客政击、计算机病毒侵入及进而造成的个人资料泄露、丢失、被盗用或被篡改等，均与本次远程协助无关。同意请点“是”，不同意请点“否”。点击“是”后将开始本次远程协助服务，整个远程控制过程将进行屏幕录制。
									</el-input>
								</el-form-item>
								<el-form-item prop="remoteMustUpgrade" label-width="200px">
									<template slot="label">
										{{getI18nValue('强制升级')}}
									</template>
									<el-radio v-model="form.remoteMustUpgrade" label="1">{{getI18nValue('是')}}</el-radio>
									<el-radio v-model="form.remoteMustUpgrade" label="0">{{getI18nValue('否')}}</el-radio>
								</el-form-item>
								<el-form-item prop="remoteServerVersion" label-width="200px">
									<template slot="label">
										{{getI18nValue('主控端版本号')}}
									</template>
									<el-input v-model="form.remoteServerVersion" :placeholder="getI18nValue('主控端版本号')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteServerUpgradeDesc" label-width="200px">
									<template slot="label">
										{{getI18nValue('主控端版本升级说明')}}
									</template>
									<el-input type="textarea" :rows="5" v-model="form.remoteServerUpgradeDesc" :placeholder="getI18nValue('主控端版本号')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteServerUpgradeUrl" label-width="200px">
									<template slot="label">
										{{getI18nValue('主控端下载链接')}}
									</template>
									<div style="display: flex;">
										<el-input style="margin-right:16px;" v-model="form.remoteServerUpgradeUrl" :placeholder="getI18nValue('主控端下载链接')"></el-input>
										<el-button type="primary" @click="importData('remoteServerUpgradeUrl')">{{getI18nValue('上传')}}</el-button>
										<el-button type="primary" plain @click="download('remoteServerUpgradeUrl')">{{getI18nValue('下载')}}</el-button>
									</div>
								</el-form-item>
								<el-form-item prop="remoteClientVersion" label-width="200px">
									<template slot="label">
										{{getI18nValue('被控端版本号')}}
									</template>
									<el-input v-model="form.remoteClientVersion" :placeholder="getI18nValue('被控端版本号')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteClientUpgradeDesc" label-width="200px">
									<template slot="label">
										{{getI18nValue('被控版本升级说明')}}
									</template>
									<el-input type="textarea" :rows="5" v-model="form.remoteClientUpgradeDesc" :placeholder="getI18nValue('被控版本升级说明')"></el-input>
								</el-form-item>
								<el-form-item prop="remoteClientUpgradeUrl" label-width="200px">
									<template slot="label">
										{{getI18nValue('被控端下载链接')}}
									</template>
									<div style="display: flex;">
										<el-input style="margin-right:16px;" v-model="form.remoteClientUpgradeUrl" :placeholder="getI18nValue('主控端下载链接')"></el-input>
										<el-button type="primary" @click="importData('remoteClientUpgradeUrl')">{{getI18nValue('上传')}}</el-button>
										<el-button type="primary" plain @click="download('remoteClientUpgradeUrl')">{{getI18nValue('下载')}}</el-button>
									</div>
								</el-form-item>
							</el-collapse-item>
						</el-collapse>
					</el-form>
				</div>
			</div>
			<div class="footer">
				<el-button type="primary" @click="save">{{getI18nValue('保存')}}</el-button>
			</div>
		</div>
	</div>
	<script type="text/javascript" src="/easitline-static/js/xss.min.js"></script>
	<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
	<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/my_i18n.js?v=202111"></script>
	<script type="text/javascript" src="/cc-base/static/js/i18n.js?v=1"></script>
	<script type="text/javascript" src="/cc-base/static/js/time.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
	<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
	<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
	<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
	<script>
		function uuid() {
			var s = [];
			var hexDigits = "0123456789abcdef";
			for (var i = 0; i < 36; i++) {
				s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
			}
			s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
			s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
			s[8] = s[13] = s[18] = s[23] = "-";

			var uuid = s.join("");
			return uuid;
		}
		function scrollToCenter(elementId) {
			var element = document.getElementById(elementId);
			if (element) {
				element.scrollIntoView({ behavior: 'smooth', block: 'start' });
			}
		}
		function getObjectURL(file) {
			var url = null ;
			if (window.createObjectURL!=undefined) {
			url = window.createObjectURL(file) ;
			} else if (window.URL!=undefined) { 
			url = window.URL.createObjectURL(file) ;
			} else if (window.webkitURL!=undefined) { 
			url = window.webkitURL.createObjectURL(file) ;
			}
			return url ;
		}
		var uploadSetting = {}
		var app = new Vue({
			el: '#app',
			data() {
				return {
					errorUrl:'/cc-base/static/images/notFile.png',
					logoUrl:'',
					portalIconUrl:'',
					selectPortal: '',
					portalOpt: {
						'1': getI18nValue('默认'),
						'2': getI18nValue('简约'),
						'3': getI18nValue('v3.3'),
						'4': getI18nValue('v3.4'),
					},
					activeNames: ['portal', 'callBar', 'media', 'attout', 'other', 'assistance'],
					form: {
						LOGO_URL: '',
						PORTAL_ICON_URL: '',
						PORTAL_TITLE: '',
						WELCOME_PAGE_URL: '',
						ISWATERMAEK: '0',
						WATERMAEKBODY: '0',
						ADDRESS_STATUS: '0',
						SATISY_STATUS: '0',
						SATISY_FLOW_NO: '',
						CCBAR_READYMODE: '0',
						CCBAR_WORKMODE: '0',
						CCBAR_SHOW_AUTO_ANSWER: '0',
						CCBAR_SHOW_VIDEO_BTN: '0',
						BEGIN_CALL_OPEN: '0',
						INCOMING_CALL: '0',
						CALL_OPEN: '0',
						CALL_RINGING_TYPE: '0',
						ORDERLISTISSHOW: '0',
						SHOW_PHONE_DIR_BTN: '0',
						IS_SHOW_MONITOR: '0',
						SHOWHANGUP: '0',
						ENABLE_PHARSE_PREFIX: '',
						PHARSE_SEARCH_CONTENT: '0',
						REFRESH_FOLLOW_MINIUTES: '',
						ENT_MAX_IMPORT_SIZE: '10000',
						ENT_MAX_EXPORT_SIZE: '10000',
						ENT_OFFLINE_EXPORT_SIZE: '1000',
						ENT_OFFLINE_EXPORT_AUDIT: '0',
						ENT_MAX_CONCURRENCE_EXPORT_SIZE: '10',
						ENT_BIG_TABLE_SHOW_PAGE: '1',
						SHOW_STATUS: '0',
						DATA_ENCRPT_DATA: '0',
						SUPPORT_MODULE_CONFIG: '0',
						DATA_DESENS_PAGE: '0',
						DATA_DESENS_EXPORT: '0',
						DESENS_TYPE: '1',
						DESENS_ROLES: '',
						SUPPORT_APPLY_DESENS: '0',
						SUPPORT_BUSI_TYPE: [],
						JNZ_STATUS: '0',
						TEMPLETE: '0',
						TEMPLETE_ID: '',
						DISTRIBUTION_BY_ROLE: '0',
						AGENT_ASSISTANT: '0',
						AGENT_KNOWLEDGE: '0',
						SLOT_KEYS: '',
						PURPOSE_KEY: '',
						ASSISTANT_EXPAND_URL: '',
						COMMUNICATION: '0',
						OPEN_VOICE_SMS_SATISFY: '0',
						VOICE_SMS_SATISFY_CONTENT: getI18nValue('您好,感谢您的来电,请对我们的服务进行评价,1-非常满意,2-满意,3-一般,4-对服务不满意,5-对结果不满意,请回复编号,谢谢!'),
						VOICE_SMS_SATISFY_TIMEOUT: '30',
						VOICE_SMS_SATISFY_BILLTIME: '5',
						VOICE_SMS_SATISFY_LIMIT_HOUR: '0',
						VOICE_SMS_SATISFY_LIMIT_COUNT: '0',
						AUTO_HANDLE_MISCALL: '0',
						AUTO_HANDLE_WORD: '0',
						SUBMIT_ORDER_URL: '',
						VIEW_ORDER_URL: '',
						H5_SUBMIT_ORDER_URL: '',
						H5_VIEW_ORDER_URL: '',
						AGENT_OPERATE_ROSTER: '0',
						OPERATE_NEED_CHECK: '0',
						SHOW_PREVIEW: '0',
						remoteSwitch: '0',
						remoteServerType:'1',
						remotePetraUrl:'',
						remoteOpenId:'',
						remoteOpenKey:'',
						remoteDomain:'',
						remoteUseSSL:'0',
						remoteUploadUrl:'',
						remoteClientH5Url:'',
						remoteUninstallPwd:'',
						remoteClientH5Hint:'',
						remoteMustUpgrade:'0',
						remoteServerVersion:'',
						remoteServerUpgradeDesc:'',
						remoteServerUpgradeUrl:'',
						remoteClientVersion:'',
						remoteClientUpgradeDesc:'',
						remoteClientUpgradeUrl:''
					},
					rules: {
						LOGO_URL: [{ required: true, message: '企业门户logo为空!' }],
						PORTAL_ICON_URL: [{ required: true, message: '浏览器页签logo为空!' }],
						PORTAL_TITLE:[{ required: true, message: '企业门户名称为空!' }],
						WELCOME_PAGE_URL:[{ required: true, message: '企业门户首页为空!' }],
						ISWATERMAEK:[{ required: true}],
						WATERMAEKBODY:[{ required: true}],
						ADDRESS_STATUS:[{ required: true}],
						SATISY_STATUS:[{ required: true}],
						SATISY_FLOW_NO:[{ required: true, message: '满意度流程号为空!' }],
						CCBAR_READYMODE:[{ required: true}],
						CCBAR_WORKMODE:[{ required: true}],
						CCBAR_SHOW_AUTO_ANSWER:[{ required: true}],
						CCBAR_SHOW_VIDEO_BTN:[{ required: true}],
						BEGIN_CALL_OPEN:[{ required: true}],
						INCOMING_CALL:[{ required: true}],
						CALL_OPEN:[{ required: true}],
						ORDERLISTISSHOW:[{ required: true}],
						SHOWHANGUP:[{ required: true}],
						SHOW_PHONE_DIR_BTN:[{ required: true}],
						IS_SHOW_MONITOR:[{ required: true}],
						ENABLE_PHARSE_PREFIX:[{ required: true, message: '客服输入引导查询常用语前缀为空!'}],
						PHARSE_SEARCH_CONTENT:[{ required: true}],
						REFRESH_FOLLOW_MINIUTES:[{ required: true, message: '首页刷新待跟进会话的频率为空!'}],
						ENT_MAX_IMPORT_SIZE:[{ required: true, message: '企业导入数量为空!'}],
						ENT_MAX_EXPORT_SIZE:[{ required: true, message: '企业导出数量为空!'}],
						ENT_OFFLINE_EXPORT_SIZE:[{ required: true, message:'离线导出数量为空!'}],
						ENT_OFFLINE_EXPORT_AUDIT:[{ required: true}],
						ENT_MAX_CONCURRENCE_EXPORT_SIZE:[{ required: true, message:'单机并发导出数为空!'}],
						SHOW_STATUS:[{ required: true}],
						JNZ_STATUS:[{ required: true}],
						TEMPLETE:[{ required: true}],
						TEMPLETE_ID:[{ required: true, message:'问卷为空!'}],
						DISTRIBUTION_BY_ROLE:[{ required: true}],
						AGENT_ASSISTANT:[{ required: true}],
						AGENT_KNOWLEDGE:[{ required: true}],
						COMMUNICATION:[{ required: true}],
						OPEN_VOICE_SMS_SATISFY:[{ required: true}],
						remoteSwitch:[{ required: true}],
						remoteServerType:[{ required: true}],
						remoteUseSSL:[{ required: true}],
						remoteMustUpgrade:[{ required: true}],
					},
					templeteList: {},
					roleRelative: {}
				}
			},
			watch: {},
			mounted() {
				let that = this;
				$('#file').change(function(val) {
					var eImg=getObjectURL($('#file')[0].files[0])
					that.logoUrl = eImg
				});
				$('#file1').change(function(val) {
					var eImg=getObjectURL($('#file1')[0].files[0])
					that.portalIconUrl = eImg
				});
			},
			created() {
				let data = {
					params: {
						pk: 'cc-base'
					},
					controls: ['param.edit', 'param.templeteList','userRelevanceDao.roleRelative']
				}
				yq.daoCall(data, null, {
					"contextPath": "/cc-base"
				}).then(res => {
					// this.form = res['param.edit'].data
					let dt = res['param.edit'].data
					if(dt){
						for(var key in dt){
							if(['SUPPORT_BUSI_TYPE'].includes(key) && dt[key] !== undefined){
								if(dt[key] == ''){
									this.$set(this.form,key,[])
								}else{
									this.$set(this.form,key,dt[key].split(','))
								}
							}else if(['DESENS_ROLES'].includes(key) && dt[key]){
									this.$set(this.form,key,dt[key].split(','))
								}else{
								this.$set(this.form,key,dt[key])
							}
						}
					}
					this.templeteList = res['param.templeteList'].data || []
					this.logoUrl = this.form.LOGO_URL?'/cc-base/servlet/attachment?action=download&filePath='+this.form.LOGO_URL:''
					this.portalIconUrl = this.form.PORTAL_ICON_URL?'/cc-base/servlet/attachment?action=download&filePath='+this.form.PORTAL_ICON_URL:''
					this.roleRelative = res['userRelevanceDao.roleRelative'].all || []
				})
			},
			methods: {
				goPage(tab){
					if(!this.activeNames.includes(tab)){
						this.activeNames.push(tab)
					}
					setTimeout(() => {
						scrollToCenter(tab);
					}, 200);
				},
				// logo文件上传-调用接口
				fileLoad(){
					let that = this
					let data = new FormData()
					data.append('lefile',$('#file')[0].files[0])
					data.append('lePortalUrl',$('#file1')[0].files[0])
					var busiType = 'ccLogo';
					var busiId = uuid();
					var flag = "flag";
					var listFlag= $("#listFlag").val();
					$.ajax({
						url : '/cc-base/servlet/attachment?action=upload2&busiId='+busiId+'&busiType='+busiType+'&listFlag='+listFlag+'&flag='+flag,
						type: 'POST',
						data: data,
						async : false,
						cache : false,
						contentType : false,
						processData : false,
						success: function(result) {
							if (result.errno == 0) {
								var fileUrl = result.data.lefile;
								var portalUrl = result.data.lePortalUrl;
								if(fileUrl!=""&&typeof(fileUrl)!= "undefined"){
									that.form.LOGO_URL = fileUrl;
								}
								if(portalUrl!=""&&typeof(portalUrl)!= "undefined"){
									that.form.PORTAL_ICON_URL = portalUrl;
								}
								let form = that.addPrefixToJSONKeys(that.form,'entParam.')
								that.sumbitForm(form)
							} else {
								that.$message.error(res.msg)
							}
						}
					})
				},
				// 删除logo文件
				delImg(type){
					switch(type){
						case 'logo':{
							this.logoUrl = '';
							this.form.LOGO_URL = '';
							break;
						}
						case 'portal':{
							this.portalIconUrl = '';
							this.form.PORTAL_ICON_URL = '';
							break;
						}
					}
				},
				// logo文件--上传
				uploadFile(type){
					switch(type){
						case 'logo':{
							document.getElementById('file').click()
							break;
						}case 'portal':{
							document.getElementById('file1').click()
							break;
						}
					}
				},
				// 主控端/被控端--上传
				importData(field) { 
					uploadSetting = {
							storage: "local",
							fileType: ".exe",
							createThumb: true,
							busiId:uuid(),
							busiType: "remoteFile",
							callback: function(data) {
									console.log(data);
							}
					};
					yq.layerShow({type:1,title:getI18nValue('文件上传'),offset:'20px',area:['420px','200px'],shadeClose:false},
						"/cc-base/pages/entmgr/importData.jsp",{
						field:field
					})
				},
				// 主控端/被控端--下载
				download(id){
					var url=this.form[id]
					window.location.href=url;
				},
				// URL按钮功能
				fullUrl(type) {
					switch (type) {
						case 'sumbitUrl': { this.form.SUBMIT_ORDER_URL = '/cc-eorder/pages/eorder/handle/send-order.jsp?type=save'; break; }
						case 'viewOrderUrl': { this.form.VIEW_ORDER_URL = '/cc-eorder/pages/eorder/search/order-index.jsp?type=view'; break; }
						case 'H5OrderUrl': { this.form.H5_SUBMIT_ORDER_URL = '/cc-eorder/pages/eorder/handle/send-order.jsp?type=save'; break; }
						case 'H5ViewOrderUrl': { this.form.H5_VIEW_ORDER_URL = '/cc-eorder/pages/eorder/search/order-index.jsp?type=view'; break; }
					}
				},
				// 侧边栏--打开侧边
				openCbl() {
					let url = '/cc-base/pages/agentconfig/channel/sidebar-list-layui.jsp';
					top.popup.openTab(url, getI18nValue('侧边栏'), {});
				},
				// 问卷--问卷管理
				QuestionManager() {
					let url = '/cc-qs/pages/qs/template/template-list.jsp'
					top.popup.openTab(url, getI18nValue('问卷管理'), {});
				},
				// 振铃弹屏配置--配置弹屏详情
				openCallRingingCfg() {
					let url = '/cc-base/pages/system/callin/call-ringing-config.jsp';
					top.popup.openTab(url, getI18nValue('振铃弹屏配置'), {});
				},
				// 来电弹屏类型--配置弹屏详情
				openCallinCfg() {
					let url = '/cc-base/pages/system/callin/sidebar/menu-list-layui.jsp';
					if (this.form.CALL_OPEN == '0') {
						url = '/cc-base/pages/callConfig/list.jsp';
					}
					top.popup.openTab(url, getI18nValue('弹屏配置'), {});
				},
				// 企业门户首页--选择框change事件
				setPageUrl(val) {
					switch (val) {
						case '1': {
							this.form.WELCOME_PAGE_URL = '/cc-workbench/pages/portal.jsp'
							break;
						}
						case '2': {
							this.form.WELCOME_PAGE_URL = '/cc-workbench/pages/newHomePage/homepage.html'
							break;
						}
						case '3': {
							this.form.WELCOME_PAGE_URL = '/cc-workbench/pages/HomePage/index.html'
							break;
						}
						case '4': {
							this.form.WELCOME_PAGE_URL = '/cc-workbench/pages/dashboard/newPage/index.html'
							break;
						}
					}
				},
				// 保存事件 -- 全局保存
				save() {
					let form = this.addPrefixToJSONKeys(this.form,'entParam.')
					if(form['entParam.SUPPORT_BUSI_TYPE'] && form['entParam.SUPPORT_BUSI_TYPE'].length > 0){
						form['entParam.SUPPORT_BUSI_TYPE'] = form['entParam.SUPPORT_BUSI_TYPE'].join(',')
					}else{
						form['entParam.SUPPORT_BUSI_TYPE'] = ''
					}
					if(form['entParam.DESENS_ROLES'] && form['entParam.DESENS_ROLES'].length > 0){
						form['entParam.DESENS_ROLES'] = form['entParam.DESENS_ROLES'].join(',')
					}else{
						form['entParam.DESENS_ROLES'] = ''
					}
					if($('#file').val() || $('#file1').val() ){
						// 先保存文件
						this.fileLoad()
					}else{
						// 不用保存文件
						this.sumbitForm(form)
					}
				},
				// 展开所有/收起所有
				openAllcollapse() {
					if (this.activeNames.length == 0) {
						this.activeNames = ['portal', 'callBar', 'media', 'attout', 'other', 'assistance']
					} else {
						this.activeNames = []
					}
				},
				// 给data中的key统一加前缀
				addPrefixToJSONKeys(data, prefix) {
					if(!data) return {}
					return Object.keys(data).reduce((acc, key) => {
						acc[prefix + key] = data[key];
						return acc;
					}, {});
				},
				// 保存-调用保存接口
				sumbitForm(form){
					yq.remoteCall('/cc-base/servlet/param?action=saveAll',form).then(res=>{
						if(res.state==1){
							this.$message.success(res.msg)
							setTimeout(() => {
								window.location.reload()
							}, 1000);
						}else{
							this.$message.error(res.msg)
						}
					})
				}
			},
		})
	</script>
</body>

</html>