<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title><span i18n-content="系统配置" ></span></title>
	<style>
		.layui-form-required:before {
    		content: "*";
    		display: inline-block;
    		font-family: SimSun;
    		margin-right: 4px;
    		font-size: 14px;
    		line-height: 1;
    		color: #ed4014;
		}		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<form style="" id="paramAllForm" data-pk="" method="post"
			autocomplete="off" data-mars-prefix="param." data-mars="param.edit">
	<div style="background: #fff;padding: 10px;">
		<!-- <form style="width: 50%" id="paramForm" data-pk="" method="post"
			autocomplete="off" data-mars-prefix="param." data-mars="param.edit"> -->
  			<fieldset class="layui-elem-field layui-form" >
  				<legend>
  				<span i18n-content="短信审核"></span>
  				</legend>
  				<div class="layui-row layui-col-space15">
  					<div class="layui-col-md12" style="margin-bottom: 5px;">
      					<div class="layui-card">
      						<div class="layui-col-md12 layui-col-sm12 "  data-mars="param.statusSms">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="发送单条短信是否审核："></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8" style="line-height: 50px;">
	        						<input userid="row.USER" type="radio" name="STATUS_SMS"
										value="1"  i18n-title="是">
									<input userid="row.USER" type="radio" checked="" name="STATUS_SMS"
								   		value="0"  i18n-title="否">
	    						</div>
      						</div>
      						<div class="layui-col-md12 layui-col-sm12 "  data-mars="param.statussmsBatch">
      							<div class="layui-col-md1 layui-col-sm1" style="width: 140px;">
	        						<label class="layui-form-label layui-form-required" style="width: 140px;" i18n-content="批量发送短信是否审核："></label>
				        		</div>
	        					<div class="layui-col-md8 layui-col-sm8" style="line-height: 50px;">
	        						<input userid="row.USER" type="radio" name="STATUSSMS_BATCH"
										value="1"  i18n-title="是">
									<input userid="row.USER" type="radio" checked="" name="STATUSSMS_BATCH"
								   		value="0"  i18n-title="否">
	    						</div>
      						</div>
      					</div>
      				</div>
  				</div>
  			</fieldset>
	</div>
</form>
	<div class="layui-col-md12 layui-col-sm12" style="padding: 5px;position: fixed;bottom: 0;left: 0px;background: #fff;text-align: center;">
		<button class="layui-btn" type="submit" lay-submit="" lay-filter="submitAll" i18n-content="保存"></button>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		requreLib.setplugs('wdate');	
		jQuery.namespace("param");
		var layform;
		var element;
		function renderLayui(){
			if(layform){
				layform.render();
			}
			if(element){
				element.render();
			}
		}
		$(function(){
			layui.use(['form', 'element'], function(){
				layform = layui.form;
				element = layui.element;
				//监听提交
				layform.on('submit(submitAll)', function(data){
					param.ajaxSubmitAllForm();
				    return false;
				});
				$("#paramAllForm").render({success : function(result){
					if(result["param.statusSms"]!=undefined){
						$("input[name='STATUS_SMS'][value='"+result["param.statusSms"].data.STATUS+"']").attr("checked",true);
					}
					if(result["param.statussmsBatch"]!=undefined){
						$("input[name='STATUSSMS_BATCH'][value='"+result["param.statussmsBatch"].data.STATUS+"']").attr("checked",true);
					}
					renderLayui();
				}});
			});
		});
		
		//保存按钮事件
		 param.ajaxSubmitAllForm = function(){
			    var data="";
			    var type="";
			    if(!form.validate("#paramAllForm")){
					return;
				};
				type="${ctxPath}/servlet/param?action=saveAll";
				data = form.getJSONObject("paramAllForm");
				ajax.remoteCall(type,data,function(result) {
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1},function(){
							window.location.reload(); 
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
		 }
		
		//保存按钮事件
		 param.ajaxSubmitForm = function(param){
			    var data="";
			    var type="";
			    if(param=="0"){
			    	if(!form.validate("#paramForm")){
						return;
					};
					type="${ctxPath}/servlet/param?action=save";
					data = form.getJSONObject("paramForm");
				}else if(param=="1"){
					if(!form.validate("#paramForm1")){
						return;
					};
					type="${ctxPath}/servlet/param?action=save1";
					data = form.getJSONObject("paramForm1");
				}
				ajax.remoteCall(type,data,function(result) {
						if(result.state == 1){
							layer.msg(result.msg,{icon: 1},function(){
								window.location.reload(); 
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				);
		 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>