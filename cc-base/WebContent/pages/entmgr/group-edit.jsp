<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>部门</title>
	<style>
		input:-webkit-autofill {  
	    -webkit-box-shadow: 0 0 0px 1000px white inset;  
		}  
		.select2-selection__rendered{text-align: left;}
		.select2-container--bootstrap{width: inherit!important;z-index: 100000000}
		.select2-container--bootstrap .select2-selection{font-size: 13px;}
		.select2-selection{background-color: #fff!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="deptMgr.deptInfo"  method="post" data-pk="${param.skillGroupId}" data-mars-prefix="skillGroup.">
				  <input type="hidden" name="skillGroup.SKILL_GROUP_ID" value="${param.skillGroupId}">
				  <input type="hidden" name="skillGroup.P_GROUP_ID" value="${param.pGroupId }">
				  <input type="hidden" name="pGroupType" value="${param.groupType }">
				  <table class="table table-edit table-vzebra">
	                    <tbody>

		                     <tr>
			                        <td class="required" width="100px" i18n-content="名称"></td>
			                        <td><input type="text" name="skillGroup.SKILL_GROUP_NAME" autocomplete="off" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     <c:if test="${empty param.skillGroupId}">
		                  <!--    <tr>
			                        <td class="required">部门类型</td>
			                        <td> -->
		                        		 <c:choose>
		                        		 <c:when test="${'0'==param.group}">
									         <select style="display:none"  name="skillGroup.GROUP_TYPE" id="GROUP_TYPE_ID" data-value="01"  readonly onchange="groupTypeId(this.value)" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
				                        		<option value="" i18n-content="请选择"></option>
				                        	</select>
									       </c:when>
		                        		 <c:when test="${'2'==param.group}">
									         <select  style="display:none" name="skillGroup.GROUP_TYPE" id="GROUP_TYPE_ID"   data-value="0101"  readonly onchange="groupTypeId(this.value)" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
				                        		<option value="" i18n-content="请选择"></option>
				                        	</select>
									       </c:when>
		                        		  <c:when test="${'4'==param.group}">
									         <select  style="display:none" name="skillGroup.GROUP_TYPE" id="GROUP_TYPE_ID" data-value="010101" readonly onchange="groupTypeId(this.value)" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
			                        			<option value="" i18n-content="请选择"></option>
			                        		 </select>
									       </c:when>
									       <c:otherwise>
									       <tr>
			                        		<td class="required" i18n-content="部门类型"></td>
									           <td>
										          <select name="skillGroup.GROUP_TYPE" id="GROUP_TYPE_ID" onchange="groupTypeId(this.value)" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
				                        		 </select>
							                   </td>
						                     </tr>
						                     <tr>
						                        <td width="100px" >
						                        	<h5> 
				                                       <span i18n-content="区域编号"></span>
				                                       <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;" 
				                                       tips-content="说明:<br>1、该字段用于一些要做特殊分权分域处理的项目使用，非必填。
				                                       "></i>
				                                   </h5>
						                        </td>
						                        <td><input type="text" name="skillGroup.REGION_CODE" autocomplete="off" class="form-control input-sm"></td>
						                     </tr>
									       </c:otherwise>
		                        		 </c:choose>
			                 <!--        </td>
		                     </tr> -->
		                     <c:if test="${'struct'!=param.skllgroupType}">
			                 	<td i18n-content="技能组类型"></td>
		                        <td>
		                        	<select name="skillGroup.SKILL_GROUP_TYPE" data-pk="${param.group }" id="SKILL_TYPE_ID"  data-mars-top="true" class="form-control input-sm">
		                        		 <c:if test="${'0'==param.group}">
		                        		 <option value="leader" i18n-content="总监"></option>
		                        		 </c:if>
		                        		  <c:if test="${'2'==param.group}">
		                        		 <option value="team" i18n-content="团队"></option>
		                        		 </c:if>
		                        	</select>
		                        </td>
		                     </c:if>
		                     </c:if>
		                     <c:if test="${'struct'==param.skllgroupType}">
			                  <input type="hidden" name="skillGroup.SKILL_GROUP_TYPE" autocomplete="off" class="form-control input-sm" value="struct">
		                     </c:if>
		                     
		                     
		                     <c:if test="${!empty param.skillGroupId}">
		                     <tr>
			                        <td class="required" i18n-content="部门类型"></td>
			                        <td>
			                         	<select name="skillGroup.GROUP_TYPE" id="GROUP_TYPE_ID" onchange="groupTypeId(this.value)" data-mars="GroupTypeDao.groupTypeDict" data-mars-top="true" class="form-control input-sm">
				                        		 </select>
				                        <!-- 
			                        	<input type="text" name="skillGroup.GROUP_TYPE_NAME" readonly="readonly" class="form-control input-sm">
			                        	 -->
			                        </td>
		                     </tr>
		                     <tr>
			                        <td width="100px" >
			                        	<h5> 
	                                       <span i18n-content="区域编号"></span>
	                                       <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;" 
	                                       tips-content="说明:<br>1、该字段用于一些要做特殊分权分域处理的项目使用，非必填。
	                                       "></i>
	                                   </h5>
			                        </td>
			                        <td><input type="text" name="skillGroup.REGION_CODE" autocomplete="off" class="form-control input-sm"></td>
		                     </tr>
		                     </c:if>
		                      <c:if test="${('4'==param.group&&'0'==param.add) || ('6'==param.group&&'1'==param.update)}">
		                      	<c:if test="${'01' ==param.groupType}">
				                     <tr class="media">
					                        <td i18n-content="外显号码"></td>
					                        <td>
					                            <select class="form-control input-sm" id="prefixSelect2" name="skillGroup.PREFIX_NUM" data-mars="common.prefixNumDic">
						                        	<option value="" i18n-content="请选择"></option>	
						                        </select>
						                    </td>
				                     </tr>
				                     <tr class="media">
					                        <td i18n-content="外显号码组"></td>
					                        <td>
					                            <select name="skillGroup.PREFIX_GROUP_ID" class="form-control input-sm" data-mars="common.prefixGroupDic">
					                                <option value="" i18n-content="请选择"></option>
					                            </select>
					                        </td>
				                     </tr>
				                     
				                     <tr class="media">
					                        <td style="width: 119px;" i18n-content="话后处理完成模式"></td>
					                        <td>
					                             <select class="form-control input-sm" name="skillGroup.WORK_TIME">
					                       		 		<option value="0" i18n-content="手工完成"></option>
					                       		 		<option value="5" i18n-content="5秒后自动完成"></option>
					                       		 		<option value="10" i18n-content="10秒后自动完成"></option>
					                       		 		<option value="15" i18n-content="15秒后自动完成"></option>
					                       		 		<option value="30" i18n-content="30秒后自动完成"></option>
					                       		 		<option value="60" i18n-content="1分钟后自动完成"></option>
					                       		 		<option value="120" i18n-content="2分钟后自动完成"></option>
					                       		 		<option value="300" i18n-content="5分钟后自动完成"></option>
					                      		 </select>
					                        </td>
				                     </tr>
				                     <tr class="media">
					                        <td nowrap="nowrap" i18n-content="自动进入话后处理"></td>
					                        <td>
						                        <!-- <label class="radio radio-info radio-inline">
						                        	<input type="radio" value="1" checked="checked" name="skillGroup.WORK_NOT_READY"><span>是</span>
						                        </label>
						                        <label class="radio radio-info radio-inline">
						                        	<input type="radio" value="0" name="skillGroup.WORK_NOT_READY"><span>否</span>
						                        </label> -->
						                        <select name="skillGroup.WORK_NOT_READY" class="form-control input-sm" data-mars="common.getDict(CC_BASE_WORK_NOT_READY)">
					                            </select>
					                        </td>
				                     </tr>
		 							  <tr class="media">
					                        <td nowrap="nowrap" i18n-content="技能组排队策略"></td>
					                        <td>
						                        <label class="radio radio-info radio-inline">
						                        	<input type="radio" value="1" checked="checked" name="skillGroup.QUEUE_STRATEGY"><span i18n-content="按最长等待时间"></span>
						                        </label>
						                        <label class="radio radio-info radio-inline">
						                        	<input type="radio" value="2" name="skillGroup.QUEUE_STRATEGY"><span i18n-content="按坐席优先级别"></span>
						                        </label>
					                        </td>
				                     </tr>
				                      <tr class="media">
					                        <td i18n-content="溢出技能组"></td>
					                        <td>
					                            <select name="skillGroup.SPILL_GROUP" class="form-control input-sm" data-mars="skillGroup.spillGroup(${param.skillGroupId})">
					                                <option value="" i18n-content="请选择"></option>
					                            </select>
					                        </td>
				                     </tr>
				                 </c:if>
                       		 </c:if>
                       		 <c:if test="${empty param.skillGroupId && 'struct'!=param.skllgroupType}">
	                       		 <tr>
			                        <td i18n-content="管理方式"></td>
			                        <td>
				                        <label class="radio radio-info radio-inline">
				                        	<input type="radio" value="1" checked="checked" name="skillGroup.MGR_TYPE"><span i18n-content="按人"></span>
				                        </label>
				                        <label class="radio radio-info radio-inline">
				                        	<input type="radio" value="2" name="skillGroup.MGR_TYPE"><span i18n-content="按工作组"></span>
				                        </label>
			                        </td>
			                     </tr>
			                 </c:if>
			                 
			                 <tr>
			                        <td width="100px" >
			                        	<h5> 
	                                       <span i18n-content="省份"></span>
	                                       <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"  tips-content="说明:<br>1、按需设置部门与省份的关联关系，可以为空。
	                                       "></i>
	                                   </h5>
			                        </td>
			                        <td>
			                        	<select id="province" name="skillGroup.PROVINCE_CODE" class="form-control input-sm" data-mars="area.provinceChooseSel" onchange='cascadeSelectProvince(this)'>
										<option value="" i18n-content="请选择"></option>
									</select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td width="100px" >
			                        	<h5> 
	                                       <span i18n-content="地市"></span>
	                                       <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"  tips-content="说明:<br>1、按需设置部门与地市的关联关系，可以为空。
	                                       "></i>
	                                   </h5>
			                        </td>
			                        <td>
			                        	<select id="city" name="skillGroup.CITY_CODE" pid = "province" class="form-control input-sm"  >
										<option value="" i18n-content="请选择"></option>
									</select>
			                        </td>
		                     </tr>
		                     
		                     <tr>
			                        <td class="required" i18n-content="优先排序"></td>
			                        <td><input type="number" name="skillGroup.IDX_ORDER"  data-rules="required|digits" class="form-control input-sm" value="99"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="SkillGroupEdit.ajaxSubmitForm()" i18n-content="保存"></button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("SkillGroupEdit");
	
	SkillGroupEdit.skillGroupId='${param.skillGroupId}';
	$(function(){
		$("#easyform").render({success:function(result){
			requreLib.setplugs('select2',function(){
				 $("#prefixSelect2").select2({theme: "bootstrap",openOnEnter:false});
			});
			//设置默认自动进入话后处理为1（呼入呼出进入）
			var flag = '${param.skillGroupId}';
			if(flag==''){
				$("select[name='skillGroup.WORK_NOT_READY']").find("option[value='1']").attr("selected",true);
			}
			if(result["deptMgr.deptInfo"]){
				var deptInfo = result["deptMgr.deptInfo"];
				var skillGroupType = deptInfo.data.SKILL_GROUP_TYPE;
				if(skillGroupType=="media"){
				   $(".media").hide();
			    }else{
				   $(".media").show();
			    }
				
				var provinceCode = deptInfo.data.PROVINCE_CODE;
				var cityCode = deptInfo.data.CITY_CODE;
				$('#province').attr("city",cityCode);
				$('#province').trigger('change');
			}
			
		}});
		groupTypeId('${param.group}');
	});
	
	function groupTypeId(value){
		if("010101"==value||4==value){
    		 $("#SKILL_TYPE_ID").html("<option value='voice'>语音</option> <option value='media'>全媒体</option>"); 
		}
	}
	
	function cascadeSelectProvince(e){
		debugger;
		var pid = $(e).attr("id");
		var city = $(e).attr("city");
		var selectProvinceCode = $(e).val();
		$.each($("select[pid=" + pid + "]"), function(index, sel){
			$(sel).data("mars", "area.cityChooseSel('" + selectProvinceCode + "')").render(
					{success:function(result){
						
						$(sel).val(city);
					}}
						
			);
			$(sel).data("");
			
			// $(sel).trigger('change', [sel]);
		});
		$(e).attr("city","");
	}
	function cascadeSelectCity(e){
		var pid = $(e).attr("id");
		var selectCId = $(e).val();
		$.each($("select[pid=" + pid + "]"), function(index, sel){
			$(sel).data("mars", "area.provinceChooseSel('" + selectCId + "')").render();
			$(sel).val("");
			$(sel).data("");
			$(sel).trigger('change', [sel]);
		});
	}
	
	
	SkillGroupEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 var group="${param.group}";
			 var update="${param.update}";
			 if(('4'==group&&'0'==update) || ('6'==group&&'1'==update)){
				 var groupType = $("select[name='skillGroup.GROUP_TYPE']").find("option:selected").text();
				 if(groupType.indexOf('(话务权限)')>0){
					 var groupId = $("select[name='skillGroup.PREFIX_GROUP_ID'] option:selected").val();
					 var num = $("select[name='skillGroup.PREFIX_NUM'] option:selected").val();
					 if(groupId == num){
						 layer.msg("来显号码或外呼号码组不能为空!",{icon: 5,offset:'20px'});
						 return;
					 }
				 }
			 }
			/*  var group=$("#GROUP_TYPE_ID").val();
			 var skill=$("#SKILL_TYPE_ID").val();
			 if("010101"==group&&""==skill){
				 layer.alert("技能组不能为空，请选择");
			 } */
			 if(SkillGroupEdit.skillGroupId==''){
				 SkillGroupEdit.insertData(); 
			 }else{
				 SkillGroupEdit.updateData(); 
			 }
		 }else{
			 i18nTooltip();
			 return;
		 };
	}
	SkillGroupEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=add",data,function(result) { 
				if(result.state == 1){
					parent.layer.msg(result.msg,{icon: 1,time:1200},function(){
						parent.SkillGroup.loadData();
						popup.layerClose("#easyform");
					});
				}else{
					parent.layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
	SkillGroupEdit.updateData = function(){
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/skillGroup?action=update",data,function(result) { 
			if(result.state == 1){
				parent.layer.msg(result.msg,{icon: 1,time:1200},function(){
					parent.SkillGroup.loadData();
					popup.layerClose("#easyform");
				});
			}else{
			      parent.layer.alert(result.msg);
			}
		  }
		);
	}
	  $(document).ready(function() {
		  $("#SKILL_TYPE_ID").change(function(){
			  var media=$('#SKILL_TYPE_ID option:selected').val();
			  if (media == 'media') {
				 $(".media").hide();
			  }else{
				 $(".media").show();
			  }
		  });
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>