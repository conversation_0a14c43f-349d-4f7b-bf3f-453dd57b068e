<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="角色编辑"></title>
	<link rel="stylesheet" type="text/css" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css">
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" data-mars="role.record" data-pk="${param.roleId}"  method="post"  autocomplete="off" data-mars-prefix="role.">
		<input type="hidden" name="role.ROLE_ID" class="form-control input-sm" value="${param.roleId}">
	  	<table class="table table-edit table-vzebra mt-10">
        	<tbody>
            	<tr>
                	<td class="required" width="30px" i18n-content="数据权限配置"></td>
                    <td>
                    	<select class="form-control input-sm" id="dataAuthType" name="role.DATA_AUTH_TYPE" data-mars="common.getDict(DATA_AUTH_TYPE)">
                    		<option value = "" i18n-content="请选择"></option>
                    	</select>
                    </td>
              	</tr>
	        </tbody>
       	</table>
       	<div class="zTreeDemoBackground left" id="entBusiTree" style="padding-bottom:50px">
			<ul id="tree" class="ztree"></ul>
		</div>
		<div class="layer-foot text-c" style="position:fixed">
			<button class="btn btn-sm btn-primary"  type="button" onclick="RoleDataEdit.ajaxSubmitForm()" i18n-content="保存"></button>
		   	<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)" i18n-content="关闭"></button>
	   </div>
	</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("RoleDataEdit");
	
	RoleDataEdit.roleId='${param.roleId}';
	
	var setting = {
			view: {
				selectedMulti: false
			},
			check: {
				enable: true,
			}
	};
	
	var roleId = '${param.roleId}';
	var treeObj;
	
	$(function(){
		$("#easyform").render({success:function(result) {
			if(result['role.record'].data.DATA_AUTH_TYPE == '4') {
				$("#entBusiTree").show();
			} else {
				$("#entBusiTree").hide();
			}
		}});
		
		ajax.remoteCall("${ctxPath}/webcall?action=role.getDataAuthTree",{roleId:roleId,busiType:'1'},function(result) { 
			var data = result.data;
			$.fn.zTree.init($("#tree"), setting, data);
			treeObj = $.fn.zTree.getZTreeObj("tree");
		});
		
		$("#dataAuthType").change(function() {
			if($(this).val() == '4') {
				$("#entBusiTree").show();
			} else {
				$("#entBusiTree").hide();
			}
		});
		
		execI18n();
	});
	RoleDataEdit.ajaxSubmitForm = function(){
		var data = form.getJSONObject("#easyform");
		
		var dataAuthTree = treeObj.getCheckedNodes(true);
		data.dataAuthTree = [];
		for(var i in dataAuthTree) {
			// 排出半勾选状态数据
			if(dataAuthTree[i].check_Child_State == 1) {
				continue;
			}
			data.dataAuthTree.push(dataAuthTree[i]);
		}
		ajax.remoteCall("${ctxPath}/servlet/role?action=updateDataAuthType",data,function(result) { 
			if(result.state == 1){
				parent.layer.msg(result.msg,{icon: 1,time:1200,offset:'160px'},function(){
					parent.layer.closeAll();
					parent.CCRole.loadData();
			    });
			}else{
				parent.layer.alert(result.msg,{icon: 5});
			}
	  	});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>