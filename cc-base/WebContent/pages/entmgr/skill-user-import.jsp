<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title i18n-content="路由码导入"></title>
</EasyTag:override>
<EasyTag:override name="content">
       	<form action=""  id="importForm" method="post" name="uploadForm" class="form-inline" id="uploadForm" enctype="multipart/form-data">
		<table class="table  table-vzebra mt-10">
			<tbody>
				<tr>
					<td width="60px" i18n-content="Excel文件"></td>
					<td><input class="hidden" type="file" id="file" name=file accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-PerformWdateel">
						<button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()" i18n-content="选择文件"></button>
					</td>
				</tr>
			    <tr>
					<td i18n-content="文件名"></td>
					<td><input id="photoCover" class="form-control input-sm" style="width:240px" type="text" readonly="readonly"></td>
				</tr>
				<tr>
					<td width="60px"><span i18n-content="场景说明"></span></td>
					<td>
						 <div class="input-group input-group-sm">
	                  		1、<span i18n-content="运营管理人员，可以通过excel编辑技能组和用户的绑定关系，快速实现批量调整坐席技能组"></span><br>
	                      </div>         
					</td>
				</tr>
				<tr>
                     <td width="60px"><span i18n-content="说明"></span>：</td>
                     <td>
	                     <div class="input-group input-group-sm">
	                  		1、<span i18n-content="通过导出功能，先导出系统中已有的技能组和坐席的绑定关系的excel文件"></span><br>
	                     	2、<span i18n-content="对导出的excel文件进行调整，如需要删除绑定关系，将关联状态设置为'移除'"></span><br>
	                     	3、<span i18n-content="对于需要新增绑定关系，可以在excel里新增一行，录入技能组id、坐席账号、优先级、关联关系四列即可，其他列可以为空"></span><br>
	                     	4、<span i18n-content="导入后，系统会显示最终的处理结果文件，请仔细核对坐席技能存在调整的数据是否正确"></span><br>
	                     	5、<span i18n-content="为了避免数据缓存，建议要求技能发生变化的坐席重新签入"></span><br>
	                      </div>                 		                        	
                     </td>
                </tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="industryTermImport.upload()" i18n-content="保存"></button>
			<button class="btn btn-sm btn-default ml-20" type="button" id="backbut" onclick="layer.closeAll();" i18n-content="关闭"></button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("industryTermImport");
		
		function ajaxSubmitForm(){
			 if(form.validate("#easyformAdd")){
				 download(); 
			 };
		}
	    
	    function download(){
			location.href ="${ctxPath}/template/路由码.xlsx";
		}
		$('input[id=file]').change(function() {
			var fileName = $(this).val();
			var splitStr = fileName.split('\\');
			var splitStrs = splitStr[splitStr.length-1];
			$('#photoCover').val(splitStrs); 
		});
		
		industryTermImport.upload = function() {
			var photoCover=$("#photoCover").val();
			if(photoCover==""){
				layer.alert("请上传文件",{icon: 5});
				return;
			}
			$("#importForm").attr("enctype","multipart/form-data");
			var formData = new FormData($("#importForm")[0]); 
			$.ajax({  
	         	url: '${ctxPath}/servlet/skillGroup?action=importSkillUser',  
		        type: 'POST',  
		        data: formData,
		        async: true,cache: false,contentType: false,processData: false, success: function (result) {
        	  		if(result.state == 1){
						parent.layer.open({
				            type: 1
				            ,title: "结果"
				            ,offset: '20px'
				            ,id: 'systemCheckResult' //防止重复弹出
				            ,content: result.data
				            ,btn: '关闭'
				            ,btnAlign: 'c' //按钮居中
				            ,area: ['50%', '80%']
				            ,shade: 0 //不显示遮罩
				            ,yes: function(){
				                parent.layer.closeAll();
				                layer.closeAll();
				                phoneRouteList.query();
				            }
				        });
					}else{
						layer.alert(result.msg,{icon: 5,time: 1200});
					}
		      	}
	     	});
		}
		
		$(function() {
			execI18n();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>