<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="特殊工作时间段配置"> </title>
</EasyTag:override>
<EasyTag:override name="content">
    <h3>websocket测试</h3>
    <button type="button" onclick="agentChatFun.connect()" >连接</button>
    <button type="button" onclick="agentChatFun.connect()" >关闭</button>
    <textarea id="message" rows="5" cols="20" ></textarea> <button type="button" onclick="send()" >发送</button>
    <div>
    	<ul id="msgList">
    		
    	</ul>
    </div>
    <button type="button" onclick="agentChatFun.agentChatList()" >会话记录查询</button>
    <div id="agentChatList"></div>
    <button type="button" onclick="agentChatFun.chatContentList()" >会话消息查询</button>
    <div id="chatContentList"></div>
    <input type="text" id="userAcc" /><button type="button" onclick="agentChatFun.createChatRecord()" >新建会话</button>
    <input type="text" id="chatRecordId" /><button type="button" onclick="agentChatFun.clearChatRecord()" >关闭会话</button>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
	
	jQuery.namespace("agentChatFun");
	
	function send() {
		var message = $("#message").val();
		agentChatFun.send('sendChat',{'cmdJson':JSON.stringify({msgContent: message, msgType:"text", chatRecordId: '1234567891534897'})});
	}
	
	agentChatFun = {
			ws:null,
			ip:'',
			port:'',
			lastMsg:'',
			host:'',
			init:function(ip,port,callback){
				var tempHost = location.origin;
				var wshost = tempHost.replace('http','ws');
				agentChatFun.host = wshost+'/cc-base/agentChat';

				ccbarWs = setInterval(function(){
					agentChatFun.heartbeat();
				},10000);
			},
			connect:function(){
				var tempHost = location.origin;
				var wshost = tempHost.replace('http','ws');
				var host = wshost+'/cc-base/agentChat';
				this.ws = new WebSocket(host);
				this.ws.onmessage = agentChatFun.handlerMessage;
				this.ws.onopen = agentChatFun.onopen;
				this.ws.onclose = agentChatFun.onclose;
			},
			disconnect:function(){
				try{
					agentChatFun.ws.close();
				}catch(e){
					console.log('ws关闭失败,',e);
				}
			},
			handlerMessage:function(message){
				var messageData = JSON.parse(message.data);
				if(messageData.msgType == "agentChat") {
					$("#msgList").append('<li>' + messageData.chat.userAcc + ':' + messageData.chat.msgContent + '</li>');
				}
				log(messageData);
			},
			onopen:function(event){
				log('WebSocket opened');
			},
			onclose:function(event){
				log('WebSocket closed');
				tipsMsg('ccbar websocket closed');
			},
			onmessage:function(message){
				var data = JSON.parse(message.data);
				log(data);
			},
			heartbeat:function(){
				agentChatFun.send('heartbeat',{'cmdJson':JSON.stringify({msgType:"heartbeat"})});
			},
			send:function(event,data,callback){
				var _data = {
					event:event
				}
				_data = $.extend({}, _data, data);
				
				_data = typeof(_data) == 'string'?_data:JSON.stringify(_data)
				if (agentChatFun.ws.readyState === WebSocket.OPEN) {
					agentChatFun.ws.send(_data);
		        }
			},
			agentChatList: function() {
				var data = {pageNo:1, pageSize:10};
				ajax.remoteCall("${ctxPath}/servlet/agentChat?action=agentChatList",data,function(result) { 
					if(result.state == 1){
						console.log(result);
						$("#agentChatList").text(JSON.stringify(result.agentChatList));
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			chatContentList: function() {
				var data = {pageNo:1, pageSize:10,chatRecordId:'1234567891534897'};
				ajax.remoteCall("${ctxPath}/servlet/agentChat?action=chatContentList",data,function(result) { 
					if(result.state == 1){
						console.log(result);
						$("#chatContentList").text(JSON.stringify(result.chatContentList));
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			createChatRecord: function() {
				var data = {userAccs:$("#userAcc").val(), recordType: '1'};
				ajax.remoteCall("${ctxPath}/servlet/agentChat?action=createChatRecord",data,function(result) { 
					if(result.state == 1){
						console.log(result);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			clearChatRecord: function() {
				var data = {chatRecordIds:$("#chatRecordId").val()};
				ajax.remoteCall("${ctxPath}/servlet/agentChat?action=clearChatRecord",data,function(result) { 
					if(result.state == 1){
						console.log(result);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		};
 		
	function log(msg) {
		console.log(msg);
	}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>