<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title i18n-content="关键字配置"></title>
    <style type="text/css">
        div#rMenu {
            position: absolute;
            visibility: hidden;
            top: 0;
            text-align: left;
            padding: 2px;
        }
		.ztree{
			overflow: auto!important;
		}
        .dropdown-menu > li > a {
            font-size: 13px;
        }

        a:link {
            color: #00adff;
        }

        .gray-bg {
            background-color: #fff
        }
        .relaType {
        	display:none;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="JavaScript:word.loadData()" id="searchForm" name="searchForm" class="form-inline" onsubmit="">
        <input type="hidden" value="-1" name="id" id="id"> <input
            type="hidden" value="" name="text" id="text">
        <input type="hidden" id="code">
        <div class="row">
            <div
                    style="background-color: #fff; margin-left: 15px; width: 20%; float: left; " class="shadow">
                <div
                        style="border-bottom: 1px solid #eee; height: 52px; line-height: 52px; padding: 0px 15px"><span
                        i18n-content="关键字目录"></span>
                    <span class="f-12"><span class="label label-info" i18n-content="右键菜单操作"> </span></span>
                    <button style="float: right;margin-top: 11px;" type="button" onclick="word.keywordDir('-1')" class="btn btn-sm btn-success btn-outline " i18n-content="新增"></button>
                </div>
                <div class="ztree" data-mars="keyWord.getZtree"
                     id="ztree"
                     style="overflow: auto; padding: 15px;"></div>
            </div>
            <div style="height: 450px; width: 76%; float: left; margin-left: 15px;">
                <div class="ibox shadow">
                    <div class="ibox-title clearfix">
                        <div class="form-group">
                            <h5> 
                                <span i18n-content="关键字"></span>
                                <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;" 
                                tips-content="说明:<br>1、用于在线客服收到客户的全媒体回复内容时，对内容进行关键字标记，结合坐席辅助，自动通过关键字搜索。
                                <br>2、关键字所对应的目录，必须关联渠道，关联技能组（可选）
                                <br>3、由于使用时存在缓存，关键字变动后，需要5分钟后生效。
                                "></i>
                            </h5>
                            
                            <div class="input-group input-group-sm pull-right">
                                <button class="btn btn-sm btn-success btn-outline" type="button"
                                        onclick="word.addWord()" i18n-content="新增"></button>
                            </div>
                        </div>
                        <hr style="margin: 3px -15px">
                        <div class="form-group" id="divId">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon" i18n-content="内容"></span> <input type="text"
                                                                                                 name="content"
                                                                                                 class="form-control input-sm"
                                                                                                 style="width: 160px">
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon" i18n-content="是否公开"></span> <select
                                    data-mars="common.getDict(SF_YN)" data-rules="required"
                                    class="form-control input-sm" name="is_public">
                                <option value="" i18n-content="请选择"></option>
                            </select>
                            </div>
                            <div class="input-group input-group-sm">
                                <button class="btn btn-sm btn-default" type="button" onclick="searchResData('1')"><span
                                        class="glyphicon glyphicon-search"></span> <span i18n-content="查询"></span>
                                </button>
                            </div>
                            <div class="input-group">
                                <button type="button" class="btn btn-sm btn-default" onclick="word.reset()"><span
                                        class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="main"></table>
                    </div>
                </div>
            </div>
        </div>

    </form>
    <div id="rMenu">
        <ul class="dropdown-menu" role="menu">
            <li><a id="agentconfig_keywords_new" href="javascript:void(0)" onclick="word.keywordDir();"
                   i18n-content="新增"></a></li>
            <li><a id="agentconfig_keywords_update" href="javascript:void(0)" onclick="word.saveCatalog();"
                   i18n-content="修改"></a></li>
            <li class="relaType">
      			<a href="javascript:void(0)" onclick="word.showSlider('01');" i18n-content="关联渠道"></a>
            </li>
            <li class="relaType">
                <a href="javascript:void(0)" onclick="word.showSlider('02');" i18n-content="关联技能组"></a>
            </li>
            <li><a id="agentconfig_keywords_del" href="javascript:void(0)" onclick="word.delDir();"
                   i18n-content="删除"></a></li>
        </ul>
    </div>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        requreLib.setplugs("select2");

        jQuery.namespace("word");
        var zTree, rMenu;
        var selectedNode;

        $(function () {
            $('#searchForm').render({
                success: function () {
                    requreLib.setplugs('slimscroll,ztree', function () {
                        $('#ztree').slimScroll({
                            height: '700px',
                            color: '#ddd'
                        });
                        var setting = {
                           	callback: {onClick: zTreeOnClick,onRightClick: OnRightClick},
                           	expandAll: true
                        }
                        commonTree.initTree(setting);
                        var ztreeObject = $.fn.zTree.getZTreeObj("ztree")
                        if (ztreeObject != null) ztreeObject.expandAll(true);
                    });
                }
            });
            requreLib.setplugs('layui', function () {
                word.loadData();
            });
        });


        function OnRightClick(event, treeId, treeNode) {
        	$("#code").val(treeNode.CODE);
        	if(treeNode.id == "-1"){// 根目录不操作右键菜单
        		return;
        	}else if(treeNode.PARENT_ID != "-1"){// 非一级目录隐藏关联选项
        		$(".relaType").hide();
        	}else{
        		$(".relaType").show();
        	}
            zTree = $.fn.zTree.getZTreeObj("ztree");
            rMenu = $("#rMenu");
            zTree.selectNode(treeNode);
            showRMenu("node", event.clientX, event.clientY);
            var nodes = zTree.getSelectedNodes();

            $("#id").val(nodes[0].id);
            $("#text").val(nodes[0].name);
            selectedNode = nodes[0];
        }

        function showRMenu(type, x, y) {
            $("#rMenu ul").show();
            if (type == "root") {
                $("#m_del").hide();
                $("#m_check").hide();
                $("#m_unCheck").hide();
            } else {
                $("#m_del").show();
                $("#m_check").show();
                $("#m_unCheck").show();
            }
            rMenu.css({
                "top": y + "px",
                "left": x + "px",
                "visibility": "visible"
            });
            $("body").bind("mousedown", onBodyMouseDown);
        }

        function hideRMenu() {
            if (rMenu)
                rMenu.css({
                    "visibility": "hidden"
                });
            $("body").unbind("mousedown", onBodyMouseDown);
        }

        function onBodyMouseDown(event) {
            if (!(event.target.id == "rMenu" || $(event.target).parents(
                "#rMenu").length > 0)) {
                rMenu.css({
                    "visibility": "hidden"
                });
            }
        }

        function zTreeOnClick(event, treeId, treeNode) {
            $("#id").val(treeNode.id);
            $("#text").val(treeNode.name);
            $("#delRes").show();
            searchResData();
        }

        function searchResData(flag) {
            if (flag == '1') {
                $("#searchForm").queryData({id: 'main', page: {curr: 1}});
            } else {
                $("#searchForm").queryData({id: 'main'});
            }
        }

        word.keywordDir = function (pid) {
            hideRMenu();
            var id = pid?pid:$("#id").val();
            popup.layerShow({
                type: 1,
                title: getI18nValue('新增'),
                offset: '20px',
                area: ['520px', '580px']
            }, "${ctxPath}/servlet/keyword?action=keywordDir", {
                PID: id,
                ID: ""
            });//新增传父id 不传当前id
        }
        word.saveCatalog = function () {
            hideRMenu();
            var id = $("#id").val();
            if (id == -1) {
                layer.msg(getI18nValue('请选择分类！'));
                return;
            }
            popup.layerShow({
                type: 1,
                title: getI18nValue('编辑'),
                offset: '20px',
                area: ['520px', '580px']
            }, "${ctxPath}/servlet/keyword?action=keywordDir", {
                PID: "",
                ID: id
            });
        }
        word.addWord = function () {
            var DIR_ID = $("#id").val();//目录id
            var text = $("#text").val();//目录id

            if (DIR_ID == "" || DIR_ID == -1) {
                alert(getI18nValue("请选择分类"));
                return;
            }
            popup.layerShow({
                type: 1,
                title: getI18nValue('新增'),
                offset: '20px',
                area: ['460px', '460px']
            }, "${ctxPath}/pages/agentconfig/config/keyword-edit.jsp", {
                ID: "",
                DIR_ID: DIR_ID,
                text: text
            });
        }
        word.editWord = function (ID) {
            var DIR_ID = $("#id").val();//目录id
            var text = $("#text").val();//目录id
            popup.layerShow({
                type: 1,
                title: getI18nValue('编辑'),
                offset: '20px',
                area: ['460px', '460px']
            }, "${ctxPath}/pages/agentconfig/config/keyword-edit.jsp", {
                ID: ID,
                DIR_ID: DIR_ID,
                text: text
            });
        }
        word.load = function () {
            $('#searchForm').render({
                success: function () {
                	 var setting = {
                     	callback: {onClick: zTreeOnClick,onRightClick: OnRightClick},
                        expandAll: true
                     }
                     commonTree.initTree(setting);
                }
            });
            requreLib.setplugs('slimscroll,ztree', function () {
                $('#ztree').slimScroll({});
            });
            searchResData();
        }

        //重置
        word.reset = function () {
            $("#divId select").val("");
            $("#divId input").val("");
        };

        word.loadData = function () {
            $("#searchForm").initTableEx({
                mars: 'keyWord.getKeywordList',
                id: 'main',
                height: 'full-120',
                cols: [
                    [
                         {width: 80, field: 'SORT_NUM', title: getI18nValue('序号'), sort: true}
                        , {field: 'CONTENT', title: getI18nValue('内容')}
                        , {width: 100, field: 'ENABLE_STATUS', title: getI18nValue('启用状态'), templet: function (row) {
                            var enableStatus = row['ENABLE_STATUS'];
                            var val = getDictTextByCode("ENABLE_STATUS", enableStatus);
                            if ("01" == enableStatus) {
                                return "<span class='layui-btn layui-btn-xs label-success '>" + val + "</span>"
                            } else {
                                return "<span class='layui-btn layui-btn-xs layui-btn-danger '>" + val + "</span>"
                            }
                        }
                    }
                        , {
                        width: 100, field: 'IS_PUBLIC', title: getI18nValue('是否公开'), templet: function (row) {
                            return getDictTextByCode('SF_YN', row.IS_PUBLIC);
                        }
                    }
                        , {
                        width: 100, field: 'TYPE', title: getI18nValue('操作'), templet: function (row) {
                            var temp = '<a  href="javascript:void(0)"  onclick="word.editWord(\'' + row.ID + '\')">' + getI18nValue("修改") + '</a> ' +
                                '<a  href="javascript:void(0)"  onclick="word.del(\'' + row.ID + '\')">' + getI18nValue("删除") + '</a> ';
                            return temp;
                        }
                    }
                    ]
                ],done:function(){
                	execI18n();
                }
            });
        }
        word.delDir = function () {
            hideRMenu();
            var id = $("#id").val();
			if(selectedNode.children.length>0){
				layer.msg(getI18nValue("存在子级目录，不允许删除！"),{icon:5});
				return;
			}
            layer.confirm(getI18nValue("是否删除？"),{icon: 0,title:getI18nValue('信息'),btn:[getI18nValue('确认'),getI18nValue('取消')]}, function () {
                ajax.remoteCall("${ctxPath}/servlet/keyword?action=DeleteKeywordDir", {id: id}, function (result) {
                    if (result.state == 1) {
                        layer.msg(getI18nValue(result.msg), {icon: 1});
                        word.load();
                    } else {
                        layer.alert(getI18nValue(result.msg), {icon: 5,title:getI18nValue('信息'),btn:[getI18nValue('确认'),getI18nValue('取消')]});
                    }
                });
            });
        }
        word.del = function (id) {
            hideRMenu();
            layer.confirm(getI18nValue("是否删除？"),{icon: 0,title:getI18nValue('信息'),btn:[getI18nValue('确认'),getI18nValue('取消')]}, function () {
                ajax.remoteCall("${ctxPath}/servlet/keyword?action=DeleteKeyword", {id: id}, function (result) {
                    if (result.state == 1) {
                        layer.msg(getI18nValue(result.msg), {icon: 1});
                        searchResData();
                    } else {
                        layer.alert(getI18nValue(result.msg), {icon: 5,title:getI18nValue('信息'),btn:[getI18nValue('确认'),getI18nValue('取消')]});
                    }
                });
            });
        }

        function getDictTextByCode(code, val) {
            if (!code) {
                console.error(getI18nValue('没有传字典编号'));
            } else {
                var dict = dictJson[code];
                if (!dict) {
                    dict = getDictByCode(code);
                    if (dict.length == 0) {
                        console.error(getI18nValue('根据字典编号找不到字典项'));
                        return val;
                    }
                    dict = dict[controls].data;
                    dictJson[code] = dict;
                }
                for (var i in dict) {
                    if (i == val) {
                        return dict[i];
                    }
                }
            }
            return val;
        }

        var commonTree = {
            	// 树div元素id
            	el: 'ztree',
            	// mars请求
            	mars: 'keyWord.getCatalogList',
            	// 请求参数
           		params: {
           			type: '${param.type}'
           		},
           		// id对应的数据库字段
           		idKey: 'ID',
           		// 父id对应的数据库字段
           		parentKey: 'PARENT_ID',
           		// 第一级目录的parentId
           		firstParentCode: '-1',
           		// 节点的数据格式转换
           		initNode: function(data) {
                    data.name = data.NAME;
                    data.id = data.ID;
                    return data;
           		},
           		// 第一个节点
           		firstNode: {
           			id: '-1',
           			name: getI18nValue('目录')
           		},
           		// 初始化树
           		initTree: function(setting) {
           			commonTree.getData(function(data) {
               			var $el = $('#'+commonTree.el);
               			var config = $.extend({}, $el.data('setting'), setting);
               			commonTree.firstNode.children = commonTree.handleData(data) || [];
               			$.fn.zTree.init($el, config, commonTree.firstNode);
               			if (setting && setting.expandAll) {
	                        var ztreeObject = $.fn.zTree.getZTreeObj(commonTree.el);
	                        if (ztreeObject != null) {
	                            ztreeObject.expandAll(true);
	                        }
               			}
                	});
           		},
            	handleData: function(datas, targetCode) {
                    var result = [];
                    for (var i in datas) {
                    	var data = datas[i];
                    	if (!data.ID) { continue; }
                        var resCode = data[commonTree.idKey];
                        var parentCode = data[commonTree.parentKey];
                        if ((targetCode==null && parentCode == commonTree.firstParentCode) || (targetCode && targetCode == parentCode)) {
                            datas.splice(i, 1, {});
                            data = commonTree.initNode(data);
                            data.children = commonTree.handleData(datas, resCode);
                            result.push(data);
                        }
                    }
                    return result;
                },
                getData: function(callback) {
                    var controls = commonTree.mars;
                    ajax.daoCall({
                        params: commonTree.params,
                        controls: [controls]
                    }, function(data) {
                        if (data[controls]) {
                            data = data[controls].data;
                            callback && callback(data);
                        }
                    })
                }
            }
        
        //打开穿梭框
        word.showSlider = function(type){// 01-渠道，02-技能组
        	hideRMenu();
        	var title = type=="02"?"关联技能组":"关联渠道";
        	popup.layerShow({
                type: 1,
                title: getI18nValue(title),
                offset: '20px',
                area: ['860px', '780px']
            }, "${ctxPath}/pages/agentconfig/phrase/dir-relative.jsp", {
                DIR_ID: $("#id").val(),
                TYPE: type,
                CODE: $("#code").val(),
                url: "${ctxPath}/servlet/keyword?action=dirRelation" , // 用于请求对应后台的url
                sender: "keyword",
            });
        }
    </script>

</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>