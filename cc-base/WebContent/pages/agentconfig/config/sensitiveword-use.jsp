<%@ page language="java" contentType="text/html;charset=UTF-8"%>
    <%@ include file="/pages/common/global.jsp"%>
        <EasyTag:override name="head">
            <title i18n-content="敏感词使用记录"></title>
            <style type="text/css">
                div#rMenu {
                    position: absolute;
                    visibility: hidden;
                    top: 0;
                    text-align: left;
                    padding: 2px;
                }
                
                .dropdown-menu>li>a {
                    font-size: 13px;
                }
            </style>
        </EasyTag:override>
        <EasyTag:override name="content">
            <input type="hidden" id="timeset">
            <form action="JavaScript:word.loadData()" id="searchForm" name="searchForm" class="form-inline">
                <input type="hidden" value="-1" name="id" id="id"> <input type="hidden" value="" name="text" id="text">
                <div class="row" style="margin: 0;">
                    <div style="height: 450px; width: 100%; float: left;">
                        <div class="ibox ">

                            <div class="ibox-title clearfix" id="resetId">
                              
                                <div class="form-group">
                                <h5 i18n-content="敏感词使用记录"></h5>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="关键字"></span> <input type="text" name="KEY_WORD" i18n-placeholder="请输入账号、姓名" class="form-control input-sm" maxlength="200" style="width: 150px">
                                    </div>

                                    <!-- <div class="input-group input-group-sm">
								<span class="input-group-addon" i18n-content="类型"></span><select name="busiType"
									data-mars="common.getDict(SENSITIVE_DIR_BUSI_TYPE)" data-rules="required"
									class="form-control input-sm" name="is_public">
									<option value="" i18n-content="请选择"></option>
								</select>
							</div> -->

                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="敏感词"></span> <input type="text" name="sensitiveword" class="form-control input-sm" style="width: 160px">
                                    </div>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-addon" i18n-content="使用日期"></span>
                                        <input type="text" class="form-control input-sm" id="date" name="date" data-mars="common.currMonthTime" data-mars-top="true" autocomplete="off" style="height:30px;width:275px">
                                        <span class="input-group-addon">-</span>
                                        <select class="form-control input-sm" name="dateRange" onchange="word.onCasecade($(this))">
									<option value="" i18n-content="请选择"></option>
									<option value="today" i18n-content="今天"></option>
									<option value="yesterday" i18n-content="昨天"></option>
									<option value="thisWeek" i18n-content="本周"></option>
									<option value="RecentlyOneMonth" i18n-content="近一个月"></option>
									<option value="RecentlyThreeMonth" i18n-content="近三个月"></option>
								</select>
                                    </div>


                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-sm btn-default" type="button" onclick="word.searchData()"><span class="glyphicon glyphicon-search"></span> <span i18n-content="查询" ></span></button>
                                    </div>

                                    <div class="input-group">
                                        <button type="button" class="btn btn-sm btn-default" onclick="word.repeat()"><span class="glyphicon glyphicon-repeat"></span> <span i18n-content="重置" ></span></button>
                                    </div>
                                    <div class="input-group input-group-sm pull-right">
								 <button class="btn btn-sm btn-success btn-outline " type="button"
									onclick="word.exportData()" i18n-content="导出"></button>
							  </div>
                                </div>
                                
                            </div>
							
                            <div class="ibox-content">
                                <table id="dataList"></table>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </EasyTag:override>
        <EasyTag:override name="script">
            <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
            <script type="text/javascript" src="/cc-base/static/js/time.js"></script>
            <script type="text/javascript">
                //requreLib.setplugs("select2");

                jQuery.namespace("word");

                requreLib.setplugs("layui", function() {
                    $("#searchForm").render({
                        success: function() {
                            // 设置layui组件引入路径 ======> 兼容IE
                            layui.config({
                                dir: '/easitline-static/lib/layui/'
                            });
                            //时间控件 加载 laydate模块
                            layui.use('laydate', function() {
                                var laydate = layui.laydate;
                                laydate.render({
                                    elem: '#date',
                                    type: 'datetime',
                                    range: '~',
                                    lang: getDateLang()
                                });
                            })
                            $("#timeset").val($("#date").val());
                            word.loadData();
                        }
                    });
                });


                //初始化加载数据
                word.loadData = function() {
                    $("#searchForm").initTableEx({
                        mars: 'sensitiveword.useRecordList',
                        limit: '15',
                        limits: [15, 25, 50, 100, 200],
                        id: 'dataList',
                        height: 'full-120',
                        cols: [
                            [{
                                width: 80,
                                align: 'center',
                                title: '序号',
                                type: 'numbers'
                            }, {
                                width: 100,
                                field: 'CREATE_ACC',
                                align: 'center',
                                title: '账号'
                            }, {
                                width: 100,
                                field: 'USER_NAME',
                                align: 'center',
                                title: '姓名'
                            }, {
                                width: 100,
                                field: 'BUSI_TYPE',
                                align: 'center',
                                title: '类型',
                                templet: function(row) {
                                    return getDictTextByCode("SENSITIVE_DIR_BUSI_TYPE", row['BUSI_TYPE']);
                                }
                            }, {
                                width: 120,
                                field: 'CHANNEL_NAME',
                                align: 'center',
                                title: '渠道'
                            }, {
                                width: 120,
                                field: 'SKILL_GROUP_NAME',
                                align: 'center',
                                title: '技能组'
                            }, {
                                width: 120,
                                field: 'TRICK',
                                align: 'center',
                                title: '敏感词'
                            }, {
                                field: 'TRICK_CONTENT',
                                align: 'center',
                                title: '内容'
                            }, {
                                width: 170,
                                field: 'CREATE_TIME',
                                align: 'center',
                                title: '使用时间'
                            }, {
                                width: 100,
                                field: 'BUSI_ID',
                                align: 'center',
                                title: '操作',
                                templet: function(row) {
                                    var busiType = row['BUSI_TYPE'];
                                    var busiId = row['BUSI_ID'];
                                    var temp = "";
                                    if ((busiType == "01" || busiType == "03") && busiId && busiId != "") {
                                        temp = '<a href="javascript:void(0)" onclick="word.chatRecord(\'' + busiId +  '\',\'' + row.SESSION_ID + '\',\'' + row.CUST_NAME + '\',\'' + row.CREATE_ACC + '\')">' + getI18nValue('聊天记录') + '</a>';
                                    }
                                    return temp;
                                }
                            }, ]
                        ]
                    });
                }

                //查询数据
                word.searchData = function(flag) {
                    if (flag == '1') {
                        $("#searchForm").queryData({
                            id: 'dataList',
                            page: {
                                curr: 1
                            }
                        });
                    } else {
                        $("#searchForm").queryData({
                            id: 'dataList'
                        });
                    }
                };

                //重置
                word.repeat = function() {
                    $("#resetId input").val("");
                    $("#resetId select").val("");
                    $("#date").val($("#timeset").val());
                }

                //设置时间
                word.onCasecade = function(p) {
                    var dateRange = p.val();
                    if (dateRange == "today") {
                        $("#date").val(getTodayStartTime() + " ~ " + getTodayEndTime());
                    } else if (dateRange == "yesterday") {
                        $("#date").val(getYesterDayStartTime() + " ~ " + getYesterDayEndTime());
                    } else if (dateRange == "thisWeek") {
                        $("#date").val(getThisWeekStartTime() + " ~ " + getThisWeekEndTime());
                    } else if (dateRange == "RecentlyOneMonth") {
                        $("#date").val(getRecentlyOneMonthStartTime() + " ~ " + getTodayEndTime());
                    } else if (dateRange == "RecentlyThreeMonth") {
                        $("#date").val(getRecentlyThreeMonthStartTime() + " ~ " + getTodayEndTime());
                    }
                }

                //查询会话记录
                word.chatRecord = function(serialId,sessionId, custName, agentName) {
                    popup.layerShow({
                        type: 2,
                        title: getI18nValue('聊天记录'),
                        id: '1',
                        shade: 0,
                        maxmin: true,
                        zIndex: 2,
                        offset: 'r',
                        area: ['550px', '98%']
                    }, "/yc-media-agent/pages/record.jsp?chatSessionId="+serialId+"&sessionId="+sessionId, {    // 3.5#20240726 切换成统一的访问界面  /cc-report/pages/call/mediaChatRecord.jsp
                        serialId: serialId,
                        // custName: custName,
                        agentName: agentName
                    });
                }
                
                word.exportData=function(){
                	layer.confirm(getI18nValue('是否导出记录'),
                			{
                				icon : 3,
                				title : getI18nValue('导出提示'),
                				offset : '20px',
                				btn : [ getI18nValue('确定'), getI18nValue('取消') ]
                			},
                			function(index) {
                				layer.close(index);
                				var data = form.getJSONObject("#searchForm");
                				location.href = encodeURI("${ctxPath}/servlet/exportNoticeServlet?action=exportWord&"
                							+ $("searchForm").serialize()
                							+ "&data=" + JSON.stringify(data));
                			});
                }
            </script>

        </EasyTag:override>
        <%@ include file="/pages/common/layout_list.jsp"%>