<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>设置启用通知</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="easyform1" class="form-inline">
        <table class="table  table-edit table-vzebra mt-10">
            <tbody>
            <tr>
                <td i18n-content="修改数量："></td>
                <td style="width:70%;">
                    <div class="input-group ">
                    	<span class="" id="setCount"></span>
                    </div>
                </td>
            </tr>
            <tr>
                <td class=required i18n-content="通知方式："></td>
                <td style="width:70%;">
                    <div class="input-group mt-5">
                    	<label class="checkbox checkbox-warning checkbox-inline">
                            <input type="checkbox" name="NOTICE_TYPE" class="form-control " data-rules="required"
                                   value="01"> <span i18n-content="短信通知"></span>
                        </label>
                        <label class="checkbox checkbox-success checkbox-inline">
                            <input type="checkbox" name="NOTICE_TYPE" class="form-control " data-rules="required"
                                   value="02"> <span i18n-content="邮件通知"></span>
                        </label>
                        <label class="checkbox checkbox-info checkbox-inline">
                            <input type="checkbox" name="NOTICE_TYPE" class="form-control" data-rules="required"
                                   value="03"> <span i18n-content="内部通知"></span>
                        </label>
                        <label class="checkbox checkbox-info checkbox-inline">
                            <input type="checkbox" name="NOTICE_TYPE" class="form-control" data-rules="required"
                                   value="04"> <span i18n-content="app"></span>
                        </label>
                    </div>

                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" onclick="setEnable.save()" i18n-content="保存"></button>
            <button type="button" class="btn btn-default btn-sm ml-20" onclick="layer.closeAll();"
                    i18n-content="关闭"></button>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        jQuery.namespace("setEnable");
        var setCount = 0;
        $(function () {
            $("#easyform1").render({success:function(){
            	if("${param.ids}"==""){
                	setCount = "${param.totalCount}";
                }else{
                	var idArr = "${param.ids}".split(",");
                    setCount = idArr.length;
                }
                $("#setCount").html(setCount);
            }});
        });

        setEnable.save = function () {
            if (form.validate("easyform1")) {
                var data = form.getJSONObject("easyform1");
                if(typeof(data.NOTICE_TYPE)=='undefined'){
                	$.alertI18n(getI18nValue("通知方式不能为空！"), {
    					icon : 5
    				});
                	return;
                }
                data.ids = "${param.ids}";
                data.noteConfig = "${param.noteConfig}";
                
                var type = "${param.type}";
                var url = null;
                if (type == "1") {
                    url = "${ctxPath}/servlet/noteConfig?action=setEnable";
                } else if (type == "2") {
                    url = "${ctxPath}/servlet/noteConfig?action=setDisable";
                }else if(type == "3"){
                	url = "${ctxPath}/servlet/noteConfig?action=clearConfing";
                }
                ajax.remoteCall(url, data, function (result) {
                    if (result.state == 1) {
                        layer.msg(getI18nValue(result.msg), {icon: 1});
                        popup.layerClose("#easyform1");
                        noteConfig.searchData();
                    } else {
                        layer.msg(getI18nValue(result.msg), {icon: 5});
                    }
                });
            }
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>