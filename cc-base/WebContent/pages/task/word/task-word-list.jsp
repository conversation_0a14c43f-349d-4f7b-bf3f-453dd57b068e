<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="任务话术管理"></title>
	<style type="text/css">
		a:link {
			color: #00adff;
		}

		::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}

		::-webkit-scrollbar-track {
			background: transparent;
		}

		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}

		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}

		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline shadow" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix" id="divId">
				<div class="form-group">
					<h5 i18n-content="任务话术管理"> </h5>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="标题"></span>
						<input type="text" name="title" class="form-control input-sm" style="width:150px">
					</div>
					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default" onclick="TaskWord.loadData('1')">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span></button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="TaskWord.reset()"><span class="glyphicon glyphicon-repeat"></span><span
							 i18n-content="重置"></span></button>
					</div>
					<div class="input-group input-group-sm pull-right btn-group">
						<button type="button" class="btn btn-sm btn-success btn-outline" onclick="TaskWord.addData()" i18n-content="新增"></button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<table id="main"></table>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("TaskWord");

		$(function() {
			$("#searchForm").render();
			TaskWord.initData();
		});

		TaskWord.loadData = function(flag) {
			if (flag == '1') {
				$("#searchForm").queryData({
					id: 'main',
					page: {
						curr: 1
					}
				});
			} else {
				$("#searchForm").queryData({
					id: 'main'
				});
			}
		}

		//初始化加载数据
		TaskWord.initData = function() {
			$("#searchForm").initTableEx({
				mars: 'TaskWordDao.list',
				id: 'main',
				height: 'full-115',
				cols: [
					[{
						width: 60,
						field: 'ID',
						title: '序号',
						type: 'numbers'
					}, {
						field: 'WORD_TITLE',
						title: '标题',
						templet: function(row) {
							var temp = '<a  href="javascript:void(0)"  onclick="TaskWord.editData(\'' + row.WORD_ID + '\')">' + row.WORD_TITLE +
								'</a> ';
							return temp;
						}
					}, {
						width: 180,
						field: 'UPDATE_TIME',
						title: '更新时间',
						sort: true
					}, {
						width: 80,
						field: 'CREATOR',
						title: '发布人'
					}, {
						width: 100,
						field: 'WORD_ID',
						title: '操作',
						templet: function(row) {
							var temp = '<a  href="javascript:void(0)"  onclick="TaskWord.editData(\'' + row.WORD_ID + '\')">' +
								getI18nValue('修改') + '</a> ' +
								'<a  href="javascript:void(0)"  onclick="TaskWord.delData(\'' + row.WORD_ID + '\')">' + getI18nValue('删除') +
								'</a> ';
							return temp;
						}
					}]
				]
			});
		}

		//重置
		TaskWord.reset = function() {
			$("#divId select").val("");
			$("#divId input").val("");
		};

		TaskWord.addData = function() {
			popup.layerShow({
				type: 1,
				title: getI18nValue('新增'),
				offset: '20px',
				area: ['70%', '80%']
			}, "${ctxPath}/pages/task/word/task-word-edit.jsp", null);
		}
		TaskWord.editData = function(wordId) {
			popup.layerShow({
				type: 1,
				title: getI18nValue('编辑'),
				offset: '20px',
				area: ['70%', '80%']
			}, "${ctxPath}/pages/task/word/task-word-edit.jsp", {
				wordId: wordId
			});
		}
		TaskWord.delData = function(wordId) {
			layer.confirm(getI18nValue('是否删除？'), {
				icon: 3,
				title: getI18nValue('提示'),
				offset: '20px',
				btn: [getI18nValue('确定'), getI18nValue('取消')]
			}, function(index) {
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/taskWord?action=delete", {
					wordId: wordId
				}, function(result) {
					if (result.state == 1) {
						layer.msg(result.msg, {
							icon: 1,
							time: 1200,
							offset: '40px'
						}, function() {
							TaskWord.loadData();
						});
					} else {
						layer.alert(result.msg, {
							icon: 5
						});
					}
				});
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
