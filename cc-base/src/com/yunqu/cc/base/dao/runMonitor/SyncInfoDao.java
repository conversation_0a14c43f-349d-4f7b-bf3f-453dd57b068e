package com.yunqu.cc.base.dao.runMonitor;

import java.sql.SQLException;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.dao.runMonitor.service.RunMonitorSql;

/**
 * <AUTHOR>
 * @date :2019年10月22日下午4:04:39
 */
@WebObject(name = "syncInfo")
public class SyncInfoDao extends AppDaoContext {
	
	private Logger logger = CommonLogger.logger;
	
	/**
	 * 同步信息
	 * @return
	 */
	@WebControl(name = "list", type = Types.LIST)
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public JSONObject list() {
		
		EasySQL sql = RunMonitorSql.syncInfoQuerySql(param.getString("CODE"), param.getString("date"), this.getEntId(), this.getBusiOrderId(), this.getTableName("c_cf_sync_info"));
		
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
		if(result!=null){
			JSONArray array = result.getJSONArray("data");
			if(array!=null){
				for(int i = 0;i<array.size();i++){
					JSONObject d = array.getJSONObject(i);
					try {
						SyncInfoModel model = SyncService.getSyncInfo(this.getEntId(), this.getBusiOrderId(), d.getString("CODE"), this.getDbName());
						if(model!=null){
							d.put("CACHE_TIME", model.getCacheTime());
						}
					} catch (SQLException e) {
						logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出现异常:"+e.getMessage(),e);
					}
				}
				result.put("data", array);
			}
		}
		
		return result;
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	

}
