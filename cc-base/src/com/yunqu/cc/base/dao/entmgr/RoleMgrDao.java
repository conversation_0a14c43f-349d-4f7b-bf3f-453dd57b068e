
package com.yunqu.cc.base.dao.entmgr;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.base.base.AppDaoContext;

@WebObject(name="roleMgr")
public class RoleMgrDao extends AppDaoContext {
	/**
	 * 角色列表
	 */
	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		EasySQL sql = this.getEasySQL("select * from "+getTableName("CC_ROLE")+" where 1=1  ");
		sql.append(this.getBusiOrderId(), "and BUSI_ORDER_ID = ? ");
		sql.append(this.getEntId(), "and ENT_ID = ? ");
		sql.appendLike(this.param.getString("condition"), "and ROLE_NAME like ?  ");
		sql.append(" order by SYS_FLAG asc,ROLE_TYPE asc ,CREATE_TIME desc ");
		JSONObject list = this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		list.put("busiId",getUserPrincipal().getBusiId());
		return list;
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public  JSONObject record(){
		return queryForRecord(new EasyRecord(getTableName("CC_ROLE"),"ROLE_ID").setPrimaryValues(param.getString("pk")));
	}
	
	@WebControl(name="roleDict",type=Types.DICT)
	public  JSONObject roleDict(){
		EasySQL sql = this.getEasySQL("select ROLE_ID,ROLE_NAME  from "+getTableName("CC_ROLE")+" where 1=1 ");
		sql.append(this.getBusiOrderId()," and BUSI_ORDER_ID = ? " );
		sql.append(this.getEntId(), " and ENT_ID = ? ");
		sql.append(" order by ROLE_TYPE");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="roleNameDict",type=Types.DICT)
	public  JSONObject roleNameDict(){
		EasySQL sql = this.getEasySQL("select ROLE_ID,ROLE_NAME  from "+getTableName("CC_ROLE")+" where 1=1 ");
		sql.append(this.getBusiOrderId()," and BUSI_ORDER_ID = ? " );
		sql.append(this.getEntId(), " and ENT_ID = ? ");
		sql.append(" order by SYS_FLAG, ROLE_TYPE");
		return this.getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="agentListSelect",type=Types.DICT)
	public  JSONObject agentListSelect(){
		return this.getDictByQuery("select USER_ID,USERNAME from cc_user where 1=1 and USER_STATE=0  and ENT_ID = ?", new Object[]{getEntId()});
	}
	@WebControl(name="resList",type=Types.TEMPLATE)
	public JSONObject resList(){
		String entRes=getEntResBusi(getEntId(), this.getUserPrincipal().getBusiId());
		String pk=param.getString("pk");
		JSONArray array=new JSONArray();
		try {
			String sql = "select t1.RES_ID,t1.RES_NAME,t2.ROLE_ID from cc_busi_res t1 left join "
						+ getTableName("cc_role_res")
						+ " t2 on t1.RES_ID = t2.RES_ID and t2.ROLE_ID = ? where t1.P_RES_ID='2000' and t1.BUSI_ID = ? and t1.RES_TYPE = 2";
			if(entRes!=null){
				sql += " and t1.RES_ID in ("+entRes+")";
			}else{
				sql += " and t1.ENT_ID in('0','"+getEntId()+"')";
			}
			sql +=" order by t1.IDX_ORDER asc,t1.RES_ID desc";
			
			List<Map<String, String>> list = this.getQuery().queryForList(sql, new Object[]{pk,this.getUserPrincipal().getBusiId()}, new MapRowMapperImpl());
			for(Map<String,String> map:list){
				JSONObject jsonObject=new JSONObject();
				String resId=map.get("RES_ID");
				jsonObject.put("resId", resId); 
				jsonObject.put("resName", map.get("RES_NAME"));
				jsonObject.put("checked", map.get("ROLE_ID") == null ? "":map.get("ROLE_ID"));
				jsonObject.put("roleId", pk);
				sql="select t1.RES_ID,t1.RES_NAME,t2.ROLE_ID from cc_busi_res t1 LEFT JOIN "
						+ getTableName("cc_role_res")
						+ " t2 on t1.RES_ID = t2.RES_ID and t2.ROLE_ID = ? where t1.BUSI_ID = ? and t1.RES_TYPE = 2 and t1.P_RES_ID= ? ";
				if(entRes!=null){
					sql += " and t1.RES_ID in ("+entRes+")";
				}else{
					sql += " and t1.ENT_ID in('0','"+getEntId()+"')";
				}
				sql += "order by t1.IDX_ORDER asc,t1.RES_ID desc";
				List<Map<String, String>> list2=this.getQuery().queryForList(sql, new Object[]{pk,this.getUserPrincipal().getBusiId(), resId},new MapRowMapperImpl());
				jsonObject.put("list", list2);
				array.add(jsonObject);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getTemplate(array);
	}
	

	/**
	 * 企业订购资源
	 */
	private String getEntResBusi(String entId,String busiId){
		String sql="select RES_ID from CC_ENT_BUSI_RES where ENT_ID = ? and BUSI_ID =?";
		try {
			 List<JSONObject> list= cache.get("ENT_RES_"+entId+"_"+busiId);
			 if(list==null){
				 list=this.getQuery().queryForList(sql, new Object[]{entId,busiId},new JSONMapperImpl());
				 cache.put("ENT_RES_"+entId+"_"+busiId, list,1800);
			 }
			 if(list==null||list.size()==0){
				 return null;
			 }
			 StringBuffer sb=new StringBuffer();
			 for(JSONObject jsonObject:list){
				 sb.append("'"+jsonObject.getString("RES_ID")+"',");
			 }
			 return sb.substring(0,sb.length()-1);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return null;
	}
	/**
	 * 资源树
	 */
	@WebControl(name = "getResTree", type = Types.TREE)
	public JSONObject getResTree() {
		String roleId = param.getString("roleId");
		String sql = "select t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t2.ROLE_ID from CC_BUSI_RES t1 left join CC_ENT_BUSI_RES t3 on t1.BUSI_ID = t3.BUSI_ID and t1.RES_ID = t3.RES_ID left join "
				+ getTableName("CC_ROLE_RES") +" t2 on t3.RES_ID = t2.RES_ID and t3.ENT_ID = t2.ENT_ID and t2.ROLE_ID = ? where t1.BUSI_ID = ? and t1.RES_TYPE = 2 and t3.ENT_ID = '"+this.getEntId()+"'";
		JSONArray arr = new JSONArray();
		JSONObject root = new JSONObject();
		root.put("resId", "2000");
		root.put("resName", "菜单权限");
		root.put("name", "菜单权限");
		root.put("open", "true");

		try {
			List<JSONObject> list = this.getQuery().queryForList(sql, new Object[]{roleId, this.getBusiId()}, new JSONMapperImpl());

			for (JSONObject res : list) {
				if (!"2000".equals(res.getString("P_RES_ID"))) {
					continue;
				}
				JSONObject _json = new JSONObject();
				_json.put("resId", res.getString("RES_ID"));
				_json.put("resName", res.getString("RES_NAME"));
				_json.put("name", res.getString("RES_NAME"));
				_json.put("open", "true");
				_json.put("checked", StringUtils.isBlank(res.getString("ROLE_ID")) ? false : true);
				this.addChildNode(_json, res.getString("RES_ID"), list, 1);
				arr.add(_json);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		if (arr.size() > 0) {
			root.put("children", arr);
		}
		return getTree(root);
	}

	private void addChildNode(JSONObject jsonObject, String presId, List<JSONObject> list, int level) {
		JSONArray arr = new JSONArray();
		for (JSONObject res : list) {
			if (presId.equals(res.getString("P_RES_ID"))) {
				JSONObject _json = new JSONObject();
				_json.put("resId", res.getString("RES_ID"));
				_json.put("resName", res.getString("RES_NAME"));
				_json.put("name", res.getString("RES_NAME"));
				_json.put("checked", StringUtils.isBlank(res.getString("ROLE_ID")) ? false : true);
				if (level < 3) {
					_json.put("open", "true");
				}

				this.addChildNode(_json, res.getString("RES_ID"), list, level++);
				arr.add(_json);
			}
		}
		if (arr.size() > 0) {
			jsonObject.put("children", arr);
		}
	}
}
