package com.yunqu.cc.base.dao.black;


import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.base.base.AppDaoContext;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;

@WebObject(name="black")
public class BlackDao extends AppDaoContext {
	/**
	 *黑名单管理查询
	 * @return
	 */
	@WebControl(name="list",type=Types.LIST)
	@InfAuthCheck(resId = {"cc-base-system-hbmd-hmd","cc-ivr-config-list"},msg = "您无权访问!")
	public  JSONObject list(){
		EasySQL sql = this.getEasySQL("select  * ");
		sql.append(" from CC_BLACK_LIST");
		sql.append(" where 1=1  ");
		sql.append(param.getString("getStartDate")," and CREATE_TIME>= ?");
		sql.append(param.getString("getEndDate")," and CREATE_TIME<= ?");
		sql.appendLike(param.getString("PHONENUM"), " and PHONENUM like ?");
		sql.append(CommonUtil.parseInteger(param.getString("LIST_CLASS")), " and LIST_CLASS = ?");
		sql.append(param.getString("STATE")," and STATE= ?");
		sql.append( " and ENT_ID=  '"+this.getEntId()+"' ");
		sql.append(" order by CREATE_TIME desc");
		JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		if(result!=null){
			JSONArray dataArray = result.getJSONArray("data");
			String key = "BLACK_LIST_"+this.getEntId()+"_";
			if(dataArray!=null){
				for(int i = 0 ;i<dataArray.size(); i++){
					JSONObject data = dataArray.getJSONObject(i);
					int intType= DateUtil.compareDate(data.getString("LIMIT_TIME"), DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD), DateUtil.TIME_FORMAT_YMD);
					if(intType==1 ){
						data.put("TIMEOUT_STATE", "Y");
					}else {
						data.put("TIMEOUT_STATE", "N");
					}
				
					if(cache.get(key+data.getString("PHONENUM")+"_EX")!=null) {
						data.put("BUSI_STATE", "Y");
					}else {
						data.put("BUSI_STATE", "N");
					}
				}
				result.put("data", dataArray);
			}
		}
		return result;
	}
	/**
	 *黑名单详情
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-base-system-hbmd-hmd","cc-ivr-config-list"},msg = "您无权访问!")
	@WebControl(name="edit",type=Types.RECORD)
	public JSONObject edit(){
		JSONObject json=this.queryForRecord(new EasyRecord("CC_BLACK_LIST","ENT_ID", "PHONENUM")
				.setPrimaryValues(this.getEntId(),param.getString("black.PHONENUM")));
		
		if(json!=null) {
			JSONObject data=json.getJSONObject("data");
			JSONObject exJson=JSONObject.parseObject(data.getString("EX_JSON"));
			if(exJson!=null ) {
				JSONObject busi=JSONObject.parseObject(exJson.getString("busi"));
				if(busi!=null) {
					Map<String, Object> map = busi.toJavaObject(Map.class);
					JSONArray array=new JSONArray();
					for (Map.Entry<String, Object> entry : map.entrySet()) {
						if("Y".equals(entry.getValue())) {
							array.add(entry.getKey()); //只保存选中的。
						}
					}
					data.put("BUSI", array);
				}
			
			}
			
		}
		
		return json;
	}
	/**
	 *获取当前时间
	 * @return
	 */
	@InfAuthCheck(resId = "cc-base-system-hbmd-hmd",msg = "您无权访问!")
	@WebControl(name="limitTime",type=Types.TEXT)
	public JSONObject limitTime(){
		JSONObject json=new JSONObject();
		 SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
	        Calendar c = Calendar.getInstance();
	        c.setTime(new Date());
	        int time=Integer.valueOf(com.yunqu.cc.base.base.Constants.getBlackLimitTime());
	        c.add(Calendar.YEAR, +time);
	        Date start = c.getTime();
	        String startDay = format.format(start);//一年后
		json.put("data", startDay);
		return json;
	}
}
