package com.yunqu.cc.base.servlet.commonNotice;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.service.inform.base.InformBaseConstanst;

/**
 * Title:通知设置
 * Description:实现通知设置业务
 * Company:云趣科技
 * <AUTHOR>
 */
@WebServlet("/servlet/noticeSetting")
public class NoticeSettingServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 1L;
	
	private Logger logger = CommonLogger.getLogger("CommonNotice");

	/**
	 * 修改通知设置
	 * 
	 * @return
	 */
	public EasyResult actionForUpdate() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			jsonObject.put("ENT_ID", getEntId());
			jsonObject.put("BUSI_ORDER_ID", getBusiOrderId());
			String code = jsonObject.getString("CODE");
			jsonObject.remove("CODE");
			EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID").setColumns(jsonObject);
			this.getQuery().update(record);
			
			CacheUtil.getNoticeCache().delCache(getEntId(), getBusiOrderId(), code);
			return EasyResult.ok(record, getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "修改工单通知设置失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, getI18nValue("更新失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 新增通知设置
	 * 
	 * @return
	 */
	public EasyResult actionForAdd() {
		try {
			JSONObject json = this.getJSONObject();
			String code = json.getString("CODE");
			json.remove("CODE");
			UserModel user = UserUtil.getUser(getRequest());
			EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID").setColumns(json);
			record.set("ID", RandomKit.randomStr());
			record.set("CREATE_USER", user.getUserAcc());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			this.getQuery().save(record);
			
			CacheUtil.getNoticeCache().delCache(getEntId(), getBusiOrderId(), code);
			
			return EasyResult.ok(record, getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "新增工单通知设置失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, getI18nValue("新增失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 快速配置页面保存
	 * 
	 * @return EasyResult
	 */
	public EasyResult actionForFastSave() {
		try {
			UserModel user = UserUtil.getUser(getRequest());
			JSONObject json = this.getJSONObject();
			EasyQuery query = this.getQuery();
			
			String busiObjId = json.getString("busiObjId");
			EasySQL sql = new EasySQL("delete from").append(getTableName("C_CF_NOTICE_NUMBER"));
			sql.append(busiObjId, "where BUSI_OBJ_ID=?", false);
			query.execute(sql.getSQL(), sql.getParams());

			EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID");
			record.set("CREATE_USER", user.getUserAcc());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			
			record.set("BEGIN_TIME", json.getString("beginTime"));
			record.set("END_TIME", json.getString("endTime"));
			record.set("TYPE", json.getString("type"));
			record.set("BUSI_OBJ_NAME", json.getString("busiObjName"));
			record.set("BUSI_OBJ_ID", busiObjId);
			
			JSONArray dataArr = json.getJSONArray("dataArr");
			for(int i=0;i<dataArr.size();i++){
				JSONObject data = dataArr.getJSONObject(i);
				record.set("ID", RandomKit.randomStr());
				record.set("NOTICE_NUMBER", data.getString("noticeNumber"));
				record.set("NOTICE_TYPE", data.getString("noticeType"));
				if(data.getBooleanValue("state")){
					record.set("STATUS", "1");  //true 1-开启
				}else{
					record.set("STATUS", "2");  //false 2-禁用
				}
				query.save(record);
			}
			
			record.remove("BEGIN_TIME");
			record.remove("END_TIME");
			JSONArray dataArr2 = json.getJSONArray("dataArr2");
			for(int i=0;i<dataArr2.size();i++){
				JSONObject data = dataArr2.getJSONObject(i);
				record.set("ID", RandomKit.randomStr());
				record.set("NOTICE_NUMBER", data.getString("noticeNumber"));
				record.set("CHANNEL_KEY", data.getString("channelKey"));
				record.set("NOTICE_TYPE", InformBaseConstanst.NOTICE_NODE_TYPE99);
				record.set("STATUS", "1");  //true 1-开启
				query.save(record);
			}
			
			CacheUtil.getNoticeCache().delCache(getEntId(), getBusiOrderId(), busiObjId);
			
			return EasyResult.ok(record, getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "新增工单通知设置失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, getI18nValue("新增失败，原因：") + ex.getMessage());
		}
	}
	
	
	/**
	 * 删除通知设置
	 * 
	 * @return
	 */
	public EasyResult actionForDel() {
		try {
			String id = getJSONObject().getString("ID");
			EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			
			CacheUtil.getNoticeCache().delCache(getEntId(), getBusiOrderId(), getJSONObject().getString("CODE"));
			
			return EasyResult.ok(record,getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, getI18nValue("删除失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 同步内部通知信息
	 * 
	 * @return
	 */
	public EasyResult actionForSync() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String type = jsonObject.getString("TYPE"); // 类型 1-用户 2-部门 4-工作组
			String busiObjId = jsonObject.getString("CODE"); // 类型 1-用户 2-部门 4-工作组
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			String date = DateUtil.getCurrentDateStr();
			UserModel userModel = UserUtil.getUser(getRequest());
			if(StringUtils.isBlank(type)) {
				return EasyResult.fail(getI18nValue("缺少通知类型"));
			}
			
			List<JSONObject> noticeNumberList = this.getQuery().queryForList("SELECT NOTICE_NUMBER FROM " + this.getTableName("C_CF_NOTICE_NUMBER") + " WHERE BUSI_OBJ_ID = ? AND NOTICE_TYPE = ?", new Object[]{busiObjId, "03"}, new JSONMapperImpl());
			List<String> numberList = new ArrayList<String>();
			if(CommonUtil.listIsNotNull(noticeNumberList)) {
				for (JSONObject noticeNumberJson : noticeNumberList) {
					numberList.add(noticeNumberJson.getString("NOTICE_NUMBER"));
				}
			}
			
			if(StringUtils.equals("1", type)) {
				// 用户
				JSONObject userJson = CacheUtil.getCcUserCache().getCache(entId, busiOrderId, busiObjId);
				if(userJson == null) {
					return EasyResult.fail(getI18nValue("未找到用户缓存信息,busiObjId:") + busiObjId);
				}
				
				if(numberList.contains(userJson.getString("USER_ACCT"))) {
					return EasyResult.fail(getI18nValue("该用户通知号码已配置，无需再次同步"));
				}
				
				EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID");
				record.put("ID", RandomKit.randomStr());
				record.put("BEGIN_TIME", "00:00:00");
				record.put("END_TIME", "23:59:59");
				record.put("TYPE", type);
				record.put("BUSI_OBJ_ID", userJson.getString("USER_ACCT"));
				record.put("BUSI_OBJ_NAME", userJson.getString("USERNAME"));
				record.put("NOTICE_TYPE", "03");
				record.put("STATUS", "1");
				record.put("NOTICE_NUMBER", userJson.getString("USER_ACCT"));
				record.set("CREATE_USER", userModel.getUserAcc());
				record.set("CREATE_TIME", date);
				record.set("ENT_ID", entId);
				record.set("BUSI_ORDER_ID", busiOrderId);
				
				this.getQuery().save(record);
			} else if(StringUtils.equals("2", type)) {
				// 部门
				EasySQL sql = new EasySQL("SELECT T3.USER_ACC,T3.USER_NAME,T1.SKILL_GROUP_NAME");
				sql.append("FROM " + this.getTableName("CC_SKILL_GROUP") + " T1");
				sql.append("LEFT JOIN " + this.getTableName("CC_SKILL_GROUP_USER") + " T2 ON T1.SKILL_GROUP_ID = T2.SKILL_GROUP_ID");
				sql.append("LEFT JOIN " + this.getTableName("V_CC_USER") + " T3 ON T2.USER_ID = T3.USER_ID AND T1.ENT_ID = T3.ENT_ID AND T1.BUSI_ORDER_ID = T3.BUSI_ORDER_ID");
				sql.append("WHERE 1 = 1");
				sql.append(entId, "AND T1.ENT_ID = ?");
				sql.append(busiOrderId, "AND T1.BUSI_ORDER_ID = ?");
				sql.append(busiObjId, "AND T1.SKILL_GROUP_ID = ?");
				sql.append("struct", "AND T1.SKILL_GROUP_TYPE = ?");
				List<JSONObject> deptUserList = this.getQuery().queryForList(sql.getSQL(), sql.getParams() , new JSONMapperImpl());
				if(CommonUtil.listIsNotNull(deptUserList)) {
					for (JSONObject userJson : deptUserList) {
						String userAcc = userJson.getString("USER_ACC");
						if(numberList.contains(userAcc)) {
							logger.info("用户通知号码[" + userAcc + "]已配置，跳过该用户");
							continue;
						}
						
						EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID");
						record.put("ID", RandomKit.randomStr());
						record.put("BEGIN_TIME", "00:00:00");
						record.put("END_TIME", "23:59:59");
						record.put("TYPE", type);
						record.put("BUSI_OBJ_ID", busiObjId);
						record.put("BUSI_OBJ_NAME", userJson.getString("SKILL_GROUP_NAME"));
						record.put("NOTICE_TYPE", "03");
						record.put("STATUS", "1");
						record.put("NOTICE_NUMBER", userAcc);
						record.set("CREATE_USER", userModel.getUserAcc());
						record.set("CREATE_TIME", date);
						record.set("ENT_ID", entId);
						record.set("BUSI_ORDER_ID", busiOrderId);
						
						this.getQuery().save(record);
					}
				}
				
			} else if(StringUtils.equals("4", type)) {
				// 工作组
				EasySQL sql = new EasySQL("SELECT T2.USER_ACC,T1.NAME");
				sql.append("FROM " + this.getTableName("C_CF_WORKGROUP") + " T1");
				sql.append("LEFT JOIN " + this.getTableName("C_CF_WORKGROUP_USER") + " T2 ON T1.ID = T2.WORKGROUP_ID");
				sql.append("WHERE 1 = 1");
				sql.append(entId, "AND T1.ENT_ID = ?");
				sql.append(busiOrderId, "AND T1.BUSI_ORDER_ID = ?");
				sql.append(busiObjId, "AND T1.ID = ?");
				List<JSONObject> workGroupUserList = this.getQuery().queryForList(sql.getSQL(), sql.getParams() , new JSONMapperImpl());
				if(CommonUtil.listIsNotNull(workGroupUserList)) {
					for (JSONObject workGroupUser : workGroupUserList) {
						String userAcc = workGroupUser.getString("USER_ACC");
						if(numberList.contains(userAcc)) {
							logger.info("用户通知号码[" + userAcc + "]已配置，跳过该用户");
							continue;
						}
						
						EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID");
						record.put("ID", RandomKit.randomStr());
						record.put("BEGIN_TIME", "00:00:00");
						record.put("END_TIME", "23:59:59");
						record.put("TYPE", type);
						record.put("BUSI_OBJ_ID", busiObjId);
						record.put("BUSI_OBJ_NAME", workGroupUser.getString("NAME"));
						record.put("NOTICE_TYPE", "03");
						record.put("STATUS", "1");
						record.put("NOTICE_NUMBER", userAcc);
						record.set("CREATE_USER", userModel.getUserAcc());
						record.set("CREATE_TIME", date);
						record.set("ENT_ID", entId);
						record.set("BUSI_ORDER_ID", busiOrderId);
						
						this.getQuery().save(record);
					}
				}
			}
			
			CacheUtil.getNoticeCache().delCache(getEntId(), getBusiOrderId(), busiObjId);
			
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, getI18nValue("删除失败，原因：") + ex.getMessage());
		}
	}
	
	/**
	 * 更改启用状态
	 * 
	 * @return
	 */
	public EasyResult actionForMenuStatusEdit() {
		try {
			String id = getJSONObject().getString("id");
			EasyRecord record = new EasyRecord(this.getTableName("C_CF_NOTICE_NUMBER"), "ID").setPrimaryValues(id);
			record.set("STATUS", getJSONObject().getString("status"));
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			this.getQuery().update(record);
			CacheUtil.getNoticeCache().reloadCache(getEntId(), getBusiOrderId(), getJSONObject().getString("TYPE")+"_"+getJSONObject().getString("CODE"));
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (Exception ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "更改启用状态失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, getI18nValue("更改启用状态失败，原因：") + ex.getMessage());
		}
	}
	/**
	 * 获取提醒触发器对象
	 * @param json
	 * @return
	 */
	private List<JSONObject> getNoticeGroups(JSONObject json) {
		Set<String> keys = json.keySet();
		List<JSONObject> noticeGroup = new ArrayList<JSONObject>();
		if(keys != null) {
			List<String> criteriaList = new ArrayList<>();
			List<String> wayList = new ArrayList<>();
			
			Iterator<String> it = keys.iterator();
			while (it.hasNext()) {
				String key = it.next();
				if(CommonUtil.isNotBlank(key)) {
					if(key.startsWith("criteria")) {
						criteriaList.add(key);
						wayList.add(key.replace("criteria", "way"));
					}
				}
			}
			Collections.sort(criteriaList);
			Collections.sort(wayList);
			int clen = criteriaList.size();
			int wlen = wayList.size();
			
			for(int i=0; i<clen; i++) {
				JSONObject obj = new JSONObject();
				String criteriaKey = "criteria";
				String criteriaValue = json.getString(criteriaList.get(i));
				String wayKey = "way";
				String wayValue = "";
				if(i < wlen) {
					JSONArray arr = json.getJSONArray(wayList.get(i));
					if(CommonUtil.listIsNotNull(arr)) {
						wayValue = String.join(",", arr.toJavaList(String.class));
					}
				}
				obj.put(criteriaKey, criteriaValue);
				obj.put(wayKey, wayValue);
				noticeGroup.add(obj);
			}
		}
		return noticeGroup;
	}
	
	/**
	 * 工单历时配置
	 * @param json
	 * @return
	 */
	private List<JSONObject> getDurationGroups(JSONObject json) {
		Set<String> keys = json.keySet();
		List<JSONObject> durationGroup = new ArrayList<JSONObject>();
		if(keys != null) {
			List<String> durationList = new ArrayList<>();
			
			Iterator<String> it = keys.iterator();
			while (it.hasNext()) {
				String key = it.next();
				if(CommonUtil.isNotBlank(key)) {
					if(key.startsWith("duration")) {
						durationList.add(key);
					}
				}
			}
			Collections.sort(durationList);
			int len = durationList.size();
			
			for(int i=0; i<len; i++) {
				JSONObject obj = new JSONObject();
				String key = "duration";
				String value = json.getString(durationList.get(i));
				obj.put(key, value);
				durationGroup.add(obj);
			}
		}
		return durationGroup;
	}

}
