package com.yunqu.cc.base.servlet.agentconfig;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.utils.StringUtil;

@WebServlet("/servlet/dataConfig")
public class DataConfigServlet extends AppBaseServlet {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 新增话务数据
	 * 
	 * @return
	 */
	public EasyResult actionForAdd() {
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject json = this.getJSONObject();
		try {		
			String CREATE_TIME = EasyCalendar.newInstance().getDateString("-");
			String ID = RandomKit.smsAuthCode(16);
			String userAccount=user.getUserAcc();//登陆账号
			json.put("ID", ID);
			json.put("CREATE_TIME", CREATE_TIME);
			json.put("CREATE_USER", userAccount);
			EasyRecord record = new EasyRecord(user.getSchemaName()+".C_QUEUE", "ID").setColumns(json);			
			this.getQuery().save(record);
			return EasyResult.ok(record, "添加成功！");				
		} catch (Exception ex) {

			this.error("添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "添加失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 更新话务数据
	 * 
	 * @return
	 */
	public EasyResult actionForUp() {
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject jsonObject = this.getJSONObject("da");
		JSONObject json = this.getJSONObject();
		try {		
			String CREATE_TIME = EasyCalendar.newInstance().getDateString("-");
			jsonObject.put("AREA_NAME", json.getString("AREA_NAME"));
			jsonObject.put("BUSI_NAME", json.getString("BUSI_NAME"));
			//jsonObject.put("ID", ID);
			jsonObject.put("UPDATE_TIME", CREATE_TIME);
			EasyRecord record = new EasyRecord(user.getSchemaName()+".C_QUEUE", "ID").setColumns(jsonObject);	
			this.getQuery().update(record);
			return EasyResult.ok(record, "更新成功！");						
		} catch (Exception ex) {
			
			this.error("添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "添加失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 根据id删除
	 * 
	 * @return
	 */

	public EasyResult actionForDele() {
		UserModel user = UserUtil.getUser(this.getRequest());
		EasyQuery easyQuery=getQuery();
		try {
			String id = this.getJsonPara("id");		
			EasyRecord record = new EasyRecord(user.getSchemaName()+".C_QUEUE", "ID").setPrimaryValues(id);
			easyQuery.deleteById(record);
			return EasyResult.ok(record);
		} catch (Exception ex) {
			this.error("删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, "删除失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 根据id批量删除
	 * 
	 * @return
	 */
	public EasyResult actionForDeles() {
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject obj  = this.getJSONObject();
		JSONArray array = obj.getJSONArray("ids");
		EasyQuery query = this.getQuery();
		try {
			if(array !=null && array.size()>0){
				for(Object id :array){
					EasyRecord record1 = new EasyRecord(user.getSchemaName()+".C_QUEUE", "ID").setPrimaryValues(id);
					query.deleteById(record1);					
				}
				return EasyResult.ok("","删除成功！");
			}else{
				return EasyResult.error(501,"请选择一列");
			}
		} catch (Exception e) {
			this.getLogger().error("[SQLException] 删除失败，原因：",e);
			return EasyResult.error(501, "删除失败，原因：" + e.getMessage());
		}
	}
}
