package com.yunqu.cc.base.servlet.agentconfig;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.job.PhraseDirRelationThread;
import com.yunqu.cc.base.job.ThreadMgr;

/**
 * Description: 关键字 
 * <AUTHOR>
 * @date 2019年9月26日
 */
@WebServlet("/servlet/keyword/*")
public class KeywordServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	private Logger logger=CommonLogger.logger;

	public String actionForKeywordDir() {
		String depCode=UserUtil.getUser(this.getRequest()).getEpCode();
		JSONObject systemModule = new JSONObject();
		JSONObject enableStatus = new JSONObject();
		JSONObject type = new JSONObject();
		try {
			systemModule = DictCache.getJsonAllDictListByGroupCode(depCode, "SYSTEM_MODULE");
			enableStatus = DictCache.getJsonAllDictListByGroupCode(depCode, "ENABLE_STATUS");
			type = DictCache.getJsonAllDictListByGroupCode(depCode, "KEYWORD_DIR_BUSI_TYPE");
		} catch (Exception e) {
			this.error("actionForCatalogEdit", e);
		}
		this.setAttr("enableStatus", enableStatus);
		this.setAttr("systemModule", systemModule);
		this.setAttr("type", type);
		return "/pages/agentconfig/config/keyword_dir.jsp";
	}

	/**
	 * 保存关键字目录
	 * @return
	 */
	public EasyResult actionForSavekeywordDir() {
		
		EasyQuery query = this.getQuery();
		JSONObject entInfo = getJSONObject();
		String id = entInfo.getString("keyworkDir.ID");
		UserModel user = UserUtil.getUser(getRequest());
		
		try{
			// 字段检验
			EasySQL sql = new EasySQL("select count(1) from  "+getTableName("C_CF_KEYWORD_DIR")+"  where 1=1 ");
			sql.append(entInfo.getString("keyworkDir.PARENT_ID")," and PARENT_ID=?");
			sql.append(entInfo.getString("keyworkDir.NAME"),"and NAME = ?");
			sql.append(getEntId(),"and EP_CODE = ?");
			sql.append(getBusiOrderId(),"and BUSI_ORDER_ID = ?");
			if(StringUtils.isNotBlank(id)){
				sql.append(id,"and ID <> ?");
			}
			if(query.queryForExist(sql.getSQL(), sql.getParams())){
				return EasyResult.fail(getI18nValue("该目录下已有相同目录名称，请重新输入"));
			}
			
			JSONObject json = new JSONObject();
			json.put("NAME", entInfo.getString("keyworkDir.NAME"));
			json.put("CODE", entInfo.getString("keyworkDir.CODE"));
			json.put("MODULE", entInfo.getString("keyworkDir.MODULE"));
			json.put("BUSI_TYPE", entInfo.getString("keyworkDir.BUSI_TYPE"));
			json.put("ENABLE_STATUS", entInfo.getString("keyworkDir.ENABLE_STATUS"));
			json.put("BAKUP", entInfo.getString("keyworkDir.BAKUP"));
			json.put("SORT_NUM", entInfo.getInteger("keyworkDir.SORT_NUM"));
			json.put("PARENT_ID", entInfo.getString("keyworkDir.PARENT_ID"));
			EasyRecord record = new EasyRecord(getTableName("C_CF_KEYWORD_DIR"), "ID").setColumns(json);
			if (StringUtils.isBlank(id)) {
				//新增操作
				String nid = RandomKit.randomStr();
				String newCode = CommonUtil.getTreeCode(getTableName("C_CF_KEYWORD_DIR"), "ID", "PARENT_ID", "CODE", "EP_CODE", entInfo.getString("keyworkDir.PARENT_ID"), this.getEntId(), this.getBusiOrderId());
				record.put("ID", nid);
				record.put("BUSI_TYPE", entInfo.getString("keyworkDir.BUSI_TYPE"));
				record.put("CREATE_TIME", EasyDate.getCurrentDateString());
				record.put("CREATE_ACC", user.getUserAcc());
				record.put("CREATE_DEPT", user.getDept().getDeptCode());
				record.put("BUSI_ORDER_ID",this.getBusiOrderId());
				record.put("EP_CODE", user.getEpCode());
				record.put("CODE", newCode);
				query.save(record);
				// 新增后还需要把关联关系加上
				JSONObject relation = getJSONObject();
				relation.put("busiOrderId", getBusiOrderId());
				relation.put("entId", getEntId());
				relation.put("schema", this.getDbName());
				relation.put("sender", "keyword");
				relation.put("nid", nid);
				relation.put("code", newCode);
				//启动线程
				ThreadMgr.getInstance().executeOneTimes(new PhraseDirRelationThread(relation));
			} else {
				//修改操作
				record.put("ID", id);
				query.update(record);
			}
			return EasyResult.ok("", getI18nValue("操作成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作失败，原因:"+e.getMessage(),e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}

	public EasyResult actionForSaveKeyword() {
		JSONObject json = new JSONObject();
		JSONObject entInfo = this.getJSONObject();
		if ("".equals(entInfo.getString("keywork.ID"))) {
			json.put("ID", RandomKit.randomStr());
			json.put("CREATE_TIME", EasyDate.getCurrentDateString());
			json.put("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
			json.put("CREATE_NO", UserUtil.getUser(getRequest()).getUserNo());
			json.put("CREATE_NAME", UserUtil.getUser(getRequest()).getUserName());
			json.put("CREATE_DEPT", UserUtil.getUser(getRequest()).getDept().getpDeptCode());
		} else {
			json.put("ID", entInfo.getString("keywork.ID"));
		}
		json.put("DIR_ID", entInfo.getString("DIR_ID"));
		json.put("IS_PUBLIC", entInfo.getString("keywork.IS_PUBLIC"));
		json.put("ENABLE_STATUS", entInfo.getString("keywork.ENABLE_STATUS"));
		json.put("SORT_NUM", entInfo.getInteger("keywork.SORT_NUM"));
		json.put("CONTENT", entInfo.getString("keywork.CONTENT"));
		json.put("BUSI_ORDER_ID", this.getBusiOrderId());
		json.put("ENT_ID", this.getEntId());
		EasyRecord record = new EasyRecord(getTableName("C_CF_KEYWORD"), "ID").setColumns(json);
		try {
			if ("".equals(entInfo.getString("keywork.ID"))) {
				this.getQuery().save(record);
			} else {
				this.getQuery().update(record);
			}
			return EasyResult.ok("", "操作成功！");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作失败，原因:"+e.getMessage(),e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}

	public EasyResult actionForDeleteKeywordDir() {
		JSONObject entInfo = this.getJSONObject();
		EasyQuery query = this.getQuery();
		try {
			String sql = "select  count(1) from "+getTableName("C_CF_KEYWORD")+" where DIR_ID= ? ";
			int num = query.queryForInt(sql, new Object[] { entInfo.getString("id") });// 是否存在常用语
			if (num > 0) {
				return EasyResult.error(500, "存在关键字无法删除");
			} else {
				query.begin();
				// 删除关联关系
				EasyRecord record = new EasyRecord(getTableName("C_CF_KEYWORD_BUSI"), "DIR_ID")
						.setPrimaryValues(entInfo.getString("id"));
				query.deleteById(record);
				record = new EasyRecord(getTableName("C_CF_KEYWORD_DIR"), "ID").setPrimaryValues(entInfo.getString("id"));
				query.deleteById(record);
				query.commit();
				return EasyResult.ok("", "操作成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 回滚操作失败，原因:"+e1.getMessage(),e1);
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作失败，原因:"+e.getMessage(),e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());

		}
	}

	public EasyResult actionForDeleteKeyword() {
		JSONObject entInfo = this.getJSONObject();
		try {
			EasyRecord record = new EasyRecord(getTableName("C_CF_KEYWORD"), "ID").setPrimaryValues(entInfo.getString("id"));
			this.getQuery().deleteById(record);
			return EasyResult.ok("", "操作成功！");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作失败，原因:"+e.getMessage(),e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 修改公共常用语、关键字、敏感词的关联关系
	 * @return
	 */
	public EasyResult actionForDirRelation() {
		JSONObject json = getJSONObject();
		json.put("busiOrderId", getBusiOrderId());
		json.put("entId", getEntId());
		json.put("schema", this.getDbName());
		//启动线程
		ThreadMgr.getInstance().executeOneTimes(new PhraseDirRelationThread(json));
		
		return EasyResult.ok("", getI18nValue("操作成功"));
	}
}
