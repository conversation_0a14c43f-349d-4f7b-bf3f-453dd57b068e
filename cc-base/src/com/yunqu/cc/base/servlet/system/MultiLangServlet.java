package com.yunqu.cc.base.servlet.system;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.base.base.AppBaseServlet;
import com.yunqu.cc.base.base.CommonLogger;
import com.yunqu.cc.base.base.Constants;
import com.yunqu.cc.base.base.QueryFactory;
import com.yunqu.cc.base.utils.StringUtil;

@MultipartConfig
@WebServlet("/servlet/multiLang/*")
public class MultiLangServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.logger;

	/**
	 * 多语言添加或者修改
	 * @return
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public EasyResult actionForSave() {
		JSONObject multiLang = getJSONObject("multiLang");
		EasyQuery query = this.getQuery();
		try {
			
			EasySQL findSql = new EasySQL();
			findSql.append(" SELECT ID FROM CC_MULTI_LANG WHERE MODULE_NAME=? AND LANG_KEY=?");
			EasyRow row = query.queryForRow(findSql.getSQL(), multiLang.getString("MODULE_NAME"),multiLang.getString("LANG_KEY"));
			
			EasyRecord record = new EasyRecord("CC_MULTI_LANG","ID").setColumns(multiLang);
			
			String id = multiLang.getString("ID");
			if(StringUtils.isNotBlank(id)){ //修改
				if(row!=null && !id.equals(row.getColumnValue("ID"))){
					return EasyResult.error(500, "添加失败：模块名、键值已存在!");
				}
				query.update(record);
			}else{
				if(row!=null){
					return EasyResult.error(500, "添加失败：模块名、键值已存在!");
				}
				record.put("ID", RandomKit.randomStr());
				query.save(record);
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 保存多语言信息"+record.toJSONString());
			
			return EasyResult.ok("", getI18nValue("操作成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"操作失败，原因：" + e.getMessage());
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}finally {
			loadMultiLang();
		}
	}
	
	/**
	 * 更新国际化
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	private void loadMultiLang() {
		//更新后台多语言库的国际化
		BaseI18nUtil.reloadAll();
		//更新前台的多语言库的国际化
		CacheUtil.delete("CC_MULTI_LANG_LIST_CACHE");
	}

	/**
	 * 多语言（批量）删除
	 * @return
	 */
	public EasyResult actionForDelete() {
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		EasyQuery query = this.getQuery();
		boolean Flag = true;
		try {
			EasyRecord record = new EasyRecord();
			EasySQL sql = new EasySQL("SELECT CC_MULTI_LANG.*,T.TEST FROM CC_MULTI_LANG,");
			sql.append("(SELECT MULTI_LANG_ID,COUNT(1)TEST FROM CC_SYS_LANG_FIELD GROUP BY MULTI_LANG_ID) T ");
			sql.append(" WHERE T.MULTI_LANG_ID = CC_MULTI_LANG.ID");
			String joinSql = StringUtil.joinSql("  CC_MULTI_LANG.ID", ids);
			sql.append("and "+joinSql);
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			if(list.size()>0){
				return EasyResult.error(500,"["+list.get(0).getColumnValue("MODULE_NAME")+"]存在国际化字段定义，无法删除！");
			}
			for (int i = 0; i < ids.size(); i++) {
				record =  new EasyRecord("CC_MULTI_LANG","ID");
				record.put("ID", ids.getString(i));
				query.deleteById(record);
			}			
			return EasyResult.ok("",getI18nValue("操作成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因：" + e.getMessage());
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}finally {
			loadMultiLang();
		}
	}
	
	/**
	 * 界面导出
	 * @throws SQLException
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public void actionForExport() throws SQLException {
		HttpServletRequest request = this.getRequest();
		
		EasySQL sql = new EasySQL("select  * ");
		sql.append(" from CC_MULTI_LANG");
		sql.append(" where 1=1  ");
		
		sql.append(request.getParameter("moduleName")," AND MODULE_NAME=?");
		sql.append(request.getParameter("langKey")," AND LANG_KEY=?");
		
		sql.append(" order by MODULE_NAME,LANG_KEY");
		
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
//		headers.add(" ID ");
		headers.add(" 模块名 ");
		headers.add(" 键值 ");
		headers.add(" 中文 ");
		headers.add(" 英文 ");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		List<String> list = null;
		if (data != null && data.size() > 0) {
			for (Map<String, String> map : data) {
				list = new ArrayList<String>();
//				list.add(map.get("ID"));
				list.add(map.get("MODULE_NAME"));
				list.add(map.get("LANG_KEY"));
				list.add(map.get("CN"));
				list.add(map.get("EN"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "多语言配置列表.xlsx");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 多配置配置列表导出失败："+e.getMessage(),e);
		}
	}
	
	
	/**
	 * 多语言信息导入
	 * @return
	 */
	@InfAuthCheck(resId = "cc-base-system-xtpz-yxjk",msg = "您无权访问！")
	public JSONObject actionForUpload() {
		EasyQuery query = this.getQuery();
		try {
			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			
			//总列数
			int totalCellNum = 0;
			
			JSONArray dataArray = new JSONArray();
			
			StringBuffer checkMsg = new StringBuffer();
			
			//遍历所有行
			for (int rowNum = 0; rowNum <= maxLine; rowNum++) {
				Row row = sheet.getRow(rowNum);
				if (rowNum == 0) {
					totalCellNum = row.getLastCellNum();
				}
				
				if (row != null && rowNum!=0) { //标题行不处理
					JSONObject data = new JSONObject();
					StringBuffer cellRowMsg = new StringBuffer();
					//遍历一行中所有列
					for (int cellNum = 0; cellNum < totalCellNum; cellNum++) {
						//获取到具体列
						Cell cell = row.getCell(cellNum);
						if(cell!=null){
							String val = Utils.getCellValue(cell); //
							
							switch (cellNum) {
								case 0:
									if(StringUtils.isBlank(val)){
										cellRowMsg.append("模块名为空;");
									}
									data.put("MODULE_NAME", val);
									continue;
								case 1:
									if(StringUtils.isBlank(val)){
										cellRowMsg.append("键值为空;");
									}
									data.put("LANG_KEY", val);
									continue;
								case 2:
									if(StringUtils.isBlank(val)){
										cellRowMsg.append("中文为空;");
									}
									data.put("CN", val);
									continue;
								case 3:
									data.put("EN", val);
									continue;
								default:
									continue;
							}
						}
					}
					
					if(cellRowMsg.length()>0){
						checkMsg.append("第").append( (rowNum+1)).append("行:").append(cellRowMsg.toString()).append("\n");
					}else{
						dataArray.add(data);
					}
				}
			}
			
			//有错误则不允许导入
			if(checkMsg.length()>0){
				return EasyResult.error(100,"数据内容有误,无法导入:\n"+checkMsg.toString());
			}

			//开始保存数据
			for (int i = 0; i < dataArray.size(); i++) {
				JSONObject jsonObject = dataArray.getJSONObject(i);
				String moduleName=jsonObject.getString("MODULE_NAME");
				String langKey=jsonObject.getString("LANG_KEY");
				
				EasyRow row =this.getQuery().queryForRow("select ID from CC_MULTI_LANG where MODULE_NAME=? and LANG_KEY=?",new Object[]{moduleName,langKey});
				
				jsonObject.put("MODULE_NAME", jsonObject.getString("MODULE_NAME"));
				jsonObject.put("LANG_KEY", jsonObject.getString("LANG_KEY"));
				jsonObject.put("CN", jsonObject.getString("CN"));
				jsonObject.put("EN", jsonObject.getString("EN"));
				EasyRecord record = new EasyRecord("CC_MULTI_LANG", "ID").setColumns(jsonObject);
				
				if(row!=null){
					record.put("ID", row.getColumnValue("ID"));
					query.update(record);
				}else{
					record.put("ID", RandomKit.randomStr());
					query.save(record);
				}
			}
			return EasyResult.ok("", getI18nValue("操作成功"));
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "多语言信息导入异常:"+e.getMessage(), e);
			return EasyResult.ok("", "多语言信息导入失败！");
		}finally {
			loadMultiLang();
		}
	}
	

	/**
	 * 查询所有的多语言配置信息
	 * @return
	 */
	public EasyResult actionForGetAllMultiLang(){
		EasyResult result = new EasyResult();
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			EasySQL sql = new EasySQL();
			sql.append(" SELECT t1.* FROM  CC_MULTI_LANG t1");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			result.put("value", list);
		} catch (Exception e) {
			logger.error("查询失败，原因："+e.getMessage(),e);
			result.addFail("查询失败！");
		}
		return result;
	}
	
	/*
	 * 修改语言管理状态
	 */
	public EasyResult actionForMenuStatusEdit(){
		String ID = getJsonPara("ID");
		EasyRecord record = new EasyRecord("CC_SYS_LANG","ID").setPrimaryValues(ID);
		record.set("ENABLE_STATUS", getJsonPara("ENABLE_STATUS"));
		record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		record.set("UPDATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
		try {
			this.getQuery().update(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"状态修改失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(record, getI18nValue("操作成功"));		
	}
	/*
	 * 修改国际化字段状态
	 */
	public EasyResult actionForFieldStatusEdit(){
		String ID = getJsonPara("ID");
		EasyRecord record = new EasyRecord("CC_SYS_LANG_FIELD","ID").setPrimaryValues(ID);
		record.set("ENABLE_STATUS", getJsonPara("ENABLE_STATUS"));
		try {
			this.getQuery().update(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"状态修改失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(record, getI18nValue("操作成功"));		
	}
	/*
	 * 增加国际化字段定义
	 */
	public EasyResult actionForAdd(){
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject jsonObject = this.getJSONObject();
		String createTime = EasyCalendar.newInstance().getDateTime("-");
		String ID =RandomKit.smsAuthCode(16);
		String userAccount = user.getUserAcc();
		jsonObject.put("ID", ID);
		jsonObject.put("CREATE_ACC", userAccount);
		jsonObject.put("CREATE_TIME", createTime);
		jsonObject.put("LANG_CODE", this.getJsonPara("LANG_CODE"));
		jsonObject.put("LANG_NAME", this.getJsonPara("LANG_NAME"));
		jsonObject.put("LANG_OUT_CODE", this.getJsonPara("LANG_OUT_CODE"));
		jsonObject.put("LANG_DESC", this.getJsonPara("LANG_DESC"));	
		EasyRecord record = new EasyRecord("CC_SYS_LANG","ID").setColumns(jsonObject);
		try {
			int queryForInt = this.getQuery().queryForInt("SELECT COUNT(1) FROM CC_SYS_LANG WHERE LANG_CODE = ?", this.getJsonPara("LANG_CODE"));
			if(queryForInt>0){
				return EasyResult.error(500,"该语言编码已存在");
			}
			this.getQuery().save(record);	
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"增加失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(record,BaseI18nUtil.getI18nValue(this.getRequest(), Constants.APP_NAME,"操作成功!"));	
	}
	/*
	 * 修改国际化字段定义
	 */
	public EasyResult actionForUpdate(){
		String ID = getJsonPara("ID");
		EasyRecord record = new EasyRecord("CC_SYS_LANG","ID").setPrimaryValues(ID);
		record.set("LANG_NAME", this.getJsonPara("LANG_NAME"));
		record.set("LANG_OUT_CODE", this.getJsonPara("LANG_OUT_CODE"));
		record.set("LANG_DESC", this.getJsonPara("LANG_DESC"));
		record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		record.set("UPDATE_ACC",UserUtil.getRequestUserAcc(getRequest()));
		try {
			this.getQuery().update(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"修改失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	/*
	 * 删除语言管理字段
	 */
	public EasyResult actionForLangDelete(){
		
		String ID = getJsonPara("id");
		EasyRecord record = new EasyRecord("CC_SYS_LANG","ID").setPrimaryValues(ID);
		try {
			 String langCode = this.getJsonPara("LANG_CODE");
			int queryForint = this.getQuery().queryForInt("SELECT COUNT(1) FROM CC_SYS_LANG_FIELD WHERE SYS_LANG_CODE = ?", langCode);
			if(queryForint>0){
				return EasyResult.error(500,"该语言被国际化字段定义表使用，不允许删除!");
			}
			this.getQuery().deleteById(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	/*
	 * 新增国际化字段定义
	 */
	public EasyResult actionForFieldAdd(){
		UserModel user = UserUtil.getUser(this.getRequest());
		JSONObject jsonObject = new JSONObject();
		String str = getJsonPara("LANG_NAME");
		String[] split = str.split(",");
		jsonObject.put("SYS_LANG_ID", split[0]);
		jsonObject.put("SYS_LANG_CODE", split[1]);
		jsonObject.put("ID", RandomKit.smsAuthCode(16));
		jsonObject.put("CONTENT", getJsonPara("CONTENT"));
		jsonObject.put("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		jsonObject.put("CREATE_USER_ACC", user.getUserAcc());
		jsonObject.put("CREATE_USER_DEPT", user.getDeptCode());
		jsonObject.put("ENT_ID", getEntId());
		jsonObject.put("BUSI_ORDER_ID", getBusiOrderId());
		jsonObject.put("BAKUP",getJsonPara("BAKUP") );
		jsonObject.put("ENABLE_STATUS",getJsonPara("ENABLE_STATUS"));
		jsonObject.put("SORT_NUM",getJsonPara("SORT_NUM"));
		jsonObject.put("MULTI_LANG_ID", getJsonPara("MULTI_LANG_ID"));
		EasyRecord record = new EasyRecord("CC_SYS_LANG_FIELD","ID").setColumns(jsonObject);
		try {
			this.getQuery().save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	/*删除国际化字段的兴义
	 * 
	 */
	public EasyResult actionForFieldDelete(){
		String ID = getJsonPara("id");
		EasyRecord record = new EasyRecord("CC_SYS_LANG_FIELD","ID").setPrimaryValues(ID);
		try {
			this.getQuery().deleteById(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
	/*
	 * 修改国际化字段
	 */
	public EasyResult actionForFieldUpdate(){
		String ID = getJsonPara("FIELD_ID");
		EasyRecord record = new EasyRecord("CC_SYS_LANG_FIELD","ID").setPrimaryValues(ID);
		record.set("CONTENT", this.getJsonPara("CONTENT"));
		record.set("BAKUP", this.getJsonPara("BAKUP"));
		record.set("ENABLE_STATUS", this.getJsonPara("ENABLE_STATUS"));
		record.set("SORT_NUM", this.getJsonPara("SORT_NUM"));
		record.set("ENT_ID", getEntId());
		record.set("BUSI_ORDER_ID", getBusiOrderId());
		record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		try {
			this.getQuery().update(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败"+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok("",getI18nValue("操作成功"));
	}
}

