package com.yunqu.cc.base.job;

import org.apache.log4j.Logger;

import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.mq.MQTopicUtil;
import com.yq.busi.common.util.notice.WsNoticeUtil;
import com.yunqu.cc.base.listener.InitListener;

/**
 * 各个模块发送预警通知到 cc-commonext的 WsNoticeUtil里，先通过mq发送给用户，同时将通知写入到队列里；
 * 之后由该线程逐个处理通知，根据通知发送结果进行入库保存
 */
public class WsNoticeHandleThread extends Thread{
	private  Logger logger = CommonCoreLogger.getLogger("notice");
	
	private long count = 0;
	@Override
	public void run() {
		
		
		logger.info("实时通知后续处理线程,开始启动...");
		
		//启动线程，更新启动计数器
		WsNoticeUtil.getInstance().reloadHandleThread();
		
		
		//线程池停掉后，关闭
		while (InitListener.start){
			
			//当mq没起来时，先睡眠30s再继续
			if(!MQTopicUtil.isRun()){
				logger.info("mq未启动 等待30s");
				CommonUtil.sleep(30);
				continue;
			}
			WsNoticeUtil.getInstance().doing();
			
			count ++;
			
			//执行1000次后，打印一次日志
			if( count % 1000 == 0){
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 通知线程执行次数:" + count);
			}
			
		}
		
		
		//运行标识设置为已关闭
		WsNoticeUtil.getInstance().stop();
		
		logger.info("实时通知后续处理线程,结束运行...");
	}
}
