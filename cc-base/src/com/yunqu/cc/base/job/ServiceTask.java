package com.yunqu.cc.base.job;

import java.util.Calendar;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.base.base.Constants;

/**
 * 定时调用服务类
 */
public  class ServiceTask extends Thread{
	
	private static String SUCCESS = "01";
	private static String FAIL = "02";

	private String schema;
	private String busiOrderId;
	private String id;
	private String type;
	private String name;
	private String reqMain;
	private String reqParam;
	private String mac;
	private String execType;
	private String execRule;
	private String epCode;
	private boolean autoExec; //是否自动执行
	private Logger logger;
	private String beginExecTime ;//记录任务本次执行的开始时间，避免执行花费10多分钟，导致详情里时间对不上
	
	//该定时任务自cc-base启动后，执行的次数
	private int runCount = 0;
	
	/**
	 * 创建定时任务处理服务
	 * @param schema  数据库名
	 * @param row   定时任务详细信息
	 * @param mac   定时任务执行机器的mac地址
	 * @param autoExec  是否自动执行
	 * @param jobMessageQueue  存储定时任务执行消息的队列
	 */
	public ServiceTask(EasyRow row,String mac,boolean autoExec){
		//01-每月执行一次  02-每天执行一次 03-每小时执行一次 04-每分钟执行一次
		//05-间隔N月执行一次 06-间隔N天执行一次 07-间隔N小时执行一次 08-间隔N分钟执行一次  09-间隔N秒执行
		this.execType = row.getColumnValue("EXEC_TYPE");
		this.id = row.getColumnValue("ID");
		this.type = row.getColumnValue("TYPE");
		this.name = row.getColumnValue("NAME");
		this.reqMain = row.getColumnValue("REQ_MAIN");
		this.reqParam = row.getColumnValue("REQ_PARAM");
		this.execRule = row.getColumnValue("EXEC_RULE");
		this.epCode = row.getColumnValue("EP_CODE");
		this.autoExec = autoExec;
		
		this.schema = row.getColumnValue("SCHEMA_NAME");
		this.busiOrderId = row.getColumnValue("BUSI_ORDER_ID");
		this.mac = mac;
		this.logger = LogEngine.getLogger(Constants.APP_NAME+"_job"+"_"+execType);
	}
	
	@Override
	public void run() {
		try {
			
			if(!JobMgr.getInstance().canRun(mac,name)){
				return;
			}
			
			//当自动执行时:按每月执行一次的任务,如果天数对应不上则不执行
			if(autoExec && DictConstants.EXEC_TYPE_BY_MONTH.equals(execType)){
				int day = CommonUtil.parseInt(execRule.substring(0,2));
				Calendar cal = Calendar.getInstance();
				if(day!=cal.get(Calendar.DAY_OF_MONTH)){
					return;
				}
			}
			//如果任务正在运行中，则不重复执行
			if(DictConstants.DICT_SY_YN_Y.equals(JobMgr.getInstance().getOneJobRunStatus(id))){
				log("线程["+name+"]正在运行中,本次不执行...");
				execJob("02","处理失败:任务正在运行中,不重复执行!");
				return;
			}
			
			//标记该任务正在运行
			JobMgr.getInstance().runOneJob(id,name);
			
			beginExecTime = DateUtil.getCurrentDateStr();
			
			log("线程["+name+"]准备执行...");
			long begin = System.currentTimeMillis();
			
			JSONObject json  = doing();
			
			log("线程["+name+"]执行完成...json"+json);
			
			long end = System.currentTimeMillis();
			
			long seconds = (end-begin)/1000;
			
			String respCode = json.getString("respCode");
			String respDesc = json.getString("respDesc") + ",结束时间:"+DateUtil.getCurrentDateStr()+",花费 ["+ (end-begin) +"] 毫秒, 约 ["+seconds+"] 秒";
			
			execJob(respCode, respDesc);
			
			runCount ++;
			
			log("线程["+name+"]执行完成,花费["+seconds+"]秒");
		} catch (Exception e) {
			logError("线程["+name+"]执行出现异常,"+e.getMessage(),e);
		}finally{
			//标记该任务已结束运行
			JobMgr.getInstance().stopOneJob(id,name);
		}
		
	}
	
	public void log(String str) {
		if(logger!=null){
			logger.info(CommonUtil.getClassNameAndMethod(this)+ str);
		}
	}
	public void logError(String str,Exception e) {
		if(logger!=null){
			logger.error(CommonUtil.getClassNameAndMethod(this)+ str,e);
		}
	}

	/**
	 * 正式开始执行任务
	 */
	public  JSONObject doing(){
		if(DictConstants.JOB_TYPE_SERVICE_INTEFACE.equals(type)){
			return execServiceJob();
		}else{
			return execHttpJob();
		}
		
	}
	
	/**
	 * 按http方式执行任务
	 * @return 
	 */
	private JSONObject execHttpJob() {
		JSONObject json = new JSONObject();
		json.put("respCode", FAIL);
		
		long start = System.currentTimeMillis();
		
		try {
			//调用http
			HttpResp resp = HttpUtil.post(reqMain, reqParam, "UTF-8");
			
			if(resp.success()){
				json.put("respCode", SUCCESS);
				json.put("respDesc", "执行成功");
			}else{
				json.put("respDesc", "执行失败:"+resp.getCode());
			}
		} catch (Exception e) {
			logError("线程["+name+"]处理失败:"+e.getMessage(),e);
			json.put("respDesc", "处理失败:"+e.getMessage());
		}finally {
			long end = System.currentTimeMillis();
			long time = end-start;
			if(time>5000){
				CommonCoreLogger.getInvokeDanagerLogger().info(" -- ["+time+"]ms,定时任务，http调用时间过长,请求："+reqMain+","+reqParam+",返回:"+json);
			}
		}
		
		return json;
	}

	/**
	 * 按service方式执行任务
	 * @return 
	 */
	public  JSONObject execServiceJob(){
		JSONObject json = new JSONObject();
		json.put("respCode", FAIL);
		long start = System.currentTimeMillis();
		try {
			//调用service类处理
			IService service = ServiceContext.getService(reqMain);
			if(service==null){
				json.put("respDesc", "处理失败:无法找到对应的Service处理类:"+reqMain);
				return json;
			}
			
			if(StringUtils.isNotBlank(reqParam) && !"0".equals(reqParam)){
				StringBuffer resultBuf = new StringBuffer("执行成功:<br>");
				//同一个任务可能要访问多个command方法，循环处理
				String[] commands = reqParam.split(";");
				if(commands!=null && commands.length>0){
					int size = commands.length;
					resultBuf.append("待执行的服务数量:"+size+"<br>");
					for(int i=0; i<size; i++){
						String cmd = commands[i];
						if(StringUtils.isNotBlank(cmd)){
							long begin = System.currentTimeMillis();
							JSONObject j = new JSONObject();
							j.put("serviceId", reqMain);
							j.put("command", cmd);
							j.put("epCode", epCode);
							j.put("schema", schema);
							j.put("entId", epCode);
							j.put("busiOrderId", busiOrderId);
							JSONObject invoke = service.invoke(j);
							resultBuf.append("执行["+reqMain+"]["+cmd+"],结果"+invoke+"<br>");
							logger.info(CommonUtil.getClassNameAndMethod(this)+ " 调用服务接口完成["+(i+1)+"/"+size+"], json = "+ j);
							
							//3.1#20220110-1 超过10s执行的任务单独打印出来，便于查看日志
							long end = System.currentTimeMillis();
							if(end-begin > 5000){
								CommonCoreLogger.getInvokeDanagerLogger().info(" -- ["+(end-begin)+"]ms 定时任务["+name+"]执行["+reqMain+"]["+cmd+"]超过10s，耗时: "+ (end-start)+",参数:"+j);
							}
						}
					}
				}
				json.put("respCode", SUCCESS);
				json.put("respDesc", resultBuf.toString());
				logger.info(CommonUtil.getClassNameAndMethod(this)+ " 服务接口执行完成!");
			}
		} catch (Exception e) {
			logError("线程["+name+"]处理失败:"+e.getMessage(),e);
			json.put("respDesc", "处理失败:"+e.getMessage());
		}finally {
			long end = System.currentTimeMillis();
			long time = end-start;
			if(time>5000){
				CommonCoreLogger.getInvokeDanagerLogger().info(" -- ["+time+"]ms,定时任务，service调用时间过长,请求："+reqMain+","+reqParam+",返回:"+json);
			}
		}
		return json;
	}

	/**
	 * 提交保存执行结果
	 * @param status
	 * @param desc
	 */
	private void execJob(String status, String desc) {
		try {
			
			if("N".equals(Constants.getSaveJobExecRecord())){
				return;
			}
			
			JSONObject exec = new JSONObject();
			exec.put("id", id);
			exec.put("beginExecTime", beginExecTime);
			exec.put("schema", schema);
			exec.put("status", status);
			exec.put("desc", desc);
			exec.put("schema", schema);
			exec.put("execType", "exec");
			exec.put("mac", mac);
			log("exec:"+exec);
			//结果信息写入队列，由单独的线程处理
			JobMessageQueue.getInstance().put(exec.toJSONString());
			
		} catch (Exception e) {
			log("线程["+name+"]更新处理结果失败:"+e.getMessage());
		}
		
	}

	/**
	 * 获取定时任务在本机上的执行次数
	 * @return
	 */
	public int getRunCount() {
		return runCount;
	};
}
