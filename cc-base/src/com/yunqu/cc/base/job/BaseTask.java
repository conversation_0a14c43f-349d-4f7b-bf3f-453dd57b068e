package com.yunqu.cc.base.job;

import org.apache.log4j.Logger;

import com.yq.busi.common.util.CommonUtil;

public  class BaseTask extends Thread{

	protected String name;
	protected Logger logger;
	
	public BaseTask(String name,Logger logger){
		this.name = name;
		this.logger = logger;
	}
	
	@Override
	public void run() {
		log("线程["+name+"]准备执行...");
		long begin = System.currentTimeMillis();
		
		doing();
		
		long end = System.currentTimeMillis();
		
		long seconds = (end-begin)/1000;
		log("线程["+name+"]执行完成,花费["+seconds+"]秒");
	}
	
	public void log(String str) {
		if(logger!=null){
			logger.info(CommonUtil.getClassNameAndMethod(this)+ str);
		}
	}

	public  void doing(){};
}
