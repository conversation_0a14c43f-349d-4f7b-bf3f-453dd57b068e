package com.yunqu.cc.base.service.notice;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.NoticeMessage;
import com.yq.busi.common.util.NoticeParamUtil;

/**
 * 
 */
public class QueueMonitorNoticeService002  extends CcBaseNoticeService{

	public QueueMonitorNoticeService002(){
		super.init();
		this.code = NOTICE_CONDITION_002;
		this.name = "全媒体排队数量限制";
		
		// 变量定义参数需要与传入业务参数中的参数名对应
		this.noticeTitle = "全媒体排队数量超量#queueCount#";
		this.noticeContent = "全媒体排队数量超量#queueCount#";
		
		this.tips = "全媒体排队数量超量需要通知的对象;提醒参数queueCount配置排队人数,单位人;支持变量:排队人数-#queueCount# ";
		
		// 通知业务参数
		this.params.add(createParam1());
	}
	
	/**
	 * 创建通知参数1
	 * @return
	 */
	private JSONObject createParam1() {
		JSONObject param1 = new JSONObject();
		param1.put("paramName", "排队人数");
		// 需要与传入业务参数中的参数名对应
		param1.put("paramCode", "queueCount");
		param1.put("paramType", "number");
		param1.put("paramDm", NoticeParamUtil.DM_01);
		param1.put("paramVal", "10");
		param1.put("paramUnit", "秒");
		return param1;
	}
	
	/**
	 * 处理通知
	 * @param entId
	 * @param busiOrderId
	 * @param user
	 * @param json  业务参数
	 * @return
	 */
	public  JSONObject notice(NoticeMessage message){
		return super.notice(message);
	}

}
