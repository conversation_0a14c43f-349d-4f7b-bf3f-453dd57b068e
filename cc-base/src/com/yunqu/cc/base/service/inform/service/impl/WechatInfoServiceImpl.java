/**
 * 
 */
package com.yunqu.cc.base.service.inform.service.impl;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.base.service.inform.base.BaseServiceImpl;
import com.yunqu.cc.base.service.inform.service.WechatInfoInf;

/**
 * <AUTHOR>
 * @date 2023-04-03 16:14:59
 */
public class WechatInfoServiceImpl extends BaseServiceImpl implements WechatInfoInf {

	@Override
	public JSONObject getTemplateList(JSONObject reqJson) {
		String channelKey = reqJson.getString("channelKey");
		try {
			String gwUrl = reqJson.getJSONObject("wechatInfo").getJSONObject("EX_JSON").getString("WECHAT_GW_URL");
			String reqUrl = gwUrl + "gw-weixin/api/getTemplateList?wechatId=" + channelKey;//暂时编码两次
			
			/*{
				"template_list": [
					{
						"template_id": "dZQpJ4P5uGzD-1GyyLEbw4g7MUTodD63lek9rJnCHtY",
						"title": "测试模板",
						"primary_industry": "",
						"deputy_industry": "",
						"content": "测试模板内容",
						"example": ""
					},
					{
						"template_id": "Z37rwEHX-FxduhnTnHbF-euljBkvJ-_Gfv3gICRo4Bw",
						"title": "测试模板2",
						"primary_industry": "",
						"deputy_industry": "",
						"content": "测试啦啦啦\n      你当前有{{orderNum.DATA}}条超时工单",
						"example": ""
					}
				]
			}*/
			HttpResp resp = HttpUtil.sendGet(reqUrl, null, null);
			logger.info(CommonUtil.getClassNameAndMethod(this) + " result:" + JSONObject.toJSONString(resp));
			if(resp != null && resp.getCode() == 200) {
				JSONObject result = JSONObject.parseObject(resp.getResult());
				if(result != null) {
					JSONArray templateList = result.getJSONArray("template_list");
					if(CommonUtil.listIsNotNull(templateList)) {
						List<JSONObject> data = new ArrayList<JSONObject>();
						for(int i = 0; i < templateList.size(); i++ ) {
							JSONObject row = templateList.getJSONObject(i);
							
							JSONObject obj = new JSONObject();
							obj.put("id", row.getString("template_id"));
							obj.put("title", row.getString("title"));
							obj.put("content", row.getString("content"));
							obj.put("paramList", getParamList(row.getString("content")));
							data.add(obj);
						}
						return getSuccJson(data);
					}
					return getSuccJson(templateList);
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return getFailJson();
	}

	@Override
	public JSONObject sendMessageByTemplate(JSONObject reqJson) {
		String channelKey = reqJson.getString("channelKey");
		try {
			String gwUrl = reqJson.getJSONObject("wechatInfo").getJSONObject("EX_JSON").getString("WECHAT_GW_URL");
			String reqUrl = gwUrl + "gw-weixin/api/sendMessageByTemplate?wechatId=" + channelKey;//暂时编码两次
			
			/*{
				"template_list": [
					{
						"template_id": "dZQpJ4P5uGzD-1GyyLEbw4g7MUTodD63lek9rJnCHtY",
						"title": "测试模板",
						"primary_industry": "",
						"deputy_industry": "",
						"content": "测试模板内容",
						"example": ""
					},
					{
						"template_id": "Z37rwEHX-FxduhnTnHbF-euljBkvJ-_Gfv3gICRo4Bw",
						"title": "测试模板2",
						"primary_industry": "",
						"deputy_industry": "",
						"content": "测试啦啦啦\n      你当前有{{orderNum.DATA}}条超时工单",
						"example": ""
					}
				]
			}*/
			
			String param = "openId=" + reqJson.getString("openId");
			param += "&templateId=" + reqJson.getString("templateId");
			param += "&url=" + URLEncoder.encode(reqJson.getString("url"), "utf-8");
			param += "&paramList=" + URLEncoder.encode(reqJson.getString("paramList"), "utf-8");
			
			logger.info(CommonUtil.getClassNameAndMethod(this) + " reqUrl:" + reqUrl + ", param:" + param);
			HttpResp resp = HttpUtil.sendGet(reqUrl, param, "utf-8");
			logger.info(CommonUtil.getClassNameAndMethod(this) + " result:" + JSONObject.toJSONString(resp));
			if(resp != null && resp.getCode() == 200) {
				JSONObject result = JSONObject.parseObject(resp.getResult());
				if(result != null) {
					return getSuccJson(null);
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		return getFailJson();
	}
	
	

}
