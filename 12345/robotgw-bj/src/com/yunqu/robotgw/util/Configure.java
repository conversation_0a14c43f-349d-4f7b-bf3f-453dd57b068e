package com.yunqu.robotgw.util;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.impl.TomcatAppContext;


public class Configure {
	public static String getProperty(String key, String defaultValue){
		AppContext appContext = TomcatAppContext.getContext(Constants.APP_NAME);
		String value = "";
		if(null != appContext) {
			value = appContext.getProperty(key, defaultValue);
		}
		return value;
	}
	
	public static String getProperty(String key) {
		AppContext appContext = TomcatAppContext.getContext(Constants.APP_NAME);
		String value = "";
		if(null != appContext) {
			value = appContext.getProperty(key, "");
		}
		return value;
	}
}
