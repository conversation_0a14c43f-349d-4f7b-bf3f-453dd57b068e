<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>队列在线人数统计报表</title>
	<style type="text/css">
th {
	text-align: center; /** 设置水平方向居中 */
	vertical-align: middle /** 设置垂直方向居中 */
}

a:link {
	color: #00adff;
}

</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form name="searchForm" class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span>队列在线人数统计报表</span><span id="titleAndTime"></span><span id="sub"></span>
					</h5>

					<div class="form-group">

						<div class="input-group input-group-sm">
							<span class="input-group-addon" >开始时间</span>
							<input type="text" class="form-control input-sm" id="startDate" name="BEGIN_TIME" style="width:150px" data-mars-reload="false" data-mars-top='true' autocomplete="off">
							<span class="input-group-addon">-</span>	
							<input type="text" class="form-control input-sm" id="endDate" name="END_TIME" style="width:150px" data-mars-reload="false" data-mars-top='true' autocomplete="off">
                    	</div>
						<div class="input-group">
							<span class="input-group-addon">技能队列</span>
							<select type="text" id="SKILL_GROUP_ID"  name="SKILL_GROUP_ID" data-mars='common.skillGroupList' class="form-control" >
								<option value="">请选择</option>
							</select>
						</div>

						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default"
								onclick="reportStat3.loadData()">
								<span class="glyphicon glyphicon-search"></span> 搜索
							</button>
						</div>


						<div class="input-group input-group-sm pull-right" style="margin-left: 10px;">
								<button type="button" class="btn btn-sm btn-info btn-outline glyphicon glyphicon-export"
									onclick="reportStat3.exportDetail()" >导出
								</button>
						</div>
					</div>

				</div>

			</div>

			<div class="ibox-content table-responsive">
				<table id="dataList"></table>
			</div>
		</div>
		<div class="stat-desc ibox-content mt-15">
			<fieldset class="content-title">
				<legend>统计口径</legend>
			</fieldset>
			1、签入人数：队列的签入人数<br>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("reportStat3");

		$(function() {
			$("#searchForm").render({
				success : function() {
					//requreLib.setplugs('select2',function(){
						//$("#SKILL_GROUP_ID").select2({theme: "bootstrap"});
					//});

					requreLib.setplugs('layui', function() {
						$("#startDate").val(getInitStartTime());
				    	$("#endDate").val(getInitEndTime());
						layui.use('laydate', function(){
					  		var laydate = layui.laydate;
					  		laydate.render({ elem: '#startDate' ,type: 'datetime'});
					  		laydate.render({ elem: '#endDate' ,type: 'datetime'});
						});
						reportStat3.loadData();
					})
				}
			});
		});

		//初始化加载数据
		reportStat3.loadData = function() {
			let clos=[[
				{
					width : 60,
					type : 'numbers',
					title : '序号'
				},
				{
					field : 'SELECT_TIME',
					title : '时间',
					align : 'center'
				},
				{
					field : 'SKILLGROUPNAME',
					title : '队列名称',
					align : 'center'
				},
				{
					field : 'LOGIN_COUNT',
					title : '签入人数',
					align : 'center'
				}
			]];
			loadTableData("statDao.getSkillLoginCountStat",clos);
		}

		//导出
		reportStat3.exportDetail = function(){
			exportReportExcel("队列在线人数统计报表","/cx-report-12345/servlet/export?action=ExportSkillLoginCountStat&"+$("#searchForm").serialize());
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>