/*
Copyright (c) 2003-2021, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/license
*/
CKEDITOR.lang['fr-ca']={"editor":"Éditeur de texte enrichi","editorPanel":"Rich Text Editor panel","common":{"editorHelp":"Appuyez sur 0 pour de l'aide","browseServer":"Parcourir le serveur","url":"URL","protocol":"Protocole","upload":"Envoyer","uploadSubmit":"Envoyer au serveur","image":"Image","flash":"Animation Flash","form":"Formulaire","checkbox":"Case à cocher","radio":"Bouton radio","textField":"Champ texte","textarea":"Zone de texte","hiddenField":"Champ caché","button":"Bouton","select":"Liste déroulante","imageButton":"Bouton image","notSet":"<Par défaut>","id":"Id","name":"Nom","langDir":"Sens d'écriture","langDirLtr":"De gauche à droite (LTR)","langDirRtl":"De droite à gauche (RTL)","langCode":"Code langue","longDescr":"URL de description longue","cssClass":"Classes CSS","advisoryTitle":"Titre","cssStyle":"Style","ok":"OK","cancel":"Annuler","close":"Fermer","preview":"Aperçu","resize":"Redimensionner","generalTab":"Général","advancedTab":"Avancé","validateNumberFailed":"Cette valeur n'est pas un nombre.","confirmNewPage":"Les changements non sauvegardés seront perdus. Êtes-vous certain de vouloir charger une nouvelle page?","confirmCancel":"Certaines options ont été modifiées.  Êtes-vous certain de vouloir fermer?","options":"Options","target":"Cible","targetNew":"Nouvelle fenêtre (_blank)","targetTop":"Fenêtre supérieur (_top)","targetSelf":"Cette fenêtre (_self)","targetParent":"Fenêtre parent (_parent)","langDirLTR":"De gauche à droite (LTR)","langDirRTL":"De droite à gauche (RTL)","styles":"Style","cssClasses":"Classe CSS","width":"Largeur","height":"Hauteur","align":"Alignement","left":"Gauche","right":"Droite","center":"Centré","justify":"Justifié","alignLeft":"Aligner à gauche","alignRight":"Aligner à Droite","alignCenter":"Align Center","alignTop":"Haut","alignMiddle":"Milieu","alignBottom":"Bas","alignNone":"None","invalidValue":"Valeur invalide.","invalidHeight":"La hauteur doit être un nombre.","invalidWidth":"La largeur doit être un nombre.","invalidLength":"Value specified for the \"%1\" field must be a positive number with or without a valid measurement unit (%2).","invalidCssLength":"La valeur spécifiée pour le champ \"%1\" doit être un nombre positif avec ou sans unité de mesure CSS valide (px, %, in, cm, mm, em, ex, pt, ou pc).","invalidHtmlLength":"La valeur spécifiée pour le champ \"%1\" doit être un nombre positif avec ou sans unité de mesure HTML valide (px ou %).","invalidInlineStyle":"La valeur spécifiée pour le style intégré doit être composée d'un ou plusieurs couples de valeur au format \"nom : valeur\", separés par des points-virgules.","cssLengthTooltip":"Entrer un nombre pour la valeur en pixel ou un nombre avec une unité CSS valide (px, %, in, cm, mm, em, ex, pt, ou pc).","unavailable":"%1<span class=\"cke_accessibility\">, indisponible</span>","keyboard":{"8":"Backspace","13":"Enter","16":"Shift","17":"Ctrl","18":"Alt","32":"Space","35":"End","36":"Home","46":"Delete","112":"F1","113":"F2","114":"F3","115":"F4","116":"F5","117":"F6","118":"F7","119":"F8","120":"F9","121":"F10","122":"F11","123":"F12","124":"F13","125":"F14","126":"F15","127":"F16","128":"F17","129":"F18","130":"F19","131":"F20","132":"F21","133":"F22","134":"F23","135":"F24","224":"Command"},"keyboardShortcut":"Keyboard shortcut","optionDefault":"Default"},"about":{"copy":"Copyright &copy; $1. Tous droits réservés.","dlgTitle":"À propos de CKEditor 4","moreInfo":"Pour les informations de licence, consulter notre site internet:"},"basicstyles":{"bold":"Gras","italic":"Italique","strike":"Barré","subscript":"Indice","superscript":"Exposant","underline":"Souligné"},"bidi":{"ltr":"Direction du texte de gauche à droite","rtl":"Direction du texte de droite à gauche"},"blockquote":{"toolbar":"Citation"},"notification":{"closed":"Notification closed."},"toolbar":{"toolbarCollapse":"Enrouler la barre d'outils","toolbarExpand":"Dérouler la barre d'outils","toolbarGroups":{"document":"Document","clipboard":"Presse papier/Annuler","editing":"Édition","forms":"Formulaires","basicstyles":"Styles de base","paragraph":"Paragraphe","links":"Liens","insert":"Insérer","styles":"Styles","colors":"Couleurs","tools":"Outils"},"toolbars":"Barre d'outils de l'éditeur"},"clipboard":{"copy":"Copier","copyError":"Les paramètres de sécurité de votre navigateur empêchent l'éditeur de copier automatiquement vos données. Veuillez utiliser les équivalents claviers (Ctrl/Cmd+C).","cut":"Couper","cutError":"Les paramètres de sécurité de votre navigateur empêchent l'éditeur de couper automatiquement vos données. Veuillez utiliser les équivalents claviers (Ctrl/Cmd+X).","paste":"Coller","pasteNotification":"Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.","pasteArea":"Coller la zone","pasteMsg":"Paste your content inside the area below and press OK."},"colorbutton":{"auto":"Automatique","bgColorTitle":"Couleur de fond","colors":{"000":"Noir","800000":"Marron","8B4513":"Brun foncé","2F4F4F":"Gris ardoise foncé","008080":"Sarcelle","000080":"Marine","4B0082":"Indigo","696969":"Gris foncé","B22222":"Rouge brique","A52A2A":"Brun","DAA520":"Doré","006400":"Vert foncé","40E0D0":"Turquoise","0000CD":"Bleu","800080":"Mauve","808080":"Gris","F00":"Rouge","FF8C00":"Orange foncé","FFD700":"Or","008000":"Vert","0FF":"Cyan","00F":"Bleu","EE82EE":"Violet","A9A9A9":"Gris pâle","FFA07A":"Saumon clair","FFA500":"Orange","FFFF00":"Jaune","00FF00":"Vert lime","AFEEEE":"Turquoise pâle","ADD8E6":"Bleu pâle","DDA0DD":"Prune","D3D3D3":"Gris pâle","FFF0F5":"Bleu lavande","FAEBD7":"Blanc antique","FFFFE0":"Jaune pâle","F0FFF0":"Miel doré","F0FFFF":"Azure","F0F8FF":"Bleu alice","E6E6FA":"Lavande","FFF":"Blanc","1ABC9C":"Strong Cyan","2ECC71":"Emerald","3498DB":"Bright Blue","9B59B6":"Amethyst","4E5F70":"Grayish Blue","F1C40F":"Vivid Yellow","16A085":"Dark Cyan","27AE60":"Dark Emerald","2980B9":"Strong Blue","8E44AD":"Dark Violet","2C3E50":"Desaturated Blue","F39C12":"Orange","E67E22":"Carrot","E74C3C":"Pale Red","ECF0F1":"Bright Silver","95A5A6":"Light Grayish Cyan","DDD":"Light Gray","D35400":"Pumpkin","C0392B":"Strong Red","BDC3C7":"Silver","7F8C8D":"Grayish Cyan","999":"Dark Gray"},"more":"Plus de couleurs...","panelTitle":"Couleurs","textColorTitle":"Couleur de texte"},"colordialog":{"clear":"Effacer","highlight":"Surligner","options":"Options de couleur","selected":"Couleur sélectionnée","title":"Choisir une couleur"},"templates":{"button":"Modèles","emptyListMsg":"(Aucun modèle disponible)","insertOption":"Remplacer tout le contenu actuel","options":"Options de modèles","selectPromptMsg":"Sélectionner le modèle à ouvrir dans l'éditeur","title":"Modèles de contenu"},"contextmenu":{"options":"Options du menu contextuel"},"copyformatting":{"label":"Copy Formatting","notification":{"copied":"Formatting copied","applied":"Formatting applied","canceled":"Formatting canceled","failed":"Formatting failed. You cannot apply styles without copying them first."}},"div":{"IdInputLabel":"ID","advisoryTitleInputLabel":"Titre","cssClassInputLabel":"Classes CSS","edit":"Modifier le DIV","inlineStyleInputLabel":"Style en ligne","langDirLTRLabel":"De gauche à droite (LTR)","langDirLabel":"Sens d'écriture","langDirRTLLabel":"De droite à gauche (RTL)","languageCodeInputLabel":"Code de langue","remove":"Supprimer le DIV","styleSelectLabel":"Style","title":"Créer un DIV","toolbar":"Créer un DIV"},"elementspath":{"eleLabel":"Chemin d'éléments","eleTitle":"element %1"},"exportpdf":{"documentReady":"Document is ready!","error":"Error occurred.","processingDocument":"Processing PDF document...","toolbar":"Export to PDF"},"filetools":{"loadError":"Error occurred during file read.","networkError":"Network error occurred during file upload.","httpError404":"HTTP error occurred during file upload (404: File not found).","httpError403":"HTTP error occurred during file upload (403: Forbidden).","httpError":"HTTP error occurred during file upload (error status: %1).","noUrlError":"Upload URL is not defined.","responseError":"Incorrect server response."},"find":{"find":"Rechercher","findOptions":"Options de recherche","findWhat":"Rechercher:","matchCase":"Respecter la casse","matchCyclic":"Recherche cyclique","matchWord":"Mot entier","notFoundMsg":"Le texte indiqué est introuvable.","replace":"Remplacer","replaceAll":"Tout remplacer","replaceSuccessMsg":"%1 remplacements.","replaceWith":"Remplacer par:","title":"Rechercher et remplacer"},"fakeobjects":{"anchor":"Ancre","flash":"Animation Flash","hiddenfield":"Champ caché","iframe":"IFrame","unknown":"Objet inconnu"},"flash":{"access":"Accès au script","accessAlways":"Toujours","accessNever":"Jamais","accessSameDomain":"Même domaine","alignAbsBottom":"Bas absolu","alignAbsMiddle":"Milieu absolu","alignBaseline":"Bas du texte","alignTextTop":"Haut du texte","bgcolor":"Couleur de fond","chkFull":"Permettre le plein-écran","chkLoop":"Boucle","chkMenu":"Activer le menu Flash","chkPlay":"Lecture automatique","flashvars":"Variables pour Flash","hSpace":"Espacement horizontal","properties":"Propriétés de l'animation Flash","propertiesTab":"Propriétés","quality":"Qualité","qualityAutoHigh":"Haute auto","qualityAutoLow":"Basse auto","qualityBest":"Meilleur","qualityHigh":"Haute","qualityLow":"Basse","qualityMedium":"Moyenne","scale":"Échelle","scaleAll":"Afficher tout","scaleFit":"Ajuster aux dimensions","scaleNoBorder":"Sans bordure","title":"Propriétés de l'animation Flash","vSpace":"Espacement vertical","validateHSpace":"L'espacement horizontal doit être un entier.","validateSrc":"Veuillez saisir l'URL","validateVSpace":"L'espacement vertical doit être un entier.","windowMode":"Mode de fenêtre","windowModeOpaque":"Opaque","windowModeTransparent":"Transparent","windowModeWindow":"Fenêtre"},"font":{"fontSize":{"label":"Taille","voiceLabel":"Taille","panelTitle":"Taille"},"label":"Police","panelTitle":"Police","voiceLabel":"Police"},"forms":{"button":{"title":"Propriétés du bouton","text":"Texte (Valeur)","type":"Type","typeBtn":"Bouton","typeSbm":"Soumettre","typeRst":"Réinitialiser"},"checkboxAndRadio":{"checkboxTitle":"Propriétés de la case à cocher","radioTitle":"Propriétés du bouton radio","value":"Valeur","selected":"Sélectionné","required":"Required"},"form":{"title":"Propriétés du formulaire","menu":"Propriétés du formulaire","action":"Action","method":"Méthode","encoding":"Encodage"},"hidden":{"title":"Propriétés du champ caché","name":"Nom","value":"Valeur"},"select":{"title":"Propriétés du champ de sélection","selectInfo":"Info","opAvail":"Options disponibles","value":"Valeur","size":"Taille","lines":"lignes","chkMulti":"Permettre les sélections multiples","required":"Required","opText":"Texte","opValue":"Valeur","btnAdd":"Ajouter","btnModify":"Modifier","btnUp":"Monter","btnDown":"Descendre","btnSetValue":"Valeur sélectionnée","btnDelete":"Supprimer"},"textarea":{"title":"Propriétés de la zone de texte","cols":"Colonnes","rows":"Lignes"},"textfield":{"title":"Propriétés du champ texte","name":"Nom","value":"Valeur","charWidth":"Largeur de caractères","maxChars":"Nombre maximum de caractères","required":"Required","type":"Type","typeText":"Texte","typePass":"Mot de passe","typeEmail":"Courriel","typeSearch":"Recherche","typeTel":"Numéro de téléphone","typeUrl":"URL"}},"format":{"label":"Format","panelTitle":"Format de paragraphe","tag_address":"Adresse","tag_div":"Normal (DIV)","tag_h1":"En-tête 1","tag_h2":"En-tête 2","tag_h3":"En-tête 3","tag_h4":"En-tête 4","tag_h5":"En-tête 5","tag_h6":"En-tête 6","tag_p":"Normal","tag_pre":"Formaté"},"horizontalrule":{"toolbar":"Insérer un séparateur horizontale"},"iframe":{"border":"Afficher la bordure du cadre","noUrl":"Veuillez entre l'URL du IFrame","scrolling":"Activer les barres de défilement","title":"Propriétés du IFrame","toolbar":"IFrame","tabindex":"Remove from tabindex"},"image":{"alt":"Texte alternatif","border":"Bordure","btnUpload":"Envoyer sur le serveur","button2Img":"Désirez-vous transformer l'image sélectionnée en image simple?","hSpace":"Espacement horizontal","img2Button":"Désirez-vous transformer l'image sélectionnée en bouton image?","infoTab":"Informations sur l'image","linkTab":"Lien","lockRatio":"Verrouiller les proportions","menu":"Propriétés de l'image","resetSize":"Taille originale","title":"Propriétés de l'image","titleButton":"Propriétés du bouton image","upload":"Téléverser","urlMissing":"L'URL de la source de l'image est manquant.","vSpace":"Espacement vertical","validateBorder":"La bordure doit être un entier.","validateHSpace":"L'espacement horizontal doit être un entier.","validateVSpace":"L'espacement vertical doit être un entier."},"indent":{"indent":"Augmenter le retrait","outdent":"Diminuer le retrait"},"smiley":{"options":"Options d'émoticônes","title":"Insérer un émoticône","toolbar":"Émoticône"},"language":{"button":"Set language","remove":"Remove language"},"link":{"acccessKey":"Touche d'accessibilité","advanced":"Avancé","advisoryContentType":"Type de contenu","advisoryTitle":"Description","anchor":{"toolbar":"Ancre","menu":"Modifier l'ancre","title":"Propriétés de l'ancre","name":"Nom de l'ancre","errorName":"Veuillez saisir le nom de l'ancre","remove":"Supprimer l'ancre"},"anchorId":"Par ID","anchorName":"Par nom","charset":"Encodage de la cible","cssClasses":"Classes CSS","download":"Force Download","displayText":"Afficher le texte","emailAddress":"Courriel","emailBody":"Corps du message","emailSubject":"Objet du message","id":"ID","info":"Informations sur le lien","langCode":"Code de langue","langDir":"Sens d'écriture","langDirLTR":"De gauche à droite (LTR)","langDirRTL":"De droite à gauche (RTL)","menu":"Modifier le lien","name":"Nom","noAnchors":"(Pas d'ancre disponible dans le document)","noEmail":"Veuillez saisir le courriel","noUrl":"Veuillez saisir l'URL","noTel":"Please type the phone number","other":"<autre>","phoneNumber":"Phone number","popupDependent":"Dépendante (Netscape)","popupFeatures":"Caractéristiques de la fenêtre popup","popupFullScreen":"Plein écran (IE)","popupLeft":"Position de la gauche","popupLocationBar":"Barre d'adresse","popupMenuBar":"Barre de menu","popupResizable":"Redimensionnable","popupScrollBars":"Barres de défilement","popupStatusBar":"Barre d'état","popupToolbar":"Barre d'outils","popupTop":"Position à partir du haut","rel":"Relation","selectAnchor":"Sélectionner une ancre","styles":"Style","tabIndex":"Ordre de tabulation","target":"Destination","targetFrame":"<Cadre>","targetFrameName":"Nom du cadre de destination","targetPopup":"<fenêtre popup>","targetPopupName":"Nom de la fenêtre popup","title":"Lien","toAnchor":"Ancre dans cette page","toEmail":"Courriel","toUrl":"URL","toPhone":"Phone","toolbar":"Lien","type":"Type de lien","unlink":"Supprimer le lien","upload":"Téléverser"},"list":{"bulletedlist":"Liste à puces","numberedlist":"Liste numérotée"},"liststyle":{"bulletedTitle":"Propriété de liste à puce","circle":"Cercle","decimal":"Décimal (1, 2, 3, etc.)","disc":"Disque","lowerAlpha":"Alphabétique minuscule (a, b, c, d, e, etc.)","lowerRoman":"Romain minuscule (i, ii, iii, iv, v, etc.)","none":"Aucun","notset":"<non défini>","numberedTitle":"Propriété de la liste numérotée","square":"Carré","start":"Début","type":"Type","upperAlpha":"Alphabétique majuscule (A, B, C, D, E, etc.)","upperRoman":"Romain Majuscule (I, II, III, IV, V, etc.)","validateStartNumber":"Le numéro de début de liste doit être un entier."},"magicline":{"title":"Insérer le paragraphe ici"},"maximize":{"maximize":"Maximizer","minimize":"Minimizer"},"newpage":{"toolbar":"Nouvelle page"},"pagebreak":{"alt":"Saut de page","toolbar":"Insérer un saut de page à l'impression"},"pastetext":{"button":"Coller comme texte","pasteNotification":"Press %1 to paste. Your browser doesn‘t support pasting with the toolbar button or context menu option.","title":"Coller comme texte"},"pastefromword":{"confirmCleanup":"Le texte que vous tentez de coller semble provenir de Word.  Désirez vous le nettoyer avant de coller?","error":"Il n'a pas été possible de nettoyer les données collées du à une erreur interne","title":"Coller de Word","toolbar":"Coller de Word"},"preview":{"preview":"Prévisualiser"},"print":{"toolbar":"Imprimer"},"removeformat":{"toolbar":"Supprimer le formatage"},"save":{"toolbar":"Sauvegarder"},"selectall":{"toolbar":"Sélectionner tout"},"showblocks":{"toolbar":"Afficher les blocs"},"sourcearea":{"toolbar":"Source"},"specialchar":{"options":"Option des caractères spéciaux","title":"Sélectionner un caractère spécial","toolbar":"Insérer un caractère spécial"},"scayt":{"btn_about":"About SCAYT","btn_dictionaries":"Dictionaries","btn_disable":"Disable SCAYT","btn_enable":"Enable SCAYT","btn_langs":"Languages","btn_options":"Options","text_title":"Spell Check As You Type"},"stylescombo":{"label":"Styles","panelTitle":"Styles de formattage","panelTitle1":"Styles de block","panelTitle2":"Styles en ligne","panelTitle3":"Styles d'objet"},"table":{"border":"Taille de la bordure","caption":"Titre","cell":{"menu":"Cellule","insertBefore":"Insérer une cellule avant","insertAfter":"Insérer une cellule après","deleteCell":"Supprimer des cellules","merge":"Fusionner les cellules","mergeRight":"Fusionner à droite","mergeDown":"Fusionner en bas","splitHorizontal":"Scinder la cellule horizontalement","splitVertical":"Scinder la cellule verticalement","title":"Propriétés de la cellule","cellType":"Type de cellule","rowSpan":"Fusion de lignes","colSpan":"Fusion de colonnes","wordWrap":"Retour à la ligne","hAlign":"Alignement horizontal","vAlign":"Alignement vertical","alignBaseline":"Bas du texte","bgColor":"Couleur d'arrière plan","borderColor":"Couleur de bordure","data":"Données","header":"En-tête","yes":"Oui","no":"Non","invalidWidth":"La largeur de cellule doit être un nombre.","invalidHeight":"La hauteur de cellule doit être un nombre.","invalidRowSpan":"La fusion de lignes doit être un nombre entier.","invalidColSpan":"La fusion de colonnes doit être un nombre entier.","chooseColor":"Sélectionner"},"cellPad":"Marge interne des cellules","cellSpace":"Espacement des cellules","column":{"menu":"Colonne","insertBefore":"Insérer une colonne avant","insertAfter":"Insérer une colonne après","deleteColumn":"Supprimer des colonnes"},"columns":"Colonnes","deleteTable":"Supprimer le tableau","headers":"En-têtes","headersBoth":"Les deux.","headersColumn":"Première colonne","headersNone":"Aucun","headersRow":"Première ligne","heightUnit":"height unit","invalidBorder":"La taille de bordure doit être un nombre.","invalidCellPadding":"La marge interne des cellules doit être un nombre positif.","invalidCellSpacing":"L'espacement des cellules doit être un nombre positif.","invalidCols":"Le nombre de colonnes doit être supérieur à 0.","invalidHeight":"La hauteur du tableau doit être un nombre.","invalidRows":"Le nombre de lignes doit être supérieur à 0.","invalidWidth":"La largeur du tableau doit être un nombre.","menu":"Propriétés du tableau","row":{"menu":"Ligne","insertBefore":"Insérer une ligne avant","insertAfter":"Insérer une ligne après","deleteRow":"Supprimer des lignes"},"rows":"Lignes","summary":"Résumé","title":"Propriétés du tableau","toolbar":"Tableau","widthPc":"pourcentage","widthPx":"pixels","widthUnit":"unité de largeur"},"undo":{"redo":"Refaire","undo":"Annuler"},"widget":{"move":"Click and drag to move","label":"%1 widget"},"uploadwidget":{"abort":"Upload aborted by the user.","doneOne":"File successfully uploaded.","doneMany":"Successfully uploaded %1 files.","uploadOne":"Uploading file ({percentage}%)...","uploadMany":"Uploading files, {current} of {max} done ({percentage}%)..."}};