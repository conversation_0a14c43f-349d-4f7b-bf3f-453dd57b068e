# TrainingServlet 统一查询方法说明

## 概述
基于现有的 `actionForSelectByDay`、`actionForSelectByMonth` 和 `actionForSelectByYear` 方法，创建了一个统一的查询方法 `actionForSelectUnified`，可以根据不同的类型参数进行按小时、按天、按月的数据查询。

## 新增方法

### 1. actionForSelectUnified() - 统一查询方法

**功能**: 根据传入的类型参数，统一处理按小时、按天、按月的查询请求

**请求参数**:
- `type` (Integer): 查询类型
  - `1`: 按小时查询
  - `2`: 按天查询  
  - `3`: 按月查询
- `startTime` (String): 开始时间
- `endTime` (String): 结束时间

**时间格式要求**:
- 按小时 (type=1): `yyyyMMdd HH` (如: `20250717 08`)
- 按天 (type=2): `yyyyMMdd` (如: `20250717`)
- 按月 (type=3): `yyyyMM` (如: `202507`)

**请求示例**:

```json
// 按小时查询
{
    "type": 1,
    "startTime": "20250717 08",
    "endTime": "20250717 18"
}

// 按天查询
{
    "type": 2,
    "startTime": "20250701",
    "endTime": "20250717"
}

// 按月查询
{
    "type": 3,
    "startTime": "202501",
    "endTime": "202507"
}
```

**返回数据格式**:
```json
{
    "success": true,
    "data": [
        {
            "DATE_ID": "时间标识",
            "VALUE": "实际值",
            "TRAINING_VALUE": "预测值",
            "ROLE": "误差率"
        }
    ]
}
```

## 辅助方法

### 1. queryByHour() - 按小时查询
- 处理按小时的数据查询逻辑
- 支持同一天内的小时范围查询
- 暂不支持跨天查询

### 2. queryByDay() - 按天查询  
- 处理按天的数据查询逻辑
- 支持日期范围查询
- 基于原有 `actionForSelectByMonth` 的逻辑

### 3. queryByMonth() - 按月查询
- 处理按月的数据查询逻辑
- 支持月份范围查询
- 基于原有 `actionForSelectByYear` 的逻辑

## 测试方法

### actionForTestUnified() - 测试方法
提供了一个测试方法来验证统一查询功能，返回使用示例和说明。

## 使用建议

1. **参数验证**: 方法内置了完整的参数验证，会检查 type 参数的有效性和时间参数的完整性
2. **错误处理**: 包含完善的异常处理机制，会返回具体的错误信息
3. **日志记录**: 所有操作都会记录详细的日志信息，便于调试和监控
4. **缓存支持**: 继承了原有方法的缓存机制，提高查询性能

## 兼容性说明

- 新增的统一方法不会影响现有的三个独立方法
- 可以根据需要选择使用统一方法或原有的独立方法
- 所有方法共享相同的数据源和查询逻辑

## 调用方式

通过 HTTP POST 请求调用:
```
POST /servlet/training/actionForSelectUnified
Content-Type: application/json

{
    "type": 2,
    "startTime": "20250701", 
    "endTime": "20250717"
}
```
