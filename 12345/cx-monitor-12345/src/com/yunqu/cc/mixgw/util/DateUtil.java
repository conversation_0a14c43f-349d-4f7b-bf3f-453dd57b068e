package com.yunqu.cc.mixgw.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.calendar.EasyDate;


public class DateUtil {
	/**
	 * 日期格式：yyyy-MM-dd HH:mm:ss
	 */
	public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	/**
	 * 日期格式：yyyy-MM-dd
	 */
	public static String TIME_FORMAT_YMD = "yyyy-MM-dd";
	public static SimpleDateFormat DEFAULT_FORMAT = new SimpleDateFormat(TIME_FORMAT);
//	private static Logger logger = CommonLogger.logger;
	
	//用于控制并发访问，对于DEFAULT_FORMAT，在多线程访问下，可能会出现错误
	public static byte[] lock = new byte[0];
	
	/**
	 * 年月日缺省分隔符
	 */
	private static char DAY_DELIMITER = '-';
	/**
	 * 根据格式获取当前时间
	 * @param format
	 * @return
	 */
	public static String getCurrentDateStr(String format){
		SimpleDateFormat fm = new SimpleDateFormat(format);
		return fm.format(new Date());
	}
	
	/**
	 * 获取当前时间
	 * @param format
	 * @return
	 */
	public static String getCurrentDateStr(){
		String time = "";
		try {
			synchronized (lock) {
				time = DEFAULT_FORMAT.format(new Date());
			}
		} catch (Exception e) {
			time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		}
		return time;
	}

	/**
	 * 获取当前月的第一天
	 * @param format
	 * @return
	 */
	public static String getCurrMonthBeginDay(String format) {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DATE, 1);
		if(StringUtils.isBlank(format)){
			synchronized (lock) {
				return DEFAULT_FORMAT.format(cal.getTime());
			}
		}
		SimpleDateFormat df = new SimpleDateFormat(format);
		return df.format(cal.getTime());
	}

	/**
	 * 
	 * @param format
	 * @return
	 */
	public static String getCurrMonthEndDay(String format) {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DATE, cal.getActualMaximum(Calendar.DATE));
		if(StringUtils.isBlank(format)){
			synchronized (lock) {
				return DEFAULT_FORMAT.format(cal.getTime());
			}
		}
		SimpleDateFormat df = new SimpleDateFormat(format);
		return df.format(cal.getTime());
	}
	
	/**
	 * add month
	 * @param format
	 * @param beginTime
	 * @param i
	 * @return
	 */
	public static String addMonth(String format, String beginTime, int i) {
		return addTime(format,beginTime,Calendar.MONTH, i);
	}
	
	/**
	 * addDay
	 * @param format
	 * @param beginTime
	 * @param i
	 * @return
	 */
	public static String addDay(String format, String beginTime, int i) {
		return addTime(format,beginTime,Calendar.DAY_OF_MONTH, i);
	}
	/**
	 * add hour
	 * @param format
	 * @param beginTime
	 * @param i
	 * @return
	 */
	public static String addHour(String format, String beginTime, int i) {
		return addTime(format,beginTime,Calendar.HOUR_OF_DAY, i);
	}
	
	/**
	 * addMinute
	 * @param format
	 * @param beginTime
	 * @param i
	 * @return
	 */
	public static String addMinute(String format, String beginTime, int i) {
		return addTime(format,beginTime,Calendar.MINUTE, i);
	}
	/**
	 * add second
	 * @param format
	 * @param beginTime
	 * @param i
	 * @return
	 */
	public static String addSecond(String format, String beginTime, int i) {
		return addTime(format,beginTime,Calendar.SECOND, i);
	}
	/**
	 * 添加时间的某个部分的数据
	 * @param format
	 * @param beginTime
	 * @param timeType  要添加的时间部分，年、月、日�?,如Calendar.SECOND
	 * @param i
	 * @return
	 */
	public static String addTime(String format, String beginTime,int timeType, int i) {
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try {
			Date begin = sdf.parse(beginTime);
			Calendar cal = Calendar.getInstance();
			cal.setTime(begin);
			cal.add(timeType, i);
			return sdf.format(cal.getTime());
		} catch (Exception e) {
//			logger.error("[DateUtil.addTime]throw exception:"+e.getMessage(),e);
		}
		return beginTime;
	}
	
	/**
	 * 根据格式转换时间
	 * @param timeFormat
	 * @param time
	 * @return
	 */
	public static Date getDate(String timeFormat, String time) {
		SimpleDateFormat format;
		if(StringUtils.isBlank(timeFormat)){
			format = DEFAULT_FORMAT;
		}else{
			format = new SimpleDateFormat(timeFormat);
		}
		try {
			synchronized (lock) {
				return format.parse(time);
			}
		} catch (ParseException e) {
//			logger.error("[DateUtil.getDate]throw exception:"+e.getMessage(),e);
		}
		return null;
	}
	public static Date getDate(String time) {
		String timeFormat="yyyy-MM-dd HH:mm:ss";
		SimpleDateFormat format;
		if(StringUtils.isBlank(timeFormat)){
			format = DEFAULT_FORMAT;
		}else{
			format = new SimpleDateFormat(timeFormat);
		}
		try {
			synchronized (lock) {
				return format.parse(time);
			}
		} catch (ParseException e) {
//			logger.error("[DateUtil.getDate]throw exception:"+e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 获取时间戳
	 * @param timeStr
	 */
	public static Timestamp getTimestamp(String timeStr){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if(10 == timeStr.length()){
			timeStr = timeStr + " 00:00:00";
		}
		Date date = null;
		Timestamp ts = null;
		try{
		    date = df.parse(timeStr);
		}catch(Exception e){
			ts = new Timestamp(System.currentTimeMillis());
			return ts;
		}
		ts = new Timestamp(date.getTime());
		return ts;
		
	}

	/**
	 * 与当前时间比较相差的分钟数 yyyy-MM-dd HH:mm:ss
	 * @param time
	 * @return
	 */
	public static int bwMinsWithCurrdate(String time) {
		if(StringUtils.isNotBlank(time)){
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				Date date = new Date();
				Date ctime = df.parse(time);
				long bw = date.getTime()-ctime.getTime();
				return (int)(bw/1000/60);
			} catch (Exception e) {
			}
		}
		return 0;
	}
	
	public static int dayForWeek(String pTime) throws Exception {
		  SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		  Calendar c = Calendar.getInstance();
		  c.setTime(format.parse(pTime));
		  int dayForWeek = 0;
		  if(c.get(Calendar.DAY_OF_WEEK) == 1){
		   dayForWeek = 7;
		  }else{
		   dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
		  }
		  return dayForWeek;
		 }
	
   public static long getDaysBetween(String time1,String time2) throws ParseException {
	   SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
	   Date date1 = format.parse(time1);
	   Date date2 = format.parse(time2);
	   return ((date2.getTime()-date1.getTime())/ 1000 / 60 / 60 / 24);
   }
   
   /**
	 * 按格式获取两个时间之间的时间数组
	 * @param calendarType 
	 * */
	public static Date[] getDateArrays(String startStr,String endStr ,int calendarType){
		ArrayList<Date> ret = new ArrayList<Date>();
		Date[] dates=null;
		try {
			Date start = new SimpleDateFormat("yyyy-MM-dd").parse(startStr);
			Date end=new SimpleDateFormat("yyyy-MM-dd").parse(endStr);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(start);
			Date tmpDate = calendar.getTime();
			long endTime = end.getTime();
			while(tmpDate.before(end)||tmpDate.getTime() == endTime){
				ret.add(calendar.getTime());
				calendar.add(calendarType, 1);
				tmpDate = calendar.getTime();
			}		
			dates = new Date[ret.size()];
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
		return ret.toArray(dates);		
	}
	
	public static String getDateStr(Date date){
		if(date==null){
			return "";
		}
		SimpleDateFormat sdf=new SimpleDateFormat(TIME_FORMAT);  
		String str=sdf.format(date);
		return str;
	}
	
	public static String getDateTimeStr(Date date){
		if(date==null){
			return "";
		}
		SimpleDateFormat sdf=new SimpleDateFormat(TIME_FORMAT);  
		String str=sdf.format(date);
		return str;
	}

	public static int bwDays(String startDate, String endDate, String fmt) {
		try {
			SimpleDateFormat format = new SimpleDateFormat(fmt);
			Date date1 = format.parse(startDate);
			Date date2 = format.parse(endDate);
			return (int) ((date2.getTime()-date1.getTime())/ 1000 / 60 / 60 / 24);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	/**
	 * 比较两个时间间相差的秒
	 * @param destETime
	 * @param srcSTime
	 * @param fmt
	 * @return
	 */
	public static int bwSeconds(String destETime, String srcSTime,String fmt) {
		try {
			SimpleDateFormat format = new SimpleDateFormat(fmt);
			Date date1 = format.parse(destETime);
			Date date2 = format.parse(srcSTime);
			return (int) ((date2.getTime()-date1.getTime())/ 1000);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
	}

	/**
	 * 对已耗时间进行格式化展示
	 * @param durationInMillis 毫秒
	 * @return 
	 */
	public static String calDuration(Long durationInMillis) {
		if(durationInMillis<=0){
			return "";
		}
		long seconds = durationInMillis/1000;
		long minutes = seconds/60;
		long hours = minutes/60;
		long days = hours/24;
		int i = 0;
		String str = "";
		if(days>0 && i<2){
			str += days + "天";
			i++;
		}
		if(hours>0 && i<2){
			str += hours%24 + "小时";
			i++;
		}
		if(minutes>0 && i<2){
			str += minutes%60 + "分钟";
			i++;
		}
		if(seconds>0 && i<2){
			str += seconds%60 + "秒";
			i++;
		}
		return str;
	}
	
    /*
     * 毫秒转化时分秒毫秒
     */
    public static String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;
 
        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;
        
        StringBuffer sb = new StringBuffer();
        if(day > 0) {
            sb.append(day+"天");
        }
        if(hour > 0) {
            sb.append(hour+"小时");
        }
        if(minute > 0) {
            sb.append(minute+"分");
        }
        if(second > 0) {
            sb.append(second+"秒");
        }
        if(milliSecond > 0) {
            sb.append(milliSecond+"毫秒");
        }
        return sb.toString();
    }
    
    /**
     *  秒转化时分秒毫秒
     * 返回 00:00:00.0
     * @param ms  毫秒
     * @return
     */
    public static String formatTimeBySeconds(Long s) {
       return formatTimeByMs(s*1000);
    }
    
    /**
     *  毫秒转化  天 时:分:秒:毫秒
     * 返回 00 00:00:00.0
     * @param ms  毫秒
     * @return
     */
    public static String formatTimeByMs(Long ms) {
    	if(ms<=0){
    		return "00:00:00.0";
    	}
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;
 
        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;
        
        StringBuffer sb = new StringBuffer();
        if(day > 0) {
            sb.append( (day<10 ? "0"+day: day) +" ");
        }
        
        if(hour > 0) {
            sb.append( (hour<10 ? "0"+hour: hour) +":");
        }else{
        	sb.append( "00:");
        }
        if(minute > 0) {
            sb.append( (minute<10 ? "0"+minute: minute)+":");
        }else{
        	sb.append( "00:");
        }
        if(second > 0) {
            sb.append( (second<10 ? "0"+second: second)+".");
        }else{
        	sb.append( "00.");
        }
        if(milliSecond > 0) {
            sb.append(  milliSecond<1 ? "0" : milliSecond );
        }else{
        	sb.append("0");
        }
        return sb.toString();
    }
    
    
    /**
     *  秒转化  时:分:秒:毫秒(去掉毫秒)
     * 返回 00:00:00.0
     * @param ms  毫秒
     * @return
     */
    public static String formatTimeHMSSBySeconds(Long s) {
       return formatTimeHMSSByMs(s*1000);
    }
    
    /**
     *  毫秒转化  时:分:秒:毫秒(去掉毫秒)
     * 返回 00 00:00:00.0
     * @param ms  毫秒
     * @return
     */
    public static String formatTimeHMSSByMs(Long ms) {
    	if(ms<=0){
    		return "00:00:00";
    	}
    	if(ms<=1000){
    		return "00:00:01";
    	}
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
 
        Long hour = ms / hh;
        Long minute = (ms -  hour * hh) / mi;
        Long second = (ms - hour * hh - minute * mi) / ss;
//        Long milliSecond = ms - hour * hh - minute * mi - second * ss;
        
        StringBuffer sb = new StringBuffer();
      
        if(hour > 0) {
            sb.append( (hour<10 ? "0"+hour: hour) +":");
        }else{
        	sb.append( "00:");
        }
        if(minute > 0) {
            sb.append( (minute<10 ? "0"+minute: minute)+":");
        }else{
        	sb.append( "00:");
        }
        if(second > 0) {
            sb.append( (second<10 ? "0"+second: second));
        }else{
        	sb.append( "00");
        }
//        if(milliSecond > 0) {
//            sb.append(  milliSecond<1 ? "0" : milliSecond );
//        }else{
//        	sb.append("0");
//        }
        return sb.toString();
    }
    
    
    /**
     *  毫秒转化  时:分:秒:毫秒(去掉毫秒)
     * 返回 00 00:00:00.0
     * @param ms  毫秒
     * @return
     */
    public static String formatTimeSSByMs(Long ms) {
    	if(ms<=0){
    		return "00:00";
    	}
    	if(ms<=1000){
    		return "00:01";
    	}
        Integer ss = 1000;
        Integer mi = ss * 60;
 
        Long minute = ms / mi;
        Long second = (ms  - minute * mi) / ss;
        
        StringBuffer sb = new StringBuffer();
        
        if(minute > 0) {
            sb.append( (minute<10 ? "0"+minute: minute)+":");
        }else{
        	sb.append( "00:");
        }
        if(second > 0) {
            sb.append( (second<10 ? "0"+second: second));
        }else{
        	sb.append( "00");
        }
//        if(milliSecond > 0) {
//            sb.append(  milliSecond<1 ? "0" : milliSecond );
//        }else{
//        	sb.append("0");
//        }
        return sb.toString();
    }


	/**
	 * 计算某个日期，与当前日期之间相差的时长，�? *�?*秒格式返回；
	 * 日期格式为：yyyy-MM-dd HH:mm:ss
	 * @param time
	 * @return
	 */
	public static String calDuration(String time) {
		if(StringUtils.isBlank(time)){
			return "";
		}
		try {
			Date date = null;
			synchronized (lock) {
				date = DEFAULT_FORMAT.parse(time);
			}
			Date now = new Date();
			return calDuration(now.getTime() -  date.getTime());
		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(DateUtil.class)+" time="+time+", throw exceptioin:"+e.getMessage(),e);
		}
		return null;
	}
	
	/**
	 * 计算2个日期之间相差的时长，以 *�?*秒格式返回；
	 * 日期格式为：yyyy-MM-dd HH:mm:ss
	 * @param startTime  �?始时�?
	 * @param endTime    结束时间
	 * @return
	 */
	public static String calDuration(String startTime,String endTime) {
		if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
			return "";
		}
		try {
			synchronized (lock) {
				Date start = DEFAULT_FORMAT.parse(startTime);
				Date end = DEFAULT_FORMAT.parse(endTime);
				return calDuration(end.getTime() -  start.getTime());
			}
		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(DateUtil.class)+" throw exceptioin:"+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 计算某个日期，与当前日期之间，相差的毫秒�?
	 * 日期格式为：yyyy-MM-dd HH:mm:ss
	 * @param time
	 * @return
	 */
	public static long calDurationMills(String time) {
		if(StringUtils.isBlank(time)){
			return -1;
		}
		try {
			Date date = null;
			synchronized (lock) {
				date = DEFAULT_FORMAT.parse(time);
			}
			Date now = new Date();
			return now.getTime() -  date.getTime();
		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(DateUtil.class)+" throw exceptioin:"+e.getMessage(),e);
		}
		return -1;
	}
	
	/**
	 * 计算两个日期之间，相差的毫秒数
	 * @param start 开始日期
	 * @param end   结束日期
	 * @return
	 */
	public static long calDurationMills(Date start,Date end) {
		if(start==null || end==null){
			return -1;
		}
		try {
			return end.getTime() -  start.getTime();
		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(DateUtil.class)+" throw exceptioin:"+e.getMessage(),e);
		}
		return -1;
	}
	
	/**
	 * 计算2个日期之间，相差的毫秒数
	 * 日期格式为：yyyy-MM-dd HH:mm:ss
	 * @param time
	 * @return
	 */
	public static long calDurationMills(String startTime,String endTime) {
		if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) ){
			return -1;
		}
		try {
			synchronized (lock) {
				Date start = DEFAULT_FORMAT.parse(startTime);
				Date end = DEFAULT_FORMAT.parse(endTime);
				return end.getTime() -  start.getTime();
			}
			
		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(DateUtil.class)+" throw exceptioin:"+e.getMessage(),e);
		}
		return -1;
	}

	/**
	 * 对某个日期，加上指定的分钟数
	 * @param date  日期
	 * @param miniuts  分钟数，可正可负
	 * @return
	 */
	public static Date addMinute(Date date, int miniuts) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MINUTE, miniuts);
		return cal.getTime();
	}

	/**
	 * 计算两个时间之间相差的小时数，日期格式为 yyyy-MM-dd hh24:mm:ss
	 * @param startTime
	 * @param endTime
	 * @return 返回两个时间之间相差的小时数
	 */
	public static double calDurationHours(String startTime, String endTime) {
		try {
			long start = 0;
			long end = 0;
			synchronized (lock) {
				start = DEFAULT_FORMAT.parse(startTime).getTime();
				end = DEFAULT_FORMAT.parse(endTime).getTime();
			}
			long second = (end - start)/1000;
			//计算得到的小时数
			double bw = Double.parseDouble(String.format("%.2f",(float)second/60/60));
			return bw;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	/**
	 * 计算两个时间之间相差的分钟数，日期格式为 yyyy-MM-dd hh24:mm:ss
	 * @param startTime
	 * @param endTime
	 * @return 返回两个时间之间相差的分钟数
	 */
	public static long calDurationMiniuts(String startTime,String endTime){
		try {
			long second = calDurationSeconds(startTime,endTime);
			long miniuts= second/60;
			//计算得到的小时数
			return miniuts;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}
	/**
	 * 计算两个时间之间相差的秒数，日期格式为 yyyy-MM-dd hh24:mm:ss
	 * @param startTime
	 * @param endTime
	 * @return 返回两个时间之间相差的秒数
	 */
	public static long calDurationSeconds(String startTime,String endTime){
		try {
			long start = 0;
			long end = 0;
			synchronized (lock) {
				start = DEFAULT_FORMAT.parse(startTime).getTime();
				end = DEFAULT_FORMAT.parse(endTime).getTime();
			}
			
			long second = (end - start)/1000;
			
			//计算得到的小时数
			return second;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}
	/**
	 * 比较两个日期的大�?
	 * @param btime
	 * @param etime
	 * @param fmt
	 * @return btime>etime 返回1; btime<etime 返回-1; 相等则返�?0
	 */
	public static int compareDate(String btime, String etime, String fmt) {
        try {
        	DateFormat df = new SimpleDateFormat(fmt);
            Date dt1 = df.parse(btime);
            Date dt2 = df.parse(etime);
            if (dt1.getTime() > dt2.getTime()) {
                return 1;
            } else if (dt1.getTime() < dt2.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
	}
	
	/**
	 * get the first day of current week
	 * @param timeFormat  eg: yyyy-MM-dd HH24:mi:ss
	 * @return
	 */
	public static String getFirstDayOfWeek(String timeFormat){
		Calendar cal = Calendar.getInstance();
        int d = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            d = -6;
        } else {
            d = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, d);
        
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        if(StringUtils.isNotBlank(timeFormat)){
        	return new SimpleDateFormat(timeFormat).format(cal.getTime());
        }
        synchronized (lock) {
        	return DEFAULT_FORMAT.format(cal.getTime());
        }
	}
	
	/**
	 * comp two date with seconds
	 * @param firstTime
	 * @param secondTime
	 * @return
	 */
	public static int bwSeconds(String firstTime, String secondTime) {
		if(StringUtils.isNotBlank(firstTime) && StringUtils.isNotBlank(secondTime)){
			SimpleDateFormat df = new SimpleDateFormat(TIME_FORMAT);
			try {
				 synchronized (lock) {
					Date fTime = df.parse(firstTime);
					Date etime = df.parse(secondTime);
					long bw = fTime.getTime()-etime.getTime();
					return (int)(bw/1000);
				 }
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return 0;
	}

	/**
	 * 根据时间戳，返回yyyy-MM-dd HH:mi:ss 格式的时间
	 * @param timeStamp
	 * @return
	 */
	public static String getDateStrByTimeStamp(int timeStamp) {
		if(timeStamp<=0){
			return "";
		}
		//change to date
		Date date = new Date(timeStamp);
		
		return getDateStr(date);
		
	}
	
	/**
	 * 根据时间戳，返回yyyy-MM-dd HH:mi:ss 格式的时间
	 * @param timeStamp
	 * @return
	 */
	public static String getDateStrByTimeStamp(long timeStamp) {
		if(timeStamp<=0){
			return "";
		}
		//change to date
		Date date = new Date(timeStamp);
		
		return getDateStr(date);
		
	}
	
	/**
	 * 根据日期，返回对应的时间戳
	 * @param date  yyyy-MM-dd HH:mi:ss
	 * @return
	 */
	public static long getMillseconds(String date) {
		if(StringUtils.isBlank(date)){
			return -1;
		}
		try {
			//change to date
			Date d = null;
			synchronized (lock) {
				d = DEFAULT_FORMAT.parse(date);
			}
			return d.getTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return -1;
		
	}
	public static Date setStartDateHMS(final Date date) {
		Calendar cal = dateToCalendar(date);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}
	public static Calendar dateToCalendar(final Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar;
	}

	public static String formatDate(Date date) {
		if(date == null){
			return null;
		}
		try {
			String d = null;
			synchronized (lock) {
				d = DEFAULT_FORMAT.format(date);
			}
			return d;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 在指定的日期中按某个时间类型添加指定步长
	 *
	 * @param datetime
	 *            String YYYY-MM-DD HH:MM:SS
	 * @param type
	 *            int YEAR,MONTH,DAY,HOUR,MINUTE,SECOND
	 * @param step
	 *            int 步长 可以是整数或负数
	 * @return String 改变后的日期时间 YYYY-MM-DD HH:MM:SS
	 */
	public static String getPreDateTime(String datetime, int type, int step) {
		Calendar calendar = new GregorianCalendar(Integer.parseInt(datetime
				.substring(0, 4)),
				Integer.parseInt(datetime.substring(5, 7)) - 1, Integer
						.parseInt(datetime.substring(8, 10)), Integer
						.parseInt(datetime.substring(11, 13)), Integer
						.parseInt(datetime.substring(14, 16)), Integer
						.parseInt(datetime.substring(17, 19)));
		calendar.add(type, step);
		return getDateTime(calendar);
	}
	
	/**
	 * 在指定的日期中按某个时间类型添加指定步长
	 *
	 * @param datetime
	 *            YYYY-MM-DD HH:MM:SS
	 * @param type
	 *            YEAR,MONTH,DAY,HOUR,MINUTE,SECOND
	 * @param step
	 *            步长 可以是整数或负数
	 * @return 改变后的日期时间 YYYY-MM-DD
	 */
	public static String getPreDate(String date, int type, int step) {
		Calendar calendar = new GregorianCalendar(Integer.parseInt(date
				.substring(0, 4)), Integer.parseInt(date.substring(5, 7)) - 1,
				Integer.parseInt(date.substring(8, 10)), 0, 0, 0);
		calendar.add(type, step);
		return getDateTime(calendar).substring(0, 10);
	}
	
	/**
	 * 根据日历返回日期时间
	 *
	 * @param Calendar
	 *            日历
	 * @return String YYYY-MM-DD HH:MM:DD
	 */
	private static String getDateTime(Calendar calendar) {
		StringBuffer buf = new StringBuffer("");

		buf.append(calendar.get(Calendar.YEAR));
		buf.append(DAY_DELIMITER);
		buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? calendar
				.get(Calendar.MONTH)
				+ 1 + "" : "0" + (calendar.get(Calendar.MONTH) + 1));
		buf.append(DAY_DELIMITER);
		buf.append(calendar.get(Calendar.DAY_OF_MONTH) > 9 ? calendar
				.get(Calendar.DAY_OF_MONTH)
				+ "" : "0" + calendar.get(Calendar.DAY_OF_MONTH));
		buf.append(" ");
		buf.append(calendar.get(Calendar.HOUR_OF_DAY) > 9 ? calendar
				.get(Calendar.HOUR_OF_DAY)
				+ "" : "0" + calendar.get(Calendar.HOUR_OF_DAY));
		buf.append(":");
		buf.append(calendar.get(Calendar.MINUTE) > 9 ? calendar
				.get(Calendar.MINUTE)
				+ "" : "0" + calendar.get(Calendar.MINUTE));
		buf.append(":");
		buf.append(calendar.get(Calendar.SECOND) > 9 ? calendar
				.get(Calendar.SECOND)
				+ "" : "0" + calendar.get(Calendar.SECOND));
		return buf.toString();
	}
	
	/**
	 * 获取当天是第几分钟
	 * @return
	 */
	public static int getDateMin() {
		// 创建Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 获取当前分钟数
        int minute = calendar.get(Calendar.MINUTE);
        return minute;
	}

	/**
	 * 获取当天是第几分钟
	 * @return
	 */
	public static int getDateSecond() {
		// 创建Calendar实例
		Calendar calendar = Calendar.getInstance();

		// 获取当前分钟数
		int minute = calendar.get(Calendar.SECOND);
		return minute;
	}
	
	/**
	 * 获取当天是第几个小时
	 * @return
	 */
	public static int getDateHour() {
		LocalDateTime now = LocalDateTime.now();
        return now.getHour();
	}
	
	/**
	 * 获取当天是第几个小时的第几分钟
	 * @return
	 */
	public static int getDateHourMin() {
		LocalDateTime now = LocalDateTime.now();
        return now.getMinute();
	}
	
	/**
	 * 获取上个月最后一天
	 * @param fmt
	 * @return
	 */
	public static String getLastMonthEndTime(String fmt) {
		LocalDate today = LocalDate.now();
         
         // 获取上个月最后一天的日期
         LocalDate lastDayOfLastMonth = today.withDayOfMonth(1).minusDays(1);
         
         // 构造日期时间对象，时间设为23:59:59（可根据需求自行调整）
         LocalDateTime dateTime = lastDayOfLastMonth.atTime(LocalTime.of(23, 59, 59));
         
         // 格式化日期时间为指定格式
         DateTimeFormatter formatter = DateTimeFormatter.ofPattern(fmt);
         String formattedDateTime = dateTime.format(formatter);
         
        return formattedDateTime;
	}
	
	/**
	 * 获取上个月第一天
	 * @param fmt
	 * @return
	 */
	public static String getLastMonthStartTime() {
		 LocalDate currentDate = LocalDate.now();
	        LocalDate previousMonth = currentDate.minusMonths(1);
	        LocalDate firstDayOfMonth = previousMonth.withDayOfMonth(1);

	        String firstDay = firstDayOfMonth.toString();
         
        return firstDay;
	}
	/**
	 * 获取此时间为此年的第几周
	 * @param time 日期字符串，格式为yyyy-MM-dd
	 * @return 返回年中的第几周
	 */
	public static int getDateWeek(String time) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		int week = 0;
		try {
			Date parse = simpleDateFormat.parse(time);
			Calendar calendar = Calendar.getInstance();
			// 设置星期一为一周开始的第一天
			calendar.setFirstDayOfWeek(Calendar.MONDAY);
			// 设置在一年中第一个星期所需最少天数
			calendar.setMinimalDaysInFirstWeek(4);
			calendar.setTime(parse);
			week = calendar.get(Calendar.WEEK_OF_YEAR);
			// 处理跨年周数问题
			if (week == 1 && parse.getMonth() == 11) {
				calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 7);
				Date pastDate = calendar.getTime();
				week = calendar.get(Calendar.WEEK_OF_YEAR) + 1;
			}
			return week;
		} catch (Exception e) {
			return 0;
		}
	}
	public static void main(String[] args) {
		System.out.println(DateUtil.bwMinsWithCurrdate(EasyDate.addTime("yyyy-MM-dd", EasyDate.getCurrentDateString("yyyy-MM-dd"), Calendar.DATE, -1)+" 01:00:00"));
	}
	
}
