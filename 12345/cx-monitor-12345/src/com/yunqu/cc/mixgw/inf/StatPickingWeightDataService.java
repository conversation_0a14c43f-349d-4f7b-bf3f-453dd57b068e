package com.yunqu.cc.mixgw.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.job.ThreadMgr;
import com.yunqu.cc.mixgw.job.item.BigMonitorCacheDataTaskV2;
import com.yunqu.cc.mixgw.job.item.BigMonitorCacheDataTaskV3;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * 统计大屏上的按天、按周和按月的剔重号码呼入数量
 */
public class StatPickingWeightDataService extends IService{
	
	private static Logger logger = CommonLogger.getLogger("picking");
	
	private EasyCache cache = CacheManager.getCache();
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" << 收到调用请求:"+json);
		
		JSONObject result = new JSONObject();
		
		String command = json.getString("command");
		String startDay = result.getString("startDay");
		String year = result.getString("year");
		if(StringUtils.isBlank(year)) {
			year = DateUtil.getCurrentDateStr("yyyy");
		}
	    if("dayRunCommand".equals(command)) {//日定时任务
			if(StringUtils.isBlank(startDay)) {
				startDay = DateUtil.addDay("yyyy-MM-dd",DateUtil.getCurrentDateStr(),-1);
			}
			result = runCommand(startDay,"1","",year,"");
		}else if("weekRunCommand".equals(command)){//周定时任务
			if(StringUtils.isBlank(startDay)){
				startDay = DateUtil.getCurrentDateStr();
			}
			if(DateUtil.getWeek(startDay)==1){//如果传入的日期是星期一，则往前推7天
				startDay = DateUtil.addDay("yyyy-MM-dd",startDay,-7);
				String week = result.getString("week");
				if (StringUtils.isBlank(week)) {
					week = getDateWeek(startDay)+"";
				}
				result = runCommand(startDay,"1",week,year,"");
			}

		}
		else if("monthRunCommand".equals(command)){//月定时任务
			String month = result.getString("month");
			if (StringUtils.isBlank(month)) {
				month = DateUtil.addMonth("yyyy-MM",DateUtil.getCurrentDateStr("yyyy-MM"),-1);
			}
			startDay = month+"-01";
			result = runCommand(startDay,"1","",year,month);
		}
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" >> 返回调用结果:"+result);
		return result;
	}
	/**
	 * 获取此时间为此年的第几周
	 * @param time 日期字符串，格式为yyyy-MM-dd
	 * @return 返回年中的第几周
	 */
	private static int getDateWeek(String time) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		int week = 0;
		try {
			Date parse = simpleDateFormat.parse(time);
			Calendar calendar = Calendar.getInstance();
			// 设置星期一为一周开始的第一天
			calendar.setFirstDayOfWeek(Calendar.MONDAY);
			// 设置在一年中第一个星期所需最少天数
			calendar.setMinimalDaysInFirstWeek(4);
			calendar.setTime(parse);
			week = calendar.get(Calendar.WEEK_OF_YEAR);
			// 处理跨年周数问题
			if (week == 1 && parse.getMonth() == 11) {
				calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 7);
				Date pastDate = calendar.getTime();
				week = calendar.get(Calendar.WEEK_OF_YEAR) + 1;
			}
			return week;
		} catch (Exception e) {
			logger.error("获取周数异常: " + e.getMessage(), e);
			return 0;
		}
	}
	/**
	 * 统计按天、按周、按月一次拨打量和多次拨打量
	 * @param startDay 统计开始时间
	 * @param type 1.日 2周 3月
	 * @param week 当年第几周 int值
	 * @param year yyyy格式
	 * @param month yyyy-MM格式
	 * @return
	 */
	public JSONObject runCommand(String startDay,String type,String week,String year,String month){
		String endDay = "";
		String date = startDay;//获取统计时间
		if("2".equals(type)) {//为周
			date = week;
			endDay = DateUtil.addDay("yyyy-MM-dd", startDay, 6);
		}else if("3".equals(type)) {//为月
			date = month;
			endDay = DateUtil.addMonth("yyyy-MM-dd", startDay, 1);
		}
		if(StringUtils.isBlank(date)) {
			date = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		}
		if(StringUtils.isBlank(year)) {
			year = DateUtil.getCurrentDateStr("yyyy");
		}

		if(!EasyDate.getCurrentDateString("yyyy-MM").equals(date.substring(0, 7))){
			String last = EasyDate.addTime("yyyy-MM-dd", EasyDate.getCurrentDateString("yyyy-MM-dd"), EasyCalendar.MONTH, -1);
			if(last.substring(0,7).equals(date.substring(0, 7))&& LocalDateTime.now().getDayOfMonth()==1&& LocalDateTime.now().getHour()<2){
				month="";
			}else{
				month=date.substring(0, 7).replace("-", "");
			}
		}
		logger.info("=============开始执行剔重号码呼入数量统计============");
		try {
			EasyQuery query = QueryFactory.getWriteQuery();
			query.setTimeout(200000);
			//12345剔重号码量
			EasySQL sql = new EasySQL("select count(1) CALL_COUNT,");
			sql.append("sum(case when CALLEENO_COUNT=1 then 1 else 0 end) ONE_CALL_COUNT,");
			sql.append("sum(case when CALLEENO_COUNT>1 then 1 else 0 end) MULTIPLE_CALL_COUNT from (");
			sql.append("select CALLEENO,count(1) CALLEENO_COUNT from ");
			sql.append(Constants.getBusiName()+".tbillrecord"+month+" where 1=1 and ");
			sql.append(" CallType in(0, 1, 2, 3, 4, 5,13, 15, 20, 21, 22, 23, 32, 33, 46) and CalleeNo='12345' ");
			sql.append(startDay," and day>=? ");
			sql.append(endDay," and day<=? ");
			sql.append(" group by CALLEENO) t1");
			JSONObject call12345 = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(call12345==null){
				call12345 = new JSONObject();
			}
			//其他热线剔重号码量
			sql = new EasySQL("select count(1) CALL_COUNT,");
			sql.append("sum(case when CALLEENO_COUNT=1 then 1 else 0 end) ONE_CALL_COUNT,");
			sql.append("sum(case when CALLEENO_COUNT>1 then 1 else 0 end) MULTIPLE_CALL_COUNT from (");
			sql.append("select CALLEENO,count(1) CALLEENO_COUNT from ");
			sql.append(Constants.getBusiName()+".tbillrecord"+month+" where 1=1 and ");
			sql.append(" CallType in(0, 1, 2, 3, 4, 5,13, 15, 20, 21, 22, 23, 32, 33, 46) and CalleeNo<>'12345' ");
			sql.append(startDay," and day>=? ");
			sql.append(endDay," and day<=? ");
			sql.append(" group by CALLEENO) t1");
			JSONObject other12345 = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(other12345==null){
				other12345 = new JSONObject();
			}
			EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_stat_monitor_repeat","DATE_ID","YEAR");
			record.put("DATE_ID", date);
			record.put("YEAR", year);
			record.put("TYPE", type);
			record.put("12345_ONE_CALL", call12345.getString("ONE_CALL_COUNT"));
			record.put("12345_MULTIPLE_CALL", call12345.getString("MULTIPLE_CALL_COUNT"));
			record.put("12345_ALL_CALL", call12345.getString("CALL_COUNT"));
			record.put("OTHER_ONE_CALL", other12345.getString("ONE_CALL_COUNT"));
			record.put("OTHER_MULTIPLE_CALL", other12345.getString("MULTIPLE_CALL_COUNT"));
			record.put("OTHER_ALL_CALL", other12345.getString("CALL_COUNT"));
			if(!query.update(record)) {
				query.save(record);
			}
			logger.info("=============执行剔重号码呼入数量统计完成============");
		}catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
			return EasyResult.fail();
		}

		return EasyResult.ok();
	}

	
	// @Override
	public String getServiceId() {
		return "STAT_PICKING_WEIGHT_DATA";
	}
	
	// @Override
	public String getName() {
		return "12345大屏定时任务";
	}

	
}
