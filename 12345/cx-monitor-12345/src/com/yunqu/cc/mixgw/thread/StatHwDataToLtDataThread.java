package com.yunqu.cc.mixgw.thread;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.cache.impl.BigMonitorCache;
import com.yunqu.cc.mixgw.util.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.yunqu.cc.mixgw.util.HttpResp;
import com.yunqu.cc.mixgw.util.HttpUtil;
import com.yunqu.cc.mixgw.util.MD5Util;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * 统计华为库中的数据到本地表中并写入大屏中所需要的数据
 * <AUTHOR>
 * @date 2023年4月10日i上午10:07:31
 */
public class StatHwDataToLtDataThread  implements Runnable{
	//日志
	private static Logger logger = CommonLogger.getLogger("hw");
	
	private EasyQuery mysqlQuery = QueryFactory.getWriteQuery(); 
	
	EasyQuery query = null;
	//华为库前缀
	private String hwUser = "ICD";
	private static int number = 1;
	//缓存
	private EasyCache cache = CacheManager.getMemcache();
	//获得传参
	JSONObject param = new JSONObject();
	@Override
	public void run() {
		try {
			mysqlQuery = QueryFactory.getWriteQuery(); 
			cache = CacheManager.getMemcache();
			invoke(this.param);
			//为空时则是从定时任务中进入的
			if(StringUtils.isBlank(this.param.getString("startDate"))) {
				//获取明天1点的时间戳
				long tomorrowTimestamp = cache.get("tomorrowTimestamp")==null?0:cache.get("tomorrowTimestamp");
				if(tomorrowTimestamp == 0) {
					//存入明天凌晨一点的时间戳
					tomorrowTimestamp = getTomorrowTimestamp();
					cache.put("tomorrowTimestamp", tomorrowTimestamp);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	/**
	 * 传递参数
	 * @param jsonObject
	 */
	public StatHwDataToLtDataThread(JSONObject jsonObject) {
		this.param = jsonObject;
	}
	
	/**
	 * 初始化方法
	 */
	public JSONObject invoke(JSONObject jsonObject) {
		logger.info("[StatHwDataToLtDataService] << " + jsonObject.toJSONString());
		JSONObject resultObject = new JSONObject();
		query = QueryFactory.getOracleDb();
		try {
			
			String startDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
			String isStartDate = jsonObject.getString("isStartDate");
			//设置是否需要存入缓存
			boolean putCache = true;
			String endDateTime = EasyDate.getCurrentDateString();
			//不传入开始时间的话默认同步今天数据
			if(StringUtils.isNotBlank(jsonObject.getString("startDate")) && jsonObject.getString("startDate").length()==10) {
				//如果日期为当天的话需要存入缓存,不是的话不需要存入，缓存是用于界面展示数据
				if(!startDate.equals(jsonObject.getString("startDate"))) {
					//获取传入的开始时间
					startDate = jsonObject.getString("startDate");
					endDateTime = jsonObject.getString("startDate")+" 23:59:59";
					putCache = false;
				}
			}
			//获取库中最大开始时间
			String startDateTime = getStartDateTime();
			if(StringUtils.isNotBlank(startDateTime) && startDateTime.split("[.]").length>0) {
				startDateTime = startDateTime.split("[.]")[0];
			}
			
			if(StringUtils.isNotBlank(isStartDate) && "true".equals(isStartDate)) {
				startDateTime = startDate+" 00:00:00";
			}
			logger.info("[startDateTime]"+startDateTime);
			
			if(Constants.getHwDb()) {
				//将华为库中的详细数据保存到本地库
				saveHwData(startDateTime,endDateTime,query);
				if(StringUtils.isNotBlank(startDateTime) && StringUtils.isNotBlank(endDateTime) &&
						!startDateTime.substring(0,10).equals(endDateTime.substring(0,10))) {//判断是否为一天
					startDateTime = endDateTime.substring(0,10)+" 00:00:00";//不为一天重新保存一下今天额外的数据
					saveHwData(startDateTime,endDateTime,query);
				}
			}else {
				//从本地库中保存数据
				saveLocationData(startDateTime,endDateTime,mysqlQuery);
			}
			
			//从本地库中统计的数据（如果putCache为true会在此方法中存入当日剔重号码总量、当日首次来电总量（七日内如果都没打过则算首次来电）、当日非首次来电总量这三个的缓存） 和三网占比
			statDate(startDate,mysqlQuery,"1");
			if(StringUtils.isBlank(isStartDate)) {
				//获取小时id
				String hourId = EasyDate.getCurrentDateString().substring(11, 13);
//				//获取分钟id
//				int mixId = StringUtils.parseInt(EasyDate.getCurrentDateString().substring(14, 16));
//				if(!"00".equals(hourId) && mixId < 20) {
//					//如果当前时间是此小时的前五分钟 重新统计一下上一个小时的数据
//					hourId = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", EasyDate.getCurrentDateString(), Calendar.HOUR_OF_DAY, -1).substring(11, 13);
//					statHourDate(startDate,hourId,mysqlQuery);
//				}
				int hourIdInt = StringUtils.parseInt(hourId);
				for(int i=0;i<hourIdInt;i++) {
					if(i<10) {
						hourId = "0"+i;
					}else {
						hourId = i+"";
					}
					statHourDate(startDate,hourId,mysqlQuery,false);
				}
				
			}
			//获取小时id
			String hourId = EasyDate.getCurrentDateString().substring(11, 13);
			statHourDate(startDate,hourId,mysqlQuery,true);
			//从本地库中统计接通率 增长率等
			statDateRate(startDate,mysqlQuery);
			//如果需要存缓存则去数据库中查询
			if(putCache) {
				saveCache(mysqlQuery);
			}

			//每5分钟保存坐席状态小时数据
			saveAgentHourState();

			resultObject.put("desc", "成功");
			resultObject.put("respCode", "000");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return resultObject;
	}
	 
	/**
	 * 保存三个统计图(接通率（周月年）、呼入量（周月年）、接通量（周月年）)的缓存
	 */
	private void saveCache(EasyQuery query) {
		//呼入量统计图json
		JSONObject inboundCountChart = new JSONObject();
		//接通量统计图json
		JSONObject connectCountChart = new JSONObject();
		//接通率统计图json
		JSONObject connectRateChart = new JSONObject();
		
		//剔重号码总量
		JSONObject removeWegightCounts = new JSONObject();
		//首次来电量
		JSONObject firstCallCounts = new JSONObject();
		//非首次来电
		JSONObject notFirstCallCounts = new JSONObject();
		
		//固话手机、本省外省话务量
		JSONObject callAnalyseCounts = new JSONObject();
		//获取当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量统计
		JSONObject callNumberStatCounts = new JSONObject();
		//整合热线电话量
		JSONObject doublingCounts = new JSONObject();
		String[] colors = {"#00DC55", "#FF0080" ,"#00FFFF"};
		//整合热线电话量
		List<String> doublingCols =  Arrays.asList("12315热线","12305邮政","12320卫健委");
		List<Integer> doublingValues = null;
		//统计日的数据
		String startDate = getDate("1");
		String endDate = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -1);//获取昨天日期
		JSONObject param = dispDataToChart(startDate,endDate,"1",query);
		JSONObject inboundCountChartJson = param.getJSONObject("inboundCountChart");
		if(inboundCountChartJson == null) inboundCountChartJson = new JSONObject();
		inboundCountChartJson.put("callCount", getCountValue("0",inboundCountChartJson));
		
		JSONObject connectCountChartJson = param.getJSONObject("connectCountChart");
		if(connectCountChartJson == null) connectCountChartJson = new JSONObject();
		connectCountChartJson.put("callCount", getCountValue("1",connectCountChartJson));
		
		JSONObject connectRateChartJson = param.getJSONObject("connectRateChart");
		if(connectRateChartJson == null) connectRateChartJson = new JSONObject();
		connectRateChartJson.put("callCount", getCountValue("2",connectRateChartJson));
		
		inboundCountChart.put("dayData", inboundCountChartJson);
		connectCountChart.put("dayData", connectCountChartJson);
		connectRateChart.put("dayData", connectRateChartJson);
		
		callAnalyseCounts.put("dayData", getCallAnalyseJson(startDate,endDate,query));
		callNumberStatCounts.put("dayData", getCallNumberStat("3",startDate,endDate,query));
		doublingValues = Arrays.asList(getIntegrateCallCount("12315",startDate,endDate,query)
				,getIntegrateCallCount("12305",startDate,endDate,query)
				,getIntegrateCallCount("12320",startDate,endDate,query));
		JSONArray hotLineCountDayData = new JSONArray();
		JSONObject json = null;
		JSONObject lable = null;
		for(int m=0;m<doublingValues.size();m++) {
			json = new JSONObject();
			json.put("value", doublingValues.get(m));
			json.put("name", doublingCols.get(m));
			lable = new JSONObject();
			lable.put("color", colors[m]);
			json.put("lable", lable);
			hotLineCountDayData.add(json);
		}
		doublingCounts.put("dayData", hotLineCountDayData);
		//保存当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量
		param = getCount(endDate,endDate,query);
		removeWegightCounts.put("dayData", param.getInteger("REMOVE_WEGIGHT_COUNT"));
		firstCallCounts.put("dayData", param.getInteger("FIRST_CALL_COUNT"));
		notFirstCallCounts.put("dayData", param.getInteger("NOT_FIRST_CALL_COUNT"));
		
		//统计周的数据
		startDate = getDate("2");
		DateUtil.getFirstDayOfWeek("yyyy-MM-dd");
		endDate = DateUtil.addDay("yyyy-MM-dd", DateUtil.getFirstDayOfWeek("yyyy-MM-dd"), -1);//获取上周最后一天日期
		param = dispDataToChart(startDate,endDate,"2",query);
		inboundCountChartJson = param.getJSONObject("inboundCountChart");
		if(inboundCountChartJson == null) inboundCountChartJson = new JSONObject();
		inboundCountChartJson.put("callCount", getCountValue("0",inboundCountChartJson));
		
		connectCountChartJson = param.getJSONObject("connectCountChart");
		if(connectCountChartJson == null) connectCountChartJson = new JSONObject();
		connectCountChartJson.put("callCount", getCountValue("1",connectCountChartJson));
		
		connectRateChartJson = param.getJSONObject("connectRateChart");
		if(connectRateChartJson == null) connectRateChartJson = new JSONObject();
		connectRateChartJson.put("callCount", getCountValue("2",connectRateChartJson));
		
		inboundCountChart.put("weekData", inboundCountChartJson);
		connectCountChart.put("weekData", connectCountChartJson);
		connectRateChart.put("weekData", connectRateChartJson);
		
		
		callAnalyseCounts.put("weekData", getCallAnalyseJson(startDate,endDate,query));
		callNumberStatCounts.put("weekData", getCallNumberStat("1",startDate,endDate,query));
		doublingValues = Arrays.asList(getIntegrateCallCount("12315",startDate,endDate,query)
				,getIntegrateCallCount("12305",startDate,endDate,query)
				,getIntegrateCallCount("12320",startDate,endDate,query));
		JSONArray hotLineCountWeekData = new JSONArray();
		for(int m=0;m<doublingValues.size();m++) {
			json = new JSONObject();
			json.put("value", doublingValues.get(m));
			json.put("name", doublingCols.get(m));
			lable = new JSONObject();
			lable.put("color", colors[m]);
			json.put("lable", lable);
			hotLineCountWeekData.add(json);
		}
		doublingCounts.put("weekData", hotLineCountWeekData);
		//保存当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量
		startDate = DateUtil.addDay("yyyy-MM-dd", endDate, -7);//获取上周第一天日期
		param = getCount(startDate,endDate,query);
		removeWegightCounts.put("weekData", param.getInteger("REMOVE_WEGIGHT_COUNT"));
		firstCallCounts.put("weekData", param.getInteger("FIRST_CALL_COUNT"));
		notFirstCallCounts.put("weekData", param.getInteger("NOT_FIRST_CALL_COUNT"));
		//统计年的数据
		startDate = getDate("3");
		endDate = DateUtil.getLastMonthEndTime("yyyy-MM-dd");//获取上月最后一天日期
		param = dispDataToChart(startDate,endDate,"3",query);
		inboundCountChartJson = param.getJSONObject("inboundCountChart");
		if(inboundCountChartJson == null) inboundCountChartJson = new JSONObject();
		inboundCountChartJson.put("callCount", getCountValue("0",inboundCountChartJson));
		
		connectCountChartJson = param.getJSONObject("connectCountChart");
		if(connectCountChartJson == null) connectCountChartJson = new JSONObject();
		connectCountChartJson.put("callCount", getCountValue("1",connectCountChartJson));
		
		connectRateChartJson = param.getJSONObject("connectRateChart");
		if(connectRateChartJson == null) connectRateChartJson = new JSONObject();
		connectRateChartJson.put("callCount", getCountValue("2",connectRateChartJson));
		
		inboundCountChart.put("monthData", inboundCountChartJson);
		connectCountChart.put("monthData", connectCountChartJson);
		connectRateChart.put("monthData", connectRateChartJson);
		
		
		callAnalyseCounts.put("monthData", getCallAnalyseJson(startDate,endDate,query));
		callNumberStatCounts.put("monthData", getCallNumberStat("2",startDate,endDate,query));
		doublingValues = Arrays.asList(getIntegrateCallCount("12315",startDate,endDate,query)
				,getIntegrateCallCount("12305",startDate,endDate,query)
				,getIntegrateCallCount("12320",startDate,endDate,query));
		JSONArray hotLineCountMonthData = new JSONArray();
		for(int m=0;m<doublingValues.size();m++) {
			json = new JSONObject();
			json.put("value", doublingValues.get(m));
			json.put("name", doublingCols.get(m));
			lable = new JSONObject();
			lable.put("color", colors[m]);
			json.put("lable", lable);
			hotLineCountMonthData.add(json);
		}
		doublingCounts.put("monthData", hotLineCountMonthData);
		//保存当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量
		startDate = DateUtil.getLastMonthStartTime();//获取上个月第一天
		param = getCount(startDate,endDate,query);
		removeWegightCounts.put("monthData", param.getInteger("REMOVE_WEGIGHT_COUNT"));
		firstCallCounts.put("monthData", param.getInteger("FIRST_CALL_COUNT"));
		notFirstCallCounts.put("monthData", param.getInteger("NOT_FIRST_CALL_COUNT"));
		
		logger.info(inboundCountChart);
		logger.info(connectCountChart);
		logger.info(connectRateChart);
		logger.info(callAnalyseCounts);
		logger.info(callNumberStatCounts);
		logger.info(doublingCounts);
		//存入缓存
		cache.put(BigMonitorCache.MONITOR_CHART_INBOUND_COUNT, inboundCountChart);
		cache.put(BigMonitorCache.MONITOR_CHART_CONNECT_COUNT, connectCountChart);
		cache.put(BigMonitorCache.MONITOR_CHART_CONNECT_RATE, connectRateChart);
		cache.put(BigMonitorCache.OLD_CALL_ANALYSE_STAT, callAnalyseCounts);
		cache.put(BigMonitorCache.CALL_NUMBER_STAT, callNumberStatCounts);
		cache.put(BigMonitorCache.OLD_HOT_LINE_COUNT_STAT, doublingCounts);
		
		cache.put(BigMonitorCache.MONITOR_REMOVE_WEGIGHT_COUNT, removeWegightCounts);
		cache.put(BigMonitorCache.MONITOR_FIRST_CALL_COUNT, firstCallCounts);
		cache.put(BigMonitorCache.MONITOR_NOT_FIRST_CALL_COUNT, notFirstCallCounts);
	}

	/**
	 * 获取重复来电量
	 * @param statType 1:日 2：周 3：月
	 * @return
	 */
	public JSONObject getRepeat(String statType){
		JSONObject result = new JSONObject();
		try {
			String startDay = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -1);
			String endDay = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -1);
			if(DateUtil.getDateHour()<1){//如果当前时间是当天凌晨1点则去统计昨天的数据
				startDay = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -2);
				endDay = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -2);

			}
			if("2".equals(statType)){
				startDay = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr(), -7);

			}


		}catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return result;
	}

	/**
	 * 通过传入的json和类型 获取每种数据的合计 dataType 0:呼入量 1:工作量 2:平均接通率
	 * @return
	 */
	public String getCountValue(String dataType,JSONObject chartJson) {
		String strValue = "0";
		if(chartJson != null) {
			int countValue = 0;
			double avgValue = 0;
			JSONObject data = chartJson.getJSONObject("data");
			if(data!=null) {
				if(dataType.equals("0")) {//呼入量
					JSONArray hotLineArr = data.getJSONArray("citizenHotLineData");//市民服务热线呼入量
					JSONArray entArr = data.getJSONArray("EntHotLineData");//企业服务热线数据
					for (int i = 0; i < hotLineArr.size(); i++) {
						countValue = countValue + hotLineArr.getIntValue(i);
					}
					for (int i = 0; i < entArr.size(); i++) {
						countValue = countValue + entArr.getIntValue(i);
					}
					strValue = String.valueOf(countValue);//转string
					
				}else if(dataType.equals("1")) {//工作量
					JSONArray CallAllData = data.getJSONArray("CallAllData");//电话受理量（接通量）
					JSONArray NetWorkData = data.getJSONArray("NetWorkData");//网络受理量
					JSONArray FollowUpData = data.getJSONArray("FollowUpData");//回访受理量
					for (int i = 0; i < CallAllData.size(); i++) {
						countValue = countValue + CallAllData.getIntValue(i);
					}
					for (int i = 0; i < NetWorkData.size(); i++) {
						countValue = countValue + NetWorkData.getIntValue(i);
					}
					for (int i = 0; i < FollowUpData.size(); i++) {
						countValue = countValue + FollowUpData.getIntValue(i);
					}
					strValue = String.valueOf(countValue);//转string
				}else if(dataType.equals("2")) {//平均接通率
					JSONArray ConnRateData = data.getJSONArray("ConnRateData");//总接通率
					for (int i = 0; i < ConnRateData.size(); i++) {
						avgValue = avgValue + ConnRateData.getDoubleValue(i);
					}
					if(ConnRateData.size()>0)
						strValue = String.format("%.2f", (avgValue/ConnRateData.size()));//转string
				}
			}
			
		}
		return strValue;
	}
	public static void main(String[] args) {
		int connectCount = 100;
		int innboundCount = 200;
		System.out.println(String.format("%.2f", (double)connectCount / (double)innboundCount));
	}
	/**
	 * 获取固话手机、本省外省话务量
	 * @param startDate
	 * @param endDate
	 * @param query
	 * @return
	 */
	public JSONObject getCallAnalyseJson(String startDate,String endDate,EasyQuery query) {
		JSONObject callAnalyseJson = new JSONObject();
		try {
			EasySQL sql = new EasySQL("select ROUND(sum(THIS_WEBSITE_COUNT),0) THIS_WEBSITE_COUNT,");
			sql.append("ROUND(sum(NOT_THIS_WEBSITE_COUNT),0) NOT_THIS_WEBSITE_COUNT,ROUND(sum(PHONE_COUNT),0) PHONE_COUNT,");
			sql.append("ROUND(sum(NOT_PHONE_COUNT),0) NOT_PHONE_COUNT,ROUND(sum(THSI_LOCAL_COUNT),0) THSI_LOCAL_COUNT,");
			sql.append("ROUND(sum(NOT_THSI_LOCAL_COUNT),0) NOT_THSI_LOCAL_COUNT from  ");
			sql.append("(select DATE_ID,IFNULL((INBOUND_COUNT*THIS_WEBSITE_RATE/100),0) THIS_WEBSITE_COUNT,");
			sql.append("(INBOUND_COUNT-IFNULL((INBOUND_COUNT*THIS_WEBSITE_RATE/100),0)) NOT_THIS_WEBSITE_COUNT,");
			sql.append("IFNULL((INBOUND_COUNT*PHONE_RATE/100),0) PHONE_COUNT,");
			sql.append("(INBOUND_COUNT-IFNULL((INBOUND_COUNT*PHONE_RATE/100),0)) NOT_PHONE_COUNT,");
			sql.append("IFNULL((INBOUND_COUNT*THSI_LOCAL_RATE/100),0) THSI_LOCAL_COUNT,");
			sql.append("(INBOUND_COUNT-IFNULL((INBOUND_COUNT*THSI_LOCAL_RATE/100),0)) NOT_THSI_LOCAL_COUNT");
			sql.append(" from "+getTableName("cx_mix_hw_stat_call_table")+" where HOT_LINE_TYPE='1' ");
			sql.append(startDate," and DATE_ID>=?");
			sql.append(endDate," and DATE_ID<=?");
			sql.append("GROUP BY DATE_ID) t1  ");
			callAnalyseJson = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return callAnalyseJson;
	}
	
	/**
	 * 获取当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量统计
	 * @param startDate
	 * @param endDate
	 * @param query
	 * @return
	 */
	public List<JSONObject> getCallNumberStat(String dateType,String startDate,String endDate,EasyQuery query) {
		List<JSONObject> callAnalyseJson = new ArrayList<JSONObject>();
		try {
			String dateTol = "DATE_ID";//日
			if("1".equals(dateType)) {//周
				dateTol = "WEEK_ID";
			}else if("2".equals(dateType)) {//月
				dateTol = "SUBSTR(DATE_ID,1,7)";
			}
			EasySQL sql = new EasySQL("SELECT "+dateTol+" DATE_NUMBER,sum(REMOVE_WEGIGHT_COUNT) REMOVE_WEGIGHT_COUNT,");
			sql.append("sum(FIRST_CALL_COUNT) FIRST_CALL_COUNT,");
			sql.append("sum(NOT_FIRST_CALL_COUNT) NOT_FIRST_CALL_COUNT from ");
			sql.append(getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
			sql.append(startDate," and DATE_ID>=?");
			sql.append(endDate," and DATE_ID<=?");
			sql.append(" and HOT_LINE_TYPE = 1");
			sql.append(" group by DATE_NUMBER order by DATE_NUMBER");
			callAnalyseJson = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return callAnalyseJson;
	}
	
	
	/**
	 * 获取整合热线数据
	 * @param phone
	 * @return
	 */
	public int getIntegrateCallCount(String phone,String startDate,String endDate,EasyQuery query) {
		int callCount = 0;
		try {
			EasySQL sql = new EasySQL("select sum(CALLIN_COUNT) CALLIN_COUNT from "+getTableName("tbill_call_stat_rpt")+" where 1=1 ");
			sql.append(phone," and (CalleeNo=?");
			sql.append(phone," or CallerNo=?)");
			sql.append(startDate+" 00:00:00"," and STAT_TIME>=?");
			sql.append(endDate+" 23:59:59"," and STAT_TIME<=?");
			callCount = query.queryForInt(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		
		return callCount;
	}
	
	/**
	 * 获取当日剔重号码总量  当日首次来电总量（七日内如果都没打过则算首次来电） 当日非首次来电总量
	 * @param startDate
	 * @param endDate
	 * @param query
	 * @return
	 */
	private JSONObject getCount(String startDate,String endDate,EasyQuery query) {
		EasySQL sql = new EasySQL("select sum(REMOVE_WEGIGHT_COUNT) as REMOVE_WEGIGHT_COUNT,"
				+ "sum(FIRST_CALL_COUNT) as FIRST_CALL_COUNT,sum(NOT_FIRST_CALL_COUNT) as NOT_FIRST_CALL_COUNT from "
				+getTableName("cx_mix_hw_stat_call_table")+" where 1=1 and HOT_LINE_TYPE in (1,3)");
		sql.append(startDate," and DATE_ID>=?");
		sql.append(endDate," and DATE_ID<=?");
		try {
			JSONObject param = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			return param;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return new JSONObject();
	}
	
	/**
	 * 处理数据返回chart类型的数据
	 * @param startDate
	 * @param endDate
	 * @param dateType 查询类型 1：日 2：周 3：月
	 * @return
	 */
	private JSONObject dispDataToChart(String startDate,String endDate,String dateType,EasyQuery query) {
		JSONObject resParams = new JSONObject();
		//呼入量统计图json
		JSONObject inboundCountChart = new JSONObject();
		//受理量统计图json
		JSONObject connectCountChart = new JSONObject();
		//接通率统计图json
		JSONObject connectRateChart = new JSONObject();
		JSONObject param = new JSONObject();
		try {
			//市民服务热线
			List<JSONObject> data1Jsons = getDataToDay(startDate,endDate,dateType,"1",query);
			//企业服务热线
			List<JSONObject> data3Jsons = getDataToDay(startDate,endDate,dateType,"3",query);
			//网络受理量
			List<JSONObject> data4Jsons = getDataToDay(startDate,endDate,dateType,"4",query);
			//回访量
			List<JSONObject> data5Jsons = getDataToDay(startDate,endDate,dateType,"5",query);
			List<String> dateList = new ArrayList<String>();
			List<String> dateListxAxis = new ArrayList<String>();
			if("1".equals(dateType)) {
				//x轴
				dateList = getDateList(startDate,endDate);
				for(String date:dateList) {
					dateListxAxis.add(date.substring(5,date.length()).replace("-", "/"));
				}
			} else if("2".equals(dateType)) {
				//查询出周ID放入x轴
				EasySQL sql = new EasySQL("select WEEK_ID from "+getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
				sql.append(startDate," and DATE_ID>=?");
				sql.append(endDate," and DATE_ID<=?");
				sql.append(" group by WEEK_ID order by DATE_ID,WEEK_ID");
				List<JSONObject> weekLists = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				for(JSONObject weekList:weekLists) {
					dateList.add(weekList.getString("WEEK_ID"));
					dateListxAxis.add("第"+weekList.getString("WEEK_ID")+"周");
				}
			} else {
				if(!startDate.substring(0,4).equals(endDate.substring(0,4))) {
					for (int i = StringUtils.parseInt(startDate.substring(5,7)); i <= 12; i++) {
						dateList.add(i+"");
						dateListxAxis.add(i+"月");
					}
					for(int i = 1; i <= StringUtils.parseInt(endDate.substring(5,7)); i++) {
						dateList.add(i+"");
						dateListxAxis.add(i+"月");
					}
					
				}else {
					for(int i = 1; i <= StringUtils.parseInt(endDate.substring(5,7)); i++) {
						dateList.add(i+"");
						dateListxAxis.add(i+"月");
					}
				}
				
			}
			//呼入量
			inboundCountChart.put("xAxis", dateListxAxis);
			//市民服务热线呼入量
			param.put("citizenHotLineData", getListCallCount(data1Jsons,dateList,"INBOUND_COUNT",dateType));
			//企业服务热线数据
			param.put("EntHotLineData", getListCallCount(data3Jsons,dateList,"INBOUND_COUNT",dateType));
			inboundCountChart.put("data", param);
			param = new JSONObject();
			//受理量
			connectCountChart.put("xAxis", dateListxAxis);
			//电话受理量（接通量）
			param.put("CallAllData", getListCallCount(data1Jsons,dateList,"CONNECT_ALL_COUNT",dateType));
			//网络受理量
			param.put("NetWorkData", getListCallCount(data4Jsons,dateList,"CONNECT_COUNT",dateType));
			//回访受理量
			param.put("FollowUpData", getListCallCount(data5Jsons,dateList,"CONNECT_COUNT",dateType));
			connectCountChart.put("data", param);
			param = new JSONObject();
			//接通率
			connectRateChart.put("xAxis", dateListxAxis);
			//总接通率
			param.put("ConnRateData", getListCallCount(data1Jsons,dateList,"CONNECT_RATE",dateType));
			connectRateChart.put("data", param);
			//存入json中
			resParams.put("inboundCountChart", inboundCountChart);
			resParams.put("connectCountChart", connectCountChart);
			resParams.put("connectRateChart", connectRateChart);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return resParams;
	}
	/**
	 * 通过接口获取的数据
	 * @return
	 */
	public JSONObject getConfigureData(){
		EasySQL sql = new EasySQL("SELECT t1.*,t8.*,t2.DATA0 PHONE_DATA0,t2.DATA1 PHONE_DATA1,t2.DATA2 PHONE_DATA2,t2.DATA3 PHONE_DATA3,t2.DATA4 PHONE_DATA4,t2.DATA5 PHONE_DATA5,t2.DATA6 PHONE_DATA6,t2.DATA7 PHONE_DATA7,t2.DATA8 PHONE_DATA8,t2.DATA9 PHONE_DATA9,t2.DATA10 PHONE_DATA10,t2.DATA11 PHONE_DATA11,t2.DATA12 PHONE_DATA12,t2.DATA13 PHONE_DATA13,t2.DATA14 PHONE_DATA14,t2.DATA15 PHONE_DATA15,t2.DATA16 PHONE_DATA16,t2.DATA17 PHONE_DATA17,t2.DATA18 PHONE_DATA18,t2.DATA19 PHONE_DATA19,t2.DATA20 PHONE_DATA20,t2.DATA21 PHONE_DATA21,t2.DATA22 PHONE_DATA22,t2.DATA23 PHONE_DATA23,t2.DATA24 PHONE_DATA24,t3.DATA0 ENTWORK_DATA0,t3.DATA1 ENTWORK_DATA1,t3.DATA2 ENTWORK_DATA2,t3.DATA3 ENTWORK_DATA3,t3.DATA4 ENTWORK_DATA4,t3.DATA5 ENTWORK_DATA5,t3.DATA6 ENTWORK_DATA6,t3.DATA7 ENTWORK_DATA7,t3.DATA8 ENTWORK_DATA8,t3.DATA9 ENTWORK_DATA9,t3.DATA10 ENTWORK_DATA10,t3.DATA11 ENTWORK_DATA11,t3.DATA12 ENTWORK_DATA12,t3.DATA13 ENTWORK_DATA13,t3.DATA14 ENTWORK_DATA14,t3.DATA15 ENTWORK_DATA15,t3.DATA16 ENTWORK_DATA16,t3.DATA17 ENTWORK_DATA17,t3.DATA18 ENTWORK_DATA18,t3.DATA19 ENTWORK_DATA19,t3.DATA20 ENTWORK_DATA20,t3.DATA21 ENTWORK_DATA21,t3.DATA22 ENTWORK_DATA22,t3.DATA23 ENTWORK_DATA23,t3.DATA24 ENTWORK_DATA24,t4.DATA0 FLOW_UP_DATA0,t4.DATA1 FLOW_UP_DATA1,t4.DATA2 FLOW_UP_DATA2,t4.DATA3 FLOW_UP_DATA3,t4.DATA4 FLOW_UP_DATA4,t4.DATA5 FLOW_UP_DATA5,t4.DATA6 FLOW_UP_DATA6,t4.DATA7 FLOW_UP_DATA7,t4.DATA8 FLOW_UP_DATA8,t4.DATA9 FLOW_UP_DATA9,t4.DATA10 FLOW_UP_DATA10,t4.DATA11 FLOW_UP_DATA11,t4.DATA12 FLOW_UP_DATA12,t4.DATA13 FLOW_UP_DATA13,t4.DATA14 FLOW_UP_DATA14,t4.DATA15 FLOW_UP_DATA15,t4.DATA16 FLOW_UP_DATA16,t4.DATA17 FLOW_UP_DATA17,t4.DATA18 FLOW_UP_DATA18,t4.DATA19 FLOW_UP_DATA19,t4.DATA20 FLOW_UP_DATA20,t4.DATA21 FLOW_UP_DATA21,t4.DATA22 FLOW_UP_DATA22,t4.DATA23 FLOW_UP_DATA23,t4.DATA24 FLOW_UP_DATA24,t5.DATA0 CALL_IN_DATA0,t5.DATA1 CALL_IN_DATA1,t5.DATA2 CALL_IN_DATA2,t5.DATA3 CALL_IN_DATA3,t5.DATA4 CALL_IN_DATA4,t5.DATA5 CALL_IN_DATA5,t5.DATA6 CALL_IN_DATA6,t5.DATA7 CALL_IN_DATA7,t5.DATA8 CALL_IN_DATA8,t5.DATA9 CALL_IN_DATA9,t5.DATA10 CALL_IN_DATA10,t5.DATA11 CALL_IN_DATA11,t5.DATA12 CALL_IN_DATA12,t5.DATA13 CALL_IN_DATA13,t5.DATA14 CALL_IN_DATA14,t5.DATA15 CALL_IN_DATA15,t5.DATA16 CALL_IN_DATA16,t5.DATA17 CALL_IN_DATA17,t5.DATA18 CALL_IN_DATA18,t5.DATA19 CALL_IN_DATA19,t5.DATA20 CALL_IN_DATA20,t5.DATA21 CALL_IN_DATA21,t5.DATA22 CALL_IN_DATA22,t5.DATA23 CALL_IN_DATA23,t5.DATA24 CALL_IN_DATA24,t6.DATA0 CONNECT_DATA0,t6.DATA1 CONNECT_DATA1,t6.DATA2 CONNECT_DATA2,t6.DATA3 CONNECT_DATA3,t6.DATA4 CONNECT_DATA4,t6.DATA5 CONNECT_DATA5,t6.DATA6 CONNECT_DATA6,t6.DATA7 CONNECT_DATA7,t6.DATA8 CONNECT_DATA8,t6.DATA9 CONNECT_DATA9,t6.DATA10 CONNECT_DATA10,t6.DATA11 CONNECT_DATA11,t6.DATA12 CONNECT_DATA12,t6.DATA13 CONNECT_DATA13,t6.DATA14 CONNECT_DATA14,t6.DATA15 CONNECT_DATA15,t6.DATA16 CONNECT_DATA16,t6.DATA17 CONNECT_DATA17,t6.DATA18 CONNECT_DATA18,t6.DATA19 CONNECT_DATA19,t6.DATA20 CONNECT_DATA20,t6.DATA21 CONNECT_DATA21,t6.DATA22 CONNECT_DATA22,t6.DATA23 CONNECT_DATA23,t6.DATA24 CONNECT_DATA24,t7.DATA0 LINE_UP_DATA0,t7.DATA1 LINE_UP_DATA1,t7.DATA2 LINE_UP_DATA2,t7.DATA3 LINE_UP_DATA3,t7.DATA4 LINE_UP_DATA4,t7.DATA5 LINE_UP_DATA5,t7.DATA6 LINE_UP_DATA6,t7.DATA7 LINE_UP_DATA7,t7.DATA8 LINE_UP_DATA8,t7.DATA9 LINE_UP_DATA9,t7.DATA10 LINE_UP_DATA10,t7.DATA11 LINE_UP_DATA11,t7.DATA12 LINE_UP_DATA12,t7.DATA13 LINE_UP_DATA13,t7.DATA14 LINE_UP_DATA14,t7.DATA15 LINE_UP_DATA15,t7.DATA16 LINE_UP_DATA16,t7.DATA17 LINE_UP_DATA17,t7.DATA18 LINE_UP_DATA18,t7.DATA19 LINE_UP_DATA19,t7.DATA20 LINE_UP_DATA20,t7.DATA21 LINE_UP_DATA21,t7.DATA22 LINE_UP_DATA22,t7.DATA23 LINE_UP_DATA23,t7.DATA24 LINE_UP_DATA24 "
				+ "FROM "+getTableName("big_moniter_data_12345_v2")+" t1 left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t2 on t1.DATA_ID=t2.DATA_ID and t2.TYPE_ID='1' left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t3 on t1.DATA_ID=t3.DATA_ID and t3.TYPE_ID='2' left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t4 on t1.DATA_ID=t4.DATA_ID and t4.TYPE_ID='3' left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t5 on t1.DATA_ID=t5.DATA_ID and t5.TYPE_ID='4' left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t6 on t1.DATA_ID=t6.DATA_ID and t6.TYPE_ID='5' left join "+getTableName("big_moniter_data_12345_v2_ext")
				+" t7 on t1.DATA_ID=t7.DATA_ID and t7.TYPE_ID='6' left join "+getTableName("big_moniter_parallel_12345_v2")
				+" t8 on t1.DATA_ID=t8.DATA_V2_ID");
		try {
			sql.append(" where IS_USE=?");
			JSONObject row = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), new Object[] {"1"},new JSONMapperImpl());
			if(row==null) {
				return new JSONObject();
			}else {
				return row;
			}
		} catch (SQLException e) {
			logger.error(e);
			return new JSONObject();
		}
	}
	/**
	 * 根据传入的数据和横坐标还有param名称查询坐标轴中需要的数据
	 * @param dataJsons
	 * @param dateList
	 * @param paramName
	 * @param dateType 1：日 2：周 3：月
	 * @return
	 */
	private List<String> getListCallCount(List<JSONObject> dataJsons,List<String> dateList,String paramName,String dateType){
		List<String> strings = new ArrayList<String>();
		for(String dateId:dateList) {
			boolean isDate = false;
			for (JSONObject dataJson : dataJsons) {
				if("1".equals(dateType)) {
					if(dateId.equals(dataJson.getString("DATE_ID"))) {
						if("1".equals(dateType)) {
							strings.add(dataJson.getString(paramName));
						}
						isDate = true;
						break;
					}
				} else if("2".equals(dateType)) {
					if(dateId.equals(dataJson.getString("WEEK_ID"))) {
						if(!"CONNECT_RATE".equals(paramName)){
							strings.add(dataJson.getString(paramName));
						}else {
							//如果是按年的接通率则需要重新计算
							//strings.add(getDivide(dataJson.getIntValue("CONNECT_COUNT"),dataJson.getIntValue("INBOUND_COUNT"))+"");
							strings.add(getDivide(dataJson.getString(paramName))+"");
						}
						isDate = true;
						break;
					}
				}
				else if("3".equals(dateType)) {
					if(dateId.equals(dataJson.getString("MONTH_ID"))) {
						if(!"CONNECT_RATE".equals(paramName)){
							strings.add(dataJson.getString(paramName));
						}else {
							logger.info("MONTH_ID"+dateId+"[CONNECT_COUNT]"+dataJson.getIntValue("CONNECT_COUNT")+"[INBOUND_COUNT]"+dataJson.getIntValue("INBOUND_COUNT"));
							//如果是按年的接通率则需要重新计算
							//strings.add(getDivide(dataJson.getIntValue("CONNECT_COUNT"),dataJson.getIntValue("INBOUND_COUNT"))+"");
							strings.add(getDivide(dataJson.getString(paramName))+"");
						}
						isDate = true;
						break;
					}
				}
				
			}
			//当前日期没有数据则传入0
			if(!isDate) {
				strings.add("0");
			}
		}
		return strings;
	}
	
	/**
	 * 根据开始时间结束时间和查询类型统计出话务量等数据
	 * @param startDate 查询开始时间
	 * @param endDate 查询结束时间
	 * @param dateType 查询类型 1：周 2：月 3：年
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @return
	 */
	private List<JSONObject> getDataToDay(String startDate,String endDate,String dateType,String hotLineType,EasyQuery query){
		List<JSONObject> resJsons = new ArrayList<JSONObject>();
		try {
			//如果统计类型为周或者月时则统计每一天的数据
			if("1".equals(dateType)) {
				EasySQL sql = new EasySQL("select DATE_ID,INBOUND_COUNT,CONNECT_COUNT,CONNECT_RATE,CONNECT_ALL_COUNT from "
				+ getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
				sql.append(startDate , " and DATE_ID >= ?");
				sql.append(endDate , " and DATE_ID <= ?");
				sql.append(hotLineType , " and HOT_LINE_TYPE = ?");
				sql.append(" order by DATE_ID");
				resJsons = mysqlQuery.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			}else if("2".equals(dateType)) {
				EasySQL sql = new EasySQL("select WEEK_ID,sum(INBOUND_COUNT) INBOUND_COUNT,sum(CONNECT_COUNT) CONNECT_COUNT,sum(CONNECT_ALL_COUNT) CONNECT_ALL_COUNT,avg(CONNECT_RATE) CONNECT_RATE from "
						+ getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
						sql.append(startDate , " and DATE_ID >= ?");
						sql.append(endDate , " and DATE_ID <= ?");
						sql.append(hotLineType , " and HOT_LINE_TYPE = ?");
						sql.append(" group by WEEK_ID order by WEEK_ID");
						resJsons = mysqlQuery.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			} else {
				//如果统计年的数据则按照每个月进行统计
				EasySQL sql = new EasySQL("select MONTH_ID,sum(INBOUND_COUNT) INBOUND_COUNT,sum(CONNECT_COUNT) CONNECT_COUNT,sum(CONNECT_ALL_COUNT) CONNECT_ALL_COUNT,avg(CONNECT_RATE) CONNECT_RATE from "
				+ getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
				sql.append(startDate , " and DATE_ID >= ?");
				sql.append(endDate , " and DATE_ID <= ?");
				sql.append(hotLineType , " and HOT_LINE_TYPE = ?");
				sql.append(" group by MONTH_ID order by MONTH_ID");
				resJsons = mysqlQuery.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return resJsons;
	}
	
	/**
	 * 获取华为明细表已入库的最大时间
	 */
	private String getStartDateTime() {
		String dateTime = EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00:00";
		String tableName = getTableName("cx_mix_12345_hw_tbilllog");
		String sql = "select max(CALLEND) from "+tableName+"";
		try {
			dateTime = mysqlQuery.queryForString(sql, new Object[] {});
			if(StringUtils.isBlank(dateTime)) {
				return EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00:00";
			}else {
				dateTime = dateTime.split("[.]")[0];
				dateTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", dateTime, Calendar.HOUR_OF_DAY, -1);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return dateTime;
	}
	/**
	 * 获取华为话单表明细数据
	 * @param startDateTime 开始时间
	 * @param endDateTime 结束时间
	 * @param query 华为库数据源
	 */
	@Transactional(rollbackFor = Exception.class)
	private void saveHwData(String startDateTime,String endDateTime,EasyQuery query) {
		//存放数据
		List<JSONObject> listDatas = new ArrayList<JSONObject>();
		String hwTitle = " count(CALLID) TOTAL ";
		//获取数量
		EasySQL sql = getHwSql(startDateTime,endDateTime,hwTitle);
		try {
			int callInCount = Integer.parseInt(query.queryForString(sql.getSQL(), sql.getParams()));
			if (callInCount > 0) {
				int pageNum = callInCount / 3000 + 1;
				//分页查询
				for (int i = 1; i <= pageNum; i++) {
					hwTitle=" PARTID,CALLID,CALLIDNUM,CALLERNO,CALLEENO,WAITBEGIN,WAITEND,ACKBEGIN,ACKEND,CALLBEGIN,CALLEND,SERVICENO,TRKNO,TRKGRPNO,MODNO,DEVICETYPE,DEVICENO,DEVICEIN,CALLTYPE,WAITCAUSE,RELEASECAUSE,SUBCCNO,VDN,MEDIATYPE,UVID,ORGCCNO,ORGCALLID,ORGCALLEENO,ORGSERVICENO,SERCCNO,SERSERVICE,USERLEVEL,USERTYPE,CALLINCAUSE,ENTERREASON,LEAVEREASON,BILLINFO,PRESERVICENO,PREDEVICETYPE,PREDEVICENO,PREDEVICEIN,SKILLID,MEDIAINFOTYPE,LOCATIONID,BILLINFO1,BILLINFO2,BILLINFO3,BILLINFO4 ";
					EasySQL callInSql = getHwSql(startDateTime,endDateTime,hwTitle);
					List<JSONObject> cdrData = query.queryForList(callInSql.getSQL(),
							callInSql.getParams(), i, 3000, new JSONMapperImpl());
					if (cdrData != null && cdrData.size() > 0) {
						logger.info("[trans]第" + i + "次取出数据" + cdrData.size() + "条");
						listDatas.addAll(cdrData);
					}
				}
			}
			//汇总入库
			if(listDatas.size() > 0) { 
				//先删除此时间段内的数据
				removeLtData(startDateTime,endDateTime);
				//拼接获取dateId 
				String dateId = startDateTime.split(" ")[0];
				for(JSONObject listData:listDatas) {
					EasyRecord record = new EasyRecord(getTableName("cx_mix_12345_hw_tbilllog"),"DATEID","PARTID","CALLID");
					listData.remove("R_R");
					record.setColumns(listData);
//					String serviceProviderId = getServiceProviderId(listData.getString("CALLERNO"));
//					record.set("IS_THIS_WEBSITE", serviceProviderId); 
					String dateIdStr = dateId.replace("-", "");
					//获取结束时间当作此条通话记录的日期
					if(StringUtils.isNotBlank(listData.getString("CALLEND")) && listData.getString("CALLEND").split(" ").length>1) {
						dateIdStr = listData.getString("CALLEND").split(" ")[0].replace("-", "");
					}
					record.set("DATEID", dateIdStr); 
					try {
						//直接保存
						mysqlQuery.save(record);
					} catch (Exception e) {
						e.printStackTrace();
						logger.info(e.getMessage()+" 数据库中已存在，不做处理!");
					}
				}
				logger.info(listDatas.size()+"条数据插入完毕！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	
	/**
	 * 从本地数据库中获取统计数据
	 * @param startDateTime
	 * @param endDateTime
	 * @param query
	 */
	private void saveLocationData(String startDateTime,String endDateTime,EasyQuery query) {
		
		try {
			String delSql = "delete from "+getTableName("cx_mix_12345_hw_tbilllog")+" where CALLEND>='"+startDateTime+"' and CALLEND<='"+endDateTime+"' ";
			query.execute(delSql);
			String sql = "insert into "+getTableName("cx_mix_12345_hw_tbilllog")+" ( PARTID,CALLID,CALLIDNUM,CALLERNO,CALLEENO,"
					+ "WAITBEGIN,WAITEND,ACKBEGIN,ACKEND,CALLBEGIN,CALLEND,SERVICENO,TRKNO,TRKGRPNO,MODNO,DEVICETYPE,DEVICENO,"
					+ "DEVICEIN,CALLTYPE,WAITCAUSE,RELEASECAUSE,SUBCCNO,VDN,MEDIATYPE,UVID,ORGCCNO,ORGCALLID,ORGCALLEENO,ORGSERVICENO,"
					+ "SERCCNO,SERSERVICE,USERLEVEL,USERTYPE,CALLINCAUSE,ENTERREASON,LEAVEREASON,BILLINFO,PRESERVICENO,PREDEVICETYPE,"
					+ "PREDEVICENO,PREDEVICEIN,SKILLID,MEDIAINFOTYPE,LOCATIONID,BILLINFO1,BILLINFO2,BILLINFO3,BILLINFO4,DATEID ) "
					+ "select  PARTID,CALLID,CALLIDNUM,CALLERNO,CALLEENO,WAITBEGIN,WAITEND,ACKBEGIN,ACKEND,CALLBEGIN,CALLEND,SERVICENO,"
					+ "TRKNO,TRKGRPNO,MODNO,DEVICETYPE,DEVICENO,DEVICEIN,CALLTYPE,WAITCAUSE,RELEASECAUSE,SUBCCNO,VDN,MEDIATYPE,UVID,"
					+ "ORGCCNO,ORGCALLID,ORGCALLEENO,ORGSERVICENO,SERCCNO,SERSERVICE,USERLEVEL,USERTYPE,CALLINCAUSE,ENTERREASON,"
					+ "LEAVEREASON,BILLINFO,PRESERVICENO,PREDEVICETYPE,PREDEVICENO,PREDEVICEIN,SKILLID,MEDIAINFOTYPE,LOCATIONID,"
					+ "BILLINFO1,BILLINFO2,BILLINFO3,BILLINFO4,REPLACE(SUBSTR(CALLEND,1,10),'-','')  DATEID from "
					+getTableName("tbilllog")+" where CALLEND>='"+startDateTime+"' and CALLEND<='"
					+endDateTime+"' and DEVICETYPE='2'  ";
			query.execute(sql);
			String selSql = "select * from "+getTableName("cx_mix_12345_hw_tbilllog")+" where CALLEND>='"+startDateTime+"' and CALLEND<='"+endDateTime+"' and (IS_THIS_WEBSITE is null or IS_THIS_WEBSITE='') and CALLEENO='12345'";
			List<JSONObject> selJsons = query.queryForList(selSql,new Object[] {},new JSONMapperImpl());
			EasyRecord record = new EasyRecord(getTableName("cx_mix_12345_hw_tbilllog"),"DATEID","PARTID","CALLID");
			JSONObject serviceProvidJson = null;
			for (JSONObject selJson : selJsons) {
				record = new EasyRecord(getTableName("cx_mix_12345_hw_tbilllog"),"DATEID","PARTID","CALLID");
				record.set("DATEID", selJson.getString("DATEID"));
				record.set("PARTID", selJson.getString("PARTID"));
				record.set("CALLID", selJson.getString("CALLID"));
				serviceProvidJson = getServiceProviderId(selJson.getString("CALLERNO"));
				record.setColumns(serviceProvidJson);
				logger.info(record);
				QueryFactory.getWriteQuery().update(record);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	
	/**
	 * 通过号码获取当前号码运营商（1：移动 、2：联通 、0：电信 、99 其他）
	 * @param mobile 手机号
	 * @return
	 */
	private JSONObject getServiceProviderId(String mobile) {
		JSONObject result = new JSONObject();
		String serviceProviderId = "99";
		try {
			if(mobile.length()<11) {
				logger.error("主叫号码"+mobile+"小于11未无法判断归属地！");
				result.put("IS_PHONE", "1");
				JSONObject fixPhone = isFixPhone(mobile);
				result.put("IS_THIS_WEBSITE", "01".equals(fixPhone.getString("NETWORK_PHONE"))?"1":"0");
				result.put("IS_LOCATION", "01".equals(fixPhone.getString("REGION_PHONE"))?"1":"0");
				return result;
			}
			if(mobile.startsWith("90")) {
				mobile = mobile.substring(2);
			}
			if(mobile.startsWith("9")) {
				mobile = mobile.substring(1);
			}
			String hcode = mobile.substring(0, 7);
            String hcodeStr = cache.get("M_HCODE_" + hcode);
            //从缓存中查询地区编码
            JSONObject jsonObject = JSONObject.parseObject(hcodeStr);
            if (jsonObject != null) {
            	serviceProviderId = jsonObject.getString("vendor");
            	if("2".equals(serviceProviderId)) {
            		serviceProviderId="1";
            	}else {
            		serviceProviderId="0";
            	}
            	String isProvince = jsonObject.getString("area");
            	if("10".equals(isProvince)) {
            		isProvince = "1";
            	}else {
            		isProvince = "0";
            	}
            	result.put("IS_PHONE", "0");
				result.put("IS_THIS_WEBSITE", serviceProviderId);
				result.put("IS_LOCATION", isProvince);
				return result;
            }else {
            	//判断是否是联通号码
    			IService service = ServiceContext.getService("CHECK_NUMBER_NET_BJT");
    			JSONObject param = new JSONObject();
    	        param.put("serialNumber", mobile);
    	        JSONObject invoke = service.invoke(param);
    	        String respCode = invoke.getString("respCode");
    	        if ("0".equals(respCode)) {
    	            //号码网络归属 ：0 非本网号码1 是本网号码
    	        	serviceProviderId = invoke.getString("numStatus");
    	            String cityCode = invoke.getString("cityCode");
    	            if("10".equals(cityCode)) {
    	            	cityCode = "1";
                	}else {
                		cityCode = "0";
                	}
    	            result.put("IS_PHONE", "0");
    				result.put("IS_THIS_WEBSITE", serviceProviderId);
    				result.put("IS_LOCATION", cityCode);
    	        } else {
    	            // 接口返回失败
    	            logger.error("H码中未查到的号码：" + mobile + "，调用本网异网接口>>CHECK_NUMBER_NET_BJT失败<< " + invoke);
    	            result.put("IS_PHONE", "1");
    				JSONObject fixPhone = isFixPhone(mobile);
    				result.put("IS_THIS_WEBSITE", "01".equals(fixPhone.getString("NETWORK_PHONE"))?"1":"0");
    				result.put("IS_LOCATION", "01".equals(fixPhone.getString("REGION_PHONE"))?"1":"0");
    	        }
            }
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
			result.put("IS_PHONE", "1");
			JSONObject fixPhone = isFixPhone(mobile);
			result.put("IS_THIS_WEBSITE", "01".equals(fixPhone.getString("NETWORK_PHONE"))?"1":"0");
			result.put("IS_LOCATION", "01".equals(fixPhone.getString("REGION_PHONE"))?"1":"0");
		}
		
		
		return result;
	}
	public static JSONObject isFixPhone(String fixPhone) {
        JSONObject result = new JSONObject();
        // 判断号码大于7位，因为以下判断需要前7位，防止索引越界异常
        if (fixPhone.length() >= 7 && !fixPhone.startsWith("1")) {
            // 判断本地（北京） 异地
            if (fixPhone.startsWith("010") || fixPhone.length() == 8) {//  010开头，或者是8位，默认为北京本地固话
                result.put("REGION_PHONE", "01");
            } else {//异地
                result.put("REGION_PHONE", "02");
            }
            String ctr1 = "";
            String ctr2 = "";
            String ctr3 = "";
            //先截取3位，判断是否是3位区号
            String areaCode = fixPhone.substring(0, 3);
            String[] areaCodes = new String[]{"010", "020", "021", "022", "023", "024", "025", "026", "027", "028", "029"};
            // 是3位区号
            if (ArrayUtils.contains(areaCodes, areaCode)) {
                // 判断固话运营商 6、8开头是联通固话 66开头的第三位是3 4 5 6 7 8 9是军线，也算异网
                ctr1 = fixPhone.substring(3, 4);
                ctr2 = fixPhone.substring(3, 5);
                ctr3 = fixPhone.substring(5, 6);
            } else if (fixPhone.length() == 8) {
                // 判断固话运营商 6、8开头是联通固话 66开头的第三位是3 4 5 6 7 8 9是军线，也算异网
                ctr1 = fixPhone.substring(0, 1);
                ctr2 = fixPhone.substring(0, 2);
                ctr3 = fixPhone.substring(2, 3);
            } else {
                // 4位区号  我国的固定电话的区号为一般为4位,少数为3位(如北京,上海等);而电话号码一般为7位或8位。所以,拨打国内固定电话,一般为11位或12位
                if (fixPhone.length() == 11 || fixPhone.length() == 12) {
                    // 判断固话运营商 6、8开头是联通固话 66开头的第三位是3 4 5 6 7 8 9是军线，也算异网
                    ctr1 = fixPhone.substring(4, 5);
                    ctr2 = fixPhone.substring(4, 6);
                    ctr3 = fixPhone.substring(6, 7);
                }
            }
            // 1 第一位等于6 并且 （第一、第二位等于66 并且 第3位不是3456789）
            // 2 第一位等于6 并且 第二位不等于66
            // 3 第一位等于8
            // 4 第一、第二位等于66 并且 第3位不是3456789
            // 符合以上3种情况 是 本网，不符合是异网
            if ((ctr1.equals("6") && (ctr2.equals("66") && !"3456789".contains(ctr3)))
            		|| (ctr1.equals("6") && !ctr2.equals("66"))
                    || ctr1.equals("8")
                    || (ctr2.equals("66") && !"3456789".contains(ctr3))) {
                result.put("NETWORK_PHONE", "01");
            } else { // 异网
                result.put("NETWORK_PHONE", "02");
            }
        } else {
            // 小于7位的号码，是114、112、10086、10010等之类的客服电话。联通客服电话之外，都属于本地异网，
            // 10018客户俱乐部服务热线
            // 10010客户服务热线
            // 10011充值服务热线
            // 10016营销专线
            // 10015消费者权益保护热线
            if ("10018,10010,10011,10016,10015".contains(fixPhone)) {
                // 本地 本网
                result.put("REGION_PHONE", "01");
                result.put("NETWORK_PHONE", "01");
            } else {
                // 本地 异网
                result.put("REGION_PHONE", "01");
                result.put("NETWORK_PHONE", "02");
            }
        }
        return result;
    }
	/**
	 * 通过号码获取当前号码运营商（1：移动 、2：联通 、0：电信 、99 其他）
	 * @param mobile 手机号
	 * @return
	 */
//	private String getServiceProviderId(String mobile) {
//		String serviceProviderId = "99";
//		try {
//			if(mobile.length()<11) {
//				logger.error("主叫号码"+mobile+"小于11未无法判断归属地！");
//				return serviceProviderId;
//			}
//			String hcode = mobile.substring(0, 7);
//            String hcodeStr = cache.get("M_HCODE_" + hcode);
//            //从缓存中查询地区编码
//            JSONObject jsonObject = JSONObject.parseObject(hcodeStr);
//            if (jsonObject != null) {
//            	serviceProviderId = jsonObject.getString("vendor");
//            	if(StringUtils.isBlank(serviceProviderId)) {
//            		logger.error("获取"+mobile+"号码的H码为："+jsonObject.toJSONString()+";号码所有运营商为空！");
//            		serviceProviderId = "99";
//            		return serviceProviderId;
//            	}
//            }else {
//            	//判断是否是联通号码
//    			IService service = ServiceContext.getService("CHECK_NUMBER_NET_BJT");
//    			JSONObject param = new JSONObject();
//    	        param.put("serialNumber", mobile);
//    	        JSONObject invoke = service.invoke(param);
//    	        String respCode = invoke.getString("respCode");
//    	        if ("0".equals(respCode)) {
//    	            //号码网络归属 ：0 非本网号码1 是本网号码
//    	            String numStatus = invoke.getString("numStatus");
//    	            if("0".equals(numStatus)) {
//    	            	serviceProviderId = "2";
//    	            }
//    	        } else {
//    	            // 接口返回失败
//    	            logger.error("H码中未查到的号码：" + mobile + "，调用本网异网接口>>CHECK_NUMBER_NET_BJT失败<< " + invoke);
//    	        }
//            }
//			
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(),e);
//		}
//		
//		
//		return serviceProviderId;
//	}
	
	/**
	 * 插入数据前先清空本地此时间段的
	 * @param startDateTime
	 * @param endDateTime
	 */
	private void removeLtData(String startDateTime,String endDateTime) {
		try {
			String sql = "delete from "+getTableName("cx_mix_12345_hw_tbilllog")+"  where CALLEND>='"+startDateTime+"' and CALLEND<='"+endDateTime+"'";
			logger.info("[removeLtDataSql]"+sql);
			//执行sql
			mysqlQuery.execute(sql);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	
	//获取从读取华为库的sql
	private EasySQL getHwSql(String startDateTime,String endDateTime,String hwTitle) {
		//获取日期的月份，使用此月份拼接华为表
		String month1 = startDateTime.substring(5,7);
		//获取日期的月份，使用此月份拼接华为表
		String month2 = endDateTime.substring(5,7);
		//获取日期的天 用作分区id
		String day1 = startDateTime.substring(8,10);
		//获取日期的天 用作分区id
		String day2 = endDateTime.substring(8,10);
		String days[] = {StringUtils.parseInt(day1)+"",StringUtils.parseInt(day2)+""};
		EasySQL sql = new EasySQL();
		if(!month1.equals(month2)) {
			if(hwTitle.contains("TOTAL")) {
				sql.append("select count(TOTAL) from ( ");
			}else {
				sql.append("select "+hwTitle+" from ( ");
			}
		}
		String tableName = hwUser + ".tbilllog" + StringUtils.parseInt(month1);
		sql.append("select "+hwTitle+" from "+tableName+" t where 1=1 and DEVICETYPE='2' ");
		//sql.appendIn(days," and PARTID ");
		sql.append(startDateTime,"  and t.callend >= to_date(?,'yyyy-mm-dd hh24:mi:ss') ");
		sql.append(endDateTime,"  and t.callend <= to_date(?,'yyyy-mm-dd hh24:mi:ss') ");
		//获取被叫号码
		sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
		//查询count比较慢 所以新增 ROWNUM 看现网数据 一天不会超过10w 所以直接<=100000
		if(hwTitle.contains("TOTAL")) {
			sql.append(" and ROWNUM <= 100000 ");
		}
//		if(!month1.equals(month2)) {;
//			tableName = hwUser + ".tbilllog" + StringUtils.parseInt(month2);
//			sql.append(" union all ");
//			sql.append("select "+hwTitle+" from "+tableName+" t where 1=1 and CALLIDNUM='-1' ");
//			sql.append(days," and PARTID ");
//			sql.append(startDateTime,"  and t.callend >= to_date(?,'yyyy-mm-dd hh24:mi:ss') ");
//			sql.append(endDateTime,"  and t.callend <= to_date(?,'yyyy-mm-dd hh24:mi:ss') ");
//			//获取被叫号码
//			sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
//			//查询count比较慢 所以新增 ROWNUM 看现网数据 一天不会超过10w 所以直接<=100000
//			if(hwTitle.contains("TOTAL")) {
//				sql.append(" and ROWNUM <= 100000 ");
//			}
//			sql.append(") t1");
//		}
		
		logger.info("[sql]"+sql.getSQL()+"[param]"+JSON.toJSONString(sql.getParams()));
		return sql;
	}
	
	/**
	 * 本地库中统计增长率
	 * @param startDate
	 * @param query
	 */
	public void statDateRate(String startDate,EasyQuery query) {
		EasySQL sql = new EasySQL("select * from "+getTableName("cx_mix_hw_stat_call_table")+" where 1=1 ");
		sql.append(startDate," and DATE_ID=?");
		try {
			List<JSONObject> jsons = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			int lastYearInboundCount = 0;
			int inboundCount = 0;
			int lastYearConnectCount = 0;
			int connectCount = 0;
			int netWorkAndCallCount = 0;
			int lastYearNetWorkAndCallCount = 0;
			//汇总数据因为现版本都是汇总后的接通率
			for(JSONObject json:jsons) {
				//不为网络受理量和回访量
				if(!"4".equals(json.getString("HOT_LINE_TYPE")) && !"5".equals(json.getString("HOT_LINE_TYPE"))) {
					lastYearInboundCount = lastYearInboundCount + json.getIntValue("LAST_YEAR_INBOUND_COUNT");
					inboundCount = inboundCount + json.getIntValue("INBOUND_COUNT");
					lastYearConnectCount = lastYearConnectCount + json.getIntValue("LAST_YEAR_CONNECT_COUNT");
					connectCount = connectCount + json.getIntValue("CONNECT_COUNT");
				}
				netWorkAndCallCount = netWorkAndCallCount + json.getIntValue("CONNECT_COUNT");
				lastYearNetWorkAndCallCount = lastYearNetWorkAndCallCount +  json.getIntValue("LAST_YEAR_CONNECT_COUNT");
				
			}
			//呼入数量与上一年增长率
			double lastYearInbountGrowthRate = 0;
			//接通数量与上一年增长率
			double lastYearConnectGrowthRate = 0;
			//上一年总接通率
			double lastYearConnRate = 0;
			//今年总接通率
			double connRate = 0;
			//接通率与上一年比增长率
			double lastYearConnectRateGrowthRate = 0;
			//网络回访数量和通话成功数量与上一年增长率
			double lastYearNetWorkAndCallRate = 0;
			//呼入数量与上一年增长率
			if(lastYearInboundCount!=0)
				lastYearInbountGrowthRate = getDivide(inboundCount,lastYearInboundCount);
			
			//接通数量与上一年增长率
			if(lastYearConnectCount!=0)
				lastYearConnectGrowthRate = getDivide(connectCount,lastYearConnectCount);
			
			//上一年总接通率
			if(lastYearInboundCount!=0)
				lastYearConnRate = getDivide(lastYearConnectCount,lastYearInboundCount);
			//今年总接通率
			if(inboundCount!=0)
				connRate = getDivide(connectCount,inboundCount);
			//接通率与上一年比增长率
			if(lastYearConnRate!=0)
				lastYearConnectRateGrowthRate = getDivide(connRate,lastYearConnRate);
			//网络回访数量和通话成功数量与上一年增长率
			if(lastYearNetWorkAndCallCount!=0)
				lastYearNetWorkAndCallRate = getDivide(netWorkAndCallCount,lastYearNetWorkAndCallCount);
			
			//将数据保存入库
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID");
			record.set("DATE_ID", startDate);
			record.set("LAST_YEAR_INBOUND_GROWTH_RATE", lastYearInbountGrowthRate);
			record.set("LAST_YEAR_CONNECT_GROWTH_RATE", lastYearConnectGrowthRate);
			record.set("LAST_YEAR_CONNECT_RATE_GROWTH_RATE", lastYearConnectRateGrowthRate);
			//record.set("CONNECT_RATE", connRate);
			//保存接通总数
			record.set("CONNECT_ALL_COUNT", connectCount);
			//网络回访数量和通话成功数量与上一年增长率
			record.set("LAST_YEAR_NETWORK_AND_CALL_RATE", lastYearNetWorkAndCallRate);
			query.update(record);
			

			String updateSql = "update "+getTableName("cx_mix_hw_stat_call_table_hour")
			+" t2 set CONNECT_ALL_COUNT = (select number from  (select DATE_ID,HOUR_ID,SUM(CONNECT_COUNT) number"
			+ " from ((SELECT * FROM "+getTableName("cx_mix_hw_stat_call_table_hour")+" where HOT_LINE_TYPE='1') "
			+ "union all (SELECT * FROM "+getTableName("cx_mix_hw_stat_call_table_hour")+" where HOT_LINE_TYPE='3'))"
			+ " t1  GROUP BY DATE_ID,HOUR_ID) t1 where  t2.DATE_ID=t1.DATE_ID and t2.HOUR_ID=t1.HOUR_ID LIMIT 1)"
			+ " where DATE_ID = '"+startDate+"' and HOT_LINE_TYPE in('1','3') ";//更新类型为1+3的总数
			query.execute(updateSql);
			
			
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			e.printStackTrace();
		}
	}
	
	/**
	 * 保存从本地库中统计的数据
	 * @param startDate
	 * @param putCache
	 * @return
	 */
  	public void statDate(String startDate,EasyQuery query,String isToday) {
		try {
			//初始化数据 1:12345热线 联合主键
			JSONObject param = statHwData(startDate,"1",query,isToday);
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID","HOT_LINE_TYPE");
			record.setColumns(param);
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			//初始化数据3:企业服务热线 联合主键
			param = statHwData(startDate,"3",query,isToday);
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID","HOT_LINE_TYPE");
			record.setColumns(param);
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			//初始化数据4:网络受理量
			param = statHwData(startDate,"4",query,isToday);
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID","HOT_LINE_TYPE");
			record.setColumns(param);
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			
			//初始化数据5:回访受理量
			param = statHwData(startDate,"5",query,isToday);
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID","HOT_LINE_TYPE");
			record.setColumns(param);
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			//初始化数据9:整合热线数据
			param = statHwData(startDate,"9",query,isToday);
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table"),"DATE_ID","HOT_LINE_TYPE");
			record.setColumns(param);
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			//进入配置界面更新占比值
//			param = getServiceProvider(startDate,query);
//			record = new EasyRecord(getTableName("big_moniter_data_12345_v2"),"IS_USE");
//			record.set("IS_USE", "1");
//			record.setColumns(param);
//			if(!mysqlQuery.update(record)) {[']
//				mysqlQuery.save(record);
//			}
			String inWhere ="(1,3)";//如果配置项中未勾选只统计12345数据时统计12345和12315
			if("0".equals(getConfigureData().getString("CALL_IN_USE"))) {//勾选了时只统计12345
				inWhere ="(1)";
			}
			String sql = "	UPDATE "+getTableName("cx_mix_hw_stat_call_table")+" set  CONNECT_RATE=(select case when sum( INBOUND_COUNT )<>0 "
					+ "then ROUND( sum( CONNECT_COUNT ) / sum( INBOUND_COUNT ), 4 ) * 100 else 0 end from "+getTableName("cx_mix_hw_stat_call_table_hour")
					+" where DATE_ID='"+startDate+"' and HOT_LINE_TYPE IN "+inWhere+" ) where   DATE_ID='"+startDate+"'";
			logger.info("update:"+sql);
			mysqlQuery.execute(sql);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
  	
  	/**
	 * 保存从本地库中统计的数据
	 * @param startDate
	 * @param putCache
	 * @return
	 */
  	public void statHourDate(String startDate,String hourId,EasyQuery query,boolean isToday) {
		try {
			logger.info("[startDate]"+startDate+"[hourId]"+hourId);
			//初始化数据 1:12345热线 联合主键
			JSONObject param = statHwHourData(startDate,"1",hourId,query);
			if(hourId.equals(EasyDate.getCurrentDateString().substring(11, 13)) && startDate.equals(EasyDate.getCurrentDateString("yyyy-MM-dd"))) {//如果为当天这个小时则去统计这个
				param.putAll(getToDayCallCount(startDate,hourId,query));
			}
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table_hour"),"DATE_ID","HOT_LINE_TYPE","HOUR_ID");
			record.setColumns(param);
			logger.info("[param1]"+record.toJSONString());
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			//初始化数据3:企业服务热线 联合主键
			param = statHwHourData(startDate,"3",hourId,query);
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table_hour"),"DATE_ID","HOT_LINE_TYPE","HOUR_ID");
			record.setColumns(param);
			logger.info("[param2]"+record.toJSONString());
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}
			if(hourId.equals(EasyDate.getCurrentDateString().substring(11, 13)) && isToday) {//to do 2025年5月10日 网络工单量只能取当前小时的，因为给的是当天汇总数据
				//初始化数据4:网络受理量
				param = statHwHourData(startDate,"4",hourId,query);
				record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table_hour"),"DATE_ID","HOT_LINE_TYPE","HOUR_ID");
				record.setColumns(param);
				if(!mysqlQuery.update(record)) {
					mysqlQuery.save(record);
				}
			}
			param = statHwHourData(startDate,"9",hourId,query);
			if(hourId.equals(EasyDate.getCurrentDateString().substring(11, 13)) && startDate.equals(EasyDate.getCurrentDateString("yyyy-MM-dd"))) {//如果为当天这个小时则去统计这个
				param.putAll(getToDayCallCount(startDate,hourId,query));
			}
			record = new EasyRecord(getTableName("cx_mix_hw_stat_call_table_hour"),"DATE_ID","HOT_LINE_TYPE","HOUR_ID");
			record.setColumns(param);
			logger.info("[param1]"+record.toJSONString());
			if(!mysqlQuery.update(record)) {
				mysqlQuery.save(record);
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	
	private JSONObject getServiceProvider(String startDate,EasyQuery query) {
		JSONObject result = new JSONObject();
		try {
			EasySQL sql = new EasySQL("SELECT sum(case when IS_THIS_WEBSITE='1' then 1 else 0 end) CALLIN_CMCC,"
					+ "sum(case when IS_THIS_WEBSITE='0' then 1 else 0 end) CALLIN_CTCC,"
					+ "sum(case when IS_THIS_WEBSITE='2' then 1 else 0 end) CALLIN_CUCC"
					+ " FROM  "+getTableName("cx_mix_12345_hw_tbilllog")+" where 1=1 ");
			sql.append(startDate.replace("-", ""), " and DATEID=?");
			JSONObject json = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(json!=null) {
				//移动
				int callinCmcc = json.getIntValue("CALLIN_CMCC");
				//电信
				int callinCtcc = json.getIntValue("CALLIN_CTCC");
				//联通
				int callinCucc = json.getIntValue("CALLIN_CUCC");
				//总数
				int count = callinCmcc+callinCtcc+callinCucc;
				if(count>0) {
					result.put("CALLIN_CMCC", ((callinCmcc*100)/count));
					result.put("CALLIN_CTCC", ((callinCtcc*100)/count));
					result.put("CALLIN_CUCC", 100-((callinCmcc*100)/count)-((callinCtcc*100)/count));
				}else {
					result.put("CALLIN_CMCC", "");
					result.put("CALLIN_CTCC", "");
					result.put("CALLIN_CUCC", "");
				}
				
				sql.append( " and RELEASECAUSE <> '551' ");
				json = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				if(json!=null) {
					//移动
					int answerCmcc = json.getIntValue("CALLIN_CMCC");
					//电信
					int answerCtcc = json.getIntValue("CALLIN_CTCC");
					//联通
					int answerCucc = json.getIntValue("CALLIN_CUCC");
					//总数
					count = answerCmcc+answerCtcc+answerCucc;
					if(count>0) {
						result.put("ANSWER_CMCC", ((answerCmcc*100)/count));
						result.put("ANSWER_CTCC", ((answerCtcc*100)/count));
						result.put("ANSWER_CUCC", 100-((answerCmcc*100)/count)-((answerCtcc*100)/count));
					}else {
						result.put("ANSWER_CMCC", "");
						result.put("ANSWER_CTCC", "");
						result.put("ANSWER_CUCC", "");
					}
					
				}
			}
			
		} catch (Exception e) {
			// TODO: handle exception
		}
		return result;
	}

	/**
	 * 传入开始时间 统计此时间内的数据
	 * @param startDate 2022-04-07
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @isToday 1为定时任务只保存今天 2手动触发不读取详细数据
	 * @return
	 */
	private JSONObject statHwData(String startDate,String hotLineType,EasyQuery query,String isToday) {
		JSONObject result = new JSONObject();
		//日期
		result.put("DATE_ID", startDate);
		//类型
		result.put("HOT_LINE_TYPE", hotLineType);
		String month = "";
		if(StringUtils.isBlank(startDate) || startDate.length()!=10) {
			logger.error("传入非法开始时间："+startDate);
			return result;
		}
		//获取日期的月份，使用此月份拼接华为表
		month = startDate.substring(5,7);
		String lastStartDate = EasyDate.addTime("yyyy-MM-dd", startDate, Calendar.YEAR, -1);
		
		//月份
		result.put("MONTH_ID", StringUtils.parseInt(month));
		//获取当前是第几周
		result.put("WEEK_ID", getDateWeek(startDate)-1);
		//获取到本地入库的表
		String tableName = getTableName("cx_mix_12345_hw_tbilllog");
		//获取上一年统计表
		String lastTableName = getTableName("cx_mix_hw_stat_call_table");
		//当不为网络工单受理量和回访量
		if(!"4".equals(hotLineType) && !"5".equals(hotLineType)) {
			JSONObject json = getCallNumber(startDate, hotLineType, query);
			//呼入总数
			//int inboundCount = getCallCount(tableName,startDate,hotLineType,"1",query,"","CALLEENO");
			int inboundCount = json.getIntValue("CALLIN_COUNT");
			result.put("INBOUND_COUNT", inboundCount*number);
			if("1".equals(hotLineType)) {
				cache.put("INBOUND_COUNT_12345", inboundCount*number+"");
			}
			//上一年呼入数量
			int lastYearInboundCount = getLastCallCount(lastTableName,lastStartDate,hotLineType,"1",query);
			result.put("LAST_YEAR_INBOUND_COUNT", lastYearInboundCount);
			//接通总数
			//int connectCount = getCallCount(tableName,startDate,hotLineType,"2",query,"","CALLEENO");
			int connectCount = json.getIntValue("CALLIN_ANSWER_COUNT");
			result.put("CONNECT_COUNT",connectCount*number);
			//上一年接通数量
			int lastYearConnectCount = getLastCallCount(lastTableName,lastStartDate,hotLineType,"2",query);
			result.put("LAST_YEAR_CONNECT_COUNT", lastYearConnectCount);
			//接通率
			//result.put("CONNECT_RATE", getDivide(connectCount,inboundCount));
			//上一年接通率
			result.put("LAST_YEAR_CONNECT_RATE",  getDivide(lastYearConnectCount,lastYearInboundCount));
			//大屏上只需要12345热线的 所以只统计12345热线的
	//		if("1".equals(hotLineType)) {
			//获取当日剔重号码
			int removeWegightCount = getRemoveWegightCount(tableName,startDate,query,hotLineType);
			logger.info("removeWegightCount"+removeWegightCount*number);
			result.put("REMOVE_WEGIGHT_COUNT", removeWegightCount*number);
			
			//当日首次来电总量（七日内如果都没打过则算首次来电）
			int firstCallCount = getFirstCallCount(tableName,startDate,query,hotLineType);
			logger.info("firstCallCount"+firstCallCount*number);
			result.put("FIRST_CALL_COUNT", firstCallCount*number);
			
			//当日非首次来电总量
			int notFirstCallCount = removeWegightCount - firstCallCount;
			result.put("NOT_FIRST_CALL_COUNT", notFirstCallCount);
			int thisWebsiteCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_THIS_WEBSITE=1 and DEVICETYPE in(1,2,5)","CALLEENO");//本网数量
			int thisLocalCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_LOCATION=1 and DEVICETYPE in(1,2,5) ","CALLEENO");//本地数量
			int phoneCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_PHONE=0  and DEVICETYPE in(1,2,5)","CALLEENO");//手机数量
			int callInCount = getCallCount(tableName,startDate,hotLineType,"1",query," and DEVICETYPE in(1,2,5) and IS_PHONE is not null","CALLEENO");//获取详情表中的通话数量
			if(callInCount>=thisWebsiteCount)//因为呼入数取得统计表，本网异网取得详情，所以存在统计表中的不是最新数据，但是详情是最新的情况
				result.put("THIS_WEBSITE_RATE", getDivide(thisWebsiteCount,callInCount));
			if(callInCount>=thisLocalCount)//因为呼入数取得统计表，本地异地取得详情，所以存在统计表中的不是最新数据，但是详情是最新的情况
				result.put("THSI_LOCAL_RATE", getDivide(thisLocalCount,callInCount));
			if(callInCount>=phoneCount)//因为呼入数取得统计表，手机数量取得详情，所以存在统计表中的不是最新数据，但是详情是最新的情况
				result.put("PHONE_RATE", getDivide(phoneCount,callInCount));
			
		} else if("4".equals(hotLineType)){
			JSONObject configureData = getConfigureData();
			//网络工单受理量
//			if("0".equals(configureData.getString(" "))){
//				String connectCount = getNetWorkCount(startDate);//通过	接口获取值
//				result.put("CONNECT_COUNT",connectCount);
//			}else{
//				String newDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
//				if(startDate.equals(newDate)) {
//					String connectCount = cache.get("ACCEPTANCE_NETWORK");
//					String inboundCount = cache.get("INBOUND_COUNT_12345");
//					if(StringUtils.isBlank(inboundCount)) {
//						result.put("CONNECT_COUNT",connectCount);
//					}else {
//						result.put("CONNECT_COUNT",StringUtils.parseInt(inboundCount)*0.22);
//					}
//				}
//				else {
//					//result.put("CONNECT_COUNT","0");
//				}
//			}
			String connectCount = getNetWorkCount(startDate);//通过	接口获取值
			result.put("CONNECT_COUNT",connectCount);
//			String newDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
//			if(startDate.equals(newDate)) {
//				String connectCount = cache.get("ACCEPTANCE_NETWORK");
//				String inboundCount = cache.get("INBOUND_COUNT_12345");
//				if(StringUtils.isBlank(inboundCount)) {
//					result.put("CONNECT_COUNT",connectCount);
//				}else {
//					result.put("CONNECT_COUNT",StringUtils.parseInt(inboundCount)*0.22);
//				}
//			}
//			else {
//				//result.put("CONNECT_COUNT","0");
//			}
			//上一年网络工单受理量
			int lastYearConnectCount = getLastCallCount(lastTableName,lastStartDate,hotLineType,"2",query);
			result.put("LAST_YEAR_CONNECT_COUNT", lastYearConnectCount);
			result.put("LAST_YEAR_CONNECT_RATE", "100");
			//呼入总数
//			int inboundCount = getCallCount(tableName,startDate,"1","1",query,"","CALLEENO");
//			int thisWebsiteCount = getCallCount(tableName,startDate,"1","1",query," and IS_THIS_WEBSITE=1 and DEVICETYPE in(1,2,5) ","CALLEENO");//本网数量
//			int thisLocalCount = getCallCount(tableName,startDate,"1","1",query," and IS_LOCATION=1  and DEVICETYPE in(1,2,5)","CALLEENO");//本地数量
//			int phoneCount = getCallCount(tableName,startDate,"1","1",query," and IS_PHONE=0 and DEVICETYPE in(1,2,5) ","CALLEENO");//手机数量
//			result.put("THIS_WEBSITE_RATE", getDivide(thisWebsiteCount,inboundCount));
//			result.put("THSI_LOCAL_RATE", getDivide(thisLocalCount,inboundCount));
//			result.put("PHONE_RATE", getDivide(phoneCount,inboundCount));
		} else if("5".equals(hotLineType)){
			//回访量
			String newDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
			if(startDate.equals(newDate)) {
				String connectCount = cache.get("ACCEPTANCE_FLOW_UP");
				//String inboundCount = cache.get("INBOUND_COUNT_12345");
				result.put("CONNECT_COUNT",connectCount);
//				if(StringUtils.isBlank(inboundCount)) {
//					result.put("CONNECT_COUNT",connectCount);
//				}else {
//					result.put("CONNECT_COUNT",StringUtils.parseInt(inboundCount)*0.44);
//				}
				//上一年回访量
				int lastYearConnectCount = getLastCallCount(lastTableName,lastStartDate,hotLineType,"2",query);
				result.put("LAST_YEAR_CONNECT_COUNT", lastYearConnectCount);
				result.put("LAST_YEAR_CONNECT_RATE", "100");
				int thisWebsiteCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_THIS_WEBSITE=1 ","CALLEENO");//本网数量
				int thisLocalCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_LOCATION=1 ","CALLEENO");//本地数量
				int phoneCount = getCallCount(tableName,startDate,hotLineType,"1",query," and IS_PHONE=0 ","CALLEENO");//手机数量
				int callInCount = getCallCount(tableName,startDate,hotLineType,"1",query,"","CALLEENO");//获取通话数量
				result.put("THIS_WEBSITE_RATE", getDivide(callInCount,thisWebsiteCount));
				result.put("THSI_LOCAL_RATE", getDivide(callInCount,thisLocalCount));
				result.put("PHONE_RATE", getDivide(callInCount,phoneCount));
			}
			else {
				//result.put("CONNECT_COUNT","0");
			}
			
			
		}
		if("2".equals(isToday)) {
			result.remove("CONNECT_COUNT");
			result.remove("NOT_FIRST_CALL_COUNT");
			result.remove("FIRST_CALL_COUNT");
			result.remove("REMOVE_WEGIGHT_COUNT");
			result.remove("CONNECT_RATE");
			result.remove("INBOUND_COUNT");
		}
		return result;
	}

//	/**
//	 * 获取网络工单受理量
//	 * @return
//	 */
//	public String getNetWorkCount(String dateId){
//		String netWorkCount = "";
//		try {
//			String username = Constants.thisContext.getProperty("SHOU_XIN_USER_NAME", "");
//			int expiresIn = 0;
//			String password = Constants.thisContext.getProperty("SHOU_XIN_PASSWORD", "");
//			String url = Constants.thisContext.getProperty("SHOU_XIN_URL", "");
//			JSONObject param = new JSONObject();
//			param.put("username", username);
//			param.put("password", password);
//			String key = "AUTHORIZATION_KEY";//鉴权参数 存储的缓存时间为失效时间
//			String refreshToken = cache.get("REFRESH_TOKEN_KEY");//刷新凭证的token，如果鉴权参数失效了则去刷新凭证
//			String authorization = cache.get(key);//鉴权参数
//			String authorizationOld = cache.get(key+"_OLD");//鉴权参数
//			if(StringUtils.isBlank(authorization) && StringUtils.isNotBlank(refreshToken) && StringUtils.isNotBlank(authorizationOld)) {//
//				param.put("refreshToken", refreshToken);
//				HttpResp resp = HttpUtil.sendPost(url+"/bigScreenInterface/oauth2/refreshToken"
//						,"refreshToken="+refreshToken,HttpUtil.TYPE_FORM,"UTF-8","Authorization",authorizationOld);
//				logger.info("【刷新token】"+JSON.toJSONString(resp));
//				if(resp!=null && resp.getCode()==200) {
//					JSONObject result = JSONObject.parseObject(resp.getResult());
//					authorization = result.getString("token_type")+" "+result.getString("access_token");
//					refreshToken = result.getString("refresh_token");
//					expiresIn = result.getIntValue("expires_in");
//					cache.put("REFRESH_TOKEN_KEY", refreshToken);//存永久
//					cache.put(key, authorization,expiresIn-10);//比失效时间少10s
//				}
//			}else{
//				HttpResp resp = HttpUtil.sendPost(url+"/bigScreenInterface/oauth2/getToken","username="+username+"&password="+ MD5Util.lowMD5_32(password)
//						,HttpUtil.TYPE_FORM,"UTF-8");
//				logger.info("【获取token】"+JSON.toJSONString(resp));
//				if(resp!=null && resp.getCode()==200) {
//					JSONObject result = JSONObject.parseObject(resp.getResult());
//					authorization = result.getString("token_type")+" "+result.getString("access_token");
//					refreshToken = result.getString("refresh_token");
//					expiresIn = result.getIntValue("expires_in");
//					cache.put("REFRESH_TOKEN_KEY", refreshToken);//存永久
//					cache.put(key, authorization,expiresIn-10);//比失效时间少10s
//					cache.put(key+"_OLD", authorization);//存永久一个
//				}
//			}
//			if(StringUtils.isNotBlank(authorization)){
//				param = new JSONObject();
//				param.put("dateid", dateId);
//				HttpResp resp = HttpUtil.sendPost(url+"/interfaces/hotline/getNetOrderCount","dateid="+dateId
//						,HttpUtil.TYPE_FORM,"UTF-8","Authorization",authorization);
//				logger.info("【获取网络量数据】"+JSON.toJSONString(resp));
//				if(resp!=null && resp.getCode()==200) {
//					JSONObject result = JSONObject.parseObject(resp.getResult());
//					netWorkCount = result.getString("data");
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(),e);
//		}
//		return netWorkCount;
//	}
	/**
	 * 获取网络工单受理量
	 * @return
	 */
	public String getNetWorkCount(String dateId){
		String netWorkCount = "";
		try {
			String username = Constants.thisContext.getProperty("SHOU_XIN_USER_NAME", "");
			String password = Constants.thisContext.getProperty("SHOU_XIN_PASSWORD", "");
			String url = Constants.thisContext.getProperty("SHOU_XIN_URL", "");
			JSONObject param = new JSONObject();
			param.put("username", username);
			param.put("password", MD5Util.lowMD5_32(password));
			logger.info("【param】"+param);
			String authorization = "";//鉴权参数
			HttpResp resp = HttpUtil.sendPost(url+"/bigScreenInterface/oauth2/getToken",
					"username="+username+"&password="+MD5Util.lowMD5_32(password),HttpUtil.TYPE_FORM,"UTF-8");
			logger.info("【获取token】"+JSON.toJSONString(resp));
			if(resp!=null && resp.getCode()==200) {
				JSONObject result = JSONObject.parseObject(resp.getResult());
				authorization = result.getString("token_type")+result.getString("access_token");
			}

			if(StringUtils.isNotBlank(authorization)){
				param = new JSONObject();
				param.put("dateid", dateId);
				resp = HttpUtil.sendPost(url+"/bigScreenInterface/interfaces/hotline/getNetOrderCount",param.toJSONString()
						,HttpUtil.TYPE_JSON,"UTF-8","Authorization",authorization);
				logger.info("【获取网络量数据】"+JSON.toJSONString(resp));
				if(resp!=null && resp.getCode()==200) {
					JSONObject result = JSONObject.parseObject(resp.getResult());
					netWorkCount = result.getString("data");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return netWorkCount;
	}
	
	/**
	 * 传入开始时间 统计此时间内的数据
	 * @param startDate 2022-04-07
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @isToday 1为定时任务只保存今天 2手动触发不读取详细数据
	 * @return
	 * @throws SQLException 
	 */
	private JSONObject statHwHourData(String startDate,String hotLineType,String hourId,EasyQuery query) throws SQLException {
		JSONObject result = new JSONObject();
		//日期
		result.put("DATE_ID", startDate);
		//类型
		result.put("HOT_LINE_TYPE", hotLineType);
		//类型
		result.put("HOUR_ID", hourId);
		//if(hourId)
		
		if(StringUtils.isBlank(startDate) || startDate.length()!=10) {
			logger.error("传入非法开始时间："+startDate);
			return result;
		}
		//当不为网络工单受理量时
		if(!"4".equals(hotLineType)) {
			JSONObject numberJson = getHourCallNumber(result.getString("DATE_ID"),hotLineType,hourId,query);
			//呼入总数
			int inboundCount = numberJson.getIntValue("CALLIN_COUNT");
			result.put("INBOUND_COUNT", inboundCount*number);
			//接通总数
			int connectCount = numberJson.getIntValue("CALLIN_ANSWER_COUNT");
			result.put("CONNECT_COUNT",connectCount*number);
			//接通率
			result.put("CONNECT_RATE", getDivide(connectCount,inboundCount));
			
		} else {
			JSONObject networkSituation = JSONObject.parseObject(cache.get(BigMonitorCache.NETWORK_SITUATION));
			int intLastHour = (StringUtils.parseInt(hourId)-1);
			String lastHour = intLastHour>9?intLastHour+"":"0"+intLastHour;
			int connectCount = 0;
			if(networkSituation!=null) {
				connectCount = networkSituation.getIntValue("networkNum");
			}
			//网络工单受理量
			int lastHourNum = 0; 
			
			if(intLastHour>=0) {
				lastHourNum = query.queryForInt("select sum(CONNECT_COUNT) from "+getTableName("cx_mix_hw_stat_call_table_hour")
				+" where DATE_ID=? and HOT_LINE_TYPE=? and hour_id<=?", new Object[] {EasyDate.getCurrentDateString("yyyy-MM-dd"),"4",lastHour});
			}
//			int callCount =  query.queryForInt("select sum(CONNECT_ALL_COUNT) from "+getTableName("cx_mix_hw_stat_call_table_hour")
//			+" where DATE_ID=? and HOT_LINE_TYPE=? and HOUR_ID =?", new Object[] {EasyDate.getCurrentDateString("yyyy-MM-dd"),"1",hourId});
			int connCount = connectCount-lastHourNum;
			if(connCount<0) {
				connCount = 0;
			}
//			if(connCount>callCount) {
//				connCount = callCount;//如果网络量大于电话量则取电话量
//			}
			logger.info("[connectCount]"+connectCount+"[lastHour]"+lastHour+"[connCount]"+connCount+"[lastHourNum]"+lastHourNum);
			result.put("CONNECT_COUNT",connCount);
		}
		return result;
	}
	
	/**
	 * 获取当日剔重号码总量、当日首次来电总量（七日内如果都没打过则算首次来电）、当日非首次来电总量

	 * @param hourId
	 * @return
	 * @throws SQLException 
	 */
	public JSONObject getToDayCallCount(String startDate,String hourId,EasyQuery query) throws SQLException {
		JSONObject result = new JSONObject();
		param = getCount(startDate,startDate,query);
		int removeWegightCount = param.getInteger("REMOVE_WEGIGHT_COUNT");
		int firstCallCount = param.getInteger("FIRST_CALL_COUNT");
		int notFirstCallCount = param.getInteger("NOT_FIRST_CALL_COUNT");
		if(!"00".equals(hourId)) {//如果当前时间不是1点则不去查询当天历史数据
			EasySQL sql = new EasySQL("select sum(REMOVE_WEGIGHT_COUNT) REMOVE_WEGIGHT_COUNT,");
			sql.append("sum(FIRST_CALL_COUNT) FIRST_CALL_COUNT,");
			sql.append("sum(NOT_FIRST_CALL_COUNT) NOT_FIRST_CALL_COUNT from ");
			sql.append(getTableName("cx_mix_hw_stat_call_table_hour")+" where 1=1 ");
			sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and DATE_ID=?");
			sql.append(hourId," and HOUR_ID<>?");
			sql.append(1," and HOT_LINE_TYPE=?");
			JSONObject toDayData = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(toDayData!=null) {
				removeWegightCount = removeWegightCount-toDayData.getIntValue("REMOVE_WEGIGHT_COUNT");
				firstCallCount = firstCallCount-toDayData.getIntValue("FIRST_CALL_COUNT");
				notFirstCallCount = notFirstCallCount-toDayData.getIntValue("NOT_FIRST_CALL_COUNT");
			}
		}
		if(removeWegightCount>0)
			result.put("REMOVE_WEGIGHT_COUNT", removeWegightCount);
		if(firstCallCount>0)
			result.put("FIRST_CALL_COUNT", firstCallCount);
		if(notFirstCallCount>0)	
			result.put("NOT_FIRST_CALL_COUNT", notFirstCallCount);
		
		return result;
		
	}
	
	/**
	 * 通过类型查询上一年呼叫数量
	 * @param startDate 开始时间 2022-04-07
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @param statType 统计类型 1：呼入总数 2：呼入接通数
	 * @return
	 */
	private int getLastCallCount(String tableName,String startDate,String hotLineType,String statType,EasyQuery query) {
		int count = 0;
		String title = "sum(INBOUND_COUNT)";
		//通过获取统计title
		if("2".equals(statType)) {
			title = "sum(CONNECT_COUNT)";
		}
		
		
		EasySQL sql = new EasySQL("SELECT "+title+" from " + tableName + " t where 1=1  ");
		sql.append(startDate , " and t.DATE_ID >= ?");
		sql.append(startDate , " and t.DATE_ID <= ?");
		sql.append(hotLineType , " and t.HOT_LINE_TYPE = ?");
		
		try {
			//logger.info(sql.getSQL()+" "+JSON.toJSONString(sql.getParams()));
			count = query.queryForInt(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("[getCallCount]"+e.getMessage(),e);
		}
		return count;
	}
	
	/**
	 * 
	 * @param tableName
	 * @param startDate
	 * @param endDate
	 * @param hotLineType
	 * @param query
	 * @return
	 */
	private JSONObject getHourCallNumber(String startDate,String hotLineType,String hourId,EasyQuery query) {
		JSONObject result = new JSONObject();
		EasySQL sql = new EasySQL("select ");
//		sql.append(" sum(CALLIN_COUNT) CALLIN_COUNT, "
//				+ "	sum(CALLIN_ANSWER_COUNT) CALLIN_ANSWER_COUNT, "
//				+ "	ROUND(sum(CALLIN_ANSWER_COUNT)/sum(CALLIN_COUNT),4) CALLIN_RATE ");
		sql.append(" sum(AGENT_USE_COUNT) CALLIN_COUNT, "
				+ "	sum(AGENT_ANSWER_COUNT) CALLIN_ANSWER_COUNT, "
				+ "	ROUND(sum(AGENT_ANSWER_COUNT)/sum(AGENT_USE_COUNT),4) CALLIN_RATE ");
		sql.append( " from "+getTableName("tbill_call_stat_rpt")+" t1  ");
		sql.append(" where 1=1 ");
		sql.append(startDate," and t1.DAY=?");
		
//		if(StringUtils.isBlank(hourId)) {//因为统计表中的小时从00开始的，所以需要-1
//			int hourIntId = StringUtils.parseInt(hourId)-1;
//			if(hourIntId<10) {
//				hourId = "0"+hourIntId;
//			}else {
//				hourId = String.valueOf(hourIntId);
//			}
//		}
		sql.append(hourId," and  t1.HOUR=?");
		
		if("1".equals(hotLineType)) {
			sql.append("12345"," and CALLEENO=?");
		}else if("3".equals(hotLineType)) {
			sql.append("12315"," and CALLEENO=?");
		}else if("9".equals(hotLineType)) {
			sql.append("12345"," and CALLEENO<>?");
		}
		sql.append(" and USER_LEVEL<>9 ");
		logger.info("[sql]"+sql.getSQL()+"[param]"+JSON.toJSONString(sql.getParams()));
		try {
			result = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return result;
	}
	
	/**
	 * 
	 * @param tableName
	 * @param startDate
	 * @param endDate
	 * @param hotLineType
	 * @param query
	 * @return
	 */
	private JSONObject getCallNumber(String startDate,String hotLineType,EasyQuery query) {
		JSONObject result = new JSONObject();
		EasySQL sql = new EasySQL("select ");
//		sql.append(" sum(CALLIN_COUNT) CALLIN_COUNT, "
//				+ "	sum(CALLIN_ANSWER_COUNT) CALLIN_ANSWER_COUNT, "
//				+ "	ROUND(sum(CALLIN_ANSWER_COUNT)/sum(CALLIN_COUNT),4) CALLIN_RATE ");
		sql.append(" sum(AGENT_USE_COUNT) CALLIN_COUNT, "
				+ "	sum(AGENT_ANSWER_COUNT) CALLIN_ANSWER_COUNT, "
				+ "	ROUND(sum(AGENT_ANSWER_COUNT)/sum(AGENT_USE_COUNT),4) CALLIN_RATE ");
		sql.append( " from "+getTableName("tbill_call_stat_rpt")+" t1  ");
		sql.append(" where 1=1 ");
		sql.append(startDate," and t1.DAY=?");
		if("1".equals(hotLineType)) {//12345热线
			sql.append("12345"," and CALLEENO=?");
		}else if("3".equals(hotLineType)) {//12315热线
			sql.append("12315"," and CALLEENO=?");
		}else if("9".equals(hotLineType)) {//整合热线数量
			sql.append("12345"," and CALLEENO<>?");
		}
		sql.append(" and USER_LEVEL<>9 ");
		logger.info("[sql]"+sql.getSQL()+"[param]"+JSON.toJSONString(sql.getParams()));
		try {
			result = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return result;
	}
	
	/**
	 * 通过类型查询呼叫数量
	 * @param startDate 开始时间 2022-04-07
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @param statType 统计类型 1：呼入总数 2：呼入接通数
	 * @return
	 */
	private int getHourCallCount(String tableName,String startDate,String endDate,String hotLineType,String statType,EasyQuery query) {
		int count = 0;
		//通过队列号
		
		EasySQL sql = new EasySQL("SELECT count(1) from " + tableName + " t where 1=1  ");
		sql.append(startDate , " and t.CALLEND >= ?");
		sql.append(endDate , " and t.CALLEND < ?");
		sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
		if("1".equals(hotLineType)) {
			String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
			for(String deviceOn:deviceOns) {
				sql.append(deviceOn, " and t.DEVICENO<>? ");
			}
		}else {
			sql.append("2" , " and t.DEVICETYPE = ?");
			sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
		}
		if("2".equals(statType)) {
			//呼入接通数排除无效电话
			sql.append(" and t.RELEASECAUSE <> '551' ");
		}
		try {
			//logger.info(sql.getSQL()+" "+JSON.toJSONString(sql.getParams()));
			count = query.queryForInt(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("[getHourCallCount]"+e.getMessage(),e);
		}
		return count;
	}
	
	
	
	/**
	 * 通过类型查询呼叫数量
	 * @param startDate 开始时间 2022-04-07
	 * @param hotLineType 热线类型 1:12345热线 2:12315热线 3:企业服务热线 联合主键
	 * @param statType 统计类型 1：呼入总数 2：呼入接通数
	 * @return
	 */
	private int getCallCount(String tableName,String startDate,String hotLineType,String statType,EasyQuery query,String whereStr,String direction) {
		int count = 0;
		//通过队列号
		EasySQL sql = new EasySQL("SELECT count(1) from " + tableName + " t where 1=1  ");
		sql.append(startDate.replace("-", "") , " and t.DATEID >= ?");
		sql.append(startDate.replace("-", "") , " and t.DATEID <= ?");
		sql.appendIn(Constants.get12345HotLinePhone().split(",")," and "+direction+" ");
		if("1".equals(hotLineType)) {
			String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
			for(String deviceOn:deviceOns) {
				sql.append(deviceOn, " and t.DEVICENO<>? ");
			}
		}else {
			sql.append("2" , " and t.DEVICETYPE = ?");
			sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
		}
		if("2".equals(statType)) {
			//呼入接通数排除无效电话
			sql.append(" and t.RELEASECAUSE <> '551' ");
		}
		if(StringUtils.isNotBlank(whereStr)) {
			sql.append(whereStr);
		}
		try {
			//logger.info(sql.getSQL()+" "+JSON.toJSONString(sql.getParams()));
			count = query.queryForInt(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("[getCallCount]"+e.getMessage(),e);
		}
		return count;
	}
	
	/**
	 * 获取当日剔重号码数量
	 * @param startDate 开始时间 2022-04-07
	 * @return
	 */
	private int getRemoveWegightCount(String tableName,String startDate,EasyQuery query,String hotLineType) {
		int count = 0;
		EasySQL sql = new EasySQL("select count(1) from (select distinct t.CALLERNO from " + tableName + " t where 1=1 ");
		sql.append(startDate.replace("-", "") , " and t.DATEID >= ?");
		sql.append(startDate.replace("-", "") , " and t.DATEID <= ?");
		sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
		if("1".equals(hotLineType)) {
			String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
			for(String deviceOn:deviceOns) {
				sql.append(deviceOn, " and t.DEVICENO<>? ");
			}
		}else {
			sql.append("2" , " and t.DEVICETYPE = ?");
			sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
		}
		sql.append(" ) t1");
		try {
			count = query.queryForInt(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("[getRemoveWegightCount]"+e.getMessage(),e);
		}
		return count;
	}
	
	/**
	 * 获取当日首次来电总量（七日内如果都没打过则算首次来电）
	 * @param startDate 开始时间 2022-04-07
	 * @return
	 */
	private int getFirstCallCount(String tableName,String startDate,EasyQuery query,String hotLineType) {
		int count = 0;
		
		EasySQL sql = new EasySQL();
		//如果是在一个月的话数据在一张表，如果不是需要union all另一张表
		sql.append("select sum(CALLERNOS) from (select count(t.CALLERNO) CALLERNOS  from "+ tableName +" t where 1=1  ");
		sql.append(startDate.replace("-", "") , " and t.DATEID >= ?");
		sql.append(startDate.replace("-", "") , " and t.DATEID <= ?");
		sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
		if("1".equals(hotLineType)) {
			String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
			for(String deviceOn:deviceOns) {
				sql.append(deviceOn, " and t.DEVICENO<>? ");
			}
		}else {
			sql.append("2" , " and t.DEVICETYPE = ?");
			sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
		}
		sql.append(" group by t.CALLERNO having count(t.CALLERNO)=1) t1");
		//分区id
		String [] partids = new String[6];
		//获取七天前的日期，因为使用的开始时间，所以就要-6
		String sevenStartDate = EasyDate.addTime("yyyy-MM-dd", startDate, Calendar.DATE, -6);
		String sevenEndDate = EasyDate.addTime("yyyy-MM-dd", startDate, Calendar.DATE, -1);
		//获取分区id
		int startDay = StringUtils.parseInt(sevenStartDate.substring(8, 10));
		int endDay = StringUtils.parseInt(sevenEndDate.substring(8, 10));
		if(endDay<startDay) {
			startDay = 0;
		}
		int num = 0;
		for(int i = startDay; i<=endDay; i++) {
			try {
				partids[num] = i+"";
				num = num+1;
			}catch (Exception e) {
			}
		}
		try {
			//获取当天非重复数量
			int maxNumber = query.queryForInt(sql.getSQL(), sql.getParams());
			sql = new EasySQL();
			sql.append("select count(1) from (select t.CALLERNO  from "+ tableName +" t where 1=1 ");
			sql.append(startDate.replace("-", "") , " and t.DATEID >= ?");
			sql.append(startDate.replace("-", "") , " and t.DATEID <= ?");
			sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
			if("1".equals(hotLineType)) {
				String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
				for(String deviceOn:deviceOns) {
					sql.append(deviceOn, " and t.DEVICENO<>? ");
				}
			}else {
				sql.append("2" , " and t.DEVICETYPE = ?");
				sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
			}
			sql.append(" group by t.CALLERNO having count(t.CALLERNO)=1) t1 inner join ");
			sql.append(" (select t.CALLERNO  from "+ tableName +" t where 1=1 ");
			//sql.appendIn(partids , " t.partid ");
			sql.append(sevenStartDate.replace("-", "") , " and t.DATEID >= ?");
			sql.append(sevenEndDate.replace("-", "") , " and t.DATEID <= ?");
			sql.append("1" , " and t.DEVICETYPE = ?");
			sql.appendIn(Constants.get12345HotLinePhone().split(",")," and CALLEENO ");
			if("1".equals(hotLineType)) {
				String[] deviceOns = Constants.getEntHotLineAgentId().split(",");
				for(String deviceOn:deviceOns) {
					sql.append(deviceOn, " and t.DEVICENO<>? ");
				}
			}else {
				sql.append("2" , " and t.DEVICETYPE = ?");
				sql.appendIn( Constants.getEntHotLineAgentId().split(","), " and t.DEVICENO ");
			}
			sql.append(" group by t.CALLERNO) t2 on t1.CALLERNO  = t2.CALLERNO ");
			//当天只接通过一次的和历史六天的比较
			logger.info("[getFirstCallCount]sql:"+sql.getSQL()+" param:"+JSON.toJSONString(sql.getParams()));
			int repetNumber = query.queryForInt(sql.getSQL(), sql.getParams());
			count = maxNumber - repetNumber;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("[getRemoveWegightCount]"+e.getMessage(),e);
		}
		return count;
	}
	/**
	 * 两个数相除保留两位小数(用于百分比的 已经x100)
	 * @param number1
	 * @param number2
	 * @return
	 */
	public double getDivide(int number1,int number2) {
		if(number2==0) {
			return 0;
		}
		double connReta = new BigDecimal(String.valueOf(number1*100)).divide(new BigDecimal(String.valueOf(number2)),2
				,BigDecimal.ROUND_HALF_DOWN).doubleValue();
		return connReta;
	}
	
	
	/**
	 * 两个数相除保留两位小数(用于百分比的 已经x100)
	 * @param number1
	 * @param number2
	 * @return
	 */
	public double getDivide(String number1) {
		BigDecimal roundedNumber =  new BigDecimal(number1).setScale(2, RoundingMode.HALF_UP);
		return roundedNumber.doubleValue();
	}
	/**
	 * 两个数相除保留两位小数(用于百分比的 已经x100)
	 * @param number1
	 * @param number2
	 * @return
	 */
	public double getDivide(double number1,double number2) {
		if(number2==0) {
			return 0;
		}
		double connReta = new BigDecimal(Double.toString(number1*100)).divide(new BigDecimal(Double.toString(number2)),2
				,BigDecimal.ROUND_HALF_DOWN).doubleValue();
		return connReta;
	}
	/**
	 * 根据传入的类型获取时间
	 * @param dateType 1：获取本周第一天 2：获取本月第一天 3：获取本年第一月
	 * @return
	 */
	public static String getDate(String dateType) {
		String dateStr = "";
		Calendar calendar = Calendar.getInstance();
		if("1".equals(dateType)) {
			calendar.add(Calendar.DATE, -6);
		}else if("2".equals(dateType)) {
			//获取七周前的日期
			calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			Date firstDayOfMonth = calendar.getTime();
			calendar.add(Calendar.DATE, -42);
			Date firstDayOfMonth1 = calendar.getTime();
			//不相等则证明跨年了则取今年的第一天
//			if(firstDayOfMonth1.getYear()!=firstDayOfMonth.getYear()) {
//				calendar = Calendar.getInstance();
//				calendar.set(Calendar.DAY_OF_YEAR, 1);
//			}
		}else {
			calendar.add(Calendar.MONTH, -12);
			 calendar.set(Calendar.DAY_OF_MONTH, 1);
		}
		Date firstDayOfMonth = calendar.getTime();
		dateStr = EasyDate.dateToString(firstDayOfMonth,"yyyy-MM-dd");
		return dateStr;
	}
	
	/**
	 * 获取一个时间是这个月的第几天
	 * @param dateType 
	 * @return
	 */
	public int getMonthDate(String startDate) {
		Calendar calendar = Calendar.getInstance();
		Date date = EasyDate.stringToDate("yyyy-MM-dd",startDate);
		calendar.setTime(date);
		int monthDay = calendar.get(Calendar.DAY_OF_MONTH);
		return monthDay;
	}
	
	/**
     *  获取两个日期之间的所有日期 (年-月-日)
     *  
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getDateList(String startTime, String endTime){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 声明保存日期集合
        List<String> list = new ArrayList<String>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);
 
            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime()<=endDate.getTime()){
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把日期增加一天
                calendar.add(Calendar.DATE, 1);
                // 获取增加后的日期
                startDate=calendar.getTime();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }
    
    /**
     * 获取明天的时间戳
     * @return
     */
    private static long getTomorrowTimestamp() {
    	Calendar calendar = Calendar.getInstance();
    	calendar.add(Calendar.DAY_OF_YEAR, 1); // 获取明天的日期
    	calendar.set(Calendar.HOUR_OF_DAY, 2); // 设置小时为2点
    	calendar.set(Calendar.MINUTE, 0); // 设置分钟为0分
    	calendar.set(Calendar.SECOND, 0); // 设置秒数为0秒
    	calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒数为0毫秒
    	//返回明天的时间
    	return calendar.getTimeInMillis();
    }
    /**
     * 获取此时间为此年的第几周
     * @param time
     * @return
     */
    private static int getDateWeek(String time) {
    	 SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    	 int week = 0;
    	 Date parse;
		try {
			parse = simpleDateFormat.parse(time);
			Calendar calendar = Calendar.getInstance();
	         //设置星期一为一周开始的第一天
	         calendar.setFirstDayOfWeek(Calendar.MONDAY);
	         //设置在一年中第一个星期所需最少天数
	         calendar.setMinimalDaysInFirstWeek(0);
	         calendar.setTime(parse);
	         week = calendar.get(Calendar.WEEK_OF_YEAR);
	         if (week == 1 && parse.getMonth() == 11) {
	             calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 7);
	             Date pastDate = calendar.getTime();
	             String result = simpleDateFormat.format(pastDate);
	             System.out.println(result);
	             week = calendar.get(Calendar.WEEK_OF_YEAR) + 1;
	         }
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
         
         return week;
    }
    
	/**
	 * 获取业务库名称
	 * @param tableName
	 * @return
	 */
	public String getTableName(String tableName) {
		return Constants.getBusiName()+"."+tableName;
	}
	


	/**
	 * 保存每小时的坐席状态数据
	 */
	public void saveAgentHourState() {
		//坐席状态汇总数据
		JSONObject agentStateDate = JSONObject.parseObject(cache.get(BigMonitorCache.TOTAL_AGENT_STATE_DATA_V2));
		if (agentStateDate == null) {
			agentStateDate = new JSONObject();
			return;
		} else {
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_agent_hour"), "DATE_ID", "HOUR_ID", "DATE_TYPE");
			record.set("DATE_ID", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			record.set("HOUR_ID", EasyDate.getCurrentDateString("HH"));
			record.set("DATE_TYPE", 0);
			agentStateDate.remove("AGENT_STATE_DATA_TREND");
			record.setColumns(agentStateDate);
			try {
				if (!mysqlQuery.update(record)) {
					mysqlQuery.save(record);
				}
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
		//坐席状态汇总数据
		JSONObject floor4 = JSONObject.parseObject(cache.get(BigMonitorCache.AREA_AGENT_STATE_DATA_4));
		if (floor4 == null) {
			floor4 = new JSONObject();
			return;
		} else {
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_agent_hour"), "DATE_ID", "HOUR_ID", "DATE_TYPE");
			record.set("DATE_ID", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			record.set("HOUR_ID", EasyDate.getCurrentDateString("HH"));
			record.set("DATE_TYPE", 1);
			record.set("AGNET_COUNT", floor4.getIntValue("allCount"));
			record.set("AGNET_CALLBACK", floor4.getIntValue("callbackCount"));
			record.set("AGNET_TALK", floor4.getIntValue("talkCount"));
			record.set("AGNET_NETWORK", floor4.getIntValue("netWorkCount"));
			record.set("AGNET_BUSINESS", floor4.getIntValue("businessCount"));
			record.set("AGENT_IDEA", floor4.getIntValue("agentIdeaCount"));
			record.set("AGENT_CALL", floor4.getIntValue("agentCallCount"));
			record.set("AGENT_BUSY", floor4.getIntValue("agentBusyCount"));
			record.set("AGENT_RECORD_HANDLE", floor4.getIntValue("agentRecordHandleCount"));
			
			try {
				if (!mysqlQuery.update(record)) {
					mysqlQuery.save(record);
				}
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
		//坐席状态汇总数据
		JSONObject floor5 = JSONObject.parseObject(cache.get(BigMonitorCache.AREA_AGENT_STATE_DATA_5));
		if (floor5 == null) {
			floor5 = new JSONObject();
			return;
		} else {
			EasyRecord record = new EasyRecord(getTableName("cx_mix_hw_stat_agent_hour"), "DATE_ID", "HOUR_ID", "DATE_TYPE");
			record.set("DATE_ID", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			record.set("HOUR_ID", EasyDate.getCurrentDateString("HH"));
			record.set("DATE_TYPE", 2);
			record.set("AGNET_COUNT", floor5.getIntValue("allCount"));
			record.set("AGNET_CALLBACK", floor5.getIntValue("callbackCount"));
			record.set("AGNET_TALK", floor5.getIntValue("talkCount"));
			record.set("AGNET_NETWORK", floor5.getIntValue("netWorkCount"));
			record.set("AGNET_BUSINESS", floor5.getIntValue("businessCount"));
			record.set("AGENT_IDEA", floor5.getIntValue("agentIdeaCount"));
			record.set("AGENT_CALL", floor5.getIntValue("agentCallCount"));
			record.set("AGENT_BUSY", floor5.getIntValue("agentBusyCount"));
			record.set("AGENT_RECORD_HANDLE", floor5.getIntValue("agentRecordHandleCount"));
			
			try {
				if (!mysqlQuery.update(record)) {
					mysqlQuery.save(record);
				}
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
	}
}