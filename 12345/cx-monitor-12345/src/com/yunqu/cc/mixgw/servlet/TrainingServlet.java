package com.yunqu.cc.mixgw.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.mixgw.util.*;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lw
 * @data 2025年06月13日 14:05
 * 话务预测接口
 */
@WebServlet("/servlet/training")
public class TrainingServlet extends AppBaseServlet {
    private static final Logger logger = CommonLogger.getLogger("training");

    /**
     * 查询预测数据二级界面
     * @return
     */
    public EasyResult actionForSelect(){
        try {
            JSONObject params = this.getJSONObject();
            
            String type = params.getString("type");//1日，2周 3月 4年，日返回小时数据，周和月返回天数据 年返回月数据
            String time = params.getString("time");//type=1或2传入的yyyy-MM-dd,3传入的yyyy-MM 4传入的yyyy
            String key = "TRAINING_SELECT_"+type+"_"+time;

            JSONObject result = CacheUtil.get(key);
            if(result == null){
                result = new JSONObject();
                List<String> xAxis = null;
                //预测值
                List<Integer> trainingValue = new ArrayList<>();
                //真实值
                List<Integer> value = new ArrayList<>();
                EasySQL trainingSql = null;
                EasySQL sql = null;
                if("1".equals(type)){//日
                    String[] axis = {"01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24"};
                    xAxis = Arrays.asList(axis);
                    //预测数据sql
                    trainingSql = new EasySQL("select * from "+ Constants.getBusiName()+".cx_monitor_day_training_12345 where 1=1 and TYPE=1 ");//取调整后的值
                    trainingSql.append(time.replace("-","")," and DATE_ID=? ");
                    //真实数据sql
                    sql = new EasySQL("SELECT t1.DATE_ID,t2.`HOUR`, sum(AGENT_USE_COUNT) CALL_IN_COUNT_ALL FROM ");
                    sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
                    sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` where 1=1 ");
                    sql.append(time," and t1.DATE_VALUE=?");
                    if(time.equals(EasyDate.getCurrentDateString("yyyy-MM-dd")))//不展示当天当前小时之前的数据
                        sql.append(EasyDate.getCurrentDateString("HH")," and t2.HOUR<?");
                    sql.append(" and USER_LEVEL<>9 ");
                    sql.append(" GROUP BY t1.DATE_ID,t2.`HOUR`");
                }else if("2".equals(type)){//周
                    xAxis = new ArrayList<>();
                    for (int i = 0; i <= 8; i++) {
                        xAxis.add(DateUtil.addDay("yyyy-MM-dd",time,(-3+i)));
                    }
                    String startDate = DateUtil.addDay("yyyy-MM-dd",time,-3);//开始时间为前三天
                    String endDate = DateUtil.addDay("yyyy-MM-dd",time,5);//结束时间为后五天
                    trainingSql = new EasySQL("select * from "+Constants.getBusiName()+".cx_monitor_month_training_12345 where 1=1 and TYPE=1 ");//取调整后的值
                    trainingSql.append(startDate.replace("-","")," and DATE_ID>=? ");
                    trainingSql.append(endDate.replace("-","")," and DATE_ID<=? ");
                    //真实数据sql
                    sql = new EasySQL("SELECT t1.DATE_ID, sum(AGENT_USE_COUNT) CALL_IN_COUNT_ALL FROM ");
                    sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
                    sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` where 1=1 ");
                    sql.append(startDate," and t1.DATE_VALUE>=?");
                    sql.append(endDate," and t1.DATE_VALUE<=?");
                    sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and t1.DATE_VALUE<?");//不展示今天之后的数据
                    sql.append(" and USER_LEVEL<>9 ");
                    sql.append(" GROUP BY t1.DATE_ID");
                }else if("3".equals(type)){//月
                    xAxis = new ArrayList<>();
                    for (int i = 0; i < DateUtil.getDaysInMonth(time+"-01"); i++) {
                        xAxis.add(DateUtil.addDay("yyyy-MM-dd",time+"-01",i));
                    }
                    String startDate = time+"-01";//开始时间为选择的月份第一天
                    String endDate = DateUtil.addMonth("yyyy-MM",time,1)+"-01";//结束时间选择的月份最后一天
                    trainingSql = new EasySQL("select DATE_ID,DATE_VALUE from "+Constants.getBusiName()+".cx_monitor_month_training_12345 where 1=1 and TYPE=1 ");//取调整后的值
                    trainingSql.append(startDate.replace("-","")," and DATE_ID>=? ");
                    trainingSql.append(endDate.replace("-","")," and DATE_ID<? ");
                    //真实数据sql
                    sql = new EasySQL("SELECT t1.DATE_ID, sum(AGENT_USE_COUNT) CALL_IN_COUNT_ALL FROM ");
                    sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
                    sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` where 1=1 ");
                    sql.append(startDate," and t1.DATE_VALUE>=?");
                    sql.append(endDate," and t1.DATE_VALUE<?");
                    sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and t1.DATE_VALUE<?");//不展示今天之后的数据
                    sql.append(" and USER_LEVEL<>9 ");
                    sql.append(" GROUP BY t1.DATE_ID");
                }else if("4".equals(type)){//年
                    String[] axis = {time+"-01",time+"-02",time+"-03",time+"-04",time+"-05",time+"-06",time+"-07",time+"-08",time+"-09",time+"-10"
                            ,time+"-11",time+"-12"};
                    xAxis = Arrays.asList(axis);
                    String startDate = time+"-01";//开始时间为选择的年第一个月
                    String endDate = time+"-12";//结束时间选择的年的最后一个月
                    trainingSql = new EasySQL("select MONTH_ID DATE_ID,DATE_VALUE from "+Constants.getBusiName()+".cx_monitor_year_training_12345 where 1=1 and TYPE=1 ");//取调整后的值
                    trainingSql.append(startDate.replace("-","")," and MONTH_ID>=? ");
                    trainingSql.append(endDate.replace("-","")," and MONTH_ID<=? ");
                    //真实数据sql
                    sql = new EasySQL("SELECT t2.MONTH DATE_ID, sum(AGENT_USE_COUNT) CALL_IN_COUNT_ALL FROM ");
                    sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 where 1=1 ");
                    sql.append(time," and t2.YEAR =? ");
                    sql.append(startDate," and t2.MONTH>=?");
                    sql.append(endDate," and t2.MONTH<=?");
                    sql.append(EasyDate.getCurrentDateString("yyyy-MM")," and t2.MONTH<?");//不展示当月之后的数据
                    sql.append(" and USER_LEVEL<>9 ");
                    sql.append(" GROUP BY t2.MONTH");
                }else{
                    return EasyResult.fail("type参数错误");
                }
                if("1".equals(type)){
                    JSONObject row = this.getQuery().queryForRow(trainingSql.getSQL(), trainingSql.getParams(), new JSONMapperImpl());
                    if(row!=null){
                        for (int i = 0; i < 24; i++) {
                            trainingValue.add(row.getIntValue("HOUR"+i));
                        }
                    }
                    //查询实际值
                    List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                    if(list != null){
                        int callValue;
                        for (String hour : xAxis) {
                            JSONObject obj = new JSONObject();
                            obj.put("hour",hour);
                            callValue = 0;
                            for (JSONObject json : list) {
                                if(json.getIntValue("HOUR")==(StringUtils.parseInt(hour)-1)){
                                    callValue = json.getIntValue("CALL_IN_COUNT_ALL");
                                    break;
                                }
                            }
                            if(callValue!=0)
                                value.add(callValue);
                        }
                    }
                } else {
                    logger.info("[trainingSql]"+trainingSql.getSQL()+"[param]"+ JSON.toJSONString(trainingSql.getParams()));
                    List<JSONObject> rows = this.getQuery().queryForList(trainingSql.getSQL(), trainingSql.getParams(), new JSONMapperImpl());
                    logger.info("[sql]"+sql.getSQL()+"[param]"+ JSON.toJSONString(sql.getParams()));
                    //查询实际值
                    List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                    //xAxis = new ArrayList<>();


                    if(list!=null && rows!=null){
                        int callInCount;
                        int trainingCount;
                        for (String date : xAxis) {
                            callInCount = 0;
                            trainingCount = 0;
                            for (JSONObject json : list) {
                                //logger.info(date.replace("-","")+"_"+json.getString("DATE_ID"));
                                if (date.replace("-","").equals(json.getString("DATE_ID")) || date.equals(json.getString("DATE_ID"))) {
                                    callInCount = json.getIntValue("CALL_IN_COUNT_ALL");
                                    break;
                                }
                            }
                            for (JSONObject row : rows) {
                                if (date.replace("-","").equals(row.getString("DATE_ID"))) {
                                    trainingCount = row.getIntValue("DATE_VALUE");
                                    break;
                                }
                            }
                            if(callInCount!=0)
                                value.add(callInCount);

                            if(trainingCount!=0)
                                trainingValue.add(trainingCount);
                            else
                                trainingValue.add(null);
                        }

                    }
                }
                result.put("xAxis",xAxis);
                result.put("trainingValue",trainingValue);
                result.put("value",value);
//                int second =Math.abs(DateUtil.bwMinsWithCurrdate(DateUtil.addDay("yyyy-MM-dd",DateUtil.getCurrentDateStr("yyyy-MM-dd"),1)+" 01:30:00")*60);
//                logger.info("[key]"+key +"[缓存时间]"+second);
                //缓存时间为5分钟
                CacheUtil.put(key,result, 5*60);

            }

            return EasyResult.ok(result);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            return EasyResult.fail("查询失败");
        }
    }

    /**
     * 按天查询详情
     * @return
     */
        public EasyResult actionForSelectByDay(){
        JSONObject params = this.getJSONObject();
        String time = params.getString("time");//yyyy-MM-dd
        List<JSONObject> list =null;
        try {
        //查询预测值
        EasySQL sql = new EasySQL("select * from "+Constants.getBusiName()+".cx_monitor_day_training_12345 where 1=1 and TYPE=1 ");//取调整后的值
        sql.append(time.replace("-","")," and DATE_ID=? ");
        JSONObject rows = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
        if(rows==null){
            rows = new JSONObject();
        }
        logger.info("[sql]"+sql.getSQL()+",[params]"+ JSON.toJSONString(sql.getParams())+"[list]"+rows);
        //查询实际值
        sql = new EasySQL("SELECT t2.`HOUR` DATE_ID, sum(AGENT_USE_COUNT) VALUE FROM ");
        sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
        sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` where 1=1 ");
        sql.append(time," and t1.DATE_VALUE=?");
        sql.append(" and USER_LEVEL<>9 ");
        if(time.equals(EasyDate.getCurrentDateString("yyyy-MM-dd")))
            sql.append(EasyDate.getCurrentDateString("HH")," and t2.HOUR<?");
        sql.append(" GROUP BY t1.DATE_VALUE,t2.`HOUR` order by t2.`HOUR`");
        list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
        logger.info("[sql]"+sql.getSQL()+",[params]"+ JSON.toJSONString(sql.getParams())+"[list]"+JSON.toJSONString(list));
        int hour = 0;
        for(JSONObject json:list){
            hour = json.getIntValue("DATE_ID");
            json.put("TRAINING_VALUE",rows.getIntValue("HOUR"+hour));
            json.put("DATE_ID",(hour+1)>=10?(hour+1):"0"+(hour+1));
            if(json.getIntValue("VALUE")!=0)//不计算当前小时
            {
                json.put("ROLE",Math.abs(json.getIntValue("TRAINING_VALUE")-json.getIntValue("VALUE"))/json.getIntValue("VALUE"));
            }
            else
                json.put("ROLE","--");
        }

        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("查询失败");
        }
        return EasyResult.ok(list);
    }

    /**
     * 按周查询详情
     * @return
     */
    public EasyResult actionForSelectByWeek(){
        JSONObject params = this.getJSONObject();
        String time = params.getString("time");//yyyy-MM-dd
        List<JSONObject> list =null;
        try {
            String startDate = DateUtil.addDay("yyyy-MM-dd",time,-3);//开始时间为前三天
            String endDate = DateUtil.addDay("yyyy-MM-dd",time,5);//结束时间为后五天
            //真实数据sql
            EasySQL sql = new EasySQL("SELECT t1.DATE_VALUE DATE_ID,CONVERT( sum(AGENT_USE_COUNT),UNSIGNED) VALUE,CONVERT(MAX(t3.DATE_VALUE),UNSIGNED) TRAINING_VALUE FROM ");
            sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
            sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` inner join ");
            sql.append(Constants.getBusiName()+".cx_monitor_month_training_12345 t3 on t1.DATE_ID=t3.DATE_ID  and TYPE=1  ");
            sql.append(" where 1=1 ");
            sql.append(startDate," and t1.DATE_VALUE>=?");
            sql.append(endDate," and t1.DATE_VALUE<=?");
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and t1.DATE_VALUE<?");
            sql.append(" and USER_LEVEL<>9 ");
            sql.append(" GROUP BY t1.DATE_VALUE order by t1.DATE_VALUE");
           list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            for(JSONObject json:list){
                if(json.getLongValue("VALUE")!=0)//排除当天数据
                {
                    json.put("ROLE",Math.abs(json.getLongValue("TRAINING_VALUE")-json.getLongValue("VALUE"))/json.getLongValue("VALUE"));
                }
                else
                    json.put("ROLE","--");
                json.put("WEEK",getDayOfWeek(json.getString("DATE_ID")));
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("查询失败");
        }
        return EasyResult.ok(list);
    }

    /**
     * 按月查询详情
     * @return
     */
    public EasyResult actionForSelectByMonth(){
        JSONObject params = this.getJSONObject();
        String time = params.getString("time");//yyyy-MM
        List<JSONObject> list =null;
        try {
            String startDate = time+"-01";//开始时间为选择的月份第一天
            String endDate = DateUtil.addMonth("yyyy-MM",time,1)+"-01";//结束时间选择的月份最后一天
            //真实数据sql
            EasySQL sql = new EasySQL("SELECT t1.DATE_VALUE DATE_ID,CONVERT( sum(AGENT_USE_COUNT),UNSIGNED) VALUE,CONVERT(MAX(t3.DATE_VALUE),UNSIGNED) TRAINING_VALUE FROM ");
            sql.append(Constants.getStatSchema()+".cc_dim_date t1 inner join ");
            sql.append(Constants.getBusiName()+".tbill_call_stat_rpt t2 on t1.DATE_VALUE=t2.`DAY` inner join ");
            sql.append(Constants.getBusiName()+".cx_monitor_month_training_12345 t3 on t1.DATE_ID=t3.DATE_ID  and TYPE=1  ");
            sql.append(" where 1=1 ");
            sql.append("2025-03-01"," and t1.DATE_VALUE>=?");//去掉2月之前的数据
            sql.append(startDate," and t1.DATE_VALUE>=?");
            sql.append(endDate," and t1.DATE_VALUE<?");
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and t1.DATE_VALUE<?");
            sql.append(" and USER_LEVEL<>9 ");
            sql.append(" GROUP BY t1.DATE_VALUE order by t1.DATE_VALUE");
            list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            for(JSONObject json:list){
                if(json.getLongValue("VALUE")!=0)
                {
                    json.put("ROLE",Math.abs(json.getLongValue("TRAINING_VALUE")-json.getLongValue("VALUE"))/json.getLongValue("VALUE"));
                }
                else
                    json.put("ROLE","--");

            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("查询失败");
        }
        return EasyResult.ok(list);
    }


    /**
     * 按年查询详情
     * @return
     */
    public EasyResult actionForSelectByYear(){
        JSONObject params = this.getJSONObject();
        String time = params.getString("time");//yyyy
        List<JSONObject> list =null;
        try {
            String startDate = time+"-01";//开始时间为选择的年第一个月
            String endDate = time+"-12";//结束时间选择的年的最后一个月
            String key = "TRAINING_SELECT_YEAR_"+time;
            list = CacheUtil.get(key);
            if(list == null) {
                //真实数据sql
                EasySQL sql = new EasySQL("SELECT t1.MONTH DATE_ID,CONVERT(sum(AGENT_USE_COUNT),UNSIGNED) VALUE FROM ");
                sql.append(Constants.getBusiName() + ".tbill_call_stat_rpt t1  ");
                sql.append(" where 1=1 ");
                sql.append(time, " and t1.YEAR =? ");
                sql.append(startDate, " and t1.MONTH>=?");
                sql.append(endDate, " and t1.MONTH<=?");
                sql.append(" and USER_LEVEL<>9 ");
                sql.append(EasyDate.getCurrentDateString("yyyy-MM")," and t1.MONTH<?");
                sql.append(" GROUP BY t1.MONTH order by t1.MONTH");
                list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                CacheUtil.put(key, list,60*10);//缓存保存十分钟
            }
            EasySQL sql = new EasySQL("SELECT t1.MONTH_ID DATE_ID,CONVERT(DATE_VALUE,UNSIGNED) TRAINING_VALUE FROM ");
            sql.append(Constants.getBusiName()+".cx_monitor_year_training_12345 t1 where 1=1 ");
            sql.append(startDate.replace("-","")," and t1.MONTH_ID>=?");
            sql.append(endDate.replace("-","")," and t1.MONTH_ID<=?");
            List<JSONObject> rows = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            String month;
            long trainingValue;
            for(JSONObject json:list){
                month = json.getString("DATE_ID").replace("-","");
                trainingValue = 0;
                for (JSONObject row:rows){
                    if(month.equals(row.getString("DATE_ID"))){
                        trainingValue = row.getLongValue("TRAINING_VALUE");
                        break;
                    }
                }
                json.put("TRAINING_VALUE",trainingValue);
                if(json.getLongValue("VALUE")!=0 && trainingValue!=0)
                {
                    json.put("ROLE",Math.abs(trainingValue-json.getLongValue("VALUE"))/json.getLongValue("VALUE"));
                }
                else
                    json.put("ROLE","--");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("查询失败");
        }
        return EasyResult.ok(list);
    }

    /**
     * 调整预测数据
     * @return
     */
    public EasyResult actionForEditVal(){
        JSONObject params = this.getJSONObject();
        String type = params.getString("type");//1日，2周 3月 4年，日返回小时数据，周和月返回天数据 年返回月数据
        //type=1传入的0-23["0","1","2"],2和3传入的4传入的yyyy-MM-dd ["2025-06-09","2025-06-10","2025-06-11"] 4传入的yyyy-MM["2025-06","2025-07","2025-08"]
        String time = params.getString("time");
        //预测值["1000","2000","3000"]和time的数量和顺序要一一对应
        int timeVal = params.getIntValue("timeVal");
        //type=1的时候必填yyyy-MM-dd
        String startDate = params.getString("startDate");
        String key = "TRAINING_SELECT_"+type+"_"+startDate;
        CacheUtil.delete(key);
        logger.info("[type]"+type+"[time]"+time+"[timeVal]"+timeVal+"[startDate]"+startDate);
        if("1".equals(type) && StringUtils.isBlank(startDate)){
            return EasyResult.fail("未获取到调整的哪天的数据");
        }
        if(StringUtils.isBlank(time)){
            logger.info(type);
            return EasyResult.fail("未获取到调整的哪个时段的数据的数据");
        }
        if(timeVal==0){
            return EasyResult.fail("未获取到调整的值");
        }
        JSONArray timeArray = JSONArray.parseArray(time);
//        JSONArray valArray = JSONArray.parseArray(timeVal);
//        if(timeArray.size()!=valArray.size()){
//            return EasyResult.fail("时间和值数量不对，请检查");
//        }

        try {
            StringBuilder msg = new StringBuilder();
            //日
            if("1".equals(type)){
                JSONObject dayJson = this.getQuery().queryForRow("select * from "
                        +Constants.getBusiName()+".cx_monitor_day_training_12345 where DATE_ID=? and TYPE=?"
                        ,new Object[]{startDate.replace("-",""),"1"},new JSONMapperImpl());
                if(dayJson==null){
                    return EasyResult.fail("未获取到当天的数据，请刷新后重试！");
                }
                EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_day_training_12345","DATE_ID","TYPE");
                record.set("DATE_ID",startDate.replace("-",""));
                record.set("TYPE",1);//修改调整值
                msg = new StringBuilder("修改了" + startDate + "天");
                JSONObject modifiedData = new JSONObject();
                for (int i=0;i<timeArray.size();i++){
                    record.set("HOUR"+(timeArray.getIntValue(i)-1),dayJson.getIntValue("HOUR"+(timeArray.getIntValue(i)-1))+timeVal);
                    msg.append((timeArray.getIntValue(i)-1)).append("小时的预测数据为").append(dayJson.getIntValue("HOUR"+(timeArray.getIntValue(i)-1))+timeVal).append(";");
                    modifiedData.put(startDate.replace("-","")+(i<10?"0"+i:i),
                            dayJson.getIntValue("HOUR"+(timeArray.getIntValue(i)-1))+timeVal);//2025060301格式
                }
                this.getQuery().update(record);
                modifyPrediction("hourly_withoutgjj",modifiedData);//修改小时预测数据
            }else if("2".equals(type) || "3".equals(type)){//周和月
                List<JSONObject> monthData = this.getQuery().queryForList("select DATE_ID,DATE_VALUE from "
                        +Constants.getBusiName()+".cx_monitor_month_training_12345 where DATE_ID in ('"
                        +StringUtils.join(timeArray,"','").replace("-","")+"') and TYPE=1",null,new JSONMapperImpl());
                if(monthData==null || monthData.size()==0){
                    return EasyResult.fail("未获取到所选日期的数据，请刷新后重试！");
                }
                Map<String,Integer> monthMap = monthData.stream().
                        collect(Collectors.toMap(t->t.getString("DATE_ID"),t->t.getIntValue("DATE_VALUE")));
                EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_month_training_12345","DATE_ID","TYPE");
                record.set("TYPE",1);//修改调整值
                msg.append("修改了");
                JSONObject modifiedData = new JSONObject();
                for (int i=0;i<timeArray.size();i++){
                    key = "TRAINING_SELECT_"+type+"_"+timeArray.getString(i);
                    CacheUtil.delete(key);
                    CacheUtil.delete(key.substring(0,key.length()-3));
                    Integer number = monthMap.get(timeArray.getString(i).replace("-",""));
                    if(number==null){
                        continue;
                    }
                    msg.append(timeArray.getString(i)).append("天的预测数据为").
                            append(number+timeVal).append(";");
                    record.set("DATE_ID",timeArray.getString(i).replace("-",""));
                    record.set("DATE_VALUE",number+timeVal);
                    record.set("IS_ADJUST","1");
                    record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
                    record.set("UPDATE_USER_ACC", getUserPrincipal().getLoginAcct());
                    this.getQuery().update(record);
                    modifiedData.put(timeArray.getString(i).replace("-",""),monthMap.get(timeArray.getString(i).replace("-",""))+timeVal);//20250603格式
                }
                modifyPrediction("daily",modifiedData);//修改日预测数据
            }else if("4".equals(type)){//年
                List<JSONObject> yearData = this.getQuery().queryForList("select MONTH_ID,DATE_VALUE from "
                        +Constants.getBusiName()+".cx_monitor_year_training_12345 where MONTH_ID in ('"
                        +StringUtils.join(timeArray,"','").replace("-","")+"') and TYPE=1",null,new JSONMapperImpl());
                if(yearData==null || yearData.size()==0){
                    return EasyResult.fail("未获取到所选日期的数据，请刷新后重试！");
                }
                Map<String,Integer> yearMap = yearData.stream().
                        collect(Collectors.toMap(t->t.getString("MONTH_ID"),t->t.getIntValue("DATE_VALUE")));
                EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_year_training_12345","MONTH_ID","TYPE");
                record.set("TYPE",1);//修改调整值
                msg.append("修改了");
                JSONObject modifiedData = new JSONObject();
                for (int i=0;i<timeArray.size();i++){
                    key = "TRAINING_SELECT_"+type+"_"+timeArray.getString(i);
                    CacheUtil.delete(key);
                    CacheUtil.delete(key.substring(0,key.length()-3));
                    Integer number = yearMap.get(timeArray.getString(i).replace("-",""));
                    if(number==null){
                        continue;
                    }
                    msg.append(timeArray.getString(i)).append("月的预测数据为").append(number+timeVal).append(";");
                    record.set("MONTH_ID",timeArray.getString(i).replace("-",""));
                    record.set("DATE_VALUE",number+timeVal);
                    record.set("IS_ADJUST","1");
                    record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
                    record.set("UPDATE_USER_ACC", getUserPrincipal().getLoginAcct());
                    this.getQuery().update(record);
                    modifiedData.put(timeArray.getString(i).replace("-",""),yearMap.get(timeArray.getString(i).replace("-",""))+timeVal);//202506格式
                }
                modifyPrediction("monthly",modifiedData);//修改日预测数据
            }
            AgentLogUtil.writeLog("",getUserName(),getUserPrincipal().getLoginAcct(),AgentLogUtil.MODEL_CONFIG,AgentLogUtil.TYPE_MODIFY,msg.toString());
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("修改失败");
        }
        return EasyResult.ok("修改成功");
    }

    /**
     * 重置预测数据
     * @return
     */
    public EasyResult actionForReset(){
        try {
            JSONObject params = this.getJSONObject();
            String type = params.getString("type");//1日，2周 3月 4年
            String time = params.getString("time");//type=1 传入的yyyy-MM-dd,2传入的yyyy-MM-dd,3传入的yyyy-MM 4传入的yyyy

            if(StringUtils.isBlank(type) || StringUtils.isBlank(time)){
                return EasyResult.fail("未获取到重置的类型和时间");
            }
            String msg = "重置了" + time + "的" + ("1".equals(type) ? "日" : "2".equals(type) ? "周" : "3".equals(type) ? "月" : "年") + "预测数据";
            String startDate = "";
            String endDate = "";
            if("1".equals(type)){
                startDate = time.replace("-","");
                JSONObject oldData = this.getQuery().queryForRow("select * from "+Constants.getBusiName()+".cx_monitor_day_training_12345 where DATE_ID=? and TYPE=0",new Object[]{startDate},new JSONMapperImpl());
                if(oldData==null){
                    return EasyResult.fail("未找到预测的数据");
                }
                JSONObject modifiedData = new JSONObject();
                EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_day_training_12345","DATE_ID","TYPE");
                record.set("DATE_ID",startDate);
                record.set("TYPE",1);
                for (int i=0;i<24;i++){
                    record.set("HOUR"+i,oldData.get("HOUR"+i));
                    modifiedData.put(startDate+(i<10?"0"+i:i),oldData.get("HOUR"+i));
                }
                this.getQuery().update(record);
                modifyPrediction("hourly_withoutgjj",modifiedData);

            }else if("2".equals(type)){
                startDate = DateUtil.addDay("yyyy-MM-dd",time,-3);//开始时间为前三天
                endDate = DateUtil.addDay("yyyy-MM-dd",time,5);//结束时间为后五天
                if (updateDay(startDate, endDate)) return EasyResult.fail("未找到预测的数据");

            }else if("3".equals(type)){
                startDate = time+"-01";//开始时间为选择的月份第一天
                endDate = DateUtil.addDay("yyyy-MM-dd", DateUtil.addMonth("yyyy-MM",time,1)+"-01",-1);//结束时间选择的月份最后一天
                if (updateDay(startDate, endDate)) return EasyResult.fail("未找到预测的数据");
            }else if("4".equals(type)){
                startDate = time+"-01";//开始时间为选择的年第一个月
                endDate = time+"-12";//结束时间选择的年的最后一个月
                List<JSONObject> oldData = this.getQuery().queryForList("select * from "+Constants.getBusiName()+".cx_monitor_year_training_12345"
                        +" where MONTH_ID>=? and MONTH_ID<=? and TYPE=0",new Object[]{startDate.replace("-","")
                        ,endDate.replace("-","")},new JSONMapperImpl());
                if(oldData==null){
                    return EasyResult.fail("未找到预测的数据");
                }
                JSONObject modifiedData = new JSONObject();
                EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_year_training_12345","MONTH_ID","TYPE");
                for (JSONObject data : oldData) {
                    record.set("MONTH_ID",data.getString("MONTH_ID"));
                    record.set("TYPE",1);
                    record.set("IS_ADJUST",0);
                    record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
                    record.set("UPDATE_USER_ACC", getUserPrincipal().getLoginAcct());
                    record.set("DATE_VALUE",data.getString("DATE_VALUE"));
                    this.getQuery().update(record);
                    modifiedData.put(data.getString("MONTH_ID"),data.getString("DATE_VALUE"));
                }
                modifyPrediction("monthly",modifiedData);
            }
            String key = "TRAINING_SELECT_"+type+"_"+time;
            CacheUtil.delete(key);
            AgentLogUtil.writeLog("",getUserName(),getUserPrincipal().getLoginAcct(),AgentLogUtil.MODEL_CONFIG,AgentLogUtil.TYPE_MODIFY, msg);
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return EasyResult.fail("重置失败");
        }
        return EasyResult.ok();
    }

    private boolean updateDay(String startDate, String endDate) throws java.sql.SQLException {
        List<JSONObject> oldData = this.getQuery().queryForList("select * from "+Constants.getBusiName()+".cx_monitor_month_training_12345"
                +" where DATE_ID>=? and DATE_ID<=? and TYPE=0",new Object[]{startDate.replace("-","")
                ,endDate.replace("-","")},new JSONMapperImpl());
        if(oldData==null){
            return true;
        }
        JSONObject modifiedData = new JSONObject();
        EasyRecord record = new EasyRecord(Constants.getBusiName()+".cx_monitor_month_training_12345","DATE_ID","TYPE");
        for (JSONObject data : oldData) {
            record.set("DATE_ID",data.getString("DATE_ID"));
            record.set("TYPE",1);
            record.set("IS_ADJUST",0);
            record.set("UPDATE_TIME", DateUtil.getCurrentDateStr());
            record.set("UPDATE_USER_ACC", getUserPrincipal().getLoginAcct());
            record.set("DATE_VALUE",data.getString("DATE_VALUE"));
            this.getQuery().update(record);
            modifiedData.put(data.getString("DATE_ID"),data.getString("DATE_VALUE"));
        }
        modifyPrediction("daily",modifiedData);
        return false;
    }

    /**
     * 修改预测数据
     *
     * @param modifiedData
     */
    public void modifyPrediction(String type, JSONObject modifiedData) {
        JSONObject result = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("type",type);
        params.put("modified_data",modifiedData);
        params.put("token", getToken());
        HttpResp resp = HttpUtil.sendPost(Constants.getTrainingUrl()+"/api/modify_prediction", params.toJSONString(), HttpUtil.TYPE_JSON,"UTF-8");
        logger.info("调整预测接口：<<"+JSON.toJSONString(resp));
        if(resp.getCode() == 200){
            result = JSONObject.parseObject(resp.getResult());
            logger.info("[type]"+type+"[modified_data]"+modifiedData.toJSONString()+"[result]"+result.toJSONString());
        }
    }

    /**
     * 获取预测数据的token
     * @return
     */
    public static String getToken(){
        String token = "";
        CommonLogger.getLogger("training").info("获取预测数据的token");
        String url = Constants.getTrainingUrl();
        HttpResp resp = HttpUtil.sendPost(url+"/api/get_token", "",HttpUtil.TYPE_JSON, "UTF-8");
        //CommonLogger.getLogger("training").info("获取预测数据的token："+ JSON.toJSONString(resp));
        if(resp.getCode() == 200){
            JSONObject json = JSONObject.parseObject(resp.getResult());
            CommonLogger.getLogger("training").info("获取预测数据的token，响应: << "+json);
            token = json.getString("token");
        }
        return token;
    }

    /**
     * 获取指定日期是周几
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     * @return 返回周几的字符串表示（周一至周日）
     */
    private String getDayOfWeek(String dateStr) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

            // 将Calendar的星期日(1)至星期六(7)转换为周一至周日的表示
            String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
            return weekDays[dayOfWeek - 1];
        } catch (Exception e) {
            logger.error("获取日期" + dateStr + "的星期几失败: " + e.getMessage(), e);
            return "";
        }
    }

    public EasyQuery getQuery() {
        EasyQuery query = QueryFactory.getWriteQuery();
        //query.setLogger(logger);
        return query;
    }



}
