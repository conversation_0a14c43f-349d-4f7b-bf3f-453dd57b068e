const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./home-D-RVkgRE.js","./age-Cosd1fO6.js","./title-CoaCN9l8.js","./_plugin-vue_export-helper-CPVWdv3y.js","./vue-CkV0NT5Y.js","./title-DaZPZX3g.css","./yewu1-DVPbknuO.js","./age-CDShKgBm.css","./workVolume-ByiKuGI7.js","./workVolume-CeZlKUsz.css","./AverageCall-Jj9nWwY4.js","./chartBg-BQo3e-6F.js","./AverageCall-BJnt1bVN.css","./rightChart.vue_vue_type_style_index_0_scoped_ce2f182c_lang-B-DBYipG.js","./rightChart-BSNK9GYE.css","./home-DGM9YqiK.css","./A1-Caf4r1sV.js","./nav-C8ynh1UL.js","./nav-CcbkuRSb.css","./A1-CtI7YjK5.css","./A2-DPObgxRW.js","./A2-CzeBPmEl.css","./C1-mLS291d6.js","./C1-4i0pGTa7.css","./C2-DkELeB3a.js","./C2-lBQB0NE-.css"])))=>i.map(i=>d[i]);
import{i as Rt,h as Ze,w as sn,r as D,a as ln,c as A,u as W,p as be,g as At,b as G,d as Se,e as U,f as we,j as cn,k as Dt,l as ne,m as et,n as fn,o as dn,q as re,s as hn,t as mn,v as Ft,x as pn,y as Te,z as tt,A as nt,F as vn,B as gn,C as yn,D as En,E as _n,G as bn}from"./vue-CkV0NT5Y.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const a of o)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const a={};return o.integrity&&(a.integrity=o.integrity),o.referrerPolicy&&(a.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?a.credentials="include":o.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(o){if(o.ep)return;o.ep=!0;const a=n(o);fetch(o.href,a)}})();function wn(e){for(var t=-1,n=e==null?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r}const rt=e=>Object.keys(e),kt="__epPropKey",Ee=e=>e,On=e=>Rt(e)&&!!e[kt],xt=(e,t)=>{if(!Rt(e)||On(e))return e;const{values:n,required:r,default:o,type:a,validator:i}=e,u={type:a,required:!!r,validator:n||i?d=>{let l=!1,s=[];if(n&&(s=Array.from(n),Ze(e,"default")&&s.push(o),l||(l=s.includes(d))),i&&(l||(l=i(d))),!l&&s.length>0){const f=[...new Set(s)].map(m=>JSON.stringify(m)).join(", ");sn(`Invalid prop: validation failed${t?` for prop "${t}"`:""}. Expected one of [${f}], got value ${JSON.stringify(d)}.`)}return l}:void 0,[kt]:!0};return Ze(e,"default")&&(u.default=o),u},Bt=e=>wn(Object.entries(e).map(([t,n])=>[t,xt(n,t)])),Cn=(e,t)=>(e.install=n=>{for(const r of[e,...Object.values({})])n.component(r.name,r)},e),Sn=["","default","small","large"],Pn=Symbol("localeContextKey"),Rn=Symbol("namespaceContextKey");D(0);const An=Symbol("zIndexContextKey"),Dn=xt({type:String,values:Sn,required:!1}),Fn=Symbol("size"),kn=Symbol("emptyValuesContextKey"),xn=Bt({emptyValues:Array,valueOnClear:{type:[String,Number,Boolean,Function],default:void 0,validator:e=>ln(e)?!e():!e}}),It=Symbol(),Oe=D();function Bn(e,t=void 0){return At()?G(It,Oe):Oe}const In=(e,t,n=!1)=>{var r;const o=!!At(),a=o?Bn():void 0,i=(r=void 0)!=null?r:o?be:void 0;if(!i)return;const c=A(()=>{const u=W(e);return a!=null&&a.value?Tn(a.value,u):u});return i(It,c),i(Pn,A(()=>c.value.locale)),i(Rn,A(()=>c.value.namespace)),i(An,A(()=>c.value.zIndex)),i(Fn,{size:A(()=>c.value.size||"")}),i(kn,A(()=>({emptyValues:c.value.emptyValues,valueOnClear:c.value.valueOnClear}))),(n||!Oe.value)&&(Oe.value=c.value),c},Tn=(e,t)=>{const n=[...new Set([...rt(e),...rt(t)])],r={};for(const o of n)r[o]=t[o]!==void 0?t[o]:e[o];return r},Ln=Bt({a11y:{type:Boolean,default:!0},locale:{type:Ee(Object)},size:Dn,button:{type:Ee(Object)},experimentalFeatures:{type:Ee(Object)},keyboardNavigation:{type:Boolean,default:!0},message:{type:Ee(Object)},zIndex:Number,namespace:{type:String,default:"el"},...xn}),$n={},jn=Se({name:"ElConfigProvider",props:Ln,setup(e,{slots:t}){U(()=>e.message,r=>{Object.assign($n,r??{})},{immediate:!0,deep:!0});const n=In(e);return()=>we(t,"default",{config:n==null?void 0:n.value})}}),Mn=Cn(jn);/*! Element Plus v2.8.5 */var Nn={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};function qn(e){return{all:e=e||new Map,on:function(t,n){var r=e.get(t);r?r.push(n):e.set(t,[n])},off:function(t,n){var r=e.get(t);r&&(n?r.splice(r.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var r=e.get(t);r&&r.slice().map(function(o){o(n)}),(r=e.get("*"))&&r.slice().map(function(o){o(t,n)})}}}const Vn=qn();let _e=0,Le=null;const Tt={myws:null,wsurl:"",initWs(e,t){let r=location.origin.replace("http","ws")+"/cx-monitor-12345/websocket/bigMonitorWs";this.myws=new WebSocket(r),this.myws.onopen=()=>this.open(t),this.myws.onmessage=o=>this.receive(o,e),this.myws.onclose=()=>this.close(),this.myws.onerror=()=>this.error()},send(e){this.myws.readyState===WebSocket.OPEN&&this.myws.send(e)},open(e){console.log(e),this.send(e),setInterval(()=>{this.send(e)},20*1e3)},receive(e,t){let n=JSON.parse(e.data);console.log("Received message:",n),Vn.emit("updataChart",n),t.dispatch("setWsData",n)},close(){_e=0,clearInterval(Le),console.log("WebSocket connection closed."),this.reconnect()},error(){_e=0,clearInterval(Le),console.log("WebSocket connection error."),this.reconnect()},reconnect(){_e<10?(_e++,Le=setTimeout(()=>{this.initWs()},5e3)):console.log("Failed to reconnect after multiple attempts.")}};function Gn(){return Lt().__VUE_DEVTOOLS_GLOBAL_HOOK__}function Lt(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const Hn=typeof Proxy=="function",zn="devtools-plugin:setup",Kn="plugin:settings:set";let Z,qe;function Wn(){var e;return Z!==void 0||(typeof window<"u"&&window.performance?(Z=!0,qe=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Z=!0,qe=globalThis.perf_hooks.performance):Z=!1),Z}function Un(){return Wn()?qe.now():Date.now()}class Qn{constructor(t,n){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=n;const r={};if(t.settings)for(const i in t.settings){const c=t.settings[i];r[i]=c.defaultValue}const o=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},r);try{const i=localStorage.getItem(o),c=JSON.parse(i);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(i){try{localStorage.setItem(o,JSON.stringify(i))}catch{}a=i},now(){return Un()}},n&&n.on(Kn,(i,c)=>{i===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(i,c)=>this.target?this.target.on[c]:(...u)=>{this.onQueue.push({method:c,args:u})}}),this.proxiedTarget=new Proxy({},{get:(i,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...u)=>(this.targetQueue.push({method:c,args:u,resolve:()=>{}}),this.fallbacks[c](...u)):(...u)=>new Promise(d=>{this.targetQueue.push({method:c,args:u,resolve:d})})})}async setRealTarget(t){this.target=t;for(const n of this.onQueue)this.target.on[n.method](...n.args);for(const n of this.targetQueue)n.resolve(await this.target[n.method](...n.args))}}function Yn(e,t){const n=e,r=Lt(),o=Gn(),a=Hn&&n.enableEarlyProxy;if(o&&(r.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))o.emit(zn,e,t);else{const i=a?new Qn(n,o):null;(r.__VUE_DEVTOOLS_PLUGINS__=r.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}}/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */var $t="store";function Jn(e){return e===void 0&&(e=null),G(e!==null?e:$t)}function ue(e,t){Object.keys(e).forEach(function(n){return t(e[n],n)})}function Xn(e){return e!==null&&typeof e=="object"}function Zn(e){return e&&typeof e.then=="function"}function er(e,t){return function(){return e(t)}}function jt(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var r=t.indexOf(e);r>-1&&t.splice(r,1)}}function Mt(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;Pe(e,n,[],e._modules.root,!0),Ke(e,n,t)}function Ke(e,t,n){var r=e._state,o=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var a=e._wrappedGetters,i={},c={},u=cn(!0);u.run(function(){ue(a,function(d,l){i[l]=er(d,e),c[l]=A(function(){return i[l]()}),Object.defineProperty(e.getters,l,{get:function(){return c[l].value},enumerable:!0})})}),e._state=Dt({data:t}),e._scope=u,e.strict&&ir(e),r&&n&&e._withCommit(function(){r.data=null}),o&&o.stop()}function Pe(e,t,n,r,o){var a=!n.length,i=e._modules.getNamespace(n);if(r.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=r),!a&&!o){var c=We(t,n.slice(0,-1)),u=n[n.length-1];e._withCommit(function(){c[u]=r.state})}var d=r.context=tr(e,i,n);r.forEachMutation(function(l,s){var f=i+s;nr(e,f,l,d)}),r.forEachAction(function(l,s){var f=l.root?s:i+s,m=l.handler||l;rr(e,f,m,d)}),r.forEachGetter(function(l,s){var f=i+s;or(e,f,l,d)}),r.forEachChild(function(l,s){Pe(e,t,n.concat(s),l,o)})}function tr(e,t,n){var r=t==="",o={dispatch:r?e.dispatch:function(a,i,c){var u=Ce(a,i,c),d=u.payload,l=u.options,s=u.type;return(!l||!l.root)&&(s=t+s),e.dispatch(s,d)},commit:r?e.commit:function(a,i,c){var u=Ce(a,i,c),d=u.payload,l=u.options,s=u.type;(!l||!l.root)&&(s=t+s),e.commit(s,d,l)}};return Object.defineProperties(o,{getters:{get:r?function(){return e.getters}:function(){return Nt(e,t)}},state:{get:function(){return We(e.state,n)}}}),o}function Nt(e,t){if(!e._makeLocalGettersCache[t]){var n={},r=t.length;Object.keys(e.getters).forEach(function(o){if(o.slice(0,r)===t){var a=o.slice(r);Object.defineProperty(n,a,{get:function(){return e.getters[o]},enumerable:!0})}}),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function nr(e,t,n,r){var o=e._mutations[t]||(e._mutations[t]=[]);o.push(function(i){n.call(e,r.state,i)})}function rr(e,t,n,r){var o=e._actions[t]||(e._actions[t]=[]);o.push(function(i){var c=n.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},i);return Zn(c)||(c=Promise.resolve(c)),e._devtoolHook?c.catch(function(u){throw e._devtoolHook.emit("vuex:error",u),u}):c})}function or(e,t,n,r){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(a){return n(r.state,r.getters,a.state,a.getters)})}function ir(e){U(function(){return e._state.data},function(){},{deep:!0,flush:"sync"})}function We(e,t){return t.reduce(function(n,r){return n[r]},e)}function Ce(e,t,n){return Xn(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var ar="vuex bindings",ot="vuex:mutations",$e="vuex:actions",ee="vuex",ur=0;function sr(e,t){Yn({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[ar]},function(n){n.addTimelineLayer({id:ot,label:"Vuex Mutations",color:it}),n.addTimelineLayer({id:$e,label:"Vuex Actions",color:it}),n.addInspector({id:ee,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree(function(r){if(r.app===e&&r.inspectorId===ee)if(r.filter){var o=[];Ht(o,t._modules.root,r.filter,""),r.rootNodes=o}else r.rootNodes=[Gt(t._modules.root,"")]}),n.on.getInspectorState(function(r){if(r.app===e&&r.inspectorId===ee){var o=r.nodeId;Nt(t,o),r.state=fr(hr(t._modules,o),o==="root"?t.getters:t._makeLocalGettersCache,o)}}),n.on.editInspectorState(function(r){if(r.app===e&&r.inspectorId===ee){var o=r.nodeId,a=r.path;o!=="root"&&(a=o.split("/").filter(Boolean).concat(a)),t._withCommit(function(){r.set(t._state.data,a,r.state.value)})}}),t.subscribe(function(r,o){var a={};r.payload&&(a.payload=r.payload),a.state=o,n.notifyComponentUpdate(),n.sendInspectorTree(ee),n.sendInspectorState(ee),n.addTimelineEvent({layerId:ot,event:{time:Date.now(),title:r.type,data:a}})}),t.subscribeAction({before:function(r,o){var a={};r.payload&&(a.payload=r.payload),r._id=ur++,r._time=Date.now(),a.state=o,n.addTimelineEvent({layerId:$e,event:{time:r._time,title:r.type,groupId:r._id,subtitle:"start",data:a}})},after:function(r,o){var a={},i=Date.now()-r._time;a.duration={_custom:{type:"duration",display:i+"ms",tooltip:"Action duration",value:i}},r.payload&&(a.payload=r.payload),a.state=o,n.addTimelineEvent({layerId:$e,event:{time:Date.now(),title:r.type,groupId:r._id,subtitle:"end",data:a}})}})})}var it=8702998,lr=6710886,cr=16777215,qt={label:"namespaced",textColor:cr,backgroundColor:lr};function Vt(e){return e&&e!=="root"?e.split("/").slice(-2,-1)[0]:"Root"}function Gt(e,t){return{id:t||"root",label:Vt(t),tags:e.namespaced?[qt]:[],children:Object.keys(e._children).map(function(n){return Gt(e._children[n],t+n+"/")})}}function Ht(e,t,n,r){r.includes(n)&&e.push({id:r||"root",label:r.endsWith("/")?r.slice(0,r.length-1):r||"Root",tags:t.namespaced?[qt]:[]}),Object.keys(t._children).forEach(function(o){Ht(e,t._children[o],n,r+o+"/")})}function fr(e,t,n){t=n==="root"?t:t[n];var r=Object.keys(t),o={state:Object.keys(e.state).map(function(i){return{key:i,editable:!0,value:e.state[i]}})};if(r.length){var a=dr(t);o.getters=Object.keys(a).map(function(i){return{key:i.endsWith("/")?Vt(i):i,editable:!1,value:Ve(function(){return a[i]})}})}return o}function dr(e){var t={};return Object.keys(e).forEach(function(n){var r=n.split("/");if(r.length>1){var o=t,a=r.pop();r.forEach(function(i){o[i]||(o[i]={_custom:{value:{},display:i,tooltip:"Module",abstract:!0}}),o=o[i]._custom.value}),o[a]=Ve(function(){return e[n]})}else t[n]=Ve(function(){return e[n]})}),t}function hr(e,t){var n=t.split("/").filter(function(r){return r});return n.reduce(function(r,o,a){var i=r[o];if(!i)throw new Error('Missing module "'+o+'" for path "'+t+'".');return a===n.length-1?i:i._children},t==="root"?e:e.root._children)}function Ve(e){try{return e()}catch(t){return t}}var M=function(t,n){this.runtime=n,this._children=Object.create(null),this._rawModule=t;var r=t.state;this.state=(typeof r=="function"?r():r)||{}},zt={namespaced:{configurable:!0}};zt.namespaced.get=function(){return!!this._rawModule.namespaced};M.prototype.addChild=function(t,n){this._children[t]=n};M.prototype.removeChild=function(t){delete this._children[t]};M.prototype.getChild=function(t){return this._children[t]};M.prototype.hasChild=function(t){return t in this._children};M.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)};M.prototype.forEachChild=function(t){ue(this._children,t)};M.prototype.forEachGetter=function(t){this._rawModule.getters&&ue(this._rawModule.getters,t)};M.prototype.forEachAction=function(t){this._rawModule.actions&&ue(this._rawModule.actions,t)};M.prototype.forEachMutation=function(t){this._rawModule.mutations&&ue(this._rawModule.mutations,t)};Object.defineProperties(M.prototype,zt);var Q=function(t){this.register([],t,!1)};Q.prototype.get=function(t){return t.reduce(function(n,r){return n.getChild(r)},this.root)};Q.prototype.getNamespace=function(t){var n=this.root;return t.reduce(function(r,o){return n=n.getChild(o),r+(n.namespaced?o+"/":"")},"")};Q.prototype.update=function(t){Kt([],this.root,t)};Q.prototype.register=function(t,n,r){var o=this;r===void 0&&(r=!0);var a=new M(n,r);if(t.length===0)this.root=a;else{var i=this.get(t.slice(0,-1));i.addChild(t[t.length-1],a)}n.modules&&ue(n.modules,function(c,u){o.register(t.concat(u),c,r)})};Q.prototype.unregister=function(t){var n=this.get(t.slice(0,-1)),r=t[t.length-1],o=n.getChild(r);o&&o.runtime&&n.removeChild(r)};Q.prototype.isRegistered=function(t){var n=this.get(t.slice(0,-1)),r=t[t.length-1];return n?n.hasChild(r):!1};function Kt(e,t,n){if(t.update(n),n.modules)for(var r in n.modules){if(!t.getChild(r))return;Kt(e.concat(r),t.getChild(r),n.modules[r])}}function mr(e){return new T(e)}var T=function(t){var n=this;t===void 0&&(t={});var r=t.plugins;r===void 0&&(r=[]);var o=t.strict;o===void 0&&(o=!1);var a=t.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Q(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=a;var i=this,c=this,u=c.dispatch,d=c.commit;this.dispatch=function(f,m){return u.call(i,f,m)},this.commit=function(f,m,y){return d.call(i,f,m,y)},this.strict=o;var l=this._modules.root.state;Pe(this,l,[],this._modules.root),Ke(this,l),r.forEach(function(s){return s(n)})},Ue={state:{configurable:!0}};T.prototype.install=function(t,n){t.provide(n||$t,this),t.config.globalProperties.$store=this;var r=this._devtools!==void 0?this._devtools:!1;r&&sr(t,this)};Ue.state.get=function(){return this._state.data};Ue.state.set=function(e){};T.prototype.commit=function(t,n,r){var o=this,a=Ce(t,n,r),i=a.type,c=a.payload,u={type:i,payload:c},d=this._mutations[i];d&&(this._withCommit(function(){d.forEach(function(s){s(c)})}),this._subscribers.slice().forEach(function(l){return l(u,o.state)}))};T.prototype.dispatch=function(t,n){var r=this,o=Ce(t,n),a=o.type,i=o.payload,c={type:a,payload:i},u=this._actions[a];if(u){try{this._actionSubscribers.slice().filter(function(l){return l.before}).forEach(function(l){return l.before(c,r.state)})}catch{}var d=u.length>1?Promise.all(u.map(function(l){return l(i)})):u[0](i);return new Promise(function(l,s){d.then(function(f){try{r._actionSubscribers.filter(function(m){return m.after}).forEach(function(m){return m.after(c,r.state)})}catch{}l(f)},function(f){try{r._actionSubscribers.filter(function(m){return m.error}).forEach(function(m){return m.error(c,r.state,f)})}catch{}s(f)})})}};T.prototype.subscribe=function(t,n){return jt(t,this._subscribers,n)};T.prototype.subscribeAction=function(t,n){var r=typeof t=="function"?{before:t}:t;return jt(r,this._actionSubscribers,n)};T.prototype.watch=function(t,n,r){var o=this;return U(function(){return t(o.state,o.getters)},n,Object.assign({},r))};T.prototype.replaceState=function(t){var n=this;this._withCommit(function(){n._state.data=t})};T.prototype.registerModule=function(t,n,r){r===void 0&&(r={}),typeof t=="string"&&(t=[t]),this._modules.register(t,n),Pe(this,this.state,t,this._modules.get(t),r.preserveState),Ke(this,this.state)};T.prototype.unregisterModule=function(t){var n=this;typeof t=="string"&&(t=[t]),this._modules.unregister(t),this._withCommit(function(){var r=We(n.state,t.slice(0,-1));delete r[t[t.length-1]]}),Mt(this)};T.prototype.hasModule=function(t){return typeof t=="string"&&(t=[t]),this._modules.isRegistered(t)};T.prototype.hotUpdate=function(t){this._modules.update(t),Mt(this,!0)};T.prototype._withCommit=function(t){var n=this._committing;this._committing=!0,t(),this._committing=n};Object.defineProperties(T.prototype,Ue);const pr={class:"mainBox"},vr={__name:"App",setup(e){const t=Jn();return Tt.initWs(t,"annularInfo"),(n,r)=>{const o=dn("router-view"),a=Mn;return re(),ne("div",pr,[et(a,{locale:W(Nn)},{default:fn(()=>[et(o)]),_:1},8,["locale"])])}}},gr="modulepreload",yr=function(e,t){return new URL(e,t).href},at={},te=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){let i=function(l){return Promise.all(l.map(s=>Promise.resolve(s).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};const c=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),d=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));o=i(n.map(l=>{if(l=yr(l,r),l in at)return;at[l]=!0;const s=l.endsWith(".css"),f=s?'[rel="stylesheet"]':"";if(!!r)for(let E=c.length-1;E>=0;E--){const C=c[E];if(C.href===l&&(!s||C.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${f}`))return;const y=document.createElement("link");if(y.rel=s?"stylesheet":gr,s||(y.as="script"),y.crossOrigin="",y.href=l,d&&y.setAttribute("nonce",d),document.head.appendChild(y),s)return new Promise((E,C)=>{y.addEventListener("load",E),y.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${l}`)))})}))}function a(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return o.then(i=>{for(const c of i||[])c.status==="rejected"&&a(c.reason);return t().catch(a)})};/*!
  * vue-router v4.4.5
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const oe=typeof document<"u";function Wt(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Er(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Wt(e.default)}const S=Object.assign;function je(e,t){const n={};for(const r in t){const o=t[r];n[r]=j(o)?o.map(e):e(o)}return n}const fe=()=>{},j=Array.isArray,Ut=/#/g,_r=/&/g,br=/\//g,wr=/=/g,Or=/\?/g,Qt=/\+/g,Cr=/%5B/g,Sr=/%5D/g,Yt=/%5E/g,Pr=/%60/g,Jt=/%7B/g,Rr=/%7C/g,Xt=/%7D/g,Ar=/%20/g;function Qe(e){return encodeURI(""+e).replace(Rr,"|").replace(Cr,"[").replace(Sr,"]")}function Dr(e){return Qe(e).replace(Jt,"{").replace(Xt,"}").replace(Yt,"^")}function Ge(e){return Qe(e).replace(Qt,"%2B").replace(Ar,"+").replace(Ut,"%23").replace(_r,"%26").replace(Pr,"`").replace(Jt,"{").replace(Xt,"}").replace(Yt,"^")}function Fr(e){return Ge(e).replace(wr,"%3D")}function kr(e){return Qe(e).replace(Ut,"%23").replace(Or,"%3F")}function xr(e){return e==null?"":kr(e).replace(br,"%2F")}function he(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Br=/\/$/,Ir=e=>e.replace(Br,"");function Me(e,t,n="/"){let r,o={},a="",i="";const c=t.indexOf("#");let u=t.indexOf("?");return c<u&&c>=0&&(u=-1),u>-1&&(r=t.slice(0,u),a=t.slice(u+1,c>-1?c:t.length),o=e(a)),c>-1&&(r=r||t.slice(0,c),i=t.slice(c,t.length)),r=jr(r??t,n),{fullPath:r+(a&&"?")+a+i,path:r,query:o,hash:he(i)}}function Tr(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ut(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Lr(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&ie(t.matched[r],n.matched[o])&&Zt(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function ie(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Zt(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!$r(e[n],t[n]))return!1;return!0}function $r(e,t){return j(e)?st(e,t):j(t)?st(t,e):e===t}function st(e,t){return j(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function jr(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let a=n.length-1,i,c;for(i=0;i<r.length;i++)if(c=r[i],c!==".")if(c==="..")a>1&&a--;else break;return n.slice(0,a).join("/")+"/"+r.slice(i).join("/")}const H={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var me;(function(e){e.pop="pop",e.push="push"})(me||(me={}));var de;(function(e){e.back="back",e.forward="forward",e.unknown=""})(de||(de={}));function Mr(e){if(!e)if(oe){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ir(e)}const Nr=/^[^#]+#/;function qr(e,t){return e.replace(Nr,"#")+t}function Vr(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Re=()=>({left:window.scrollX,top:window.scrollY});function Gr(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Vr(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function lt(e,t){return(history.state?history.state.position-t:-1)+e}const He=new Map;function Hr(e,t){He.set(e,t)}function zr(e){const t=He.get(e);return He.delete(e),t}let Kr=()=>location.protocol+"//"+location.host;function en(e,t){const{pathname:n,search:r,hash:o}=t,a=e.indexOf("#");if(a>-1){let c=o.includes(e.slice(a))?e.slice(a).length:1,u=o.slice(c);return u[0]!=="/"&&(u="/"+u),ut(u,"")}return ut(n,e)+r+o}function Wr(e,t,n,r){let o=[],a=[],i=null;const c=({state:f})=>{const m=en(e,location),y=n.value,E=t.value;let C=0;if(f){if(n.value=m,t.value=f,i&&i===y){i=null;return}C=E?f.position-E.position:0}else r(m);o.forEach(P=>{P(n.value,y,{delta:C,type:me.pop,direction:C?C>0?de.forward:de.back:de.unknown})})};function u(){i=n.value}function d(f){o.push(f);const m=()=>{const y=o.indexOf(f);y>-1&&o.splice(y,1)};return a.push(m),m}function l(){const{history:f}=window;f.state&&f.replaceState(S({},f.state,{scroll:Re()}),"")}function s(){for(const f of a)f();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:u,listen:d,destroy:s}}function ct(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?Re():null}}function Ur(e){const{history:t,location:n}=window,r={value:en(e,n)},o={value:t.state};o.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(u,d,l){const s=e.indexOf("#"),f=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+u:Kr()+e+u;try{t[l?"replaceState":"pushState"](d,"",f),o.value=d}catch(m){console.error(m),n[l?"replace":"assign"](f)}}function i(u,d){const l=S({},t.state,ct(o.value.back,u,o.value.forward,!0),d,{position:o.value.position});a(u,l,!0),r.value=u}function c(u,d){const l=S({},o.value,t.state,{forward:u,scroll:Re()});a(l.current,l,!0);const s=S({},ct(r.value,u,null),{position:l.position+1},d);a(u,s,!1),r.value=u}return{location:r,state:o,push:c,replace:i}}function Qr(e){e=Mr(e);const t=Ur(e),n=Wr(e,t.state,t.location,t.replace);function r(a,i=!0){i||n.pauseListeners(),history.go(a)}const o=S({location:"",base:e,go:r,createHref:qr.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Yr(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Qr(e)}function Jr(e){return typeof e=="string"||e&&typeof e=="object"}function tn(e){return typeof e=="string"||typeof e=="symbol"}const nn=Symbol("");var ft;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ft||(ft={}));function ae(e,t){return S(new Error,{type:e,[nn]:!0},t)}function V(e,t){return e instanceof Error&&nn in e&&(t==null||!!(e.type&t))}const dt="[^/]+?",Xr={sensitive:!1,strict:!1,start:!0,end:!0},Zr=/[.+*?^${}()[\]/\\]/g;function eo(e,t){const n=S({},Xr,t),r=[];let o=n.start?"^":"";const a=[];for(const d of e){const l=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let s=0;s<d.length;s++){const f=d[s];let m=40+(n.sensitive?.25:0);if(f.type===0)s||(o+="/"),o+=f.value.replace(Zr,"\\$&"),m+=40;else if(f.type===1){const{value:y,repeatable:E,optional:C,regexp:P}=f;a.push({name:y,repeatable:E,optional:C});const _=P||dt;if(_!==dt){m+=10;try{new RegExp(`(${_})`)}catch(x){throw new Error(`Invalid custom RegExp for param "${y}" (${_}): `+x.message)}}let w=E?`((?:${_})(?:/(?:${_}))*)`:`(${_})`;s||(w=C&&d.length<2?`(?:/${w})`:"/"+w),C&&(w+="?"),o+=w,m+=20,C&&(m+=-8),E&&(m+=-20),_===".*"&&(m+=-50)}l.push(m)}r.push(l)}if(n.strict&&n.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function c(d){const l=d.match(i),s={};if(!l)return null;for(let f=1;f<l.length;f++){const m=l[f]||"",y=a[f-1];s[y.name]=m&&y.repeatable?m.split("/"):m}return s}function u(d){let l="",s=!1;for(const f of e){(!s||!l.endsWith("/"))&&(l+="/"),s=!1;for(const m of f)if(m.type===0)l+=m.value;else if(m.type===1){const{value:y,repeatable:E,optional:C}=m,P=y in d?d[y]:"";if(j(P)&&!E)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const _=j(P)?P.join("/"):P;if(!_)if(C)f.length<2&&(l.endsWith("/")?l=l.slice(0,-1):s=!0);else throw new Error(`Missing required param "${y}"`);l+=_}}return l||"/"}return{re:i,score:r,keys:a,parse:c,stringify:u}}function to(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function rn(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const a=to(r[n],o[n]);if(a)return a;n++}if(Math.abs(o.length-r.length)===1){if(ht(r))return 1;if(ht(o))return-1}return o.length-r.length}function ht(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const no={type:0,value:""},ro=/[a-zA-Z0-9_]/;function oo(e){if(!e)return[[]];if(e==="/")return[[no]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,r=n;const o=[];let a;function i(){a&&o.push(a),a=[]}let c=0,u,d="",l="";function s(){d&&(n===0?a.push({type:0,value:d}):n===1||n===2||n===3?(a.length>1&&(u==="*"||u==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:d,regexp:l,repeatable:u==="*"||u==="+",optional:u==="*"||u==="?"})):t("Invalid state to consume buffer"),d="")}function f(){d+=u}for(;c<e.length;){if(u=e[c++],u==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:u==="/"?(d&&s(),i()):u===":"?(s(),n=1):f();break;case 4:f(),n=r;break;case 1:u==="("?n=2:ro.test(u)?f():(s(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&c--);break;case 2:u===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+u:n=3:l+=u;break;case 3:s(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&c--,l="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),s(),i(),o}function io(e,t,n){const r=eo(oo(e.path),n),o=S(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function ao(e,t){const n=[],r=new Map;t=gt({strict:!1,end:!0,sensitive:!1},t);function o(s){return r.get(s)}function a(s,f,m){const y=!m,E=pt(s);E.aliasOf=m&&m.record;const C=gt(t,s),P=[E];if("alias"in s){const x=typeof s.alias=="string"?[s.alias]:s.alias;for(const $ of x)P.push(pt(S({},E,{components:m?m.record.components:E.components,path:$,aliasOf:m?m.record:E})))}let _,w;for(const x of P){const{path:$}=x;if(f&&$[0]!=="/"){const N=f.record.path,B=N[N.length-1]==="/"?"":"/";x.path=f.record.path+($&&B+$)}if(_=io(x,f,C),m?m.alias.push(_):(w=w||_,w!==_&&w.alias.push(_),y&&s.name&&!vt(_)&&i(s.name)),on(_)&&u(_),E.children){const N=E.children;for(let B=0;B<N.length;B++)a(N[B],_,m&&m.children[B])}m=m||_}return w?()=>{i(w)}:fe}function i(s){if(tn(s)){const f=r.get(s);f&&(r.delete(s),n.splice(n.indexOf(f),1),f.children.forEach(i),f.alias.forEach(i))}else{const f=n.indexOf(s);f>-1&&(n.splice(f,1),s.record.name&&r.delete(s.record.name),s.children.forEach(i),s.alias.forEach(i))}}function c(){return n}function u(s){const f=lo(s,n);n.splice(f,0,s),s.record.name&&!vt(s)&&r.set(s.record.name,s)}function d(s,f){let m,y={},E,C;if("name"in s&&s.name){if(m=r.get(s.name),!m)throw ae(1,{location:s});C=m.record.name,y=S(mt(f.params,m.keys.filter(w=>!w.optional).concat(m.parent?m.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),s.params&&mt(s.params,m.keys.map(w=>w.name))),E=m.stringify(y)}else if(s.path!=null)E=s.path,m=n.find(w=>w.re.test(E)),m&&(y=m.parse(E),C=m.record.name);else{if(m=f.name?r.get(f.name):n.find(w=>w.re.test(f.path)),!m)throw ae(1,{location:s,currentLocation:f});C=m.record.name,y=S({},f.params,s.params),E=m.stringify(y)}const P=[];let _=m;for(;_;)P.unshift(_.record),_=_.parent;return{name:C,path:E,params:y,matched:P,meta:so(P)}}e.forEach(s=>a(s));function l(){n.length=0,r.clear()}return{addRoute:a,resolve:d,removeRoute:i,clearRoutes:l,getRoutes:c,getRecordMatcher:o}}function mt(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function pt(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:uo(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function uo(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function vt(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function so(e){return e.reduce((t,n)=>S(t,n.meta),{})}function gt(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function lo(e,t){let n=0,r=t.length;for(;n!==r;){const a=n+r>>1;rn(e,t[a])<0?r=a:n=a+1}const o=co(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function co(e){let t=e;for(;t=t.parent;)if(on(t)&&rn(e,t)===0)return t}function on({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function fo(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const a=r[o].replace(Qt," "),i=a.indexOf("="),c=he(i<0?a:a.slice(0,i)),u=i<0?null:he(a.slice(i+1));if(c in t){let d=t[c];j(d)||(d=t[c]=[d]),d.push(u)}else t[c]=u}return t}function yt(e){let t="";for(let n in e){const r=e[n];if(n=Fr(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(j(r)?r.map(a=>a&&Ge(a)):[r&&Ge(r)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+n,a!=null&&(t+="="+a))})}return t}function ho(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=j(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const mo=Symbol(""),Et=Symbol(""),Ae=Symbol(""),Ye=Symbol(""),ze=Symbol("");function ce(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function z(e,t,n,r,o,a=i=>i()){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((c,u)=>{const d=f=>{f===!1?u(ae(4,{from:n,to:t})):f instanceof Error?u(f):Jr(f)?u(ae(2,{from:t,to:f})):(i&&r.enterCallbacks[o]===i&&typeof f=="function"&&i.push(f),c())},l=a(()=>e.call(r&&r.instances[o],t,n,d));let s=Promise.resolve(l);e.length<3&&(s=s.then(d)),s.catch(f=>u(f))})}function Ne(e,t,n,r,o=a=>a()){const a=[];for(const i of e)for(const c in i.components){let u=i.components[c];if(!(t!=="beforeRouteEnter"&&!i.instances[c]))if(Wt(u)){const l=(u.__vccOpts||u)[t];l&&a.push(z(l,n,r,i,c,o))}else{let d=u();a.push(()=>d.then(l=>{if(!l)throw new Error(`Couldn't resolve component "${c}" at "${i.path}"`);const s=Er(l)?l.default:l;i.mods[c]=l,i.components[c]=s;const m=(s.__vccOpts||s)[t];return m&&z(m,n,r,i,c,o)()}))}}return a}function _t(e){const t=G(Ae),n=G(Ye),r=A(()=>{const u=W(e.to);return t.resolve(u)}),o=A(()=>{const{matched:u}=r.value,{length:d}=u,l=u[d-1],s=n.matched;if(!l||!s.length)return-1;const f=s.findIndex(ie.bind(null,l));if(f>-1)return f;const m=bt(u[d-2]);return d>1&&bt(l)===m&&s[s.length-1].path!==m?s.findIndex(ie.bind(null,u[d-2])):f}),a=A(()=>o.value>-1&&yo(n.params,r.value.params)),i=A(()=>o.value>-1&&o.value===n.matched.length-1&&Zt(n.params,r.value.params));function c(u={}){return go(u)?t[W(e.replace)?"replace":"push"](W(e.to)).catch(fe):Promise.resolve()}return{route:r,href:A(()=>r.value.href),isActive:a,isExactActive:i,navigate:c}}const po=Se({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:_t,setup(e,{slots:t}){const n=Dt(_t(e)),{options:r}=G(Ae),o=A(()=>({[wt(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[wt(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const a=t.default&&t.default(n);return e.custom?a:Ft("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},a)}}}),vo=po;function go(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function yo(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!j(o)||o.length!==r.length||r.some((a,i)=>a!==o[i]))return!1}return!0}function bt(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const wt=(e,t,n)=>e??t??n,Eo=Se({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=G(ze),o=A(()=>e.route||r.value),a=G(Et,0),i=A(()=>{let d=W(a);const{matched:l}=o.value;let s;for(;(s=l[d])&&!s.components;)d++;return d}),c=A(()=>o.value.matched[i.value]);be(Et,A(()=>i.value+1)),be(mo,c),be(ze,o);const u=D();return U(()=>[u.value,c.value,e.name],([d,l,s],[f,m,y])=>{l&&(l.instances[s]=d,m&&m!==l&&d&&d===f&&(l.leaveGuards.size||(l.leaveGuards=m.leaveGuards),l.updateGuards.size||(l.updateGuards=m.updateGuards))),d&&l&&(!m||!ie(l,m)||!f)&&(l.enterCallbacks[s]||[]).forEach(E=>E(d))},{flush:"post"}),()=>{const d=o.value,l=e.name,s=c.value,f=s&&s.components[l];if(!f)return Ot(n.default,{Component:f,route:d});const m=s.props[l],y=m?m===!0?d.params:typeof m=="function"?m(d):m:null,C=Ft(f,S({},y,t,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(s.instances[l]=null)},ref:u}));return Ot(n.default,{Component:C,route:d})||C}}});function Ot(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const _o=Eo;function bo(e){const t=ao(e.routes,e),n=e.parseQuery||fo,r=e.stringifyQuery||yt,o=e.history,a=ce(),i=ce(),c=ce(),u=hn(H);let d=H;oe&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=je.bind(null,h=>""+h),s=je.bind(null,xr),f=je.bind(null,he);function m(h,v){let p,g;return tn(h)?(p=t.getRecordMatcher(h),g=v):g=h,t.addRoute(g,p)}function y(h){const v=t.getRecordMatcher(h);v&&t.removeRoute(v)}function E(){return t.getRoutes().map(h=>h.record)}function C(h){return!!t.getRecordMatcher(h)}function P(h,v){if(v=S({},v||u.value),typeof h=="string"){const b=Me(n,h,v.path),k=t.resolve({path:b.path},v),le=o.createHref(b.fullPath);return S(b,k,{params:f(k.params),hash:he(b.hash),redirectedFrom:void 0,href:le})}let p;if(h.path!=null)p=S({},h,{path:Me(n,h.path,v.path).path});else{const b=S({},h.params);for(const k in b)b[k]==null&&delete b[k];p=S({},h,{params:s(b)}),v.params=s(v.params)}const g=t.resolve(p,v),R=h.hash||"";g.params=l(f(g.params));const F=Tr(r,S({},h,{hash:Dr(R),path:g.path})),O=o.createHref(F);return S({fullPath:F,hash:R,query:r===yt?ho(h.query):h.query||{}},g,{redirectedFrom:void 0,href:O})}function _(h){return typeof h=="string"?Me(n,h,u.value.path):S({},h)}function w(h,v){if(d!==h)return ae(8,{from:v,to:h})}function x(h){return B(h)}function $(h){return x(S(_(h),{replace:!0}))}function N(h){const v=h.matched[h.matched.length-1];if(v&&v.redirect){const{redirect:p}=v;let g=typeof p=="function"?p(h):p;return typeof g=="string"&&(g=g.includes("?")||g.includes("#")?g=_(g):{path:g},g.params={}),S({query:h.query,hash:h.hash,params:g.path!=null?{}:h.params},g)}}function B(h,v){const p=d=P(h),g=u.value,R=h.state,F=h.force,O=h.replace===!0,b=N(p);if(b)return B(S(_(b),{state:typeof b=="object"?S({},R,b.state):R,force:F,replace:O}),v||p);const k=p;k.redirectedFrom=v;let le;return!F&&Lr(r,g,p)&&(le=ae(16,{to:k,from:g}),Je(g,g,!0,!1)),(le?Promise.resolve(le):Y(k,g)).catch(I=>V(I)?V(I,2)?I:xe(I):ge(I,k,g)).then(I=>{if(I){if(V(I,2))return B(S({replace:O},_(I.to),{state:typeof I.to=="object"?S({},R,I.to.state):R,force:F}),v||k)}else I=ve(k,g,!0,O,R);return pe(k,g,I),I})}function Fe(h,v){const p=w(h,v);return p?Promise.reject(p):Promise.resolve()}function se(h){const v=ye.values().next().value;return v&&typeof v.runWithContext=="function"?v.runWithContext(h):h()}function Y(h,v){let p;const[g,R,F]=wo(h,v);p=Ne(g.reverse(),"beforeRouteLeave",h,v);for(const b of g)b.leaveGuards.forEach(k=>{p.push(z(k,h,v))});const O=Fe.bind(null,h,v);return p.push(O),X(p).then(()=>{p=[];for(const b of a.list())p.push(z(b,h,v));return p.push(O),X(p)}).then(()=>{p=Ne(R,"beforeRouteUpdate",h,v);for(const b of R)b.updateGuards.forEach(k=>{p.push(z(k,h,v))});return p.push(O),X(p)}).then(()=>{p=[];for(const b of F)if(b.beforeEnter)if(j(b.beforeEnter))for(const k of b.beforeEnter)p.push(z(k,h,v));else p.push(z(b.beforeEnter,h,v));return p.push(O),X(p)}).then(()=>(h.matched.forEach(b=>b.enterCallbacks={}),p=Ne(F,"beforeRouteEnter",h,v,se),p.push(O),X(p))).then(()=>{p=[];for(const b of i.list())p.push(z(b,h,v));return p.push(O),X(p)}).catch(b=>V(b,8)?b:Promise.reject(b))}function pe(h,v,p){c.list().forEach(g=>se(()=>g(h,v,p)))}function ve(h,v,p,g,R){const F=w(h,v);if(F)return F;const O=v===H,b=oe?history.state:{};p&&(g||O?o.replace(h.fullPath,S({scroll:O&&b&&b.scroll},R)):o.push(h.fullPath,R)),u.value=h,Je(h,v,p,O),xe()}let K;function ke(){K||(K=o.listen((h,v,p)=>{if(!Xe.listening)return;const g=P(h),R=N(g);if(R){B(S(R,{replace:!0}),g).catch(fe);return}d=g;const F=u.value;oe&&Hr(lt(F.fullPath,p.delta),Re()),Y(g,F).catch(O=>V(O,12)?O:V(O,2)?(B(O.to,g).then(b=>{V(b,20)&&!p.delta&&p.type===me.pop&&o.go(-1,!1)}).catch(fe),Promise.reject()):(p.delta&&o.go(-p.delta,!1),ge(O,g,F))).then(O=>{O=O||ve(g,F,!1),O&&(p.delta&&!V(O,8)?o.go(-p.delta,!1):p.type===me.pop&&V(O,20)&&o.go(-1,!1)),pe(g,F,O)}).catch(fe)}))}let J=ce(),L=ce(),q;function ge(h,v,p){xe(h);const g=L.list();return g.length?g.forEach(R=>R(h,v,p)):console.error(h),Promise.reject(h)}function un(){return q&&u.value!==H?Promise.resolve():new Promise((h,v)=>{J.add([h,v])})}function xe(h){return q||(q=!h,ke(),J.list().forEach(([v,p])=>h?p(h):v()),J.reset()),h}function Je(h,v,p,g){const{scrollBehavior:R}=e;if(!oe||!R)return Promise.resolve();const F=!p&&zr(lt(h.fullPath,0))||(g||!p)&&history.state&&history.state.scroll||null;return pn().then(()=>R(h,v,F)).then(O=>O&&Gr(O)).catch(O=>ge(O,h,v))}const Be=h=>o.go(h);let Ie;const ye=new Set,Xe={currentRoute:u,listening:!0,addRoute:m,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:E,resolve:P,options:e,push:x,replace:$,go:Be,back:()=>Be(-1),forward:()=>Be(1),beforeEach:a.add,beforeResolve:i.add,afterEach:c.add,onError:L.add,isReady:un,install(h){const v=this;h.component("RouterLink",vo),h.component("RouterView",_o),h.config.globalProperties.$router=v,Object.defineProperty(h.config.globalProperties,"$route",{enumerable:!0,get:()=>W(u)}),oe&&!Ie&&u.value===H&&(Ie=!0,x(o.location).catch(R=>{}));const p={};for(const R in H)Object.defineProperty(p,R,{get:()=>u.value[R],enumerable:!0});h.provide(Ae,v),h.provide(Ye,mn(p)),h.provide(ze,u);const g=h.unmount;ye.add(h),h.unmount=function(){ye.delete(h),ye.size<1&&(d=H,K&&K(),K=null,u.value=H,Ie=!1,q=!1),g()}}};function X(h){return h.reduce((v,p)=>v.then(()=>se(p)),Promise.resolve())}return Xe}function wo(e,t){const n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let i=0;i<a;i++){const c=t.matched[i];c&&(e.matched.find(d=>ie(d,c))?r.push(c):n.push(c));const u=e.matched[i];u&&(t.matched.find(d=>ie(d,u))||o.push(u))}return[n,r,o]}function No(){return G(Ae)}function qo(e){return G(Ye)}const an=bo({history:Yr(),routes:[{path:"/",redirect:"/home"},{path:"/home",component:()=>te(()=>import("./home-D-RVkgRE.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"大屏总览",code:"home"}},{path:"/A1",component:()=>te(()=>import("./A1-Caf4r1sV.js"),__vite__mapDeps([16,6,17,3,4,18,13,14,19]),import.meta.url),meta:{title:"人员详情",code:"A1"}},{path:"/A1",component:()=>te(()=>import("./A1-Caf4r1sV.js"),__vite__mapDeps([16,6,17,3,4,18,13,14,19]),import.meta.url),meta:{title:"人员详情",code:"A1"}},{path:"/A2",component:()=>te(()=>import("./A2-DPObgxRW.js"),__vite__mapDeps([20,17,3,4,18,10,2,5,11,12,21]),import.meta.url),meta:{title:"历史话务",code:"A2"}},{path:"/C1",component:()=>te(()=>import("./C1-mLS291d6.js"),__vite__mapDeps([22,17,3,4,18,2,5,1,6,7,23]),import.meta.url),meta:{title:"人员总览",code:"C1"}},{path:"/C2",component:()=>te(()=>import("./C2-DkELeB3a.js"),__vite__mapDeps([24,17,3,4,18,2,5,11,8,9,25]),import.meta.url),meta:{title:"实时话务",code:"C2"}}]});an.beforeEach((e,t,n)=>{e.meta&&e.meta.title&&(document.title=`${e.meta.title}`),Tt.send("annularInfo"),n()});let Oo={agentInfoMonitor:{},busiMonitor:{},annularInfo:{}},Co={},So={SET_DATA:(e,t)=>{e[t.flag]=t[t.flag],localStorage.setItem(t.flag,JSON.stringify(t[t.flag]))}},Po={setWsData({commit:e},t){e("SET_DATA",t)}};const Ro=mr({state:Oo,getters:Co,mutations:So,actions:Po});(function(){try{if(typeof document<"u"){var e=document.createElement("style");e.appendChild(document.createTextNode('.vue3-marquee{display:flex!important;position:relative}.vue3-marquee.horizontal{overflow-x:hidden!important;flex-direction:row!important;width:100%;height:max-content}.vue3-marquee.vertical{overflow-y:hidden!important;flex-direction:column!important;height:100%;width:max-content}.vue3-marquee:hover>.marquee{animation-play-state:var(--pauseOnHover)}.vue3-marquee:active>.marquee{animation-play-state:var(--pauseOnClick)}.vue3-marquee>.marquee{flex:0 0 auto;min-width:var(--min-width);min-height:var(--min-height);z-index:1;animation:var(--orientation) var(--duration) linear var(--delay) var(--loops);animation-play-state:var(--pauseAnimation);animation-direction:var(--direction)}.vue3-marquee.horizontal>.marquee{display:flex;flex-direction:row;align-items:center}.vue3-marquee.vertical>.marquee{display:flex;flex-direction:column;align-items:center}@keyframes scrollX{0%{transform:translate(0)}to{transform:translate(-100%)}}@keyframes scrollY{0%{transform:translateY(0)}to{transform:translateY(-100%)}}.vue3-marquee>.overlay{position:absolute;width:100%;height:100%}.vue3-marquee>.transparent-overlay{position:absolute;width:100%;height:100%}.vue3-marquee>.overlay:before,.vue3-marquee>.overlay:after{content:"";position:absolute;z-index:2}.vue3-marquee.horizontal>.overlay:before,.vue3-marquee.horizontal>.overlay:after{background:linear-gradient(to right,var(--gradient-color));height:100%;width:var(--gradient-length)}.vue3-marquee.vertical>.overlay:before,.vue3-marquee.vertical>.overlay:after{background:linear-gradient(to bottom,var(--gradient-color));height:var(--gradient-length);width:100%}.vue3-marquee.horizontal>.overlay:after{transform:rotate(180deg)}.vue3-marquee.vertical>.overlay:after{transform:rotate(-180deg)}.vue3-marquee>.overlay:before{left:0;top:0}.vue3-marquee.horizontal>.overlay:after{right:0;top:0}.vue3-marquee.vertical>.overlay:after{left:0;bottom:0}')),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}})();var Ao=Object.defineProperty,Ct=Object.getOwnPropertySymbols,Do=Object.prototype.hasOwnProperty,Fo=Object.prototype.propertyIsEnumerable,St=(e,t,n)=>t in e?Ao(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Pt=(e,t)=>{for(var n in t||(t={}))Do.call(t,n)&&St(e,n,t[n]);if(Ct)for(var n of Ct(t))Fo.call(t,n)&&St(e,n,t[n]);return e},ko=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n};const xo=Se({props:{vertical:{type:Boolean,default:!1},direction:{type:String,default:"normal"},duration:{type:Number,default:20},delay:{type:Number,default:0},loop:{type:Number,default:0},clone:{type:Boolean,default:!1},gradient:{type:Boolean,default:!1},gradientColor:{type:Array,default:[255,255,255]},gradientWidth:{type:String},gradientLength:{type:String},pauseOnHover:{type:Boolean,default:!1},pauseOnClick:{type:Boolean,default:!1},pause:{type:Boolean,default:!1},animateOnOverflowOnly:{type:Boolean,default:!1}},emits:["onComplete","onLoopComplete","onPause","onResume","onOverflowDetected","onOverflowCleared"],setup(e,{emit:t}){const n=D(0),r=D("100%"),o=D("100%"),a=D(0),i=D(!1),c=D(!0),u=D(0),d=D(0),l=D(0),s=D(0),f=D(!1),m=D(!1),y=D(0),E=D(null),C=D("200px"),P=D(!1),_=D(null),w=D(null),x=async()=>{await $(),a.value++},$=async()=>{e.vertical&&(i.value=!0),setInterval(()=>{if(r.value="0%",o.value="0%",_.value!==null&&w.value!==null&&_.value&&w.value)if(e.vertical&&"clientHeight"in _.value&&"clientHeight"in w.value){s.value=_.value.clientHeight,l.value=w.value.clientHeight;const L=Math.ceil(l.value/s.value);return n.value=e.animateOnOverflowOnly?0:isFinite(L)?L:0,i.value=!1,n.value}else if(!e.vertical&&"clientWidth"in _.value&&"clientWidth"in w.value){if(d.value=_.value.clientWidth,u.value=w.value.clientWidth,e.animateOnOverflowOnly&&P.value)return d.value<=u.value?(c.value=!0,t("onOverflowCleared")):(c.value=!1,t("onOverflowDetected")),0;const L=Math.ceil(u.value/d.value);return n.value=isFinite(L)?L:0,n.value}else return r.value="100%",o.value="100%",0;else return r.value="100%",o.value="100%",0},100)};U(d,async()=>{e.clone&&x()}),U(u,async()=>{(e.clone||e.animateOnOverflowOnly)&&x()}),U(()=>e.pause,(L,q)=>{L!==q&&t(L?"onResume":"onPause")});const N=()=>{e.pauseOnHover&&(t("onPause"),f.value=!0)},B=()=>{e.pauseOnHover&&(t("onResume"),f.value=!1)},Fe=()=>{e.pauseOnClick&&(t("onPause"),m.value=!0)},se=()=>{e.pauseOnClick&&(t("onResume"),m.value=!1)},Y=A(()=>e.pause||e.vertical&&i.value||e.animateOnOverflowOnly&&c.value?"paused":"running"),pe=A(()=>e.pauseOnHover&&(f.value||m.value)||!e.pauseOnHover&&Y.value==="paused"?"paused":"running"),ve=A(()=>e.pauseOnHover&&f.value||e.pauseOnClick&&m.value||!e.pauseOnHover&&Y.value==="paused"?"paused":"running"),K=A(()=>{const L={"--duration":`${e.duration}s`,"--delay":`${e.delay}s`,"--direction":`${e.direction}`,"--pauseOnHover":`${pe.value}`,"--pauseOnClick":`${ve.value}`,"--pauseAnimation":`${Y.value}`,"--loops":`${e.loop===0?"infinite":e.loop}`,"--gradient-color":`rgba(${e.gradientColor[0]}, ${e.gradientColor[1]}, ${e.gradientColor[2]}, 1), rgba(${e.gradientColor[0]}, ${e.gradientColor[1]}, ${e.gradientColor[2]}, 0)`,"--gradient-length":`${C.value}`,"--min-width":`${r.value}`,"--min-height":`${o.value}`},q={"--orientation":"scrollX",orientation:"horizontal"};return e.vertical&&(q["--orientation"]="scrollY"),Pt(Pt({},L),q)}),ke=A(()=>!!e.gradient),J=async()=>{e.vertical?(o.value="100%",r.value="auto",e.animateOnOverflowOnly&&console.warn("The `animateOnOverflowOnly` prop is not supported for vertical marquees.")):(o.value="auto",e.animateOnOverflowOnly?r.value="auto":r.value="100%"),e.gradient&&(e.gradientWidth?(console.warn("The `gradientWidth` prop has been deprecated and will be removed in a future release. Please use `gradientLength` instead."),C.value=e.gradientWidth):e.gradientLength&&(C.value=e.gradientLength)),(e.clone||e.animateOnOverflowOnly)&&(await $(),x()),P.value=!0};return En(async()=>{J(),E.value=setInterval(()=>{y.value++,e.loop!==0&&y.value===e.loop&&(t("onComplete"),clearInterval(E.value)),t("onLoopComplete")},e.duration*1e3)}),_n(()=>{clearInterval(E.value)}),{ready:P,contentWidth:d,containerWidth:u,contentHeight:s,containerHeight:l,loopCounter:y,loopInterval:E,mouseOverMarquee:f,mouseDownMarquee:m,minWidth:r,minHeight:o,animateOnOverflowPause:c,marqueeContent:_,marqueeOverlayContainer:w,componentKey:a,showGradient:ke,cloneAmount:n,ForcesUpdate:x,checkForClone:$,setupMarquee:J,getCurrentStyle:K,hoverStarted:N,hoverEnded:B,mouseDown:Fe,mouseUp:se}}}),Bo={class:"transparent-overlay",ref:"marqueeOverlayContainer","aria-hidden":!0},Io={class:"marquee",ref:"marqueeContent"},To={key:1,"aria-hidden":!0,class:"marquee"};function Lo(e,t,n,r,o,a){return e.ready?(re(),ne("div",{class:nt(["vue3-marquee",{vertical:e.vertical,horizontal:!e.vertical}]),style:yn(e.getCurrentStyle),key:e.componentKey,onMouseenter:t[0]||(t[0]=(...i)=>e.hoverStarted&&e.hoverStarted(...i)),onMouseleave:t[1]||(t[1]=(...i)=>e.hoverEnded&&e.hoverEnded(...i)),onMousedown:t[2]||(t[2]=(...i)=>e.mouseDown&&e.mouseDown(...i)),onMouseup:t[3]||(t[3]=(...i)=>e.mouseUp&&e.mouseUp(...i))},[tt("div",Bo,null,512),e.showGradient?(re(),ne("div",{key:0,"aria-hidden":!0,class:nt(["overlay",{vertical:e.vertical,horizontal:!e.vertical}])},null,2)):Te("",!0),tt("div",Io,[we(e.$slots,"default")],512),!e.animateOnOverflowOnly||e.animateOnOverflowOnly&&!e.animateOnOverflowPause?(re(),ne("div",To,[we(e.$slots,"default")])):Te("",!0),(re(!0),ne(vn,null,gn(e.cloneAmount,i=>(re(),ne("div",{"aria-hidden":!0,class:"marquee cloned",key:i},[we(e.$slots,"default")]))),128))],38)):Te("",!0)}var $o=ko(xo,[["render",Lo]]),jo={install(e,t){var n;const r=(n=t==null?void 0:t.name)!=null?n:"Vue3Marquee";e.component(r,$o)}};console.log("版本号：","v1.0.0");const De=bn(vr);De.use(an);De.use(Ro);De.use(jo);De.mount("#app");export{Vn as B,qo as a,No as b,Jn as u};
