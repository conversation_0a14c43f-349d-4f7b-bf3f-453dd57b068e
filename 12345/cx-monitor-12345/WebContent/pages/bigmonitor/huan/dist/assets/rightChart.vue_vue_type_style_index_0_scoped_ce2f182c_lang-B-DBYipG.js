import{z as j,A as M,B as K,C as Y,a as q,b as H,c as U,d as W,e as J,f as P,g as Q,h as X,i as Z,j as ss,k as ts,l as es,m as as,n as is,o as ls,p as os,q as ns,r as _s,s as ds,t as rs,u as us,v as cs,w as gs,x as vs,y as As}from"./yewu1-DVPbknuO.js";import{u as B,B as z}from"./main-BY7WlIbH.js";import{_ as O,a as F,b as ps}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{c as y,r as T,e as E,D as ms,l as u,z as s,y as R,I as i,u as a,q as c,k as I,F as $,B as V,m as w}from"./vue-CkV0NT5Y.js";const Ts={class:"templateBox1x"},fs={class:"right"},bs={key:0,class:"card_box king"},ys={class:"userInfo"},Es={class:"info"},hs={class:"info_value"},Ns={class:"value"},xs={class:"userInfoDetail"},Ds={class:"detail"},ks={class:"detailValue"},Ls={class:"detail"},Ss={class:"detailValue"},Cs={class:"detail"},Gs={class:"detailValue"},Fs={class:"userInfoTime"},Rs={class:"detail"},Is={class:"TimeValue"},$s={class:"detail"},Vs={class:"TimeValue"},ws={key:1,class:"card_box sos"},Bs={class:"userInfo"},zs={class:"info"},Os={class:"info_value"},js={class:"value"},Ms={class:"userInfoDetail"},Ks={class:"detail"},Ys={class:"detailValue"},qs={class:"detail"},Hs={class:"detailValue"},Us={class:"detail"},Ws={class:"detailValue"},Js={class:"userInfoTime"},Ps={class:"detail"},Qs={class:"TimeValue"},Xs={class:"detail"},Zs={class:"TimeValue"},st={__name:"right",props:{type:{type:String,default:"king"}},setup(b){const p=B();let l=y(()=>{var o;return((o=p.state.annularInfo)==null?void 0:o.helpAgent)||{}}),f=y(()=>{var o;return((o=p.state.annularInfo)==null?void 0:o.agentTalkRanking)||{}}),n=T({AGENT_NO:"暂无",AGENT_NAME:"暂无",agentCount:"1"}),_=T({AGENT_NO:"暂无",AGENT_NAME:"暂无",agentCount:"1"});return E(l,(o,t)=>{n.value=o}),E(f,(o,t)=>{_.value=o[0]}),ms(()=>{n.value=l.value,_.value=f.value[0]}),z.on("updataChart",o=>{n.value=o.annularInfo.agentTalkRanking,_.value=o.annularInfo.helpAgent}),console.log(p.state),(o,t)=>{var g,v,m,e,d,r,A,h,N,x,D,k,L,S,C,G;return c(),u("div",Ts,[s("div",fs,[b.type==="king"?(c(),u("div",bs,[t[10]||(t[10]=s("div",{class:"tag"},"金牌话务员",-1)),t[11]||(t[11]=s("div",{class:"icon"},[s("img",{src:j,alt:""})],-1)),s("div",ys,[t[1]||(t[1]=s("div",{class:"avatar"},[s("img",{src:M,alt:""})],-1)),s("div",Es,[s("div",null,i(((g=a(_))==null?void 0:g.agentName)||"暂无"),1),s("div",null,i(((v=a(_))==null?void 0:v.agentId)||"暂无"),1)]),t[2]||(t[2]=s("div",{class:"line"},null,-1)),s("div",hs,[s("div",Ns,i(((m=a(_))==null?void 0:m.agentCount)||0),1),t[0]||(t[0]=s("div",{class:"label"},"话务量",-1))])]),s("div",xs,[s("div",Ds,[t[3]||(t[3]=s("div",{class:"detailLabel"},"所属班组",-1)),s("div",ks,i(((e=a(_))==null?void 0:e.work_group_name)||"暂无"),1)]),t[6]||(t[6]=s("div",{class:"detailLine"},null,-1)),s("div",Ls,[t[4]||(t[4]=s("div",{class:"detailLabel"},"入职年限",-1)),s("div",Ss,i(((d=a(_))==null?void 0:d.entryYear)||"0")+"年",1)]),t[7]||(t[7]=s("div",{class:"detailLine"},null,-1)),s("div",Cs,[t[5]||(t[5]=s("div",{class:"detailLabel"},"技能组",-1)),s("div",Gs,i((r=a(_))!=null&&r.skills?a(_).skills.split(",")[0]:"暂无"),1)])]),s("div",Fs,[s("div",Rs,[t[8]||(t[8]=s("div",{class:"detailLabel"},"今日通话时长",-1)),s("div",Is,i((A=a(_))==null?void 0:A.todayCallInTime),1)]),s("div",$s,[t[9]||(t[9]=s("div",{class:"detailLabel"},"今日话后时长",-1)),s("div",Vs,i((h=a(_))==null?void 0:h.todayArrangeTime),1)])])])):R("",!0),b.type==="sos"?(c(),u("div",ws,[t[22]||(t[22]=s("div",{class:"tag"},"求助SOS",-1)),t[23]||(t[23]=s("div",{class:"icon"},[s("img",{src:K,alt:""})],-1)),s("div",Bs,[t[13]||(t[13]=s("div",{class:"avatar"},[s("img",{src:Y,alt:""})],-1)),s("div",zs,[s("div",null,i(((N=a(n))==null?void 0:N.AGENT_NAME)||"暂无"),1),s("div",null,i(((x=a(n))==null?void 0:x.AGENT_NO)||"暂无"),1)]),t[14]||(t[14]=s("div",{class:"line"},null,-1)),s("div",Os,[s("div",js,i(((D=a(n))==null?void 0:D.agentCount)||0),1),t[12]||(t[12]=s("div",{class:"label"},"话务量",-1))])]),s("div",Ms,[s("div",Ks,[t[15]||(t[15]=s("div",{class:"detailLabel"},"所属班组",-1)),s("div",Ys,i(((k=a(n))==null?void 0:k.work_group_name)||"暂无"),1)]),t[18]||(t[18]=s("div",{class:"detailLine"},null,-1)),s("div",qs,[t[16]||(t[16]=s("div",{class:"detailLabel"},"入职年限",-1)),s("div",Hs,i(((L=a(n))==null?void 0:L.entryYear)||"0")+"年",1)]),t[19]||(t[19]=s("div",{class:"detailLine"},null,-1)),s("div",Us,[t[17]||(t[17]=s("div",{class:"detailLabel"},"技能组",-1)),s("div",Ws,i((S=a(n))!=null&&S.skills?a(n).skills.split(",")[0]:"暂无"),1)])]),s("div",Js,[s("div",Ps,[t[20]||(t[20]=s("div",{class:"detailLabel"},"今日通话时长",-1)),s("div",Qs,i((C=a(n))==null?void 0:C.todayCallInTime),1)]),s("div",Xs,[t[21]||(t[21]=s("div",{class:"detailLabel"},"今日话后时长",-1)),s("div",Zs,i((G=a(n))==null?void 0:G.todayArrangeTime),1)])])])):R("",!0)])])}}},Nt=O(st,[["__scopeId","data-v-584c43ea"]]),tt={class:"content_left"},et={class:"left_L_box"},at={class:"content_left_top"},it={class:"icon"},lt=["src"],ot={class:"name"},nt={class:"value"},_t={class:"content_left_bottom"},dt={class:"left_L_box"},rt={class:"content_left_top"},ut={class:"icon"},ct=["src"],gt={class:"name"},vt={class:"value"},At={class:"content_left_bottom"},pt={class:"floorTag Tag4f"},mt={class:"infoBox"},Tt={class:"value"},ft={__name:"leftChart",setup(b){const p=B(),l=Object.assign({"/src/assets/A1/A1-bg1.png":As,"/src/assets/A1/A1-bg2.png":vs,"/src/assets/A1/floor.png":gs,"/src/assets/A1/floor1.png":cs,"/src/assets/A1/floorTag.png":us,"/src/assets/A1/floorTag1.png":rs,"/src/assets/A1/huahou.png":ds,"/src/assets/A1/huahou1.png":_s,"/src/assets/A1/huifang.png":ns,"/src/assets/A1/huifang1.png":os,"/src/assets/A1/jiehua.png":ps,"/src/assets/A1/jiehua1.png":ls,"/src/assets/A1/king.png":is,"/src/assets/A1/king_avatar.png":as,"/src/assets/A1/kongxian.png":es,"/src/assets/A1/kongxian1.png":ts,"/src/assets/A1/shimang.png":ss,"/src/assets/A1/shimang1.png":Z,"/src/assets/A1/sos.png":X,"/src/assets/A1/sos_avatar.png":Q,"/src/assets/A1/tonghua.png":P,"/src/assets/A1/tonghua1.png":J,"/src/assets/A1/wangluo.png":W,"/src/assets/A1/wangluo1.png":U,"/src/assets/A1/yewu.png":H,"/src/assets/A1/yewu1.png":q});let f=y(()=>{var e;return((e=p.state.annularInfo)==null?void 0:e.floor4)||{}}),n=T(0),_=T([{icon:l["/src/assets/A1/jiehua.png"].default,name:"接话",value:"0"},{icon:l["/src/assets/A1/huifang.png"].default,name:"回访",value:"0"},{icon:l["/src/assets/A1/wangluo.png"].default,name:"网络",value:"0"},{icon:l["/src/assets/A1/yewu.png"].default,name:"业务",value:"0"}]),o=T([{icon:l["/src/assets/A1/huahou.png"].default,name:"话后",value:"0"},{icon:l["/src/assets/A1/tonghua.png"].default,name:"通话",value:"0"},{icon:l["/src/assets/A1/kongxian.png"].default,name:"空闲",value:"0"},{icon:l["/src/assets/A1/shimang.png"].default,name:"示忙",value:"0"}]);E(f,(e,d)=>{t(e),m(e)}),z.on("changeDate",e=>{t(e.floor4),m(e.floor4)});function t(e){_.value=[{icon:l["/src/assets/A1/jiehua.png"].default,name:"接话",value:e.talkCount},{icon:l["/src/assets/A1/huifang.png"].default,name:"回访",value:e.callbackCount},{icon:l["/src/assets/A1/wangluo.png"].default,name:"网络",value:e.netWorkCount},{icon:l["/src/assets/A1/yewu.png"].default,name:"业务",value:e.businessCount}],o.value=[{icon:l["/src/assets/A1/huahou.png"].default,name:"话后",value:e.agentRecordHandleCount},{icon:l["/src/assets/A1/tonghua.png"].default,name:"通话",value:e.agentCallCount},{icon:l["/src/assets/A1/kongxian.png"].default,name:"空闲",value:e.agentIdeaCount},{icon:l["/src/assets/A1/shimang.png"].default,name:"示忙",value:e.agentBusyCount}],n.value=e.allCount}let g=I({tooltip:{trigger:"axis"},legend:{data:["接话","回访","网络","业务"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#00FFFF","#0080FF","#00DF64","#FFDC00"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4,interval:6},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]}),v=I({tooltip:{trigger:"axis"},legend:{data:["话后","空闲","通话","示忙"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#8572FF","#9AD30C","#EFAF10","#DC0055"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:6,interval:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]});function m(e){var d,r;v.xAxis.data=(d=e.AGENT_STATE_DATA_TREND)==null?void 0:d.cols,v.series=[{name:"话后",type:"line",smooth:!0,symbolSize:0,data:e.AGENT_STATE_DATA_TREND.AGENT_RECORD_HANDLE_TREND},{name:"空闲",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGENT_IDEA_TREND},{name:"通话",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGENT_CALL_TREND},{name:"示忙",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGENT_BUSY_TREND}],g.xAxis.data=(r=e.AGENT_STATE_DATA_TREND)==null?void 0:r.cols,g.series=[{name:"接话",type:"line",smooth:!0,symbolSize:0,data:e.AGENT_STATE_DATA_TREND.AGNET_TALK_TREND},{name:"回访",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGNET_CALLBACK_TREND},{name:"网络",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGNET_NETWORK_TREND},{name:"业务",type:"line",symbolSize:0,smooth:!0,data:e.AGENT_STATE_DATA_TREND.AGNET_BUSINESS_TREND}]}return(e,d)=>(c(),u("div",tt,[s("div",et,[s("div",at,[(c(!0),u($,null,V(a(_),(r,A)=>(c(),u("div",{class:"item",key:A},[s("div",it,[s("img",{src:r.icon,alt:""},null,8,lt)]),s("div",ot,i(r.name),1),s("div",nt,i(r.value),1)]))),128))]),s("div",_t,[d[0]||(d[0]=s("div",{class:"yName"},"人数",-1)),w(F,{option:a(g)},null,8,["option"])])]),s("div",dt,[s("div",rt,[(c(!0),u($,null,V(a(o),(r,A)=>(c(),u("div",{class:"item",key:A},[s("div",ut,[s("img",{src:r.icon,alt:""},null,8,ct)]),s("div",gt,i(r.name),1),s("div",vt,i(r.value),1)]))),128))]),s("div",At,[d[1]||(d[1]=s("div",{class:"yName"},"人数",-1)),w(F,{option:a(v)},null,8,["option"])])]),s("div",pt,[s("div",mt,[s("div",Tt,i(a(n)),1),d[2]||(d[2]=s("div",{class:"label"},"四楼总人数",-1))])])]))}},xt=O(ft,[["__scopeId","data-v-a5822a07"]]);export{xt as L,Nt as R};
