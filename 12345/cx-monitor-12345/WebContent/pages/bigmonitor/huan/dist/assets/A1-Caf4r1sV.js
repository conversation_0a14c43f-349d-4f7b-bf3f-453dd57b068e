import{a as S,b as F,c as R,d as L,e as G,f as C,g as k,h as w,i as I,j as z,k as B,l as j,m as O,n as $,o as K,p as q,q as H,r as U,s as V,t as W,u as M,v as Y,w as J,x as P,y as Q,_ as X}from"./yewu1-DVPbknuO.js";import{_ as Z}from"./nav-C8ynh1UL.js";import{L as ss,R as ts}from"./rightChart.vue_vue_type_style_index_0_scoped_ce2f182c_lang-B-DBYipG.js";import{_ as x,a as b,b as es}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{u as as}from"./main-BY7WlIbH.js";import{c as os,r as p,e as _s,D as ns,k as y,l as _,z as t,F as E,B as N,I as l,u as c,m as i,q as n}from"./vue-CkV0NT5Y.js";const is={class:"content_left"},ls={class:"left_L_box"},cs={class:"content_left_top"},rs={class:"icon"},us=["src"],gs={class:"name"},ds={class:"value"},ps={class:"content_left_bottom"},As={class:"left_L_box"},ms={class:"content_left_top"},fs={class:"icon"},Ts=["src"],vs={class:"name"},hs={class:"value"},bs={class:"content_left_bottom"},ys={class:"floorTag Tag5f"},Es={class:"infoBox"},Ns={class:"value"},xs={__name:"rightChart",setup(D){const A=as(),e=Object.assign({"/src/assets/A1/A1-bg1.png":Q,"/src/assets/A1/A1-bg2.png":P,"/src/assets/A1/floor.png":J,"/src/assets/A1/floor1.png":Y,"/src/assets/A1/floorTag.png":M,"/src/assets/A1/floorTag1.png":W,"/src/assets/A1/huahou.png":V,"/src/assets/A1/huahou1.png":U,"/src/assets/A1/huifang.png":H,"/src/assets/A1/huifang1.png":q,"/src/assets/A1/jiehua.png":es,"/src/assets/A1/jiehua1.png":K,"/src/assets/A1/king.png":$,"/src/assets/A1/king_avatar.png":O,"/src/assets/A1/kongxian.png":j,"/src/assets/A1/kongxian1.png":B,"/src/assets/A1/shimang.png":z,"/src/assets/A1/shimang1.png":I,"/src/assets/A1/sos.png":w,"/src/assets/A1/sos_avatar.png":k,"/src/assets/A1/tonghua.png":C,"/src/assets/A1/tonghua1.png":G,"/src/assets/A1/wangluo.png":L,"/src/assets/A1/wangluo1.png":R,"/src/assets/A1/yewu.png":F,"/src/assets/A1/yewu1.png":S});let r=os(()=>{var s;return((s=A.state.annularInfo)==null?void 0:s.floor5)||{}}),m=p(0),f=p([{icon:e["/src/assets/A1/jiehua1.png"].default,name:"接话",value:"0"},{icon:e["/src/assets/A1/huifang1.png"].default,name:"回访",value:"0"},{icon:e["/src/assets/A1/wangluo1.png"].default,name:"网络",value:"0"},{icon:e["/src/assets/A1/yewu1.png"].default,name:"业务",value:"0"}]),T=p([{icon:e["/src/assets/A1/huahou1.png"].default,name:"话后",value:"0"},{icon:e["/src/assets/A1/tonghua1.png"].default,name:"通话",value:"0"},{icon:e["/src/assets/A1/kongxian1.png"].default,name:"空闲",value:"0"},{icon:e["/src/assets/A1/shimang1.png"].default,name:"示忙",value:"0"}]);_s(r,(s,a)=>{v(s),h(s)}),ns(()=>{v(r.value),h(r.value)});function v(s){f.value=[{icon:e["/src/assets/A1/jiehua1.png"].default,name:"接话",value:s.talkCount},{icon:e["/src/assets/A1/huifang1.png"].default,name:"回访",value:s.callbackCount},{icon:e["/src/assets/A1/wangluo1.png"].default,name:"网络",value:s.netWorkCount},{icon:e["/src/assets/A1/yewu1.png"].default,name:"业务",value:s.businessCount}],T.value=[{icon:e["/src/assets/A1/huahou1.png"].default,name:"话后",value:s.agentRecordHandleCount},{icon:e["/src/assets/A1/tonghua1.png"].default,name:"通话",value:s.agentCallCount},{icon:e["/src/assets/A1/kongxian1.png"].default,name:"空闲",value:s.agentIdeaCount},{icon:e["/src/assets/A1/shimang1.png"].default,name:"示忙",value:s.agentBusyCount}],m.value=s.allCount}let u=y({tooltip:{trigger:"axis"},legend:{data:["接话","回访","网络","业务"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#00FFFF","#0080FF","#00DF64","#FFDC00"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4,interval:6},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]}),g=y({tooltip:{trigger:"axis"},legend:{data:["话后","空闲","通话","示忙"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#8572FF","#9AD30C","#EFAF10","#DC0055"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:6,interval:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]});function h(s){var a,o;g.xAxis.data=(a=s.AGENT_STATE_DATA_TREND)==null?void 0:a.cols,g.series=[{name:"话后",type:"line",smooth:!0,symbolSize:0,data:s.AGENT_STATE_DATA_TREND.AGENT_RECORD_HANDLE_TREND},{name:"空闲",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGENT_IDEA_TREND},{name:"通话",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGENT_CALL_TREND},{name:"示忙",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGENT_BUSY_TREND}],u.xAxis.data=(o=s.AGENT_STATE_DATA_TREND)==null?void 0:o.cols,u.series=[{name:"接话",type:"line",smooth:!0,symbolSize:0,data:s.AGENT_STATE_DATA_TREND.AGNET_TALK_TREND},{name:"回访",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGNET_CALLBACK_TREND},{name:"网络",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGNET_NETWORK_TREND},{name:"业务",type:"line",symbolSize:0,smooth:!0,data:s.AGENT_STATE_DATA_TREND.AGNET_BUSINESS_TREND}]}return(s,a)=>(n(),_("div",is,[t("div",ls,[t("div",cs,[(n(!0),_(E,null,N(c(f),(o,d)=>(n(),_("div",{class:"item",key:d},[t("div",rs,[t("img",{src:o.icon,alt:""},null,8,us)]),t("div",gs,l(o.name),1),t("div",ds,l(o.value),1)]))),128))]),t("div",ps,[a[0]||(a[0]=t("div",{class:"yName"},"人数",-1)),i(b,{option:c(u)},null,8,["option"])])]),t("div",As,[t("div",ms,[(n(!0),_(E,null,N(c(T),(o,d)=>(n(),_("div",{class:"item",key:d},[t("div",fs,[t("img",{src:o.icon,alt:""},null,8,Ts)]),t("div",vs,l(o.name),1),t("div",hs,l(o.value),1)]))),128))]),t("div",bs,[a[1]||(a[1]=t("div",{class:"yName"},"人数",-1)),i(b,{option:c(g)},null,8,["option"])])]),t("div",ys,[t("div",Es,[t("div",Ns,l(c(m)),1),a[2]||(a[2]=t("div",{class:"label"},"五楼总人数",-1))])])]))}},Ds=x(xs,[["__scopeId","data-v-ce2f182c"]]),Ss={style:{display:"flex"}},Fs={class:"box_a1"},Rs={class:"left"},Ls={__name:"A1",setup(D){return(A,e)=>(n(),_("div",Ss,[i(Z),t("div",Fs,[t("div",Rs,[i(ss),e[0]||(e[0]=t("div",{class:"content_center"},[t("img",{src:X,alt:""})],-1)),i(Ds)]),i(ts)])]))}},Bs=x(Ls,[["__scopeId","data-v-abb80ec9"]]);export{Bs as default};
