import{_ as P}from"./nav-C8ynh1UL.js";import{c as _,_ as V}from"./title-CoaCN9l8.js";import{_ as I,a as w}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{C as z,a as L}from"./age-Cosd1fO6.js";import{u as b}from"./main-BY7WlIbH.js";import{c as R,k as M,e as N,D as G,l as D,z as T,m as F,n as j,H as $,I as q,u as g,q as U,r as H}from"./vue-CkV0NT5Y.js";import"./yewu1-DVPbknuO.js";const K={class:"templateBox2x"},W={class:"box_c1"},k={class:"chartBox"},X={__name:"nativePlace",setup(C){const J=b();let t=R(()=>{var f;return((f=J.state.annularInfo)==null?void 0:f.jobHeadDetails)||{}});const x=M({grid:{left:"5%",top:"25%",bottom:"12%",right:"5%"},tooltip:{trigger:"axis",axisPointer:{lineStyle:{color:"rgba(255,255,255,0.8)"}}},xAxis:{data:["北京户口","津冀户口","其他省份"],axisLabel:{textStyle:{color:"#fff"}},axisTick:{inside:!0,length:4},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}}},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]});N(t,(f,r)=>{t.value=f,x.series={type:"custom",itemStyle:{color:function(O){return A}},labelLine:{show:!0},label:{show:!0,position:"top",color:"#00FFFF",fontSize:24,fontWeight:"bold"},data:[_.calculateSum(t.value.JOB_BEIJING_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_JIN_JI_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_OTHER_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0],renderItem:E}});var A={type:"linear",x:0,x2:1,y:0,y2:0,colorStops:[{offset:0,color:"#00DDFF"},{offset:.5,color:"#00FFFF"},{offset:.51,color:"#00A4BE"},{offset:1,color:"#005F72"}]},E=function(f,r){var O=r.value(1),v=r.coord([r.value(0),O]),e=r.size([r.value(1)-r.value(0),O]),s=r.style(),o=e[0]*.6,i=v[0]-o/2,l=v[1]-18,p=10,n=[[i+o/2,l]];n.push([i,e[1]+l]),n.push([i+o/2,e[1]+l+p]),n.push([i+o,e[1]+l]);var u={type:"group",children:[{z2:3,type:"polygon",shape:{points:n},style:s}]};return u};G(()=>{t.value=t.value,x.series={type:"custom",itemStyle:{color:function(f){return A}},labelLine:{show:!0},label:{show:!0,position:"top",color:"#00FFFF",fontSize:24,fontWeight:"bold"},data:[_.calculateSum(t.value.JOB_BEIJING_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_JIN_JI_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_OTHER_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0],renderItem:E}});function B(f,r,O){const v={beijing:f,jinji:r,qita:O};let e={beijing:"北京户口居多",jinji:"津冀户口居多",qita:"其他省份居多"};const o=Object.keys(v).sort((i,l)=>v[i]-v[l])[2];return e[o]}return(f,r)=>(U(),D("div",K,[T("div",W,[F(V,null,{default:j(()=>[$("籍贯分析 "+q(B(g(t).JOB_BEIJING_HOUS_RATE,g(t).JOB_JIN_JI_HOUS_RATE,g(t).JOB_OTHER_HOUS_RATE)),1)]),_:1}),T("div",k,[r[0]||(r[0]=T("div",{class:"yName"},"人数",-1)),F(w,{option:x},null,8,["option"])])])]))}},Y=I(X,[["__scopeId","data-v-3cd5bc27"]]),Q={style:{display:"flex"}},Z={__name:"C1",setup(C){const J=b();let t=R(()=>{var e;return((e=J.state.annularInfo)==null?void 0:e.jobHeadDetails)||{}}),x=H({}),A=H([{name:"28岁以下",value:0,rate:0,itemStyle:{color:"#00FFFF"}},{name:"28岁至35岁",value:0,rate:0,itemStyle:{color:"#0055FF"}},{name:"35岁以上",value:0,rate:0,itemStyle:{color:"#00DC55"}}]);const E=M({grid:{left:"10%",top:"25%",bottom:"12%",right:"8%"},tooltip:{trigger:"axis",axisPointer:{lineStyle:{color:"rgba(255,255,255,0.8)"}}},xAxis:{data:["北京户口","津冀户口","其他省份"],axisLabel:{textStyle:{color:"#fff"}},axisTick:{inside:!0,length:4},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}}},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]});N(t,(e,s)=>{t.value=e,B(t.value),x.value=v(A.value,.7),E.series={type:"custom",itemStyle:{color:function(o){return f}},labelLine:{show:!0},label:{show:!0,position:"top",color:"#00FFFF",fontSize:24,fontWeight:"bold"},data:[_.calculateSum(t.value.JOB_BEIJING_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_JIN_JI_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0,_.calculateSum(t.value.JOB_OTHER_HOUS_RATE,t.value.JOB_HEAD_COUNT)||0],renderItem:r}});function B(e){A.value=[{name:"28岁以下",value:_.calculateSum(e.JOB_AVG_28_RATE,e.JOB_HEAD_COUNT)||0,rate:e.JOB_AVG_28_RATE,itemStyle:{color:"#00FFFF"}},{name:"28岁至35岁",value:_.calculateSum(e.JOB_AVG_28_35_RATE,e.JOB_HEAD_COUNT)||0,rate:e.JOB_AVG_28_35_RATE,itemStyle:{color:"#0055FF"}},{name:"35岁以上",value:_.calculateSum(e.JOB_AVG_35_RATE,e.JOB_HEAD_COUNT)||0,rate:e.JOB_AVG_35_RATE,itemStyle:{color:"#00DC55"}}]}var f={type:"linear",x:0,x2:1,y:0,y2:0,colorStops:[{offset:0,color:"#00DDFF"},{offset:.5,color:"#00FFFF"},{offset:.51,color:"#00A4BE"},{offset:1,color:"#005F72"}]},r=function(e,s){var o=s.value(1),i=s.coord([s.value(0),o]),l=s.size([s.value(1)-s.value(0),o]),p=s.style(),n=l[0]*.6,u=i[0]-n/2,y=i[1]-18,a=10,m=[[u+n/2,y]];m.push([u,l[1]+y]),m.push([u+n/2,l[1]+y+a]),m.push([u+n,l[1]+y]);var d={type:"group",children:[{z2:3,type:"polygon",shape:{points:m},style:p}]};return d};function O(e,s,o,i,l,p){let n=(e+s)/2,u=e*Math.PI*2,y=s*Math.PI*2,a=n*Math.PI*2;o=!1,l=typeof l<"u"?l:1/3;let m=o?Math.sin(a)*.1:0,d=o?Math.cos(a)*.1:0,S=1;return{u:{min:-Math.PI,max:Math.PI*3,step:Math.PI/32},v:{min:0,max:Math.PI*2,step:Math.PI/20},x:function(c,h){return c<u?m+Math.cos(u)*(1+Math.cos(h)*l)*S:c>y?m+Math.cos(y)*(1+Math.cos(h)*l)*S:m+Math.cos(c)*(1+Math.cos(h)*l)*S},y:function(c,h){return c<u?d+Math.sin(u)*(1+Math.cos(h)*l)*S:c>y?d+Math.sin(y)*(1+Math.cos(h)*l)*S:d+Math.sin(c)*(1+Math.cos(h)*l)*S},z:function(c,h){return c<-Math.PI*.5?Math.sin(c):c>Math.PI*2.5?Math.sin(c)*p*.1:Math.sin(h)>0?1*p*.1:-1}}}function v(e,s){let o=[],i=0,l=0,p=0,n=[],u=(1-s)/(1+s);for(let a=0;a<e.length;a++){i+=e[a].value;let m={name:typeof e[a].name>"u"?`series${a}`:e[a].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:e[a],pieStatus:{selected:!1,hovered:!1,k:1/10}};if(typeof e[a].itemStyle<"u"){let d={};typeof e[a].itemStyle.color<"u"&&(d.color=e[a].itemStyle.color),typeof e[a].itemStyle.opacity<"u"&&(d.opacity=e[a].itemStyle.opacity),m.itemStyle=d}o.push(m)}for(let a=0;a<o.length;a++)p=l+o[a].pieData.value,o[a].pieData.startRatio=l/i,o[a].pieData.endRatio=p/i,o[a].parametricEquation=O(o[a].pieData.startRatio,o[a].pieData.endRatio,!1,!1,u,o[a].pieData.value),l=p,n.push(o[a].name);return{fontFamily:"Source Han Sans CN",xAxis3D:{},yAxis3D:{},zAxis3D:{},grid3D:{viewControl:{alpha:25,beta:0,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,autoRotate:!1,distance:90},left:"0%",width:"100%",show:!1,boxHeight:35},series:o}}return(e,s)=>(U(),D("div",Q,[F(P),F(z),F(L),F(Y)]))}},ie=I(Z,[["__scopeId","data-v-bf23ce58"]]);export{ie as default};
