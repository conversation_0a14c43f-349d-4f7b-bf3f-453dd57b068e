import{_ as I}from"./nav-C8ynh1UL.js";import{L as P,T as z,a as O,c as R,w as $,A as B}from"./AverageCall-Jj9nWwY4.js";import{_ as G,e as F,r as h,L as a}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{u as H}from"./main-BY7WlIbH.js";import{r as U,c as V,e as W,k as b,l as q,m as u,z as M,q as X}from"./vue-CkV0NT5Y.js";import"./title-CoaCN9l8.js";import"./chartBg-BQo3e-6F.js";const Y={style:{display:"flex"}},j={class:"box_a1"},f=16,d=8,J={__name:"A2",setup(K){const L=H();let l=U("dayData"),s=V(()=>{var e;return((e=L.state.annularInfo)==null?void 0:e.<PERSON>)||{}});W(s,(e,t)=>{S(e.callNumberStatCounts[l.value]),T(e.CHART_INBOUND_COUNT[l.value]),A(e.CHART_CONNECT_COUNT[l.value]),_(e.CHART_CONNECT_RATE[l.value]),N(s.value.doublingCounts[l.value]),s.value=e});const g=b({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(o){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${o.color};
        margin-bottom: 4px;
        background:${o.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${o.seriesName.includes("率")?o.value+"%":o.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]}),y=b({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(o){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${o.color};
        margin-bottom: 4px;
        background:${o.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${o.seriesName.includes("率")?o.value+"%":o.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]}),p=b({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(o){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${o.color};
        margin-bottom: 4px;
        background:${o.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${o.seriesName.includes("率")?o.value+"%":o.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]}),m=b({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(o){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${o.color};
        margin-bottom: 4px;
        background:${o.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${o.value+"%"} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter:"{value}%"}},series:[]});let c=[["rgba(0, 220, 85, 0.8)","#D0FFE2","rgba(0, 220, 85, 0.6)","#D0FFE2","rgba(0, 220, 85,1)","#D0FFE2"],["rgba(255, 0, 128,0.8)","#FFDAEC","rgba(255, 0, 128,0.6)","#FFDAEC","rgba(255, 0, 128,1)","#FFDAEC"],["rgba(0, 255, 255,0.8)","#CBFFFA","rgba(0, 255, 255,0.6)","#CBFFFA","rgba(0, 255, 255,1)","#CBFFFA"]],C=b({tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(e){const t=e[1];return`${t.name}:${t.value}人`}},grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff",fontSize:10},alignWithLabel:!0,boundaryGap:!0},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,1)+"千万":e>=1e6?e.substring(0,1)+"百万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]});function S(e){g.legend.data=["单次拨打","多次拨打"],g.color=["#00ffff","#0080FF"],g.xAxis.data=e.map(t=>t.DATE_NUMBER),g.series=[{name:"单次拨打",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.map(t=>Number(t.FIRST_CALL_COUNT))},{name:"多次拨打",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.map(t=>Number(t.NOT_FIRST_CALL_COUNT))}]}function T(e){y.legend.data=["企业","市民"],y.color=["#00ffff","#0080FF"],y.xAxis.data=e.xAxis.map(t=>t),y.series=[{name:"企业",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.EntHotLineData.map(t=>Number(t))},{name:"市民",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.citizenHotLineData.map(t=>Number(t))}]}function A(e){p.legend.data=["电话","网络","回访"],p.color=["#00ffff","#0080FF","#00DC55"],p.xAxis.data=e.xAxis.map(t=>t),p.series=[{name:"电话",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.FollowUpData.map(t=>Number(t))},{name:"网络",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.NetWorkData.map(t=>Number(t))},{name:"回访",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#00DC55"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.CallAllData.map(t=>Number(t))}]}function _(e){m.color=["#00ffff","#0080FF"],m.xAxis.data=e.xAxis.map(t=>t),m.series=[{name:"接通率",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new a(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.ConnRateData.map(t=>Number(t))}]}function N(e){C.xAxis.data=e.map(t=>t.name),C.series=[{name:"",type:"custom",renderItem:(t,o)=>{const r=o.coord([o.value(0),o.value(1)]);return console.log([o.value(0),o.value(1)]),{type:"group",children:[{type:"CubeLeft",shape:{api:o,xValue:o.value(0),yValue:o.value(1),x:r[0],y:r[1],xAxisPoint:o.coord([o.value(0),0])},style:{fill:new a(0,0,0,1,[{offset:0,color:c[t.dataIndexInside][0]},{offset:.96,color:c[t.dataIndexInside][1]}])}},{type:"CubeRight",shape:{api:o,xValue:o.value(0),yValue:o.value(1),x:r[0],y:r[1],xAxisPoint:o.coord([o.value(0),0])},style:{fill:new a(0,0,0,1,[{offset:0,color:c[t.dataIndexInside][2]},{offset:.98,color:c[t.dataIndexInside][3]}])}},{type:"CubeTop",shape:{api:o,xValue:o.value(0),yValue:o.value(1),x:r[0],y:r[1],xAxisPoint:o.coord([o.value(0),0])},style:{fill:new a(0,0,0,1,[{offset:0,color:c[t.dataIndexInside][4]},{offset:1,color:c[t.dataIndexInside][5]}])}}]}},data:e},{name:"",type:"bar",label:{normal:{show:!0,position:"top",formatter:t=>`${t.value}`,fontSize:16,offset:[0,-15],color:function(t){console.log(t);var o=["#81D3F8","#FF6600","#FF004D"];return o[t.dataIndex]}}},itemStyle:{color:"transparent"},tooltip:{},data:e}]}function k(e){console.log(e),l.value=e,S(s.value.callNumberStatCounts[l.value]),T(s.value.CHART_INBOUND_COUNT[l.value]),A(s.value.CHART_CONNECT_COUNT[l.value]),_(s.value.CHART_CONNECT_RATE[l.value]),N(s.value.doublingCounts[l.value])}const D=F({shape:{x:0,y:0},buildPath:function(e,t){const o=t.xAxisPoint,r=[t.x,t.y],i=[t.x-f,t.y-d],n=[o[0]-f,o[1]-d],x=[o[0],o[1]];e.moveTo(r[0],r[1]).lineTo(i[0],i[1]).lineTo(n[0],n[1]).lineTo(x[0],x[1]).closePath()}}),w=F({shape:{x:0,y:0},buildPath:function(e,t){const o=t.xAxisPoint,r=[t.x,t.y],i=[o[0],o[1]],n=[o[0]+f,o[1]-d],x=[t.x+f,t.y-d];e.moveTo(r[0],r[1]).lineTo(i[0],i[1]).lineTo(n[0],n[1]).lineTo(x[0],x[1]).closePath()}}),E=F({shape:{x:0,y:0},buildPath:function(e,t){const o=[t.x,t.y],r=[t.x+f,t.y-d],i=[t.x,t.y-f],n=[t.x-f,t.y-d];e.moveTo(o[0],o[1]).lineTo(r[0],r[1]).lineTo(i[0],i[1]).lineTo(n[0],n[1]).closePath()}});return h("CubeLeft",D),h("CubeRight",w),h("CubeTop",E),(e,t)=>{const o=I;return X(),q("div",Y,[u(o),M("div",j,[u(P,{onChangeDate:k}),u(z),u(O),u(R),u($),u(B)])])}}},le=G(J,[["__scopeId","data-v-7f2772cd"]]);export{le as default};
