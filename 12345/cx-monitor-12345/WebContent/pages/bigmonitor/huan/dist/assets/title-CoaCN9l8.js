import{_ as u}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{l as i,q as l,z as d,f as g}from"./vue-CkV0NT5Y.js";const S={toTimeFormat(t){const e=parseInt(t,10);if(isNaN(e))return"";const n=Math.floor(e/3600),r=Math.floor(e%3600/60),s=e%60;return n>0?`${n}h${r}m${s}s`:r>0?`${r}m${s}s`:`${s}s`},toPercentage(t){const e=Number(t);return isNaN(e)?"0.00%":(e*100).toFixed(2)+"%"},maskPhoneNumber(t){if(typeof t!="string"||t.length<7)return"输入无效";const e=t.substring(0,3),n=t.substring(t.length-4),r=t.length-e.length-n.length,s="*".repeat(r);return e+s+n},calculateSumAndPercentage(t,e){const n=Number(t)+Number(e);return console.log(t,e),n?(Number(t)/n*100).toFixed(2)+"%":0},formatTimestamp(t){const e=new Date(t),n=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),c=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0");return`${n}-${r}-${s} ${o}:${c}:${a}`},formatNumberWithUnits(t){if(console.log(1234,t),typeof t!="number"||isNaN(t))throw new Error("Input must be a valid number.");function e(n,r){return n.toFixed(1)+r}if(t>=1e8){const n=t/1e8;return e(n,"亿")}else if(t>=1e4){const n=t/1e4;return e(n,"万")}else return t.toString()},calculatePercentage(t,e){const n=e.reduce((s,o)=>s+(Number(o.value)||0),0);return(n>0?Number(t)/n*100:0).toFixed(2)+"%"},calculateSum(t,e){const n=Number(t)*Number(e)/100;return Math.round(n)}},m={},f={class:"titleBox"};function h(t,e){return l(),i("div",f,[d("span",null,[g(t.$slots,"default",{},void 0,!0)])])}const $=u(m,[["render",h],["__scopeId","data-v-fec8fc6e"]]);export{$ as _,S as c};
