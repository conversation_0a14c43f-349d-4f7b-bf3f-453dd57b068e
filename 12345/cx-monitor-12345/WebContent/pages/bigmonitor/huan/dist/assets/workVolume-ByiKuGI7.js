import{_ as u,L as l,c as f,a as d}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{u as p}from"./main-BY7WlIbH.js";import{c as m,k as b,e as g,D as x,l as _,z as o,I as y,u as h,m as S,q as F}from"./vue-CkV0NT5Y.js";const k={class:"templateBox1x"},D={class:"box"},N={class:"label"},w={class:"value"},C={class:"content_left_bottom"},L={__name:"workVolume",setup(B){const c=p();let a=m(()=>c.state.annularInfo||{});const s=b({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(t){var e="";return t.forEach(function(r){e+=`<div style="margin-left:4px;color:#fff;border:1px solid ${r.color};
        margin-bottom: 4px;
        background:${r.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${r.seriesName.includes("率")?r.value+"%":r.value} </div>`}),console.log(t),e}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(t){return t=t.toString(),t>=1e11?t.substring(0,5)/10+"亿":t>=1e10?t.substring(0,4)/10+"亿":t>=1e9?t.substring(0,3)/10+"亿":t>=1e8?t.substring(0,2)/10+"亿":t>=1e7?t.substring(0,4)+"万":t>=1e6?t.substring(0,3)+"万":t>=1e5?t.substring(0,2)+"万":t>=1e4?t.substring(0,2)/10+"万":t>=1e3?t.substring(0,2)/10+"千":t}}},series:[]});g(a,(t,e)=>{a.value=t,console.log(t),i(t.toDatWorkload)}),x(()=>{i(a.value.toDatWorkload)});function i(t){t&&(s.legend.data=["电话","网络","回访"],s.color=["#00ffff","#0080FF","#00DC55"],s.xAxis.data=t.xAxis.map(e=>e),s.series=[{name:"电话",type:"line",smooth:!0,symbolSize:0,areaStyle:{color:new l(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:t.phoneData.map(e=>Number(e))},{name:"网络",type:"line",symbolSize:0,smooth:!0,areaStyle:{color:new l(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:t.netWorkData.map(e=>Number(e))},{name:"回访",type:"line",symbolSize:0,smooth:!0,areaStyle:{color:new l(0,0,0,1,[{offset:0,color:"#00DC55"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:t.returnData.map(e=>Number(e))}])}return(t,e)=>{var n;const r=d;return F(),_("div",k,[o("div",D,[o("div",N,[e[0]||(e[0]=o("div",{style:{display:"flex"}},[o("div",{class:"iconBox"},[o("img",{src:f,alt:""})]),o("div",{class:"text"},"工作量")],-1)),o("div",w,y(((n=h(a).toDatWorkload)==null?void 0:n.callCount)||0),1)]),o("div",C,[e[1]||(e[1]=o("div",{class:"yName"},"工作量",-1)),S(r,{option:s},null,8,["option"])])])])}}},I=u(L,[["__scopeId","data-v-0e3ecb9f"]]);export{I as w};
