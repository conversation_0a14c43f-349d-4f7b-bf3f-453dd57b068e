import{_ as J,c as M}from"./title-CoaCN9l8.js";import{_ as le}from"./chartBg-BQo3e-6F.js";import{u as V,B as v}from"./main-BY7WlIbH.js";import{_ as z,a as G,L as F,e as Y,r as K,c as Q}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{c as W,r as P,e as R,K as ae,l as A,z as o,m as $,n as Z,H as ee,F as te,B as oe,A as se,u as n,I as N,C as j,q as O,k as X}from"./vue-CkV0NT5Y.js";const ne={class:"templateBox2x"},re={class:"left"},ie={class:"top"},ce={class:"selectBox"},ue=["onClick"],de={class:"center"},fe={class:"centerItem mb"},_e={class:"itemTop"},me={class:"value"},xe={class:"rate"},ge={class:"progress"},pe={class:"centerItem"},be={class:"itemTop"},ye={class:"value"},he={class:"rate"},Ce={class:"progress"},Te={class:"bottom"},ve={class:"echartsBox"},Ne={class:"infoNumber"},Fe={class:"dotBox"},Se={class:"label"},Le={class:"value"},Ae={class:"rate"},Oe={__name:"left",emits:["changeDate"],setup(q,{emit:S}){const f=V();let d=W(()=>{var u;return((u=f.state.annularInfo)==null?void 0:u.HotLineChart)||{}}),a=P({}),c=P("dayData"),e=0,t=null,r=P([{name:"本省电话量",value:0,itemStyle:{color:"#00FFFF"}},{name:"外省电话量",value:0,itemStyle:{color:"#FFDC00",opacity:.6}}]),y=[{label:"日",value:"dayData"},{label:"周",value:"weekData"},{label:"月",value:"monthData"}];R(c,(u,x)=>{var m,h;console.log(u,123),v.emit("changeDate",u),r.value=[{name:"本省电话量",value:Number((m=a.value[u])==null?void 0:m.THSI_LOCAL_COUNT),itemStyle:{color:"#00FFFF"}},{name:"外省电话量",value:Number((h=a.value[u])==null?void 0:h.NOT_THSI_LOCAL_COUNT),itemStyle:{color:"#FFDC00",opacity:.6}}],l.value=T(r.value,.7)});let l=P({});R(d,(u,x)=>{var m,h;a.value=u.callAnalyseCounts,r.value=[{name:"本省电话量",value:Number((m=a.value[c.value])==null?void 0:m.THSI_LOCAL_COUNT),itemStyle:{color:"#00FFFF"}},{name:"外省电话量",value:Number((h=a.value[c.value])==null?void 0:h.NOT_THSI_LOCAL_COUNT),itemStyle:{color:"#FFDC00",opacity:.6}}],l.value=T(r.value,.7)});function s(){e==2?e=0:e++,i(y[e].value)}function i(u){c.value=u}v.on("updateDate",u=>{var x,m;a.value=u.annularInfo.HotLineChart.callAnalyseCounts,r.value=[{name:"本省电话量",value:Number((x=a.value[c.value])==null?void 0:x.THSI_LOCAL_COUNT),itemStyle:{color:"#00FFFF"}},{name:"外省电话量",value:Number((m=a.value[c.value])==null?void 0:m.NOT_THSI_LOCAL_COUNT),itemStyle:{color:"#FFDC00",opacity:.6}}],l.value=T(r.value,.7),t=setInterval(()=>{s()},1e4)}),ae(()=>{clearInterval(t)});function p(u,x,m,h,b,I){let w=(u+x)/2,H=u*Math.PI*2,U=x*Math.PI*2,_=w*Math.PI*2;m=!1,b=typeof b<"u"?b:1/3;let D=m?Math.sin(_)*.1:0,L=m?Math.cos(_)*.1:0,E=1;return{u:{min:-Math.PI,max:Math.PI*3,step:Math.PI/32},v:{min:0,max:Math.PI*2,step:Math.PI/20},x:function(g,C){return g<H?D+Math.cos(H)*(1+Math.cos(C)*b)*E:g>U?D+Math.cos(U)*(1+Math.cos(C)*b)*E:D+Math.cos(g)*(1+Math.cos(C)*b)*E},y:function(g,C){return g<H?L+Math.sin(H)*(1+Math.cos(C)*b)*E:g>U?L+Math.sin(U)*(1+Math.cos(C)*b)*E:L+Math.sin(g)*(1+Math.cos(C)*b)*E},z:function(g,C){return g<-Math.PI*.5?Math.sin(g):g>Math.PI*2.5?Math.sin(g)*I*.1:Math.sin(C)>0?1*I*.1:-1}}}function T(u,x){let m=[],h=0,b=0,I=0,w=[],H=x/x;for(let _=0;_<u.length;_++){h+=u[_].value;let D={name:typeof u[_].name>"u"?`series${_}`:u[_].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:u[_],pieStatus:{selected:!1,hovered:!1,k:1/10}};if(typeof u[_].itemStyle<"u"){let L={};typeof u[_].itemStyle.color<"u"&&(L.color=u[_].itemStyle.color),typeof u[_].itemStyle.opacity<"u"&&(L.opacity=u[_].itemStyle.opacity),D.itemStyle=L}m.push(D)}for(let _=0;_<m.length;_++)I=b+m[_].pieData.value,m[_].pieData.startRatio=b/h,m[_].pieData.endRatio=I/h,m[_].parametricEquation=p(m[_].pieData.startRatio,m[_].pieData.endRatio,!1,!1,H,m[_].pieData.value),b=I,w.push(m[_].name);return{fontFamily:"Source Han Sans CN",xAxis3D:{},yAxis3D:{},zAxis3D:{},grid3D:{viewControl:{alpha:30,beta:70,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,autoRotate:!1,distance:120},left:"0%",width:"100%",show:!1,boxHeight:35},series:m}}return(u,x)=>{var h,b,I,w,H,U,_,D,L,E;const m=J;return O(),A("div",ne,[o("div",re,[o("div",ie,[$(m,null,{default:Z(()=>x[0]||(x[0]=[ee("历史话务数据分析")])),_:1}),o("div",ce,[(O(!0),A(te,null,oe(n(y),(g,C)=>(O(),A("div",{class:se(["selectItem",{active:g.value===n(c)}]),key:C,onClick:ot=>i(g.value)},N(g.label),11,ue))),128))])]),o("div",de,[o("div",fe,[o("div",_e,[x[1]||(x[1]=o("div",{class:"labelBox"},[o("div",{class:"index"},"01"),o("div",{class:"label"},"固话")],-1)),o("div",me,N((h=n(a)[n(c)])==null?void 0:h.NOT_PHONE_COUNT),1),o("div",xe,N(n(M).calculateSumAndPercentage((b=n(a)[n(c)])==null?void 0:b.NOT_PHONE_COUNT,(I=n(a)[n(c)])==null?void 0:I.PHONE_COUNT)),1)]),o("div",ge,[o("div",{class:"progress-line",style:j([{background:`linear-gradient(\r
                  270deg,\r
                  #ffffff 0%,\r
                  #00ffff 17%,\r
                  rgba(0, 255, 255, 0.2) 100%\r
                )`},{width:n(M).calculateSumAndPercentage((w=n(a)[n(c)])==null?void 0:w.NOT_PHONE_COUNT,(H=n(a)[n(c)])==null?void 0:H.PHONE_COUNT)}])},null,4)])]),o("div",pe,[o("div",be,[x[2]||(x[2]=o("div",{class:"labelBox"},[o("div",{class:"index"},"02"),o("div",{class:"label"},"手机")],-1)),o("div",ye,N((U=n(a)[n(c)])==null?void 0:U.PHONE_COUNT),1),o("div",he,N(n(M).calculateSumAndPercentage((_=n(a)[n(c)])==null?void 0:_.PHONE_COUNT,(D=n(a)[n(c)])==null?void 0:D.NOT_PHONE_COUNT)),1)]),o("div",Ce,[o("div",{class:"progress-line",style:j([{background:`linear-gradient(\r
                  270deg,\r
                  #ffffff 0%,\r
                  #00dcaa 17%,\r
                  rgba(0, 220, 170, 0.2) 100%\r
                )`},{width:n(M).calculateSumAndPercentage((L=n(a)[n(c)])==null?void 0:L.PHONE_COUNT,(E=n(a)[n(c)])==null?void 0:E.NOT_PHONE_COUNT)}])},null,4)])])]),o("div",Te,[o("div",ve,[$(G,{option:n(l)},null,8,["option"]),x[3]||(x[3]=o("img",{src:le,alt:""},null,-1))]),o("div",Ne,[(O(!0),A(te,null,oe(n(r),(g,C)=>(O(),A("div",{class:"infoNumberItem",key:C},[o("div",Fe,[o("div",{class:"dot",style:j({"border-color":g.itemStyle.color})},null,4),o("div",Se,N(g.name),1)]),o("div",Le,N(n(M).formatNumberWithUnits(g.value||0)),1),o("div",Ae,N(n(M).calculatePercentage(g.value,n(r))),1)]))),128))])])])])}}},it=z(Oe,[["__scopeId","data-v-03925272"]]),Ie={class:"templateBox1x"},He={class:"box"},De={class:"content_left_bottom"},Ee={__name:"TelephoneVolume",setup(q){const S=V();let f=P("dayData"),d=W(()=>{var e;return((e=S.state.annularInfo)==null?void 0:e.HotLineChart)||{}});R(d,(e,t)=>{c(e.callNumberStatCounts[f.value]),d.value=e}),v.on("changeDate",e=>{console.log(e,"changeDate"),f.value=e,c(d.value.callNumberStatCounts[f.value])}),v.on("updataChart",e=>{console.log(e.annularInfo.HotLineChart.callNumberStatCounts[f.value],"telephoneVolume"),d.value=e.annularInfo.HotLineChart,c(e.annularInfo.HotLineChart.callNumberStatCounts[f.value])});const a=X({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(r){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${r.color};
        margin-bottom: 4px;
        background:${r.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${r.seriesName.includes("率")?r.value+"%":r.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]});function c(e){a.legend.data=["单次拨打","多次拨打"],a.color=["#00ffff","#0080FF"],a.xAxis.data=e.map(t=>t.DATE_NUMBER),a.series=[{name:"单次拨打",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.map(t=>Number(t.FIRST_CALL_COUNT))},{name:"多次拨打",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.map(t=>Number(t.NOT_FIRST_CALL_COUNT))}]}return(e,t)=>{const r=J,y=G;return O(),A("div",Ie,[o("div",He,[$(r,null,{default:Z(()=>t[0]||(t[0]=[ee("电话量拨打趋势")])),_:1}),o("div",De,[t[1]||(t[1]=o("div",{class:"yName"},"数量",-1)),$(y,{option:a},null,8,["option"])])])])}}},ct=z(Ee,[["__scopeId","data-v-af47913f"]]),Pe={class:"templateBox1x"},$e={class:"box1"},Ue={class:"content_left_bottom"},B=16,k=8,Be={__name:"TelephoneVolumeTotal",setup(q){const S=V();let f=P("dayData"),d=W(()=>{var l;return((l=S.state.annularInfo)==null?void 0:l.HotLineChart)||{}});R(d,(l,s)=>{e(d.value.doublingCounts[f.value]),d.value=l}),v.on("changeDate",l=>{console.log(l,"changeDate"),f.value=l,e(d.value.doublingCounts[f.value])}),v.on("updataChart",l=>{console.log(l.annularInfo.HotLineChart,"TelephoneVolumeTotal"),e(l.annularInfo.HotLineChart.doublingCounts[f.value])});let a=[["rgba(0, 220, 85, 0.8)","#D0FFE2","rgba(0, 220, 85, 0.6)","#D0FFE2","rgba(0, 220, 85,1)","#D0FFE2"],["rgba(255, 0, 128,0.8)","#FFDAEC","rgba(255, 0, 128,0.6)","#FFDAEC","rgba(255, 0, 128,1)","#FFDAEC"],["rgba(0, 255, 255,0.8)","#CBFFFA","rgba(0, 255, 255,0.6)","#CBFFFA","rgba(0, 255, 255,1)","#CBFFFA"]],c=X({tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(l){const s=l[1];return`${s.name}:${s.value}人`}},grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff",fontSize:10},alignWithLabel:!0,boundaryGap:!0,interval:0},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(l){return l=l.toString(),l>=1e11?l.substring(0,5)/10+"亿":l>=1e10?l.substring(0,4)/10+"亿":l>=1e9?l.substring(0,3)/10+"亿":l>=1e8?l.substring(0,2)/10+"亿":l>=1e7?l.substring(0,1)+"千万":l>=1e6?l.substring(0,1)+"百万":l>=1e5?l.substring(0,2)+"万":l>=1e4?l.substring(0,2)/10+"万":l>=1e3?l.substring(0,2)/10+"千":l}}},series:[]});function e(l){c.xAxis.data=l.map(s=>s.name),c.series=[{name:"",type:"custom",renderItem:(s,i)=>{const p=i.coord([i.value(0),i.value(1)]);return console.log([i.value(0),i.value(1)]),{type:"group",children:[{type:"CubeLeft",shape:{api:i,xValue:i.value(0),yValue:i.value(1),x:p[0],y:p[1],xAxisPoint:i.coord([i.value(0),0])},style:{fill:new F(0,0,0,1,[{offset:0,color:a[s.dataIndexInside][0]},{offset:.96,color:a[s.dataIndexInside][1]}])}},{type:"CubeRight",shape:{api:i,xValue:i.value(0),yValue:i.value(1),x:p[0],y:p[1],xAxisPoint:i.coord([i.value(0),0])},style:{fill:new F(0,0,0,1,[{offset:0,color:a[s.dataIndexInside][2]},{offset:.98,color:a[s.dataIndexInside][3]}])}},{type:"CubeTop",shape:{api:i,xValue:i.value(0),yValue:i.value(1),x:p[0],y:p[1],xAxisPoint:i.coord([i.value(0),0])},style:{fill:new F(0,0,0,1,[{offset:0,color:a[s.dataIndexInside][4]},{offset:1,color:a[s.dataIndexInside][5]}])}}]}},data:l},{name:"",type:"bar",label:{normal:{show:!0,position:"top",formatter:s=>`${s.value}`,fontSize:16,offset:[0,-15],color:function(s){console.log(s);var i=["#81D3F8","#FF6600","#FF004D"];return i[s.dataIndex]}}},itemStyle:{color:"transparent"},tooltip:{},data:l}]}const t=Y({shape:{x:0,y:0},buildPath:function(l,s){const i=s.xAxisPoint,p=[s.x,s.y],T=[s.x-B,s.y-k],u=[i[0]-B,i[1]-k],x=[i[0],i[1]];l.moveTo(p[0],p[1]).lineTo(T[0],T[1]).lineTo(u[0],u[1]).lineTo(x[0],x[1]).closePath()}}),r=Y({shape:{x:0,y:0},buildPath:function(l,s){const i=s.xAxisPoint,p=[s.x,s.y],T=[i[0],i[1]],u=[i[0]+B,i[1]-k],x=[s.x+B,s.y-k];l.moveTo(p[0],p[1]).lineTo(T[0],T[1]).lineTo(u[0],u[1]).lineTo(x[0],x[1]).closePath()}}),y=Y({shape:{x:0,y:0},buildPath:function(l,s){const i=[s.x,s.y],p=[s.x+B,s.y-k],T=[s.x,s.y-B],u=[s.x-B,s.y-k];l.moveTo(i[0],i[1]).lineTo(p[0],p[1]).lineTo(T[0],T[1]).lineTo(u[0],u[1]).closePath()}});return K("CubeLeft",t),K("CubeRight",r),K("CubeTop",y),(l,s)=>{const i=J,p=G;return O(),A("div",Pe,[o("div",$e,[$(i,null,{default:Z(()=>s[0]||(s[0]=[ee("历史整合热线电话量")])),_:1}),o("div",Ue,[s[1]||(s[1]=o("div",{class:"yName"},"数量",-1)),$(p,{option:n(c)},null,8,["option"])])])])}}},ut=z(Be,[["__scopeId","data-v-366a8759"]]),Re={class:"templateBox1x"},we={class:"box"},Me={class:"label"},ke={class:"value"},Ve={class:"content_left_bottom"},ze={__name:"callVolume",setup(q){const S=V();let f=P("dayData"),d=W(()=>{var e;return((e=S.state.annularInfo)==null?void 0:e.HotLineChart)||{}});R(d,(e,t)=>{c(e.CHART_INBOUND_COUNT[f.value]),d.value=e}),v.on("changeDate",e=>{console.log(e,"changeDate"),f.value=e,c(d.value.CHART_INBOUND_COUNT[f.value])}),v.on("updataChart",e=>{var t;console.log(e,"callVolume"),d.value=e,c((t=e.HotLineChart)==null?void 0:t.CHART_INBOUND_COUNT[f.value])});const a=X({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(r){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${r.color};
        margin-bottom: 4px;
        background:${r.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${r.seriesName.includes("率")?r.value+"%":r.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]});function c(e){e&&(a.legend.data=["企业","市民"],a.color=["#00ffff","#0080FF"],a.xAxis.data=e.xAxis.map(t=>t),a.series=[{name:"企业",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.EntHotLineData.map(t=>Number(t))},{name:"市民",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.citizenHotLineData.map(t=>Number(t))}])}return(e,t)=>{var y;const r=G;return O(),A("div",Re,[o("div",we,[o("div",Me,[t[0]||(t[0]=o("div",{style:{display:"flex"}},[o("div",{class:"iconBox"},[o("img",{src:Q,alt:""})]),o("div",{class:"text"},"呼入量")],-1)),o("div",ke,N(n(d).CHART_INBOUND_COUNT?(y=n(d).CHART_INBOUND_COUNT[n(f)])==null?void 0:y.callCount:0),1)]),o("div",Ve,[t[1]||(t[1]=o("div",{class:"yName"},"呼入量",-1)),$(r,{option:a},null,8,["option"])])])])}}},dt=z(ze,[["__scopeId","data-v-cdbff3dd"]]),Ge={class:"templateBox1x"},We={class:"box"},qe={class:"label"},Xe={class:"value"},Ye={class:"content_left_bottom"},Ke={__name:"workVolume",setup(q){const S=V();let f=P("dayData"),d=W(()=>{var e;return((e=S.state.annularInfo)==null?void 0:e.HotLineChart)||{}});R(d,(e,t)=>{c(e.CHART_CONNECT_COUNT[f.value]),d.value=e}),v.on("updataChart",e=>{console.log(e,"workVolume"),d.value=e.annularInfo.HotLineChart,c(e.annularInfo.HotLineChart.CHART_CONNECT_COUNT[f.value])}),v.on("changeDate",e=>{console.log(e,"changeDate"),f.value=e,c(d.value.CHART_CONNECT_COUNT[f.value])});const a=X({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(r){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${r.color};
        margin-bottom: 4px;
        background:${r.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${r.seriesName.includes("率")?r.value+"%":r.value} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(e){return e=e.toString(),e>=1e11?e.substring(0,5)/10+"亿":e>=1e10?e.substring(0,4)/10+"亿":e>=1e9?e.substring(0,3)/10+"亿":e>=1e8?e.substring(0,2)/10+"亿":e>=1e7?e.substring(0,4)+"万":e>=1e6?e.substring(0,3)+"万":e>=1e5?e.substring(0,2)+"万":e>=1e4?e.substring(0,2)/10+"万":e>=1e3?e.substring(0,2)/10+"千":e}}},series:[]});function c(e){console.log(e,"array"),a.legend.data=["电话","网络","回访"],a.color=["#00ffff","#0080FF","#00DC55"],a.xAxis.data=e==null?void 0:e.xAxis.map(t=>t),a.series=[{name:"电话",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.FollowUpData.map(t=>Number(t))},{name:"网络",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.NetWorkData.map(t=>Number(t))},{name:"回访",type:"line",symbolSize:0,smooth:!1,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#00DC55"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:e.data.CallAllData.map(t=>Number(t))}]}return(e,t)=>{var y;const r=G;return O(),A("div",Ge,[o("div",We,[o("div",qe,[t[0]||(t[0]=o("div",{style:{display:"flex"}},[o("div",{class:"iconBox"},[o("img",{src:Q,alt:""})]),o("div",{class:"text"},"工作量")],-1)),o("div",Xe,N(n(d).CHART_CONNECT_COUNT?(y=n(d).CHART_CONNECT_COUNT[n(f)])==null?void 0:y.callCount:0),1)]),o("div",Ye,[t[1]||(t[1]=o("div",{class:"yName"},"工作量",-1)),$(r,{option:a},null,8,["option"])])])])}}},ft=z(Ke,[["__scopeId","data-v-d711ebbd"]]),je={class:"templateBox1x"},Je={class:"box"},Qe={class:"label"},Ze={class:"value"},et={class:"content_left_bottom"},tt={__name:"AverageCall",setup(q){const S=V();let f=P("dayData"),d=W(()=>{var e;return((e=S.state.annularInfo)==null?void 0:e.HotLineChart)||{}});v.on("changeDate",e=>{console.log(e,"changeDate"),f.value=e,c(d.value.CHART_CONNECT_RATE[f.value])}),R(d,(e,t)=>{c(e.CHART_CONNECT_RATE[f.value]),d.value=e}),v.on("updataChart",e=>{console.log(e,123),d.value=e,c(e.annularInfo.HotLineChart.CHART_CONNECT_RATE[f.value])});const a=X({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(e){var t="";return e.forEach(function(r){t+=`<div style="margin-left:4px;color:#fff;border:1px solid ${r.color};
        margin-bottom: 4px;
        background:${r.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${r.value+"%"} </div>`}),console.log(e),t}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter:"{value}%"}},series:[]});function c(e){a.color=["#00ffff","#0080FF"],a.xAxis.data=e.xAxis.map(t=>t),a.series=[{name:"接通率",type:"line",smooth:!1,symbolSize:0,areaStyle:{color:new F(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:e.data.ConnRateData.map(t=>Number(t))}]}return(e,t)=>{var y,l,s;const r=G;return O(),A("div",je,[o("div",Je,[o("div",Qe,[t[0]||(t[0]=o("div",{style:{display:"flex"}},[o("div",{class:"iconBox"},[o("img",{src:Q,alt:""})]),o("div",{class:"text"},"平均接通率")],-1)),o("div",Ze,N((y=n(d))!=null&&y.CHART_CONNECT_RATE?(s=(l=n(d))==null?void 0:l.CHART_CONNECT_RATE[n(f)])==null?void 0:s.callCount:0)+"% ",1)]),o("div",et,[t[1]||(t[1]=o("div",{class:"yName"},"接通率",-1)),$(r,{option:a},null,8,["option"])])])])}}},_t=z(tt,[["__scopeId","data-v-557a5bcf"]]);export{_t as A,it as L,ct as T,ut as a,dt as c,ft as w};
