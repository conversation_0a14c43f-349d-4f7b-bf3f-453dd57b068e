import{C as Z,a as K}from"./age-Cosd1fO6.js";import{w as J}from"./workVolume-ByiKuGI7.js";import{c as ee,w as te,A as se,L as le,T as oe,a as ae}from"./AverageCall-Jj9nWwY4.js";import{L as ne,R as X}from"./rightChart.vue_vue_type_style_index_0_scoped_ce2f182c_lang-B-DBYipG.js";import{_ as ie}from"./yewu1-DVPbknuO.js";import{_ as T,a as G,L as H,e as M,r as P}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{_ as E}from"./title-CoaCN9l8.js";import{u as R,B as k,a as ce}from"./main-BY7WlIbH.js";import{c as A,e as z,k as Y,l as f,q as u,m as b,z as e,n as C,H as F,u as g,I as d,r as $,D as O,F as L,B as S,y as D,J as W,A as N,K as U,L as V,o as re}from"./vue-CkV0NT5Y.js";import"./chartBg-BQo3e-6F.js";const de={class:"box1"},ue={class:"content_left_bottom"},_e={__name:"TelephoneVolumeTotal",setup(I){const p=R();let r=A(()=>p.state.annularInfo||{});z(r,(l,_)=>{r.value=l,t(l.toDayDoublingList)});let n=[["rgba(0, 220, 85, 0.8)","#D0FFE2"],["rgba(255, 0, 128,0.8)","#FFDAEC"],["rgba(0, 255, 255,0.8)","#CBFFFA"]],s=Y({grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:0,textStyle:{color:"#fff",fontSize:10}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0},data:["12345邮政","12388妇联","12315热线"]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"},formatter(l){return l=l.toString(),l>=1e11?l.substring(0,5)/10+"亿":l>=1e10?l.substring(0,4)/10+"亿":l>=1e9?l.substring(0,3)/10+"亿":l>=1e8?l.substring(0,2)/10+"亿":l>=1e7?l.substring(0,4)+"万":l>=1e6?l.substring(0,3)+"万":l>=1e5?l.substring(0,2)+"万":l>=1e4?l.substring(0,2)/10+"万":l>=1e3?l.substring(0,2)/10+"千":l}}},series:[]});k.on("changeDate",l=>{console.log(l,"changeDate"),currentTime.value=l,t(HotLineChart.value.toDayDoublingList)}),k.on("updataChart",l=>{console.log(l.annularInfo.toDayDoublingList,"wolegedacao"),t(l.annularInfo.toDayDoublingList)});function t(l){s.xAxis.data=l.map(_=>_.name),s.series=[{type:"bar",barMaxWidth:"auto",barWidth:40,itemStyle:{color:function(_){return{x:0,y:0,x2:0,y2:1,type:"linear",global:!1,colorStops:[{offset:0,color:n[_.dataIndex][0]},{offset:1,color:n[_.dataIndex][1]}]}}},data:l,label:{show:!0,position:"top",distance:10,fontSize:16}},{data:[1,1,1],type:"pictorialBar",barMaxWidth:"20",symbolOffset:[0,"50%"],symbolSize:[40,15],itemStyle:{color:function(_){return{x:0,y:0,x2:0,y2:1,type:"linear",global:!1,colorStops:[{offset:0,color:n[_.dataIndex][1]},{offset:1,color:n[_.dataIndex][1]}]}}}},{data:l,type:"pictorialBar",barMaxWidth:"20",symbolPosition:"end",symbolOffset:[0,"-50%"],symbolSize:[40,12],zlevel:2,itemStyle:{color:function(_){return{x:0,y:0,x2:0,y2:1,type:"linear",global:!1,colorStops:[{offset:0,color:n[_.dataIndex][0]},{offset:1,color:n[_.dataIndex][1]}]}}}}]}return(l,_)=>{const a=E,o=G;return u(),f("div",de,[b(a,null,{default:C(()=>_[0]||(_[0]=[F("整合热线电话量")])),_:1}),e("div",ue,[_[1]||(_[1]=e("div",{class:"yName"},"数量",-1)),b(o,{option:g(s)},null,8,["option"])])])}}},fe=T(_e,[["__scopeId","data-v-b83ea848"]]),be={class:"box"},ve={class:"label"},ge={style:{display:"flex"}},pe={class:"text"},me={class:"value"},he={class:"content_left_bottom"},ye={__name:"callVolume",setup(I){const p=R();let r=A(()=>p.state.annularInfo||{});const n=Y({tooltip:{trigger:"axis"},legend:{data:[],textStyle:{color:"#fff"},width:300,itemGap:5,right:0},color:[],grid:{top:"24%",left:"0%",right:"0%",bottom:"15%",containLabel:!0},tooltip:{trigger:"axis",borderColor:"rgba(0, 0, 0, 0)",borderWidth:0,backgroundColor:"rgba(0,0,0,0)",extraCssText:"box-shadow: 0 0 3px rgba(0, 0, 0, 0);",axisPointer:{type:"none"},textStyle:{color:"#fff"},formatter:function(t){var l="";return t.forEach(function(_){l+=`<div style="margin-left:4px;color:#fff;border:1px solid ${_.color};
        margin-bottom: 4px;
        background:${_.color+"1f"} ; border-radius: 4px; padding: 0 8px;">${_.seriesName.includes("率")?_.value+"%":_.value} </div>`}),console.log(t),l}},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(t){return t=t.toString(),t>=1e11?t.substring(0,5)/10+"亿":t>=1e10?t.substring(0,4)/10+"亿":t>=1e9?t.substring(0,3)/10+"亿":t>=1e8?t.substring(0,2)/10+"亿":t>=1e7?t.substring(0,4)+"万":t>=1e6?t.substring(0,3)+"万":t>=1e5?t.substring(0,2)+"万":t>=1e4?t.substring(0,2)/10+"万":t>=1e3?t.substring(0,2)/10+"千":t}}},series:[]});z(r,(t,l)=>{r.value=t,console.log(t),s(t.toDatCallIn)}),k.on("updataChart",t=>{s(t.annularInfo.toDatCallIn)});function s(t){console.log("呼入量",t),n.legend.data=["企业","市民"],n.color=["#00ffff","#0080FF"],n.xAxis.data=t.xAxis.map(l=>l),n.series=[{name:"企业",type:"line",smooth:!0,symbolSize:0,areaStyle:{color:new H(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"rgba(0,255,255,0)"}])},data:t.entData.map(l=>Number(l))},{name:"市民",type:"line",symbolSize:0,smooth:!0,areaStyle:{color:new H(0,0,0,1,[{offset:0,color:"#0080FF"},{offset:1,color:"rgba(0, 128, 255, 0)"}])},data:t.cityData.map(l=>Number(l))}]}return(t,l)=>{var o;const _=E,a=G;return u(),f("div",be,[e("div",ve,[e("div",ge,[e("div",pe,[b(_,null,{default:C(()=>l[0]||(l[0]=[F("实时呼入量 "),e("span",{style:{visibility:"hidden"}},"占位占位",-1)])),_:1})])]),e("div",me,d(((o=g(r).toDatCallIn)==null?void 0:o.callCount)||0),1)]),e("div",he,[l[1]||(l[1]=e("div",{class:"yName"},"呼入量",-1)),b(a,{option:n},null,8,["option"])])])}}},xe=T(ye,[["__scopeId","data-v-38e5751b"]]),Te={class:"templateBox6x"},Ie={class:"left"},Ae={__name:"left",setup(I){return(p,r)=>(u(),f("div",Te,[e("div",Ie,[b(ne),r[0]||(r[0]=e("div",{class:"content_center"},[e("img",{src:ie,alt:""})],-1)),b(fe),b(xe)])]))}},ze=T(Ae,[["__scopeId","data-v-c9cf0d72"]]),$e={class:"templateBox2x"},Ce={class:"box1"},Le={class:"title"},Se={class:"list"},Ee={key:0,class:"num icon0"},Fe={key:1,class:"num icon1"},Re={key:2,class:"num icon2"},De={class:"text"},Be=["innerHTML"],je={__name:"notification2x",setup(I){const p=R();let r=A(()=>{var s;return((s=p.state.annularInfo)==null?void 0:s.configureNotice)||{}}),n=$([]);return z(r,(s,t)=>{r.value=s,n.value=[s.NOTICE_ONE,s.NOTICE_TWO,s.NOTICE_THREE,s.NOTICE_FOUR]}),O(()=>{n.value=[r.value.NOTICE_ONE,r.value.NOTICE_TWO,r.value.NOTICE_THREE,r.value.NOTICE_FOUR]}),(s,t)=>{const l=E;return u(),f("div",$e,[e("div",Ce,[e("div",Le,[b(l,null,{default:C(()=>[F(d(g(r).NOTICE_TOP),1)]),_:1})]),t[0]||(t[0]=e("div",{class:"line"},null,-1)),e("ul",Se,[(u(!0),f(L,null,S(g(n),(_,a)=>(u(),f("li",{key:a,class:"item"},[a==0?(u(),f("div",Ee,[e("span",null,d(a+1<10?"0"+(a+1):a+1),1)])):D("",!0),a>0&&a<3?(u(),f("div",Fe,[e("span",null,d(a+1<10?"0"+(a+1):a+1),1)])):D("",!0),a>=3?(u(),f("div",Re,[e("span",null,d(a+1<10?"0"+(a+1):a+1),1)])):D("",!0),e("div",De,[e("span",{style:{"white-space":"pre"},innerHTML:_},null,8,Be)])]))),128))])])])}}},ke=T(je,[["__scopeId","data-v-a9fd4860"]]),Oe={class:"templateBox2x"},Ne={class:"box2"},qe={class:"box2-children"},He={class:"itemBox"},Me={class:"label"},Pe={class:"value"},Ge={class:"itemBox"},Ye={class:"label"},Ue={class:"value"},Xe={__name:"hotLine",setup(I){const p=R();let r=$([{label:"挂机满意率",value:"0.00%"},{label:"话后处理时长",value:"0:00"},{label:"平均通话时长",value:"0:00"}]),n=$([{label:"挂机满意率",value:"0.00%"},{label:"话后处理时长",value:"0:00"},{label:"平均通话时长",value:"0:00"}]),s=A(()=>{var t;return((t=p.state.annularInfo)==null?void 0:t.handlingItemsEvect)||{}});return z(s,(t,l)=>{console.log(p.state),s.value=t,r.value=[{label:"挂机满意率",value:t.SMRX_SATISFIED},{label:"话后处理时长",value:t.SMRX_RECORD_HANDLE},{label:"平均通话时长",value:t.SMRX_AVG_CALL_TIME}],n.value=[{label:"挂机满意率",value:t.QYRX_SATISFIED},{label:"话后处理时长",value:t.QYRX_RECORD_HANDLE},{label:"平均通话时长",value:t.QYRX_AVG_CALL_TIME}]}),O(()=>{var t;s.value=(t=p.state.annularInfo)==null?void 0:t.handlingItemsEvect,r.value=[{label:"挂机满意率",value:s.value.SMRX_SATISFIED},{label:"话后处理时长",value:s.value.SMRX_RECORD_HANDLE},{label:"平均通话时长",value:s.value.SMRX_AVG_CALL_TIME}],n.value=[{label:"挂机满意率",value:s.value.QYRX_SATISFIED},{label:"话后处理时长",value:s.value.QYRX_RECORD_HANDLE},{label:"平均通话时长",value:s.value.QYRX_AVG_CALL_TIME}]}),(t,l)=>(u(),f("div",Oe,[e("div",Ne,[e("div",qe,[b(E,null,{default:C(()=>l[0]||(l[0]=[F("市民热线")])),_:1}),e("div",He,[(u(!0),f(L,null,S(g(r),(_,a)=>(u(),f("div",{class:"item",key:a},[e("div",Me,d(_.label),1),e("div",Pe,d(_.value),1)]))),128))])]),e("div",null,[b(E,null,{default:C(()=>l[1]||(l[1]=[F("企业热线")])),_:1}),e("div",Ge,[(u(!0),f(L,null,S(g(n),(_,a)=>(u(),f("div",{class:"item",key:a},[e("div",Ye,d(_.label),1),e("div",Ue,d(_.value),1)]))),128))])])])]))}},Q=T(Xe,[["__scopeId","data-v-25516e26"]]),Ve={class:"templateBox1x"},Qe={class:"box3"},we={class:"content"},We={class:"top"},Ze={class:"name"},Ke={class:"name"},Je={class:"center"},et={class:"bottom"},tt={class:"name"},st={class:"name"},lt={class:"name"},ot={__name:"ltZbry",setup(I){const p=R();let r=A(()=>{var n;return((n=p.state.annularInfo)==null?void 0:n.serviceLeave)||{}});return z(r,(n,s)=>{r.value=n}),(n,s)=>(u(),f("div",Ve,[e("div",Qe,[b(E,null,{default:C(()=>s[0]||(s[0]=[F("联通值班人员")])),_:1}),e("div",we,[e("div",We,[e("div",null,[s[1]||(s[1]=e("div",{class:"job"},"联通带班领导",-1)),e("div",Ze,d(g(r).LTNAME1),1)]),e("div",null,[s[2]||(s[2]=e("div",{class:"job"},"联通值班经理",-1)),e("div",Ke,d(g(r).LTNAME2),1)])]),e("div",Je,[s[3]||(s[3]=e("div",{class:"job"},"联通运营主管",-1)),s[4]||(s[4]=e("div",{class:"lineBox"},[e("div",{class:"ver"}),e("div",{class:"btm"})],-1)),e("div",et,[e("div",tt,d(g(r).LTNAME3),1),e("div",st,d(g(r).LTNAME4),1),e("div",lt,d(g(r).LTNAME5),1)])])])])]))}},at=T(ot,[["__scopeId","data-v-6064fae5"]]),nt={class:"templateBox1x"},it={class:"box3 box4"},ct={class:"content"},rt={class:"name"},dt={class:"top"},ut={class:"name1"},_t={class:"name1"},ft={class:"center"},bt={class:"bottom"},vt={class:"name"},gt={class:"name"},pt={class:"name"},mt={__name:"zfZbry",setup(I){const p=R();let r=$([{label:"挂机满意率",value:"0.00%"},{label:"话后处理时长",value:"0:00"},{label:"平均通话时长",value:"0:00"}]),n=$([{label:"挂机满意率",value:"0.00%"},{label:"话后处理时长",value:"0:00"},{label:"平均通话时长",value:"0:00"}]),s=A(()=>{var a;return((a=p.state.annularInfo)==null?void 0:a.serviceLeave)||{}}),t=A(()=>{var a;return((a=p.state.annularInfo)==null?void 0:a.handlingItemsEvect)||{}}),l=A(()=>{var a;return((a=p.state.annularInfo)==null?void 0:a.configureNotice)||{}}),_=$([]);return z(l,(a,o)=>{l.value=a,_.value=[a.NOTICE_ONE,a.NOTICE_TWO,a.NOTICE_THREE,a.NOTICE_FOUR]}),z(s,(a,o)=>{s.value=a}),z(t,(a,o)=>{console.log(p.state),t.value=a,r.value=[{label:"挂机满意率",value:a.SMRX_SATISFIED},{label:"话后处理时长",value:a.SMRX_RECORD_HANDLE},{label:"平均通话时长",value:a.SMRX_AVG_CALL_TIME}],n.value=[{label:"挂机满意率",value:a.QYRX_SATISFIED},{label:"话后处理时长",value:a.QYRX_RECORD_HANDLE},{label:"平均通话时长",value:a.QYRX_AVG_CALL_TIME}]}),(a,o)=>(u(),f("div",nt,[e("div",it,[b(E,null,{default:C(()=>o[0]||(o[0]=[F("政府值班人员")])),_:1}),e("div",ct,[e("div",null,[o[1]||(o[1]=e("div",{class:"job"},"带班领导",-1)),e("div",rt,d(g(s).JJDBLD),1)]),e("div",dt,[e("div",null,[o[2]||(o[2]=e("div",{class:"job"},"带班主任",-1)),e("div",ut,d(g(s).ZXZBZR),1)]),e("div",null,[o[3]||(o[3]=e("div",{class:"job"},"带班处长",-1)),e("div",_t,d(g(s).ZXZBRY),1)])]),e("div",ft,[o[4]||(o[4]=e("div",{class:"job"},"中心值班人员",-1)),o[5]||(o[5]=e("div",{class:"lineBox"},[e("div",{class:"ver"}),e("div",{class:"btm"})],-1)),e("div",bt,[e("div",vt,d(g(s).LTYYZG1),1),e("div",gt,d(g(s).LTYYZG2),1),e("div",pt,d(g(s).LTYYZG3),1)])])])])]))}},w=T(mt,[["__scopeId","data-v-5b62aa0c"]]),ht={class:"tableBody"},yt={class:"tableTd"},xt={class:"tableTd"},Tt={class:"tableTd"},It={class:"tableTd"},At={class:"tableTd pf"},zt={__name:"personageTable",setup(I){let p=[{jx:"第一",bm:"受理组",name:"吴卓",bz:"专席1组",gh:"14404",pf:"178.58"},{jx:"第二",bm:"受理组",name:"石美灵",bz:"受理4班",gh:"14126",pf:"168.25"},{jx:"第三",bm:"受理组",name:"关文飞",bz:"受理35班",gh:"17327",pf:"159.02"},{jx:"第三",bm:"受理组",name:"梁洁铭",bz:"受理32班",gh:"11862",pf:"159.02"},{jx:"最佳新人",bm:"受理组",name:"刘振明",bz:"辅助5班",gh:"11605",pf:"130.44"}];return(r,n)=>(u(),f("div",null,[n[0]||(n[0]=W('<div class="tableHeader" data-v-0dbc6cfc><div class="tableTd" data-v-0dbc6cfc>优秀奖</div><div class="tableTd" data-v-0dbc6cfc>部门</div><div class="tableTd" data-v-0dbc6cfc>姓名</div><div class="tableTd" data-v-0dbc6cfc>班组</div><div class="tableTd" data-v-0dbc6cfc>工号</div><div class="tableTd" data-v-0dbc6cfc>综合评分</div></div>',1)),e("div",ht,[(u(!0),f(L,null,S(g(p),(s,t)=>(u(),f("div",{class:"tableLine",key:t},[e("div",{class:N(["tableTd jx",[{yi:s.jx==="第一"},{er:s.jx==="第二"},{san:s.jx==="第三"},{qt:s.jx!=="第一"&&s.jx!=="第二"&&s.jx!=="第三"}]])},d(s.jx),3),e("div",yt,d(s.bm),1),e("div",xt,d(s.name),1),e("div",Tt,d(s.bz),1),e("div",It,d(s.gh),1),e("div",At,d(s.pf),1)]))),128))])]))}},$t=T(zt,[["__scopeId","data-v-0dbc6cfc"]]),Ct={class:"tableHeader"},Lt={class:"tableBody"},St={class:"tableTd"},Et={class:"tableTd"},Ft={class:"tableTd"},Rt={class:"tableTd pf"},Dt={__name:"groupTable",setup(I){let p=A(()=>{var n;return((n=r.state.annularInfo)==null?void 0:n.goodWorkLiad)||{}});const r=R();return(n,s)=>(u(),f("div",null,[e("div",Ct,[(u(!0),f(L,null,S(g(p).GOOD_WORK_TITLE,(t,l)=>(u(),f("div",{class:"tableTd",key:l},d(t),1))),128))]),e("div",Lt,[(u(!0),f(L,null,S(g(p).GOOD_WORK_DATA,(t,l)=>(u(),f("div",{class:"tableLine",key:l},[e("div",{class:N(["tableTd jx",[{yi:t.F0==="第一"},{er:t.F0==="第二"},{san:t.F0==="第三"},{qt:t.F0!=="第一"&&t.F0!=="第二"&&t.F0!=="第三"}]])},d(t.F0),3),e("div",St,d(t.F1),1),e("div",Et,d(t.F2),1),e("div",Ft,d(t.F3),1),e("div",Rt,d(t.F4),1)]))),128))])]))}},Bt=T(Dt,[["__scopeId","data-v-06232f98"]]),jt={class:"templateBox2x"},kt={class:"left"},Ot={class:"top"},Nt={class:"selectBox",style:{visibility:"hidden"}},qt=["onClick"],Ht={class:"bottom"},Mt={__name:"rank",emits:["changeRank"],setup(I,{emit:p}){const r=R();let n=A(()=>{var i;return((i=r.state.annularInfo)==null?void 0:i.goodWorkLiad)||{}}),s=$("group"),t=0,l=null,_=[{label:"班组",value:"group"},{label:"班组",value:"group"}];function a(){t==2?t=0:t++,o(_[t].value)}function o(i){s.value=i}return O(()=>{l=setInterval(()=>{a()},1e4)}),U(()=>{clearInterval(l)}),(i,c)=>{const h=E;return u(),f("div",jt,[e("div",kt,[e("div",Ot,[b(h,null,{default:C(()=>[F(d(g(n).GOOD_WORK_NAME),1)]),_:1}),e("div",Nt,[(u(!0),f(L,null,S(g(_),(y,x)=>(u(),f("div",{class:N(["selectItem",{active:y.value===g(s)}]),key:x,onClick:v=>o(y.value)},d(y.label),11,qt))),128))])]),e("div",Ht,[g(s)==="person"?(u(),V($t,{key:0})):D("",!0),g(s)==="group"?(u(),V(Bt,{key:1})):D("",!0)])])])}}},Pt=T(Mt,[["__scopeId","data-v-aeefd4bb"]]),Gt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAAAmCAYAAAAr1RLQAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAN/SURBVHic7Zy7ThtREIa/XeMLCBOBoLBEQUHlli5SJJ4gHVKKvESeAPwAUV6CIhIddQqk1LRUFBRILkDcTMA2+GyKBXzsvZyzyy5rw3wS2mF2zsXW0e+Z1WocADyvCfwEPuN5FRxHAR6e5+E4yngFDzBfg+OS3Ru3g+uM+6NtpTxcN9wG9fL/8zilfN/zfT0mzBfnHww8SqXBi/3sL5XCY/x9DwCPx0fFzIy+t3z/rq4USimWlhTttqLRGAADHOcRwHk6PH9QahfX3Qf+McQLsYe+fh8qlaDfbNvP0etBtRo/x2hM/NrdLtRqwdj7e5idtf0s4fdvb2F+Pjqm04F63bdvbmBhIfl3d34Oy8u+fXYGKyvJ5zg+hvV13z46gmYzeo69PdjaGve7ADjOwAV+Abu47m/gjizp9+1je71Mly6U29uid+Bzemofe3iYZGZFq+WBf5K+APtJRltTqdjHVqu5bKEQ5ueL3oHP6qp97MZGsrl3dhSAi+dVeXi4TzbaElEgM9fX+e0jPwV6wU01yhZRIDOfPuW3jzwV6Il8D5AoULGIAk0pHyEHekIUKA8kB8oIUSAzkgPFIApULKJAU4rkQBkhCmRGcqAYRIHMSA4UgyhQsYgCTSmSA2WEKJCZD5EDPTykmnxEgUyHyUaB0h6ybjfdOJ27BG+66ArU6cTH2uRAFxf2a+voCnRyEh9ro0CtVsBld4DKZauwAPqhMf2c2RyOtD9ztVq6cTpzc/axugLV669fe2kp3ThdgdbW4mNtFGh7O+CSHCgPJAfKCMmBzHyIHCgtokBm5DlQDKJAxSIKNKVIDpQRk6JAScr4+4jXw01lvK46um0q46NyoMvLoZ2kjG+3h7auQKYyXlegg4PwmNRlfFomRYGSlPGzs+F+Uxmvq45um8r4qBxocXFoJynjG42hrSuQqYzXFWhzMzzmzcv4SVGgt0ZyoIyYFAV6ayQHyghRIDPyHCgGUSAz8hwoBlGg9OhVWBKkCnsHZJED6VVYEqQKewdIDpQRokBm3kEO1KNcjnh69kpEgYrlTRTIcf4CX1ONNiEKVCz59gdywVegH8B34BuQ4LU7C0SBzExnDuTSbDoADjDaZBMqDBs72jXPTH+NbsRp8kXft2uyGddwM6zJ5njjzPFmmqPxweacwSago0029Qabk9Bks9NR1Ou+fXER2WTzPy2zxdRymECmAAAAAElFTkSuQmCC",Yt=Object.freeze(Object.defineProperty({__proto__:null,default:Gt},Symbol.toStringTag,{value:"Module"})),Ut=""+new URL("gj-DTTAYxlc.png",import.meta.url).href,Xt=Object.freeze(Object.defineProperty({__proto__:null,default:Ut},Symbol.toStringTag,{value:"Module"})),Vt=""+new URL("jj-CGsw37R4.png",import.meta.url).href,Qt=Object.freeze(Object.defineProperty({__proto__:null,default:Vt},Symbol.toStringTag,{value:"Module"})),wt=""+new URL("jq-C61wN0Yw.png",import.meta.url).href,Wt=Object.freeze(Object.defineProperty({__proto__:null,default:wt},Symbol.toStringTag,{value:"Module"})),Zt=""+new URL("yj-DN5G881x.png",import.meta.url).href,Kt=Object.freeze(Object.defineProperty({__proto__:null,default:Zt},Symbol.toStringTag,{value:"Module"})),Jt={class:"templateBox2x"},es={class:"left"},ts={class:"top"},ss={class:"bottom"},ls={class:"leftList"},os={class:"selectBox"},as=["onClick"],ns=["src"],is={class:"table"},cs={class:"tableBody"},rs={class:"tableTd"},ds={class:"tableTd"},us={class:"tableTd"},_s={class:"tableTd"},fs={class:"tableTd"},bs={__name:"prize",emits:["changePrize"],setup(I,{emit:p}){const r=R();let n=A(()=>{var y;return((y=r.state.annularInfo)==null?void 0:y.HotLineChart)||{}});const s=Object.assign({"/src/assets/info/active.png":Yt,"/src/assets/info/gj.png":Xt,"/src/assets/info/jj.png":Qt,"/src/assets/info/jq.png":Wt,"/src/assets/info/yj.png":Kt});let t=$({}),l=$("gj"),_=$([{gh:11135,name:"刘一涵",bz:"白2班",cqfz:"4组"},{gh:13489,name:"王泽红",bz:"辅助",cqfz:"4组"},{gh:87509,name:"常久伟",bz:"网络",cqfz:"4组"},{gh:16758,name:"卢慧玲",bz:"网络",cqfz:"4组"},{gh:11427,name:"白双",bz:"26班",cqfz:"4组"}]),a=0,o=null,i=[{label:"冠军",value:"gj",icon:s["/src/assets/info/gj.png"].default,table:[{gh:11135,name:"刘一涵",bz:"白2班",cqfz:"4组"},{gh:13489,name:"王泽红",bz:"辅助",cqfz:"4组"},{gh:87509,name:"常久伟",bz:"网络",cqfz:"4组"},{gh:16758,name:"卢慧玲",bz:"网络",cqfz:"4组"},{gh:11427,name:"白双",bz:"26班",cqfz:"4组"}]},{label:"亚军",value:"yj",icon:s["/src/assets/info/yj.png"].default,table:[{gh:14879,name:"于雪婷",bz:"28班",cqfz:"3组"},{gh:13373,name:"高雨莲",bz:"24班",cqfz:"3组"},{gh:13759,name:"谷艳娟",bz:"专席1班",cqfz:"3组"},{gh:13580,name:"闫云帅",bz:"诉求",cqfz:"3组"},{gh:87166,name:"朱明",bz:"质检",cqfz:"3组"}]},{label:"季军",value:"jj",icon:s["/src/assets/info/jj.png"].default,table:[{gh:87072,name:"孙帅",bz:"网络",cqfz:"1组"},{gh:12611,name:"陈佳豪",bz:"网络",cqfz:"1组"},{gh:17803,name:"谢雨欣",bz:"回访",cqfz:"1组"},{gh:87827,name:"褚红卫",bz:"质检",cqfz:"1组"},{gh:17141,name:"苏乐童",bz:"企业",cqfz:"1组"}]},{label:"进取奖",value:"ds",icon:s["/src/assets/info/jq.png"].default,table:[{gh:13238,name:"海洋",bz:"回访",cqfz:"2组"},{gh:87062,name:"吴子萌",bz:"辅助",cqfz:"2组"},{gh:11407,name:"白书朋",bz:"辅助",cqfz:"2组"},{gh:13468,name:"张仲",bz:"4班",cqfz:"2组"},{gh:13309,name:"于冲冲",bz:"诉求",cqfz:"2组"}]},{label:"进取奖",value:"dw",icon:s["/src/assets/info/jq.png"].default,table:[{gh:17003,name:"黄凯辉",bz:"32班",cqfz:"5组"},{gh:11247,name:"刘磊",bz:"21班",cqfz:"5组"},{gh:87773,name:"刘巧娜",bz:"质检",cqfz:"5组"},{gh:12888,name:"杨帅",bz:"业务",cqfz:"5组"},{gh:17130,name:"孟琦",bz:"企业",cqfz:"5组"}]}];z(l,(y,x)=>{k.emit("changePrize",y),console.log(y),i.map((v,m)=>{v.value===y&&(_.value=v.table)})}),z(n,(y,x)=>{t.value=y.callAnalyseCounts});function c(){a==2?a=0:a++,h(i[a].value)}function h(y){l.value=y}return O(()=>{t.value=n.value.callAnalyseCounts,o=setInterval(()=>{c()},1e4)}),U(()=>{clearInterval(o)}),(y,x)=>{const v=E;return u(),f("div",Jt,[e("div",es,[e("div",ts,[b(v,null,{default:C(()=>x[0]||(x[0]=[F("2024年岗位练兵决赛参赛人员获奖情况")])),_:1})]),e("div",ss,[e("div",ls,[e("div",os,[(u(!0),f(L,null,S(g(i),(m,q)=>(u(),f("div",{class:N(["selectItem",{active:g(l)==m.value}]),onClick:qs=>h(m.value),key:q},[e("img",{src:m.icon,alt:"",class:"jp"},null,8,ns),e("span",null,d(m.label),1)],10,as))),128))])]),e("div",is,[x[1]||(x[1]=W('<div class="tableHeader" data-v-6728448b><div class="tableTd" data-v-6728448b>序号</div><div class="tableTd" data-v-6728448b>工号</div><div class="tableTd" data-v-6728448b>姓名</div><div class="tableTd" data-v-6728448b>班组</div><div class="tableTd" data-v-6728448b>抽签分组</div></div>',1)),e("div",cs,[(u(!0),f(L,null,S(g(_),(m,q)=>(u(),f("div",{class:"tableLine",key:q},[e("div",rs,d(q+1),1),e("div",ds,d(m.gh),1),e("div",us,d(m.name),1),e("div",_s,d(m.bz),1),e("div",fs,d(m.cqfz),1)]))),128))])])])])])}}},vs=T(bs,[["__scopeId","data-v-6728448b"]]),gs={class:"templateBox3x"},ps={class:"left"},ms={class:"top"},hs={class:"selectBox"},ys=["onClick"],xs={class:"bottom"},Ts={class:"list"},Is={key:0,class:"num icon0"},As={key:1,class:"num icon1"},zs={key:2,class:"num icon2"},$s={class:"lable"},Cs={class:"text"},Ls={__name:"business",emits:["changeBusiness"],setup(I,{emit:p}){const r=R();let n=A(()=>{var c;return((c=r.state.annularInfo)==null?void 0:c.busiTipsData)||{}}),s=$([]),t=$("BUSI_HIGH_FREQUENCY"),l=0,_=null,a=[{label:"每日重点",value:"BUSI_DAILY_GREAT"},{label:"高频热点",value:"BUSI_HIGH_FREQUENCY"},{label:"质检",value:"BUSI_QUALITY"}];console.log(n,"busiTipsData"),z(t,(c,h)=>{k.emit("changeBusiness",c),s.value=n.value[c]}),k.on("updataChart",c=>{s.value=c.annularInfo.busiTipsData[t.value]});function o(){l==2?l=0:l++,i(a[l].value)}function i(c){t.value=c}return O(()=>{setTimeout(()=>{t.value="BUSI_HIGH_FREQUENCY",t.value="BUSI_DAILY_GREAT"},100),s.value=n.value[t.value],_=setInterval(()=>{o()},3e4)}),U(()=>{clearInterval(_)}),(c,h)=>{const y=E,x=re("Vue3Marquee");return u(),f("div",gs,[e("div",ps,[e("div",ms,[b(y,null,{default:C(()=>h[0]||(h[0]=[F("业务提示 "),e("span",{style:{visibility:"hidden"}},"占位",-1)])),_:1}),e("div",hs,[(u(!0),f(L,null,S(g(a),(v,m)=>(u(),f("div",{class:N(["selectItem",{active:v.value===g(t)}]),key:m,onClick:q=>i(v.value)},d(v.label),11,ys))),128))])]),e("div",xs,[e("ul",Ts,[(u(!0),f(L,null,S(g(s),(v,m)=>(u(),f("li",{key:m,class:"item"},[m==0?(u(),f("div",Is,[e("span",null,d(m+1<10?"0"+(m+1):m+1),1)])):D("",!0),m>0&&m<3?(u(),f("div",As,[e("span",null,d(m+1<10?"0"+(m+1):m+1),1)])):D("",!0),m>=3?(u(),f("div",zs,[e("span",null,d(m+1<10?"0"+(m+1):m+1),1)])):D("",!0),e("div",null,[e("div",$s,d(v.title),1),b(x,{duration:(v==null?void 0:v.value.length)>50&&(v==null?void 0:v.value.length)<60?70:(v==null?void 0:v.value.length)>60&&(v==null?void 0:v.value.length)<80?80:(v==null?void 0:v.value.length)>80&&(v==null?void 0:v.value.length)<100?100:(v==null?void 0:v.value.length)>100?140:40,direction:"normal",pauseOnHover:""},{default:C(()=>[e("p",Cs,d(v.value),1)]),_:2},1032,["duration"])])]))),128))])])])])}}},Ss=T(Ls,[["__scopeId","data-v-4aff6c61"]]),Es={class:"templateBox2x"},Fs={class:"box1"},Rs={class:"content_left_bottom"},B=16,j=8,Ds={__name:"top5",setup(I){const p=R();let r=A(()=>{var o;return((o=p.state.annularInfo)==null?void 0:o.top5Chart)||{}});O(()=>{console.log(r.value,"array"),t(r.value)}),z(r,(o,i)=>{console.log(o,"array"),r.value=o,t(r.value)});let n=[["rgba(255, 186, 51, 0.8)","#fff","rgba(255, 186, 51, 0.6)","#fff","rgba(255, 186, 51, 1)","#fff"],["rgba(80, 154, 211,0.8)","#fff","rgba(80, 154, 211,0.6)","#fff","rgba(80, 154, 211,1)","#fff"],["rgba(158, 107, 68,0.8)","#fff","rgba(158, 107, 68,0.6)","#fff","rgba(158, 107, 68,1)","#fff"],["rgba(0, 220, 85, 0.8)","#D0FFE2","rgba(0, 220, 85, 0.6)","#D0FFE2","rgba(0, 220, 85,1)","#D0FFE2"],["rgba(0, 255, 255,0.8)","#CBFFFA","rgba(0, 255, 255,0.6)","#CBFFFA","rgba(0, 255, 255,1)","#CBFFFA"]],s=Y({tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(o){const i=o[1];return`${i.name}:${i.value}人`}},grid:{top:"24%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{textStyle:{color:"#fff",fontSize:10},alignWithLabel:!0,boundaryGap:!0},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff",fontSize:9},formatter(o){return o=o.toString(),o>=1e11?o.substring(0,5)/10+"亿":o>=1e10?o.substring(0,4)/10+"亿":o>=1e9?o.substring(0,3)/10+"亿":o>=1e8?o.substring(0,2)/10+"亿":o>=1e7?o.substring(0,1)+"千万":o>=1e6?o.substring(0,1)+"百万":o>=1e5?o.substring(0,2)+"万":o>=1e4?o.substring(0,2)/10+"万":o>=1e3?o.substring(0,2)/10+"千":o}}},series:[]});function t(o){console.log(o.xAxis,"array"),s.xAxis.data=o.xAxis,s.series=[{name:"",type:"custom",renderItem:(i,c)=>{const h=c.coord([c.value(0),c.value(1)]);return console.log([c.value(0),c.value(1)]),{type:"group",children:[{type:"CubeLeft",shape:{api:c,xValue:c.value(0),yValue:c.value(1),x:h[0],y:h[1],xAxisPoint:c.coord([c.value(0),0])},style:{fill:new H(0,0,0,1,[{offset:0,color:n[i.dataIndexInside][0]},{offset:.96,color:n[i.dataIndexInside][1]}])}},{type:"CubeRight",shape:{api:c,xValue:c.value(0),yValue:c.value(1),x:h[0],y:h[1],xAxisPoint:c.coord([c.value(0),0])},style:{fill:new H(0,0,0,1,[{offset:0,color:n[i.dataIndexInside][2]},{offset:.98,color:n[i.dataIndexInside][3]}])}},{type:"CubeTop",shape:{api:c,xValue:c.value(0),yValue:c.value(1),x:h[0],y:h[1],xAxisPoint:c.coord([c.value(0),0])},style:{fill:new H(0,0,0,1,[{offset:0,color:n[i.dataIndexInside][4]},{offset:1,color:n[i.dataIndexInside][5]}])}}]}},data:o.yAxis},{name:"",type:"bar",label:{normal:{show:!0,position:"top",formatter:i=>`${i.value}`,fontSize:16,offset:[0,-15],color:function(i){console.log(i);var c=["#81D3F8","#FF6600","#FF004D"];return c[i.dataIndex]}}},itemStyle:{color:"transparent"},tooltip:{},data:o.yAxis}]}const l=M({shape:{x:0,y:0},buildPath:function(o,i){const c=i.xAxisPoint,h=[i.x,i.y],y=[i.x-B,i.y-j],x=[c[0]-B,c[1]-j],v=[c[0],c[1]];o.moveTo(h[0],h[1]).lineTo(y[0],y[1]).lineTo(x[0],x[1]).lineTo(v[0],v[1]).closePath()}}),_=M({shape:{x:0,y:0},buildPath:function(o,i){const c=i.xAxisPoint,h=[i.x,i.y],y=[c[0],c[1]],x=[c[0]+B,c[1]-j],v=[i.x+B,i.y-j];o.moveTo(h[0],h[1]).lineTo(y[0],y[1]).lineTo(x[0],x[1]).lineTo(v[0],v[1]).closePath()}}),a=M({shape:{x:0,y:0},buildPath:function(o,i){const c=[i.x,i.y],h=[i.x+B,i.y-j],y=[i.x,i.y-B],x=[i.x-B,i.y-j];o.moveTo(c[0],c[1]).lineTo(h[0],h[1]).lineTo(y[0],y[1]).lineTo(x[0],x[1]).closePath()}});return P("CubeLeft",l),P("CubeRight",_),P("CubeTop",a),(o,i)=>{const c=E,h=G;return u(),f("div",Es,[e("div",Fs,[b(c,null,{default:C(()=>i[0]||(i[0]=[F("话务量TOP5")])),_:1}),e("div",Rs,[i[1]||(i[1]=e("div",{class:"yName"},"话务量",-1)),b(h,{option:g(s)},null,8,["option"])])])])}}},Bs=T(Ds,[["__scopeId","data-v-67e2a1e1"]]),js={style:{display:"flex","flex-wrap":"wrap"}},ks={key:0},Os={class:"temBox"},Ns={__name:"home",setup(I){var n;const p=ce();console.log(p.query.config,"route");let r=(n=p.query)==null?void 0:n.config;return(s,t)=>(u(),f("div",js,[(u(),f(L,null,S(78,l=>e("div",{class:N(["item",{line:g(r)}]),key:l},[g(r)?(u(),f("span",ks,d(l),1)):D("",!0)],2)),64)),e("div",Os,[b(ee),b(te),b(se),b(Z),b(J),b(K),b(at),b(Q),b(w),b(ze),b(X,{type:"king"}),b(Ss),b(ke),b(Pt),b(vs),b(w),b(Bs),b(Q),b(X,{type:"sos"}),b(le),b(oe),b(ae)])]))}},Ws=T(Ns,[["__scopeId","data-v-2dd11cdd"]]);export{Ws as default};
