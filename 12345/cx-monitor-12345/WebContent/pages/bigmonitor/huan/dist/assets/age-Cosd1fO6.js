import{c as b,_ as J}from"./title-CoaCN9l8.js";import{a as V,b as U,c as z,d as k,e as P,f as j,g as K,h as q,i as W,j as X,k as Y,l as Z,m as Q,n as ee,o as te,p as se,q as ae,r as oe,s as ne,t as ie,u as le,v as _e,w as re,x as ce,y as ue}from"./yewu1-DVPbknuO.js";import{_ as O,a as B,b as de}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{u as L}from"./main-BY7WlIbH.js";import{c as M,r as D,e as I,D as $,k as H,l as T,q as y,z as e,F as C,B as G,I as m,u as c,m as R,n as me,H as pe,C as ge}from"./vue-CkV0NT5Y.js";const Ae=""+new URL("number_icon-CgXIeueR.png",import.meta.url).href,fe=""+new URL("man-BsByM6xy.png",import.meta.url).href,he=""+new URL("woman-yke9fiD3.png",import.meta.url).href,ve={class:"content_left"},Te={class:"left_L_box"},ye={class:"content_left_top"},Ee={class:"icon"},be=["src"],xe={class:"name"},Ne={class:"value"},Se={class:"content_left_bottom"},De={class:"left_L_box"},Re={class:"content_left_top"},Fe={class:"icon"},Be=["src"],Ce={class:"name"},Ge={class:"value"},Oe={class:"content_left_bottom"},Le={__name:"leftChart",setup(w){const x=L(),a=Object.assign({"/src/assets/A1/A1-bg1.png":ue,"/src/assets/A1/A1-bg2.png":ce,"/src/assets/A1/floor.png":re,"/src/assets/A1/floor1.png":_e,"/src/assets/A1/floorTag.png":le,"/src/assets/A1/floorTag1.png":ie,"/src/assets/A1/huahou.png":ne,"/src/assets/A1/huahou1.png":oe,"/src/assets/A1/huifang.png":ae,"/src/assets/A1/huifang1.png":se,"/src/assets/A1/jiehua.png":de,"/src/assets/A1/jiehua1.png":te,"/src/assets/A1/king.png":ee,"/src/assets/A1/king_avatar.png":Q,"/src/assets/A1/kongxian.png":Z,"/src/assets/A1/kongxian1.png":Y,"/src/assets/A1/shimang.png":X,"/src/assets/A1/shimang1.png":W,"/src/assets/A1/sos.png":q,"/src/assets/A1/sos_avatar.png":K,"/src/assets/A1/tonghua.png":j,"/src/assets/A1/tonghua1.png":P,"/src/assets/A1/wangluo.png":k,"/src/assets/A1/wangluo1.png":z,"/src/assets/A1/yewu.png":U,"/src/assets/A1/yewu1.png":V});let u=M(()=>{var s;return((s=x.state.annularInfo)==null?void 0:s.agentStateDate)||{}}),_=D(0),N=D([{icon:a["/src/assets/A1/jiehua.png"].default,name:"接话",value:"0"},{icon:a["/src/assets/A1/huifang.png"].default,name:"回访",value:"0"},{icon:a["/src/assets/A1/wangluo.png"].default,name:"网络",value:"0"},{icon:a["/src/assets/A1/yewu.png"].default,name:"业务",value:"0"}]),F=D([{icon:a["/src/assets/A1/huahou.png"].default,name:"话后",value:"0"},{icon:a["/src/assets/A1/tonghua.png"].default,name:"通话",value:"0"},{icon:a["/src/assets/A1/kongxian.png"].default,name:"空闲",value:"0"},{icon:a["/src/assets/A1/shimang.png"].default,name:"示忙",value:"0"}]);I(u,(s,i)=>{S(s),n(s)}),$(()=>{S(u.value),n(u.value)});function S(s){N.value=[{icon:a["/src/assets/A1/jiehua.png"].default,name:"接话",value:s.AGENT_CALL},{icon:a["/src/assets/A1/huifang.png"].default,name:"回访",value:s.AGNET_CALLBACK},{icon:a["/src/assets/A1/wangluo.png"].default,name:"网络",value:s.AGNET_NETWORK},{icon:a["/src/assets/A1/yewu.png"].default,name:"业务",value:s.AGNET_BUSINESS}],F.value=[{icon:a["/src/assets/A1/huahou.png"].default,name:"话后",value:s.agentRecordHandleCount},{icon:a["/src/assets/A1/tonghua.png"].default,name:"通话",value:s.agentCallCount},{icon:a["/src/assets/A1/kongxian.png"].default,name:"空闲",value:s.agentIdeaCount},{icon:a["/src/assets/A1/shimang.png"].default,name:"示忙",value:s.agentBusyCount}],_.value=s.allCount}let t=H({tooltip:{trigger:"axis"},legend:{data:["接话","回访","网络","业务"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#00FFFF","#0080FF","#00DF64","#FFDC00"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:4,interval:6},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]}),d=H({tooltip:{trigger:"axis"},legend:{data:["话后","空闲","通话","示忙"],textStyle:{color:"#fff"},width:300,itemGap:5,right:8,top:5},color:["#8572FF","#9AD30C","#EFAF10","#DC0055"],grid:{top:"34%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,axisLabel:{interval:6,textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"#0080FF",type:"solid",opacity:.3}},axisTick:{inside:!0,length:6,interval:4},data:[]},yAxis:{type:"value",alignTicks:!0,splitNumber:1.5,splitLine:{lineStyle:{color:"#00FFFF",type:"dashed",opacity:.2}},axisLabel:{textStyle:{color:"#fff"}}},series:[]});function n(s){var i,l,p,A,E,o,f,g,h,r;d.xAxis.data=(i=s.AGENT_STATE_DATA_TREND)==null?void 0:i.cols,d.series=[{name:"话后",type:"line",smooth:!0,symbolSize:0,data:(l=s.AGENT_STATE_DATA_TREND)==null?void 0:l.AGENT_RECORD_HANDLE_TREND},{name:"空闲",type:"line",symbolSize:0,smooth:!0,data:(p=s.AGENT_STATE_DATA_TREND)==null?void 0:p.AGENT_IDEA_TREND},{name:"通话",type:"line",symbolSize:0,smooth:!0,data:(A=s.AGENT_STATE_DATA_TREND)==null?void 0:A.AGENT_CALL_TREND},{name:"示忙",type:"line",symbolSize:0,smooth:!0,data:(E=s.AGENT_STATE_DATA_TREND)==null?void 0:E.AGENT_BUSY_TREND}],t.xAxis.data=(o=s.AGENT_STATE_DATA_TREND)==null?void 0:o.cols,t.series=[{name:"接话",type:"line",smooth:!0,symbolSize:0,data:(f=s.AGENT_STATE_DATA_TREND)==null?void 0:f.AGNET_TALK_TREND},{name:"回访",type:"line",symbolSize:0,smooth:!0,data:(g=s.AGENT_STATE_DATA_TREND)==null?void 0:g.AGNET_CALLBACK_TREND},{name:"网络",type:"line",symbolSize:0,smooth:!0,data:(h=s.AGENT_STATE_DATA_TREND)==null?void 0:h.AGNET_NETWORK_TREND},{name:"业务",type:"line",symbolSize:0,smooth:!0,data:(r=s.AGENT_STATE_DATA_TREND)==null?void 0:r.AGNET_BUSINESS_TREND}]}return(s,i)=>(y(),T("div",ve,[e("div",Te,[e("div",ye,[(y(!0),T(C,null,G(c(N),(l,p)=>(y(),T("div",{class:"item",key:p},[e("div",Ee,[e("img",{src:l.icon,alt:""},null,8,be)]),e("div",xe,m(l.name),1),e("div",Ne,m(l.value),1)]))),128))]),e("div",Se,[i[0]||(i[0]=e("div",{class:"yName"},"数量",-1)),R(B,{option:c(t)},null,8,["option"])])]),e("div",De,[e("div",Re,[(y(!0),T(C,null,G(c(F),(l,p)=>(y(),T("div",{class:"item",key:p},[e("div",Fe,[e("img",{src:l.icon,alt:""},null,8,Be)]),e("div",Ce,m(l.name),1),e("div",Ge,m(l.value),1)]))),128))]),e("div",Oe,[i[1]||(i[1]=e("div",{class:"yName"},"数量",-1)),R(B,{option:c(d)},null,8,["option"])])])]))}},Me=O(Le,[["__scopeId","data-v-1f37379c"]]),Ie={class:"templateBox3x"},$e={class:"box_a1"},we={class:"left"},He={class:"numberBox"},Je={class:"top"},Ve={class:"value"},Ue={class:"sex_number"},ze={class:"number"},ke={class:"sex_number"},Pe={class:"number"},je={__name:"left",setup(w){const x=L();let a=M(()=>{var u;return((u=x.state.annularInfo)==null?void 0:u.jobHeadDetails)||{}});return I(a,(u,_)=>{a.value=u}),$(()=>{a.value=a}),(u,_)=>(y(),T("div",Ie,[e("div",$e,[e("div",we,[e("div",He,[e("div",Je,[_[1]||(_[1]=e("img",{src:Ae,alt:""},null,-1)),e("div",null,[_[0]||(_[0]=e("div",{class:"label"},"坐席总人数",-1)),e("div",Ve,m(c(a).JOB_HEAD_COUNT||0),1)])]),e("div",Ue,[_[2]||(_[2]=e("div",{style:{display:"flex"}},[e("div",{class:"icon"},[e("img",{src:fe,alt:""})]),e("div",{class:"sex"},"男")],-1)),e("div",ze,m(c(b).calculateSum(c(a).JOB_MAN_HEAD_RATE,c(a).JOB_HEAD_COUNT)||0),1)]),e("div",ke,[_[3]||(_[3]=e("div",{style:{display:"flex"}},[e("div",{class:"icon"},[e("img",{src:he,alt:""})]),e("div",{class:"sex"},"女")],-1)),e("div",Pe,m(c(b).calculateSum(c(a).JOB_WOMAN_HEAD_RATE,c(a).JOB_HEAD_COUNT)||0),1)])]),R(Me)])])]))}},rt=O(je,[["__scopeId","data-v-b8cb7541"]]),Ke=""+new URL("pieBg-DTZhIXFa.png",import.meta.url).href,qe={class:"templateBox2x"},We={class:"box_c1"},Xe={style:{display:"flex","justify-content":"space-between"}},Ye={class:"chartBox1"},Ze={class:"infoNumber"},Qe={class:"dotBox"},et={class:"label"},tt={class:"value"},st={class:"rate"},at={__name:"age",setup(w){const x=L();let a=M(()=>{var t;return((t=x.state.annularInfo)==null?void 0:t.jobHeadDetails)||{}}),u=D({}),_=D([{name:"28岁以下",value:0,rate:0,itemStyle:{color:"#00FFFF"}},{name:"28岁至35岁",value:0,rate:0,itemStyle:{color:"#0055FF"}},{name:"35岁以上",value:0,rate:0,itemStyle:{color:"#00DC55"}}]);I(a,(t,d)=>{a.value=t,N(a.value),u.value=S(_.value,.7)});function N(t){_.value=[{name:"28岁以下",value:b.calculateSum(t.JOB_AVG_28_RATE,t.JOB_HEAD_COUNT)||0,rate:t.JOB_AVG_28_RATE,itemStyle:{color:"#00FFFF"}},{name:"28岁至35岁",value:b.calculateSum(t.JOB_AVG_28_35_RATE,t.JOB_HEAD_COUNT)||0,rate:t.JOB_AVG_28_35_RATE,itemStyle:{color:"#0055FF"}},{name:"35岁以上",value:t.JOB_HEAD_COUNT-(b.calculateSum(t.JOB_AVG_28_35_RATE,t.JOB_HEAD_COUNT)+b.calculateSum(t.JOB_AVG_28_RATE,t.JOB_HEAD_COUNT))||0,rate:t.JOB_AVG_35_RATE,itemStyle:{color:"#00DC55"}}]}$(()=>{N(a.value),u.value=S(_.value,.7)});function F(t,d,n,s,i,l){let p=(t+d)/2,A=t*Math.PI*2,E=d*Math.PI*2,o=p*Math.PI*2;n=!1,i=typeof i<"u"?i:1/3;let f=n?Math.sin(o)*.1:0,g=n?Math.cos(o)*.1:0,h=1;return{u:{min:-Math.PI,max:Math.PI*3,step:Math.PI/32},v:{min:0,max:Math.PI*2,step:Math.PI/20},x:function(r,v){return r<A?f+Math.cos(A)*(1+Math.cos(v)*i)*h:r>E?f+Math.cos(E)*(1+Math.cos(v)*i)*h:f+Math.cos(r)*(1+Math.cos(v)*i)*h},y:function(r,v){return r<A?g+Math.sin(A)*(1+Math.cos(v)*i)*h:r>E?g+Math.sin(E)*(1+Math.cos(v)*i)*h:g+Math.sin(r)*(1+Math.cos(v)*i)*h},z:function(r,v){return r<-Math.PI*.5?Math.sin(r):r>Math.PI*2.5?Math.sin(r)*l*.1:Math.sin(v)>0?1*l*.1:-1}}}function S(t,d){let n=[],s=0,i=0,l=0,p=[],A=(1-d)/(1+d);for(let o=0;o<t.length;o++){s+=t[o].value;let f={name:typeof t[o].name>"u"?`series${o}`:t[o].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:t[o],pieStatus:{selected:!1,hovered:!1,k:1/10}};if(typeof t[o].itemStyle<"u"){let g={};typeof t[o].itemStyle.color<"u"&&(g.color=t[o].itemStyle.color),typeof t[o].itemStyle.opacity<"u"&&(g.opacity=t[o].itemStyle.opacity),f.itemStyle=g}n.push(f)}for(let o=0;o<n.length;o++)l=i+n[o].pieData.value,n[o].pieData.startRatio=i/s,n[o].pieData.endRatio=l/s,n[o].parametricEquation=F(n[o].pieData.startRatio,n[o].pieData.endRatio,!1,!1,A,n[o].pieData.value),i=l,p.push(n[o].name);return{fontFamily:"Source Han Sans CN",xAxis3D:{},yAxis3D:{},zAxis3D:{},grid3D:{viewControl:{alpha:25,beta:0,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,autoRotate:!1,distance:90},left:"0%",width:"100%",show:!1,boxHeight:35},series:n}}return(t,d)=>(y(),T("div",qe,[e("div",We,[R(J,null,{default:me(()=>[pe("平均年龄 "+m(c(a).JOB_AVG_AGE||0)+"岁",1)]),_:1}),e("div",Xe,[e("div",Ye,[R(B,{option:c(u)},null,8,["option"]),d[0]||(d[0]=e("img",{src:Ke,alt:""},null,-1))]),e("div",Ze,[(y(!0),T(C,null,G(c(_),(n,s)=>(y(),T("div",{class:"infoNumberItem",key:s},[e("div",Qe,[e("div",{class:"dot",style:ge({"border-color":n.itemStyle.color})},null,4),e("div",et,m(n.name),1)]),e("div",tt,m(n.value),1),e("div",st,m(n.rate)+"%",1)]))),128))])])])]))}},ct=O(at,[["__scopeId","data-v-bcc15242"]]);export{rt as C,ct as a};
