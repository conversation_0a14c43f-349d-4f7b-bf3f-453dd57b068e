import{a as m,b as p}from"./main-BY7WlIbH.js";import{_ as v}from"./_plugin-vue_export-helper-CPVWdv3y.js";import{r as f,l as t,z as o,F as C,B as b,A as k,u as r,I as x,q as n}from"./vue-CkV0NT5Y.js";const B={class:"nav_box"},g={class:"nav_list"},h=["onClick"],A={__name:"nav",setup(y){const c=m();let l=[{name:"人员总览",code:"C1"},{name:"人员详情",code:"A1"},{name:"实时数据",code:"C2"},{name:"历史数据",code:"A2"}],i=f(c.meta.code);const u=p();function _(a){u.push({path:`/${a}`})}return(a,e)=>(n(),t("div",B,[e[0]||(e[0]=o("div",{class:"decoration top"},null,-1)),o("ul",g,[(n(!0),t(C,null,b(r(l),(s,d)=>(n(),t("li",{class:k(["nav_item",{active:r(i)===s.code}]),key:d,onClick:z=>_(s.code)},[o("span",null,x(s.name),1)],10,h))),128))]),e[1]||(e[1]=o("div",{class:"decoration bottom"},null,-1))]))}},R=v(A,[["__scopeId","data-v-5305ab1b"]]);export{R as _};
