<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传数据</title>
	<style>
		 #importDataList-body td:nth-child(even) {  
			 display: none;
   		 }  
	    #importDataList-body td:nth-child(odd) {  
	        background: White;  
	    } 
	    #autoAddZeroTr{display: none!important;}
	    .gray-bg{
	    	 background: White;  
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="custUpload" method="post" enctype="multipart/form-data">
				
				  <table class="table table-vzebra">
	                    <tbody>
		                     <tr>
		                        <td class="required">话机信息</td>
		                        <td>
		                        	 <input type="file" id="file" name="file" class="hidden" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/>
		                        	 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()"> 点击上传  </button>
				                 	 <a class="btn btn-sm btn-link" href="${ctxPath}/template/phoneTemp.xlsx" download="话机模版.xlsx" target="view_window">下载模板</a>
		                        </td>
		                     </tr>
		                     <tr>
								<td>文件名</td> 
								<td><input id="filePath" class="form-control input-sm" style="width:200px" type="text" readonly="readonly"></td>
							</tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
						<input class="btn btn-sm btn-primary" type="button" onclick="upload()" value="上传"/>
						<input class="btn btn-sm btn-default" type="button" id="backbut" onclick="parent.layer.closeAll();" value="关闭"/>
					</div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	 jQuery.namespace("custUpload");
	 $(document).ready(function() {
	        $(".hidden").change(function(){
	            var arrs=$(this).val().split('\\');
	            var filename=arrs[arrs.length-1];
	            $("#filePath").val(filename);
	        });
	    });
	function upload(){
		var formData = new FormData($("#custUpload")[0]);
		$.ajax({  
	          url: '${ctxPath}/servlet/data?action=phoneUpload',  
	          type: 'POST',  
	          data: formData,async: true,cache: false,contentType: false,processData: false,  
	          success: function (result) {
	        	  layer.closeAll('dialog');
	        	  if(result.state==1){
	        		  layer.msg("导入成功",{icon: 1,time:1200},function(){
	        			  popup.layerClose("#custUpload");
	  	        	  	  parent.phone.loadData();
	        		  })
	        	  	
	        	  }else{
	        		  layer.alert(result.msg,{icon: 5,offset:"20px"});
	        	  }
	          },error:function(){
	        	  layer.alert(result.msg);
	        	  layer.closeAll('dialog');
	          },beforeSend:function(){
	        	  	layer.msg('数据解析中...', {icon: 16 ,shade: 0.01,time:0,offset:'180px'});
	          } 
	     }); 
	}



	 

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>