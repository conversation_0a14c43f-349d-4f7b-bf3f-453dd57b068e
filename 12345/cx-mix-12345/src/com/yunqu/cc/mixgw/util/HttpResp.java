package com.yunqu.cc.mixgw.util;

public class HttpResp {
	
	private int code = 0;
	private String result;
	
	private String exception;
	
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	
	
	public boolean success(){
		return code==200;
	}
	public String getException() {
		return exception;
	}
	public void setException(String exception) {
		this.exception = exception;
	}
}
