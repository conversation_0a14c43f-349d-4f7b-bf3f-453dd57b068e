package com.yunqu.cc.mixgw.util;

import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 参数工具
 * <AUTHOR>
 *
 */
public class paramUtils {
	
	/**
	 * 请求参数转变成json
	 * @param request
	 * @return
	 */
	public static JSONObject parseRequest(HttpServletRequest request){
		JSONObject params = new JSONObject();
		Enumeration<String> names = request.getParameterNames();
		while(names.hasMoreElements()){
			String key = names.nextElement();
			String val = request.getParameter(key);
			params.put(key, val);
		}
		return params;
	}

	/**
	 * 字符串转化数字
	 * @param str
	 * @return
	 */
	public static int parseStrToInt(String str){
		if(StringUtils.isBlank(str)){
			return 0;
		}
		try{
			return Integer.parseInt(str);
		}catch(Exception e){
			return 0;
		}
	}
	
	/**
	 * 格式化日期小时
	 * @param date	yyyyMMdd
	 * @param hour	h
	 * @return yyyy-MM-dd HH:mm
	 */
	public static String formatDateHour(String date, String hour){
		return formatDate(date) + " " + formatHour(hour);
	}

	/**
	 * 格式化日期
	 * yyyyMMdd -> yyyy-MM-dd
	 */
	public static String formatDate(String date){
		if(StringUtils.isBlank(date)){
			return "";
		}
		return date.substring(0, 4)+"-"+date.substring(4,6)+"-"+date.substring(6);
	}

	/**
	 * 格式化小时
	 * h -> hh:mm
	 */
	public static String formatHour(String hour){
		if(StringUtils.isBlank(hour)){
			return "";
		}
		if(hour.length() == 1){
			return "0"+hour+":00";
		}
		return hour+":00";
	}
	

	/**
	 * 求百分比
	 * @param value1 被除数
	 * @param value2 除数
	 * @return
	 */
	public static String findPercent(String value1, String value2) {
        if (!StringUtils.isNumeric(value1) || !StringUtils.isNumeric(value2)
                || "0".equals(value1) || "0".equals(value2)) {
            return "0";
        }
        BigDecimal f1 = new BigDecimal((float) Integer.valueOf(value1) / Integer.valueOf(value2)).setScale(4, BigDecimal.ROUND_HALF_UP);
        BigDecimal f2 = f1.multiply(new BigDecimal(100));
        double f3 = f2.doubleValue();
        return f3 + "";
    }
	
	/**
	 * 求平均数
	 * @param value1 被除数
	 * @param value2 除数
	 * @return
	 */
	public static int findAvg(String value1, String value2) {
		if(StringUtils.isAnyBlank(value1, value2)){
			return 0;
		}
        if (!isNumeric(value1) || !isNumeric(value2)
                || "0".equals(value1) || "0".equals(value2)) {
            return 0;
        }
        BigDecimal b1 = new BigDecimal(value1);
        BigDecimal b2 = new BigDecimal(value2);
        BigDecimal b3 = b1.divide(b2,4,BigDecimal.ROUND_HALF_UP).setScale(0, BigDecimal.ROUND_HALF_UP);
        return b3.intValue();
    }
	
	/**
	 * 判断是否为数字
	 * @param str
	 * @return
	 */
	private static boolean isNumeric(String str) {
		 Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*\\.?[\\d]*$");  
		 return pattern.matcher(str).matches();
	}
	
}
