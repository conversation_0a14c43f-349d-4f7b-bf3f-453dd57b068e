package com.yunqu.robotgwsx.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.ListUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.robotgwsx.dao.CheckDataDao;
import com.yunqu.robotgwsx.util.Configure;
import com.yunqu.robotgwsx.util.Constants;
import com.yunqu.robotgwsx.util.DateUtil;
import com.yunqu.robotgwsx.data.DataCache;
import com.yunqu.robotgwsx.util.Proxy;

/**
 * 检查数据服务类
 * <AUTHOR>
 *
 */
public class CheckDataService {
	   Logger logger = LogEngine.getLogger(Constants.APP_NAME+"check");
	   private CheckDataDao checkDataDao = new CheckDataDao();

		// 检查是否所有数据都回传
		public void checkData(String startTime, String endTime) {
			String callBackUrl = Configure.getProperty("CALLBACKURL");
			if (callBackUrl == null && "".equals(callBackUrl))
				return;
			// cc_task_obj
			List<Map<String, Object>> data = checkDataDao.getCCTaskObj(startTime, endTime);
			// result
			List<Map<String, Object>> result = checkDataDao.getRobotGwResult(startTime, endTime);
			logger.info("[CheckDataService.checkData]data.size=" + data.size() + ",result.size=" + result.size());

			// 统计出未回传的objId
			List<Map<String, Object>> noobjIdList = new ArrayList<>();

			for (Map<String, Object> dataMap : data) {
				boolean flag = true;
				for (Map<String, Object> resultMap : result) {
					if (dataMap.get("OBJ_ID").equals(resultMap.get("OBJ_ID"))) {
						flag = false;
						break;
					}
				}
				if (flag&&DateUtil.getMax(dataMap.get("SUBMIT_TIME").toString(),"2021-12-13 59:59:59")) {
					noobjIdList.add(dataMap);
				}
			}
			logger.info("[CheckDataService.checkData]未回传的数据，总共" + noobjIdList.size());
			if (noobjIdList.size() <= 0) {
				return;
			}
			int count = noobjIdList.size() / 100 + 1;
			logger.info("[CheckDataService.checkData]数据分为" + count + "组");
			List<List<Map<String, Object>>> groupList = groupByData(noobjIdList, count);
			for (List<Map<String, Object>> objIdList : groupList) {
				// 拼接objId的in语句
				String objIds = join(objIdList, "OBJ_ID");
				List<Map<String, Object>> sessionDataList = checkDataDao.getSessionData(objIds);
				logger.info("[CheckDataService.checkData]sessionDataList有" + sessionDataList.size() + "条");
				JSONArray arrayList = join(objIdList, sessionDataList);
				logger.info("[CheckDataService.checkData]需要补充的数据有" + arrayList.size() + "条");

				Map<String, String> headers = new HashMap<String, String>();
				headers.put("Authorization",
						DataCache.USERINFO.get("atokenType") + DataCache.USERINFO.get("accessToken"));
				for (int k = 0; k < arrayList.size(); k++) {
					JSONArray arrayParams = arrayList.getJSONArray(k);
					logger.info("[CheckDataService.checkData]请求url=" + callBackUrl + ",data="
							+ arrayParams.toJSONString() + ",headers=" + JSON.toJSONString(headers));
					String res = "";
					try {
						res = Proxy.postJsonArray(callBackUrl, headers, arrayParams);
						checkDataDao.insertGWResult(arrayParams);
					} catch (Exception e) {
						logger.error("[CheckDataService.checkData]" + e.getMessage(), e);
					}
					logger.info("[CheckDataService.checkData]返回=" + res);
				}
			}
		}

	   public String join(List<Map<String,Object>> list,String key) {
		   String objIds = "";
		   if(list.size()<=0) {
			   return objIds;
		   }
		   for(Map<String,Object> objId:list) {
			   if("".equals(objIds)) {
				   objIds = "("+"'"+objId.get(key)+"'";
			   }else {
				   objIds +=","+"'"+objId.get(key)+"'";
			   }
		   }
		   if(!"".equals(objIds))
			   objIds +=")";
		   return objIds;
	   }
	   //按业务库分组
	   public Map<String,Object> groupByBusi(List<Map<String,Object>> objList){
		   Map<String,Object> result = new HashMap<String,Object>();
		   List<String> busiList = new ArrayList<String>(); //有几种busi库
		   for(Map<String,Object> map:objList) {
			   if(!busiList.contains(map.get("SCHEMA_ID"))) {
				   busiList.add(map.get("SCHEMA_ID").toString());
			   }
		   }
		   for(String busi:busiList) {
			   List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
			   result.put(busi, list);
		   }
		   //分组
		   for(String key:result.keySet()) {
			   List<Map<String,Object>> list = (List<Map<String, Object>>) result.get(key);
			   for(Map<String,Object> objMap:objList) {
				   if(key.equals(objMap.get("SCHEMA_ID"))) {
					   list.add(objMap);
				   }
			   }
			   result.put(key, list);
		   }
		   return result;
		   
	   }

		// 拼接回传数据
		public JSONArray join(List<Map<String, Object>> list, List<Map<String, Object>> visitData) {

			JSONArray array = new JSONArray();

			for (Map<String, Object> map : list) {
				for (Map<String, Object> temp : visitData) {
					if (map.get("OBJ_ID").equals(temp.get("OBJ_ID"))) {
						// 录音
						String recordFile = "";
						if (temp.get("RECORD_FILE") != null) {
							recordFile = temp.get("RECORD_FILE").toString();
						}
						map.put("RECORD_FILE", recordFile);
						// 通话时长
						String callTime = "";
						if (temp.get("CALL_TIME") != null) {
							callTime = temp.get("CALL_TIME").toString();
						}
						map.put("CALL_TIME", callTime);
						// 开始时间
						String startTime = "";
						if (temp.get("START_TIME") != null) {
							startTime = temp.get("START_TIME").toString();
						}
						map.put("START_TIME", startTime);
						// 结束时间
						String endTime = "";
						if (temp.get("END_TIME") != null) {
							endTime = temp.get("END_TIME").toString();
						}
						map.put("END_TIME", endTime);
						// 联系
						String relationFlag = "";
						if (temp.get("RELATION_FLAG") != null) {
							relationFlag = temp.get("RELATION_FLAG").toString();
						}
						map.put("RELATION_FLAG", relationFlag);
						// 解决
						String finishFlag = "";
						if (temp.get("FINISH_FLAG") != null) {
							finishFlag = temp.get("FINISH_FLAG").toString();
						}
						map.put("FINISH_FLAG", finishFlag);
						// 满意
						String satisfyFlag = "";
						if (temp.get("SATISFY_FLAG") != null) {
							satisfyFlag = temp.get("SATISFY_FLAG").toString();
						}
						map.put("SATISFY_FLAG", satisfyFlag);
						// 是否有效
						String validVisitFlag = "";
						if (temp.get("VALID_VISIT_FLAG") != null) {
							validVisitFlag = temp.get("VALID_VISIT_FLAG").toString();
						}
						map.put("VALID_VISIT_FLAG", validVisitFlag);
						// 无效原因
						String invalidReason = "";
						if (temp.get("INVALID_REASON") != null) {
							invalidReason = temp.get("INVALID_REASON").toString();
						}
						map.put("INVALID_REASON", invalidReason);
						// 不满意原因
						String dissatisfiedReasonkey = "";
						if (temp.get("DISSATISFIED_REASONKEY") != null) {
							dissatisfiedReasonkey = temp.get("DISSATISFIED_REASONKEY").toString();
						}
						map.put("DISSATISFIED_REASONKEY", dissatisfiedReasonkey);
						map.put("CALLRESULT", "1");
						String serialId = "";
						if(temp.get("SERIAL_ID") !=null) {
							serialId = temp.get("SERIAL_ID").toString();
						}
						map.put("SERIAL_ID", serialId);
						break;
					}
				}
			}
			for (Map<String, Object> map : list) {
				JSONArray arraytemp = new JSONArray();
				JSONObject object = new JSONObject();
				String orderId = map.get("F2").toString();
				String dispatchId = map.get("F3").toString();
				String orderNum = map.get("F4").toString();
				String calledNumber = map.get("TEL_NUM1").toString();
				
				String objId= map.get("OBJ_ID").toString();
				String taskId= map.get("TASK_ID").toString();
				object.put("orderId", orderId);
				object.put("dispatchId", dispatchId);
				object.put("orderNum", orderNum);
				object.put("calledNumber", calledNumber);
				object.put("submitTime", map.get("SUBMIT_TIME"));
				object.put("objId", objId);
				object.put("taskId", taskId);
				if (map.get("CALLRESULT") == null) {
					object.put("isValidVisit", "0");
					object.put("invalidReason", "0");
					object.put("callResult", "0");
					String serialId = com.yunqu.robotgwsx.util.StringUtils.uuid().replaceAll("-", "").toUpperCase();
					object.put("serialId", serialId);
				} else {
					String serialId = map.get("SERIAL_ID").toString();
					object.put("serialId", serialId);
					String isValidVisit = "0";
					String invalidReason = "2";
					// 是否有效
					if (map.get("VALID_VISIT_FLAG") != null) {
						isValidVisit = map.get("VALID_VISIT_FLAG").toString();
					}
					// 无效原因
					if (map.get("INVALID_REASON") != null) {
						invalidReason = map.get("INVALID_REASON").toString();
					}
					String isSolved = "";
					if (map.get("FINISH_FLAG") != null)
						isSolved = map.get("FINISH_FLAG").toString();
					String isSatisfaction = "";
					if (map.get("SATISFY_FLAG") != null)
						isSatisfaction = map.get("SATISFY_FLAG").toString();
					String isContact = "";
					if (map.get("RELATION_FLAG") != null)
						isContact = map.get("RELATION_FLAG").toString();
					String satisfactionReasonKey = "";
					if (map.get("DISSATISFIED_REASONKEY") != null)
						satisfactionReasonKey = map.get("DISSATISFIED_REASONKEY").toString().replaceAll("&", ",");
					String satisfactionReasonName = "";
					if (map.get("DISSATISFIED_REASONKEY") != null)
						satisfactionReasonName = map.get("DISSATISFIED_REASONKEY").toString().replaceAll("&", ",");
					 if(!"".equals(isSolved)&&!"".equals(isSatisfaction)&&!"".equals(isContact)) {
						  isValidVisit ="1";  
						  invalidReason = "";
					  }
					  //流程没有走完 算不配合
					  if("".equals(isSolved)||"".equals(isSatisfaction)||"".equals(isContact)) {
						  isValidVisit ="0";  
						  invalidReason = "1";
						  isSolved = "";
						  isContact ="";
						  isSatisfaction ="";
						  satisfactionReasonKey ="";
						  satisfactionReasonName ="";
					  }
					  String beginTime = map.get("START_TIME").toString();
					  String releaseTime = map.get("END_TIME").toString();
					  String recordFileName = map.get("RECORD_FILE").toString();
					  String callTime = map.get("CALL_TIME").toString();
					  String[] recordFileNames=recordFileName.split("[/]");
					  String recordPath=Configure.getProperty("SESSION_VOICEFILE_SAVEPATH")+recordFileName.replace(recordFileNames[recordFileNames.length-1], "");
					  String recordName=recordFileNames[recordFileNames.length-1]+"."+Configure.getProperty("VOICE_TYPE","mp3");
					  object.put("isValidVisit", isValidVisit);
					  object.put("invalidReason", invalidReason);
					  object.put("isSolved", isSolved);
					  object.put("isSatisfaction", isSatisfaction);
					  object.put("isContact", isContact);
					  object.put("satisfactionReasonKey", satisfactionReasonKey);
					  object.put("satisfactionReasonName", satisfactionReasonName);
					  object.put("visitCallTime", beginTime);
					  object.put("beginTime", beginTime);
					  object.put("releaseTime", releaseTime);
					  object.put("timeLength", callTime);
					  object.put("recordPath", recordPath);
					  object.put("recordName", recordName);
					  object.put("callResult", "1");

				}
				arraytemp.add(object);
                array.add(arraytemp);
			}

			return array;
		}
		public boolean truncateDataAndResult(String time) {
			return checkDataDao.truncateDataAndResult(time);
		}
		public static List<List<Map<String,Object>>> groupByData(List<Map<String,Object>> array,int count) {
			List<List<Map<String,Object>>> dataArray = new ArrayList();
			for(int i=0;i<count;i++) {
				List<Map<String,Object>> newArray = new ArrayList();
				int num =1;
				for(int j=i*100;j<array.size();j++) {
					num++;
					newArray.add(array.get(j));
					if(num>100)
						break;
				}
				dataArray.add(newArray);
			}
			return dataArray;
		}
}
