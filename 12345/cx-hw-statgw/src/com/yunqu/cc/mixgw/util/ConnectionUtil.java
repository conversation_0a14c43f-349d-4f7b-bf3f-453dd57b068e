package com.yunqu.cc.mixgw.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.log4j.Logger;

import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;

public class ConnectionUtil {
	// 以下代码将获取数据库连接操作封装为一个接口,可通过给定用户名和密码来连接数据库。
	private static Logger logger = CommonLogger.getLogger("conn");

	public static Connection GetConnection() {
		// 驱动类。
		String driver = "com.huawei.gauss.jdbc.ZenithDriver";
		// 数据库连接描述符。
		String sourceURL = "jdbc:zenith:@" + Constants.getAppConfigProperty("CX_HW_GAUSS_IP_PORT");

		String username = Constants.getAppConfigProperty("CX_HW_GAUSS_USER");
		String passwd = Constants.getAppConfigProperty("CX_HW_GAUSS_PASSWORD");
		Connection conn = null;
		try {
			// 加载数据库驱动。
			Class.forName(driver).newInstance();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
		try {
			// 创建数据库连接。
			logger.info("[sourceURL]"+sourceURL+"[username]"+username+"[passwd]"+passwd);
			conn = DriverManager.getConnection(sourceURL, username, passwd);
			logger.info("Connection succeed!");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}

		return conn;
	};

	public static void main(String[] args) {
		Connection conn = GetConnection();
		// 准备sql语句
		String sql = "select * from icd.tagentinfo";

		try {
			PreparedStatement st = conn.prepareStatement(sql);
			// 获取的结果集
			ResultSet rt = st.executeQuery();
			// 进行循环
			while (rt.next()) {
				System.out.println(
						rt.getString(1) + "/t" + rt.getString(2) + "/t" + rt.getString(3) + "/t" + rt.getString(4));
			}

		} catch (SQLException e) {
			e.printStackTrace();
			System.err.println(e.getMessage());
		} finally {
			if (conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}

}
