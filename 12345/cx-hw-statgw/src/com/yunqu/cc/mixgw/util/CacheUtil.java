package com.yunqu.cc.mixgw.util;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.cache.impl.RedisImpl;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.params.SetParams;

import java.util.*;
import java.util.Map.Entry;


public class CacheUtil {
	
public static EasyCache cache = CacheManager.getMemcache();
	
	
	/**
	 * 缓存数据
	 * @param key 
	 * @param value
	 * @param timer  缓存有效时间，单位：s
	 */
	public static void put(String key ,Object value,int timer){
		try {
			cache.put(key, value,timer);
		} catch (Exception ex) {
			CommonLogger.logger.info("CacheUtil.put("+key+","+value+") error,cause:"+ex.getMessage(),ex);
		}
	}
	
	public static void put(String key ,Object value){
		try {
			cache.put(key, value);
		} catch (Exception ex) {
			CommonLogger.logger.info("CacheUtil.put("+key+","+value+") error,cause:"+ex.getMessage(),ex);
		}
	}
	
	
	@SuppressWarnings("unchecked")
	public static <T> T get(String key){
		try {
			return (T)cache.get(key);
		} catch (Exception ex) {
			CommonLogger.logger.info("CacheUtil.get("+key+") error,cause:"+ex.getMessage(),ex);
			return null;
		}

	}
	public static void delete(String key){
		try {
			cache.delete(key);
		} catch (Exception ex) {
			CommonLogger.logger.info("CacheUtil.get("+key+") error,cause:"+ex.getMessage(),ex);
		}
	}
	
	/**
	 * 返回redis 某个key的值
	 * @param key
	 * @return
	 */
	public static  String  redisGet(String key){
		try {
			return oper("get", key, null, null);
		} catch (Exception e) {
		}
		return null;
	}
	
	/**
	 * 往redis hash类型数据中，增加某个值
	 * @param key
	 * @param value
	 * @return
	 */
	public static  String  hmset(String key,Map<String, String> map){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.hmset(key, map);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.hmset(key, map);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.hmset数据["+key+"]["+JSON.toJSONString(map)+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return "";
	}
	/**
	 * 操作redis的公共方法
	 * @param opType  操作redis的方法名，如 get、smembers等
	 * @param key     操作key
	 * @param args    操作参数，依次为
	 * @return
	 */
	private static <T> T oper(String opType,String key,String... args) throws Exception{
		T obj = null;
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				switch (opType) {
					case "get":
						obj = (T) jedis.get(key);
						break;
					case "smembers":
						obj = (T) jedis.smembers(key);
						break;
					case "lrange":
						obj = (T) jedis.lrange(key, 0L, 1000L);
					case "hgetAll":
						obj = (T) jedis.hgetAll(key);
						break;
					default:
						break;
				}
			}else{
				cluster = getJedisCluster();
				if(cluster!=null){
					switch (opType) {
						case "get":
							obj = (T) cluster.get(key);
							break;
						case "smembers":
							obj = (T) cluster.smembers(key);
							break;
						case "lrange":
							obj = (T) cluster.lrange(key, 0L, 1000L);
						case "hgetAll":
							obj = (T) cluster.hgetAll(key);
							break;
						default:
							break;
					}
				}
			}
			
		} catch (Exception e) {
			CommonLogger.logger.error("获取redis数据["+opType+"]["+key+"]["+args+"]异常:"+e.getMessage(),e);
			throw e;
		}finally{
			closeRedis(jedis,cluster);
		}
		return obj;
	}
	
	/**
	 * 往redis 某个key里设置值，不过期
	 * @param key
	 * @param value
	 * @return
	 */
	public static  String  set(String key,String value){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.set(key,value);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.set(key,value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.set数据["+key+"]["+value+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return value;
	}
	
	/**
	 * 返回redis set集合中某个key的所有值
	 * @param key
	 * @return
	 */
	public static  Set<String>  smembers(String key) throws Exception{
		return  oper("smembers", key);
	}
	
	/**
	 * 往redis set集合中的某个key里设置值
	 * @param key
	 * @param value
	 * @return
	 */
	public static long sadd(String key, String value) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if (jedis != null) {
				return jedis.sadd(key, value);
			}
			cluster = getJedisCluster();
			if (cluster != null) {
				return cluster.sadd(key, value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.sadd数据[" + key + "][" + value + "]异常:" + ex.getMessage(), ex);
		} finally {
			closeRedis(jedis, cluster);
		}
		return 0l;
	}
	
	/**
	 * 删除redis set集合中的某个key里设置值
	 * @param key
	 * @param value
	 * @return
	 */
	public static long srem(String key, String value) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if (jedis != null) {
				return jedis.srem(key, value);
			}
			cluster = getJedisCluster();
			if (cluster != null) {
				return cluster.srem(key, value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.sadd数据[" + key + "][" + value + "]异常:" + ex.getMessage(), ex);
		} finally {
			closeRedis(jedis, cluster);
		}
		return 0l;
	}
	
	/**
	 * 往redis hash类型数据中，增加某个值
	 * @param key
	 * @param value
	 * @return
	 */
	public static long hadd(String key, Map<String,String> value) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if (jedis != null) {
				return jedis.hset(key, value);
			}
			cluster = getJedisCluster();
			if (cluster != null) {
				return cluster.hset(key, value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.hset数据[" + key + "][" + value + "]异常:" + ex.getMessage(), ex);
		} finally {
			closeRedis(jedis, cluster);
		}
		return 0l;
	}
	
	public static  String  sget(String key,String value){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.getSet(key,value);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.getSet(key,value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.getSet数据["+key+"]["+value+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return null;
	}

	/**
	 * 往redis hash类型数据中，增加某个值
	 * @param key
	 * @param value
	 * @return
	 */
	public static  long  hadd(String key,String field,String value){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.hset(key, field, value);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.hset(key, field, value);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.hset数据["+key+"]["+value+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return 0l;
	}
	
	/**
	 * 从redis hash类型数据中，获取某个值
	 * @param key
	 * @param field
	 * @return
	 */
	public static  String  hget(String key,String field){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.hget(key, field);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.hget(key, field);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.hget数据["+key+"]["+field+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return null;
	}
	

	/**
	 * 从redis hash类型数据中，删除某个值
	 * @param key
	 * @param field
	 * @return
	 */
	public static  Long  hdel(String key,String field){
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				return jedis.hdel(key, field);
			}
			cluster = getJedisCluster();
			if(cluster!=null){
				return cluster.hdel(key, field);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.hdel数据["+key+"]["+field+"]异常:"+ex.getMessage(),ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return 0L;
	}
	
//	/**
//	 * 加锁
//	 * @param lockKey  锁的标识
//	 * @param requestId  加锁使用的值，可以随机设置一个值
//	 * @param expireTime 过期时间，秒
//	 * @return
//	 */
//	public synchronized static boolean lock(String lockKey,String requestId, int expireTime) {
//		Jedis jedis = getJedis();
//		JedisCluster cluster = null;
//		try {
//			String result = null;
//			
//				if(jedis!=null){
//					result = jedis.set(lockKey, requestId, new SetParams().nx().px(expireTime));
////					result = jedis.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
//				}
//				
//				if(StringUtils.isBlank(result)){
//					cluster = getJedisCluster();
//					if(cluster!=null){
//						result = cluster.set(lockKey, requestId, new SetParams().nx().ex(expireTime));
//					}
//				}
//			
//			if (result!=null && LOCK_SUCCESS.equals(result)){
//				return true;
//			}
//			return false;
//		} catch (Exception ex) {
//			CommonLogger.logger.error(ex,ex);
//		}finally{
//			closeRedis(jedis,cluster);
//		}
//		return false;
//	}
//
//	/**
//	 * 解锁
//	 * @param lockKey   锁的标识
//	 * @param requestId 锁的值
//	 * @return
//	 */
//	public synchronized static boolean unlock(String lockKey,String requestId){
//		
//		Jedis jedis = getJedis();
//		JedisCluster cluster = null;
//		try {
//			String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
//	        Object result = null;
//			
//				if(jedis!=null){
//					result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));
//				}
//				
//				if(result == null){
//					cluster = getJedisCluster();
//					if(cluster!=null){
//						result = cluster.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));
//					}
//				}
//			
//			
//			if (result!=null && "OK".equals(result)){
//				return true;
//			}
//			return false;
//		} catch (Exception ex) {
//			CommonLogger.logger.error(ex,ex);
//		}finally{
//			closeRedis(jedis,cluster);
//		}
//		return false;
//	}
	
	
	
	/**
	 * 加锁
	 * @param lockKey  锁的标识
	 * @param requestId  加锁使用的值，可以随机设置一个值
	 * @param expireTime 过期时间，秒
	 * @return
	 */
	public synchronized static boolean lock(String lockKey,String requestId, int expireTime) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			String result = null;
			if(jedis!=null){
				result = jedis.get(lockKey);
				if(StringUtils.isNotBlank(result)){
					return false;
				}
				jedis.setex(lockKey, expireTime, requestId);  //3.2#20220519-1 使用分布式锁方式，并发解锁速度太慢，无法满足获取工单编号类的场景；因此改为 synchronized + 某个key的锁来避免多台并发
				return true;
			}else{
				cluster = getJedisCluster();
				if(cluster!=null){
					result = cluster.get(lockKey);
					if(StringUtils.isNotBlank(result)){
						return false;
					}
					cluster.setex(lockKey, expireTime, requestId);
					return true;
				}
			}
				
			
			return false;
		} catch (Exception ex) {
			CommonLogger.logger.error(ex,ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return false;
	}

	/**
	 * 解锁
	 * @param lockKey   锁的标识
	 * @param requestId 锁的值
	 * @return
	 */
	public synchronized static boolean unlock(String lockKey,String requestId){
		
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if(jedis!=null){
				jedis.del(lockKey);
			}else{
				cluster = getJedisCluster();
				if(cluster!=null){
					cluster.del(lockKey);
				}
			}
			return false;
		} catch (Exception ex) {
			CommonLogger.logger.error(ex,ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return false;
	}

	private static Jedis getJedis() {
		//获取获取连接类型
		checkRedisConnType();
		//集群类型时，返回空
		if("cluster".equalsIgnoreCase(connType)){
			return null;
		}
		RedisImpl redis = null;
		try {
			redis = (RedisImpl)CacheManager.getRedis();
			Jedis jedis = redis.getJedis();
			return jedis;
		} catch (Exception e) {
			redis = null;
			CommonLogger.logger.error("获取redis连接异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	private static JedisCluster getJedisCluster() {
		//非集群类型时，返回空
		if(!"cluster".equalsIgnoreCase(connType)){
			return null;
		}
		
		RedisImpl redis = null;
		try {
			redis = (RedisImpl)CacheManager.getRedis();
			JedisCluster cluster = redis.getCluster();
			return cluster;
		} catch (Exception e) {
			redis = null;
			CommonLogger.logger.error("获取redis集群连接异常:"+e.getMessage(),e);
		}
		return null;
	}
	
	private static void closeRedis(Jedis jedis, JedisCluster cluster) {
		try {
			if(jedis!=null){
				jedis.close();
			}
			if(cluster!=null){
				//使用redis使用JedisCluster负载的时候，不用清理连接 ，jedisCluster内部使用了池化技术，
				//每次使用完毕都会自动释放Jedis如果手动关闭，反而会报错No reachable node in cluster（目前遇到该问题版本为redis6.4）
				//cluster.close();
			}
		} catch (Exception e) {
			CommonLogger.logger.error("关闭redis连接异常:"+e.getMessage(),e);
		}
		
	}
	
	
	public static void main(String[] args) throws Exception {
		
		// 配置Redis信息
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(100);
        config.setMaxIdle(50);
        config.setMaxWaitMillis(3000);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        
        // 集群
        JedisPool pool = new JedisPool(config,"172.16.68.175", 6379,30000,"yunqu168"); 
         
        Jedis jedis = pool.getResource();
        System.out.println(jedis);
        
        
        jedis.hset("ibac_201", "routeAddr", "*************");
        System.out.println(jedis.hget("ibac_201", "routeAddr"));
        
        System.out.println(jedis.hgetAll("ibac_201"));
        
        System.out.println("加锁前："+jedis.get("test"));
        
        String result = jedis.set("test", "2", new SetParams().nx().ex(3));
        System.out.println("加锁结果："+result);
        
        System.out.println("加锁后："+jedis.get("test"));
        
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object o = jedis.eval(script, Collections.singletonList("test"), Collections.singletonList("2"));
		System.out.println("解锁："+o);
		System.out.println("解锁后："+jedis.get("test"));
        
        jedis.close();
        pool.destroy();
	}
	
	/**
	 * 获取redis里的 hash 数据结构
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static Map<String, String> hgetAll(String key) {
		try {
			return  oper("hgetAll", key);
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.keys数据[" + key + "]异常:" + ex.getMessage(), ex);
		}
		return  null;
	}
	
	/**
	 * 获取redis里的 list 数据结构
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static  List<String>  lrange(String key) throws Exception {
		return  oper("lrange", key);
	}

	/**
	 * 按某个key获取redis里的数据，兼容 hash、set、list三种数据结构
	 * @param key
	 * @return
	 */
	public static String getCacheObj(String key) {
		try {
			try {
				String str = redisGet(key);
				if(StringUtils.isNotBlank(str)){
					return str;
				}
			} catch (Exception e) {
				CommonLogger.logger.error("获取redis.get-[" + key + "]异常，继续按hgetAll获取:" + e.getMessage(), e);
			}
			
			
			Map<String, String> map = hgetAll(key);
			if (map != null && map.size()>0) {  //若key不存在，会返回空列表
				return JSON.toJSONString(map);
			}
		} catch (Exception e) {
			CommonLogger.logger.error("获取redis.hgetAll-[" + key + "]异常，继续按smembers获取:" + e.getMessage(), e);
			try {
				Set<String> set = smembers(key);
				if (set != null && set.size()>0) {
					return JSON.toJSONString(set);
				}
			} catch (Exception e1) {
				try {
					CommonLogger.logger.error("获取redis.smembers-[" + key + "]异常，继续按lrange获取:" + e1.getMessage(),
							e1);
					List<String> list = lrange(key);
					if (list != null && list.size()>0) {
						return JSON.toJSONString(list);
					}
				} catch (Exception e2) {
					CommonLogger.logger.error("获取redis.lrange-[" + key + "]异常，不再获取:" + e2.getMessage(), e2);
				}
			}
		}
		return null;
	}

	public static Map<String, String> hscan(String key) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		ScanResult<Entry<byte[], byte[]>> scanResult = null;
		
		Map<String, String>  map = new HashMap<String,String>();
		
		try {
			key = "CC-REMOTE-M-8002@cc";
			ScanParams params = new ScanParams();
//			params.match("CC-REMOTE-M-8002@cc");
//			params.count(2);
			params.match("CC-REMOTE-M-*");
			
			
			String cursor = ScanParams.SCAN_POINTER_START;
			
			if(jedis!=null){
				
//				for(int i=0;i<=50000;i++){
//					jedis.sadd("SETTEST", "SETTEST-value-"+i);
//				}
				long start = System.currentTimeMillis();
				Set<String> smembers = jedis.smembers("SETTEST");
				for(String t : smembers){
					
				}
				long end = System.currentTimeMillis();
				
				
				ScanResult<byte[]> result = jedis.scan("0".getBytes(),params);
				
				Set<String> keys = jedis.keys("MT*");
				
				ScanResult<byte[]> scan = jedis.scan("0".getBytes(),params);
				 cursor = scan.getCursor();// 返回0 说明遍历完成
				 for(int i =0;i<100;i++){
					 List<byte[]> list = scan.getResult();
					 cursor = scan.getCursor();
					 if("0".equals(cursor)){
						 break;
					 }
				 }
		           
				
				
				scanResult = jedis.hscan(key.getBytes(), "0".getBytes(), params);
			}else{
				cluster = getJedisCluster();
				if(cluster!=null){
					scanResult = cluster.hscan(key.getBytes(), "0".getBytes(), params);
				}
			}
			if(scanResult!=null){
				List<Entry<byte[], byte[]>> list = scanResult.getResult();
				for(Entry<byte[], byte[]> entry : list){
					String key2 = String.valueOf(entry.getKey());
					String value2 = String.valueOf(entry.getValue());
					
				}
				cursor = scanResult.getCursor();
				if("0".equals(cursor)){
					
				}
			}
			
		} catch (Exception ex) {
			CommonLogger.logger.error(ex,ex);
		}finally{
			closeRedis(jedis,cluster);
		}
		return map;
	}
	
	/**
	 * 获取redis里的 所有key
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static Set<String> keys(String key) {
		Jedis jedis = getJedis();
		JedisCluster cluster = null;
		try {
			if (jedis != null) {
				return jedis.keys(key);
			}
			cluster = getJedisCluster();
			if (cluster != null) {
				return cluster.keys(key);
			}
		} catch (Exception ex) {
			CommonLogger.logger.error("操作redis.keys数据[" + key + "]异常:" + ex.getMessage(), ex);
		} finally {
			closeRedis(jedis, cluster);
		}
		return null;
	}
	
	
	/**
	 * 获取redis的连接方式，在控制台里配置, 每隔100s会自动从console配置里重新获取
	 * @return   single - 单体    cluster -集群  sentinel-哨兵模式
	 */
	private static String checkRedisConnType(){
		
		//获取redis配置信息少于60s时，不获取数据
		if(System.currentTimeMillis()-connTypeCheckTime < 100000){
			return connType;
		}
		
		String redisConf = ServerContext.getProperties("redisConf", "");
		JSONObject params = new JSONObject();
		if (StringUtils.isNotBlank(redisConf)) {
			params = JSONObject.parseObject(redisConf);
			connType =  params.getString("connType");  //cluster  single  sentinel
		} 
		if(StringUtils.isBlank(connType)){
			connType = "single";
		}
		connTypeCheckTime = System.currentTimeMillis();
		
		return connType;
	}
	
	private static String connType = "single";
	private static long connTypeCheckTime = 0;
	
	
}
