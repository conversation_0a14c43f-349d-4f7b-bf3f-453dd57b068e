<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单类型编辑</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform"  method="post"  autocomplete="off" data-mars-prefix="areaCode.">
				<input type="hidden" name="pId" value="${param.id }">
				<input type="hidden" name="pName" value="${param.title }">
				<table class="table table-edit table-vzebra mt-10">
                    <tbody>
	                     <tr>
		                        <td class="required" width="30px">地区编码</td>
		                        <td><input type="text" name="areaCode.AREA_CODE" data-rules="required" class="form-control input-sm"></td>
	                     </tr>
	                     <tr>
		                        <td class="required" width="30px">地区名称</td>
		                        <td><input type="text" name="areaCode.AREA_NAME" data-rules="required" class="form-control input-sm"></td>
	                     </tr>
                    </tbody>
	            </table>
				<div class="layer-foot text-c">
			   		<button class="btn btn-sm btn-primary"  type="button" onclick="areaEdit.ajaxSubmitForm()">保存</button>
			   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				</div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("areaEdit");
	
	areaEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 areaEdit.insertData(); 
		 };
	}
	areaEdit.insertData = function() {
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/areaCode?action=add",data,function(result) { 
			if(result.state == 1){
				loadData();
				layer.closeAll();
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		  }
		);
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>