package com.yunqu.yc.agent.msg.event;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.EventType;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.msg.RequestData;
import com.yunqu.yc.agent.msg.model.RequestDataV1;

/**
 * 处理登录请求
 * <AUTHOR>
 * 请求信息包括：
 *
 */
public class ClearForceCall extends  AbstractEvent {


	@Override
	public JSONObject doEvent(RequestData requestData) {
		
		RequestDataV1 _requestData = (RequestDataV1)requestData;
		
		
		JSONObject data = _requestData.getData();
		//客户端请求数据
		String agentId = data.getString("agentId");
		String called = data.getString("called");
		//ICCS LOGO 接口所需数据
		JSONObject msgObject = new JSONObject();
		msgObject.put("called", called);
		return this.createMessageObject(_requestData, EventType.cmdClearForceCall , msgObject);
		
	}

	@Override
	public String checkRequestParam(RequestData requestData) {
		// TODO Auto-generated method stub
		return null;
	}

}
