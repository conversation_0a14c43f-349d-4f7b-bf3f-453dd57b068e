package com.yunqu.yc.agent.msg.iccs;

import java.sql.SQLException;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.agent.listener.GlobalContextListener;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.ICCSLogger;
import com.yunqu.yc.agent.monitor.DBMonitor;
import com.yunqu.yc.agent.monitor.RedisMonitor;
import com.yunqu.yc.agent.util.CacheUtil;


/**
 * 磐石服务节点状态管理。
 * 
 * <AUTHOR>
 *
 */
public class ServiceNode implements Runnable{
	
	//保存当前磐石域可用过的状态，磐石DS，定期把磐石域的状态缓存到redis。mars读取本地redis的磐石域的状态。
	private static final String DOMAIN_REDIS_PREFIX = "NGSP_STATE_";
	
	//最后加载磐石配置信息时间，每5分钟从数据库中重新加载磐石数据。
	long lastInitTimer =  System.currentTimeMillis();
	
	//A节点,节点信息 及 备份节点在MGR中进行配置。
	private String petraIdA;
	//B节点
	private String petraIdB;
	
	//A节点：域名
	private String domainA;
	//B节点：域名
	private String domainB;
	
	//A节点可用状态
	private boolean stateA = true;
	
	//B节点缺省为不可用状态，需要就行检测。
	private boolean stateB = false;
	
	private static Map<String,ServiceNode> petraStateMap = new ConcurrentHashMap<String,ServiceNode>();
	
	/**
	 * 获得当前服务的磐石节点，这个根据redis的缓存值进行判断： NGSP_STATE_ngsp-dev2 {"state":"Available","Content":"","timestamp":"20220719223930923"}
	 * @param petraId
	 * @return
	 */
	public String getServerPetraNode(){
		String _petraId =  this.getActivePetraId();
		if(StringUtils.isNotBlank(_petraId)) return _petraId;
		//如果找不到可用的节点缺省返回当前节点
		return petraIdA;
	}
	
	/**
	 * 获得磐石服务节点对象
	 * @param petraId  磐石节点ID
	 * @return
	 */
	public static ServiceNode getPetraNode(String petraId){
		ServiceNode  node = petraStateMap.get(petraId);
		if(node == null){
			node = new ServiceNode(petraId);
			try {
				node.init();
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex,ex);
				ICCSLogger.getLogger().error(ex,ex);
			}
			Thread thread = new Thread(node);
			thread.start();
			petraStateMap.put(petraId, node);
		}
		return node;
	}
	
	/**
	 * 获得备份节点中活动的节点
	 * @return
	 */
	public String getActiveBackupNode(){
		if(this.stateB) return this.domainB;
		return "";
	}
	
	
	public ServiceNode(String petraId){
		this.petraIdA = petraId;
		if(StringUtils.isBlank(petraId)){
			try {
				Integer.parseInt(petraId);
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex,ex);
			}
		}
	}
	
	
	private long lastStateATimestamp = 0;
	
	/**
	 * 获取当前活动节点
	 * @return
	 */
	public String getActivePetraId(){
		if("A".equalsIgnoreCase(Constants.getConfigPetraCenter())) return this.petraIdA;
		if("B".equalsIgnoreCase(Constants.getConfigPetraCenter())) return this.petraIdB;
		if(stateA) return this.petraIdA;
		if(stateB) return this.petraIdB;
		return this.petraIdA;
	}
	
	/**
	 * 获得当前备份可用节点
	 * @return
	 */
	public String getBackupActivePetraId(){
		if(stateB) return this.petraIdB;
		return "";
	}
	
	
	/**
	 * 获取当前活动的磐石节点
	 * @return
	 */
	public String getActivePetraDomain(){
		
		//如果没有配置双中心，则直接返回是A中心。
		if(StringUtils.isBlank(domainB)) return this.domainA;
		
		//这个根据配置当前活动的节点，主要用户测试双中心场景，配置项为：ACTIVE_CENTER  ，0:系统指定,A:A中心 ,B:B中心,C:C中心  ，填0时候，mars自动管理双中心，否则返回指定的中心。
		if("A".equalsIgnoreCase(Constants.getConfigPetraCenter())) return this.domainA;

		if("B".equalsIgnoreCase(Constants.getConfigPetraCenter())) return this.domainB;
		//判断当前中心的状态，stateA  stateB  stateC 保存当前中心的状态，按优先级别返回。
		
		//若A中心状态正常则返回A
		if(stateA) return this.domainA;
		else{
			//如果A中心的最后不可用的状态时间，少于配置时间，这不进行迁移。
			if((System.currentTimeMillis() - this.lastStateATimestamp) > Constants.getMaxBlockedTime()){  //缺省30秒。
				if(stateB) return this.domainB;
			}
		}
		if(stateB) return this.domainB;
		
		return this.domainA;
	}
	
	//初始磐石节点信息
	private void init() throws SQLException{
		
		ICCSLogger.getLogger().info("初始磐石["+this.petraIdA+"]双中心节点信息...");
		CcbarLogger.getLogger().info("初始磐石["+this.petraIdA+"]双中心节点信息...");
		String sql = "select * from CC_PETRA_RES  where PETRA_ID = ?";
		JSONObject row = QueryFactory.getQuickQuery().queryForRow(sql, new Object[]{this.petraIdA},new JSONMapperImpl());
		//获得当前企业配置的磐石节点A，并获取A节点配置的备份节点B和备份节点C。
		this.domainA = row.getString("PETRA_NAME");
		//this.domainStateCheck(this.domainA, "A");
		petraIdB = row.getString("BACKUP_B");
		//如果B节点不为空，这获取磐石平台B的节点名称
		if(StringUtils.isNotBlank(petraIdB)){
			sql = "select * from CC_PETRA_RES  where PETRA_ID = ?";
			row = QueryFactory.getQuickQuery().queryForRow(sql, new Object[]{this.petraIdB},new JSONMapperImpl());
			this.domainB = row.getString("PETRA_NAME");
			//this.domainStateCheck(this.domainB, "B");
		}
		CcbarLogger.getLogger().info("磐石["+this.petraIdA+"]双中心节点信息,A中心："+domainA+",B中心："+domainB);
		ICCSLogger.getLogger().info("磐石["+this.petraIdA+"]双中心节点信息,A中心："+domainA+",B中心："+domainB);
	}
	/**
	 NGSP_STATE_ngsp-dev2 {"state":"Available","Content":"","timestamp":"20220719223930923"}
	 NGSP_STATE_ngsp-dev2 {"state":"Unavailable","Content":"ACD Count=0;","timestamp":"20220719214941696"}
	 NGSP_STATE_ngsp-dev2 {"state":"Unavailable","Content":"APPRule:ivr,ivr_bak, unavailable;","timestamp":"20220719223445912"}
	 */
	
	private void domainStateCheck(String domainName,String nodeId){
		if(!RedisMonitor.isOk()) return;
		if(StringUtils.isBlank(domainName)) return;
		//如果只是单中心的情况，不做中心的检查。
		if(StringUtils.isBlank(petraIdB)) return;
		String domainStateString  = CacheUtil.petraGet(DOMAIN_REDIS_PREFIX+domainName);
		//如果获取不到状态信息，则把该节点状态置成false，不可用。
		if(StringUtils.isBlank(domainStateString)){
			ICCSLogger.getLogger().warn("查找不到["+DOMAIN_REDIS_PREFIX+domainName+"]节点在redis的的状态信息!");
			CcbarLogger.getLogger().warn("查找不到["+DOMAIN_REDIS_PREFIX+domainName+"]节点在redis的状态信息!");
			if("A".equalsIgnoreCase(nodeId)){
				TCPClient tcpClinet =  MsgHandler.getClient(this.petraIdA);
				if(tcpClinet.isOK()) {
					ICCSLogger.getLogger().warn("查找不到["+DOMAIN_REDIS_PREFIX+domainName+"]节点在redis的的状态信息,但ICCS状态链接正常，设置当前节点的可用状态为true，请检查DS进程是否退出!");
					CcbarLogger.getLogger().warn("查找不到["+DOMAIN_REDIS_PREFIX+domainName+"]节点在redis的的状态信息,但ICCS状态链接正常，设置当前节点的可用状态为true，请检查DS进程是否退出!");
					this.setState(nodeId, true);
					return;
				}
			}
			this.setState(nodeId, false);
		}else{
			JSONObject domainAObj = JSONObject.parseObject(domainStateString);
			if(domainAObj!=null){
				String state = domainAObj.getString("state");
				//如果磐石的监控状态为可用状态，则代表改磐石可用。 取值：Available  or Upgrading  or Unavailable ,Upgrading:为升级状态。  Warning 告警状态。
				boolean bl = "Available".equalsIgnoreCase(state) || "Upgrading".equalsIgnoreCase(state) || "Warning".equalsIgnoreCase(state);
				this.setState(nodeId, bl);
				if(!bl){
					ICCSLogger.getLogger().warn("["+DOMAIN_REDIS_PREFIX+domainName+"]节点状态不可用->"+domainStateString);
					CcbarLogger.getLogger().warn("["+DOMAIN_REDIS_PREFIX+domainName+"]节点状态不可用->"+domainStateString);
				}
			}else{
				ICCSLogger.getLogger().warn("["+DOMAIN_REDIS_PREFIX+domainName+"]节点状态JSON格式错误->"+domainStateString);
				CcbarLogger.getLogger().warn("["+DOMAIN_REDIS_PREFIX+domainName+"]节点状态JSON格式错误->"+domainStateString);
				this.setState(nodeId, false);
			}
		}
	}
	
	private void setState(String nodeId,boolean state){
		if("A".equalsIgnoreCase(nodeId)){
			if(stateA!=state) this.lastStateATimestamp = System.currentTimeMillis();  //记录状态变化的时间，用于A中心不可用的时候，超过指定的异常时间后进行切换。
			stateA = state;
		}
		if("B".equalsIgnoreCase(nodeId)) {
			stateB = state;
		}
	}
	
	@Override
	public void run() {
		
		while(GlobalContextListener.runState){
			try {
				
				long _timer = System.currentTimeMillis() - lastInitTimer;
				//每5分钟初始化一次磐石的配置信息，检查磐石的配置是否有改变。
				if(_timer > 600*1000){
					this.lastInitTimer = System.currentTimeMillis();
					try {
						if(DBMonitor.isOk())  this.init();
					} catch (Exception ex) {
						CcbarLogger.getLogger().error(ex,ex);
						ICCSLogger.getLogger().error(ex,ex);
					}
				}
				
				this.domainStateCheck(this.domainB,"B");
				this.domainStateCheck(this.domainA,"A");
				
				if(StringUtils.isBlank(this.domainB)){
					if(ServerContext.isDebug()) CcbarLogger.getLogger().info("磐石节点状态->A["+this.domainA+"]="+stateA);
				}else{
					if(ServerContext.isDebug()) CcbarLogger.getLogger().info("磐石双中心节点[当前在用节点："+this.getActivePetraDomain()+"]->A中心["+this.domainA+"]="+stateA+",B中心["+this.domainB+"]="+stateB);
				}
				//告知磐石平台，目前mars在用的节点是那个。 
				if("0".equals(Constants.getConfigPetraCenter())){  //0:系统指定,A:A中心 ,B:B中心,C:C中心  
					CacheUtil.petraPut("MARS_ACTIVE_NGSP", this.getActivePetraDomain(),30);
				}
				
			} catch (Exception ex) {
				ICCSLogger.getLogger().error(ex,ex);
				CcbarLogger.getLogger().error(ex,ex);
			}finally{
				try {
					Thread.sleep(10*1000);
				} catch (Exception ex2) {
				}
			}
		}
		
	}
}
