package com.yunqu.yc.agent.util;

/**
 * 
 * <AUTHOR>
 *
 */
public class PhoneCipher {

	private static int MUL_SEED =  9876;
	private static int SUB_SEED =  6789;
	
	/**
	 * 对电话号码进行数学运算进行加密处理，返回格式：  00 + (9+phone) * MUL_SEED - SUB_SEED;
	 * @param phone
	 * @return
	 */
	
	public static String encode(String phone){
		
		String _phone = "9" + phone;
		long num =  0;
		try {
			num = Long.parseLong(_phone);
			num = num  * MUL_SEED;
			num = num -  SUB_SEED;
		} catch (Exception ex) {
			return phone;
		}
		return "00"+num;
	}
	
	
	/**
	 * 对号码进行解码，如果非00开头，则直接返回。
	 * @param phone
	 * @return
	 */
	public static String decode(String phone){
		if(!phone.startsWith("00")) return phone;
		String _phone  = phone.replaceFirst("00", "");
		long num =  0;
		try {
			num = Long.parseLong(_phone);
			num = num +  SUB_SEED;
			num = num  / MUL_SEED;
		} catch (Exception ex) {
			return phone;
		}
		return (num+"").replaceFirst("9","");
	}
	
	public static void main(String[] args) {
		String phone = "13380278728";
		String data = PhoneCipher.encode(phone);
		String _phone = PhoneCipher.decode(data);
		System.out.println("phone="+phone);
		System.out.println("data="+data);
		System.out.println("_phone="+_phone);
	}
}
