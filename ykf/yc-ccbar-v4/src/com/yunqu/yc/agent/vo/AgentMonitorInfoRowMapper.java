package com.yunqu.yc.agent.vo;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.easitline.common.db.EasyRowMapper;
import com.yunqu.yc.agent.log.CcbarLogger;

public class AgentMonitorInfoRowMapper implements EasyRowMapper<AgentMonitorInfo>{

	@SuppressWarnings("unchecked")
	@Override
	public AgentMonitorInfo mapRow(ResultSet rs, int rownum) {
		AgentMonitorInfo agent = new AgentMonitorInfo();
		try {
			agent.setAgentId(rs.getString("AGENT_ID"));
			agent.setAgentName(rs.getString("AGENT_NAME"));
			agent.setAgentState(rs.getString("AGENT_STATE"));
			agent.setWorkModel(rs.getString("WORK_MODE"));
			agent.setStateTime(rs.getString("STATE_TIME"));
		} catch (SQLException ex) {
			CcbarLogger.getLogger().error(ex,ex);
		}
		return agent;
	}

}
