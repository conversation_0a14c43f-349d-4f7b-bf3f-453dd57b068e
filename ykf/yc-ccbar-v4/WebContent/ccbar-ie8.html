<html>

<head>
    <meta charset="UTF-8">
    <title>ccbar for ie8</title>
    <!-- 页面静态资源 客户平台引入则需要在地址前面加上服务器地址,即提供的接入地址 -->
    <link rel="stylesheet" type="text/css" href="/yc-ccbar/ccbar/ccbar-sdk.css">
    <script src="http://cdn.bootcss.com/jquery/1.4.4/jquery.min.js"></script>
    <script src='/yc-ccbar/ccbar/ccbar.js'></script>
    <script src='/yc-ccbar/ccbar/md5.js'></script>
    <!-- 提供给低版本jquery的js,如使用jquery1.9以上,则引入ccbar_sdk.js -->
    <script src="/yc-ccbar/ccbar/ccbar_sdk.older.js"></script>

</head>

<body>
    <div class="demo">
        接入地址: <input id="host" value="" type="text" autocomplete="off" class="layui-input">  
        企业ID: <input id="entId" value="" type="text" autocomplete="off" class="layui-input">  
        登录秘钥: <input id="loginKey" value="" type="text" autocomplete="off" class="layui-input">  
        产品ID: <input id="productId" value="" type="text" autocomplete="off" class="layui-input"> 
        ccbar类型 <select id="layoutType" autocomplete="off" class="layui-input">
                                  <option value="true" selected="">基本功能默认界面</option>
                                  <option value="advanced">带进阶功能默认界面</option>
                                  <option value="false">自定义界面</option>
                                </select>
        <p>
            <button onclick="iniSDK()">初始化</button>
        </p>
    </div>
    <div id="ccbar"></div>
    <script>


     function GetQueryString(name)
    {
         var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
         var r = window.location.search.substr(1).match(reg);
         if(r!=null)return  unescape(r[2]); return null;
    }

    // sdk初始化
    function iniSDK(){
        var host = $("#host").val();
        var entId = $("#entId").val();
        var loginKey = $("#loginKey").val();
        var productId = $("#productId").val();
        var type = $("#layoutType").val();

        ccbarsdk.init(host,entId,productId,loginKey,function(){
          ccbar_plugin.init('','',type);
        });
    }

    //测试用方法,通过连接
    $(function(){
      var host = location.origin;//云呼平台接入地址，由云呼平台提供
      var entId = GetQueryString('entId');//企业ID，由云呼平台提供
      var loginKey = GetQueryString('loginKey');//登录密钥，32位长度，由云呼平台提供
      var productId = GetQueryString('productId');  //企业订购的产品，由云呼平台提供

      $("#host").val(host);
      $("#entId").val(entId);
      $("#loginKey").val(loginKey);
      $("#productId").val(productId);
    })

    /*$(function() {
        var host = ''; //云呼平台接入地址，由云呼平台提供
        var entId = ''; //企业ID，由云呼平台提供
        var loginKey = ''; //登陆密钥，32位长度，由云呼平台提供
        var productId = '002'; //企业订购的产品，由云呼平台提供
        var type = 'advanced'; //为空或者true时为默认基础功能界面,advanced为带更多功能的界面,false时只进行初始化,界面自行开发
        //进行ccbar初始化,必填参数为云呼平台接入地址,企业ID，由云呼平台提供,登陆密钥,订购产品,可选参数为是否需要ccbar默认界面,默认为true,false表示不使用默认的界面,需要自己实现,实现方式参见demo和代码说明
        ccbarsdk.init(host, entId, productId, loginKey, function() {
            ccbar_plugin.init('', '', type);
        });
    });*/
    </script>
</body>

</html>