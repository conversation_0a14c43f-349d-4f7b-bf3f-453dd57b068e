package com.yunqu.yc.api.robot;

import com.alibaba.fastjson.JSONObject;

public class RobotSession {
	
	long updateTime = System.currentTimeMillis();
	/**
	 * (必填)营销结果： A B C D E F
	 */
	private String result ;
	/**
	 * (必填)分数，0~100分
	 */
	private String score ;
	/**
	 * 营销提参内容，JSON数组
	 */
	private String labels ;
	
	/**
	 * 提参信息
	 */
	private String params ;
	
	/**
	 * session发起时间，对应字段：userData.ivrBeginTime
	 */
	private String sessionTime;
	/**
	 * IVR地址：userData.ivrAddr
	 */
	private String clientId;
	
	private String seq;
	
	private int order = -1;
	
	private String entId;
	
	private String called;
	
	private String taskId;
	
	private String objId;
	
	private String ivrSessionId;
	
	/**
	 * IVRsessionId,userData.ivrSessionId
	 */
	private String sessionId;
	
	public long getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(long updateTime) {
		this.updateTime = updateTime;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getScore() {
		return score;
	}
	public void setScore(String score) {
		this.score = score;
	}
	public String getLabels() {
		return labels;
	}
	public void setLabels(String labels) {
		this.labels = labels;
	}
	public String getSessionTime() {
		return sessionTime;
	}
	public void setSessionTime(String sessionTime) {
		this.sessionTime = sessionTime;
	}

	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	
	
	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}
	
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getSeq() {
		return seq;
	}
	public void setSeq(String seq) {
		this.seq = seq;
	}
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	
	public String getCalled() {
		return called;
	}
	public void setCalled(String called) {
		this.called = called;
	}
	
	public String getParams() {
		return params;
	}
	public void setParams(String params) {
		this.params = params;
	}
	
	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	
	public String getObjId() {
		return objId;
	}
	public void setObjId(String objId) {
		this.objId = objId;
	}
	
	
	
	
	public String getIvrSessionId() {
		return ivrSessionId;
	}
	public void setIvrSessionId(String ivrSessionId) {
		this.ivrSessionId = ivrSessionId;
	}
	public JSONObject toJSONObject(){
		JSONObject jsonObject = new JSONObject();
		//会话时间
		jsonObject.put("sessionTime", this.getSessionTime());
		//会话ID
		jsonObject.put("sessionId", this.getSessionId());
		jsonObject.put("ivrSessionId", this.getIvrSessionId());
		//
		jsonObject.put("seq", this.getSeq());
		//结果，分为 A  B C D E  F
		jsonObject.put("result", this.getResult());
		//标签信息，String 
		jsonObject.put("labels", this.getLabels());
		//提参结果，String 
		jsonObject.put("params", this.getParams());
		//分值
		jsonObject.put("score", this.getScore());
		
		jsonObject.put("called", this.getCalled());
		
		jsonObject.put("taskId", this.getTaskId());
		
		jsonObject.put("objId", this.getObjId());
		
		return jsonObject;
	}
	
	
	
	
	public String toString(){
		return JSONObject.toJSONString(this);
	}
	
}
