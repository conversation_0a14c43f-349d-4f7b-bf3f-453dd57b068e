package com.yunqu.yc.api.client;



import java.io.BufferedReader;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpConnectionManager; 
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpClientParams;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;

import com.yunqu.yc.api.model.RequestDataModel;

/**
 * Http request proxy.
 * <AUTHOR>
 *
 */
public class Proxy {
	//const
	public final static int DEFAULT_CONNECT_TIMEOUT = 5000;
	public final static int DEFAULT_SO_TIMEOUT      = 5000;
	public final static int DEFAULT_BUFFER_SIZE     = 1024;
	public final static int DEFAULT_MAX_CONNECTIONS = 100;
	
	private final static String CHARSET  = "UTF-8";
	private final static String CS_PREFIX  = "charset=";
	private final static int CS_PREFIX_LEN = CS_PREFIX.length();
	
    // 数据编码格式 
    private final String encoding;
	// client
    private final HttpClient client;
    // buffer
    private final int bufferSize;
    
    private static Proxy proxy = new Proxy();
    
    private Proxy(){
    	this("UTF-8", DEFAULT_CONNECT_TIMEOUT, DEFAULT_SO_TIMEOUT, 
    			DEFAULT_BUFFER_SIZE, DEFAULT_MAX_CONNECTIONS);
    }
    
   
  
    
    private Proxy(final String encoding, final int connectTimeout, final int soTimeout, 
    		final int bufferSize, final int maxConnections){
    	this.encoding = encoding;
    	// connect-parameters
    	final HttpConnectionManagerParams mp = new HttpConnectionManagerParams();
    	mp.setConnectionTimeout(connectTimeout);
    	mp.setSoTimeout(soTimeout);
    	mp.setStaleCheckingEnabled(true);
    	mp.setTcpNoDelay(true);
    	mp.setMaxTotalConnections(maxConnections);
    	mp.setParameter("http.protocol.content-charset", CHARSET);
    	mp.setParameter("http.protocol.element-charset", CHARSET);
//    	mp.setParameter(HTTP.CONTENT_ENCODING, HTTP.UTF_8);
//    	mp.setParameter(HTTP.CHARSET_PARAM, HTTP.UTF_8);
//    	mp.setParameter(HTTP.DEFAULT_PROTOCOL_CHARSET, HTTP.UTF_8);
    	final HttpConnectionManager mgr = new MultiThreadedHttpConnectionManager();
    	mgr.setParams(mp);
    	// client-init
    	this.client = new HttpClient(mgr);
    	// client-parameters
    	final HttpClientParams cparams = new HttpClientParams();
    	// 设置httpClient的连接超时，对连接管理器设置的连接超时是无用的
    	cparams.setConnectionManagerTimeout(connectTimeout);
    	cparams.setContentCharset(CHARSET);
    	cparams.setHttpElementCharset(CHARSET);
    	this.client.setParams(cparams);
    	this.bufferSize = bufferSize;
    }
    
    public HttpClient getClient(){
        return client;  
    } 
    
    public String getEncoding() {
		return encoding;
	}
    
    public static String post(final String url, final Map<String, String> headers, final RequestDataModel requestDataModel) throws HttpException{
    	
    	// post方式  
        final PostMethod post = new PostMethod(url);
        
        //设定头部数据
        setHeaders(post,headers);
        
        if(requestDataModel != null){ 
            final NameValuePair[] params = new NameValuePair[7];  
            params[0] = new NameValuePair("command", requestDataModel.getCommand());  
            params[1] = new NameValuePair("clientId", requestDataModel.getClientId());  
            params[2] = new NameValuePair("serialId", requestDataModel.getSerialId());  
            params[3] = new NameValuePair("timestamp", requestDataModel.getTimestamp());  
            params[4] = new NameValuePair("version", requestDataModel.getVersion());  
            params[5] = new NameValuePair("data", requestDataModel.getData());  
            params[6] = new NameValuePair("sign", requestDataModel.getSign());  
            post.setRequestBody(params);
        } 
        try {  
        	return (proxy.execute(post, CHARSET));
        }finally{
        	post.releaseConnection();  
        }
    }
    
    /**
     * 支持二进制流
     * @param url
     * @param headers
     * @param requestDataModel
     * @return
     * @throws Exception
     */
    
    public static InputStream binaryPost(final String url, final Map<String, String> headers, final RequestDataModel requestDataModel) throws Exception{
    	
    	// post方式  
        PostMethod post = new PostMethod(url);
        HttpClient client = new HttpClient();  
        //设定头部数据
        setHeaders(post,headers);
        
        if(requestDataModel != null){ 
            final NameValuePair[] params = new NameValuePair[7];  
            params[0] = new NameValuePair("command", requestDataModel.getCommand());  
            params[1] = new NameValuePair("clientId", requestDataModel.getClientId());  
            params[2] = new NameValuePair("serialId", requestDataModel.getSerialId());  
            params[3] = new NameValuePair("timestamp", requestDataModel.getTimestamp());  
            params[4] = new NameValuePair("version", requestDataModel.getVersion());  
            params[5] = new NameValuePair("data", requestDataModel.getData());  
            params[6] = new NameValuePair("sign", requestDataModel.getSign());  
            post.setRequestBody(params);
        } 
        int status = client.executeMethod(post);  
        if (status == HttpStatus.SC_OK) {  
           return post.getResponseBodyAsStream();
        } else {  
           throw new Exception("HTTP 请求错误！");
        }  
    }
    
    
    
    private  static void   setHeaders( HttpMethod method,  Map<String, String> headers){
    	if(headers != null){
    		final Set<Map.Entry<String, String>> headset = headers.entrySet();  
            for(Iterator<Map.Entry<String, String>> it = headset.iterator(); it.hasNext();){
            	Map.Entry<String, String> header = it.next();
            	method.setRequestHeader(header.getKey(), header.getValue());  
            }
    	}
     
    }
    
    private String execute(final HttpMethod method, final String encoding) throws HttpException{
  	  InputStream in = null;  
  	  BufferedReader reader = null; 
  	  try {
  		  client.executeMethod(method);
  		  // get-encoding
  		  String encode = encoding;
  		  final Header ctypeh = method.getResponseHeader("Content-Type");
  		  if(ctypeh != null){
  			  final String cv = ctypeh.getValue();
  			  final String ctype;
  			  if(cv == null){
  				  ctype = null;
  			  }else{
  				  ctype = cv.toLowerCase(Locale.ENGLISH);
  			  }
  			  final int i;
  			  if(ctype != null && (i=ctype.indexOf(CS_PREFIX))!=-1){
  				  encode = ctype.substring(i+CS_PREFIX_LEN).trim();
  				  // test encoding
  				  try{
  					  "a".getBytes(encode);
  				  }catch(java.io.UnsupportedEncodingException e){
  					  encode = encoding;
  				  }
  				  //
  			  }
  		  }
  		  //
  		  if(encode == null){
  			  return (method.getResponseBodyAsString());
  		  }
  		  in = method.getResponseBodyAsStream(); 
			  reader = new BufferedReader(new InputStreamReader(in, encode), bufferSize);
	          final StringBuffer sbuf = new StringBuffer(bufferSize>>>1);
	          for(String line = reader.readLine(); line != null; line = reader.readLine()){
	        	  sbuf.append(line).append("\r\n");  
	          }  
  		  return (sbuf.toString());
  	  }catch(IOException e){
  		  throw new HttpException(e.getMessage());
  	  } finally{
  		  if(reader != null){
  			  try {
  				  reader.close();  
  			  }
  			  catch (IOException e) {}
  		  }
  		  if(in != null){
  			  try {  
  				  in.close();  
  			  } catch (IOException e) {}  
  		  }
  	  }
  }
}
