package com.yunqu.yc.agent.base;


public class SPAEvent {

	public final static int MSG_EVT_AGENTMSGARRIVED = 1053; // 实时消息到达
	public final static int MSG_EVT_BELOGOFFED = 1054; // 被注销异步事件
	public final static int MSG_EVT_BEBLOCKED = 1055; // 被闭塞异步事件
	public final static int MSG_EVT_BEUNBLOCKED = 1056; // 被解闭塞异步事件
	public final static int MSG_EVT_BEFORCECLEARCALL = 1057; // 被强制挂机异步事件
	public final static int MSG_EVT_BEINTERCEPTCALL = 1058; // 被拦截异步事件
	public final static int MSG_EVT_LISTENSTOPPED = 1059; // 监听结束异步事件
	public final static int MSG_EVT_PHONECANCELED = 1089; // 呼叫已取消异步事件
	public final static int MSG_EVT_BEINTERVENTED = 1092; // 被班长强插异步事件

	public final static int MSG_EVT_ALERTING = 1071; // 振铃异步事件
	public final static int MSG_EVT_PHONECONNECT = 1072; // 呼叫建立异步事件
	public final static int MSG_EVT_PHONEDISCONNECT = 1073; // 呼叫拆除异步事件
	public final static int MSG_EVT_PHONETRANSFERED = 1074; // 被叫被转换事件
	public final static int MSG_EVT_CONSULTATIONCLEARED = 1075; // 咨询拆除异步事件
	public final static int MSG_EVT_GENERAL = 1104; // 通用事件
	public final static int MSG_EVT_DATAREQUESTAGENT = 1110; // ivr给坐席发消息

}
