
<!DOCTYPE HTML>
<html lang="zh-hans" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>获取空闲坐席 · ccbar jssdk话务能力接口文档</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="http://www.yunqu-info.com/">
        <meta name="identifier" content="false" scheme="ISBN">
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-splitter/splitter.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-prism/prism.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-back-to-top-button/plugin.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="event.html" />
    
    
    <link rel="prev" href="cmd-grouplist.html" />
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                        <b>1.1.</b>
                    
                    简介
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="rule.html">
            
                <a href="rule.html">
            
                    
                        <b>1.2.</b>
                    
                    通讯协议和规则
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="layout.html">
            
                <a href="layout.html">
            
                    
                        <b>1.3.</b>
                    
                    开发流程
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="data-.html">
            
                <a href="data-.html">
            
                    
                        <b>1.4.</b>
                    
                    基于data-*的界面绑定
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="initv2.html">
            
                <a href="initv2.html">
            
                    
                        <b>1.5.</b>
                    
                    初始化方法V2.0
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="call.html">
            
                <a href="call.html">
            
                    
                        <b>1.6.</b>
                    
                    话务事件方法
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="cmd-logon.html">
            
                <a href="cmd-logon.html">
            
                    
                        <b>1.6.1.</b>
                    
                    签入
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="cmd-logoff.html">
            
                <a href="cmd-logoff.html">
            
                    
                        <b>1.6.2.</b>
                    
                    签出
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="cmd-makcall.html">
            
                <a href="cmd-makcall.html">
            
                    
                        <b>1.6.3.</b>
                    
                    外呼
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="cmd-clearcall.html">
            
                <a href="cmd-clearcall.html">
            
                    
                        <b>1.6.4.</b>
                    
                    挂机
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.5" data-path="cmd-ready.html">
            
                <a href="cmd-ready.html">
            
                    
                        <b>1.6.5.</b>
                    
                    置忙
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.6" data-path="cmd-notready.html">
            
                <a href="cmd-notready.html">
            
                    
                        <b>1.6.6.</b>
                    
                    置闲
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.7" data-path="cmd-holdcall.html">
            
                <a href="cmd-holdcall.html">
            
                    
                        <b>1.6.7.</b>
                    
                    保持
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.8" data-path="cmd-retrievecall.html">
            
                <a href="cmd-retrievecall.html">
            
                    
                        <b>1.6.8.</b>
                    
                    恢复
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.9" data-path="cmd-consultcall.html">
            
                <a href="cmd-consultcall.html">
            
                    
                        <b>1.6.9.</b>
                    
                    咨询
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.10" data-path="cmd-transfercall.html">
            
                <a href="cmd-transfercall.html">
            
                    
                        <b>1.6.10.</b>
                    
                    转移
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.11" data-path="cmd-conferencecall.html">
            
                <a href="cmd-conferencecall.html">
            
                    
                        <b>1.6.11.</b>
                    
                    三方
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.12" data-path="cmd-worknotready.html">
            
                <a href="cmd-worknotready.html">
            
                    
                        <b>1.6.12.</b>
                    
                    进入话后整理
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.13" data-path="cmd-workready.html">
            
                <a href="cmd-workready.html">
            
                    
                        <b>1.6.13.</b>
                    
                    结束话后整理
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.14" data-path="cmd-autoanswer.html">
            
                <a href="cmd-autoanswer.html">
            
                    
                        <b>1.6.14.</b>
                    
                    自动应答切换
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.15" data-path="cmd-workmode.html">
            
                <a href="cmd-workmode.html">
            
                    
                        <b>1.6.15.</b>
                    
                    呼叫模式切换
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.16" data-path="cmd-callerlist.html">
            
                <a href="cmd-callerlist.html">
            
                    
                        <b>1.6.16.</b>
                    
                    获取外显列表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.17" data-path="cmd-grouplist.html">
            
                <a href="cmd-grouplist.html">
            
                    
                        <b>1.6.17.</b>
                    
                    获取技能组列表
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="1.6.18" data-path="cmd-agentlist.html">
            
                <a href="cmd-agentlist.html">
            
                    
                        <b>1.6.18.</b>
                    
                    获取空闲坐席
            
                </a>
            

            
        </li>
        <li class="chapter" data-level="1.6.19" data-path="cmd-admin.html">
            
                <a href="cmd-admin.html">
            
                    
                        <b>1.6.19.</b>
                    
                    管理员坐席
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="event.html">
            
                <a href="event.html">
            
                    
                        <b>1.7.</b>
                    
                    事件通知
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="event-agentstate.html">
            
                <a href="event-agentstate.html">
            
                    
                        <b>1.7.1.</b>
                    
                    坐席状态同步
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="event-notify.html">
            
                <a href="event-notify.html">
            
                    
                        <b>1.7.2.</b>
                    
                    消息响应通知
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="event-callevent.html">
            
                <a href="event-callevent.html">
            
                    
                        <b>1.7.3.</b>
                    
                    话务事件通知
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="agentstate.html">
            
                <a href="agentstate.html">
            
                    
                        <b>1.8.</b>
                    
                    坐席状态列表
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.9" data-path="createcause.html">
            
                <a href="createcause.html">
            
                    
                        <b>1.9.</b>
                    
                    呼叫原因(createCause)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.10" data-path="clearCause.html">
            
                <a href="clearCause.html">
            
                    
                        <b>1.10.</b>
                    
                    挂机原因(clearCause)
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.11" data-path="errorcode.html">
            
                <a href="errorcode.html">
            
                    
                        <b>1.11.</b>
                    
                    错误代码
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.12" data-path="qa.html">
            
                <a href="qa.html">
            
                    
                        <b>1.12.</b>
                    
                    常见问题
            
                </a>
            

            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >获取空闲坐席</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="&#x83B7;&#x53D6;&#x7A7A;&#x95F2;&#x5750;&#x5E2D;">&#x83B7;&#x53D6;&#x7A7A;&#x95F2;&#x5750;&#x5E2D;</h1>
<p>&#x901A;&#x8FC7;&#x63A5;&#x53E3;&#x53EF;&#x4EE5;&#x83B7;&#x53D6;&#x5230;&#x6280;&#x80FD;&#x7EC4;&#x4E0B;&#x7684;&#x7A7A;&#x95F2;&#x5750;&#x5E2D;</p>
<p><code>CallControl.agentList(skillGroupId,callback)</code></p>
<h5 id="options&#x53C2;&#x6570;&#x8BF4;&#x660E;">options&#x53C2;&#x6570;&#x8BF4;&#x660E;</h5>
<table>
<thead>
<tr>
<th>&#x53C2;&#x6570;</th>
<th>&#x6570;&#x636E;&#x7C7B;&#x578B;</th>
<th>&#x5FC5;&#x586B;</th>
<th>&#x9ED8;&#x8BA4;&#x503C;</th>
<th>&#x8BF4;&#x660E;</th>
</tr>
</thead>
<tbody>
<tr>
<td>skillGroupId</td>
<td>Function</td>
<td>&#x5426;</td>
<td></td>
<td>&#x6280;&#x80FD;&#x7EC4;id,&#x4E3A;&#x7A7A;&#x65F6;&#x83B7;&#x53D6;&#x6280;&#x80FD;&#x7EC4;&#x5217;&#x8868;</td>
</tr>
<tr>
<td>callback</td>
<td>Function</td>
<td>&#x5426;</td>
<td></td>
<td>&#x56DE;&#x8C03;&#x65B9;&#x6CD5;</td>
</tr>
</tbody>
</table>
<h5 id="response">response</h5>
<pre class="language-"><code>

</code></pre>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="cmd-grouplist.html" class="navigation navigation-prev " aria-label="Previous page: 获取技能组列表">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="event.html" class="navigation navigation-next " aria-label="Next page: 事件通知">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"获取空闲坐席","level":"1.6.18","depth":2,"next":{"title":"事件通知","level":"1.7","depth":1,"path":"jssdk/event.md","ref":"jssdk/event.md","articles":[{"title":"坐席状态同步","level":"1.7.1","depth":2,"path":"jssdk/event-agentstate.md","ref":"jssdk/event-agentstate.md","articles":[]},{"title":"消息响应通知","level":"1.7.2","depth":2,"path":"jssdk/event-notify.md","ref":"jssdk/event-notify.md","articles":[]},{"title":"话务事件通知","level":"1.7.3","depth":2,"path":"jssdk/event-callevent.md","ref":"jssdk/event-callevent.md","articles":[]}]},"previous":{"title":"获取技能组列表","level":"1.6.17","depth":2,"path":"jssdk/cmd-grouplist.md","ref":"jssdk/cmd-grouplist.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","splitter","-search","-sharing","search-plus","prism","-highlight","back-to-top-button","-toggle-chapters","copy-code-button"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":true,"progress":true},"splitter":{},"search-plus":{},"prism":{},"back-to-top-button":{},"copy-code-button":{},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"http://www.yunqu-info.com/","pdf":{"pageBreaksBefore":"/","headerTemplate":null,"paperSize":"a4","margin":{"right":62,"left":62,"top":36,"bottom":36},"fontSize":12,"fontFamily":"Arial","footerTemplate":null,"chapterMark":"pagebreak","pageNumbers":false},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"isbn":"false","variables":{},"title":"ccbar jssdk话务能力接口文档","language":"zh-hans","links":{"sharing":{"all":null,"facebook":false,"google":false,"twitter":false,"weibo":true}},"gitbook":"*","description":"话务能力接口文档","extension":null},"file":{"path":"jssdk/cmd-agentlist.md","mtime":"2019-11-15T02:36:18.459Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2021-08-20T10:34:45.232Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-splitter/splitter.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-back-to-top-button/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-copy-code-button/toggle.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

