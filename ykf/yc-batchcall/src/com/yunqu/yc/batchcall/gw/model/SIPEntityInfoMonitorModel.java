package com.yunqu.yc.batchcall.gw.model;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.batchcall.gw.log.GWLogger;
import com.yunqu.yc.batchcall.gw.util.CacheUtil;


/**
 * 监控SBC状态
 * {
	"sessionId": "540007A55FF3CD915FF3D6D55FF3D6D5",
	"clientId": "ipmg_br139@*************:7551",
	"messageType": "cmdService",
	"entId": "",
	"seq": "77",
	"data": {
		"serviceId": "BATCHCALL_MONITOR_SERVICE",
		"value": {
			"msgType": "sipEntityInfo",
			"localSIPAddr": "*************:5888",
			"Time": "2021-01-05 11:02:45",
			"sipEntityStatus": [{
				"remoteSIPAddr": "*************:5060",
				"status": "active"
			}]
		}
	},
	"userData": {}
}

localSIPAddr:ipmg本身地址，  sipEntityStatus：ipmg所管理的sbc
 * <AUTHOR>
 *
 */
public class SIPEntityInfoMonitorModel extends MonitorModel {
	
	JSONArray sipEntityStatus;

	@Override
	public void init(JSONObject jsonObject) {
		sipEntityStatus = jsonObject.getJSONArray("sipEntityStatus");
	}

	@Override
	public void save() throws Exception {
		if(sipEntityStatus == null) return;
		for(int i = 0;i<sipEntityStatus.size();i++){
			JSONObject sbcInfo = sipEntityStatus.getJSONObject(i);
			CacheUtil.put("SBC_STATUS_"+sbcInfo.getString("remoteSIPAddr"), sbcInfo.getString("status"),90);
			GWLogger.getLogger().info("sipEntityStatus->SBC_"+sbcInfo.getString("remoteSIPAddr")+"->"+sbcInfo);
			String alias = sbcInfo.getString("alias");
			if(StringUtils.isNotBlank(alias)) {
				CacheUtil.put("SBC_STATUS_"+alias, sbcInfo.getString("status"),30);
			}
		}
	}

}
