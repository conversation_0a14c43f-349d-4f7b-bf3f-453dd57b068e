package com.yunqu.yc.notify.base;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.notify.log.NotifyLogger;

/**
 * 企业上下文，保持企业相关的信息。
 * <AUTHOR>
 *
 */
public class EntContext {
	
	private String entId;
	private String schemaId;
	private String entName;
	private String entCode;
	private String resEntId;
	private String petraId;
	private int license = 0 ;
	private JSONObject extConf = new JSONObject();
	private long timer = System.currentTimeMillis();
	private String ccbarKey = "";
	private String recordFileUrl = "";
	private int ttsLicense;
	private int ivrLicense;
	private int callLicense;

    private Map<String,String> busiOrderIds = null;
	
	private static Logger logger = NotifyLogger.getLogger();
	
	private static Map<String,EntContext> contexts = new HashMap<String,EntContext>();
	
	private static String defBusiId ;
	
	public static EntContext  getContext(String entId){
		
		EntContext context = contexts.get(entId);
		//缓存信息每5分钟更新一次
		if(context!=null){
			if(System.currentTimeMillis()-context.timer>300*1000){
				contexts.remove(entId);
				context = null;
			}
		}
		if(context == null) {
			context = new EntContext(entId);
			try {
				context.init();
				contexts.put(entId, context);
			} catch (Exception ex) {
				logger.error("EntContext.init() error >>cause:"+ex.getMessage(),ex);
			}
		}
		return context;
	}
	
	
	public String getEntId(){
		return this.entId;
	}
	
	public String getPetraId() {
		return petraId;
	}


	public String getEntCode() {
		return entCode;
	}


	public int getLicense() {
		return license;
	}



	public void setLicense(int license) {
		this.license = license;
	}

	public int getCallLicense() {
		return callLicense;
	}

	public void setCallLicense(int callLicense) {
		this.callLicense = callLicense;
	}

	public String getResEntId() {
		if(StringUtils.isBlank(this.resEntId))  return this.entId;
		if("0".equals(this.resEntId)) return this.entId;
		return resEntId;
	}


	public void setResEntId(String resEntId) {
		this.resEntId = resEntId;
	}



	public String getCcbarKey() {
		return ccbarKey;
	}


	public void setCcbarKey(String ccbarKey) {
		this.ccbarKey = ccbarKey;
	}

	/**
	 * 获得企业所在的schema
	 * @param tableName 企业名称
	 * @return
	 */
	public String getTableName(String tableName){
		return schemaId + "." + tableName;
	}
	public String getSchemaId() {
		return schemaId;
	}
	public String getRecordFileUrl() {
		return recordFileUrl;
	}

	public void setRecordFileUrl(String recordFileUrl) {
		this.recordFileUrl = recordFileUrl;
	}
	
	public EntContext(String entId){
		this.entId = entId;
	}
	
	public String getEntName(){
		return this.entName;
	}
	
	public String getExtConf(String key){
		return this.extConf.getString(key);
	}
	
	public String getBusiOrderId(String busiId){
		return this.busiOrderIds.get(defBusiId);
	}
	
	/**
	 * 初始化企业的相关上下文信息，包括：磐石网关，mars网关和数据库等
	 * @throws Exception
	 */
	private void init() throws Exception{
		
		String sql = "select * from CC_ENT where ENT_ID = ?";
		EasyRow entRow = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{this.entId});
		this.resEntId = entRow.getColumnValue("P_ENT_ID");
		this.entCode = entRow.getColumnValue("ENT_CODE");
		this.entName =  entRow.getColumnValue("ENT_NAME");

		
		sql = "select ENT_EXT_CONF from CC_ENT where ENT_ID = ?";
		entRow = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{this.getResEntId()});
		this.ccbarKey = null;
		String extConfString = entRow.getColumnValue("ENT_EXT_CONF");
		//NotifyLogger.getLogger().info("extConfString->"+extConfString);
		if(StringUtils.isNotBlank(extConfString)){
			this.extConf = JSONObject.parseObject(extConfString);
			this.ccbarKey = extConf.getString("CCBAR_DES_KEY");
		}
		if(StringUtils.isBlank(this.ccbarKey)) this.ccbarKey = Constants.getSecretKey();
		
		sql = "select  t1.SCHEMA_ID,AGENT_LICENSE,t1.PETRA_ID,t1.IVR_LICENSE,t1.TTS_LICENSE,t1.CALL_LICENSE,t2.RECORD_FILE_URL from CC_ENT_RES  t1, CC_PETRA_RES t2  where t1.PETRA_ID = t2.PETRA_ID and t1.ENT_ID = ?";
		EasyRow row = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{this.getResEntId()});
		this.schemaId = row.getColumnValue("SCHEMA_ID");
		this.petraId = row.getColumnValue("PETRA_ID");
		this.recordFileUrl = row.getColumnValue("RECORD_FILE_URL");

		try {
			this.ttsLicense = Integer.parseInt(row.getColumnValue("TTS_LICENSE"));
		}catch (Exception e){
			logger.error(e.getMessage(),e);
			this.ttsLicense = 0;
		}

		try {
			this.ivrLicense = Integer.parseInt(row.getColumnValue("IVR_LICENSE"));
		}catch (Exception e){
			logger.error(e.getMessage(),e);
			this.ivrLicense = 0;
		}

		try {
			this.callLicense = Integer.parseInt(row.getColumnValue("CALL_LICENSE"));
		}catch (Exception e){
			logger.error(e.getMessage(),e);
			this.callLicense = 0;
		}

		try {
			this.license = Integer.parseInt(row.getColumnValue("AGENT_LICENSE"));
		} catch (Exception ex) {
			logger.error(ex,ex);
			this.license = 0;
		}
		
		busiOrderIds = new HashMap<String, String>();
		sql = "select BUSI_ORDER_ID,BUSI_ID  from   CC_BUSI_ORDER  where ENT_ID = ? and ORDER_STATE = 1";
		List<JSONObject>  orders =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
		for(JSONObject jsonObject:orders){
			busiOrderIds.put(jsonObject.getString("BUSI_ID"), jsonObject.getString("BUSI_ORDER_ID"));
			defBusiId = jsonObject.getString("BUSI_ID");
		}
	}

	public int getTtsLicense() {
		return ttsLicense;
	}

	public void setTtsLicense(int ttsLicense) {
		this.ttsLicense = ttsLicense;
	}

	public int getIvrLicense() {
		return ivrLicense;
	}

	public void setIvrLicense(int ivrLicense) {
		this.ivrLicense = ivrLicense;
	}

}
