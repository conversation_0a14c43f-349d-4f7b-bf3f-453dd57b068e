package com.yunqu.yc.notify.exception;

@SuppressWarnings("serial")
public class CallException extends Exception{

	/**
	 * 异常代码
	 */
	private int errorCode ;
	
	/**
	 * 异常信息
	 */
	private String msg;
	
	public CallException(int errorCode ,String msg){
		super(msg);
		this.errorCode = errorCode;
	}

	public int getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(int errorCode) {
		this.errorCode = errorCode;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	

}


