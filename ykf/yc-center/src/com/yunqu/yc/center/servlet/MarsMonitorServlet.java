package com.yunqu.yc.center.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.sso.impl.DefaultUserPrincipal;

@WebServlet("/mars/monitor")
public class MarsMonitorServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		if (!getUserPrincipal(req).isResource("yc_center_ocs_monitor")) {
			return;
		}
		EasyCache cache = CacheManager.getMemcache();
		String marsMonitor = (String) cache.get("MARS_MONITOR");
		if (marsMonitor == null) {
			marsMonitor = "";
		}
		resp.setCharacterEncoding("UTF-8");
		PrintWriter writer = resp.getWriter();
		writer.write(marsMonitor);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doGet(req, resp);
	}

	private UserPrincipal getUserPrincipal(HttpServletRequest req) {
		UserPrincipal userPrincipal = (UserPrincipal) req.getUserPrincipal();
		if (userPrincipal == null) {
			userPrincipal = (UserPrincipal) req.getSession().getAttribute("G_User_Principal");
		}
		if (userPrincipal == null) {
			userPrincipal = new DefaultUserPrincipal();
		}
		return userPrincipal;
	}

}
