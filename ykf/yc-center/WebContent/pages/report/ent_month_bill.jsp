<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业计费统计报表</title>
	 <style type="text/css">
		#dataList td:nth-child(1){display: table-cell;}
		#dataList tr td{white-space:nowrap;min-width:120px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
            			<div class="form-group">
	             		        <h5> 企业月账单统计<span id="titleAndTime"></span></h5>
	             		      	<div class="input-group input-group-sm pull-right">
								      <button type="button" class="btn btn-sm btn-success " onclick="RptTaskStat.exportRptTaskStat()"><i class="glyphicon glyphicon-export"></i>  导 出 </button>
						        </div>
             				  <div class="input-group input-group-sm ml-20" style="width: 200px">
								      <span class="input-group-addon">企业名称</span>	
						              <select name="entId" class="form-control input-sm" data-mars-top="true" id="entId" data-mars="ent.entDict">
						              		<option value="">请选择</option>
	              					  </select>
							  </div>
							  <div class="input-group input-group-sm ml-20" style="width: 200px">
								      <span class="input-group-addon">月份</span>	
						              <select name="monthId" class="form-control input-sm" data-mars-top="true" id="monthId" data-mars="statistic.fourMonths">
						              		<option value="">请选择</option>
	              					  </select>
							  </div>
							  <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="RptTaskStat.loadData()"><span class="glyphicon glyphicon-search"></span> 查询 </button>
							  </div>
						  </div>
             		</div>
	              	<div class="ibox-content table-responsive">
		           	     <table style="min-width: 1200px" class="table table-auto table-bordered table-hover table-condensed text-nowrap text-c" data-auto-fill="10" data-mars="statistic.entMonthBill">
                             <thead>
	                         	 <tr>
								      <th class="text-c">企业名称</th>
								      <th class="text-c">月份</th>
								      <th class="text-c">计费号码</th>
								      <th class="text-c">计费单元</th>
								      <th class="text-c">通道数</th>
								      <th class="text-c">套餐内计费时长</th>
								      <th class="text-c">基础套餐费（元）</th>
								      <th class="text-c">超套餐费用（元）</th>
								      <th class="text-c">总通话次数</th>
								      <th class="text-c">总通话时长</th>
								      <th class="text-c">总计费时长</th>
								      <th class="text-c">超套餐计费时长</th>
								      <th class="text-c">套餐内通话时长</th>
								      <th class="text-c">基础账单费（元）</th>
								      <th class="text-c">超套账单费（元）</th>
								      <th class="text-c">费用合计（元）</th>
								      <th class="text-c">处理状态</th>
								      <th class="text-c">更新时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                       	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:ENT_NAME}}</td> 
											<td>{{:MONTH_ID}}</td>
											<td>{{:FEE_CODE}}</td>
											<td>{{:FEE_UNIT_DESC}}</td>
											<td>{{:CALL_LICENSE}}</td>
											<td>{{:BASE_TIMES}}</td>
											<td>{{call:BASE_FEE fn='formatFee'}}</td>
											<td>{{call:OVER_FEE fn='formatFee'}}</td>
											<td>{{:CALL_COUNT}}</td>
											<td>{{:CALL_TIME}}</td>
											<td>{{:BILL_TIME}}</td>
											<td>{{:OVER_BILL_TIME}}</td>
											<td>{{:SUM_BASE_TIME}}</td>
											<td>{{call:SUM_BASE_FEE fn='formatFee'}}</td>
											<td>{{call:SUM_OVER_FEE fn='formatFee'}}</td>
											<td>{{call:SUM_FEE fn='formatFee'}}</td>
											<td>{{if STAT_STATE == 0}}待处理{{else STAT_STATE == 1}}处理中{{else STAT_STATE == 9}}处理完成{{/if}}</td>
											<td>{{:LAST_UPDATE_TIME}}</td>
									    </tr>
								    {{/for}}
														         
							 </script>
                    	<div class="row paginate">
                     		<jsp:include page="/pages/common/pagination.jsp">
                     			<jsp:param value="15" name="pageSize"/>
                     		</jsp:include>
                     	</div>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("RptTaskStat");
		
		$(function(){
			$("#searchForm").render({success:function(result){
				if(result['ent.entDict']){
					requreLib.setplugs('select2',function(){
						 $("#entId").select2({theme: "bootstrap"});
					});
				}
			}});
		});
		
		//格式化秒数
		function formatFee(value) {
		    return value * 0.01;
		}
		
		RptTaskStat.loadData=function(){
			$("#searchForm").searchData();
		}
		RptTaskStat.exportRptTaskStat = function(){
			layer.confirm('是否导出企业月账单统计表？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportEntMonthBill&"+$("#searchForm").serialize();
			});
		}
		
	</script>
	
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
