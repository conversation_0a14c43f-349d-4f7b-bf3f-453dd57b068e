<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传敏感词数据</title>
	<style>
		 #importDataList-body td:nth-child(even) {  
			 display: none;
   		 }  
	    #importDataList-body td:nth-child(odd) {  
	        background: White;  
	    } 
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="uploadForm" method="post" enctype="multipart/form-data">
				  <input type="hidden" id="listJson" name="jsonStr" >
				  <table class="table table-vzebra">
	                    <tbody>
		                     <tr>
			                        <td class="required"> 敏感词资料</td>
			                        <td>
			                        	 <input type="file" id="file" name="file" onchange="upload()" class="hidden" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/>
			                        	 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()"> 上传 </button>
					                 	 <a class="ml-20 btn btn-sm btn-link" href="${ctxPath}/servlet/sensitiveWrods?action=downLoadTemp" target="_blank">下载模板</a>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c">
			                        	导入结果
			                        </td>
			                        <td id="import_result">
			                        	待导入
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c">
			                        	备注
			                        </td>
			                        <td>请一次性导入小于50000条数据！</td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="uploadForm.ajaxSubmitForm()"> 确认 </button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("uploadForm");
	
	uploadForm.ajaxSubmitForm = function(){
		var list = $("#listJson").val();
		if(list&&list!=''){
			$("#uploadForm").removeAttr("enctype");
			var data = form.getJSONObject("#uploadForm");	
			 ajax.remoteCall("${ctxPath}/servlet/sensitiveWrods?action=saveBatch",data,function(result) { 
				 if(result.state == 1){
					 layer.open({content:result.msg,icon: 1,offset:'30px',yes: function(index, layero){
							callback && callback();
							layer.close(index);
							popup.layerClose("#uploadForm");
	        		 }});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}else{
			layer.msg("请先导入数据",{icon: 5});
		}
			 
	}
	
	function upload(){
		$("#uploadForm").attr("enctype","multipart/form-data");
		var formData = new FormData($("#uploadForm")[0]); 
		$.ajax({  
	          url: '${ctxPath}/servlet/sensitiveWrods?action=upload',  
	          type: 'POST',  
	          data: formData,async: false,cache: false,contentType: false,processData: false,  
	          success: function (result) {
	        	  if(result.state==1){
		        	  var result=result.data;
		        	  $("#import_result").text("已找到"+result.count+"条数据，确认数据无误后点击确认按钮!");
		        	  $("#listJson").val(result.listJson);
	        	  }else{
	        		  layer.open({content: result.msg,yes: function(index, layero){
	        			    layer.close(index); 
	        			    popup.layerClose("#uploadForm");
	        		  }});
	        	  }
	          }  
	     }); 
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>