<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>组织架构</title>
	<style type="text/css">
		.ztree li span{line-height: 30px!important;}
		.ztree * {
		    height: 30px!important;
		    font-size: 14px!important;
	     }
		 div#rMenu {position:absolute; visibility:hidden; top:0;text-align: left;padding: 2px;}
		.dropdown-menu>li>a{font-size: 13px;}
	    .ibox-header{margin-bottom:10px}
	    .ibox-header-content{background-color: #ffffff;padding: 10px 20px;border-color: #e7eaec;border-style: solid solid none;border-width: 1px 0;}
	    .ibox-header-content h4{font-weight:600;font-size:16px;margin:8px 6px 0px}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" id="searchForm" name="searchForm" class="form-inline">
			<input type="hidden" name="sipCompId" id="sipCompId">
			<div class="row">
					<div  style="background-color: #fff;margin-left: 15px;width: 18%;float: left;height: 100%">
						<div style="border-bottom: 1px solid #eee;height: 52px;line-height: 52px;padding: 0px 15px"> SIP中继厂商
							<span class="f-12 pull-right">
								<button type="button" class="btn btn-sm btn-info btn-outline" onclick="SipCompany.addData()">新增 </button>
							</span>
						</div>
						<div class="ztree" data-mars="SipCompanyDao.companyTree" id="ztree" data-setting="{view:{showLine:false,showIcon: false,fontCss:{color:'#46b8da',weight:'bold'}},edit: {enable: true,showRemoveBtn:false,showRenameBtn:false},callback: {onClick: zTreeOnClick,onRightClick: onRightClick}}" style="max-height: 530px;overflow:auto; padding: 15px 0px;max-height: 520px;"></div>
					</div>
					<div style="height: 100%;width: 78%;float: left;margin-left: 15px;">
						<div class="ibox">
							<div class="ibox-header" data-mars="SipCompanyDao.companyRecord" id="sipCompanyInfo">
			             		<div class="ibox-title" style="border-bottom: none;">
									 <div class="form-group">
									 	中继厂商名称：
				             		     <h5 id="SIP_COMP_NAME"> 厂商信息</h5>
										 <div class="input-group input-group-sm pull-right">
										     <button type="button" class="btn btn-sm btn-info btn-outline" onclick="SipCompany.editData()" id="editDataBtn">编辑 </button>
										 </div>
									 </div>
			             	         <div class="ibox-header-content">
			             	            <div class="row">
			             	                <div class="col-xs-3">
			             	                    <small>中继厂商代码</small>
			             	                    <h4 class="f-16" id="SIP_COMP_CODE">-</h4>
			             	                </div>
			             	                <div class="col-xs-3">
			             	                    <small>中继联系人</small>
			             	                    <h4 id="LINK_MAN">-</h4>
			             	                 </div>
			             	                 <div class="col-xs-3">
			             	                    <small>联系电话</small>
			             	                    <h4 id="LIKN_PHONE">-</h4>
			             	                 </div>
			             	                 <div class="col-xs-3">
			             	                    <small>状态</small>
			             	                    <h4 id="STATE" data-fn="parseSipCompStat">-</h4>
			             	                 </div>
			             	            </div>
			             	        </div>
			             	    </div>
		             	    </div>
		             	    <div class="ibox-header">
			             		<div class="ibox-title">
									 <div class="form-group">
				             		      <h5> 中继商SBC对接信息 </h5>
			             		          <div class="input-group input-group-sm">
											      <span class="input-group-addon">SBC名称</span>	
												  <input type="text" name="sipSbcName" class="form-control input-sm" style="width:100px">
										   </div>
										   <div class="input-group input-group-sm">
												<button type="button" class="btn btn-sm btn-default" onclick="SipCompany.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
										   </div>
										   <div class="input-group input-group-sm pull-right">
										     	<button type="button" class="btn btn-sm btn-info btn-outline" onclick="SipCompany.addSbc()">新增SBC </button>
										   </div>
									  </div>
			             	    </div>
			              	    <div class="ibox-content">
							 	<table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" data-options="{container:'dataList',template:'list-template',pagination:true}" id="tableHead" data-mars="SipCompanyDao.sipSbcList">
	                             	<thead>
			                         	 <tr>
										      <th class="text-c">序号</th>
										      <th class="text-c">SBC名称</th>
										      <th class="text-c">SBC接入地址</th>
										      <th class="text-c">SBC端口</th>
										      <th class="text-c">SBC类型</th>
										      <th class="text-c">中继类型</th>
										      <th class="text-c">SBC对接方式</th>
										      <th class="text-c">所属厂商</th>
										      <th class="text-c">操作</th>
				   						 </tr>
	                             	</thead>
	                             	<tbody id="dataList">
	                             	</tbody>
		                  		</table>
                        	 	<script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
									   	    <td>{{:#index+1}}</td>
									   	    <td>{{:SIP_SBC_NAME}}</td>
									   	    <td>{{:SIP_SBC_ADDR}}</td>
											<td>{{:SIP_SBC_PORT}}</td>                                       
											<td>{{if SBC_TYPE == 1}}坐席SBC{{else}}中继商SBC{{/if}}</td>         
											<td>{{if SIP_TYPE == 1}}sip路线{{else}}物理路线{{/if}}</td> 
											<td>{{if CALL_TYPE == 1}}呼入{{else CALL_TYPE == 2}}呼出{{else}}呼入呼出{{/if}}</td> 
											<td>{{:SIP_COMP_NAME}}</td> 
											<td>
												<a href="javascript:void(0)" onclick="SipCompany.editSbc('{{:SIP_SBC_ID}}','{{:SIP_COMP_ID}}')">编辑</a>&nbsp;&nbsp;
												<a href="javascript:void(0)" onclick="SipCompany.delSbc('{{:SIP_SBC_ID}}')">移除</a>
											</td>
									    </tr>
								    {{/for}}					         
							 	</script>
			                     <div class="row paginate" id="page">
			                     	<jsp:include page="/pages/common/pagination.jsp"/>
			                     </div> 
								 </div>
		              	    </div>
						</div>
					</div>
		</div>
	
	</form>
	<div id="rMenu">
		<ul class="dropdown-menu" role="menu">
			<li class="edit_data"><a href="javascript:void(0)" onclick="SipCompany.editData();"><span class="glyphicon glyphicon-edit"></span><span class="item-name"id="editDataLi"> 编辑</span></a></li>
			<li class="del_data"><a href="javascript:void(0)" onclick="SipCompany.delData();"><span class="glyphicon glyphicon-trash"></span><span class="item-name"id="delDataLi"> 删除</span></a></li>
			<li class="add_user"><a href="javascript:void(0)" onclick="SipCompany.addSbc();"><span class="glyphicon glyphicon-plus"></span><span class="item-name"> 新增成员</span></a></li>
		</ul>
	</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		$("#sipCompanyInfo").hide();
		jQuery.namespace("SipCompany");
		
		var zTree, rMenu,skillName;
		var height=$(window).height();
		requreLib.setplugs('slimscroll,ztree',function(){
			 $('#ztree').slimScroll({  
	                height: height-100,
	                color: '#ddd'
	        });
		});
		$(function(){
			SipCompany.loadData();
		});
		
		function onRightClick(event, treeId, treeNode) {
			zTree = $.fn.zTree.getZTreeObj("ztree");
			rMenu = $("#rMenu");
			zTree.selectNode(treeNode);
			var nodes = zTree.getSelectedNodes();
			if(nodes&&nodes.length>0){
				$(".add_data").removeClass("hidden");
				$(".edit_data").removeClass("hidden");
				$(".del_data").removeClass("hidden");
				$(".add_user").removeClass("hidden");
				
				if(nodes[0].id == 0){
					$(".edit_data").addClass("hidden");
					$(".del_data").addClass("hidden");
					$(".add_user").addClass("hidden");
				}
				
				if(nodes[0].busiType == 1){
					$(".add_data").addClass("hidden");
				}
				$("#sipCompId").val(nodes[0].id);
				$("#busiType").val(nodes[0].busiType);
				skillName = nodes[0].name;
		        showRMenu("node", event.clientX, event.clientY);
			}
		}
		function showRMenu(type, x, y) {
			$("#rMenu ul").show();
			if (type=="root") {
				$("#m_del").hide();
				$("#m_check").hide();
				$("#m_unCheck").hide();
			} else {
				$("#m_del").show();
				$("#m_check").show();
				$("#m_unCheck").show();
			}
			rMenu.css({"top":y+"px", "left":x+"px", "visibility":"visible"});
			$("body").bind("mousedown", onBodyMouseDown);
		}
		function hideRMenu() {
			if (rMenu) rMenu.css({"visibility": "hidden"});
			$("body").unbind("mousedown", onBodyMouseDown);
		}
		function onBodyMouseDown(event){
			if (!(event.target.id == "rMenu" || $(event.target).parents("#rMenu").length>0)) {
				rMenu.css({"visibility" : "hidden"});
			}
		}
		
		function zTreeOnClick(event, treeId, treeNode){
			if(treeNode.id == 0){
				$("#sipCompanyInfo").hide();
			}else{
				$("#sipCompanyInfo").show();
			}
			
			$("#sipCompId").val(treeNode.id);
			$("#busiType").val(treeNode.busiType);
			SipCompany.loadData();
		}
		
		function parseSipCompStat(val){
			if(val == 0){
				return '正常';
			}
			return '停用';
		}
		
		function getPrefixGroup(val){
			if(val&&val != undefined&&val!=''){
				var count=$("#prefixGroup option").length;
			    for(var i=0;i<count;i++) {           
			    	var obj = $("#prefixGroup").get(0).options[i];
			    	if(obj.value == val) {
			    		return obj.text;
			        }
		        }
			}else{
				return '';
			}
		}
	  	
	  	SipCompany.loadData = function(){
	  		if($("#sipCompId").val() == 0){
				$("#sipCompanyInfo").hide();
			}else{
				$("#sipCompanyInfo").show();
			}
	  		$(".pageIndexV").val(-1);
	  		
		  	$("#searchForm").render();
	  	}
	  	
	  	SipCompany.addData = function(){
		  	hideRMenu();
			popup.layerShow({type:1,title:'中继厂商新增',offset:'20px',area:['400px','400px']},"${ctxPath}/pages/sbc/sip-conpany-edit.jsp",{});
	  	}
	  	
	  	SipCompany.editData = function(){
		  	hideRMenu();
		    popup.layerShow({type:1,title:'中继厂商编辑',offset:'20px',area:['400px','400px']},"${ctxPath}/pages/sbc/sip-conpany-edit.jsp",{sipCompId:$("#sipCompId").val()});
	   	}
	  	
	  	SipCompany.delData = function(resId,resName){
		   	$("#rMenu ul").show();
			var id =$("#sipCompId").val();
			if(id == "" || id == null){
				layer.msg("无法删除根节点！");
				return ;
			}
			layer.confirm('是否确定删除？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/servlet/sipCompany?action=deleteSipCompany", {sipCompId:id}, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	$("#sipCompId").val('');
					    	SipCompany.loadData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
						SipCompany.loadData();
					}
	  			});
			});
		}
	  	
	  	SipCompany.addSbc = function(){
		  	hideRMenu();
		  	var sipCompId =$("#sipCompId").val();
			if(sipCompId == "0" || sipCompId==null ||sipCompId==""){
				layer.msg("请选择一个SIP中继厂商！");
				return ;
			}
			popup.layerShow({type:1,title:'中继商SBC对接信息新增',offset:'rb',area:['550px','100%']},"${ctxPath}/pages/sbc/sip-conpany-sbc.jsp",{sipCompId:sipCompId});
	  	}
	  
	  	SipCompany.delSbc=function(sipSbcId){
			layer.confirm('确定移除吗?',{icon: 3, title:'提示'}, function(){
				ajax.remoteCall("${ctxPath}/servlet/sipCompany?action=deleteSipSbc",{sipSbcId:sipSbcId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
							SipCompany.loadData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});   
	  	}
	  	
	  	SipCompany.editSbc = function(sipSbcId,sipCompId){
		    popup.layerShow({type:1,title:'中继商SBC对接信息编辑',offset:'rb',area:['480px','100%']},"${ctxPath}/pages/sbc/sip-conpany-sbc.jsp",{sipCompId:sipCompId,sipSbcId:sipSbcId});
		}
	  
	</script>


</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>