<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>通知语音管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 通知语音管理</h5>
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon">企业名称</span>	
									  <input type="text" name="entName" class="form-control input-sm" style="width:140px">
							   </div>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">语音名称</span>	
									  <input type="text" name="voxName" class="form-control input-sm" onkeydown='if(event.keyCode==13){return false;}' style="width:100px">
							   </div>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">申请状态</span>	
									  <select name="voxState" class="form-control input-sm" > 
									  	<option value="0">待审批</option>
									  	<option value="1">审批通过</option>
									  	<option value="9">审批不通过</option>
									  </select>
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="VoxList.loadData()">
										<span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="entVox.applyList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">企业名称</th>
								      <th class="text-c">序号</th>
								      <th class="text-c">语音名称</th>
								      <th class="text-c">语音文件名</th>
								      <th class="text-c">申请时间</th>
								      <th class="text-c">申请状态</th>
								      <th class="text-c">审批时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr class="text-c">
											<td>{{:ENT_NAME}}</td> 
											<td>{{:VOX_IDX}}</td>                                         
											<td>{{:VOX_NAME}}</td>                                         
											<td>{{:VOX_FILENAME}}</td>                                              
											<td>{{:APPLY_TIME}}</td>          
											<td>{{getText:VOX_STATE 'voxState'}}</td>                             
											<td>{{:APPROVER_TIME}}</td>          
											<td> 
												{{if VOX_STATE == 0}}
												<a  href="javascript:void(0)" onclick="VoxList.editData('{{:VOX_ID}}',0)">审核</a>
												{{else}}
												<a  href="javascript:void(0)" onclick="VoxList.editData('{{:VOX_ID}}',1)">详情</a>
												{{/if}}
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("VoxList");
		
		VoxList.loadData=function(){
			$("#searchForm").searchData();
		} 
		
		VoxList.editData=function(voxId,state){
			var title = '通知语音审批';
			if(title == 1){
				title = '通知语音详情';
			}
		    popup.layerShow({type:1,title:'通知语音审批',offset:'20px',area:['480px','548px']},"${ctxPath}/pages/vox/vox-info.jsp",{voxId:voxId,state:state});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>