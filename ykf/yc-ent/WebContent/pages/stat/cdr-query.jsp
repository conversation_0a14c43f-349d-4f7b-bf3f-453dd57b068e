<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>通话记录查询</title>
	<style>
	.line{border:0;}
	</style>
</EasyTag:override>

<EasyTag:override name="content">
	<form class="form-inline" method="post" name="searchForm" id="searchForm">
		<input type="hidden" value="0" name="deptId" id="deptId">
		<input type="hidden" value="" name="deptCode" id="deptCode">
		<input type="hidden" value="无" name="deptName" id="deptName">
		<input type="hidden" value="" name="deptType" id="deptType">
     	<div style="background-color: #fff;margin-left: 15px;width: 18%;float: left;height: 568px">
			<div style="border-bottom: 1px solid #eee;height: 52px;line-height: 52px;padding: 0px 15px"><span class="glyphicon glyphicon-home"></span> 组织架构 <span class="f-12"></span></div>
			<div class="ztree" data-mars="framework.getFrameworkTree" id="ztree" data-setting="{callback: {onClick: zTreeOnClick}}" style="max-height: 530px;overflow:auto; padding: 15px;"></div>
		</div>
		<div style="height: 568px;width: 78%;float: left;margin-left: 15px;">
			<div class="ibox">
				<div class="ibox-title clearfix">
					<div class="form-group">
           		        <h5><span class="glyphicon glyphicon-list"></span>通话记录查询 <span id="titleAndTime"> </span></h5>
           		      	<div class="input-group input-group-sm pull-right">
						      <button type="button" class="btn btn-sm btn-success " onclick="Report.exportEntRepotList()"><i class="glyphicon glyphicon-export"></i> 导 出 </button>
				        </div>
				    </div>
					<div class="form-group">
						<hr style="margin: 3px -15px">
						<div class="input-group input-group-sm">
							<span class="input-group-addon">通话日期</span> 
							<input  name="startDate" onClick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}'})" id="startDate" data-mars-top="true"  data-mars="common.today"  size = "12" class="form-control input-sm">
							<span class="input-group-addon">-</span> 
							<input  name="endDate" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',maxDate:'#F{$dp.$D(\'startDate\',{d:+2});}'})" id="endDate" data-mars-top="true"  data-mars="common.today"   size = "12"  class="form-control input-sm">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">主叫号码</span> 
							<input  name="caller"  size = "11"  class="form-control">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">被叫号码</span> 
							<input  name="called"  size = "11"  class="form-control">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">坐席工号</span> 
							<input  name="agentPhone"  size = "11"  class="form-control">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">通话时长</span> 
							<select name="billTime" class="form-control" onchange="Report.loadData()">
								<option value="0">全部</option>
								<option value="1">有效通话</option>
								<option value="2">0~30秒</option>
								<option value="3">30秒~1分钟</option>
								<option value="4">1分钟~2分钟</option>
								<option value="5">2分钟~5分钟</option>
								<option value="6">5分钟以上</option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">呼叫结果</span> 
							<select name="clearCause" id="clearCause" class="form-control" onchange="Report.loadData()">
								<option value="">请选择</option>
								<option value="0">成功</option>
								<option value="1">无人应答</option>
								<option value="2">用户忙</option>
								<option value="3">用户挂机</option>
								<option value="4">网络忙</option>
								<option value="5">空号</option>
								<option value="6">拒接</option>
								<option value="7">关机</option>
								<option value="8">停机</option>
								<option value="9">不在服务区</option>
								<option value="10">传真机</option>
								<option value="11">欠费</option>
								<option value="12">重复号码</option>
								<option value="13">电话总机</option>
								<option value="14">久叫不应</option>
								<option value="50">回铃音反馈-关机</option>
								<option value="51">回铃音反馈-空号</option>
								<option value="52">回铃音反馈-停机</option>
								<option value="53">回铃音反馈-用户拒接</option>
								<option value="54">回铃音反馈-用户忙</option>
								<option value="55">回铃音反馈-不在服务区</option>
								<option value="56">回铃音反馈-无应答</option>
								<option value="98">坐席挂机</option>
								<option value="99">系统错误</option>
								<option value="100">其它呼叫失败</option>
							</select>
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">呼叫结果</span> 
							<select name="satisfId" class="form-control" onchange="Report.loadData()">
								<option value="">请选择</option>
								<option value="2">呼入</option>
								<option value="1">呼出</option>
							</select>
						</div>
						<div class="input-group input-group-sm for-agentSys">
							<span class="input-group-addon">满意度</span> 
							<select name="satisfId" id="satisfId" class="form-control" onchange="Report.loadData()">
								<option value="">请选择</option>
								<option value="0">未评价</option>
								<option value="1">非常满意</option>
								<option value="2">满意</option>
								<option value="3">一般</option>
								<option value="4">不满意</option>
							</select>
						</div>
						
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="Report.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					    </div>
					   
				    </div>
				</div>
				<div class="ibox-content">
					<table  data-mars="statistic.entRepotList" data-auto-fill="10" class="table table-auto table-striped table-bordered table-hover table-condensed text-c" id="tableHead">
						<thead>
							<tr>
								<th class="text-c">被叫号码</th>
								<th class="text-c">主叫号码</th>							
								<th class="text-c">话机号码</th>
								<th class="text-c">呼叫原因</th>
								<th class="text-c">呼叫结果</th>
								<th class="text-c">坐席</th>
								<th class="text-c">机构名称</th>
								<th class="text-c">开始时间</th>
								<th class="text-c">结束时间</th>
								<th class="text-c">通话时长</th>
								<%--<th class="text-c">6秒计费时长</th>--%>
								<th class="text-c">计费时长（分钟）</th>
								<th class="text-c" data-sort="DATE_ID">日期</th>
								<th class="text-c">录音</th>
							</tr> 
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<div class="row paginate">
	               	       <jsp:include page="/pages/common/pagination_more.jsp"/>
	                   </div> 
					<script id="list-template" type="text/x-jsrender">
					 {{for list}}
							<tr>
							<td>{{call:CALLED _CALLED fn='getPhone'}}</td> 
							<td>{{call:CALLER _CALLER fn='getPhone'}}</td>							
							<td>{{:PHONE_NUM}}</td> 
							<td>{{if CREATE_CAUSE == 2}}呼入{{else}}呼出{{/if}} </td>
							<td>{{getText:CLEAR_CAUSE '#clearCause'}} </td>
							<td>{{if AGENT_NAME}}{{:AGENT_NAME}}-{{/if}}{{:AGENT_PHONE}}</td> 
							<td>{{:ENT_NAME}}</td>
							<td>{{dateToTime:BILL_BEGIN_TIME}}</td>
							<td>{{dateToTime:BILL_END_TIME}}</td>
							<td>{{clock:BILL_TIME}}</td>
							<td>{{:FEE_TIME_60}} </td>
							<td>{{:DATE_ID}}</td>
							<td>
                                {{if RECORD_FILE}}
                                    <a href="javascript:void(0)" onclick="Report.recoredListener('{{:SERIAL_ID}}',
                                    '{{:RECORD_FILE}}','{{:BILL_TIME}}','{{call:CALLED _CALLED fn='getPhone'}}','{{call:CALLER _CALLER fn='getPhone'}}',
                                    '{{:BILL_BEGIN_TIME}}','{{:BILL_END_TIME}}','{{:AGENT_NAME}}-{{:AGENT_PHONE}}')">播放录音</a>
                                {{/if}}
                            </td>		
							</tr>
						{{/for}}
					 </script>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">

<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript">
	
		jQuery.namespace("Report");
		
		$(function(){
			$("#searchForm").render();
			$("#entId").change(function(event){
				Report.loadData();
			});
		});
	
		Report.loadData=function(){
			var data = form.getJSONObject("#searchForm");
			var called = data["called"];
			if(!called||called==""){
				if(!checkDate(3)){
	            	return;
	            }
			}
			$("#searchForm").searchData();
		}
		function zTreeOnClick(event, treeId, treeNode){
			  $("#deptCode").val(treeNode.id);
			  $("#deptId").val(treeNode.NODE_ID);
			  $("#deptType").val(treeNode.NODE_TYPE);
			  $("#deptName").val(treeNode.name);
			  Report.loadData();
		}
		
		$.views.converters('dateToTime',function(datetime){
			if(!datetime) return "";
			return datetime.toString().substr(11);
		});
		
		$.views.converters('clock',function(time){
			
			return formatClock(time);
			function formatClock(time) {
				if(time == undefined || time == null || time=='') return "0s";
				time = parseInt(time);
				var h = Math.floor(time/3600);
				var m = Math.floor(time%3600/60);
				var s = time%60;
				m = m<10?'0'+m:m;
				s = s<10?'0'+s:s;

				return h+":"+m+":"+s;
			}
		});

		Report.recoredListener = function(serialId,recordFile,billTime,called,caller,billBeginTime,billEndTime,agent) {
			 var entId=$("#entId").val();
			 ajax.remoteCall("${ctxPath}/servlet/call?action=getRecordFile", {serialId:serialId,entId:entId,recordFile:recordFile},function(result) {
				 if(result.state == 1){
					 recordFile =  result.data;
					 var data =
						 {serialId:serialId,entId:entId,recordFile:recordFile,billBeginTime:billBeginTime,called:called,called:called,billBeginTime:billBeginTime,billEndTime:billEndTime,agent:agent};
					 popup.layerShow({type:2,title:'播放录音',offset:'20px',area:['750px','450px']},"${ctxPath}/pages/stat/record-play.jsp",data);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			);
		}
		
		Report.exportEntRepotList = function(){
			var data = form.getJSONObject("#searchForm");
			var called = data["called"];
			if(!called||called==""){
				if(!checkDate(3)){
	            	return;
	            }
			}
			layer.confirm('是否导出通话记录？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportEntRepotList&"+$("#searchForm").serialize();
			});
		}
		changeTask = function(){
			Report.loadData();
		}

		Report.toggleMore = function(){
			var btn = $("#moreBtn").find(".glyphicon");
			$("#more").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		
		function getPhone(val,realPhone){
			if(val==''){
				return '--';
			}
			var iscrypt=localStorage.getItem("iscrypt");
			if(iscrypt=='true'&&realPhone){
				return advReplace(realPhone,-4,3,'*');
			}
			if(iscrypt=='false'&&realPhone){
				return realPhone;
			}
			if(val.substring(0,1)=='#'){
				return '******';
			}
			return val;
		}
		
		function getPhone(val,realPhone){
			if(val==''){
				return '--';
			}
			var iscrypt=localStorage.getItem("iscrypt");
			if(iscrypt=='true'&&realPhone){
				return advReplace(realPhone,-4,3,'*');
			}
			if(iscrypt=='false'&&realPhone){
				return realPhone;
			}
			if(val.substring(0,1)=='#'){
				return '******';
			}
			return val;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>