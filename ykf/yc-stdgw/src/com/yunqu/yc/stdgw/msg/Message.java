package com.yunqu.yc.stdgw.msg;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;

/**
 * <AUTHOR>
 *
 */
public abstract class Message {
	
	public abstract  ResponseData onMessage(RequestData requestData);
	
	protected  EasyCache cache = CacheManager.getMemcache();
	
	public ResponseData getResponseData(RequestData requestData){
		ResponseData responseData = new ResponseData();
		responseData.setClientId(requestData.getClientId());
		responseData.setEntId(requestData.getEntId());
		responseData.setSessionId(requestData.getSessionId());
		responseData.setUserData(requestData.getUserData());
		responseData.setMessageType(getMessageType());
		responseData.setSeq(requestData.getSeq());
		responseData.setResult("000");
		responseData.setDesc("succ");
		return responseData;
	}
	
	public abstract String getMessageType();
}
