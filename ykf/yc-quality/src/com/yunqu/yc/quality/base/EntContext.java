package com.yunqu.yc.quality.base;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class EntContext {
	
	private String petraGwUrl;
	private String marsGwUrl;
	private String entId;
	private String schemaId;
	private String marsId;
	private String petraId;
	private String bpoPrefix;
	private String entName;
	private String entCode;
	private String resEntId;
	private String satisfPrefix;  
	private String ivrOrderPrefix; 
	private String marsUrl;
	private int license = 0 ;
	private String state = "9"; 
	private String defaultBusiId = "";
	private long timer = System.currentTimeMillis();
	private String callbackUrl = "";
	private String safeIp = "";
	private String recordFileUrl = "";
	
	private JSONObject verifyTemp;
	
	//双呼流程
	private String appCall = "appcall";
	//语音通知流程
	private String voiceCall = "voicecall";
	
	
	private  Map<String,String> busiOrderIds = null;
	
	private  Map<String,String> schemas = new HashMap<String,String>();

	private  static Map<String,EntContext> contexts = new HashMap<String,EntContext>();
	
	private   List<JSONObject> callers = new ArrayList<JSONObject>();
	
	private   Set<String> phones = new HashSet<String>();
	
	private   Map<String,JSONObject> voxs = new HashMap<String,JSONObject>();
	
	private   Map<String,JSONObject> smsChns = new HashMap<String,JSONObject>();
	
	
	public JSONObject getVerifyTemp(){
		return verifyTemp;
	}

	public String getAppCall() {
		if(StringUtils.isBlank(this.appCall)) return "appcall";
		return appCall;
	}


	public void setAppCall(String appCall) {
		this.appCall = appCall;
	}


	public String getVoiceCall() {
		if(StringUtils.isBlank(this.voiceCall)) return "voicecall";
		return voiceCall;
	}


	public void setVoiceCall(String voiceCall) {
		this.voiceCall = voiceCall;
	}


	public static EntContext  getContext(String entId){
		EntContext context = contexts.get(entId);
		if(context!=null){
			if(System.currentTimeMillis()-context.timer>300*1000){
				context = null;
			}
		}
		if(context == null) {
			context = new EntContext(entId);
			try {
				context.init();
				contexts.put(entId, context);
			} catch (Exception ex) {
				getLogger().error("EntContext.init() error >>cause:"+ex.getMessage(),ex);
			}
		}
		return context;
	}
	
	
	public String getEntCode() {
		return entCode;
	}



	public int getLicense() {
		return license;
	}



	public void setLicense(int license) {
		this.license = license;
	}


	public String getEntId(){
		return this.entId;
	}


	public String getResEntId() {
		if(StringUtils.isBlank(this.resEntId))  return this.entId;
		if("0".equals(this.resEntId)) return this.entId;
		return resEntId;
	}





	public void setResEntId(String resEntId) {
		this.resEntId = resEntId;
	}

	/**
	 * 获得语音通知的列表
	 * @return
	 */
	public List<JSONObject> getVoxList(){
		Collection<JSONObject> colls = voxs.values();
		List<JSONObject> list  = new  ArrayList<JSONObject>();
		for(JSONObject voxObj:colls) list.add(voxObj);
		return list;
	}


	public String getMarsUrl() {
		return marsUrl;
	}



	public String getTableName(String tableName){
		String schemaId = schemas.get(entId);
		if(StringUtils.isBlank(schemaId)){
			String sql = "select SCHEMA_ID from CC_ENT_RES where ENT_ID = ? ";
			try {
				schemaId = QueryFactory.getQuery(entId).queryForString(sql, new Object[]{this.getResEntId()});
			} catch (Exception ex) {
				getLogger().error(ex,ex);
			}
			schemas.put(entId, schemaId);
		}
		return schemaId + "." + tableName;
	}
	
	public EntContext(String entId){
		this.entId = entId;
	}
	
	public String getEntName(){
		return this.entName;
	}
	
	public String getBusiOrderId(String busiId){
		return this.busiOrderIds.get(busiId);
	}
	
	public void setBusiOrderId(String busiId, String busiOrderId){
		busiOrderIds.put(busiId, busiOrderId);
	}
	
	public String getDefaultBusiId(){
		return this.defaultBusiId;
	}
	
	/**
	 * 初始化企业的相关上下文信息，包括：磐石网关，mars网关和数据库�?
	 * @throws Exception
	 */
	private void init() throws Exception{
		
		
		String sql = "select P_ENT_ID,ENT_TYPE,ENT_NAME,ENT_CODE,FEE_CODE,ENT_STATE from CC_ENT where ENT_ID = ?";
		
		JSONObject entInfo = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{entId}, new JSONMapperImpl());
		this.entCode = entInfo.getString("ENT_CODE");
		this.entName = entInfo.getString("ENT_NAME");
		this.state = entInfo.getString("ENT_STATE");
		
		if("3".equals(entInfo.getString("ENT_TYPE"))){
			this.resEntId = entInfo.getString("P_ENT_ID");
		}else{
			this.resEntId = this.entId;
		}
		
		sql = "select  t1.ENT_ID ,t1.PETRA_ID,t1.MARS_ID,t2.PETRA_URL,t2.RECORD_FILE_URL,t3.MARS_URL ,t1.SCHEMA_ID,t1.AGENT_LICENSE,t1.BPO_CALL_PREFIX,t2.SATISF_PREFIX ,t2.IVR_ORDER_PREFIX     from CC_ENT_RES  t1  , CC_PETRA_RES t2 ,  CC_MARS_RES t3  where t1.PETRA_ID = t2.PETRA_ID and t1.MARS_ID = t3.MARS_ID  and t1.ENT_ID = ?";
		EasyRow row = QueryFactory.getQuery(entId).queryForRow(sql, new Object[]{this.getResEntId()});
		
		
		this.petraId = row.getColumnValue("PETRA_ID");
		this.marsId = row.getColumnValue("MARS_ID");
		this.petraGwUrl = row.getColumnValue("PETRA_URL")+"/petradatagw/interface";
		this.marsGwUrl = row.getColumnValue("MARS_URL") +"/yc-api/interface";
		this.marsUrl = row.getColumnValue("MARS_URL");
		this.schemaId = row.getColumnValue("SCHEMA_ID");
		this.bpoPrefix = StringUtils.trimToEmpty(row.getColumnValue("BPO_CALL_PREFIX"));
		this.satisfPrefix = row.getColumnValue("SATISF_PREFIX");
		this.ivrOrderPrefix = row.getColumnValue("IVR_ORDER_PREFIX");
		this.recordFileUrl = row.getColumnValue("RECORD_FILE_URL");
		try {
			this.license = Integer.parseInt(row.getColumnValue("AGENT_LICENSE"));
		} catch (Exception ex) {
			getLogger().error(ex,ex);
		}
		
		busiOrderIds = new HashMap<String, String>();
		sql = "select BUSI_ORDER_ID,BUSI_ID  from   CC_BUSI_ORDER  where ORDER_STATE = 1 and ENT_ID = ?";
		List<JSONObject>  orders =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
		for(JSONObject jsonObject:orders){
			busiOrderIds.put(jsonObject.getString("BUSI_ID"), jsonObject.getString("BUSI_ORDER_ID"));
			//if(Constants.getDefaultBusiId().equalsIgnoreCase(defaultBusiId))  continue;
			this.defaultBusiId = jsonObject.getString("BUSI_ID");
		}
		
		sql = "select PREFIX_NUM,CALL_PREFIX_CODE,AREA_CODE from CC_PREFIX  where   PREFIX_STATE = 0  and  ENT_ID = ?";
		callers = QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
		
		sql = "select PHONE_NUM from CC_PHONE  where   ENT_ID = ?";
		List<JSONObject>  _phones =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.entId},new JSONMapperImpl());
		for(JSONObject phoneObj:_phones){
			phones.add(phoneObj.getString("PHONE_NUM"));
		}
		
		sql = "select * from CC_ENT_VOX  where VOX_TYPE = 3  and ENT_ID = ?";
		List<JSONObject>  _voxs =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.resEntId},new JSONMapperImpl());
		for(JSONObject vox:_voxs){
			voxs.put(vox.getString("VOX_ID"),vox);
		}
		
		try {
			sql = "select * from CC_SMS_CHN  where ENT_ID = ?";
			List<JSONObject>  _smsTemps =  QueryFactory.getQuery(entId).queryForList(sql, new Object[]{this.resEntId},new JSONMapperImpl());
			for(JSONObject temp:_smsTemps){
				smsChns.put(temp.getString("SMS_CHN_ID"),temp);
				sql = "select * from CC_SMS_TEMP_PARAM  where SMS_CHN_ID = ? ";
				List<JSONObject> params = QueryFactory.getQuery(entId).queryForList(sql, new Object[]{temp.getString("SMS_CHN_ID")},new JSONMapperImpl());
				temp.put("TEMP_PARAM", params);
				smsChns.put(temp.getString("SMS_CHN_ID"),temp);
				//短信类型,  10  验证码短信  20 通知类短信
				if("10".equals(temp.getString("SMS_CHN_TYPE"))){
					this.verifyTemp = temp;
				}
			}
		} catch (Exception ex) {
			getLogger().error(ex,ex);
		}

		
		try {
			String entExtConf = QueryFactory.getQuery(entId).queryForString("select ENT_EXT_CONF from CC_ENT where ENT_ID = ?", new Object[]{entId});
			//CcbargetLogger().info("ENT_EXT_CONF >>"+entId+ ":"+entExtConf);
			if(StringUtils.isNotBlank(entExtConf)){
				JSONObject configObj = JSON.parseObject(entExtConf);
				this.callbackUrl = StringUtils.trimToEmpty(configObj.getString("CALLBACK_URL"));
				this.appCall = StringUtils.trimToEmpty(configObj.getString("APP_CALL"));
				this.voiceCall = StringUtils.trimToEmpty(configObj.getString("VOICE_CALL"));
				this.safeIp = StringUtils.trimToEmpty(configObj.getString("CCBAR_PROXY_SAFE_IP"));
			}
		} catch (Exception ex) {
			getLogger().error(ex,ex);
		}
	}
	
	public JSONObject getVoxObj(String voxId){
		return voxs.get(voxId);
	}
	
	public JSONObject getSmsObj(String smsChnId){
		return smsChns.get(smsChnId);
	}
	
	public boolean isPhone(String phone){
		return phones.contains(phone);
	}
	
	/**
	 * 随机获取一个主主叫
	 * @return
	 */
	public JSONObject getNextCallerObj(){
		if(callers.size() == 0) return null;
		Random r = new Random();
		return callers.get(r.nextInt(callers.size()));
	}
	
	public String getNextCaller() {
		JSONObject callerInfo = this.getNextCallerObj();
		if (callerInfo == null) {
			return null;
		}

		return callerInfo.getString("PREFIX_NUM");
	}
	
	public String getSatisfPrefix() {
		return satisfPrefix;
	}





	public String getIvrOrderPrefix() {
		return ivrOrderPrefix;
	}





	public String getSchemaId() {
		return schemaId;
	}

	public String getMarsId() {
		return marsId;
	}

	public String getPetraId() {
		return petraId;
	}

	public String getPetraGwUrl(){
		return this.petraGwUrl;
	}
	
	public String getMarsGwUrl(){
		return this.marsGwUrl;
	}


	public String getBpoPrefix() {
		return bpoPrefix;
	}

	public boolean  isValid() {
		if("0".equals(this.state)) return true;
		return false;
	}
	
	public String getCallbackUrl() {
		return callbackUrl;
	}


	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getSafeIp(){
		return this.safeIp;
	}

	public String getRecordFileUrl() {
		return recordFileUrl;
	}

	public void setRecordFileUrl(String recordFileUrl) {
		this.recordFileUrl = recordFileUrl;
	}

	public static Logger getLogger() {
		return LogEngine.getLogger("yc-quality");
	}
	
}
