/*input{
  min-width: 360px;
}*/
.city-picker-input {
    opacity: 0 !important;
    top: -9999px;
    left: -9999px;
    position: absolute;
}

.city-picker-span {
    position: relative;
    display: inline-block;
    outline: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    /* border: 1px solid #ccc;*/
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #ccc;
    width: 100%;
    cursor: pointer;
    border-radius: 0 2px 2px 0;
}

.city-picker-span > .placeholder {
    color: #aaa;
    padding-left: 10px;
}

.city-picker-span > .arrow {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 10px;
    margin-top: -3px;
    height: 5px;
    background: url(drop-arrow.png) -10px -25px no-repeat;
}

/*.city-picker-span.focus,
.city-picker-span.open {
    border-bottom-color: #46A4FF;
}*/

.city-picker-span.open > .arrow {
    background-position: -10px -10px;
}

.city-picker-span > .title > span {
    color: #333;
    padding: 5px;
    font-size: 14px;
    border-radius: 3px;
}

.city-picker-span > .title > span:hover {
    background-color: #f1f8ff;
}

.city-picker-dropdown {
    position: absolute;
    width: 315px;
    left: -9999px;
    top: -9999px;
    outline: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    z-index: 999999;
    display: none;
    min-width: 330px;
    margin-bottom: 20px;

}

.city-select-wrap {
    box-shadow: 0 0px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid #ddd;
}

.city-select-tab {
    border-bottom: 1px solid #ccc;
    background: #f0f0f0;
    font-size: 13px;
    line-height: 22px;
}

.city-select-tab > a {
    display: inline-block;
    padding: 8px 22px;
    border-left: 1px solid #ccc;
    border-bottom: 1px solid transparent;
    color: #4D4D4D;
    text-align: center;
    outline: 0;
    text-decoration: none;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: -1px;
}

.city-select-tab > a.active {
    background: #fff;
    border-bottom: 1px solid #fff;
    /*color: #46A4FF;*/
    color: #f58664;
}

.city-select-tab > a:first-child {
    border-left: none;
}

.city-select-tab > a:last-child.active {
    border-right: 1px solid #ccc;
}

.city-select-content {
    width: 100%;
    min-height: 10px;
    background-color: #fff;
    padding: 10px 0px;
}

.city-select {
    font-size: 13px;
}

.city-select dl {
    line-height: 2;
    clear: both;
    padding: 3px 0;
    margin: 0;
}

.city-select dt {
    position: absolute;
    width: 2.5em;
    font-weight: 500;
    text-align: right;
    line-height: 2;
}

.city-select dd {
    margin-left: 0;
    line-height: 2;
}

.city-select.province dd {
    margin-left: 3em;
}

.city-select a {
    display: inline-block;
    padding: 0 10px;
    outline: 0;
    text-decoration: none;
    white-space: nowrap;
    margin-right: 2px;
    text-decoration: none;
    color: #333;
    cursor: pointer;
}

.city-select a:hover,
.city-select a:focus {
    background-color: #f1f8ff;
    border-radius: 2px;
    color: #f58664;
}

.city-select a.active {
    background-color: #f58664;
    color: #fff;
    border-radius: 2px;
}

.icon_ca {
    /*background: url(/content/images/icon_ca.png) no-repeat center;*/
    width: 15px;
    height: 15px;
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -7px;
    z-index: 99;
    cursor: pointer;
}
