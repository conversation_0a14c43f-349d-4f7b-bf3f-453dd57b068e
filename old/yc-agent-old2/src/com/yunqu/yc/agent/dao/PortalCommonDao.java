package com.yunqu.yc.agent.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentLogger;
import com.yunqu.yc.agent.base.AppDaoContext;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

@WebObject(name="PortalCommonDao")
public class PortalCommonDao extends AppDaoContext {
	
	@WebControl(name="personInfo",type=Types.TEMPLATE)
	public JSONObject personInfo(){
		return getJsonResult(getUserPrincipal());
	}
	/**
	 * 通知公告
	 * @return
	 */
	@WebControl(name="noticeList",type=Types.TEMPLATE)
	public JSONObject noticeList(){
		EasySQL sql = this.getEasySQL("select NOTICE_TYPE, TITLE,PUBLISH_TIME,AUTHOR,NOTICE_ID,PV from "+getTableName("CC_NOTICE"));
		sql.append(" where 1=1");
		sql.appendLike(param.getString("title"), " and TITLE like ?");
		sql.append(getEntId(), " and ENT_ID = ?");
		sql.append(getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(" and NOTICE_TYPE != 6");//排除工单提醒历史数据
		YCUserPrincipal principal=getUserPrincipal();
		if(principal.getRoleType()!=Constants.ROLE_TYPE_MANAGER){
			String[] attr = (String[]) principal.getAttribute("skillGroupIds");
			if(attr==null||attr.length==0){
				sql.append("and SKILL_GROUP_ID='0'");
			}else{
				sql.append("and SKILL_GROUP_ID in (0,"+StringUtils.join(attr, ",")+")");
			}
		}
		sql.append(" order by PUBLISH_TIME desc");
		EasyQuery query=getQuery();
		query.setMaxRow(8);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="memoList",type=Types.TEMPLATE)
	public JSONObject memoList(){
		EasySQL sql=new EasySQL("select * from "+getTableName("cc_memo")+" where 1=1");
		sql.append(getEntId(), " and ENT_ID = ?");
		sql.append(getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(this.getUserPrincipal().getUserId(), "and CREATE_BY = ?");
		EasyQuery query=getQuery();
		query.setMaxRow(8);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 工单统计
	 * @return
	 */
	@WebControl(name="orderStat",type=Types.RECORD)
	public JSONObject orderStat(){
		int roleType = this.getUserPrincipal().getRoleType();
		EasySQL sql = this.getEasySQL("select count(1) as ALL_COUNT,count(CASE WHEN ORDER_STATE = 2 THEN 1 else null end) as DEAL_COUNT from");
		sql.append(getTableName("CC_AGENT_ORDER t1"));
		if(Constants.ROLE_TYPE_MONITOR == roleType){				//班长
			sql.append("STRAIGHT_JOIN "+getTableName("CC_SKILL_GROUP_USER")+" t2 ON t2.USER_ID = t1.AGENT_ID ");
		}
		sql.append(" where 1=1");
		sql.append(getEntId()," and t1.ENT_ID=?");
		sql.append(getBusiOrderId(), " and t1.BUSI_ORDER_ID=?");
		sql.append(EasyCalendar.newInstance().getDateInt(), " and t1.DATE_ID=?");
		if(Constants.ROLE_TYPE_MONITOR == roleType){				//班长
			String[] skillGroupId = getSkillGroupId();
			if(skillGroupId != null){				//判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
				sql.append(getEntId(),"and t2.ENT_ID = ?");
				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
				if(skillGroupId.length == 1){
					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?");
				}else{
					sql.append("and t2.SKILL_GROUP_ID in ("+StringUtils.join(skillGroupId, ",")+")");
				}
			}else{
				sql.append(getUserId()," and AGENT_ID=?");
			}
		}
//		if(Constants.ROLE_TYPE_MONITOR == roleType){				//班长
//			String[] skillGroupId = getSkillGroupId();
//			if(skillGroupId != null){				//判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
//				sql.append("and AGENT_ID in ( select t2.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t2 where 1=1");
//				sql.append(getEntId(),"and t2.ENT_ID = ?");
//				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
//				if(skillGroupId.length == 1){
//					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?)");
//				}else{
//					sql.append("and t2.SKILL_GROUP_ID in ("+StringUtils.join(skillGroupId, ",")+"))");
//				}
//			}else{
//				sql.append(getUserId()," and AGENT_ID=?");
//			}
//		}else 
		if (Constants.ROLE_TYPE_AGENT == roleType){			//坐席
			sql.append(getUserId(),"and AGENT_ID=?");
		}
		return queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
	/**
	 * 前一天质检统计
	 * @return
	 */
	@WebControl(name="lastDayQcStat",type=Types.RECORD)
	public JSONObject leaveMessageStat(){
		int roleType = this.getUserPrincipal().getRoleType();
		EasySQL sql = this.getEasySQL("select count(1) as TOTAL_COUNT,");
		sql.append(" sum(case when t2.PASS_FLAG=1 then 1 else 0 end) as PASS_COUNT,");//及格数
		sql.append(" sum(case when t2.PASS_FLAG is not null then 1 else 0 end) as WAIT_COUNT");//待质检数
		sql.append(" from "+getTableName("CC_CALL_RECORD")+" t1");
		sql.append(" left join "+getTableName("CC_QC_RESULT")+" t2 on t1.SERIAL_ID = t2.SERIAL_ID ");
		sql.append("where 1=1");
		sql.append(getYesterday(), " and t1.DATE_ID = ? ");
		sql.append(getEntId()," and t1.ENT_ID=?");
		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ? ");
		//如果不是管理员
		if(Constants.ROLE_TYPE_MONITOR == roleType){				//班长
			String[] skillGroupId = getSkillGroupId();
			if(skillGroupId != null){				//判断该班长是否存在技能组，如果存在查询该团队情况，如果不存在查询个人
				sql.append("and t1.AGENT_ID in ( select t2.USER_ID from "+getTableName("CC_SKILL_GROUP_USER")+" t2 where 1=1");
				sql.append(getEntId(),"and t2.ENT_ID = ?");
				sql.append(getBusiOrderId(), "and t2.BUSI_ORDER_ID = ?");
				if(skillGroupId.length == 1){
					sql.append(skillGroupId[0], "and t2.SKILL_GROUP_ID = ?)");
				}else{
					sql.append("and t2.SKILL_GROUP_ID in ("+StringUtils.join(skillGroupId, ",")+"))");
				}
			}else{
				sql.append(getUserId()," and t1.AGENT_ID=?");
			}
		}else if(Constants.ROLE_TYPE_AGENT == roleType){			//坐席
			sql.append(this.getUserId()," and t1.AGENT_ID=?");
		}
		AgentLogger.getLogger().info("质检 "+getUserPrincipal().getUserName()+sql.getSQL()+sql.getParams());
		return queryForRecord(sql.getSQL(), sql.getParams(), null);
	}
	
	
	@WebControl(name="calendar",type=Types.OTHER)
	public JSONObject calendar(){
		return getJsonResult(null);
	}
	
	/**
	 * 获取前一天的时间
	 */
	private int getYesterday(){
		EasyCalendar instance = EasyCalendar.newInstance();
		instance.add(EasyCalendar.DAY, -1);
		return instance.getDateInt();
	}
	
}
