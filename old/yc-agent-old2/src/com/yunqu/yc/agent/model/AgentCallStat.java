package com.yunqu.yc.agent.model;

import org.easitline.common.utils.excel.annotation.ExcelField;

public class AgentCallStat {
    @ExcelField(title = "技能组", order = 1)
    private String groupName;
	@ExcelField(title = "坐席", order = 2)
	private String agent;
	@ExcelField(title = "呼入数", order = 3)
	private String inCallCount;
	@ExcelField(title = "接通数）", order = 4)
	private String inSuccCount;
	@ExcelField(title = "接通率", order = 5)
	private String inSuccPercent;
	
	/*@ExcelField(title = "转坐席数", order = 6)
	private String inAgentSuccCount;*/
	
    @ExcelField(title = "呼损率", order = 6)
    private String inErrorPercent;
	@ExcelField(title = "通话时长（分钟）", order = 7)
	private String inTotalTime;
	@ExcelField(title = "通话均长（秒） ", order = 8)
	private String inPerCapitaTime;
	
	@ExcelField(title = "呼入数", order = 9)
	private String outCallCount;
	@ExcelField(title = "接通数）", order = 10)
	private String outSuccCount;
	@ExcelField(title = "接通率", order = 11)
	private String outSuccPercent;
	
	/*@ExcelField(title = "转坐席数", order = 12)
	private String outAgentSuccCount;*/
	
	@ExcelField(title = "通话时长（分钟）", order = 12)
	private String outTotalTime;
	@ExcelField(title = "通话均长（秒）", order = 13)
	private String outPerCapitaTime;
	@ExcelField(title = "呼入数", order = 14)
	private String agentInCallCount;
	@ExcelField(title = "呼出数", order = 15)
	private String agentOutCallCount;
	
	public AgentCallStat(){
		
	}

	public String getAgent() {
		return agent;
	}

	public void setAgent(String agent) {
		this.agent = agent;
	}

	public String getInTotalTime() {
		return inTotalTime;
	}

	public void setInTotalTime(String inTotalTime) {
		this.inTotalTime = inTotalTime;
	}

	public String getInCallCount() {
		return inCallCount;
	}

	public void setInCallCount(String inCallCount) {
		this.inCallCount = inCallCount;
	}

	/*public String getInAgentSuccCount() {
		return inAgentSuccCount;
	}

	public void setInAgentSuccCount(String inAgentSuccCount) {
		this.inAgentSuccCount = inAgentSuccCount;
	}*/

	/*public String getOutAgentSuccCount() {
		return outAgentSuccCount;
	}

	public void setOutAgentSuccCount(String outAgentSuccCount) {
		this.outAgentSuccCount = outAgentSuccCount;
	}
*/
	public String getInPerCapitaTime() {
		return inPerCapitaTime;
	}

	public void setInPerCapitaTime(String inPerCapitaTime) {
		this.inPerCapitaTime = inPerCapitaTime;
	}

	public String getOutTotalTime() {
		return outTotalTime;
	}

	public void setOutTotalTime(String outTotalTime) {
		this.outTotalTime = outTotalTime;
	}

	public String getOutCallCount() {
		return outCallCount;
	}

	public void setOutCallCount(String outCallCount) {
		this.outCallCount = outCallCount;
	}

	public String getOutSuccCount() {
		return outSuccCount;
	}

	public void setOutSuccCount(String outSuccCount) {
		this.outSuccCount = outSuccCount;
	}

	public String getOutSuccPercent() {
		return outSuccPercent;
	}

	public void setOutSuccPercent(String outSuccPercent) {
		this.outSuccPercent = outSuccPercent;
	}

	public String getOutPerCapitaTime() {
		return outPerCapitaTime;
	}

	public void setOutPerCapitaTime(String outPerCapitaTime) {
		this.outPerCapitaTime = outPerCapitaTime;
	}

	public String getInSuccCount() {
		return inSuccCount;
	}

	public void setInSuccCount(String inSuccCount) {
		this.inSuccCount = inSuccCount;
	}

	public String getInSuccPercent() {
		return inSuccPercent;
	}

	public void setInSuccPercent(String inSuccPercent) {
		this.inSuccPercent = inSuccPercent;
	}

    public String getInErrorPercent() {
        return inErrorPercent;
    }

    public void setInErrorPercent(String inErrorPercent) {
        this.inErrorPercent = inErrorPercent;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

	public String getAgentInCallCount() {
		return agentInCallCount;
	}

	public void setAgentInCallCount(String agentInCallCount) {
		this.agentInCallCount = agentInCallCount;
	}

	public String getAgentOutCallCount() {
		return agentOutCallCount;
	}

	public void setAgentOutCallCount(String agentOutCallCount) {
		this.agentOutCallCount = agentOutCallCount;
	}
    
}
