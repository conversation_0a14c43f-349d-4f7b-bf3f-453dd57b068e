package com.yunqu.yc.agent.servlet.order;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.agent.base.AppBaseServlet;

/**
 * 角色servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/orderRole")
public class OrderRoleServlet extends AppBaseServlet {

	private static final long serialVersionUID = 7738338623822931016L;

	/**
	 * 添加角色
	 */
	public EasyResult actionForAddRole(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_ROLE"), "ORDER_ROLE_ID");
			record.setColumns(getJSONObject("orderRole"));
			String sql="select count(1) from " + getTableName("CC_AGENT_ORDER_ROLE") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and ORDER_ROLE_NAME = ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), StringUtils.trimToEmpty(record.getString("ORDER_ROLE_NAME"))})) {
				result.addFail("该角色名称已经存在！");
				return result;
			}
			record.set("ENT_ID", getEntId());
			record.set("BUSI_ORDER_ID", getBusiOrderId());
			record.setPrimaryValues(RandomKit.randomStr());
			this.getQuery().save(record);
			result.setMsg("添加成功！");
		} catch (SQLException e) {
			this.error("添加失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	

	/**
	 * 更新角色
	 */
	public EasyResult actionForUpdateRole(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord(getTableName("CC_AGENT_ORDER_ROLE"), "ORDER_ROLE_ID");
			record.setColumns(getJSONObject("orderRole"));
			String sql="select count(1) from " + getTableName("CC_AGENT_ORDER_ROLE") + " where ENT_ID = ? and BUSI_ORDER_ID = ? and ORDER_ROLE_NAME = ? and ORDER_ROLE_ID <> ?";
			if(this.getQuery().queryForExist(sql,new Object[]{getEntId(), getBusiOrderId(), StringUtils.trimToEmpty(record.getString("ORDER_ROLE_NAME")), record.getString("ORDER_ROLE_ID")})) {
				result.addFail("该角色名称已经存在！");
				return result;
			}
			this.getQuery().update(record);
			result.setMsg("修改成功！");
		} catch (SQLException e) {
			this.error("修改失败！失败原因：" + e.getMessage(), e);
			result.addFail("修改失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
	/**
	 * 删除角色
	 */
	public EasyResult actionForDeleteRole(){
		try {
			EasyQuery query = this.getQuery();
			String roleId = getJsonPara("roleId");
			Object[] param = new Object[]{roleId};
			// TODO 如果角色已经被使用，则不能被删除
//			if(query.queryForExist("select count(1) from " + getTableName("CC_AGENT_ORDER_TMP ")+" where ORDER_ROLE_ID = ?", param)){
//				return EasyResult.fail("该角色已经使用，不能删除！");
//			}
			query.execute("delete from " + getTableName("CC_AGENT_ORDER_ROLE") + " where ORDER_ROLE_ID = ?", param);
			return EasyResult.ok(null,"删除成功!");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("删除失败"+e.getMessage());
		}
	}

}
