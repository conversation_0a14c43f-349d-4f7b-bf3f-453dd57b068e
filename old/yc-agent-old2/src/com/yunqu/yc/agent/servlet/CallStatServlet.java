package com.yunqu.yc.agent.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AppBaseServlet;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.QueryFactory;

/**
 * 大屏监控所有数据查询
 * 话务统计接口
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/callStat/*")
public class CallStatServlet extends AppBaseServlet {

	private static final long serialVersionUID = -1864094683991960092L;

	public EasyResult actionForGetCallStat(){
		EasyQuery query = this.getQuery();
		EasyCalendar cal = EasyCalendar.newInstance();
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		//统计当天各时段话务
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
		String tableName = getStatTableName(ycstatTableInfo.get("TARGET_TABLE_NAME"));
		EasySQL sql = new EasySQL("select ");
		sql.append(" ENT_ID,DATE_ID,HOUR_ID,");
		sql.append(" SUM(case when CALL_TYPE_ID=1 then CALL_COUNT else 0 end) OUT_CALL_COUNT,");//呼出次数
		sql.append(" SUM(case when CALL_TYPE_ID=2 then CALL_COUNT else 0 end) INT_CALL_COUNT");//呼入次数
		sql.append(" from "+tableName);
		sql.append(" where 1=1");
		sql.append(entId," and ENT_ID = ?");
		sql.append(busiOrderId," and BUSI_ORDER_ID = ?");
		sql.append(cal.getDateInt()," and DATE_ID =?");
		sql.append(" group by ENT_ID,DATE_ID,HOUR_ID order by ENT_ID,DATE_ID,HOUR_ID");
		List<JSONObject> dayCallList=null;
		try {
			dayCallList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		List<JSONObject> newDayCallList = new ArrayList<JSONObject>();
		for (int i = 0; i < 24; i++) {
			JSONObject obj = null;
			for (int j = 0; dayCallList!=null&&j < dayCallList.size(); j++) {
				String hour = dayCallList.get(j).getString("HOUR_ID");
				if(i == Integer.parseInt(hour)){
					obj = dayCallList.get(j);
					break;
				}
			}
			if(obj==null){
				obj = new JSONObject();
				obj.put("ENT_ID", entId);
				obj.put("DATE_ID", cal.getDateInt());
				obj.put("OUT_CALL_COUNT", 0);
				obj.put("INT_CALL_COUNT", 0);
				obj.put("HOUR_ID", i);
			}

			newDayCallList.add(obj);
		}

		//统计当月话务
		sql = new EasySQL("select ");
		sql.append(" ENT_ID,MONTH_ID,DATE_ID,");
		sql.append(" SUM(case when CALL_TYPE_ID=1 then CALL_COUNT else 0 end) OUT_CALL_COUNT,");//呼出次数
		sql.append(" SUM(case when CALL_TYPE_ID=2 then CALL_COUNT else 0 end) INT_CALL_COUNT");//呼入次数
		sql.append(" from "+tableName);
		sql.append(" where 1=1");
		sql.append(entId," and ENT_ID = ?");
		sql.append(busiOrderId," and BUSI_ORDER_ID = ?");
		sql.append(cal.getFullMonth()," and MONTH_ID = ?");
		sql.append(" group by ENT_ID,MONTH_ID,DATE_ID order by ENT_ID,MONTH_ID,DATE_ID");
		List<JSONObject> monthCallList=null;
		try {
			monthCallList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}

		String dateInt = cal.getMonthLastDay().getDateInt()+"";
		int lastDayNum = Integer.valueOf(dateInt.substring(dateInt.length()-2, dateInt.length()));
		List<JSONObject> newMonthCallList = new ArrayList<JSONObject>();
		for (int i = 0; i < lastDayNum; i++) {
			JSONObject obj = null;
			for (int j = 0; monthCallList!=null&&j < monthCallList.size(); j++) {
				String date = monthCallList.get(j).getString("DATE_ID");
				if(i+1 == Integer.parseInt(date.substring(date.length()-2, date.length()))){
					obj = monthCallList.get(j);
					break;
				}
			}
			if(obj==null){
				obj = new JSONObject();
				obj.put("ENT_ID", entId);
				obj.put("MONTH_ID", cal.getFullMonth());
				obj.put("DATE_ID", cal.getFullMonth()+(i+1));
				obj.put("OUT_CALL_COUNT", 0);
				obj.put("INT_CALL_COUNT", 0);
			}
			obj.put("day", i+1);

			newMonthCallList.add(obj);

		}

		//当天置忙原因分析
		sql = new EasySQL("select ");
		sql.append(" SUM(t1.NOTREADY_COUNT)  NOTREADY_COUNT,");//总置忙数
		sql.append(" SUM(t1.NOTREADY1_COUNT)  NOTREADY1_COUNT,");//小休次数
		sql.append(" SUM(t1.NOTREADY2_COUNT)  NOTREADY2_COUNT,");//会议次数
		sql.append(" SUM(t1.NOTREADY3_COUNT)  NOTREADY3_COUNT");//培训次数
		tableName = getStatTableName("CC_RPT_AGENT_INDEX");
		sql.append(" from "+tableName+" t1");
		sql.append(" where 1=1");
		sql.append(entId," and t1.ENT_ID = ?");
		sql.append(cal.getDateInt()," and t1.DATE_ID =?");
		JSONObject notReadyStat=null;
		try {
			notReadyStat = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			e.printStackTrace();
		}

		if(notReadyStat==null||StringUtils.isBlank(notReadyStat.getString("NOTREADY_COUNT"))){
			notReadyStat = new JSONObject();
			notReadyStat.put("NOTREADY_COUNT", "0");
			notReadyStat.put("NOTREADY1_COUNT", "0");
			notReadyStat.put("NOTREADY2_COUNT", "0");
			notReadyStat.put("NOTREADY3_COUNT", "0");
		}
		JSONObject result = new JSONObject();
		result.put("dayCallList", newDayCallList);
		result.put("monthCallList", newMonthCallList);
		result.put("notReadyStat", notReadyStat);
		return EasyResult.ok(result, "操作成功");
	}

	public EasyResult actionForCallMonitor() throws SQLException{
		EasyQuery query = this.getQuery();
		EasyCache cache = CacheManager.getMemcache();
		EasyCalendar cal = EasyCalendar.newInstance();
		String entId = getEntId();
		String busiOrderId = getBusiOrderId();
		String groupId = this.getRequest().getParameter("groupId");//获取技能组id
		//班组话务情况
		//获取该企业的所有技能组
		EasySQL sql = new EasySQL("select SKILL_GROUP_ID,SKILL_GROUP_NAME,AGENT_COUNT from "+getTableName("CC_SKILL_GROUP")+" where 1=1 ");
		sql.append(entId, " and ENT_ID = ? ");
		sql.append(this.getBusiOrderId(),"and BUSI_ORDER_ID = ?");
		sql.append(groupId," and SKILL_GROUP_ID=?");//如果传入了技能组id则按照技能组id查询
		sql.append(" order by IDX_ORDER");
		List<JSONObject> groupList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());

		int onlineCount=0;//在线坐席数
		int queueCount=0;//呼叫排队数
//		int agentCount=query.queryForInt("select count(1) from "+getTableName("CC_BUSI_USER")+" where ENT_ID=? and BUSI_ORDER_ID=?", new Object[]{entId,busiOrderId});
		int agentCount=0;//总坐席数
		if(groupList!=null){
			for (int i = 0; i < groupList.size(); i++) {
				JSONObject skillGroup = groupList.get(i);//技能组对象
				JSONObject skillGroupAgent  = cache.get("ICCS_SKILLGROUP_"+skillGroup.getString("SKILL_GROUP_ID"));//技能组监控对象
				if(skillGroupAgent != null){
					skillGroup.putAll(skillGroupAgent);
				}
				skillGroup.put("groupId",skillGroup.remove("SKILL_GROUP_ID"));
				skillGroup.put("skillGroupName",skillGroup.remove("SKILL_GROUP_NAME"));
				skillGroup.put("agentCount",skillGroup.remove("AGENT_COUNT"));
				onlineCount+=skillGroup.getIntValue("logonAgentCount");
				queueCount+=skillGroup.getIntValue("queueCallCount");
				agentCount+=skillGroup.getIntValue("agentCount");
			}
		}

		//坐席实时话务监控
		long thisTime = System.currentTimeMillis()/1000;
		EasySQL sql2 = new EasySQL("select t1.AGENT_ID ,t1.AGENT_NAME,t1.AGENT_STATE,t1.CALL_TYPE,");
		sql2.append("("+thisTime+" - t1.STATE_TIME ) as STATE_TIME");
		sql2.append(" from "+getStatTableName("CC_RPT_AGENT_MONITOR")+" t1");
		sql2.append(" where t1.LOGON_FLAG = 1 ");
		sql2.append(entId, " and t1.ENT_ID = ? " , "-1");
		sql2.append(groupId," and t1.AGENT_ID in (select t1.USER_ACCT from cc_user t1 inner join "
				+getTableName("cc_skill_group_user")+" t2 on t1.USER_ID=t2.USER_ID where 1=1 and t2.SKILL_GROUP_ID=?)");//如果传入技能组id则去查一下坐席的技能组id
		//sql2.append(thisTime - 10*60," and t1.ping_time > ?");
		sql2.append(" order by t1.AGENT_ID ASC ");
		List<JSONObject> agentList = query.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());

		//坐席话务实时情况
		EasySQL sql3 = new EasySQL("select t1.ENT_ID ,count(t1.AGENT_ID) TOTALCOUNT,");
		sql3.append("sum(case when t1.AGENT_STATE='通话中' then 1 else 0 end) TALKAGENTCOUNT,");
		sql3.append("sum(case when t1.AGENT_STATE like '置忙%' then 1 else 0 end) BUSYAGENTCOUNT,");
		sql3.append("sum(case when t1.AGENT_STATE='置闲' then 1 else 0 end) IDLEAGENTCOUNT,");
		sql3.append("sum(case when t1.AGENT_STATE= '话后整理' then 1 else 0 end) WORKNOTREADYAGENTCOUNT");
		sql3.append(" from "+getStatTableName("CC_RPT_AGENT_MONITOR")+" t1");
		sql3.append(" where t1.LOGON_FLAG = 1 ");
		sql3.append(entId, " and t1.ENT_ID = ? " , "-1");
		sql2.append(groupId," and t1.AGENT_ID in (select t1.USER_ACCT from cc_user t1 inner join "
				+getTableName("cc_skill_group_user")+" t2 on t1.USER_ID=t2.USER_ID where 1=1 and t2.SKILL_GROUP_ID=?)");//如果传入技能组id则去查一下坐席的技能组id
		//sql3.append(thisTime - 10*60," and t1.ping_time > ?");
		sql3.append("group by t1.ENT_ID order by t1.ENT_ID ASC ");
		JSONObject entMonitor = query.queryForRow(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
		if(entMonitor==null||StringUtils.isBlank(entMonitor.getString("TOTALCOUNT"))){
			entMonitor = new JSONObject();
			entMonitor.put("TOTALCOUNT", "0");
			entMonitor.put("TALKAGENTCOUNT", "0");
			entMonitor.put("BUSYAGENTCOUNT", "0");
			entMonitor.put("IDLEAGENTCOUNT", "0");
			entMonitor.put("WORKNOTREADYAGENTCOUNT", "0");
		}

		//当天总体话务分析
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("cc_rpt_noanswer_stat");
		String tableName = getStatTableName(ycstatTableInfo.get("TARGET_TABLE_NAME"));
		EasySQL noanswerSql = new EasySQL("select sum(NOANSWER_COUNT) from "+tableName+" where 1=1 ");
		noanswerSql.append(entId," and ENT_ID=?");
		noanswerSql.append(cal.getDateInt()," and DATE_ID=?");
		noanswerSql.append(groupId," and GROUP_ID=?");//如果传入技能组id的话
		int notanswerCount = query.queryForInt(noanswerSql.getSQL(),noanswerSql.getParams());

		ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
		tableName = getStatTableName(ycstatTableInfo.get("TARGET_TABLE_NAME"));
		sql = new EasySQL("select ");
		sql.append(" SUM(CALL_COUNT) CALL_COUNT,");//呼出次数
		sql.append(" SUM(TOTAL_TIME) TOTAL_TIME");//通话时长
		sql.append(" from "+tableName);
		sql.append(" where 1=1");
		sql.append(entId," and ENT_ID = ?");
		sql.append(busiOrderId," and BUSI_ORDER_ID = ?");
		sql.append(cal.getDateInt()," and DATE_ID =?");
		sql.append(groupId," and GROUP_ID=?");//如果传入技能组id的话
		JSONObject dayCall = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

		JSONObject allCall = new JSONObject();
		allCall.put("notAnswerCount", notanswerCount);//未接来电数
		allCall.put("callCount", dayCall.getIntValue("CALL_COUNT"));//通话次数
		allCall.put("callTime", dayCall.getIntValue("TOTAL_TIME"));//通话时长
		allCall.put("onlineCount", onlineCount);//在线坐席数
		allCall.put("agentCount", agentCount);//坐席总数
		allCall.put("queueCount", queueCount);//排队数

		JSONObject result = new JSONObject();
		result.put("groupList", groupList);//班组话务情况
		result.put("agentList", agentList);//坐席实时话务监控
		result.put("entMonitor", entMonitor);//坐席话务实时情况
		result.put("allCall", allCall);//总体话务分析
		return EasyResult.ok(result, "操作成功");
	}
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	private Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME from "+getStatTableName("cc_stat_table_info")+" where TABLE_ID = ?  ";
			tabInfo = this.getQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
		}
		return tabInfo;
	}
	/**
	 * 获取ycStat的表名
	 */
	private String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}

	@Override
	public EasyQuery getQuery() {
		return QueryFactory.getReadQuery();
	}
}
