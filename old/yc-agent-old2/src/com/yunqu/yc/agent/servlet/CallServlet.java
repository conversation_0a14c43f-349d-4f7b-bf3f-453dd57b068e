package com.yunqu.yc.agent.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import com.yunqu.yc.agent.base.Constants;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AppBaseServlet;
import com.yunqu.yc.agent.utils.FileCheckUtils;
import com.yunqu.yc.agent.utils.PhoneCryptor;

/**
 * 名单表 Servlet类
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/call/*")
@MultipartConfig
public class CallServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 系统添加黑名单
	 * @return
	 */
	public EasyResult actionForAddBlack(){
		EasyResult result = new EasyResult();
		try {
			if(this.getQuery().queryForExist("select count(1) from CC_BLACK_LIST where ENT_ID = ? and PHONENUM = ?", new Object[]{getEntId(),getJsonPara("black.PHONENUM")})){
				result.addFail("该号码已经存在！");
				return result;
			}
			EasyRecord record = new EasyRecord("CC_BLACK_LIST", "ENT_ID", "PHONENUM");
			JSONObject jsonObject=getJSONObject();
			String telNum = jsonObject.getString("telNum");
			if(telNum.startsWith("0") && telNum.length() == 12){
				telNum = telNum.substring(1);
			}
			record.set("PHONENUM", telNum);
			record.set("MEMO", jsonObject.getString("memo"));
			record.set("TYPE", jsonObject.getOrDefault("type", 1));
			record.set("ENT_ID", getEntId());
			record.set("CREATOR", this.getUserPrincipal().getUserName());
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			setIntType(record, "LIST_CLASS", "TYPE", "STATE");
			getQuery().save(record);
			cache.put("BLACK_LIST_"+record.getString("ENT_ID")+"_"+record.getString("PHONENUM"), "Y",7200);
			result.setMsg("添加成功！");
		} catch (SQLException e) {
			e.printStackTrace();
			this.error("添加失败，原因："+e.getMessage(),e);
			result.addFail("添加失败:已经重复添加！");
		}
		return result;
	}
	
	/**
	 * 修改黑名单
	 * @return
	 */
	public EasyResult actionForUpdateBlack(){
		EasyResult result = new EasyResult();
		EasyRecord record = new EasyRecord("CC_BLACK_LIST", "ENT_ID", "PHONENUM");
		record.setColumns(getJSONObject("black"));
		JSONObject jsonObject=getJSONObject();
		String telNum = jsonObject.getString("telNum");
		if(telNum.startsWith("0") && telNum.length() == 12){
			telNum = telNum.substring(1);
		}
		record.set("PHONENUM", telNum);
		record.set("MEMO", jsonObject.getString("memo"));
		record.set("ENT_ID",getEntId());
		try {
			setIntType(record, "LIST_CLASS", "TYPE", "STATE");
			getQuery().update(record);
			cache.put("BLACK_LIST_"+record.getString("ENT_ID")+"_"+record.getString("PHONENUM"), "Y",7200);
			result.setMsg("修改成功！");
		} catch (SQLException e) {
			this.error("修改失败，原因："+e.getMessage(),e);
			result.addFail("修改失败！");
		}
		return result;
	}
	
	/**
	 * 删除黑名单
	 * @return
	 */
	public EasyResult actionForDeleteBlack(){
		EasyResult result = new EasyResult();
		try {
			EasyRecord record = new EasyRecord("CC_BLACK_LIST", "ENT_ID", "PHONENUM");
			record.setPrimaryValues(getEntId(),getJsonPara("phonenum"));
			this.getQuery().deleteById(record);
			cache.delete("BLACK_LIST_"+record.getString("ENT_ID")+"_"+record.getString("PHONENUM"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 下载话机模板
	 */
	public void actionForDownLoadTemp(){
		try {
			List<String> list=new ArrayList<String>();
			List<ExcelHeaderStyle> headers = new ArrayList<ExcelHeaderStyle>();
			ExcelHeaderStyle phoneStyle = new ExcelHeaderStyle();
			phoneStyle.setData("号码");
			phoneStyle.setWidth(3200);
			headers.add(phoneStyle);
			
			ExcelHeaderStyle memoStyle = new ExcelHeaderStyle();
			memoStyle.setData("号码备注");
			memoStyle.setWidth(4200);
			headers.add(memoStyle);
			
			File tempFile=FileKit.createTempFile("黑名单模板.xlsx");
			ExcelUtils.getInstance().exportObjects2Excel(list,headers,tempFile.getAbsolutePath());
			renderFile(tempFile);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 上传数据
	 * @throws IOException
	 * @throws ServletException
	 * @throws EncryptedDocumentException
	 * @throws InvalidFormatException
	 */
	public void actionForUpload() throws IOException, ServletException, EncryptedDocumentException, InvalidFormatException{
		Part part=getFile("file");
		if(!FileCheckUtils.isSafeFile(part,"xls,xlsx")){
			renderJson(EasyResult.fail("上传文件类型不正确，只支持 xls,xlsx !"));
			return;
		}
		Workbook workbook = WorkbookFactory.create(part.getInputStream());
		List<List<String>> list = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);
        int maxLine =sheet.getLastRowNum();
        if(maxLine < 1){
			renderJson(EasyResult.fail("上传的数据为空!"));
			return;
		}else if(maxLine > 8000){
			renderJson(EasyResult.fail("上传的数据过大，请分批导入，每批次少于8000条数据！"));
			return;
		}
        for (int ii = 0; ii <= maxLine; ii++) {
            List<String> rows = new ArrayList<>();
            Row row = sheet.getRow(ii);
            if(row!=null){
            	for(int j=0;j<row.getLastCellNum();j++){
            		Cell cell=row.getCell(j);
            		if(cell!=null){
            			String val = Utils.getCellValue(cell);
            			if(StringUtils.isBlank(val)){
            				rows.add("");
            			}else{
            				rows.add(val);
            			}
            		}else{
            			rows.add("");
            		}
            	}
            	for(int i = rows.size(); i < 2; i++){
            		rows.add("");
            	}
            	list.add(rows);
            }
	    }

		if(list==null||list.size()<=1){
			renderJson(EasyResult.fail("读取数据为空!"));
			return;
		}
        JSONObject data = new JSONObject();
        data.put("count", list.size()-1);
        
        JSONArray array=new JSONArray();
		for(int j=1;j<list.size();j++){
			List<String> strs=list.get(j);
			if(StringUtils.isBlank(strs.get(0))){
				continue;
			}
			JSONObject jsonObject=new JSONObject(true);
			jsonObject.put("PHONENUM", strs.get(0));
			jsonObject.put("MEMO", strs.get(1));
			array.add(jsonObject);
		}
		data.put("listJson", JSONArray.toJSONString(array));
		renderJson(EasyResult.ok(data));
	}
	
	/**
	 * 保存批量数据
	 * @throws SQLException
	 */
	public EasyResult actionForSaveBatch() throws SQLException{
		EasyResult result = new EasyResult();
		EasyQuery query = this.getQuery();
		JSONObject jsonObject = this.getJSONObject();
		int errorCount = 0;
		String entId = getEntId();
		try {
			String jsonStr = jsonObject.getString("jsonStr");
			JSONArray list = JSONArray.parseArray(jsonStr);
			if(list==null||list.size()<=0){
				renderJson(EasyResult.fail("读取数据为空!"));
				result.addFail("读取数据为空!");
				return result;
			}
			EasyRecord record=new EasyRecord("CC_BLACK_LIST","ENT_ID","PHONENUM");
			record.set("TYPE", 1);
			record.set("CREATOR", this.getUserPrincipal().getUserName());
			record.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			if(list.size()>0){
				List<Map<String, String>> phoneList = getQuery().queryForList("select PHONENUM from CC_BLACK_LIST where ENT_ID = ?", new Object[]{entId}, new MapRowMapperImpl());
				int size = list.size();
				HashSet<String> phoneSet = null;
				if(phoneList != null && phoneList.size() > 0){
					size += phoneList.size();
					phoneSet = new HashSet<>(size);
					Iterator<Map<String, String>> iterator = phoneList.iterator();
					while(iterator.hasNext()){
						phoneSet.add(iterator.next().get("PHONENUM").trim());
					}
				}else{
					phoneSet = new HashSet<>(size);
				}
				for (int i=0;i<list.size();i++) {
					JSONObject row=list.getJSONObject(i);
					String phoneNum =StringUtils.trimToEmpty(row.getString("PHONENUM"));
					if(phoneNum.startsWith("0") && phoneNum.length() == 12){
						phoneNum = phoneNum.substring(1);
					}
					if(phoneSet.add(phoneNum)){
						record.setPrimaryValues(entId,phoneNum);
						record.set("MEMO", row.getString("MEMO"));
						try{
							setIntType(record, "LIST_CLASS", "TYPE", "STATE");
							query.save(record);
						}catch(Exception ex){
							this.error(ex.getMessage(), ex);
							errorCount ++;
						}
					}else{
						errorCount ++;
					}
					//每五百条提交一次事务
					if((i+1) != errorCount && (i-errorCount+1)%500 == 0){
						
					}
				}
			}
			
			result.setMsg((list.size() - errorCount) + "条数据添加成功，" + errorCount +"条数据添加失败！");
		} catch (Exception e) {
			this.error("添加数据失败！失败原因：" + e.getMessage(), e);
			result.addFail("添加数据失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	/**
	 * 根据objId获取对应的录音文件
	 * @return
	 */
	public EasyResult actionForGetRecordFile(){
		EasyResult result = new EasyResult();
		try {
			List<JSONObject> queryForList = null;
			String serialId=getJsonPara("serialId");//录音id
			String objId=getJsonPara("objId");//任务对象Id
			String type =Constants.RECORD_TYPE;
			EasyQuery query = this.getQuery();

			if(StringUtils.isNotBlank(objId)){
				queryForList = query.queryForList("select * from " + getTableName("CC_CALL_RECORD") + " where OBJ_ID = ? order by SERIAL_ID", new Object[]{objId},new JSONMapperImpl());
			}else {
				queryForList = query.queryForList("select * from " + getTableName("CC_CALL_RECORD") + " where SERIAL_ID = ?", new Object[]{serialId},new JSONMapperImpl());
			}
			queryForList = PhoneCryptor.getInstance().decrypt(queryForList, new String[]{"CALLER","CALLED","CUST_PHONE"}, false);
			//判断该对象是否存在录音文件
			if(queryForList != null && queryForList.size() > 0){
				JSONObject data = null;
				String id = "";
				String recordFile = "";
				int callMode=1;
				for(int index = 0;index<queryForList.size();index++){
					data = queryForList.get(index);
					callMode=data.getIntValue("CALL_MODE");
					if(1==callMode) {
						recordFile=StringUtils.trim(data.getString("RECORD_FILE"));
					}else {
						recordFile=StringUtils.trim(data.getString("VIDEO_FILE"));
					}
					if(type.equals(Constants.ROBOT_TYPE)){//机器人
						id = data.getString("OBJ_ID");
					}else{
						id = data.getString("SERIAL_ID");
					}
					if(StringUtils.isNotBlank(recordFile)){
						break;
					}
				}

				if(StringUtils.isNotBlank(recordFile)){
					String prefix="";
					if(1==callMode) {
						  prefix = getRecordUrlPrefix();
					}else {
						  prefix = "/data/fileserver/webrtc/volterecord/";
					}
					 if(prefix.startsWith("http")){//http全路径
						 recordFile = prefix + recordFile;
					 }else{
						 String prefixExt = getRecordUrlPrefixExt();
						 recordFile = "/yc-agent/record/play?p="+prefix+"&i="+id+"&t="+type+"&pext="+prefixExt;//t=1通话，t=2漏话，t=3未接,t=4留言,t,5=机器人
					 }
				}

				result.setUrl(recordFile);
				result.setSuccess(data, "获取录音文件成功");
			}else {
				result.addFail("没有通话录音文件！");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail("获取录音文件失败！失败原因：" + e.getMessage());
		}
		return result;
	}

	@Override
	protected String getResId() {
		return null;
	}
}
