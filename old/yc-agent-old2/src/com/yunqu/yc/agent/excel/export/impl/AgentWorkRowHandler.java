package com.yunqu.yc.agent.excel.export.impl;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.excel.export.BaseRowHandler;

/**
 * 坐席工作日志
 * <AUTHOR>
 * @date 2024年10月9日i下午2:01:33
 *
 */
public class AgentWorkRowHandler extends BaseRowHandler{
	
	
	private final Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);
	
	@Override
	public String getName() {
		return "坐席工作日志";
	}

	@Override
	public List<String> getHead() {
		List<String> headers = new ArrayList<String>();
		headers.add("操作时间");
		headers.add("坐席");
		headers.add("操作");
		headers.add("操作说明");
		return headers;
	}

	@Override
	public List<Object> excute(Map<String,String> rs) {
		List<Object> list = new ArrayList<Object>();
		try {
			list.add(rs.get("BEGIN_TIME"));
			list.add(rs.get("AGENT_ID")+(StringUtils.isBlank(rs.get("AGENT_NAME"))?"":"("+rs.get("AGENT_NAME")+")"));
			list.add(rs.get("EVENT_NAME"));
			list.add(StringUtils.isBlank(rs.get("REASON_NAME"))?"-":rs.get("REASON_NAME"));
			
		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}
		return list;
	}
	
	@Override
	public void start() {
		
	}

	@Override
	public void done() {
		
	}

	@Override
	public RowWriteHandler getRowWriteHandler() {
		RowWriteHandler rowWriteHandler = new RowWriteHandler() {

			private Map<String, Object> dictMap;

			@Override
			public void beforeRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Integer rowIndex,
					Integer relativeRowIndex, Boolean isHead) {
			}

			@Override
			public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
					Integer relativeRowIndex, Boolean isHead) {
			}
			@Override
			@SuppressWarnings("unchecked")
			public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
					Integer relativeRowIndex, Boolean isHead) {
				if (!isHead && dictMap != null) {
					for (Entry<String, Object> dict : dictMap.entrySet()) {
						Map<String, String> map = (Map<String, String>) dict.getValue();
						String value = row.getCell(Integer.parseInt(dict.getKey())).getStringCellValue();
						row.getCell(Integer.parseInt(dict.getKey())).setCellValue(map.get(value));
					}
				}

			}
			public Map<String, Object> getDictMap() {
				return dictMap;
			}

			public void setDictMap(Map<String, Object> dictMap) {
				this.dictMap = dictMap;
			}
		};
		return rowWriteHandler;
	}

	@Override
	public AbstractColumnWidthStyleStrategy getColumnWidthStyleStrategy() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
		// 头的策略
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		// 背景设置为红色
		headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

		// 字体
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setFontName("微软雅黑");// 设置字体名字
		headWriteFont.setFontHeightInPoints((short) 10);// 设置字体大小
		headWriteFont.setBold(true);// 字体加粗
		headWriteCellStyle.setWriteFont(headWriteFont); // 在样式用应用设置的字体;
		headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
		headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headWriteCellStyle.setShrinkToFit(true);
		// 内容的策略
		WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
		// 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
		// 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
		// 文字
		WriteFont contentWriteFont = new WriteFont();
		// 字体大小
		contentWriteFont.setFontHeightInPoints((short) 10);
		contentWriteFont.setFontName("微软雅黑");// 设置字体名字
		contentWriteCellStyle.setWriteFont(contentWriteFont);
		// 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
		return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
	}

}
