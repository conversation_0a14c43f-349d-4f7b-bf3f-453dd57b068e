<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>编辑模板</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" data-mars="orderTempV1.record"  method="post" data-pk="${param.tempId}" data-mars-prefix="orderTemp.">
				  <input type="hidden" name="orderTemp.TMP_ID" class="form-control input-sm" value="${param.tempId}">
				   <table class="table table-edit table-vzebra">
	                    <tbody>
		                     <tr>
			                        <td class="required" width="120px">工单模板名称</td>
			                        <td><input data-placement="bottom" type="text" name="orderTemp.TMP_NAME" class="form-control input-sm" data-rules="required"></td>
			                 </tr>
				             <tr>
				                   <td>工单是否需要审批</td>
				                   <td>
			                            <label class="radio radio-inline radio-info">
									    	<input type="radio" name="orderTemp.APPROVAL_FLAG" value="0" onclick="OrderTempEdit.onClickFlag()"> <span>无需审批</span>
										</label>
										<label class="radio radio-inline radio-info">
										    <input type="radio" name="orderTemp.APPROVAL_FLAG" value="1" onclick="OrderTempEdit.onClickFlag()" <c:if test="${empty param.tempId}">checked="checked"</c:if>> <span>需要经办人审批</span>
										</label>
				                   </td>
				             </tr>
		                     <tr id="groupId">
			                        <td class="required">工单审批经办组</td>
			                        <td>
			                        	<select class="form-control input-sm" name="orderTemp.ORDER_GROUP_ID" data-mars="groupV1.groupDict">
			                        		<option value="">请选择</option>
					      				</select>
					      			</td>
			                 </tr>
		                     <tr>
			                        <td class="required" width="100px">排序</td>
			                        <td><input type="number" name="orderTemp.TMP_ORDER" class="form-control input-sm" data-rules="required" value="0"></td>
			                 </tr>
			                 <c:if test="${!empty param.tempId}">
				             <tr>
				                   <td>使用状态</td>
				                   <td>
			                            <label class="radio-inline">
									    	<input type="radio" name="orderTemp.TMP_STATE" value="0"> 正常
										</label>
										<label class="radio-inline" style="margin-left: 35px;">
										    <input type="radio" name="orderTemp.TMP_STATE" value="1"> 停用
										</label>
				                   </td>
				             </tr>
				             </c:if>
	                    </tbody>
	               </table>
				  <table class="table table-auto table-bordered table-hover table-condensed" data-mars="orderTempV1.dataList" data-template = "data-template" data-container="data-List">
				  		<thead>
				  			<tr>
				  				<th>启用</th>
				  				<th>名称</th>
				  				<th>类型</th>
				  				<th>必填</th>
				  				<th>取值</th>
				  				<th>列表显示</th>
				  				<th>查询条件</th>
				  			</tr>
				  		</thead>
                        <tbody id="data-List">
                        </tbody>
	                  </table>
                       	 <script id="data-template" type="text/x-jsrender">
							{{for data}}
								<tr>
			                        <td class="text-c">
			                        	<input type="checkbox" name="DATA{{:#index + 1}}.inuse" value="1"/>
			                        </td>
			                        <td width="120px;">
						                 <input name="orderTemp.DATA{{:#index + 1}}" class="form-control input-sm" type="text"/>
			                        </td>
			                        <td>
			                        	<select class="form-control input-sm" name="DATA{{:#index + 1}}.dataType" onchange="OrderTempEdit.onChangeType(this,'DATA{{:#index + 1}}')">
			                        		<option value="text">文本</option>
			                        		<option value="date">日期</option>
			                        		<option value="time">日期时间</option>
			                        		<option value="number">数字</option>
			                        		<option value="select">下拉框</option>
			                        		<option value="radio">单选框</option>
			                        		<option value="checkbox">多选框</option>
			                        		<option value="ivr">ivr确认</option>
			                        		<option value="called">被叫回显</option>
			                        		<option value="caller">主叫回显</option>
			                        		<option value="origCalled">原被叫回显</option>
			                        		<option value="origCaller">原主叫回显</option>
			                        	</select>
			                        </td>
			                        <td class="text-c">
			                        	<input type="checkbox" name="DATA{{:#index + 1}}.required" value="1"/>
			                        </td>
			                        <td>
			                        	<input id="DATA{{:#index + 1}}origCalled" type="text" name="DATA{{:#index + 1}}.origCalled" class="form-control input-sm DATA{{:#index + 1}}" placeholder="默认值"/>
			                        	<input id="DATA{{:#index + 1}}origCaller" type="text" name="DATA{{:#index + 1}}.origCaller" class="form-control input-sm DATA{{:#index + 1}}" placeholder="默认值"/>
			                        	<input id="DATA{{:#index + 1}}called" type="text" name="DATA{{:#index + 1}}.called" class="form-control input-sm DATA{{:#index + 1}}" placeholder="默认值"/>
			                        	<input id="DATA{{:#index + 1}}caller" type="text" name="DATA{{:#index + 1}}.caller" class="form-control input-sm DATA{{:#index + 1}}" placeholder="默认值"/>
			                        	<input id="DATA{{:#index + 1}}text" type="text" name="DATA{{:#index + 1}}.text" class="form-control input-sm DATA{{:#index + 1}}" placeholder="默认值"/>
			                        	<input id="DATA{{:#index + 1}}ivr" type="text" name="DATA{{:#index + 1}}.ivr" class="form-control input-sm DATA{{:#index + 1}}" placeholder="ivr流程字冠"/>
			                        	<input id="DATA{{:#index + 1}}date" name="DATA{{:#index + 1}}.date" class="form-control input-sm DATA{{:#index + 1}} Wdate" placeholder="默认当前日期" onClick="WdatePicker({})">
			                        	<input id="DATA{{:#index + 1}}time" name="DATA{{:#index + 1}}.time" class="form-control input-sm DATA{{:#index + 1}} Wdate" placeholder="默认当前时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
										<input id="DATA{{:#index + 1}}number" name="DATA{{:#index + 1}}.number" class="form-control input-sm DATA{{:#index + 1}}" type="number" placeholder="默认值">
										<div class="input-group input-group-sm DATA{{:#index + 1}}" id="DATA{{:#index + 1}}select">
											<input type="text" name="DATA{{:#index + 1}}.select" class="form-control input-sm" id="DATA{{:#index + 1}}option" readonly="readonly"/>
											<span class="input-group-addon" onclick="OrderTempEdit.addOption(this)" style="cursor: pointer;">添加选项</span>
										</div>
			                        </td>
			                        <td class="text-c">
			                        	<input type="checkbox" name="DATA{{:#index + 1}}.show" value="1"/>
			                        </td>
			                        <td class="text-c">
			                        	<input type="checkbox" name="DATA{{:#index + 1}}.query" value="1"/>
			                        </td>
		                     </tr>	
							{{/for}}         
					    </script>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="OrderTempEdit.ajaxSubmitForm()"> 保存 </button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	jQuery.namespace("OrderTempEdit");
	
	OrderTempEdit.tempId='${param.tempId}';
	
	$(function(){
		$("#easyform").render({success:function(result){
			for(var i = 1; i <=20; i++){
				$(".DATA"+i).each(function(){
					$(this).hide();
				});
				$("#DATA"+i+"text").show();
			}
			var data=result['orderTempV1.record'].data;
			if(data!=null&&data!='{}'){
				if(data['APPROVAL_FLAG'] == 0) $("#groupId").hide();
				for(var o in data){
					var val=data[o];
					if(val!=''&&val.indexOf("{")>=0){
						var json=eval("("+data[o]+")");
						$("#easyform input[name='orderTemp."+o+"']").val(json.name);
						var inuse=json.state;
						if(inuse==1){
							$("#easyform input[name='"+o+".inuse']").attr("checked","true");
						}else{
							$("#easyform input[name='"+o+".inuse']").removeAttr("checked");
						}
						
						var required = json.required;
						if(required==1){
							$("#easyform input[name='"+o+".required']").attr("checked","true");
						}else{
							$("#easyform input[name='"+o+".required']").removeAttr("checked");
						}
						
						var show = json.show;
						if(show==1){
							$("#easyform input[name='"+o+".show']").attr("checked","true");
						}else{
							$("#easyform input[name='"+o+".show']").removeAttr("checked");
						}
						
						var query = json.query;
						if(query==1){
							$("#easyform input[name='"+o+".query']").attr("checked","true");
						}else{
							$("#easyform input[name='"+o+".query']").removeAttr("checked");
						}
						
						var dataType=json.dataType;
						$("#"+o+"text").hide();
						$("#easyform select[name='"+o+".dataType']").val(dataType);
						if("select" == dataType || "radio" == dataType || "checkbox" == dataType){
							$("#"+o+"select").show();
							$("#"+o+"a").show();
							$("#"+o+"option").val(json.value);
						}else if("date" == dataType){
							$("#"+o+"date").show();
							$("#"+o+"date").val(json.value);
						}else if("time" == dataType){
							$("#"+o+"time").show();
							$("#"+o+"time").val(json.value);
						}else if("number" == dataType){
							$("#"+o+"number").show();
							$("#"+o+"number").val(json.value);
						}else if("ivr" == dataType){
							$("#"+o+"ivr").show();
							$("#"+o+"ivr").val(json.value);
						}else{
							$("#"+o+"text").show();
							$("#"+o+"text").val(json.value);
						}
					}
				}
			}
			$("#easyform input[type='checkbox']:checked").each(function(){
				$(this).parents("tr").css("background-color","#DATA4eded");
			});
		}});
		$("#easyform input[type='checkbox']").click(function(){
			if($(this).is(":checked")){
				$(this).parents("tr").css("background-color","#DATA4eded");
			}else{
				$(this).parents("tr").css("background-color","#fff");
			}
		});
	});
	OrderTempEdit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 if(OrderTempEdit.tempId==''){
				 OrderTempEdit.insertData(); 
			 }else{
				 OrderTempEdit.updateData(); 
			 }
		 };
	}
	OrderTempEdit.insertData = function() {
			var data = form.getJSONObject("#easyform");
			if(data['orderTemp.APPROVAL_FLAG'] == 1 ){
				var groupId = data['orderTemp.ORDER_GROUP_ID'];
				if(!groupId || groupId == undefined || groupId == ''){
					layer.open({
						  title: '提示'
						  ,content: '请先选择经办组！'
					});     
					return;
				}
			}
			 ajax.remoteCall("${ctxPath}/servlet/orderTempV1?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:1200},function(){
						popup.layerClose("#easyform");
						OrderTemp.loadData();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			  }
			); 
		}
	OrderTempEdit.updateData = function(){
		var data = form.getJSONObject("#easyform");
		if(data['orderTemp.APPROVAL_FLAG'] == 1 ){
			var groupId = data['orderTemp.ORDER_GROUP_ID'];
			if(!groupId || groupId == undefined || groupId == ''){
				layer.open({
					  title: '提示'
					  ,content: '请先选择经办组！'
				});   
				return;
			}
		}
		ajax.remoteCall("${ctxPath}/servlet/orderTempV1?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					popup.layerClose("#easyform");
					OrderTemp.loadData();
				});
			}else{
			      layer.alert(result.msg);
			}
		  }
		);
	}
	
	OrderTempEdit.onChangeType = function(e,val){
		$("."+val).each(function(){
			$(this).hide();
			$(this).val("");
		});
		if("select" == $(e).val() || "radio" == $(e).val() || "checkbox" == $(e).val()){
			$("#"+val+"select").show();
		}else{
			$("#"+val+$(e).val()).show();
		}
	}
	
	var el;
	OrderTempEdit.addOption = function(e){
		el = e;
		var val = $(el).parent().find("input").val();
		popup.layerShow({type:1,title:'编辑模板',offset:'20px',area:['320px','420px']},"${ctxPath}/pages/order/template/option-list.jsp",{val:val});
	}
	
	OrderTempEdit.callbackOption = function(data){
		$(el).parent().find("input").val(data);
	}
	OrderTempEdit.onClickFlag = function(){
		var val = $("input[name='orderTemp.APPROVAL_FLAG']:checked").val();
		if(val == 0){
			$("#groupId").hide();
		}else {
			$("#groupId").show();
		}
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>