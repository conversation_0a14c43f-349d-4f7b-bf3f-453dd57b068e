<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择经办组</title>
	<style>
		#dataList2 tr{cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" id="orderGroupSelectForm"  data-temp-id="${param.tempId }" style="margin-bottom: 65px">
             	<div class="ibox">
	              	<div class="ibox-content" style="padding: 0px">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="6" data-container="dataList2" data-template="list-template2" data-auto-fill="5" data-mars="group.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">选择</th>
								      <th>经办组名称</th>
								      <th>创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList2">
                             </tbody>
		                 </table>
                        	 <script id="list-template2" type="text/x-jsrender">
								   {{for  list}}
										<tr class="{{:ORDER_GROUP_ID}}">
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" {{if CHECKED}}checked{{/if}} {{hasCheck:ORDER_GROUP_ID}} data-name="{{:EXAM_GROUP_NAME}}" name="orderGroupIds" value="{{:ORDER_GROUP_ID}}"/><span></span></label></td>
											<td>{{:EXAM_GROUP_NAME}}</td>                                         
											<td>{{cutText:CREATE_TIME 12 ''}}</td>                                         
									    </tr>
								    {{/for}}					         
							 </script>
	              	</div> 
	              	<div class="layer-foot text-c" style="position: fixed;">
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="OrderGroupSelect.saveData()">确定</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("OrderGroupSelect");
		
		OrderGroupSelect.tempId = "${param.tempId}";
		OrderGroupSelect.oldGroupIds = "";
		$(function(){
			$("#orderGroupSelectForm").render({success:function(result){
				$("#dataList2 tr").on('click',function(event){
					if(event.target.type == 'radio'|| event.target.type == 'checkbox') { event.stopPropagation(); return;}
					var val=$(this).find("input").prop("checked");
					$(this).find("input").prop("checked",!val);
				});
				
				if(result["group.list"]){
					var list = result["group.list"].data;
					for (var i = 0; i < list.length; i++) {
						if(list[i].CHECKED){
							OrderGroupSelect.oldGroupIds += list[i].ORDER_GROUP_ID + ",";
						}
					}
					OrderGroupSelect.oldGroupIds.substring(0,ids.length-1);
				}
			}});
		});
		var ids = '${param.ids }';
		$.views.converters("hasCheck", function(val) {
			if(ids != ''){
				var arr = ids.split(',');
				for(var i in arr){
					if(val == arr[i]){
						return "checked";
					}
				}
			}
		});
		
		OrderGroupSelect.saveData=function(){
			var length=$("#orderGroupSelectForm input[type='checkbox']:checked").length;
			if(length==0){
				layer.msg("请至少选择一个经办组!");
				return;
			}
			
			var arr =  new Array();
			$("#orderGroupSelectForm input[name='orderGroupIds']:checked").each(function(index){
				var data ={id:$(this).val(),name:$(this).data("name")};
				arr.push(data);
			});
			parent.changeSkillGroup(arr);
			popup.layerClose();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>