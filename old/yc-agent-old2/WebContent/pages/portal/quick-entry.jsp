<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>快速入口管理</title>
	<style type="text/css">
		.item {
		    background-color: #F6F6F6;
		    padding: 10px 20px;
		    padding-right: 40px;
		    border-radius: 40px;
		    display: inline-block;
		    margin: 10px;
		    position: relative;
		    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%);
		    -moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		    -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%);
		    -moz-user-select: none;
		    -webkit-user-select: none;
		    -ms-user-select: none;
		    -khtml-user-select: none;
		    user-select: none;
		}
		
		.reduce {
		    border-radius: 50%;
		    background-color: #F04143;
		    width: 20px;
		    height: 20px;
		    line-height: 20px;
		    position: absolute;
		    right: 10px;
		    top: 10px;
		    text-align: center;
		    cursor: pointer;
		}
		
		.add {
			border-radius: 50%;
			background-color: #4C89FB;
			width: 20px;
			height: 20px;
			line-height: 20px;
			position: absolute;
			right: 10px;
			top: 10px;
			text-align: center;
			cursor: pointer;
		}
		
		.layui-icon-subtraction,
		.layui-icon-addition {
			color: #fff;
		}
		
		.ibox-content {
			min-height: 800px;
			overflow: auto;
		}
		.custom-input {
			margin: 10px 0px 10px 20px !important;
		}
		
		
		
  ::-webkit-scrollbar {
   width: 8px;
   height: 8px;
   background: transparent;
  }
  ::-webkit-scrollbar-track {
   background: transparent;
  }
  ::-webkit-scrollbar-thumb {
   border-radius: 8px;
   background-color: #C1C1C1;
  }
  ::-webkit-scrollbar-thumb:hover {
   background-color: #A8A8A8;
  }
  
       
		
		
	</style>
	<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
       <form name="searchForm" class="form-inline" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix" id="divId">
						 <div class="form-group">
	             		       	<h5 >快速入口管理</h5>
								<div class="input-group input-group-sm">
							      <span class="input-group-addon" >名称</span>	
								  <input type="text" id="resName" name="resName" class="form-control input-sm" style="width:200px">
							   	</div>
							    <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="quickEntry.loadData();"><span class="glyphicon glyphicon-search" >查询</span></button>
								</div>
								<div class="input-group ">
									<button type="button" class="btn btn-sm btn-default" onclick="quickEntry.reset()"><span class="glyphicon glyphicon-repeat" ></span> 重置</button>
								</div>
								<div class="input-group ">
									<button type="button" class="btn btn-sm btn-default" onclick="quickEntry.showAddItem(this)"><span class="glyphicon glyphicon-plus" >添加</span> </button>
								</div>
						</div>
             	    </div>  
	              <div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10"  data-mars="QuickEntryDao.getQuickEntryList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">序号</th>
								      <th class="text-c">标题</th>
								      <th class="text-c">排序</th>
								      <th class="text-c">状态</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td  class="text-c">{{:#index+1}}</td>                                         
											<td  class="text-c">{{:ENTRY_NAME}}</td>                             
											<td  class="text-c">
												<a href="javascript:void(0)" onclick="quickEntry.editSortShow('{{:ENTRY_ID}}','{{:ENTRY_IDX}}')">{{:ENTRY_IDX}}</a>
											</td> 
											<td  class="text-c">
 											{{if ENTRY_STATE == 1}}
           											启用
           									{{else}}
         											<span style="color:red">禁用</span>
          								    {{/if}}
											</td>
											<td  class="text-c"> 
										    {{if ENTRY_STATE == 1}}
           											<a  href="javascript:void(0)" onclick="quickEntry.stopEntry('{{:ENTRY_ID}}')">禁用</a>	
           									{{else}}
         											<a  href="javascript:void(0)" onclick="quickEntry.startEntry('{{:ENTRY_ID}}')">启用</a>
          								    {{/if}}
													<a  href="javascript:void(0)" onclick="quickEntry.delEntry('{{:ENTRY_ID}}')">删除</a>
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
<%-- 	                     	<jsp:include page="/pages/common/pagination.jsp"/> --%>
	                     </div> 
	              	</div> 
					<div id="additem" style="display: none;width: 100%;height: 100%;padding: 10px;">
						<div class="form-group custom-input">
							<div class="input-group input-group-sm">
								<input type="text" id="modelName" name="modelName" class="form-control input-sm" style="width:200px">
							</div>
							<div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="quickEntry.getUserMenuList();"><span class="glyphicon glyphicon-search" >查询</span></button>
							</div>
						</div>
						<div style="overflow: auto;height: 90%;box-sizing: border-box;">
							<div id="additemDiv">
								
							</div>
						</div>
						<div style="text-align: right;height: 40px;width: 100%;background-color: #fff;height: 10%;box-sizing: border-box;padding-top: 5px;">
						</div>
					</div>
                </div>
        </form>
        <div style="display: none;" id="editSortDiv">
        <form id="editSort" method="post"  autocomplete="off" >
				
				  <input type="hidden" name="editSort.ENTRY_ID" class="form-control input-sm" >
				  <table class="table table-edit table-vzebra mt-10" style="margin-top: 25px">
	                    <tbody >
		                      <tr >
			                        <td class="required" style="width: 100px">排序</td>
			                        <td><input type="number" name="editSort.ENTRY_IDX" data-rules="required" min="0" max="1000" onkeypress="return event.charCode >= 48" oninput="if (value < 0) value = 0;if(value>1000)value=1000" class="form-control input-sm"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="quickEntry.editSort()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				    </div>
		</form>
		</div>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("quickEntry");
		
		/* $(document).ready(function () { 
			quickEntry.getUserMenuList();
		}); */
		
		quickEntry.stopEntry=function(entryId){
			layer.confirm('是否禁用？',  function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/quickEntry?action=stopEntry", {entryId:entryId}, function(result) {
					if (result.state == 1) {	
						layer.msg(result.msg, {icon : 1, time: 1000},function(){
						location.reload();
						});
					} else {
						layer.msg(result.msg, {icon : 5, time: 1000});
					}
				});
			})
		}
		
		quickEntry.delEntry=function(entryId){
			layer.confirm('确认删除吗?', function(index){
				ajax.remoteCall("${ctxPath}/servlet/quickEntry?action=delEntry", {entryId:entryId}, function(result) {
					if (result.state == 1) {
						layer.msg(result.msg, {icon : 1, time: 1000},function(){
						location.reload();
						});
					} else {
						layer.msg(result.msg, {icon : 5, time: 1000});
					}
							
				});
			})
		}
		
		quickEntry.startEntry=function(entryId){
			ajax.remoteCall("${ctxPath}/servlet/quickEntry?action=startEntry", {entryId:entryId}, function(result) {
				if (result.state == 1) {
					layer.msg(result.msg, {icon : 1, time: 1000},function(){
						location.reload();
					});
				} else {
					layer.msg(result.msg, {icon : 5, time: 1000});
				}
							
			});
		}

		quickEntry.editSortShow= function(entryId,entrySort){
			$("input[name='editSort.ENTRY_ID']").val(entryId);
			$("input[name='editSort.ENTRY_IDX']").val(entrySort);
			index = layer.open({
				type: 1,
				title: "修改顺序",
				area: ["400px", "200px"],
				content: $('#editSortDiv'),
				offset: '100px',
				cancel: function(index, layero){
					layer.close(index);
				  return false; 
				}  
			});
		}
		
		quickEntry.editSort= function(){
			var data = form.getJSONObject("#editSort");
			if (form.validate("#editSort")) {
				ajax.remoteCall("${ctxPath}/servlet/quickEntry?action=editSort",data, function(result) {
					if (result.state == 1) {
						layer.close(index);
						layer.msg(result.msg, {icon : 1, time: 1000},function(){
							location.reload();
					    });
					} else {
						layer.msg(result.msg, {icon : 5, time: 1000});
					}
				});
			}
		}
		
		
		//重置
		quickEntry.reset=function(){
			$("#divId select").val("");
	    	$("#divId input").val("");
		};
		
		
		
		quickEntry.getUserMenuList = function(fn){
			var resName = $("#modelName").val();
			$("#additemDiv").empty();
			ajax.remoteCall("${ctxPath}/webcall?action=QuickEntryDao.getUserMenuList",{resName:resName},function(result) { 
				if(result != null&&result!=undefined){
					var modelData= result.data;
					$("#additemDiv").append('');
					for (var i = 0; i < modelData.length; i++) {
						if(quickEntry.hasUseId(modelData[i].RES_ID) == 1){
							var div = document.createElement('div');
							div.className = 'item';
							div.innerHTML = modelData[i].RES_NAME + '<span class="add" onClick="quickEntry.addItemFunc(this)" data-id="' +
											modelData[i].RES_ID + '" data-name="'+
											modelData[i].RES_NAME+'"  data-url="'+
											modelData[i].RES_URL+'"><i class="layui-icon layui-icon-addition"></i></span>';
							$("#additemDiv").append(div);
						}
					}
					fn();
				}else{
					layer.alert(result.msg,{icon: 5, time: 1000});
				}
			}); 
		} 
		
		
		var index;
		//添加
		quickEntry.showAddItem = function(that) {
			quickEntry.getUserMenuList(function(){
				index=layer.open({
					type : 1,
					title : "添加快速入口",
					area : [ "60%", "50%" ],
					maxmin:true,
					content : $('#additem'),
					cancel : function(index, layero) {
						layer.close(index);
						location.reload();
						return false;
					}
				});
			})
		};

		//新增快速入口
		quickEntry.addItemFunc = function(that) {
			var resId = $(that).attr('data-id');
			var resName = $(that).attr('data-name');
			var resUrl = $(that).attr('data-url');
			url = "${ctxPath}/servlet/quickEntry?action=addEntry";
			ajax.remoteCall(url, {resId:resId,resName:resName,resUrl:resUrl}, function(result) {
				if (result.state == 1) {
					quickEntry.getUserMenuList();
					layer.msg(result.msg, {icon : 1, time: 500},function(){
				    });
				}else{
					layer.msg(result.msg, {icon : 5, time: 1000});
				}
			});
		}
		
		quickEntry.entryResIdJson=[];
		
		quickEntry.parseResult=function(result){
			if(result['QuickEntryDao.getQuickEntryList']){
				if(result['QuickEntryDao.getQuickEntryList'].data){
					for(i=0;i<result['QuickEntryDao.getQuickEntryList'].data.length;i++){
						quickEntry.entryResIdJson.push(result['QuickEntryDao.getQuickEntryList'].data[i]['ENTRY_RES_ID'])
					}
				}
			}
		}
		
		quickEntry.hasUseId = function(resId){
			if(resId){
				for(var i=0;i<quickEntry.entryResIdJson.length;i++){
					if(resId == quickEntry.entryResIdJson[i]){
						return 0;
					}
				}
			}
			return 1;
		}

		quickEntry.loadData=function(){
			$("#searchForm").searchData({success:function(result){quickEntry.parseResult(result)}});
		}
		
		$(function(){
			$("#searchForm").render({success:function(result){quickEntry.parseResult(result)}});
		})
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>