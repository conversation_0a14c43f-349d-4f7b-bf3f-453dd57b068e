(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-416b1b2b"],{"00c5":function(t,e,s){"use strict";var a=s("4ea4");e.__esModule=!0,e.default=void 0;var n=s("e5f6"),i=s("18d0"),o=s("01f5"),c=s("fc4d"),l=a(s("b0e9")),u=(0,n.createNamespace)("number-keyboard"),r=u[0],d=u[1],h=u[2],f=["blue","big"],m=["delete","big","gray"],p=r({mixins:[(0,c.BindEventMixin)((function(t){this.hideOnClickOutside&&t(document.body,"touchstart",this.onBlur)}))],model:{event:"update:value"},props:{show:Boolean,title:String,zIndex:[Number,String],closeButtonText:String,deleteButtonText:String,theme:{type:String,default:"default"},value:{type:String,default:""},extraKey:{type:String,default:""},maxlength:{type:[Number,String],default:Number.MAX_VALUE},transition:{type:Boolean,default:!0},showDeleteKey:{type:Boolean,default:!0},hideOnClickOutside:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0}},watch:{show:function(t){this.transition||this.$emit(t?"show":"hide")}},computed:{keys:function(){for(var t=[],e=1;e<=9;e++)t.push({text:e});switch(this.theme){case"default":t.push({text:this.extraKey,theme:["gray"],type:"extra"},{text:0},{text:this.deleteText,theme:["gray"],type:"delete"});break;case"custom":t.push({text:0,theme:["middle"]},{text:this.extraKey,type:"extra"});break}return t},deleteText:function(){return this.deleteButtonText||h("delete")}},methods:{onBlur:function(){this.show&&this.$emit("blur")},onClose:function(){this.$emit("close"),this.onBlur()},onAnimationEnd:function(){this.$emit(this.show?"show":"hide")},onPress:function(t,e){if(""!==t){var s=this.value;"delete"===e?(this.$emit("delete"),this.$emit("update:value",s.slice(0,s.length-1))):"close"===e?this.onClose():s.length<this.maxlength&&(this.$emit("input",t),this.$emit("update:value",s+t))}},genTitle:function(){var t=this.$createElement,e=this.title,s=this.theme,a=this.closeButtonText,n=this.slots("title-left"),i=a&&"default"===s,c=e||i||n;if(c)return t("div",{class:[d("title"),o.BORDER_TOP]},[n&&t("span",{class:d("title-left")},[n]),e&&t("span",[e]),i&&t("span",{attrs:{role:"button",tabindex:"0"},class:d("close"),on:{click:this.onClose}},[a])])},genKeys:function(){var t=this,e=this.$createElement;return this.keys.map((function(s){return e(l.default,{key:s.text,attrs:{text:s.text,type:s.type,theme:s.theme},on:{press:t.onPress}},["delete"===s.type&&t.slots("delete"),"extra"===s.type&&t.slots("extra-key")])}))},genSidebar:function(){var t=this.$createElement;if("custom"===this.theme)return t("div",{class:d("sidebar")},[t(l.default,{attrs:{text:this.deleteText,type:"delete",theme:m},on:{press:this.onPress}},[this.slots("delete")]),t(l.default,{attrs:{text:this.closeButtonText,type:"close",theme:f},on:{press:this.onPress}})])}},render:function(){var t=arguments[0];return t("transition",{attrs:{name:this.transition?"van-slide-up":""}},[t("div",{directives:[{name:"show",value:this.show}],style:{zIndex:this.zIndex},class:d([this.theme,{"safe-area-inset-bottom":this.safeAreaInsetBottom}]),on:{touchstart:i.stopPropagation,animationend:this.onAnimationEnd,webkitAnimationEnd:this.onAnimationEnd}},[this.genTitle(),t("div",{class:d("body")},[this.genKeys(),this.genSidebar()])])])}});e.default=p},"15c5":function(t,e,s){},"4d18":function(t,e,s){},"62ab":function(t,e,s){},8300:function(t,e,s){},9298:function(t,e,s){s("a29f"),s("8300")},a2ec:function(t,e,s){"use strict";var a=s("4d18"),n=s.n(a);n.a},b0e9:function(t,e,s){"use strict";e.__esModule=!0,e.default=void 0;var a=s("e5f6"),n=s("fa5c"),i=s("01f5"),o=(0,a.createNamespace)("key"),c=o[0],l=o[1],u=c({mixins:[n.TouchMixin],props:{type:String,text:[Number,String],theme:{type:Array,default:function(){return[]}}},data:function(){return{active:!1}},computed:{className:function(){var t=this.theme.slice(0);return this.active&&t.push("active"),this.type&&t.push(this.type),l(t)}},mounted:function(){this.bindTouchEvent(this.$el)},methods:{onTouchStart:function(t){t.stopPropagation(),this.touchStart(t),this.active=!0},onTouchMove:function(t){this.touchMove(t),this.direction&&(this.active=!1)},onTouchEnd:function(){this.active&&(this.active=!1,this.$emit("press",this.text,this.type))}},render:function(){var t=arguments[0];return t("i",{attrs:{role:"button",tabindex:"0"},class:[i.BORDER,this.className]},[this.slots("default")||this.text])}});e.default=u},b4e0:function(t,e,s){"use strict";var a=s("15c5"),n=s.n(a);n.a},c430b:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"page flex"},[s("div",{staticClass:"header",style:"padding-top:"+t.statusBarHeight+"px"},[s("div",{staticClass:"header-title"},[t._v("任务")])]),s("div",{staticClass:"content flex-item"},[s("div",{staticClass:"flex"},[s("ccbar",{ref:"ccbarbox"}),s("div",{staticClass:"scroll-list flex-item",staticStyle:{"padding-top":"10px"}},[t._l(t.showTaskList,(function(e,a){return[s("div",{staticClass:"task-list",on:{click:function(s){return t.jumpDetail(e.taskId,e)}}},[s("div",{staticClass:"title"},[s("span",{domProps:{textContent:t._s(e.taskName)}})]),s("div",{staticClass:"info"},[s("span",{staticClass:"validDate",domProps:{textContent:t._s(e.validDate)}}),s("span",{staticClass:"task-md"},[t._v("名单: "),s("span",{domProps:{textContent:t._s(e.useCount)}}),t._v("/"),s("span",{domProps:{textContent:t._s(e.totalCount)}})]),s("span",{staticClass:"task-zx"},[t._v("成功: "),s("span",{domProps:{textContent:t._s(e.succCount)}}),t._v("条")])]),s("div",{staticClass:"call-btn",on:{click:function(s){return s.stopPropagation(),t.jumpTaskCall(e)}}},[t._v("执行")])])]})),0==t.showTaskList.length?s("img",{staticClass:"noDataImg",attrs:{src:t.noDataImg}}):t._e()],2)],1)])])},n=[],i=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"ccbar-panel"},[s("van-cell",{attrs:{icon:"service"},on:{click:t.changeStatus}},[s("div",{attrs:{slot:"title"},slot:"title"},[t._v("当前状态: "),s("span",{staticStyle:{color:"#3cf","font-weight":"bold"},attrs:{clsss:"agentStatusClass"},domProps:{textContent:t._s(t.agentStatusName)}})])]),s("van-action-sheet",{attrs:{id:"ccnarActions",actions:t.actions,"cancel-text":"取消",description:"坐席状态变更"},on:{close:t.onClose,select:t.onSelect,cancel:t.onClose},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}}),s("div",{staticClass:"ccbar-dial"},[s("van-overlay",{attrs:{"z-index":"3",show:t.showDial}},[s("van-number-keyboard",{attrs:{show:t.showDial,theme:"custom","extra-key":"#","close-button-text":"呼叫"},on:{blur:function(e){t.showDial=!1},input:t.onInput,delete:t.onDelete,close:t.makeCall},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[s("div",{attrs:{slot:"title-left"},slot:"title-left"},[s("van-field",{attrs:{readonly:"",clickable:"","input-align":"center"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)])],1)],1)],1)},o=[],c=(s("f5ab"),s("00c5")),l=s.n(c),u=(s("9298"),s("dd01")),r=s.n(u),d=(s("e815"),s("9fdc")),h=s.n(d),f=s("2b0e");f["default"].use(h.a),f["default"].use(r.a),f["default"].use(l.a);var m={props:["text"],data:function(){return{agentStatus:"未签入",show:!1,showDial:!1,value:"",actions:[{name:"置闲",command:"ready"},{name:"置忙",command:"reset"}]}},methods:{changeStatus:function(){this.show=!0},onSelect:function(t){var e=this;this.show=!1;var s=t.command||t.target.command;"makecall"!=s?this.$ccbar.sendCommand(s).then((function(t){e.getStatus()})):this.showDial=!0},onClose:function(){this.show=!1},onInput:function(t){},onDelete:function(){},makeCall:function(){var t=this;""!=this.value?this.$ccbar.sendCommand("makecall",{called:this.value}).then((function(e){"000"==e.result?t.$utils.notify("发起呼叫成功"):t.$utils.notify(e.desc)})):this.$utils.notify("请输入电话号码")},getStatus:function(){var t=this;this.$ccbar.sendCommand("getStatus").then((function(e){"000"==e.result&&(t.agentStatus=e.data.agentStatus,t.$store.commit("ccbarStateSet",e.data.agentStatus))}))}},mounted:function(){console.log("ccbar show"),this.getStatus()},computed:{agentStatusName:function(){var t=this.$store.state.ccbar.agentStatus||this.agentStatus;return this.$ccbar.getStatusName(t)},agentStatusClass:function(){return"class-123"}}},p=m,v=(s("b4e0"),s("2877")),g=Object(v["a"])(p,i,o,!1,null,null,null),b=g.exports,x={data:function(){return{state:"签入",userInfo:{},taskList:[],noDataImg:"./images/nodata.png"}},components:{ccbar:b},mounted:function(){this.$refs.ccbarbox&&this.$refs.ccbarbox.getStatus(),this.getTaskList()},onShow:function(){this.$refs.ccbarbox&&this.$refs.ccbarbox.getStatus()},methods:{jumpDetail:function(t,e){var s={id:t,name:e.taskName};this.$router.push({name:"taskDetail",params:s,query:s})},jumpTaskCall:function(t){var e={taskId:t.taskId,name:t.taskName};this.$router.push({name:"taskCall",params:e})},getTaskList:function(){var t={action:"taskList"},e=this;this.$ccbar.sendCommand("task",t).then((function(t){"000"==t.result&&(e.taskList=t.data.list)}))},logon:function(){},logout:function(){this.state="签出"},getStateName:function(t){var e={1:"待导入",2:"待分配",3:"未到时间",4:"待启动",5:"启动中",6:"已暂停",7:"已关闭",8:"已完成"};return e[t]||t}},computed:{showTaskList:function(){var t=this.taskList;return t}},created:function(){},onReachBottom:function(){wx.showToast({title:"没有更多数据了",duration:1e3,icon:"none",mask:!0})}},y=x,k=(s("a2ec"),Object(v["a"])(y,a,n,!1,null,"30e95cb1",null));e["default"]=k.exports},dd01:function(t,e,s){"use strict";var a=s("4ea4");e.__esModule=!0,e.default=void 0;var n=a(s("2638")),i=s("e5f6"),o=s("dc8a"),c=s("01f5"),l=(0,i.createNamespace)("password-input"),u=l[0],r=l[1];function d(t,e,s,a){for(var l,u=e.mask,d=e.value,h=e.length,f=e.gutter,m=e.focused,p=e.errorInfo,v=p||e.info,g=[],b=0;b<h;b++){var x,y=d[b],k=0!==b&&!f,S=m&&b===d.length,C=void 0;0!==b&&f&&(C={marginLeft:(0,i.addUnit)(f)}),g.push(t("li",{class:(x={},x[c.BORDER_LEFT]=k,x),style:C},[u?t("i",{style:{visibility:y?"visible":"hidden"}}):y,S&&t("div",{class:r("cursor")})]))}return t("div",{class:r()},[t("ul",(0,n.default)([{class:[r("security"),(l={},l[c.BORDER_SURROUND]=!f,l)],on:{touchstart:function(t){t.stopPropagation(),(0,o.emit)(a,"focus",t)}}},(0,o.inherit)(a,!0)]),[g]),v&&t("div",{class:r(p?"error-info":"info")},[v])])}d.props={info:String,gutter:[Number,String],focused:Boolean,errorInfo:String,mask:{type:Boolean,default:!0},value:{type:String,default:""},length:{type:[Number,String],default:6}};var h=u(d);e.default=h},e815:function(t,e,s){s("a29f"),s("8a5a")},f5ab:function(t,e,s){s("a29f"),s("62ab")}}]);