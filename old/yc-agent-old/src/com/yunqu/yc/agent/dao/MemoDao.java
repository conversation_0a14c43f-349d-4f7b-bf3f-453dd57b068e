package com.yunqu.yc.agent.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AppDaoContext;

@WebObject(name="MemoDao")
public class MemoDao extends AppDaoContext {

	@WebControl(name="list", type=Types.LIST)
	public JSONObject list(){
		EasySQL sql = this.getEasySQL("select * from "+getTableName("CC_MEMO")+" where 1=1");
		sql.appendLike(param.getString("content"), " and CONTENT like ?");
		sql.append(getEntId(), " and ENT_ID = ?");
		sql.append(getBusiOrderId(), " and BUSI_ORDER_ID = ?");
		sql.append(this.getUserPrincipal().getUserId(), "and CREATE_BY = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="record", type=Types.RECORD)
	public JSONObject record(){
		String memoId=param.getString("memoId");
		if(StringUtils.isBlank(memoId)){
			memoId=param.getString("memo.MEMO_ID");
		}
		return queryForRecord(new EasyRecord(getTableName("CC_MEMO"), "MEMO_ID").setPrimaryValues(memoId));
	}
}
