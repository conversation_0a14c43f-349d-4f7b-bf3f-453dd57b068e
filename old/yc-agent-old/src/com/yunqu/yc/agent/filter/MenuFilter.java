package com.yunqu.yc.agent.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.utils.MenuResUtils;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

/**
 * 菜单拦截器
 * <AUTHOR>
 *
 */
public class MenuFilter implements Filter {
	
	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain chain) throws IOException, ServletException {
		if(Constants.openFilterUrl()){
			HttpServletRequest request = (HttpServletRequest)servletRequest;
			YCUserPrincipal principal  = (YCUserPrincipal)request.getUserPrincipal();

			if(principal.isAdmin()){
				chain.doFilter(servletRequest, servletResponse);	
				return;
			}
			
			String requestURI = request.getRequestURI();
			String userId = principal.getUserId();
			
			if(!MenuResUtils.hasResource(userId, requestURI)){
				HttpServletResponse response = (HttpServletResponse)servletResponse;
				response.sendRedirect("/yc-agent/pages/common/500.jsp");
				return;
			}
		}
		chain.doFilter(servletRequest, servletResponse);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		// TODO Auto-generated method stub
	}

}
