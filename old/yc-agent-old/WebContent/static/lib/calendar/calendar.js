/**
 * [calendarControl API]
 * @type {Object}
 */
var calendarControl = {
     config:{
    	 DAYS_PAGE: 42,      //每页日历显示的天数
    	 template :{
    		 thead_tmpl: '<thead>'+
					       ' <tr>'+
						 		'<th colspan="7">'+
						 			'<span class="pull-left pre-month"><i class="glyphicon glyphicon-menu-left"></i></span>'+
						 			'<font id="yM">yyyy年MM月</font>'+
						 			'<span class="pull-right next-month"><i class="glyphicon glyphicon-menu-right"></i></span>'+
						 		'</th>'+
					 	    '</tr>'+
					     '</thead>',
             tbody_tmpl: '<tbody id="calendar-tbody">'+
							'<tr>'+
								'<td>日</td>'+
								'<td>一</td>'+
								'<td>二</td>'+
								'<td>三</td>'+
								'<td>四</td>'+
								'<td>五</td>'+
								'<td>六</td>'+
						    '</tr>'+
	                      '</tbody>'
    	 }
     },
     layout:{
    	 $tableCalendar: $("#table-calendar")  	 
     },
     init:function(){
    	 //渲染日历的头部
    	 var tmpl = this.config.template;
    	 var calendar = this.layout.$tableCalendar;
    	 calendar.append(tmpl.thead_tmpl);
    	 calendar.append(tmpl.tbody_tmpl);
    	 
		 var today = new Date();   //获取今天日期
		 var t_year = today.getFullYear(); //获取今天所属年份
		 var t_month = today.getMonth();  //获取今天所属月份（0-11）
		// var t_date = today.getDate();    //获取今天的日期数（1-31）
		 var t_week = today.getDay();     //返回今天是星期几（0-6）
		 //设置顶部显示的年月
		 this.setYMText(t_year,t_month);
		 //渲染日历
		 this.renderCalendar(t_year,t_month);
		 //事件绑定
		 this.bindEvent()
     },
     //设置顶部显示的年月
     setYMText: function(year,month){
    	 var $yM = this.layout.$tableCalendar.find("#yM");
    	 $yM.data("cur-year",year);
    	 $yM.data("cur-month",month);
    	 $yM.attr("data-cur-year",year);
    	 $yM.attr("data-cur-month",month);
    	 var _month = month+1;
    	 var monthStr = _month< 10?'0'+_month:_month;
    	 $yM.text(year+"年"+monthStr+"月");
     },
     //渲染日历
     renderCalendar:function(year,month){
	     //console.log("render year=========="+year);
		 //console.log("render month=========="+month)
		 var curMonthDays = this.getMonTotalDays(year,month+1); //获取当前月的天数
		 var preMonthDays = this.getMonTotalDays(year,month);   //获取上个月的天数
		 var start_date = this.getMonStartDate(year,month);     //每页日历开始的日期
		 var $calendar_tbody = this.layout.$tableCalendar.find("#calendar-tbody");
		 var _trs = ["<tr class='tr-date'>"];
		 var pre_days = preMonthDays-start_date+1;
		 //拼接每页的需要显示的上个月的日期
		 for(var i=0;i<pre_days;i++){
			var dateStr = this.getDateStr(year,month-1,start_date);
			_trs.push("<td class='pre-month-date' id='date-"+dateStr+"' data-date='"+dateStr+"'><span>"+start_date+"</span></td>");
			start_date++;		
		 }
		 if(pre_days==7){
			_trs.push("</tr>");
			_trs.push("<tr class='tr-date'>");
		 }
		 //拼接每页的需要显示的当前月的日期
		 var today = new Date();   //获取今天日期
		 var t_year = today.getFullYear(); //获取今天所属年份
		 var t_month = today.getMonth();  //获取今天所属月份（0-11）
		 var t_date = today.getDate();    //获取今天的日期数（1-31）
		 for(var j=1;j<=curMonthDays;j++){
			var dateStr = this.getDateStr(year,month,j);
			//高亮当天日期
			if(year==t_year&&month==t_month&&j==t_date){
				_trs.push("<td class='cur-month-date today' id='date-"+dateStr+"' data-date='"+dateStr+"'><span>"+j+"</span></td>");
			}else{
				_trs.push("<td class='cur-month-date' id='date-"+dateStr+"' data-date='"+dateStr+"'><span>"+j+"</span></td>");
			}
			
			if(_trs.length%9==8){
				_trs.push("</tr>");
				_trs.push("<tr class='tr-date'>");
			}
			
		 }
		 //拼接每页的需要显示的下个月的日期
		 var next_days = this.config.DAYS_PAGE - (curMonthDays + pre_days);
		 if(next_days>1){
			for(var k=1;k<=next_days;k++){
				var dateStr = this.getDateStr(year,month+1,k);
				_trs.push("<td class='next-month-date' id='date-"+dateStr+"' data-date='"+dateStr+"'><span>"+k+"</span></td>");
				if(_trs.length%9==8){
					_trs.push("</tr>");
					if(k!=next_days){
						_trs.push("<tr class='tr-date'>");
					}	
			   }
				
			}
		 }
		 //console.log(_trs.join(""));
		 //先清空后append
		 $calendar_tbody.find("tr:gt(0)").empty();
		 $calendar_tbody.append(_trs.join(""));
     },
     //获取某个月的的天数（即某月有一共有多少天,month传值1-12）
     getMonTotalDays: function(year,month){
     	 var d = new Date(year,month,0)
     	 return  d.getDate();
     },
     //每页日历开始的日期
     getMonStartDate: function(year,month){
	    var result_date = "0";
	    var d = new Date(year,month,1);     
	    var week = d.getDay();        //当月1号星期几      
	    var monthdays = this.getMonTotalDays(year,month);   //上个月有多少天
	    switch(week){
		   case 0:{result_date=monthdays-6;break;}
		   case 1:{result_date=monthdays-0;break;}
		   case 2:{result_date=monthdays-1;break;}
		   case 3:{result_date=monthdays-2;break;}
		   case 4:{result_date=monthdays-3;break;}
		   case 5:{result_date=monthdays-4;break;}
		   case 6:{result_date=monthdays-5;break;}
	    } 
	    return result_date;
    },
	//根据传入的年月日转换成yyyy年MM月dd日
	getDateStr: function(year,month,day){
	  	var d = new Date(year,month,day);
	  	return d.Format("yyyy-MM-dd");
	},
	//点击上一月
	renderPreMonth: function(){
		var $yM = this.layout.$tableCalendar.find("#yM");
		var cur_year = $yM.data("cur-year");
		var cur_month = $yM.data("cur-month");
		var _year,_month;
		//跨年情况
	    if(cur_month == 0){
	    	_year = cur_year-1;
	    	_month = 11;
	    }else{
	    	_year = cur_year;
	    	_month = cur_month-1;
	    }
	    this.setYMText(_year,_month);
		this.renderCalendar(_year,_month);
	},
	//点击下一月
	renderNextMonth: function(){
		var $yM = this.layout.$tableCalendar.find("#yM");
		var cur_year = $yM.data("cur-year");
		var cur_month = $yM.data("cur-month");
		var _year,_month;
	    if(cur_month == 11){
	    	_year = cur_year+1;
	    	_month = 0;
	    }else{
	    	_year = cur_year;
	    	_month = cur_month+1;
	    }
	    this.setYMText(_year,_month);
	    this.renderCalendar(_year,_month);
	},
	//事件绑定
	bindEvent: function(){
		var _this = this;
		//点击上一月
		_this.layout.$tableCalendar.find(".pre-month").on("click",function(){
			_this.renderPreMonth();
	    });
		//点击下一月
		_this.layout.$tableCalendar.find(".next-month").on("click",function(){
			_this.renderNextMonth();
	    });
	},
	//每页日历的第一个日期
	getPageFirstDate: function(){
		var result_date = this.layout.$tableCalendar.find(".tr-date:first").find("td:first").eq(0).data("date");
	    //console.log(result_date);
		return result_date;
	    
	},
	//每页日历的最后一个日期
	getPageLastDate: function(){
		var result_date = this.layout.$tableCalendar.find(".tr-date:last").find("td:last").data("date");
	    //console.log(result_date);
		return result_date;
	},
	//渲染日程提醒
	renderData: function(){
	    this.getPageFirstDate();
	    this.getPageLastDate();
		var html1 = "<div class='tips'>"+
				        "<span data-toggle='popover'><i class='mIcon mSize16 mPeixun'></i></span>"+
				        "<span data-toggle='popover'><i class='mIcon mSize16 mKaoshi'></i></span>"+
				        "<span data-toggle='popover'><i class='mIcon mSize16 mZhiban'></i></span>"+
				     "</div>";
       $(".table-calendar #date-2018-04-12").append(html1);
       
	   var html2 = "<div class='tips'>"+
				        "<span data-toggle='popover'><i class='mIcon mSize16 mPeixun'></i></span>"
				   "</div>";
       $(".table-calendar #date-2018-04-04").append(html2);
       
       var html3 = "<div class='tips'>"+
				       "<span data-toggle='popover'><i class='mIcon mSize16 mPeixun'></i></span>"+
				       "<span data-toggle='popover'><i class='mIcon mSize16 mKaoshi'></i></span>"+
				   "</div>";
       $(".table-calendar #date-2018-04-22").append(html3);
       
       var head_html = $("#schedule-template #schedule-header").clone().html();
   	   var content_html = $("#schedule-template #schedule-content").clone().html();
   	   //console.log(head_html);
   	   //console.log(content_html);
    /*   $("span[data-toggle='popover']").popover({
       	   trigger: 'click',   //click，hover，focus or manual
           placement: 'bottom', //top, bottom, left or right
           title: head_html,
           html: 'true',
           content: content_html
       })*/
   	   $("span[data-toggle='popover']").each(function(){
		   	$(this).popover({
		    	trigger: 'manual',   //click，hover，focus or manual
		        placement: 'bottom', //top, bottom, left or right
		        title: head_html,
		        html: 'true',
		        content: content_html
		    }).on("mouseenter", function () {
                 var _this = this;
                 $(this).popover("show");
                 $(this).siblings(".popover").on("mouseleave", function () {
                    $(_this).popover('hide');
                 });
            }).on("mouseleave", function () {
            	 var _this = this;
                 setTimeout(function () {
                     if (!$(".popover:hover").length) {
                         $(_this).popover("hide")
                     }
                 }, 100);
            })
   	   })
       $("span[data-toggle='popover']").on('shown.bs.popover', function () {
    	   $(".popover-title #easy-tab li").click(function(){
  	       	  $(this).addClass("active").siblings().removeClass("active");
  	       	  var tab_id = $(this).find("a").data("tab-id");
  	       	  $(".popover-content #"+tab_id).addClass("active").siblings().removeClass("active");
           })
       })
	}
	
}

//日期格式化
Date.prototype.Format = function (fmt) { 
    var o = {
        "M+": this.getMonth() + 1, 
        "d+": this.getDate(), 
        "H+": this.getHours(),  
        "m+": this.getMinutes(),  
        "s+": this.getSeconds(), 
        "q+": Math.floor((this.getMonth() + 3) / 3), 
        "S": this.getMilliseconds()  
    };
    var year = this.getFullYear();
    var yearstr = year + '';
    yearstr = yearstr.length >= 4 ? yearstr : '0000'.substr(0, 4 - yearstr.length) + yearstr;
    
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (yearstr + "").substr(4 - RegExp.$1.length));
    for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}