<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>坐席工作情况</title>
	<style type="text/css">
		#searchForm tr td{white-space: nowrap;min-width:50px;max-width:200px;text-overflow:ellipsis;overflow:hidden }
		#searchForm th{white-space: nowrap; }
		.stat-desc{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  class="form-inline" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 坐席工作情况<span id="titleAndTime"> </span></h5>
							  <div class="input-group input-group-sm pull-right">
								      <button type="button" class="btn btn-sm btn-success btn-outline" onclick="exportAgentWorkStat()"><i class="glyphicon glyphicon-export"></i>  导 出 </button>
						      </div>
							  <div class="input-group input-group-sm">
								    <span class="input-group-addon">坐席工号</span>
								    <select name="agentId" id="agentId" data-mars="common.acctDict" data-mars-top="true" multiple="multiple" size="1">
             		    			</select>	
							  </div>
							  
							  <div class="input-group input-group-sm">
								  	<span class="input-group-addon">开始日期</span> 
								  	<input name="startDate" onClick="WdatePicker({})" data-mars-reload="false" id="startDate" data-mars-top="true" data-mars-top="true" data-mars="report.today"  size = "12" class="form-control input-sm Wdate">
							  </div>
							  <div class="input-group input-group-sm">
								  	<span class="input-group-addon">结束日期</span> 
								  	<input name="endDate" data-mars-reload="false" data-mars="report.today" data-mars-top="true" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\',{d:0});}'})" size = "12"  class="form-control input-sm Wdate">
							  </div>
							  <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							  </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content table-responsive">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c table-record" data-auto-fill-col="16" data-mars="statistic.agentWorkList">
                             <thead>
	                         	 <tr>
								     <!--  <th class="text-c">日期</th> -->
								      <th class="text-l" rowspan="2">部门</th>
								      <th class="text-l" rowspan="2">人员</th>
								      <!-- <th data-sumid="sum2" data-v="col2" class="text-c" rowspan="2">登录时长</th> -->
								      <th data-sumid="sum3" data-v="col3" class="text-c" rowspan="2">工作时长</th>
								      
                                      <th class="text-c" colspan="4">示忙次数</th>
                                      <th class="text-c" colspan="4">示忙时长</th>
                                      
									  <th data-sumid="sum22" data-v="col22" class="text-c" rowspan="2">话后整理次数</th>
									  <th data-sumid="sum23" data-v="col23" class="text-c" rowspan="2">话后整理时长</th>
										
									  <th data-sumid="sum16" data-v="col16" class="text-c" rowspan="2">呼出状态空闲时长</th>
									  <th data-sumid="sum17" data-v="col17" class="text-c" rowspan="2">有效服务时长</th>
								      <th data-sumid="sum18" data-v="col18" class="text-c" rowspan="2">接话次数</th>
								      <th data-sumid="sum19" data-v="col19" class="text-c" rowspan="2">呼出次数</th>
								      <th data-sumid="sum20" data-v="col20" class="text-c" rowspan="2">通话时长</th>
								      <th class="text-c" rowspan="2">工时利用率</th>
		   						 </tr>
		   						 <tr>
                                      <th data-sumid="sum6" data-v="col6" class="text-c">小休</th>
                                      <th data-sumid="sum7" data-v="col7" class="text-c">会议</th>
                                      <th data-sumid="sum8" data-v="col8" class="text-c">培训</th>
                                      <th data-sumid="sum9" data-v="col9" class="text-c">其他</th>
                                      
                                      <th data-sumid="sum10" data-v="col10" class="text-c">小休</th>
                                      <th data-sumid="sum11" data-v="col11" class="text-c">会议</th>
                                      <th data-sumid="sum12" data-v="col12" class="text-c">培训</th>
                                      <th data-sumid="sum13" data-v="col13" class="text-c">其他</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                             
			                   <tr data-row="sum" id="sumTr" style="font-weight: bold;">
			                       <td colspan="2">汇总</td>
			                       <!-- <td id="sum2" data-sum="sum2"></td> -->
			                       <td id="sum3" data-sum="sum3"></td>
			                       <td id="sum6" data-sum="sum6"></td>
			                       <td id="sum7" data-sum="sum7"></td>
			                       <td id="sum8" data-sum="sum8"></td>
			                       <td id="sum9" data-sum="sum9"></td>
			                       <td id="sum10" data-sum="sum10"></td>
			                       <td id="sum11" data-sum="sum11"></td>
			                       <td id="sum12" data-sum="sum12"></td>
			                       <td id="sum13" data-sum="sum13"></td>
			                       
			                       <td id="sum22" data-sum="sum22"></td>
			                       <td id="sum23" data-sum="sum23"></td>
			                       
			                       <td id="sum16" data-sum="sum16"></td>
			                       <td id="sum17" data-sum="sum17"></td>
			                       <td id="sum18" data-sum="sum18"></td>
			                       <td id="sum19" data-sum="sum19"></td>
			                       <td id="sum20" data-sum="sum20"></td>
			                       <td id="sum21"></td>
			                   </tr>
		                 </table>
						<div class="stat-desc">
							<b>统计口径：</b><br>
							工作时长：坐席签入后的在线总时长<br>
							示忙时长：坐席示忙(小休、会议、培训)状态下的时长 <br>
							有效服务时长：呼入状态下的通话时长+呼入状态下空闲时长+外呼状态下通话时长<br>
							接话次数：呼入接听通话次数<br>
							呼出次数：外呼通话次数<br>
							通话时长：呼入通话时长+呼出通话时长<br>
							工时利用率：有效服务时长/工作时长<br>
		              	</div>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											{{!--<td>{{:DATE_ID}}</td>--}}
											<td class="text-l" title="{{:SKILL_GROUP_NAME}}">{{:SKILL_GROUP_NAME}}</td>
											<td class="text-l">{{call:AGENT_ID fn='formatAgent'}}</td>
											<!--<td data-col="col2" data-val="{{:ONLINE_TIME}}">{{call:ONLINE_TIME fn='formatSecond'}}</td>-->
											<td data-col="col3" data-val="{{:SERVICE_TIME}}">{{call:SERVICE_TIME fn='formatSecond'}}</td>
											<td data-col="col6">{{if NOTREADY1_COUNT}}{{:NOTREADY1_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col7">{{if NOTREADY2_COUNT}}{{:NOTREADY2_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col8">{{if NOTREADY3_COUNT}}{{:NOTREADY3_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col9">{{:NOTREADY_COUNT-NOTREADY1_COUNT-NOTREADY2_COUNT-NOTREADY3_COUNT}}</td>
											<td data-col="col10" data-val="{{if NOTREADY1_TIME}}{{:NOTREADY1_TIME}}{{else}}0{{/if}}">{{if NOTREADY1_TIME}}{{call:NOTREADY1_TIME fn='formatSecond'}}{{else}}0{{/if}}</td>
											<td data-col="col11" data-val="{{if NOTREADY2_TIME}}{{:NOTREADY2_TIME}}{{else}}0{{/if}}">{{if NOTREADY2_TIME}}{{call:NOTREADY2_TIME fn='formatSecond'}}{{else}}0{{/if}}</td>
											<td data-col="col12" data-val="{{if NOTREADY3_TIME}}{{:NOTREADY3_TIME}}{{else}}0{{/if}}">{{if NOTREADY3_TIME}}{{call:NOTREADY3_TIME fn='formatSecond'}}{{else}}0{{/if}}</td>
											<td data-col="col13" data-val="{{:NOTREADY_TIME-NOTREADY1_TIME-NOTREADY2_TIME-NOTREADY3_TIME}}">{{call:NOTREADY_TIME-NOTREADY1_TIME-NOTREADY2_TIME-NOTREADY3_TIME fn='formatSecond'}}</td>

											<td data-col="col22">{{if WORK_NOTREADY_COUNT}}{{:WORK_NOTREADY_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col23" data-val="{{if WORK_NOTREADY_TIME}}{{:WORK_NOTREADY_TIME}}{{else}}0{{/if}}">{{if WORK_NOTREADY_TIME}}{{call:WORK_NOTREADY_TIME fn='formatSecond'}}{{else}}0{{/if}}</td>

											<td data-col="col16" data-val="{{if OUTBOUND_STATE_TIME}}{{:OUTBOUND_STATE_TIME}}{{else}}0{{/if}}">{{call:OUTBOUND_STATE_TIME fn='formatSecond'}}</td>
											<td data-col="col17" data-val="{{if VALID_SERVICE_TIME}}{{:VALID_SERVICE_TIME}}{{else}}0{{/if}}">{{call:VALID_SERVICE_TIME fn='formatSecond'}}</td>
											<td data-col="col18">{{if IN_CALL_COUNT}}{{:IN_CALL_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col19">{{if OUT_CALL_COUNT}}{{:OUT_CALL_COUNT}}{{else}}0{{/if}}</td>
											<td data-col="col20" data-val="{{:TOTAL_TIME}}">{{if TOTAL_TIME}}{{call:TOTAL_TIME fn='formatSecond'}}{{else}}0{{/if}}</td>
											<td>{{call:VALID_SERVICE_TIME SERVICE_TIME fn='formatPerse'}}%</td>
									    </tr>
								    {{/for}}					         
							 </script>
	              	</div> 
                </div>
        </form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
    	requreLib.setplugs("wdate");
    	var acctDict = {};
		$(function(){
			$("#searchForm").render({success:function(result){
				if(result['common.acctDict']){
					acctDict = result['common.acctDict'].data;
				}
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect({
						 enableFiltering: true,
						 maxHeight: 400,
						 includeSelectAllOption: true,
						 selectAllText:'全选',
						 nonSelectedText: '请选择'
					});
				});
				var statData = result['statistic.agentWorkList'];
				if(statData){
					sumStat();
				}
                if (statData && statData.data && statData.data.length > 0) {
                    sumStat();
                    $("#sumTr").show();
                } else {
                    $("#sumTr").hide();
                }
                
                $(".stat-desc").show();
			}});
		});
		
		function searchData(){
			$("#searchForm").searchData({success:function(result){
				var statData = result['statistic.agentWorkList'];
				if(statData){
					sumStat();
				}
                if (statData && statData.data && statData.data.length > 0) {
                    sumStat();
                    $("#sumTr").show();
                } else {
                    $("#sumTr").hide();
                }
			}});
		}
		
		function formatSecond(time){
			if(time == undefined || time == null || time =='') return "0:00:00";
			time = parseInt(time);
			var h = Math.floor(time/3600);
			var m = Math.floor(time%3600/60);
			var s = time%60;
			m = m<10?'0'+m:m;
			s = s<10?'0'+s:s;

			return h+":"+m+":"+s;
		}
		
		exportAgentWorkStat = function(){
			layer.confirm('是否导出坐席工作统计表？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				var agentId = "";
				var data = form.getJSONObject("#searchForm");
				if(data.agentId){
					agentId = JSON.stringify(data.agentId);
					agentId = encodeURI(agentId)
				}
				location.href = "${ctxPath}/servlet/export?action=exportAgentWorkStat&"+$("#searchForm").serialize()+"&agentIds="+agentId;
			});
		}
		
		function sumStat() {
            $(".table-record").each(function () {
                var table = $(this);
                var needRecord = table.find("th[data-v]");
                var tr = table.find('tr');
                var temp = {};
                needRecord.each(function () {
                    var count = 0;
                    var count4 = 0;
                    var cols = $(this).data('v').split(',');
                    for (var i = 0; i < cols.length; i++) {
                        table.find("td[data-col='" + cols[i] + "']").each(function () {
                        	if (cols[i] == "col2"||cols[i] == "col3"||cols[i] == "col10"||cols[i] == "col11"||cols[i] == "col12"||cols[i] == "col13"||cols[i] == "col16"||cols[i] == "col17" ||cols[i] == "col20" ||cols[i] == "col23") {
                                count = Number(count) + Number($(this).data("val"));
                            }else{
	                        	count = Number(count) + Number($(this).text());
                            }
                        });
                    }
                    temp[$(this).data('sumid')] = count;
                });
                for (var sumid in temp) {
                    table.find('[data-sum="' + sumid + '"]').each(function (index) {
                        var prefix_text = $(this).data('sumtext');
                        var t = prefix_text ? prefix_text + String(temp[sumid]) : temp[sumid];
                        $(this).text(t);
                    });
                }
            });

            $("#sum21").text(formatPerse($("#sum17").text(),$("#sum3").text())+"%");

            var sum2 = $("#sum2").text();
            $("#sum2").text(formatSecond(sum2));
            
            var sum3 = $("#sum3").text();
            $("#sum3").text(formatSecond(sum3));

            var sum10 = $("#sum10").text();
            $("#sum10").text(formatSecond(sum10));

            var sum11 = $("#sum11").text();
            $("#sum11").text(formatSecond(sum11));

            var sum12 = $("#sum12").text();
            $("#sum12").text(formatSecond(sum12));
            
            var sum13 = $("#sum13").text();
            $("#sum13").text(formatSecond(sum13));
            
            var sum16 = $("#sum16").text();
            $("#sum16").text(formatSecond(sum16));
            
            var sum17 = $("#sum17").text();
            $("#sum17").text(formatSecond(sum17));
            
            var sum20 = $("#sum20").text();
            $("#sum20").text(formatSecond(sum20));
            
            var sum23 = $("#sum23").text();
            $("#sum23").text(formatSecond(sum23));
        }
		
		function formatPerse(v1,v2){
			if (isNaN(v1)||isNaN(v2)||v1==""||v2==""){
                return 0;
            }
			var x = v1/v2;
            if (isNaN(x)) {
                return 0;
            }
            var f = parseFloat(x);
            f = Math.round(x*10000)/100;
            return f+"";
        }

		function formatAgent(agentId){
			return acctDict[agentId];
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>