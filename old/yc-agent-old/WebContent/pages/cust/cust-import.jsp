<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传数据</title>
	<style>
		 #importDataList-body td:nth-child(even) {  
			 display: none;
   		 }  
	    #importDataList-body td:nth-child(odd) {  
	        background: White;  
	    } 
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="custUpload" method="post" enctype="multipart/form-data">
				  <input type="hidden" id="importData" name="importData" class="form-control input-sm" value="">
				  <input type="hidden" name="taskObjTempId" id="taskObjTempId" value="">
				  <table class="table table-vzebra">
	                    <tbody>
		                     <tr>
			                        <td class="text-c">
			                        	模板
			                        </td>
			                        <td>
			                         	  <select  name="tempId" data-value="${param.tempId}" class="form-control input-sm" onchange="changeTempId($(this));" id="tempId" data-mars="CustDataObj.templateDict"></select>			                         	
			                        </td>
		                     </tr>
		                     <tr style="display: none;">
			                        <td class="text-c">
			                        	外地移动号码加"0" 
			                        </td>
			                        <td>
			                        	<label class="radio radio-info radio-inline ">
				                         	<input type="radio" name="autoAddZero" value="1"/><span>是</span>
			                        	</label>
			                        	<label class="radio radio-info radio-inline ">
			                         		<input type="radio" name="autoAddZero" checked="checked" value="0"/><span>否</span>
			                         	</label>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="required">客户资料</td>
			                        <td>
			                        	 <input type="file" id="file" name="file" onchange="upload()" class="hidden" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/>
			                        	 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()"> 点击上传  </button>
					                 	 <a class="btn btn-sm btn-link" id="downloadUrl" href="" data-href="/yc-portal/servlet/custTemp?action=downLoadTemp&tempId=" target="_blank">下载模板</a>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c">
			                        	导入结果
			                        </td>
			                        <td id="import_result" style="color: red">
			                        	只支持Excel文本格式的数据导入。
			                        	<br>
			                         	  每次导入限制5万以内 ,超过请分多次导入!
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="text-c"  id="showDetail" colspan="2" style="display: none;text-align: center;">
			                        	<a href="javascript:detail()">点击查看已上传数据</a>
			                        </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="custUpload.ajaxSubmitForm()"> 提交 </button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
	jQuery.namespace("custUpload");
	
	custUpload.tempId='${param.tempId}';
	 var task;
	 var successCount=0;
	$(function(){
		$("#custUpload").render({success:function(){
			changeTempId($("#tempId"));
		}});
	});
	
	custUpload.ajaxSubmitForm = function(){
			if(successCount==0){
				layer.msg("无数据成功导入!");
				return;
			}
			layer.confirm("是否确认提交？",{title:'请确认数据无误',icon:7,offset:'40px'},function(){
				$("#custUpload").removeAttr("enctype");
				var data = form.getJSONObject("#custUpload");
				 layer.msg('数据正在导到任务中...', {icon: 16 ,shade: 0.01,time:0,offset:'250px'});
				 ajax.remoteCall("${ctxPath}/servlet/custData?action=doUpload",data,function(result) { 
					clearInterval(task);
					 layer.closeAll('dialog');
					 if(result.state == 1){
						 layer.alert(result.msg,{icon: 1,offset:'60px'},function(index){
							layer.close(index);
							popup.layerClose("#custUpload");
							TaskCustList.searchData();
						 });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				  },{
					  loading:false,error:function(){
						layer.msg("导入出现问题,请稍后再试!",function(){
							clearInterval(task);
						});
					  }
				  }
				);
				task=window.setInterval(staticData,2000)
			});
	}
	function upload(){
		$("#custUpload").attr("enctype","multipart/form-data");
		var formData = new FormData($("#custUpload")[0]); 
		$.ajax({  
	          url: '${ctxPath}/servlet/custData?action=custUpload',  
	          type: 'POST',  
	          data: formData,async: true,cache: false,contentType: false,processData: false,  
	          success: function (result) {
	        	  layer.closeAll('dialog');
	        	  if(result.state==1){
	        		  if(sessionStorage) layer.style(sessionStorage.index,{height:'360px'});
		        	  var result=result.data;
		        	  $("#taskObjTempId").val(result.cacheKey);
		        	  $("#import_result").html("已找到"+result.count+"条数据，确认数据无误后点击确认按钮!");
		        	  successCount=result.count;
		        	  var href="/yc-portal/pages/cust/cust-import-temp-data.jsp?taskObjTempId="+result.cacheKey+"&pageNo=0";
		        	  $("#showDetail").html("<a class='btn btn-sm btn-link' title='查看详细' href='"+href+"' target='_blank'>点击查看已上传数据</a>");
		        	  $("#showDetail").show();
	        	  }else{
	        		  layer.alert(result.msg);
	        	  }
	          },error:function(){
	        	  layer.closeAll('dialog');
	          },beforeSend:function(){
	        	  var autoAddZero=$("input[name='autoAddZero']:checked").val();
	        	  if(autoAddZero==1){
	        	  	layer.msg('数据正在解析中(系统需对所有客户号码进行加0判断,预计最长需要1-2分钟,请耐心等待)', {icon: 16 ,shade: 0.01,time:0,offset:'180px'});
	        	  }else{
	        	  	layer.msg('数据解析中...', {icon: 16 ,shade: 0.01,time:0,offset:'180px'});
	        	  }
	          } 
	     }); 
	}
	function staticData(){
		 ajax.remoteCall("${ctxPath}/servlet/custData?action=static",{id:$("#taskObjTempId").val()},function(result) { 
			if(result.state == 1){//全部完成
				clearInterval(task);
			}
			$("#import_result").text(result.msg);
		  },{loading:false,error:function(){
			  clearInterval(task);
		  }}
		);
	}
	function  detail(){
		  popup.layerShow({type:3,title:'查看数据',offset:'20px',area:['530px','80%'],full:true},$("#importDataList").html());
	}
	
	function changeTempId(obj){
		var t=$("#downloadUrl");
		$("#downloadUrl").href=t.attr("href",t.data("href")+obj.val());
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>