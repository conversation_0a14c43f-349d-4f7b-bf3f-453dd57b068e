<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>我的会话记录</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form  class="form-inline" id="searchForm">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 我的会话记录</h5>
							  <div class="input-group input-group-sm">
									  <span class="input-group-addon">会话时间</span>
									  <input type="text" name="startTime" id="startTime" data-mars="common.todayFirstTime" data-mars-top="true" class="form-control input-sm" style="width:140px" onClick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt: 'yyyy-MM-dd HH:mm:ss'})" >
									  <span class="input-group-addon">-</span>
									  <input type="text" name="endTime" id="endTime" data-mars="common.todayEndTime" data-mars-top="true" class="form-control input-sm" style="width:140px" onClick="WdatePicker({minDate:'#F{$dp.$D(\'startTime\')}',dateFmt: 'yyyy-MM-dd HH:mm:ss'})">
							  </div>
							  <div class="input-group input-group-sm">
								    <span class="input-group-addon">渠道类型</span>	
									<select name="channelType" class="form-control input-sm">
										<option value=""> 请选择</option>
										<option value="1"> 网页</option>
										<option value="2"> 微信</option>
										<option value="3"> 新浪微博</option>
										<option value="4">语音</option>
		                      			<option value="5">APP</option>
									</select>
							  </div>
							  <div class="input-group input-group-sm hidden">
								    <span class="input-group-addon">挂机原因</span>	
									<select name="clearCause" class="form-control input-sm">
										<option value=""> 请选择</option>
										<option value="1"> 客户主动结束服务</option>
										<option value="2"> 坐席结束服务</option>
										<option value="3"> 超时结束</option>
										<option value="4"> 客户排队结束</option>
										<option value="5"> 客户登录超时</option>
										<option value="6"> 转移结束</option>
										<option value="0"> 其它</option>
									</select>
							  </div>
							  <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-default" onclick="QcClass.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							  </div>
							  <div class="input-group input-group-sm pull-right">
				   					<a class="btn btn-sm btn-success"  href="javascript:void(0)" onclick="QcClass.exportExportMedia()"><i class="glyphicon glyphicon-export"></i> 导 出 </a>
				    		  </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" data-mars="MediaRecordDao.myChatList">
                             <thead>
	                         	 <tr>
								      <th>日期</th>
									 <th>会话ID</th>
									 <th>客户姓名</th>
									 <th>渠道类型</th>
									 <th>渠道名称 </th>
									 <th>业务名称（渠道按键）</th>
									 <th>服务时间</th>
									 <th>服务时长</th>
									 <th>结束服务原因</th>
									 <th>客户评价</th>
									 <th>评价时间</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:DATE_ID}}</td>
											<td>{{:SESSION_ID}}</td>
											<td>{{:CUST_NAME}}</td>
											<td>{{getText:CHANNEL_TYPE 'channelType'}}</td>
											<td>{{:CHANNEL_NAME}}</td>
											<td>{{:KEY_NAME}}</td>
											<td>{{call:BEGIN_TIME fn='fnTime'}} - {{call:END_TIME fn='fnTime'}}</td>
											<td>{{fnTime:SERVER_TIME}}</td>
											<td>{{getText:CLEAR_CAUSE 'clearCause'}}</td>
											<td>{{:SATISF_NAME}}</td>
											<td>{{:SATISF_TIME}}</td>
											<td class="text-c">
												<a href="javascript:void(0)" onclick="QcClass.chatRecord('{{:SESSION_ID}}','{{:SERIAL_ID}}','{{:CUST_NAME}}')">聊天记录</a>
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>
<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("QcClass");
		
		$(function(){
			$("#searchForm").render();
		});
		
		QcClass.searchData = function(){
			$("#searchForm").searchData();
		}
		$.views.converters('fnTime',function(time){
			if(time == undefined || time == null || time=='') return "0s";
			time = parseInt(time);
			var h = Math.floor(time/3600);
			var m = Math.floor(time%3600/60);
			var s = time%60;
			m = m<10?'0'+m:m;
			s = s<10?'0'+s:s;
			return h+":"+m+":"+s;
		});
		function fnTime(time){
			return time.substring(11);
		} 
		
		function cutMobile(str){
			if(!str||str=="") return "";
			var startIndex = str.indexOf("mobile\":\"");
			if(startIndex<0) return "";
			startIndex+=9;
			return str.substring(startIndex,startIndex+11);
		} 
		QcClass.chatRecord=function(sessionId,chatSessionId,custName){
			var pageTitle = '聊天记录';
			if(custName){
				pageTitle += '（'+custName+'）';
			}
		    popup.layerShow({type:2,title:pageTitle,offset:'20px',area:['80%','80%']},'/yc-media-agent/pages/record.jsp',{sessionId:sessionId,chatSessionId:chatSessionId});
		}
		QcClass.exportExportMedia = function(){
			layer.confirm('是否导出全媒体记录？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				location.href = "${ctxPath}/servlet/export?action=exportMyMediaChat&"+$("#searchForm").serialize();
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>