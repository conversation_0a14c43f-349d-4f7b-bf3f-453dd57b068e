<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>留言明细</title>
	<link rel="stylesheet" href="/yc-media-agent/static/css/chat-record.css">
	<style>
		.layer-skin .layui-layer-content > .container-fluid::after {height: 0px;}
		.layer-skin .layui-layer-content>.container-fluid{
			height: 100%;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<div class="blank-page">
     	<form style="width: 100%" id="wordDetailForm" data-pk="${param.wordId}" data-mars-prefix="record.">
			<input type="hidden" name="wordId" value="${param.wordId}"/>
     		<select id="sender" class="hidden">
		  		<option value="1">用户</option>
		  		<option value="2">客服</option>
		  		<option value="3">机器人</option>
		  		<option value="4">系统</option>
		  	</select>
			<div class="chat">
		      <div class="chat-main" data-mars="MediaRecordDao.wordMsgList" data-template="chat-msg-template">
			
			   </div>

				<div class="reply-area">
					<h5>回复用户</h5>
					<textarea id="msgcontext" name="msgContent" class="form-control input-sm" maxlength="500" rows="5" i18n-placeholder="请输入...." placeholder="请输入...." style="width: 100%; height: 85px;"></textarea>
					<span class="text-success mt-10">
						<i class="glyphicon glyphicon-info-sign"></i>
						<span i18n-content="1.微信48小时内可以收到回复">1.微信48小时内可以收到回复</span><br>
						&nbsp;&nbsp;&nbsp;&nbsp;<span i18n-content="2.其他渠道是否可以收到回复,依赖客户端实现方式;">2.其他渠道是否可以收到回复,依赖客户端实现方式;</span>
					</span>
					<p class="text-c mt-40">
						<button class="btn btn-sm btn-primary" style="width:80px" type="button" onclick="sendMSG()" i18n-content="发送">发送</button>
					</p>
				</div>
			 </div>
		    <script id="chat-msg-template" type="text/x-jsrender">
		    {{for data}}
                {{if SENDER == 1}}
		            <div class="msg-box">
						<div class="msg-receive">
							<img src="${ctxPath}/static/images/user.png" class="avatar">
							<div class="msg msg-group">
							    <span class="name">{{getText:SENDER '#sender'}}<span class="time">{{:MSG_TIME}}</span></span>
                                {{msg:MSG_CONTENT MSG_TYPE}}
							</div>
						</div>
				    </div>
				{{else}}
                    <div class="msg-box clearfix">
						<div class="msg-send">
							<img src="${ctxPath}/static/images/user.png" class="avatar">
							<div class="msg msg-group">
							    <span class="name"><span class="time">{{:MSG_TIME}}</span>{{getText:SENDER '#sender'}}</span>
                                {{msg:MSG_CONTENT MSG_TYPE}}
							</div>
						</div>
					</div>
               {{/if}}
		    {{/for}}
	        </script>
		
	    </form>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <script src="/yc-media-agent/static/js/chat/qq-wechat-emotion-parser.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.slimscroll.min.js"></script>
	<script type="text/javascript">

		$(function(){
			readerData();
		});

		function readerData(){
			$("#wordDetailForm").render({complete:function(result){
				 $(".chat-main").slimScroll({color:'#aaa',height:'100%'});
			}});
		}

		function sendMSG(){
			var data = form.getJSONObject("#wordDetailForm");
			ajax.remoteCall("${ctxPath}/servlet/word?action=sendMsg", data, function(result) {
	  			if(result.state == 1){
	  				layer.msg("发送成功！",{icon:1},function(idx1){
						readerData();
						$("#msgcontext").text("");
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
  			});
		}
		function openBigImg(url) {
				var js = '<script type="text\/javascript">'+rotateImg.toString()+'rotateImg("bigImg")<\/script>';
		    	var index = window.top.layer.open({type:1, area: ['50%', '80%'],content: '<div id="bigImg" style="width:100%; height:100%; overflow:scroll; text-align:center;"><img src="'+url+'"/></div>'+js,shade: 0.1,fixed: false,shadeClose:true,offset:'20px'});
		    	window.top.layer.full(index);
			}

		//图片旋转
		    function rotateImg(parentId) {
				$.getScript("${ctxPath}/static/lib/jquery/jquery.rotate.min.js").done(function() {
					  $('#'+parentId+' img').click(function() {
						  var rotate = $(this).data('rotate');
						  if (rotate) {
							  $(this).data('rotate', rotate+1);
						  } else {
							  $(this).data('rotate', 1);
							  rotate = 1;
						  }
						  $(this).parent('div').rotate({animateTo: 90*rotate});
					  })
				})
		    }

		$.views.converters("msg", function(msgContent,msgType) {
			try {
				if( msgType == 'text'){
					msgContent = qqWechatEmotionParser(msgContent)
					return ' <span class="arrow"></span><div class="msg-content">'+msgContent+'</div>';
				}else if( msgType == 'image'){
					return '<img data-magnify="gallery" onclick="openBigImg(\''+msgContent+'\')" style="width:100px" data-src="'+msgContent+'" src="'+msgContent+'">';
				}else if( msgType == 'file'){
					var contentobj = JSON.parse(msgContent);
					return ' <span class="arrow"></span><div class="msg-content"><a href="'+contentobj.url+'">'+contentobj.name+'</a></div>';
				}else if(msgType == 'video'){
					return '<div class="chat-video">'+
								'<video controls>'+
								   '<source src="'+msgContent+'" type="video/ogg" />'+
								'</video>'+
						   '</div>'
				}else if(msgType == 'voice'){
					return '<div data-url="'+msgContent+'" class="chat-video"><audio preload="none" src="'+msgContent+'" controls ></audio></div>'
				}
			}catch (e){
				console.error(e);
				msgContent = qqWechatEmotionParser(msgContent)
				return ' <span class="arrow"></span><div class="msg-content">'+msgContent+'</div>';
			}
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
