<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单配置-表单生成</title>

	<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/flow.css">
    <style>
	       body,
    html {
        background: #f5f6f7;
        overflow: hidden;
    }
    .pageIframe {
        width: 100%;
        height: 100%;
        border: 0;
    }
 	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <input type="hidden" value="${ctxPath }" id="path">
  
  <div id="flowFormCreate" class="layui-card">  
        <div class="layui-card-header">
           <!--  <span><i class="layui-icon layui-icon-edit"></i> 界面编辑</span> -->
            <div class="layui-btn-group card-tools">
                <button onclick="formCreater.isEdit=true" type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                  		  编辑
                </button>
                <button onclick="formCreater.isEdit=false" type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                		    预览
                </button>
                <button onclick="saveStepFields()" type="button" class="layui-btn layui-btn-success layui-btn-sm">
               		     保存
                </button>
            </div>
        </div>
        <div class="layui-card-body pd-0" style="height: calc( 100% - 90px ); position:  relative;">
            <div id="formCreater" class="form-creater">
                <div class="form-creater-side">
                    <label class="layout-item-label">布局</label>
                    <!-- 布局选项 -->
                    <div @dblclick="addRow(1)" class="layout-item">
                        <span class="item-name">单行单列</span>
                        <a href="javascript:;" data-tmpl="row" data-col="1" title="拖动组件" class="item-drop"></a>
                    </div>
                    <div @dblclick="addRow(2)" class="layout-item">
                        <span class="item-name">单行两列</span>
                        <a href="javascript:;" data-tmpl="row" data-col="2" title="拖动组件" class="item-drop"></a>
                    </div>
                    <div @dblclick="addRow(3)" class="layout-item">
                        <span class="item-name">单行三列</span>
                        <a href="javascript:;" data-tmpl="row" data-col="3" title="拖动组件" class="item-drop"></a>
                    </div>
                    <!-- 自定义字段选项 -->
                    <label class="layout-item-label">字段</label>
                    <template v-for="dataItem,dataKey in formData.unUsedDataDictionary">
                        <div class="layout-item-list">
                            <div data-type="text" :data-ref="dataKey" class="layout-item">
                                <span class="item-type layui-badge layui-bg-blue">{{getDataTypeName(dataItem.dataType)}}</span><span class="item-name">{{dataItem.name}}</span>
                                <a href="javascript:;" :data-tmpl="dataItem.dataType" :data-data="dataKey" title="拖动组件" class="item-drop"></a>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="form-creater-main">
                    <form class="layui-form" action="" onsubmit="return false">
                        <div id="formCreaterArea" style="border: 1px solid #ddd;" class="creater-drop-area" :class="{isEdit:isEdit}">
                            <template v-for="colItem,colIndex in formData.colsData">
                                <div class="layui-row">
                                    <!-- 行操作按钮 -->
                                    <div v-if="isEdit" class="row-btns layui-btn-group">
                                        <!-- <button type="button" class="layui-btn layui-btn-primary layui-btn-xs">拖动</button> -->
                                        <button type="button" @click.stop="upRow(colIndex)" :disabled="colIndex == 0" class="layui-btn layui-btn-primary layui-btn-xs" :class="{'layui-btn-disabled':colIndex == 0}">
                                            <i class="layui-icon layui-icon-up"></i>向上
                                        </button>
                                        <button type="button" @click.stop="downRow(colIndex)" :disabled="colIndex == formData.colsData.length" class="layui-btn layui-btn-primary layui-btn-xs" :class="{'layui-btn-disabled':(colIndex == formData.colsData.length -1)}"><i class="layui-icon layui-icon-down"></i>向下</button>
                                        <button type="button" @click="delRow(colIndex)" class="layui-btn layui-btn-danger layui-btn-xs"><i class="layui-icon layui-icon-delete"></i>删除</button>
                                    </div>
                                    <!-- 只渲染有数据的,预览用 -->
                                    <div style="padding-top: 10px;padding-right: 15px;">
	                                    <div v-if="!isEdit" v-for="refItem,refIndex in colItem.cols" :class="getRowClass(colItem.colNum)">
	                                        <component :is="getTemplateName(refItem.refData)" :name="refItem.refData" :data="getTemplateData(refItem.refData)"></component>
	                                    </div>
                                    </div>
                                    <!-- 输出带格子,编辑用 -->
                                    <div v-if="isEdit" v-for="colIndex2 in Number(colItem.colNum)" :class="getRowClass(colItem.colNum)">
                                        <div v-if="colItem.cols[colIndex2-1]" class="row-btns layui-btn-group">
                                            <!-- <button type="button" class="layui-btn layui-btn-primary layui-btn-xs">拖动</button> -->
                                            <button @click.stop="delItem(colIndex,colIndex2-1)" type="button" class="layui-btn layui-btn-danger layui-btn-xs"><i class="layui-icon layui-icon-delete"></i>删除</button>
                                        </div>
                                        <component v-if="colItem.cols[colIndex2-1]" :is="getTemplateName(colItem.cols[colIndex2-1].refData)" :name="colItem.cols[colIndex2-1].refData" :data="getTemplateData(colItem.cols[colIndex2-1].refData)"></component>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</EasyTag:override>
<EasyTag:override name="script">

    <script id="template-drag" type="x-template">
        <div class="tmpl-drag">
            <div class="tmpl-btns">
                <div class="layui-btn-group">
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe654;</i>
                  </button>
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe642;</i>
                  </button>
                  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm">
                    <i class="layui-icon">&#xe640;</i>
                  </button>
                </div>
            </div>
            <div class="tmpl-detail"></div>
        </div>
    </script>
    <script id="template-text" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <input type="text" :name="name" :value="data.value" :required="data.required == 1" placeholder="请输入文本" autocomplete="off" class="form-control" style="width:100%;" />
            </div>
      </div>
    </script>
    <script id="template-date" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <input readonly type="text" onclick="WdatePicker({dateFmt: 'yyyy-MM-dd'})" :name="name" :value="data.value" :required="data.required == 1" placeholder="请选择日期" autocomplete="off" class="form-control wDate" style="width:100%;background:#fff url(/easitline-static/lib/My97DatePicker/skin/date.png) no-repeat right;" />
            </div>
      </div>    
    </script>
    <script id="template-datetime" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <input readonly type="text" onclick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" :name="name" :value="data.value" :required="data.required == 1" placeholder="请选择日期时间" autocomplete="off" class="form-control wDate" style="width:100%;background:#fff url(/easitline-static/lib/My97DatePicker/skin/date.png) no-repeat right;" />
            </div>
      </div>    
    </script>
    <script id="template-number" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <input type="number" :name="name" :value="data.value" :required="data.required == 1" placeholder="请输入数字" autocomplete="off" class="form-control" style="width:100%;" />
            </div>
      </div>    
    </script>
    <script id="template-select" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <select :name="name" id="" lay-ignore class="form-control" style="width:100%;">
                    <option>请选择{{data.name}}</option>
                    <option v-for="optionItem,optionIndex in data.value" :value="optionItem">{{optionItem}}</option>
                </select>
            </div>
      </div>    
    </script>
    <script id="template-radio" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <div class="checkbox-radio-group">
                    <label v-for="optionItem,optionIndex in data.value" class="radio radio-info radio-inline">
                        <input type="radio" :name="name" :value="optionItem">
                        <span>{{optionItem}}</span>
                    </label>
                </div>
            </div>
      </div>    
    </script>
    <script id="template-checkbox" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label">{{data.name}}</label>
            <div class="layui-input-block">
                <div class="checkbox-radio-group">
                    <label v-for="optionItem,optionIndex in data.value" class="checkbox checkbox-info checkbox-inline">
                        <input type="checkbox" :name="name" :value="optionItem">
                        <span>{{optionItem}}</span>
                    </label>
                </div>
            </div>
      </div>    
    </script>
    <script id="template-textArea" type="x-template">
        <div class="layui-form-item">
            <label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
            <div class="layui-input-block">
				<textarea class="form-control" :name="'order.'+name" :value="data.value" :data-rules="data.required == 1?'required':''" :required="data.required == 1" placeholder="请输入文本" style="width:100%;" rows="3"></textarea>
            </div>
      </div>    
    </script>
    
    <script id="template-cascader" type="x-template">
		<div  class="layui-form-item" id="cascaderDiv">
    		<label class="layui-form-label" v-bind:class="{required: data.required == 1}">{{data.name}}</label>
   		 	<div class="layui-input-block">
				   <input type="text" :id="data.id" class="layui-input" readonly="readonly" placeholder="请选择">
			</div>
 		 </div>
    </script>
  
    <script src="${ctxPath}/static/js/charts/vue.js" charset="utf-8"></script>
    <script src="${ctxPath}/static/js/form-creater.js" charset="utf-8"></script>
    <%-- <script src="${ctxPath}/static/modules/cascader/cascader.js" charset="utf-8"></script> --%>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<%-- <link rel="stylesheet" href="${ctxPath}/static/modules/cascader/cascader.css"> --%>
    <script>

    layui.config({
        base: '${ctxPath}/static/modules/'
    });
    
    var colDataJson = []
    var unUsedDataJson = [];
    var tempId = '${param.tempId }';
    var stepId = '${param.stepId }';
    var stepType = '${param.stepType}';
  //  var cascader;
    $(function() {
        layui.use('element',function() {
            var element = layui.element;
        });
        
   
        // 获取模板字段和步骤字段用于显示
        ajax.remoteCall("${ctxPath}/servlet/orderTemp?action=getTemplateData",{tempId:tempId,stepId:stepId},function(result) { 
			if(result.state == 1){
				// 过滤那些空的字段，只保留有值的
				var tempDataJson = filterData(result.data.templateData)//,result.data.selectId
				colDataJson =  filterColData(result.data.refDataFields);
				// 查找出尚未使用的字段
				unUsedDataJson = filterUnUsedData(result.data.unUsedFields);
				//var selID = result.data.selectId;
				
			    createrInit({
			            isEdit: true,
			            onInit: function() {
			                //加载模拟数据
			                setTimeout(function() {
			                    formCreater.setData(tempDataJson);
			                    formCreater.setColsData(colDataJson);
			                    formCreater.setUnUsedDataDictionary(unUsedDataJson);
			                }, 300);
			            }
			        });
			    //如果有级联下拉框的话，则需要初始化接口
			    //if(selID){
			    	// console.log($("#cascaderDiv").html());
			   //  	layui.use(['jquery','cascader','form'],function(){
			   //  		var $ = layui.jquery;
			   //  		var form = layui.form;
			   //      	var cascader = layui.cascader;
			   //      	var cas = cascader.render({
						// 		elem: '#cascaderText',
						// 		data:[
				  //       	 		{value:'123',label:'456',children:[
				  //       	 			{value:'789',label:"子集"}
				  //       	 		]},
				  //       	 		{value:'240',label:"第一层"},
				  //       	 		{value:'250',label:"第一层"},
				  //       	 		{value:'260',label:"第一层"},
				  //       	 		{value:'280',label:"第一层"},
				  //       	 	],
						// 	    filterable: true,
						// 	 	clearable:true,
						// 	 	trigger:'click'
						// 	});
						// console.log('cas',cas)
			   //      	form.render()
			   //      	// cas.reload() ; // 重载
			   //      	cascader.render();
			   //  	});
			   
			  
			   
			   
			   
			   
			    //}
			    
			}else{
				layer.alert(result.msg,{icon: 5});
			}
			// console.log($("#cascaderDiv").html());
			
		  }
		);
    });
    
    // 保存步骤字段
    function saveStepFields(){
    	 ajax.remoteCall("${ctxPath}/servlet/orderTemp?action=saveStepFields",{stepId:stepId,fieldsJson:colDataJson},function(result) { 
 			if(result.state == 1){
 				layer.msg(result.msg,{icon: 1,time:1200});
 			}else{
 				layer.alert(result.msg,{icon: 5});
 			}
 		  }
 		);
    }
    

    // 过滤字段空数据
    function filterData(dataJson){//selectId
    	var tempDataJson = {};
    	for(key in dataJson){
    		var field = dataJson[key];
    		if(field && field!=''){
    			tempDataJson[key] = JSON.parse(field);
    			//如果是级联的下拉框则要展示将selectID 返回
    			/* if("cascader" == tempDataJson[key].dataType){
    				tempDataJson[key].selectId = selectId;
    			} */
    		}
    	}
    	return tempDataJson;
    }
    
    // 过滤空数据
    function filterColData(refDataFields){
    	if(refDataFields && refDataFields!=''){
    		return JSON.parse(refDataFields);
    	}else{
    		return [];
    	}
    }

 	// 过滤字段空数据
    function filterUnUsedData(unUsedFields){
    	var result = {};
    	for(key in unUsedFields){
    		var field = unUsedFields[key];
    		if(field && field!=''){
    			result[key] = JSON.parse(field);
    		}
    	}
    	return result;
    }

    function setCreate() {
        var windowHeight = $(window).height();
    }
    
   
    
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp" %>
