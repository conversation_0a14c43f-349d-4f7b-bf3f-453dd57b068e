<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单经办组队列</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" data-toggle="render" onsubmit="return false;" autocomplete="off">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5> 工单经办组管理</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">经办组名称</span>	
									  <input type="text" name="examGroupName" class="form-control input-sm" style="width:122px">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="OrderGroup.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
							   <div class="input-group input-group-sm pull-right">
							       <button type="button" class="btn btn-sm btn-success" onclick="OrderGroup.addData()">+新增经办组</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="group.groupList">
                             <thead>
	                         	 <tr>
								      <th class="text-c">编号</th>
								      <th>经办组名称</th>
								      <th class="text-c"data-sort="t1.CREATE_TIME" >创建时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td> {{:#index + 1}}</td>                                         
											<td  class="text-l"> <a  href="javascript:void(0)" onclick="OrderGroup.editData('{{:ORDER_GROUP_ID}}')">{{:EXAM_GROUP_NAME}}</a></td>                                  
											<td> {{:CREATE_TIME}}</td>
											<td width = "160px">
									            <a  href="javascript:void(0)" onclick="OrderGroup.lookAgent('{{:ORDER_GROUP_ID}}','{{:EXAM_GROUP_NAME}}')">设置经办人员</a>&nbsp;-
									            <a  href="javascript:void(0)" onclick="OrderGroup.delData('{{:ORDER_GROUP_ID}}','{{:EXAM_GROUP_NAME}}')">删除</a>
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("OrderGroup");
		
		OrderGroup.loadData=function(){
			$("#searchForm").searchData();
		}
		OrderGroup.addData=function(){
			popup.layerShow({type:1,title:'新增',offset:'40px',area:['451px','191px']},"${ctxPath}/pages/order2/group/group-edit.jsp",null);
		}
		OrderGroup.editData=function(groupId,groupName){
		    popup.layerShow({type:1,title:'编辑',offset:'40px',area:['451px','191px']},"${ctxPath}/pages/order2/group/group-edit.jsp",{groupId:groupId});
		}
		OrderGroup.lookAgent=function(groupId,groupName){
			popup.openTab('${ctxPath}/pages/order2/group/group-user-list.jsp',groupName+'经办人管理',{groupId:groupId,groupName:groupName})
		}
		OrderGroup.delData = function(groupId,groupName){
			layer.confirm('是否确定删除？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/servlet/orderGroup?action=deleteGroup", {groupId:groupId}, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	OrderGroup.loadData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
						OrderGroup.loadData();
					}
	  			});
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>