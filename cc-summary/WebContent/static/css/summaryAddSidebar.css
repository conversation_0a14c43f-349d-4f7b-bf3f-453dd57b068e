body{
	padding: 10px;
}
.layui-input-block {
	margin-left: 90px;
	margin-right: 0px;
}

.layui-form-label {
	width: 90px;
}

.ItemFather {
	position: relative;
}

.selece_btn {
	position: absolute;
	right: -5px;
	top: 2px;
	height: 35px;
	padding: 4px 10px;
}

.container-fluid {
	padding: 0;
}


.selectedBox{
	display: inline-block;
	margin-right: 10px;
	margin-bottom: 10px; 
}
.one, .two{
	display: inline-block;
	/* background-color: #3CB978; */
	color: #fff;
	padding: 10px;
}
.one{
	border-radius: 5px 0 0 5px ;
	line-height: 20px;
	position: relative;
}

.two{
	 border-radius:5px;
	 line-height: 19px;
}

.one:after, .one:before {
  border: 20px solid transparent;
  border-left: 10px solid #3CB978;
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  right: -20px;
  content: ' '
}

.one:before {
  border-left-color: #fff;
  right: -21px;
}
.TheX{
	margin-left: 10px;
}
.TheX:hover{
	cursor: pointer;
}

#current{
	display: inline-block;
	margin-left: 20px;
}

input::-webkit-input-placeholder{
    color:transparent;
    font-size:20px;
}

.searchItem{
	display: block;
	/* margin: 10px; */
	/* border-radius: 5px; */
	/* border: 1px solid #000; */
	color: #000;
	background-color: #fff;
	padding: 5px 10px;
}
.searchItem:hover{
	background-color: #f6f6f6;
	color: #000;
}
