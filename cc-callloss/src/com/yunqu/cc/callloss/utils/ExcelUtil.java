package com.yunqu.cc.callloss.utils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.callloss.base.CommonLogger;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

/**
 * 导出Excel公共方法
 */
public class ExcelUtil {
	private static Logger logger=CommonLogger.logger;

    private ExcelUtil() {

    }

	/**
	 * 静态内部类，用于持有单例实例
	 */
	private static class SingletonHolder {
		private static final ExcelUtil INSTANCE = new ExcelUtil();
	}
    public static ExcelUtil getInstance() {
		return SingletonHolder.INSTANCE;
    }

	public void exportObject2File (String templatePath,String targetPath,JSONObject param) {
		exportWorkbook(param, templatePath, targetPath);
	}

	public void exportExcelFile(String templatePath,String fileName,JSONObject param,HttpServletRequest req,HttpServletResponse resp) {
		File targetFile = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		if(exportWorkbook(param, templatePath, targetFile.getAbsolutePath())) {
			Render.renderFile(req,resp,targetFile,fileName);
		}
	}

	private boolean exportWorkbook(JSONObject param,String templatePath,String targetFilePath) {
		boolean result = false;
		if(StringUtils.isBlank(templatePath)) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" EXCEL模板文件路径为空，请检查！");
			return result;
		}
		File file = new File(templatePath);
		if(!file.exists()) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" EXCEL模板文件不存在，请检查！");
			return result;
		}
		if(file!=null){
			try (FileInputStream in = new FileInputStream(file)){
				XLSTransformer xlsTransformer = new XLSTransformer();
				Workbook workbook = xlsTransformer.transformXLS(in,param);
				workbook.write(new FileOutputStream(targetFilePath));
				result = true;
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 导出Excel文件异常："+e.getMessage(),e);
			}
		}
		return result;
	}

}