package com.yunqu.cc.callloss.dao;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.callloss.base.AppDaoContext;
import com.yunqu.cc.callloss.base.CommonLogger;
import com.yunqu.cc.callloss.base.Constants;
import com.yunqu.cc.callloss.utils.NoticeHelper;

@WebObject(name="triggerDao")
public class TriggerDao extends AppDaoContext {
	protected Logger logger = CommonLogger.logger;

	@WebControl(name="triggerList",type=Types.LIST)
	public  JSONObject triggerList(){
		JSONObject resultJson = new JSONObject();
		try {
			String id = this.param.getString("ID");
			if(StringUtils.isBlank(id)) {
				resultJson.put("msg", "请求成功!");
				resultJson.put("state", 1);
				resultJson.put("pageSize", 100);
				resultJson.put("pageNumber", 1);
				resultJson.put("data", new JSONArray());
				return resultJson;
			}
			String noticeConfigStr = this.getQuery().queryForString("SELECT NOTICE_CFG FROM " + this.getTableName("C_CF_NOTICE_CFG") + " WHERE ID = ?", id);
			JSONObject noticeConfig = JSONObject.parseObject(noticeConfigStr);
			JSONArray noticeGroups = noticeConfig.getJSONArray("noticeGroups");
			JSONArray data = new JSONArray();
			if(CommonUtil.listIsNotNull(noticeGroups)) {
				JSONArray criteriaArr = NoticeHelper.getNoticeInfo();
				Map<String, JSONObject> criteriaMap = new HashMap<String, JSONObject>();
				for(int i = 0; i < criteriaArr.size(); i++ ) {
					JSONObject criteriaJson = criteriaArr.getJSONObject(i);
					criteriaMap.put(criteriaJson.getString("code"), criteriaJson);
				}
				for(int i = 0; i < noticeGroups.size(); i++ ) {
					data.add(convertData(noticeGroups.getJSONObject(i), criteriaMap));
				}
			}
			resultJson.put("msg", "请求成功!");
			resultJson.put("state", 1);
			resultJson.put("pageSize", 100);
			resultJson.put("pageNumber", 1);
			resultJson.put("data", data);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			resultJson.put("msg", "请求成功!");
			resultJson.put("state", 1);
			resultJson.put("pageSize", 100);
			resultJson.put("pageNumber", 1);
			resultJson.put("data", new JSONArray());
		}
		return resultJson;
	}
	
	@WebControl(name="getConfig",type=Types.RECORD)
	public JSONObject getConfig() {
		EasySQL sql = getEasySQL("SELECT * FROM " + this.getTableName("C_CF_NOTICE_CFG"));
		sql.append(" WHERE 1=1 ");
		sql.append(this.getEntId(), " AND ENT_ID = ? ");
		sql.append(this.getBusiOrderId(), " AND BUSI_ORDER_ID = ? ");
		sql.append(Constants.APP_NAME, " AND MODULE_CODE = ? ", false);
		JSONObject result = queryForRecord(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		JSONObject data = result.getJSONObject("data");
		String noticeCfg = data.getString("NOTICE_CFG");
		if (StringUtils.isNotBlank(noticeCfg)) {
			JSONObject noticeCfgJson = JSON.parseObject(noticeCfg);
			data.put("EMAIL_ACC", noticeCfgJson.getString("EMAIL_ACC"));
			data.put("MSG_CHANNEL", noticeCfgJson.getString("MSG_CHANNEL"));
		}
		result.put("data", data);
		return result;
	}
	
	@WebControl(name="getNoticeConfig",type=Types.RECORD)
	public JSONObject getNoticeConfig() {
		JSONObject result = new JSONObject();
		JSONObject data = new JSONObject();
		try {
			if(StringUtils.isNotBlank(this.param.getString("ID"))) {
				String noticeConfigStr = this.getQuery().queryForString("SELECT NOTICE_CFG FROM " + this.getTableName("C_CF_NOTICE_CFG") + " WHERE ID = ?", this.param.getString("RECORD_ID"));
				JSONObject noticeConfigJson = JSON.parseObject(noticeConfigStr);
				JSONArray noticeGroups = noticeConfigJson.getJSONArray("noticeGroups");
				if(noticeGroups != null && noticeGroups.size() > 0) {
					for(int i = 0; i < noticeGroups.size(); i++ ) {
						if(StringUtils.equals(this.param.getString("ID"), noticeGroups.getJSONObject(i).getString("ID"))) {
							data.putAll(noticeGroups.getJSONObject(i));
						}
					}
				}
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		result.put("data", data);
		return result;
	}
	
	/**
	 * 获取账号
	 * @return
	 */
	@WebControl(name = "getEmailList", type = Types.DICT)
	public JSONObject getEmailList() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT T.ACCOUNT AS ACC,T.ACCOUNT AS NAME FROM " + this.getTableName("C_EMAIL_ACC") + "  T ");
		sql.append("Y", "WHERE T.ENABLE_STATUS = ?");
		sql.append(this.getEntId(), "AND T.EP_CODE = ?");
		sql.append(this.getBusiOrderId(), "AND T.BUSI_ORDER_ID = ?");
		sql.append("ORDER BY T.CREATE_TIME");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/*
	 * 获取短信渠道内容
	 * 
	 */
	@WebControl(name="channelList",type=Types.DICT)
	public JSONObject channelList(){
		EasySQL sql = this.getEasySQL("SELECT ID,NAME FROM " + this.getTableName("C_SMS_CHANNEL") + " T WHERE 1 = 1 ");
		sql.append("1", " AND T.STATUS = ?");
		sql.append(this.getEntId(), " AND EP_CODE = ? ");
		sql.append(this.getBusiOrderId(), " AND BUSI_ORDER_ID = ? ", false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 通知条件|通知对象信息下拉框
	 * @return
	 */
	@WebControl(name="conditionDict",type=Types.DICT)
	public JSONObject conditionDict() {
		JSONObject result = getDictByQuery("SELECT CONDITION_CODE,CONDITION_NAME FROM " + this.getTableName("C_CF_NOTICE_CON_OBJ") + " WHERE MODULE_CODE = ? GROUP BY CONDITION_CODE,CONDITION_NAME", Constants.APP_NAME);
		JSONObject data = result.getJSONObject("data");
		if (data == null) {
			return result;
		}
		for (String code : data.keySet()) {
			String name = data.getString(code);
			name = BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, name);
			data.put(code, name);
		}
		return result;
	}
	
	/**
	 * 通知条件|通知对象信息下拉框
	 * @return
	 */
	@WebControl(name="noticeObjDict",type=Types.DICT)
	public JSONObject noticeObjDict() {
		String conditionCode = this.param.getString("conditionCode");
		if(StringUtils.isBlank(conditionCode)) {
			conditionCode = "none";
		}
		JSONObject result = getDictByQuery("SELECT OBJECT_CODE,OBJECT_NAME FROM " + this.getTableName("C_CF_NOTICE_CON_OBJ") + " WHERE MODULE_CODE = ? AND CONDITION_CODE = ?", Constants.APP_NAME, conditionCode);
		JSONObject data = result.getJSONObject("data");
		if (data == null) {
			return result;
		}
		for (String code : data.keySet()) {
			String name = data.getString(code);
			name = BaseI18nUtil.getI18nValue(request, Constants.APP_NAME, name);
			data.put(code, name);
		}
		return result;
	}
	
	/**
	 * 技能组下拉框
	 * @return
	 */
	@WebControl(name="skillDict",type=Types.DICT)
	public JSONObject skillDict() {
		return getDictByQuery("SELECT SKILL_GROUP_ID,SKILL_GROUP_NAME FROM " + this.getTableName("CC_SKILL_GROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND SKILL_GROUP_TYPE IN (?,?)", this.getEntId(), this.getBusiOrderId(), "voice", "media");
	}
	
	/**
	 * 热线号码下拉框
	 * @return
	 */
	@WebControl(name="servicePhoneDict",type=Types.DICT)
	public JSONObject servicePhoneDict() {
		return getDictByQuery("SELECT SERVICE_PHONE,SERVICE_DESC FROM " + this.getTableName("C_CF_SERVICE_PHONE") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND ENABLE_STATUS = ?", this.getEntId(), this.getBusiOrderId(), "01");
	}
	
	/**
	 * 用户下拉框
	 * @return
	 */
	@WebControl(name="userDict",type=Types.DICT)
	public JSONObject userDict() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT USER_ACC,USER_NAME FROM " + this.getTableName("V_CC_USER") + " WHERE 1 = 1");
		sql.append(this.getEntId(), "AND ENT_ID = ?");
		sql.append(this.getBusiOrderId(), "AND BUSI_ORDER_ID = ?");
		sql.append(" AND STATUS = '0' ");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="workGroupDict",type=Types.DICT)
	public JSONObject workGroupDict() {
		return getDictByQuery("SELECT ID,NAME FROM " + this.getTableName("C_CF_WORKGROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ?", this.getEntId(), this.getBusiOrderId());
	}
	
	/**
	 * 部门下拉框
	 * @return
	 */
	@WebControl(name="deptDict",type=Types.DICT)
	public JSONObject deptDict() {
		return getDictByQuery("SELECT SKILL_GROUP_ID,SKILL_GROUP_NAME FROM " + this.getTableName("CC_SKILL_GROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND SKILL_GROUP_TYPE = ?", this.getEntId(), this.getBusiOrderId(), "struct");
	}
	
	/**
	 * 转换json
	 * @param obj 待转换的json串
	 * @param criteriaMap 配置信息
	 * @return
	 * @throws SQLException
	 */
	private JSONObject convertData(JSONObject obj, Map<String,JSONObject> criteriaMap) throws SQLException {
		JSONObject criteriaJson = criteriaMap.get(obj.getString("criteria"));
		String groupConcat = "GROUP_CONCAT";
		if(DBTypes.ORACLE == this.getQuery().getTypes()) {
			groupConcat = "WM_CONCAT";
		}
		
		JSONObject data = new JSONObject();
		data.put("ID", obj.getString("ID"));
		data.put("CRITERIA", criteriaJson.getString("name"));
		data.put("NOTICE_OBJ_TYPE", obj.getString("noticeObjType"));
		data.put("NOTICE_TRIGGER_OBJ_TYPE", obj.getString("noticeTriggerObjType"));
		
		String noticeTriggerObj = null;
		switch(obj.getString("noticeTriggerObjType")) {
		case "01":
			noticeTriggerObj = this.getQuery().queryForString("SELECT " + groupConcat + "(SKILL_GROUP_NAME) FROM " + this.getTableName("CC_SKILL_GROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND SKILL_GROUP_TYPE IN (?,?) AND SKILL_GROUP_ID IN ('" + String.join("','", obj.getString("noticeTriggerObjSkill").split(",")) + "')", this.getEntId(), this.getBusiOrderId(), "voice", "media");
			break;
		case "02":
			noticeTriggerObj = this.getQuery().queryForString("SELECT " + groupConcat + "(SERVICE_DESC) FROM " + this.getTableName("C_CF_SERVICE_PHONE") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND ENABLE_STATUS = ? AND SERVICE_PHONE IN ('" + String.join("','", obj.getString("noticeTriggerObjPhone").split(",")) + "')", this.getEntId(), this.getBusiOrderId(), "01");
			break;
		default:
			break;
		}
		data.put("NOTICE_TRIGGER_OBJ", noticeTriggerObj);
		
		String noticeObj = null;
		switch(obj.getString("noticeObjType")) {
		case "01":
			JSONArray objects = criteriaJson.getJSONArray("objects");
			for(int i = 0; i < objects.size(); i++ ) {
				if(StringUtils.equals(obj.getString("noticeObj"), objects.getJSONObject(i).getString("code"))) {
					noticeObj = objects.getJSONObject(i).getString("name");
				}
			}
			break;
		case "02":
			noticeObj = this.getQuery().queryForString("SELECT " + groupConcat + "(USER_NAME) FROM " + this.getTableName("V_CC_USER") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND STATUS = ? AND USER_ACC IN ('" + String.join("','", obj.getString("noticeUser").split(",")) + "')", this.getEntId(), this.getBusiOrderId(), "0");
			break;
		case "03":
			noticeObj = this.getQuery().queryForString("SELECT " + groupConcat + "(SKILL_GROUP_NAME) FROM " + this.getTableName("CC_SKILL_GROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND SKILL_GROUP_TYPE IN (?,?) AND SKILL_GROUP_ID IN ('" + String.join("','", obj.getString("noticeSkill").split(",")) + "')", this.getEntId(), this.getBusiOrderId(), "voice", "media");
			break;
		case "04":
			noticeObj = this.getQuery().queryForString("SELECT " + groupConcat + "(SKILL_GROUP_NAME) FROM " + this.getTableName("CC_SKILL_GROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND SKILL_GROUP_TYPE = ? AND SKILL_GROUP_ID IN ('" + String.join("','", obj.getString("noticeDept").split(",")) + "')", this.getEntId(), this.getBusiOrderId(), "struct");
			break;
		case "05":
			noticeObj = this.getQuery().queryForString("SELECT " + groupConcat + "(NAME) FROM " + this.getTableName("C_CF_WORKGROUP") + " WHERE ENT_ID = ? AND BUSI_ORDER_ID = ? AND ID IN ('" + String.join("','", obj.getString("noticeWorkGroup").split(",")) + "')", this.getEntId(), this.getBusiOrderId());
			break;
		}
		data.put("NOTICE_OBJ", noticeObj);
		String ways = "";
		String way = obj.getString("way");
		for(String w : way.split(",")) {
			ways += DictCache.getDictVal(this.getEntId(), "CC_BASE_NOTICE_TYPE", w) + ",";
		}
		data.put("WAY", ways);
		return data;
	}
	
}
