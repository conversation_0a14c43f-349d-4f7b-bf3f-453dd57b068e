package com.yunqu.cc.callloss.service.notice;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.NoticeMessage;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.NoticeObjUtil;
import com.yq.busi.common.util.notice.WsNoticeUtil;
import com.yunqu.cc.callloss.base.Constants;
import com.yunqu.cc.callloss.model.CurrentStatus;


/**
 * 
 */
public class CallStatusNoticeService003  extends CalllossNoticeService{

	public CallStatusNoticeService003(){
		super.init();
		this.code = NOTICE_CONDITION_003;
		this.name = "新语音漏话通知";
	}
	
	/**
	 * 获取该类通知的配置信息
	 * @return
	 */
	public  JSONObject getNoticeInfo(){

		JSONObject condition1 = new JSONObject();
		condition1.put("code", code);
		condition1.put("name", name);
		JSONObject exJson1 = new JSONObject();
		exJson1.put("tips", "有新语音漏话需要通知的对象(定时任务扫描，10分钟一次)");
		//exJson1.put("noticeTitle", "有打给#phone#的新漏话");
		//exJson1.put("noticeContent", "有打给#phone#的新漏话");
		exJson1.put("noticeTitle", "您#phone#近#hour#小时语音漏话超过#count#条");
		exJson1.put("noticeContent", "您#phone#近#hour#小时语音漏话超过#count#条");
		
		condition1.put("exJson", exJson1);
		
		//业务对象
		/*JSONArray array1 = new JSONArray();
		array1.add(this.getNoticeObject001());
		array1.add(this.getNoticeObject002());
		condition1.put("objects", array1);*/
		
		JSONObject param1 = new JSONObject();
		param1.put("paramName", "热线号码");
		param1.put("paramCode", "X");
		param1.put("paramType", "string");
		param1.put("paramVal", "");
		JSONObject param2 = new JSONObject();
		param2.put("paramName", "检测时长");
		param2.put("paramCode", "Y");
		param2.put("paramType", "number");
		param2.put("paramVal", "24");
		param2.put("paramUnit", "小时");
		JSONObject param3 = new JSONObject();
		param3.put("paramName", "数量");
		param3.put("paramCode", "Z");
		param3.put("paramType", "number");
		param3.put("paramVal", "3");
		param3.put("paramUnit", "条");
		JSONArray paramArray1 = new JSONArray();
		paramArray1.add(param1);
		paramArray1.add(param2);
		paramArray1.add(param3);
		exJson1.put("params", paramArray1);
		
		condition1.put("exJson", exJson1);
		
		//业务对象
		JSONArray array1 = new JSONArray();
		//array1.add(this.getNoticeObject001());
		//array1.add(this.getNoticeObject002());
		condition1.put("objects", array1);
		
		
		return condition1;
	}

	/**
	 * 处理通知
	 * @param entId
	 * @param busiOrderId
	 * @param user
	 * @param json  业务参数
	 * @return
	 */
	public  JSONObject notice(NoticeMessage message){
		JSONObject result = new JSONObject();
		try {
			if(!(message instanceof CurrentStatus)){
				return null;
			}
			CurrentStatus agentScan = (CurrentStatus)message;
			String entId = agentScan.getNoticeEntId();
			String busiOrderId = agentScan.getNoticeBusiOrderId();
			List<JSONObject> wordList = agentScan.getDataList();
			//String phone = agentScan.getPhone();
			
			//需要进行通知处理的
			List<JSONObject> noticeCfgs = NoticeObjUtil.getNoticeConfigByType(entId, busiOrderId, Constants.APP_NAME, code);
			if(noticeCfgs==null || noticeCfgs.size()<1){
				result.put("respCode", "000");
				result.put("respDesc", "没有配置");
				logger.warn("无法处理该类通知,没有配置:"+name+",entId="+entId+",busiOrderId="+busiOrderId+",code="+code+",moduleCode="+Constants.APP_NAME);		
				return result;
			}

			for(JSONObject cfg : noticeCfgs){
				String title = "您#phone#近#hour#小时语音漏话超过#count#条";
				String content = "您#phone#近#hour#小时语音漏话超过#count#条";
				int interval = 60;
				
				String callNumber = "";
				int hour = 24;
				int count = 3;
				JSONObject exJson = cfg.getJSONObject("exJson");
				String noticeId = cfg.getString("noticeId");
				if(exJson!=null){
					if(StringUtils.isNotBlank(exJson.getString("noticeTitle"))){
						title = exJson.getString("noticeTitle");
					}
					if(StringUtils.isNotBlank(exJson.getString("noticeContent"))){
						content = exJson.getString("noticeContent");
					}
					if(exJson.getIntValue("interval") > 0) {
						interval = exJson.getIntValue("interval");
					}
					
					JSONArray array = exJson.getJSONArray("params");
					callNumber = array.getJSONObject(0).getString("paramVal");
					if(array.size() > 1) {
						hour = array.getJSONObject(1).getInteger("paramVal");
					}
					if(array.size() > 2) {
						count = array.getJSONObject(2).getInteger("paramVal");
					}
				}
				
				int judgeCount = 0;
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Calendar ca = Calendar.getInstance();
				String currTime = sdf.format(ca.getTime());
				//判断触发条件
				for (JSONObject jo : wordList) {
					String called = jo.getString("CALLED");
					String beginTime = jo.getString("BEGIN_TIME");
					int queueStayTime = jo.getInteger("QUEUE_STAY_TIME");
					if(queueStayTime<=0){
						continue;
					}
					//匹配时间
					if(getDistanceTime(beginTime,currTime,hour)){
						if(StringUtils.isBlank(callNumber)){
							//配置项--不填写热线号码，即匹配所有的热线号码
							judgeCount++;
						}else{
							if(callNumber.contains(called)){
								//配置项--填写热线号码，需要匹配热线号码
								judgeCount++;
							}
						}
					}
				}
				
				//达到触发条件
				if(judgeCount>count){
					logger.info("热线号码:" + callNumber + ",检测时长:" + count + "小时,漏话数量:"+judgeCount+"");	
					//避免重复发送
					String key = "cc-callloss-notice-send-result-"+noticeId;
					if(StringUtils.isNotBlank(CacheUtil.get(key))){
						logger.info("近X小时语音漏话超过Y条通知，不重复发送通知");			
						continue;
					}
					//找出通知对象
					JSONObject noticeObjs = NoticeObjUtil.getNoticeObjs(cfg,code);
					
					//找出公用的业务对象对应的通知对象
					this.addCommonNoticeObj(noticeObjs, agentScan);
					
					//找出本通知类型特有的业务对象，对应的通知对象
					addCustNoticeObj(noticeObjs, agentScan);
					
					message.setNoticeObj(noticeObjs);
					
					title = title.replaceAll("#phone#", callNumber);
					title = title.replaceAll("#hour#", hour+"");
					title = title.replaceAll("#count#", judgeCount+"");
					content = content.replaceAll("#phone#", callNumber);
					content = content.replaceAll("#hour#", hour+"");
					content = content.replaceAll("#count#", judgeCount+"");
					noticeTrigger(message, title, content, name, "today", logger,null);
					logger.info("近X小时语音漏话超过Y条已通知"+JsonUtil.toJSONString(message));
					CacheUtil.put(key, "Y", interval);
				}
			}
			
			
			
			
			result.put("respCode", "000");
			result.put("respDesc", "操作成功");
			return result;
		} catch (Exception e) {
			result.put("respCode", "999");
			result.put("respDesc", "操作异常");
			logger.error("处理坐席置忙通知异常:"+e.getMessage(),e);
			return result;
		}
		
	}

}
