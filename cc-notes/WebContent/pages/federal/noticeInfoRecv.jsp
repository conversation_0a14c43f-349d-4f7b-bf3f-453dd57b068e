<%@page import="com.yunqu.cc.note.base.Constants"%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%
	String DURING=Constants.getDuringTheDiner();
%>
<EasyTag:override name="head">
	<title><span i18n-content="查看公告"></span></title>


	<script type="text/javascript" src="/cc-workbench/static/umeditor/lang/zh-cn/zh-cn.js"></script>
	<script type="text/javascript" src="/cc-notes/static/js/my_i18n.js"></script>
	<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
	<link rel="stylesheet" type="text/css" href="/cc-notes/static/css/bs.css">
</EasyTag:override>
<style>
	body {
		color: #333;
	}

	.hoverDed:hover {
		color: red;
	}

	.hoverDed:hover span {
		color: red;
	}

	*::-webkit-scrollbar {
		width: 4px;
		height: 10px;
	}

	*::-webkit-scrollbar-thumb {
		border-radius: 5px;
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		background: rgba(0, 0, 0, 0.2);
	}

	*::-webkit-scrollbar-track {
		-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
		border-radius: 0;
		background: rgba(0, 0, 0, 0.1);
	}

	.adv-msg {
		overflow: hidden;
	}

	.adv-msg span {
		height: 100%;
		display: inline-block;
		overflow: auto;
	}

	img {
		image-rendering: -moz-crisp-edges;
		image-rendering: -o-crisp-edges;
		image-rendering: -webkit-optimize-contrast;
		image-rendering: crisp-edges;
		-ms-interpolation-mode: nearest-neighbor;
	}

	#content {
		padding: 8px;
		border-radius: 5px;
	}

	.container {
		width: 1000px;
	}

	.commentbox {
		width: 85%;
		margin: 20px auto;
		margin-bottom: 68px;
	}

	.mytextarea {
		width: 100%;
		overflow: auto;
		word-break: break-all;
		height: 100px;
		color: #000;
		font-size: 1em;
		resize: none;
	}

	.comment-list {
		width: 99%;
		overflow-y: auto;
		overflow-x: hidden;
		margin: 20px auto;
		clear: both;
		/* padding-top: 20px; */
		max-height: 700px;
		overflow: auto;
	}

	.comment-list .comment-info {
		position: relative;
		padding-right: 10px;
		margin-bottom: 20px;
		margin-bottom: 20px;
		border-bottom: 1px solid #ccc;
	}

	.comment-list .comment-info header {
		width: 10%;
		position: absolute;
	}

	.comment-list .comment-info header img {
		width: 50px;
		height: 50px;
		border-radius: 50%;
		padding: 5px;
	}

	.comment-list .comment-info .comment-right {
		padding: 5px 0px 5px 11%;
	}

	.comment-list .comment-info .comment-right h3 {
		margin: 5px 0px;
	}

	.comment-list .comment-info .comment-right .comment-content-header {
		height: 25px;
	}

	.comment-list .comment-info .comment-right .comment-content-header span,
	.comment-list .comment-info .comment-right .comment-content-footer span {
		padding-right: 2em;
	}

	.comment-list .comment-info .comment-right .comment-content-header span,
	.comment-list .comment-info .comment-right .comment-content-footer span.reply-btn,
	.send,
	.reply-list-btn {
		cursor: pointer;
	}

	.comment-list .comment-info .comment-right .reply-list {
		border-left: 3px solid #ccc;
		padding-left: 7px;
	}

	.comment-list .comment-info .comment-right .reply-list .reply {
		border-bottom: 1px dashed #ccc;
	}

	.comment-list .comment-info .comment-right .reply-list .reply div span {
		padding-left: 10px;
	}

	.comment-list .comment-info .comment-right .reply-list .reply p span {
		padding-right: 2em;
		color: #aaa;
	}

	.container {
		width: 100% !important;
	}

	.content {
		box-shadow: none !important;
		font-size: 18px;
		word-break:break-all;
	}

	#ifHas {
		max-height: 500px;
		overflow-y: auto;
		overflow-x: hidden;
	}

	.glyphicon {
		margin-right: 10px;
		cursor: pointer;
	}
</style>

<EasyTag:override name="content">
	<div style="padding: 0px 15px 20px;">
		<h4 style="text-align: center;color: #000;" id="title"></h4>
		<div style="text-align: center;margin-top: 10px;"><span i18n-content="发件人："></span><span style="margin-right: 20px;" id="people"></span> <span i18n-content="通知时间："></span><span
				id="time" style="margin-right: 10px;"></span> <span i18n-content="点赞数："></span>
			<span id="zanNum"></span>
			<p style="display: inline-block;margin-left: 15px;" class="hoverDed"><i
					class='glyphicon  glyphicon-thumbs-up' onclick='zan(this,"03")' style='cursor: pointer;' id="zan"
					data-iszan="false"></i>
			</p>
		</div>
		<p style="text-indent: 2em;margin-top: 10px;" id="msg"></p>

		<hr>

		<div id="ifHas" style="margin-top: 30px;">
			<p
				style="font-size: 20px; font-family: 'Microsoft yahei'; font-weight: normal; color: #000;border-bottom: 1px solid #DBDBDB;">
				<span style=" border-bottom: 3px solid #CBCBCB;"></span><span i18n-content="附件下载："></span></p>
			<div style="display: flex;flex-direction: column;" id="load">



			</div>
		</div>




		<div style="margin-top: 30px;">
			<p
				style="font-size: 20px; font-family: 'Microsoft yahei'; font-weight: normal; color: #000;border-bottom: 1px solid #DBDBDB;">
				<span style=" border-bottom: 3px solid #CBCBCB;"></span> <span i18n-content="评论："></span></p>


			<div class="container">
				<div class="commentbox" style="position: relative;">
					<div style="position: absolute;top: 80px;right: 7px;z-index: 99999;" id="strLen"></div>
					<textarea cols="80" rows="50" maxlength="160" i18n-placeholder="来说几句吧......" onkeydown='strnumber(this)'  onkeyup='strnumber(this)' onblur='strnumber(this)' onchange='strnumber(this)' class="mytextarea" id="content"></textarea>
					<div class="btn btn-info pull-right" style="margin-top: 10px;" id="comment" onclick="send()" i18n-content="评论">评论
					</div>
				</div>
				<div class="comment-list">

				</div>
			</div>
		</div>

	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script src="/cc-notes/static/js/jq1.10.2.js"></script>
	<script src="/cc-notes/static/js/bs.js"></script>
	<script src="/cc-notes/static/js/jquery.comment.js"></script>
	<script type="text/javascript">
		var allData = {}
		var arr = []

		loadData()



		function strnumber(it) {
			var len = $(it).val().length;
		   $('#strLen').html(len + '/160')
		   if(len >= 160){
			   $('#strLen').css('color','red')
		   }else{
			   $('#strLen').css('color','#000')
		   }
		}

		function loadData() {
			var data = {
				NOTICE_ID: '${param.noticeId}'
			}
			ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=noteInfo", data, function(res) {
				console.log('公告数据', res)
				allData = res.data;
				$('#msg').html(decodeURIComponent(res.data.NOTICE_CONTENT.replace(/\+/g, '%20')))
				$('#title').html(res.data.NOTICE_TITLE)
				$('#people').html(res.data.SENDER_NAME)
				$('#time').html(res.data.SEND_TIME)
				$('#zanNum').html(res.data.PRAISE_COUNT)

				if (res.data.IS_PRAISE == 1) {
					$('#zan').css("color", "red");
					$('#zan').data('iszan', true);
				}


				var str = '';
				//导出并下载文件
				if (isIE() && res.data.attachments.length) { //IE浏览器
					for (var i = 0; i < res.data.attachments.length; i++) {
						str +=
							' <div style="margin-left: 60px;display: flex;margin: 5px;"> <img src="/cc-notes/static/images/file.jpg" alt="文件" style="width:24px;vertical-align: bottom;margin-right: 10px;"><span style="vertical-align: baseline;flex: 2;line-height: 26px;max-width: 80%;overflow: auto;">' +
							res.data.attachments[i].NAME + '</span><a href=\'javaScript:downLoad("' + res.data
							.attachments[i].FILE_PATH +
							'")\' style="vertical-align: baseline;flex: 1;line-height: 26px;margin-left: 20px;" i18n-content="下载">下载</a></div>'
					}
					$('#load').html(str)
				} else if (isIE() == false && res.data.attachments.length) {
					for (var i = 0; i < res.data.attachments.length; i++) {
						str +=
							' <div style="margin-left: 60px;display: flex;margin: 5px;"> <img src="/cc-notes/static/images/file.jpg" alt="文件" style="width:24px;vertical-align: bottom;margin-right: 10px;"><span style="vertical-align: baseline;flex: 2;line-height: 26px;max-width: 80%;overflow: auto;">' +
							res.data.attachments[i].NAME + '</span><a href=\'javaScript:downLoad("' + res.data
							.attachments[i].FILE_PATH +
							'")\' style="vertical-align: baseline;flex: 1;line-height: 26px;margin-left: 20px;" i18n-content="下载">下载</a></div>'
					}
					$('#load').html(str)
				} else {
					$('#ifHas').hide();
				}


				for (var i = 0; i < res.data.comments.length; i++) {
					arr.push({
						id: res.data.comments[i].ID,
						img: res.data.comments[i].IMG_URL ? res.data.comments[i].IMG_URL :
							"/cc-notes/static/images/" + randomNum(1, 5) + ".jpg",
						replyName: res.data.comments[i].CREATE_NAME,
						beReplyName: getI18nValue("匿名"),
						content: res.data.comments[i].CONTENT,
						time: res.data.comments[i].CREATE_TIME,
						zan: res.data.comments[i].PRAISE_COUNT,
						iszan: res.data.comments[i].IS_PRAISE
					})
				}


				$(".comment-list").addCommentList({
					data: arr,
					add: ""
				});
			});
			execI18n();
		}

		function send() {
			if(isEmpty($('#content').val())){
				layer.msg(getI18nValue('内容不得为空!'),{icon:5,time:1500})
				return
			}
			if($('#content').val().length > 160){
				layer.msg(getI18nValue('最大限制为160个字!'),{icon:5,time:1500})
				return
			}
			var data = {
				BUSI_ID: allData.BUSI_ID,
				PARENT_ID: allData.BUSI_ID,
				CONTENT: $('#content').val()
			}
			ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=addComment", data, function(res) {
				console.log('发送评论', res)
				if (res.state == 1) {
					$(".comment-list").addCommentList({
						data: [],
						add: ""
					});
					window.location.reload()
				}
			});
		}
		
		
		function isEmpty(obj) {
			try {
				if (obj == null || obj == undefined) {
					return true;
				}
				//判断数字是否是NaN
				if (typeof obj === "number") {
					if (isNaN(obj)) {
						return true;
					} else {
						return false;
					}
				}
				//判断参数是否是布尔、函数、日期、正则，是则返回false
				if (typeof obj === "boolean" || typeof obj === "function" || obj instanceof Date || obj instanceof RegExp) {
					return false;
				}
				//判断参数是否是字符串，去空，如果长度为0则返回true
				if (typeof obj === "string") {
					if (obj.trim().length == 0) {
						return true;
					} else {
						return false;
					}
				}
		
				if (typeof obj === 'object') {
					//判断参数是否是数组，数组为空则返回true
					if (obj instanceof Array) {
						if (obj.length == 0) {
							return true;
						} else {
							return false;
						}
					}
		
					//判断参数是否是对象，判断是否是空对象，是则返回true
					if (obj instanceof Object) {
						//判断对象属性个数
						if (Object.getOwnPropertyNames(obj).length == 0) {
							return true;
						} else {
							return false;
						}
					}
				}
			} catch (e) {
				console.log(e);
				return false;
			}
		}
		

		//初始化  评论   数据

		// var arr = [{
		//     id: 1,
		//     img: "/cc-notes/static/images/1.jpg",
		//     replyName: "Tom",
		//     beReplyName: "匿名",
		//     content: "同学聚会，看到当年追我的屌丝开着宝马车带着他老婆来了，他老婆是我隔壁宿舍的同班同学，心里后悔极了。",
		//     time: "2017-10-17 11:42:53",
		// }, {
		//     id: 2,
		//     img: "/cc-notes/static/images/2.jpg",
		//     replyName: "匿名",
		//     beReplyName: "",
		//     content: "到菜市场买菜，看到一个孩子在看摊，我问：“一只鸡多少钱？” 那孩子回答：“23。” 我又问：“两只鸡多少钱？” 孩子愣了一下，一时间没算过来，急中生智大吼一声：“一次只能买一只！”",
		//     time: "2017-10-17 11:42:53",
		// }, {
		//     id: 3,
		//     img: "/cc-notes/static/images/3.jpg",
		//     replyName: "帅大叔",
		//     beReplyName: "匿名",
		//     content: "同学聚会，看到当年追我的屌丝开着宝马车带着他老婆来了，他老婆是我隔壁宿舍的同班同学，心里后悔极了。",
		//     time: "2017-10-17 11:42:53",
		// }];









		//  点赞 操作
		function zan(a, type) {
			// （未有点赞记录，添加点赞，若有记录则为取消点赞）
			var num = 0;
			if (type == '03') {
				num = allData.BUSI_ID
			} else {
				type = '04';
				num = $(a).data('num');
			}
			console.log('currentType', type)
			var data = {
				BUSI_ID: num,
				TYPE: type //TYPE（03-点赞公告 04-点赞评论）
			}
			ajax.remoteCall("/cc-notes/servlet/noticeInfo?action=handlePraise", data, function(res) {
				console.log('点赞', res)

				var is = $(a).data('iszan');

				if (is == true) {
					if (type == '03') {
						var zanNum = Number($("#zanNum").html());
						zanNum = zanNum - 1;
						$("#zanNum").html(zanNum);
						$(a).css("color", "");
						$(a).data('iszan', false);
					} else if (type == '04') {
						//$(a).html('点赞');
						var zanNum = Number($("#zanNum" + num).html());
						zanNum = zanNum - 1;
						$("#zanNum" + num).html(zanNum);
						$(a).prev().css("color", "");
						$(a).css("color", "");
						$(a).data('iszan', false);
					}
				} else {

					if (type == '03') {
						var zanNum = Number($("#zanNum").html());
						zanNum = zanNum + 1;
						$("#zanNum").html(zanNum);
						$(a).css("color", "red");
						$(a).data('iszan', true);
					} else if (type == '04') {
						// $(a).html('已点赞');
						var zanNum = Number($("#zanNum" + num).html());
						zanNum = zanNum + 1;
						$("#zanNum" + num).html(zanNum);
						$(a).prev().css("color", "red");
						$(a).css("color", "red");
						$(a).data('iszan', true);
					}
				}

			});
		}


		// $(function() {
		//     $(".comment-list").addCommentList({
		//         data: arr,
		//         add: ""
		//     });
		//     $("#comment").click(function() {
		//         var obj = new Object();
		//         obj.img = "/cc-notes/static/images/4.jpg";
		//         obj.replyName = "匿名";
		//         obj.content = $("#content").val();
		//         obj.browse = "深圳";
		//         obj.osname = "win10";
		//         obj.replyBody = "";
		//         $(".comment-list").addCommentList({
		//             data: [],
		//             add: obj
		//         });
		//     });
		// })



		function downLoad(filePath) {
			//导出并下载文件
			//debugger;
			if (isIE()) { //IE浏览器保存文本框内容
				window.location.href = "/cc-base/servlet/attachment?action=download&oper=download&filePath=" + encodeURIComponent(filePath);
			} else {
				window.location.href = "/cc-base/servlet/attachment?action=download&oper=download&filePath=" + encodeURIComponent(filePath);
			}
		}

		function isIE() {
			if (!!window.ActiveXObject || "ActiveXObject" in window)
				return true;
			else
				return false;
		}

		function getQueryVariable(variable) {
			var query = window.location.search.substring(1);
			var vars = query.split("&");
			for (var i = 0; i < vars.length; i++) {
				var pair = vars[i].split("=");
				if (pair[0] == variable) {
					return pair[1];
				}
			}
			return (false);
		}

		//生成从minNum到maxNum的随机数
		function randomNum(minNum, maxNum) {
			switch (arguments.length) {
				case 1:
					return parseInt(Math.random() * minNum + 1, 10);
					break;
				case 2:
					return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
					break;
				default:
					return 0;
					break;
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>
