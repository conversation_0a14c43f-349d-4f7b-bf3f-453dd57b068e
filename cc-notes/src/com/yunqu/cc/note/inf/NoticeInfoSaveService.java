package com.yunqu.cc.note.inf;

import java.net.URLDecoder;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.note.base.AppBaseServlet;
import com.yunqu.cc.note.base.AppDaoContext;
import com.yunqu.cc.note.base.CommonLogger;
import com.yunqu.cc.note.base.Constants;
import com.yunqu.cc.note.base.QueryFactory;
import com.yunqu.cc.note.dao.NoticeInfoDao;
import com.yunqu.cc.note.dao.NoticeInfoSaveDao;
import com.yunqu.cc.note.model.NoticeInfo;
import com.yunqu.cc.note.model.NoticeRecv;
import com.yunqu.cc.note.model.NoticeSend;

/**
 * Title:其他模块添加公告
 * Description:新增员工通知消息推送 
 * <AUTHOR>
 * @version 1.0
 */
public class NoticeInfoSaveService extends IService{
	/**
	 * 调用业务逻辑方法
	 */
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		CommonLogger.logger.info(CommonUtil.getClassNameAndMethod(this)+">>invoke>>JSONObject:"+json.toJSONString());
		NoticeInfoSaveDao not=new NoticeInfoSaveDao();
		return not.serviceInfo(json);
	}
		
}
