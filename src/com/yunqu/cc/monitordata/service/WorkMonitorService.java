package com.yunqu.cc.monitordata.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.cc.monitordata.util.CommonUtils;
import com.yunqu.cc.monitordata.util.DateUtil;
import com.yunqu.cc.monitordata.util.ParamUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理查询坐席监控请求
 */
public class WorkMonitorService {

    private static final Logger logger = CommonLogger.getLogger("workMonitor");

    private static final EasyCache cache = CacheManager.getMemcache();

    private static final String busiName = Constants.getBusiName();
    private static final String aiaDbName = Constants.getAiaDb();

    /**
     * 首页 -> 综合总话务量统计展示->展示当天接话量、回访总量、网络总量统计数据
     */
    public static JSONObject allCallStat() {
        JSONObject result = new JSONObject();
        try {
            // 电话量与网络量 和 回访量统计图 + 并线号码受理分析
            String str = cache.get("ZZ_CALL_NETWORK_FOLLOW_DOUBLING_V2");
            if (StringUtils.isBlank(str)) {
                return result;
            }
            JSONObject resultData = JSONObject.parseObject(str);
            // 当天接话量
            int acceptanceCount = resultData.getIntValue("ACCEPTANCE_COUNT");
            // 网络量总数
            int acceptanceNetwork = resultData.getIntValue("ACCEPTANCE_NETWORK");
            // 当天回访量
            int acceptanceFlowUp = resultData.getIntValue("ACCEPTANCE_FLOW_UP");
            // 总量
            String acceptanceTotal = String.valueOf(acceptanceCount + acceptanceNetwork + acceptanceFlowUp);
            // 当天接话量占比
            String acceptanceCountPercent = ParamUtil.findPercent(String.valueOf(acceptanceCount), acceptanceTotal);
            // 当天回访量占比
            String acceptanceFlowUpPercent = ParamUtil.findPercent(String.valueOf(acceptanceFlowUp), acceptanceTotal);
            // 网络量占比
            BigDecimal acceptanceCallCountPercent = new BigDecimal("100")
                    .subtract(new BigDecimal(acceptanceCountPercent))
                    .subtract(new BigDecimal(acceptanceFlowUpPercent));
            result.put("ACCEPTANCE_COUNT_PERCENT", acceptanceCountPercent + "%");
            result.put("ACCEPTANCE_FLOW_UP_PERCENT", acceptanceFlowUpPercent + "%");
            result.put("ACCEPTANCE_CALL_COUNT_PERCENT", acceptanceCallCountPercent.setScale(2, BigDecimal.ROUND_HALF_UP) + "%");


            result.put("ACCEPTANCE_COUNT", acceptanceCount);
            result.put("ACCEPTANCE_CALL_COUNT", acceptanceNetwork);
            result.put("ACCEPTANCE_FLOW_UP", acceptanceFlowUp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }


    // 上月总和排名
    public static List<JSONObject> lastMonthRanking(JSONObject param) {
        try {
            // agent work
            String type = param.getString("type");
            // 关键词
            String keyWord = param.getString("keyWord");
            String tableName = "";
            String fieldName = "";
            String nameFieldName = "";
            // 上个月
            String monthId = DateUtil.addMonth("yyyy-MM", DateUtil.getCurrentDateStr("yyyy-MM"), -1);
            if ("agent".equals(type)) {
                tableName = busiName + ".cx_agent_score_config_12345";
                if (StringUtils.isNotBlank(keyWord)) {
                    fieldName = " AND AGENT_NAME LIKE '%" + keyWord + "%'";
                }
                nameFieldName = "AGENT_NAME NAME,AGENT_NO,";
            } else if ("work".equals(type)) {
                tableName = busiName + ".cx_work_score_config_12345";
                if (StringUtils.isNotBlank(keyWord)) {
                    fieldName = " AND WORK_GROUP LIKE '%" + keyWord + "%'";
                }
                nameFieldName = "WORK_GROUP NAME,WORK_GROUP AGENT_NO,";
            }
            EasyQuery query = QueryFactory.getReadQuery();
            String selectSql = "SELECT " + nameFieldName + " ALL_SCORE,MONTHLY_RANKING,MONTH_ID,REWARD,DEDUCTION,ON_HOOK_SATISFACTION,BUSI_NUMBER_SCORE,QUALITY_SCORE,ATTENDANCE_SCORE,MONTHLY_EXAM_SCORE,AFTER_LONG_SCORE FROM " + tableName + " WHERE MONTH_ID = ? " + fieldName + " ORDER BY CONVERT(ALL_SCORE, UNSIGNED) DESC";
            List<JSONObject> list = query.queryForList(selectSql, new Object[]{monthId}, new JSONMapperImpl());
            if (list == null || list.isEmpty()) {
                return new ArrayList<>();
            }
            for (JSONObject json : list) {
                //获取上月五维图数据
                json.put("LAST_MONTH_SCORE_FIVE", CommonUtils.getLastMonthScoreFive(json));
                json.remove("BUSI_NUMBER_SCORE");
                json.remove("QUALITY_SCORE");
                json.remove("ATTENDANCE_SCORE");
                json.remove("MONTHLY_EXAM_SCORE");
                json.remove("AFTER_LONG_SCORE");
            }
            return list;

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    /**
     * 个人五维图数据
     */
    public static JSONObject agentFiveWayStat(JSONObject param) {
        JSONObject result = new JSONObject();
        try {
            // agent work
            String type = param.getString("type");
            String tableName = "";
            String nameFieldName = "";
            String whereFieldName = "";
            if ("agent".equals(type)) {
                tableName = busiName + ".cx_agent_score_config_12345";
                nameFieldName = "AGENT_NAME NAME,AGENT_NO,";
                whereFieldName = " AND AGENT_NO = ?";
            } else if ("work".equals(type)) {
                tableName = busiName + ".cx_work_score_config_12345";
                nameFieldName = "WORK_GROUP NAME,";
                whereFieldName = " AND WORK_GROUP = ?";
            }
            String agentNo = param.getString("agentNo");
            String monthId = param.getString("monthId");
            String selectSql = "SELECT " + nameFieldName + " ALL_SCORE,REWARD,DEDUCTION,ON_HOOK_SATISFACTION,MONTHLY_RANKING,MONTH_ID,BUSI_NUMBER_SCORE,QUALITY_SCORE,ATTENDANCE_SCORE,MONTHLY_EXAM_SCORE,AFTER_LONG_SCORE FROM " + tableName + " WHERE MONTH_ID = ? " + whereFieldName;
            JSONObject data = QueryFactory.getReadQuery().queryForRow(selectSql, new Object[]{monthId, agentNo}, new JSONMapperImpl());

            // 总分
            result.put("allScore", data.getString("ALL_SCORE"));
            // 奖励分
            result.put("reward", data.getString("REWARD"));
            // 月度排行
            result.put("monthlyRanking", data.getString("MONTHLY_RANKING"));
            // 挂机满意度
            result.put("onHookSatisfaction", data.getString("ON_HOOK_SATISFACTION"));
            // 减扣分
            result.put("deduction", data.getString("DEDUCTION"));
            //获取上月五维图数据
            result.put("lastMonthScoreFive", CommonUtils.getLastMonthScoreFive(data));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 查询通话中的通话
     */
    public static JSONObject callData(JSONObject param) {
        // 坐席Id
        String agentId = param.getString("agentId");

        EasySQL sql = new EasySQL();
        sql.append("SELECT");
        sql.append("CALL_ID,");
        sql.append("ORDER_ID,");
        sql.append("AGENT_NAME,");
        sql.append("AGENT_PHONE,");
        sql.append("AGENT_NO,");
        sql.append("CUST_PHONE,");
        sql.append("START_TIME,");
        sql.append("END_TIME,");
        sql.append("CALL_FLAG,");
        sql.append("CALL_CONTENT,");
        sql.append("CALL_TIME,");
        sql.append("RECORD_PATH");
        sql.append("FROM " + aiaDbName + ".AIA_CALL_LIST");
        sql.append("WHERE 1 = 1");
        sql.append(agentId, "AND AGENT_NO = ?");
        sql.append(DateUtil.getCurrentDateStr("yyyy-MM-dd"), "AND START_TIME >= ?");
        sql.append("AND (END_TIME IS NULL OR END_TIME = '')");
        sql.append("ORDER BY START_TIME DESC LIMIT 1");
        EasyQuery query = QueryFactory.getReadQuery();
        try {
            JSONObject data = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            if (data == null) {
                return new JSONObject();
            }
            return data;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new JSONObject();
    }

    /**
     * 历史通话
     * @param param
     * @return
     */
    public static JSONObject historyCall(JSONObject param) {
        try {
            String startTime = param.getString("startTime");
            String endTime = param.getString("endTime");
            // 关键词
            String keyWord = param.getString("keyWord");
            // 坐席Id
            String agentId = param.getString("agentId");
            int pageNo = param.getIntValue("pageNo");
            int pageSize = param.getIntValue("pageSize");
            EasySQL sql = new EasySQL();
            sql.append("SELECT");
            sql.append("CALL_ID,");
            sql.append("ORDER_ID,");
            sql.append("AGENT_NAME,");
            sql.append("AGENT_PHONE,");
            sql.append("AGENT_NO,");
            sql.append("CUST_PHONE,");
            sql.append("START_TIME,");
            sql.append("END_TIME,");
            sql.append("CALL_FLAG,");
            sql.append("CALL_CONTENT,");
            sql.append("CALL_TIME,");
            sql.append("RECORD_PATH");
            sql.append("FROM " + aiaDbName + ".AIA_CALL_LIST");
            sql.append("WHERE 1 = 1");
            sql.append(agentId, "AND AGENT_NO = ?");
            sql.append(startTime, "AND START_TIME >= ?");
            sql.append(endTime, "AND START_TIME <= ?");
            sql.append(keyWord, "AND CUST_PHONE = ?");
            sql.appendSort("START_TIME", "DESC");
            EasyQuery query = QueryFactory.getReadQuery();

            return CommonUtils.queryForPageList(query,sql,pageNo,pageSize);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new JSONObject();
    }

    /**
     * 获取工单详情
     * @param param
     * @return
     */
    public static JSONObject workOrderDetail(JSONObject param) {
        JSONObject result = new JSONObject();
        try {
            String orderId = param.getString("orderId");
            String sql = "select CUST_NAME,CUST_SEX,CUST_PHONE,SPECIAL_FLAG,ORDER_TYPE,REFLECT_NAME,TITLE,MAJOR_CONTENT,AREA_NAME,STREET_NAME,ADDRESS,OFFICE_NAME from " + Constants.getAiaDb() + ".AIA_ORDER_INFO where ORDER_ID = ?";
            result = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{orderId}, new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取坐席话务统计信息
     * @param param
     * @return
     */
    public static JSONObject agentCallStat(JSONObject param) {
        try {
            String agentId = param.getString("agentId");
            // AGENTID 坐席id
            // CALL_IN_COUNT_ALL //当天接听量
            // AVG_CALL_IN_TIME //平均通话时长
            // AVG_ARRANGE_TIME //平均话后时长
            // LOGIN_TIME //签入总时长
            // CALL_IN_TIME_ALL //接听总时长
            // ARRANGE_TIME //话后总时长
            // BUSY_TIME //示忙总时长
            //获取当前坐席的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
            return CommonUtils.getUserCallData(agentId, "1");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public static List<Map<String, Object>> queryWorkGroup(YCUserPrincipal ycuser) {
        // 查询所有坐席数据 坐席id 姓名 班组id 头像url 呼入量 呼出量
        List<Map<String, String>> tAgentInfoList = CommonUtils.queryAgentList();
        // 用流计算根据班组分组
        Map<String, List<Map<String, String>>> groupAgentMap = tAgentInfoList.stream().collect(Collectors.groupingBy(t -> t.get("WORKGROUPID")));
        List<Map<String, Object>> result = new ArrayList<>();
        // 班组信息
        String workGroupInfo = CommonUtils.queryWorkGroupInfoList();
        if (StringUtils.isNotBlank(workGroupInfo)) {

            List<Map<String, String>> tworkgrouplist = JSON.parseObject(workGroupInfo, new TypeReference<List<Map<String, String>>>() {});
            if (ycuser != null && ycuser.getRoleType() == Constants.ROLE_TYPE_MONITOR) {
                String workGroupId = CommonUtils.getUserWorkGroupId(ycuser.getUserId());
                tworkgrouplist = tworkgrouplist.stream().filter(t -> t.get("WORKGROUPID").equals(workGroupId)).collect(Collectors.toList());
            }
            Map<String, Object> workgroupMap = null;
            for (Map<String, String> workgroup : tworkgrouplist) {
                workgroupMap = new HashMap<>();
                String workGroupId = workgroup.get("WORKGROUPID");
                String workGroupName = workgroup.get("WORKGROUP");
                workgroupMap.put("workGroupId", workGroupId);
                workgroupMap.put("workGroupName", workGroupName);
                List<Map<String, String>> userList = groupAgentMap.get(workGroupId);
                if(userList == null || userList.isEmpty()){
                    continue;
                }
                if (workGroupName.contains("回访")) {
                    workgroupMap.put("businessType", "回访");
                } else {
                    workgroupMap.put("businessType", "接话");
                }
                // 班组的工作量
                int workGroupWorkCount = 0;
                for (Map<String, String> user : userList) {
                    if (workGroupName.contains("回访")) {
                        String callOutCountAll = user.get("CALL_OUT_COUNT_ALL");
                        if (StringUtils.isBlank(callOutCountAll)) {
                            callOutCountAll = "0";
                        }
                        user.put("WORK_COUNT", callOutCountAll);
                        workGroupWorkCount += Integer.parseInt(callOutCountAll);
                    } else {
                        String callInCountAll = user.get("CALL_IN_COUNT_ALL");
                        if (StringUtils.isBlank(callInCountAll)) {
                            callInCountAll = "0";
                        }
                        user.put("WORK_COUNT", callInCountAll);
                        workGroupWorkCount += Integer.parseInt(callInCountAll);
                    }
                    user.remove("CALL_IN_COUNT_ALL");
                    user.remove("CALL_OUT_COUNT_ALL");
                }
                // 按照工作量排序
                userList.sort(Comparator.comparing(m -> Integer.parseInt(m.get("WORK_COUNT")), Comparator.reverseOrder()));
                workgroupMap.put("userList", userList);
                // 班组工作量
                workgroupMap.put("workGroupWorkCount", workGroupWorkCount);
                result.add(workgroupMap);
            }
            // 按照工作量排序
            result.sort(Comparator.comparing(m -> Integer.parseInt(m.get("workGroupWorkCount").toString()), Comparator.reverseOrder()));
        }
        return result;
    }
}
