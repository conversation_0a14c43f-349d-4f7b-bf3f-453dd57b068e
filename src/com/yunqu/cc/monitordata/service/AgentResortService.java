package com.yunqu.cc.monitordata.service;


import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.cc.monitordata.util.CommonUtils;
import com.yunqu.cc.monitordata.util.HttpResp;
import com.yunqu.cc.monitordata.util.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import java.util.ArrayList;
import java.util.List;

/**
 * 助手推送坐席求助信息接口
 * <AUTHOR>
 * @date 2023年8月25日i上午10:38:31
 *
 */
public class AgentResortService extends IService {

	private static Logger logger = CommonLogger.getLogger("agent");
	EasyQuery query = QueryFactory.getWriteQuery();
	EasyQuery readQuery = QueryFactory.getReadQuery();
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		logger.info("==》【AgentResortService】"+json.toJSONString());
		JSONObject dictByQuery = new JSONObject();
		//pool.execute(new Thread(() -> {
		dictByQuery = saveResort(json);
		//}));
		logger.info("==《【AgentResortService】"+dictByQuery);
		return dictByQuery;
	}
	//保存数据入库
	public JSONObject saveResort(JSONObject json) {
		JSONObject result = new JSONObject();
		try {
			result.put("msgId", json.getString("msgId"));
			EasyRecord record = new EasyRecord(Constants.getBusiName() +".cx_agent_remind_table","MSG_ID");
			record.put("MSG_ID", json.getString("msgId"));
			record.put("AGENT_NO", json.getString("agentNo"));
			record.put("AGENT_NAME", json.getString("agentName"));
			record.put("AGENT_PHONE", json.getString("agentPhone"));
			record.put("QUEUE_NAME", json.getString("queueName"));
			record.put("GROUP_NAME", json.getString("groupName"));
			record.put("AGENT_IP", json.getString("agentIp"));
			record.put("TYPE", "1");
			record.put("REMIND_TYPE", json.getString("resortTypeName"));//求助类型
			String resortType = json.getString("resortType");
			if("1".equals(resortType)) {//取消求助
				record.put("RESORT_TYPE", 1);
			}else {
				record.put("RESORT_TYPE", 2);
			}
			String resortTypeName = json.getString("resortTypeName");
			JSONObject helpJson = query.queryForRow("select * from "+Constants.getAiaDb()+".aia_agent_resort_type where RESORT_TYPE_NAME=?"
					, new Object[] {resortTypeName},new JSONMapperImpl());

			String helpType = "1";
			String helpColor = "1";

			if(helpJson != null) {
				helpType = helpJson.getString("RESORT_TYPE_ID");
				helpColor = helpJson.getString("COLOR");
			}
			record.put("HELP_TYPE", helpType);//求助类型 1:坐席疑难求助;2:领导介入请求;3:话务申请小休;4:紧急突发事件
			record.put("HELP_LIGHT_COLOR", helpColor);//告警灯颜色 1:蓝;2:黄;3:橙;4:红
			
			record.put("REMIND_CONTENT", json.getString("resortContent"));
			record.put("REMIND_TIME", EasyDate.getCurrentDateString());
			record.put("REMIND_DATE", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			JSONObject sendUserJson = getPUserInfo(json.getString("agentNo"));
			if(sendUserJson!=null && !sendUserJson.isEmpty() && !"1".equals(resortType)) {//发起告警
				record.put("DISTRIBUTE_TIME", EasyDate.getCurrentDateString());//派发时间
				record.put("DISTRIBUTE_AGENT_ID", sendUserJson.getString("USER_ID"));//派发人id
				record.put("DISTRIBUTE_AGENT_ACC", sendUserJson.getString("USER_ACCT"));//派发工号
				record.put("DISTRIBUTE_COUNT", 1);//如果首次接受有人的话 转派数量为1
				//在派发记录表中记录派发人
				query.execute("insert " + Constants.getBusiName() +".cx_agent_help_distribute_table(DISTRIBUTE_ID,MSG_ID,DISTRIBUTE_TIME,"
					+ "DISTRIBUTE_AGENT_ID,DISTRIBUTE_AGENT_ACC) values(?,?,?,?,?)"
	    			,new Object[] {RandomKit.randomStr(),json.getString("msgId"),EasyDate.getCurrentDateString(),
	    			sendUserJson.getString("USER_ID"),sendUserJson.getString("USER_ACCT")});
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(json);
				sendLight(sendUserJson.getString("SSO_ACCT"),sendUserJson.getString("LAST_LOGIN_IP"),
						sendUserJson.getString("AGENT_PHONE"),list,"1");//发送开启告警灯
			}
			if("1".equals(resortType)) {//为取消告警时
				String msgId = json.getString("msgId");
				String sql = "select DISTRIBUTE_AGENT_ID from "+Constants.getBusiName()
						+".cx_agent_remind_table where MSG_ID=?";
				logger.info("取消求助[sql]:"+sql+"[msgId]"+msgId);
				String userId = readQuery.queryForString(sql, new Object[] {msgId});
				if(StringUtils.isNotBlank(userId)) {//如果有处理人
					sendUserJson = readQuery.queryForRow("select * from cc_user where user_id=?", new Object[] {userId},new JSONMapperImpl());
					if(sendUserJson!=null&& !sendUserJson.isEmpty()) {
						List<JSONObject> list = new ArrayList<JSONObject>();
						list.add(json);
						sendLight(sendUserJson.getString("SSO_ACCT"),sendUserJson.getString("LAST_LOGIN_IP"),
								sendUserJson.getString("AGENT_PHONE"),list,"0");//发送关闭告警灯
					}
					
				}
			}
			//logger.info("求助=》"+record.toJSONString());
			if(!query.update(record)) {
				query.save(record);
			}
			List<JSONObject> lists = null;
			query.setConvertField(9);//默认
			lists = query.queryForList("select MSG_ID,AGENT_NO agentId,AGENT_NAME agentName,AGENT_PHONE agentPhone,GROUP_NAME agentGroup from "
							+ Constants.getBusiName()+".cx_agent_remind_table where IS_READ ='0' and AGENT_NO=?"
					, new Object[]{json.getString("agentNo")}, new JSONMapperImpl());
			if (lists.size() > 0){
				CommonUtils.sendAgent("1", "", "" ,Constants.getEntId(), lists,null);//发送告警
				for (JSONObject jsonObject : lists) {
					record = new EasyRecord(Constants.getBusiName()+".cx_agent_alarm_12345","MSG_ID","ALARM_TYPE");//保存统一告警表
					record.set("MSG_ID", jsonObject.getString("MSG_ID"));
					record.set("ALARM_TYPE", "8");//求助
					record.set("IS_ALARM", "2");
					record.set("HANDLE_USER_ACCT", "被系统处理");
					query.update(record);//将当前坐席的历史求助更新为已处理
					record = new EasyRecord(Constants.getBusiName()+".cx_agent_remind_table","MSG_ID");//保存统一告警表
					record.set("MSG_ID", jsonObject.getString("MSG_ID"));
					record.set("IS_READ", "1");
					query.update(record);//将当前坐席的历史求助更新为已处理
				}
				query.setConvertField(2);//全大写
			}


//			record = new EasyRecord(Constants.getBusiName()+".cx_agent_alarm_12345","AGENT_ID","ALARM_TYPE");//保存统一告警表
//			record.set("ALARM_TYPE", "8");//求助
//			record.set("AGENT_ID", json.getString("agentNo"));
//			record.set("IS_ALARM", "2");
//			record.set("HANDLE_USER_ACCT", "被系统处理");
			query.update(record);//将当前坐席的历史求助更新为已处理
			record = new EasyRecord(Constants.getBusiName()+".cx_agent_alarm_12345","MSG_ID","ALARM_TYPE");//保存统一告警表
			record.set("MSG_ID", json.getString("msgId"));
			record.set("ALARM_TYPE", "8");//求助
			record.set("MONTH_ID", EasyDate.getCurrentDateString("yyyyMM"));
			record.set("DATE_ID", EasyDate.getCurrentDateString("yyyyMMdd"));
			record.set("AGENT_ID", json.getString("agentNo"));
			record.set("AGENT_NAME", json.getString("agentName"));
			record.set("AGENT_PHONE", json.getString("agentPhone"));
			record.set("ALARM_TIME", EasyDate.getCurrentDateString());
			record.set("ALARM_COUNT", "1");
			record.set("HELP_TYPE", helpType);
			record.set("ALARM_COUNT", "1");
			if("1".equals(resortType)) {//取消求助更新为不告警
				record.set("IS_ALARM", "2");
			}
			if(!query.update(record)){
				query.save(record);
			}
//			sendNotice(json.getString("agentNo"),json.getString("agentName"),json.getString("groupName")
//					,json.getString("resortTypeName"),json.getString("resortType"), json.getString("agentIp"));
			result.put("result", "0");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e.getMessage(),e);
			result.put("result", "1");
			result.put("info", e.getMessage());
		}
		return result;
	}
	
	/**
	 * 获取值班长详情
	 * @param ssoAcct
	 * @return
	 */
	public JSONObject getPUserInfo(String ssoAcct) {//目前坐席使用的企业呼叫中心的组织架构，然后坐席通过外部账号绑定坐席本平台的账号
		JSONObject pUserinfo = new JSONObject();
		String updateSql = "update cc_user t1 set t1.LOGOUT_TIME=(select LOGOUT_TIME from cc_login_log t2 where t1.user_ACCT=t2.user_acct order by LOGIN_TIME desc limit 1) where (t1.LOGOUT_TIME='' or LOGOUT_TIME is null)  and LOGIN_TIME is not null";
		try {
			query.execute(updateSql);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		EasySQL sql = new EasySQL("select t1.* from cc_user t1 inner join "+Constants.getBusiName()+".cc_skill_group_user");
		sql.append(" t2 on t1.user_id=t2.user_id and IS_LEADER='Y' inner join ");
//		sql.append("(select t3.P_GROUP_ID from (SELECT * from cc_user where 1=1 ");
//		sql.append(ssoAcct," and SSO_ACCT=?");
//		sql.append(" limit 1) t1 ");
//		sql.append("inner join "+Constants.RECIVE_KEY_DB+".cc_skill_group_user t2 on t1.user_id=t2.user_id inner join ");
		sql.append("(select t2.SKILL_GROUP_ID from (SELECT * from "+Constants.getBusiName()+".tagentInfo where 1=1 ");
		sql.append(ssoAcct," and AGENTID=?");
		sql.append(Constants.getHwVdnId()," and VDN=?");
		sql.append(Constants.getHwCcId()," and SUBCCNO=?");
		sql.append(" limit 1) t1 ");
		sql.append("inner join "+Constants.getBusiName()+".cc_skill_group t2 on t1.AGENTWORKGROUP=t2.SKILL_GROUP_NAME ");
		sql.append("and t2.SKILL_GROUP_TYPE='struct') t3 on t2.SKILL_GROUP_ID=t3.SKILL_GROUP_ID");
		//sql.append("  inner join "+Constants.RECIVE_KEY_DB+".cx_help_ip_list t5 on t1.LAST_LOGIN_IP=t5.HELP_IP ");
		sql.append("where (t1.LOGOUT_TIME='' or LOGOUT_TIME is null)  and LOGIN_TIME is not null");
		try {
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(list != null && list.size()>0) {//如果有数据
				JSONObject r = null;
				//获取用户登录状态
				JSONObject j = null;
				for (JSONObject user : list) {
//					j = new JSONObject();
//					j.put("userAcc", user.getString("USER_ACCT"));
//					j.put("command", "srhUserLoginState");
//					try {
//						IService service = ServiceContext.getService("CCBASE_CCUSER_INTERFACE");//查询当前值班长的状态
//						r = service.invoke(j);
//					} catch (ServiceException e) {
//						logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
//					}
//					if(r!=null && "Y".equals(r.getString("loginState"))){//或者当前值班长是否登录，如果登录了则返回当前坐席
//						pUserinfo = user;
//						return pUserinfo;
//					}
					pUserinfo = user;
					return pUserinfo;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return pUserinfo;
	}
	/**
	 * 发送灯光，让亮或者关闭
	 */
	public void sendLight(String ssoAcct,String loginIp,String agentNo,List<JSONObject> agentInfoList,String switchType) {
		try {
			
			JSONObject param = new JSONObject();
			param.put("msgId", RandomKit.randomStr());//本次消息交互的消息流水号,uuid
			param.put("switchType", switchType);//物理灯开关类型，0灭灯，1亮灯
			param.put("agentPhone", StringUtils.isBlank(ssoAcct)?agentNo:ssoAcct);//支撑坐席话机号码
			param.put("agentIp", loginIp);//支撑坐席所在机器IP
			param.put("agentNo", agentNo);//支撑坐席工号
			param.put("agentInfoList", agentInfoList);//支撑席求助信息数组
			HttpResp resp = HttpUtil.sendPost(AppContext.getContext("cx-report-12345").getProperty("CX_WARN_ADDR", "")+"/aiamgr/msg/lightSwitch.do"
					, param.toJSONString(), HttpUtil.TYPE_JSON, "UTF-8");
			logger.info("入参："+param.toJSONString()+"；返回值："+resp.getResult());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
	}
	
	
	public static void main(String[] args) {
		int number1 = 11,number2=17;
		for (int i = 0; i <= 500; i++) {
			if(i%number1==0 && i%number2==0) {
				System.out.println(i);
			}
			
		}
	}
}
