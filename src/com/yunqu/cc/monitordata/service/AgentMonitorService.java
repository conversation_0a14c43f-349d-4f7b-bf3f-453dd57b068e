package com.yunqu.cc.monitordata.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CacheUtil;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.cc.monitordata.util.*;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理查询坐席监控请求
 */
public class AgentMonitorService {

    private static final Logger logger = CommonLogger.getLogger("agentMonitor");

    private static final EasyCache cache = CacheManager.getMemcache();

    /**
     * 首页 ->总签入席
     * 在线 logined
     * 通话 talking
     * 示忙 setbusy
     * 话后 lateAdjust
     */
    public static JSONObject allLogonAgent(JSONObject requestData) {
        JSONObject result = new JSONObject();
        try {
            if (requestData != null) {
                String skillIds = requestData.getString("skillIds");
                String str = cache.get("queryStatInfoOfEverySkill:" + skillIds);
                if (StringUtils.isNotBlank(str)) {
                    JSONObject resultData = JSONObject.parseObject(str);
                    JSONObject skillInfo = resultData.getJSONObject("skillInfo");
                    JSONObject callCountResultBean = skillInfo.getJSONObject("callCountResultBean");
                    JSONObject agentCountResultBean = skillInfo.getJSONObject("agentCountResultBean");
                    // 排队的呼叫数（等待呼叫数）。
                    String callWaitNums = callCountResultBean.getString("callWaitNums");
                    // 签入话务员数。
                    String agentLoginNums = agentCountResultBean.getString("agentLoginNums");
                    // 通话话务员数。
                    String agentTalkingNums = agentCountResultBean.getString("agentTalkingNums");
                    // 空闲话务员数。
                    String agentIdleNums = agentCountResultBean.getString("agentIdleNums");
                    // 示忙话务员数。
                    String agentSetbusyNums = agentCountResultBean.getString("agentSetbusyNums");
                    // 事后整理话务员数，包括调整态等。
                    String agentWorkNums = agentCountResultBean.getString("agentWorkNums");
                    result.put("logined", ParamUtil.parseStrToInt(agentLoginNums));
                    result.put("talking", ParamUtil.parseStrToInt(agentTalkingNums));
                    result.put("setbusy", ParamUtil.parseStrToInt(agentSetbusyNums));
                    result.put("lateAdjust", ParamUtil.parseStrToInt(agentWorkNums));
                    result.put("idle", ParamUtil.parseStrToInt(agentIdleNums));
                    result.put("callWait", ParamUtil.parseStrToInt(callWaitNums));
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 首页 柱状图
    // 查询多个技能队列信息
    public static List<JSONObject> querySkillsCallInfo(JSONObject requestData) {
        List<JSONObject> result = new ArrayList<>();
        try {
            // 技能队列信息
            String skillGroupInfo = CommonUtils.querySkillInfoList();
            Map<String, String> skillMap = new HashMap<>();
            if (StringUtils.isNotBlank(skillGroupInfo)) {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                List<Map<String, String>> list = JSON.parseObject(skillGroupInfo, new TypeReference<List<Map<String, String>>>() {
                });
                skillMap = list.stream().collect(Collectors.toMap(t -> t.get("SKILLGROUPID"), t -> t.get("SKILLGROUPNAME"), (o, n) -> n, HashMap::new));
            }
            if (requestData != null) {
                String skillIds = requestData.getString("skillIds");
                String[] skillIdArr = skillIds.split(",");
                for (String skillId : skillIdArr) {
                    JSONObject obj = new JSONObject();
                    String str = cache.get("queryStatInfoOfEverySkill:" + skillId);
                    if (StringUtils.isNotBlank(str)) {
                        JSONObject resultData = JSONObject.parseObject(str);
                        JSONObject skillInfo = resultData.getJSONObject("skillInfo");
                        JSONObject callCountResultBean = skillInfo.getJSONObject("callCountResultBean");
                        JSONObject agentCountResultBean = skillInfo.getJSONObject("agentCountResultBean");
                        // 排队的呼叫数（等待呼叫数）。
                        String callWaitNums = callCountResultBean.getString("callWaitNums");
                        // 签入话务员数。
                        String agentLoginNums = agentCountResultBean.getString("agentLoginNums");
                        // 通话话务员数。
                        String agentTalkingNums = agentCountResultBean.getString("agentTalkingNums");
                        // 空闲话务员数。
                        String agentIdleNums = agentCountResultBean.getString("agentIdleNums");
                        // 示忙话务员数。
                        String agentSetbusyNums = agentCountResultBean.getString("agentSetbusyNums");
                        // 事后整理话务员数，包括调整态等。
                        String agentWorkNums = agentCountResultBean.getString("agentWorkNums");
                        obj.put("skillId", skillId);
                        obj.put("skillName", skillMap.get(skillId));
                        obj.put("callWaitNums", ParamUtil.parseStrToInt(callWaitNums));
                        obj.put("agentLoginNums", ParamUtil.parseStrToInt(agentLoginNums));
                        obj.put("agentTalkingNums", ParamUtil.parseStrToInt(agentTalkingNums));
                        obj.put("agentIdleNums", ParamUtil.parseStrToInt(agentIdleNums));
                        obj.put("agentSetbusyNums", ParamUtil.parseStrToInt(agentSetbusyNums));
                        obj.put("agentWorkNums", ParamUtil.parseStrToInt(agentWorkNums));
                        result.add(obj);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 首页  业务类别占比
    public static JSONObject busiTypeRatio(JSONObject requestData) {
        JSONObject result = new JSONObject();
        int inCallNums01 = 0;
        int inCallNums02 = 0;
        int inCallNums03 = 0;
        int inCallNums04 = 0;
        try {
            String skillInfoList = CommonUtils.querySkillInfoList();
            List<Map<String, String>> list = JSON.parseObject(skillInfoList, new TypeReference<List<Map<String, String>>>() {
            });
            for (Map<String, String> map : list) {
                String skillGroupName = map.get("SKILLGROUPNAME");
                String skillGroupId = map.get("SKILLGROUPID");
                if ("18".equals(skillGroupId)) {
                    continue;
                }
                // 技能组 对应类型：01接话  02网络 03回访 04业务
                String skillType = CommonUtils.getSkillType(skillGroupName);
                String inCallNums = "0";
                // 查询技能队列信息以及累计最长排队时长
                String statInfoStr = cache.get("queryStatInfoOfEverySkill:" + skillGroupId);
                if (StringUtils.isNotBlank(statInfoStr)) {
                    JSONObject statInfo = JSONObject.parseObject(statInfoStr);
                    JSONObject skillInfo = statInfo.getJSONObject("skillInfo");
                    JSONObject agentCountResultBean = skillInfo.getJSONObject("agentCountResultBean");
                    // 通话话务员数。
                    inCallNums = agentCountResultBean.getString("agentTalkingNums");
                }
                if ("01".equals(skillType)) {
                    if (StringUtils.isNotBlank(inCallNums)) {
                        inCallNums01 += Integer.parseInt(inCallNums);
                    }
                } else if ("02".equals(skillType)) {
                    if (StringUtils.isNotBlank(inCallNums)) {
                        inCallNums02 += Integer.parseInt(inCallNums);
                    }
                } else if ("03".equals(skillType)) {
                    if (StringUtils.isNotBlank(inCallNums)) {
                        inCallNums03 += Integer.parseInt(inCallNums);
                    }
                } else if ("04".equals(skillType)) {
                    if (StringUtils.isNotBlank(inCallNums)) {
                        inCallNums04 += Integer.parseInt(inCallNums);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        result.put("inCallNums01", inCallNums01);
        result.put("inCallNums02", inCallNums02);
        result.put("inCallNums03", inCallNums03);
        result.put("inCallNums04", inCallNums04);
        return result;
    }

    // 首页 右下角 数据统计
    public static JSONObject homeDataStat(JSONObject requestData) {
        JSONObject result = new JSONObject();
        String totalInCallCount = "0";
        String onlineAgentCount = "0";
        String agentInCallCount = "0";
        try {
            // 技能队列id
//            String skillIds = requestData.getString("skillIds");
            JSONObject countedallcallstats = CtiUtils.countedallcallstats();

            JSONArray vdnAndNodes = countedallcallstats.getJSONArray("vdnAndNodes");
            if (vdnAndNodes != null) {
                for (int i = 0; i < vdnAndNodes.size(); i++) {
                    JSONObject vdnAndNode = vdnAndNodes.getJSONObject(i);
                    if (Constants.getHwVdnId().equals(vdnAndNode.getString("vdnId"))) {
                        JSONObject totalCallStat = vdnAndNode.getJSONObject("totalCallStat");
                        JSONObject totalResource = vdnAndNode.getJSONObject("totalResource");
                        // 当前呼入呼叫的总个数
                        totalInCallCount = totalCallStat.getString("totalInCallCount");
                        // 在线座席数
                        onlineAgentCount = totalResource.getString("onlineAgentCount");
                        // 当前服务设备为Agent的呼入呼叫个数
                        agentInCallCount = totalCallStat.getString("agentInCallCount");
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        result.put("totalInCallCount", ParamUtil.parseStrToInt(totalInCallCount));
        result.put("onlineAgentCount", ParamUtil.parseStrToInt(onlineAgentCount));
        result.put("agentInCallCount", ParamUtil.parseStrToInt(agentInCallCount));
        return result;
    }

    // 首页  接通率图表
    public static JSONObject homeCallRate(JSONObject requestData) {
        JSONObject result = new JSONObject();
        try {
//            String skillIds = requestData.getString("skillIds");
            String skillIds = "1";
            String str = cache.get("skilltrafficstat:" + skillIds);
            if (StringUtils.isNotBlank(str)) {
                JSONArray skilltrafficstat = JSONArray.parseArray(str);
                //正序
                skilltrafficstat.sort(Comparator.comparing(st -> ((JSONObject) st).getString("time")));
                List<String> xList = new ArrayList<>();
                List<String> yList = new ArrayList<>();
                if (skilltrafficstat != null && skilltrafficstat.size() > 0) {
                    for (int i = 0; i < skilltrafficstat.size(); i++) {
                        JSONObject obj = skilltrafficstat.getJSONObject(i);
                        Long time = obj.getLong("time");
                        String dateStr = DateUtil.getDateStrByTimeStamp(time);
                        if (StringUtils.isNotBlank(dateStr)) {
                            xList.add(dateStr.substring(11, 16));
                        }
                        String callRate = obj.getString("callRate");
                        yList.add(callRate);
                    }
                } else {
                    String currentDateStr = DateUtil.getCurrentDateStr();
                    String firstHour = DateUtil.addHour("yyyy-MM-dd HH:mm:ss", currentDateStr, -1);
                    List<String> minutes = DateUtil.getMinutes(firstHour, currentDateStr, "yyyy-MM-dd HH:mm:ss");
                    for (String minute : minutes) {
                        xList.add(minute.substring(11, 16));
                        yList.add("0");
                    }
                }
                result.put("x", xList);
                result.put("y", yList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 首页 接通率（昨日）
     * @return
     */
    public static JSONObject homeYesterdayCallRate() {
        JSONObject result = new JSONObject();
        EasyQuery readQuery = QueryFactory.getReadQuery();
        List<String> xList = new ArrayList<>();
        List<String> yList = new ArrayList<>();
        // 获取前一天时间
        String dateId = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr("yyyy-MM-dd"), -1);

        try {
            String endDate = DateUtil.getCurrentDateStr("HH:mm");
            String startDate = DateUtil.addMinute("HH:mm", endDate, -60);
            String sql = "select HOUR_ID,CALL_RATE from " + Constants.getBusiName() + ".CX_CTI_CALL_DATA where DATE_ID = ? and HOUR_ID >= ? and HOUR_ID <= ?";
            List<JSONObject> list = readQuery.queryForList(sql, new Object[]{dateId,startDate,endDate}, new JSONMapperImpl());
            if(list == null || list.isEmpty()){
                String month = "";
                if (!EasyDate.getCurrentDateString("yyyy-MM").equals(dateId.substring(0, 7))) {
                    String last = EasyDate.addTime("yyyy-MM-dd", EasyDate.getCurrentDateString("yyyy-MM-dd"), EasyCalendar.MONTH, -1);
                    if (last.substring(0, 7).equals(dateId.substring(0, 7)) && LocalDateTime.now().getDayOfMonth() == 1 && LocalDateTime.now().getHour() < 2) {
                        month = "";
                    } else {
                        month = dateId.substring(0, 7).replace("-", "");
                    }
                }
                endDate = DateUtil.addDay("yyyy-MM-dd HH:mm", DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm"), -1);
                startDate = DateUtil.addMinute("yyyy-MM-dd HH:mm", endDate, -60) + ":00";
                endDate += ":59";
                sql = "select" +
                        " DATE_FORMAT(CALL_BEGIN, '%H:%i') HOUR_ID," +
                        "ROUND((sum(case when AGENT_TIME + ACK_TIME > 0 then 1 else 0 end) / sum(case when AGENT_COUNT + WAIT_COUNT > 0 then 1 else 0 end)) * 100,2) CALL_RATE"+
                        " from " + Constants.getBusiName() +
                        ".tbillrecord" + month +
                        " where" +
                        " CALL_BEGIN >= ?" +
                        " AND CALL_BEGIN <= ?" +
                        " AND CALLTYPE in(0, 1, 2, 3, 4, 5, 13, 15, 20, 21, 22, 23, 32, 33, 46)" +
                        " AND USER_LEVEL <> 9" +
                        " group by HOUR_ID" +
                        " ORDER BY HOUR_ID ASC";
                list = readQuery.queryForList(sql, new Object[]{startDate,endDate}, new JSONMapperImpl());
            }
            //正序
            list.sort(Comparator.comparing(st -> st.getString("HOUR_ID")));
            for (JSONObject obj : list) {
                String hourId = obj.getString("HOUR_ID");
                xList.add(hourId);
                // 计算接通率
                String callRate = obj.getString("CALL_RATE");;
                yList.add(callRate);
            }
            result.put("x", xList);
            result.put("y", yList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 查询技能队列下监控数据（顶部数据）调用该接口，就是点击某一个技能队列
    public static JSONObject topData(JSONObject requestData) {
        JSONObject result = new JSONObject();
        try {
            String skillIds = requestData.getString("skillIds");
            String agentstatusinfoStr = cache.get("agentstatusinfobyskillids:" + skillIds);
            if (StringUtils.isNotBlank(agentstatusinfoStr)) {
                JSONObject agentStatus = JSONObject.parseObject(agentstatusinfoStr);
                // 上班人数 -> 签入业务代表数
                String logined = agentStatus.getString("logined");
                // 通话人数 -> 通话业务代表数
                String talking = agentStatus.getString("talking");
                // 空闲人数 -> 空闲业务代表数
                String idle = agentStatus.getString("idle");
                // 可用人数 -> 可用业务代表数
                String usable = agentStatus.getString("usable");
                // 示忙人数 -> 示忙业务代表数
                String setbusy = agentStatus.getString("setbusy");
                result.put("logined", ParamUtil.parseStrToInt(logined));
                result.put("talking", ParamUtil.parseStrToInt(talking));
                result.put("idle", ParamUtil.parseStrToInt(idle));
                result.put("usable", ParamUtil.parseStrToInt(usable));
                result.put("setbusy", ParamUtil.parseStrToInt(setbusy));
            }

            //休息人数 -> 休息话务员数
            String agentRestNums = "0";
            //最长坐席空闲时间（秒） -> 最长座席空闲时间（5分钟内）
            String agentMaxIdleTime = "0";
            //正在处理呼叫数 -> 正在处理的呼叫数
            String processingCallNums = "0";
            //排队呼叫数 -> 排队的呼叫数（等待呼叫数）
            String callWaitNums = "0";
            //平均通话时长（秒） -> 平均通话时长（5分钟内平均)
            String evenCallTime = "0";
            // 平均呼叫等待时长 ->  平均呼叫等待时长（5分钟内平均）
            String evenWaitTime = "0";

            // 查询技能队列信息以及累计最长排队时长
            String statInfoStr = cache.get("queryStatInfoOfEverySkill:" + skillIds);
            if (StringUtils.isNotBlank(statInfoStr)) {
                JSONObject statInfo = JSONObject.parseObject(statInfoStr);
                JSONObject skillInfo = statInfo.getJSONObject("skillInfo");
                JSONObject callCountResultBean = skillInfo.getJSONObject("callCountResultBean");
                JSONObject agentCountResultBean = skillInfo.getJSONObject("agentCountResultBean");
                //休息人数 -> 休息话务员数
                agentRestNums = agentCountResultBean.getString("agentRestNums");
                //最长坐席空闲时间（秒） -> 最长座席空闲时间（5分钟内）
                agentMaxIdleTime = agentCountResultBean.getString("agentMaxIdleTime");
                //正在处理呼叫数 -> 正在处理的呼叫数
                processingCallNums = callCountResultBean.getString("processingCallNums");
                //排队呼叫数 -> 排队的呼叫数（等待呼叫数）
                callWaitNums = callCountResultBean.getString("callWaitNums");
                //平均通话时长（秒） -> 平均通话时长（5分钟内平均)
                evenCallTime = callCountResultBean.getString("evenCallTime");
                // 平均呼叫等待时长 ->  平均呼叫等待时长（5分钟内平均）
                evenWaitTime = callCountResultBean.getString("evenWaitTime");
            }
            String callstatusinfoStr = cache.get("callstatusinfo:" + skillIds);
            if (StringUtils.isNotBlank(callstatusinfoStr)) {
                JSONObject callstatusinfo = JSONObject.parseObject(callstatusinfoStr);
                //TODO 每分钟平均处理呼叫数 -> 每分钟处理呼叫数。
                String totalCallNums = callstatusinfo.getString("totalCallNums");
                result.put("agentRestNums", ParamUtil.parseStrToInt(agentRestNums));
                result.put("agentMaxIdleTime", ParamUtil.parseStrToInt(agentMaxIdleTime));
                result.put("processingCallNums", ParamUtil.parseStrToInt(processingCallNums));
                result.put("callWaitNums", ParamUtil.parseStrToInt(callWaitNums));
                result.put("evenCallTime", ParamUtil.parseStrToInt(evenCallTime));
                result.put("evenWaitTime", ParamUtil.parseStrToInt(evenWaitTime));
                result.put("totalCallNums", ParamUtil.parseStrToInt(totalCallNums));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 查询技能队列下监控数据（中间柱状图数据）调用该接口，就是点击某一个技能队列
    public static JSONObject middleData(JSONObject requestData) {
        JSONObject result = new JSONObject();
        try {
            String skillIds = requestData.getString("skillIds");
            /* ===============业务代表状态================== */
            String agentstatusinfoStr = cache.get("agentstatusinfobyskillids:" + skillIds);
            // 签入业务代表数
            String logined = "0";
            // 通话业务代表数
            String talking = "0";
            // 空闲业务代表数
            String idle = "0";
            // 示忙业务代表数
            String setbusy = "0";
            // 事后整理业务代表数
            String lateAdjust = "0";
            // 休息业务代表数
            String rest = "0";
            // 示忙率 -> 业务代表示忙率。返回值的类型为浮点数。
            String busyRate = "0";
            if (StringUtils.isNotBlank(agentstatusinfoStr)) {
                JSONObject agentStatus = JSONObject.parseObject(agentstatusinfoStr);
                // 签入业务代表数
                logined = agentStatus.getString("logined");
                // 通话业务代表数
                talking = agentStatus.getString("talking");
                // 空闲业务代表数
                idle = agentStatus.getString("idle");
                // 示忙业务代表数
                setbusy = agentStatus.getString("setbusy");
                // 事后整理业务代表数
                lateAdjust = agentStatus.getString("lateAdjust");
                // 休息业务代表数
                rest = agentStatus.getString("rest");
                // 示忙率 -> 业务代表示忙率。返回值的类型为浮点数。
                busyRate = agentStatus.getString("busyRate");
            }
            /* ===============呼叫量================== */
            // 正在处理呼叫数 -> 正在处理的呼叫数。
            String processingCallNums = "0";
            // 每分钟处理呼叫数
            String totalCallNums = "0";
            // 排队呼叫数 -> 排队的呼叫数（等待呼叫数）。
            String callWaitNums = "0";
            // 话务量
            String inCallNums = "0";
            // 短通话数 -> 短通话数
            String shortCallNums = "0";

            /* ===============时长================== */
            // TODO 最长业务代表空闲时长 -> 最长座席空闲时长（5分钟内）
            String agentMaxIdleTime = "0";
            // 最长呼叫等待时间 ->最长呼叫等待时长
            String maxCallWaitTimes = "0";
            // 平均通话时长 -> 平均通话时长（5分钟内）
            String evenCallTime = "0";
            // 平均呼叫等待时长 ->  平均呼叫等待时长（5分钟内平均）
            String evenWaitTime = "0";

            /* ===============接通率================== */
            // 接通率 -> 呼叫接通率。返回值的类型为浮点数。
            String callRate = "0";
            // N秒接通率 ->  N秒接通率。返回值的类型为浮点数。
            String nCallRate = "0";
            String callstatusinfoStr = cache.get("callstatusinfo:" + skillIds);
            if (StringUtils.isNotBlank(callstatusinfoStr)) {
                JSONObject callstatusinfo = JSONObject.parseObject(callstatusinfoStr);
                // 正在处理呼叫数 -> 正在处理的呼叫数。
                processingCallNums = callstatusinfo.getString("processingCallNums");
                // 每分钟处理呼叫数
                totalCallNums = callstatusinfo.getString("totalCallNums");
                // 排队呼叫数 -> 排队的呼叫数（等待呼叫数）。
                callWaitNums = callstatusinfo.getString("callWaitNums");
                // 话务量
                inCallNums = callstatusinfo.getString("inCallNums");
                // 短通话数 -> 短通话数
                shortCallNums = callstatusinfo.getString("shortCallNums");

                /* ===============时长================== */
                // TODO 最长业务代表空闲时长 -> 最长座席空闲时长（5分钟内）
                agentMaxIdleTime = callstatusinfo.getString("agentMaxIdleTime");
                // 最长呼叫等待时间 ->最长呼叫等待时长
                maxCallWaitTimes = callstatusinfo.getString("maxCallWaitTimes");
                // 平均通话时长 -> 平均通话时长（5分钟内）
                evenCallTime = callstatusinfo.getString("evenCallTime");
                // 平均呼叫等待时长 ->  平均呼叫等待时长（5分钟内平均）
                evenWaitTime = callstatusinfo.getString("evenWaitTime");

                /* ===============接通率================== */
                // 接通率 -> 呼叫接通率。返回值的类型为浮点数。
                callRate = callstatusinfo.getString("callRate");
                // N秒接通率 ->  N秒接通率。返回值的类型为浮点数。
                nCallRate = callstatusinfo.getString("ncallRate");
            }

            // 期望接通率 -> 技能队列的期望接通率
            String expiredEap = "0";
            String skillstatusinfoStr = cache.get("skillstatusinfo:" + skillIds);
            if (StringUtils.isNotBlank(skillstatusinfoStr)) {
                JSONObject skillstatusinfo = JSONObject.parseObject(skillstatusinfoStr);
                expiredEap = skillstatusinfo.getString("expiredEap");
            }
            result.put("logined", ParamUtil.parseStrToInt(logined));
            result.put("talking", ParamUtil.parseStrToInt(talking));
            result.put("idle", ParamUtil.parseStrToInt(idle));
            result.put("setbusy", ParamUtil.parseStrToInt(setbusy));
            result.put("workSubstateBegin", ParamUtil.parseStrToInt(lateAdjust));
            result.put("rest", ParamUtil.parseStrToInt(rest));
            result.put("processingCallNums", ParamUtil.parseStrToInt(processingCallNums));
            result.put("totalCallNums", ParamUtil.parseStrToInt(totalCallNums));
            result.put("callWaitNums", ParamUtil.parseStrToInt(callWaitNums));
            result.put("maxCallWaitTimes", ParamUtil.parseStrToInt(maxCallWaitTimes));
            result.put("agentMaxIdleTime", ParamUtil.parseStrToInt(agentMaxIdleTime));
            result.put("evenCallTime", ParamUtil.parseStrToInt(evenCallTime));
            result.put("evenWaitTime", ParamUtil.parseStrToInt(evenWaitTime));
            result.put("inCallNums", ParamUtil.parseStrToInt(inCallNums));
            result.put("shortCallNums", ParamUtil.parseStrToInt(shortCallNums));
            result.put("callRate", ParamUtil.parseStrToDouble(callRate));
            result.put("nCallRate", ParamUtil.parseStrToDouble(nCallRate));
            result.put("expiredEap", ParamUtil.parseStrToInt(expiredEap));
            result.put("busyRate", ParamUtil.parseStrToDouble(busyRate));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 查询技能队列下监控数据（下面人员活动状态列表数据）调用该接口，就是点击某一个技能队列
    public static JSONObject belowData(JSONObject requestData) {
        JSONObject result = new JSONObject();
        List<Map<String, Object>> resultList = new ArrayList<>();

        int pageNumber = 1;
        int pageSize = 10;
        int totalRow = -1;
        int totalPage = 1;

        try {
            pageNumber = requestData.getIntValue("pageNumber");
            pageSize = requestData.getIntValue("pageSize");
            // 技能队列id
            String skillIds = requestData.getString("skillIds");
            // 班组id
            String _workGroupId = requestData.getString("workGroupId");
            // 状态
            String currentStates = requestData.getString("currentState");

            String sortType = requestData.getString("sortType");
            String sortName = requestData.getString("sortName");
            String key = requestData.getString("agentId");//关键字
            List<String> _currentStateValues = new ArrayList<>();
            if (StringUtils.isNotBlank(currentStates)) {
                for (String currentState : currentStates.split(",")) {
                    String currentStateName = CommonUtils.getAgentCurrentState(Integer.parseInt(currentState));
                    _currentStateValues.add(currentStateName);
                }
            }
            String[] skillIdArr = skillIds.split(",");
            Map<String, String> workgroupMap = new HashMap<>();
            // 班组信息
            String workGroupInfo = CommonUtils.queryWorkGroupInfoList();
            if (StringUtils.isNotBlank(workGroupInfo)) {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                List<Map<String, String>> list = JSON.parseObject(workGroupInfo, new TypeReference<List<Map<String, String>>>() {
                });
                workgroupMap = list.stream().collect(Collectors.toMap(t -> t.get("WORKGROUPID"), t -> t.get("WORKGROUP"), (o, n) -> n, HashMap::new));
            }
            for (String skillId : skillIdArr) {
                List<Map<String, Object>> newList;
                String str = cache.get("agentstatusinfo:" + skillId);
                List<Map<String, Object>> list = new ArrayList<>();
                if (StringUtils.isNotBlank(str)) {
                    JSONArray agentstatusinfoList = JSONObject.parseArray(str);
                    for (int i = 0; i < agentstatusinfoList.size(); i++) {
                        Map<String, Object> map = new HashMap<>();
                        JSONObject resultData = agentstatusinfoList.getJSONObject(i);
                        // 工号   -> 座席工号
                        Integer agentId = resultData.getInteger("agentId");
                        Integer workGroupId = resultData.getInteger("workGroupId");
                        // 班组名称待确认
                        String workGroupName = workgroupMap.get(resultData.getString("workGroupId"));
                        // 总示忙时间
                        Integer totalBusyTime = resultData.getInteger("totalBusyTime");
                        // 签入次数
                        Integer signInCount = resultData.getInteger("signInCount");
                        // 签出次数
                        Integer signOutCount = resultData.getInteger("signOutCount");
                        // 所属团队id
                        Integer team = resultData.getInteger("team");

                        // 姓名   -> 座席姓名
                        String name = resultData.getString("name");
                        // 状态   -> 当前状态 0：表示话务员未签入。1：表示话务员空闲。2：表示预占用状态。3：表示占用状态。4：表示应答状态。5：表示通话状态。6：表示工作状态。7：表示忙状态。8：表示请假休息。9：表示学习态。10：表示调整态。
                        String currentState = CommonUtils.getAgentCurrentState(resultData.getInteger("currentState"));
                        // 当前状态持续时长（秒）   -> 当前状态持续时长（秒）
                        Integer currentStateTime = resultData.getInteger("currentStateTime");
                        // 当前状态开始时间。非时间格式。返回值的类型为八字节整数。返回值为自1970年1月1日00:00:00 GMT以来此时间点的毫秒数。
                        Long curStateStartTime = resultData.getLong("curStateStartTime");
                        // 前一个状态   ->
                        String prevState = CommonUtils.getAgentCurrentState(resultData.getInteger("prevState"));
                        // 前一个状态时长   ->
                        Integer prevStateTime = resultData.getInteger("prevStateTime");

                        // 所属分布式接入节点ID   -> 所属分布式接入节点ID
                        Integer locationId = resultData.getInteger("locationId");
                        // 坐席类型   ->  座席类型
//                    Integer agentType = resultData.getInteger("agentType");
                        // 媒体能力   ->  媒体能力
//                    Integer mediaAbility = resultData.getInteger("mediaAbility");
                        // 电话号码  -> 座席电话号码。
                        String agentPhone = resultData.getString("agentPhone");
                        // 上班时间  -> 上班时间。非时间格式。返回值的类型为八字节整数。返回值为自1970年1月1日00:00:00 GMT以来此时间点的毫秒数。
                        Long loginTime = resultData.getLong("loginTime");
                        // 主动收线次数  -> 座席主动收线次数
                        Integer agentRelease = resultData.getInteger("agentRelease");
                        // 总通话时长 总的通话时长
                        Integer totalTalkingTimes = resultData.getInteger("totalTalkingTimes");
                        //TODO 总通话次数  座席接通呼叫的总次数（统计周期为当次签入）。
                        Integer totalTalkingCount = resultData.getInteger("totalTalkingCount");
                        // 平均通话时长  平均通话时长。
                        Integer avgTalkingTime = resultData.getInteger("avgTalkingTime");
                        // 保持时长 保持时长。
                        Integer keepTime = resultData.getInteger("keepTime");
                        // 保持次数
                        Integer keepNums = resultData.getInteger("keepNums");
                        // 三方通话次数
                        Integer conferenceNums = resultData.getInteger("conferenceNums");
                        // 呼出次数
                        Integer callOutNums = resultData.getInteger("callOutNums");
                        // 内部转移次数
                        Integer transferNums = resultData.getInteger("transferNums");
                        // 转出的次数
                        Integer transferOutNums = resultData.getInteger("transferOutNums");
                        // 重定向次数
                        Integer redirectNums = resultData.getInteger("redirectNums");
                        // 内部呼叫次数
                        Integer interCallNums = resultData.getInteger("interCallNums");
                        // 久叫不应次数
                        Integer noAnswerNums = resultData.getInteger("noAnswerNums");
                        // 休息次数
                        Integer restNums = resultData.getInteger("restNums");
                        // 休息超时次数
                        Integer restOutNums = resultData.getInteger("restOutNums");
                        // 休息时长
                        Integer restTime = resultData.getInteger("restTime");
                        // 实际休息时长
                        Integer actualRestTime = resultData.getInteger("actualRestTime");
                        // 休息超时时长
                        Integer restOutTime = resultData.getInteger("restOutTime");
                        // 目前私有呼收数
                        Integer currentPrivateCallNum = resultData.getInteger("currentPrivateCallNum");
                        // 转移类型
                        Integer transferFlg = resultData.getInteger("transferFlg");
                        // 转移设备
                        Integer transferDevice = resultData.getInteger("transferDevice");
                        // 密码状态
                        Integer passwordStatus = resultData.getInteger("passwordStatus");
                        // 密码剩余有效天数
                        Integer surplusDay = resultData.getInteger("surplusDay");
                        // 示忙时长
                        Integer busyTime = resultData.getInteger("busyTime");
                        // 示忙次数
                        Integer busyNums = resultData.getInteger("busyNums");
                        // 事后整理时长 ->事后整理态时长，单位：秒，仅在手动切换整理态（座席状态）时才生效。
                        Integer workTime = resultData.getInteger("workTime");
                        //TODO 这里是id，需要换成对应的中文 当前签入或者拥有的技能  -> 目前签入或拥有的技能。返回值的类型为整数数组。
                        JSONArray agentSkills = resultData.getJSONArray("agentSkills");
                        // 电话状态 座席电话状态。0：表示电话就绪，CTI可以外呼。1：表示不可用，CTI不可外呼。
                        Integer phoneState = resultData.getInteger("phoneState");
                        map.put("agentId", agentId);
                        map.put("name", name);
                        map.put("currentState", currentState);
                        map.put("currentStateCode", resultData.getInteger("currentState"));
                        map.put("currentStateTime", currentStateTime);
                        map.put("curStateStartTime", DateUtil.getDateStrByTimeStamp(curStateStartTime));
                        map.put("prevState", prevState);
                        map.put("prevStateTime", prevStateTime);
                        map.put("locationId", locationId);
                        map.put("agentType", "PC+PHONE座席");
                        map.put("mediaAbility", "音频");
                        map.put("agentPhone", agentPhone);
                        map.put("loginTime", DateUtil.getDateStrByTimeStamp(loginTime));
                        map.put("agentRelease", agentRelease);
                        map.put("totalTalkingTimes", totalTalkingTimes);
                        map.put("totalTalkingCount", totalTalkingCount);
                        map.put("avgTalkingTime", avgTalkingTime);
                        map.put("keepTime", keepTime);
                        map.put("keepNums", keepNums);
                        map.put("conferenceNums", conferenceNums);
                        map.put("callOutNums", callOutNums);
                        map.put("transferNums", transferNums);
                        map.put("transferOutNums", transferOutNums);
                        map.put("redirectNums", redirectNums);
                        map.put("interCallNums", interCallNums);
                        map.put("noAnswerNums", noAnswerNums);
                        map.put("restNums", restNums);
                        map.put("restOutNums", restOutNums);
                        map.put("restTime", restTime);
                        map.put("actualRestTime", actualRestTime);
                        map.put("restOutTime", restOutTime);
                        map.put("currentPrivateCallNum", currentPrivateCallNum);
                        map.put("transferFlg", transferFlg);
                        map.put("transferDevice", transferDevice);
                        map.put("passwordStatus", passwordStatus);
                        map.put("surplusDay", surplusDay);
                        map.put("busyTime", busyTime);
                        map.put("busyNums", busyNums);
                        map.put("workTime", workTime);
                        map.put("agentSkills", agentSkills);
                        map.put("phoneState", phoneState);
                        map.put("workGroupId", workGroupId);
                        map.put("workGroupName", workGroupName);
                        map.put("totalBusyTime", totalBusyTime);
                        map.put("signInCount", signInCount);
                        map.put("signOutCount", signOutCount);
                        map.put("team", team);
                        list.add(map);
                    }
                }
                if (StringUtils.isNotBlank(_workGroupId) && StringUtils.isNotBlank(currentStates)) {
                    // 根据逗号分隔字符串，获取匹配的值列表
                    List<String> _workGroupIdValues = Arrays.asList(_workGroupId.split(","));
                    // 班组 和 状态都选了
                    newList = list.stream()
                            .filter(map -> _workGroupIdValues.contains(String.valueOf(map.get("workGroupId"))))
                            .filter(map -> _currentStateValues.contains(String.valueOf(map.get("currentState"))))
                            .collect(Collectors.toList());
                } else if (StringUtils.isNotBlank(_workGroupId) && StringUtils.isBlank(currentStates)) {
                    // 根据逗号分隔字符串，获取匹配的值列表
                    List<String> _workGroupIdValues = Arrays.asList(_workGroupId.split(","));
                    // 只选了 班组
                    newList = list.stream()
                            .filter(map -> _workGroupIdValues.contains(String.valueOf(map.get("workGroupId"))))
                            .collect(Collectors.toList());
                } else if (StringUtils.isNotBlank(currentStates) && StringUtils.isBlank(_workGroupId)) {
                    // 只选了 状态
                    newList = list.stream()
                            .filter(map -> _currentStateValues.contains(String.valueOf(map.get("currentState"))))
                            .collect(Collectors.toList());
                } else {
                    newList = list;
                }
                //如果输入了关键字
                if (StringUtils.isNotBlank(key)) {
                    newList = newList.stream()
                            .filter(map -> key.equals(String.valueOf(map.get("agentId"))) || key.equals(String.valueOf(map.get("name"))))
                            .collect(Collectors.toList());
                }
                resultList.addAll(newList);
            }
            if (StringUtils.isNotBlank(sortType) && StringUtils.isNotBlank(sortName)) {
                if ("desc".equals(sortType)) {
                    resultList.sort(Comparator.comparing(m -> String.valueOf(m.get(sortName)), Comparator.reverseOrder()));
                } else {
                    // 默认根据班组排序 升序排序
                    resultList.sort(Comparator.comparing(m -> String.valueOf(m.get(sortName))));
                }
            } else {
                // 默认根据班组排序 升序排序
                resultList.sort(Comparator.comparing(m -> String.valueOf(m.get("workGroupId"))));
            }
            totalRow = resultList.size();
            totalPage = (totalRow + (pageSize - 1)) / pageSize;
        } catch (Exception e) {
            logger.error("查询技能队列下监控数据异常，" + e.getMessage(), e);
        }
        result.put("totalRow", totalRow);
        result.put("totalPage", totalPage);
        result.put("pageSize", pageSize);
        result.put("pageNumber", pageNumber);
        result.put("data", getPage(resultList, pageNumber, pageSize));
        return result;
    }

    // 坐席位置图监控
    public static JSONObject queryAgentPlace(JSONObject requestData,YCUserPrincipal ycuser) {
        String hfCode = requestData.getString("hfCode");//8:4楼B区 9:4楼A区 10:4楼C区 11:5楼C区 12:5楼A区，27:4楼汇总，23:5楼汇总 50:汇总
        JSONObject result = new JSONObject();
        List<Map<String, Object>> userMap = null;
        try {
            String key = getAgentPlaceKey(requestData,"1",ycuser);//获取点位图监控key
            result = cache.get(key);
            if(result!=null) {
                return result;
            }
            result = new JSONObject();
            List<Map<String, Object>> list = getHfData(hfCode);
            if (list!=null) {
                List<Map<String, Object>> statUserMap = new ArrayList<>();//需要排除状态
                userMap = getUserMap(requestData,list,ycuser,statUserMap);
                JSONObject stat = getStatData(userMap,ycuser,null,null,statUserMap);
                JSONObject statData = stat.getJSONObject("statData");
                result.put("userMap", userMap);
                result.put("statData", statData);
                cache.put(key,result,5);//缓存保存5s
            }
        } catch (Exception e) {
            logger.error("坐席位置图监控查询异常", e);
        }
        return result;
    }
    // 坐席位置图监控-按技能组
    public static JSONObject queryAgentPlaceSkill(JSONObject requestData,YCUserPrincipal ycuser) {
        String hfCode = requestData.getString("hfCode");
        JSONObject result = new JSONObject();
        List<Map<String, Object>> userMap = null;
        try {
            String key = getAgentPlaceKey(requestData,"2",ycuser);//坐席位置图监控-按技能组key
            result = cache.get(key);
            if(result!=null) {
                return result;
            }
            result = new JSONObject();
            List<Map<String, Object>> list = getHfData(hfCode);
            if (list!=null) {
                List<Map<String, Object>> statUserMap = new ArrayList<>();//需要排除状态
                userMap = getUserMap(requestData,list,ycuser,statUserMap);
                // 技能队列初始化信息
                Map<String,JSONObject> skillInitData = CommonUtils.getSkillInitData();
                String agentSkill = requestData.getString("agentSkills");
                if(StringUtils.isNotBlank(agentSkill) && !"[]".equals(agentSkill)){
                    Map<String,JSONObject> skillInitDataCopy = new HashMap<>();
                    try {
                        //logger.info("[agentSkill]"+agentSkill);
                        JSONArray arr = JSONArray.parseArray(agentSkill);
                        if(arr!=null && arr.size()>0){
                            for (int i = 0; i < arr.size(); i++) {
                                if(StringUtils.isNotBlank(arr.getString(i)))
                                    skillInitDataCopy.put(arr.getString(i),skillInitData.get(arr.getString(i)));
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        //logger.error(e.getMessage(),e);
                        String[] skillArr = agentSkill.split(",");
                        for (String skill : skillArr) {
                            skillInitDataCopy.put(skill,skillInitData.get(skill));
                        }
                    }
                    skillInitData = skillInitDataCopy;
                }
                JSONObject stat = getStatData(userMap,ycuser,skillInitData,null,statUserMap);
                JSONObject statData = stat.getJSONObject("statData");
                result.put("userMap", stat.get("skillInitData"));
                result.put("statData", statData);
                cache.put(key,result,5);//缓存保存5s
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("坐席位置图监控查询异常", e);
        }
        return result;
    }

    // 坐席位置图监控-按工作组
    public static JSONObject queryAgentPlaceWorkGroup(JSONObject requestData,YCUserPrincipal ycuser) {
        String hfCode = requestData.getString("hfCode");
        JSONObject result = new JSONObject();
        List<Map<String, Object>> userMap = null;
        try {
            String key = getAgentPlaceKey(requestData,"3",ycuser);//坐席位置图监控-按技能组key
            result = cache.get(key);
            if(result!=null) {
                return result;
            }
            result = new JSONObject();
            List<Map<String, Object>> list = getHfData(hfCode);
            if (list!=null) {
                List<Map<String, Object>> statUserMap = new ArrayList<>();//需要排除状态
                userMap = getUserMap(requestData,list,ycuser,statUserMap);
                // 技能队列初始化信息
                Map<String,JSONObject> workGroupInitData = CommonUtils.getWorkGroupInitData(ycuser.getUserId(),ycuser.getRoleType());
                String agentWorkGroup = requestData.getString("agentWorkGroup");
                if(StringUtils.isNotBlank(agentWorkGroup) && !"[]".equals(agentWorkGroup)){
                    Map<String,JSONObject> workGroupInitDataCopy = new HashMap<>();
                    try {
                        //logger.info("[agentWorkGroup]"+agentWorkGroup);
                        JSONArray arr = JSONArray.parseArray(agentWorkGroup);
                        if(arr!=null && arr.size()>0){
                            for (int i = 0; i < arr.size(); i++) {
                                if(StringUtils.isNotBlank(arr.getString(i)))
                                    workGroupInitDataCopy.put(arr.getString(i),workGroupInitData.get(arr.getString(i)));
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        //logger.error(e.getMessage(),e);
                        String[] workArr = agentWorkGroup.split(",");
                        for (String work : workArr) {
                            workGroupInitDataCopy.put(work,workGroupInitData.get(work));
                        }
                    }
                    workGroupInitData = workGroupInitDataCopy;
                }
                JSONObject stat = getStatData(userMap,ycuser,null,workGroupInitData,statUserMap);
                JSONObject statData = stat.getJSONObject("statData");
                result.put("userMap", stat.get("workGroupInitData"));
                result.put("statData", statData);
                cache.put(key,result,5);//缓存保存5s
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("坐席位置图监控查询异常", e);
        }
        return result;
    }
    /**
     * 获取话房点位图的key，用来存储缓存
     * @param requestData
     * @param type
     * @param ycuser
     * @return
     */
    public static String  getAgentPlaceKey(JSONObject requestData,String type,YCUserPrincipal ycuser){
        String hfCode = requestData.getString("hfCode");
        String currentState = requestData.getString("currentState");
        String agentName = requestData.getString("agentName");
        String agentSkill = requestData.getString("agentSkills");
        String agentWorkGroup = requestData.getString("agentWorkGroup");
        String busiType = requestData.getString("busiType");
        String key = "queryAgentPlace:" + hfCode+ "_" + currentState + "_" +type+"_"+ agentName + "_" + agentSkill + "_" + agentWorkGroup+"_"+busiType;
        if(ycuser!=null && ycuser.getRoleType()==Constants.ROLE_TYPE_MONITOR) {
            String workGroupId = CommonUtils.getUserWorkGroupId(ycuser.getUserId());
            key = key + "_" + workGroupId;
        }
        return key;
    }

    /**
     * 获取话房的数据
     * @param hfCode
     * @return
     */
    public static List<Map<String ,Object>> getHfData(String hfCode){
        List<Map<String, Object>> list = null;
        String str = "";
        str = cache.get("queryAgentPlace:" + hfCode);
        if (StringUtils.isNotBlank(str)) {
            list = JSON.parseObject(str, new TypeReference<List<Map<String, Object>>>() {});
        }
        if(list!=null){
            return list;
        }
        if("27".equals(hfCode) || "23".equals(hfCode) || "50".equals(hfCode)){
            list = new ArrayList<>();
            String str4b = cache.get("queryAgentPlace:8");
            if(StringUtils.isNotBlank(str4b) && !"23".equals(hfCode)) {
                list.addAll(JSON.parseObject(str4b, new TypeReference<List<Map<String, Object>>>() {}));
            }
            String str4a = cache.get("queryAgentPlace:9");
            if(StringUtils.isNotBlank(str4a) && !"23".equals(hfCode)) {
                list.addAll(JSON.parseObject(str4a, new TypeReference<List<Map<String, Object>>>() {}));
            }
            String str4c = cache.get("queryAgentPlace:10");
            if(StringUtils.isNotBlank(str4c) && !"23".equals(hfCode)) {
                list.addAll(JSON.parseObject(str4c, new TypeReference<List<Map<String, Object>>>() {}));
            }
            String str5c = cache.get("queryAgentPlace:11");
            if(StringUtils.isNotBlank(str5c) && !"27".equals(hfCode)) {
                list.addAll(JSON.parseObject(str5c, new TypeReference<List<Map<String, Object>>>() {}));
            }
            String str5a = cache.get("queryAgentPlace:12");
            if(StringUtils.isNotBlank(str5a) && !"27".equals(hfCode)) {
                list.addAll(JSON.parseObject(str5a, new TypeReference<List<Map<String, Object>>>() {}));
            }
            cache.put("queryAgentPlace:" + hfCode,JSONObject.toJSONString(list),5);//缓存写5s
        }
        return list;
    }

    /**
     * 获取点位图中过滤后的坐席
     * @param list
     * @param ycuser
     * @return
     */
    public static List<Map<String, Object>> getAllUserMap(JSONObject requestData,List<Map<String, Object>> list,YCUserPrincipal ycuser){
        List<Map<String, Object>> userMap = null;
        try {

        }catch (Exception e){
            e.printStackTrace();
        }
        return userMap;
    }

    /**
     * 获取点位图中过滤后的坐席
     * @param list
     * @param ycuser
     * @return
     */
    public static List<Map<String, Object>> getUserMap(JSONObject requestData,List<Map<String, Object>> list,YCUserPrincipal ycuser
            ,List<Map<String, Object>> statUserMap){
        List<Map<String, Object>> userMap = null;
        try {
            //1签入 2通话 3空闲 4话后 5示忙 7超长通话 8话后超时 9静音 10语速超快 11抢话 12违规词 13敏感词 14求助 15首发诉求
            String currentState = requestData.getString("currentState");
            String agentName = requestData.getString("agentName");
            String agentSkill = requestData.getString("agentSkills");
            String busiType = requestData.getString("busiType");//01接话  02网络 03回访 04业务
            List<String> agentSkills = new ArrayList<>();
            if(StringUtils.isNotBlank(agentSkill)){
                try {
                    //logger.info("[agentSkill]"+agentSkill);
                    JSONArray arr = JSONArray.parseArray(agentSkill);
                    if(arr!=null && arr.size()>0){
                        for (int i = 0; i < arr.size(); i++) {
                            agentSkills.add(arr.getString(i));
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    //logger.error(e.getMessage(),e);
                    String[] skillArr = agentSkill.split(",");
                    for (String skill : skillArr) {
                        agentSkills.add(skill);
                    }
                }


            }
            String agentWorkGroup = requestData.getString("agentWorkGroup");
            List<String> agentWorkGroups = new ArrayList<>();
            if(StringUtils.isNotBlank(agentWorkGroup)){
                try {
                    //logger.info("[agentWorkGroups]"+agentWorkGroups);
                    JSONArray arr = JSONArray.parseArray(agentWorkGroup);
                    if(arr!=null && arr.size()>0){
                        for (int i = 0; i < arr.size(); i++) {
                            agentWorkGroups.add(arr.getString(i));
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    //logger.error(e.getMessage(),e);
                    String[] workArr = agentWorkGroup.split(",");
                    for (String work : workArr) {
                        agentWorkGroups.add(work);
                    }
                }


            }
            // 根据逗号分隔字符串，获取匹配的值列表
            List<String> currentStateValues;
            if(StringUtils.isNotBlank(currentState)){
                if("1".equals(currentState)){
                    currentStateValues = Arrays.asList(new String[]{"1","2","3","4","5","6","7","8","9","10"});
                } else if("2".equals(currentState)){
                    currentStateValues = Arrays.asList(new String[]{"2","3","4","5"});
                } else if("3".equals(currentState)){
                    currentStateValues = Arrays.asList(new String[]{"1"});
                } else if("4".equals(currentState)){
                    currentStateValues = Arrays.asList(new String[]{"6","10"});
                } else if("5".equals(currentState)){
                    currentStateValues = Arrays.asList(new String[]{"7","8","9"});
                } else{
                    currentStateValues = null;
                }
            } else {
                currentStateValues = null;
            }
            //不是管理员角色那就是班长 就只能看到自己班组内的数据或者是指派给自己的求助的数据
            logger.info("[agent]"+ycuser.getLoginAcct()+"[role]"+ycuser.getRoleType());
            if(ycuser!=null && ycuser.getRoleType()==Constants.ROLE_TYPE_MONITOR) {
                String workGroupId = CommonUtils.getUserWorkGroupId(ycuser.getUserId());
                logger.info("【班长登录】"+ycuser.getLoginAcct()+"【班组】"+workGroupId);
                //  || ycuser.getUserId().equals(map.get("distributeAgentId"))
                userMap =  list.stream()
                        .filter(map -> workGroupId.equals(String.valueOf(map.get("agentGroup")))).collect(Collectors.toList());
                userMap.forEach(map ->{
                    if(!ycuser.getUserId().equals(map.get("distributeAgentId")) || StringUtils.isBlank(map.get("distributeAgentId")+"")) {
                        logger.info("过滤agentPhone："+map.get("agentPhone"));
                        map.put("seekHelpMsgId", "");
                    }
                });
                logger.info("【班长登录】"+ycuser.getLoginAcct()+"【筛选后数据】"+JSON.toJSONString(userMap));
            }else{
                userMap = list;
            }
            statUserMap.addAll(userMap.stream()
                    .filter(map -> StringUtils.isNotBlank(agentName)?
                            agentName.contains(String.valueOf(map.get("agentName"))) || agentName.contains(String.valueOf(map.get("agentId"))) : true)
                    .filter(map ->agentSkills.size()>0 ? agentSkills.contains(String.valueOf(map.get("agentSkill"))) : true)//先技能组只存储当前签入的，排除总签入席
                    .filter(map ->agentWorkGroups.size()>0?agentWorkGroups.contains(String.valueOf(map.get("agentGroup"))) : true)
                    .filter(map ->"04".equals(busiType)?String.valueOf(map.get("agentGroupName")).contains("培训")
                            || String.valueOf(map.get("agentGroupName")).contains("诉求") || String.valueOf(map.get("agentGroupName")).contains("信息")
                            || String.valueOf(map.get("agentGroupName")).contains("业务") || String.valueOf(map.get("agentGroupName")).contains("值班长")
                            || String.valueOf(map.get("agentGroupName")).contains("质检"): true)
                    .filter(map ->"03".equals(busiType)?String.valueOf(map.get("agentGroupName")).contains("回访") : true)
                    .filter(map ->"02".equals(busiType)?String.valueOf(map.get("agentGroupName")).contains("网络") : true)
                    .filter(map ->"01".equals(busiType)?!String.valueOf(map.get("agentGroupName")).contains("培训")
                            && !String.valueOf(map.get("agentGroupName")).contains("诉求") && !String.valueOf(map.get("agentGroupName")).contains("信息")
                            && !String.valueOf(map.get("agentGroupName")).contains("业务") && !String.valueOf(map.get("agentGroupName")).contains("值班长")
                            && !String.valueOf(map.get("agentGroupName")).contains("质检") && !String.valueOf(map.get("agentGroupName")).contains("网络")
                            && !String.valueOf(map.get("agentGroupName")).contains("回访"): true)
                    .collect(Collectors.toList()));
            //userMap.addAll(statUserMap);
            userMap = statUserMap.stream().filter(map -> "7".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("extraLongCallMsgId"))): true)//超长通话
                    .filter(map -> currentStateValues!=null?currentStateValues.contains(String.valueOf(map.get("currentState"))) : true)
                    .filter(map -> "8".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("afterLongMsgId"))): true)//话后超时
                    .filter(map -> "9".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("muteMsgId"))): true)//静音
                    .filter(map -> "10".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("speechSpeedMsgId"))): true)//语速过快
                    .filter(map -> "11".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("robTalkMsgId"))): true)//抢话
                    .filter(map -> "12".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("violationWordMsgId"))): true)//违规词
                    .filter(map -> "13".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("sensitiveWordMsgId"))): true)//敏感词
                    .filter(map -> "14".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("seekHelpMsgId"))): true)//求助
                    .filter(map -> "15".equals(currentState)?StringUtils.isNotBlank(String.valueOf(map.get("debugAppealMsgId"))): true)//首发诉求
                    .collect(Collectors.toList());
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return userMap;
    }


    /**
     * 获取统计的数据和对应的用户点位图数据、技能组点位图数据和工作组点位图数据
     * @param userMap
     * @param ycuser
     * @return
     */
    public static JSONObject getStatData(List<Map<String, Object>> userMap,YCUserPrincipal ycuser,Map<String,JSONObject> skillInitData
            ,Map<String,JSONObject> workGroupInitData,List<Map<String, Object>> statUserMap){
        JSONObject result = new JSONObject();
        //1签入 2通话 3空闲 4话后 5示忙 7超长通话 8话后超时 9静音 10语速超快 11抢话 12违规词 13敏感词 14求助 15首发诉求
        JSONObject statData = new JSONObject();//统计数据
        try {

            if(userMap!=null){
                logger.info("[userMap]"+userMap.size()+"[statUserMap]"+statUserMap.size());
                StringBuilder skillIds = new StringBuilder();
                statData.put("loginCount",0);//签入数
                statData.put("talkingCount",0);//通话数
                statData.put("ideaCount",0);//空闲数
                statData.put("setBusyCount",0);//示忙数
                statData.put("agentRecordHandleCount",0);//话后坐席数
                statData.put("extraLongCallCount",0);//超长通话
                statData.put("afterLongCount",0);//话后超时
                statData.put("muteCount",0);//静音
                statData.put("speechSpeedCount",0);//语速过快
                statData.put("robTalkCount",0);//抢话
                statData.put("violationWordCount",0);//违规词
                statData.put("sensitiveWordCount",0);//敏感词
                statData.put("seekHelpCount",0);//求助
                statData.put("debugAppealCount",0);//首发诉求
                boolean isExist = false;
                int number = 0;
                for(Map<String, Object> map : userMap){
                    isExist = true;

                    if(skillInitData !=null){//如果初始化的技能组队列不为空则是按技能组统计点位图，则将所有坐席摘出来放到技能组队列中
                        String skillId = map.get("agentSkill")+"";
                        JSONObject skillJson = skillInitData.get(skillId);
                        if(skillJson!=null){
                            JSONArray users =  skillJson.getJSONArray("users");
                            if(users==null)
                                users = new JSONArray();
                            users.add(map);
                            skillJson.put("users", users);
                            skillJson.put("agentCount", users.size());
                            skillInitData.put(skillId, skillJson);
                        }else {
                            //logger.debug("skillId is null"+JSON.toJSONString(map));
                            isExist = false;
                        }

                    }
                    if (workGroupInitData !=null){//如果初始化的工作组队列不为空则是按工作组统计点位图，则将所有坐席摘出来放到工作组队列中
                        String workGroupId = map.get("agentGroup")+"";
                        JSONObject workGroupJson = workGroupInitData.get(workGroupId);
                        if(workGroupJson!=null){
                            JSONArray users =  workGroupJson.getJSONArray("users");
                            if(users==null)
                                users = new JSONArray();
                            users.add(map);
                            workGroupJson.put("users", users);
                            workGroupJson.put("agentCount", users.size());
                            workGroupInitData.put(workGroupId, workGroupJson);
                        }else {
                            //logger.debug("agentGroup is null"+JSON.toJSONString(map));
                            isExist = false;
                        }
                    }
//                    if(isExist){
//                        if(!skillIds.toString().contains(map.get("agentSkill")+",")){//汇总当前筛选到的技能组
//                            skillIds.append(map.get("agentSkill")).append(",");
//                        }
//
//                        if(StringUtils.isNotBlank(map.get("distributeAgentId")+"")
//                                && !ycuser.getUserId().equals(map.get("distributeAgentId"))) {//仅求助可以转派，如果转派了就无法查看求助信息了
//                            map.put("seekHelpMsgId", "");
//                        }
//                        //0：表示话务员未签入。1：表示话务员空闲。2：表示预占用状态。3：表示占用状态。4：表示应答状态。5：表示通话状态。6：表示工作状态。7：表示忙状态。8：表示请假休息。9：表示学习态。10：表示调整态。
//                        String state = String.valueOf(map.get("currentState"));
//                        if(!"null".equals(state) && StringUtils.isNotBlank(state) && !"0".equals(state)){
//                            statData.put("loginCount", statData.getIntValue("loginCount")+1);//签入数
//                        }
//                        if(StringUtils.isNotBlank(state) && ("2".equals(state) || "3".equals(state)|| "4".equals(state)|| "5".equals(state))){
//                            statData.put("talkingCount", statData.getIntValue("talkingCount")+1);//通话数
//                        } else if(StringUtils.isNotBlank(state) && ("1".equals(state))){
//                            statData.put("ideaCount", statData.getIntValue("ideaCount")+1);//空闲数
//                        } else if(StringUtils.isNotBlank(state) && ("10".equals(state)||"6".equals(state))){
//                            statData.put("agentRecordHandleCount", statData.getIntValue("agentRecordHandleCount")+1);//话后坐席数
//                        } else if(StringUtils.isNotBlank(state) && ("7".equals(state)||"8".equals(state)||"9".equals(state))){
//                            statData.put("setBusyCount", statData.getIntValue("setBusyCount")+1);//示忙数
//                        }
//                        if(!"null".equals(map.get("extraLongCallMsgId")+"") && StringUtils.isNotBlank(map.get("extraLongCallMsgId")+"")){
//                            statData.put("extraLongCallCount", statData.getIntValue("extraLongCallCount")+1);//超长通话
//                        }
//                        if(!"null".equals(map.get("afterLongMsgId")+"") && StringUtils.isNotBlank(map.get("afterLongMsgId")+"")){
//                            statData.put("afterLongCount", statData.getIntValue("afterLongCount")+1);//话后超时
//                        }
//                        if(!"null".equals(map.get("muteMsgId")+"") && StringUtils.isNotBlank(map.get("muteMsgId")+"")){
//                            statData.put("muteCount", statData.getIntValue("muteCount")+1);//静音
//                        }
//                        if(!"null".equals(map.get("speechSpeedMsgId")+"") && StringUtils.isNotBlank(map.get("speechSpeedMsgId")+"")){
//                            statData.put("speechSpeedCount", statData.getIntValue("speechSpeedCount")+1);//语速过快
//                        }
//                        if(!"null".equals(map.get("robTalkMsgId")+"") && StringUtils.isNotBlank(map.get("robTalkMsgId")+"")){
//                            statData.put("robTalkCount", statData.getIntValue("robTalkCount")+1);//抢话
//                        }
//                        if(!"null".equals(map.get("violationWordMsgId")+"") && StringUtils.isNotBlank(map.get("violationWordMsgId")+"")){
//                            statData.put("violationWordCount", statData.getIntValue("violationWordCount")+1);//违规词
//                        }
//                        if(!"null".equals(map.get("sensitiveWordMsgId")+"") && StringUtils.isNotBlank(map.get("sensitiveWordMsgId")+"")){
//                            statData.put("sensitiveWordCount", statData.getIntValue("sensitiveWordCount")+1);//敏感词
//                        }
//                        if(!"null".equals(map.get("seekHelpMsgId")+"") && StringUtils.isNotBlank(map.get("seekHelpMsgId")+"")){
//                            statData.put("seekHelpCount", statData.getIntValue("seekHelpCount")+1);//求助
//                        }
//                        if(!"null".equals(map.get("debugAppealMsgId")+"") && StringUtils.isNotBlank(map.get("debugAppealMsgId")+"")){
//                            statData.put("debugAppealCount", statData.getIntValue("debugAppealCount")+1);//首发诉求
//                        }
//                        if((map.get("isNull")+"").equals("1")){
//                            number = number+1;
//                            map.put("number", number);//座位号
//                        }
//                    }
                }
                for(Map<String, Object> map : statUserMap){
                    isExist = true;
                    if(skillInitData !=null){//如果初始化的技能组队列不为空则是按技能组统计点位图，则将所有坐席摘出来放到技能组队列中
                        String skillId = map.get("agentSkill")+"";
                        JSONObject skillJson = skillInitData.get(skillId);
                        if(skillJson!=null){
                        }else {
                            //logger.debug("skillId is null"+JSON.toJSONString(map));
                            isExist = false;
                        }
                    }
                    if (workGroupInitData !=null){//如果初始化的工作组队列不为空则是按工作组统计点位图，则将所有坐席摘出来放到工作组队列中
                        String workGroupId = map.get("agentGroup")+"";
                        JSONObject workGroupJson = workGroupInitData.get(workGroupId);
                        if(workGroupJson!=null){

                        }else {
                            //logger.debug("agentGroup is null"+JSON.toJSONString(map));
                            isExist = false;
                        }
                    }
                    if(isExist){
                        if(!skillIds.toString().contains(map.get("agentSkill")+",")){//汇总当前筛选到的技能组
                            skillIds.append(map.get("agentSkill")).append(",");
                        }

                        if(StringUtils.isNotBlank(map.get("distributeAgentId")+"")
                                && !ycuser.getUserId().equals(map.get("distributeAgentId"))) {//仅求助可以转派，如果转派了就无法查看求助信息了
                            map.put("seekHelpMsgId", "");
                        }
                        //0：表示话务员未签入。1：表示话务员空闲。2：表示预占用状态。3：表示占用状态。4：表示应答状态。5：表示通话状态。6：表示工作状态。7：表示忙状态。8：表示请假休息。9：表示学习态。10：表示调整态。
                        String state = String.valueOf(map.get("currentState"));
                        if(!"null".equals(state) && StringUtils.isNotBlank(state) && !"0".equals(state)){
                            statData.put("loginCount", statData.getIntValue("loginCount")+1);//签入数
                        }
                        if(StringUtils.isNotBlank(state) && ("2".equals(state) || "3".equals(state)|| "4".equals(state)|| "5".equals(state))){
                            statData.put("talkingCount", statData.getIntValue("talkingCount")+1);//通话数
                        } else if(StringUtils.isNotBlank(state) && ("1".equals(state))){
                            statData.put("ideaCount", statData.getIntValue("ideaCount")+1);//空闲数
                        } else if(StringUtils.isNotBlank(state) && ("10".equals(state)||"6".equals(state))){
                            statData.put("agentRecordHandleCount", statData.getIntValue("agentRecordHandleCount")+1);//话后坐席数
                        } else if(StringUtils.isNotBlank(state) && ("7".equals(state)||"8".equals(state)||"9".equals(state))){
                            statData.put("setBusyCount", statData.getIntValue("setBusyCount")+1);//示忙数
                        }
                        if(!"null".equals(map.get("extraLongCallMsgId")+"") && StringUtils.isNotBlank(map.get("extraLongCallMsgId")+"")){
                            statData.put("extraLongCallCount", statData.getIntValue("extraLongCallCount")+1);//超长通话
                        }
                        if(!"null".equals(map.get("afterLongMsgId")+"") && StringUtils.isNotBlank(map.get("afterLongMsgId")+"")){
                            statData.put("afterLongCount", statData.getIntValue("afterLongCount")+1);//话后超时
                        }
                        if(!"null".equals(map.get("muteMsgId")+"") && StringUtils.isNotBlank(map.get("muteMsgId")+"")){
                            statData.put("muteCount", statData.getIntValue("muteCount")+1);//静音
                        }
                        if(!"null".equals(map.get("speechSpeedMsgId")+"") && StringUtils.isNotBlank(map.get("speechSpeedMsgId")+"")){
                            statData.put("speechSpeedCount", statData.getIntValue("speechSpeedCount")+1);//语速过快
                        }
                        if(!"null".equals(map.get("robTalkMsgId")+"") && StringUtils.isNotBlank(map.get("robTalkMsgId")+"")){
                            statData.put("robTalkCount", statData.getIntValue("robTalkCount")+1);//抢话
                        }
                        if(!"null".equals(map.get("violationWordMsgId")+"") && StringUtils.isNotBlank(map.get("violationWordMsgId")+"")){
                            statData.put("violationWordCount", statData.getIntValue("violationWordCount")+1);//违规词
                        }
                        if(!"null".equals(map.get("sensitiveWordMsgId")+"") && StringUtils.isNotBlank(map.get("sensitiveWordMsgId")+"")){
                            statData.put("sensitiveWordCount", statData.getIntValue("sensitiveWordCount")+1);//敏感词
                        }
                        if(!"null".equals(map.get("seekHelpMsgId")+"") && StringUtils.isNotBlank(map.get("seekHelpMsgId")+"")){
                            statData.put("seekHelpCount", statData.getIntValue("seekHelpCount")+1);//求助
                        }
                        if(!"null".equals(map.get("debugAppealMsgId")+"") && StringUtils.isNotBlank(map.get("debugAppealMsgId")+"")){
                            statData.put("debugAppealCount", statData.getIntValue("debugAppealCount")+1);//首发诉求
                        }
                        if((map.get("isNull")+"").equals("1")){
                            number = number+1;
                            map.put("number", number);//座位号
                        }
                    }
                }
                if(StringUtils.isNotBlank(skillIds.toString())){
                    logger.info("[skillIds]"+skillIds.toString());
                    statData.put("callWaitNums",CommonUtils.countCallWaitNums(skillIds.toString()));//获取排队数
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        result.put("statData", statData);
        skillInitData = getRemoveZeroData(skillInitData);//清空组中没有人的数据
        result.put("skillInitData", skillInitData);
        workGroupInitData = getRemoveZeroData(workGroupInitData);//清空组中没有人的数据
        result.put("workGroupInitData", workGroupInitData);
        return result;
    }

    /**
     * 删除组内没有人数的集合
     * @param requestData
     * @return
     */
    public static Map<String,JSONObject> getRemoveZeroData(Map<String,JSONObject> requestData) {
        if(requestData==null){
            return null;
        }
        JSONObject json = null;
        // 创建一个临时列表存储需要移除的键
        List<String> keysToRemove = new ArrayList<>();
        for(String key : requestData.keySet()){
            json = requestData.get(key);
            if(json!=null && json.getIntValue("agentCount")==0){
                keysToRemove.add(key);
            }
        }
        // 遍历完成后再移除
        for(String key : keysToRemove){
            requestData.remove(key);
        }
        return requestData;
    }

    // 点击某个坐席查询坐席信息
    public static JSONObject queryAgentInfoById(JSONObject requestData) {
        JSONObject result = new JSONObject();

        String agentId = requestData.getString("agentId");
        JSONArray agentList = CtiUtils.siteagentinfobyagentids(agentId);
        if (agentList.size() > 0) {
            JSONObject agentObj = agentList.getJSONObject(0);

            List<Map<String, String>> tAgentInfoList = CommonUtils.queryAgentInfo();
            Map<String, String> agentInfo = tAgentInfoList.stream()
                    .filter(m -> agentId.equals(m.get("AGENTID")))
                    .findFirst()
                    .orElse(new HashMap<>());
            // 查询指定VDN下的签入座席所带电话号码
            String skills = agentInfo.get("AGENTSKILLS");
            String skillGroupName = CommonUtils.querySkillGroupNameById(skills);
            //话机号
            //String agentPhone = CommonUtils.getCitAgentPhone(agentId);
            String agentPhone = agentObj.getString("agentPhone");
            // 座席ID
            result.put("agentId", agentObj.getString("agentId"));
            result.put("agentName", agentInfo.get("NAME"));
            // 座席状态
            result.put("currentState", agentObj.getString("currentState"));
            result.put("currentStateTime", agentObj.getString("currentStateTime"));
            // 基本信息
           JSONObject userInitData = CommonUtils.getUserHwInfo(agentObj.getString("agentId"));
            //头像
            result.put("url",userInitData.getString("URL"));
            //性别
            result.put("sex","0".equals(userInitData.getString("SEX"))?"女":"男");
            //年龄
            result.put("brithDate",userInitData.getString("BRITH_DATE"));
            //入职年限
            result.put("entryDate",userInitData.getString("ENTRY_DATE"));
            //添受理业务类型 businessType
            getWorkGroupType(agentInfo.get("WORKGROUP"),result);
            //坐席话机号
            result.put("agentPhone",agentPhone);
            JSONObject phoneJson = CommonUtils.queryPhoneById(agentPhone);//话机相关信息
            if(phoneJson!=null){
                //区域编码 8-12对应//六里桥4楼B区//六里桥4楼A区//六里桥4楼C区//六里桥5楼C区//六里桥5楼A区
                result.put("roomLocation",phoneJson.getString("ROOM_LOCATION"));
                //座位号
                result.put("seatNo",phoneJson.getString("SEAT_NO"));
            }
            // 上班时间
            result.put("loginTime", DateUtil.getFormattedDateTime(agentObj.getLong("logonTime")));
            // 班组
            result.put("workGroupName", agentInfo.get("WORKGROUP"));
            // 技能列表 技能组名称
            result.put("skillGroupName", skillGroupName);
            result.put("alarms",CommonUtils.getUserAlarm(agentObj.getString("agentId")));//将告警数据写入

            //获取当前坐席的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
            JSONObject toDayData = CommonUtils.getUserCallData(agentObj.getString("agentId"),"1");
            //获取当前坐席的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长
            JSONObject toWeekData = CommonUtils.getUserCallData(agentObj.getString("agentId"),"2");
            //获取当前坐席的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长
            JSONObject toMonthData = CommonUtils.getUserCallData(agentObj.getString("agentId"),"3");
            result.put("toDayCall",toDayData);
            //获取五维图数据
            result.put("workNumberFive",CommonUtils.getWorkNumberJson(toDayData,toWeekData,toMonthData,"AGENT"));
            //将坐席月度评分查询出来
            JSONObject agentScoreInfo = CommonUtils.getAgentScoreInfo(agentId);
            result.put("agentScoreInfo",agentScoreInfo);
            result.put("honor1",agentScoreInfo.getString("HONOR1"));//个人荣誉1
            result.put("honor2",agentScoreInfo.getString("HONOR2"));//个人荣誉2
            result.put("honor3",agentScoreInfo.getString("HONOR3"));//个人荣誉3
            //获取上月五维图数据
            result.put("lastMonthScoreFive",CommonUtils.getLastMonthScoreFive(agentScoreInfo));
            // 坐席首发诉求列表
            agentObj.put("source","mars");
            //result.put("actionForAppealList",CommonUtils.appealList(agentObj));
            //分析建议
            result.put("analysisText","");

        }
        return result;
    }

    /**
     * 获取坐席首发诉求详情
     * @param data
     * @return
     */
    public static EasyResult actionForAppealInfo(JSONObject data) {
        return CommonUtils.appealInfo(data);
    }



    // 点击某个班组画像查询班组信息
    public static JSONObject queryWorkGroupInfoById(JSONObject requestData) {
        JSONObject result = new JSONObject();
        String workGroupId = requestData.getString("workGroupId");
        //获取当前班组的班组名称
        String workGroupName = CommonUtils.getWorkGroupName(workGroupId);
        result.put("workGroupId",workGroupId);
        result.put("workGroupName",workGroupName);
        //获取当前班组的班长
        String workGroupMonitors = CommonUtils.getWorkGroupMonitors(workGroupId);
        result.put("workGroupMonitors",workGroupMonitors);
        //获取当前班组内的坐席
        List<JSONObject> workGroupAgent = CommonUtils.getWorkGroupAgent(workGroupId);
        //班组内坐席数量
        result.put("workGroupCount",workGroupAgent.size());
        //获取当前班组内的坐席受理签入人数 loginCount、通话人数 callCount、空闲人数 ideaCount、话后人数 afterCallCount、示忙人数 busyCount
        JSONObject workGroupAgentStat = CommonUtils.getAgentStat(workGroupId);
        //班组内统计数据
        result.putAll(workGroupAgentStat);
        //获取当前班组内的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
        JSONObject toDayData = CommonUtils.getWorkGroupCallData(workGroupId,"1");
        //获取七天内当前班组的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长
        JSONObject toWeekData = CommonUtils.getWorkGroupCallData(workGroupId,"2");
        //获取三十天内当前班组的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长
        JSONObject toMonthData = CommonUtils.getWorkGroupCallData(workGroupId,"3");
        result.put("toDayCall",toDayData);
        //获取当前班组内的当月接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
        result.put("toMonthCall",CommonUtils.getWorkGroupCallData(workGroupId,"month"));
        //获取五维图数据
        result.put("workNumberFive",CommonUtils.getWorkNumberJson(toDayData,toWeekData,toMonthData,"WORK_GROUP"));
        //获取当前班组内产生的告警统计
        result.put("alarms",CommonUtils.getWorkGroupAlarm(workGroupId));

        //将班组月度评分查询出来
        JSONObject workGroupScoreInfo = CommonUtils.getWorkGroupScoreInfo(workGroupName);
        result.put("workGroupScoreInfo",workGroupScoreInfo);
        //获取上月五维图数据
        result.put("lastMonthScoreFive",CommonUtils.getLastMonthScoreFive(workGroupScoreInfo));
        result.put("honor1",workGroupScoreInfo.getString("HONOR1"));//班组荣誉1
        result.put("honor2",workGroupScoreInfo.getString("HONOR2"));//班组荣誉2
        result.put("honor3",workGroupScoreInfo.getString("HONOR3"));//班组荣誉3
        return result;
    }
    /**
     * 根据工作组名称对工作组分类
     * @param workGroupName
     * @return
     */
    private static String getWorkGroupType(String workGroupName,JSONObject agent){
        if(StringUtils.isNotBlank(workGroupName)){
            //技能组 对应类型：01接话  02网络 03回访 04业务
            if(workGroupName.contains("回访")) {
                agent.put("businessType", "回访");
                return "03";
            }else if(workGroupName.contains("网络")) {
                agent.put("businessType", "网络");
                return "02";
            }else if(workGroupName.contains("培训")||workGroupName.contains("诉求")||workGroupName.contains("信息")||workGroupName.contains("业务")
                    ||workGroupName.contains("值班长")||workGroupName.contains("质检")) {
                agent.put("businessType", "业务");
                return "04";
            }else {
                agent.put("businessType", "接话");
                return "01";//未识别默认通话
            }
        }else {
            agent.put("businessType", "接话");
            return "01";//未识别默认通话
        }

    }

    //获取告警模版
    public static Map<String, String> getWarnTemp(YCUserPrincipal ycuser, String msgType) {
        // msgType 0话后超时，1超长通话，2超快语速，3求助，4其它，5转派 6公告 7静默 8抢话 9违规词 10敏感词
        //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求
//        RedisClusterUtil redisClusterUtil = new RedisClusterUtil();
        msgType = CommonUtils.returnAlarmType(msgType);
        Map<String, JSONObject> dictList = CommonUtils.getWarnTemp(msgType,"");
        logger.info(dictList);
        Map<String, String> dict = new HashMap<>();
        JSONObject obj;
        for(String key:dictList.keySet()){
            obj = dictList.get(key);
            dict.put(key,obj.getString("TEMPLATE_NAME"));
        }
        logger.info("[dict]"+dict);
        return dict;
    }

    // 转移求助
    public static boolean transferWarn(YCUserPrincipal ycuser,JSONObject requestData) throws SQLException {
        String msgId = requestData.getString("msgId");
        String agentId = requestData.getString("agentId");
        String agentAcc = QueryFactory.getReadQuery().queryForString("select USER_ACCT from "+Constants.getBusiName()+".cc_busi_user where USER_ID=?", new Object[] {agentId});
        String agentNo = requestData.getString("agentNo");//求助所属人工号
        // 话房编码
        String hfCode = requestData.getString("hfCode");
        String str = cache.get("queryAgentPlace:" + hfCode);
        if (StringUtils.isNotBlank(str)) {
            List<JSONObject> list = JSON.parseObject(str, new TypeReference<List<JSONObject>>() {
            });
            JSONObject agentInfo = list.stream()
                    .filter(obj -> agentNo.equals(obj.get("agentId")))
                    .findFirst()
                    .orElse(null);
            if (agentInfo != null) {
                if (msgId.equals(agentInfo.getString("seekHelpMsgId"))) {
                    agentInfo.put("seekHelpMsgId", "");
                }
                int sum = 100;
                if("50".equals(hfCode) ||"27".equals(hfCode) || "23".equals(hfCode)){
                    sum = 5;
                }
                cache.put("queryAgentPlace:" + hfCode, JSON.toJSONString(list),sum);
                logger.info("处理告警信息======================开始===========================");
                logger.info(JSON.toJSONString(list));
                logger.info("处理告警信息======================结束==========================");
            }
        }
        String operatingDesc = ycuser.getLoginAcct()+"值班长于"+ EasyDate.getCurrentDateStr()+"将"+agentNo+"坐席求助转派给"+agentAcc+"班长！";
        AgentLogUtil.writeProcessLog(msgId,ycuser.getUserName(),ycuser.getLoginAcct().replaceAll("@yc",""),AgentLogUtil.MODEL_DISPATCH,operatingDesc);
        return CommonUtils.transferWarn(msgId, agentId,agentAcc);
    }


    public static Map<String, Object> queryAgentById(JSONObject requestData) {
        String agentId = requestData.getString("agentId");
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, String>> dictList = CommonUtils.getDict("AgentMonitor_HF");
            for (Map<String, String> hfMap : dictList) {
                String hfCode = hfMap.get("CODE");
                String str = cache.get("queryAgentPlace:" + hfCode);
                if (StringUtils.isNotBlank(str)) {
                    List<Map<String, Object>> list = JSON.parseObject(str, new TypeReference<List<Map<String, Object>>>() {
                    });
                    if (StringUtils.isNotBlank(agentId)) {
                        Map<String, Object> agentInfo = list.stream()
                                .filter(m -> agentId.equals(m.get("agentId")))
                                .findFirst()
                                .orElse(null);
                        if (agentInfo != null) {
                            agentInfo.put("code", hfCode);
                            result = agentInfo;
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("坐席人员查找异常", e);
        }
        return result;
    }

    //位置图监控-左侧话房数据
    public static List<Map<String, Object>> queryPhoneRoom1() {

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            String str = cache.get("AgentMonitor:phoneRoomWarn");
            if (StringUtils.isNotBlank(str)) {
                result = JSON.parseObject(str, new TypeReference<List<Map<String, Object>>>() {
                });
            }
        } catch (Exception e) {
            logger.error("坐席位置图-左侧话房查询异常", e);
        }
        return result;
    }

    //位置图监控-左侧话房数据
    public static JSONObject queryPhoneRoom(YCUserPrincipal ycuser) {
        String key = "AgentMonitor:phoneRoomWarn_"+ycuser.getUserId();
        JSONObject result = cache.get(key);
        if (result != null) {
            return result;
        }
        result = new JSONObject();
        List<JSONObject> dictList = CommonUtils.queryPhoneRoom();
        try {
            Map<String ,String> agentWarnMap = CommonUtils.getAgentWarn(ycuser);
            if(agentWarnMap==null){
                agentWarnMap = new HashMap<>();
            }
            JSONObject json = null;
            String str = cache.get("queryAgentPhoneSeat");//获取在线的坐席
            if (StringUtils.isNotBlank(str)) {
                json = JSONObject.parseObject(str);
            }
            if(json==null) {
                json = new JSONObject();
            }
            List<JSONObject> list4 = new ArrayList<>();
            List<JSONObject> list5 = new ArrayList<>();
            List<String> hfWarnList4 = new ArrayList<>();//4楼告警图
            List<String> hfWarnList5 = new ArrayList<>();//5楼告警图
            List<String> hfWarnListAll = new ArrayList<>();//全部告警图
            // 话房编码
            for (JSONObject obj : dictList) {
                String code = obj.getString("CODE");
                // 存放话房是否存在告警未处理的坐席
                List<String> hfWarnList = new ArrayList<>();
                // 话房下的坐席电话和ip（ip目前没用到）
                List<Map<String, String>> list = CommonUtils.queryHfIpList(code);
                for (Map<String, String> phoneMap : list) {
                    // 话机号码
                    String phone = phoneMap.get("PHONE_NUMBER");
                    // 座位号
                    String seatNo = phoneMap.get("SEAT_NO");
                    if (StringUtils.isNotBlank(agentWarnMap.get(phone))) {
                        hfWarnList.add(seatNo);
                    }
                }
                Collections.sort(hfWarnList);
                if(hfWarnList.size() > 0){
                    obj.put("hfWarnList",hfWarnList);
                }else {
                    obj.put("hfWarnList",new JSONArray());
                }
                if("8".equals(code) || "9".equals(code) || "10".equals(code)) {
                    list4.add(obj);
                    hfWarnList4.addAll(hfWarnList);
                }else{
                    list5.add(obj);
                    hfWarnList5.addAll(hfWarnList);
                }
                hfWarnListAll.addAll(hfWarnList);
            }

            List<JSONObject> list = new ArrayList<>();
            JSONObject json4 = new JSONObject();
            json4.put("CODE","27");
            json4.put("NAME","4楼");
            json4.put("hfWarnList",hfWarnList4);
            json4.put("children",list4);
            JSONObject json5 = new JSONObject();
            json5.put("CODE","23");
            json5.put("NAME","5楼");
            json5.put("hfWarnList",hfWarnList5);
            json5.put("children",list5);
            list.add(json4);
            list.add(json5);
            result.put("CODE","50");
            result.put("NAME","全部");
            result.put("hfWarnList",hfWarnListAll);
            result.put("children",list);
            cache.put(key, result,5);//缓存保存5s
        } catch (Exception e) {
            logger.error("坐席位置图-左侧话房查询异常", e);
        }
        return result;
    }

    //指派人工号
    public static List<JSONObject> queryDistributeUserAcc(String msgId) {

        List<JSONObject> dictList = null;
        try {
            dictList = CommonUtils.queryDistributeUserAcc(msgId);
            if(dictList==null){
                dictList = new ArrayList<>();
            }
//            JSONObject test = new JSONObject();
//            test.put("USER_ID","82798310591849988218246");
//            test.put("AGENT_NAME","刘东升");
//            dictList.add(test);
        } catch (Exception e) {
            logger.error("坐席位置图-指派人工号查询异常", e);
        }
        return dictList;
    }

    //指派人工号
    public static List<JSONObject> queryDistributeUserAccOld(YCUserPrincipal ycuser) {

        List<JSONObject> dictList = null;
        try {
            dictList = CommonUtils.queryDistributeUserAcc(ycuser);

        } catch (Exception e) {
            logger.error("坐席位置图-指派人工号查询异常", e);
        }
        return dictList;
    }

    // 获取指定页数的数据
    private static List<Map<String, Object>> getPage(List<Map<String, Object>> dataList, int pageNumber, int pageSize) {
        int startIndex = (pageNumber - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, dataList.size());
        if (startIndex >= dataList.size()) {
            return new ArrayList<>();
        }
        return dataList.subList(startIndex, endIndex);
    }

    /**
     * 获取当前坐席所有待办列表
     * @param ycuser
     * @return
     */
    public static JSONObject getToDuList(YCUserPrincipal ycuser,JSONObject param){
        return CommonUtils.getToDuList(ycuser,param);
    }


    /**
     * 处理告警
     * @param ycUser
     * @param requestData
     * @return
     */
    public static JSONObject handleAlarm(YCUserPrincipal ycUser, JSONObject requestData, UserModel user){
        String msgId = requestData.getString("msgId");//消息id
        String alarmType = requestData.getString("alarmType");//告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求

        if(StringUtils.isAllBlank(msgId,alarmType)){
            logger.error("[handleAlarm]告警id和告警类型不能全部为空:"+requestData);
            return EasyResult.fail("告警id和告警类型不能全部为空。");
        }
        //String agentId = requestData.getString("agentId");//坐席id
//        if(StringUtils.isBlank(msgId)){
//            logger.error("[handleAlarm]告警id不为空:"+requestData);
//            return EasyResult.fail("告警id不为空。");
//        }
        // 话房编码
        String hfCode = requestData.getString("hfCode");
        if(StringUtils.isBlank(hfCode)){
            logger.error("[handleAlarm]话房id不能为空:"+requestData);
            return EasyResult.fail("话房id不能为空。");
        }
        List<Map<String ,Object>> list =  getHfData(hfCode);
        List<JSONObject> sendAgentList = new ArrayList<JSONObject>();//需要通知的坐席
        if (list!=null) {
            String sendAgent = requestData.getString("sendAgent");//是否通知坐席1通知 0不通知
            String msgTemp = requestData.getString("msgTemp");//通知模版必填
            String msgType = "";
            if("1".equals(sendAgent) && StringUtils.isBlank(msgTemp)){
                return EasyResult.fail("通知坐席的情况下必须要选择模版。");
            }
            List<String> agentIds = CommonUtils.getAlarmAgentInfo(msgId,alarmType,ycUser);
            if(agentIds==null || agentIds.size()==0){
                return EasyResult.fail("当前消息已经被处理，或者坐席状态已经变更，请刷新后重试。");
            }
            List<Map<String ,Object>> newList = new ArrayList<>();
            newList = list.stream()
                    .filter(obj -> agentIds.contains(obj.get("agentId")))
                    .collect(Collectors.toList());
            String alarmName = "";
            //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求
            String key;
            if ("1".equals(alarmType)) {
                key = "extraLongCallMsgId";
                alarmName = "超长通话";
            } else if ("2".equals(alarmType)) {
                key = "afterLongMsgId";
                alarmName = "话后超时";
            } else if ("3".equals(alarmType)) {
                key = "muteMsgId";
                alarmName = "静音";
            } else if ("4".equals(alarmType)) {
                key = "speechSpeedMsgId";
                alarmName = "语速过快";
            } else if ("5".equals(alarmType)) {
                key = "robTalkMsgId";
                alarmName = "抢话";
            } else if ("6".equals(alarmType)) {
                key = "violationWordMsgId";
                alarmName = "坐席违规词";
            } else if ("7".equals(alarmType)) {
                key = "sensitiveWordMsgId";
                alarmName = "市民违规词";
            } else if ("8".equals(alarmType)) {
                key = "seekHelpMsgId";
                alarmName = "求助";
            } else if ("9".equals(alarmType)) {
                key = "debugAppealMsgId";
                alarmName = "首发诉求";
            } else {
                key = "";
            }
            String operatingDesc = ycUser.getLoginAcct()+"坐席于"+ EasyDate.getCurrentDateStr()+"处理"+String.join(",",agentIds)+"坐席的"+alarmName+"类型告警！";
            if (newList.size() > 1) {//大于1条则是批量处理
                operatingDesc = ycUser.getLoginAcct() + "坐席于" + EasyDate.getCurrentDateStr() + "批量处理" + String.join(",", agentIds) + "坐席的" + alarmName + "类型告警！";
            }
            list.forEach(map ->{
                if(agentIds.contains(map.get("agentId")+"")) {
                    sendAgentList.add(new JSONObject(map));
                    map.put(key, "");
                }
            });
//            for(Map<String ,Object> agent : newList) {
//                sendAgentList.add(new JSONObject(agent));//需要通知的坐席
//                //更改缓存，用来处理点位图展示内容
//                agent.put(key, "");
//            }
            int sum = 100;
            if("50".equals(hfCode) ||"27".equals(hfCode) || "23".equals(hfCode)){
                sum = 5;
            }
            cache.put("queryAgentPlace:" + hfCode, JSON.toJSONString(list),sum);
            if(!"9".equals(alarmType)){//不是首发诉求
                msgType = CommonUtils.returnAlarmType(alarmType);
                if("1".equals(sendAgent)){//通知坐席
                    CommonUtils.sendAgent(sendAgent, msgTemp, msgType, ycUser.getEntId(), sendAgentList,ycUser);
                }else{
                    //求助处理的时候需要通知坐席
                    CommonUtils.sendAgent(sendAgent, "", msgType, ycUser.getEntId(), sendAgentList,ycUser);
                }
                CommonUtils.updateAgentWarn(msgId, alarmType,msgTemp,user);
            }else{
                CommonUtils.appealAudit(requestData);
            }
            AgentLogUtil.writeProcessLog(msgId,ycUser.getUserName(),ycUser.getLoginAcct().replaceAll("@yc",""),
                    AgentLogUtil.MODEL_DISPATCH,operatingDesc);
        }
        return EasyResult.ok();
    }

    /**
     * 获取到坐席或班组的求助信息
     * @param param
     * @return
     */
    public static JSONObject queryHelpList(JSONObject param){
        JSONObject result = new JSONObject();
        //当前页码
        int pageIndex = param.getIntValue("pageIndex");
        //当前页数量
        int pageSize = param.getIntValue("pageSize");
        if(pageIndex==0 || pageSize==0){
            return EasyResult.fail("请传入页码和页条数");
        }
        //坐席工号
        String agentId = param.getString("agentId");
        //班组工号
        String workId = param.getString("workId");
        if(StringUtils.isBlank(agentId) && StringUtils.isBlank(workId)){
            return EasyResult.fail("请传入坐席工号或班组工号");
        }
        return CommonUtils.queryHelpList(agentId,workId,pageIndex,pageSize);
    }

}
