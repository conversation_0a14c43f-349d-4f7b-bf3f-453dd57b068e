package com.yunqu.cc.monitordata.inf;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.util.DateUtil;
import com.yunqu.cc.monitordata.util.DesUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.kit.RandomKit;

/**
 * Title: CallOutService
 * Description: 外呼服务
 * Copyright: Copyright (c) 2025
 * Company: 云趣科技
 * @Author: chenzhiwei
 * @Version 1.0
 */
public class CallOutService extends IService {
    private final Logger logger = CommonLogger.getLogger("callOut");
    protected EasyCache cache = CacheManager.getMemcache();
    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        logger.info("外呼服务接收到的请求参数：" + jsonObject);
        String command = jsonObject.getString("command");
        // 名单ID
        String objId = jsonObject.getString("objId");
        // 被叫号码
        String called = jsonObject.getString("called");
        // 通知内容
        String noticeContent = jsonObject.getString("noticeContent");
        // 姓名
        String name = jsonObject.getString("name");

        // 获取当前时间
        String currentDate = DateUtil.getCurrentDateStr();
        // 202511
        String monthId = currentDate.replace("-", "").substring(0, 6);
        // 获取当前月创建的任务ID
//        String taskId = RedissonUtil.get("ROBOT_TASK:" + monthId);
        String taskId = cache.get("ROBOT_TASK:" + monthId);
        if (StringUtils.isBlank(taskId)) {
            taskId = Constants.geTaskId();
        }

        if ("robotCallOut".equals(command)) {
            // 机器人外呼
            JSONObject params = new JSONObject();
            // 企业id
            params.put("entId", Constants.getEntId());
            // 业务id
            params.put("busiId", "002");
            // 流水号
            params.put("serialId", RandomKit.randomStr());
            // 时间戳，毫秒
            params.put("timestamp", System.currentTimeMillis());
            // 内置任务
            params.put("taskId", taskId);

            JSONArray jsonArray = new JSONArray();
            JSONObject cust = new JSONObject();
            cust.put("objId", objId);
            cust.put("手机号", called);
            cust.put("姓名", name);
            cust.put("通知内容", noticeContent);
            jsonArray.add(cust);
            params.put("custList", jsonArray);
            params.put("callbackUrl", Constants.getCallbackUrl());

            // 获取外呼地址
            String url = Constants.getRobotCallOutUrl() + "?entId=" + Constants.getEntId();
            // 获取密钥
            String secretKey = Constants.getSecretKey();
            logger.info("密钥：" + secretKey);
            String requestStr = DesUtil.EncryptDES(secretKey, params.toString());
            logger.info("机器人外呼地址:" + url + ", 加密前请求参数：" + params + ",加密后的请求参数：" +  requestStr);
            HttpResponse response = HttpUtil.createPost(url)
                    .contentType("application/json")
                    .body(requestStr)
                    .timeout(60000)
                    .execute();
            String resp = response.body();
            // 测试
            logger.info("机器人外呼结果：" + resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject respJson = JSONObject.parseObject(resp);
                if ("Succ".equals(respJson.getString("systemCode"))) {
                    // 外呼成功
                    JSONObject result = new JSONObject();
                    result.put("respCode", "000");
                    result.put("respDesc", "外呼成功");
                    return result;
                } else {
                    // 外呼失败
                    JSONObject result = new JSONObject();
                    result.put("respCode", "999");
                    result.put("respDesc", "外呼失败");
                    return result;
                }
            } else {
                logger.info("外呼结果异常");
                JSONObject result = new JSONObject();
                result.put("respCode", "999");
                result.put("respDesc", "外呼结果异常");
                return result;
            }
        }

        return null;
    }
}
