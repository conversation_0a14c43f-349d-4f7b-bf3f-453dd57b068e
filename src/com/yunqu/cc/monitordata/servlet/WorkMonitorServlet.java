package com.yunqu.cc.monitordata.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.AppBaseServlet;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.service.AgentMonitorService;
import com.yunqu.cc.monitordata.service.WorkMonitorService;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;

import javax.servlet.annotation.WebServlet;

/**
 * 工作监控模块接口
 */
@WebServlet("/servlet/workMonitor")
public class WorkMonitorServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    protected Logger logger = CommonLogger.getLogger("workMonitor");

    /**
     * 工作监控入口
     * @return
     */
    public JSONObject actionForInterface() {
        String reqStr = getRequestStr(this.getRequest());
        try {
            logger.info("工作监控请求入参 >> " + reqStr);
            if (StringUtils.isBlank(reqStr)) {
                return EasyResult.fail("参数不能为空");
            }
            JSONObject data = JSON.parseObject(reqStr);
            return proxy(data, this.getUserPrincipal());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return EasyResult.fail();
    }

    /**
     * 坐席监控相关方法
     * @param param
     * @param ycuser
     * @return
     * @throws Exception
     */
    protected JSONObject proxy(JSONObject param, YCUserPrincipal ycuser) {

        String serialId = param.getString("serialId");      //消息流水号
        String messageId = param.getString("messageId");    //请求消息类型
        String timestamp = param.getString("timestamp");    //时间戳
        String entId = param.getString("entId");            //企业Id

        JSONObject result = new JSONObject();
        result.put("result", Constants.RESULT_000);
        result.put("desc", Constants.SUCC);
        result.put("serialId", serialId);
        // 首页 -> 综合总话务量统计展示->展示当天接话量、回访总量、网络总量统计数据
        if ("allCallStat".equalsIgnoreCase(messageId)) {
            result.put("data", WorkMonitorService.allCallStat());
            return result;
        }
        // 上月总和排名
        if ("lastMonthRanking".equalsIgnoreCase(messageId)) {
            // agent work
            String type = param.getString("type");
            if (StringUtils.isBlank(type)) {
                result.put("desc", "类型不能为空");
                return result;
            }
            result.put("data", WorkMonitorService.lastMonthRanking(param));
            return result;
        }
        // 五维图
        if ("agentFiveWayStat".equalsIgnoreCase(messageId)) {
            // agent work
            String type = param.getString("type");
            if (StringUtils.isBlank(type)) {
                result.put("desc", "类型不能为空");
                return result;
            }
            result.put("data", WorkMonitorService.agentFiveWayStat(param));
            return result;
        }
        // 历史通话
        if ("historyCall".equalsIgnoreCase(messageId)) {
            String startTime = param.getString("startTime");
            String endTime = param.getString("endTime");

            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                result.put("desc", "开始时间或结束时间不能为空");
                return result;
            }
            JSONObject json = WorkMonitorService.historyCall(param);
            result.put("data", json.getJSONArray("data"));
            result.put("totalRow", json.getIntValue("totalRow"));
            result.put("pageSize", json.getIntValue("pageSize"));
            result.put("pageNumber", json.getIntValue("pageNumber"));
            result.put("totalPage", json.getIntValue("totalPage"));
            return result;
        }
        // 通话中的数据
        if ("callData".equalsIgnoreCase(messageId)) {
            String agentId = param.getString("agentId");
            if (StringUtils.isBlank(agentId)) {
                result.put("desc", "坐席id不能为空");
                return result;
            }
            result.put("data", WorkMonitorService.callData(param));
            return result;
        }
        // 工单详情
        if ("workOrderDetail".equalsIgnoreCase(messageId)) {
            JSONObject data = WorkMonitorService.workOrderDetail(param);
            result.put("data", data == null || data.isEmpty() ? new JSONObject() : data);
            return result;
        }
        // 获取坐席话务统计信息
        if ("agentCallStat".equalsIgnoreCase(messageId)) {
            result.put("data", WorkMonitorService.agentCallStat(param));
            return result;
        }
        // 坐席位置图监控-按工作组
        if ("queryWorkGroup".equalsIgnoreCase(messageId)) {
            result.put("data", WorkMonitorService.queryWorkGroup(ycuser));
            return result;
        }
        return null;
    }
}
