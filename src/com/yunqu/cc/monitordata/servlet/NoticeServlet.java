package com.yunqu.cc.monitordata.servlet;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.monitordata.base.AppBaseServlet;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.service.CallTaskObjService;
import com.yunqu.cc.monitordata.service.SmsTaskObjService;
import com.yunqu.cc.monitordata.util.AgentLogUtil;
import com.yunqu.cc.monitordata.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;

/**
 * Title: NoticeServlet
 * Description: 消息通知（助手消息、短信通知、外呼通知）
 * Copyright: Copyright (c) 2025
 * Company: 云趣科技
 * @Author: chenzhiwei
 * @Version 1.0
 */
@WebServlet("/servlet/notice")
public class NoticeServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    protected static final Logger logger = CommonLogger.logger;

    /**
     * 新建助手消息
     */
    public EasyResult actionForCreateAssistantMsg() {
        try {
            // 获取前端交互的参数
            JSONObject param = getJSONObject();
            // 消息模板id
            String templateId = param.getString("templateId");
            // 消息名称
            String templateName = param.getString("templateName");
            // 消息主题
            String templateSubject = param.getString("templateSubject");
            // 详细信息
            String detail = param.getString("detail");
            // 消息类型
            String msgType = param.getString("msgType");
            // 班组，传入班组ID
            String workGroupIds = param.getString("workGroupIds");
            // 发送方式 1-立即发送 2-指定时间
            String sendType = param.getString("sendType");
            // 指定时间
            String sendTime = param.getString("sendTime");

            // 必传参数校验
            if (StringUtils.isAnyBlank(templateName, templateSubject, detail, msgType, sendType)) {
                return EasyResult.fail("必填参数不能为空");
            }

            if (workGroupIds.isEmpty()) {
                return EasyResult.fail("班组不能为空");
            }

            if (!"0".equals(sendType)) {
                if (StringUtils.isBlank(sendTime)) {
                    return EasyResult.fail("请选择发送时间");
                }
            }
            UserModel user = UserUtil.getUser(getRequest());
            // 调用第三方接口，发送助手消息
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("msgName", templateName);
            paramsMap.put("msgSub", templateSubject);
            paramsMap.put("msgContent", detail);
            paramsMap.put("msgRemindType", msgType);
            paramsMap.put("sendType", sendType);
            paramsMap.put("robotId", "assis_1");
            paramsMap.put("corpKey", getEntId());
            paramsMap.put("userCode", user.getUserAcc());
            paramsMap.put("userName", user.getUserName());

            if ("2".equals(sendType)) {
                paramsMap.put("sendTime", sendTime);
            }
            paramsMap.put("msgWordId", workGroupIds);
            String request = prepareParams(paramsMap);
            String sendMsgUrl = Constants.getSendMsgUrl();
            logger.info("------>发送助手消息，请求地址：" + sendMsgUrl + ",请求参数：" + request);
            String resp = HttpUtil.post(sendMsgUrl, request, 60000);
            logger.info("------>发送助手消息，响应结果：" + resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject json = JSONObject.parseObject(resp);
                if (json.getIntValue("code") != 0) {
                    return EasyResult.fail("发送助手消息失败:" + json.getString("msg"));
                }
                AgentLogUtil.writeAddLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "新建助手消息");

                // 创建助手消息留痕，方便报表统计
                EasyQuery query = this.getQuery();
                // 将模版数据落库
                EasyRecord record = new EasyRecord(getTableName("CX_NOTICE_TASK"), "TASK_ID");
                record.put("TASK_ID", RandomKit.randomStr());
                record.put("ENT_ID", getEntId());
                record.put("BUSI_ORDER_ID", getBusiOrderId());
                record.put("NOTICE_TYPE", Constants.NOTICE_TYPE_ASSISTANT);
                record.put("TEMLPATE_ID", templateId);
                record.put("SEND_TYPE", sendType);
                record.put("CREATED_BY", getUserName());
                record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
                query.save(record);
                return EasyResult.ok();
            } else {
                return EasyResult.fail("发送助手消息失败");
            }
        } catch (Exception e) {
            logger.error("创建助手消息失败:" + e.getMessage(), e);
            return EasyResult.fail("发送助手消息失败");
        }
    }


    /**
     * 拼接请求参数
     * @param paramsMap 请求参数
     * @return 拼接后的参数
     */
    private String prepareParams(Map<String, String> paramsMap) {
        StringBuilder param = new StringBuilder();
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            param.append("&").append(entry.getKey()).append("=").append(entry.getValue());
        }
        param = new StringBuilder(param.substring(1));
        return param.toString();
    }

    /**
     * 存为新模版
     */
    public EasyResult actionForSaveNewTemplate() {
        UserModel user = UserUtil.getUser(getRequest());
        try {
            // 获取前端交互的参数
            JSONObject param = getJSONObject();
            // 模板名称
            String templateName = param.getString("templateName");
            // 模版主题
            String templateSubject = param.getString("templateSubject");
            // 详细信息
            String detail = param.getString("detail");
            // 消息类型
            String msgType = param.getString("msgType");

            // 必填参数校验
            if (StringUtils.isAnyBlank(templateSubject, templateName, detail, msgType)) {
                return EasyResult.fail("必填参数不能为空");
            }

            // 创建助手消息模版的时候，需要同步给第三方
            String saveModelUrl = Constants.getSaveModelUrl();
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("modelName", templateName);
            paramsMap.put("modelSub", templateSubject);
            paramsMap.put("modelContent", detail);
            paramsMap.put("modelRemindType", msgType);
            paramsMap.put("robotId", "assis_1");
            paramsMap.put("corpKey", getEntId());
            paramsMap.put("userCode", user.getUserAcc());
            paramsMap.put("userName", user.getUserName());

            String params = prepareParams(paramsMap);
            logger.info("------>新建助手消息模版，请求地址：" + saveModelUrl + ",请求参数：" + params);
            String resp = HttpUtil.post(saveModelUrl, params, 60000);
            logger.info("------>新建助手消息模版，响应结果：" + resp);
            if (StringUtils.isNotBlank(resp)) {
                JSONObject json = JSONObject.parseObject(resp);
                if (json.getIntValue("code") != 0) {
                    return EasyResult.fail("同步第三方失败:" + json.getString("msg"));
                }
            }

        } catch (Exception e) {
            logger.error("另存助手消息新模版失败:" + e.getMessage(), e);
            return EasyResult.fail("另存助手消息新模版失败:" + e.getMessage());
        }
        AgentLogUtil.writeModifyLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "另存助手消息模版");
        return EasyResult.ok();
    }

    /**
     * 新建短信消息
     */
    public EasyResult actionForCreateSmsMsg() {
        try {
            // 获取前端交互的参数
            JSONObject param = getJSONObject();
            // 短信模板id
            String templateId = param.getString("templateId");
            // 短信内容，前端应传入拼接好的短信内容
            String smsContent = param.getString("content");
            // 班组，传入班组ID
            String workGroupIds = param.getString("workGroupIds");
            // 发送方式 1-立即发送 2-指定时间
            String sendType = param.getString("sendType");
            // 指定时间
            String sendTime = param.getString("sendTime");

            if (StringUtils.isAnyBlank(templateId, smsContent, workGroupIds)) {
                return EasyResult.fail("必填参数不能为空");
            }

            if (!"1".equals(sendType)) {
                if (StringUtils.isBlank(sendTime)) {
                    return EasyResult.fail("请选择发送时间");
                }
            }

            String taskId = RandomKit.randomStr();
            EasyQuery query = this.getQuery();
            // 通过传来的班组id，获取坐席工号、坐席姓名、坐席班组, 坐席手机号, 班组名称
            List<JSONObject> list = getAgentInfo(workGroupIds);
            logger.info("list:" + list.size());
            EasyCalendar cal = EasyCalendar.newInstance();
            String monthId = cal.getFullMonth();
            int dateId = cal.getDateInt();
            if ("1".equals(sendType)) {
                for (JSONObject json : list) {
                    String mobile = json.getString("MOBILE");
                    // 班组id
                    String skillGroupId = json.getString("SKILL_GROUP_ID");
                    // 班组名称
                    String skillGroupName = json.getString("SKILL_GROUP_NAME");
                    // 坐席姓名
                    String agentName = json.getString("USERNAME");
                    // 坐席工号
                    String agentNo = json.getString("AGENT_PHONE");
                    // 系统参数 [坐席姓名][坐席工号][坐席班组]
                    smsContent = StrUtil.replace(smsContent, "[坐席姓名]", agentName);
                    smsContent = StrUtil.replace(smsContent, "[坐席工号]", agentNo);
                    smsContent = StrUtil.replace(smsContent, "[坐席班组]", skillGroupName);
                    logger.info("短信内容：" + smsContent + "，班组id：" + skillGroupId + "，班组名称：" + skillGroupName + "，坐席姓名：" + agentName);
                    // 调用立即发送逻辑
                    IService service = ServiceContext.getService("CX-SMS-12345-SERVICE");
                    JSONObject request = new JSONObject();
                    request.put("command", "sendSms");
                    request.put("content", smsContent);
                    // 坐席手机号
                    request.put("phone", mobile);
                    logger.info("------>立即发送短信SOA请求参数：" + request);
                    JSONObject result = service.invoke(request);
                    logger.info("实时发送短信返回结果：" + result);
                    if ("000".equals(result.getString("respCode"))) {
                        // 短信成功提交到短信平台，更新状态
                        EasyRecord record = new EasyRecord(getTableName("CX_SMS_TASK_OBJ"));
                        record.put("OBJ_ID", RandomKit.uuid());
                        record.put("ENT_ID", getEntId());
                        record.put("BUSI_ORDER_ID", getBusiOrderId());
                        record.put("MONTH_ID", monthId);
                        record.put("DATE_ID", dateId);
                        record.put("TASK_ID", taskId);
                        record.put("CUST_NAME", json.getString("USERNAME"));
                        record.put("PHONE", mobile);
                        record.put("CONTENT", smsContent);
                        // 班组ID
                        record.put("WORK_GROUP_ID", skillGroupId);
                        // 班组名称
                        record.put("WORK_GROUP_NAME", skillGroupName);
                        // 坐席工号
                        record.put("AGENT_PHONE", json.getString("AGENT_PHONE"));
                        record.put("NEXT_RUN_TIME", 0);
                        // 名单状态
                        record.put("TASK_STATE", "");
                        // 发送状态 1-发送中
                        record.put("SEND_STATE", "1");
                        // 回执状态 2-未知
                        record.put("RECEIPT_STATE", "2");
                        // 创建人
                        record.put("CREATED_BY", getUserName());
                        record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
                        query.save(record);
                    } else {
                        logger.info("短信提交到短信平台失败，手机号：" + mobile + ",坐席姓名：" + json.getString("USERNAME"));
                    }
                }
            } else {
                // 定时发送将带下发数据存入表中，然后通过定时任务扫描处理
                ForkJoinTask<Integer> forkJoinTask = null;
                // 开始时间
                long start = System.currentTimeMillis();
                if (list != null) {
                    forkJoinTask = new SmsTaskObjService(list, Constants.getBusiName(), taskId, getEntId(), getBusiOrderId(), smsContent, sendTime, getUserName());
                }

                ForkJoinPool forkJoinPool = new ForkJoinPool();
                // 提交任务
                ForkJoinTask<Integer> submit = forkJoinPool.submit(forkJoinTask);
                // 获得结果
                int sum = submit.get();
                long end = System.currentTimeMillis();
                logger.info("[保存短信名单信息] 查询数量 = " + list.size() + " 入库数量 = " + sum + " 时间:" + (end - start));
                // 判断 查询条数 跟入库条数是否一致，不一致执行回滚逻辑
                if (list.size() != sum) {
                    getQuery().execute("delete from " + getTableName("CX_SMS_TASK_OBJ") + " where TASK_ID = ?", taskId);
                    return EasyResult.fail("通知对象数量可能发送变更，请重新提交");
                }
            }

            // 将模版数据落库
            EasyRecord record = new EasyRecord(getTableName("CX_NOTICE_TASK"), "TASK_ID");
            record.put("TASK_ID", taskId);
            record.put("ENT_ID", getEntId());
            record.put("BUSI_ORDER_ID", getBusiOrderId());
            record.put("MONTH_ID", monthId);
            record.put("DATE_ID", dateId);
            record.put("NOTICE_TYPE", Constants.NOTICE_TYPE_SMS);
            // 0-未发送; 1-已发送; 2-发送中
            if ("1".equals(sendType)) {
                // 立即发送，将状态修改为发送中
                record.put("TASK_STATE", "2");
            } else {
                // 定时发送时，将状态修改为未发送
                record.put("TASK_STATE", "0");
            }
            record.put("TEMLPATE_ID", templateId);
            record.put("SEND_TYPE", sendType);
            record.put("CREATED_BY", getUserName());
            record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
            query.save(record);
        } catch (Exception e) {
            logger.error("新建短信消息失败:" + e.getMessage(), e);
            return EasyResult.fail("新建短信消息失败:" + e.getMessage());
        }
        UserModel user = UserUtil.getUser(getRequest());
        AgentLogUtil.writeAddLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "新建短信消息");
        return EasyResult.ok();
    }

    private List<JSONObject> getAgentInfo(String workGroupIds) throws SQLException {
        EasyQuery query = this.getQuery();
        EasySQL sql = new EasySQL("SELECT t3.USERNAME, t3.MOBILE, t3.AGENT_PHONE, t1.SKILL_GROUP_ID, t2.SKILL_GROUP_NAME  from " + getTableName("cc_skill_group_user t1") + ",");
        sql.append(getTableName("cc_skill_group t2") + ",cc_user t3 where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t1.USER_ID = t3.USER_ID");
        sql.append("and t2.SKILL_GROUP_TYPE= 'struct'");
        sql.appendIn(workGroupIds.split(","),"and t2.SKILL_GROUP_NAME ");
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        return list;
    }


    /**
     * 新建外呼消息
     */
    public EasyResult actionForCreateCallMsg() {
        try {
            // 获取前端交互的参数
            JSONObject param = getJSONObject();
            // 短信模板id
            String templateId = param.getString("templateId");
            // 短信内容，前端应传入拼接好的短信内容
            String content = param.getString("content");
            // 班组，传入班组ID
            String workGroupIds = param.getString("workGroupIds");
            // 发送方式 1-立即发送 2-指定时间
            String sendType = param.getString("sendType");
            // 指定时间
            String sendTime = param.getString("sendTime");

            if (StringUtils.isAnyBlank(templateId, content, workGroupIds)) {
                return EasyResult.fail("必填参数不能为空");
            }

            if (!"1".equals(sendType)) {
                if (StringUtils.isBlank(sendTime)) {
                    return EasyResult.fail("请选择发送时间");
                }
            }
            String taskId = RandomKit.randomStr();
            // 获取坐席信息
            List<JSONObject> agentInfoList = getAgentInfo(workGroupIds);
            if ("1".equals(sendType)) {
                EasyCalendar cal = EasyCalendar.newInstance();
                String monthId = cal.getFullMonth();
                int dateId = cal.getDateInt();

                for (JSONObject agentInfo : agentInfoList) {
                    // 被叫号码
                    String mobile = agentInfo.getString("MOBILE");
                    // 班组名称
                    String skillGroupName = agentInfo.getString("SKILL_GROUP_NAME");
                    // 坐席姓名
                    String agentName = agentInfo.getString("USERNAME");
                    // 坐席工号
                    String agentNo = agentInfo.getString("AGENT_PHONE");
                    // 系统参数 [坐席姓名][坐席工号][坐席班组]
                    content = StrUtil.replace(content, "[坐席姓名]", agentName);
                    content = StrUtil.replace(content, "[坐席工号]", agentNo);
                    content = StrUtil.replace(content, "[坐席班组]", skillGroupName);
                    // objId
                    String objId = RandomKit.uuid();
                    // 调用立即发送逻辑
                    IService service = ServiceContext.getService("CX-CALL-OUT-SERVICE");
                    JSONObject request = new JSONObject();
                    request.put("command", "robotCallOut");
                    request.put("noticeContent", content);
                    request.put("called", mobile);
                    request.put("objId", objId);
                    request.put("name", agentName);
                    JSONObject result = service.invoke(request);
                    if ("000".equals(result.getString("respCode"))) {
                        logger.info("立即发送外呼通知任务调用立即发送成功：" + result);
                        // 将外呼明细存入CX_CALL_TASK_OBJ
                        saveCallObj(objId, taskId, agentInfo, monthId, dateId, content);
                    } else {
                        logger.error("立即发送外呼通知任务调用立即发送失败：" + result);
                    }
                }

            } else {
                // 定时发送将带下发数据存入表中，然后通过定时任务扫描处理
                ForkJoinTask<Integer> forkJoinTask = null;
                // 开始时间
                long start = System.currentTimeMillis();
                if (agentInfoList != null) {
                    forkJoinTask = new CallTaskObjService(agentInfoList, Constants.getBusiName(), taskId, getEntId(), getBusiOrderId(), content, sendTime, getUserName());
                }

                ForkJoinPool forkJoinPool = new ForkJoinPool();
                // 提交任务
                ForkJoinTask<Integer> submit = forkJoinPool.submit(forkJoinTask);
                // 获得结果
                int sum = submit.get();
                long end = System.currentTimeMillis();
                logger.info("[保存短信名单信息] 查询数量 = " + agentInfoList.size() + " 入库数量 = " + sum + " 时间:" + (end - start));
                // 判断 查询条数 跟入库条数是否一致，不一致执行回滚逻辑
                if (agentInfoList.size() != sum) {
                    getQuery().execute("delete from " + getTableName("CX_CALL_TASK_OBJ") + " where TASK_ID = ?", taskId);
                    return EasyResult.fail("通知对象数量可能发送变更，请重新提交");
                }
            }

            EasyQuery query = this.getQuery();
            // 将模版数据落库
            EasyRecord record = new EasyRecord(getTableName("CX_NOTICE_TASK"), "TASK_ID");
            record.put("TASK_ID", taskId);
            record.put("ENT_ID", getEntId());
            record.put("BUSI_ORDER_ID", getBusiOrderId());
            record.put("NOTICE_TYPE", Constants.NOTICE_TYPE_CALL);
            // 0-未发送; 1-已发送; 2-发送中
            if ("1".equals(sendType)) {
                // 立即发送，将状态修改为发送中
                record.put("TASK_STATE", "2");
            } else {
                // 定时发送时，将状态修改为未发送
                record.put("TASK_STATE", "0");
            }
            record.put("TEMLPATE_ID", templateId);
            record.put("SEND_TYPE", sendType);
            record.put("CREATED_BY", getUserName());
            record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
            query.save(record);

        } catch (Exception e) {
            logger.error("创建外呼消息异常：" + e.getMessage(), e);
            return EasyResult.fail("创建外呼消息异常：" + e.getMessage());
        }
        UserModel user = UserUtil.getUser(getRequest());
        AgentLogUtil.writeAddLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "新建外呼消息");
        return EasyResult.ok();
    }

    /**
     * 保存外呼明细
     */
    private void saveCallObj(String objId, String taskId, JSONObject agentInfo, String monthId, int dateId, String content) throws SQLException {
        EasyQuery query = this.getQuery();
        EasyRecord record = new EasyRecord(getTableName("CX_CALL_TASK_OBJ"));
        record.put("OBJ_ID", objId);
        record.put("ENT_ID", getEntId());
        record.put("BUSI_ORDER_ID", getBusiOrderId());
        record.put("MONTH_ID", monthId);
        record.put("DATE_ID", dateId);
        record.put("TASK_ID", taskId);
        record.put("CUST_NAME", agentInfo.getString("USERNAME"));
        record.put("PHONE", agentInfo.getString("MOBILE"));
        record.put("CONTENT", content);
        // 班组ID
        record.put("WORK_GROUP_ID", agentInfo.getString("SKILL_GROUP_ID"));
        // 班组名称
        record.put("WORK_GROUP_NAME", agentInfo.getString("SKILL_GROUP_NAME"));
        // 坐席工号
        record.put("AGENT_PHONE", agentInfo.getString("AGENT_PHONE"));
        record.put("NEXT_RUN_TIME", 0);
        // 创建人
        record.put("CREATED_BY", getUserName());
        record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
        // 名单状态 0-未提交 1-已提交
        record.put("TASK_STATE", "1");
        query.save(record);
    }

}
