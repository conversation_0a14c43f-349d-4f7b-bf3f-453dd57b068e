package com.yunqu.cc.monitordata.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.monitordata.base.AppBaseServlet;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.service.AgentMonitorService;
import com.yunqu.cc.monitordata.util.CommonUtils;
import com.yunqu.cc.monitordata.util.ParamUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.io.DataInputStream;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;


/**
 *点位图监控接口
 */
@WebServlet("/servlet/pointMapMonitor")
public class PointMapMonitorServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private final Logger logger = CommonLogger.getLogger("pointMapMonitor");

	/**
	 * 接口入口
	 */
	//@PreAuthorize(resId = {"cx_12345_monitorSeat","cx-agentSeat_warn","CX_12345_AGENT_MONITOR"},  msg="没有权限")
	public JSONObject actionForInterface() {
		JSONObject data = json(this.getRequest());
		try {
			logger.info(data);
			return proxy(data,this.getUserPrincipal());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail("操作失败！");
	}
	
	
	public JSONObject json(HttpServletRequest request) {
		JSONObject jsonObject = null;
		int contLength = request.getContentLength();
		if(contLength>0){
			
			byte[] content = new byte[request.getContentLength()];
			
			try {
				DataInputStream dis = new DataInputStream(request.getInputStream());
				dis.readFully(content);
				logger.info("<< Request["+request.getRemoteAddr()+","+request.getRemotePort()+"],body->"+new String(content));
				//String str=URLDecoder.decode(new String(content), "UTF-8");
				String str = new String(content);
				try {
					jsonObject = JSON.parseObject(str);
				} catch (Exception e) {
					jsonObject = ParamUtil.parseParamToJson(str);
				}
				if(jsonObject == null){
					return jsonObject;
				}
			} catch (Exception ex) {
				logger.error(ex,ex);
				return jsonObject;
			}
		}else {
			//contLength小于等于0时看是否request是否有值
			jsonObject = new JSONObject();
			Enumeration enu=request.getParameterNames();  
			while(enu.hasMoreElements()){  
				String paraName=(String)enu.nextElement();  
				jsonObject.put(paraName, request.getParameter(paraName));
			} 
			if(StringUtils.isBlank(jsonObject.getString("command"))) {
				return jsonObject;
			}
		}
		return jsonObject;
	}
	
	protected JSONObject proxy(JSONObject param,YCUserPrincipal ycuser) throws Exception{
		logger.info("坐席监控前端请求入参 >>" + param.toJSONString());
		String serialId = param.getString("serialId");	//消息流水号
		String messageId = param.getString("messageId");	//请求消息类型
		String timestamp = param.getString("timestamp");	//时间戳
		String entId = param.getString("entId");			//企业Id

		JSONObject result = new JSONObject();
		result.put("result", Constants.RESULT_000);
		result.put("desc", Constants.SUCC);
		result.put("serialId", serialId);
		// 查询右侧楼层树，并且标记是否有需要处理的告警
		if("queryPhoneRoom".equalsIgnoreCase(messageId)){
			result.put("data", AgentMonitorService.queryPhoneRoom(ycuser));
			return result;
		}
		// 坐席位置图监控
		if("queryAgentPlace".equalsIgnoreCase(messageId)){
			String hfCode = param.getString("hfCode");
			if(StringUtils.isBlank(hfCode)){
				result.put("desc", "请选择话房！");
				return result;
			}
			result.put("data", AgentMonitorService.queryAgentPlace(param,ycuser));
			return result;
		}
		// 坐席位置图监控-按技能组
		if("queryAgentPlaceSkill".equalsIgnoreCase(messageId)){
			String hfCode = param.getString("hfCode");
			if(StringUtils.isBlank(hfCode)){
				result.put("desc", "请选择话房！");
				return result;
			}
			result.put("data", AgentMonitorService.queryAgentPlaceSkill(param,ycuser));
			return result;
		}
		// 坐席位置图监控-按工作组
		if("queryAgentPlaceWorkGroup".equalsIgnoreCase(messageId)){
			String hfCode = param.getString("hfCode");
			if(StringUtils.isBlank(hfCode)){
				result.put("desc", "请选择话房！");
				return result;
			}
			result.put("data", AgentMonitorService.queryAgentPlaceWorkGroup(param,ycuser));
			return result;
		}
		// 点击某个坐席查询坐席信息
		if("queryAgentInfoById".equalsIgnoreCase(messageId)){
			String agentId = param.getString("agentId");
			if(StringUtils.isBlank(agentId)){
				result.put("desc", "坐席id不能为空");
				return result;
			}
			result.put("data", AgentMonitorService.queryAgentInfoById(param));
			return result;
		}
		// 点击某个坐席查询坐席信息
//		if("queryAgentAppealInfoById".equalsIgnoreCase(messageId)){
//			String appealId = param.getString("appealId");
//			if(StringUtils.isBlank(appealId)){
//				result.put("desc", "首发诉求id不能为空");
//				return result;
//			}
//			return  AgentMonitorService.actionForAppealInfo(param);
//		}
		// 点击某个坐席查询班组信息
		if("queryWorkGroupInfoById".equalsIgnoreCase(messageId)){
			String workGroupId = param.getString("workGroupId");
			if(StringUtils.isBlank(workGroupId)){
				result.put("desc", "技能组id不能为空");
				return result;
			}
			result.put("data", AgentMonitorService.queryWorkGroupInfoById(param));
			return result;
		}
		// 点击某个坐席或者班组查看当天求助信息
		if("queryHelpList".equalsIgnoreCase(messageId)){
			result.put("data", AgentMonitorService.queryHelpList(param));
			return result;
		}
		// 获取当前坐席所有待办(告警、首发诉求、求助)列表
		if("getToDuList".equalsIgnoreCase(messageId)){
			result = AgentMonitorService.getToDuList(ycuser,param);
			return result;
		}
		// 获取告警/求助模版(目前类型还没改过来)
		if("getWarnTemp".equalsIgnoreCase(messageId)){
			String msgType = param.getString("msgType");
			if(StringUtils.isBlank(msgType)){
				result.put("desc", "消息类型不能为空");
				return result;
			}
			result.put("data", AgentMonitorService.getWarnTemp(ycuser,msgType));
			//logger.info(result);
			return result;
		}

		// 处理告警(包括批量处理、告警处理、首发诉求处理和求助处理)
		if("handleAlarm".equalsIgnoreCase(messageId)){
			UserModel user = UserUtil.getUser(this.getRequest());
			JSONObject res = AgentMonitorService.handleAlarm(ycuser,param,user);
			result.put("result", res.getString("state"));
			result.put("desc", res.getString("msg"));
			return result;
		}

		// 将求助信息转移给某个班长
		if("transferWarn".equalsIgnoreCase(messageId)){
			String agentId = param.getString("agentId");
			if(StringUtils.isBlank(agentId)){
				result.put("desc", "转移坐席id不能为空");
				return result;
			}
			String msgId = param.getString("msgId");
			if(StringUtils.isBlank(msgId)){
				result.put("desc", "消息id不能为空");
				return result;
			}
			String agentNo = param.getString("agentNo");
			if(StringUtils.isBlank(msgId)){
				result.put("desc", "求助人工号不能为空");
				return result;
			}
			if(AgentMonitorService.transferWarn(ycuser,param)){
				result.put("desc", "处理成功");
			}else {
				result.put("result", Constants.RESULT_999);
				result.put("desc", "处理失败");
			}
			return result;
		}

		// 从人员活动列表中点击工号跳转到 坐席位置图监控
		if("queryAgentById".equalsIgnoreCase(messageId)){
			String agentId = param.getString("agentId");
			if(StringUtils.isBlank(agentId) ){
				result.put("desc", "坐席id不能都为空");
				return result;
			}
			result.put("data", AgentMonitorService.queryAgentById(param));
			return result;
		}

		// 从坐席表中查询字典
		if("queryUserAcc".equalsIgnoreCase(messageId)){
			String msgId = param.getString("msgId");//消息id
			if(StringUtils.isBlank(msgId)){
				result.put("desc", "消息id不能为空");
				return result;
			}
			result.put("data", AgentMonitorService.queryDistributeUserAcc(msgId));
			return result;
		}
		


		// 获取所有技能组列表
		if("getSkillGroupList".equalsIgnoreCase(messageId)){
			String skillGroupInfo = CommonUtils.querySkillInfoList();
			if(StringUtils.isNotBlank(skillGroupInfo)){
				List<Map<String, String>> list = JSON.parseObject(skillGroupInfo, new TypeReference<List<Map<String, String>>>() {});
				result.put("data", list);
			}
			return result;
		}
		// 获取所有工作组列表
		if("getWorkGroupList".equalsIgnoreCase(messageId)){
			String workGroupInfo = CommonUtils.queryWorkGroupInfoList();
			if(StringUtils.isNotBlank(workGroupInfo)){
				List<Map<String, String>> list = JSON.parseObject(workGroupInfo, new TypeReference<List<Map<String, String>>>() {});
				result.put("data", list);
			}
			return result;
		}

		// 获取角色类型
		if("getUsetType".equalsIgnoreCase(messageId)){
			if(ycuser!=null)
				result.put("data", ycuser.getRoleType());
			else
				result.put("data", "-1");//如果没登录
			return result;
		}
		return result;
	}

	/**
	 * 通过话机号码读取通话记录中的转写记录
	 * @return
	 */
	public JSONObject actionForReadRecord() {
		JSONObject data = json(this.getRequest());
		try {
			String agentPhone = data.getString("agentPhone");
			if(StringUtils.isBlank(agentPhone)){
				return EasyResult.fail("未获取到当前坐席话机号，请刷新后重试！");
			}
			HttpResp resp = HttpUtil.sendPost(Constants.getWarnAddr()+"/aiamgr/agentmgr/getAgentCallInfo.do",data.toJSONString(),HttpUtil.TYPE_JSON);
			logger.info("actionForReadRecord << Request["+data+"],body->"+JSON.toJSONString(resp));
			if(resp.getCode()==200){
				return EasyResult.ok(resp.getResult());
			}else {
				return EasyResult.fail("获取转写记录失败！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(),e);
		}
		return EasyResult.fail("操作失败！");
	}

}
