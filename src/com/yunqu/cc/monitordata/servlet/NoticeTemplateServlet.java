package com.yunqu.cc.monitordata.servlet;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.monitordata.base.AppBaseServlet;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.util.AgentLogUtil;
import com.yunqu.cc.monitordata.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.util.HashMap;
import java.util.Map;

/**
 * Title: NoticeTemplateServlet
 * Description: 通知模板管理
 * Copyright: Copyright (c) 2025
 * Company: 云趣科技
 * @Author: chenzhiwei
 * @Version 1.0
 */
@WebServlet("/servlet/noticeTemplate")
public class NoticeTemplateServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    protected static final Logger logger = CommonLogger.logger;


    /**
     * 新建模版/修改模版
     */
    public EasyResult actionForSave() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            // 获取前端传来的参数
            JSONObject param = getJSONObject();
            // 模版类型 0-助手消息模板 1-短信通知模板 2-外呼通知模板
            String templateType = param.getString("templateType");
            // 模板名称
            String templateName = param.getString("templateName");
            // 模版主题
            String templateSubject = param.getString("templateSubject");
            // 短信模版id
            String smsTemplateId = param.getString("smsTemplateId");
            // 详细信息
            String detail = param.getString("detail");
            // 消息类型
            String msgType = param.getString("msgType");

            // 获取模版id，如果前端传来模版id说明是修改
            String templateId = param.getString("id");

            // 必填参数校验
            if (StringUtils.isAnyBlank(templateType, templateName, detail)) {
                return EasyResult.fail("必填参数不能为空");
            }

            String templateTypeName = "";
            if (Constants.NOTICE_TYPE_ASSISTANT.equals(templateType)) {
                templateTypeName = "助手消息模板";
            } else if (Constants.NOTICE_TYPE_SMS.equals(templateType)) {
                templateTypeName = "短信通知模版";
            } else if (Constants.NOTICE_TYPE_CALL.equals(templateType)) {
                templateTypeName = "外呼通知模版";
            }

            // 当助手消息模板时，模板主题和消息类型必填
            if (Constants.NOTICE_TYPE_ASSISTANT.equals(templateType)) {
                if (StringUtils.isAnyBlank(templateSubject, msgType)) {
                    return EasyResult.fail("模板主题不能为空");
                }

                // 创建助手消息模版的时候，需要同步给第三方
                String saveModelUrl = Constants.getSaveModelUrl();
                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put("modelName", templateName);
                paramsMap.put("modelSub", templateSubject);
                paramsMap.put("modelContent", detail);
                paramsMap.put("modelRemindType", msgType);
                paramsMap.put("robotId", "assis_1");
                paramsMap.put("corpKey", getEntId());
                paramsMap.put("userCode", user.getUserAcc());
                paramsMap.put("userName", user.getUserName());
                String params = prepareParams(paramsMap);
                logger.info("------>新建助手消息模版，请求地址：" + saveModelUrl + ",请求参数：" + params);
                String resp = HttpUtil.post(saveModelUrl, params, 60000);
                logger.info("------>新建助手消息模版，响应结果：" + resp);
                if (StringUtils.isNotBlank(resp)) {
                    JSONObject json = JSONObject.parseObject(resp);
                    if (json.getIntValue("code") != 0) {
                        return EasyResult.fail("调第三方失败:" + json.getString("msg"));
                    }

                    if (StringUtils.isBlank(templateId)) {
                        AgentLogUtil.writeAddLog(
                                user.getUserName(),
                                user.getUserAcc(),
                                AgentLogUtil.MODEL_DISPATCH,
                                "新增" + templateTypeName);
                    } else {
                        AgentLogUtil.writeModifyLog(
                                user.getUserName(),
                                user.getUserAcc(),
                                AgentLogUtil.MODEL_DISPATCH,
                                "修改" + templateTypeName);
                    }
                    return EasyResult.ok();
                }
            }
            // 当短信通知模板时，短信模版id必填
            if (Constants.NOTICE_TYPE_SMS.equals(templateType)) {
                if (StringUtils.isBlank(smsTemplateId)) {
                    return EasyResult.fail("短信模版id不能为空");
                }
            }

            // 将模版数据落库
            EasyRecord record = new EasyRecord(getTableName("CX_NOTICE_TEMPLATE"), "ID");
            record.put("TEMPLATE_TYPE", templateType);
            record.put("TEMPLATE_NAME", templateName);
            if (Constants.NOTICE_TYPE_SMS.equals(templateType)) {
                record.put("SMS_TEMPLATE_ID", smsTemplateId);
            }
            record.put("DETAIL", detail);
            // 修改人
            record.put("UPDATED_BY", getUserName());
            // 修改时间
            record.put("UPDATED_TIME", DateUtil.getCurrentDateStr());

            if (StringUtils.isBlank(templateId)) {
                record.put("ID", RandomKit.randomStr());
                // 创建人
                record.put("CREATED_BY", getUserName());
                // 创建时间
                record.put("CREATED_TIME", DateUtil.getCurrentDateStr());
                this.getQuery().save(record);
                // 将操作写入日志
                AgentLogUtil.writeAddLog(
                        user.getUserName(),
                        user.getUserAcc(),
                        AgentLogUtil.MODEL_DISPATCH,
                        "新增" + templateTypeName);
            } else {
                record.put("ID", templateId);
                this.getQuery().update(record);
                // 将操作写入日志
                AgentLogUtil.writeModifyLog(
                        user.getUserName(),
                        user.getUserAcc(),
                        AgentLogUtil.MODEL_DISPATCH,
                        "修改" + templateTypeName);
            }

        } catch (Exception e) {
            return EasyResult.fail("操作失败:" + e.getMessage());
        }
        return EasyResult.ok();
    }

    /**
     * 删除模版
     */
    public EasyResult actionForDelete() {
        try {
            JSONObject param = this.getJSONObject();
            // 模版id
            String templateId = param.getString("templateId");
            // 模版类型 0-助手消息模板 1-短信通知模板 2-外呼通知模板
            String templateType = param.getString("templateType");

            if (StringUtils.isBlank(templateId)) {
                return EasyResult.fail("模版ID不能为空");
            }

            // 获取登录用户信息
            UserModel user = UserUtil.getUser(getRequest());
            if (Constants.NOTICE_TYPE_ASSISTANT.equals(templateType)) {
                // 调用助手模版删除接口
                String deleteModelUrl = Constants.getDeleteModelUrl();
                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put("modelIds", templateId);
                paramsMap.put("robotId", "assis_1");
                paramsMap.put("corpKey", getEntId());
                paramsMap.put("userCode", user.getUserAcc());
                paramsMap.put("userName", user.getUserName());
                String params = prepareParams(paramsMap);
                logger.info("------>删除助手消息模版，请求地址：" + deleteModelUrl + ",请求参数：" + params);
                String resp = HttpUtil.post(deleteModelUrl, params, 60000);
                logger.info("------>删除助手消息模版，响应结果：" + resp);
                if (StringUtils.isNotBlank(resp)) {
                    JSONObject json = JSONObject.parseObject(resp);
                    if (json.getIntValue("code") != 0) {
                        return EasyResult.fail("调第三方失败:" + json.getString("msg"));
                    }
                    AgentLogUtil.writeDeleteLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "删除助手消息模版");
                    return EasyResult.ok();
                }
            } else {
                // 查询数据库里是否存在改模版
                String tSql = "SELECT count(1) FROM " + this.getTableName("CX_NOTICE_TEMPLATE") +
                        " WHERE ID = ?";
                boolean isExist = this.getQuery().queryForExist(tSql, templateId);
                if (!isExist) {
                    return EasyResult.fail("消息模板id不存在!");
                }

                // 删除数据
                String deleteSql = "DELETE FROM " + this.getTableName("CX_NOTICE_TEMPLATE") +
                        " WHERE ID = ? ";
                this.getQuery().execute(deleteSql, templateId);
                // 查询模板名称
                String templateName = this.getQuery()
                        .queryForString("SELECT TEMPLATE_NAME FROM " + getTableName("CX_NOTICE_TEMPLATE") + " WHERE ID = ?", templateId);
                AgentLogUtil.writeDeleteLog(user.getUserName(), user.getUserAcc(), AgentLogUtil.MODEL_DISPATCH, "删除模版,模板名称为" + templateName);
            }
        } catch (Exception e) {
            return EasyResult.fail("操作失败！" + e.getMessage());
        }
        return EasyResult.ok();
    }


    /**
     * 拼接请求参数
     * @param paramsMap 请求参数
     * @return 拼接后的参数
     */
    private String prepareParams(Map<String, String> paramsMap) {
        StringBuilder param = new StringBuilder();
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            param.append("&").append(entry.getKey()).append("=").append(entry.getValue());
        }
        param = new StringBuilder(param.substring(1));
        return param.toString();
    }
}
