package com.yunqu.cc.monitordata.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.AppDaoContext;
import com.yunqu.cc.monitordata.base.Constants;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

/**
 * 坐席监控模块
 *
 * <AUTHOR>
 * @date 2023/8/31 15:48
 */
@WebObject(name = "agentMonitor")
public class AgentMonitorDao extends AppDaoContext {

    /**
     * 查询所有技能组队列
     */
    @WebControl(name = "skillList", type = Types.DICT)
    public JSONObject skillList() {
        EasySQL sql = new EasySQL();
        // 技能队列名称
        String skillGroupName = param.getString("skillGroupName");
        sql.append("select SKILLGROUPID,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(SKILLGROUPNAME,'普通语音电话',''),'Chinese',''),'普通用户','') ,'医院挂号',''),'医院挂号网呼',''),',','') SKILLGROUPNAME from ");
        sql.append(getTableName("tskillgroup") + " where 1 = 1");
        sql.append(Constants.getHwCcId()," and subccno = ?");
        sql.append(Constants.getHwVdnId()," and vdn = ?");
        sql.append(" and SKILLGROUPID != '18'");
        sql.appendLike(skillGroupName," and SKILLGROUPNAME like ?");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }

    /**
     * 查询 用户选择的 技能组队列
     */
    @WebControl(name = "userSelectSkill", type = Types.DICT)
    public JSONObject userSelectSkill() {
        String userId = getUserId();
        EasySQL sql = new EasySQL();
        sql.append("select SKILLGROUPID,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(SKILLGROUPNAME,'普通语音电话',''),'Chinese',''),'普通用户','') ,'医院挂号',''),'医院挂号网呼',''),',','') SKILLGROUPNAME from ");
        sql.append(getTableName("tskillgroup") + " where 1 = 1");
        sql.append(Constants.getHwCcId()," and subccno = ?");
        sql.append(Constants.getHwVdnId()," and vdn = ?");
        sql.append(userId, " and SKILLGROUPID in(select SKILL_ID from " + getTableName("cx_user_skill") + " where USER_ID = ?)");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }
    /**
     * 查询所有班组
     */
    @WebControl(name = "queryWorkgroup", type = Types.DICT)
    public JSONObject queryWorkgroup() {
        EasySQL sql = new EasySQL();
        sql.append("select WORKGROUPID,WORKGROUP from ");
        sql.append(getTableName("tworkgroup") + " where 1 = 1");
        sql.append(Constants.getHwCcId()," and subccno = ?");
        sql.append(Constants.getHwVdnId()," and vdn = ?");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }

    /**
     * 查询所有话房
     */
    @WebControl(name = "queryPhoneRoom", type = Types.DICT)
    public JSONObject queryPhoneRoom() {
        EasySQL sql= new EasySQL(" SELECT t1.CODE,t1.NAME  FROM " + getTableName("C_CF_DICT") + " t1 left join " + getTableName("C_CF_DICTGROUP") + " t2 on t1.DICT_GROUP_ID=t2.ID WHERE t2.CODE = 'AgentMonitor_HF'");
        return this.getDictByQuery(sql.getSQL(), sql.getParams());
    }
}
