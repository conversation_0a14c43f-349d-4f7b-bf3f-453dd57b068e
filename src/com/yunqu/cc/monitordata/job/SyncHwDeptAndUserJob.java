package com.yunqu.cc.monitordata.job;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.sql.SQLException;
import java.util.List;

/**
 * 2025-06-10 17:42
 * 同步华为的部门和用户信息到大屏企业下
 */
public class SyncHwDeptAndUserJob implements Job {

    private static final Logger logger = CommonLogger.getLogger("sync");
    private static final EasyCache cache = CacheManager.getMemcache();
    private static String  busiName = "";

    private static final String USER_PASSWORD = MD5Util.getHexMD5("XFKL#123456");

    @Override
    public void execute(JobExecutionContext jobexecutioncontext) {
        try {
            String entId = Constants.getEntId();
            String busiOrderId = Constants.getBusiOrderId();
            String deptCodeId = Constants.getDeptCodeId();
            busiName = Constants.getBusiName();
            EasyQuery query = QueryFactory.getWriteQuery();
            if (StringUtils.isAnyBlank(entId, busiOrderId, deptCodeId,busiName)) {
                logger.info("企业ID、订购id或者部门编码为空，不进行同步");
                return;
            }
            logger.info("》》》开始同步用户和部门数据");
            //保存部门
            saveDept(query);
            //保存人员
            syncUser(query);
            logger.info("《《《结束同步用户和部门数据");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 保存部门的数据
     * @return
     */
    public boolean saveDept(EasyQuery query){
        String deptCodeId = Constants.getDeptCodeId();
        try {
            EasySQL sql = new EasySQL("select * from "+getTableName("tworkgroup")+" t1 where 1=1");
            sql.append(Constants.getHwVdnId()," and t1.VDN=?");
            sql.append(Constants.getHwCcId()," and t1.SUBCCNO=?");
            sql.append(" and WORKGROUPID not in (select WORK_GROUP_ID from "+getTableName("cx_skill_work_relation")+")");
            List<JSONObject> deptList = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            //有没写入的重新写入表
            if(deptList.size()>0){
                int maxDeptCode = maxDeptCode(query);
                if(maxDeptCode==0){
                    maxDeptCode = 1000;
                }
                EasyRecord record = null;
                for(JSONObject dept:deptList){
                    maxDeptCode = maxDeptCode+1;
                    record = new EasyRecord(getTableName("cc_skill_group"),"SKILL_GROUP_ID");
                    record.put("SKILL_GROUP_ID",maxDeptCode);
                    record.put("SKILL_GROUP_CODE",maxDeptCode);
                    record.put("GROUP_TYPE","01");
                    record.put("P_GROUP_ID",deptCodeId);
                    record.put("BUSI_ORDER_ID",Constants.getBusiOrderId());
                    record.put("ENT_ID",Constants.getEntId());
                    record.put("SKILL_GROUP_NAME",dept.getString("WORKGROUP"));
                    record.put("SKILL_GROUP_TYPE","struct");
                    record.put("BUSI_TYPE","1");
                    record.put("QUEUE_STRATEGY","1");
                    record.put("CREATE_TIME",EasyDate.getCurrentDateString());
                    try {
                        query.save(record);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    record = new EasyRecord(getTableName("cx_skill_work_relation"),"ID");
                    record.put("ID", RandomKit.randomStr());
                    record.put("SKILL_GROUP_ID",maxDeptCode);
                    record.put("WORK_GROUP_ID",dept.getString("WORKGROUPID"));
                    record.put("CREATE_TIME",EasyDate.getCurrentDateString());
                    try {
                        query.save(record);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
            //更新班组内名称
            query.execute("update "+getTableName("cc_skill_group")+" t1 inner join "
                    +getTableName("cx_skill_work_relation")+" t2 on t1.SKILL_GROUP_ID=t2.SKILL_GROUP_ID " +
                    "inner join "+getTableName("tworkgroup")+" t3 on t2.WORK_GROUP_ID=t3.WORKGROUPID set t1.SKILL_GROUP_NAME=t3.WORKGROUP");

            //查询被删除的班组
            List<JSONObject> deptList2 = query.queryForList("SELECT t1.* from "+getTableName("cx_skill_work_relation")
                    +" t1 where t1.WORK_GROUP_ID not in(SELECT WORKGROUPID from "+getTableName("tworkgroup")+" where VDN=? and SUBCCNO=?)"
                    ,new Object[]{Constants.getHwVdnId(),Constants.getHwCcId()},new JSONMapperImpl());
            for (JSONObject dept:deptList2){
                //删除班组和用户之间的关系
                query.execute("delete from "+getTableName("cc_skill_group_user")+" where SKILL_GROUP_ID=?"
                        ,new Object[]{dept.getString("SKILL_GROUP_ID")});
                //删除班组
                query.execute("delete from "+getTableName("cc_skill_group")+" where SKILL_GROUP_ID=?"
                        ,new Object[]{dept.getString("SKILL_GROUP_ID")});
            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return false;
        }

        return true;
    }


    /**
     * 同步坐席数据到用户表
     * @param query
     * @return
     */
    public boolean syncUser(EasyQuery query){
        try {
            EasySQL sql = new EasySQL("select * from "+getTableName("tagentinfo")+" t1 where 1=1");
            sql.append(Constants.getHwVdnId()," and t1.VDN=?");
            sql.append(Constants.getHwCcId()," and t1.SUBCCNO=?");
            sql.append(" and AGENTID not in (select USER_ACCT from cc_user where SSO_ACCT is not null)");
            List<JSONObject> userList = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if(userList!=null && userList.size()>0){
                for(JSONObject user:userList){
                    addUser(query,user);
                }
            }
            //更新坐席名称名称
            query.execute("update "+getTableName("cc_busi_user")+" t1 inner join "
                    +getTableName("tagentinfo")+" t2 on t1.USER_ACCT=t2.AGENTID " +
                    " set t1.AGENT_NAME=t2.NAME where t2.NAME is not null");
            //更新坐席名称名称
            query.execute("update CC_USER t1 inner join "
                    +getTableName("tagentinfo")+" t2 on t1.USER_ACCT=t2.AGENTID " +
                    " set t1.USERNAME=t2.NAME where t2.NAME is not null");
            //排找已经被禁用的用户
            sql = new EasySQL("select USER_ID from cc_user t1 where 1=1");
            sql.append(USER_PASSWORD," and t1.USER_PWD=? and t1.SSO_ACCT is not null ");
            sql.append(" and t1.SSO_ACCT NOT IN(select AGENTID from "+getTableName("tagentinfo")+" t1 where 1=1");
            sql.append(Constants.getHwVdnId()," and t1.VDN=?");
            sql.append(Constants.getHwCcId()," and t1.SUBCCNO=?");
            sql.append(")");
            //被删除的用户列表
            List<JSONObject> unUserList = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if(unUserList!=null && unUserList.size()>0){
                for(JSONObject unUser:unUserList){
                    //用户状态，0：正常 1：暂停  9：删除
                    query.execute("update cc_user set USER_STATE=9 where USER_ID=?",new Object[]{unUser.getString("USER_ID")});
                    //用户状态，0：正常 1：停用
                    query.execute("update "+getTableName("cc_busi_user")+" set USER_STATE=1 where USER_ID=?",new Object[]{unUser.getString("USER_ID")});
                }
            }
            //查询有调整部门的坐席，再将部门动态调整
            sql = new EasySQL("select t1.USER_ID,t2.WORKGROUPID,t3.SKILL_GROUP_ID from cc_user t1 inner join ");
            sql.append(getTableName("tagentinfo")+" t2 on t1.SSO_ACCT=t2.AGENTID inner join ");
            sql.append(getTableName("cc_skill_group_user")+" t3 on t1.USER_ID=t3.USER_ID inner join ");
            sql.append(getTableName("cx_skill_work_relation")+" t4 on t3.SKILL_GROUP_ID=t4.SKILL_GROUP_ID and t2.WORKGROUPID<>t4.WORK_GROUP_ID where 1=1 ");
            sql.append(Constants.getHwVdnId()," and t2.VDN=?");
            sql.append(Constants.getHwCcId()," and t2.SUBCCNO=?");
            List<JSONObject> deptList = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if(deptList!=null && deptList.size()>0){
                String deptId = "";
                for(JSONObject dept:deptList){
                    deptId = getDeptId(query,dept.getString("WORKGROUPID"));
                    //更新旧的部门到新的部门中
                    query.execute("update "+getTableName("cc_skill_group_user")+" set SKILL_GROUP_ID=? where SKILL_GROUP_ID=? and USER_ID=?",
                            new Object[]{deptId,dept.getString("SKILL_GROUP_ID"),dept.getString("USER_ID")});
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return false;
        }

        return true;
    }


    /**
     * 新增用户
     * @param query
     * @param user
     */
    public void addUser(EasyQuery query,JSONObject user){
        try {
            String agentId = user.getString("AGENTID");
            String name = user.getString("NAME");
            EasyRecord record = new EasyRecord("CC_USER","USER_ID");
            String userId = RandomKit.randomStr();
            record.put("USER_PWD",USER_PASSWORD);
            record.put("USER_ID",userId);
            record.put("SSO_ACCT",agentId);
            record.put("AGENT_PHONE",agentId);
            record.put("ENT_ID", Constants.getEntId());
            record.put("USERNAME",name);
            record.put("USER_ACCT",getUserAcct(query,agentId));
            //record.put("MOBILE",mobile);
            record.put("USER_STATE","0");//用户状态，0：正常 1：暂停  9：删除
            record.put("CREATOR","system");
            record.put("CREATE_TIME",EasyDate.getCurrentDateString());
            record.put("ADMIN_FLAG",0);
            record.put("LOCK_STATE",0);//0：正常  1：已锁定
            query.save(record);
            addBusiUser(query,record);//新增业务库
            addBusiUserDept(query,userId,user.getString("WORKGROUPID"),agentId);//新增业务库部门
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 获取用户账号
     * @param query
     * @param agentId
     * @return
     */
    public String getUserAcct(EasyQuery query,String agentId){
        return agentId;

    }

    /**
     * 获取到当前最大的部门编码，在这个基础上累加
     * @param query
     * @return
     * @throws SQLException
     */
    public int maxDeptCode(EasyQuery query) throws SQLException {
        return query.queryForInt("select max(SKILL_GROUP_ID) from "+getTableName("cc_skill_group")+" where P_GROUP_ID=?",new Object[]{Constants.getDeptCodeId()});
    }


    /**
     * 同步用户
     * @return
     */
    public EasyResult addBusiUser(EasyQuery query, JSONObject jsonObject) throws Exception {
        String userId = jsonObject.getString("USER_ID");
        String userName = jsonObject.getString("USERNAME");
        String mobile = jsonObject.getString("MOBILE");
        Integer userState = Integer.valueOf(jsonObject.getString("USER_STATE"));
        String userAcc = jsonObject.getString("USER_ACCT");
        String agentPhone = jsonObject.getString("AGENT_PHONE");
        String entId = Constants.getEntId();
        String busiOrderId = Constants.getBusiOrderId();
        String creator = "system";
        try {
            String createTime = EasyDate.getCurrentDateString();
            query.setLogger(logger);
            query.execute("insert into "+getTableName("CC_BUSI_USER")+" (USER_ID,BUSI_ORDER_ID,ENT_ID,MOBILE,AGENT_NAME,USER_STATE,CREATOR," +
                    "CREATE_TIME,USER_ACCT,AGENT_PHONE) values(?,?,?,?,?,?,?,?,?,?)",
                    userId,busiOrderId,entId,mobile,userName,userState,creator,createTime,userAcc,agentPhone);
        } catch (Exception e) {
            logger.error("操作失败:"+e.getMessage(), e);
            throw new Exception("同步业务库表失败，失败原因："+e.getMessage());
        }
        return EasyResult.ok("","操作成功");
    }

    /**
     * 给坐席通过工作组id设置部门
     * @param query
     * @throws Exception
     */
    public void addBusiUserDept(EasyQuery query, String userId,String workGroupId,String agentId) throws Exception {
        try {
            String deptId = getDeptId(query,workGroupId);
            if(StringUtils.isBlank(deptId)){
                logger.error(agentId+"操作失败:没有找到部门id");
                return;
            }
            query.execute("INSERT INTO "+getTableName("cc_skill_group_user")
                    +"(SKILL_GROUP_ID, USER_ID, BUSI_ORDER_ID, ENT_ID, IDX_ORDER, WORK_TIME, ENABLE_STATUS, WORK_GROUP_ID, IS_LEADER, LIMIT_TIME) " +
                    "VALUES (?, ?, ?, ?, 1, 0, '01', '0', 'N', '2099-12-31');",new Object[]{deptId,userId,Constants.getBusiOrderId(),Constants.getEntId()});
        }catch (Exception e){
            logger.error("操作失败:"+e.getMessage(), e);
            throw new Exception("同步业务库表失败，失败原因："+e.getMessage());
        }
    }

    /**
     * 通过华为班组id获取部门id
     * @param workGroupId
     * @return
     */
    public String getDeptId(EasyQuery query,String workGroupId){
        String key = "CX_DEPT_ID_"+workGroupId;
        String deptId = cache.get(key);
        try {
            if(StringUtils.isBlank(deptId)){
                deptId = query.queryForString("select SKILL_GROUP_ID from "+getTableName("cx_skill_work_relation")+" where WORK_GROUP_ID=? ",new Object[]{workGroupId});
                cache.put(key, deptId, 5*60);//缓存保存5分钟
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return deptId;
    }

    private String getTableName(String tableName){
        return busiName+"."+tableName;
    }
}
