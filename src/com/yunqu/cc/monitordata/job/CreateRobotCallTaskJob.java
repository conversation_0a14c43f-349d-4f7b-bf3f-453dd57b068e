package com.yunqu.cc.monitordata.job;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.util.DateUtil;
import com.yunqu.cc.monitordata.util.DesUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.CacheTime;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.utils.kit.RandomKit;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * Title: CreateRobotCallTaskJob
 * Description:每个月1号创建机器人外呼任务
 * Copyright: Copyright (c) 2013
 * Company: 云趣科技
 * @Author: chenzhiwei
 * @Version 1.0
 */
public class CreateRobotCallTaskJob implements Job {
    private static final Logger logger = CommonLogger.getLogger("create-task-job");

    protected EasyCache cache = CacheManager.getMemcache();
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("进入定时创建机器人外呼任务....");
        // 获取当前时间
        String currentDate = DateUtil.getCurrentDateStr();
        // 202511
        String monthId = currentDate.replace("-", "").substring(0, 6);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("entId", Constants.getEntId());
        jsonObject.put("busiId", "002");
        jsonObject.put("serialId", RandomKit.randomStr());
        jsonObject.put("timestamp", System.currentTimeMillis());
        String taskId = RandomKit.uuid();
        jsonObject.put("taskId", taskId);
        String taskName = "机器人外呼任务" + monthId;
        jsonObject.put("taskName", taskName);

        JSONObject params = new JSONObject();
        params.put("startDate", currentDate.substring(0, 10));
        // 当前月的最后一天
        String currMonthEndDay = DateUtil.getCurrMonthEndDay("yyyy-MM-dd");
        params.put("endDate", currMonthEndDay);
        params.put("taskType", "3");

        JSONArray custTempList = new JSONArray();
        custTempList.add("手机号");
        custTempList.add("姓名");
        custTempList.add("通知内容");
        params.put("custTempList", custTempList);
        params.put("custTempTelNum", "手机号");
        params.put("robotId", Constants.getRobotId());
        jsonObject.put("params", params);

        // 获取创建机器人外呼接口
        String url = Constants.getCreateRobotCallTaskUrl() + "?entId=" + Constants.getEntId();
        // 获取密钥
        String secretKey = Constants.getSecretKey();
        logger.info("密钥：" + secretKey);
        String requestStr = DesUtil.EncryptDES(secretKey, jsonObject.toString());
        logger.info("创建机器人外呼任务地址:" + url + ", 加密前请求参数：" + jsonObject + ",加密后的请求参数：" +  requestStr);
        HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .body(requestStr)
                .timeout(60000)
                .execute();
        String resp = response.body();
        logger.info("创建机器人外呼任务结果：" + resp);
        if (StringUtils.isNotBlank(resp)) {
            JSONObject respJson = JSONObject.parseObject(resp);
            if ("Succ".equals(respJson.getString("systemCode"))) {
                // 将任务id和任务名称保存到缓存中
//                RedissonUtil.set("ROBOT_TASK:" + monthId, taskId);
                cache.put("ROBOT_TASK:" + monthId, taskId, CacheTime.ONE_DAY * 31);
                // 创建成功，将任务启动
                JSONObject startParams = new JSONObject();
                startParams.put("entId", Constants.getEntId());
                startParams.put("busiId", "002");
                startParams.put("serialId", RandomKit.randomStr());
                startParams.put("timestamp", System.currentTimeMillis());
                startParams.put("taskId", taskId);

                String startUrl = Constants.getStartRobotCallTaskUrl() + "?entId=" + Constants.getEntId();
                String request = DesUtil.EncryptDES(secretKey, startParams.toString());
                logger.info("创建机器人外呼任务地址:" + startUrl + ", 加密前请求参数：" + jsonObject + ",加密后的请求参数：" +  request);
                HttpResponse response1 = HttpUtil.createPost(startUrl)
                        .contentType("application/json")
                        .body(request)
                        .timeout(60000)
                        .execute();
                String resp1 = response1.body();
                logger.info("启动机器人外呼任务结果：" + resp1);
                if (StringUtils.isNotBlank(resp1)) {
                    JSONObject respJson1 = JSONObject.parseObject(resp1);
                    if ("Succ".equals(respJson1.getString("systemCode"))) {
                        logger.info("启动机器人外呼任务成功");
                    } else {
                        logger.info("启动机器人外呼任务失败");
                    }
                }
            } else {
                logger.info("创建机器人外呼任务失败");
            }
        }
    }

    public static void main(String[] args) throws JobExecutionException {
        CreateRobotCallTaskJob job = new CreateRobotCallTaskJob();
        job.execute(null);
    }

}
