package com.yunqu.cc.monitordata.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.cc.monitordata.listener.InitListener;
import com.yunqu.cc.monitordata.util.*;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class AgentPlaceJob implements Job {

    private static final Logger logger = CommonLogger.getLogger("agentPlace");
    private static final EasyCache cache = CacheManager.getMemcache();
    private static final String busiName = Constants.getBusiName();


    @Override
    public void execute(JobExecutionContext jobexecutioncontext) {

        if (!Constants.isStartAgentplaceTask()) {
            logger.info("告警任务未开启");
            return;
        }
        try {
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("1", "CALL_LONG");
                }
            });
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("3", "VOICE");
                }
            });
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("4", "SPEECH_FAST");
                }
            });
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("5", "FORESTALL");
                }
            });
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("6", "BADWORD");
                }
            });
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词
                    writeAlarmTypeWarn("7", "SENS");
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    //更新已经迁出的坐席的告警数据
                    updateMissingAgentIds();
                }
            });
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 更新已经迁出的坐席的告警数据
     */
    public void updateMissingAgentIds() {
        String key = "updateMissingAgentIds_ISCACHE";
        try {
            if (!InitListener.runState) {
                logger.error("进程已经停止");
                return;
            }

            String isCache = CacheUtil.get(key);
            if(StringUtils.isNotBlank(isCache)){
                logger.info("正在更新已经迁出的坐席的告警数据，本次跳过");
                return;
            }
            CacheUtil.put(key,"Y",10*60);//保存十分钟
            // 查询指定VDN下的签入座席所带电话号码
            List<JSONObject> ctiAgentPhones = CtiUtils.queryallagentphones().toJavaList(JSONObject.class);
            //获取所有有告警的坐席工号
            List<String> agentWarnNos = getAgentWarnAgentNos();
            // 获取所有ctiAgentPhones中的agentId
            Set<String> ctiAgentIds = ctiAgentPhones.stream()
                    .filter(obj -> obj != null && obj.containsKey("agentId"))
                    .map(obj -> obj.getString("agentId"))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            // 找出在告警列表中但不在ctiAgentPhones中的工号
            List<String> missingAgentIds = agentWarnNos.stream()
                    .filter(agentId -> !ctiAgentIds.contains(agentId))
                    .collect(Collectors.toList());
            //更新已经签出的坐席的告警
            CommonUtils.updateLogoutWarm(missingAgentIds);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }finally {
            CacheUtil.delete(key);
        }
    }

    /**
     * 获取所有未处理的告警的坐席工号
     * @return
     */
    public List<String> getAgentWarnAgentNos() {
        List<String> agentNos = new ArrayList<>();
        String sql = "select AGENT_ID from " + busiName + ".cx_agent_alarm_12345 where IS_ALARM=1  and DATE_ID>=? and DATE_ID<=? group by AGENT_ID";
        try {
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql, new Object[]{
                    EasyDate.addTime("yyyyMMdd", EasyDate.getCurrentDateString("yyyyMMdd"), Calendar.DAY_OF_YEAR, -7)
                    ,EasyDate.getCurrentDateString("yyyyMMdd")}, new JSONMapperImpl());
            if (list != null) {
                for (JSONObject obj : list) {
                    agentNos.add(obj.getString("AGENT_ID"));
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return agentNos;
    }


    /**
     * 通过传入的告警类型和告警名称去读取告警表，并将告警记录写入
     * @param alarmType
     * @param alarmName
     */
    public void writeAlarmTypeWarn(String alarmType, String alarmName) {
        if (!InitListener.runState) {
            logger.error("进程已经停止");
            return;
        }
        String key = "writeAlarmTypeWarn_isCache_" + alarmType;
        try {

            String isCache = cache.get(key);
            if (StringUtils.isNotBlank(isCache)) {
                logger.info("通过传入的告警类型和告警名称去读取告警表，并将告警记录写入" + alarmType + "_" + alarmName);
                return;
            }
            cache.put(key,"Y");
            // 先查询需要更新的记录ID
            EasySQL querySql = new EasySQL("SELECT t1.MSG_ID FROM " + busiName + ".cx_agent_alarm_12345 t1 ");
            querySql.append("JOIN " + Constants.getAiaDb() + ".aia_monitor_info t2 ON t1.MSG_ID = t2.CALL_ID ");
            querySql.append("WHERE t1.IS_ALARM='1' AND t2.END_TIME>=? AND t1.alarm_type=? and t1.DATE_ID>=? and t1.DATE_ID<=?");

            List<JSONObject> idsToUpdate = QueryFactory.getReadQuery().queryForList(
                    querySql.getSQL(),
                    new Object[]{
                            DateUtil.addDay("yyyy-MM-dd HH:mm:ss", EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1),
                            alarmType,DateUtil.addDay("yyyyMMdd",DateUtil.getCurrentDateStr("yyyyMMdd"), -1)
                            ,DateUtil.getCurrentDateStr("yyyyMMdd")
                    },
                    new JSONMapperImpl()
            );
            int batchSize = 100;
            // 分批处理更新，每批100条
            if (idsToUpdate != null && !idsToUpdate.isEmpty()) {

                for (int i = 0; i < idsToUpdate.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, idsToUpdate.size());
                    List<JSONObject> batch = idsToUpdate.subList(i, endIndex);

                    // 构建ID列表
                    StringBuilder idList = new StringBuilder();
                    for (JSONObject obj : batch) {
                        if (idList.length() > 0) {
                            idList.append(",");
                        }
                        idList.append("'").append(obj.getString("MSG_ID")).append("'");
                    }

                    // 执行批量更新
                    String updateSql = "UPDATE " + busiName + ".cx_agent_alarm_12345 SET IS_ALARM='2' " +
                            "WHERE IS_ALARM='1' AND MSG_ID IN (" + idList.toString() + ") AND alarm_type=? and DATE_ID>=? and DATE_ID<=?";

                    QueryFactory.getWriteQuery().execute(updateSql, new Object[]{alarmType,DateUtil.addDay("yyyyMMdd",DateUtil.getCurrentDateStr("yyyyMMdd"), -1)
                            ,DateUtil.getCurrentDateStr("yyyyMMdd")});

                    logger.info("已更新" + batch.size() + "条告警记录，类型: " + alarmType);
                }
            }
            //更新语句，更新通话结束且还未处理的告警
//            String updateSql = "update "+busiName+".cx_agent_alarm_12345 t1  " +
//                    "set IS_ALARM='2' where IS_ALARM='1' and t1.MSG_ID in(select call_ID from (select CALL_ID from "
//                    +Constants.getAiaDb()+".aia_monitor_info  where END_TIME>=?)t2) and alarm_type=?";
//            // logger.info("[告警数据]"+updateSql+"[param]"+DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1));
//            QueryFactory.getWriteQuery().execute(updateSql,new Object[]{
//                    DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1),alarmType});
            EasySQL sql = new EasySQL();
            // 只选择必要的字段，避免使用 t1.*
            sql.append("SELECT t1.CALL_ID, t1.AGENT_ID, t1.AGENT_PHONE, t2.NAME AS AGENT_NAME, t1." + alarmName);
            sql.append(" FROM " + Constants.getAiaDb() + ".aia_monitor_info t1");
            // 使用INNER JOIN替代LEFT JOIN，因为我们需要匹配的记录
            sql.append(" INNER JOIN " + busiName + ".tagentinfo t2 ON t1.AGENT_ID = t2.AGENTID");
            // 添加WHERE条件
            sql.append(" WHERE t1.END_TIME = '' AND t1." + alarmName + " > 0");
            sql.append(Constants.getHwCcId(), " AND t2.SUBCCNO = ?");
            sql.append(Constants.getHwVdnId(), " AND t2.VDN = ?");
            // 使用NOT EXISTS代替LEFT JOIN，效率更高
            sql.append(" AND NOT EXISTS (SELECT 1 FROM " + busiName + ".cx_agent_alarm_12345 t3");
            sql.append(alarmType," WHERE t3.MSG_ID = t1.CALL_ID AND t3.ALARM_TYPE = ?");
            sql.append( DateUtil.addHour("yyyyMMdd",
                    EasyDate.getCurrentDateString("yyyyMMdd"), -1)," AND t3.DATE_ID >= ?");
            sql.append(EasyDate.getCurrentDateString("yyyyMMdd")," AND t3.DATE_ID <= ?");
            sql.append(")");
            logger.info("[告警数据]"+sql.getSQL()+"[param]"+ JSON.toJSONString(sql.getParams()));
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            //更新坐席上次同一个类型的告警为不告警状态
            List<Object[]> updateAgentOldAlarm = new ArrayList<>();
            //新增超长通话告警
            List<Object[]> saveAgentAlarm = new ArrayList<>();
            if (list != null && list.size() > 0) {
                for (JSONObject json : list) {
                    saveAlarm(alarmType, json.getString("AGENT_ID"), json.getString("AGENT_NAME")
                            , json.getString("AGENT_PHONE"), json.getString("CALL_ID"), System.currentTimeMillis()
                            , json.getIntValue(alarmName),updateAgentOldAlarm,saveAgentAlarm);//保存告警记录
                }
            }
            batchSize = 10;
            //更新之前产生告警，且现在状态变更了的告警状态为不告警
            if(updateAgentOldAlarm.size()>0){
                String updateSql = "update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2" +
                        " where ALARM_TYPE=? and DATE_ID>=? and DATE_ID<=? and IS_ALARM=1 and AGENT_ID=?";
                for (int i = 0; i < updateAgentOldAlarm.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, updateAgentOldAlarm.size());
                    List<Object[]> batch = updateAgentOldAlarm.subList(i, endIndex);
                    QueryFactory.getWriteQuery().executeBatch(updateSql,batch);
                }

            }
            //保存新增的告警数据
            if(saveAgentAlarm.size()>0){
                String saveSql = "insert IGNORE into "+busiName+".cx_agent_alarm_12345(MSG_ID,ALARM_TYPE,ALARM_COUNT,DATE_ID,AGENT_ID,AGENT_PHONE," +
                        "AGENT_NAME,ALARM_TIME,AUDIT_LEVEN,IS_ALARM,MONTH_ID) " +
                        "values(?,?,?,?,?,?,?,?,?,?,?)";
                for (int i = 0; i < saveAgentAlarm.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, saveAgentAlarm.size());
                    List<Object[]> batch = saveAgentAlarm.subList(i, endIndex);
                    QueryFactory.getWriteQuery().executeBatch(saveSql, batch);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            cache.delete(key);
        }
    }

    /**
     * 保存告警信息
     * @param alarmType
     * @param alarmUserId
     * @param alarmUserName
     * @param msgId
     */
    public String saveAlarm(String alarmType,String alarmUserId,String alarmUserName,String alarmUserPhone
            ,String msgId,long currentStateTime,int alarmCount,
                            List<Object[]> updateAgentOldAlarm,
                            List<Object[]> saveAgentAlarm){
        try {
            String alarmTimeStr = EasyDate.getCurrentDateString("yyyyMMdd");
            //更新这个坐席已经告警过但是未处理的数据
            updateAgentOldAlarm.add(new Object[]{alarmType,DateUtil.addDay("yyyyMMdd",alarmTimeStr, -1),alarmTimeStr,alarmUserId});
            if(StringUtils.isBlank(msgId)){
                msgId = RandomKit.randomStr();
            }
            saveAgentAlarm.add(new Object[]{msgId,alarmType,alarmCount, EasyDate.getCurrentDateString("yyyyMMdd")
                    ,alarmUserId,alarmUserPhone,alarmUserName,EasyDate.getCurrentDateString(),currentStateTime,"1", EasyDate.getCurrentDateString("yyyyMM")});
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return msgId;
    }

}
