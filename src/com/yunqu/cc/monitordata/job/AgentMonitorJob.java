package com.yunqu.cc.monitordata.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.cc.monitordata.listener.InitListener;
import com.yunqu.cc.monitordata.util.*;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.*;
import java.util.stream.Collectors;

public class AgentMonitorJob implements Job {

    private static final Logger logger = CommonLogger.getLogger("AgentMonitorThread");

    private static final EasyCache cache = CacheManager.getMemcache();
    String skillIds = "";
    String isSkillAllMatch = "false";
    @Override
    public void execute(JobExecutionContext jobexecutioncontext) {
        try {
            if(!InitListener.runState){
                logger.error("进程已经停止");
                return;
            }
            JSONObject userData = new JSONObject();
            // 用户选的技能队列id
            skillIds = CommonUtils.queryUserSelectSkillId();
            //logger.info("用户选的技能队列id ：" + skillIds);
            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    agentstatusinfobyskillids();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    agentstatusinfo();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    callstatusinfo();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    skillstatusinfo();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    queryStatInfoOfEverySkill();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    queryAgentPlace();
                }
            });

            ThreadMgr.getInstance().executeOneTimes(new Runnable() {
                @Override
                public void run() {
                    skilltrafficstat();
                }
            });

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
    /**
     * 查询多个技能下座席状态的实时监控信息
     * answering  应答业务代表数。
     * busyRate   业务代表示忙率。
     * idle       空闲业务代表数。
     * lateAdjust 事后整理业务代表数。
     * logined    签入业务代表数。
     * notlogined 未签入业务代表数。
     * occupy     占用态话务员数。
     * preoccupy  预占用态话务员数。
     * rest       休息业务代表数。
     * setbusy    示忙业务代表数。
     * studing    学习态话务员数。
     * talking    通话业务代表数。
     * usable     可用业务代表数。
     * workSubstateBegin 调整业务代表数。
     * callOut    呼出业务代表数。
     *
     * @return void
     * <AUTHOR>
     * @date 2024/1/24 08:54
     */
    public void agentstatusinfobyskillids() {
        String[] skillIdArr = skillIds.split(",");
        for (String skillId : skillIdArr) {
            JSONObject result = CtiUtils.queryAgentStatusBySkill(skillId, isSkillAllMatch);
            cache.put("agentstatusinfobyskillids:" + skillId, result.toJSONString(), 300);
        }
    }

    /**
     * 查询多个技能下人员活动态的实时监控技能信息
     *
     * @return void
     * <AUTHOR>
     * @date 2024/1/24 09:06
     */
    public JSONArray agentstatusinfo() {
        String[] skillIdArr = skillIds.split(",");
        JSONArray allResult = new JSONArray();
        for (String skillId : skillIdArr) {
            JSONArray result = CtiUtils.agentstatusinfo(skillId, isSkillAllMatch, "agentstatusinfo");
            allResult.addAll(result);
            cache.put("agentstatusinfo:" + skillId, result.toJSONString(), 300);
        }
        cache.put("agentstatusinfo:all", allResult, 300);
        return allResult;
    }

    /**
     * 查询分层服务下多个技能下指定时间段内的信息
     *
     * @return void
     * <AUTHOR>
     * @date 2024/1/24 09:12
     */
    public void callstatusinfo() {
        // 时间戳 毫秒
        long startTime = DateUtil.getTimestamp(DateUtil.getCurrentDateStr("yyyy-MM-dd") + " 00:00:00").getTime();
        long endTime = DateUtil.getTimestamp(DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss")).getTime();

        String[] skillIdArr = skillIds.split(",");
        for (String skillId : skillIdArr) {
            JSONObject result = CtiUtils.callstatusinfo(skillId, startTime, endTime);
            cache.put("callstatusinfo:" + skillId, result.toJSONString(), 300);
        }
    }

    /**
     * 查询技能队列的实时监控信息
     *
     * @return void
     * <AUTHOR>
     * @date 2024/1/24 09:12
     */
    public void skillstatusinfo() {
        JSONArray skillstatusinfo = CtiUtils.skillstatusinfo(skillIds);
        for (int i = 0; i < skillstatusinfo.size(); i++) {
            JSONObject result = skillstatusinfo.getJSONObject(i);
            String skillId = result.getString("skillId");
            cache.put("skillstatusinfo:" + skillId, result.toJSONString(), 300);
        }
    }

    /**
     * 查询技能队列的实时监控信息
     *
     * @return void
     * <AUTHOR>
     * @date 2024/1/24 09:12
     */
    public void queryStatInfoOfEverySkill() {
        JSONArray resultDatas = CtiUtils.queryStatInfoSkill(skillIds);
        for (int i = 0; i < resultDatas.size(); i++) {
            JSONObject result = resultDatas.getJSONObject(i);
            String skillId = result.getString("skillId");
            cache.put("queryStatInfoOfEverySkill:" + skillId, result.toJSONString(), 300);
        }
    }



    // 坐席位置图列表
    public void queryAgentPlace() {
        String key = "queryAgentPlace_isCache";
        String isCache = cache.get(key);
        if(StringUtils.isNotBlank(isCache)){
            logger.info("上次任务没跑完，跨过此次");
            return;
        }
        cache.put(key, "isCache");
        try {

            List<Map<String, String>> dictList = CommonUtils.getDict("AgentMonitor_HF");
            // 从报表表中获取坐席信息
            List<Map<String, String>> tAgentInfoList = CommonUtils.queryAgentInfo();
            // 将坐席信息 转成 Map<坐席id,名字> 下面列表用到了
            Map<String, String> tAgentInfo = tAgentInfoList.stream().collect(Collectors.toMap(t -> t.get("AGENTID"), t -> t.get("NAME"), (o, n) -> n, HashMap::new));
            // 将坐席信息 转成 Map<坐席id,工作组id> 下面列表用到了
            Map<String, String> tAgentGroupInfo = tAgentInfoList.stream().collect(Collectors.toMap(t -> t.get("AGENTID"), t -> t.get("WORKGROUPID"), (o, n) -> n, HashMap::new));
            // 将坐席信息 转成 Map<坐席id,工作组名称> 下面列表用到了
            Map<String, String> tAgentGroupNameInfo = tAgentInfoList.stream().collect(Collectors.toMap(t -> t.get("AGENTID"), t -> t.get("WORKGROUP"), (o, n) -> n, HashMap::new));
            //获取用户角色列表
            Map<String,String> userRoleMap = CommonUtils.getUserRole();
            // 查询指定VDN下的签入座席所带电话号码
            List<JSONObject> ctiAgentPhones = CtiUtils.queryallagentphones().toJavaList(JSONObject.class);
            //获取所有坐席的信息
            JSONArray allResult = cache.get("agentstatusinfo:all");
            if(allResult==null){
                allResult = agentstatusinfo();
            }
            List<JSONObject> agentStatusInfo = allResult.toJavaList(JSONObject.class);
            Map<String, JSONObject> agentStatuses = agentStatusInfo.stream()
                    .filter(jsonObject -> jsonObject.getIntValue("currentState") != 0)
                    .collect(Collectors.toMap(
                            jsonObject -> jsonObject.getString("agentId"),
                            jsonObject -> jsonObject,
                            (existing, replacement) -> replacement // 当有重复键时，用新值替换旧值
                    ));

            //获取所有有告警的坐席工号
//            List<String> agentWarnNos = getAgentWarnAgentNos();
//            // 获取所有ctiAgentPhones中的agentId
//            Set<String> ctiAgentIds = ctiAgentPhones.stream()
//                    .filter(obj -> obj != null && obj.containsKey("agentId"))
//                    .map(obj -> obj.getString("agentId"))
//                    .filter(StringUtils::isNotBlank)
//                    .collect(Collectors.toSet());
//
//            // 找出在告警列表中但不在ctiAgentPhones中的工号
//            List<String> missingAgentIds = agentWarnNos.stream()
//                    .filter(agentId -> !ctiAgentIds.contains(agentId))
//                    .collect(Collectors.toList());
            //更新已经签出的坐席的告警
//            CommonUtils.updateLogoutWarm(missingAgentIds);
            //获取所有超长通话告警
            Map<String,String> extraLongCallWarn = getAgentWarn("1");
            //获取所有话后超时的告警:因为话后超时的只能通过状态时长获取是否超时，所以需要摘出来单独判断
            Map<String,String> agentAfterLongWarn = getAgentWarn("2");
            //获取所有超长通话告警
            Map<String,String> muteWarn = getAgentWarn("3");
            //获取所有静音告警
            Map<String,String> speechSpeedWarn = getAgentWarn("4");
            //获取所有抢话告警
            Map<String,String> robTalkWarn = getAgentWarn("5");
            //获取所有违规词告警
            Map<String,String> violationWordWarn = getAgentWarn("6");
            //获取所有敏感词告警
            Map<String,String> sensitiveWordWarn = getAgentWarn("7");

            //Map<String,String> seekHelpWarn = getAgentWarn("8");

            List<JSONObject> help = getAgentHelp("8");
            //获取求助告警
            Map<String,String> seekHelpWarn = help.stream().collect(Collectors.toMap(t -> t.getString("AGENT_ID"),
                    t -> t.getString("MSG_ID"), (o, n) -> n, HashMap::new));
            //获取求助告警指派人
            Map<String,String> seekHelpWarnId = help.stream().collect(Collectors.toMap(t -> t.getString("AGENT_ID"),
                    t -> t.getString("DISTRIBUTE_AGENT_ID"), (o, n) -> n, HashMap::new));
            //获取所有首发诉求
            Map<String,String> debugAppealWarn = getAgentWarn("9");
            // 查询多个技能下的座席信息
            List<JSONObject> agentSkillStatusInfo = CtiUtils.agentskillstatusinfo(skillIds).toJavaList(JSONObject.class);
            // 将 未签入的坐席工号过滤
            Map<String, String> agentSkills = agentSkillStatusInfo.stream()
                    .collect(Collectors.toMap(
                            jsonObject -> jsonObject.getString("agentId"),  // 使用 agentId 作为键
                            jsonObject -> {
                                JSONArray skills = jsonObject.getJSONArray("agentSkills");
                                if (skills != null && skills.size() > 0) {
                                    String agentSkill = skills.getString(0);
                                    if("18".equals(agentSkill) && skills.size()>1){//排除总签入席
                                        return skills.getString(1);
                                    }
                                    return skills.getString(0);
                                }
                                return "";
                            } // 使用 currentState 作为值
                    ));

            // 放坐席信息  话机号  坐席id 坐席姓名 坐席状态
            List<Map<String, Object>> agentInfoList = new ArrayList<>();
            JSONObject agentstatus = null;
            String currentState = null;
            int currentStateTime = 0;
            String afterLongMsgId = null;
            int mm = 0;
            //批量更新不是话后超时且之前有话后超时的告警的集合
            List<Object[]> batchUpdateNotAfter = new ArrayList<>();
            //更新坐席上次同一个类型的告警为不告警状态
            List<Object[]> updateAgentOldAlarm = new ArrayList<>();
            //新增超长通话告警
            List<Object[]> saveAgentAlarm = new ArrayList<>();

            for (JSONObject agentPhone : ctiAgentPhones) {
                try {
                    if (agentPhone == null) {
                        logger.warn("遇到null的agentPhone对象，跳过");
                        continue;
                    }
                    Map<String, Object> agentInfoMap = new HashMap<>();
                    // 获取agentId前检查键是否存在
                    if (!agentPhone.containsKey("agentId")) {
                        logger.warn("agentPhone对象中不包含agentId键，跳过");
                        continue;
                    }
                    String agentId = agentPhone.getString("agentId");
                    if (StringUtils.isBlank(agentId)) {
                        logger.warn("agentId为空，跳过");
                        continue;
                    }
                    // 获取phone前检查键是否存在
                    if (!agentPhone.containsKey("phone")) {
                        logger.warn("agentPhone对象中不包含phone键，跳过");
                        continue;
                    }
                    String phone = agentPhone.getString("phone");
                    // 检查Map是否为null
                    if (tAgentInfo == null || tAgentGroupInfo == null || tAgentGroupNameInfo == null || userRoleMap == null) {
                        logger.error("关键Map对象为null: tAgentInfo=" + (tAgentInfo == null) +
                                ", tAgentGroupInfo=" + (tAgentGroupInfo == null) +
                                ", tAgentGroupNameInfo=" + (tAgentGroupNameInfo == null) +
                                ", userRoleMap=" + (userRoleMap == null));
                        break; // 中断循环
                    }
                    agentInfoMap.put("agentPhone", phone);
                    agentInfoMap.put("agentId", agentId);
                    agentInfoMap.put("agentName", tAgentInfo.get(agentId));
                    agentInfoMap.put("agentGroup", tAgentGroupInfo.get(agentId));
                    agentInfoMap.put("agentGroupName", tAgentGroupNameInfo.get(agentId));
                    agentInfoMap.put("role", userRoleMap.get(agentId));//角色
                    // 超长通话
                    if(StringUtils.isNotBlank(extraLongCallWarn.get(agentId))) {
                        agentInfoMap.put("extraLongCallMsgId",extraLongCallWarn.get(agentId));//超长通话告警id
                    }else {
                        agentInfoMap.put("extraLongCallMsgId","");//超长通话id
                    }
                    // 话后超时
                    if(StringUtils.isNotBlank(agentAfterLongWarn.get(agentId))) {
                        agentInfoMap.put("afterLongMsgId",agentAfterLongWarn.get(agentId));//话后超时告警id
                    }else {
                        agentInfoMap.put("afterLongMsgId","");//话后超时id
                    }
                    // 静音
                    if(StringUtils.isNotBlank(muteWarn.get(agentId))) {
                        agentInfoMap.put("muteMsgId",muteWarn.get(agentId));//静音告警id
                    }else {
                        agentInfoMap.put("muteMsgId","");//静音id
                    }
                    // 语速过快
                    if(StringUtils.isNotBlank(speechSpeedWarn.get(agentId))) {
                        agentInfoMap.put("speechSpeedMsgId",speechSpeedWarn.get(agentId));//语速过快告警id
                    }else {
                        agentInfoMap.put("speechSpeedMsgId","");//语速过快id
                    }
                    // 抢话
                    if(StringUtils.isNotBlank(robTalkWarn.get(agentId))) {
                        agentInfoMap.put("robTalkMsgId",robTalkWarn.get(agentId));//抢话告警id
                    }else {
                        agentInfoMap.put("robTalkMsgId","");//抢话id
                    }
                    // 违规词
                    if(StringUtils.isNotBlank(violationWordWarn.get(agentId))) {
                        agentInfoMap.put("violationWordMsgId",violationWordWarn.get(agentId));//违规词告警id
                    }else {
                        agentInfoMap.put("violationWordMsgId","");//违规词id
                    }
                    // 敏感词
                    if(StringUtils.isNotBlank(sensitiveWordWarn.get(agentId))) {
                        agentInfoMap.put("sensitiveWordMsgId",sensitiveWordWarn.get(agentId));//敏感词告警id
                    }else {
                        agentInfoMap.put("sensitiveWordMsgId","");//敏感词id
                    }
                    // 求助
                    if(StringUtils.isNotBlank(seekHelpWarn.get(agentId))) {
                        agentInfoMap.put("seekHelpMsgId",seekHelpWarn.get(agentId));//求助告警id
                        agentInfoMap.put("distributeAgentId",seekHelpWarnId.get(agentId));//求助告警id

                    }else {
                        agentInfoMap.put("seekHelpMsgId","");//求助id
                    }
                    // 首发诉求
                    if(StringUtils.isNotBlank(agentId)) {
                        agentInfoMap.put("debugAppealMsgId",debugAppealWarn.get(agentId));//首发诉求id
                    }else {
                        agentInfoMap.put("debugAppealMsgId","");//首发诉求id
                    }
                    agentstatus = agentStatuses.get(agentId);
                    if (agentstatus != null) {
                        currentState = agentstatus.getString("currentState");
                        currentStateTime = agentstatus.getIntValue("currentStateTime");
                        agentInfoMap.put("currentState", currentState);
                        agentInfoMap.put("currentStateTime", currentStateTime);
                        if(("6".equals(currentState) || "10".equals(currentState))) {//判断是否为话后
                            if (isAfterLongCall(currentStateTime)) {//超过了超时时长
                                afterLongMsgId = saveAlarm("2", agentId, tAgentInfo.get(agentId),phone
                                        , "", currentStateTime,1,updateAgentOldAlarm,saveAgentAlarm);//写入话后超时告警
                                agentInfoMap.put("afterLongMsgId", afterLongMsgId);//话后超时告警
                            }
                        }else{
                            afterLongMsgId = agentAfterLongWarn.get(agentId);//话后超时告警id
                            if(StringUtils.isNotBlank(afterLongMsgId)) {
//                                QueryFactory.getWriteQuery().execute("update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2 where MSG_ID=?"
//                                        ,new Object[]{afterLongMsgId} );//更新未处理的告警为不告警
                                batchUpdateNotAfter.add(new Object[]{afterLongMsgId,2,DateUtil.addDay("yyyyMMdd",DateUtil.getCurrentDateStr("yyyyMMdd"), -1)
                                        ,DateUtil.getCurrentDateStr("yyyyMMdd")});
                            }
                        }
                        //if("2""3","4","5")
                    }
                    agentInfoMap.put("agentSkill", agentSkills.get(agentId));
                    agentInfoList.add(agentInfoMap);
                }catch (Exception e){
                    logger.error(e.getMessage(),e);
                    e.printStackTrace();
                }
            }
            int  batchSize = 10;
            //每天有700多坐席在线 顶多七百多个值就不分页了
            if(batchUpdateNotAfter.size()>0){
                //更新之前产生话后超时告警，且现在状态变更了的告警状态为不告警
                String updateNotAfterSql = "update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2 where MSG_ID=? and ALARM_TYPE=? and DATE_ID>=? and DATE_ID<=?";
                //QueryFactory.getWriteQuery().executeBatch(updateNotAfterSql,batchUpdateNotAfter);
                for (int i = 0; i < batchUpdateNotAfter.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, batchUpdateNotAfter.size());
                    List<Object[]> batch = batchUpdateNotAfter.subList(i, endIndex);
                    QueryFactory.getWriteQuery().executeBatch(updateNotAfterSql,batch);
                }
            }
            //更新之前产生告警，且现在状态变更了的告警状态为不告警
            if(updateAgentOldAlarm.size()>0){
                String sql = "update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2" +
                        " where ALARM_TYPE=? and DATE_ID>=? and DATE_ID<=? and IS_ALARM=1 and AGENT_ID=?";
                //QueryFactory.getWriteQuery().executeBatch(sql,updateAgentOldAlarm);
                for (int i = 0; i < updateAgentOldAlarm.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, updateAgentOldAlarm.size());
                    List<Object[]> batch = updateAgentOldAlarm.subList(i, endIndex);
                    QueryFactory.getWriteQuery().executeBatch(sql,batch);
                }
            }
            //保存新增的告警数据
            if(saveAgentAlarm.size()>0){
                String sql = "insert into "+busiName+".cx_agent_alarm_12345(MSG_ID,ALARM_TYPE,ALARM_COUNT,DATE_ID,AGENT_ID,AGENT_PHONE," +
                        "AGENT_NAME,ALARM_TIME,AUDIT_LEVEN,IS_ALARM,MONTH_ID) " +
                        "values(?,?,?,?,?,?,?,?,?,?,?)";
                //QueryFactory.getWriteQuery().executeBatch(sql,saveAgentAlarm);
                for (int i = 0; i < saveAgentAlarm.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, saveAgentAlarm.size());
                    List<Object[]> batch = saveAgentAlarm.subList(i, endIndex);
                    QueryFactory.getWriteQuery().executeBatch(sql,batch);
                }
            }


            JSONObject phoneJsons = new JSONObject();
            // 遍历话房
            for (Map<String, String> dictMap : dictList) {

                // 存放话房是否存在告警未处理的坐席
//                List<String> hfWarnList = new ArrayList<>();
                List<Map<String, Object>> result = new ArrayList<>();
                // 话房编码
                String code = dictMap.get("CODE");
                // 话房下的坐席电话和ip（ip目前没用到）
                List<Map<String, String>> list = CommonUtils.queryHfIpList(code);

                for (Map<String, String> phoneMap : list) {

                    // 话机号码
                    String phone = phoneMap.get("PHONE_NUMBER");
                    // 座位号
                    String seatNo = phoneMap.get("SEAT_NO");
                    // 横坐标
                    String horizontal = phoneMap.get("HORIZONTAL");
                    // 纵坐标
                    String longitudinal = phoneMap.get("LONGITUDINAL");
                    // 是否为空 1不为空 2为空
                    String isNull = phoneMap.get("IS_NULL");
                    // 派发人id
                    //String distributeAgentId = "";
                    Map<String, Object> agentInfo = agentInfoList.stream()
                            .filter(m -> phone.equals(m.get("agentPhone")))
                            .findFirst()
                            .orElse(null);
                    if (agentInfo == null) {
                        agentInfo = new HashMap<>();
                        agentInfo.put("agentPhone", phone);
                        agentInfo.put("isAgent", "0");// 是否查到坐席 0 没查到
                        agentInfo.put("extraLongCallMsgId","");//超长通话id
                        agentInfo.put("afterLongMsgId","");//话后超时id
                        agentInfo.put("muteMsgId","");//静音id
                        agentInfo.put("speechSpeedMsgId","");//语速过快id
                        agentInfo.put("robTalkMsgId","");//抢话id
                        agentInfo.put("violationWordMsgId","");//违规词id
                        agentInfo.put("sensitiveWordMsgId","");//敏感词id
                        agentInfo.put("seekHelpMsgId","");//求助id
                        agentInfo.put("debugAppealMsgId","");//首发诉求id
                    } else {
                        phoneJsons.put(seatNo, "1");
                        agentInfo.put("isAgent", "1"); // 是否查到坐席 1 查到
                    }
                    agentInfo.put("seatNo", seatNo);
                    agentInfo.put("horizontal", horizontal);
                    agentInfo.put("longitudinal", longitudinal);
                    agentInfo.put("isNull", isNull);
                    //agentInfo.put("distributeAgentId", distributeAgentId);
                    result.add(agentInfo);
                }

                cache.put("queryAgentPlace:" + code, JSONObject.toJSONString(result));
            }
            //logger.info("我执行完了！");
            cache.put("queryAgentPhoneSeat", JSONObject.toJSONString(phoneJsons));//获取是否查到坐席且是否告警 1告警 2 不告警
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("坐席位置图监控查询异常", e);
            cache.delete(key);
        }

        cache.delete(key);
    }

    public void queryPhoneRoom() {
        List<JSONObject> list = CommonUtils.queryPhoneRoom();
        for (JSONObject obj : list) {
            String code = obj.getString("CODE");
//            String name = obj.getString("NAME");
            String hfWarnStr = cache.get("hfWarnList:" + code);
            if(StringUtils.isNotBlank(hfWarnStr)) {
                JSONArray hfWarnList = JSONObject.parseArray(hfWarnStr);
                obj.put("hfWarnList",hfWarnList);
            }else {
                obj.put("hfWarnList",new JSONArray());
            }
        }
        cache.put("AgentMonitor:phoneRoomWarn", JSONObject.toJSONString(list));
    }

    private static final String busiName = Constants.getBusiName();
    /**
     * 保存告警信息
     * @param alarmType
     * @param alarmUserId
     * @param alarmUserName
     * @param msgId
     */
    public String saveAlarm(String alarmType,String alarmUserId,String alarmUserName,String alarmUserPhone
            ,String msgId,long currentStateTime,int alarmCount,
                            List<Object[]> updateAgentOldAlarm,
                            List<Object[]> saveAgentAlarm){
        try {
            String alarmTimeStr = EasyDate.getCurrentDateString("yyyyMMdd");
            if("2".equals(alarmType)){//如果为话后整理的话，通过状态开始时间判断是否产生告警
               // alarmTimeStr = DateUtil.addSecond("yyyy-MM-dd HH:mm:ss", DateUtil.getCurrentDateStr(),-(int) currentStateTime);
                //logger.info("[alarmType]"+alarmType+"[alarmUserId]"+alarmUserId+"[alarmUserName]"+alarmUserName+"[alarmUserPhone]"+alarmUserPhone+"[msgId]"+msgId+"[currentStateTime]"+currentStateTime);

                JSONObject alarmJson = QueryFactory.getReadQuery().queryForRow("select MSG_ID,IS_ALARM from "+busiName+".cx_agent_alarm_12345" +
                                " where ALARM_TYPE=? and AUDIT_LEVEN<? and AGENT_ID=? and DATE_ID>=? and DATE_ID<=?"
                        ,new Object[]{alarmType,currentStateTime,alarmUserId,DateUtil.addDay("yyyyMMdd",DateUtil.getCurrentDateStr("yyyyMMdd"), -1)
                                ,DateUtil.getCurrentDateStr("yyyyMMdd")},new JSONMapperImpl() );
                if(alarmJson!=null){
                    String oldMsgId = alarmJson.getString("MSG_ID");//获取消息id
                    String isAlarm = alarmJson.getString("IS_ALARM");
                    if("2".equals(isAlarm)){
                        //logger.info("已经处理了！");
                        return "";
                    }
                    if(StringUtils.isNotBlank(oldMsgId)){
                        // logger.info("上次话后整理还未结束，此次不记录！");
                        return oldMsgId;
                    }


                }
            }

            try {
                //更新这个坐席已经告警过但是未处理的数据
//                QueryFactory.getWriteQuery().execute("update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2" +
//                                " where ALARM_TYPE=? and ALARM_TIME>? and ALARM_TIME<? and IS_ALARM=1 and AGENT_ID=?"
//                        ,new Object[]{alarmType,DateUtil.addDay("yyyy-MM-dd HH:mm:ss",alarmTimeStr, -1),alarmTimeStr,alarmUserId} );
                updateAgentOldAlarm.add(new Object[]{alarmType,DateUtil.addDay("yyyyMMdd",alarmTimeStr, -1),alarmTimeStr,alarmUserId});
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
            if(StringUtils.isBlank(msgId)){
                msgId = RandomKit.randomStr();
            }
            saveAgentAlarm.add(new Object[]{msgId,alarmType,alarmCount, EasyDate.getCurrentDateString("yyyyMMdd")
                    ,alarmUserId,alarmUserPhone,alarmUserName,EasyDate.getCurrentDateString(),currentStateTime,"1", EasyDate.getCurrentDateString("yyyyMM")});

//            EasyRecord record = new EasyRecord(busiName+".cx_agent_alarm_12345","MSG_ID","ALARM_TYPE","ALARM_COUNT");
//            record.set("MSG_ID", msgId);//消息id，其中求助的在cx-mix-12345中写入，其他告警类型在此写入，话后超时的id是随机生成的，其他id都是话单id
//            record.set("ALARM_TYPE", alarmType);//告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求
//            record.set("ALARM_COUNT", alarmCount);
//            record.set("DATE_ID", EasyDate.getCurrentDateString("yyyy-MM-dd"));
//            record.set("AGENT_ID", alarmUserId);
//            record.set("AGENT_PHONE", alarmUserPhone);
//            record.set("AGENT_NAME", alarmUserName);
//            record.set("ALARM_TIME", alarmTimeStr);
//
//            record.set("AUDIT_LEVEN", currentStateTime);
//            record.set("IS_ALARM", "1");//是否告警1 告警 2不告警
//            if(!QueryFactory.getWriteQuery().update(record)){
//                QueryFactory.getWriteQuery().save(record);
//            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return msgId;
    }



    /**
     * 判断当前状态是否为话后超时，是的话是否超过了话后超时配置的阈值，超过了返回true
     * @param currentStateTime
     * @return
     */
    public boolean isAfterLongCall(int currentStateTime){
        boolean isAfterLongCall = false;
        JSONObject monitorJson = new JSONObject();
        try {
            String monitorCache = CacheUtil.hget("AIA_OPERATE_MONITOR", "6");
            if(StringUtils.isNotBlank(monitorCache)) {
                monitorJson = JSONObject.parseObject(monitorCache);
            }else {
                monitorJson = new JSONObject();
            }
            if(StringUtils.isBlank(monitorJson.getString("status"))) {
                monitorJson.put("status", "1");
                monitorJson.put("configNum",  AppContext.getContext("cx-monitor-12345").getProperty("TIMEOUT_NUMBER", "120"));
            }
            String status = monitorJson.getString("status");
            String configNum = monitorJson.getString("configNum");
            if("1".equals(status)) {
                // 判断是否为话后超时
                if(currentStateTime > Integer.parseInt(configNum)) {
                    isAfterLongCall = true;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            monitorJson = new JSONObject();
        }
        return isAfterLongCall;
    }

    /**
     * 获取所有未处理的求助数据
     * @return
     */
    public  List<JSONObject>  getAgentHelp(String alarmType) {
        List<JSONObject> list= null;
        String sql = "select t1.MSG_ID,t1.AGENT_ID,t2.DISTRIBUTE_AGENT_ID from "+busiName+".cx_agent_alarm_12345 t1 inner join "+busiName+".cx_agent_remind_table t2 on t1.MSG_ID=t2.MSG_ID" +
                " where t1.ALARM_TYPE=? and t1.IS_ALARM=1  and t1.DATE_ID>=? and t1.DATE_ID<=?";
        try {
            list = QueryFactory.getReadQuery().queryForList(sql,new Object[]{alarmType,
                    EasyDate.addTime("yyyyMMdd",EasyDate.getCurrentDateString("yyyyMMdd"), Calendar.DAY_OF_YEAR,-7)
                    ,EasyDate.getCurrentDateString("yyyyMMdd")},new JSONMapperImpl());
//            if (list != null) {
//                for (JSONObject obj : list) {
//                    agentWarn.put(obj.getString("AGENT_ID"),obj.getString("MSG_ID"));
//                }
//            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return list;
    }

    /**
     * 获取所有未处理的告警数据
     * @return
     */
    public Map<String,String> getAgentWarn(String alarmType) {
        Map<String,String> agentWarn = new HashMap<>();
        String sql = "select MSG_ID,AGENT_ID from "+busiName+".cx_agent_alarm_12345 where ALARM_TYPE=? and IS_ALARM=1  and DATE_ID>=? and DATE_ID<=?";
        try {
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql,new Object[]{alarmType,
                    EasyDate.addTime("yyyyMMdd",EasyDate.getCurrentDateString("yyyyMMdd"), Calendar.DAY_OF_YEAR,-7)
                    ,EasyDate.getCurrentDateString("yyyyMMdd")},new JSONMapperImpl());
            if (list != null) {
                for (JSONObject obj : list) {
                    agentWarn.put(obj.getString("AGENT_ID"),obj.getString("MSG_ID"));
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return agentWarn;
    }

    /**
     * 读取助手告警表，将告警数据写入告警表中
     */
    public void writeWarn(){
        String key = "writeWarn_isCache";
        try {

            String isCache = cache.get(key);
            if(StringUtils.isNotBlank(isCache)){
                logger.info("读取助手告警表任务没跑完，跨过此次");
                return;
            }
            cache.put(key, "writeWarn_isCache");
            //更新语句，更新通话结束且还未处理的告警
            String updateSql = "update "+busiName+".cx_agent_alarm_12345 t1  " +
                    "set IS_ALARM='2' where IS_ALARM='1' and t1.MSG_ID in(select call_ID from (select CALL_ID from "
                    +Constants.getAiaDb()+".aia_monitor_info  where END_TIME>='2025-07-11 01:04:00')t2) ";
            logger.info("[告警数据]"+updateSql+"[param]"+DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1));
            QueryFactory.getWriteQuery().execute(updateSql,new Object[]{
                    DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1)});

        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }finally {
            cache.delete(key);
        }

    }

    /**
     * 通过传入的告警类型和告警名称去读取告警表，并将告警记录写入
     * @param alarmType
     * @param alarmName
     */
    public void writeAlarmTypeWarn(String alarmType,String alarmName){
        String key = "writeAlarmTypeWarn_isCache_"+alarmType;
        try {

            String isCache = cache.get(key);
            if(StringUtils.isNotBlank(isCache)){
                logger.info("通过传入的告警类型和告警名称去读取告警表，并将告警记录写入"+alarmType+"_"+alarmName);
                return;
            }
            cache.put(key,"Y");
            // 先查询需要更新的记录ID
            EasySQL querySql = new EasySQL("SELECT t1.MSG_ID FROM " + busiName + ".cx_agent_alarm_12345 t1 ");
            querySql.append("JOIN " + Constants.getAiaDb() + ".aia_monitor_info t2 ON t1.MSG_ID = t2.CALL_ID ");
            querySql.append("WHERE t1.IS_ALARM='1' AND t2.END_TIME>=? AND t1.alarm_type=? and t1.DATE_ID>=? and t1.DATE_ID<=?");

            List<JSONObject> idsToUpdate = QueryFactory.getReadQuery().queryForList(
                    querySql.getSQL(),
                    new Object[]{
                            DateUtil.addDay("yyyy-MM-dd HH:mm:ss", EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1),
                            alarmType,DateUtil.addDay("yyyyMMdd",DateUtil.getCurrentDateStr("yyyyMMdd"), -1)
                            ,DateUtil.getCurrentDateStr("yyyyMMdd")
                    },
                    new JSONMapperImpl()
            );

            // 分批处理更新，每批100条
            if (idsToUpdate != null && !idsToUpdate.isEmpty()) {
                int batchSize = 100;
                for (int i = 0; i < idsToUpdate.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, idsToUpdate.size());
                    List<JSONObject> batch = idsToUpdate.subList(i, endIndex);

                    // 构建ID列表
                    StringBuilder idList = new StringBuilder();
                    for (JSONObject obj : batch) {
                        if (idList.length() > 0) {
                            idList.append(",");
                        }
                        idList.append("'").append(obj.getString("MSG_ID")).append("'");
                    }

                    // 执行批量更新
                    String updateSql = "UPDATE " + busiName + ".cx_agent_alarm_12345 SET IS_ALARM='2' " +
                            "WHERE IS_ALARM='1' AND MSG_ID IN (" + idList.toString() + ") AND alarm_type=?";

                    QueryFactory.getWriteQuery().execute(updateSql, new Object[]{alarmType});

                    logger.info("已更新" + batch.size() + "条告警记录，类型: " + alarmType);
                }
            }
            //更新语句，更新通话结束且还未处理的告警
//            String updateSql = "update "+busiName+".cx_agent_alarm_12345 t1  " +
//                    "set IS_ALARM='2' where IS_ALARM='1' and t1.MSG_ID in(select call_ID from (select CALL_ID from "
//                    +Constants.getAiaDb()+".aia_monitor_info  where END_TIME>=?)t2) and alarm_type=?";
//            // logger.info("[告警数据]"+updateSql+"[param]"+DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1));
//            QueryFactory.getWriteQuery().execute(updateSql,new Object[]{
//                    DateUtil.addDay("yyyy-MM-dd HH:mm:ss",EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1),alarmType});
            EasySQL sql = new EasySQL();
            // 只选择必要的字段，避免使用 t1.*
            sql.append("SELECT t1.CALL_ID, t1.AGENT_ID, t1.AGENT_PHONE, t2.NAME AS AGENT_NAME, t1." + alarmName);
            sql.append(" FROM " + Constants.getAiaDb() + ".aia_monitor_info t1");
            // 使用INNER JOIN替代LEFT JOIN，因为我们需要匹配的记录
            sql.append(" INNER JOIN " + busiName + ".tagentinfo t2 ON t1.AGENT_ID = t2.AGENTID");
            // 添加WHERE条件
            sql.append(" WHERE t1.END_TIME = '' AND t1." + alarmName + " > 0");
            sql.append(Constants.getHwCcId(), " AND t2.SUBCCNO = ?");
            sql.append(Constants.getHwVdnId(), " AND t2.VDN = ?");
            // 使用NOT EXISTS代替LEFT JOIN，效率更高
            sql.append(" AND NOT EXISTS (SELECT 1 FROM " + busiName + ".cx_agent_alarm_12345 t3");
            sql.append(alarmType," WHERE t3.MSG_ID = t1.CALL_ID AND t3.ALARM_TYPE = ?");
            sql.append( DateUtil.addHour("yyyy-MM-dd HH:mm:ss",
                    EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss"), -1)," AND t3.ALARM_TIME >= ?");
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:ss")," AND t3.ALARM_TIME <= ?");
            sql.append(")");

            //logger.info("[告警数据]"+sql.getSQL()+"[param]"+ JSON.toJSONString(sql.getParams()));
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            //更新坐席上次同一个类型的告警为不告警状态
            List<Object[]> updateAgentOldAlarm = new ArrayList<>();
            //新增超长通话告警
            List<Object[]> saveAgentAlarm = new ArrayList<>();

            if(list!=null && list.size()>0) {
                for (JSONObject json:list){
                    saveAlarm(alarmType,json.getString("AGENT_ID"),json.getString("AGENT_NAME")
                            ,json.getString("AGENT_PHONE"),json.getString("CALL_ID"),System.currentTimeMillis()
                            ,json.getIntValue(alarmName),updateAgentOldAlarm,saveAgentAlarm);//保存告警记录
                }
            }

            //更新之前产生告警，且现在状态变更了的告警状态为不告警
            if(updateAgentOldAlarm.size()>0){
                String updateSql = "update "+busiName+".cx_agent_alarm_12345 set IS_ALARM=2" +
                        " where ALARM_TYPE=? and ALARM_TIME>? and ALARM_TIME<? and IS_ALARM=1 and AGENT_ID=?";
                QueryFactory.getWriteQuery().executeBatch(updateSql,updateAgentOldAlarm);
            }
            //保存新增的告警数据
            if(saveAgentAlarm.size()>0){
                String saveSql = "insert into "+busiName+".cx_agent_alarm_12345(MSG_ID,ALARM_TYPE,ALARM_COUNT,DATE_ID,AGENT_ID,AGENT_PHONE," +
                        "AGENT_NAME,ALARM_TIME,AUDIT_LEVEN,IS_ALARM,MONTH_ID) " +
                        "values(?,?,?,?,?,?,?,?,?,?,?)";
                QueryFactory.getWriteQuery().executeBatch(saveSql,saveAgentAlarm);
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }finally {
            cache.delete(key);
        }
    }
    public void skilltrafficstat() {
        String skillId = "1";
        JSONArray skilltrafficstat = CtiUtils.skilltrafficstat(skillId);
        cache.put("skilltrafficstat:" + skillId, skilltrafficstat.toJSONString(), 300);
        try {
            String cacheKey = "skilltrafficstat:executed";
            // 检查是否已经执行过
            if (CacheUtil.exists(cacheKey)) {
                //logger.info("最近10分钟内已执行过 skilltrafficstat，跳过本次执行");
                return;
            }
            // 判断下当前时间，没10分钟执行下面逻辑
            if (skilltrafficstat != null && !skilltrafficstat.isEmpty()) {
                EasyQuery writeQuery = QueryFactory.getWriteQuery();
                EasyRecord record = new EasyRecord(busiName+".CX_CTI_CALL_DATA", "DATE_ID", "HOUR_ID");
                for (int i = 0; i < skilltrafficstat.size(); i++) {
                    JSONObject obj = skilltrafficstat.getJSONObject(i);
                    Long time = obj.getLong("time");
                    String dateStr = DateUtil.getDateStrByTimeStamp(time);
                    if (StringUtils.isNotBlank(dateStr)) {
                        String dateId = dateStr.substring(0, 10);
                        String hourId = dateStr.substring(11, 16);
                        String callRate = obj.getString("callRate");
                        record.set("DATE_ID", dateId);
                        record.set("HOUR_ID", hourId);
                        record.set("CALL_RATE", callRate);
                        if(!writeQuery.update(record)){
                            writeQuery.save( record);
                        }
                    }
                }
                // 设置执行标记，有效期为10分钟
                CacheUtil.put(cacheKey, "Y", 600); // 600秒 = 10分钟
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
