package com.yunqu.cc.monitordata.listener;


import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.enums.TaskEnum;
import com.yunqu.cc.monitordata.util.CacheUtil;
import com.yunqu.cc.monitordata.util.ThreadMgr;
import org.apache.log4j.Logger;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;


/**
 * 应用初始化 InitListener.java
 */
@WebListener
public class InitListener implements ServletContextListener {

    protected Logger logger = CommonLogger.logger;
    public static boolean runState = true;
    /**
     * 模块启动
     */
    @Override
    public void contextInitialized(ServletContextEvent arg0) {
        try {
            runState=true;
            logger.info("监听启动开始");
            if (Constants.isStartTask()) {
                TaskEnum.initTask();
            }
        } catch (Exception e) {
            logger.error("监听启动异常：" + e.getMessage(), e);
        }
    }


    @Override
    public void contextDestroyed(ServletContextEvent arg0) {
        try {
            runState= false;
            logger.info("监听关闭");
            CacheUtil.delete("queryAgentPlace_isCache");
            CacheUtil.delete("writeWarn_isCache");
            CacheUtil.delete("writeWarn_isCache");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_1");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_3");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_4");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_5");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_6");
            CacheUtil.delete("writeAlarmTypeWarn_isCache_7");
            CacheUtil.delete("updateMissingAgentIds_ISCACHE");
            ThreadMgr.getInstance().shutDown();
            TaskEnum.destroyedTask();
        } catch (Exception e) {
            logger.error("监听关闭异常：" + e.getMessage());
        }
    }

}