package com.yunqu.cc.monitordata.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.monitordata.base.CommonLogger;
import com.yunqu.cc.monitordata.base.Constants;
import com.yunqu.cc.monitordata.base.QueryFactory;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.log4j.Logger;
import org.easitline.common.core.Globals;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.io.*;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023-10-30 16:35
 */
public class CommonUtils {
    private static final EasyQuery readQuery = QueryFactory.getReadQuery();
    private static final EasyQuery writeQuery = QueryFactory.getWriteQuery();
    private static final EasyCache cache = CacheManager.getMemcache();
    private static final String busiName = Constants.getBusiName();
    private static final Logger logger = CommonLogger.getLogger("AgentMonitor");

    public static String getAgentCurrentState(int num) {
        // 当前状态 0：表示话务员未签入。1：表示话务员空闲。2：表示预占用状态。3：表示占用状态。4：表示应答状态。5：表示通话状态。6：表示工作状态。7：表示忙状态。8：表示请假休息。9：表示学习态。10：表示调整态。
        switch (num) {
            //
            case 0:
                return "未签入";
            case 1:
                return "空闲";
            case 2:
                return "预占";
            case 3:
                return "占用";
            case 4:
                return "应答";
            case 5:
                return "通话";
            case 6:
                return "工作";
            case 7:
                return "示忙";
            case 8:
                return "请假";
            case 9:
                return "学习";
            case 10:
                return "调整";
            default:
                return "";
        }
    }

    /**
     * 根据技能组名称对技能组分类
     *
     * @param skillName
     * @return
     */
    public static String getSkillType(String skillName) {
        //技能组 对应类型：01接话  02网络 03回访 04业务
        if (skillName.contains("回访")) {
            return "03";
        } else if (skillName.contains("网络")) {
            return "02";
        } else if (skillName.contains("培训") || skillName.contains("诉求") || skillName.contains("信息") || skillName.contains("业务") || skillName.contains("值班长") || skillName.contains("质检")) {
            return "04";
        } else {
            return "01";//未识别默认通话
        }
    }

    /**
     * 获取所有用户选择的技能队列id
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/2 10:32
     */
    public static String queryUserSelectSkillId() {
        String skillIds = "";
        try {
            // 用户选的技能队列id
//            skillIds = cache.get("agentmonitor_skillIds");
//            if (StringUtils.isBlank(skillIds)) {
            // 获取 用户配置的技能队列信息
            skillIds = readQuery.queryForString("select GROUP_CONCAT(distinct SKILL_ID) from " + busiName + ".cx_user_skill where SKILL_ID <> ''");
//            }
            if (StringUtils.isBlank(skillIds)) {
                // 18 是总签入席 技能队列id
                skillIds = "18";
            } else {
                skillIds += ",18";
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return skillIds;
    }

    /**
     * 获取所有用户选择的技能队列列表
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/2 10:32
     */
    public static String querySkillInfoList() {
        String skillGroupInfo = "";
        try {
            // 技能队列信息
            skillGroupInfo = cache.get("AgentMonitor:skillGroupInfo");
            if (StringUtils.isBlank(skillGroupInfo)) {
                EasySQL sql = new EasySQL();
                sql.append("select SKILLGROUPID, REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(SKILLGROUPNAME,'普通语音电话',''),'Chinese',''),'普通用户','') ,'医院挂号',''),'医院挂号网呼',''),',','') SKILLGROUPNAME from " + busiName + ".tskillgroup where 1 = 1");
                sql.append(Constants.getHwCcId(), " and SUBCCNO = ?");
                sql.append(Constants.getHwVdnId(), " and VDN = ?");
                List<Map<String, String>> list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
                skillGroupInfo = JSON.toJSONString(list);
                cache.put("AgentMonitor:skillGroupInfo", skillGroupInfo, 3600);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return skillGroupInfo;
    }

    /**
     * 获取技能组的初始队列
     * @return
     */
    public static  Map<String,JSONObject> getSkillInitData(){
        Map<String,JSONObject> skillTypeMap = cache.get("AgentMonitor:skillInitData");
        if(skillTypeMap==null){
            String skillGroupInfo = querySkillInfoList();
            Map<String, String> skillMap = new HashMap<>();
            if (StringUtils.isNotBlank(skillGroupInfo)) {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                List<Map<String, String>> list = JSON.parseObject(skillGroupInfo, new TypeReference<List<Map<String, String>>>() {});
                Map<String, JSONObject> finalSkillTypeMap = new HashMap<>();
                list.forEach(t -> {
                    JSONObject json = new JSONObject();
                    json.put("dataName", t.get("SKILLGROUPNAME"));
                    json.put("agentCount", 0);
                    finalSkillTypeMap.put(t.get("SKILLGROUPID"), json);
                });
                cache.put("AgentMonitor:skillInitData", finalSkillTypeMap, 3600);
                return finalSkillTypeMap;
            }
        }
        return skillTypeMap;
    }


    /**
     * 获取所有用户选择的技能队列列表
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/2 10:32
     */
    public static String querySkillGroupNameById(String skillGroupid) {
        String skillGroupName = "";
        try {
            if (StringUtils.isNotBlank(skillGroupid)) {
                EasySQL sql = new EasySQL();
                sql.append("select GROUP_CONCAT(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(SKILLGROUPNAME,'普通语音电话',''),'Chinese',''),'普通用户','') ,'医院挂号',''),'医院挂号网呼',''),',','')) SKILLGROUPNAME from " + busiName + ".tskillgroup where 1 = 1");
                sql.append(Constants.getHwCcId(), " and SUBCCNO = ?");
                sql.append(Constants.getHwVdnId(), " and VDN = ?");
                sql.appendIn(skillGroupid.split(";"), " and SKILLGROUPID");
                skillGroupName = readQuery.queryForString(sql.getSQL(), sql.getParams());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return skillGroupName;
    }

    /**
     * 获取所有班组列表
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/2 10:32
     */
    public static String queryWorkGroupInfoList() {
        String workGroupInfo = "";
        try {
            // 班组信息
            workGroupInfo = cache.get("AgentMonitor:workGroupInfo");
            if (StringUtils.isBlank(workGroupInfo)) {
                EasySQL sql = new EasySQL();
                sql.append("select WORKGROUPID,WORKGROUP from ");
                sql.append(Constants.getBusiName() + ".tworkgroup where 1 = 1");
                sql.append(Constants.getHwCcId(), " and SUBCCNO = ?");
                sql.append(Constants.getHwVdnId(), " and VDN = ?");
                List<Map<String, String>> list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
                workGroupInfo = JSON.toJSONString(list);
                cache.put("AgentMonitor:workGroupInfo", workGroupInfo, 3600);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return workGroupInfo;
    }

    public static int countCallWaitNums(String skills){
        //logger.info("countCallWaitNums:skills:"+skills);
        String key = "AgentMonitor:callWaitNums"+skills;
        int callWaitNums = 0;
        String callWaitNumsStr = cache.get(key);
        if(StringUtils.isBlank(callWaitNumsStr)){
            JSONArray skilltrafficstat = CtiUtils.skilltrafficstat(skills);
            for(int i=0;i<skilltrafficstat.size();i++){
                JSONObject json = skilltrafficstat.getJSONObject(i);
                callWaitNums += json.getIntValue("callWaitNums");
            }
            cache.put(key,callWaitNums+"",5);//缓存保存5s
        }else {
            callWaitNums = StringUtils.parseInt(callWaitNumsStr);
        }
        return callWaitNums;
    }

    /**
     * 通过用户id和角色id获取班组初始化信息
     * @param userId
     * @param roleType
     * @return
     */
    public static Map<String ,JSONObject> getWorkGroupInitData(String userId,int roleType) {
        String key = "AgentMonitor:workGroupInfo"+userId;
        Map<String, JSONObject> workGroupMap = cache.get("AgentMonitor:workGroupInfo"+userId);
        if (workGroupMap == null) {

            Map<String, String> workgroupMap = new HashMap<>();
            // 班组信息
            String workGroupInfo = queryWorkGroupInfoList();
            List<Map<String, String>> list = new ArrayList<>();
            if (StringUtils.isNotBlank(workGroupInfo)) {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                list = JSON.parseObject(workGroupInfo, new TypeReference<List<Map<String, String>>>() {});
                workgroupMap = list.stream().collect(Collectors.toMap(t -> t.get("WORKGROUPID"), t -> t.get("WORKGROUP"), (o, n) -> n, HashMap::new));
            }
            Map<String, JSONObject> finalWorkGroupMap = new HashMap<>();
            if(roleType==2){
                String workGroupId = getUserWorkGroupId(userId);
                JSONObject json = new JSONObject();
                json.put("dataName",workgroupMap.get(workGroupId));
                json.put("agentCount",0);
                finalWorkGroupMap.put(workGroupId,json);
            }else{
                list.forEach(t -> {
                    JSONObject json = new JSONObject();
                    json.put("dataName", t.get("WORKGROUP"));
                    json.put("agentCount", 0);
                    finalWorkGroupMap.put(t.get("WORKGROUPID"), json);
                });
            }
            cache.put(key, finalWorkGroupMap, 3600);
            return finalWorkGroupMap;
        }
        return workGroupMap;
    }

    /**
     * 获取话房所有话机号码
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/2 10:32
     */
    public static List<Map<String, String>> queryHfIpList(String hfCode) {
        List<Map<String, String>> list = new ArrayList<>();
        try {
            String sql = "select PHONE_NUMBER,SEAT_IP,SEAT_NO,HORIZONTAL,LONGITUDINAL,IS_NULL from " + busiName + ".cx_12345_phone where ROOM_LOCATION = ? order by CAST(HORIZONTAL AS SIGNED),CAST(LONGITUDINAL AS SIGNED)";
            list = readQuery.queryForList(sql, new Object[]{hfCode}, new MapRowMapperImpl());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return list;
    }

    /**
     * 获取指定VDN下的签入座席所带电话号码
     * @param agentId
     * @return
     */
    public static  String getCitAgentPhone(String agentId){
        String key = "AgentMonitor:getCitAgentPhone";
        Map<String, String> ctiAgentPhone = cache.get(key);
        if(ctiAgentPhone==null){
            // 查询指定VDN下的签入座席所带电话号码
            List<JSONObject> ctiAgentPhones = CtiUtils.queryallagentphones().toJavaList(JSONObject.class);
            if(ctiAgentPhones!=null && ctiAgentPhones.size()>0){
                ctiAgentPhone = ctiAgentPhones.stream()
                        .collect(Collectors.toMap(
                                jsonObject -> jsonObject.getString("agentId"),  // 使用 agentId 作为键
                                jsonObject -> jsonObject.getString("phone")  // 使用 phone 作为值
                        ));
            }else {
                ctiAgentPhone = new HashMap<>();
            }
            cache.put(key,ctiAgentPhone,5*30);//缓存保存5分钟
        }

        return ctiAgentPhone.get(agentId);
    }
    /**
     * 根据话机号获取话机详情
     *
     * @return java.lang.String
     */
    public static JSONObject queryPhoneById(String phone) {
        String key = "AgentMonitor:queryPhoneById:"+phone;
        JSONObject result = cache.get(key);

        try {
            if(result==null){
                String sql = "select PHONE_NUMBER,ROOM_LOCATION,SEAT_NO from " + busiName + ".cx_12345_phone where PHONE_NUMBER = ? ";
                result = readQuery.queryForRow(sql, new Object[]{phone}, new JSONMapperImpl());
                cache.put(key,result,5*60);//缓存保存5分钟
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    // 查询字典编码
    public static List<Map<String, String>> getDict(String dictGroupCode) {
        List<Map<String, String>> list = new ArrayList<>();
        try {
            EasySQL sql = new EasySQL(" SELECT t1.CODE,t1.NAME  FROM " + busiName + ".C_CF_DICT t1 left join " + busiName + ".C_CF_DICTGROUP t2 on t1.DICT_GROUP_ID=t2.ID WHERE 1=1 ");
            sql.append(dictGroupCode, " AND t2.CODE = ?");
            list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
        } catch (SQLException e) {
            logger.error("查询字典编码异常", e);
        }
        return list;
    }

    /**
     * 查询坐席id 名字 技能队列id 班组id 班组名称
     */
    public static List<Map<String, String>> queryAgentInfo() {
        List<Map<String, String>> list = new ArrayList<>();
        try {
            String agentInfoStr = cache.get("AgentMonitor:AgentInfoList");
            if (StringUtils.isNotBlank(agentInfoStr)) {
                list = JSON.parseObject(agentInfoStr, new TypeReference<List<Map<String, String>>>() {
                });
            } else {
                String sql = "select t1.AGENTID,t1.NAME,t1.AGENTSKILLS,t1.WORKGROUPID,t2.WORKGROUP from " + busiName + ".tagentinfo t1 left join " + busiName + ".tworkgroup t2 on t1.WORKGROUPID = t2.WORKGROUPID and t2.VDN = ? and t2.SUBCCNO = ? where t1.VDN = ? and t1.SUBCCNO = ?";
                readQuery.setMaxRow(20000);
                list = readQuery.queryForList(sql, new Object[]{Constants.getHwVdnId(), Constants.getHwCcId(), Constants.getHwVdnId(), Constants.getHwCcId()}, new MapRowMapperImpl());
                cache.put("AgentMonitor:AgentInfoList", JSON.toJSONString(list), 3600);
            }
        } catch (SQLException e) {
            logger.error("查询字典编码异常", e);
        }
        return list;
    }

    /**
     * 获取坐席业务数据，如姓名、性别、年龄、入职年限、角色
     * @param userId
     * @return
     */
    public static JSONObject getUserHwInfo(String userId) {
        String key = "AgentMonitor:hwAgentInfo:"+userId;
        String userHwInfoStr = cache.get(key);
        JSONObject userHwInfo = null;
        try {
            if (StringUtils.isBlank(userHwInfoStr)) {
                EasySQL sql = new EasySQL("select t2.USER_ID,t2.IMG_URL,t1.AGENTID,t1.`NAME`,t2.MOBILE,t1.AGENTWORKGROUP,t3.EXT_CONF,t2.SEX,t2.BIRTH_DAY from ");
                sql.append(busiName+".tagentinfo t1 inner join ");
                sql.append("cc_user t2 on t1.AGENTID=t2.SSO_ACCT inner join ");
                sql.append(busiName+".cc_busi_user t3 on t2.USER_ID=t3.USER_ID where 1=1 ");
                sql.append(userId,"and t1.AGENTID = ?");
                userHwInfo = readQuery.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if(userHwInfo==null){
                    userHwInfo = new JSONObject();
                }
                JSONObject agentInfo = new JSONObject();
                agentInfo.put("AGENT_ID",userId);
                agentInfo.put("AGENT_NAME",userHwInfo.getString("NAME"));
                agentInfo.put("SEX","0".equals(userHwInfo.getString("SEX"))?"女":"男");
                if (StringUtils.isNotBlank(userHwInfo.getString("BRITH_DATE"))) {
                    agentInfo.put("BRITH_DATE", DateUtil.bwDays(userHwInfo.getString("BRITH_DATE"), DateUtil.getCurrentDateStr("yyyy-MM-dd"),
                            "yyyy-MM-dd")/365);
                }else {
                    agentInfo.put("BRITH_DATE",0);
                }
                JSONObject extJson = userHwInfo.getJSONObject("EXT_CONF");
                if(extJson!=null){
                    if(StringUtils.isNotBlank(extJson.getString("ENTRY_DATE"))){
                        agentInfo.put("ENTRY_DATE",DateUtil.bwDays(extJson.getString("ENTRY_DATE"), DateUtil.getCurrentDateStr("yyyy-MM-dd"),
                                "yyyy-MM-dd")/365);
                    }else {
                        agentInfo.put("ENTRY_DATE",0);
                    }
                    agentInfo.put("ROLE",extJson.getString("ROLE_NAME"));
                }else{
                    agentInfo.put("ENTRY_DATE",0);
                    agentInfo.put("ROLE","坐席员");
                }

                cache.put(key, agentInfo.toJSONString());
            }else {
                userHwInfo = JSON.parseObject(userHwInfoStr);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return userHwInfo;
    }

//    public static Map<String, List<JSONObject>> getAgentWarn(YCUserPrincipal ycuser) {
//        Map<String, List<JSONObject>> result = new HashMap<>();
//        try {
//            String warnTime = Constants.getQueryWarnTime();
//            String dateStr = DateUtil.addMinute("yyyy-MM-dd HH:mm:ss", DateUtil.getCurrentDateStr(), -Integer.parseInt(warnTime));
//            EasySQL sql = new EasySQL();
//            sql.append("select * from (");
//            sql.append("select AGENT_PHONE,TYPE,REMIND_TYPE WARN_TYPE,max(MSG_ID) MSG_ID,max(REMIND_TIME) REMIND_TIME,DISTRIBUTE_AGENT_ID from ");
//            sql.append(busiName + ".cx_agent_remind_table");
//            sql.append(" where 1=1");
//            sql.append(" and IS_READ != '1' and TYPE = '0'");
//            sql.append(DateUtil.getCurrentDateStr("yyyy-MM-dd"), " and REMIND_DATE=? ");
//            sql.append(dateStr, "and REMIND_TIME >= ?");
//            try {
//                if(ycuser!=null && !ycuser.isAdmin() && ycuser.getRoleType()!=1) {//如果角色不为管理员只能看到派发给自己的告警信息
//                    sql.append(ycuser.getUserId(), "and DISTRIBUTE_AGENT_ID = ?");
//                }else {
//                    //sql.append( "and DISTRIBUTE_AGENT_ID is null");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                logger.error(e.getMessage(),e);
//            }
//
//            sql.append(" group by AGENT_PHONE,TYPE,REMIND_TYPE,DISTRIBUTE_AGENT_ID");
//            sql.append(" ORDER BY REMIND_TIME DESC) t1");
//
//            sql.append(" UNION ALL");
//            sql.append(" select * from (");
//            sql.append("select AGENT_PHONE,TYPE,RESORT_TYPE WARN_TYPE,max(MSG_ID) MSG_ID,max(REMIND_TIME) REMIND_TIME,DISTRIBUTE_AGENT_ID from ");
//            sql.append(busiName + ".cx_agent_remind_table");
//            sql.append(" where 1=1");
//            sql.append(" and IS_READ != '1' and TYPE = '1' and RESORT_TYPE = '2'");
//            sql.append(DateUtil.getCurrentDateStr("yyyy-MM-dd"), " and REMIND_DATE=? ");
//            sql.append(dateStr, "and REMIND_TIME >= ?");
//            try {
//                if(ycuser!=null && !ycuser.isAdmin() && ycuser.getRoleType()!=1) {//如果角色不为管理员只能看到派发给自己的告警信息
//                    sql.append(ycuser.getUserId(), "and DISTRIBUTE_AGENT_ID = ?");
//                }else {
//                    //sql.append( "and DISTRIBUTE_AGENT_ID is null");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                logger.error(e.getMessage(),e);
//            }
//            sql.append(" group by AGENT_PHONE,TYPE,RESORT_TYPE,DISTRIBUTE_AGENT_ID");
//
//            sql.append(" ORDER BY REMIND_TIME DESC) t2");
//            //if(ycuser!=null)
//            //logger.info(sql.getSQL()+"[param]"+JSON.toJSONString(sql.getParams()));
//            List<JSONObject> agentIps = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
//            if (agentIps != null && agentIps.size() > 0) {
//                //然后返回一个map集合，key代表组名，value代表该组中的数据
//                result = agentIps.stream().collect(Collectors.groupingBy(x -> x.getString("AGENT_PHONE")));
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error(e.getMessage(),e);
//        }
//        return result;
//    }

    public static JSONObject getAgentWarnByMsgId(String msgId) {
        JSONObject result = new JSONObject();
        try {
            EasySQL sql = new EasySQL(" select * from " + busiName + ".cx_agent_remind_table");
            sql.append(" where 1=1");
            sql.append(msgId, " and MSG_ID =? ");
            result = readQuery.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e);
        }
        return result;
    }

    public static List<JSONObject> getAgentWarnByMsgIds(String msgIds) {

        List<JSONObject> result = new ArrayList<>();
        try {
            EasySQL sql = new EasySQL(" select MSG_ID,");
            sql.append(" case when  TYPE ='0' and REMIND_TYPE = '0' then '超长通话'");
            sql.append(" when  TYPE ='0' and REMIND_TYPE = '1' then '语速超快'");
            sql.append(" when  TYPE ='0' and REMIND_TYPE = '2' then '话后超时'");
            sql.append(" when  TYPE ='1' and RESORT_TYPE = '1' then '取消求助'");
            sql.append(" when  TYPE ='1' and RESORT_TYPE = '2' then '发起求助'");
            sql.append(" else ''");
            sql.append(" end WARN_NAME,case when  TYPE ='0' then REMIND_TYPE else 3 end WARN_TYPE ");
            sql.append(" from " + busiName + ".cx_agent_remind_table");
            sql.append(" where 1=1");
            sql.append("1", " and IS_READ != ? ");
            sql.appendIn(msgIds.split(","), " and MSG_ID ");

            result = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e);
        }
        return result;
    }

    public static boolean updateAgentWarn(String msgId,String alarmType,String msgTemp,YCUserPrincipal ycuser) {
        try {
            if(StringUtils.isNotBlank(msgId)) {
                JSONObject agentRemind = readQuery.queryForRow("select TYPE,REMIND_TYPE,RESORT_TYPE,REMIND_TIME,AGENT_PHONE from "
                        + busiName + ".cx_agent_remind_table where MSG_ID = ?", new Object[]{msgId}, new JSONMapperImpl());//从求助表中查询出来详情
                // 0告警1是求助
                String type = agentRemind.getString("TYPE");
                // 1 取消求助 2 发起求助 3 告警
                String resortType = agentRemind.getString("RESORT_TYPE");
                // 告警时间
                String remindTime = agentRemind.getString("REMIND_TIME");
                String agentPhone = agentRemind.getString("AGENT_PHONE");
                if("1".equals(type) && "2".equals(resortType)){
                    writeQuery.execute("update " + busiName + ".cx_agent_remind_table set IS_READ = '1' where TYPE = ? and RESORT_TYPE = ? " +
                            "and REMIND_DATE = ?  and AGENT_PHONE = ?"
                            ,type,resortType,remindTime.substring(0,10),agentPhone);//更新求助表
                }
                writeQuery.execute("update " + busiName + ".cx_agent_alarm_12345 set IS_ALARM = '2',HANDLE_STATUS='1',HANDLE_USER=?,HANDLE_USER_ACCT=?" +
                        ",HANDLE_TIME=?,HANDLE_TEMP=? where MSG_ID = ? and ALARM_TYPE = ? ",new Object[]{
                        ycuser.getUserId(),ycuser.getLoginAcct(),EasyDate.getCurrentDateString(),msgTemp,msgId,alarmType
                });//更新告警表
            }else {
                if("8".equals(alarmType)) {//类型8是求助
                    writeQuery.execute("update " + busiName + ".cx_agent_remind_table set IS_READ = '1' where TYPE = ? ",1);//更新求助表
                }
                writeQuery.execute("update " + busiName + ".cx_agent_alarm_12345 set IS_ALARM = '2',HANDLE_STATUS='1',HANDLE_USER=?,HANDLE_USER_ACCT=?" +
                        ",HANDLE_TIME=?,HANDLE_TEMP=? where ALARM_TYPE = ? ",new Object[]{
                        ycuser.getUserId(),ycuser.getLoginAcct(),EasyDate.getCurrentDateString(),msgTemp,alarmType
                });//更新告警表
            }
            RestUtil.sendLight(msgId,"0");//告警灯
            return true;
        } catch (Exception e) {
            logger.error(e);
            return false;
        }
    }

    public static boolean transferWarn(String msgId,String agentId,String agentAcc) {//转移求助
        try {
            //String agentAcc = readQuery.queryForString("select USER_ACCT from "+busiName+".cc_busi_user where USER_ID=?", new Object[] {agentId});
            //更新告警记录表中的派发人
            writeQuery.execute("update " + busiName +".cx_agent_remind_table set DISTRIBUTE_COUNT=DISTRIBUTE_COUNT+1,DISTRIBUTE_TIME=? " +
                            ", DISTRIBUTE_AGENT_ID=? ,DISTRIBUTE_AGENT_ACC=? where MSG_ID=?"
                    ,new Object[] {EasyDate.getCurrentDateString(),agentId,agentAcc,msgId});
            //在派发记录表中记录派发人
            writeQuery.execute("insert " + busiName +".cx_agent_help_distribute_table(DISTRIBUTE_ID,MSG_ID,DISTRIBUTE_TIME,DISTRIBUTE_AGENT_ID" +
                            ",DISTRIBUTE_AGENT_ACC) values(?,?,?,?,?)"
                    ,new Object[] {RandomKit.randomStr(),msgId,EasyDate.getCurrentDateString(),agentId,agentAcc});
            RestUtil.sendLight(msgId,"1");
            return true;
        } catch (Exception e) {
            logger.error(e);
            return false;
        }
    }
    /**
     *
     * @param sendAgent 1有模版 0没有模版
     * @param msgTemp
     * @param msgType
     * @param entId
     * @param agentList
     */
    public static void sendAgent(String sendAgent,String msgTemp,String msgType,String entId,List<JSONObject> agentList) {
        String entStrs = CacheUtil.get("AIA_MSG_MODEL_LIST");
        if(StringUtils.isBlank(entStrs)) {
            logger.info("获取消息模版缓存为空1，请检查缓存。");
            return;
        }
        JSONObject entLists = JSONObject.parseObject(entStrs);
        if(entLists == null) {
            logger.info("获取消息模版缓存为空，请检查缓存。");
            return;
        }
        JSONObject entJson = entLists.getJSONObject(entId);
        if(entJson == null) {
            logger.info("获取"+entId+"企业缓存为空，请检查缓存。");
            return;
        }

        JSONArray array = entJson.getJSONArray(msgType);
        if(array == null) {
            logger.info("获取"+entId+"企业"+msgType+"类型缓存为空，请检查缓存。");
            return;
        }
        JSONObject tempJson = null;
        for (int i = 0; i < array.size(); i++) {
            tempJson = array.getJSONObject(i);
            if(msgTemp.equals(tempJson.getString("MODEL_ID"))) {
                break;
            } else {
                tempJson = null;
            }
        }
        if("3".equals(msgType) && tempJson==null) {//如果为求助类型
            tempJson = new JSONObject();
            logger.info("获取"+entId+"企业"+msgType+"类型"+msgTemp+"模版为空，请检查缓存。");
        }else if(tempJson==null) {
            //tempJson = new JSONObject();
            logger.info("获取"+entId+"企业"+msgType+"类型"+msgTemp+"模版为空，请检查缓存。");
            return;
        }
        JSONObject json = new JSONObject();
        json.put("msgId", RandomKit.randomStr());
        logger.info("[msgTemp]"+msgTemp+"[sendAgent]"+sendAgent);
        if(StringUtils.isNotBlank(msgTemp)) {//通知坐席的情况下才有
            json.put("msgName", tempJson.getString("MODEL_NAME"));
            json.put("msgSub", tempJson.getString("MODEL_SUB"));
            json.put("msgContent", tempJson.getString("MODEL_CONTENT"));
        }

        json.put("msgRemindType", msgType);//消息提醒类型，0话后超时，1超长通话，2超快语速，3求助，4其它，5转派
        json.put("sendType", "0");//消息发送类型，0：立即发送1：定时发送
        if("1".equals(sendAgent)) {//（用于新增求助处理完成改变坐席求助状态）消息是否通知提示坐席，0否 1是，默认1。当为0时，消息模板可为空
            json.put("notifyFlag", "1");
        }else{
            json.put("notifyFlag", "0");
        }
        json.put("sendSource", "0");
        json.put("corpKey", entId);//企业id
        json.put("robotId", "assis_1");//机器人id
        JSONArray agentInfoList = new JSONArray();
        JSONObject agentInfo = null;
        for (JSONObject agent : agentList) {
            agentInfo = new JSONObject();
            agentInfo.put("agentNo", agent.getString("agentId"));
            agentInfo.put("agentName", agent.getString("agentName"));
            agentInfo.put("agentSeat", StringUtils.isBlank(agent.getString("seatNo"))?agent.getString("agentName"):agent.getString("seatNo"));
            agentInfo.put("agentGroup", agent.getString("agentGroup"));
            agentInfo.put("agentPhone", agent.getString("agentPhone"));
            agentInfoList.add(agentInfo);
        }
        json.put("agentInfoList", agentInfoList);
        logger.info("sendAgent->"+json.toJSONString());
        HttpResp resp = HttpUtil.sendPost(Constants.getWarnAddr()+"/aiamgr/msg/sendToAgent.do", json.toJSONString(),HttpUtil.TYPE_JSON);
        if(resp!=null) {
            logger.info("CODE:"+resp.getCode()+" Result:"+resp.getResult());
        }

    }


    /**
     * 查询话房信息
     *
     * <AUTHOR>
     * @date 2024/3/5 17:51
     */
    public static List<JSONObject> queryPhoneRoom() {
        List<JSONObject> list = new ArrayList<>();
        try {
            // 话房数据
            String phoneRoom = cache.get("AgentMonitor:phoneRoom");
            if (StringUtils.isBlank(phoneRoom)) {
                EasySQL sql = new EasySQL(" SELECT t1.CODE,t1.NAME  FROM " + busiName + ".C_CF_DICT t1 left join " + busiName + ".C_CF_DICTGROUP t2 on t1.DICT_GROUP_ID=t2.ID WHERE t2.CODE = 'AgentMonitor_HF'");
                list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                cache.put("AgentMonitor:phoneRoom", JSON.toJSONString(list), 3600);
            } else {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                list = JSON.parseObject(phoneRoom, new TypeReference<List<JSONObject>>() {
                });
            }
        } catch (Exception e) {
            logger.error("查询话房异常" + e.getMessage(), e);
        }
        return list;
    }

    /**
     * 查询派发坐席信息
     *
     */
    public static List<JSONObject> queryDistributeUserAcc(String msgId) {
        List<JSONObject> list = new ArrayList<>();
        String updateSql = "update cc_user t1 set t1.LOGOUT_TIME=(select LOGOUT_TIME from cc_login_log t2 where t1.user_ACCT=t2.user_acct order by LOGIN_TIME desc limit 1) where (t1.LOGOUT_TIME='' or LOGOUT_TIME is null)  and LOGIN_TIME is not null";
        try {
            writeQuery.execute(updateSql);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        try {
            EasySQL sql = new EasySQL("select t1.USER_ID,concat(t1.USERNAME,'-',t1.USER_ACCT) AGENT_NAME from cc_user t1 inner join "+busiName+".cc_skill_group_user");
            sql.append(" t2 on t1.user_id=t2.user_id inner join ");
            sql.append("(select t2.SKILL_GROUP_ID from (SELECT * from "+busiName+".tagentInfo  t1 ");
            sql.append("inner join "+busiName+".cx_agent_remind_table t2 on t1.AGENTID=t2.AGENT_NO where 1=1 ");
            sql.append(msgId," and t2.MSG_ID=?");
            sql.append(Constants.getHwVdnId()," and VDN=?");
            sql.append(Constants.getHwCcId()," and SUBCCNO=?");
            sql.append(" limit 1) t1 ");
            sql.append("inner join "+busiName+".cc_skill_group t2 on t1.AGENTWORKGROUP=t2.SKILL_GROUP_NAME ");
            sql.append("and t2.SKILL_GROUP_TYPE='struct') t3 on t2.SKILL_GROUP_ID=t3.SKILL_GROUP_ID");
            //sql.append("  inner join "+busiName+".cx_help_ip_list t5 on t1.LAST_LOGIN_IP=t5.HELP_IP ");
            sql.append("where (t1.LOGOUT_TIME='' or LOGOUT_TIME is null) and LOGIN_TIME is not null");//查询已登录 未登出的坐席
            list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (Exception e) {
            logger.error("查询派发坐席信息异常" + e.getMessage(), e);
        }
        return list;
    }

    /**
     * 查询派发坐席信息
     *
     */
    public static List<JSONObject> queryDistributeUserAcc(YCUserPrincipal ycuser) {
        List<JSONObject> list = new ArrayList<>();
        try {
            String userAcc = "";
            if(ycuser!=null) {
                userAcc = ycuser.getLoginAcct();
            }
            // 话房数据
            String userAccts = cache.get("AgentMonitor:distributeUserAcc"+userAcc);
            if (StringUtils.isBlank(userAccts) || "[]".equals(userAccts)) {

                EasySQL sql = new EasySQL(" SELECT * FROM " + busiName + ".cx_mix_phone_room where user_acct='"+userAcc+"'");
                JSONObject phoneRecord = readQuery.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if(phoneRecord==null || StringUtils.isBlank(phoneRecord.getString("PHONE_ROOM_ID"))) {
                    sql = new EasySQL("SELECT DISTINCT  t3.USER_ID,concat(t3.AGENT_NAME,'-',t3.USER_ACCT) AGENT_NAME FROM "+busiName+".cc_role t1 inner join "+busiName
                            +".cc_role_user t2 on t1.ROLE_ID=t2.ROLE_ID inner join "+busiName
                            +".cc_busi_user t3 on t2.USER_ID=t3.USER_ID where t1.ROLE_TYPE=2 and t3.USER_STATE=0");
                    list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                } else {
                    String roomId = phoneRecord.getString("PHONE_ROOM_ID");
                    String groupName = "";
                    if("1".equals(roomId)) {
                        groupName = "9楼.培训教室";
                    }else if("2".equals(roomId)) {
                        groupName = "9楼.东话房";
                    }else if("3".equals(roomId)) {
                        groupName = "9楼.中话房";
                    }else if("4".equals(roomId)) {
                        groupName = "9楼.西话房";
                    }else if("5".equals(roomId)) {
                        groupName = "8楼.企业话房";
                    }else if("6".equals(roomId)) {
                        groupName = "8楼.回访话房";
                    }else if("7".equals(roomId)) {
                        groupName = "8楼.辅助话房";
                    }
                    sql = new EasySQL("SELECT DISTINCT t3.USER_ID,concat(t3.AGENT_NAME,'-',t3.USER_ACCT) AGENT_NAME FROM "+busiName+".cc_role t1 inner join "+busiName
                            +".cc_role_user t2 on t1.ROLE_ID=t2.ROLE_ID inner join "+busiName
                            +".cc_busi_user t3 on t2.USER_ID=t3.USER_ID where t1.ROLE_NAME like '%"+groupName+"%'");
                    list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                }
                cache.put("AgentMonitor:distributeUserAcc"+userAcc, JSON.toJSONString(list), 3600);
            } else {
                // 使用 Fastjson 解析 JSON 字符串为 List<Map>
                list = JSON.parseObject(userAccts, new TypeReference<List<JSONObject>>() {
                });
            }
        } catch (Exception e) {
            logger.error("查询派发坐席信息异常" + e.getMessage(), e);
        }
        return list;
    }

    //把一个文件中的内容读取成一个String字符串
    public static String getFileToStr(String path) {

        // 获取当前类的所在路径
        String classPath = Globals.BASE_DIR + File.separator + "webapps" + File.separator + Constants.APP_NAME;

        File file = new File(classPath + path);
        String jsonStr = "";
        try {
            FileReader fileReader = new FileReader(file);
            Reader reader = new InputStreamReader(new FileInputStream(file), "utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            fileReader.close();
            reader.close();
            jsonStr = sb.toString();
            return jsonStr;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前用户是否为班长，如果是的话则返回所在班组的ID
     * @param userId
     * @return
     */
    public static String getUserWorkGroupId(String userId){
        String key = "AgentMonitor:userWorkGroupId"+userId;
        String workGroupId = cache.get(key);
        if(StringUtils.isBlank(workGroupId)){
            try {
                EasySQL sql = new EasySQL("SELECT t3.WORKGROUPID FROM "+busiName+".cc_skill_group_user t1 inner join "
                        +busiName+".cc_skill_group t2 on t1.SKILL_GROUP_ID=t2.SKILL_GROUP_ID inner join "
                        +busiName+".tworkgroup t3 on t2.SKILL_GROUP_NAME=t3.WORKGROUP where t2.SKILL_GROUP_TYPE='struct' and t1.IS_LEADER='Y' ");
                sql.append(Constants.getHwVdnId()," and t3.VDN=?");
                sql.append(Constants.getHwCcId()," and t3.SUBCCNO=?");
                sql.append(userId,"and t1.USER_ID=?");
                workGroupId = readQuery.queryForString(sql.getSQL(), sql.getParams());
                cache.put(key,workGroupId,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return workGroupId;
    }
    /**
     * 获取当前用户班组名
     * @param workGroupId
     * @return
     */
    public static String getWorkGroupName(String workGroupId){
        String key = "AgentMonitor:workGroupName"+workGroupId;
        String workGroupName = cache.get(key);
        if(StringUtils.isBlank(workGroupName)){
            try {
                EasySQL sql = new EasySQL("SELECT WORKGROUP FROM "+busiName+".tworkgroup where 1=1 ");
                sql.append(Constants.getHwVdnId()," and VDN=?");
                sql.append(Constants.getHwCcId()," and SUBCCNO=?");
                sql.append(workGroupId,"and WORKGROUPID=?");
                workGroupName = readQuery.queryForString(sql.getSQL(), sql.getParams());
                cache.put(key,workGroupName,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return workGroupName;
    }
    /**
     * 获取当前坐席的告警记录
     * @param userId
     * @return
     */
    public static JSONObject getUserAlarm(String userId){
        String key = "AgentMonitor:getUserAlarm_"+userId;
        JSONObject userAlarm = cache.get(key);
        if(userAlarm!=null){
            try {
                EasySQL sql = new EasySQL("SELECT t1.* FROM "+busiName+".tbill_agent_workgroup_stat_rpt t1  where 1=1  ");
                sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and t1.DAY=?");
                sql.append(userId," and t1.AGENTID=?");
                userAlarm = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(userAlarm==null){
                    userAlarm = new JSONObject();
                }
                cache.put(key,userAlarm,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return userAlarm;
    }

    /**
     * 获取当前坐席的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
     * @param userId
     * @param statType 1日 2周 3月
     * @return
     */
    public static JSONObject getUserCallData(String userId,String statType){
        String key = "AgentMonitor:getUserCallData_"+userId+"_"+statType;
        JSONObject userCallData = cache.get(key);
        String startDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        String endDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        if("2".equals(statType)){
            startDate = DateUtil.addDay("yyyy-MM-dd",endDate,-6);//获取七天前日期
        }else if("3".equals(statType)){
            startDate = DateUtil.addDay("yyyy-MM-dd",endDate,-29);//获取三十天前日期
        }
        if(userCallData==null){
            try {
                EasySQL sql = new EasySQL("SELECT");
                sql.append("t1.*");
                //只有按天的才需要查询挂机满意度
                if("1".equals(statType)){
                    sql.append(",t2.GOOD_PERCENT ");//挂机满意度
                }
                sql.append("FROM");
                sql.append("(");
                sql.append("SELECT");
                sql.append("AGENTID,");//坐席id
                sql.append("CONVERT(sum( CALL_IN_COUNT_ALL ),UNSIGNED) CALL_IN_COUNT_ALL,");//当天接听量
                sql.append("CONVERT(round( sum( CALL_IN_TIME_ALL ) / sum( CALL_IN_COUNT_ALL ), 0 ),UNSIGNED) AVG_CALL_IN_TIME,");//平均通话时长
                sql.append("CONVERT(round( sum( ARRANGE_TIME ) / sum( ARRANGE_COUNT ), 0 ),UNSIGNED) AVG_ARRANGE_TIME,");//平均话后时长
                sql.append("CONVERT(sum( LOGIN_TIME ),UNSIGNED) LOGIN_TIME,");//签入总时长
                sql.append("CONVERT(sum( CALL_IN_TIME_ALL ),UNSIGNED) CALL_IN_TIME_ALL,");//接听总时长
                sql.append("CONVERT(sum( ARRANGE_TIME ),UNSIGNED) ARRANGE_TIME,");//话后总时长
                sql.append("CONVERT(sum( BUSY_TIME ),UNSIGNED) BUSY_TIME ");//示忙总时长
                sql.append("FROM");
                sql.append(busiName+".tbill_agent_stat_rpt ");
                sql.append("WHERE");
                sql.append(userId,"AGENTID =? ",false);
                sql.append(startDate,"AND DAY >=? ");
                sql.append(endDate, " AND DAY <=? ");
                sql.append("GROUP BY");
                sql.append("AGENTID ");
                sql.append(") t1");
                //只有按天的才需要查询挂机满意度
                if("1".equals(statType)) {
                    sql.append("LEFT JOIN (");
                    sql.append("SELECT");
                    sql.append("workno,");//坐席id
                    sql.append("round(");
                    sql.append("sum( CASE WHEN MARK = '1' OR MARK = '2' THEN 1 ELSE 0 END ) / sum( CASE WHEN mark != '0' THEN 1 ELSE 0 END ),4) GOOD_PERCENT ");//挂机满意度
                    sql.append("FROM");
                    sql.append(busiName + ".t_sce_dz_12345myd ");
                    sql.append("WHERE");
                    sql.append(userId, "workno =? ");
                    sql.append(startDate + " 00:00:00", "AND ENTERTIME >=? ");
                    sql.append(endDate + " 23:59:59", "AND ENTERTIME <=? ");
                    sql.append("GROUP BY");
                    sql.append("workno ");
                    sql.append(") t2 ON t1.AGENTID = t2.workno");
                }
                userCallData = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(userCallData == null){
                    userCallData = new JSONObject();
                }
                sql = new EasySQL();
                sql.append("select CONVERT(sum(ORDER_COUNT),UNSIGNED) ORDER_COUNT from "+busiName+".cx_agent_order_day_stat where 1=1 ");
                sql.append(userId,"and AGENT_NO =? ",false);
                sql.append(startDate,"AND DATE_ID >=? ");
                sql.append(endDate, " AND DATE_ID <=? ");
                JSONObject orderJson = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(orderJson!=null){
                    userCallData.put("ORDER_COUNT",orderJson.getString("ORDER_COUNT"));
                }
                cache.put(key,userCallData,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return userCallData;
    }

    /**
     * 获取当前班组的当天接听量、平均通话时长、平均话后时长、签入总时长、接听总时长、话后总时长、示忙总时长、挂机满意度
     * @param workGroupId
     * @return
     */
    public static JSONObject getWorkGroupCallData(String workGroupId,String statType){
        String key = "AgentMonitor:getWorkGroupCallData_"+workGroupId+"_"+statType;
        JSONObject workGroupCallData = cache.get(key);
        String startDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        String endDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        if("2".equals(statType)){
            startDate = DateUtil.addDay("yyyy-MM-dd",endDate,-6);//获取七天前日期
        }else if("3".equals(statType)){
            startDate = DateUtil.addDay("yyyy-MM-dd",endDate,-29);//获取三十天前日期
        }else if("month".equals(statType)){
            startDate = EasyDate.getCurrentDateString("yyyy-MM")+"-01";//获取本月第一天
        }
        if(workGroupCallData==null){
            try {
                EasySQL sql = new EasySQL("SELECT");
                sql.append("CONVERT(sum( CALL_IN_COUNT_ALL ),UNSIGNED) CALL_IN_COUNT_ALL,");//当天接听量
                sql.append("CONVERT(round( sum( CALL_IN_TIME_ALL ) / sum( CALL_IN_COUNT_ALL ), 0 ),UNSIGNED) AVG_CALL_IN_TIME,");//平均通话时长
                sql.append("CONVERT(round( sum( ARRANGE_TIME ) / sum( ARRANGE_COUNT ), 0 ),UNSIGNED) AVG_ARRANGE_TIME,");//平均话后时长
                sql.append("CONVERT(sum( LOGIN_TIME ),UNSIGNED) LOGIN_TIME,");//签入总时长
                sql.append("CONVERT(sum( CALL_IN_TIME_ALL ),UNSIGNED) CALL_IN_TIME_ALL,");//接听总时长
                sql.append("CONVERT(sum( ARRANGE_TIME ),UNSIGNED) ARRANGE_TIME,");//话后总时长
                sql.append("CONVERT(sum( BUSY_TIME ),UNSIGNED) BUSY_TIME ");//示忙总时长
                sql.append("FROM");
                sql.append(busiName+".tbill_agent_stat_rpt ");
                sql.append("WHERE");
                sql.append(workGroupId,"AGENTID in(select AGENTID from "+busiName+".tagentInfo where WORKGROUPID=? and" +
                        " SUBCCNO='"+Constants.getHwCcId()+"' and VDN='"+Constants.getHwVdnId()+"') ",false);
                sql.append(startDate,"AND DAY >=? ");
                sql.append(endDate,"AND DAY <=? ");
                workGroupCallData = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(workGroupCallData == null){
                    workGroupCallData = new JSONObject();
                }
                //只有按天和按月才需要挂机满意度
                if("1".equals(statType) || "month".equals(statType)) {
                    sql = new EasySQL();
                    sql.append("SELECT");
                    sql.append("round(");
                    sql.append("sum( CASE WHEN MARK = '1' OR MARK = '2' THEN 1 ELSE 0 END ) / sum( CASE WHEN mark != '0' THEN 1 ELSE 0 END ),4) GOOD_PERCENT ");//挂机满意度
                    sql.append("FROM");
                    sql.append(busiName + ".t_sce_dz_12345myd ");
                    sql.append("WHERE");
                    sql.append(workGroupId, "workno in(select AGENTID from " + busiName + ".tagentinfo where WORKGROUPID=? and" +
                            " SUBCCNO='" + Constants.getHwCcId() + "' and VDN='" + Constants.getHwVdnId() + "') ", false);
                    sql.append(startDate + " 00:00:00", "AND ENTERTIME >=? ");
                    sql.append(endDate + " 23:59:59", "AND ENTERTIME <=? ");

                    JSONObject goodPercent = readQuery.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                    if (goodPercent != null) {
                        workGroupCallData.put("GOOD_PERCENT", goodPercent.getString("GOOD_PERCENT"));
                    }
                }
                sql = new EasySQL();
                sql.append("select CONVERT(sum(ORDER_COUNT),UNSIGNED) ORDER_COUNT from "+busiName+".cx_agent_order_day_stat where 1=1 ");
                sql.append(workGroupId," and WORK_GROUP =? ",false);
                sql.append(startDate,"AND DATE_ID >=? ");
                sql.append(endDate, " AND DATE_ID <=? ");
                JSONObject orderJson = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(orderJson!=null){
                    workGroupCallData.put("ORDER_COUNT",orderJson.getString("ORDER_COUNT"));
                }
                cache.put(key,workGroupCallData,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return workGroupCallData;
    }

    /**
     * 获取当前班组的班长
     * @param workGroupId
     * @return
     */
    public static String getWorkGroupMonitors(String workGroupId) {
        String key = "AgentMonitor:getWorkGroupMonitors_" + workGroupId;
        String workGroupMonitors = cache.get(key);
        if (StringUtils.isBlank(workGroupMonitors)) {
            try {
                EasySQL sql = new EasySQL("SELECT GROUP_CONCAT(NAME) AGENT_NAME from "+busiName+".tagentinfo t1 ");
                //角色类型： １：管理员  ２：班长  ３：坐席
                sql.append("inner join "+busiName+".cc_busi_user t2 on t1.AGENTID=t2.AGENT_PHONE");
                sql.append("inner join "+busiName+".cc_role t3 on t2.ROLE_ID=t3.ROLE_ID where t3.ROLE_TYPE='2' ");
                sql.append(Constants.getHwCcId()," and t1.SUBCCNO=?");
                sql.append(Constants.getHwVdnId()," and t1.VDN=?");
                sql.append(workGroupId," and t1.WORKGROUPID=?");
                workGroupMonitors = readQuery.queryForString(sql.getSQL(), sql.getParams());
                cache.put(key, workGroupMonitors, 5 * 60);//缓存存储5分钟
            } catch (Exception e) {
                e.printStackTrace();
                logger.error(e.getMessage(), e);
            }
        }
        return workGroupMonitors;
    }

    /**
     * 获取当前班组下的坐席
     * @param workGroupId
     * @return
     */
    public static List<JSONObject> getWorkGroupAgent(String workGroupId) {
        String key = "AgentMonitor:getWorkGroupAgent_" + workGroupId;
        List<JSONObject> workGroupAgent = cache.get(key);
        if (workGroupAgent==null) {
            try {
                EasySQL sql = new EasySQL("SELECT * from "+busiName+".tagentinfo t1 where 1=1 ");
                sql.append(Constants.getHwCcId()," and t1.SUBCCNO=?");
                sql.append(Constants.getHwVdnId()," and t1.VDN=?");
                sql.append(workGroupId," and t1.WORKGROUPID=?");
                workGroupAgent = readQuery.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                cache.put(key, workGroupAgent, 5 * 60);//缓存存储5分钟
            } catch (Exception e) {
                e.printStackTrace();
                logger.error(e.getMessage(), e);
            }
        }
        return workGroupAgent;
    }

    /**
     * 获取当前坐席列表下的坐席的座席的所有信息
     * @param workGroupId
     * @return
     */
    public static JSONArray getAgentInfoList(String workGroupId) {
        String key = "AgentMonitor:getAgentInfoList_" + workGroupId;
        JSONArray getAgentInfoList = cache.get(key);
        if (getAgentInfoList==null) {
            List<JSONObject> agentList = getWorkGroupAgent(workGroupId);
            StringBuilder builder = new StringBuilder();
            for (JSONObject agent : agentList) {
                builder.append(agent.getString("AGENTID")).append(",");
            }
            getAgentInfoList = CtiUtils.siteagentinfobyagentids(builder.toString());
            cache.put(key, getAgentInfoList, 5);//缓存存储5秒
        }
        return getAgentInfoList;
    }

    /**
     * 获取当前坐席列表下的坐席统计信息
     * @param workGroupId
     * @return
     */
    public static JSONObject getAgentStat(String workGroupId) {
        String key = "AgentMonitor:getAgentStat_" + workGroupId;
        JSONObject getAgentStat = cache.get(key);
        if (getAgentStat==null) {
            getAgentStat = new JSONObject();
            JSONArray array = getAgentInfoList(workGroupId);
            JSONObject json = null;
            String currentState = "";
            for (int i = 0; i < array.size(); i++) {
                json = array.getJSONObject(i);
                // 状态   -> 当前状态 0：表示话务员未签入。1：表示话务员空闲。2：表示预占用状态。3：表示占用状态。4：表示应答状态。5：表示通话状态。6：表示工作状态。7：表示忙状态。8：表示请假休息。9：表示学习态。10：表示调整态。
                currentState = json.getString("currentState");
                if(!"0".equals(currentState)){
                    //签入数
                    getAgentStat.put("loginCount",getAgentStat.getIntValue("loginCount")+1);
                }
                if("2".equals(currentState) || "3".equals(currentState) || "4".equals(currentState) || "5".equals(currentState)){
                    //通话数
                    getAgentStat.put("callCount",getAgentStat.getIntValue("callCount")+1);
                }else if("1".equals(currentState)){
                    //空闲数
                    getAgentStat.put("ideaCount",getAgentStat.getIntValue("ideaCount")+1);
                }else if("6".equals(currentState) || "10".equals(currentState)){
                    //话后
                    getAgentStat.put("afterCallCount",getAgentStat.getIntValue("afterCallCount")+1);
                }else if("7".equals(currentState) || "8".equals(currentState) || "9".equals(currentState)){
                    //示忙
                    getAgentStat.put("busyCount",getAgentStat.getIntValue("busyCount")+1);
                }
            }
            cache.put(key, getAgentStat, 5);//缓存存储5秒
        }
        return getAgentStat;
    }

    /**
     * 获取当前班组的告警记录
     * @param workGroupId
     * @return
     */
    public static JSONObject getWorkGroupAlarm(String workGroupId){
        String key = "AgentMonitor:getWorkGroupAlarm_"+workGroupId;
        JSONObject userAlarm = cache.get(key);
        if(userAlarm==null){
            try {
                EasySQL sql = new EasySQL("SELECT");
                sql.append("sum( BUSY_COUNT ) BUSY_COUNT,");//示忙次数
                sql.append("sum( EMO_COUNT ) EMO_COUNT,");//情绪异常次数
                sql.append("sum( SPEED_COUNT ) SPEED_COUNT,");//语速过快次数
                sql.append("sum( ROB_TRAFFICE_COUNT ) ROB_TRAFFICE_COUNT,");//抢话次数
                sql.append("sum( DISABLE_COUNT ) DISABLE_COUNT,");//禁用词/违规词
                sql.append("sum( PUSH_COUNT ) PUSH_COUNT,");//推送满意度次数
                sql.append("sum( MY_COUNT ) MY_COUNT,");//满意次数
                sql.append("sum( BMY_COUNT ) BMY_COUNT,");//不满意次数
                sql.append("sum( VERY_BMY_COUNT ) VERY_BMY_COUNT,");//非常不满意次数
                sql.append("sum( VOICE_COUNT ) VOICE_COUNT,");//静音次数
                sql.append("sum( SENS_COUNT ) SENS_COUNT,");//敏感词次数
                sql.append("sum( SECRECY_SENS_COUNT ) SECRECY_SENS_COUNT,");//保密词次数
                sql.append("sum( SPEECH_FAST_COUNT ) SPEECH_FAST_COUNT,");//语速超快次数
                sql.append("sum( CALL_LONG_COUNT ) CALL_LONG_COUNT,");//超长通话次数
                sql.append("sum( HELP_COUNT ) HELP_COUNT,");//求助次数
                sql.append("sum( PHONE_AFTEL_COUNT ) PHONE_AFTEL_COUNT ");//话后超时次数
                sql.append("FROM "+busiName+".tbill_agent_workgroup_stat_rpt   where 1=1  ");
                sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," and DAY=?");
                sql.append(workGroupId," and WORKGROUPID=?");
                userAlarm = readQuery.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
                if(userAlarm==null){
                    userAlarm = new JSONObject();
                }
                cache.put(key,userAlarm,5*60);//缓存存储5分钟
            }catch (Exception e){
                e.printStackTrace();
                logger.error(e.getMessage(),e);
            }
        }

        return userAlarm;
    }

    /**
     * 获取所有未处理的告警数据
     * @return
     */
    public static Map<String,String> getAgentWarn(YCUserPrincipal ycuser) {
        String key = "AgentMonitor:getWorkGroupAlarm_"+ycuser.getLoginAcct();
        Map<String,String> agentWarn = cache.get(key);
        if(agentWarn!=null){
            return agentWarn;
        }
        try {
            agentWarn = new HashMap<>();
            EasySQL sql = new EasySQL("select t1.AGENT_PHONE,t1.AGENT_ID from ");
            sql.append(busiName+".cx_agent_alarm_12345 t1");
            if(ycuser.getRoleType()==Constants.ROLE_TYPE_MONITOR){//班长角色看班组内
                sql.append(ycuser.getGroupName()," inner join "+busiName+".tagentinfo t2 on t1.AGENT_ID=t2.AGENTID and t2.AGENTWORKGROUP=? ");
            }
            sql.append(EasyDate.addTime("yyyy-MM-dd",EasyDate.getCurrentDateString("yyyy-MM-dd"), Calendar.DAY_OF_YEAR,-7),
                    " where t1.IS_ALARM=1 and t1.DATE_ID>=? ");
            List<JSONObject> list = QueryFactory.getReadQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if (list != null) {
                for (JSONObject obj : list) {
                    agentWarn.put(obj.getString("AGENT_PHONE"),obj.getString("AGENT_ID"));
                }
            }
            cache.put(key,agentWarn,5);//缓存存储5秒
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return agentWarn;
    }

    /**
     * 获取当前坐席所有的待办列表
     * @param ycuser
     * @return
     */
    public static JSONObject getToDuList(YCUserPrincipal ycuser,JSONObject param){
        String key = "AgentMonitor:toDuList_"+ycuser.getUserId()+"_"+param.getIntValue("pageIndex")+"_"+param.getIntValue("pageSize");
        JSONObject result = cache.get(key);
        try {
            if(result==null){
                int role = ycuser.getRoleType();
                if(role == Constants.ROLE_TYPE_AGENT){//坐席直接返回
                    return result;
                }
                //坐席名称、坐席id、坐席号码、告警类型、告警时间、话房id
                EasySQL sql = new EasySQL("SELECT t1.MSG_ID,t1.AGENT_NAME,t1.AGENT_ID,t1.AGENT_PHONE,t1.ALARM_TYPE,t1.ALARM_TIME,t3.ROOM_LOCATION FROM");
                sql.append(busiName+".cx_agent_alarm_12345 t1");
                sql.append(" left join "+busiName+".cx_12345_phone t3 on t1.AGENT_PHONE=t3.PHONE_NUMBER ");
                if(role==Constants.ROLE_TYPE_MONITOR){//班长角色看班组内
                    sql.append(ycuser.getGroupName()," inner join "+busiName+".tagentinfo t2 on t1.AGENT_ID=t2.AGENTID and t2.AGENTWORKGROUP=? ");
                }
                sql.append(EasyDate.addTime("yyyy-MM-dd",EasyDate.getCurrentDateString("yyyy-MM-dd"), Calendar.DAY_OF_YEAR,-7),
                        " where t1.IS_ALARM=1 and t1.DATE_ID>=? ");
                sql.append(" order by ALARM_TYPE desc,ALARM_TIME desc");
                result = queryForPageList(readQuery,sql,param.getIntValue("pageIndex"),param.getIntValue("pageSize"));
                cache.put(key, result,5);//缓存保存5s
            }

        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return result;
    }

    /**
     * 通过消息id和消息类型获取消息详情
     * @param magId
     * @param msgType
     * @return
     */
    public static  List<String> getAlarmAgentInfo(String magId,String msgType,YCUserPrincipal ycuser){
        List<String>  result = new ArrayList<>();
        try {
            List<JSONObject>  alarmAgents = null;
            EasySQL sql = new EasySQL("SELECT AGENT_ID FROM " + busiName + ".cx_agent_alarm_12345 where IS_ALARM=1 ");
            sql.append(magId, " and MSG_ID =? ");
            sql.append(msgType, " and ALARM_TYPE =? ");
            int role = ycuser.getRoleType();
            if(role==Constants.ROLE_TYPE_MONITOR){//班长角色看班组内
                sql.append(ycuser.getGroupName()," inner join "+busiName+".tagentinfo t2 on t1.AGENT_ID=t2.AGENTID and t2.AGENTWORKGROUP=? ");
            }
            alarmAgents = QueryFactory.getReadQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            for (JSONObject agent : alarmAgents) {
                result.add(agent.getString("AGENT_ID"));
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取坐席的评分信息
     * @return
     */
    public static JSONObject getAgentScoreInfo(String agentId){
        String key = "AgentMonitor:getAgentScoreInfo_"+agentId;
        JSONObject json = cache.get(key);
        try {
            if(json == null){
                EasySQL sql = new EasySQL("SELECT t1.* FROM "+busiName+".cx_agent_score_config_12345 t1 where 1=1 ");
                sql.append(agentId," and t1.AGENT_NO=? ");
                sql.append("ORDER BY MONTH_ID desc limit 1");
                json = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if(json==null){
                    json = new JSONObject();
                }
                cache.put(key, json, 5*60);//坐席个人评分保存5分钟
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return json;
    }
    /**
     * 获取班组的评分信息
     * @return
     */
    public static JSONObject getWorkGroupScoreInfo(String workGroup){
        String key = "AgentMonitor:getWorkGroupScoreInfo_"+workGroup;
        JSONObject json = cache.get(key);
        try {
            if(json == null){
                EasySQL sql = new EasySQL("SELECT t1.* FROM "+busiName+".cx_work_score_config_12345 t1 where 1=1 ");
                sql.append(workGroup," and t1.WORK_GROUP=? ");
                sql.append("ORDER BY MONTH_ID desc limit 1");
                json = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if(json==null){
                    json = new JSONObject();
                }
                cache.put(key, json, 5*60);//坐席个人评分保存5分钟
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return json;
    }

    /**
     * 获取坐席和班组五维图配置
     * @return
     */
    public static JSONObject getWorkNumberConfig(){
        String key = "CX_CONFIG_WORK_NUMBER_CONFIG";
        //{"AGENT":{"DAY_CALL_IN_NUMBER":"10000","DAY_CALL_TIME_AVG":"10000","DAY_AFTER_TIME_AVG":"10000","DAY_LOGIN_TIME":"10000","DAY_ORDER_COUNT":"10000","WEEK_CALL_IN_NUMBER":"10000","WEEK_CALL_TIME_AVG":"10000","WEEK_AFTER_TIME_AVG":"10000","WEEK_LOGIN_TIME":"10000","WEEK_ORDER_COUNT":"10000","MONTH_CALL_IN_NUMBER":"10000","MONTH_CALL_TIME_AVG":"10000","MONTH_AFTER_TIME_AVG":"10000","MONTH_LOGIN_TIME":"10000","MONTH_ORDER_COUNT":"10000"},"WORK_GROUP":{"DAY_CALL_IN_NUMBER":"10000","DAY_CALL_TIME_AVG":"10000","DAY_AFTER_TIME_AVG":"10000","DAY_LOGIN_TIME":"10000","DAY_ORDER_COUNT":"10000","WEEK_CALL_IN_NUMBER":"10000","WEEK_CALL_TIME_AVG":"10000","WEEK_AFTER_TIME_AVG":"10000","WEEK_LOGIN_TIME":"10000","WEEK_ORDER_COUNT":"10000","MONTH_CALL_IN_NUMBER":"10000","MONTH_CALL_TIME_AVG":"10000","MONTH_AFTER_TIME_AVG":"10000","MONTH_LOGIN_TIME":"10000","MONTH_ORDER_COUNT":"10000"}}
        JSONObject json = cache.get(key);
        try {
            if(json == null){
                //WORK_NUMBER_CONFIG：工作量五维图配置字段 CALL_IN_NUMBER是呼入量 CALL_TIME_AVG平均通话时长 AFTER_TIME_AVG话后平均时长 LOGIN_TIME签入时长 ORDER_COUNT工单量
                EasySQL sql = new EasySQL("SELECT WORK_NUMBER_CONFIG FROM "+busiName+".cx_12345_config where 1=1 ");
                json = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if(json!=null && StringUtils.isNotBlank(json.getString("WORK_NUMBER_CONFIG"))){
                    json = json.getJSONObject("WORK_NUMBER_CONFIG");
                }else{
                    json = new JSONObject();
                }
                cache.put(key, json);//坐席和班组五维图配置保存缓存
            }
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        return json;
    }
    /**
     * 根据传入的日、周、月数据获取五维图数据
     * @param toDayData
     * @param toWeekData
     * @param toMonthData
     * @param type AGENT,WORK_GROUP
     * @return
     */
    public static JSONObject getWorkNumberJson(JSONObject toDayData,JSONObject toWeekData,JSONObject toMonthData,String type){
        JSONObject result = new JSONObject();
        JSONObject workNumberConfig = CommonUtils.getWorkNumberConfig();
        JSONObject config = workNumberConfig.getJSONObject(type);
        if (config!=null){
            result.put("day",getWorkNumberByStatType(toDayData,config,"DAY_"));
            result.put("week",getWorkNumberByStatType(toWeekData,config,"WEEK_"));
            result.put("month",getWorkNumberByStatType(toMonthData,config,"MONTH_"));
        }

        return result;
    }

    /**
     * 根据数据获取五维图中的各个维度的数据
     * @param data
     * @param config
     * @param statType DAY_;WEEK_;MONTH_;
     * @return
     */
    public static JSONObject getWorkNumberByStatType(JSONObject data,JSONObject config,String statType){
        JSONObject result = new JSONObject();
        double number = data.getDoubleValue("CALL_IN_COUNT_ALL");//呼入量
        double numberMax = config.getDoubleValue(statType+"CALL_IN_NUMBER");//最大接听量
        long numberLong =  Long.parseLong(String.format("%.0f", number));
        long numberMaxLong =  Long.parseLong(String.format("%.0f", numberMax));
        result.put("callInCount",numberLong);
        result.put("callInCountLeven",getLeven(numberLong,numberLong));//获取级别

        number = data.getDoubleValue("AVG_CALL_IN_TIME");//平均通话时长
        numberMax = config.getDoubleValue(statType+"CALL_TIME_AVG");//最大平均通话时长
        numberLong =  Long.parseLong(String.format("%.0f", number));
        numberMaxLong =  Long.parseLong(String.format("%.0f", numberMax));
        result.put("avgCallInTime",numberLong);
        result.put("avgCallInTimeLeven",getLeven(numberLong,numberMaxLong));//获取级别

        number = data.getDoubleValue("AVG_ARRANGE_TIME");//平均话后时长
        numberMax = config.getDoubleValue(statType+"CALL_TIME_AVG");//最大平均话后时长
        numberLong =  Long.parseLong(String.format("%.0f", number));
        numberMaxLong =  Long.parseLong(String.format("%.0f", numberMax));
        result.put("avgArrangeTime",numberLong);
        result.put("avgArrangeTimeLeven",getLeven(numberLong,numberMaxLong));//获取级别

        number = data.getDoubleValue("LOGIN_TIME");//签入总时长
        numberMax = config.getDoubleValue(statType+"LOGIN_TIME")*60*60;//签入总时长（配置的是小时，需要转成秒）
        numberLong =  Long.parseLong(String.format("%.0f", number));
        numberMaxLong =  Long.parseLong(String.format("%.0f", numberMax));
        result.put("loginTime",numberLong);
        result.put("loginTimeLeven",getLeven(numberLong,numberMaxLong));//获取级别

        number = data.getDoubleValue("ORDER_COUNT");//工单量
        numberMax = config.getDoubleValue(statType+"ORDER_COUNT");//最大工单量
        numberLong =  Long.parseLong(String.format("%.0f", number));
        numberMaxLong =  Long.parseLong(String.format("%.0f", numberMax));
        result.put("orderCount",numberLong);
        result.put("orderCountLeven",getLeven(numberLong,numberMaxLong));//获取级别
        return result;
    }

    /**
     * 获取级别
     * @param number
     * @param numberMax
     * @return
     */
    public static long getLeven(long number,long numberMax) {
        if(number>=numberMax){
            return 5;
        }
        long numberMax5 = numberMax/5;
        if(numberMax5==0){
            return 1;
        }
        long leven = number/numberMax5;
        return leven==0?1:leven>5?5:leven;
    }

    /**
     * 获取上个月评分
     * @return
     */
    public static JSONObject getLastMonthScoreFive(JSONObject scoreInfo){
        JSONObject result = new JSONObject();
        int number = scoreInfo.getIntValue("BUSI_NUMBER_SCORE");//业务量
        int numberMax = 150;//业务量得分
        result.put("busiNumberScore",number);
        result.put("busiNumberScoreLeven",getLeven(number,numberMax));//获取级别

        number = scoreInfo.getIntValue("QUALITY_SCORE");//质检得分
        numberMax = 40;//质检得分
        result.put("qualityScore",number);
        result.put("qualityScoreLeven",getLeven(number,numberMax));//获取级别

        number = scoreInfo.getIntValue("ATTENDANCE_SCORE");//出勤得分
        numberMax = 10;//出勤得分
        result.put("attendanceScore",number);
        result.put("attendanceScoreLeven",getLeven(number,numberMax));//获取级别

        number = scoreInfo.getIntValue("MONTHLY_EXAM_SCORE");//月考得分
        numberMax = 5;//月考得分
        result.put("monthlyExamScore",number);
        result.put("monthlyExamScoreLeven",getLeven(number,numberMax));//获取级别

        number = scoreInfo.getIntValue("AFTER_LONG_SCORE");//话后处理得分
        numberMax = 5;//话后处理得分
        result.put("afterLongScore",number);
        result.put("afterLongScoreLeven",getLeven(number,numberMax));//获取级别
        return result;
    }

    /**
     * 根据坐席工号查询发起的首发诉求列表
     * @return
     */
    public static JSONObject appealList(JSONObject data){
        String hwAgentId = data.getString("hwAgentId");
        int pageIndex = data.getIntValue("pageIndex");
        int pageSize = data.getIntValue("pageSize");
        // mars  助手
        String source = data.getString("source");
        String appealType = data.getString("appealType");
        String appealResult = data.getString("appealResult");
        try {
            EasySQL sql = new EasySQL();
            // 用户id
            JSONObject userInfo =  readQuery.queryForRow("SELECT USER_ID FROM CC_USER WHERE SSO_ACCT = ?", new Object[]{hwAgentId}, new JSONMapperImpl());
            if (userInfo == null) {
                logger.warn("根据华为工号未查询到对应的用户信息，华为工号：" + hwAgentId);
                return EasyResult.fail("工号：" + hwAgentId + "未查询到对应的用户信息");
            }
            String userId = userInfo.getString("USER_ID");
            if("mars".equals(source)){
                sql.append("SELECT APPEAL_ID,APPEAL_NAME,APPEAL_TYPE,APPEAL_CONTENT,APPEAL_SUBMIT_TIME FROM ");
                sql.append(busiName+".CX_APPEAL_RECORD");
                sql.append("WHERE 1 = 1");
                sql.append("and APPEAL_STATE = '2'");
                sql.append(userId, " AND USER_ID = ?");
            }else if("assistant".equals(source)){
                sql.append("SELECT T1.APPEAL_ID,T1.APPEAL_NAME,T1.APPEAL_TYPE,T1.APPEAL_CONTENT,T1.APPEAL_SUBMIT_TIME,T1.APPEAL_RESULT,T1.APPEAL_STATE,T2.AUDIT_USER_NAME FROM ");
                sql.append(busiName+".CX_APPEAL_RECORD T1");
                sql.append("LEFT JOIN");
                sql.append(busiName + ".CX_APPEAL_AUDIT_RECORD T2 ON T1.APPEAL_ID = T2.APPEAL_ID");
                sql.append("AND T2.AUDIT_TIME = ( SELECT MAX( AUDIT_TIME ) FROM");
                sql.append(busiName + ".CX_APPEAL_AUDIT_RECORD WHERE APPEAL_ID = T1.APPEAL_ID)");
                sql.append("WHERE 1 = 1");
                sql.append(appealType,"AND T1.APPEAL_TYPE = ?");
                sql.append(appealResult, " AND T1.APPEAL_RESULT = ?");
            }
            return queryForPageList(readQuery,sql,pageIndex,pageSize);
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
       return new JSONObject();
    }



    /**
     * 待办列表-查询首发诉求详情
     * @return
     */
    public static EasyResult appealInfo(JSONObject data) {
        try {
            String appealId = data.getString("appealId");
            // 来源 1 从待办列表 2 从个人画像
            String source = data.getString("source");
            if (StringUtils.isBlank(appealId)) {
                return EasyResult.fail("appealId不能为空！");
            }
            // 首发诉求详情
            JSONObject appealRecord = readQuery.queryForRow("SELECT APPEAL_ID,APPEAL_NAME,APPEAL_TYPE,APPEAL_CONTENT,APPEAL_SUBMIT_TIME FROM " + busiName + ".CX_APPEAL_RECORD WHERE APPEAL_ID = ?", new Object[]{appealId}, new JSONMapperImpl());
            if ("2".equals(source)) {
                // 审核记录
                List<JSONObject> auditRecordList = readQuery.queryForList("SELECT AUDIT_TIME,AUDIT_RESULT,HANDLING_OPINION,AUDIT_LEVEL,AUDIT_USER_NAME FROM " + busiName + ".CX_APPEAL_AUDIT_RECORD WHERE APPEAL_ID = ? ORDER BY AUDIT_LEVEL", new Object[]{appealId}, new JSONMapperImpl());
                appealRecord.put("auditRecordList", auditRecordList);
                // 用流计算筛选出auditRecordList中AUDIT_RESULT = 1的数量
                long agreeCount = auditRecordList.stream().filter(t -> "1".equals(t.getString("AUDIT_RESULT"))).count();
                long disagreeCount = auditRecordList.stream().filter(t -> "2".equals(t.getString("AUDIT_RESULT"))).count();
                // 同意数
                appealRecord.put("agreeCount", agreeCount);
                // 不同意数
                appealRecord.put("disagreeCount", disagreeCount);
            }
            return EasyResult.ok(appealRecord);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return EasyResult.fail();
    }

    /**
     * 待办列表详情-审核首发诉求
     * @return
     */
    public static EasyResult appealAudit(JSONObject data) {
        try {
            // 诉求id  msgId
            String appealId = data.getString("appealId");
            // 1通过 2不通过
            String auditResult = data.getString("auditResult");
            // 处理意见
            String handlingOpinion = data.getString("handlingOpinion");
            // 审核级别 1、班长 2、值班长 3......
            String auditLevel = data.getString("auditLevel");
            // 用户id
            String userId = data.getString("userId");
            // 用户姓名
            String userName = data.getString("userName");
            if (StringUtils.isBlank(appealId) || StringUtils.isBlank(auditResult) || StringUtils.isBlank(handlingOpinion)) {
                return EasyResult.fail("appealId、auditResult、handlingOpinion不能为空！");
            }
            EasyRecord record = new EasyRecord(busiName + ".CX_APPEAL_AUDIT_RECORD", "ID");
            record.put("ID", RandomKit.randomStr());
            // 首发诉求id
            record.put("APPEAL_ID", appealId);
            // 审核结果
            record.put("AUDIT_RESULT", auditResult);
            // 处理意见
            record.put("HANDLING_OPINION", handlingOpinion);
            // 审核时间
            record.put("AUDIT_TIME", DateUtil.getCurrentDateStr());
            // 审核级别
            record.put("AUDIT_LEVEL", auditLevel);
            // 审核人
            record.put("AUDIT_USER_ID", userId);

            record.put("AUDIT_USER_NAME", userName);
            writeQuery.save(record);
            // 不通过后，修改首发诉求状态
            if ( "2".equals(auditResult)) {
                writeQuery.executeUpdate("UPDATE " + busiName + ".CX_APPEAL_RECORD SET APPEAL_RESULT = '2',APPEAL_STATE = '1' WHERE APPEAL_ID = ?", appealId);
            }else if("1".equals(auditResult)){
                writeQuery.executeUpdate("UPDATE " + busiName + ".CX_APPEAL_RECORD SET APPEAL_RESULT = '1' WHERE APPEAL_ID = ?", appealId);
            }
            // 当值班长审核完，就把数据传到京办app
            if ("2".equals(auditLevel) && "1".equals(auditResult)) {
                // TODO 数据传到京办app
            }
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return EasyResult.fail();
    }

    /**
     * 查询所有坐席数据 坐席id 姓名 班组id 头像url 呼入量 呼出量
     */
    public static List<Map<String, String>> queryAgentList() {
        List<Map<String, String>> list = new ArrayList<>();
        try {
            String agentInfoStr = cache.get("WorkMonitor:AgentInfoList");
            if (StringUtils.isNotBlank(agentInfoStr)) {
                list = JSON.parseObject(agentInfoStr, new TypeReference<List<Map<String, String>>>() {});
            } else {
                String startDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
                String endDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
                EasySQL sql = new EasySQL("SELECT T3.*,CONVERT(T4.CALL_IN_COUNT_ALL,UNSIGNED) CALL_IN_COUNT_ALL,CONVERT(T4.CALL_OUT_COUNT_ALL,UNSIGNED) CALL_OUT_COUNT_ALL FROM");
                sql.append("(SELECT t1.AGENTID,t1.NAME,t1.WORKGROUPID,t2.IMG_URL from " + busiName + ".tagentinfo t1 left join cc_user t2 on t1.AGENTID = t2.SSO_ACCT");
                sql.append(Constants.getHwVdnId(),"where t1.VDN = ?");
                sql.append( Constants.getHwCcId(),"AND t1.SUBCCNO = ?) T3");
                sql.append("LEFT JOIN");
                sql.append("(SELECT AGENTID,SUM(CALL_IN_COUNT_ALL) CALL_IN_COUNT_ALL,SUM(CALL_OUT_COUNT_ALL) CALL_OUT_COUNT_ALL FROM " + busiName + ".tbill_agent_stat_rpt");
                sql.append(startDate,"WHERE DAY >= ?");
                sql.append(endDate,"AND DAY <= ? ");
                sql.append("GROUP BY AGENTID) T4 ON T3.AGENTID = T4.AGENTID");
                readQuery.setMaxRow(20000);
//                logger.info("查询工作量班组数据："+sql.getSQL() + "params:" + JSONObject.toJSONString(sql.getParams()));
                list = readQuery.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
                cache.put("WorkMonitor:AgentInfoList", JSON.toJSONString(list), 3600);
            }
        } catch (SQLException e) {
            logger.error("查询字典编码异常", e);
        }
        return list;
    }

    /**
     * 查询坐席或班组的求助信息
     * @param agentId
     * @param workId
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static JSONObject queryHelpList(String agentId,String workId,int pageIndex,int pageSize) {
        JSONObject result = null;
        try {
            String date = EasyDate.getCurrentDateString("yyyy-MM-dd");

            //名称、类型、内容、时间、已读状态（0未读 1已读）、处理人
            EasySQL sql = new EasySQL("select t2.NAME,t1.REMIND_TYPE,t1.REMIND_TIME,t1.IS_READ,t1.DISTRIBUTE_AGENT_ACC from ");
            sql.append(busiName+".cx_agent_remind_table t1 inner join "+busiName+".tagentInfo t2 on t1.AGENT_NO=t2.AGENTID where 1=1 ");
            sql.append(Constants.getHwVdnId(),"and t2.VDN = ?");
            sql.append( Constants.getHwCcId(),"AND t2.SUBCCNO = ?");
            sql.append(date," and t1.REMIND_DATE=?");
            sql.append("1"," and t1.TYPE=?");//求助
            sql.append(agentId," and t1.AGENT_NO=?");
            sql.append(workId," and t2.WORKGROUPID=?");
            result = queryForPageList(readQuery,sql,pageIndex,pageSize);
        } catch (Exception e) {
            logger.error("查询字典编码异常", e);
        }
        return result;
    }

    /**
     * 分页查询
     * @param query
     * @param sql
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws SQLException
     */
    protected static JSONObject queryForPageList(EasyQuery query, EasySQL sql, int pageIndex, int pageSize) throws Exception {
        EasySQL countSql = new EasySQL("select count(1) from (" + sql.getSQL() + ") temp");
        int total = query.queryForInt(countSql.getSQL(), sql.getParams());
        if(pageIndex<=0) {
            pageIndex = 1;
        }
        if(pageSize<=0) {
            pageSize = 1;
        }
        List<JSONObject> list = query.queryForList(sql.getSQL(),sql.getParams(), pageIndex, pageSize,new JSONMapperImpl());
        JSONObject result = new JSONObject();
        result.put("msg", "请求成功!");
        result.put("state", 1);
        result.put("pageSize", pageSize);
        result.put("pageNumber", pageIndex);
        result.put("totalRow", total);
        result.put("totalPage", (total + (pageSize - 1)) / pageSize);
        result.put("data", list);
        return result;
    }
    protected static JSONObject queryForPageList(EasyQuery query, EasySQL sql, String pageIndex, String pageSize) throws Exception {
        return queryForPageList(query, sql, Integer.parseInt(pageIndex), Integer.parseInt(pageSize));
    }

    /**
     * 获取正确的模版类型，传入//告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求
     * @param msgType
     * @return
     */
    public static String returnAlarmType(String msgType){
        //告警类型1.超长通话 2.话后超时 3.静音 4.语速过快 5.抢话 6.坐席违规词 7.市民违规词 8.求助 9.首发诉求
        // msgType 0话后超时，1超长通话，2超快语速，3求助，4其它，5转派 6公告 7静默 8抢话 9违规词 10敏感词
        if("2".equals(msgType)){
            return "0";// 0话后超时
        }else if("3".equals(msgType)){
            return "7";// 静音
        }else if("4".equals(msgType)){
            return "2";// 语速过快
        }else if("5".equals(msgType)){
            return "8";// 抢话
        }else if("6".equals(msgType)){
            return "9";// 坐席违规词
        }else if("7".equals(msgType)){
            return "10";// 市民敏感词
        }else if("8".equals(msgType)){
            return "3";// 3求助
        }else {
            return msgType;
        }

    }


}

