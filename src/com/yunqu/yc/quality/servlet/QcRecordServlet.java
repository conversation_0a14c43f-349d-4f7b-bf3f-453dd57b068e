package com.yunqu.yc.quality.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.quality.base.AppBaseServlet;
import com.yunqu.yc.quality.base.EntContext;
import com.yunqu.yc.quality.base.QueryFactory;

/**
 * 质检结果Servlet
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/qcRecord")
public class QcRecordServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 根据objId获取对应的录音文件
	 * @return
	 */
	public EasyResult actionForGetRecordFile(){
		EasyResult result = new EasyResult();
		JSONObject json = this.getJSONObject();

		String entId = json.getString("entId");
		EntContext context = EntContext.getContext(entId);
		try {
			List<JSONObject> queryForList = null;
			String serialId = json.getString("serialId");
			String objId = json.getString("objId");
			if(StringUtils.isNotBlank(serialId)){
				if("ecuser".equals(json.getString("userType"))){
					queryForList = this.getQuery().queryForList("select t1.*,t2.USER_ACCT,t2.USERNAME from " + context.getTableName("CC_CALL_RECORD t1") + " left join CC_EC_USER t2 on t1.AGENT_ID = t2.AGENT_ID where t1.SERIAL_ID = ?", new Object[]{serialId},new JSONMapperImpl());
				}else{
					queryForList = this.getQuery().queryForList("select * from " + context.getTableName("CC_CALL_RECORD") + " where SERIAL_ID = ?", new Object[]{serialId},new JSONMapperImpl());
				}
			}else if(StringUtils.isNotBlank(objId)){
				queryForList = this.getQuery().queryForList("select * from " + context.getTableName("CC_CALL_RECORD") + " where OBJ_ID = ? order by SERIAL_ID", new Object[]{objId},new JSONMapperImpl());
			}
			if(queryForList != null && queryForList.size() > 0){
				String recordFile=queryForList.get(0).getString("RECORD_FILE");
				serialId=queryForList.get(0).getString("SERIAL_ID");
				if(StringUtils.isNotBlank(recordFile)){
					 String prefix = getRecordUrlPrefix(entId);
					 if(prefix.startsWith("http")){
						 recordFile = prefix + recordFile;
					 }else{
						 recordFile = "/yc-qualityZn/record/play/"+entId+"/1/"+serialId;
					 }
				}
				result.setUrl(recordFile);
				result.setSuccess(queryForList.get(0), "获取录音文件成功");
			}else {
				result.addFail("没有通话录音文件！");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			result.addFail("获取录音文件失败！失败原因：" + e.getMessage());
		}
		return result;
	}
	
//	/**
//	 * 质检详情
//	 * @param resultId
//	 * @return
//	 */
//	private void setResultDetail(String resultId, String entId){
//
//		String entId = json.getString("entId");
//		EntContext context = EntContext.getContext(entId);
//		
//		StringBuffer sb = new StringBuffer();
//		try {
//			List<JSONObject> list = this.getQuery().queryForList("select * from "+getTableName("CC_QC_RESULT_ITEM")+" where QC_RESULT_ID = ?", new Object[]{resultId}, new JSONMapperImpl());
//			if(list != null){
//				for (JSONObject rowP : list) {
//					if("2000".equals(rowP.getString("P_ITEM_ID"))){
//						sb.append(rowP.getString("ITEM_NAME")+"：[");
//						String itemIdP = rowP.getString("RESULT_ITEM_ID");
//						for (JSONObject rowC : list) {
//							if(itemIdP.equals(rowC.getString("P_ITEM_ID")) && 1 == rowC.getIntValue("VALID_STATE")){
//								sb.append(rowC.getString("ITEM_NAME"));
//								if(rowC.getIntValue("SCORE") > 0){
//									sb.append("：+");
//								}else{
//									sb.append("：");
//								}
//								sb.append(rowC.getIntValue("SCORE"));
//								sb.append("分，");
//							}
//						}
//						sb.append("];");
//						sb.append((char)10);
//					}
//				}
//			}
//			this.getQuery().execute("update "+getTableName("CC_QC_RESULT")+" set RESULT_CONTENT = ? where QC_RESULT_ID = ?", new Object[]{sb.toString(),resultId});
//		} catch (SQLException e) {
//			this.error(e.getMessage(), e);
//		}
//	}
	
	/**
	 * 录音文件前缀
	 * @return
	 */
	private String getRecordUrlPrefix(String entId){
		String url=CacheManager.getMemcache().get("RecordUrlPrefix_"+entId);
		   try {
			   if(StringUtils.isBlank(url)){
				   String sql = "select t2.RECORD_FILE_URL from CC_ENT_RES  t1  , CC_PETRA_RES t2 where t1.PETRA_ID = t2.PETRA_ID    and t1.ENT_ID =  ?";
				    url=this.getQuery().queryForString(sql, new Object[]{entId});
				    if(StringUtils.isNotBlank(url)){
				    	if(url.lastIndexOf("/")==-1){
				    		url=url+"/";
				    	}
				    }
				    CacheManager.getMemcache().put("RecordUrlPrefix_"+entId, url);
			   }
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return url;
	}
	
	@Override
	protected EasyQuery getQuery() {
		return QueryFactory.getWriteQuery();
	}
	
	@Override
	protected String getResId() {
		// TODO Auto-generated method stub
		return null;
	}

}
