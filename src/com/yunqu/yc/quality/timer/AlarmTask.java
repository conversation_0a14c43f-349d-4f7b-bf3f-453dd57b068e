package com.yunqu.yc.quality.timer;


import com.alibaba.fastjson.JSON;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.utils.HttpUtil;
import com.yunqu.yc.quality.utils.http.HttpResp;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import java.net.URLEncoder;

/**
 * 10分钟定时任务获取告警信息
 *
 * <AUTHOR>
 */
public class AlarmTask implements Job {

    static Logger logger = CommonLogger.getLogger("alarm");

    protected static EasyQuery getQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
        query.setTimeout(60);
        query.setLogger(logger);
        return query;
    }

    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        if (!Constants.isDaemon()) {
            logger.info("AlarmTask，跨过执行。");
            return;
        }

        try {
            // 获取任务的告警规则配置
            Map<String, List<JSONObject>> taskAlarmRules = getTaskAlarmRules();

            if (taskAlarmRules.isEmpty()) {
                logger.info("AlarmTask: 未找到配置告警规则的任务，跳过执行");
                return;
            }

            // 处理每个任务的告警检查
            for (Map.Entry<String, List<JSONObject>> entry : taskAlarmRules.entrySet()) {
                String taskId = entry.getKey();
                List<JSONObject> alarmItems = entry.getValue();

                // 获取任务的时间范围
                Map<String, String> timeRange = getTaskTimeRange(taskId);
                if (StringUtils.isBlank(timeRange.get("startTime")) || StringUtils.isBlank(timeRange.get("endTime"))) {
                    logger.error("时间范围为空，跳过任务[" + taskId + "]");
                    continue;
                }
                // 获取质检项命中统计
                Map<Integer, Integer> hitCounts = getQcItemHitCounts(taskId, timeRange);

                // 检查告警条件并处理告警
                List<JSONObject> triggeredAlarms = checkAndProcessAlarms(taskId, alarmItems, hitCounts);

                if (!triggeredAlarms.isEmpty()) {
                    // 处理告警通知逻辑
                    processAlarms(taskId, triggeredAlarms, timeRange);
                } else {
                    logger.info("任务[" + taskId + "]未触发告警");
                }
            }
        } catch (Exception e) {
            logger.error("AlarmTask执行异常", e);
            throw new JobExecutionException(e);
        }
    }


    /**
     * 获取任务的告警规则配置信息
     *
     * @return Map<String, List < JSONObject>> key为taskId, value为告警项信息列表
     */
    private Map<String, List<JSONObject>> getTaskAlarmRules() {
        Map<String, List<JSONObject>> taskAlarmRules = new HashMap<>();
        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT * FROM CC_QC_ALARM_RULE t1 order by CREATE_TIME asc");
            List<JSONObject> ruleList = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            for (JSONObject rule : ruleList) {
                String taskId = rule.getString("TASK_ID");
                List<JSONObject> rules = new ArrayList<>();
                // 遍历10个质检项
                for (int i = 1; i <= 10; i++) {
                    int threshold = rule.getIntValue("QC_ITEM_THRESHOLD" + i);
                    // 阈值大于0的告警项
                    if (threshold > 0) {
                        JSONObject object = new JSONObject();
                        object.put("QC_ITEM_THRESHOLD", threshold);
                        object.put("QC_ITEM_INDEX", i);
                        rules.add(object);
                    }
                }
                if (rules.size() > 0) {
                    taskAlarmRules.put(taskId, rules);
                }
            }
        } catch (Exception e) {
            logger.error("获取任务告警规则配置失败", e);
        }
        return taskAlarmRules;
    }


    /**
     * 获取任务质检项命中统计
     *
     * @param taskId    任务ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return Map<Integer, Integer> key为质检项索引(1-10), value为命中次数
     */
    private Map<Integer, Integer> getQcItemHitCounts(String taskId, Map<String, String> timeRange) {
        Map<Integer, Integer> hitCounts = new HashMap<>();
        try {
            EasySQL sql = new EasySQL();
            sql.append("SELECT ");
            // 统计每个质检项的命中次数
            for (int i = 1; i <= 10; i++) {
                if (i < 10) {
                    sql.append("SUM(QC_ITEM" + i + ") as ITEM" + i + "_COUNT, ");
                } else {
                    sql.append("SUM(QC_ITEM" + i + ") as ITEM" + i + "_COUNT");
                }
            }
            sql.append(" FROM cc_qc_capacity");
            sql.append(taskId," WHERE TASK_ID = ?");

            sql.append(timeRange.get("startTime"), " AND QC_TIME >= ?");
            sql.append(timeRange.get("endTime"), " AND QC_TIME <= ?");
            JSONObject row = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            if (row != null) {
                for (int i = 1; i <= 10; i++) {
                    int count = row.getIntValue("ITEM" + i + "_COUNT");
                    hitCounts.put(i, count);
                }
            }
        } catch (Exception e) {
            logger.error("获取质检项命中统计失败, taskId: " + taskId, e);
        }
        return hitCounts;
    }

    /**
     * 检查告警条件并处理告警
     *
     * @param taskId     任务ID
     * @param alarmItems 告警项配置
     * @param hitCounts  质检项命中统计
     * @return List<AlarmItemInfo> 触发告警的质检项列表
     */
    private List<JSONObject> checkAndProcessAlarms(String taskId, List<JSONObject> alarmItems, Map<Integer, Integer> hitCounts) {
        List<JSONObject> triggeredAlarms = new ArrayList<>();

        for (JSONObject item : alarmItems) {
            int hitCount = hitCounts.getOrDefault(item.getInteger("QC_ITEM_INDEX"), 0);
            if (hitCount >= item.getIntValue("QC_ITEM_THRESHOLD")) {
                item.put("QC_ITEM_COUNT", hitCount);
                triggeredAlarms.add(item);
                logger.info("任务[" + taskId + "]质检项[" + item.getInteger("QC_ITEM_INDEX") + "]命中次数[" + hitCount + "]超过阈值[" + item.getIntValue("QC_ITEM_THRESHOLD") + "]");
            }
        }
        return triggeredAlarms;
    }

    /**
     * 获取任务的时间范围
     *
     * @param taskId 任务ID
     * @return Map包含startTime和endTime
     */
    private Map<String, String> getTaskTimeRange(String taskId) {
        Map<String, String> timeRange = new HashMap<>();

        try {
            EasySQL sql = new EasySQL("SELECT QC_PERIOD FROM CC_QC_ZN_TASK WHERE TASK_ID = ?");
            String taskType = getQuery().queryForString(sql.getSQL(), new Object[]{taskId});

            String startTime = "1970-01-01 00:00:00";
            String endTime = "1970-01-01 00:00:00";
            if (StringUtils.isNotBlank(taskType)) {
                if ("1".equals(taskType)) {
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.DATE, -1);
                    cal.set(Calendar.HOUR_OF_DAY, 0);
                    cal.set(Calendar.MINUTE, 0);
                    cal.set(Calendar.SECOND, 0);
                    startTime = EasyDate.dateToString(new Date(cal.getTime().getTime()), "yyyy-MM-dd HH:mm:ss");
                    cal.set(Calendar.HOUR_OF_DAY, 1);
                    cal.set(Calendar.MINUTE, 59);
                    cal.set(Calendar.SECOND, 59);
                    endTime = EasyDate.dateToString(new Date(cal.getTime().getTime()), "yyyy-MM-dd HH:mm:ss");
                }else if("4".equals(taskType)){//近10分钟
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:00");
                    startTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", endTime,Calendar.MINUTE,-10);
                } else if("5".equals(taskType)){//近15分钟
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:00");
                    startTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", endTime,Calendar.MINUTE,-15);
                } else if("6".equals(taskType)){//近30分钟
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:00");
                    startTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", endTime,Calendar.MINUTE,-30);
                }  else if("7".equals(taskType)){//近30分钟
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:00");
                    startTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", endTime,Calendar.MINUTE,-60);
                }   else if("8".equals(taskType)){//重检
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm:00");
                    startTime = EasyDate.addTime("yyyy-MM-dd HH:mm:ss", endTime,Calendar.HOUR_OF_DAY,-1);
                } else {
                    startTime = EasyDate.getCurrentDateString("yyyy-MM-dd 00:00:00");
                    endTime = EasyDate.getCurrentDateString("yyyy-MM-dd 23:59:59");
                }
                timeRange.put("startTime", startTime);
                timeRange.put("endTime", endTime);
                logger.debug("任务[" + taskId + "]时间范围: " + startTime + " 至 " + endTime);
            } else {
                logger.error("任务[" + taskId + "]配置的周期未读取到！");
            }
        } catch (Exception e) {
            logger.error("获取任务时间范围失败, taskId: " + taskId, e);
        }
        return timeRange;
    }


    /**
     * 获取告警短信模板
     * @return
     */
    private String getAlarmSmsTemplate() {
        try {
            return getQuery().queryForString("SELECT CONTENT FROM CC_QC_ALARM_SMS_TEMPLATE WHERE STATUS = 1 ORDER BY CREATE_TIME DESC LIMIT 1", null);
        } catch (Exception e) {
            logger.error("获取告警短信模板失败", e);
            return null;
        }
    }

    /**
     * 获取告警接收人手机号列表
     * @param taskId
     * @return
     */
    private Set<String> getAlarmReceivers(String taskId) {
        Set<String> receivers = new HashSet<>();
        try {
            // 获取全局告警配置的接收人
            List<Map<String, String>> globalReceivers = getQuery().queryForList(
                "SELECT USER_MOBILE FROM CC_QC_ALARM_RECEIVER WHERE USER_STATUS = 1 and USER_TYPE = 1",
                null,
                new MapRowMapperImpl()
            );
            for (Map<String, String> receiver : globalReceivers) {
                if (StringUtils.isNotBlank(receiver.get("USER_MOBILE"))) {
                    receivers.add(receiver.get("USER_MOBILE"));
                }
            }

            // 获取任务所属企业的告警接收人
            String entId = getQuery().queryForString("SELECT ENT_ID FROM CC_QC_ZN_TASK WHERE TASK_ID = ?", new Object[]{taskId});
            if (StringUtils.isNotBlank(entId)) {
                List<Map<String, String>> entReceivers = getQuery().queryForList(
                    "SELECT USER_MOBILE FROM cc_qc_alarm_receiver WHERE ENT_ID = ? AND USER_STATUS = 1 AND USER_TYPE = 2",
                    new Object[]{entId},
                    new MapRowMapperImpl()
                );
                for (Map<String, String> receiver : entReceivers) {
                    if (StringUtils.isNotBlank(receiver.get("USER_MOBILE"))) {
                        receivers.add(receiver.get("USER_MOBILE"));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取告警接收人失败", e);
        }
        return receivers;
    }

    /**
     * 处理告警内容
     * @param template
     * @param alarm
     * @param taskId
     * @param timeRange
     * @return
     */
    private String processAlarmContent(String template, JSONObject alarm, JSONObject taskInfo,JSONObject znClass, Map<String, String> timeRange) {
        try {

            String qcItemName = znClass.getString("QC_ITEM_NAME" + alarm.getIntValue("QC_ITEM_INDEX"));
            String content = template;
            content = content.replace("｛告警内容｝", "质检项命中数量超过阈值");
            content = content.replace("{企业名称}", taskInfo.getString("ENT_NAME"));
            content = content.replace("｛告警时间}", EasyDate.getCurrentDateString());
            content = content.replace("{告警质检项}", qcItemName);
            content = content.replace("{任务名称}", taskInfo.getString("TASK_NAME"));
            content = content.replace("{阈值}", String.valueOf(alarm.getIntValue("QC_ITEM_THRESHOLD")));
            content = content.replace("{统计时间范围}", timeRange.get("startTime")+"至"+timeRange.get("endTime"));
            return content;
        } catch (Exception e) {
            logger.error("处理告警内容失败", e);
            return template;
        }
    }
    
    /**
     * 发送告警短信并记录
     * @param mobile 手机号
     * @param content 短信内容
     */
    private void sendAndRecordAlarmSms(String mobile, String content) {
        try {
            // 1. 发送短信
           //String param = "mobile=" + URLEncoder.encode(mobile, "UTF-8") + "&content=" + URLEncoder.encode(content, "UTF-8") + "&type=qlzj";

            JSONObject json=new JSONObject();
            json.put("smsContent", URLEncoder.encode(content, "UTF-8"));
            json.put("phone",  URLEncoder.encode(mobile, "UTF-8"));
            logger.info("[sms]发送告警短信请求内容："+json.toJSONString());
            //String post = cn.hutool.http.HttpUtil.post(Constants.get114SmsUrl() + "?" + param, "");
            HttpResp resp= HttpUtil.sendPost(Constants.get114SmsUrl()+"/114smsgw/SendSms",json.toJSONString(), "GBK", "application/json;charset=utf-8", HttpUtil.TYPE_JSON);
            if (resp !=null && resp.getCode() == 200) {
                logger.info("发送成功，rtJson :" + json.toJSONString());
            }
            logger.info("[sms]发送告警短信响应内容："+ JSON.toJSONString(resp));
        } catch (Exception e) {
            logger.error("发送告警短信失败");
            logger.error(e.getMessage(),e);
        }
    }

    /**
     * 处理告警通知
     * @param taskId 任务ID
     * @param triggeredAlarms 触发的告警列表
     * @param timeRange 时间范围
     */
    private void processAlarms(String taskId, List<JSONObject> triggeredAlarms, Map<String, String> timeRange) {
        try {
            // 1. 获取告警短信模板
            String templateContent = getAlarmSmsTemplate();
            if (StringUtils.isBlank(templateContent)) {
                logger.error("未找到有效的告警短信模板");
                return;
            }

            // 2. 获取告警接收人手机号列表
            Set<String> receivers = getAlarmReceivers(taskId);
            if (receivers.isEmpty()) {
                logger.warn("任务[" + taskId + "]未配置告警接收人");
                return;
            }

            // 3. 处理每个触发的告警
            for (JSONObject alarm : triggeredAlarms) {

                // 获取任务信息
                JSONObject taskInfo = getQuery().queryForRow(
                        "SELECT TASK_ID,TASK_NAME,ZN_CLASS_ID, ENT_NAME FROM CC_QC_ZN_TASK t LEFT JOIN CC_ENT e ON t.ENT_ID = e.ENT_ID WHERE t.TASK_ID = ?",
                        new Object[]{taskId},
                        new JSONMapperImpl()
                );
                // 获取质检项名称
                JSONObject znClass = getQuery().queryForRow("SELECT * FROM CC_QC_CLASS_ZN WHERE ZN_CLASS_ID = ?", new Object[]{taskInfo.getString("ZN_CLASS_ID")}, new JSONMapperImpl());

                // 构建告警内容
                String content = processAlarmContent(templateContent, alarm, taskInfo,znClass, timeRange);
                
                // 4. 发送告警短信并记录
                for (String mobile : receivers) {
                    sendAndRecordAlarmSms(mobile, content);
                }
                saveRecordAlarm(receivers, content, alarm, taskInfo,znClass);
            }
        } catch (Exception e) {
            logger.error("处理告警通知失败", e);
        }
    }


    /**
     * 记录告警记录
     * @param mobile
     * @param content
     * @param alarmInfo
     * @param taskInfo
     * @param znClass
     */
    private void saveRecordAlarm(Set<String> mobile, String content, JSONObject alarmInfo,JSONObject taskInfo,JSONObject znClass) {
        String alarmId = RandomKit.randomStr();
        String currentTime = EasyDate.getCurrentDateString();
        try {
            EasyRecord record = new EasyRecord("CC_QC_ALARM_RECORD", "ALARM_ID");
            record.set("ALARM_ID", alarmId);
            record.set("TASK_ID", taskInfo.getString("TASK_ID"));
            record.set("ZN_CLASS_ID", znClass.getString("ZN_CLASS_ID"));
            record.set("QC_ITEM_NAME", znClass.getString("QC_ITEM_NAME"+alarmInfo.getInteger("QC_ITEM_INDEX")));
            record.set("QC_ITEM_INDEX", alarmInfo.getInteger("QC_ITEM_INDEX"));
            record.set("QC_ITEM_COUNT", alarmInfo.getInteger("QC_ITEM_COUNT"));
            record.set("QC_ITEM_THRESHOLD", alarmInfo.getInteger("QC_ITEM_THRESHOLD"));
            record.set("RECEIVER_MOBILES", String.join(",", mobile));
            record.set("SMS_CONTENT", content);
            record.set("SEND_TIME", currentTime);
            getQuery().save(record);
        } catch (Exception e) {
            logger.error("添加告警记录失败"+e.getMessage());
            logger.error(e.getMessage(),e);
        }
    }

}
