package com.yunqu.cc.report.servlet.stat;

import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.DuplicateSubmit;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.report.base.AppBaseServlet;
import com.yunqu.cc.report.base.CommonLogger;
import com.yunqu.cc.report.base.Constants;
import com.yunqu.cc.report.base.QueryFactory;
import com.yunqu.cc.report.dao.call.sql.CallOverViewSql;
import com.yunqu.cc.report.service.stat.VoiceCallLossService;
import com.yunqu.cc.report.service.stat.VoiceOverallService;
import com.yunqu.cc.report.service.stat.VoiceSatisifyService;
import com.yunqu.cc.report.service.stat.VoiceServiceLevelService;


@WebServlet("/servlet/callStatServlet")
public class CallStatServlet extends AppBaseServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Logger logger = CommonLogger.logger;
	
	/**
	 * 话务趋势分析
	 * @return
	 */
	@InfAuthCheck(resId = "cc-report-fx-voice-qs", msg = "您无权访问!")
//	@DuplicateSubmit
	public EasyResult actionForTrendAnalyse() {
		try {
			EasyResult result = EasyResult.ok();
			JSONObject jsonObject = this.getJSONObject();
			String startDate = jsonObject.getString("beginStatDate");
			String endDate = jsonObject.getString("endStatDate");
			String [] dateArr = getDateArr(startDate, endDate);
			startDate = startDate.replaceAll("-", "");
			endDate = endDate.replaceAll("-", "");
			String entId = this.getEntId();
			String busiOrderId = this.getBusiOrderId();
			
			VoiceOverallService overallService = new VoiceOverallService(entId,busiOrderId);
			//查询汇总数据
			List<JSONObject> trendAnalyse = overallService.getOverallData(startDate,endDate,"day",true,true,false,true,true,null);
			
			List<Integer> callCount = new ArrayList<Integer>();// 总接入量
			List<Integer> agentAnswerCount = new ArrayList<Integer>();// 呼入通话
			List<Integer> outConnSuccCount = new ArrayList<Integer>();// 呼出通话
			//漏话和未接
//			List<JSONObject> missCallAndNoAnswerList = getNoAnswerAndMissCall(entId,startDate,endDate);
			VoiceCallLossService calllossService = new VoiceCallLossService(entId,busiOrderId);
			List<JSONObject> missCallAndNoAnswerList = calllossService.getCalllossData(startDate,endDate,"day");
			
			List<Integer> missCallCount = new ArrayList<Integer>();// 漏话
			List<Integer> noAnswerCount = new ArrayList<Integer>();// 未接
			List<Integer> wordCount = new ArrayList<Integer>();// 留言
			List<Integer> connAgentCount = new ArrayList<Integer>();// 坐席数
			List<Integer> reqAgentCount = new ArrayList<Integer>();// 请求人工量
			List<Integer> inAgentCount = new ArrayList<Integer>();// 接话坐席数
			List<Double> reqSuccRate = new ArrayList<Double>();// 请求人工率
			List<String> xAxisList = new ArrayList<String>();// 横坐标
			for(int i=0;i<dateArr.length;i++) {
				String dateFormat = dateArr[i].replaceAll("-", "");
				xAxisList.add(dateArr[i].substring(5));
				int callCountNum = 0;
				int agentAnswerCountNum = 0;
				int outConnSuccCountNum = 0;
				
				int noAnswerNum = 0;
				int missCallNum = 0;
				int wordCountNum = 0;
				int connAgentCountNum = 0;
				int reqAgentCountNum = 0;
				int inAgentCountNum = 0;
				double reqSuccRateNum = 0.00;
				for(JSONObject json : trendAnalyse) {
					if(dateFormat.equals(json.getString("DATE_ID"))) {
						callCountNum = (json.getIntValue("IN_CALL_COUNT"));
						agentAnswerCountNum = (json.getIntValue("IN_CONN_SUCC_COUNT"));
						outConnSuccCountNum = (json.getIntValue("OUT_CONN_SUCC_COUNT"));
						
						wordCountNum = (json.getIntValue("WORD_COUNT"));
						connAgentCountNum = (json.getIntValue("CONN_AGENT_COUNT"));
						reqAgentCountNum = (json.getIntValue("REQ_AGENT_COUNT"));
						inAgentCountNum = (json.getIntValue("IN_AGENT_COUNT"));
						reqSuccRateNum = (json.getDoubleValue("REQ_SUCC_RATE"));
						break;
					}
				}
				for(JSONObject json : missCallAndNoAnswerList) {
					if(dateFormat.equals(json.getString("DATE_ID"))) {
						noAnswerNum = json.getIntValue("NOANSWER_COUNT");
						missCallNum = json.getIntValue("MISCALL_COUNT");
						break;
					}
				}
				callCount.add(callCountNum);
				agentAnswerCount.add(agentAnswerCountNum);
				outConnSuccCount.add(outConnSuccCountNum);
				noAnswerCount.add(noAnswerNum);
				missCallCount.add(missCallNum);
				wordCount.add(wordCountNum);
				connAgentCount.add(connAgentCountNum);
				reqAgentCount.add(reqAgentCountNum);
				inAgentCount.add(inAgentCountNum);
				reqSuccRate.add(reqSuccRateNum);
			}
			JSONObject trendAnalyseJson = new JSONObject();
			trendAnalyseJson.put("xAxisList", xAxisList);
			trendAnalyseJson.put("callCount", callCount);
			trendAnalyseJson.put("inConnSuccCount", agentAnswerCount);
			trendAnalyseJson.put("inAgentCount", inAgentCount);
			trendAnalyseJson.put("noAnswerCount", noAnswerCount);
			trendAnalyseJson.put("missCallCount", missCallCount);
			trendAnalyseJson.put("wordCount", wordCount);
			trendAnalyseJson.put("reqAgentCount", reqAgentCount);
			trendAnalyseJson.put("reqAgentRatio", reqSuccRate);
			trendAnalyseJson.put("outConnSuccCount", outConnSuccCount);
			trendAnalyseJson.put("agentCount", connAgentCount);
			result.put("trendAnalyseJson", trendAnalyseJson);
			
			//分时图表
			List<JSONObject> timeAnalyse = overallService.getOverallData(startDate,endDate,"hour",true,true,true,false,true,null);
			
			String[] timeArr = {"0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23"};
			List<Double> agentAnswerCount2 = new ArrayList<Double>();// 呼入接通、坐席接通
			List<Double> outConnSuccCount2 = new ArrayList<Double>();// 呼出接通
			List<Double> connAgentCount2 = new ArrayList<Double>();// 通话坐席数
			List<Double> callCount2 = new ArrayList<Double>();// 总呼入量
			List<Double> reqAgentCount2 = new ArrayList<Double>();// 请求人工数
			List<Double> alertingCount2 = new ArrayList<Double>();// 转人工量
			List<Double> inConnSuccRate2 = new ArrayList<Double>();// 坐席接通率
			List<String> xAxisList2 = new ArrayList<String>();
			for(int i=0;i<timeArr.length;i++){
				xAxisList2.add(timeArr[i]);
				double agentAnswerCountNum = 0.0;
				double outConnSuccCountNum = 0.0;
				double connAgentCountNum = 0.0;
				double callCountNum = 0.0;
				double reqAgentCountNum = 0.0;
				double alertingCountNum = 0.0;
				double inConnSuccRateNum = 0.00;
				for(JSONObject timeAnalyseJson : timeAnalyse) {
					if(timeAnalyseJson.getString("HOUR").equals(timeArr[i])) {
						agentAnswerCountNum = timeAnalyseJson.getDoubleValue("IN_CONN_SUCC_COUNT");
						outConnSuccCountNum = timeAnalyseJson.getDoubleValue("OUT_CONN_SUCC_COUNT");
						connAgentCountNum = timeAnalyseJson.getDoubleValue("CONN_AGENT_COUNT");
						callCountNum = timeAnalyseJson.getDoubleValue("IN_CALL_COUNT");
						alertingCountNum = timeAnalyseJson.getDoubleValue("ALTERING_COUNT");
						reqAgentCountNum = timeAnalyseJson.getDoubleValue("REQ_AGENT_COUNT");
						inConnSuccRateNum = timeAnalyseJson.getDoubleValue("IN_CONN_SUCC_RATE");
						break;
					}
				}
				agentAnswerCount2.add(agentAnswerCountNum);
				outConnSuccCount2.add(outConnSuccCountNum);
				connAgentCount2.add(connAgentCountNum);
				callCount2.add(callCountNum);
				reqAgentCount2.add(reqAgentCountNum);
				alertingCount2.add(alertingCountNum);
				inConnSuccRate2.add(inConnSuccRateNum);
			}
			JSONObject timeAnalyseJson = new JSONObject();
			timeAnalyseJson.put("inConnSuccCount", agentAnswerCount2);
			timeAnalyseJson.put("outConnSuccCount", outConnSuccCount2);
			timeAnalyseJson.put("agentCount", connAgentCount2);
			timeAnalyseJson.put("avgCallCount", callCount2);
			timeAnalyseJson.put("avgReqCount", reqAgentCount2);
			timeAnalyseJson.put("avgAgentCount", alertingCount2);
			timeAnalyseJson.put("avgConnCount", agentAnswerCount2);
			timeAnalyseJson.put("avgConnRate", inConnSuccRate2);
			timeAnalyseJson.put("xAxisList", xAxisList2);
			result.put("timeAnalyseJson", timeAnalyseJson);
			
			return result;
		}catch(Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail();
		}
	}

	/**
	 * 满意度分析
	 * @return
	 */
	@InfAuthCheck(resId = "cc-report-fx-voice-myd", msg = "您无权访问!")
	public EasyResult actionForSatisfAnalyse() {
		JSONObject result = new JSONObject();
		String beginStatDate = getJSONObject().getString("beginStatDate");
		String endStatDate = getJSONObject().getString("endStatDate");
		String[] dateArr = getDateArr(beginStatDate, endStatDate);
		String startDate = beginStatDate.replaceAll("-", "");
		String endDate = endStatDate.replaceAll("-", "");
		
		try {
			VoiceSatisifyService satisfyService = new VoiceSatisifyService(getEntId(),getBusiOrderId());
			List<JSONObject> satisfyList = satisfyService.getSatisfyData(startDate, endDate, "");
			if(satisfyList != null && satisfyList.size()>0) {
				result.put("eval", satisfyList.get(0));
			}
			//日评价分析
			List<JSONObject> satisfyList2 = satisfyService.getSatisfyData(startDate, endDate, "day");
			//封装成前端使用的数据结构
			if(satisfyList2 != null && satisfyList2.size()>0) {
				List<String> dateId = new ArrayList<String>();
				List<Integer> smsIvNum = new ArrayList<Integer>();// 短信评价数
				List<Integer> voiceIvNum = new ArrayList<Integer>();// 语音评价数
				for(int i=0;i<dateArr.length;i++) {
					int smsIv = 0;
					int voiceIv = 0;
					for(JSONObject j : satisfyList2) {
						if(dateArr[i].replaceAll("-", "").equals(j.getString("DATE_ID"))) {
							smsIv = j.getIntValue("SMS_EVA_NUM");
							voiceIv = j.getIntValue("VOICE_EVA_NUM");
							break;
						}
					}
					dateId.add(dateArr[i].substring(5));
					smsIvNum.add(smsIv);
					voiceIvNum.add(voiceIv);
				}
				JSONObject ivType = new JSONObject();
				ivType.put("DATE_ID", dateId);
				ivType.put("SMS_IV_NUM", smsIvNum);
				ivType.put("VOICE_IV_NUM", voiceIvNum);
				result.put("ivType", ivType);
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok(result);
	}

	/**
	 * 服务水平分析
	 * @return
	 */
	@InfAuthCheck(resId = "cc-report-fx-voice-sc", msg = "您无权访问!")
	public EasyResult actionForServiceLevelAnalyse() {
		String callinStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALLIN_STAT").get("TARGET_TABLE_NAME");
		String callStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALL_STAT").get("TARGET_TABLE_NAME");
		EasyQuery query = getQueryRead();
		JSONObject param = getJSONObject();
		try {
			EasySQL sql = new EasySQL("select sum(t1.AGENT_ANSWER_COUNT) ANSWER_COUNT, sum(t1.QUEUE_COUNT) ALTERING_COUNT,");
			sql.append("sum(case when t1.CLEAR_CAUSE = 6 and t1.ALERTING_CLASS = 1 then t1.CALL_COUNT else 0 end) ANSWER_COUNT_5,");// 5秒接通数
			sql.append("sum(case when t1.CLEAR_CAUSE = 6 and t1.ALERTING_CLASS = 2 then t1.CALL_COUNT else 0 end) ANSWER_COUNT_10,");// 10秒接通数
			sql.append("sum(case when t1.CLEAR_CAUSE = 6 and t1.ALERTING_CLASS = 3 then t1.CALL_COUNT else 0 end) ANSWER_COUNT_15,");// 15秒接通数
			sql.append("sum(case when t1.CLEAR_CAUSE = 6 and t1.ALERTING_CLASS >=4 then t1.CALL_COUNT else 0 end) ANSWER_COUNT_20,");// 20秒接通数
			//sql.append("sum(CALL_COUNT) CALL_COUNT,sum(QUEUE_COUNT) QUEUE_COUNT,");
			sql.append("sum(case when QUEUE_CLASS = 1 then QUEUE_COUNT else 0 end) QUEUE_COUNT_5,");// 排队<= 5 秒
			sql.append("sum(case when QUEUE_CLASS = 2 then QUEUE_COUNT else 0 end) QUEUE_COUNT_10,");// 排队<= 10 秒
			sql.append("sum(case when QUEUE_CLASS = 3 then QUEUE_COUNT else 0 end) QUEUE_COUNT_15,");// 排队<= 15 秒
			sql.append("sum(case when QUEUE_CLASS = 4 then QUEUE_COUNT else 0 end) QUEUE_COUNT_20,");// 排队<= 20 秒
			sql.append("sum(case when QUEUE_CLASS = 5 then QUEUE_COUNT else 0 end) QUEUE_COUNT_25,");// 排队<= 25 秒
			sql.append("sum(case when QUEUE_CLASS = 6 then QUEUE_COUNT else 0 end) QUEUE_COUNT_30,");// 排队<= 30 秒
			sql.append("sum(case when QUEUE_CLASS = 7 then QUEUE_COUNT else 0 end) QUEUE_COUNT_60,");// 排队<= 60秒
			sql.append("sum(case when QUEUE_CLASS = 8 then QUEUE_COUNT else 0 end) QUEUE_COUNT_120,");// 排队<= 120 秒
			sql.append("sum(case when QUEUE_CLASS = 9 then QUEUE_COUNT else 0 end) QUEUE_COUNT_180,");// 排队<= 180 秒
			sql.append("sum(case when QUEUE_CLASS = 10 then QUEUE_COUNT else 0 end) QUEUE_COUNT_181,");// 排队> 180 秒
			sql.append("sum(case when ALERTING_CLASS = 1 then ALTERING_COUNT else 0 end) ALTERING_COUNT_5,");// 振铃时长<= 5 秒
			sql.append("sum(case when ALERTING_CLASS = 2 then ALTERING_COUNT else 0 end) ALTERING_COUNT_10,");// 振铃时长<= 10 秒
			sql.append("sum(case when ALERTING_CLASS = 3 then ALTERING_COUNT else 0 end) ALTERING_COUNT_15,");// 振铃时长<= 15 秒
			sql.append("sum(case when ALERTING_CLASS = 4 then ALTERING_COUNT else 0 end) ALTERING_COUNT_20,");// 振铃时长<= 20 秒
			sql.append("sum(case when ALERTING_CLASS = 5 then ALTERING_COUNT else 0 end) ALTERING_COUNT_25,");// 振铃时长<= 25 秒
			sql.append("sum(case when ALERTING_CLASS = 6 then ALTERING_COUNT else 0 end) ALTERING_COUNT_30,");// 振铃时长<= 30 秒
			sql.append("sum(case when ALERTING_CLASS = 7 then ALTERING_COUNT else 0 end) ALTERING_COUNT_31");// 振铃时长> 30 秒
			sql.append("from "+callinStat+" t1 where 1=1");
			sql.append(getEntId(),"and ENT_ID = ?");
			//前端查询条件
			if(StringUtils.isNotBlank(param.getString("beginStatDate"))){
				sql.append(CommonUtil.parseInteger(param.getString("beginStatDate").replaceAll("-", "")),"and DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(param.getString("endStatDate"))){
				sql.append(CommonUtil.parseInteger(param.getString("endStatDate").replaceAll("-", "")),"and DATE_ID <= ?");
			}
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 接通率sql: "+sql.getSQL()+" param: "+sql.getParams());
			}
			JSONObject callin = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			
			EasySQL callSql = new EasySQL("select");
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 1 then t1.CALL_COUNT else 0 end) as ACW_LESS_15,");// 话后整理时长<=15秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 2 then t1.CALL_COUNT else 0 end) as ACW_LESS_30,");// 话后整理通话时长<=30秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 3 then t1.CALL_COUNT else 0 end) as ACW_LESS_60,");// 话后整理通话时长<=60秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 4 then t1.CALL_COUNT else 0 end) as ACW_LESS_120,");// 话后整理通话时长<=120秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 5 then t1.CALL_COUNT else 0 end) as ACW_LESS_180,");// 话后整理通话时长<=180秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 6 then t1.CALL_COUNT else 0 end) as ACW_LESS_300,");// 话后整理通话时长<=300秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.ACW_CLASS = 7 then t1.CALL_COUNT else 0 end) as ACW_LESS_301,");// 话后整理通话时长>300秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 1 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_10,");// 通话时长<=10秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 2 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_20,");// 呼入通话时长<=20秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 3 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_30,");// 呼入通话时长<=30秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 4 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_60,");// 呼入通话时长<=60秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 5 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_180,");// 呼入通话时长<=180秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 6 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_300,");// 呼入通话时长<=300秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 7 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_600,");// 呼入通话时长<=600秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 8 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_1800,");// 呼入通话时长<=1800秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 9 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_3600,");// 呼入通话时长<=3600秒数量
			callSql.append("sum(case when t1.CALL_TYPE_ID = 2 and t1.CALLTIME_CLASS = 10 then t1.CALL_COUNT else 0 end) as CALLTIME_LESS_3601");// 呼入通话时长>3600秒数量
			callSql.append("from "+callStat+" t1 where 1=1");
			callSql.append(getEntId(),"and ENT_ID = ?");
			//callSql.append(getBusiOrderId(),"and BUSI_ORDER_ID = ?");
			//前端查询条件
			if(StringUtils.isNotBlank(param.getString("beginStatDate"))){
				callSql.append(CommonUtil.parseInteger(param.getString("beginStatDate").replaceAll("-", "")),"and DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(param.getString("endStatDate"))){
				callSql.append(CommonUtil.parseInteger(param.getString("endStatDate").replaceAll("-", "")),"and DATE_ID <= ?");
			}
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 话后整理和通话时长sql: "+callSql.getSQL()+" param: "+callSql.getParams());
			}
			EasyRow row2 = query.queryForRow(callSql.getSQL(), callSql.getParams());

			
//			String startDate = param.getString("beginStatDate").replaceAll("-", "");
//			String endDate = param.getString("endStatDate").replaceAll("-", "");
//			VoiceServiceLevelService serviceLevel = new VoiceServiceLevelService(getEntId(), getBusiOrderId());
//			List<JSONObject> levelData = serviceLevel.getLevelData(startDate, endDate, "", true, true, false);
//			logger.info("=====================> levelData: "+JSONObject.toJSONString(levelData));
			
			JSONObject result = new JSONObject();
			result.put("callin", callin);
			result.put("callStat", row2.toJSONObject());
			return EasyResult.ok(result);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询坐席话务分析异常："+e.getMessage(),e);
			return EasyResult.fail();
		}
	}
	
	/**
	 * 呼损分析
	 * @return
	 */
	@InfAuthCheck(resId = "cc-report-fx-voice-hs", msg = "您无权访问!")
	public EasyResult actionForCalllossAnalyse() {
		EasyQuery query = getQueryRead();
		String callinStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALLIN_STAT").get("TARGET_TABLE_NAME");
		String noAnswer = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_NOANSWER_STAT").get("TARGET_TABLE_NAME");
		JSONObject param = getJSONObject();
		String beginStatDate = param.getString("beginStatDate");
		String endStatDate = param.getString("endStatDate");
		if(StringUtils.isNotBlank(beginStatDate)){
			beginStatDate = beginStatDate.replaceAll("-", "");
		}
		if(StringUtils.isNotBlank(endStatDate)){
			endStatDate = endStatDate.replaceAll("-", "");
		}
		String ifNull = "IFNULL";
		DBTypes types = query.getTypes();
		if(types == DBTypes.ORACLE || types==DBTypes.DAMENG  ) {
			ifNull = "NVL";
		}else if(types == DBTypes.PostgreSql) {
			ifNull = "COALESCE";
		}
		
		EasySQL commonSql = new EasySQL();
		commonSql.append("sum(t1.CALL_COUNT) CALL_COUNT,");// 接入次数
		commonSql.append("sum(case when t1.CLEAR_CAUSE = 0 then t1.CALL_COUNT else 0 end) CLEAR_CAUSE_0,"); // 未按键挂机
		commonSql.append("sum(case when t1.CLEAR_CAUSE = 1 then t1.CALL_COUNT else 0 end) CLEAR_CAUSE_1,"); // IVR自助服务挂机
		commonSql.append("sum(case when t1.CLEAR_CAUSE = 2 then t1.CALL_COUNT else 0 end) CLEAR_CAUSE_2,"); // 用户排队挂机
		commonSql.append("sum(case when t1.CLEAR_CAUSE = 3 then t1.CALL_COUNT else 0 end) CLEAR_CAUSE_3,"); // 坐席振铃挂机
		commonSql.append("sum(case when t1.CLEAR_CAUSE = 4 then t1.CALL_COUNT else 0 end) CLEAR_CAUSE_4,"); // 转接外线挂机
		commonSql.append(ifNull+"(sum(WORD_COUNT),0) CLEAR_CAUSE_5"); // 留言挂机
		commonSql.append("from "+callinStat+" t1 where 1=1");
		commonSql.append(getEntId(),"and ENT_ID = ?");
		//前端查询条件
		commonSql.append(CommonUtil.parseInteger(beginStatDate),"and DATE_ID >= ?");
		commonSql.append(CommonUtil.parseInteger(endStatDate),"and DATE_ID <= ?");
		try {
			EasySQL sql = new EasySQL("select");
			sql.append(commonSql.getSQL());
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 话呼损分析sql: "+sql.getSQL()+" ,param: "+JSON.toJSONString(commonSql.getParams()));
			}
			JSONObject row1 = query.queryForRow(sql.getSQL(), commonSql.getParams(), new JSONMapperImpl());
			
			JSONObject result = new JSONObject();
			result.putAll(row1);
			
			//呼损总览日分布
			EasySQL sql3 = new EasySQL("select DATE_ID,");
			sql3.append(commonSql.getSQL());
			sql3.append("group by DATE_ID order by DATE_ID");
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 呼损总览日分布sql: "+sql3.getSQL()+" param: "+JSON.toJSONString(sql3.getParams()));
			}
			List<JSONObject> totalAnalyse = query.queryForList(sql3.getSQL(), commonSql.getParams(), new JSONMapperImpl());
			
			//调整数据结构
			List<String> cause0 = new ArrayList<String>();
			List<String> cause1 = new ArrayList<String>();
			List<String> cause2 = new ArrayList<String>();
			List<String> cause3 = new ArrayList<String>();
			List<String> cause4 = new ArrayList<String>();
			List<String> cause5 = new ArrayList<String>();
			List<Integer> total = new ArrayList<Integer>();
			List<String> timeLine = new ArrayList<String>();
			for(JSONObject json : totalAnalyse) {
				timeLine.add(json.getString("DATE_ID"));
				cause0.add(json.getString("CLEAR_CAUSE_0"));
				cause1.add(json.getString("CLEAR_CAUSE_1"));
				cause2.add(json.getString("CLEAR_CAUSE_2"));
				cause3.add(json.getString("CLEAR_CAUSE_3"));
				cause4.add(json.getString("CLEAR_CAUSE_4"));
				cause5.add(json.getString("CLEAR_CAUSE_5"));
				total.add(json.getIntValue("CLEAR_CAUSE_0")+json.getIntValue("CLEAR_CAUSE_1")+json.getIntValue("CLEAR_CAUSE_2")+json.getIntValue("CLEAR_CAUSE_3")+json.getIntValue("CLEAR_CAUSE_4")+json.getIntValue("CLEAR_CAUSE_5"));
			}
			JSONObject dayAnalyse = new JSONObject();
			dayAnalyse.put("timeLine", timeLine);
			dayAnalyse.put("cause0", cause0);
			dayAnalyse.put("cause1", cause1);
			dayAnalyse.put("cause2", cause2);
			dayAnalyse.put("cause3", cause3);
			dayAnalyse.put("cause4", cause4);
			dayAnalyse.put("cause5", cause5);
			dayAnalyse.put("total", total);
			result.put("dayAnalyse", dayAnalyse);
			
			return EasyResult.ok(result);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询呼损分析异常："+e.getMessage(),e);
			return EasyResult.fail();
		}
	}
	
	/**
	 * 坐席话务分析
	 * @return
	 */
	public EasyResult actionForAgentCallAnalyse() {
		return EasyResult.ok();
	}
	
	/**
	 * 部门/坐席话务分析
	 * @return
	 */
	@InfAuthCheck(resId = {"cc-report-fx-voice-agent","cc-report-fx-voice-dept"}, msg = "您无权访问!")
	public EasyResult actionForDeptCallAnalyse() {
		EasyQuery query = getQueryRead();
		JSONObject result = new JSONObject();
		String callinStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALLIN_STAT").get("TARGET_TABLE_NAME");
		String callStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALL_STAT").get("TARGET_TABLE_NAME");
		String noAnswerStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_NOANSWER_STAT").get("TARGET_TABLE_NAME");
		JSONObject param = getJSONObject();
		String ifNull = "IFNULL";
		DBTypes types = query.getTypes();
		if(types == DBTypes.ORACLE || types==DBTypes.DAMENG) {
			ifNull = "NVL";
		}else if(types == DBTypes.PostgreSql) {
			ifNull = "COALESCE";
		}
		

		//用于区分是部门还是坐席
		String type = getJSONObject().getString("type");
		String table = "";
		String joinSql = "";
		String field = "";
		EasySQL commonSql = new EasySQL();
		commonSql.append(getEntId(),"and t2.ENT_ID = ?");
		commonSql.append(getEntId(),"and t1.ENT_ID = ?");
		//前端查询条件
		if(StringUtils.isNotBlank(param.getString("beginStatDate"))){
			String beginStatDate= param.getString("beginStatDate").replaceAll("-", "");
			commonSql.append(CommonUtil.parseInteger(beginStatDate),"and DATE_ID >= ?");
		}
		if(StringUtils.isNotBlank(param.getString("endStatDate"))){
			String endStatDate= param.getString("endStatDate").replaceAll("-", "");
			commonSql.append(CommonUtil.parseInteger(endStatDate),"and DATE_ID <= ?");
		}
		
		if("dept".equals(type)) {
			field = "t2.SKILL_GROUP_NAME";
			table = getTableName("CC_SKILL_GROUP t2");
			//commonSql.append("struct","and t2.SKILL_GROUP_TYPE = ?");
			//commonSql.append("and t1.DEPT_CODE is not null");
			joinSql = "t1.DEPT_CODE = t2.SKILL_GROUP_CODE";
		}else {
			field = "t2.USERNAME";
			table = "CC_USER t2";
			joinSql = "t1.AGENT_ID = t2.USER_ID";
		}
		EasySQL groupSql = new EasySQL();
		groupSql.append("group by "+field);
		groupSql.append("order by NUM desc");
		
		try {
			//呼入排行
			EasySQL callinSql = new EasySQL("select "+field+" GROUP_NAME,sum(t1.CALL_COUNT) NUM from "+ callinStat +" t1");
			callinSql.append("left join "+table+" on "+joinSql+" where 1=1");
			callinSql.append(commonSql.getSQL());
			callinSql.append(groupSql.getSQL());
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + type+"呼入排行,sql="+callinSql.getSQL()+"param= "+JSON.toJSONString(commonSql.getParams()));
			}
			List<JSONObject> callinList = query.queryForList(callinSql.getSQL(), commonSql.getParams(), 1, 10, new JSONMapperImpl());
			result.put("callin", formatCallAnalyse(callinList));
			
			//呼出排行
			EasySQL calloutSql = new EasySQL("select sum(case when t1.CREATE_CAUSE in (6,8) then t1.CALL_COUNT else 0 end) NUM,");
			calloutSql.append(field+" GROUP_NAME from "+callStat+" t1");
			calloutSql.append("left join "+table+" on "+joinSql+" where 1=1");
			calloutSql.append(commonSql.getSQL());
			calloutSql.append(groupSql.getSQL());
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + type+"呼出排行,sql="+groupSql.getSQL()+"param= "+JSON.toJSONString(commonSql.getParams()));
			}
			List<JSONObject> calloutList = query.queryForList(calloutSql.getSQL(), commonSql.getParams(), 1, 10, new JSONMapperImpl());
			result.put("callout", formatCallAnalyse(calloutList));
			
			//未接排行
			EasySQL noAnswerSql = new EasySQL("select sum(NOANSWER_COUNT) NUM,"+field+" GROUP_NAME from");
			if("dept".equals(type)) {
				noAnswerSql.append(noAnswerStat+" t1 left join "+table+" on t1.GROUP_ID = t2.SKILL_GROUP_ID where 1=1");
			}else {
				noAnswerSql.append(noAnswerStat+" t1 left join "+table+" on t1.AGENT_ID = t2.USER_ACCT where 1=1");
			}
			noAnswerSql.append(commonSql.getSQL());
			noAnswerSql.append(groupSql.getSQL());
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + type+"未接排行,sql="+noAnswerSql.getSQL()+"param= "+JSON.toJSONString(commonSql.getParams()));
			}
			List<JSONObject> noAnswerList = query.queryForList(noAnswerSql.getSQL(), commonSql.getParams(), 1, 10, new JSONMapperImpl());
			result.put("noAnswer", formatCallAnalyse(noAnswerList));
			
			//满意度排行
			EasySQL satisfySql = new EasySQL("select "+field+" GROUP_NAME,");
			satisfySql.append("sum(case when SATISF_ID > 0  then CALL_COUNT else 0 end) EVALUATE,");
			satisfySql.append("sum(case when SATISF_ID in (1,2) then CALL_COUNT else 0 end) SATISFY,");
			satisfySql.append("(case when sum(case when SATISF_ID > 0  then CALL_COUNT else 0 end) > 0 then round("+ifNull+"(sum(case when SATISF_ID in (1,2) then CALL_COUNT else 0 end) / sum(case when SATISF_ID > 0  then CALL_COUNT else 0 end)*100,0)) else 0 end) NUM");
			//satisfySql.append("case when count(SATISF_ID) = 0 then 0 else (sum(case when SATISF_ID IN (1, 2) then 1 else 0 end) / count(SATISF_ID)) end NUM");
			//satisfySql.append("case when count(SATISF_ID) = 0 then 0 else concat(round(sum(case when SATISF_ID IN (1, 2) then 1 else 0 end) / count(SATISF_ID)*100),'%') end NUM");
			satisfySql.append("from "+callinStat+" t1 left join "+table+" on "+joinSql+" where 1=1");
			commonSql.append(0,"and SATISF_ID <> ?");
			satisfySql.append(commonSql.getSQL());
			satisfySql.append(groupSql.getSQL());
			if(ServerContext.isDebug()) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + type+"满意度排行,sql="+satisfySql.getSQL()+"param= "+JSON.toJSONString(commonSql.getParams()));
			}
			List<JSONObject> satisfyList = query.queryForList(satisfySql.getSQL(), commonSql.getParams(), 1, 10, new JSONMapperImpl());
			result.put("satisfy", formatCallAnalyse(satisfyList));
		
			return EasyResult.ok(result);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询"+type+"的话务分析异常："+e.getMessage(),e);
			return EasyResult.fail();
		}
	}

	/**
	 * 地区话务分析
	 * @return
	 */
	@InfAuthCheck(resId = "cc-report-fx-voice-area", msg = "您无权访问!")
	public EasyResult actionForAreaCallAnalyse() {
		String callStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALL_STAT").get("TARGET_TABLE_NAME");
		JSONObject param = getJSONObject();
		EasyQuery query = this.getQueryRead();
		List<JSONObject> province = new ArrayList<JSONObject>();
		try {
			EasySQL sql = new EasySQL("select t2.PROVINCE_NAME,");
			sql.append("sum(case when t1.CREATE_CAUSE in (1,2,6,8) then t1.CALL_COUNT else 0 end) CALL_NUM");// 话务量
			sql.append("from "+ callStat + " t1 left join CC_AREA t2");
			sql.append("on t1.AREA_CODE = t2.AREA_CODE where 1=1");
			sql.append(getBusiOrderId(),"and t1.BUSI_ORDER_ID = ?");
			sql.append(getEntId(),"and t1.ENT_ID = ?");
			sql.append("and t2.AREA_CODE is not null");
			//前端查询条件
			if(StringUtils.isNotBlank(param.getString("beginStatDate"))){
				String beginStatDate=param.getString("beginStatDate").replaceAll("-", "");
				sql.append(CommonUtil.parseInteger(beginStatDate),"and DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(param.getString("endStatDate"))){
				String endStatDate=param.getString("endStatDate").replaceAll("-", "");
				sql.append(CommonUtil.parseInteger(endStatDate),"and DATE_ID <= ?");
			}
			sql.append(param.getString("province"),"and t2.PROVINCE_CODE = ?");
			
			sql.append("group by t2.PROVINCE_NAME");
			sql.append("order by CALL_NUM desc");
			province = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 查询地区话务分析异常："+e.getMessage(),e);
			return EasyResult.fail();
		}
		return EasyResult.ok(province);
	}
	

	/**
	 * 获取日期数组
	 * @param beginStatDate 开始日期
	 * @param endStatDate 结束日期
	 * @return
	 */
	private String[] getDateArr(String beginStatDate, String endStatDate) {
		SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.TIME_FORMAT_YMD);
		Date[] dateArr = DateUtil.getDateArrays(beginStatDate, endStatDate, Calendar.DAY_OF_YEAR);
		String[] rtDate = new String [dateArr.length];
		for (int i = 0; i < dateArr.length; i++) {
			rtDate[i] = sdf.format(dateArr[i]);
		}
		return rtDate;
	}
	
	/**
	 * 部门、个人话务分析排行数据处理
	 * @param list
	 */
	private JSONObject formatCallAnalyse(List<JSONObject> list) {
		JSONObject result = new JSONObject();
		if(list != null && list.size()>0) {
			List<String> group = new ArrayList<String>();
			List<String> num = new ArrayList<String>();
			List<String> evaluate = new ArrayList<String>();
			List<String> satisfy = new ArrayList<String>();
			for(JSONObject json : list) {
				group.add(json.getString("GROUP_NAME"));
				num.add(json.getString("NUM"));
				evaluate.add(json.getString("EVALUATE"));
				satisfy.add(json.getString("SATISFY"));
			}
			result.put("group", group);
			result.put("num", num);
			if(StringUtils.isNotBlank(list.get(0).getString("EVALUATE"))) {
				result.put("evaluate", evaluate);
				result.put("satisfy", satisfy);
			}
		}
		return result;
	}
	
	
	
}
