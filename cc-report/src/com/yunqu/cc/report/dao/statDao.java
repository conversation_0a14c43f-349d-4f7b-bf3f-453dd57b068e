package com.yunqu.cc.report.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.report.base.AppDaoContext;
import com.yunqu.cc.report.base.Constants;

/**
 * Title:获取地区话务明细表格
 * Description:
 * <AUTHOR>
 * @version 1.0
 */
@WebObject(name="stat")
public class statDao extends AppDaoContext {
	
	@WebControl(name = "areaDetail", type = Types.LIST)
	public JSONObject getAreaDetail() {
		String callStat = Constants.getStatSchema() + "." + getYcstatTableByTaget("CC_RPT_CALL_STAT").get("TARGET_TABLE_NAME");
		EasySQL sql2 = new EasySQL("select t2.AREA_NAME,");
		sql2.append("sum(case when t1.CREATE_CAUSE in (1,2,6,8) then t1.CALL_COUNT else 0 end) CALL_NUM");// 话务量
		sql2.append("from "+ callStat + " t1 left join CC_AREA t2");
		sql2.append("on t1.AREA_CODE = t2.AREA_CODE where 1=1");
		sql2.append(getBusiOrderId(),"and t1.BUSI_ORDER_ID = ?");
		sql2.append(getEntId(),"and t1.ENT_ID = ?");
		//前端查询条件
		if(StringUtils.isNotBlank(param.getString("beginStatDate"))){
			String beginStatDate=param.getString("beginStatDate").replaceAll("-", "");
			sql2.append(StringUtils.parseInt(beginStatDate),"and DATE_ID >= ?");
		}
		if(StringUtils.isNotBlank(param.getString("endStatDate"))){
			String endStatDate=param.getString("endStatDate").replaceAll("-", "");
			sql2.append(StringUtils.parseInt(endStatDate),"and DATE_ID <= ?");
		}
		sql2.append("and t2.AREA_NAME is not null");
		sql2.append(param.getString("province"),"and t2.PROVINCE_CODE = ?");
		sql2.append("group by t2.AREA_NAME");
		sql2.append("order by CALL_NUM desc");
		return queryForPageList(sql2.getSQL(), sql2.getParams());
	}
	
	@WebControl(name = "getProvince", type = Types.LIST)
	public JSONObject getProvince() {
		EasySQL sql = new EasySQL("select distinct PROVINCE_CODE,PROVINCE_NAME from CC_AREA");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
}