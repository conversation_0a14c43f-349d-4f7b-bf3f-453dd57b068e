package com.yunqu.cc.report.dao.custService;

import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.report.base.CommonLogger;

public class OffMsgStatisticalSql {

	/**
	 * 离线统计报表SQL
	 *
	 * @param startDate
	 * @param endDate
	 * @param channelNo
	 * @return
	 */
	public static EasySQL offMsgStatisticalSql(String startDate, String endDate, String channelNo,String schema, String entId) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (");
		sql.append("SELECT T_.* FROM (");
		sql.append("SELECT T.CHANNEL_NAME,");
		sql.append("(CASE WHEN T.USER_ID IS NULL THEN '未分配' ELSE T.USER_ID END) USER_ID,");
		sql.append("(CASE WHEN T.USERNAME IS NULL THEN '未分配' ELSE T.USERNAME END) USER_NAME,");
		sql.append("SUM(CASE WHEN T.STATE=0 THEN 1 ELSE 0 END) WAIT_DO,");
		sql.append("SUM(CASE WHEN T.STATE=1 THEN 1 ELSE 0 END) DONE,");
		sql.append("SUM(CASE WHEN T.STATE=3 THEN 1 ELSE 0 END) NOT_DO");
		sql.append("FROM " + schema + ".CC_MEDIA_WORD T ");
		sql.append("WHERE 1=1");
		sql.append(entId," AND T.ENT_ID = ? ");
		sql.append(startDate + " 00:00:00", "AND T.WORD_TIME>=?");
		sql.append(endDate + " 23:59:59", "AND T.WORD_TIME<=?");
		if (StringUtils.isNotBlank(channelNo)) {
			channelNo = channelNo.replace("[", "").replace("]", "").replace("\"", "'");
			String str = "AND T.CHANNEL_KEY in (" + channelNo + ") ";
			sql.append(str);
		}
		sql.append("GROUP BY T.CHANNEL_ID,T.CHANNEL_NAME, T.USER_ID,T.USERNAME ORDER BY WAIT_DO");
		sql.append(") T_");
		sql.append("UNION ALL");
		sql.append("SELECT T_.* FROM (");
		sql.append("SELECT '所有' CHANNEL_NAME,");
		sql.append("'所有' USER_ID,");
		sql.append("'所有' USER_NAME,");
		sql.append("SUM(CASE WHEN T.STATE=0 THEN 1 ELSE 0 END) WAIT_DO,");
		sql.append("SUM(CASE WHEN T.STATE=1 THEN 1 ELSE 0 END) DONE,");
		sql.append("SUM(CASE WHEN T.STATE=3 THEN 1 ELSE 0 END) NOT_DO");
		sql.append("FROM " + schema + ".CC_MEDIA_WORD T ");
		sql.append("WHERE 1=1");
		sql.append(entId," AND T.ENT_ID = ? ");
		sql.append(startDate + " 00:00:00", "AND T.WORD_TIME>=?");
		sql.append(endDate + " 23:59:59", "AND T.WORD_TIME<=?");
		if (StringUtils.isNotBlank(channelNo)) {
			channelNo = channelNo.replace("[", "").replace("]", "").replace("\"", "'");
			String str = "AND T.CHANNEL_KEY in (" + channelNo + ") ";
			sql.append(str);
		}
		sql.append(") T_) A");
		CommonLogger.logger.debug("离线统计报表SQL ,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 常用语使用次数统计报表SQL
	 *
	 * @param startDate
	 * @param endDate
	 * @param channelNo
	 * @return
	 */
	public static EasySQL commonUseStatisticalSql(String startDate, String endDate, String channelNo,String schema) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT (CASE WHEN T.CHANNEL_KEY IS NULL THEN '无' ELSE T.CHANNEL_KEY END) CHANNEL_KEY,");
		sql.append("(CASE WHEN T1.NAME IS NULL THEN '无' ELSE T1.NAME END) CHANNEL_NAME,");
		sql.append("T3.CONTENT, COUNT(1) USE_NUM FROM C_CF_USE_LOG  T ");
		sql.append("LEFT JOIN " + schema + ".V_CF_CHANNEL T1 ON T.CHANNEL_KEY = T1.CODE");
		sql.append("JOIN " + schema + ".C_CF_PHRASE T3 ON T.BUSI_ID = T3.ID");
		sql.append("WHERE T.TYPE='01'");
		sql.append(startDate + " 00:00:00", "AND T.CREATE_TIME>=?");
		sql.append(endDate + " 23:59:59", "AND T.CREATE_TIME<=?");
		if (StringUtils.isNotBlank(channelNo)) {
			channelNo = channelNo.replace("[", "").replace("]", "").replace("\"", "'");
			String str = "AND T.CHANNEL_KEY in (" + channelNo + ") ";
			sql.append(str);
		}
		sql.append("GROUP BY T.CHANNEL_KEY,T1.NAME,T3.CONTENT");
		sql.append("ORDER BY CHANNEL_KEY,USE_NUM DESC");

		CommonLogger.logger.debug("常用语使用次数统计报表SQL ,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

}
