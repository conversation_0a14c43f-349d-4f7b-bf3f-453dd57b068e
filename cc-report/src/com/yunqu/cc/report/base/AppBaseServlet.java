package com.yunqu.cc.report.base;

import java.io.PrintWriter;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyModel;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.CEConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.BaseI18nUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yq.busi.common.util.UserUtil;

import com.yunqu.cc.report.model.ExtraDayCallModel;
import com.yunqu.cc.report.model.mapper.ExtraDayCallRowMapper;
import com.yunqu.cc.report.utils.ReportUtils;
import com.yunqu.cc.report.v3.vo.CommonCallModel;
import com.yunqu.cc.report.v3.vo.CommonCallRowMapper;
import com.yunqu.cc.report.v3.vo.CommonMediaThirdMapper;
import com.yunqu.cc.report.v3.vo.CommonMediaThirdModel;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

public abstract class AppBaseServlet extends EasyBaseServlet {

	protected EasyCache cache = CacheManager.getMemcache();

	protected Logger logger = CommonLogger.logger;

	private static final long serialVersionUID = 1L;

	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_WIRTE_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	protected String getTableName(String tableName) {
		String dbName = getDbName();
		if (StringUtils.notBlank(dbName)) {
			return dbName + "." + tableName;
		}
		return tableName;
	}

	@Override
	public EasyQuery getQuery() {
		String entId = getEntId();
		return QueryFactory.getQuery(entId);
	}

	public EasyQuery getQueryRead() {
		return QueryFactory.getQueryRead();
	}

	@SuppressWarnings("unchecked")
	@Override
	protected <T> T getModel(Class<T> modelClass, String modelName) {
		EasyModel bean = null;
		try {
			bean = (EasyModel) modelClass.newInstance();
			bean.setDbName(getDbName());
			bean.setColumns(this.getJSONObject(modelName));
		} catch (InstantiationException | IllegalAccessException e) {
			e.printStackTrace();
		}
		return (T) bean;
	}

	@Override
	protected YCUserPrincipal getUserPrincipal() {
		UserPrincipal userPrincipal = super.getUserPrincipal();
		if(userPrincipal instanceof YCUserPrincipal){
			return (YCUserPrincipal)super.getUserPrincipal();
		}else{
			return (YCUserPrincipal)this.getRequest().getUserPrincipal();
		}
	}

	public String getEntId() {
		UserModel user = UserUtil.getUser(this.getRequest());
		return user.getEpCode();
	}

	public String getDbName() {
		UserModel user = UserUtil.getUser(this.getRequest());
		return user.getSchemaName();
	}

	protected String getBusiOrderId() {
		YCUserPrincipal principal = getUserPrincipal();
		return principal.getBusiOrderId();
	}

	public String getResEntId() {
		YCUserPrincipal principal = getUserPrincipal();
		return principal.getResEntId();
	}

	protected String getBusiId() {
		YCUserPrincipal principal = getUserPrincipal();
		return principal.getBusiId();
	}

	@Override
	protected String getResId() {
		return null;
	}

	protected String getUserName() {
		UserModel user = UserUtil.getUser(this.getRequest());
		return user.getUserName();
	}

	protected String getUserId() {
		UserModel user = UserUtil.getUser(this.getRequest());
		return user.getUserId();
	}

	protected String getCustTempId() {
		YCUserPrincipal principal = getUserPrincipal();
		String custTempId = (String) principal.getAttribute("custTempId");
		if (StringUtils.isBlank(custTempId)) {
			try {
				custTempId = this.getQuery().queryForString(
						"select CUST_TEMP_ID from " + getTableName("CC_SKILL_GROUP t1,")
								+ getTableName("CC_SKILL_GROUP_USER t2")
								+ " where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t2.USER_ID = ?",
						new Object[] { this.getEntId(), this.getBusiOrderId(), this.getUserId() });
				if (StringUtils.isNotBlank(custTempId)) {
					principal.setAttribute("custTempId", custTempId);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return custTempId;
	}

	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}

	/**
	 * 国际化
	 */
	public String getI18nValue(String str){
		return BaseI18nUtil.getI18nValue(getRequest(), Constants.APP_NAME, str);
	}

	/**
	 * 获取配置项公共方法
	 * @param key
	 * @return
	 */
	protected String getBaseParamValue(String key){
		return SystemParamUtil.getEntParam(this.getDbName(), this.getEntId(),  this.getBusiOrderId(), CEConstants.BASE_APP_NAME, key);
	}


	protected boolean checkExportCount(HttpServletRequest request,HttpServletResponse response){
		String msg = "当前导出人数大于并发限制，请稍后再试！";
		String opr = "返回上一级";
		Boolean flag = true;
		try {
			flag = ExportCount.check(this.getDbName(), this.getEntId(),  this.getBusiOrderId());
			if(!flag){
				response.reset();
				response.setContentType("text/html");
				response.setCharacterEncoding("utf-8");
				PrintWriter writer = response.getWriter();
				writer.write(ReportUtils.getHtml(getI18nValue(msg), getI18nValue(opr)));
				writer.flush();
			}
		} catch (Exception e) {
			this.error(e.getMessage(),e);
		}
		return flag;
	}

	protected void decrease(boolean flag) {
		if(flag){
			ExportCount.decrease(this.getDbName(), this.getEntId(),  this.getBusiOrderId());
		}
	}
	/**
	 * 返回查询结果
	 * @param sql 查询语句
	 * @param params  参数
	 * @param commonCallRowMapper  EasyRowMapper的实现，为空时候，缺省为：MapRowMapperImpl
	 * @return
	 */
	protected List<CommonCallModel> queryForSumList(String sql, Object[] params, CommonCallRowMapper commonCallRowMapper)  {
		EasyQuery query = getQueryRead();
		JSONObject resultJson = new JSONObject();
		List<CommonCallModel> list = null;
		try {
			list = query.queryForList(sql, params, commonCallRowMapper);
			list.add(commonCallRowMapper.getSumCallModel(this.getRequest()));
		} catch (SQLException ex) {
			this.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + ex.getMessage(), ex);
			resultJson.put("msg", ex.getMessage());
			resultJson.put("state",0);
			return list;
		}
		return list;
	}


	/**
	 * 返回查询结果
	 * @param sql 查询语句
	 * @param params  参数
	 * @param commonCallRowMapper  EasyRowMapper的实现，为空时候，缺省为：MapRowMapperImpl
	 * @return
	 */
	protected List<ExtraDayCallModel> queryForSumOtherList(String sql, Object[] params, ExtraDayCallRowMapper extraDayCallRowMapper)  {
		EasyQuery query = getQueryRead();
		JSONObject resultJson = new JSONObject();
		List<ExtraDayCallModel> list = null;
		try {
			list = query.queryForList(sql, params, extraDayCallRowMapper);
			list.add(extraDayCallRowMapper.getSumCallModel(this.getRequest()));
		} catch (SQLException ex) {
			this.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + ex.getMessage(), ex);
			resultJson.put("msg", ex.getMessage());
			resultJson.put("state",0);
			return list;
		}
		return list;
	}

	/**
	 * 返回查询结果
	 * @param sql 查询语句
	 * @param params  参数
	 * @param CommonMediaThirdMapper  EasyRowMapper的实现，为空时候，缺省为：MapRowMapperImpl
	 * @return
	 */
	protected List<CommonMediaThirdModel> queryForSumThirdist(String sql, Object[] params, CommonMediaThirdMapper commonMediaThirdMapper)  {
		EasyQuery query = getQueryRead();
		JSONObject resultJson = new JSONObject();
		List<CommonMediaThirdModel> list = null;
		try {
			list = query.queryForList(sql, params, commonMediaThirdMapper);
			list.add(commonMediaThirdMapper.getSumCallModel(this.getRequest()));
		} catch (SQLException ex) {
			this.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + ex.getMessage(), ex);
			resultJson.put("msg", ex.getMessage());
			resultJson.put("state",0);
			return list;
		}
		return list;
	}

	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME FROM " + getStatTableName("CC_STAT_TABLE_INFO") + " WHERE TABLE_ID = ?  ";
			tabInfo =QueryFactory.getReadQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
		} catch (Exception ex) {
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "获取最新统计库信息失败：" + ex.getMessage(),ex);
		}
		//设置默认的统计表
		if(tabInfo == null){
			tabInfo = new HashMap<>();
			tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
			tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		}
		return tabInfo;
	}
}
