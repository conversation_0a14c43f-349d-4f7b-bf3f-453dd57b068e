jQuery.namespace("satisfAnalyse");

satisfAnalyse.initData = function() {
	var param = getParam();
	if(!param){
		return;
	}
	ajax.remoteCall("/cc-report/servlet/mediaStatServlet?action=satisfAnalyse",param,function(result) { 
		if(result.state == 1){
			var data  = result.data;
			
			if(data.circle){
				//参评率
				var option1 = JSON.parse(JSON.stringify(satisfAnalyse.circleOption));
				option1.title.text = getRate(data.circle.EVAL_NUM,data.circle.TOTAL);
				option1.title.subtext = getI18nValue("参评率");
				option1.series[0].data = [data.circle.EVAL_NUM,data.circle.TOTAL];
				circleChart1.setOption(option1);
				//满意率
				var option2 = JSON.parse(JSON.stringify(satisfAnalyse.circleOption));
				option2.title.text = getRate(data.circle.SATIS_NUM,data.circle.EVAL_NUM);
				option2.title.subtext = getI18nValue("满意率");
				option2.series[0].data = [data.circle.SATIS_NUM,data.circle.EVAL_NUM];
				circleChart2.setOption(option2);
				//坐席邀评率
				var option3 = JSON.parse(JSON.stringify(satisfAnalyse.circleOption));
				option3.title.text = getRate(data.circle.IV_EVAL_NUM,data.circle.TOTAL);
				option3.title.subtext = getI18nValue("坐席邀评率");
				option3.series[0].data = [data.circle.IV_EVAL_NUM,data.circle.TOTAL];
				circleChart3.setOption(option3);
				//客户主动评价率
				var option4 = JSON.parse(JSON.stringify(satisfAnalyse.circleOption));
				option4.title.text = getRate(data.circle.CUST_ASK_EVAL,data.circle.TOTAL);
				//option4.title.text = getRate(data.circle.EVAL_NUM-data.circle.IV_EVAL_NUM,data.circle.EVAL_NUM);
				option4.title.subtext = getI18nValue("客户主动评价率");
				//option4.series[0].data = [data.circle.EVAL_NUM-data.circle.IV_EVAL_NUM,data.circle.EVAL_NUM];
				option4.series[0].data = [data.circle.CUST_ASK_EVAL,data.circle.TOTAL];
				circleChart4.setOption(option4);
			}else{
				circleChart1.setOption(satisfAnalyse.circleOption);
				circleChart2.setOption(satisfAnalyse.circleOption);
				circleChart3.setOption(satisfAnalyse.circleOption);
				circleChart4.setOption(satisfAnalyse.circleOption);
			}
			if(data.dayEval){
				var option = formatDateOption(satisfAnalyse.dayOption);
				option.title.text = getI18nValue('日评价分析');
				option.xAxis.data = data.dayEval.DATE_ID;
				option.series[0].data = data.dayEval.EVAL_NUM;
				option.series[1].data = data.dayEval.SATIS_NUM;
				option.series[2].data = data.dayEval.SATIS_RATE;
				dayChart.setOption(option);
			}else{
				dayChart.setOption(satisfAnalyse.dayOption);
			}
			if(data.indexEval){
				var option = JSON.parse(JSON.stringify(satisfAnalyse.indexOption));
				option.xAxis.data = [getI18nValue('非常满意'),getI18nValue('满意'),getI18nValue('一般'),getI18nValue('不满意')];
				option.series[0].data = [data.indexEval.VERY_SATIS_NUM,data.indexEval.SATIS_NUM,data.indexEval.USUAL_NUM,data.indexEval.UNSATIS_NUM];
				indexChart.setOption(option);
			}else{
				indexChart.setOption(satisfAnalyse.indexOption);
			}
			if(data.channelEval){
				var option = JSON.parse(JSON.stringify(satisfAnalyse.dayOption));
				option.title.text = getI18nValue('渠道评价排行');
				option.xAxis.data = data.channelEval.CHANNEL_NAME;
				option.series[0].data = data.channelEval.EVAL_NUM;
				option.series[1].data = data.channelEval.SATIS_NUM;
				option.series[2].data = data.channelEval.SATIS_RATE;
				channelChart.setOption(option);
			}else{
				channelChart.setOption(satisfAnalyse.dayOption);
			}
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	}); 
}

//圈
satisfAnalyse.circleOption = {
	title: {
		text: '',
		subtext: "",
		x: 'center',
		y: 'center',
		textStyle: {
			color: '#5f6988',
			fontSize: 28,
		}
	},
	color:['#76B1F7','#FD8992','#fe4365','#f9cdad','#c8c8a9','#83af9b'],
	series: [{
		type: 'pie',
		radius: ['50%', '60%'],
		silent: true,
		label: {
			normal: {
				show: false,
			}
		},
		data:[],
		animation: false
	}]
}

//日评价分析
satisfAnalyse.dayOption = {
    title: {
        text: ''
    },
    tooltip: {
        trigger: 'axis'
    },
    legend: {
        data: [getI18nValue('参评数'), getI18nValue('满意数'), getI18nValue('满意率(%)')]
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    toolbox: {
        feature: {
            saveAsImage: {}
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: true,
        data: []
    },
    yAxis: [
		{
    		name: '',
			type: 'value'
	    },
    	{
	    	name: getI18nValue('满意率(%)'),
    		type: 'value',
    		max: 100,
    		min: 0,
    		splitLine: {
    			show: false
	        }
    	}
	],
    series: [
        {
        	name: getI18nValue('参评数'),
        	type: 'bar',
        	barWidth : 30,
        	yAxisIndex: 0,
        	data: []
        },
        {
            name: getI18nValue('满意数'),
            type: 'bar',
            barWidth : 30,
            yAxisIndex: 0,
            data: []
        },
        {
        	name: getI18nValue('满意率(%)'),
        	type: 'line',
        	yAxisIndex: 1,
        	data: []
        },
    ]
};

//指标分析
satisfAnalyse.indexOption = {
    title: {
        text: getI18nValue('指标评价分析')
    },
    tooltip: {
        trigger: 'axis'
    },
    legend: {
        data: [getI18nValue('评价次数')]
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    toolbox: {
        feature: {
            saveAsImage: {}
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: true,
        data: []
    },
    yAxis: {
		type: 'value'
    },
    series: [
        {
            name: getI18nValue('评价次数'),
            type: 'bar',
            barWidth : 30,
            data: []
        }
    ]
};


var circleChart1 = echarts.init(document.getElementById("circle_1"));
var circleChart2 = echarts.init(document.getElementById("circle_2"));
var circleChart3 = echarts.init(document.getElementById("circle_3"));
var circleChart4 = echarts.init(document.getElementById("circle_4"));
var dayChart = echarts.init(document.getElementById("dayChart"));
var indexChart = echarts.init(document.getElementById("indexChart"));
var channelChart = echarts.init(document.getElementById("channelChart"));
$(function() {
	satisfAnalyse.initData();
});