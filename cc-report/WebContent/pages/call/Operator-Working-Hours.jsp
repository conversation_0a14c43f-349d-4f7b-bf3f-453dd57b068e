<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>

<EasyTag:override name="head">
	<title>话务员工作时长统计</title>
</EasyTag:override>
<!--条件查询 -->
<EasyTag:override name="content">
	<form action="" method="post" name="searchForm" class="form-inline"
		id="searchForm" onsubmit="return false" data-toggle="">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						 话务员工作时长统计
					</h5>
					<div class="input-group input-group-sm pull-right btn-group">
                        <button type="button" class="btn btn-sm btn-success btn-outline"  onclick="RingTime.exportAgentCallStat()"><i class="glyphicon glyphicon-export"></i>
                          		导 出
                        </button>
                    </div>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group">

					<div class="input-group input-group-sm">
						<span class="input-group-addon">开始时间</span> <input type="text"
							name="startDate" id="startDate" data-mars-top="true"
							data-mars="RingTime.todayFirstTime" class="form-control input-sm"
							autocomplete="off" size="18"
							onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})"> <span
							class="input-group-addon">结束时间</span> <input type="text"
							name="endDate" id="endDate" data-mars-top="true"
							data-mars="RingTime.todayEndTime" class="form-control input-sm"
							autocomplete="off" size="18"
							onClick="WdatePicker({dateFmt: 'yyyy-MM-dd '})">
					</div>
					
					<div class="input-group input-group-sm">
						<span class="input-group-addon">快捷方式</span> <select
							class="form-control" id="time" name="time">
							<option value="00" selected="selected">请选择</option>
							<option value="01" id="today">今天</option>
							<option value="02">昨天</option>
							<option value="03">上周</option>
							<option value="04">上月</option>
							<option value="05">上年</option>
						</select>
					</div>
					
					<script type="text/javascript">
					
					//日期
					$("#time").change(function(){
									//获取状态
									var time=$(this).children('option:selected').val();
									
								switch (time) {
								    case '01':
								    	$("#startDate").val(showToday());
										$("#endDate").val(showToday());
								        break;
								    case '02':
								    	$("#startDate").val(showYesterday());
										$("#endDate").val(showYesterday());
								         break;
								    case '03':
								    	$("#startDate").val(showLastWeekFirstDay());
										$("#endDate").val(showLastWeekLastDay());
								         break;
								    case '04':
								    	$("#startDate").val(showLastMonthFirstDay());
										$("#endDate").val(showLastMonthLastDay());
								         break;
								    case '05':
								    	$("#startDate").val(showLastYearFirstDay());
							             $("#endDate").val(showLastYearLastDay());
										break;
								} 
								
								 //获取今年第一天
							     function showThisYearFirstDay(){
							        var date = new Date();
							        return date.getFullYear()+"-01-01";
							     }
							    //获取今年最后一天
							     function showThisYearLastDay(){
							        var date = new Date();
							        return date.getFullYear()+"-12-31";
							     }
							     //获取去年第一天
							     function showLastYearFirstDay(){
							        var date = new Date();
							        return date.getFullYear()-1+"-01-01";
							     }
							    //获取去年最后一天
							     function showLastYearLastDay(){
							        var date = new Date();
							        return date.getFullYear()-1+"-12-31";
							     }

							     //获取今天
							     function showToday(){
							      var date = new Date();
							      var month=date.getMonth()+1;
							      if (month < 10) {
							          month = '0' + month;
							        }
							      if(date.getDate()<10){
							    	  return date.getFullYear()+"-"+month+"-0"+date.getDate();
							      }else{
							    	  return date.getFullYear()+"-"+month+"-"+date.getDate(); 
							      }
							     }

							     //获取昨天
							     function showYesterday(){
							      var date=new Date();     
							      date.setDate(date.getDate()-1);     
							      month=Number(date.getMonth())+1
							      if (month < 10) {
							          month = '0' + month
							        }
							      if(date.getDate()<10){
							    	  return date.getFullYear()+"-"+month+"-0"+date.getDate();  
							      }else{
							    	  return date.getFullYear()+"-"+month+"-"+date.getDate();  
							      }
							      
							     }

							    //获取明天
							     function showTomorrow()
							    {     
							    var date=new Date();     
							    date.setDate(date.getDate()+1);     
							    M=Number(date.getMonth())+1     
							    return date.getFullYear()+"-"+M+"-"+date.getDate();     
							    }

							     //上周一第一天
							     function showLastWeekFirstDay(){
							         var startDateTime = '';
							         var myDate = new Date(new Date().getTime() - 7 * 24 * 3600 * 1000);
							         var day = myDate.getDay();
							         var time = myDate.getDate() - day + (day === 0 ? -6 : 1);
							         var startTime = new Date(myDate.setDate(time));
							         var month=startTime.getMonth() + 1;
							         if(month<10){
							        	 month="0"+month;
							         }
							         startDateTime =startTime.getFullYear() +'-' +month+'-' +startTime.getDate();
							         return startDateTime;
							     }

							     //上周一第最后一天
							     function showLastWeekLastDay(){
							         var startDateTime = '';
							         var myDate = new Date(new Date().getTime() - 7 * 24 * 3600 * 1000);
							         var day = myDate.getDay();
							         var time = myDate.getDate() - day + (day === 0 ? -6 : 1);
							         var endTime = new Date(myDate.setDate(time + 6));
							         var month=endTime.getMonth() + 1;
							         if(month<10){
							        	 month="0"+month;
							         }
							         if(endTime.getDate()<10){
							        	 endDateTime =endTime.getFullYear() +'-' +month +'-0' +endTime.getDate();
							         }else{
							        	 endDateTime =endTime.getFullYear() +'-' +month +'-' +endTime.getDate();
							         }
							         return endDateTime;
							     }

							    //本周第一天
							   function showWeekFirstDay(){     
							    var Nowdate=new Date();     
							    var WeekFirstDay=new Date(Nowdate-(Nowdate.getDay()-1)*86400000);     
							    M=Number(WeekFirstDay.getMonth())+1
							    if(M<10){
							    	M="0"+M
							    }
							    if(WeekFirstDay.getDate()<10){
							    	return WeekFirstDay.getFullYear()+"-"+M+"-0"+WeekFirstDay.getDate();
							    }else{
							    	return WeekFirstDay.getFullYear()+"-"+M+"-"+WeekFirstDay.getDate();
							    }
							    
							  }
							    //本周最后天
							     function showWeekLastDay(){     
							    var Nowdate=new Date();     
							    var WeekFirstDay=new Date(Nowdate-(Nowdate.getDay()-1)*86400000);     
							    var WeekLastDay=new Date((WeekFirstDay/1000+6*86400)*1000);     
							    M=Number(WeekLastDay.getMonth())+1
							    if(M<10){
							    	M="0"+M
							    }
							    var day = Number(WeekLastDay.getDate());
							    if(day<10){
							    	day="0"+day;
							    }
							    //return WeekLastDay.getFullYear()+"-"+M+"-"+WeekLastDay.getDate(); 
							    return WeekLastDay.getFullYear()+"-"+M+"-"+day;
							  }
							  //本月第一天
							  function showMonthFirstDay(){     
							    var Nowdate=new Date();     
							    var MonthFirstDay=new Date(Nowdate.getYear(),Nowdate.getMonth(),1);     
							    M=Number(MonthFirstDay.getMonth())+1;
							    if(M<10){
							    	M="0"+M;
							    }
							    return Nowdate.getFullYear()+"-"+M+"-0"+MonthFirstDay.getDate();     
							}
							//本月最后一天
							function showMonthLastDay(){     
							    var Nowdate=new Date();     
							    var MonthNextFirstDay=new Date(Nowdate.getYear(),Nowdate.getMonth()+1,1);     
							    var MonthLastDay=new Date(MonthNextFirstDay-86400000);     
							    M=Number(MonthLastDay.getMonth())+1;
							    if(M<10){
							    	M="0"+M;
							    }
							    return Nowdate.getFullYear()+"-"+M+"-"+MonthLastDay.getDate();     
							}

							 // 获取上个月时间
							  function showLastMonthFirstDay(){     
							        var nowdays = new Date()
							        var year = nowdays.getFullYear()
							        var month = nowdays.getMonth()
							        if (month === 0) {
							          month = 12
							          year = year - 1
							        }
							        if (month < 10) {
							          month = '0' + month
							        }
							        //var yDate = new Date(year, month, 0)
							 
							        return startDateTime = year + '-' + month + '-01' //上个月第一天   
							}
							//获取上月最后一天
							function showLastMonthLastDay(){     
							     var nowdays = new Date()
							        var year = nowdays.getFullYear()
							        var month = nowdays.getMonth()
							        if (month === 0) {
							          month = 12
							          year = year - 1
							        }
							        if (month < 10) {
							          month = '0' + month
							        }
							        var yDate = new Date(year, month, 0)
							        return endDateTime = year + '-' + month + '-' + yDate.getDate() //上个月最后一天   
							}
								
								
							})
					</script>

					<div class="input-group input-group-sm" id="agentDiv">
        		         <span class="input-group-addon">坐席</span>
        		         <select name="agentId" id="agentId" data-mars="common.userDictByName" data-mars-top="true" multiple="multiple" size="1">
        		         </select>
				    </div>

					<!-- 搜索按钮 -->
					<div class="input-group input-group-sm  mr-10">
						<button type="button" class="btn btn-sm btn-default"
							onclick="RingTime.search()">
							<span class="glyphicon glyphicon-search"></span> 搜索
						</button>
					</div>

				</div>




			</div>

			<!-- 表格数据 -->
			<div class="ibox-content">
				<table id="dataList" lay-filter="test">
				</table>
			</div>
			
			<div class="ibox-content table-responsive">
		                <table id="main" class="layui-hide" style="width: 100%;">
		                </table>
		    </div>
			
		</div>
	</form>
</EasyTag:override>



<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("RingTime");

		requreLib.setplugs("layui", function() {
			$("#searchForm").render({
				success : function() {
					requreLib.setplugs('multiselect',function(){
						$("#agentId").multiselect({
							 enableFiltering: true,
							 maxHeight: 400,
							 includeSelectAllOption: true,
							 selectAllText:'全选',
							 nonSelectedText: '请选择'
						});
					});
					RingTime.search();
				}
			});
		});

		RingTime.search = function() {
			$("#searchForm").initTableEx(
					
							{
								//mars : 'RingTime.search'
								url:'${ctxPath}/webcall?action=OperWorkingHoursDao.search',
								page:true,
								loading:true,
								limit : 10,
								cols : [[
									{
										field: 'AGENT_ID',
										title: '坐席工号',
										align: 'center'
									},
									{
										field: 'USERNAME',
										title: '坐席姓名',
										align: 'center'
									},
								{
									field: 'ONLINE_TIME',
									title: '登录时长',
									align: 'center'
								},
								{
									field: 'SERVICE_TIME',
									title: '通话时长',
									align: 'center'
								},
								{
									field: 'NOTREADY_TIME',
									title: '示忙时长',
									align: 'center'
								},
								{
									field: 'WORK_NOTREADY_TIME',
									title: '后处理时长',
									align: 'center',
								},
								{
									field: 'INBOUND_TIME',
									title: '呼入模式时长',
									align: 'center'
								},
								{
									field: 'OUTBOUND_TIME',
									title: '呼出模式时长',
									align: 'center'
								}
								]]
								
							});
		}

		
				     
		
		//坐席工号
	/* 	$(function(){
			$("#searchForm").render({success:function(){
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect({
						 enableFiltering: true,
						 maxHeight: 400,
						 includeSelectAllOption: true,
						 selectAllText:'全选',
						 nonSelectedText: '请选择'
					});
				});
			}});
		}); */
		
		//导出
		RingTime.exportAgentCallStat = function () {
            layer.confirm('是否导出话务员工作时长统计报表？', {icon: 3, title: '导出提示', offset: '20px'}, function (index) {
                layer.close(index);
               
                var agentId = "";
				var data = form.getJSONObject("#searchForm");
				if(data.agentId){
					agentId = JSON.stringify(data.agentId);
				}

                location.href = "${ctxPath}/servlet/OperWorkHoursServlet?action=exportRepotList&" + $("#searchForm").serialize()+"&agentIds="+agentId;
            });
        } 
		
	</script>




</EasyTag:override>


<%@ include file="/pages/common/layout_list.jsp"%>