<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>整体接入分析</title>
	<style type="text/css">
		.shadow {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
			-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
		}

		#sub {
			transform: translate(7px, 2px);
			display: inline-block;
		}
		.layui-form-checkbox i{
			height:30px;
		}
		.dropdown-menu {
			min-width: auto;

		}
		.dropdown-menu:hover{
        cursor: default;
   		 }
   		 .dropdown-menu {
			min-width: auto;

		}
		.dropdown-menu:hover{
        cursor: default;
   		 }
   		  .btn-outline{
   		 	margin-left:10px;
   		 }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline shadow" id="searchForm" data-page-hide='true'>
             	<div class="ibox">
             		<div class="ibox-title clearfix" id="divId">
			
				<div class="form-group">
				  <h5><span i18n-content="整体接入分析"></span> <span id="titleAndTime"> </span><span id="sub1" ><i class="layui-icon layui-icon-about" style="color: #1E9FFF;"></i></span></h5>
				<div class="input-group input-group-sm layui-form">
						   <input type="checkbox" name="stMethod" id="stMethod" i18n-title="汇总" value="01" lay-filter="stMethod"/>
					</div>
				<div class="input-group input-group-sm">
						<select
							class="form-control" id="dimension" name="dimension" >
							<option value="00" selected="selected" i18n-content="请选择"></option>
							<option value="01" i18n-content="按渠道"></option>
							<option value="02" i18n-content="按技能组"></option>
							<option value="03"  i18n-content="按渠道技能组"></option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="日期"></span>
						<input type="text" class="form-control input-sm" id="startDate" name="startDate" style="width:100px"
							 data-mars-reload="false" data-mars-top='true' autocomplete="off">
						<span class="input-group-addon">-</span>
						<input type="text" class="form-control input-sm" id="endDate" name="endDate" style="width:100px"
							data-mars-reload="false" data-mars-top='true' autocomplete="off">
							<span class="input-group-addon">-</span>
		                    <select class="form-control input-sm" name="dateRange"
									onchange="onCasecade($(this))">
									<option value="" i18n-content="请选择"></option>
									<option value="today" i18n-content="今天"></option>
									<option value="yesterday" i18n-content="昨天"></option>
									<option value="lastWeek" i18n-content="上周"></option>
									<option value="lastMonth" i18n-content="上月"></option>
									<!-- <option value="lastYear" i18n-content="去年"></option> -->
							</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" i18n-content="统计类型"></span> <select
							class="form-control" id="stType" data-mars="common.getDict('REPORT_STAT_TYPE')" name="stType">
						</select>
					</div>
					<div class="input-group input-group-sm" >
						<span class="input-group-addon" i18n-content="渠道"></span> <select
							class="form-control" id="channelNo" name="channelNo"
							data-mars="custService.queryChannel" multiple="multiple">
						</select>
					</div>
					<div class="input-group input-group-sm" >
						<span class="input-group-addon" i18n-content="技能组"></span> <select
							class="form-control" id="skillNo" name="skillNo"
							data-mars="custService.querySkill" multiple="multiple">
						</select>
					</div>


					<div class="input-group input-group-sm">
						<button type="button" class="btn btn-sm btn-default"
							onclick="assessAgentDemo.searchData('1')" id="query">
							<span class="glyphicon glyphicon-search"></span><span i18n-content="查询"></span>
						</button>
					</div>
					<div class="input-group ">
						<button type="button" class="btn btn-sm btn-default" onclick="assessAgentDemo.reset()"><span class="glyphicon glyphicon-repeat" i18n-content="重置"></span> </button>
					</div>
					<div class="input-group input-group-sm pull-right">
	      							<div class="dropdown-anchor"></div>
	      							<EasyTag:res resId="cc-media-report-permission-online"><button type="button" class="btn btn-sm btn-info btn-outline"  i18n-title="导出" onclick="assessAgentDemo.exportOnLine()" i18n-content="直接导出" id="export"></button></EasyTag:res>
							        <EasyTag:res resId="cc-media-report-permission-offline"><button type="button" class="btn btn-sm btn-info btn-outline"  i18n-title="离线导出" onclick="assessAgentDemo.exportOffLine()" i18n-content="离线导出"></button></EasyTag:res>
						</div>
					

				</div>
			</div>
	              	<div class="ibox-content table-responsive">

						<table id="tree-table"></table>

	              	<div class="stat-desc" id="statDesc" style="display:none;color:#676A6C;">
						<p>1 、接入量：           请求接入平台量，只要输入了导航按键就算，包含进机器人、人工、转接、会话激活数; </p>
						<p>2 、进机器人量：       只要进入了机器人就算，包含后面转人工和未转人工的;                             </p>
						<p>3 、机器人受理占比:    进机器人量/接入量;                                                            </p>
						<p>4 、机器人解决量：     进入机器人后直接结束的会话量，未转人工;                                       </p>
						<p>5 、机器人解决占比:    机器人解决量/接入量;                                                          </p>
						<p>6 、请求人工量:        包含所有要求转人工的，包含排队结束、排队时主动结束、排队后进留言、进人工的;   </p>
						<p>7 、请求人工占比:      请求人工量/接入量;                                                            </p>
						<p>8 、排队量：	       进排队量;                                                                        </p>
						<p>9 、排队超时量：       排队超时结束量，进入排队后;                                                   </p>
						<p>10、排队主动放弃量:    排队放弃量，排队过程中主动放弃进入人工量;                                     </p>
						<p>11、排队占比:	       排队量/接入量;                                                               </p>
						<p>12、留言量：           进留言量，包含非工作时间进留言，排队超时进留言;                               </p>
						<p>13、留言占比:          留言量/接入量;                                                                </p>
						<p>14、进人工量：         接通量,请求人工成功量;                                                        </p>
						<p>15、进人工占比:        进人工量/接入量;                                                              </p>
						<p>16、机器人处理总时长： 机器人处理总时长;             		                                        </p>
						<p>17、平均机器人处理时长:机器人处理总时长/机器人解决量;                                                </p>
						<p>18、排队总时长：       排队总时长;                                                                   </p>
						<p>19、平均排队时长:      排队总时长/排队量;                                                            </p>
						<p>20、服务总时长：       进人工会话的总时长;                                                           </p>
						<p>21、平均服务时长:      服务总时长/进人工量;                                                          </p>
						<p>22、会话量：           会话记录的数量;                                                               </p>
						<p>23、有效会话量:        接入人工接通且客户、客服都有交流;                                             </p>
						<p>24、有效会话率:        有效会话量/会话数;                                                            </p>
						<p>25、客服无回复会话量： 客户成功进入人工客服且发送了内容，而客服没有进行回复会话量;                   </p>
						<p>26、客户无回复会话量： 客户成功进入人工客服后未发送任何内容的会话量;                                 </p>
						<p>27、无交互会话量：     客户成功进入人工客服后，客户、客服均未发送任何内容;                           </p>
						<p>28、客服接待率：      （会话量-无交互量-客服无回复会话量）/（会话量-无交互量）*100%;                 </p>
	              	</div>
                </div>
       		</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script type="text/javascript" src = "${ctxPath}/static/js/reportCommon.js?v=20210819"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/time.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/tableCommon.js?v=20220722"></script>
	<script type="text/javascript">
		jQuery.namespace("assessAgentDemo");
		var offCheck;

		$(function(){
			 assessAgentDemo.getOffCheck();
		})
		assessAgentDemo.getOffCheck = function(){
				ajax.remoteCall("${ctxPath}/webcall?action=common.getOffCheck",{},function(result) {
					if(result.state == 1){
						offCheck = result.data;
					}
				});
			}

		$.views.converters("reqCount", function(total,transOutNum) {
			return parseInt(total) - parseInt(transOutNum);
		});
		//搜索
		assessAgentDemo.searchData=function(flag){
			$("#query").attr("disabled",true);
			 setTimeout(countDown,5000);
				if(flag=='1'){
					$("#searchForm").queryData({id:'tree-table',page:{curr:1}});
				}else{
					$("#searchForm").queryData({id:'tree-table'});
				}
			}

		//重置
		assessAgentDemo.reset=function(){
				$("#divId select").val("");
		    	$("#divId input").val("");
		    	cancelCheck("stMethod");
		    	$("#stMethod").val("01");
		    	$("#dimension").val("00");
		    	$("#stType").val("01");
		    	var todayDate = getTodayDate();
		    	$("#startDate").val(todayDate);
		    	$("#endDate").val(todayDate);
		    	//复选框重置
				$("#channelNo").multiselect("destroy");
				$("#skillNo").multiselect("destroy");
        		requreLib.setplugs('multiselect',function(){
					$("#channelNo").multiselect({
						 enableFiltering: true,
						 maxHeight: 200,
						 includeSelectAllOption: true,
						 selectAllText:getI18nValue('全选'),
						 nonSelectedText: getI18nValue('请选择')
					});
				});
        		requreLib.setplugs('multiselect',function(){
					$("#skillNo").multiselect({
						 enableFiltering: true,
						 maxHeight: 200,
						 includeSelectAllOption: true,
						 selectAllText:getI18nValue('全选'),
						 nonSelectedText: getI18nValue('请选择')
					});
				});
			};


		   requreLib.setplugs("layui",function(){
			   var todayDate = getTodayDate();

			   $("#startDate").val(todayDate);
		       $("#endDate").val(todayDate);

		        	$("#searchForm").render({success:function(){
		        		requreLib.setplugs('multiselect',function(){
							$("#channelNo").multiselect({
								 enableFiltering: true,
								 maxHeight: 200,
								 includeSelectAllOption: true,
								 selectAllText:getI18nValue('全选'),
								 nonSelectedText: getI18nValue('请选择')
							});
						});
		        		requreLib.setplugs('multiselect',function(){
							$("#skillNo").multiselect({
								 enableFiltering: true,
								 maxHeight: 200,
								 includeSelectAllOption: true,
								 selectAllText:getI18nValue('全选'),
								 nonSelectedText: getI18nValue('请选择')
							});
						});
		        		//加载时间控件
						layui.use('laydate', function(){
					  		var laydate = layui.laydate;
					  		laydate.render({ elem: '#startDate' ,type: 'date',lang:getDateLang()});
					  		laydate.render({ elem: '#endDate' ,type: 'date',lang:getDateLang()});
						});
		        	}});
		        	assessAgentDemo.loadData();
		        });


		   assessAgentDemo.loadData = function(){
				$("#searchForm").initTableEx({
					url:'${ctxPath}/webcall?action=mediaReport.entAccess'
					,id:'tree-table'
					,limit:15
					,limits:[15,25,50,100,200]
					,height:comHeight-120 //具体高度页面自定义
					,page:true
					,title:'整体接入分析'
					,cellMinWidth:60
					,loading:true
					,cols: [
						[
							{minWidth:100,align:'center',title: '序号',fixed:'left',type:'numbers'},
							{minWidth:140,align:'center',field:'ST_DATE',fixed:'left', title: '时间',templet:function(row){
								if(row.dateId == "日期"	){
									return getI18nValue(row.dateId);
								}
								return row.dateId
							}},
							{minWidth:120,align:'center',field:'CHANNEL_NAME' ,fixed:'left', title: '渠道',templet:function(row){
								if(row.channelName == "所有"	){
									return getI18nValue(row.channelName);
								}
								return row.channelName;
								}
							},
							{minWidth:100,align:'center',field:'SKILL_NAME' ,fixed:'left', title: '技能组',templet:function(row){
								if(row.groupName == "所有"	){
									return getI18nValue(row.groupName);
								}
								return row.groupName;
								}
							},
							{minWidth:100,align:'center',field:'inCount' ,sort:true, title: '接入量'},
							{minWidth:120,align:'center',field:'inRobotCount' ,sort:true, title: '进机器人量'},
							{minWidth:140,align:'center',field:'inRobotRate' ,sort:true, title: '机器人受理占比'},
							{minWidth:130,align:'center',field:'robotDoneCount' ,sort:true, title: '机器人解决量'},
							{minWidth:140,align:'center',field:'robotDoneRate' ,sort:true, title: '机器人解决占比'},
							{minWidth:120,align:'center',field:'reqAgentCount' ,sort:true, title: '请求人工量'},
							{minWidth:140,align:'center',field:'reqAgentRate' ,sort:true, title: '请求人工占比'},
							{minWidth:100,align:'center',field:'inQueueCount' ,sort:true, title: '排队量'},
							{minWidth:120,align:'center',field:'queueOutTimeCount' ,sort:true, title: '排队超时量'},
							{minWidth:150,align:'center',field:'queueGiveUpCount' ,sort:true, title: '排队主动放弃量'},
							{minWidth:120,align:'center',field:'inQueueRate' ,sort:true, title: '排队占比'},
							{minWidth:100,align:'center',field:'inWordCount' ,sort:true, title: '留言量'},
                            {minWidth:100,align:'center',field:'leaveMessageRate' ,sort:true, title: '留言占比'},
                            {minWidth:100,align:'center',field:'connectCount' ,sort:true, title: '进人工量'},
							{minWidth:120,align:'center',field:'connectRate' ,sort:true, title: '进人工占比'},
                            {minWidth:150,align:'center',field:'robotServiceTime' ,sort:true, title: '机器人处理总时长'},
                            {minWidth:170,align:'center',field:'avgRobotServTime' ,sort:true, title: '平均机器人处理时长(秒)'},
                            {minWidth:130,align:'center',field:'inQueueTime' ,sort:true, title: '排队总时长'},
                            {minWidth:130,align:'center',field:'avgAwaitTime' ,sort:true, title: '平均排队时长(秒)'},
							{minWidth:120,align:'center',field:'servTime' ,sort:true, title: '服务总时长'},
                            {minWidth:130,align:'center',field:'avgServTime' ,sort:true, title: '平均服务时长(秒)'},
                            {minWidth:150,align:'center',field:'recordCount',sort:true,title:'会话量'},
                            {minWidth:150,align:'center',field:'eRecordCount',sort:true,title:'有效会话量'},
	                        {minWidth:150,align:'center',field:'eRecordRate',sort:true,title:'有效会话率'},
	                        {minWidth:150,align:'center',field:'agentNoReplyCount',sort:true,title:'客服无回复会话量'},
	                        {minWidth:150,align:'center',field:'customerNoReplyCount',sort:true,title:'客户无回复会话量'},
	                        {minWidth:150,align:'center',field:'noExchangeCount',sort:true,title:'无交互会话量'},
	                        {minWidth:150,align:'center',field:'agentReceptionRate',sort:true,title:'客服接待率'},
				         ]
					],
					done:function(res,curr,count){
			        	if(res) {
			        		var updateTime = res.updateTime;
							if(updateTime&&updateTime!=""){
								$("#titleAndTime").html("<font color='#5cb85c'>("+getI18nValue('数据更新时间')+":"+updateTime+")</font>");
							}
							resultJson = res;
							setTimeout(()=>{$("#query").attr("disabled",false)},1000)
			        	}
			        }
				});

	       }

		   assessAgentDemo.exportOffLine = function(){
				layer.confirm(getI18nValue('是否导出报表？')+'<br/><br/>',{icon: 3, btn: [getI18nValue('确定'), getI18nValue('取消')], title:getI18nValue('导出提示'),offset:'20px',shade:0,zIndex: 2}, function(index){
					layer.close(index);
					var channelNo = $.trim($("#channelNo").val());
					if(channelNo != "") {
						channelNo = "'" + channelNo.replace(/,/g, "','") + "'";
					}

					var skillNo = $.trim($("#skillNo").val());
					if(skillNo != "") {
						skillNo = "'" + skillNo.replace(/,/g, "','") + "'";
					}
					var stMethod = "02";
					if($('input[name="stMethod"]:checked').length != 0){
						stMethod = "01";
					}

					if("1"==offCheck||1==offCheck){
						//跳转离线导出申请
						popup.layerShow({type:2,title:getI18nValue('离线导出申请原因'),offset:'20px',area:['400px','300px']},"${ctxPath}/pages/offline/exportReason.jsp?"
								+ "channelNo=" + channelNo + "&skillNo=" + skillNo + "&stType=" + $("#stType").val()
								+ "&startDate=" + $("#startDate").val() + "&endDate=" + $("#endDate").val()
								+ "&stMethod=" + stMethod + "&userAcc=" + $("#userAcc").val()
								+ "&userName=" + $("#userName").val() + "&dimension=" + $("#dimension").val()
								+"&reportName=" + getI18nValue("整体接入分析")+ "&expCommand=" + "ENTACCESS",{});
					}else{
						var param = {};

						param.channelNo=channelNo;
						param.stType=$("#stType").val();
						param.skillNo=skillNo;
						param.startDate= $("#startDate").val();
						param.endDate=$("#endDate").val() ;
						param.stMethod=stMethod;
						param.userAcc= $("#userAcc").val();
						param.userName= $("#userName").val();
						//param.dept= dept;
						param.dimension=$("#dimension").val();
						param.reportName=getI18nValue("整体接入分析");
						param.expCommand="ENTACCESS";
						var params = {};
						params['EXP_PARAM']= param;
						ajax.remoteCall("${ctxPath}/servlet/offlineExport?action=saveMediaList",params,function(result) {
							if(result.state == 1){
								var exportUrl = "/cc-base/pages/offlineExport/myOfflineExport.jsp";
								layer.alert(getI18nValue(result.msg)+','+getI18nValue('可点击')+' <a style="color:blue" href="javascript:void(0);" onclick="popup.openTab(\''+exportUrl+'\',\''+ getI18nValue("导出列表")+'\', {});">'+getI18nValue('导出列表')+'</a> '+getI18nValue('查看导出文件'),{icon: 1, btn: [getI18nValue('关闭')],btnAlign:'c', title:getI18nValue('提示'),offset:'20px',shade:0,zIndex: 3}, function(index){
									popup.layerClose(this);
									layer.closeAll();
							});
						}else{
							layer.msg(getI18nValue(result.msg),{icon: 5});
						}
					});
					}
				});
			}

			assessAgentDemo.exportOnLine = function() {
				layer.confirm(getI18nValue('是否导出报表？'),{icon: 3, btn: [getI18nValue('确定'), getI18nValue('取消')], title:getI18nValue('导出提示'),offset:'20px',shade:0,zIndex: 2}, function(index){
					$("#export").attr("disabled",true);
					setTimeout(releaseEx,10000);
					layer.close(index);
					var channelNo = $.trim($("#channelNo").val());
					if(channelNo != "") {
						channelNo = "'" + channelNo.replace(/,/g, "','") + "'";
					}

					var skillNo = $.trim($("#skillNo").val());
					if(skillNo != "") {
						skillNo = "'" + skillNo.replace(/,/g, "','") + "'";
					}
					var stMethod = "02";
					if($('input[name="stMethod"]:checked').length != 0){
						stMethod = "01";
					}
					location.href = "${ctxPath}/servlet/mediaExportStat?action=entAccessExport"
							+ "&channelNo=" + channelNo + "&skillNo=" + skillNo + "&stType=" + $("#stType").val()
							+ "&startDate=" + $("#startDate").val() + "&endDate=" + $("#endDate").val()
							+ "&stMethod=" + stMethod + "&userAcc=" + $("#userAcc").val()
							+ "&userName=" + $("#userName").val() + "&dimension=" + $("#dimension").val();
				});
			}

		assessAgentDemo.downloadExl = function() {
			layer.confirm(getI18nValue('是否导出报表？'),{icon: 3, btn: [getI18nValue('确定'), getI18nValue('取消')], title:getI18nValue('导出提示'),offset:'20px',shade:0,zIndex: 2}, function(index){
				$("#export").attr("disabled",true);
				setTimeout(releaseEx,5000);
				layer.close(index);
				var channelNo = $.trim($("#channelNo").val());
				if(channelNo != "") {
					channelNo = "'" + channelNo.replace(/,/g, "','") + "'";
				}

				var skillNo = $.trim($("#skillNo").val());
				if(skillNo != "") {
					skillNo = "'" + skillNo.replace(/,/g, "','") + "'";
				}
				var stMethod = "02";
				if($('input[name="stMethod"]:checked').length != 0){
					stMethod = "01";
				}
				location.href = "${ctxPath}/servlet/mediaExportStat?action=entAccessExport"
						+ "&channelNo=" + channelNo + "&skillNo=" + skillNo + "&stType=" + $("#stType").val()
						+ "&startDate=" + $("#startDate").val() + "&endDate=" + $("#endDate").val()
						+ "&stMethod=" + stMethod + "&userAcc=" + $("#userAcc").val()
						+ "&userName=" + $("#userName").val() + "&dimension=" + $("#dimension").val();
			});

		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
