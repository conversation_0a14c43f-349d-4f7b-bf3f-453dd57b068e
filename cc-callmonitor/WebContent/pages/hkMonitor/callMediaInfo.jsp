<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width initial-scale=1">
		<title i18n-lay-html="客户联络中心"></title>
		<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<link rel="stylesheet" type="text/css" href="/cc-callmonitor/pages/hkMonitor/css/media-monitor.css">
	</head>
	<style>
		/* .monitor-page {
			min-width: 1800px;
			min-height: 900px;
		}
 */
		.dimgray {
			color: #fff;
			font-size: 12px;
		}

		.pull-right {
			float: right
		}

		.transparent {
			border-color: transparent;
			background-color: transparent;
		}

		#fullpage {
			cursor: pointer
		}

		#updateTime {
			display: inline-block;
			margin-right: 15px;
		}

		.monitor-title {
			position: absolute;
			top: 10px;
		}

		* {
			color: #fff;
		}

		.tableHead th {
			font-family: MicrosoftYaHei;
			font-size: 13px;
			color: #94D8FF;
			letter-spacing: 0.87px;
			text-align: right;
			font-weight: normal;
		}

		tbody td {
			opacity: 0.8;
			font-family: MicrosoftYaHei;
			font-size: 13px;
			color: #E0F3FF;
			letter-spacing: 0.87px;
			font-weight: normal;
		}

		td {
			text-align: right !important;
		}

		td:first-child {
			text-align: left !important;
		}

		.imgTips {
			background-image: -webkit-linear-gradient(bottom, #29A3DC, #0D58BE);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			margin-right: 16px;
		}

		.layui-icon-reply-fill:before {
			margin-left: 4px;
		}

		.callinfo-box .count span {
			color: yellow;
			font-size: 30px;
		}

		.line-bar-box-down {
			padding-top: 7px;
		}

		#talk_rate {
			font-size: 22px;
			color: red;
			font-weight: 600;
		}

		.line-bar-box {
			text-align: center;
		}

		.topage:hover {
			background: #33B5FF !important;
			cursor: pointer;
			margin-right: 10px;
		}

		#skillCallInfoShow tr td {
			height: 34px !important;
		}

		.flex-item {
			overflow: visible !important;
		}
	</style>

	<body class="monitor-page">
		<div class="monitor-page-header" style="position: relative;">
			<img src="/cc-callmonitor/pages/hkMonitor/images/hk/div@1x_01.png" alt="" class="headimg" draggable="false">
			<div class="title" i18n-lay-html="客户联络中心"></div>
			<div class="timeAndNum">
				<p style="font-family: MicrosoftYaHei-Bold;font-size: 16px; color: #0093C5;text-align: left;font-weight: 600;margin-bottom: 6px;"
					id="clockTime">
				</p>
				<p
					style="font-family: MicrosoftYaHei-Bold;font-size: 16px; color: #A7A9B2; text-align: left;font-weight: 600;">
					总排队数：<span style=" color:#F7B505;" id="totalLine">0</span></p>
			</div>
		</div>
		<div class="monitor-page-content">
			<div class="monitor-page-rows flex">
				<!-- <div class="monitor-title">
					<div id="clockTime" class="clock"></div>
					<span i18n-lay-html="全媒体指标"></span> 
					<span class="dimgray" i18n-lay-html="最近更新时间："></span><span id="updateTime" class="dimgray"></span>
                        <span id="fullpage" class="dimgray " i18n-lay-html="[全屏]"></span> 
				</div> -->
				<div class="flex-item" style="flex: 1.4">
					<div class="flex-row">
						<div class="flex-item">
							<div class="flex">
								<div class="flex-item box-m" style="margin-bottom: 10px;height:290px">
									<div class="monitor-box leftTop">
										<div class="newHeader" i18n-lay-html="当日服务概览"></div>

										<div class="content" style="display: flex;flex-direction:column;">
											<div
												style="height: 50px;text-align: left;border-bottom: 1px solid #32496D;padding-bottom: 20px;position: relative;">
												<div
													style="width: 100%;display: flex;position: absolute;top: 30px; transform: translateY(-50%);">
													<div class="light" style="width: 20%;min-width: 105px;">
														<p class="digits fontOne"></p>
														<p class="digitsname">需求量</p>
													</div>

													<div class="light" style="width: 60%;">
														<div style="margin: 0 auto;width: 110px;min-width: 105px;">
															<p class="digits fontTwo"></p>
															<p class="digitsname"
																style="text-align: left;padding-left: 13px;">接听量</p>
														</div>
													</div>

													<div class="light" style="width: 20%;min-width: 105px;">
														<p class="digits fontThree"></p>
														<p class="digitsname" style="padding-left:13px">人工接听量</p>
													</div>
												</div>
											</div>

											<div style="display: flex;flex: 1;">
												<div style="flex:1.4;padding-top: 5px;display: flex;flex-direction: column;justify-content:space-around;"
													class="allP">
													<p style="flex:1;"><span class="alltitle">需求客户数：</span><span
															class="COMMAND_NUM"></span>
													</p>
													<p style="flex:1;"><span class="alltitle">在线需求量：</span><span
															class="IN_NUM"></span>
													</p>
													<p style="flex:1;"><span class="alltitle">在线机器人：</span><span
															class="ROBOT_NUM"></span></p>
													<p style="flex:1;"><span class="alltitle">在线人工：</span><span
															class="AGENT_NUM"></span></p>
												</div>
												<div style="flex:1" id="chart-gl">

												</div>
											</div>

										</div>
									</div>
								</div>


								<div class="flex-item box-m hover-stop" id="theone" style="height:320px">
									<div class="monitor-box leftTop2" id="zaixian" style="position: relative;">
										<div class="newHeader" i18n-lay-html="在线服务水平"
											style="transform: translateY(-5px);"></div>
										<div class="content">
											<div class="flex flex-vac" style="overflow: auto;">
												<table data-layout="fixed" data-align="left" data-border="none"
													data-zebra="true" class="monitor-table">
													<thead class="tableHead">
														<tr>
															<th i18n-lay-html="渠道" style="text-align: left !important;">
															</th>
															<th i18n-lay-html="在线需求量"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="人工会话量"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="排队数"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="最大等待时长"
																style="text-align: right!important;"></th>
														</tr>
													</thead>
													<tbody id="skillCallInfoShow">

													</tbody>
												</table>
											</div>

										</div>

										<div style="text-align: center;" id="zhishiqi">

										</div>
									</div>


									<div class="monitor-box leftTop2" id="zhuanxian"
										style="position: relative;display: none;">
										<div class="newHeader" i18n-lay-html="专线服务水平"
											style="transform: translateY(-5px);"></div>
										<div class="content">
											<div class="flex flex-vac" style="overflow: auto;">
												<table data-layout="fixed" data-align="left" data-border="none"
													data-zebra="true" class="monitor-table">
													<thead class="tableHead">
														<tr>
															<th i18n-lay-html="线路" style="text-align: left !important;">
															</th>
															<th i18n-lay-html="需求数"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="接听数"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="接通率"
																style="text-align: right!important;"></th>
															<th i18n-lay-html="漏话" style="text-align: right!important;">
															</th>
															<th i18n-lay-html="未接" style="text-align: right!important;">
															</th>
															<th i18n-lay-html="未接处理量"
																style="text-align: right!important;"></th>
														</tr>
													</thead>
													<tbody id="skillCallInfoShow7">

													</tbody>
												</table>
											</div>



										</div>

										<div style="text-align: center;" id="zhishiqi7">

										</div>
									</div>
								</div>

							</div>
						</div>
						<div class="flex-item box-m" style="flex: 2">
							<div class="monitor-box centerTop"  onmouseenter="mouseIn(8)" onmouseleave="mouseOut(8)">
								<div class="newHeader" i18n-lay-html="当日话务趋势" style="top: 3.7%;"></div>

								<div class="content" style="position: relative;">
									<div id="chart-map" class="chart-area"></div>
									<div style="position: absolute;right: 20px;top: 10px;color: #000000;cursor:pointer">
										<span id="carouselBtn0" class="layui-badge layui-bg-cyan" onclick="carousel('0')"><span i18n-content="按小时"></span></span>
										<span id="carouselBtn1" class="layui-badge layui-bg-cyan" onclick="carousel('1')"><span i18n-content="按半小时"></span></span>
										<span id="carouselBtn2" class="layui-badge layui-bg-cyan" onclick="carousel('2')"><span i18n-content="按分钟"></span></span>
									</div>
								</div>
							</div>
						</div>
						<div class="flex-item" style="margin-left: 5px;">
							<div class="flex">
								<div class="monitor-box centerRight"
									style="background-size: 100% 98%;transform: translateY(-1%);">
									<div class="newHeader" i18n-lay-html="坐席状态" style="left: 13%;top: 8%;"></div>

									<div id="chart-zuixiState" class="chart-area"></div>
								</div>

								<div class="monitor-box centerRight" style="overflow: visible;" onmouseenter="mouseIn(4)" onmouseleave="mouseOut(4)">
									<div class="newHeader" i18n-lay-html="班组状态" style="left: 13%;top: 8%;"></div>

									<div id="chart-banzuState" class="chart-area"></div>

									<div style="text-align: center;position: absolute;bottom: 0px;left: 50%;transform: translateX(-50%);"
										id="zhishiqi4"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				
				<div class="flex-item">
					<div class="flex-row">

						<div class="flex-item box-m" style="flex: 0.985">
							<div class="monitor-box centerLeft hover-stop" style="position: relative;">
								<div class="newHeader" i18n-lay-html="队列接通率"></div>

								<div class="content" style="position: relative;">
									<div id="chart-queue" onmouseenter="mouseIn(2)" onmouseleave="mouseOut(2)"
										class="chart-area"></div>

									<div style="text-align: center;position: absolute;bottom: 0px;left: 50%;transform: translateX(-50%);"
										id="zhishiqi2">

									</div>
								</div>
							</div>
						</div>

						<div class="flex-item box-m" style="flex: 2">
							<div class="monitor-box centerBottom hover-stop" style="position: relative;">
								<div class="newHeader" i18n-lay-html="队列等待信息" style="left: 6%;top: 4%;"></div>
								<div style="position: absolute;left: 280px;top: 5%;">总排队人数：<span id="allAwait"
										style="color: #F7B505;">0</span></div>
								<div class="content">
									<div id="chart-await" onmouseenter="mouseIn(3)" onmouseleave="mouseOut(3)"
										class="chart-area"></div>

									<div style="text-align: center;position: absolute;bottom: 0px;left: 50%;transform: translateX(-50%);"
										id="zhishiqi3">

									</div>
								</div>
							</div>
						</div>

						<div class="flex-item box-m" style="flex: 0.99" style="overflow: visible !important;">
							<div class="flex" style="overflow: visible;">
								<div class="monitor-box bottomRight" style="overflow: visible;" onmouseenter="mouseIn(5)" onmouseleave="mouseOut(5)">
									<div class="newHeader" i18n-lay-html="技能组状态" style="left: 13%;top: 5%;"></div>

									<div id="chart-skillState" class="chart-area" style="overflow: visible;"></div>
									<div style="text-align: center;position: absolute;bottom: 0px;left: 50%;transform: translateX(-50%);"
										id="zhishiqi5"></div>
								</div>

								<div class="monitor-box bottomRight" style="overflow: visible;"onmouseenter="mouseIn(6)" onmouseleave="mouseOut(6)">
									<div class="newHeader" i18n-lay-html="出勤分布" style="left: 13%;top: 5%;"></div>

									<div id="chart-banzuDistribution" class="chart-area"></div>
									<div style="text-align: center;position: absolute;bottom: 0px;left: 50%;transform: translateX(-50%);"
										id="zhishiqi6"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>

		<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/xss.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
		<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
		<script type="text/javascript" src="/easitline-static/js/layTable.js"></script>
		<script type="text/javascript" src="/cc-callmonitor/pages/hkMonitor/js/my_i18n.js?v=2020070"></script>
		<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
		<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
		<script type="text/javascript" src="/cc-callmonitor/pages/hkMonitor/js/common.js?v=202101052241"></script>
		<script src="/easitline-static/lib/echarts/echarts.min.js" type="text/javascript" charset="utf-8"></script>

		<script src="/cc-callmonitor/pages/hkMonitor/js/monitor.js"></script>
		<script type="text/javascript">
			$("#fullpage").on('click', function(event) {
				requestFullScreen(document.documentElement);
			});

			window.onload = function() {
				//右上角时钟
				clockTime();
				//大屏数据
				monitorCall();
				$(window).on('resize', pageResize)
			}

			//屏幕大小变动
			function pageResize() {
				window.location.reload()
			}
			
			var centerTopData = {
				data_6:{},
				data_12:{},
				data_24:{},
			};
			


			//webSocket请求
			var websocket = null;
			var websocketTimer = null;

			function monitorCall() {
				var url = "ws://" + window.location.host + "${ctxPath}/websocket/hkMonitorWs"
				if('https:' == document.location.protocol){
					url = "wss://" + window.location.host + "${ctxPath}/websocket/hkMonitorWs"					
				}
				//判断当前浏览器是否支持WebSocket
				if ('WebSocket' in window) {
					websocket = new WebSocket(url);
				} else if ('MozWebSocket' in window) {
					websocket = new MozWebSocket(url);
				} else {
					console.log("当前浏览器不支持 WebSocket，请更换浏览器尝试！");
					layer.msg(getI18nValue("当前浏览器不支持 WebSocket，请更换浏览器尝试！"), {
						icon: 5
					});
				}

				//连接发生错误的回调方法
				websocket.onerror = function() {
					console.error("WebSocket连接发生错误");
					reconnect(url);
				};

				//连接关闭的回调方法
				websocket.onclose = function(e) {
					console.warn("WebSocket连接关闭, code: " + e.code + " ,reson: " + e.reason);
					reconnect(url);
				}

				//监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
				window.onbeforeunload = function() {
					//关闭WebSocket连接
					websocket.close();
				}

				//连接成功建立的回调方法
				websocket.onopen = function() {
					console.log("WebSocket连接成功!");
					websocket.send("getData");
					
					//请求数据，频率：5s
					websocketTimer = setInterval(function(){
						websocket.send("getData");
					},5000);
					//12s后变更后台的数据获取的判断方式
					setTimeout(function() {
						websocket.send("condition");
					}, 12000);
				}

				//接收到消息的回调方法
				websocket.onmessage = function(event) {
					console.log("接收到数据： " + event.data);
					renderPage(JSON.parse(event.data));
				}
			}
			
			//websocket重连
			function reconnect(url) {
				clearInterval(websocketTimer);
			    if (reconnect.lockReconnect) return;
			    reconnect.lockReconnect = true;
			    setTimeout(function () {     //没连接上会一直重连，设置延迟避免请求过多
			    	console.warn("WebSocket尝试重连...");
			    	monitorCall(url);
			        reconnect.lockReconnect = false;
			    }, 2000);
			}
			
 			//websocket心跳检测
/* 			var heartCheck = {
			    timeout: 13*1000,  //  心跳检测时长
			    timeoutObj: null, // 定时变量
			    reset: function () { // 重置定时
			        clearTimeout(this.timeoutObj);
			        this.start();
			    },
			    start: function () { // 开启定时
			        this.timeoutObj = setTimeout(function () {
			          // 心跳时间内收不到消息，主动触发连接关闭，开始重连
			          websocket.close();
			        },heartCheck.timeout)
			    }
			} */


			//根据获取的数据进行页面渲染
			function renderPage(result) {
				//总排队数
				if (result.queue_call_count) {
					$("#totalLine").text(result.queue_call_count ? result.queue_call_count : 0);
				}
				//左上
				var leftTop = result.leftTop;
				if (leftTop) {
					clocknum(leftTop.ALL_DEMAND ? leftTop.ALL_DEMAND : 0, 'fontOne');
					clocknum(leftTop.ALL_ANSWER ? leftTop.ALL_ANSWER : 0, 'fontTwo');
					clocknum(leftTop.ANSWER ? leftTop.ANSWER : 0, 'fontThree');
					$(".COMMAND_NUM").text(leftTop.COMMAND_NUM ? leftTop.COMMAND_NUM : 0);
					$(".IN_NUM").text(leftTop.IN_NUM ? leftTop.IN_NUM : 0);
					$(".ROBOT_NUM").text(leftTop.ROBOT_NUM ? leftTop.ROBOT_NUM : 0);
					$(".AGENT_NUM").text(leftTop.AGENT_NUM ? leftTop.AGENT_NUM : 0);
					if (leftTop.ALL_DEMAND && leftTop.ALL_ANSWER && leftTop.ALL_DEMAND != 0) {
						myHistoryRiskChartInit(leftTop.ALL_ANSWER / leftTop.ALL_DEMAND) //仪表盘
					} else {
						myHistoryRiskChartInit(0) //仪表盘
					}
				}
				//左中
				var online = result.leftCenter.online;
				var queueList = result.leftCenter.queueList;

				var isLine = sessionStorage.getItem('isLine')
				if (isLine && isLine == '专线' && queueList && queueList.length > 0) {
					$('#zhuanxian').show()
					$('#zaixian').hide()
					onlyServe(queueList)
				} else if (online && online.length > 0) {
					$('#zaixian').show()
					$('#zhuanxian').hide()
					onlineServe(online)
				}

				//左下 
				var leftBottom = result.leftBottom;
				if (leftBottom) {
					var arr_CALL_NUM = getNewArray(leftBottom.CALL_NUM, 6);
					var arr_DEMAND_NUM = getNewArray(leftBottom.DEMAND_NUM, 6);
					var arr_QUEUE_NAME = getNewArray(leftBottom.QUEUE_NAME, 6);
					var allArr = [];
					for (var i = 0; i < arr_CALL_NUM.length; i++) {
						var obj = {
							CALL_NUM: arr_CALL_NUM[i],
							DEMAND_NUM: arr_DEMAND_NUM[i],
							QUEUE_NAME: arr_QUEUE_NAME[i]
						}
						allArr.push(obj)
					}
					sessionStorage.setItem('allArr', JSON.stringify(allArr))
					if (allArr.length != 0) {
						var str2 = '<div style="text-align:center;">'
						for (let i = 0; i < allArr.length; i++) {
							str2 += '<span id="chooseItem2_' + i + '" class="chooseItem2"  onclick="chooseItem2(' + Number(i) +
								')" style="margin-right:10px;display: inline-block;">一</span>'
						}
						str2 += '</div>'
						$('#zhishiqi2').html(str2)

						//控制轮播
						if (isAutoPaly2 == true) { //无鼠标悬停
							if (currentPage2 < allArr.length - 1) {
								currentPage2++
							} else {
								currentPage2 = 0;
							}
						}else { //有鼠标悬停
							//防止当前数据少于上次获取的数据时，发生的数组越界问题
							currentPage2 = currentPage2-1 > allArr.length ? allArr.length : currentPage2-1;
						}
						
						chooseItem2(currentPage2, 'init')
					}
				}

				
				//中下
				var centerBottom = result.centerBottom;
				if (centerBottom) {
					var arr_QUEUE_COUNT = getNewArray(centerBottom.QUEUE_COUNT, 10);
					var arr_QUEUE_NAME = getNewArray(centerBottom.QUEUE_NAME, 10);
					var arr_MAX_QUEUE_TIME = getNewArray(centerBottom.MAX_QUEUE_TIME, 10);
					var arr_AVG_QUEUE_TIME = getNewArray(centerBottom.AVG_QUEUE_TIME, 10);
					
					var allArr3 = [];
					for (var i = 0; i < arr_QUEUE_NAME.length; i++) {
						var obj = {
							QUEUE_COUNT: arr_QUEUE_COUNT[i],
							QUEUE_NAME: arr_QUEUE_NAME[i],
							MAX_QUEUE_TIME: arr_MAX_QUEUE_TIME[i],
							AVG_QUEUE_TIME: arr_AVG_QUEUE_TIME[i],
						}
						allArr3.push(obj)
					}
					sessionStorage.setItem('allArr3', JSON.stringify(allArr3))
					if (allArr3.length != 0) {

						var str3 = '<div style="text-align:center;">'
						for (let i = 0; i < allArr3.length; i++) {
							str3 += '<span id="chooseItem3_' + i + '" class="chooseItem3"  onclick="chooseItem3(' + Number(i) +
								')" style="margin-right:10px;display: inline-block;">一</span>'
						}
						str3 += '</div>'
						$('#zhishiqi3').html(str3)

						//控制轮播
						if (isAutoPaly3 == true) { //无鼠标悬停
							if (currentPage3 < allArr3.length - 1) {
								currentPage3++
							} else {
								currentPage3 = 0;
							}
						}else { //有鼠标悬停
							//防止当前数据少于上次获取的数据时，发生的数组越界问题
							currentPage3 = currentPage3-1 > allArr3.length ? allArr3.length : currentPage3-1;
						}
						
						chooseItem3(currentPage3, centerBottom.allAwait)
					}
				}

				
				//中上
				centerTopData = result.centerTop;
				if (centerTopData && !$.isEmptyObject(centerTopData)) {
					//根据鼠标悬停控制轮播
					currentPage_top = isAutoPaly8 ? currentPage_top : currentPage_top-1;
					currentPage_top = currentPage_top<0 ? 2 : currentPage_top;
					$("#carouselBtn"+currentPage_top).click();
				}

				//右上
				var agentStatus = result.agentStatus

/* 				agentStatus = [{
					"agentState": "置忙",
					"agentCount": 66
				}, {
					"agentState": "置闲",
					"agentCount": 3
				}, {
					"agentState": "呼入通话",
					"agentCount": 142
				}, {
					"agentState": "外呼通话",
					"agentCount": 20
				}, {
					"agentState": "振铃",
					"agentCount": 3
				}, {
					"agentState": "话后整理",
					"agentCount": 9
				}] */



				if (agentStatus) {
					var data = [];
					$.each(agentStatus, function(k, v) {
						var json = {
							value: v.agentCount,
							name: v.agentState
						};
						data.push(json);
					})
					zuoxiState(data);
				}
				

				//右中
				var r = result.rightDept;

				if (r) {
					if (r.deptState) {

						var json = r.deptState;
						var allArr4 = [];
						var deptArr = [];
						for (var i = 0; i < json.status.length; i++) {
							allArr4.push(getNewArray(json.num[i], 7));
						}
						json.num = allArr4;
						json.dept = getNewArray(json.dept, 7);

						sessionStorage.setItem('allArr4', JSON.stringify(json));

						var str4 = '<div style="text-align:center;">'
						for (let i = 0; i < allArr4[0].length; i++) {
							str4 += '<span id="chooseItem4_' + i + '" class="chooseItem4"  onclick="chooseItem4(' + Number(i) +
								')" style="margin-right:10px;display: inline-block;">一</span>'
						}
						str4 += '</div>'
						$('#zhishiqi4').html(str4)

						//控制轮播
						if (isAutoPaly4 == true) { //无鼠标悬停
							if (currentPage4 < json.dept.length - 1) {
								currentPage4++
							} else {
								currentPage4 = 0;
							}
						}else { //有鼠标悬停
							//防止当前数据少于上次获取的数据时，发生的数组越界问题
							currentPage4 = currentPage4-1 > json.dept.length ? json.dept.length : currentPage4-1;
						}
						
						chooseItem4(currentPage4);
					}


					//右下1
					if (r.skillState) {
						// var arr_AVG_TIME = getNewArray(centerBottom.AVG_TIME, 10);
						var skillStateArr = []
						$.each(r.skillState.status, function(k, v) {
							skillStateArr[k] = getNewArray(v, 7);
						})
						var skillStateXdata = getNewArray(r.skillState.xdata, 7)

						var allArr5 = [];
						for (var i = 0; i < skillStateXdata.length; i++) {
							var obj = {
								status: {}
							}
							$.each(r.skillState.status, function(k, v) {
								var o = skillStateArr[k];
								obj.status[k] = o[i];
							})
							obj.xdata = skillStateXdata[i];
							allArr5.push(obj)
						}
						sessionStorage.setItem('allArr5', JSON.stringify(allArr5))
						var str5 = '<div style="text-align:center;transform:translateY(5px);">'
						for (let i = 0; i < allArr5.length; i++) {
							str5 += '<span id="chooseItem5_' + i + '" class="chooseItem5"  onclick="chooseItem5(' + Number(i) +
								')" style="margin-right:10px;display: inline-block;">一</span>'
						}
						str5 += '</div>'

						//控制轮播
						if (isAutoPaly5 == true) { //无鼠标悬停
							if (currentPage5 < allArr5.length - 1) {
								currentPage5++
							} else {
								currentPage5 = 0;
							}
						}else { //有鼠标悬停
							//防止当前数据少于上次获取的数据时，发生的数组越界问题
							currentPage5 = currentPage5-1 > allArr5.length ? allArr5.length : currentPage5-1;
						}

						//渲染
						$('#zhishiqi5').html(str5)
						chooseItem5(currentPage5)
					}
				}
				
				//右下2
				rightBottom = result.rightBottom2;
				if (rightBottom) {
					var arr_deptList = getNewArray(rightBottom.deptList, 6);
					var arr_onlineList = getNewArray(rightBottom.onlineList, 6);
					var arr_attendanceList = getNewArray(rightBottom.attendanceList, 6);
					var allArr6 = [];
					for (var i = 0; i < arr_deptList.length; i++) {
						var obj = {
							deptList: arr_deptList[i],
							onlineList: arr_onlineList[i],
							attendanceList: arr_attendanceList[i],
						}
						allArr6.push(obj)
					}
					
					sessionStorage.setItem('allArr6', JSON.stringify(allArr6))
					var str6 = '<div style="text-align:center;transform:translateY(5px);">'
					for (let i = 0; i < allArr6.length; i++) {
						str6 += '<span id="chooseItem6_' + i + '" class="chooseItem6"  onclick="chooseItem6(' + Number(i) +
							')" style="margin-right:10px;display: inline-block;">一</span>'
					}
					str6 += '</div>'

					//控制轮播
					if (isAutoPaly6 == true) { //无鼠标悬停
						if (currentPage6 < allArr6.length - 1) {
							currentPage6++
						} else {
							currentPage6 = 0;
						}
					}else { //有鼠标悬停
						//防止当前数据少于上次获取的数据时，发生的数组越界问题
						currentPage6 = currentPage6-1 > allArr6.length ? allArr6.length : currentPage6-1;
					}
					
					//渲染
					$('#zhishiqi6').html(str6)
					chooseItem6(currentPage6)
				}
			}

			var currentPage_top = 0;

			var currentPage2 = 0;
			var isAutoPaly2 = true;

			function chooseItem2(id) {
				currentPage2 = id;
				var allArr = JSON.parse(sessionStorage.getItem('allArr'))
				var arr2 = [];
				for (var j = 0; j < allArr[id].DEMAND_NUM.length; j++) {
					arr2.push(toPercent(allArr[id].CALL_NUM[j], allArr[id].DEMAND_NUM[j]))
				}
				allArr[id].proportion = arr2;

				changeColor('2', id)
				queueConnectionRate(allArr[id], 'chart-queue');
			}

			function mouseIn(id) {
				switch (id) {
					case 2:
						isAutoPaly2 = false
						break;
					case 3:
						isAutoPaly3 = false
						break;
					case 4:
						isAutoPaly4 = false
						break;
					case 5:
						isAutoPaly5 = false
						break;
					case 6:
						isAutoPaly6 = false
						break;
					case 8:
						isAutoPaly8 = false
						break;
					default:
						默认代码块
				}
			}

			function mouseOut(id) {
				switch (id) {
					case 2:
						isAutoPaly2 = true
						break;
					case 3:
						isAutoPaly3 = true
						break;
					case 4:
						isAutoPaly4 = true
						break;
					case 5:
						isAutoPaly5 = true
						break;
					case 6:
						isAutoPaly6 = true
						break;
					case 8:
						isAutoPaly8 = true
						break;
					default:
						默认代码块
				}
			}




			var currentPage3 = 0;
			var isAutoPaly3 = true;

			function chooseItem3(id, num) {
				currentPage3 = id;
				var allArr = JSON.parse(sessionStorage.getItem('allArr3'))

				changeColor('3', id)
				queueAwait(allArr[id], num)
			}

			var currentPage4 = 0;
			var isAutoPaly4 = true;

			function chooseItem4(id) {
				currentPage4 = id;
				var allArr = JSON.parse(sessionStorage.getItem('allArr4'))

				changeColor('4', id)
				banzuState(allArr, id)
			}



			var currentPage5 = 0;
			var isAutoPaly5 = true;

			function chooseItem5(id) {
				currentPage5 = id;
				var allArr = JSON.parse(sessionStorage.getItem('allArr5'))
				changeColor('5', id)
				skillState(allArr[id])
			}


			var currentPage6 = 0;
			var isAutoPaly6 = true;

			function chooseItem6(id) {
				currentPage6 = id;
				var allArr = JSON.parse(sessionStorage.getItem('allArr6'))
				changeColor('6', id)
				if(allArr[id]){
					AttendanceView(allArr[id]);
				}
			}


			function toPercent(num, total) {
				if (total == 0) {
					return 100.00;
				}
				return (Math.round(num / total * 10000) / 100.00); // 小数点后两位百分比
			}

			function requestFullScreen(element) {
				var requestMethod = element.requestFullScreen || //W3C
					element.webkitRequestFullScreen || //Chrome
					element.mozRequestFullScreen || //FireFox
					element.msRequestFullScreen; //IE11
				if (requestMethod) {
					requestMethod.call(element);
				} else if (typeof window.ActiveXObject !== "undefined") { //for Internet Explorer
					var wscript = new ActiveXObject("WScript.Shell");
					if (wscript !== null) {
						wscript.SendKeys("{F11}");
					}
				}
			}
			
			var isAutoPaly8 = true;
			//中上的点击轮播
			function carousel(val){
				$("#carouselBtn"+val).siblings().removeClass("layui-bg-green").addClass("layui-bg-cyan");
				$("#carouselBtn"+val).removeClass("layui-bg-cyan").addClass("layui-bg-green");
				if(val == 0){
					centerTop(centerTopData.data_24);
					currentPage_top = 1;
				}else if(val == 1){
					centerTop(centerTopData.data_12);
					currentPage_top = 2;
				}else{
					centerTop(centerTopData.data_6);
					currentPage_top = 0;
				}
			}

			function clocknum(num, el) {
				$('.' + el).empty();
				var html = '';
				var strarr = num.toString().split('');
				var digit_to_name = 'zero one two three four five six seven eight nine'.split(' ');
				for (var i = 0; i < strarr.length; i++) {
					if (strarr[i] == '.') {
						html += '<div class="dot"></div>'
					} else if (strarr[i] == ',') {
						html += '<span class="dou">, </span>'
					} else {
						var clasname = digit_to_name[strarr[i]];
						html += '<div class="' + clasname + '">' +
							'<span class="d1"></span>' +
							'<span class="d2"></span>' +
							'<span class="d3"></span>' +
							'<span class="d4"></span>' +
							'<span class="d5"></span>' +
							'<span class="d6"></span>' +
							'<span class="d7"></span>' +
							'</div>';
					}
				}
				$('.' + el).append(html);
			}
			
		</script>
	</body>

</html>
