package com.yunqu.cc.callmoinitor.base;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.easitline.common.core.service.BaseService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;

public class AppBaseService extends BaseService {

	protected EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_WIRTE_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}
	
	/**
	 * 格式化秒,将秒数转成 "00:00:00"格式
	 *
	 * @param value
	 * @return
	 */
	protected String parseSecondToTime(String value) {
		if (!StringUtils.isNumeric(value)) {
			return "00:00:00";
		}
		int theTime = Integer.valueOf(value);// 秒
		int theTime1 = 0;// 分
		int theTime2 = 0;// 小时
		if (theTime >= 60) {
			theTime1 = theTime / 60;
			theTime = theTime % 60;
			if (theTime1 >= 60) {
				theTime2 = theTime1 / 60;
				theTime1 = theTime1 % 60;
			}
		}
		String result = "" + theTime + "";
		if (theTime < 10) {
			result = "0" + theTime;
		}
		if (theTime1 > 0) {
			if (theTime1 < 10) {
				result = "0" + theTime1 + ":" + result;
			} else {
				result = theTime1 + ":" + result;
			}
		} else {
			result = "00:" + result;
		}
		if (theTime2 > 0) {
			if (theTime2 < 10) {
				result = "0" + theTime2 + ":" + result;
			} else {
				result = theTime2 + ":" + result;
			}
		} else {
			result = "00:" + result;
		}
		return result;
	}
	
	protected String parseNum(int value1, int value2) {
        if (value1 == 0 || 0 == value2) {
            return "0";
        }
        BigDecimal f1 = new BigDecimal(value1).divide(new BigDecimal(value2), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal f2 = f1.multiply(new BigDecimal(100));
        double f3 = f2.doubleValue();
        return f3 + "";
    }
	
	protected String parseRatio(int value1, int value2) {
        if (value1 == 0 || 0 == value2) {
            return "0";
        }
        BigDecimal f1 = new BigDecimal(value1).divide(new BigDecimal(value2), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal f2 = f1.multiply(new BigDecimal(100));
        double f3 = f2.doubleValue();
        return f3 + "";
    }
	
	/**
	 * 获取接通率
	 * @param json CALL_COUNT：呼入数   CONN_SUCC_COUNT：接通数
	 * @return
	 */
	protected String getConnectRatio(JSONObject json) {
		int callCount = json.getIntValue("CALL_COUNT");
		int connSuccCount = json.getIntValue("CONN_SUCC_COUNT");
		return this.parseNum(connSuccCount, callCount);
	}
	
	/**
	 * 设置缓存
	 * @param cacheKey 缓存key
	 * @param valueMap 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void setCache(String cacheKey, Map valueMap, boolean isString) {
		cacheKey = cacheKey + "keys";
		Set<String> keySets = CacheUtil.get(cacheKey);
		if(keySets != null && keySets.size() > 0) {
			keySets.forEach(key -> {
				if(!valueMap.containsKey(key)) {
					CacheUtil.delete(key);
				}
			});
		}
		if(valueMap != null && valueMap.keySet().size() > 0) {
			valueMap.keySet().forEach(key -> {
				if(isString) {
					CacheUtil.put((String)key, JSONObject.toJSONString(valueMap.get(key)), 30);
				} else {
					CacheUtil.put((String)key, valueMap.get(key), 30);
				}
			});
		}
		if(valueMap != null) {
			Set<String> keys = new HashSet<String>(valueMap.keySet());
			CacheUtil.put(cacheKey, keys, 30);
		}
	}
	
	/**
	 * 设置缓存
	 * @param cacheKey 缓存key
	 * @param valueMap 
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected void setCache(Map valueMap) {
		if(valueMap != null && valueMap.keySet().size() > 0) {
			valueMap.keySet().forEach(key -> {
				CacheUtil.put((String)key, JSONObject.toJSONString(valueMap.get(key)), 30);
			});
		}
	}
	
	/**
	 * 获取ICCS_SKILLGROUP缓存,兼容新旧两种方式
	 * @param skillId
	 * @return
	 */
	public JSONObject getIccsSkill(String skillId) {
		String cacheKey = "ICCS_SKILLGROUP_" + skillId;	
		try {
			JSONObject iccsSkill = CacheUtil.get(cacheKey);
			return iccsSkill;
		} catch (Exception e) {
			String iccsSkillStr = CacheUtil.get(cacheKey);
			if(StringUtils.isNotBlank(iccsSkillStr)) {
				return JSONObject.parseObject(iccsSkillStr);
			}
		}
		return null;
	}
	
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME FROM " + Constants.getStatSchema() + ".CC_STAT_TABLE_INFO WHERE TABLE_ID = ?  ";
			tabInfo = QueryFactory.getReadQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception e) {
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "获取统计库中的最新统计表名和统计时间出错:"+e.getMessage(),e);
		}
		return tabInfo;
	}
	
	/**
	 * 获取Stat数据库的表名
	 */
	protected String getStatTableName(String tableName){
		return Constants.getStatSchema() + "." + tableName;
	}
	
	public JSONObject getIccsEntJson(String entId) {
		String cacheKey = "ICCS_ENT_"+entId;
		
		JSONObject iccsEntJson = null;
		try {
			iccsEntJson = CacheUtil.get(cacheKey);
		} catch (Exception e) {
			CommonLogger.getLogger("ash").error(CommonUtil.getClassNameAndMethod(this) + " 兼容旧版缓存数据获取(无需处理) error:" + e.getMessage());
		}
		
		if(iccsEntJson == null) {
			try {
				iccsEntJson = new JSONObject();
				iccsEntJson.put("queueCallCount", CacheUtil.hget(cacheKey, "queueCallCount"));
				iccsEntJson.put("logonAgentCount", CacheUtil.hget(cacheKey, "logonAgentCount"));
				iccsEntJson.put("idleAgentCount", CacheUtil.hget(cacheKey, "idleAgentCount"));
				iccsEntJson.put("busyAgentCount", CacheUtil.hget(cacheKey, "busyAgentCount"));
				iccsEntJson.put("talkAgentCount", CacheUtil.hget(cacheKey, "talkAgentCount"));
				iccsEntJson.put("pdsIdleAgentCount", CacheUtil.hget(cacheKey, "pdsIdleAgentCount"));
				iccsEntJson.put("alertAgentCount", CacheUtil.hget(cacheKey, "alertAgentCount"));
				iccsEntJson.put("workNotReadyAgentCount", CacheUtil.hget(cacheKey, "workNotReadyAgentCount"));
				iccsEntJson.put("aveQueueLen", CacheUtil.hget(cacheKey, "aveQueueLen"));
				iccsEntJson.put("maxQueueLen", CacheUtil.hget(cacheKey, "maxQueueLen"));
				iccsEntJson.put("abandonQueueCount", CacheUtil.hget(cacheKey, "abandonQueueCount"));
				iccsEntJson.put("abandonAgentCount", CacheUtil.hget(cacheKey, "abandonAgentCount"));
				iccsEntJson.put("queueClearCallCount", CacheUtil.hget(cacheKey, "queueClearCallCount"));
				iccsEntJson.put("queueTimeOutCount", CacheUtil.hget(cacheKey, "queueTimeOutCount"));
				iccsEntJson.put("updateTime", CacheUtil.hget(cacheKey, "updateTime"));
			} catch (Exception e) {
				CommonLogger.getLogger("ash").error(CommonUtil.getClassNameAndMethod(this) + " 兼容旧版缓存数据获取(无需处理) error:" + e.getMessage());
			}
		}
		return iccsEntJson;
	}

}
