(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-tab-media"],{"0fbd":function(t,e,a){"use strict";a.r(e);var n=a("d19a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},1211:function(t,e,a){var n=a("e934");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("19c00a9c",n,!0,{sourceMap:!1,shadowMode:!1})},"2fce":function(t,e,a){"use strict";var n=a("1211"),i=a.n(n);i.a},3728:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"page"},[e("v-uni-web-view",{staticClass:"iframe",attrs:{src:this.url}}),e("m-tabbar",{attrs:{tabIndex:this.tabIndex}})],1)},i=[]},b3e3:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797");var n=a("8f59"),i={computed:(0,n.mapState)({menuList:function(t){return t.user.menuList}}),watch:{menuList:{handler:function(t){var e=this;t.forEach((function(t,a){t.RES_ID==e.RES_ID&&(e.tabIndex=a,e.url=e.$ip+t.RES_URL)}))},deep:!0,immediate:!0}},data:function(){return{tabIndex:0,url:"",hideNav:!1}},onLoad:function(t){this.hideNav="true"===t["hideNav"]}};e.default=i},c8b4:function(t,e,a){"use strict";a.r(e);var n=a("3728"),i=a("0fbd");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("2fce");var r=a("828b"),d=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"32356469",null,!1,n["a"],void 0);e["default"]=d.exports},d19a:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("b3e3")),o={mixins:[i.default],data:function(){return{RES_ID:"cc-mobile-chat"}}};e.default=o},e934:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";uni-image[data-v-32356469]{width:100%;height:100%}#h5App uni-page-head[data-v-32356469]{display:none}#h5App uni-page-head[uni-page-head-type=default] ~ uni-page-wrapper[data-v-32356469]{height:calc(100% - env(safe-area-inset-top))}.card[data-v-32356469]{background:#fff;border-radius:%?16?%;padding:%?32?%;box-sizing:border-box;margin-bottom:%?24?%}.card .title[data-v-32356469]{color:#262626;font-size:%?32?%;font-weight:700;line-height:%?48?%;margin-bottom:%?32?%}.uni-form-item[data-v-32356469]{margin-bottom:%?32?%}.uni-form-item .title[data-v-32356469]{font-size:%?28?%;font-weight:400;line-height:2;letter-spacing:0;color:#262626}.uni-form-item .uni-input[data-v-32356469]{height:%?80?%;border-radius:%?8?%;background:#f2f4f7;padding:0 %?16?%}.uni-forms .uni-forms-item__error[data-v-32356469]{display:none}.uni-forms .uni-forms-item[data-v-32356469]{margin-bottom:%?32?%;padding-top:%?32?%}.uni-forms .uni-forms-item[data-v-32356469]::after{position:absolute;bottom:%?-32?%;left:0;content:"";width:100%;height:%?2?%;background:#ebf1f8}.uni-forms .uni-forms-item[data-v-32356469]:last-child::after{display:none}.uni-forms .uni-forms-item .formContent[data-v-32356469]{display:flex;align-items:center;justify-content:space-between}.uni-forms .uni-forms-item .formContent .form-text[data-v-32356469]{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.uni-forms .uni-forms-item .formContent .form-placeholder[data-v-32356469]{font-size:%?28?%;font-weight:400;line-height:%?44?%;letter-spacing:0;color:#c5c5c5}.uni-forms .uni-forms-item__label[data-v-32356469]{height:%?44?%;padding:0;font-size:%?28?%;font-weight:400;line-height:%?44?%;letter-spacing:0;color:#262626}uni-page-body[data-v-32356469], .page[data-v-32356469]{height:100%;min-height:calc(100vh - 44px);color:#868686;font-size:%?28?%;background:#f2f4f7;overflow:auto}.page[data-v-32356469]{box-sizing:border-box}.u-form .u-form-item[data-v-32356469]{position:relative;border-bottom:1px solid #ebf1f8}.u-form .u-form-item[data-v-32356469]:last-child{border-bottom:none}.u-form .isRequired .u-form-item__body__left__content__label span[data-v-32356469]{position:relative}.u-form .isRequired .u-form-item__body__left__content__label span[data-v-32356469]::after{position:absolute;right:-8px;content:"*";color:red}.u-form .u-form-item__body__left[data-v-32356469]{margin-bottom:0!important}.u-form .m-block[data-v-32356469]{width:100%}.u-form .m-block.disabled .u-input[data-v-32356469]{background-color:#fff!important}.u-form .m-block.disabled .u-input__content__field-wrapper__field[data-v-32356469]{color:#c5c5c5!important}.u-form .u-form-item__body__left__content__label[data-v-32356469]{font-size:%?28?%;line-height:%?44?%;color:#262626;margin-bottom:%?10?%}.mpage .pagebox[data-v-32356469]{display:flex;align-items:center;justify-content:center;height:%?64?%;border:1px solid #ebf1f8;border-radius:%?8?%;color:#262626;font-size:%?28?%;font-weight:400;padding:0 %?20?%;box-sizing:border-box}.mpage .pagebox .text[data-v-32356469]{flex:1}.mpage .pagebox span[data-v-32356469]{min-width:auto!important}\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.page .iframe[data-v-32356469]{height:calc(100% - 50px)}',""]),t.exports=e}}]);