package com.yq.busi.common.util.thread;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.base.CommonCoreLogger;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.task.BaseTask;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.MacUtil;



/**
 * 统一管理企业呼叫中心各模块的线程；（不包含定时任务的线程，定时任务线程由cc-base管理）
 * 各业务模块如需使用线程，无需模块创建线程，直接使用该类统一管理；
 * 如果node节点部署多台，每台都会维护这个线程池，需要调用者控制好多台调用的情况。
 *
 */
public class ThreadManager {

	// 日志打印类
	protected static Logger logger = CommonCoreLogger.getThreadLogger();
	
	//最大的线程处理数量，cpu*8
	private static int maxThreadNum = Runtime.getRuntime().availableProcessors() * 8;
	
	private static ThreadManager instance = new ThreadManager();
	
	//执行循环的线程
	private static ScheduledExecutorService scheService = Executors.newScheduledThreadPool(maxThreadNum);
	
	//执行非循环的线程
	private static ScheduledExecutorService scheOneService = Executors.newScheduledThreadPool(maxThreadNum);
	
	/**
	 * 存储已添加的需要重复执行的定时任务
	 */
	private static Map<String,ThreadDataModel> repeatThreadMap = new HashMap<String,ThreadDataModel>();
	
	
	private ThreadManager(){}
	
	public static ThreadManager getInstance(){
		return instance;
	}
	//标识线程池是否已经启动
	private static  boolean isStart = false; 
	
	private static boolean isClosed = false;
	
	/**
	 * 启动线程池
	 * @param appName 应用模块名称
	 */
	public  void  start(String appName){
		if(!check(appName)){
			return;
		}
		if(isStart){
			logger.error("["+appName+"][ThreadManager-start]The ThreadManager has been started.");
			return;
		}
		logger.info("["+appName+"][ThreadManager-start] ThreadManager  start begin.");
		
		
		isStart = true;
		isClosed = false;
		logger.info("["+appName+"][ThreadManager-start] ThreadManager  start end.");
	}
	
	/**
	 * 检查是否可以操作，只允许cc-base操作核心方法
	 * @param appName
	 * @return 检查模块是否为cc-base
	 */
	private boolean check(String appName) {
		if("cc-base".equals(appName)){
			return true;
		}
		logger.error("["+appName+"][ThreadManager-check] 该模块不允许操作.");
		return false;
	}

	/**
	 * 定时任务需要结束时，做一些清理操作
	 * @param appName 应用模块名称
	 */
	private void clear(String appName) {
		if(!check(appName)){
			return;
		}
		try {
			if(repeatThreadMap!=null){
				for(String key : repeatThreadMap.keySet()){
					stopOneThread(appName,key);
				}
			}
		} catch (Exception e) {
		}
		if(repeatThreadMap!=null){
			repeatThreadMap.clear();
		}
		logger.info("["+appName+"][ThreadManager-clear] end...");
	}
	
	/**
	 * 停止缓存里某个线程任务
	 * @param appName 应用模块名称
	 * @param key     key
	 */
	private void stopOneThread(String appName,String key) {
		ThreadDataModel model = repeatThreadMap.get(key);
		if(model==null){
			logger.info("["+appName+"][ThreadManager-stopOneThread] 停止线程错误,缓存里没有key["+key+"]对应的线程类.");
			repeatThreadMap.remove(key);
			return;
		}
		try {
			ScheduledFuture sf = model.getScheduledFuture();
			if(sf!=null){
				sf.cancel(false);
			}
			repeatThreadMap.remove(key);
			logger.info("["+appName+"][ThreadManager-stopOneThread] 停止线程["+key+"]:"+model.getClassName());
		} catch (Exception e) {
			logger.error("["+appName+"][ThreadManager-stopOneThread] 停止线程异常:"+model.getClassName()+e.getMessage(),e);
		}
	}
	/**
	 * 停止缓存里某个模块的所有线程
	 * 在某个业务模块结束时，需要调用该方法，将该模块的所有定时任务全部停掉
	 * @param appName 应用模块名称
	 */
	public void stopThread(String appName) {
		if(StringUtils.isBlank(appName)){
			return;
		}
		if(repeatThreadMap.size()==0){
			return ;
		}
		logger.info("["+appName+"][ThreadManager-stopThread] 开始停止模块的所有线程");
		for(String key : repeatThreadMap.keySet()){
			if(key.startsWith(appName)){
				stopOneThread(appName,key);
			}
		}
		
		System.out.println(monitor());
	}

	/**
	 * 关闭线程池
	 * @param appName 应用模块名称
	 */
	public  void shutDown(String appName){
		if(!check(appName)){
			return;
		}
		logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager shutDown begin...");
		if(scheService!=null && !scheService.isShutdown()){
			logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager scheService shutDown...");
			try {
				logger.info("["+appName+"][ThreadManager-shutDown] 开始关闭循环执行线程池");
				scheService.awaitTermination(10, TimeUnit.SECONDS);
			} catch (Exception e) {
				logger.error("["+appName+"][ThreadManager-shutDown] 关闭循环执行线程池异常:"+e.getMessage(),e);
			}finally {
				logger.info("["+appName+"][ThreadManager-shutDown] 强制关闭循环执行线程池");
				try {
					scheService.shutdownNow();
				} catch (Exception e2) {
				}
			}
			
			scheService = null;
		}
		if(scheOneService!=null &&!scheOneService.isShutdown()){
			logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager scheOneService shutDown...");
			try {
				logger.info("["+appName+"][ThreadManager-shutDown] 开始关闭单次执行线程池");
				scheOneService.awaitTermination(10, TimeUnit.SECONDS);
			} catch (Exception e) {
				logger.error("["+appName+"][ThreadManager-shutDown] 关闭单次执行线程池异常:"+e.getMessage(),e);
			}finally {
				logger.info("["+appName+"][ThreadManager-shutDown] 强制关闭单次执行线程池");
				try {
					scheOneService.shutdownNow();
				} catch (Exception e2) {
				}
			}
			scheOneService = null;
		}
		
		logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager shutDown end...");
		isStart = false;
		isClosed = true;
		//释放原来的资源
		clear(appName);
	}
	
	/**
	 * 重启线程池，请谨慎调用
	 * @param appName  应用模块名称
	 */
	public void restart(String appName){
		if(!check(appName)){
			return;
		}
		logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager restart begin...");
		//清除已有的
		clear(appName);
		//停止线程池
		shutDown(appName);
		
		//重置线程池
		scheService = Executors.newScheduledThreadPool(maxThreadNum);
		scheOneService = Executors.newScheduledThreadPool(maxThreadNum);
		
		//启动线程池
		start(appName);
		logger.info("["+appName+"][ThreadManager-shutDown] ThreadManager restart end...");
	}

	/**
	 * 添加需要循环执行的线程；需要调用者控制好多台机器并行的处理；
	 * @param appName  应用模块名称
	 * @param task  要处理的线程类
	 * @param delay     延迟处理时间
	 * @param period    处理周期
	 * @param unit      单位
	 * @return  ScheduledFuture 线程任务对象，如果为空，则添加失败
	 * @throws Exception  异常
	 */
	public  ScheduledFuture  executeRepeat(String appName,BaseTask task,long delay, long period, TimeUnit unit) throws Exception{
		String className = task.getClass().getName();
		
		String key = appName+"--"+className;
		
		ThreadDataModel model = repeatThreadMap.get(key);
		if(model!=null){
			logger.error("["+appName+"][key="+key+"]线程类已存在，无法将线程类加入到重复执行任务队列中:"+className);
			throw new Exception("不能重复添加该线程类");
		}
		
		ScheduledFuture scheduledFuture = scheService.scheduleAtFixedRate(task, delay, period, unit);
		
		ThreadDataModel threadDataModel = new ThreadDataModel(className,task,delay,period,unit,scheduledFuture);
		repeatThreadMap.put(key, threadDataModel);
		
		logger.info("["+appName+"][ThreadManager-executeRepeat] 统一线程池里添加需要重复执行的线程[key="+key+"]:" + threadDataModel.toString());
		
		return scheduledFuture;
	}
	
	/**
	 * 添加只需要执行一次的线程
	 * @param appName  应用模块名称
	 * @param task     要处理的任务
	 */
	public  void executeOneTimes(String appName,BaseTask task){
		String className = task.getClass().getName();
		scheOneService.submit(task);
		logger.info("["+appName+"][ThreadManager-executeOneTimes] 统一线程池里添加需要执行一次的线程:" + className);
	}
	
	/**
	 * 创建一个新的线程运行，并使用线程池中的线程
	 * @param appName 应用模块名称
	 * @param task    任务
	 */
	public void startNewThread(String appName,BaseTask task) {
		String className = task.getClass().getName();
		task.start();
		
		logger.info("["+appName+"][ThreadManager-startNewThread] 通过统一线程池单独启动线程并运行:" + className);
	}
	
	/**
	 * 线程池是否已经关闭
	 * @param appName 应用模块名
	 * @return 是否已关闭
	 */
	public  boolean isShutDown(String appName){
		return scheService.isShutdown() && scheOneService.isShutdown();
	}
	
	/**
	 * 打印监控信息
	 * @return
	 */
	public String monitor(){
		StringBuffer sb = new StringBuffer();
		sb.append("所属机器信息:<br>").append(MacUtil.getLocalMacName()).append("<br>");
		sb.append("循环执行线程池:<br>");
		ThreadPoolExecutor e1 = (ThreadPoolExecutor)scheService;
		sb.append("线程池的核心线程数量: ").append(e1.getCorePoolSize()).append("<br>");
		sb.append("线程池当前的线程数量: ").append(e1.getPoolSize()).append("<br>");
		sb.append("线程池的最大线程数量: ").append(e1.getMaximumPoolSize()).append("<br>");
		sb.append("线程池中正在执行任务的线程数量: ").append(e1.getActiveCount()).append("<br>");
		sb.append("线程池已经执行的和未执行的任务总数: ").append(e1.getTaskCount()).append("<br>");
		sb.append("线程池已完成的任务数量(该值小于等于taskCount): ").append(e1.getCompletedTaskCount()).append("<br>");
		sb.append("线程池曾经创建过的最大线程数量(通过这个数据可以知道线程池是否满过，也就是达到了maximumPoolSize): ").append(e1.getLargestPoolSize()).append("<br>");
		
		sb.append("单次执行线程池:<br>");
		ThreadPoolExecutor e2 = (ThreadPoolExecutor)scheOneService;
		sb.append("线程池的核心线程数量: ").append(e2.getCorePoolSize()).append("<br>");
		sb.append("线程池当前的线程数量: ").append(e2.getPoolSize()).append("<br>");
		sb.append("线程池的最大线程数量: ").append(e2.getMaximumPoolSize()).append("<br>");
		sb.append("线程池中正在执行任务的线程数量: ").append(e2.getActiveCount()).append("<br>");
		sb.append("线程池已经执行的和未执行的任务总数: ").append(e2.getTaskCount()).append("<br>");
		sb.append("线程池已完成的任务数量(该值小于等于taskCount): ").append(e2.getCompletedTaskCount()).append("<br>");
		sb.append("线程池曾经创建过的最大线程数量(通过这个数据可以知道线程池是否满过，也就是达到了maximumPoolSize): ").append(e2.getLargestPoolSize()).append("<br>");
		
		sb.append("循环执行线程汇总:<br>");
		sb.append("需要循环执行的线程数量: ").append(repeatThreadMap.size()).append("<br>");
		if(repeatThreadMap.size()>0){
			sb.append("循环执行的线程明细: ").append(repeatThreadMap.size()).append("<br>");
			for(String key : repeatThreadMap.keySet()){
				ThreadDataModel model = repeatThreadMap.get(key);
				sb.append("线程: ").append(model.toString()).append("<br>");
			}
		}
		
		return sb.toString();
	}
	
	/**
	 * 线程池是否已关闭
	 * @return
	 */
	public  boolean isClosed(){
		return isClosed;
	}
	
	/**
	 * 线程池是否已启动
	 * @return
	 */
	public  boolean isRunning(){
		return isStart;
	}
	
	
	/**
	 * 查询当前机器是否允许运行定时任务
	 * @return 是否允许运行
	 */
	public boolean runJob(){
		return runJob(MacUtil.getLocalMacName());
	}
	
	
	/**
	 * 查询指定机器是否允许运行定时任务
	 * @param mac：机器名，可以通过方法  MacUtil.getLocalMacName() 获取，为空时，会自动获取本机名称
	 * @return 处理结果
	 */
	public boolean runJob(String mac){
		//运行缓存标志key，缓存里存在目前在运行定时任务的mac
		String RUNNING_CACHE_KEY = "cc-base-jobs-mac";
		
		//为空时，取本机
		if(StringUtils.isBlank(mac)){
			mac = MacUtil.getLocalMacName();
		}
		
		//无法获取到机器信息，则不运行
		if(StringUtils.isBlank(mac)){
			logger.error("无法获取到机器名称，不允许运行定时任务.");
			return false;
		}
		
		//判断本机的cc-base里是否开启定时任务
		String runJob = ConfigUtil.getString("cc-base", "RUN_JOB");
		if(StringUtils.isBlank(runJob) || DictConstants.DICT_SY_YN_N.equals(runJob)){
//			logger.info(CommonUtil.getClassNameAndMethod(this)+" 已更改设置,已关闭定时任务,暂时不执行!");
			return false;
		}
		
		//用于控制，在多台机器开启定时任务的情况下，只有一台机器可以执行定时任务
		String m = CacheUtil.get(RUNNING_CACHE_KEY);
		
		if(StringUtils.isBlank(m)){  //当前没有机器运行，则允许当前机器运行
			m = mac;
			CacheUtil.put(RUNNING_CACHE_KEY,m,300); //3.3#20230316-1 从60s改为300s，避免升级时，定时任务被其它机器的定时任务抢走
			
		}else{
			if(!m.equals(mac)){
				return false;
			}else{
//			jobsLogger.info("线程["+name+"]允许执行...目前定时任务执行机器:"+m);
			}
		}
		return true;
	}
	
	public static void main(String[] args) throws Exception {
		
	}
	
	

}


