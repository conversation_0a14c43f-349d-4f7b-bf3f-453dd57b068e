/**
 * 
 */
package com.yq.busi.common.service.thirdParty.impl;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.thirdParty.ThirdPartyGwService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ThirdPartyUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;

/**
 * <AUTHOR>
 * @date 2022-10-30 08:51:30
 * {@link https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create}
 */
public class FeishuServiceImpl implements ThirdPartyGwService {

	@Override
	public JSONObject sendMessage(JSONObject channelJson, JSONObject messageJson) {
		JSONObject result = new JSONObject();
		try {
			String webhookUrl = channelJson.getString("WEBHOOK_URL");
			if(StringUtils.isBlank(webhookUrl)) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 缺少必填参数WEBHOOK_URL:" + webhookUrl);
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "发送消息失败!");
				return result;
			}
			
			Map<String, String> headers = new HashMap<String, String>();
			headers.put("Content-Type", "application/json");
			
			JSONObject data = ThirdPartyUtil.createFeishuMessage(messageJson);
			if(data == null) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + " 不支持的消息类型:messageJson" + messageJson);
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "不支持消息类型!");
				return result;
			}
			
			// 开启加密配置
			JSONObject exParam = channelJson.getJSONObject("EX_PARAM");
			if(exParam != null) {
				if(StringUtils.equals("Y", exParam.getString("IS_SECRET"))) {
					// 加密串
					String secret = exParam.getString("SECRET");
					long timestamp = System.currentTimeMillis() / 1000;
					
					String sign = genSign(secret, timestamp);
					data.put("timestamp", timestamp);
					data.put("sign", sign);
				}
			}
			
			HttpResp resp = HttpUtil.sendPost(webhookUrl, headers, data.toJSONString(), HttpUtil.TYPE_JSON, "utf-8");
			if(resp.getCode() == 200 && StringUtils.isNotBlank(resp.getResult())) {
				JSONObject reqResult = JSONObject.parseObject(resp.getResult());
				if(StringUtils.equals("0", reqResult.getString("StatusCode"))) {
					// 发送消息成功
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "发送消息成功!");
					return result;
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
		}
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		result.put("respDesc", "发送消息失败!");
		return result;
	}
	
	private static String genSign(String secret, long timestamp) throws NoSuchAlgorithmException, InvalidKeyException {
	    //把timestamp+"\n"+密钥当做签名字符串
	    String stringToSign = timestamp + "\n" + secret;
	    
	    //使用HmacSHA256算法计算签名
	    Mac mac = Mac.getInstance("HmacSHA256");
	    mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
	    byte[] signData = mac.doFinal(new byte[]{});
	    return new String(Base64.encodeBase64(signData));
	 }

	
	public static void main(String[] args) {
		JSONObject channelJson = new JSONObject();
		channelJson.put("WEBHOOK_URL", "https://open.feishu.cn/open-apis/bot/v2/hook/dc81f81c-1b29-4188-9785-58ec52bcc583");
		
		String secret = "cXlSqDq7b5sKnCzpuC5HPb";
		long timestamp = System.currentTimeMillis() / 1000;
		String sign = "";
		try{
			sign = genSign(secret, timestamp);
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		Map<String, String> headers = new HashMap<String, String>();
		// headers.put("x-acs-dingtalk-access-token", accessToken);
		headers.put("Content-Type", "application/json");
		
		JSONObject messageJson = new JSONObject();
		/*messageJson.put("messageType", "markdown");
		messageJson.put("messageContent", "实时新增用户反馈<font color=\"warning\">132例</font>，请相关同事注意。\n"
				+ ">类型:<font color=\"comment\">用户反馈</font>\n "
				+ ">普通用户反馈:<font color=\"comment\">117例</font>\n>VIP用户反馈:<font color=\"comment\">15例</font>");*/
		messageJson.put("messageType", "text");
		messageJson.put("messageContent", "text");
		JSONObject data = ThirdPartyUtil.createFeishuMessage(messageJson);
		
		data.put("sign", sign);
		data.put("timestamp", timestamp);
		String webhookUrl = channelJson.getString("WEBHOOK_URL");
		HttpResp resp = HttpUtil.sendPost(webhookUrl, headers, data.toJSONString(), HttpUtil.TYPE_JSON, "utf-8");
		System.out.println(resp.getResult());
	}

}
